#日志配置
logging:
  level.com.sankuai.shangou.qnh.orderapi.mapper: debug # 打印mybatis的sql日志

sso:
  clientId: b996447f09
  env: test

storeCache:
  clusterName: redis-sg-common_qa
  readTimeout: 1000
  connTimeout: 2000
  routerType: master-only
  poolMaxIdle: 16
  poolMaxTotal: 32
  poolWaitMilles: 500
  poolMinIdle: 3

mts3:
  url: http://msstest.vip.sankuai.com
  bucketName: eapi
  expireSeconds: 300

squirrel:
  drunkHorseClusterName: redis-sg-drunkhorse_qa
  readTimeout: 100
  routerType: master-only
  poolMaxIdle: 16
  poolMaxTotal: 32
  poolWaitMillis: 500
  poolMinIdle: 3