sso:
  clientId: 05b21b97ac
  env: prod

storeCache:
  clusterName: redis-sg-common_product
  readTimeout: 1000
  connTimeout: 2000
  routerType: master-only
  poolMaxIdle: 16
  poolMaxTotal: 32
  poolWaitMilles: 500
  poolMinIdle: 3

mts3:
  url: https://s3plus.vip.sankuai.com
  bucketName: eapi
  expireSeconds: 300

squirrel:
  drunkHorseClusterName: redis-sg-drunkhorse_stage
  readTimeout: 100
  routerType: master-only
  poolMaxIdle: 16
  poolMaxTotal: 32
  poolWaitMillis: 500
  poolMinIdle: 3