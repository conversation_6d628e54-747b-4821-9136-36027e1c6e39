package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.JacksonUtils;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.BatchReviewResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ReviewFailedDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ReviewResultDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.review.BatchReviewTenantProductResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.codehaus.jackson.type.TypeReference;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 批量审核响应
 * @author: WangSukuan
 * @create: 2020-03-12
 **/
@TypeDoc(
        description = "批量审核响应"
)
@Data
@ApiModel("批量审核响应")
public class ReviewBatchResponse {

    @FieldDoc(
            description = "错误数据明细 ReviewBatchRequest.reviewBizType 为 1 时有效", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "错误数据明细")
    private List<String> errorReasons = new ArrayList<>();

    @FieldDoc(
            description = "错误数据明细 ReviewBatchRequest.reviewBizType 为 2 时有效", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "错误数据明细")
    private ReviewResultVO reviewResultVO;


    public ReviewBatchResponse buildReviewBatchResponse(Integer reviewBizType, BatchReviewResponse batchReviewResponse){

        if (StringUtils.isEmpty(batchReviewResponse.getErrorData())){
            return this;
        }
        if (1 == reviewBizType){
            this.errorReasons = JacksonUtils.fromJson(batchReviewResponse.getErrorData(), new TypeReference<List<String>>() {});
        }else {
            ReviewResultDTO reviewResultDTO = JacksonUtils.fromJson(batchReviewResponse.getErrorData(), ReviewResultDTO.class);
            this.reviewResultVO = new ReviewResultVO().buildReviewResultVO(reviewResultDTO);
        }
        return this;

    }

    public static ReviewBatchResponse buildTenantReviewBatchResponse(BatchReviewTenantProductResponse rpcResp,
                                                                     List<ReviewFailedVO> createSupplyRelationFailList) {
        ReviewBatchResponse batchResponse = new ReviewBatchResponse();
        List<ReviewFailedVO> totalFailList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(rpcResp.getData().getFailedList())) {
            totalFailList.addAll(Fun.map(rpcResp.getData().getFailedList(), rpcFailItem -> {
                        ReviewFailedVO reviewFailedVO = new ReviewFailedVO();
                        reviewFailedVO.setBizId(rpcFailItem.getReviewId());
                        reviewFailedVO.setName(rpcFailItem.getName());
                        reviewFailedVO.setReason(rpcFailItem.getReason());
                        return reviewFailedVO;
                    }));
        }
        if (CollectionUtils.isNotEmpty(createSupplyRelationFailList)) {
            totalFailList.addAll(createSupplyRelationFailList);
        }
        ReviewResultVO reviewResultVO = new ReviewResultVO();
        reviewResultVO.setSuccessCount((long)(rpcResp.getData().getSuccessCount() - CollectionUtils.size(createSupplyRelationFailList)));
        reviewResultVO.setFailedCount((long)(rpcResp.getData().getFailedCount() + CollectionUtils.size(createSupplyRelationFailList)));
        reviewResultVO.setFailedList(totalFailList);
        batchResponse.setReviewResultVO(reviewResultVO);

        return batchResponse;
    }
}
