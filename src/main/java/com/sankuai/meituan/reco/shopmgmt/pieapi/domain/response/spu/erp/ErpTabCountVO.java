package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ERP tab页数量统计
 *
 * <AUTHOR>
 * @since 2023/05/22
 */
@TypeDoc(
        description = "ERP tab页数量统计"
)
@Data
@ApiModel("ERP tab页数量统计")
public class ErpTabCountVO {
    @FieldDoc(description = "上架商品数", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "上架商品数")
    private Integer onShelfCount;

    @FieldDoc(description = "下架商品数", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "下架商品数")
    private Integer offShelfCount;

    @FieldDoc(description = "手动下架商品数", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "手动下架商品数")
    private Integer manualOffShelfCount;

}
