package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.rider;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.warehouse.AbnOrderService;
import com.sankuai.shangou.logistics.warehouse.dto.AbnOrderDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023-12-19
 * @email <EMAIL>
 */
@Slf4j
@Component
@Rhino
public class AbnOrderRemoteService {

    @Resource
    private AbnOrderService abnOrderService;

    @Degrade(rhinoKey = "AbnOrderServiceWrapper-getUnprocessedAbnOrderCount",
            fallBackMethod = "getUnprocessedAbnOrderCountFallback",
            timeoutInMilliseconds = 1000)
    public int getUnprocessedAbnOrderCount(long warehouseId) {
        try {
            TResult<List<AbnOrderDTO>> unprocessedOrderListResult = abnOrderService.getUnprocessed(warehouseId);
            if (!unprocessedOrderListResult.isSuccess() || CollectionUtils.isEmpty(unprocessedOrderListResult.getData())) {
                return 0;
            }
            return unprocessedOrderListResult.getData().size();
        } catch (Exception e) {
            log.error("invoke abnOrderService.getUnprocessed error");
            return 0;
        }

    }

    private int getUnprocessedAbnOrderCountFallback(long warehouseId){
        log.error("queryDeliveryInfoFallback {}", warehouseId);
        return 0;
    }

    @Degrade(rhinoKey = "AbnOrderRemoteService.getUnprocessedAbnOrderBySource", fallBackMethod = "getUnprocessedAbnOrderBySourceFallBack", timeoutInMilliseconds = 1500)
    public Optional<AbnOrderDTO> getUnprocessedAbnOrderBySource(Long storeId, Integer channelId, String viewOrderId) {
        log.info("start invoke abnOrderService.getUnprocessed, storeId: {}", storeId);
        TResult<List<AbnOrderDTO>> result = abnOrderService.getUnprocessed(storeId);
        log.info("end invoke abnOrderService.getUnprocessed, resp: {}", result);
        if (!result.isSuccess()) {
            throw new RuntimeException("查询缺货异常单失败");
        }

        if (CollectionUtils.isEmpty(result.getData())) {
            return Optional.empty();
        }

        return result.getData().stream()
                .filter(dto -> Objects.equals(dto.getSourceOrderNo(), viewOrderId) && Objects.equals(dto.getSourceType(), ChannelOrderConvertUtils.convertBizType(channelId)))
                .findAny();

    }

    public Optional<AbnOrderDTO> getUnprocessedAbnOrderBySourceFallBack(Long storeId, Integer channelId, String viewOrderId) {
        log.warn("AbnOrderWrapper.getUnprocessedAbnOrderBySource 发生降级");
        return Optional.empty();
    }
}
