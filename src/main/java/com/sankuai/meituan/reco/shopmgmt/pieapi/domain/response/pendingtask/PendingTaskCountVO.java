/*
 * Copyright (c) 2019 Meituan.com. All Rights Reserved.
 */
package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pendingtask;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 代办任务数量
 */
@TypeDoc(
        description = "代办任务信息"
)
@Data
@ApiModel("代办任务信息")
public class PendingTaskCountVO {

    @FieldDoc(
            description = "任务码(值等于权限码,前后端约定https://km.sankuai.com/page/222769177)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "任务码(值等于权限码,前后端约定https://km.sankuai.com/page/222769177)", required = true)
    @NotNull
    private String authCode;

    @FieldDoc(
            description = "代办数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "代办数量", required = true)
    @NotNull
    private Integer count;
}
