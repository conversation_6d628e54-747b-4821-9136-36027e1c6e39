package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@TypeDoc(
        description = "波次拣货出库商品数量和类型查询返回"
)
@ApiModel("波次拣货出库商品数量和类型查询返回")
@Data
public class WarehousePickQueryCountRequest {

    @FieldDoc(
            description = "调入方门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "调入方门店id")
    private List<String> outWarehouseOrderNo;

    @FieldDoc(
            description = "当前操作单据标识", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "单据标识")
    private String orderTag;

}
