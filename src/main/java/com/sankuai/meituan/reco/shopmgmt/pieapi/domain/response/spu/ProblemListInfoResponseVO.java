package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.CompareSpuListDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;

@TypeDoc(
        description = "不一致列表详情"
)
@Data
public class ProblemListInfoResponseVO {


    @FieldDoc(
            description = "问题商品列表", requiredness = Requiredness.REQUIRED
    )
    List<ProblemListInfoVO> problemSpuInfoList;

    @FieldDoc(
            description = "分页信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "分页信息", required = true)
    private PageInfoVO pageInfo;


}