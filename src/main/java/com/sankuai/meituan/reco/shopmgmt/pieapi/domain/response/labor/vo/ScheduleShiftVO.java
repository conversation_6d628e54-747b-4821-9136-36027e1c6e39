package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022-10-19
 * @email <EMAIL>
 */
@TypeDoc(
        description = "新建/编辑班次请求"
)
@ApiModel("新建/编辑班次请求")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ScheduleShiftVO {


    @FieldDoc(
            description = "规则id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "规则id")
    @NotNull
    private Long ruleId;

    @FieldDoc(
            description = "班次id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "班次id")
    @NotNull
    private Long shiftId;

    @FieldDoc(
            description = "颜色", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "颜色")
    @NotNull
    private String color;

    @FieldDoc(
            description = "名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "名称")
    @NotNull
    private String name;

    @FieldDoc(
            description = "上班时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "上班时间")
    @NotNull
    private String startWorkTime;

    @FieldDoc(
            description = "上班打卡起始时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "上班打卡起始时间")
    @NotNull
    private String startWorkCheckInDurationBegin;

    @FieldDoc(
            description = "上班打卡结束时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "上班打卡结束时间")
    @NotNull
    private String startWorkCheckInDurationEnd;

    @FieldDoc(
            description = "下班时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "下班时间")
    @NotNull
    private String endWorkTime;

    @FieldDoc(
            description = "下班打卡起始时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "下班打卡起始时间")
    @NotNull
    private String endWorkCheckInDurationBegin;

    @FieldDoc(
            description = "下班打卡结束时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "下班打卡结束时间")
    @NotNull
    private String endWorkCheckInDurationEnd;

}
