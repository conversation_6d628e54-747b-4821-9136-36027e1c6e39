package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.FallbackException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.osw.OSWServiceWrapper;
import com.sankuai.meituan.reco.store.management.thrift.EmpowerTaskLogicException;
import com.sankuai.shangou.common.PageListDTO;
import com.sankuai.shangou.common.PageQueryDTOV2;
import com.sankuai.shangou.common.Result;
import com.sankuai.shangou.common.ResultCodeEnum;
import com.sankuai.shangou.commons.thrift.publisher.request.TPageRequest;
import com.sankuai.shangou.commons.thrift.publisher.request.UserContext;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.dhprocess.api.base.PageResponse;
import com.sankuai.shangou.logistics.dhprocess.api.judge.DutyProcessQueryThriftService;
import com.sankuai.shangou.logistics.dhprocess.api.judge.dto.DutyPostProcessingBaseDTO;
import com.sankuai.shangou.logistics.duty.admitting.model.AdmittingTaskDTO;
import com.sankuai.shangou.logistics.dutycenter.api.admitting.service.AdmittingTaskFrontendQueryService;
import com.sankuai.shangou.logistics.dutycenter.api.admitting.service.cmd.UndoneAdmittingTaskFrontendQueryCmdDTO;
import com.sankuai.shangou.logistics.dutycenter.api.dutyorder.service.cmd.DutyQueryContextDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Rhino
@Service
public class DutyCenterWrapper {

    @Autowired
    private AdmittingTaskFrontendQueryService admittingTaskQueryService;
    @Autowired
    private OSWServiceWrapper oswServiceWrapper;
    @Autowired
    private DutyProcessQueryThriftService dutyProcessQueryThriftService;




    @Degrade(rhinoKey = "DutyCenterWrapper.queryPendingCount",
            fallBackMethod = "queryPendingCountFallback",
            timeoutInMilliseconds = 1200,
            ignoreExceptions = {EmpowerTaskLogicException.class})
    @MethodLog(logRequest = true, logResponse = true)
    public Integer queryPendingCount(Long tenantId, Long warehouseId, Long accountId) {
        try {
            UndoneAdmittingTaskFrontendQueryCmdDTO cmdDTO = new UndoneAdmittingTaskFrontendQueryCmdDTO();
            PageQueryDTOV2<UndoneAdmittingTaskFrontendQueryCmdDTO> pageQuery = new PageQueryDTOV2<>(cmdDTO, 1, 0, null);

            Map<Long, String> warehouseOrgMap = Optional.ofNullable(oswServiceWrapper.queryWarehouseOrgCodes(tenantId, Lists.newArrayList(warehouseId))).orElse(new HashMap<>());
            if (MapUtils.isEmpty(warehouseOrgMap)) {
                return 0;
            }
            DutyQueryContextDTO context = new DutyQueryContextDTO(accountId, warehouseOrgMap.get(warehouseId));
            Result<PageListDTO<AdmittingTaskDTO>> result = admittingTaskQueryService.pageQueryUndoneTasksV2(tenantId, pageQuery, context);
            if(Objects.isNull(result) || result.getCode() != ResultCodeEnum.SUCCESS.getCode() || Objects.isNull(result.getModule())) {
                throw new CommonRuntimeException("query pending task count failed, result=" + result);
            }
            TPageRequest pageRequest = new TPageRequest(1,0);
            TResult<PageResponse<DutyPostProcessingBaseDTO>> dhprocessPageResult = dutyProcessQueryThriftService.listPostProcessingList(pageRequest, warehouseId, new UserContext(tenantId, accountId, "", "", null));
            if(Objects.isNull(dhprocessPageResult) || dhprocessPageResult.getCode() != ResultCodeEnum.SUCCESS.getCode() || Objects.isNull(dhprocessPageResult.getData())) {
                throw new CommonRuntimeException("query pending pageResponseTResult task count failed, result=" + dhprocessPageResult);
            }
            return Long.valueOf(result.getModule().getTotal()+dhprocessPageResult.getData().getTotal()).intValue();
        } catch (Exception e) {
            log.error("DutyCenterWrapper.queryPendingCount occur exception, tenantId={}, accountId={}", tenantId, accountId, e);
            throw new CommonRuntimeException(e);
        }
    }

    private Integer queryPendingCountFallback(Long tenantId, Long warehouseId, Long accountId) {
        throw new FallbackException("DutyCenterWrapper.queryPendingCount");
    }
}
