package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2020-03-30 11:35
 * @Description:
 */
@TypeDoc(
        description = "克重退款页面检查退款VO"
)
@ApiModel("克重退款页面检查退款VO")
@Data
public class OrderItemWeightRefundCheckVO {
    @FieldDoc(
            description = "商品内部sku", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品sku", required = true)
    private String innerSkuId;

    @FieldDoc(
            description = "商品内部spu", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品spu", required = true)
    private String spu;


    @FieldDoc(
            description = "商品erp编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品erp编码", required = true)
    private String erpItemCode;

    @FieldDoc(
            description = "upc", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "upc", required = true)
    private String upc;

    @FieldDoc(
            description = "商家在渠道的skuId", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商家在渠道的skuId")
    private String customerSkuId;

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称", required = true)
    private String skuName;

    @FieldDoc(
            description = "商品图片URL", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品图片URL", required = true)
    private String picUrl;

    @FieldDoc(
            description = "规格", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "规格", required = true)
    private String specification;

    @FieldDoc(
            description = "商品渠道重量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品渠道重量", required = true)
    private Integer channelWeight;

    @FieldDoc(
            description = "可退差价数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "可退差价数量", required = true)
    private Integer canRefundCount;

    @FieldDoc(
            description = "实付价格", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "实付价格", required = true)
    private Integer currentPrice;

    @FieldDoc(
            description = "订单明细itemId", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "订单明细itemId")
    private String itemId;

    @FieldDoc(
            description = "商品项退差价记录列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品项退差价记录列表", required = true)
    private List<WeightRefundAfterSaleRecordVO> weightRefundAfterSaleRecordVOS;
}
