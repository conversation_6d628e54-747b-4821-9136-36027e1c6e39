package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.appraise;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: <EMAIL>
 * @Date: 2020/9/14 16:18
 * @Description:
 */
@ApiModel("体检指标明细查询请求")
@Data
@NoArgsConstructor
public class IndicatorDetailRequest {

    private Long planId;

    private Long poiId;

    private Integer type;

    private String startDate;

    private String endDate;

    private String statisticalStartDate;

    private String statisticalEndDate;

}
