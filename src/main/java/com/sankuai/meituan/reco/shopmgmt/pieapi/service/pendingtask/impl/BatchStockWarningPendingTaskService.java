/*
 * Copyright (c) 2019 Meituan.com. All Rights Reserved.
 */
package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.impl;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.AbstractSinglePendingTaskService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.StockWrapper;
import com.sankuai.meituan.reco.store.management.stock.biz.comprehensive.query.stockquery.rep.BatchStockWarningQueryReq;
import com.sankuai.meituan.reco.store.management.stock.biz.comprehensive.query.stockquery.resp.BatchStockWarningQueryResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 处于效期预警状态批次库存量查询
 * <br><br>
 * Author: linjianyu <br>
 * Date: 2022-02-28 Time: 15:10
 * @since 3.1.1 效期预警版本
 */
@Service
public class BatchStockWarningPendingTaskService extends AbstractSinglePendingTaskService {

    private static final int DEFAULT_PAGE = 1;
    private static final int DEFAULT_PAGE_SIZE = 1;
    private static final int DEFAULT_WARNING_TYPE = 1;  //  效期预警类型

    @Autowired
    private StockWrapper stockWrapper;

    @Override
    public PendingTaskResult getPendingTaskCount(PendingTaskParam param) throws Exception {
        BatchStockWarningQueryReq rpcReq = new BatchStockWarningQueryReq();
        rpcReq.setTenantId(param.getTenantId());
        rpcReq.setPageNo(DEFAULT_PAGE);
        rpcReq.setPageSize(DEFAULT_PAGE_SIZE);
        rpcReq.setWarningType(DEFAULT_WARNING_TYPE);
        rpcReq.setContainDetails(false);
        //  单仓/门店或全仓/门店, 仓/门店查询时不指定repository参数
        if (param.getStoreIds() != null && param.getStoreIds().size() == 1) {
            rpcReq.setRepositoryId(param.getEntityId());
            rpcReq.setRepositoryType(param.getEntityType());
        }

        BatchStockWarningQueryResp result = stockWrapper.batchStockWarningCount(rpcReq);
        int count = result == null ? 0 : result.getExpiredCount() + result.getOfflineCount() + result.getWarningCount();
        return PendingTaskResult.createNumberMarker(count);
    }

    @SuppressWarnings("deprecation")
    @Override
    protected AuthCodeEnum module() {
        return AuthCodeEnum.BATCH_STOCK_WARNING;
    }
}
