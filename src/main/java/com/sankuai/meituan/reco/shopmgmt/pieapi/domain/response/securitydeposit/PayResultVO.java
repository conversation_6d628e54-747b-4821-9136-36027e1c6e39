package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.securitydeposit;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: <EMAIL>
 * @Date: 2020/3/6 20:51
 * @Description:
 */
@TypeDoc(description = "支付结果对象")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PayResultVO {


    /**
     * 支付状态
     */
    private Integer status;


    /**
     * 支付失败原因
     */
    private String failReason;


}
