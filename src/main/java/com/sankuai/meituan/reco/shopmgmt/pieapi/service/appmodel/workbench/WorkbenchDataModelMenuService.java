package com.sankuai.meituan.reco.shopmgmt.pieapi.service.appmodel.workbench;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiRelationDto;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.appmodel.QueryMenuInfoRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.BizModeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.ModeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.TenantBizModeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.meituan.shangou.saas.tenant.api.dto.tenant.BoothSettleModeDto;
import com.meituan.shangou.saas.tenant.thrift.common.enums.YesNoEnum;
import com.meituan.shangou.sac.dto.model.SacMenuNodeDto;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PoiSgRealTimeDataDto;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.appmodel.MenuInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.management.RevenueOverviewVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.MenuCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.DateUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MoneyUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.AccountTypeEnum;
import com.sankuai.meituan.util.ConfigUtilAdapter;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 工作台数据模块菜单service
 *
 * <AUTHOR>
 * @since 2021/7/5
 */
@Service
@Slf4j
public class WorkbenchDataModelMenuService extends AbstractWorkBenchMenuService {

    /**
     * 扩展数据key：待处理任务数量
     */
    private static final String DATA_VALUE = "dataValue";

    private static final String DATA_CR_VALUE = "dataValueCr";

    /**
     * 扩展数据key：提示信息
     */
    private static final String DESC = "desc";

    private static final String DEFAULT_VALUE = "--";

    private static final String NEW_MENU_TAG = "newMenu";

    @Autowired
    private SettlementCurrentRevenueWrapper settlementCurrentRevenueWrapper;

    @Autowired
    private AuthThriftWrapper authThriftWrapper;

    @Autowired
    private SgDataWrapper sgDataWrapper;

    @Autowired
    private DwDataWrapper dwDataWrapper;

    @Autowired
    private TenantWrapper tenantWrapper;

    @Override
    protected MenuCodeEnum getMenuCode() {
        return MenuCodeEnum.WORKBENCH_DATA;
    }


    @Override
    protected List<MenuInfo> getSubMenuInfos(Map<String, SacMenuNodeDto> menuCodeWithNodeMap,
                                             IdentityInfo identityInfo, QueryMenuInfoRequest request, Boolean possibleNewQueryGray) {
        Long tenantId = identityInfo.getUser().getTenantId();
        Set<String> menuCodes = menuCodeWithNodeMap.keySet();

        List<MenuInfo> menuInfos = new ArrayList<>();
        // 业务数据是否获取成功，key：子数据菜单code，value：是否获取成功
        Map<String, Boolean> dataQueryResultMap = new HashMap<>();

        // 获取数仓实时数据，获取失败为null
        PoiSgRealTimeDataDto realTimeDataDto = null;
        MenuCodeEnum jumpMenuCodeEnum = MenuCodeEnum.WORKBENCH_DATA_JUMP;
        if (request != null && StringUtils.equals(request.getTag(), NEW_MENU_TAG)) {
            List<Long> poiIds = new ArrayList<>();
            poiIds.addAll(identityInfo.getStoreIdList());
            // 新版首页
            realTimeDataDto = getRealTimeDataAndFillResultMapNew(poiIds, dataQueryResultMap);
            ModeEnum modeEnum = getMode(tenantId);
            if (modeEnum == ModeEnum.B) {
                // B+版披露营业额、预计利润、有效订单数、整单超时率
                // 获取总营业额
                if (menuCodes.contains(MenuCodeEnum.TOTAL_TURNOVER.getCode())) {
                    MenuInfo menuInfo = buildTotalTurnoverMenuInfoNew(realTimeDataDto, menuCodeWithNodeMap);
                    menuInfos.add(menuInfo);
                }
                // 获取预计收入
                if (menuCodes.contains(MenuCodeEnum.EXPECTED_REVENUE.getCode())) {
                    MenuInfo menuInfo = buildExpectedRevenueMenuInfoNew(realTimeDataDto, menuCodeWithNodeMap);
                    menuInfos.add(menuInfo);
                }
                // 获取有效订单量
                if (menuCodes.contains(MenuCodeEnum.EFFECTIVE_ORDER_COUNT.getCode())) {
                    MenuInfo menuInfo = buildEffectiveOrderCntMenuInfoNew(realTimeDataDto, menuCodeWithNodeMap);
                    menuInfos.add(menuInfo);
                }
                // 获取履约超时率
                if (menuCodes.contains(MenuCodeEnum.PICK_OVERTIME_RATE.getCode())) {
                    MenuInfo pickOverTimeRateMenuInfo = buildPickOverTimeRateMenuInfoNew(realTimeDataDto, menuCodeWithNodeMap);
                    menuInfos.add(pickOverTimeRateMenuInfo);
                }
            } else {
                // 超市版披露商品销售额、销售收入、有效订单数、整单超时率
                // 获取商品销售额
                if (menuCodes.contains(MenuCodeEnum.SALE_VOLUME.getCode())) {
                    MenuInfo saleVolumeMenuInfo = buildSaleVolumeMenuInfoNew(realTimeDataDto, menuCodeWithNodeMap);
                    menuInfos.add(saleVolumeMenuInfo);
                }
                // 获取销售收入
                if (menuCodes.contains(MenuCodeEnum.SALE_INCOME.getCode())) {
                    MenuInfo saleIncomeMenuInfo = buildSaleIncomeMenuInfoNew(realTimeDataDto, menuCodeWithNodeMap);
                    menuInfos.add(saleIncomeMenuInfo);
                }
                // 有效订单量
                if (menuCodes.contains(MenuCodeEnum.EFFECTIVE_ORDER_COUNT.getCode())) {
                    MenuInfo effectiveOrderCntMenuInfo = buildEffectiveOrderCntMenuInfoNew(realTimeDataDto, menuCodeWithNodeMap);
                    menuInfos.add(effectiveOrderCntMenuInfo);
                }
                // 获取履约超时率
                if (menuCodes.contains(MenuCodeEnum.PICK_OVERTIME_RATE.getCode())) {
                    MenuInfo pickOverTimeRateMenuInfo = buildPickOverTimeRateMenuInfoNew(realTimeDataDto, menuCodeWithNodeMap);
                    menuInfos.add(pickOverTimeRateMenuInfo);
                }
            }
            jumpMenuCodeEnum = MenuCodeEnum.WORKBENCH_DATA_JUMP_NEW;
        } else {
            Long poiId = identityInfo.getStoreId();
            List<Long> poiIds = Collections.singletonList(poiId);

            // 得到仓的关联门店
            List<PoiRelationDto> warehousePoiRelation = tenantWrapper.queryPoiByWarehouseId(tenantId, Collections.singletonList(poiId));

            if (CollectionUtils.isNotEmpty(warehousePoiRelation)) {
                // 如果仓没有门店信息，则返回empty
                if (CollectionUtils.isEmpty(warehousePoiRelation.get(0).getTargetPoiInfoList())) {
                    return Collections.emptyList();
                }
                poiIds = warehousePoiRelation.get(0).getTargetPoiInfoList().stream()
                        .map(PoiInfoDto::getPoiId).collect(Collectors.toList());
            }
            // 老版首页
            realTimeDataDto = getRealTimeDataAndFillResultMap(poiIds, dataQueryResultMap);
            // 获取总营业额
            if (menuCodes.contains(MenuCodeEnum.TOTAL_TURNOVER.getCode())) {
                MenuInfo totalTurnoverMenuInfo = buildTotalTurnoverMenuInfo(realTimeDataDto, menuCodeWithNodeMap);
                menuInfos.add(totalTurnoverMenuInfo);
            }
            // 获取预计收入、有效订单量
            if (menuCodes.contains(MenuCodeEnum.EXPECTED_REVENUE.getCode())
                    || menuCodes.contains(MenuCodeEnum.EFFECTIVE_ORDER_COUNT.getCode())) {
                List<MenuInfo> revenueMenueInfos = buildRevenueMenuInfos(tenantId, poiId, realTimeDataDto, menuCodeWithNodeMap, dataQueryResultMap);
                menuInfos.addAll(revenueMenueInfos);
            }
            // 获取履约超时率
            if (menuCodes.contains(MenuCodeEnum.PICK_OVERTIME_RATE.getCode())) {
                MenuInfo pickOverTimeRateMenuInfo = buildPickOverTimeRateMenuInfo(realTimeDataDto, menuCodeWithNodeMap);
                menuInfos.add(pickOverTimeRateMenuInfo);
            }
            jumpMenuCodeEnum = MenuCodeEnum.WORKBENCH_DATA_JUMP;
        }

        // 如果所有数据都获取失败，需要清空列表
        boolean allDataQueryFail = dataQueryResultMap.values().stream()
                .allMatch(result -> result.equals(Boolean.FALSE));
        if (allDataQueryFail) {
            menuInfos = new ArrayList<>();
        }

        // 如果数据模块有值，且有跳转权限，构造跳转子菜单数据
        if (CollectionUtils.isNotEmpty(menuInfos) && menuCodes.contains(jumpMenuCodeEnum.getCode())) {
            MenuInfo menuInfo = buildMenuInfoBySacMenuNodeDto(menuCodeWithNodeMap.get(jumpMenuCodeEnum.getCode()));
            menuInfos.add(menuInfo);
        }

        return menuInfos;
    }

    @Override
    protected Boolean hideParentMenu(Set<String> hasAuthSubMenuCodes) {
        Set<String> showDataMenuCodes = new HashSet<>(Arrays.asList(MenuCodeEnum.TOTAL_TURNOVER.getCode(),
                MenuCodeEnum.EXPECTED_REVENUE.getCode(),
                MenuCodeEnum.EFFECTIVE_ORDER_COUNT.getCode(),
                MenuCodeEnum.PICK_OVERTIME_RATE.getCode()));
        showDataMenuCodes.retainAll(hasAuthSubMenuCodes);

        return CollectionUtils.isEmpty(showDataMenuCodes);
    }

    private PoiSgRealTimeDataDto getRealTimeDataAndFillResultMap(List<Long> poiIds, Map<String, Boolean> dataQueryResultMap) {
        try {
            PoiSgRealTimeDataDto realTimeDataDto = sgDataWrapper.queryRealTimeDataByPowerAPi(poiIds);
            log.info("工作台查询实时数据 poiId:{},realTimeDataDto:{}", poiIds, realTimeDataDto);
            dataQueryResultMap.put(MenuCodeEnum.TOTAL_TURNOVER.getCode(), true);
            dataQueryResultMap.put(MenuCodeEnum.EXPECTED_REVENUE.getCode(), true);
            dataQueryResultMap.put(MenuCodeEnum.EFFECTIVE_ORDER_COUNT.getCode(), true);
            dataQueryResultMap.put(MenuCodeEnum.PICK_OVERTIME_RATE.getCode(), true);
            return realTimeDataDto;
        }
        catch (Exception e) {
            log.error("查询数仓失败 poiId:{}", poiIds, e);
            dataQueryResultMap.put(MenuCodeEnum.TOTAL_TURNOVER.getCode(), false);
            dataQueryResultMap.put(MenuCodeEnum.EXPECTED_REVENUE.getCode(), false);
            dataQueryResultMap.put(MenuCodeEnum.EFFECTIVE_ORDER_COUNT.getCode(), false);
            dataQueryResultMap.put(MenuCodeEnum.PICK_OVERTIME_RATE.getCode(), false);
            return null;
        }
    }

    private PoiSgRealTimeDataDto getRealTimeDataAndFillResultMapNew(List<Long> poiIds, Map<String, Boolean> dataQueryResultMap) {
        try {
            PoiSgRealTimeDataDto realTimeDataDto = dwDataWrapper.queryRealTimeData(poiIds);
            log.info("工作台查询实时数据 poiId:{},realTimeDataDto:{}", poiIds, realTimeDataDto);
            dataQueryResultMap.put(MenuCodeEnum.TOTAL_TURNOVER.getCode(), true);
            dataQueryResultMap.put(MenuCodeEnum.SALE_VOLUME.getCode(), true);
            dataQueryResultMap.put(MenuCodeEnum.SALE_INCOME.getCode(), true);
            dataQueryResultMap.put(MenuCodeEnum.EXPECTED_REVENUE.getCode(), true);
            dataQueryResultMap.put(MenuCodeEnum.EFFECTIVE_ORDER_COUNT.getCode(), true);
            dataQueryResultMap.put(MenuCodeEnum.PICK_OVERTIME_RATE.getCode(), true);
            return realTimeDataDto;
        }
        catch (Exception e) {
            log.error("查询数仓失败 poiId:{}", poiIds, e);
            dataQueryResultMap.put(MenuCodeEnum.TOTAL_TURNOVER.getCode(), false);
            dataQueryResultMap.put(MenuCodeEnum.SALE_VOLUME.getCode(), false);
            dataQueryResultMap.put(MenuCodeEnum.SALE_INCOME.getCode(), false);
            dataQueryResultMap.put(MenuCodeEnum.EXPECTED_REVENUE.getCode(), false);
            dataQueryResultMap.put(MenuCodeEnum.EFFECTIVE_ORDER_COUNT.getCode(), false);
            dataQueryResultMap.put(MenuCodeEnum.PICK_OVERTIME_RATE.getCode(), false);
            return null;
        }
    }

    private MenuInfo buildTotalTurnoverMenuInfoNew(PoiSgRealTimeDataDto realTimeDataDto, Map<String, SacMenuNodeDto> menuCodeWithNodeMap) {
        String menuCode = MenuCodeEnum.TOTAL_TURNOVER.getCode();
        String totalTurnover = DEFAULT_VALUE;
        String rate = DEFAULT_VALUE;

        if (realTimeDataDto != null && realTimeDataDto.getOpenAmtGtv() != null) {
            totalTurnover = formatWithTwoDecimal(realTimeDataDto.getOpenAmtGtv());

        }
        if (realTimeDataDto != null && realTimeDataDto.getOpenAmtGtvCr() != null) {
            rate = BigDecimal.valueOf(realTimeDataDto.getOpenAmtGtvCr())
                    .multiply(BigDecimal.valueOf(100))
                    .setScale(2, RoundingMode.HALF_UP)
                    .doubleValue() + "%";
        }

        MenuInfo menuInfo = buildMenuInfoBySacMenuNodeDto(menuCodeWithNodeMap.get(menuCode));
        menuInfo.getExtraInfoMap().put(DATA_VALUE, totalTurnover);
        menuInfo.getExtraInfoMap().put(DATA_CR_VALUE, rate);
        return menuInfo;
    }

    private MenuInfo buildSaleVolumeMenuInfoNew(PoiSgRealTimeDataDto realTimeDataDto, Map<String, SacMenuNodeDto> menuCodeWithNodeMap) {
        String menuCode = MenuCodeEnum.SALE_VOLUME.getCode();
        String saleVolume = DEFAULT_VALUE;
        String rate = DEFAULT_VALUE;

        if (realTimeDataDto != null && realTimeDataDto.getProdAmt() != null) {
            saleVolume = formatWithTwoDecimal(realTimeDataDto.getProdAmt());
        }
        if (realTimeDataDto != null && realTimeDataDto.getProdAmtCr() != null) {
            rate = BigDecimal.valueOf(realTimeDataDto.getProdAmtCr())
                    .multiply(BigDecimal.valueOf(100))
                    .setScale(2, RoundingMode.HALF_UP)
                    .doubleValue() + "%";
        }

        MenuInfo menuInfo = buildMenuInfoBySacMenuNodeDto(menuCodeWithNodeMap.get(menuCode));
        menuInfo.getExtraInfoMap().put(DATA_VALUE, saleVolume);
        menuInfo.getExtraInfoMap().put(DATA_CR_VALUE, rate);
        return menuInfo;
    }

    private MenuInfo buildSaleIncomeMenuInfoNew(PoiSgRealTimeDataDto realTimeDataDto, Map<String, SacMenuNodeDto> menuCodeWithNodeMap) {
        String menuCode = MenuCodeEnum.SALE_INCOME.getCode();
        String saleIncome = DEFAULT_VALUE;
        String rate = DEFAULT_VALUE;

        if (realTimeDataDto != null && realTimeDataDto.getOrdAmt() != null) {
            saleIncome = formatWithTwoDecimal(realTimeDataDto.getOrdAmt());
        }
        if (realTimeDataDto != null && realTimeDataDto.getOrdAmtCr() != null) {
            rate = BigDecimal.valueOf(realTimeDataDto.getOrdAmtCr())
                    .multiply(BigDecimal.valueOf(100))
                    .setScale(2, RoundingMode.HALF_UP)
                    .doubleValue() + "%";
        }

        MenuInfo menuInfo = buildMenuInfoBySacMenuNodeDto(menuCodeWithNodeMap.get(menuCode));
        menuInfo.getExtraInfoMap().put(DATA_VALUE, saleIncome);
        menuInfo.getExtraInfoMap().put(DATA_CR_VALUE, rate);
        return menuInfo;
    }

    private MenuInfo buildPickOverTimeRateMenuInfoNew(PoiSgRealTimeDataDto realTimeDataDto, Map<String, SacMenuNodeDto> menuCodeWithNodeMap) {
        String menuCode = MenuCodeEnum.PICK_OVERTIME_RATE.getCode();
        String pickOverTimeRate = DEFAULT_VALUE;
        String rate = DEFAULT_VALUE;

        if (realTimeDataDto != null && realTimeDataDto.getPerformanceOvertimeRate() != null) {
            Double overTimeRate = BigDecimal.valueOf(realTimeDataDto.getPerformanceOvertimeRate())
                    .multiply(BigDecimal.valueOf(100))
                    .setScale(2, RoundingMode.HALF_UP)
                    .doubleValue();
            pickOverTimeRate = formatWithTwoDecimal(overTimeRate);
        }
        if (realTimeDataDto != null && realTimeDataDto.getPerformanceOvertimeRateCr() != null) {
            rate = BigDecimal.valueOf(realTimeDataDto.getPerformanceOvertimeRateCr())
                    .multiply(BigDecimal.valueOf(100))
                    .setScale(2, RoundingMode.HALF_UP)
                    .doubleValue() + "%";
        }

        MenuInfo menuInfo = buildMenuInfoBySacMenuNodeDto(menuCodeWithNodeMap.get(menuCode));
        menuInfo.getExtraInfoMap().put(DATA_VALUE, pickOverTimeRate);
        menuInfo.getExtraInfoMap().put(DATA_CR_VALUE, rate);
        return menuInfo;
    }

    private MenuInfo buildEffectiveOrderCntMenuInfoNew(PoiSgRealTimeDataDto realTimeDataDto, Map<String, SacMenuNodeDto> menuCodeWithNodeMap) {
        String menuCode = MenuCodeEnum.EFFECTIVE_ORDER_COUNT.getCode();
        String finOrdNum = DEFAULT_VALUE;
        String rate = DEFAULT_VALUE;

        if (realTimeDataDto != null && realTimeDataDto.getFinOrdNum() != null) {
            finOrdNum = realTimeDataDto.getFinOrdNum().toString();
        }
        if (realTimeDataDto != null && realTimeDataDto.getFinOrdNumCr() != null) {
            rate = BigDecimal.valueOf(realTimeDataDto.getFinOrdNumCr())
                    .multiply(BigDecimal.valueOf(100))
                    .setScale(2, RoundingMode.HALF_UP)
                    .doubleValue() + "%";
        }

        MenuInfo menuInfo = buildMenuInfoBySacMenuNodeDto(menuCodeWithNodeMap.get(menuCode));
        menuInfo.getExtraInfoMap().put(DATA_VALUE, finOrdNum);
        menuInfo.getExtraInfoMap().put(DATA_CR_VALUE, rate);
        return menuInfo;
    }

    private MenuInfo buildExpectedRevenueMenuInfoNew(PoiSgRealTimeDataDto realTimeDataDto, Map<String, SacMenuNodeDto> menuCodeWithNodeMap) {
        String menuCode = MenuCodeEnum.EXPECTED_REVENUE.getCode();
        String toPredictIncome = DEFAULT_VALUE;
        String rate = DEFAULT_VALUE;

        if (realTimeDataDto != null && realTimeDataDto.getToPredictIncome() != null) {
            toPredictIncome = formatWithTwoDecimal(realTimeDataDto.getToPredictIncome());
        }
        if (realTimeDataDto != null && realTimeDataDto.getToPredictIncomeCr() != null) {
            rate = BigDecimal.valueOf(realTimeDataDto.getToPredictIncomeCr())
                    .multiply(BigDecimal.valueOf(100))
                    .setScale(2, RoundingMode.HALF_UP)
                    .doubleValue() + "%";
        }

        MenuInfo menuInfo = buildMenuInfoBySacMenuNodeDto(menuCodeWithNodeMap.get(menuCode));
        menuInfo.getExtraInfoMap().put(DATA_VALUE, toPredictIncome);
        menuInfo.getExtraInfoMap().put(DATA_CR_VALUE, rate);
        return menuInfo;
    }

    private MenuInfo buildTotalTurnoverMenuInfo(PoiSgRealTimeDataDto realTimeDataDto, Map<String, SacMenuNodeDto> menuCodeWithNodeMap) {
        String menuCode = MenuCodeEnum.TOTAL_TURNOVER.getCode();
        String totalTurnover = DEFAULT_VALUE;

        if (realTimeDataDto != null && realTimeDataDto.getOpenAmtGtv() != null) {
            totalTurnover = formatWithTwoDecimal(realTimeDataDto.getOpenAmtGtv());
        }

        MenuInfo menuInfo = buildMenuInfoBySacMenuNodeDto(menuCodeWithNodeMap.get(menuCode));
        menuInfo.getExtraInfoMap().put(DATA_VALUE, totalTurnover);
        return menuInfo;
    }

    private MenuInfo buildPickOverTimeRateMenuInfo(PoiSgRealTimeDataDto realTimeDataDto, Map<String, SacMenuNodeDto> menuCodeWithNodeMap) {
        String menuCode = MenuCodeEnum.PICK_OVERTIME_RATE.getCode();
        String pickOverTimeRate = DEFAULT_VALUE;

        if (realTimeDataDto != null && realTimeDataDto.getPerformanceOvertimeRate() != null) {
            Double overTimeRate = BigDecimal.valueOf(realTimeDataDto.getPerformanceOvertimeRate())
                    .multiply(BigDecimal.valueOf(100))
                    .setScale(2, RoundingMode.HALF_UP)
                    .doubleValue();
            pickOverTimeRate = formatWithTwoDecimal(overTimeRate);
        }

        MenuInfo menuInfo = buildMenuInfoBySacMenuNodeDto(menuCodeWithNodeMap.get(menuCode));
        menuInfo.getExtraInfoMap().put(DATA_VALUE, pickOverTimeRate);
        return menuInfo;
    }

    private List<MenuInfo> buildRevenueMenuInfos(Long tenantId, Long poiId, PoiSgRealTimeDataDto realTimeDataDto,
                                                 Map<String, SacMenuNodeDto> menuCodeWithNodeMap, Map<String, Boolean> dataQueryResultMap) {
        // 获取有效订单量 + 预计收入
        RevenueInfo revenueInfo = getRevenueInfoAndFillResultMap(tenantId, poiId, realTimeDataDto, dataQueryResultMap);

        Set<String> menuCodes = menuCodeWithNodeMap.keySet();
        String revenueMenuCode = MenuCodeEnum.EXPECTED_REVENUE.getCode();
        String orderCountMenuCode = MenuCodeEnum.EFFECTIVE_ORDER_COUNT.getCode();

        List<MenuInfo> revenueMenuInfos = new ArrayList<>();
        // 构造预计收入菜单：有预计收入权限，且获取到预计收入公式成功，才展示模块
        if (menuCodes.contains(revenueMenuCode)) {
            MenuInfo menuInfo = buildMenuInfoBySacMenuNodeDto(menuCodeWithNodeMap.get(revenueMenuCode));
            // 填充业务数据：预计收入
            Map<String, Object> extraInfoMap = menuInfo.getExtraInfoMap();
            extraInfoMap.put(DATA_VALUE, revenueInfo.getRevenue());
            // 填充业务数据：预计收入提示
            if (revenueInfo.getRevenueTips() != null) {
                extraInfoMap.put(DESC, revenueInfo.getRevenueTips());
            }
            revenueMenuInfos.add(menuInfo);
        }

        // 构造有效订单菜单
        if (menuCodes.contains(orderCountMenuCode)) {
            MenuInfo menuInfo = buildMenuInfoBySacMenuNodeDto(menuCodeWithNodeMap.get(orderCountMenuCode));
            // 填充业务数据：有效订单数
            Map<String, Object> extraInfoMap = menuInfo.getExtraInfoMap();
            extraInfoMap.put(DATA_VALUE, revenueInfo.getCompleteOrderCount());
            revenueMenuInfos.add(menuInfo);
        }

        return revenueMenuInfos;
    }

    private RevenueInfo getRevenueInfoAndFillResultMap(Long tenantId, Long poiId, PoiSgRealTimeDataDto realTimeDataDto,
                                                         Map<String, Boolean> dataQueryResultMap) {
        RevenueInfo revenueInfo;
        try {
            // 数据来源于数仓
            if (revenueFromSgData(tenantId, poiId)) {
                revenueInfo = buildRevenueInfoFromSgData(realTimeDataDto);
                log.info("预计收入+订单量来自数仓 poiId:{},revenueInfo:{}", poiId, revenueInfo);
            }
            // 数据来源于结算
            else {
                revenueInfo = getRevenueInfoFromSettlementAndFillResultMap(tenantId, poiId, dataQueryResultMap);
                log.info("预计收入+订单量来自结算 poiId:{},revenueInfo:{}", poiId, revenueInfo);
            }
        }
        catch (Exception e) {
            log.error("获取预计收入+订单量失败 poiId:{}", poiId, e);
            revenueInfo = new RevenueInfo();
            revenueInfo.setRevenue(DEFAULT_VALUE);
            revenueInfo.setCompleteOrderCount(DEFAULT_VALUE);
            dataQueryResultMap.put(MenuCodeEnum.EXPECTED_REVENUE.getCode(), false);
            dataQueryResultMap.put(MenuCodeEnum.EFFECTIVE_ORDER_COUNT.getCode(), false);
        }
        return revenueInfo;
    }

    /**
     * 数据是否来自数仓
     * 门店不涉及摊位结算 或 (非摊主账号 && 租户在mcc中)
     *
     * @param tenantId
     * @return
     */
    private boolean revenueFromSgData(Long tenantId, Long poiId) {
        // 获取门店结算配置
        Long todayBeginMills = DateUtils.cutMillisAtBeginOfDay(new Date());
        BoothSettleModeDto boothSettleModeDto = tenantWrapper.getBoothSettlementConfig(tenantId, poiId, todayBeginMills);
        boolean needSettle = boothSettleModeDto != null && YesNoEnum.isYes(boothSettleModeDto.getNeedBoothSettle());
        if (!needSettle) {
            return true;
        }
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return AccountTypeEnum.BOOTH.getValue() != user.getAccountType()
                && MccConfigUtil.getNewRevenueTenantIds().contains(String.valueOf(tenantId));
    }

    private String formatWithTwoDecimal(Double money) {
        DecimalFormat df = new DecimalFormat("#0.00");
        df.setRoundingMode(RoundingMode.HALF_UP);
        return df.format(money);
    }

    /**
     * 通过数仓侧数据构造营收信息
     *
     * @param realTimeDataDto
     * @return
     */
    private RevenueInfo buildRevenueInfoFromSgData(PoiSgRealTimeDataDto realTimeDataDto) {
        // 预计收入
        String revenue = DEFAULT_VALUE;
        if (realTimeDataDto != null && realTimeDataDto.getToPredictIncome() != null) {
            revenue = formatWithTwoDecimal(realTimeDataDto.getToPredictIncome());
        }
        // 有效订单数量
        String completeOrderCount = DEFAULT_VALUE;
        if (realTimeDataDto != null && realTimeDataDto.getFinOrdNum() != null) {
            completeOrderCount = String.valueOf(realTimeDataDto.getFinOrdNum());
        }

        // 构造返回值
        RevenueInfo revenueInfo = new RevenueInfo();
        revenueInfo.setRevenue(revenue);
        revenueInfo.setCompleteOrderCount(completeOrderCount);
        revenueInfo.setRevenueTips(ConfigUtilAdapter.getString("merchantReceiveAmtTips", ""));
        return revenueInfo;
    }

    /**
     * 通过结算侧数据构造营收信息，并填充结果map
     *
     * @param tenantId
     * @param poiId
     * @param dataQueryResultMap
     * @return
     */
    private RevenueInfo getRevenueInfoFromSettlementAndFillResultMap(Long tenantId, Long poiId, Map<String, Boolean> dataQueryResultMap) {
        String revenueMenuCode = MenuCodeEnum.EXPECTED_REVENUE.getCode();
        String orderCountMenuCode = MenuCodeEnum.EFFECTIVE_ORDER_COUNT.getCode();

        String revenue = DEFAULT_VALUE;
        String revenueTips = null;
        String completeOrderCount = DEFAULT_VALUE;

        try {
            Long boothId = authThriftWrapper.getBoothIdByCurrentUser();
            RevenueOverviewVO revenueOverviewVO = settlementCurrentRevenueWrapper.overview(tenantId, poiId, boothId);
            dataQueryResultMap.put(revenueMenuCode, true);
            dataQueryResultMap.put(orderCountMenuCode, true);

            // 对数据赋值
            revenueTips = revenueOverviewVO.getRevenueTips();
            if (revenueOverviewVO.getTotalRevenue() != null) {
                revenue = formatWithTwoDecimal(MoneyUtils.centToYuan(revenueOverviewVO.getTotalRevenue()));
            }
            if (revenueOverviewVO.getCompleteOrderCount() != null) {
                completeOrderCount = revenueOverviewVO.getCompleteOrderCount().toString();
            }
        }
        catch (Exception e) {
            log.error("获取预计收入、有效订单数异常 tenantId:{},poiId:{}", tenantId, poiId, e);
            dataQueryResultMap.put(revenueMenuCode, false);
            dataQueryResultMap.put(orderCountMenuCode, false);
        }

        // 构造返回值
        RevenueInfo revenueInfo = new RevenueInfo();
        revenueInfo.setRevenue(revenue);
        revenueInfo.setCompleteOrderCount(completeOrderCount);
        revenueInfo.setRevenueTips(revenueTips);
        return revenueInfo;
    }

    /**
     * 获取业态枚举
     *
     * @param tenantId
     * @return
     */
    private ModeEnum getMode(Long tenantId) {
        TenantBizModeEnum tenantBizModeEnum = tenantWrapper.getTenantBizMode(tenantId);
        if (tenantBizModeEnum != null) {
            BizModeEnum bizModeEnum = BizModeEnum.ofStrCodeNullable(tenantBizModeEnum.getCode());
            if (bizModeEnum != null && bizModeEnum.getMode() == ModeEnum.B) {
                return ModeEnum.B;
            }
        }
        return ModeEnum.MARKET;
    }

    @Data
    public static class RevenueInfo {
        /**
         * 预计收入
         */
        private String revenue;
        /**
         * 预计收入文案
         */
        private String revenueTips;
        /**
         * 有效订单量
         */
        private String completeOrderCount;
    }

}