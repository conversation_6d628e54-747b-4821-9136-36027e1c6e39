package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.labor;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.shangou.bizmng.labor.api.training.TOnboardTrainingService;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/3/5 11:40
 **/

@Rhino
@Slf4j
public class LaborManagementServiceWrapper {
    @Resource
    private TOnboardTrainingService  tOnboardTrainingService;

    @Degrade(rhinoKey = "LaborManagementServiceWrapper.batchQueryTrainingResult", fallBackMethod = "batchQueryTrainingResultFallback", timeoutInMilliseconds = 1000)
    @MethodLog(logRequest = true, logResponse = true)
    public Map<Long, Boolean> batchQueryTrainingResult(Long tenantId, List<Long> accountIds) {
        log.info("start invoke tOnboardTrainingService.queryAccountTrainingFinishResult, accountId: {}", accountIds);
        TResult<Map<Long, Boolean>> tResult = tOnboardTrainingService.queryAccountTrainingFinishResult(tenantId, accountIds);
        log.info("end invoke tOnboardTrainingService.queryAccountTrainingFinishResult, tResult: {}", tResult);

        if (tResult == null || !tResult.isSuccess()) {
            throw new ThirdPartyException("查询员工培训结果失败");
        }

        return Optional.ofNullable(tResult.getData()).orElse(Collections.emptyMap());
    }

    public Map<Long, Boolean> batchQueryTrainingResultFallback(Long tenantId, List<Long> accountIds) {
        log.info("查询培训结果接口发生降级, accountIds: {}", accountIds);
        return Collections.emptyMap();
    }
}
