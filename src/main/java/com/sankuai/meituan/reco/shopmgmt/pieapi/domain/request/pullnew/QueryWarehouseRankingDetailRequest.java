package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pullnew;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/9/22
 */
@TypeDoc(
        description = "查询指定部门id排行榜信息",
        authors = {
                "liuxun"
        },
        version = "V1.0"
)
@Data
@ApiModel("查询指定部门id排行榜信息")
public class QueryWarehouseRankingDetailRequest {

    @FieldDoc(
            description = "榜单类型，区域榜0，门店榜1，个人榜2"
    )
    @ApiModelProperty(value = "榜单类型，区域榜0，门店榜1，个人榜2")
    private int rankType;

    private Integer targetId;

    @FieldDoc(
            description = "开始时间"
    )
    @ApiModelProperty(value = "开始时间")
    private String startTimeStr;

    @FieldDoc(
            description = "截止时间"
    )
    @ApiModelProperty(value = "截止时间")
    private String endTimeStr;


    @FieldDoc(
            description = "筛选部门层级，全国/城市/区域/门店"
    )
    @ApiModelProperty(value = "筛选部门层级，全国/城市/区域/门店")
    private Integer departmentType;

    @FieldDoc(
            description = "筛选部门id"
    )
    @ApiModelProperty(value = "筛选部门id")
    private Integer departmentId;

    @FieldDoc(
            description = "数据类型，0-首单量，1-二单量，3-好友数，4-会员卡，5-送达单量"
    )
    @ApiModelProperty(value = "数据类型，0-首单量，1-二单量，3-好友数，4-会员卡，5-送达单量")
    private Integer dataType;

}
