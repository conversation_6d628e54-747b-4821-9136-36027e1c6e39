package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2025/3/27
 * @Description
 */
@TypeDoc(
        description = "类目临期推荐规则请求"
)
@ApiModel("类目临期推荐规则请求")
@Getter
@Setter
@ToString
public class ChannelAdventRuleRequest {
    @FieldDoc(
            description = "渠道id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道id")
    @NotNull
    private Integer channelId;

    @FieldDoc(
            description = "渠道类目id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道类目id")
    private String channelCategoryId;
}
