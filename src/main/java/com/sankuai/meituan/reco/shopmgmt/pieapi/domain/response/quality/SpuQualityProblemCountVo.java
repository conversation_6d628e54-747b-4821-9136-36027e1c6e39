package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.quality;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.product.dto.SpuQualityProblemCountDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/9/20
 */
@TypeDoc(
        name = "商品问题汇总",
        description = "商品问题汇总"
)
@Data
public class SpuQualityProblemCountVo {

    @FieldDoc(
            description = "牵牛花权益码"
    )
    @ApiModelProperty(name = "牵牛花权益码")
    private int ruleCode;

    @FieldDoc(
            description = "问题数"
    )
    @ApiModelProperty(name = "问题数")
    private int count;

    public static SpuQualityProblemCountVo of(SpuQualityProblemCountDto countDto) {
        SpuQualityProblemCountVo countVo = new SpuQualityProblemCountVo();
        countVo.setCount(countDto.getCount());
        countVo.setRuleCode(countDto.getRuleCode());
        return countVo;
    }
}
