package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;
// Copyright (C) 2019 Meituan
// All rights reserved

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.Resource;

import org.apache.commons.lang3.time.DateUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.meituan.linz.boot.util.Fun;
import com.meituan.shangou.saas.resource.management.dto.AppraiseResultWithIndicatorDTO;
import com.meituan.shangou.saas.resource.management.dto.appraise.AppraisePreviewDTO;
import com.meituan.shangou.saas.resource.management.dto.appraise.IndicatorDetailDTO;
import com.meituan.shangou.saas.resource.management.dto.appraise.request.AppraisePreviewRequest;
import com.meituan.shangou.saas.resource.management.dto.appraise.request.AppraiseResultQueryRequest;
import com.meituan.shangou.saas.resource.management.dto.appraise.request.IndicatorDetailQueryRequest;
import com.meituan.shangou.saas.resource.management.dto.appraise.response.AppraisePreviewResponse;
import com.meituan.shangou.saas.resource.management.dto.appraise.response.AppraiseResultWithIndicatorResponse;
import com.meituan.shangou.saas.resource.management.dto.appraise.response.IndicatorDetailResponse;
import com.meituan.shangou.saas.resource.management.dto.bonus.request.BonusIdRequest;
import com.meituan.shangou.saas.resource.management.thrift.BonusThriftService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.appraise.IndicatorDetailRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.appraise.AppraiseResultHistoryByPlanTypeVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.appraise.AppraiseResultHistoryResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.appraise.IndicatorDetailVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.appraise.IndicatorDetailsResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.management.MerchantExaminationResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;

import lombok.extern.slf4j.Slf4j;

/**
 * @author: <EMAIL>
 * @class: ResourceManagementWrapper
 * @date: 2020-07-07 19:59:09
 * @desc: 资源管理wrapper
 */
@Slf4j
@Service
public class ResourceManagementWrapper {

    @Resource
    private AppraiseThriftAdapter appraiseThriftAdapter;

    @Resource
    private BonusThriftService bonusThriftService;

    @Resource
    private AuthThriftWrapper authThriftWrapper;


    public CommonResponse<MerchantExaminationResponse> getPreviewForUser(IdentityInfo identityInfo){
        User user = identityInfo.getUser();
        AppraisePreviewRequest request = new AppraisePreviewRequest();
        request.setPoiId(identityInfo.getStoreId());
        request.setTenantId(user.getTenantId());
        AppraisePreviewResponse response = appraiseThriftAdapter.previewEntityAppraisePlan(request);
        if (response.getStatus().getCode() != 0) {
            return CommonResponse.fail(-1, response.getStatus().getMessage());
        }

        List<String> permissionCodes = queryAuthCodes(response.getPreview());;

        MerchantExaminationResponse ret = new MerchantExaminationResponse(response.getPreview(), permissionCodes);
        return CommonResponse.success(ret);
    }

    private List<String> queryAuthCodes(AppraisePreviewDTO preview) {
        if (MccConfigUtil.useSacAuthenticationV2()) {
            List<String> authCodes = buildAuthCodes(preview);
            return authThriftWrapper.queryAuthorizedCodes(authCodes);
        }
        else {
            return authThriftWrapper.getCurrentAccountAllPermissionCodes();
        }
    }

    private List<String> buildAuthCodes(AppraisePreviewDTO preview) {
        if (preview == null) {
            return Collections.emptyList();
        }
        return preview.getPlanPreviewList().stream()
                .filter(p -> Objects.nonNull(p.getDisplayRules()))
                .flatMap(p -> p.getDisplayRules().stream())
                .filter(rule -> Objects.nonNull(rule.getAuthCodes()))
                .flatMap(rule -> rule.getAuthCodes().stream())
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }


    /**
     *查询指标的明细数据
     * @param req
     * @return
     */
    public CommonResponse<IndicatorDetailsResponse> queryIndicatorDetail(IdentityInfo identityInfo, IndicatorDetailRequest req) {

        IndicatorDetailResponse response = null;
        if (Integer.valueOf(1).equals(req.getType())) {
            response = queryPlanIndicatorDetails(identityInfo, req);
        } else {
            response = queryBonusIndicatorDetails(identityInfo.getUser().getTenantId(), req.getPlanId());
        }

        if (response.getStatus().getCode() != 0) {
            return CommonResponse.fail(-1, response.getStatus().getMessage());
        }

        List<IndicatorDetailDTO> indicatorDetails = response.getIndicatorDetails();

        IndicatorDetailsResponse resp = new IndicatorDetailsResponse();
        resp.setIndicatorDetails(ConverterUtils.convertList(indicatorDetails, IndicatorDetailVO::new));
        return CommonResponse.success(resp);
    }



    private IndicatorDetailResponse queryPlanIndicatorDetails(IdentityInfo identityInfo, IndicatorDetailRequest req) {
        IndicatorDetailQueryRequest request = new IndicatorDetailQueryRequest();
        request.setTenantId(identityInfo.getUser().getTenantId());
        request.setPoiId(req.getPoiId());
        request.setPlanId(req.getPlanId());

        try {
            request.setStartTime(DateUtils.parseDate(req.getStartDate(), "yyyy-MM-dd").getTime());
            request.setEndTime(DateUtils.parseDate(req.getEndDate(), "yyyy-MM-dd").getTime());
            request.setStatisticalStartTime(DateUtils.parseDate(req.getStatisticalStartDate(), "yyyy-MM-dd").getTime());
            request.setStatisticalEndTime(DateUtils.parseDate(req.getStatisticalEndDate(), "yyyy-MM-dd").getTime());
        } catch (Exception e) {
            log.error("日期解析失败", e);
        }

        IndicatorDetailResponse response = appraiseThriftAdapter.queryIndicatorDetails(request);

        return response;
    }



    private IndicatorDetailResponse queryBonusIndicatorDetails(Long tenantId, Long bonusId) {
        BonusIdRequest request = new BonusIdRequest();
        request.setTenantId(tenantId);
        request.setBonusId(bonusId);
        IndicatorDetailResponse indicatorDetailResponse = bonusThriftService.queryBonusIndicatorDetails(request);

        return indicatorDetailResponse;
    }


    /**
     * 查询历史考核结果
     * @param tenantId
     * @param month
     * @return
     */
    public CommonResponse<AppraiseResultHistoryResponse> queryAppraiseResultHistory(Long tenantId, Long poiId, String month) {
        Date startDate = null;
        try {
            startDate = DateUtils.parseDate(month, "yyyy.MM");
        } catch (Exception e) {
            return CommonResponse.fail(-1, "日期格式错误");
        }

        //获取当月最后一天的时间
        Date endDate = DateUtils.addSeconds(DateUtils.addMonths(startDate, 1), -1);

        AppraiseResultQueryRequest req = new AppraiseResultQueryRequest();
        req.setTenantId(tenantId);
        req.setPoiIds(Arrays.asList(poiId));
        req.setStartTime(startDate.getTime());
        req.setEndTime(endDate.getTime());

        AppraiseResultWithIndicatorResponse response = appraiseThriftAdapter.queryAppraiseResult(req);

        if (response.getStatus().getCode() != 0) {
            return CommonResponse.fail(-1, response.getStatus().getMessage());
        }

        AppraiseResultHistoryResponse resp = new AppraiseResultHistoryResponse();

        if (CollectionUtils.isEmpty(response.getResults())) {
            resp.setPlanDetails(Lists.newArrayList());
        } else {

            List<String> permissionCodes = queryAuthCodes(response.getResults());
            Map<Integer, List<AppraiseResultWithIndicatorDTO>> groupByType = response.getResults().stream().filter(r -> r.getBonus() != null).collect(Collectors.groupingBy(r -> r.getBonus().getPlanType()));
            resp.setPlanDetails(ConverterUtils.convertList(Lists.newArrayList(groupByType.entrySet()), e -> new AppraiseResultHistoryByPlanTypeVO(e.getKey(), e.getValue(), permissionCodes)));
        }

        return CommonResponse.success(resp);
    }

    private List<String> queryAuthCodes(List<AppraiseResultWithIndicatorDTO> results) {

        if (MccConfigUtil.useSacAuthenticationV2()) {
            List<String> authCodeList = results.stream()
                    .filter(r -> Objects.nonNull(r.getDetails()))
                    .flatMap(r -> r.getDetails().stream())
                    .filter(detail -> Objects.nonNull(detail.getAuthCodes()))
                    .flatMap(detail -> detail.getAuthCodes().stream())
                    .collect(Collectors.toList());
            return authThriftWrapper.queryAuthorizedCodes(authCodeList);
        }
        else {
            return authThriftWrapper.getCurrentAccountAllPermissionCodes();
        }


    }


}
