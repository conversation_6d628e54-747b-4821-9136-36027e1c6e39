package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.account;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.request.SendBindMobileVerificationCodeRequest;
import com.sankuai.meituan.shangou.saas.common.aop.feature.Validatable;
import com.sankuai.meituan.shangou.saas.common.utils.AssertUtil;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/07/24
 */
@TypeDoc(
        description = "验证并绑定手机号请求"
)
@Data
public class VerifyInterfaceAndBindMobileReq implements Validatable {

    @FieldDoc(
            description = "web灵犀sdk获取的uuid"
    )
    private String uuid;

    @FieldDoc(
            description = "手机号"
    )
    private String mobile;

    @FieldDoc(
            description = "用户输入的短信验证码"
    )
    private String smscode;

    @Override
    public void validate() {
        AssertUtil.isTrue(StringUtils.isNotBlank(uuid), "uuid 参数非法");
        AssertUtil.isTrue(StringUtils.isNotBlank(mobile), "mobile 参数非法");
        AssertUtil.isTrue(StringUtils.isNotBlank(smscode), "smscode 参数非法");
    }

    public SendBindMobileVerificationCodeRequest toThriftRequest() {
        SendBindMobileVerificationCodeRequest request = new SendBindMobileVerificationCodeRequest();
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        User user = identityInfo.getUser();
        String os = identityInfo.getOs();
        request.setTenantId(user.getTenantId());
        request.setAccountId(user.getAccountId());
        request.setMobile(mobile);
        request.setSmscode(smscode);
        HttpServletRequest httpRequest =
                ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String ip = httpRequest.getHeader("x-real-ip");
        String ua = httpRequest.getHeader("user-agent");
        request.setUuid(uuid);
        request.setIp(ip);
        request.setUa(ua);
        request.setPlatform(os);
        request.setVersion(identityInfo.getAppVersion());
        return request;
    }

}
