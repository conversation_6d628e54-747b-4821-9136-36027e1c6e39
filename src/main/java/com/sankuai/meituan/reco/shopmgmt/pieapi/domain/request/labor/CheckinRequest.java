package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.ParamException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2022/10/19 16:51
 **/
@Data
public class CheckinRequest {
    @FieldDoc(description = "打卡位置-经度")
    @ApiModelProperty("打卡位置-经度")
    private String longitude;

    @FieldDoc(description = "打卡位置-纬度")
    @ApiModelProperty("打卡位置-纬度")
    private String latitude;

    public void valid() {
        try {
            BigDecimal longitude = new BigDecimal(this.longitude);
            BigDecimal Latitude = new BigDecimal(this.latitude);
        } catch (Exception e) {
           throw new ParamException("经纬度参数不合法");
        }
    }
}
