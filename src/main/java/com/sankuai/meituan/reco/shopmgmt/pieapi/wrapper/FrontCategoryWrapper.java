package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.google.common.collect.Lists;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.CreateFrontCategoryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.FrontCategoryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.FrontCategorySearchRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.FrontCategorySortRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.FrontCategoryTopRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.ModifyFrontCategoryLevelRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.ModifyFrontCategoryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.QueryFrontCategoryByPoiRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.FrontCategoryVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.ocms.thrift.category.dto.ChannelStoreFrontCategoryDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.category.dto.ChannelStoreQueryResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.Response;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.ChannelStoreCategoryThriftService;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreCategoryDetailDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.StoreCategoryQueryResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.StoreCategorySearchResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.service.StoreCategoryBizThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2022/6/23 7:31 下午
 **/

@Slf4j
@Service
public class FrontCategoryWrapper {

    @Autowired
    private ChannelStoreCategoryThriftService.Iface channelStoreCategoryThriftService;

    @Autowired
    private StoreCategoryBizThriftService storeCategoryBizThriftService;

    public CommonResponse<List<FrontCategoryVO>> queryByPoi(QueryFrontCategoryByPoiRequest request, User user) {

        try {
            ChannelStoreQueryResponse response = channelStoreCategoryThriftService.findChannelStoreCategory(request.to(user));
            if (response.getCode() != ResultCode.SUCCESS.getCode()) {
                log.error("查询店内分类异常， request [{}], response [{}].", request, response);
                return CommonResponse.fail(response.getCode(), response.getMsg());
            }
            List<FrontCategoryVO> frontCategoryVOList = categoryVOConvert(response.getChannelStoreCategoryList());
            return CommonResponse.success(frontCategoryVOList);
        } catch (TException e) {
            log.error("查询店内分类异常, request:{}", request, e);
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse createCategory(CreateFrontCategoryRequest request, User user) {

        try {
            Response response = channelStoreCategoryThriftService.createChannelStoreCategory(request.to(user));
            if (response.getCode() != ResultCode.SUCCESS.getCode()) {
                log.error("创建店内分类异常， request [{}], response [{}].", request, response);
                return CommonResponse.fail(response.getCode(), response.getMsg());
            }
            return CommonResponse.success(ResultCode.SUCCESS.getCode());
        } catch (TException e) {
            log.error("创建店内分类异常, request:{}", request, e);
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse modifyFrontCategory(ModifyFrontCategoryRequest request, User user) {

        try {
            Response response = channelStoreCategoryThriftService.changeChannelStoreCategory(request.to(user));
            if (response.getCode() != ResultCode.SUCCESS.getCode()) {
                log.error("修改店内分类名称异常， request [{}], response [{}].", request, response);
                return CommonResponse.fail(response.getCode(), response.getMsg());
            }
            return CommonResponse.success(ResultCode.SUCCESS.getCode());
        } catch (TException e) {
            log.error("修改店内分类名称异常, request:{}", request, e);
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse modifyFrontCategoryLevel(ModifyFrontCategoryLevelRequest request, User user) {
        //
        if (request.getLevel()==1) {
            try {
                Response response = channelStoreCategoryThriftService.upgradeChannelStoreCategory(request.toUpgrade(user));
                if (response.getCode() != ResultCode.SUCCESS.getCode()) {
                    log.error("店内分类升级异常， request [{}], response [{}].", request, response);
                    return CommonResponse.fail(response.getCode(), response.getMsg());
                }
                return CommonResponse.success(ResultCode.SUCCESS.getCode());
            } catch (TException e) {
                log.error("店内分类升级异常, request:{}", request, e);
                throw new CommonRuntimeException(e);
            }
        } else {
            try {
                Response response = channelStoreCategoryThriftService.degradeChannelStoreCategory(request.toDegrade(user));
                if (response.getCode() != ResultCode.SUCCESS.getCode()) {
                    log.error("店内分类降级级异常， request [{}], response [{}].", request, response);
                    return CommonResponse.fail(response.getCode(), response.getMsg());
                }
                return CommonResponse.success(ResultCode.SUCCESS.getCode());
            } catch (TException e) {
                log.error("店内分类降级级异常, request:{}", request, e);
                throw new CommonRuntimeException(e);
            }
        }
    }

    public CommonResponse deleteFrontCategory(FrontCategoryRequest request, User user) {
        try {
            Response response = channelStoreCategoryThriftService.deleteChannelStoreCategory(request.to(user));
            if (response.getCode() != ResultCode.SUCCESS.getCode()) {
                log.error("删除店内分类异常， request [{}], response [{}].", request, response);
                return CommonResponse.fail(response.getCode(), response.getMsg());
            }
            return CommonResponse.success(ResultCode.SUCCESS.getCode());
        } catch (TException e) {
            log.error("删除店内分类异常, request:{}", request, e);
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse<FrontCategoryVO> detail(FrontCategoryRequest request, User user) {
        try {
            StoreCategoryQueryResponse response = storeCategoryBizThriftService.queryStoreCategoryById(request.toQuery(user));
            if (response.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
                log.error("查询店内分类异常， request [{}], response [{}].", request, response);
                return CommonResponse.fail(response.getStatus().getCode(), response.getStatus().getMsg());
            }
            FrontCategoryVO frontCategoryVOList = FrontCategoryVO.fCategoryConvert(response.getStoreCategoryDetailDTO());
            return CommonResponse.success(frontCategoryVOList);
        } catch (TException e) {
            log.error("查询店内分类异常, request:{}", request, e);
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse resortCategory(FrontCategorySortRequest request, User user) {
        try {
            Response response = channelStoreCategoryThriftService.sortChannelStoreCategory(request.to(user));
            if (response.getCode() != ResultCode.SUCCESS.getCode()) {
                log.error("排序店内分类异常， request [{}], response [{}].", request, response);
                return CommonResponse.fail(response.getCode(), response.getMsg());
            }
            return CommonResponse.success(ResultCode.SUCCESS.getCode());
        } catch (TException e) {
            log.error("排序店内分类异常, request:{}", request, e);
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse<List<FrontCategoryVO>> searchCategory(FrontCategorySearchRequest request, User user) {
        try {
            StoreCategorySearchResponse response = storeCategoryBizThriftService.searchStoreCategory(request.to(user));
            if (response.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
                log.error("查询店内分类异常， request [{}], response [{}].", request, response);
                return CommonResponse.fail(response.getStatus().getCode(), response.getStatus().getMsg());
            }
            List<FrontCategoryVO> frontCategoryVOList = categoryVOBizConvert(response.getStoreCategoryDetailDTOList());
            return CommonResponse.success(frontCategoryVOList);
        } catch (TException e) {
            log.error("查询店内分类异常, request:{}", request, e);
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse topCategory(FrontCategoryTopRequest request, User user) {
        try {
            com.sankuai.meituan.shangou.empower.productbiz.client.response.CommonResponse response = storeCategoryBizThriftService.topStoreCategory(request.to(user));
            if (response.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
                log.error("修改店内分类名称异常， request [{}], response [{}].", request, response);
                return CommonResponse.fail(response.getStatus().getCode(), response.getStatus().getMsg());
            }
            return CommonResponse.success(ResultCode.SUCCESS.getCode());
        } catch (TException e) {
            log.error("修改店内分类名称异常, request:{}", request, e);
            throw new CommonRuntimeException(e);
        }
    }

    private List<FrontCategoryVO> categoryVOConvert(List<ChannelStoreFrontCategoryDTO> categoryDTOs) {
        if (CollectionUtils.isEmpty(categoryDTOs)) {
            return Lists.newArrayListWithCapacity(0);
        }
        //得到一级分类
        List<ChannelStoreFrontCategoryDTO> firstLevelCategorys = categoryDTOs.stream().filter(c -> c.getLevel() == 1).collect(Collectors.toList());
        //二级分类分组
        Map<Long, List<ChannelStoreFrontCategoryDTO>> groupByParentId = categoryDTOs.stream().collect(Collectors.groupingBy(ChannelStoreFrontCategoryDTO::getParentId));

        List<FrontCategoryVO> retCategoryVOs = Lists.newArrayListWithCapacity(firstLevelCategorys.size());
        for (ChannelStoreFrontCategoryDTO frontCategoryDTO : firstLevelCategorys) {
            FrontCategoryVO frontCategoryVO = FrontCategoryVO.fCategoryConvert(frontCategoryDTO);
            frontCategoryVO.setChildren(ConverterUtils.convertList(groupByParentId.get(frontCategoryDTO.getId()), FrontCategoryVO::fCategoryConvert));
            frontCategoryVO.setHasChildren(ConverterUtils.wrap(CollectionUtils.isNotEmpty(frontCategoryVO.getChildren())));
            retCategoryVOs.add(frontCategoryVO);
        }
        return retCategoryVOs;
    }

    private List<FrontCategoryVO> categoryVOBizConvert(List<StoreCategoryDetailDTO> storeCategoryDetailDTOList) {
        if (CollectionUtils.isEmpty(storeCategoryDetailDTOList)) {
            return Lists.newArrayListWithCapacity(0);
        }
        //得到一级分类
        List<StoreCategoryDetailDTO> firstLevelCategorys = storeCategoryDetailDTOList.stream().filter(c -> c.getLevel() == 1).collect(Collectors.toList());
        //二级分类分组
        Map<Long, List<StoreCategoryDetailDTO>> groupByParentId = storeCategoryDetailDTOList.stream()
                .collect(Collectors.groupingBy(StoreCategoryDetailDTO::getParentStoreCategoryId));

        List<FrontCategoryVO> retCategoryVOs = Lists.newArrayListWithCapacity(firstLevelCategorys.size());
        for (StoreCategoryDetailDTO frontCategoryDTO : firstLevelCategorys) {
            FrontCategoryVO frontCategoryVO = FrontCategoryVO.fCategoryConvert(frontCategoryDTO);
            frontCategoryVO.setChildren(ConverterUtils.convertList(groupByParentId.get(frontCategoryDTO.getStoreCategoryId()), FrontCategoryVO::fCategoryConvert));
            frontCategoryVO.setHasChildren(ConverterUtils.wrap(CollectionUtils.isNotEmpty(frontCategoryVO.getChildren())));
            retCategoryVOs.add(frontCategoryVO);
        }
        return retCategoryVOs;
    }

}
