package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.wavepick.converter;

import com.sankuai.meituan.reco.pickselect.thrift.wave.dto.WarehouseSeedItemSkuDTO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.WarehouseSeedSkuItemVO;
import org.mapstruct.Mapper;

/**
 * @Description:
 * @Author: zhangjian155
 * @Date: 2022/8/25 18:09
 */
@Mapper(componentModel = "spring", uses = {WarehouseSeedStoreItemConverter.class, WarehousePickItemSkuSpecConverter.class})
public abstract class WarehouseSeedItemSkuConverter {
    public abstract WarehouseSeedSkuItemVO conver2Vo(WarehouseSeedItemSkuDTO warehouseSeedItemSkuDTO);
}
