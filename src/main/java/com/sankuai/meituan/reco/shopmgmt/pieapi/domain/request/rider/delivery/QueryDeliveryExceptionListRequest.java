package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.delivery;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@TypeDoc(
        description = "查询配送异常列表请求"
)
@ApiModel("查询配送异常列表请求")
@Data
public class QueryDeliveryExceptionListRequest {
    @FieldDoc(
            description = "渠道id列表",
            requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "渠道id列表")
    private List<Integer> channelIds;

    @FieldDoc(
            description = "门店id",
            requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店id")
    private Long storeId;

    @FieldDoc(
            description = "异常类型",
            requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "异常类型")
    private List<Integer> exceptionTypeList ;

    @FieldDoc(
            description = "订单支付时间-开始日期",
            requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "订单支付时间-开始日期")
    private Long payStartTimeStamp;

    @FieldDoc(
            description = "订单支付时间-结束日期",
            requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "订单支付时间-结束日期")
    private Long payEndTimeStamp;

    @FieldDoc(
            description = "异常上报时间-开始日期",
            requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "异常上报时间-开始日期")
    private Long reportStartTimeStamp;

    @FieldDoc(
            description = "异常上报时间-结束日期",
            requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "异常上报时间-结束日期")
    private Long reportEndTimeStamp;

    @FieldDoc(
            description = "第几页", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "第几页", required = true)
    @NotNull(message = "页码不能为空")
    private Integer pageNum;

    @FieldDoc(
            description = "每页行数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "每页行数", required = true)
    @NotNull(message = "页大小不能为空")
    private Integer pageSize;
}
