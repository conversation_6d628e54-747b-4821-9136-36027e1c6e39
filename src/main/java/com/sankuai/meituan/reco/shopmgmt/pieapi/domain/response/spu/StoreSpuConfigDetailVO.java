package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.SpuStoreConfigDTO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TypeDoc(
        description = "商品门店配置详情VO"
)
@Data
public class StoreSpuConfigDetailVO {

    @FieldDoc(
            description = "配置类型"
    )
    @ApiModelProperty(value = "配置类型")
    private String configType;

    @FieldDoc(
            description = "配置类型名称"
    )
    @ApiModelProperty(value = "配置类型名称")
    private String configTypeName;

    @FieldDoc(
            description = "配置值"
    )
    @ApiModelProperty(value = "配置值")
    private String configValue;

    @FieldDoc(
            description = "配置描述"
    )
    @ApiModelProperty(value = "配置描述")
    private String comment;

    public static StoreSpuConfigDetailVO ofDTO(SpuStoreConfigDTO spuStoreConfigDTO) {
        StoreSpuConfigDetailVO storeSpuConfigDetailVO = new StoreSpuConfigDetailVO();
        storeSpuConfigDetailVO.setConfigType(spuStoreConfigDTO.getConfigType());
        storeSpuConfigDetailVO.setConfigTypeName(spuStoreConfigDTO.getConfigTypeName());
        storeSpuConfigDetailVO.setConfigValue(spuStoreConfigDTO.getConfigValue());
        storeSpuConfigDetailVO.setComment(spuStoreConfigDTO.getComment());
        return storeSpuConfigDetailVO;
    }
}
