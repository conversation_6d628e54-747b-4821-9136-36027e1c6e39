package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@TypeDoc(
        description = "查询待外拣货及采现结商品响应"
)
@Data
@ApiModel("查询待外拣货及采现结商品响应")
@NoArgsConstructor
public class QuerySkusForPickAndOutwardSourcingResponseVO {

    @FieldDoc(
            description = "待拣货及外采订单列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "待拣货及外采订单列表", required = true)
    List<OrderForPickAndOutwardSourcingVO> orders = new ArrayList<>();

    @FieldDoc(
            description = "待拣货及外采商品列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "待拣货及外采商品列表", required = true)
    List<SkuForPickAndOutwardSourcingVO> skus = new ArrayList<>();
}
