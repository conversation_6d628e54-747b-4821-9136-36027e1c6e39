package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/7/11
 * desc: 部分退款请求
 */
@TypeDoc(
        description = "部分退款请求"
)
@ApiModel("部分退款请求")
@Data
public class PartRefundRequest {

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道ID")
    private Integer channelId;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道订单号")
    private String channelOrderId;

    @FieldDoc(
            description = "部分退款原因", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "部分退款原因")
    @NotNull(message = "退款原因不能为空")
    private String reason;

    @FieldDoc(
            description = "部分原因code,如果是自定义原因传-1", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "部分原因code")
    public int reasonCode;

    @FieldDoc(
            description = "部分退款商品列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "部分退款商品列表")
    private List<PartRefundProductVO> partRefundProductList;

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID")
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "商品改库存为0列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品已售完列表")
    private List<RefundGoodsSoldOutVO> refundGoodsSoldOutVOList;

    @FieldDoc(
            description = "商品下架列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品售罄列表")
    private List<RefundGoodsTakenOffVO> refundGoodsTakenOffVOList;

}
