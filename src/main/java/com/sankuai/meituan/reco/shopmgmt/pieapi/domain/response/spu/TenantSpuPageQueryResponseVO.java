package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.PageInfoDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.TenantSpuPageQueryBizResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Title: TenantSpuPageQueryResponseVO
 * @Description: 租户商品SPU分页查询响应结果
 * @Author: wuyongjiang
 * @Date: 2022/8/25 15:03
 */
@TypeDoc(
        description = "租户商品SPU分页查询响应结果"
)
@Data
@ApiModel("租户商品SPU分页查询响应结果")
public class TenantSpuPageQueryResponseVO {
    @FieldDoc(
            description = "租户商品信息列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户商品信息列表", required = true)
    private List<TenantSpuVO> tenantSpuList;

    @FieldDoc(
            description = "分页信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "分页信息", required = true)
    private PageInfoVO pageInfo;

    public static TenantSpuPageQueryResponseVO convertBizResponse(TenantSpuPageQueryBizResponse response) {
        TenantSpuPageQueryResponseVO tenantSpuPageQueryResponseVO = new TenantSpuPageQueryResponseVO();
        tenantSpuPageQueryResponseVO.setTenantSpuList(response.getTenantSpuDTOList().stream().map(TenantSpuVO::convertFromBizDto).collect(Collectors.toList()));
        tenantSpuPageQueryResponseVO.setPageInfo(convertBiz(response.getPageInfoDTO()));
        return tenantSpuPageQueryResponseVO;
    }

    private static PageInfoVO convertBiz(PageInfoDTO pageInfoDTO) {
        if (Objects.isNull(pageInfoDTO)) {
            return null;
        }
        PageInfoVO pageInfoVO = new PageInfoVO();
        pageInfoVO.setPage(pageInfoDTO.getPage());
        pageInfoVO.setSize(pageInfoDTO.getSize());
        pageInfoVO.setTotalPage(pageInfoDTO.getTotalPage());
        pageInfoVO.setTotalSize(Optional.ofNullable(pageInfoDTO.getTotal()).map(Long::intValue).orElse(null));
        return pageInfoVO;
    }
}
