package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ChannelTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.converters.abnormal.AbnormalConverter;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.FrontCategorySimpleVO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelSpuDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.FrontCategoryWithPathDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.enums.ChannelAuditStatusEnum;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelAuditDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.PlatformSoldOutInfoDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreCategoryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Title: ChannelSpuVO
 * @Description: 渠道商品SPU信息
 * @Author: zhaolei12
 * @Date: 2020/5/16 1:08 下午
 */
@TypeDoc(
        description = "渠道商品SPU信息"
)
@Data
@ApiModel("渠道商品SPU信息")
public class ChannelSpuVO {

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID", required = true)
    private Long storeId;

    @FieldDoc(
            description = "渠道id  -1-线下 100-美团 200-饿了么 300-京东到家", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道id", required = true)
    private Integer channelId;

    @FieldDoc(
            description = "SPU编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "SPU编码", required = true)
    private String spuId;

    @FieldDoc(
            description = "渠道自定义SPU编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道自定义SPU编码", required = true)
    private String customSpuId;

    @FieldDoc(
            description = "渠道实体SPU编码", requiredness = Requiredness.NONE
    )
    @ApiModelProperty(name = "渠道实体SPU编码", required = false)
    private String channelSpuId;

    @FieldDoc(
            description = "上线状态 true-已上线 false-未上线", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "上线状态 true-已上线 false-未上线", required = true)
    private Boolean online;

    @FieldDoc(
            description = "上架状态 1-上架  2-下架", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "上架状态 1-上架  2-下架", required = true)
    private Integer spuStatus;

    @FieldDoc(
            description = "门店商品多分类", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店商品多分类")
    private List<FrontCategorySimpleVO> frontCategories = new ArrayList<>();

    @FieldDoc(
            description = "门店商品多分类", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道类目信息")
    private ChannelCategoryVO channelCategory;

    @FieldDoc(
            description = "门店商品多分类", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道品牌信息")
    private ChannelBrandVO channelBrand;

    @FieldDoc(
            description = "渠道商品SKU信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道商品SKU信息", required = true)
    private List<ChannelSkuVO> channelSkuList;

    @FieldDoc(
            description = "可售状态"
    )
    @ApiModelProperty(name = "可售状态")
    private Integer allowSale;

    @FieldDoc(
            description = "平台停售状态"
    )
    @ApiModelProperty(name = "平台停售状态")
    private Integer stopSellingStatus;

    @FieldDoc(
            description = "审核状态"
    )
    @ApiModelProperty(name = "审核状态")
    private Integer auditStatus;

    @FieldDoc(
            description = "审核信息"
    )
    @ApiModelProperty(name = "审核信息")
    private String auditStatusComment;

    @FieldDoc(
            description = "合规审核状态"
    )
    @ApiModelProperty(name = "合规审核状态")
    private Integer normAuditStatus;

    @FieldDoc(
            description = "合规审核信息"
    )
    @ApiModelProperty(name = "合规审核信息")
    private String normAuditComment;

    @FieldDoc(
            description = "合并后审核状态 "
    )
    private Integer mergedAuditStatus;

    @FieldDoc(
            description = "合并后的审核信息"
    )
    private String mergedAuditComment;

    @FieldDoc(
            description = "是否为平台下架"
    )
    @ApiModelProperty(name = "是否为平台下架")
    private boolean platformSoldOut;

    @FieldDoc(
            description = "平台下架消息"
    )
    @ApiModelProperty(name = "平台下架消息")
    private PlatformSoldOutInfoVO platformSoldOutInfo;

    @FieldDoc(
            description = "售后服务类型，目前仅抖音渠道使用"
    )
    @ApiModelProperty(name = "售后服务，目前仅抖音渠道使用")
    private String afterSaleServiceType;

    @FieldDoc(
            description = "资质列表，目前仅抖音渠道使用"
    )
    @ApiModelProperty(name = "资质列表，目前仅抖音渠道使用")
    private List<QualificationVO> qualificationList;

    public static List<ChannelSpuVO> ofDTOList(List<ChannelSpuDTO> channelSpuDTOList, PlatformSoldOutInfoDTO soldOutInfoDTO) {
        if (CollectionUtils.isEmpty(channelSpuDTOList)) {
            return Lists.newArrayList();
        }

        return channelSpuDTOList.stream().filter(Objects::nonNull)
                .map(channelSpuDTO -> ChannelSpuVO.ofDTO(channelSpuDTO, soldOutInfoDTO))
                .collect(Collectors.toList())
                .stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static ChannelSpuVO ofDTO(ChannelSpuDTO channelSpuDTO, PlatformSoldOutInfoDTO soldOutInfoDTO) {
        if (channelSpuDTO == null) {
            return null;
        }
        ChannelSpuVO channelSpuVO = new ChannelSpuVO();
        channelSpuVO.setStoreId(channelSpuDTO.getStoreId());
        channelSpuVO.setSpuId(channelSpuDTO.getSpuId());
        channelSpuVO.setCustomSpuId(channelSpuDTO.getCustomSpuId());
        channelSpuVO.setChannelId(channelSpuDTO.getChannelId());
        channelSpuVO.setOnline(channelSpuDTO.getOnline());
        channelSpuVO.setSpuStatus(channelSpuDTO.getSpuStatus());
        channelSpuVO.setFrontCategories(convertFrontCategories(channelSpuDTO.getFrontCategories()));
        channelSpuVO.setChannelCategory(ChannelCategoryVO.ofDTO(channelSpuDTO.getChannelCategory()));
        channelSpuVO.setChannelBrand(ChannelBrandVO.ofDTO(channelSpuDTO.getChannelBrand()));
        channelSpuVO.setChannelSkuList(ChannelSkuVO.ofDTOList(channelSpuDTO.getChannelSkuList()));

        // 商品审核状态，宽限期驳回对外只展示一个状态
        int mtAuditStatus = channelSpuDTO.getAuditStatus() == null ? 0 : channelSpuDTO.getAuditStatus();
        ChannelAuditStatusEnum channelAuditStatusEnum = ChannelAuditStatusEnum.ofCode(mtAuditStatus);
        channelSpuVO.setAllowSale(channelAuditStatusEnum != null ? channelAuditStatusEnum.toAllowSale() : 1);
        if (ChannelAuditStatusEnum.GRACE_PERIOD_REJECTED_EXPECT.equals(channelAuditStatusEnum)) {
            channelSpuVO.setAuditStatus(ChannelAuditStatusEnum.GRACE_PERIOD_REJECTED.getCode());
        } else {
            channelSpuVO.setAuditStatus(mtAuditStatus);
        }
        

        channelSpuVO.setAuditStatusComment(channelSpuDTO.getAuditComment());
        channelSpuVO.setNormAuditStatus(channelSpuDTO.getNormAuditStatus());
        channelSpuVO.setNormAuditComment(channelSpuDTO.getNormAuditComment());

        //商品停售状态
        channelSpuVO.setStopSellingStatus(channelSpuDTO.getStopSellingStatus());
        channelSpuVO.setPlatformSoldOut(channelSpuDTO.isPlatformSoldOut());
        if (channelSpuDTO.getChannelId().equals(ChannelTypeEnum.MEITUAN.getChannelId())) {
            channelSpuVO.setPlatformSoldOutInfo(PlatformSoldOutInfoVO.of(soldOutInfoDTO));
        }
        channelSpuVO.setChannelSpuId(channelSpuDTO.getChannelSpuId());
        if (CollectionUtils.isNotEmpty(channelSpuDTO.getDouyinQualificationList())) {
            channelSpuVO.setQualificationList(QualificationVO.ofOcmsDTOList(channelSpuDTO.getDouyinQualificationList()));
        }
        channelSpuVO.setAfterSaleServiceType(channelSpuDTO.getDouyinAfterSaleServiceType());
        return channelSpuVO;
    }

    private static List<FrontCategorySimpleVO> convertFrontCategories(List<FrontCategoryWithPathDTO> frontCategoryWithPathDTOList) {
        if (CollectionUtils.isEmpty(frontCategoryWithPathDTOList)) {
            return Lists.newArrayList();
        }
        List<FrontCategorySimpleVO> frontCategorySimpleVOList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(frontCategoryWithPathDTOList)) {
            for (FrontCategoryWithPathDTO frontCategoryWithPathDTO : frontCategoryWithPathDTOList) {
                FrontCategorySimpleVO frontCategorySimpleVO = new FrontCategorySimpleVO(frontCategoryWithPathDTO);
                frontCategorySimpleVOList.add(frontCategorySimpleVO);
            }
        }
        return frontCategorySimpleVOList;
    }

    public static List<ChannelSpuVO> ofBizDTOList(List<com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelSpuDTO> channelSpuDTOList,
                                                  PlatformSoldOutInfoDTO soldOutInfoDTO, List<ChannelAuditDTO> channelAuditDTOList) {
        if (CollectionUtils.isEmpty(channelSpuDTOList)) {
            return Lists.newArrayList();
        }

        Map<Integer, ChannelAuditDTO> channelAuditDTOMap = Fun.toMapQuietly(channelAuditDTOList, ChannelAuditDTO::getChannelId);

        return channelSpuDTOList.stream().filter(Objects::nonNull)
                .map(channelSpuDTO -> ChannelSpuVO.ofBizDTO(channelSpuDTO, soldOutInfoDTO, channelAuditDTOMap.get(channelSpuDTO.getChannelId())))
                .collect(Collectors.toList())
                .stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static ChannelSpuVO ofBizDTO(com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelSpuDTO channelSpuDTO,
                                        PlatformSoldOutInfoDTO soldOutInfoDTO, ChannelAuditDTO channelAuditDTO) {
        if (channelSpuDTO == null) {
            return null;
        }
        ChannelSpuVO channelSpuVO = new ChannelSpuVO();
        channelSpuVO.setStoreId(channelSpuDTO.getStoreId());
        channelSpuVO.setSpuId(channelSpuDTO.getSpuId());
        channelSpuVO.setCustomSpuId(channelSpuDTO.getCustomSpuId());
        channelSpuVO.setChannelId(channelSpuDTO.getChannelId());
        channelSpuVO.setOnline(channelSpuDTO.getOnline());
        channelSpuVO.setSpuStatus(channelSpuDTO.getSpuStatus());
        channelSpuVO.setFrontCategories(convertBizFrontCategories(channelSpuDTO.getFrontCategories()));
        channelSpuVO.setChannelCategory(ChannelCategoryVO.ofBizDTO(channelSpuDTO.getChannelCategory()));
        channelSpuVO.setChannelBrand(ChannelBrandVO.ofBizDTO(channelSpuDTO.getChannelBrand()));
        channelSpuVO.setChannelSkuList(ChannelSkuVO.ofBizDTOList(channelSpuDTO.getChannelSkuList()));

        // 商品审核状态，宽限期驳回对外只展示一个状态
        int mtAuditStatus = channelSpuDTO.getAuditStatus() == null ? 0 : channelSpuDTO.getAuditStatus();
        ChannelAuditStatusEnum channelAuditStatusEnum = ChannelAuditStatusEnum.ofCode(mtAuditStatus);
        channelSpuVO.setAllowSale(channelAuditStatusEnum != null ? channelAuditStatusEnum.toAllowSale() : 1);
        if (ChannelAuditStatusEnum.GRACE_PERIOD_REJECTED_EXPECT.equals(channelAuditStatusEnum)) {
            channelSpuVO.setAuditStatus(ChannelAuditStatusEnum.GRACE_PERIOD_REJECTED.getCode());
        } else {
            channelSpuVO.setAuditStatus(mtAuditStatus);
        }


        channelSpuVO.setAuditStatusComment(channelSpuDTO.getAuditComment());
        channelSpuVO.setNormAuditStatus(channelSpuDTO.getNormAuditStatus());
        channelSpuVO.setNormAuditComment(channelSpuDTO.getNormAuditComment());

        //商品停售状态
        channelSpuVO.setStopSellingStatus(channelSpuDTO.getStopSellingStatus());
        channelSpuVO.setPlatformSoldOut(channelSpuDTO.isPlatformSoldOut());
        if (channelSpuDTO.getChannelId().equals(ChannelTypeEnum.MEITUAN.getChannelId())) {
            channelSpuVO.setPlatformSoldOutInfo(PlatformSoldOutInfoVO.of(soldOutInfoDTO));
        }
        channelSpuVO.setChannelSpuId(channelSpuDTO.getChannelSpuId());
        if (CollectionUtils.isNotEmpty(channelSpuDTO.getDouyinQualificationList())) {
            channelSpuVO.setQualificationList(QualificationVO.ofBizDTOList(channelSpuDTO.getDouyinQualificationList()));
        }
        channelSpuVO.setAfterSaleServiceType(channelSpuDTO.getDouyinAfterSaleServiceType());
        if (channelAuditDTO != null) {
            channelSpuVO.setMergedAuditStatus(channelAuditDTO.getAuditStatus());
            channelSpuVO.setMergedAuditComment(AbnormalConverter.getMergedAuditComment(channelAuditDTO.getChannelId(), channelAuditDTO.getAuditStatus()));
        }
        return channelSpuVO;
    }

    private static List<FrontCategorySimpleVO> convertBizFrontCategories(List<StoreCategoryDTO> frontCategoryWithPathDTOList) {
        if (CollectionUtils.isEmpty(frontCategoryWithPathDTOList)) {
            return Lists.newArrayList();
        }
        List<FrontCategorySimpleVO> frontCategorySimpleVOList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(frontCategoryWithPathDTOList)) {
            for (StoreCategoryDTO frontCategoryWithPathDTO : frontCategoryWithPathDTOList) {
                FrontCategorySimpleVO frontCategorySimpleVO = new FrontCategorySimpleVO(frontCategoryWithPathDTO);
                frontCategorySimpleVOList.add(frontCategorySimpleVO);
            }
        }
        return frontCategorySimpleVOList;
    }

}
