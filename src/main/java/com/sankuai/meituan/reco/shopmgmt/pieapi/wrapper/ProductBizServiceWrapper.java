package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import com.meituan.linz.boot.util.Fun;
import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiStatusEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.ListResultData;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.MedicalStandardSpuQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.QueryAllAbnormalTypeListRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.BatchSkuReviewRecallApplyRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SkuReviewApplyListQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SkuReviewListQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.QueryFranchiseTenantSkuBySpuIdApiRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.SupplierSkuInfoQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.SupplierSkuInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.AbnormalRuleNodeVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.AbnormalTypeQueryResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.BatchSkuReviewRecallApplyResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.SkuReviewListQueryResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.MedicalStandardSpuAndControlFieldsVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.TenantSkuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.TenantSpuSimpleVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.facade.PoiServiceFacade;
import com.sankuai.meituan.reco.shopmgmt.pieapi.facade.item.AbnormalProductClient;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.auth.sdk.context.SessionContext;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.PermissionGroupTypeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.service.AuthThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.PermissionGroupVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryPermissionGroupRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryPermissionGroupResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ResponseCodeEnum;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreSpuBasicDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.QueryMedicalStandardProductRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.QueryStoreSpuBasicInfoRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.TenantSpuDetailBizRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.TenantSupplierSkuQueryRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.review.BatchRecallProductReviewRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.review.PageQueryReviewProductRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.QueryMedicalStandardProductResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.QueryStoreSpuBasicInfoResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.TenantSkuQueryBizResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.TenantSupplierSkuResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.review.BatchRecallProductReviewResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.review.PageQueryReviewProductResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.service.MedicalStandardThriftService;
import com.sankuai.meituan.shangou.empower.productbiz.client.service.ProductReviewBizThriftService;
import com.sankuai.meituan.shangou.empower.productbiz.client.service.StoreSpuBizThriftService;
import com.sankuai.meituan.shangou.empower.productbiz.client.service.TenantSpuControlThriftService;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import org.apache.commons.collections4.ListUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/8 11:19 AM
 */
@Service
@Slf4j
public class ProductBizServiceWrapper {

    @Resource
    private StoreSpuBizThriftService storeSpuBizThriftService;

    @Autowired
    private TenantSpuControlThriftService tenantSpuControlThriftService;

    @Autowired
    private AbnormalProductClient abnormalProductClient;

    @Autowired
    private ProductReviewBizThriftService productReviewBizThriftService;

    @Autowired
    private AuthThriftService.Iface authThriftService;

    @Autowired
    private PoiServiceFacade poiServiceFacade;

    @Resource
    private MedicalStandardThriftService medicalStandardThriftService;

    public StoreSpuBasicDTO queryStoreSpuBasicInfo(Long tenantId, Long storeId, String spuId, boolean forceMaster) throws BizException {
        QueryStoreSpuBasicInfoRequest request = new QueryStoreSpuBasicInfoRequest();
        request.setTenantId(tenantId);
        request.setStoreId(storeId);
        request.setSpu(spuId);

        QueryStoreSpuBasicInfoResponse response;
        try {
            if (forceMaster) {
                response = storeSpuBizThriftService.queryStoreSpuBasicInfoFromMaster(request);
            } else {
                response = storeSpuBizThriftService.queryStoreSpuBasicInfo(request);
            }
            if (response == null || ResultCode.SUCCESS.getCode() != response.getStatus().getCode()) {
                throw new BizException("queryStoreSpuBasicInfo failed, spuId:" + spuId);
            }
        } catch (TException e) {
            log.error("queryStoreSpuBasicInfo error, spuId:{}, msg:{}", spuId, e.getMessage(), e);
            throw new BizException("查询门店商品基础信息失败");
        }
        return response.getStoreSpu();
    }

    public CommonResponse<TenantSpuSimpleVO> queryFranchiseTenantSku(QueryFranchiseTenantSkuBySpuIdApiRequest request, User user) {
        try {
            TenantSpuDetailBizRequest rpcRequest = new TenantSpuDetailBizRequest();
            rpcRequest.setSpuId(request.getFranchiseeSpuId());
            rpcRequest.setTenantId(user.getTenantId());
            rpcRequest.setIncludeCoveredStores(true);
            TenantSkuQueryBizResponse response = tenantSpuControlThriftService.queryFranchiseTenantSkuList(rpcRequest);
            log.info("ProductBizServiceWrapper.queryFranchiseTenantSku, 查询必备商品规格, request:{}, user:{}, response:{}", request, user, response);
            return new CommonResponse<>(response.getStatus().getCode(), response.getStatus().getMsg(), buildTenantSpuSimpleVO(response));
        } catch (TException e) {
            throw new CommonRuntimeException(e);
        }
    }

    private TenantSpuSimpleVO buildTenantSpuSimpleVO(TenantSkuQueryBizResponse skuQueryResponse) {
        TenantSpuSimpleVO tenantSpuSimpleVO = new TenantSpuSimpleVO();
        tenantSpuSimpleVO.setTenantSkuVOList(TenantSkuVO.ofBizDTOList(skuQueryResponse.getTenantSkuDTOList()));
        // canCustomizeSpec字段目前只有菜大全使用，此处不返回
        return tenantSpuSimpleVO;
    }


    public CommonResponse<ListResultData<SupplierSkuInfoVO>> querySupplierSkuInfo(SupplierSkuInfoQueryRequest request) {
        try {
            TenantSupplierSkuQueryRequest req = new TenantSupplierSkuQueryRequest();
            req.setTenantId(SessionContext.getCurrentSession().getTenantId());
            req.setSupplierSkuIds(request.getSupplierSkuIds());
            req.setJoinAreaIds(request.getJoinAreaIds());
            TenantSupplierSkuResponse response = tenantSpuControlThriftService.querySupplierSkuInfo(req);
            ListResultData<SupplierSkuInfoVO> listResult = new ListResultData<>();
            listResult.setList(Fun.map(response.getTenantSupplierSkuList(),SupplierSkuInfoVO::build));
            return new CommonResponse<>(response.getStatus().getCode(), response.getStatus().getMsg(), listResult);
        } catch (TException e) {
            throw new CommonRuntimeException(e);
        }

    }

    public CommonResponse<AbnormalTypeQueryResponseVO> getAllAbnormalTypeList(QueryAllAbnormalTypeListRequest request) {
        Long merchantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        Long poiId = null;
        try{
            if (request != null){
                poiId = request.getStoreId();
            }

            AbnormalTypeQueryResponseVO response = new AbnormalTypeQueryResponseVO();
            List<AbnormalRuleNodeVO> allAbnormalRuleTree = abnormalProductClient.getAllAbnormalRuleTree(merchantId, poiId);
            if (CollectionUtils.isNotEmpty(allAbnormalRuleTree)){
                // 目前的需求暂时不需要子节点，去掉子节点
                allAbnormalRuleTree.forEach(abnormalRuleNodeVO -> abnormalRuleNodeVO.setChildren(null));
            }
            response.setAbnormalRuleNodes(allAbnormalRuleTree);
            return CommonResponse.success(response);
        } catch (BizException e) {
            log.error("查询所有异常类型列表业务异常， merchantId: {}， request:{}",  merchantId, request, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        } catch (Exception e){
            log.error("查询所有异常类型列表异常， merchantId: {}， request:{}", merchantId, request,  e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "系统异常");
        }
    }

    /**
     * 新品提报记录列表
     *
     * @param request
     * @return
     */
    public CommonResponse<SkuReviewListQueryResponse> queryProductReviewApplyList(SkuReviewApplyListQueryRequest request) {

        // 提报商品列表必传门店ID
        if (Objects.isNull(request.getStoreId()) || request.getStoreId() <= 0L) {
            return CommonResponse.fail(ResultCode.PARAM_ERR.getCode(), "门店ID无效");
        }

        User user = ApiMethodParamThreadLocal.getInstance().get().getUser();
        PageQueryReviewProductRequest pageQueryReviewProductRequest = request.convertToQueryReviewProductRequest(user);
        PageQueryReviewProductResponse response = productReviewBizThriftService.pageQueryReviewProduct(pageQueryReviewProductRequest);
        // 这里参考旧接口ocmsServiceWrapper.querySkuReviewApplyList，不判断code==0
        if (Objects.isNull(response) || Objects.isNull(response.getStatus())) {
            log.error("查询新品提报记录列表失败, request:{}", JacksonUtils.toJson(pageQueryReviewProductRequest));
            return CommonResponse.fail2(ResultCode.INTERNAL_SERVER_ERROR);
        }
        return new CommonResponse<>(response.getStatus().getCode(), response.getStatus().getMsg(),
                SkuReviewListQueryResponse.fromPageQueryReviewProductResponse(response));
    }

    /**
     * 总部商品审核列表
     *
     * @param request
     * @return
     */
    public CommonResponse<SkuReviewListQueryResponse> queryProductReviewList(SkuReviewListQueryRequest request) {
        User user = ApiMethodParamThreadLocal.getInstance().get().getUser();
        PageQueryReviewProductRequest pageQueryReviewProductRequest = request.convertToQueryReviewProductRequest(user);

        // 搜索和列表分开处理，如果指定搜索门店，则不需要查权限
        if (CollectionUtils.isEmpty(request.getStoreIdSet())) {
            try {
                // 查询账号的门店数据权限
                QueryPermissionGroupRequest permissionGroupRequest = new QueryPermissionGroupRequest();
                permissionGroupRequest.setTenantId(user.getTenantId());
                permissionGroupRequest.setAccountId(user.getAccountId());
                permissionGroupRequest.setType(PermissionGroupTypeEnum.POI.getValue());
                QueryPermissionGroupResponse permissionGroupResponse = authThriftService.queryPermissionGroupList(permissionGroupRequest);
                List<PermissionGroupVo> permissionGroupVos = new ArrayList<>();
                if (null != permissionGroupResponse
                        && null != permissionGroupResponse.getResult()
                        && permissionGroupResponse.getResult().getCode() == ResponseCodeEnum.SUCCESS.getValue()) {
                    permissionGroupVos = permissionGroupResponse.getPermissionGroupCodeList();
                }
                if (CollectionUtils.isNotEmpty(permissionGroupVos)) {
                    List<Long> storeIds = permissionGroupVos.stream()
                            .map(PermissionGroupVo::getCode)
                            .map(Long::valueOf)
                            .collect(Collectors.toList());
                    // 账号没有任何有权限的门店，返回空列表
                    if (CollectionUtils.isEmpty(storeIds)) {
                        return CommonResponse.success(SkuReviewListQueryResponse.buildEmptyResponse(request));
                    }

                    log.info("账号门店权限，ids:{}", storeIds);
                    pageQueryReviewProductRequest.setStoreIds(storeIds);

                    this.removeStoreIdsConditionIfSetAll(pageQueryReviewProductRequest);
                }
            }
            catch (TException e) {
                log.error("authThriftService.queryPermissionGroupList TException", e);
                throw new CommonRuntimeException(e);
            }
        }

        PageQueryReviewProductResponse response = productReviewBizThriftService.pageQueryReviewProduct(pageQueryReviewProductRequest);
        if (Objects.isNull(response) || Objects.isNull(response.getStatus())) {
            log.error("查询总部商品审核列表失败, request:{}", JacksonUtils.toJson(pageQueryReviewProductRequest));
            return CommonResponse.fail2(ResultCode.INTERNAL_SERVER_ERROR);
        }
        return new CommonResponse<>(response.getStatus().getCode(), response.getStatus().getMsg(),
                SkuReviewListQueryResponse.fromPageQueryReviewProductResponse(response));
    }

    private void removeStoreIdsConditionIfSetAll(PageQueryReviewProductRequest request) {
        try {
            if (CollectionUtils.size(request.getStoreIds()) <= MccConfigUtil.getQueryReviewSpuRemoveStoreIdsThreshold()) {
                return;
            }

            // 查询租户下所有的启用门店
            List<PoiInfoDto> poiInfoDtoList = poiServiceFacade.queryCurrentTenantPoiList();
            Set<Long> allEnableStoreIdSet = ListUtils.emptyIfNull(poiInfoDtoList)
                    .stream()
                    .filter(poiInfoDto -> Objects.equals(poiInfoDto.getPoiStatus(), PoiStatusEnum.ONLINE.getKey()))
                    .map(PoiInfoDto::getPoiId)
                    .collect(Collectors.toSet());

            // 筛选出账号有权限的启用门店（因为管理员账号可以查询到停用门店，需要比对口径一致）
            List<Long> hasPermissionEnableStoreIdList = ListUtils.emptyIfNull(request.getStoreIds())
                    .stream()
                    .filter(allEnableStoreIdSet::contains)
                    .distinct()
                    .collect(Collectors.toList());

            // 如果账号有全部门店权限，就不用带入门店ID条件
            if (CollectionUtils.size(allEnableStoreIdSet) == CollectionUtils.size(hasPermissionEnableStoreIdList)) {
                request.setStoreIds(Collections.emptyList());
            }
        }
        catch (Exception e) {
            log.error("queryProductReviewList 处理是否取消带入门店ID查询条件异常", e);
        }
    }

    public CommonResponse<BatchSkuReviewRecallApplyResponse> batchRecallReport(BatchSkuReviewRecallApplyRequest request) {
        User user = ApiMethodParamThreadLocal.getInstance().get().getUser();
        BatchRecallProductReviewRequest batchRecallProductReviewRequest = request.convertToBatchRecallProductReviewRequest(user);
        BatchRecallProductReviewResponse batchRecallResponse = productReviewBizThriftService.batchRecall(batchRecallProductReviewRequest);
        if (Objects.isNull(batchRecallResponse) || Objects.isNull(batchRecallResponse.getStatus())) {
            // 这里返回的code可能是部分成功,所以没有判断code==0
            log.error("批量撤回提报商品失败, request:{}", JacksonUtils.toJson(batchRecallProductReviewRequest));
            return CommonResponse.fail2(ResultCode.INTERNAL_SERVER_ERROR);
        }
        return new CommonResponse<>(batchRecallResponse.getStatus().getCode(), batchRecallResponse.getStatus().getMsg(),
                BatchSkuReviewRecallApplyResponse.fromBatchRecallProductReviewResponse(batchRecallResponse));
    }

    public CommonResponse<List<MedicalStandardSpuAndControlFieldsVO>> queryMedicalStandardSpuInfo(MedicalStandardSpuQueryRequest request, User user) {
        try {
            QueryMedicalStandardProductRequest rpcRequest = request.convertToBizRequest(user);
            log.info("tenantSpuBizThriftService.queryMedicalStandardProduct() request:{}", rpcRequest);
            QueryMedicalStandardProductResponse rpcResponse = medicalStandardThriftService.queryMedicalStandardProduct(rpcRequest);
            if (rpcResponse == null || rpcResponse.getStatus() == null) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), "查询医药标品库商品失败，请重试");
            }
            if (rpcResponse.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), rpcResponse.getStatus().getMsg());
            }
            return new CommonResponse<>(rpcResponse.getStatus().getCode(), rpcResponse.getStatus().getMsg(),
                    Fun.map(rpcResponse.getDataList(), MedicalStandardSpuAndControlFieldsVO::fromDTO));
        }
        catch (Exception e) {
            log.error("ProductBizServiceWrapper.queryMedicalStandardSpuInfo 未知异常, request:{}", request, e);
            return CommonResponse.fail2(ResultCode.INTERNAL_SERVER_ERROR);
        }
    }
}
