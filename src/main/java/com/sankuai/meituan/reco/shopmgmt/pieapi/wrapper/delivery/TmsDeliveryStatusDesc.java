package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.delivery;

import com.fasterxml.jackson.annotation.JsonCreator;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/11
 */
public enum TmsDeliveryStatusDesc {

    /**
     * 初始化，等待发起配送
     */
    INIT(0, "暂无配送状态"),

    /**
     * 已发起配送，等待配送平台接单
     */
    DELIVERY_LAUNCHED(1, "商家发起配送"),

    /**
     * 等待分配骑手，配送平台已接单
     */
    WAITING_TO_ASSIGN_RIDER(30, "等待分配骑手"),

    /**
     * 骑手已接单
     */
    RIDER_ASSIGNED(40, "骑手已接单"),

    /**
     * 骑手已到店
     */
    RIDER_ARRIVED_SHOP(45, "骑手已到店"),

    /**
     * 骑手已取货
     */
    RIDER_TAKEN_GOODS(50, "骑手已取货"),

    /**
     * 商家配送中
     */
    MERCHANT_DELIVERING(51, "自行配送"),

    /**
     * 配送完成，骑手已送达
     */
    DELIVERY_DONE(60, "骑手已送达"),

    /**
     * 配送拒单
     */
    DELIVERY_REJECTED(110, "配送拒单"),

    /**
     * 配送失败
     */
    DELIVERY_FAILED(120, "配送失败"),

    /**
     * 配送已取消
     */
    DELIVERY_CANCELLED(130, "配送已取消");

    private int code;
    private String desc;

    TmsDeliveryStatusDesc(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    private static final Map<Integer, TmsDeliveryStatusDesc> CODE_TO_ENUM_MAP = new HashMap<>();

    static {
        for (TmsDeliveryStatusDesc each : values()) {
            CODE_TO_ENUM_MAP.put(each.getCode(), each);
        }
    }

    @JsonCreator
    public static String getDescFromCode(int code) {

        TmsDeliveryStatusDesc tmsDeliveryStatusDesc = CODE_TO_ENUM_MAP.get(code);
        if (tmsDeliveryStatusDesc == null) {
            return INIT.desc;
        }
        return tmsDeliveryStatusDesc.desc;
    }

    @JsonCreator
    public static TmsDeliveryStatusDesc getFromCode(int code) {

        TmsDeliveryStatusDesc tmsDeliveryStatusDesc = CODE_TO_ENUM_MAP.get(code);
        if (tmsDeliveryStatusDesc == null) {
            return INIT;
        }
        return tmsDeliveryStatusDesc;
    }
}
