package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.shangou.empower.price.client.response.contrast.ContrastCategoryBetweenStoresResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.List;

/**
 * @Author: wangyihao04
 * @Date: 2020-12-01 19:30
 * @Mail: <EMAIL>
 */
@TypeDoc(
        description = "门店间类目对比返回值"
)
@ApiModel("门店间类目对比返回值")
@Getter
@AllArgsConstructor
@ToString
public class ContrastCategoryBetweenStoresVO {
    @FieldDoc(
            description = "分类指标列表"
    )
    @ApiModelProperty("分类指标列表")
    private List<ContrastCategoryDataVO> categoryIndexes;

    public static ContrastCategoryBetweenStoresVO valueOf(ContrastCategoryBetweenStoresResponse response){
        return new ContrastCategoryBetweenStoresVO(ConverterUtils.convertList(response.getCategoryIndexList(), ContrastCategoryDataVO::valueOf));
    }
}
