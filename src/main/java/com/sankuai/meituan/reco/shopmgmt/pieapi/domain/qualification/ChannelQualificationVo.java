package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.qualification;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelqualification.dto.ChannelQualificationQueryDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/7/1 17:54
 **/
@TypeDoc(
        name = "资质vo对象"
)
@Data
@ToString
@EqualsAndHashCode
public class ChannelQualificationVo {

    private String key;
    private String name;
    private List<String> textList;
    private Boolean isRequired;
    private List<ChannelQualificationAttachmentVo> qualityAttachments;


    public static ChannelQualificationVo fromDto(ChannelQualificationQueryDTO dto){
        if(dto == null){
            return null;
        }
        ChannelQualificationVo vo = new ChannelQualificationVo();
        vo.setKey(dto.getKey());
        vo.setName(dto.getName());
        vo.setTextList(dto.getText());
        vo.setIsRequired(dto.getIsRequire());
        if(CollectionUtils.isNotEmpty(dto.getQualityAttachments())){
            vo.setQualityAttachments(
                    dto.getQualityAttachments().stream()
                            .map(ChannelQualificationAttachmentVo::fromDto)
                            .collect(Collectors.toList())
            );
        }

        return vo;
    }

}
