package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：2020/5/19 2:53 下午
 */
@TypeDoc(
        description = "修改商品前台分类响应"
)
@Data
@ApiModel("修改商品前台分类响应")
public class BatchChangeSpuFrontCategoryResponseVO {

    @FieldDoc(
            description = "任务ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "任务ID")
    private Long taskId;

    @FieldDoc(
            description = "错误记录", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "错误记录")
    private List<ChannelSpuFailRecordVO> errorRecordList;
}
