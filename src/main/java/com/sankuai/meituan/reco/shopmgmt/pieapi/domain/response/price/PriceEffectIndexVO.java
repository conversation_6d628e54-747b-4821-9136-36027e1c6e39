package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.price.client.dto.price_effect.PriceEffectIndexDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Objects;

/**
 * @Author: wangyihao04
 * @Date: 2020-11-04 09:37
 * @Mail: <EMAIL>
 */
@TypeDoc(
        description = "价格指标模型"
)
@ApiModel("价格指标模型")
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
public class PriceEffectIndexVO {
    @FieldDoc(
            description = "指标类型"
    )
    @ApiModelProperty("指标类型")
    private String indexType;
    @FieldDoc(
            description = "指标名称"
    )
    @ApiModelProperty("指标名称")
    private String indexName;
    @FieldDoc(
            description = "指标值"
    )
    @ApiModelProperty("指标值")
    private Double indexValue;

    public static PriceEffectIndexVO valueOf(PriceEffectIndexDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }
        return PriceEffectIndexVO.builder()
                .indexType(dto.getIndexType().getIndexKey())
                .indexName(dto.getIndexType().getIndexKey())
                .indexValue(dto.getIndexValue())
                .build();
    }
}
