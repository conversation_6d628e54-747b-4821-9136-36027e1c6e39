package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.management;
// Copyright (C) 2020 Meituan
// All rights reserved

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.oslo.appraise.client.common.enums.AppraisePlanTypeEnum;
import com.meituan.shangou.saas.resource.management.dto.appraise.AppraisePlanPreviewDTO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.RulePassedEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@TypeDoc(
        description = "考核计划对象"
)
@Data
public class AppraisePlanVO {

    @FieldDoc(
            description = "考核计划id",
            example = {}
    )
    private Long planId;

    @FieldDoc(
            description = "考核名称",
            example = {}
    )
    private String planName;

    @FieldDoc(
            description = "考核类型名称",
            example = {}
    )
    private String planTypeName;

    @FieldDoc(
            description = "考核类型, BONUS 平价补贴, GOAL_BASED_INSPIRATION 目标激励, MEET_CRITERION_APPRAISAL 红线考核, SERVICE_START 服务之星, PUNISHMENT 惩罚",
            example = {}
    )
    private String planType;

    @FieldDoc(
            description = "考核规则不达标数量",
            example = {}
    )
    private Integer ruleFailedResultNum;

    @FieldDoc(
            description = "考核规则通过数量",
            example = {}
    )
    private Integer rulePassedResultNum;

    @FieldDoc(
            description = "考核描述",
            example = {}
    )
    private String planDesc;



    @FieldDoc(
            description = "考核周期",
            example = {}
    )
    private AppraisePeriodVO period;

    @FieldDoc(
            description = "考核规则列表",
            example = {}
    )
    private List<AppraiseRuleVO> rules;

    @FieldDoc(
            description = "今日数据规则列表",
            example = {}
    )
    private List<AppraiseRuleVO> currentDayRules;


    @FieldDoc(
            description = "今日数据规则不达标数量",
            example = {}
    )
    private Integer currentDayRuleFailedNum;

    @FieldDoc(
            description = "今日数据规则通过数量",
            example = {}
    )
    private Integer currentDayRulePassedNum;


    private Integer detailQueryType;


    private String memo;

    
    private Integer passed;
    

    private Double amount;


    private String resultDescription;


    private Integer status;


    private String amountTitle;




    public AppraisePlanVO(AppraisePlanPreviewDTO planPreview, List<String> authCodes) {
        this.planId = planPreview.getPlanId();
        this.planName = planPreview.getPlanName();
        this.planTypeName = AppraisePlanTypeEnum.valueOf(planPreview.getPlanType()).getDesc();
        this.planType = planPreview.getPlanType();
        this.planDesc = planPreview.getPlanDesc();
        this.period = new AppraisePeriodVO(planPreview.getPeriod());
        this.rules = planPreview.getDisplayRules().stream()
                .map( ruleDto-> new AppraiseRuleVO(ruleDto, authCodes)).collect(Collectors.toList());
        if (planPreview.getCurrentDayDisplayRules() != null) {
            this.currentDayRules = planPreview.getCurrentDayDisplayRules().stream()
                .map( ruleDto-> new AppraiseRuleVO(ruleDto, authCodes)).collect(Collectors.toList());
        }

        this.status = planPreview.getPreviewStatus();
        if (planPreview.getBonus() != null && planPreview.getBonus()) {
            this.detailQueryType = 2;
        } else {
            this.detailQueryType = 1;
        }
        this.memo = planPreview.getMemo();

        this.passed = planPreview.getPassed() == null ? RulePassedEnum.NOT_FIXED.getCode()
                :  (planPreview.getPassed() ? RulePassedEnum.PASSED.getCode() : RulePassedEnum.NOT_PASSED.getCode());
        this.amount = ConverterUtils.nonNullConvert(planPreview.getAmount(), Math::abs);
        this.resultDescription = planPreview.getResultDescription();

        initNumbers();
    }

    private void initNumbers(){
        if(CollectionUtils.isEmpty(this.rules)){
            this.rulePassedResultNum = 0;
            this.ruleFailedResultNum = 0;
            return;
        }
        this.rulePassedResultNum = (int) this.rules.stream().filter(r -> r.getRulePassed() == 1).count();
        this.ruleFailedResultNum = (int) this.rules.stream().filter(r -> r.getRulePassed() == 0).count();

        if (!CollectionUtils.isEmpty(this.currentDayRules)) {
            this.currentDayRuleFailedNum = (int) this.currentDayRules.stream().filter(r -> r.getRulePassed() == 0).count();
            this.currentDayRulePassedNum = (int) this.currentDayRules.stream().filter(r -> r.getRulePassed() == 1).count();
        }


    }



}
