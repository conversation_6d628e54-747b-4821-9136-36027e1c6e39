package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.regionselect.rsm;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * Email <EMAIL>
 * Date 2022/12/7 3:19 下午
 * Description
 */
@TypeDoc(
        description = "查询寻址任务的请求类"
)
@ApiModel("查询寻址任务的请求类")
@Data
public class FdcAddressingTaskQueryListRequest {

    @FieldDoc(description = "城市ID")
    @ApiModelProperty(value = "城市ID")
    private List<Integer> cityIdList;

    @FieldDoc(description = "任务名称")
    @ApiModelProperty(value = "任务名称")
    private String taskName;

    @FieldDoc(description = "测算区域名")
    @ApiModelProperty(value = "测算区域名")
    private String aoiName;

    @FieldDoc(description = "任务属性，（寻仓人员属性）0:三方，1:自有")
    @ApiModelProperty(value = "任务属性，（寻仓人员属性）0:三方，1:自有)")
    private Integer ownerType;

    @FieldDoc(description = "任务状态")
    @ApiModelProperty(value = "任务状态")
    private Integer taskStatus;

    @FieldDoc(description = "归属机构(寻仓人员归属组织结构id)")
    @ApiModelProperty(value = "归属机构(寻仓人员归属组织结构id)")
    private List<Long> ownerOrgIdList;

    @FieldDoc(description = "寻仓人员员工姓名")
    @ApiModelProperty(value = "寻仓人员员工姓名")
    private String ownerEmployeeName;

    @FieldDoc(description = "仓源名")
    @ApiModelProperty(value = "仓源名")
    private String rentalSourceName;

    @FieldDoc(description = "页码")
    @ApiModelProperty(value = "页码")
    private Integer page;

    @FieldDoc(description = "每页大小")
    @ApiModelProperty(value = "每页大小")
    private Integer pageSize;
}
