package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/7/11
 * desc: 取消订单请求
 */
@TypeDoc(
        description = "取消订单请求"
)
@ApiModel("取消订单请求")
@Data
public class CancelOrderRequest {

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID")
    @NotNull
    private Integer channelId;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道订单号")
    @NotNull
    public String channelOrderId;

    @FieldDoc(
            description = "取消订单原因", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "取消订单原因")
    @NotNull
    public String reason;

    @FieldDoc(
            description = "取消订单原因code,如果是自定义原因传-1", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "取消订单原因")
    @NotNull
    public int reasonCode;

    @FieldDoc(
            description = "商品改库存为0列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品已售完列表")
    private List<RefundGoodsSoldOutVO> refundGoodsSoldOutVOList;

    @FieldDoc(
            description = "商品下架列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品售罄列表")
    private List<RefundGoodsTakenOffVO> refundGoodsTakenOffVOList;


    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店ID")
    private Long storeId;
}
