package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.wavepick.converter;

import com.sankuai.meituan.reco.pickselect.thrift.wave.dto.WarehouseSeedCompleteModuleDTO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.WarehouseSeedCompleteModuleVO;
import org.mapstruct.Mapper;

/**
 * @Description:
 * @Author: zhangjian155
 * @Date: 2022/8/25 18:49
 */
@Mapper(componentModel = "spring", uses = {WarehouseSeedCompleteTaskModuleConverter.class, WarehouseGoodsOwnerModuleConverter.class, WarehouseReceiveStoreInfoConverter.class})
public abstract class WarehouseSeedCompleteModuleConverter {
    public abstract WarehouseSeedCompleteModuleVO convert2Response(WarehouseSeedCompleteModuleDTO warehouseSeedCompleteModuleDTO);
}
