package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.delivery;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@TypeDoc(
        description = "骑手位置同步请求"
)
@ApiModel("骑手操作请求")
@Data
public class RiderLocationSyncRequest {
    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店id", required = true)
    private Long storeId;

    @FieldDoc(
            description = "经度", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "经度")
    private String longitude;

    @FieldDoc(
            description = "纬度", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "纬度")
    private String latitude;

    @FieldDoc(
            description = "定位结果来源", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "定位结果来源")
    private String provider;

    @FieldDoc(
            description = "定位结果精确度", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "定位结果精确度")
    private String accuracy;

    @FieldDoc(
            description = "方向信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "方向信息")
    private String bearing;

    @FieldDoc(
            description = "速度信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "速度信息")
    private String speed;

    @FieldDoc(
            description = "时间信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "时间信息")
    private String time;

}
