package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dianping.rhino.annotation.Rhino;
import com.meituan.linz.boot.exception.ServiceRpcException;
import com.meituan.linz.boot.util.Fun;
import com.meituan.shangou.sac.dto.model.SacMenuNodeWithChild;
import com.meituan.shangou.sac.dto.request.authenticate.AccountAuthPermissionsRequest;
import com.meituan.shangou.sac.dto.request.authenticate.GetAccountMenusByMenuCodesRequest;
import com.meituan.shangou.sac.dto.request.biz.PoiSelectRequest;
import com.meituan.shangou.sac.dto.response.SacStatus;
import com.meituan.shangou.sac.dto.response.authenticate.AccountAuthPermissionsResponse;
import com.meituan.shangou.sac.dto.response.authenticate.GetAccountMenusByMenuCodesResponse;
import com.meituan.shangou.sac.dto.response.biz.PoiSelectResponse;
import com.meituan.shangou.sac.thrift.authenticate.AuthenticateService;
import com.meituan.shangou.sac.thrift.biz.LoginSelectService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.controller.poi.response.PoiSelectVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.JacksonUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.store.management.thrift.common.BaseResult;
import com.sankuai.meituan.shangou.empower.auth.sdk.bean.AppIdEnum;
import com.sankuai.meituan.shangou.empower.auth.sdk.bean.SessionInfo;
import com.sankuai.meituan.shangou.empower.auth.sdk.context.SessionContext;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;

import lombok.extern.slf4j.Slf4j;

/**
 * 权限
 *
 * <AUTHOR>
 * @since 2021/7/9
 */

@Rhino
@Service
@Slf4j
public class SacWrapper {



    @Autowired
    private AuthenticateService authenticateService;

    @Resource
    private LoginSelectService loginSelectService;

    /**
     * 根据菜单code查询菜单及子菜单信息
     *
     * @param accountId
     * @param appId
     * @param menuCodes
     * @return
     */
    public Map<String, SacMenuNodeWithChild> getAccountMenusByMenuCodes(long accountId, int appId, List<String> menuCodes) {
        GetAccountMenusByMenuCodesRequest request = new GetAccountMenusByMenuCodesRequest();
        request.setAccountId(accountId);
        request.setAppId(appId);
        request.setMenuCodes(menuCodes);
        GetAccountMenusByMenuCodesResponse response;
        try {
            log.info("根据菜单code查询菜单及子菜单信息 request:{}", request);
            response = authenticateService.getAccountMenusByMenuCodes(request);
            log.info("根据菜单code查询菜单及子菜单信息 response:{}", JacksonUtils.toJson(response));
        }
        catch (Exception e) {
            log.error("根据菜单code查询菜单信息异常,request:{}", request);
            throw new ServiceRpcException("根据菜单code查询菜单信息异常", e);
        }
        if (response == null || response.sacStatus == null || response.sacStatus.code != 0) {
            log.error("根据菜单code查询菜单信息失败,request:{},response:{}", request, JacksonUtils.toJson(response));
            throw new ServiceRpcException("根据菜单code查询菜单信息失败");
        }
        return response.getMenuCodeSacMenuNodeMap();
    }

    /**
     * 批量查询权限code是否有权限
     *
     * @param accountId
     * @param authId
     * @param permissionCodes
     * @return
     */
    public Map<String, Boolean> accountAuthPermissions(long accountId, int authId, List<String> permissionCodes) {
        if (CollectionUtils.isEmpty(permissionCodes)) {
            return Collections.emptyMap();
        }
        Map<String, Boolean> result = permissionCodes
                .stream()
                .distinct()
                .collect(Collectors.toMap(Function.identity(),
                        s -> false));
        AccountAuthPermissionsRequest request = new AccountAuthPermissionsRequest();
        request.setAccountId(accountId);
        request.setAppId(authId);
        request.setPermissionCodes(permissionCodes);
        AccountAuthPermissionsResponse response;
        try {
            log.info("批量查询权限code是否有权限 request:{}", request);
            response = authenticateService.accountAuthPermissions(request);
            log.info("批量查询权限code是否有权限 response:{}", response);
        }
        catch (Exception e) {
            log.error("批量查询权限code是否有权限异常 request:{}", request);
            return result;
        }
        if (response == null || response.sacStatus == null || response.sacStatus.code != 0) {
            log.error("批量查询权限code是否有权限失败,request:{},response:{}", request, response);
            return result;
        }
        if (MapUtils.isNotEmpty(response.getAuthResult())) {
            response.getAuthResult().forEach(result::put);
        }
        return result;
    }


    /**
     * 门店选择列表
     * @return
     */
    public List<PoiSelectVo> poiSelect() {
        SessionInfo sessionInfo = SessionContext.getCurrentSession();
        PoiSelectRequest request = new PoiSelectRequest();
        request.setTenantId(sessionInfo.getTenantId());
        request.setAccountId(sessionInfo.getAccountId());
        request.setAccountType(sessionInfo.getAccountType());
        request.setAuthAppId(sessionInfo.getAuthAppId());
        PoiSelectResponse response = loginSelectService.poiSelect(request);
        handleSacStatus(response.getSacStatus());
        return Fun.map(response.getPoiSelectDtoList(), PoiSelectVo::fromDto);
    }

    private void handleSacStatus(SacStatus sacStatus) {
        if (sacStatus.getCode() != ResultCodeEnum.SUCCESS.getValue()) {
            throw new BizException(sacStatus.getCode(), sacStatus.getMessage());
        }
    }

}
