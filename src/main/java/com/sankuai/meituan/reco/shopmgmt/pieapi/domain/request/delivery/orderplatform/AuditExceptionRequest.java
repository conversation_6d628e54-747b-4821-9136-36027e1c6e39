package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery.orderplatform;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * AuditExceptionReq
 *
 * <AUTHOR>
 * @since 2023/2/27
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AuditExceptionRequest extends OrderPlatformDeliveryReq {

    /**
     * 是否同意
     */
    @NotNull
    private Boolean isAgree;

}
