package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.appraise;

import com.meituan.shangou.saas.resource.management.dto.appraise.IndicatorDailyDetailDTO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import lombok.Data;
import lombok.ToString;

/**
 * @Author: <EMAIL>
 * @Date: 2020/9/14 17:11
 * @Description:
 */
@ToString
@Data
public class IndicatorDayDetailVO {

    private String date;

    private String value;

    private Integer rulePassed;

    private Boolean isRealTime;


    public IndicatorDayDetailVO(IndicatorDailyDetailDTO detailDTO) {
        this.date = detailDTO.getDate();
        this.value = detailDTO.getValue();
        this.rulePassed = ConverterUtils.nonNullConvert(detailDTO.getRulePassed(), b -> b ? 1:0);
        this.isRealTime = detailDTO.getRealTime();
    }

}
