package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.RegionDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Title: RegionVO
 * @Description: 区域信息
 * @Author: zhaolei12
 * @Date: 2020/5/16 5:39 下午
 */
@TypeDoc(
        description = "区域信息"
)
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("区域信息")
public class RegionVO {

    @FieldDoc(
            description = "区域ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "区域ID")
    private Long regionId;

    @FieldDoc(
            description = "区域名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "区域名称")
    private String regionName;

    public static RegionVO ofDTO(RegionDTO regionDTO) {
        if (regionDTO == null) {
            return null;
        }

        RegionVO regionVO = new RegionVO();
        regionVO.setRegionId(regionDTO.getRegionId());
        regionVO.setRegionName(regionDTO.getName());
        return regionVO;
    }

    public static RegionVO ofDTO(com.sankuai.meituan.shangou.empower.productbiz.client.dto.RegionDTO regionDTO) {
        if (regionDTO == null) {
            return null;
        }

        RegionVO regionVO = new RegionVO();
        regionVO.setRegionId(regionDTO.getRegionId());
        regionVO.setRegionName(regionDTO.getName());
        return regionVO;
    }

    public static RegionDTO toDTO(RegionVO regionVO) {
        if (regionVO == null) {
            return null;
        }

        RegionDTO regionDTO = new RegionDTO();
        regionDTO.setRegionId(regionVO.getRegionId());
        regionDTO.setName(regionVO.getRegionName());
        return regionDTO;
    }
}
