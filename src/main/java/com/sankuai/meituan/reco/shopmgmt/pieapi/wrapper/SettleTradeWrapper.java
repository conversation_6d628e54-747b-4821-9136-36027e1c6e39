package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.realtimesettle.RealTimeSettlePayRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.realtimesettle.RealTimeTradeConfirmedRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.realtimesettle.RealTimeTradeQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.realtimesettle.StoreConfigQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.realtimesettle.RealTimeSettleConfigVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.realtimesettle.RealTimeSettleTradeStatusVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.realtimesettle.RealTimeSettleTradeVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.FlowValveException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.settlement.constant.ResponseStatusCodeEnum;
import com.sankuai.meituan.shangou.empower.settlement.dto.response.ResponseStatus;
import com.sankuai.meituan.shangou.empower.trade.dto.model.ChannelOrderKey;
import com.sankuai.meituan.shangou.empower.trade.dto.model.TradeSkuDTO;
import com.sankuai.meituan.shangou.empower.trade.dto.request.RealTimeSettleTradeRequest;
import com.sankuai.meituan.shangou.empower.trade.dto.request.TenantShopConfigRequest;
import com.sankuai.meituan.shangou.empower.trade.dto.request.TradeConfirmedRequest;
import com.sankuai.meituan.shangou.empower.trade.dto.request.TradeQueryRequest;
import com.sankuai.meituan.shangou.empower.trade.dto.response.RealTimeSettleConfigResponse;
import com.sankuai.meituan.shangou.empower.trade.dto.response.RealTimeSettleTradeResponse;
import com.sankuai.meituan.shangou.empower.trade.dto.response.TradeNoResponse;
import com.sankuai.meituan.shangou.empower.trade.dto.response.TradeQueryResponse;
import com.sankuai.meituan.shangou.empower.trade.services.SettleTradeThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2020/11/6
 **/
@Slf4j
@Component
public class SettleTradeWrapper {

    @Resource
    SettleTradeThriftService settleTradeThriftService;

    public Long generateTradeNo() {
        try {
            TradeNoResponse response = settleTradeThriftService.generateTradeNo();
            checkResponseStatus(response.getResponseStatus());
            return response.getTradeNo();
        } catch (TException e) {
            log.warn("generateTradeNo error!", e);
            throw new FlowValveException(ResultCode.FAIL.code, "交易单号获取接口异常，请稍后重试");
        }
    }

    public RealTimeSettleTradeVO realTimeSettlePay(RealTimeSettlePayRequest request) {
        try {
            RealTimeSettleTradeRequest tradeRequest = convert2TradeRequest(request);
            RealTimeSettleTradeResponse response = settleTradeThriftService.createRealTimeSettleTrade(tradeRequest);
            checkResponseStatus(response.getResponseStatus());
            RealTimeSettleTradeVO vo = new RealTimeSettleTradeVO();
            vo.setTradeNo(String.valueOf(response.getTradeNo()));
            return vo;
        } catch (TException e) {
            log.warn("createRealTimeSettleTrade error!", e);
            throw new FlowValveException(ResultCode.BOOTH_PAY_CREATE_TRADE_EXCEPTION.code, "现结交易创建接口异常，请稍后重试");
        }
    }

    private RealTimeSettleTradeRequest convert2TradeRequest(RealTimeSettlePayRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        RealTimeSettleTradeRequest tradeRequest = new RealTimeSettleTradeRequest();
        tradeRequest.setTenantId(user.getTenantId());
        tradeRequest.setShopId(request.getStoreId());
        tradeRequest.setTradeNo(Long.parseLong(request.getTradeNo()));
        tradeRequest.setBoothId(Long.parseLong(request.getBoothId()));
        tradeRequest.setBoothName(request.getBoothName());
        tradeRequest.setPayAmount(request.getPayAmount());
        tradeRequest.setOperatorId(user.getAccountId());
        tradeRequest.setOperatorAccount(user.getAccountName());
        tradeRequest.setOperatorName(user.getOperatorName());
        List<TradeSkuDTO> skuList = request.getSkuList().stream().map(e -> {
                    TradeSkuDTO sku = new TradeSkuDTO();
                    sku.setSkuId(e.getSkuId());
                    sku.setSkuName(e.getSkuName());
                    sku.setPayAmount(e.getPayAmount());
                    sku.setAttributes(e.getAttributes());
                    sku.setQuantity(e.getQuantity());
                    List<ChannelOrderKey> orderKeyList = e.getOrderKeyList().stream().map(k -> {
                        ChannelOrderKey key = new ChannelOrderKey();
                        key.setChannelId(k.getChannelId());
                        key.setChannelOrderId(k.getChannelOrderId());
                        return key;
                    }).collect(Collectors.toList());
                    sku.setOrderKeyList(orderKeyList);
                    return sku;
                }
        ).collect(Collectors.toList());
        tradeRequest.setSkuList(skuList);
        return tradeRequest;
    }


    public RealTimeSettleTradeStatusVO queryRealTimeTrade(RealTimeTradeQueryRequest request) {
        try {
            IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
            TradeQueryRequest tradeQueryRequest = new TradeQueryRequest();
            tradeQueryRequest.setTenantId(identityInfo.getUser().getTenantId());
            tradeQueryRequest.setTradeNo(Long.parseLong(request.getTradeNo()));
            TradeQueryResponse response = settleTradeThriftService.querySettleTrade(tradeQueryRequest);
            checkResponseStatus(response.getResponseStatus());
            RealTimeSettleTradeStatusVO vo = new RealTimeSettleTradeStatusVO();
            vo.setTradeNo(response.getTradeNo());
            vo.setTradeStatus(response.getTradeStatus());
            vo.setStatusComment(response.getStatusComment());
            return vo;
        } catch (TException e) {
            log.warn("querySettleTrade error!", e);
            throw new FlowValveException(ResultCode.FAIL.code, "现结交易查询接口异常，请稍后重试");
        }
    }

    public RealTimeSettleConfigVO queryStoreRealTimeSettleConfig(StoreConfigQueryRequest request) {
        try {
            IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
            TenantShopConfigRequest configRequest = new TenantShopConfigRequest();
            configRequest.setTenantId(identityInfo.getUser().getTenantId());
            configRequest.setShopId(request.getStoreId());
            RealTimeSettleConfigResponse response = settleTradeThriftService.queryRealTimeSettleConfig(configRequest);
            checkResponseStatus(response.getResponseStatus());
            RealTimeSettleConfigVO vo = new RealTimeSettleConfigVO();
            vo.setUseRealTimeSettle(response.getUseRealTimeSettle());
            vo.setSettleLimitRatio(response.getSettleLimitRatio());
            vo.setMaxSkuPriceLimit(response.getMaxSkuPriceLimit());
            vo.setPayTimeOutSeconds(response.getPayTimeOutSeconds());
            return vo;
        } catch (TException e) {
            log.warn("queryRealTimeSettleConfig error!", e);
            throw new FlowValveException(ResultCode.FAIL.code, "门店现结交易配置查询接口异常，请稍后重试");
        }
    }

    public void realTimeSettleTradeConfirmed(RealTimeTradeConfirmedRequest request) {
        try {
            IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
            TradeConfirmedRequest confirmedRequest = new TradeConfirmedRequest();
            confirmedRequest.setTenantId(identityInfo.getUser().getTenantId());
            confirmedRequest.setTradeNo(Long.parseLong(request.getTradeNo()));
            ResponseStatus status = settleTradeThriftService.tradeConfirmed(confirmedRequest);
            checkResponseStatus(status);
        } catch (TException e) {
            log.warn("tradeConfirmed error!", e);
            throw new FlowValveException(ResultCode.FAIL.code, "现结交易已确认接口异常，请稍后重试");
        }
    }

    private void checkResponseStatus(ResponseStatus status) {
        if (status.getCode() != ResponseStatusCodeEnum.SUCCESS.getValue()) {
            throw new FlowValveException(status.getCode(), status.getMessage());
        }
    }
}
