package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@TypeDoc(description = "不一致商品")
@Data
@Builder
public class DiffCompareSpuResponseVO {

    @FieldDoc(
            description = "分页信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "分页信息", required = true)
    private PageInfoVO pageInfo;

    @FieldDoc(
            description = "不一致商品列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "不一致商品列表", required = true)
    private List<MultiChannelDiffCompareSpuInfoVO> diffCompareSpuInfoVOS;
}
