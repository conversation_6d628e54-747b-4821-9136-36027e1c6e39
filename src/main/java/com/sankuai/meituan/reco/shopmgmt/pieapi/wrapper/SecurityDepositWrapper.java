package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

import com.meituan.shangou.saas.tenant.thrift.common.enums.SecurityDepositModeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.meituan.shangou.saas.resource.management.dto.ResultStatus;
import com.meituan.shangou.saas.resource.management.dto.budget.BudgetRecordDTO;
import com.meituan.shangou.saas.resource.management.dto.budget.request.BudgetRecordsQueryRequest;
import com.meituan.shangou.saas.resource.management.dto.budget.response.BudgetAllRecordsResponse;
import com.meituan.shangou.saas.resource.management.thrift.BudgetThriftService;
import com.meituan.shangou.saas.tenant.thrift.ContractThriftService;
import com.meituan.shangou.saas.tenant.thrift.SecurityDepositThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.Status;
import com.meituan.shangou.saas.tenant.thrift.common.enums.ContractStatusEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.SecurityDepositStatusEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.YesNoEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.config.PoiManageModeDto;
import com.meituan.shangou.saas.tenant.thrift.dto.contract.ContractInfoDTO;
import com.meituan.shangou.saas.tenant.thrift.dto.contract.response.PoiContractInfoQueryResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.securitydeposit.SimpleSecurityDepositDto;
import com.meituan.shangou.saas.tenant.thrift.dto.securitydeposit.response.SimpleSecurityDepositResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.tenant.TenantInfoDto;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.IntegerBooleanConstants;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.SecurityDepositConstants;
import com.sankuai.meituan.reco.shopmgmt.pieapi.converters.PriceConverter;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.securitydeposit.PayResultVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.securitydeposit.SecurityDepositChangeRecordVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.securitydeposit.SecurityDepositInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.securitydeposit.SecurityDepositPaySuccessVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.ContractShowStatus;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.DateUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.AccountTypeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.AccountInfoVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.AccountValueVo;
import com.sankuai.meituan.shangou.empower.pay.constant.GatheringPayStatus;
import com.sankuai.meituan.shangou.empower.pay.constant.GatheringTradeType;
import com.sankuai.meituan.shangou.empower.pay.dto.request.gathering.CreateGatheringOrderRequest;
import com.sankuai.meituan.shangou.empower.pay.dto.request.gathering.GatheringTradeQueryRequest;
import com.sankuai.meituan.shangou.empower.pay.dto.response.gathering.CreateGatheringOrderResponse;
import com.sankuai.meituan.shangou.empower.pay.dto.response.gathering.GatheringTradeOrderInfoResponse;
import com.sankuai.meituan.shangou.empower.pay.services.gathering.GatheringThriftService;
import com.sankuai.meituan.shangou.empower.settlement.dto.response.ResponseStatus;

import lombok.extern.slf4j.Slf4j;

/**
 * @Author: <EMAIL>
 * @Date: 2020/3/2 21:39
 * @Description:
 */
@Slf4j
@Component
public class SecurityDepositWrapper {

    @Autowired
    private SecurityDepositThriftService securityDepositThriftService;
    @Autowired
    private ContractThriftService contractThriftService;


    @Autowired
    private BudgetThriftService budgetThriftService;


    @Autowired
    private GatheringThriftService gatheringThriftService;


    @Autowired
    private AuthThriftWrapper authThriftWrapper;

    @Autowired
    private TenantWrapper tenantWrapper;


    /**
     * 查询资金变更记录
     *
     * @param tenantId
     * @param budgetId
     * @return
     */
    public List<SecurityDepositChangeRecordVO> queryDepositChangeRecord(Long tenantId, Long budgetId) {

        BudgetRecordsQueryRequest request = new BudgetRecordsQueryRequest();

        request.setTenantId(tenantId);
        request.setBudgetId(budgetId);
        try {

            BudgetAllRecordsResponse response = budgetThriftService.queryAllRecords(request);

            checkResponse(response.getStatus());

            List<BudgetRecordDTO> budgetRecordDTOS = response.getBudgetRecordDTOS();

            if (CollectionUtils.isEmpty(budgetRecordDTOS)) {
                return Collections.emptyList();
            }

            return budgetRecordDTOS.stream().map(this::changeRecordConvert).collect(Collectors.toList());
        }
        catch (CommonRuntimeException e) {
            throw e;
        }
        catch (Exception e) {
            log.warn("查询保证金异常: request [{}].", request, e);
            throw new CommonRuntimeException("查询保证金异常，请稍后重试.", ResultCode.SECURITY_DEPOSIT_ERROR);
        }

    }

    private SecurityDepositChangeRecordVO changeRecordConvert(BudgetRecordDTO budgetRecordDTO) {
        SecurityDepositChangeRecordVO changeRecordVO = new SecurityDepositChangeRecordVO();

        double amount = BigDecimal.valueOf(budgetRecordDTO.getAfterAmount())
                .subtract(BigDecimal.valueOf(budgetRecordDTO.getBeforeAmount())).doubleValue();
        changeRecordVO.setAmount(PriceConverter.formatMoney(amount));
        changeRecordVO.setOpTime(budgetRecordDTO.getCreateTime());
        changeRecordVO.setReason(budgetRecordDTO.getMemo());

        return changeRecordVO;
    }

    /**
     * 检查返回
     *
     * @param status
     */
    private void checkResponse(ResultStatus status) {

        if (status == null) {
            throw new CommonRuntimeException("查询资金异常，请稍后重试.", ResultCode.SECURITY_DEPOSIT_ERROR);
        }

        if (status.getCode() != 0) {
            throw new CommonRuntimeException(status.getMessage(), ResultCode.SECURITY_DEPOSIT_ERROR);
        }


    }

    public SecurityDepositPaySuccessVO securityDepositPay(User user, Long storeId, Long amountToPayThisTime) {
        Long tenantId = user.getTenantId();
        Long userId =  user.getAccountId();
        Long epAccountId = user.getEpAccountId();
        if (epAccountId == null) {
            throw new CommonRuntimeException("该账号未接入epassport账户体系，无法缴纳保证金，请联系系统管理员", ResultCode.SECURITY_DEPOSIT_ERROR);
        }

        if (epAccountId.doubleValue() == 0L) {
            epAccountId = getEpAccountFromAuth();
        }

        SecurityDepositInfoVO securityDeposit = getSecurityDeposit(user, storeId);

        if (securityDeposit == null ||
                securityDeposit.getStatus() == SecurityDepositStatusEnum.UNINITIALIZED.getType()) {
            throw new CommonRuntimeException("保证金信息不存在", ResultCode.SECURITY_DEPOSIT_ERROR);
        }


        if (securityDeposit.getRemainAmount() >= securityDeposit.getAmount()) {
            throw new CommonRuntimeException("保证金已缴纳", ResultCode.SECURITY_DEPOSIT_ERROR);
        }


        CreateGatheringOrderRequest request = new CreateGatheringOrderRequest();
        try {

            Long paidAmount;

            Long shouldPayAmount = BigDecimal.valueOf(securityDeposit.getAmount()).subtract(BigDecimal.
                    valueOf(securityDeposit.getRemainAmount())).multiply(BigDecimal.valueOf(100)).longValue();

            if (amountToPayThisTime == null) {
                paidAmount = shouldPayAmount;
            }
            else {

                if (amountToPayThisTime <= 0L) {
                    throw new CommonRuntimeException("本次缴纳金额不能小于等于0", ResultCode.SECURITY_DEPOSIT_ERROR);
                }

                if (amountToPayThisTime > shouldPayAmount) {
                    throw new CommonRuntimeException("本次缴纳金额不能大于剩余应缴金额", ResultCode.SECURITY_DEPOSIT_ERROR);
                }
                paidAmount = amountToPayThisTime;
            }

            request.setTenantId(tenantId);
            request.setOperatorId(userId);
            request.setPayAmount(paidAmount);
            request.setTradeType(GatheringTradeType.DEPOSIT_TYPE.getType());
            request.setUserIp(getRemoteIp());
            request.setExpireMinutes(60 * 24);
            request.setOutOrderNo("" + System.currentTimeMillis());

            Map<String, Long> notifyJson = Maps.newConcurrentMap();
            notifyJson.put("poiId", storeId);
            request.setOrderNotifyJson(JSONObject.toJSONString(notifyJson));
            request.setEpAccountId(epAccountId);
            CreateGatheringOrderResponse gatheringOrder = gatheringThriftService.createGatheringOrder(request);

            checkResponse(gatheringOrder.getResponseStatus());

            return new SecurityDepositPaySuccessVO(gatheringOrder.getTradeNo(), gatheringOrder.getPayToken());

        }
        catch (CommonRuntimeException e) {
            throw e;
        }
        catch (Exception e) {
            log.warn("支付保证金异常: request [{}].", request, e);
            throw new CommonRuntimeException("系统繁忙，请稍后重试或联系运营确认.", ResultCode.SECURITY_DEPOSIT_ERROR);
        }

    }

    private Long getEpAccountFromAuth() {
        try {
            AccountInfoVo accountVo = authThriftWrapper.getCurrentAccountWithoutPermission();
            return accountVo.getEpAccountId();
        }
        catch (TException e) {
            log.error("查询账号信息失败", e);
        }
        return 0L;
    }

    public SecurityDepositInfoVO getSecurityDeposit(User user, Long storeId) {
        if (storeId == null) {
            throw new CommonRuntimeException("未传门店ID.");
        }

        try {
            long tenantId = user.getTenantId();
            PoiManageModeDto poiManageMode = tenantWrapper.queryPoiManageMode(tenantId);
            if (poiManageMode == null || !YesNoEnum.isYes(poiManageMode.getSupportSecurityDeposit())) {
                return null;
            }
            IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
            int appId = identityInfo.getAuthId();
            // 当选择“共享前置仓”，且该租户“协议保证金管理模式=门店模式”，不校验协议状态&保证金状态
            int shareableWarehouseAppId = 9;
            if (appId == shareableWarehouseAppId && Objects.equals(poiManageMode.getSecurityDepositMode(), SecurityDepositModeEnum.POI_MODE.code())) {
                return null;
            }

            SimpleSecurityDepositDto securityDepositDto = querySecurityDeposit(tenantId, storeId);

            SecurityDepositInfoVO securityDepositInfoVO = new SecurityDepositInfoVO();
            securityDepositInfoVO.setId(securityDepositDto.getSecurityDepositId());
            securityDepositInfoVO.setAmount(securityDepositDto.getAmount());
            securityDepositInfoVO.setRemainAmount(securityDepositDto.getRemainAmount());
            securityDepositInfoVO.setStatus(securityDepositDto.getStatus());
            securityDepositInfoVO.setBudgetId(securityDepositDto.getBudgetId());

            fillContractInfo(tenantId, storeId, securityDepositInfoVO);
            fillIsStoreManagerAndManagerList(tenantId, storeId, securityDepositInfoVO);

            return securityDepositInfoVO;
        }
        catch (CommonRuntimeException e) {
            throw e;
        }
        catch (Exception e) {
            log.warn("查询保证金、电子协议状态异常: request [{}].", storeId, e);
            throw new CommonRuntimeException("查询保证金、电子协议状态异常，请稍后重试.", ResultCode.SECURITY_DEPOSIT_ERROR);
        }

    }

    private void fillIsStoreManagerAndManagerList(Long tenantId, Long storeId, SecurityDepositInfoVO securityDepositInfoVO) {
        PoiManageModeDto poiManageModeDto = tenantWrapper.queryPoiManageMode(tenantId);
        if (poiManageModeDto == null || !poiManageModeDto.supportSecurityDeposit()) {
            return;
        }

        if (poiManageModeDto.supportSecurityDepositTenantMode()) {
            securityDepositInfoVO.setSecurityDepositMode(SecurityDepositModeEnum.TENANT_MODE.code());
            fillIsStoreManagerAndManagerListInTenantMode(tenantId, securityDepositInfoVO);
        }
        else {
            securityDepositInfoVO.setSecurityDepositMode(SecurityDepositModeEnum.POI_MODE.code());
            fillIsStoreManagerAndManagerListInPoiMode(tenantId, storeId, securityDepositInfoVO);
        }

    }

    private void fillIsStoreManagerAndManagerListInTenantMode(Long tenantId, SecurityDepositInfoVO securityDepositInfoVO) {

        try {
            AccountInfoVo currentAccount = authThriftWrapper.getCurrentAccountWithoutPermission();
            if (Objects.equals(currentAccount.getAccountType(), AccountTypeEnum.ADMIN.getValue())) {
                securityDepositInfoVO.setIsStoreManager(IntegerBooleanConstants.BOOLEAN_TRUE);
            }
            else {
                securityDepositInfoVO.setIsStoreManager(IntegerBooleanConstants.BOOLEAN_FALSE);
                securityDepositInfoVO.setStoreManagerList(Arrays.asList(getTenantManagerName(tenantId)));
            }
        }
        catch (TException e) {
            log.error("查询账号信息失败, tenantId:{}", tenantId, e);
        }
    }

    private String getTenantManagerName(Long tenantId) {
        TenantInfoDto tenantInfo = tenantWrapper.getTenantInfo(tenantId);
        return tenantInfo == null ? null : tenantInfo.getChargeName();
    }

    /**
     * 在门店模式下填充是否门店管理者和管理者列表.
     *
     * @param tenantId
     * @param storeId
     * @param securityDepositInfoVO
     */
    private void fillIsStoreManagerAndManagerListInPoiMode(Long tenantId, Long storeId, SecurityDepositInfoVO securityDepositInfoVO) {

        List<AccountInfoVo> hasPermissionAccountList =
                authThriftWrapper.queryAccountListWithPoiPermission(tenantId, storeId);
        if (CollectionUtils.isEmpty(hasPermissionAccountList)) {
            securityDepositInfoVO.setIsStoreManager(0);
            return;
        }

        List<Long> hasPermissionAccountIds =
                ConverterUtils.convertList(hasPermissionAccountList, AccountInfoVo::getAccountId);
        // 填充当前账号是否店长及租户的店长名称列表
        List<AccountValueVo> accountInfos = authThriftWrapper.queryStoreManagerInfos(tenantId, hasPermissionAccountIds);
        if (CollectionUtils.isEmpty(accountInfos)) {
            securityDepositInfoVO.setIsStoreManager(0);
            return;
        }

        List<Long> accountIds = ConverterUtils.convertList(accountInfos, AccountValueVo::getAccountId);
        Long accountId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId();
        if (accountIds.contains(accountId)) {
            securityDepositInfoVO.setIsStoreManager(1);
        }
        else {
            securityDepositInfoVO.setIsStoreManager(0);
            List<Long> staffIds = ConverterUtils.convertList(accountInfos, AccountValueVo::getStaffId);
            List<String> employeeNames = tenantWrapper.getEmployeeNames(tenantId, staffIds);
            securityDepositInfoVO.setStoreManagerList(employeeNames);
        }
    }

    private SimpleSecurityDepositDto querySecurityDeposit(Long tenantId, Long storeId) {
        SimpleSecurityDepositResponse response = securityDepositThriftService.querySecurityDeposit(tenantId, storeId);
        checkResponse(response.getStatus());
        return response.getSecurityDepositDto();
    }

    /**
     * 检查返回
     *
     * @param status
     */
    private void checkResponse(Status status) {

        if (status == null) {
            throw new CommonRuntimeException("查询保证金、电子协议状态异常，请稍后重试.", ResultCode.SECURITY_DEPOSIT_ERROR);
        }

        if (status.getCode() != 0) {
            throw new CommonRuntimeException(status.getMessage(), ResultCode.SECURITY_DEPOSIT_ERROR);
        }


    }

    private void fillContractInfo(Long tenantId, Long storeId, SecurityDepositInfoVO securityDepositInfoVo) {


        PoiContractInfoQueryResponse response = contractThriftService.queryPoiContractInfo(tenantId, storeId);

        checkResponse(response.getStatus());

        ContractInfoDTO contractInfo = response.getContractInfo();

        // 电子协议相关信息
        if (contractInfo.getStatus() == null ||
                Arrays.asList(ContractStatusEnum.NONEEDTOSIGN.getType(),
                        ContractStatusEnum.SIGNEDOFFLINE.getType())
                        .contains(contractInfo.getStatus())) {
            securityDepositInfoVo.setShowProtocolButton(0);
        }
        else {
            securityDepositInfoVo.setShowProtocolButton(1);
        }


        if (isMainContractNeedSign(contractInfo)) {
            fillUseMainContract(securityDepositInfoVo, contractInfo);
        }
        else if (isSupplementalContractNeedSign(contractInfo)) {
            fillUseSupplementalContract(securityDepositInfoVo, contractInfo);
        }
        else {
            fillUseMainContract(securityDepositInfoVo, contractInfo);
        }

    }

    private boolean isMainContractNeedSign(ContractInfoDTO contractInfo) {
        boolean isUnsigned = Objects.equals(contractInfo.getStatus(), ContractStatusEnum.UNSIGNED.getType());
        boolean canRenew = contractInfo.getCanRenew();
        return isUnsigned || canRenew;
    }

    private boolean isSupplementalContractNeedSign(ContractInfoDTO contractInfo) {
        boolean isUnsigned = Objects.equals(contractInfo.getSupplementalStatus(), ContractStatusEnum.UNSIGNED.getType());
        boolean canRenew = BooleanUtils.isTrue(contractInfo.getSupplementalCanRenew());
        return isUnsigned || canRenew;
    }

    private void fillUseMainContract(SecurityDepositInfoVO securityDepositInfoVo, ContractInfoDTO contractInfo) {
        securityDepositInfoVo.setIsMainContract(Boolean.TRUE);
        securityDepositInfoVo.setContractId(contractInfo.getContractId());
        ContractShowStatus contractShowStatus = convertToContractShowStatus(contractInfo.getStatus(),
                contractInfo.getCanRenew());
        securityDepositInfoVo.setContractStatus(ConverterUtils.nonNullConvert(contractShowStatus,
                ContractShowStatus::getCode));
        securityDepositInfoVo.setUnsignedContractUrl(contractInfo.getUnsignedContractUrl());
        securityDepositInfoVo.setContractUrl(contractInfo.getContractUrl());
        securityDepositInfoVo.setAppendConfirmLetterContractUrl(contractInfo.getAppendConfirmLetterContractUrl());
        securityDepositInfoVo.setContractSignTime(
                ConverterUtils.nonNullConvert(contractInfo.getContractSignTime(),
                        t -> DateFormatUtils.format(t, DateUtils.YYYY_MM_DD)));
        securityDepositInfoVo.setContractExpireTime(
                ConverterUtils.nonNullConvert(contractInfo.getContractExpireTime(),
                        t -> DateFormatUtils.format(t, DateUtils.YYYY_MM_DD)));
    }

    private ContractShowStatus convertToContractShowStatus(Integer contractStatus, Boolean canRenewContract) {
        if (contractStatus == null) {
            return null;
        }

        if (canRenewContract) {
            return ContractShowStatus.CANRENEW;
        }

        return ContractShowStatus.getByCode(contractStatus);
    }

    private void fillUseSupplementalContract(SecurityDepositInfoVO securityDepositInfoVo, ContractInfoDTO contractInfo) {
        securityDepositInfoVo.setIsMainContract(Boolean.FALSE);
        securityDepositInfoVo.setContractId(contractInfo.getSupplementalContractId());
        ContractShowStatus contractShowStatus = convertToContractShowStatus(contractInfo.getSupplementalStatus(),
                contractInfo.getSupplementalCanRenew());
        securityDepositInfoVo.setContractStatus(ConverterUtils.nonNullConvert(contractShowStatus,
                ContractShowStatus::getCode));
        securityDepositInfoVo.setUnsignedContractUrl(contractInfo.getSupplementalUnsignedContractUrl());
        securityDepositInfoVo.setContractUrl(contractInfo.getSupplementalContractUrl());
        securityDepositInfoVo.setContractSignTime(
                ConverterUtils.nonNullConvert(contractInfo.getSupplementalContractSignTime(),
                        t -> DateFormatUtils.format(t, DateUtils.YYYY_MM_DD)));
        securityDepositInfoVo.setContractExpireTime(
                ConverterUtils.nonNullConvert(contractInfo.getSupplementalContractExpireTime(),
                        t -> DateFormatUtils.format(t, DateUtils.YYYY_MM_DD)));
    }

    private String getRemoteIp() {
        HttpServletRequest request =
                ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        if (request == null) {
            return "";
        }

        String forwardedFor = request.getHeader("x-forwarded-for");
        if (request.getHeader("x-forwarded-for") == null) {
            return request.getRemoteAddr();
        }

        if (StringUtils.contains(forwardedFor, ",")) {
            return StringUtils.split(forwardedFor, ",")[0];
        }

        return forwardedFor;

    }

    private void checkResponse(ResponseStatus status) {
        if (status == null) {
            throw new CommonRuntimeException("支付保证金异常，请稍后重试.", ResultCode.SECURITY_DEPOSIT_ERROR);
        }

        if (status.getCode() != 0) {
            throw new CommonRuntimeException(status.getMessage(), ResultCode.SECURITY_DEPOSIT_ERROR);
        }
    }


    public PayResultVO queryPayResult(Long tenantId, String tradeNo) {
        GatheringTradeQueryRequest request = new GatheringTradeQueryRequest();

        try {
            request.setTenantId(tenantId);
            request.setTradeNo(tradeNo);
            GatheringTradeOrderInfoResponse response = gatheringThriftService.queryGatheringTradeOrder(request);
            checkResponse(response.getResponseStatus());
            if (response.getTradeOrder() != null) {
                if (GatheringPayStatus.PAY_SUCCESS.getType() == response.getTradeOrder().getPayStatus()) {
                    return new PayResultVO(SecurityDepositConstants.PAY_SUCCESS_RET, null);
                }
                else if (GatheringPayStatus.PAYING.getType() == response.getTradeOrder().getPayStatus()) {
                    return new PayResultVO(SecurityDepositConstants.PAYING, null);
                }
                else {
                    return new PayResultVO(SecurityDepositConstants.PAY_FAIL_RET,
                            response.getTradeOrder().getPayStatusComment());
                }
            }
            else {
                return new PayResultVO(1, null);
            }
        }
        catch (CommonRuntimeException e) {
            throw e;
        }
        catch (Exception e) {
            log.warn("支付保证金异常: request [{}].", request, e);
            throw new CommonRuntimeException("查询支付状态异常.", ResultCode.SECURITY_DEPOSIT_ERROR);
        }

    }

}
