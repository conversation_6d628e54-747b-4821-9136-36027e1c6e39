package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.delivery;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StoreDeliveryChannelLaunchPointVo {

    @FieldDoc(
            description = "渠道名称"
    )
    @ApiModelProperty(value = "渠道名称")
    private Integer channelType;

    @FieldDoc(
            description = "配送发单节点"
    )
    @ApiModelProperty(value = "配送发单节点")
    private Integer deliveryLaunchPoint;

    @FieldDoc(
            description = "立即单自动呼叫骑手延迟时间"
    )
    @ApiModelProperty(value = "预约单自动呼叫骑手延迟时间")
    private Integer deliveryLaunchDelayMinutes;

    @FieldDoc(
            description = "预约单自动呼叫骑手延迟时间"
    )
    @ApiModelProperty(value = "预约单自动呼叫骑手延迟时间")
    private Integer bookingOrderDeliveryLaunchMinutes;
}
