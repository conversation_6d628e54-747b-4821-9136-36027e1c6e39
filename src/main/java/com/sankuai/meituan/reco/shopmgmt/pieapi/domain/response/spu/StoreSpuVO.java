package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.google.common.collect.Maps;
import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.CommonConstants;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.ChannelStoreSkuKey;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.FrontCategorySimpleVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.StoreSkuPropertyVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.TimeSlotVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.SaleAttrVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.product.container.SpuContainer;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.store.BoothVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.store.RegionVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.store.StoreVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MoneyUtils;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.CustomizeStockFlag;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.PlatformSoldOutInfoDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreSkuPropertyDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreSpuKeyDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.TimeFragmentDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.spu.StoreSpuDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.assertj.core.util.Lists;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Title: StoreSpuVo
 * @Description: 门店商品信息
 * @Author: zhaolei12
 * @Date: 2020/5/16 12:05 下午
 */
@TypeDoc(
        description = "门店商品SPU信息"
)
@Data
@ApiModel("门店商品SPU信息")
public class StoreSpuVO extends SpuContainer {

    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户ID", required = true)
    private Long tenantId;
    @FieldDoc(
            description = "区域信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "区域信息")
    private RegionVO region;
    @FieldDoc(
            description = "门店信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店信息", required = true)
    private StoreVO store;
    @FieldDoc(
            description = "spu编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "spu编码", required = true)
    private String spuId;
    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称", required = true)
    private String name;
    @FieldDoc(
            description = "商品图片", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品图片", required = true)
    @Deprecated
    private List<String> images;

    @FieldDoc(
            description = "商品图片", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品图片", required = true)
    private List<ImageInfoVO> imageUrlInfos;

    @FieldDoc(
            description = "总部图片列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "总部图片列表")
    private List<String> tenantImageUrlList;

    @FieldDoc(
            description = "称重类型 1-称重计量 2-称重计件 3-非称重"
    )
    @ApiModelProperty(name = "称重类型")
    private Integer weightType;
    @FieldDoc(
            description = "产地", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "产地")
    private String producingPlace;
    @FieldDoc(
            description = "ERP类目信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "ERP类目信息")
    private CategoryVO category;
    @FieldDoc(
            description = "品牌信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "品牌信息")
    private BrandVO brand;

    @FieldDoc(
            description = "可售时间，如果为无限，此字段为空。\n" +
                    "\n" +
                    "key:工作日 见@Enum WeekDayEnum\n" +
                    "* value:时间段 时间段不允许有交集，个数不超过5个\n" +
                    "* {\"09:00-09:30\"},{\"13:30-15:00\"},{\"20:00-21:00\"}", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "可售时间")
    private Map<Integer, List<TimeSlotVO>> availableTimes;
    @FieldDoc(
            description = "是否为“力荐”商品，字段取值范围：0-否， 1-是", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否为“力荐”商品")
    private Integer specialty;
    @FieldDoc(
            description = "商品描述 200字以内", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品描述")
    private String description;
    @FieldDoc(
            description = "商品属性 不超过十个属性", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品属性")
    private List<StoreSkuPropertyVO> properties;
    @FieldDoc(
            description = "商品卖 10字以内", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品卖点")
    private String sellPoint;

    @FieldDoc(
            description = "摊位信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "摊位信息")
    private BoothVO booth;

    @FieldDoc(
            description = "各渠道一级前台分类", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "各渠道一级前台分类")
    private Map<Integer, String> channelId2FirstFrontCategoryNameMap;

    @FieldDoc(
            description = "标签列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "标签列表")
    private List<SpuTagVO> tagList;

    @FieldDoc(
            description = "门店商品SKU列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店商品SKU列表")
    private List<StoreSkuVO> storeSkuList;

    @FieldDoc(
            description = "渠道商品SPU列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道商品SPU列表")
    private List<ChannelSpuVO> channelSpuList;

    @FieldDoc(
            description = "城市月售", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "城市月售")
    private Integer cityMonthSaleAmount;

    @FieldDoc(
            description = "月销量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "月销量 SPU维度=SKU销量总和")
    private Integer monthSaleAmount;

    @FieldDoc(
            description = "自定义名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "自定义名称")
    private String customizedName;

    @FieldDoc(
            description = "是否自定义名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否自定义名称")
    private Boolean customizedNameFlag;

    @FieldDoc(
            description = "是否自定义名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否自定义图片")
    private Boolean customizedPicFlag;

    @FieldDoc(
            description = "自定义图片列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "自定义图片列表")
    private List<String> customizedImageUrlList;

    @FieldDoc(
            description = "自定义图片展示列表"
    )
    @ApiModelProperty(name = "自定义图片展示列表")
    public List<ImageInfoVO> customizedImgInfoList;

    @FieldDoc(
            description = "所属城市商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "所属城市商品名称")
    private String regionSpuName;


    @FieldDoc(
            description = "所属城总部商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "所属城总部商品名称")
    private String merchantSpuName;
    @FieldDoc(
            description = "商品信息是否不一致", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品信息是否不一致")
    private Boolean isSpuInfoUnEqual;
    @FieldDoc(
            description = "类型(101商家端商品缺失;102蔬果派商品缺失;201商家端规格缺失;202蔬果派规格缺失;301基本信息不一致;401价格不一致)", requiredness = Requiredness
            .OPTIONAL
    )
    @ApiModelProperty(name = "商品不一致类型")
    private Integer unEqualType;
    private List<Integer> unEqualTypeList;

    @FieldDoc(
            description = "商品问题总数（商品不一致，审核状态，是否平台停售）", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品问题总数")
    private Integer problemCount;

    @FieldDoc(
            description = "库存描述字段", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "库存描述字段")
    private String stockDesc;

    @FieldDoc(
            description = "自定义视频", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "自定义视频")
    private VideoInfoVO video;

    @FieldDoc(
            description = "租户视频", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户视频")
    private VideoInfoVO tenantVideo;

    @FieldDoc(
            description = "商品自定义图详", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品自定义图详")
    public List<String> pictureContents;

    @FieldDoc(
            description = "租户图详", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户图详")
    public List<String> tenantPictureContents;

    @FieldDoc(
            description = "自定义图详标识", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "自定义图详标识")
    public Boolean customizedPicContentFlag;

    @FieldDoc(
            description = "自定义视频标识", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "自定义视频标识")
    public Boolean customizedVideoFlag;

    @FieldDoc(
            description = "规格类型 1单规格 2多规格", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "规格类型 1单规格 2多规格")
    public Integer specType;

    @FieldDoc(
            description = "门店商品状态，1上架，2下架", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店商品状态，1上架，2下架")
    public Integer storeSaleStatus;

    @FieldDoc(
            description = "京东到家渠道类目属性列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "京东到家渠道类目属性列表")
    private List<SaleAttrVo> jdSaleAttrList;

    @FieldDoc(
            description = "门店店内分类", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店店内分类")
    private List<FrontCategorySimpleVO> storeCategories;


    @FieldDoc(
            description = "总部店内分类", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "总部店内分类")
    private List<FrontCategorySimpleVO> merchantStoreCategories;

    @FieldDoc(
            description = "门店自定义店内分类", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店自定义店内分类")
    private List<FrontCategorySimpleVO> poiStoreCategories;

    @FieldDoc(
            description = "是否控品", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否控品")
    private Integer controlProduct;

    @FieldDoc(
            description = "加盟区域id", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "加盟区域id")
    private Integer joinAreaId;

    @FieldDoc(
            description = "加盟区域名称", requiredness = Requiredness.OPTIONAL

    )
    @ApiModelProperty(name = "加盟区域名称")
    private String joinAreaName;

    @FieldDoc(
            description = "是否控货", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否控货")
    private Integer controlGoods;

    @FieldDoc(
            description = "门店商品是否受控", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店商品是否受控")
    private Integer underControl;

    @FieldDoc(
            description = "加盟主匹配的spuId", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "加盟主匹配的spuId")
    private String franchisorControlProductSpuId;

    @FieldDoc(
            description = "是否自定义商品属性", requiredness = Requiredness.OPTIONAL)
    private Boolean customizedPropertiesFlag;

    @FieldDoc(
            description = "是否自定义商品卖点", requiredness = Requiredness.OPTIONAL)
    private Boolean customizedSellPointFlag;

    @FieldDoc(
            description = "是否自定义商品描述", requiredness = Requiredness.OPTIONAL)
    private Boolean customizedDescriptionFlag;

    @FieldDoc(
            description = "租户商品属性 不超过十个属性", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "租户商品属性 不超过十个属性")
    private List<StoreSkuPropertyVO> tenantProperties;

    @FieldDoc(
            description = "租户商品卖点", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "租户商品卖点")
    private String tenantSellPoint;

    @FieldDoc(
            description = "租户商品描述", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "租户商品描述")
    private String tenantDescription;

    @FieldDoc(description = "异常类型列表")
    @ApiModelProperty(name = "异常类型列表")
    private List<String> abnormalCodes;

    @FieldDoc(description = "异常商品文案")
    @ApiModelProperty(name = "异常商品文案")
    private String abnormalMarkMsg;

    @FieldDoc(description = "异常类型指引")
    @ApiModelProperty(name = "异常类型指引")
    private String abnormalTips;

    @FieldDoc(
            description = "跳转页面类型： @See com.sankuai.meituan.shangou.empower.productbiz.client.enums.AbnormalJumpPageType"
    )
    @ApiModelProperty(name = "跳转页面类型： @See com.sankuai.meituan.shangou.empower.productbiz.client.enums.AbnormalJumpPageType")
    private Integer jumpPageType;

    @FieldDoc(
            description = "不一致类型集合"
    )
    @ApiModelProperty(name = "不一致类型集合")
    private List<Integer> diffCompareTypeList;

    @FieldDoc(
            description = "不可售渠道信息列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "不可售渠道信息列表")
    private List<NoSaleChannelInfoVO> noSaleChannelInfoList;

    @FieldDoc(
            description = "美团商品标签（平台为商品打的标签）", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "美团商品标签（平台为商品打的标签）")
    private List<MtLabelVO> mtLabelList;

    @FieldDoc(
            description = "医药器械资质图信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "医药器械资质图信息")
    private MedicalDeviceQuaInfoVO medicalDeviceQuaInfo;

    @FieldDoc(
            description = "是否门店自定义资质图信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否门店自定义资质图信息")
    private Boolean customizedMedicalDeviceQuaInfo;

    @FieldDoc(
            description = "主档商品医药器械资质图信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "主档商品医药器械资质图信息")
    private MedicalDeviceQuaInfoVO tenantMedicalDeviceQuaInfo;

    @FieldDoc(
            description = "美团售后服务类型", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "美团售后服务类型")
    private String mtAfterSaleServiceType;

    @FieldDoc(
            description = "门店是否自定义美团售后服务类型", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店是否自定义美团售后服务类型")
    private Boolean customizedMtAfterSaleServiceType;

    @FieldDoc(
            description = "主档商品美团售后服务类型", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "主档商品美团售后服务类型")
    private String tenantMtAfterSaleServiceType;

    @FieldDoc(
            description = "主档商品创建来源", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "主档商品创建来源")
    private Integer tenantCreateSource;

    @FieldDoc(
            description = "商品货盘归属", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "商品货盘归属")
    private Integer palletSrc;

    @FieldDoc(
            description = "自定义京东品牌标识", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "自定义京东品牌标识")
    private Boolean customizedJdBrandIdFlag;

    @FieldDoc(
            description = "渠道品牌", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "渠道品牌")
    private List<ChannelBrandVO> channelBrandList;

    @FieldDoc(
            description = "主档渠道品牌", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "主档渠道品牌")
    private List<ChannelBrandVO> tenantChannelBrandList;

    @FieldDoc(
            description = "京东配送要求，1常温,2冷藏,3冷冻")
    @ApiModelProperty(value = "配送要求，1常温,2冷藏,3冷冻")
    public Integer deliveryRequirement;

    @FieldDoc(
            description = "自定义京东配送要求标识", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "自定义京东配送要求标识")
    private Boolean customizedDeliveryRequirementFlag;

    @FieldDoc(
            description = "京东总部配送要求", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "自定义京东配送要求标识")
    private Integer tenantDeliveryRequirement;

    @FieldDoc(
            description = "门店商品AI推荐信息", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "门店商品AI推荐信息")
    private AiRecommendVO aiRecommendVO;

    @FieldDoc(
            description = "主档商品AI推荐信息", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "主档商品AI推荐信息")
    private AiRecommendVO tenantAiRecommendVO;

    @FieldDoc(
            description = "主档商品是否匹配医药标品库，1-是，2-否", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "主档商品是否匹配医药标品库，1-是，2-否")
    private Integer matchMedicalStandard;

    @FieldDoc(
            description = "特殊管控商品资质"
    )
    @ApiModelProperty("特殊管控商品资质")
    private List<String> controlQuaPicUrl;


    @FieldDoc(
            description = "特殊管控商品资质自定义标识"
    )
    @ApiModelProperty("特殊管控商品资质自定义标识")
    private Boolean customizedControlQuaPicUrlFlag;

    @FieldDoc(
            description = "租户特殊管控商品资质"
    )
    @ApiModelProperty("租户特殊管控商品资质")
    private List<String> tenantControlQuaPicUrl;

    @FieldDoc(
            description = "名称补充语自定义标", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "名称补充语自定义标")
    private Boolean customizedNameSupplementFlag;

    @FieldDoc(
            description = "门店商品名称补充语", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店商品名称补充语")
    private NameSupplementInfoVO nameSupplementInfo;

    @FieldDoc(
            description = "商品主档名称补充语", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品主档名称补充语")
    private NameSupplementInfoVO tenantNameSupplementInfo;

    @FieldDoc(
            description = "类目特殊图片"
    )
    @ApiModelProperty("类目特殊图片")
    private List<SpecialPictureVO> mtSpecialPictureList;

    @ApiModelProperty("主档类目特殊图")
    private List<TenantSpecialPictureVO> tenantMtSpecialPictureList;

    public static List<StoreSpuVO> ofDTOList(List<StoreSpuDTO> storeSpuDTOList,
                                             Map<StoreSpuKeyDTO, PlatformSoldOutInfoDTO> storeSpuAbnormalInfoMap) {
        if (CollectionUtils.isEmpty(storeSpuDTOList)) {
            return Lists.newArrayList();
        }

        return storeSpuDTOList.stream().filter(Objects::nonNull)
                .map(spuDTO -> StoreSpuVO.ofDTO(spuDTO, storeSpuAbnormalInfoMap.get(StoreSpuKeyDTO.builder()
                        .storeId(spuDTO.getStore().getStoreId()).spuId(spuDTO.getSpuId()).build())))
                .collect(Collectors.toList())
                .stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static StoreSpuVO ofDTO(StoreSpuDTO storeSpuDTO, PlatformSoldOutInfoDTO soldOutInfoDTO) {
        if (storeSpuDTO == null) {
            return null;
        }
        StoreSpuVO storeSpuVO = new StoreSpuVO();
        storeSpuVO.setTenantId(storeSpuDTO.getTenantId());
        storeSpuVO.setSpuId(storeSpuDTO.getSpuId());
        storeSpuVO.setName(storeSpuDTO.getName());
        storeSpuVO.setStore(StoreVO.ofDTO(storeSpuDTO.getStore()));
        storeSpuVO.setRegion(RegionVO.ofDTO(storeSpuDTO.getRegion()));
        storeSpuVO.setImages(storeSpuDTO.getImageUrls());
        storeSpuVO.setWeightType(storeSpuDTO.getWeightType());

        storeSpuVO.setAvailableTimes(convertAvailableTime(storeSpuDTO.getAvailableTimes()));
        storeSpuVO.setSpecialty(storeSpuDTO.getSpecialty());

        // 属性、卖点、描述
        storeSpuVO.setDescription(storeSpuDTO.getDescription());
        storeSpuVO.setCustomizedDescriptionFlag(storeSpuDTO.getCustomizedDescriptionFlag());
        storeSpuVO.setTenantDescription(storeSpuDTO.getTenantDescription());
        storeSpuVO.setCustomizedPropertiesFlag(storeSpuDTO.getCustomizedPropertiesFlag());
        storeSpuVO.setTenantProperties(convertProperties(storeSpuDTO.getTenantProperties()));
        storeSpuVO.setProperties(convertProperties(storeSpuDTO.getProperties()));
        storeSpuVO.setSellPoint(storeSpuDTO.getSellPoint());
        storeSpuVO.setCustomizedSellPointFlag(storeSpuDTO.getCustomizedSellPointFlag());
        storeSpuVO.setTenantSellPoint(storeSpuDTO.getTenantSellPoint());

        storeSpuVO.setChannelId2FirstFrontCategoryNameMap(storeSpuDTO.getChannelId2FirstFrontCategoryNameMap());

        storeSpuVO.setProducingPlace(storeSpuDTO.getProducingPlace());
        storeSpuVO.setCategory(CategoryVO.ofDTO(storeSpuDTO.getCategory()));
        storeSpuVO.setBrand(BrandVO.ofDTO(storeSpuDTO.getBrand()));

        storeSpuVO.setBooth(BoothVO.ofDTO(storeSpuDTO.getBooth()));

        // 标签信息转换
        storeSpuVO.setTagList(SpuTagVO.ofDTOList(storeSpuDTO.getTagList()));

        storeSpuVO.setStoreSkuList(StoreSkuVO.ofDTOList(storeSpuDTO.getStoreSkuList()));
        storeSpuVO.setChannelSpuList(ChannelSpuVO.ofDTOList(storeSpuDTO.getChannelSpuList(), soldOutInfoDTO));

        final boolean qnhRequest = MccConfigUtil.isQnhRequest(storeSpuDTO.getTenantId(),
                Optional.ofNullable(storeSpuDTO.getStore()).map(StoreDTO::getStoreId).orElse(null));

        fillChannelSkuStockDesc(storeSpuVO.getChannelSpuList(), storeSpuVO.getStoreSkuList(), qnhRequest);

        // 商品SPU月销量
        storeSpuVO.setMonthSaleAmount(storeSpuDTO.getMonthSaleAmount());

        if (storeSpuDTO.getCustomizedNameFlag()) {
            storeSpuVO.setCustomizedName(storeSpuDTO.getName());
        }
        storeSpuVO.setCustomizedNameFlag(storeSpuDTO.getCustomizedNameFlag());
        storeSpuVO.setCustomizedPicFlag(storeSpuDTO.getCustomizedPicFlag());
        storeSpuVO.setCustomizedImageUrlList(storeSpuDTO.getCustomizedImageUrlList());
        storeSpuVO.setTenantImageUrlList(storeSpuDTO.getTenantImageUrlList());
        storeSpuVO.setMerchantSpuName(storeSpuDTO.getMerchantSpuName());
        storeSpuVO.setRegionSpuName(storeSpuDTO.getRegionSpuName());
        storeSpuVO.setStockDesc(qnhRequest ? CommonConstants.STOCK_IGNORE_CHAR : fillStockDesc(storeSpuDTO));
        //视频图详
        storeSpuVO.setVideo(VideoInfoVO.of(storeSpuDTO.getVideo()));
        storeSpuVO.setTenantVideo(VideoInfoVO.of(storeSpuDTO.getTenantVideo()));
        storeSpuVO.setPictureContents(storeSpuDTO.getPictureContents());
        storeSpuVO.setTenantPictureContents(storeSpuDTO.getTenantPictureContents());
        storeSpuVO.setCustomizedVideoFlag(storeSpuDTO.getCustomizedVideoFlag());
        storeSpuVO.setCustomizedPicContentFlag(storeSpuDTO.getCustomizedPicContentsFlag());
        storeSpuVO.setSpecType(storeSpuDTO.getSpecType());
        storeSpuVO.setStoreSaleStatus(storeSpuDTO.getSaleStatus());
        //分渠道定价设置
        fillMultiChannelPriceType(storeSpuVO.getStoreSkuList(), storeSpuVO.getChannelSpuList());

        List<String> delImgList = Optional.ofNullable(storeSpuDTO.getChannelSpuList()).orElse(Lists.newArrayList())
                .stream()
                .filter(dto -> CollectionUtils.isNotEmpty(dto.getAuditDelImgList()))
                .map(ChannelSpuDTO::getAuditDelImgList)
                .flatMap(List::stream)
                .collect(Collectors.toList());
        List<ImageInfoVO> imageInfoVOS = Optional.ofNullable(storeSpuDTO.getImageUrls()).orElse(Lists.newArrayList())
                .stream()
                .map(imgUrl -> {
                    return ImageInfoVO.builder()
                            .imgUrl(imgUrl)
                            .isDelete(delImgList.contains(imgUrl))
                            .build();
                }).collect(Collectors.toList());
        storeSpuVO.setImageUrlInfos(imageInfoVOS);

        // 自定义图片和门店图片取值一致
        if (BooleanUtils.isTrue(storeSpuDTO.getCustomizedPicFlag())) {
            storeSpuVO.setCustomizedImgInfoList(imageInfoVOS);
        }

        storeSpuVO.setJdSaleAttrList(ConverterUtils.convertList(storeSpuDTO.getSpuSaleAttributeDTOS(), SaleAttrVo::fromDTO));

        if (CollectionUtils.isNotEmpty(storeSpuDTO.getStoreCategories())){
            storeSpuVO.setStoreCategories(Fun.map(storeSpuDTO.getStoreCategories(), FrontCategorySimpleVO::new));
        }
        if (CollectionUtils.isNotEmpty(storeSpuDTO.getMerchantStoreCategories())){
            storeSpuVO.setMerchantStoreCategories(Fun.map(storeSpuDTO.getMerchantStoreCategories(), FrontCategorySimpleVO::new));
        }
        if (CollectionUtils.isNotEmpty(storeSpuDTO.getPoiStoreCategories())){
            storeSpuVO.setPoiStoreCategories(Fun.map(storeSpuDTO.getPoiStoreCategories(), FrontCategorySimpleVO::new));
        }

        storeSpuVO.setMatchMedicalStandard(storeSpuDTO.getMatchMedicalStandard());

        //增加加盟主字段
        storeSpuVO.setControlProduct(storeSpuDTO.getControlProduct());
        storeSpuVO.setControlGoods(storeSpuDTO.getControlGoods());
        storeSpuVO.setJoinAreaId(storeSpuDTO.getJoinAreaId());
        storeSpuVO.setJoinAreaName(storeSpuDTO.getJoinAreaName());
        storeSpuVO.setFranchisorControlProductSpuId(storeSpuDTO.getFranchisorControlProductSpuId());
        storeSpuVO.setUnderControl(storeSpuDTO.getUnderControl());
        // 美团商品标签
        storeSpuVO.setMtLabelList(Fun.map(storeSpuDTO.getMtLabelDtoList(), MtLabelVO::ofOcmsDto));
        if (storeSpuDTO.getMedicalDeviceQuaInfo() != null) {
            storeSpuVO.setMedicalDeviceQuaInfo(MedicalDeviceQuaInfoVO.fromMedicalDeviceQuaDTO(storeSpuDTO.getMedicalDeviceQuaInfo()));
        }
        if (storeSpuDTO.getTenantMedicalDeviceQuaInfo() != null) {
            storeSpuVO.setTenantMedicalDeviceQuaInfo(MedicalDeviceQuaInfoVO.fromMedicalDeviceQuaDTO(storeSpuDTO.getTenantMedicalDeviceQuaInfo()));
        }
        storeSpuVO.setCustomizedMedicalDeviceQuaInfo(storeSpuDTO.getCustomizedMedicalDeviceQuaInfo());
        storeSpuVO.setMtAfterSaleServiceType(storeSpuDTO.getMtAfterSaleServiceType());
        storeSpuVO.setCustomizedMtAfterSaleServiceType(storeSpuDTO.getCustomizedMtAfterSaleServiceType());
        storeSpuVO.setTenantMtAfterSaleServiceType(storeSpuDTO.getTenantMtAfterSaleServiceType());

        // 主档商品创建来源
        storeSpuVO.setTenantCreateSource(storeSpuDTO.getTenantCreateSource());
        // 货盘归属
        storeSpuVO.setPalletSrc(storeSpuDTO.getPalletSrc());
        //京东品牌自定义
        storeSpuVO.setCustomizedJdBrandIdFlag(storeSpuDTO.getCustomizedJdBrandIdFlag());
        storeSpuVO.setChannelBrandList(storeSpuDTO.getJdBrand() != null ? Lists.newArrayList(ChannelBrandVO.ofOcms(storeSpuDTO.getJdBrand())) : new ArrayList<>());
        storeSpuVO.setTenantChannelBrandList(storeSpuDTO.getTenantJdBrand() != null ? Lists.newArrayList(ChannelBrandVO.ofOcms(storeSpuDTO.getTenantJdBrand())) : new ArrayList<>());
        //京东配送要求
        storeSpuVO.setCustomizedDeliveryRequirementFlag(storeSpuDTO.getCustomizedDeliveryRequirementFlag());
        storeSpuVO.setDeliveryRequirement(storeSpuDTO.getDeliveryRequirement());
        storeSpuVO.setTenantDeliveryRequirement(storeSpuDTO.getTenantDeliveryRequirement());
        storeSpuVO.setChannelSaleAttrInfoList(ConverterUtils.convertList(storeSpuDTO.getChannelSaleAttributes(), ChannelSaleAttrInfoVO::of));
        storeSpuVO.setControlQuaPicUrl(storeSpuDTO.getControlQuaPicUrl());
        storeSpuVO.setCustomizedControlQuaPicUrlFlag(storeSpuDTO.getCustomizedControlQuaPicUrlFlag());
        storeSpuVO.setTenantControlQuaPicUrl(storeSpuDTO.getTenantControlQuaPicUrl());
        storeSpuVO.setAiRecommendVO(AiRecommendVO.fetchAiRecommendOCMSDTO(storeSpuDTO.getAiRecommendInfo()));
        storeSpuVO.setTenantAiRecommendVO(AiRecommendVO.fetchAiRecommendOCMSDTO(storeSpuDTO.getTenantAiRecommendInfo()));
        storeSpuVO.setCustomizedNameSupplementFlag(storeSpuDTO.getCustomizedNameSupplementFlag());
        storeSpuVO.setNameSupplementInfo(NameSupplementInfoVO.fromDTO(storeSpuDTO.getNameSupplementInfo()));
        storeSpuVO.setTenantNameSupplementInfo(NameSupplementInfoVO.fromDTO(storeSpuDTO.getTenantNameSupplementInfo()));
        // 美团类目特殊图片
        storeSpuVO.setMtSpecialPictureList(ConverterUtils.convertList(storeSpuDTO.getMtSpecialPictureDTOList(), SpecialPictureVO::toVO));
        storeSpuVO.setTenantMtSpecialPictureList(ConverterUtils.convertList(storeSpuDTO.getTenantMtSpecialPictureDTOList(), TenantSpecialPictureVO::ocmsToVO));
        return storeSpuVO;
    }

    // 按照前端的展示要求构造统一以及分渠道定价
    private static void fillMultiChannelPriceType(List<StoreSkuVO> storeSkuList, List<ChannelSpuVO> channelSpuList) {

        Map<Integer, ChannelSpuVO> channelSpuVOMap = ConverterUtils.listStreamMapToMap(channelSpuList, ChannelSpuVO::getChannelId, channelSpuVO -> channelSpuVO);

        fillMultiChannelPriceType(storeSkuList, channelSpuList, channelSpuVOMap);

    }

    /**
     * 根据门店开通渠道构建定价，避免门店未开通的渠道分渠道定价时必填问题
     * @param storeSpuVO
     * @param channelIdList
     */
    public static void fillMultiChannelPriceTypeByStoreOpenChannel(StoreSpuVO storeSpuVO, List<Integer> channelIdList) {
        if(Objects.isNull(storeSpuVO)){
            return;
        }
        List<StoreSkuVO> storeSkuList = storeSpuVO.getStoreSkuList();
        List<ChannelSpuVO> channelSpuList = storeSpuVO.getChannelSpuList();
        List<ChannelSpuVO> needHandleChannelSpuList = Fun.filter(channelSpuList, each -> Objects.nonNull(each) && channelIdList.contains(each.getChannelId()));
        Map<Integer, ChannelSpuVO> channelSpuVOMap = Fun.toMapQuietly(needHandleChannelSpuList, ChannelSpuVO::getChannelId);

        fillMultiChannelPriceType(storeSkuList, needHandleChannelSpuList, channelSpuVOMap);

    }

    private static void fillMultiChannelPriceType(List<StoreSkuVO> storeSkuList, List<ChannelSpuVO> channelSpuList, Map<Integer, ChannelSpuVO> channelSpuVOMap) {
        if(Objects.isNull(storeSkuList) || Objects.isNull(channelSpuList) || Objects.isNull(channelSpuVOMap)){
            return;
        }

        for (StoreSkuVO storeSkuVO : storeSkuList) {

            List<ChannelPriceVO> channelOnlinePriceList = Lists.newArrayList();
            List<ChannelSkuVO> channelSkuVOs = Lists.newArrayList();
            channelSpuVOMap.forEach((channelId, channelSpu) -> {
                ChannelSkuVO channelSkuVO = parseChannelSkuVO(storeSkuVO.getSkuId(), channelId, channelSpuList);
                channelSkuVOs.add(channelSkuVO);
                setChannelPriceVO(channelOnlinePriceList, channelSkuVO, channelId);
            });

            List<ChannelSkuVO> priceSkus = channelSkuVOs.stream().filter(Objects::nonNull).collect(Collectors.toList());
            List<ChannelSkuVO> nullSkus = channelSkuVOs.stream().filter(Objects::isNull).collect(Collectors.toList());

            if (priceSkus.size() == channelSkuVOs.size() && channelSkuVOs.size() > 0) {
                List<Long> priceList = channelSkuVOs.stream().map(ChannelSkuVO::getPrice).collect(Collectors.toList());
                Set<Long> prices = new HashSet<>(priceList);
                if (prices.size() == 1) {
                    storeSkuVO.setChannelPriceType(1);
                    storeSkuVO.setChannelOnlinePrice(MoneyUtils.centToYuan(priceList.get(0)).toString());
                } else {
                    storeSkuVO.setChannelPriceType(2);
                }
            } else if (priceSkus.size() == 1) {
                storeSkuVO.setChannelPriceType(1);
                storeSkuVO.setChannelOnlinePrice(MoneyUtils.centToYuan(priceSkus.get(0).getPrice()).toString());
            } else if (nullSkus.size() == channelSkuVOs.size()) {
                storeSkuVO.setChannelPriceType(1);
                storeSkuVO.setChannelOnlinePrice("9999");
            } else {
                storeSkuVO.setChannelPriceType(2);
            }

            storeSkuVO.setChannelOnlinePriceList(channelOnlinePriceList);
        }

    }

    private static void setChannelPriceVO(List<ChannelPriceVO> channelOnlinePriceList, ChannelSkuVO channelSkuVO, Integer channelId) {
        if (channelSkuVO != null) {
            channelOnlinePriceList.add(new ChannelPriceVO(channelId, MoneyUtils.centToYuan
                    (channelSkuVO.getPrice()).toString()));
        } else {
            channelOnlinePriceList.add(new ChannelPriceVO(channelId, "9999"));
        }

    }

    private static ChannelSkuVO parseChannelSkuVO(String skuId, Integer channelId, List<ChannelSpuVO> channelSpuList) {
        ChannelSpuVO channelSpuVO = channelSpuList.stream().filter(spu -> spu.getChannelId() == channelId)
                .findFirst().orElse(null);
        if (channelSpuVO == null) {
            return null;
        }
        return channelSpuVO.getChannelSkuList().stream().filter(sku -> Objects.equals(sku.getSkuId(), skuId)).findFirst()
                .orElse(null);
    }

    private static void fillChannelSkuStockDesc(List<ChannelSpuVO> channelSpuVOList, List<StoreSkuVO> storeSkuVOList,
                                                boolean qnhRequest) {
        if (CollectionUtils.isEmpty(channelSpuVOList)) {
            return;
        }
        Map<String, StoreSkuVO> storeSkuVOMap = storeSkuVOList.stream().collect(Collectors.toMap(StoreSkuVO::getSkuId, Function
                .identity(), (k1, k2) -> k1));
        channelSpuVOList.forEach(channelSpuVO -> {
            if (CollectionUtils.isEmpty(channelSpuVO.getChannelSkuList())) {
                return;
            }
            for (ChannelSkuVO channelSkuVO : channelSpuVO.getChannelSkuList()) {
                StoreSkuVO storeSkuVO = storeSkuVOMap.get(channelSkuVO.getSkuId());

                if (qnhRequest) {
                    channelSkuVO.setStockDesc(CommonConstants.STOCK_IGNORE_CHAR);
                    continue;
                }

                if (storeSkuVO == null) {
                    continue;
                }
                if (storeSkuVO.getCustomizeStockFlag() != null && storeSkuVO.getCustomizeStockFlag() == CustomizeStockFlag
                        .UNCUSTOMIZE.getValue()) {
                    channelSkuVO.setStockDesc("无限库存");
                } else {
                    channelSkuVO.setStockDesc((channelSkuVO.getStock() != null && channelSkuVO.getStock() > 0) ? channelSkuVO
                            .getStock().toString() : "售罄");
                }
            }
        });
    }

    public static String fillStockDesc(StoreSpuDTO storeSpuDTO) {
        if (checkCustomizeStockFlag(storeSpuDTO)) {
            return "无限库存";
        } else {
            int totalStockCount = calcTotalStockCount(storeSpuDTO);
            return totalStockCount > 0 ? String.valueOf(totalStockCount) : "售罄";
        }
    }

    public static String fillStockDesc(StoreSpuDto storeSpuDto) {
        if (checkCustomizeStockFlag(storeSpuDto)) {
            return "无限库存";
        } else {
            int totalStockCount = calcTotalStockCount(storeSpuDto);
            return totalStockCount > 0 ? String.valueOf(totalStockCount) : "售罄";
        }
    }

    private static boolean checkCustomizeStockFlag(StoreSpuDTO storeSpuDTO) {
        if (CollectionUtils.isEmpty(storeSpuDTO.getStoreSkuList())) {
            return false;
        }
        for (StoreSkuDTO storeSkuDTO : storeSpuDTO.getStoreSkuList()) {
            // 有一个是无限库存 展示为无限
            if (storeSkuDTO.getCustomizeStockFlag() != null && storeSkuDTO.getCustomizeStockFlag() == CustomizeStockFlag
                    .UNCUSTOMIZE.getValue()) {
                return true;
            }
        }
        return false;
    }

    private static boolean checkCustomizeStockFlag(StoreSpuDto storeSpuDDto) {
        if (CollectionUtils.isEmpty(storeSpuDDto.getStoreSkuList())) {
            return false;
        }
        for (com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreSkuDTO storeSkuDTO : storeSpuDDto.getStoreSkuList()) {
            // 有一个是无限库存 展示为无限
            if (storeSkuDTO.getCustomizedStockFlag() != null
                    && storeSkuDTO.getCustomizedStockFlag() == CustomizeStockFlag.UNCUSTOMIZE.getValue()) {
                return true;
            }
        }
        return false;
    }

    private static Integer calcTotalStockCount(StoreSpuDTO storeSpuDTO) {
        AtomicReference<Integer> sumStockCount = new AtomicReference<>(0);
        if (CollectionUtils.isEmpty(storeSpuDTO.getChannelSpuList())) {
            return sumStockCount.get();
        }
        storeSpuDTO.getChannelSpuList().forEach(channelSpuVO -> {
            List<ChannelSkuDTO> channelSkuList = channelSpuVO.getChannelSkuList();
            if (CollectionUtils.isEmpty(channelSkuList)) {
                return;
            }
            sumStockCount.set(channelSkuList.stream().filter(Objects::nonNull)
                    .filter(item -> Objects.nonNull(item.getStock()))
                    .filter(item -> item.getStock() >= 0)
                    .mapToInt(ChannelSkuDTO::getStock).sum());
        });
        return sumStockCount.get();
    }

    private static Integer calcTotalStockCount(StoreSpuDto storeSpuDto) {
        AtomicReference<Integer> sumStockCount = new AtomicReference<>(0);
        if (CollectionUtils.isEmpty(storeSpuDto.getChannelSpuList())) {
            return sumStockCount.get();
        }
        storeSpuDto.getChannelSpuList().forEach(channelSpuDto -> {
            List<com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelSkuDTO> channelSkuList = channelSpuDto.getChannelSkuList();
            if (CollectionUtils.isEmpty(channelSkuList)) {
                return;
            }
            sumStockCount.set(channelSkuList.stream().filter(Objects::nonNull)
                    .filter(item -> Objects.nonNull(item.getStock()))
                    .filter(item -> item.getStock() >= 0)
                    .mapToInt(com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelSkuDTO::getStock).sum());
        });
        return sumStockCount.get();
    }

    private static Map<Integer, List<TimeSlotVO>> convertAvailableTime(Map<Integer, List<ProductTimeSlotDTO>> availableTimeDtoMap) {
        Map<Integer, List<TimeSlotVO>> availableTimeVoMap = new HashMap<>();
        if (MapUtils.isEmpty(availableTimeDtoMap)) {
            return availableTimeVoMap;
        }
        for (Map.Entry<Integer, List<ProductTimeSlotDTO>> entry : availableTimeDtoMap.entrySet()) {
            List<TimeSlotVO> timeSlotVOList = new ArrayList<>();
            if (CollectionUtils.isEmpty(entry.getValue())) {
                continue;
            }
            for (ProductTimeSlotDTO timeSlotDTO : entry.getValue()) {
                if (null == entry.getValue()) {
                    continue;
                }
                timeSlotVOList.add(new TimeSlotVO(timeSlotDTO));
            }
            availableTimeVoMap.put(entry.getKey(), timeSlotVOList);
        }
        return availableTimeVoMap;
    }

    private static Map<Integer, List<TimeSlotVO>> convertAvailableTimeForBiz(Map<Integer, List<TimeFragmentDTO>> availableTimeDtoMap) {
        Map<Integer, List<TimeSlotVO>> availableTimeVoMap = new HashMap<>();
        if (MapUtils.isEmpty(availableTimeDtoMap)) {
            return availableTimeVoMap;
        }
        for (Map.Entry<Integer, List<TimeFragmentDTO>> entry : availableTimeDtoMap.entrySet()) {
            List<TimeSlotVO> timeSlotVOList = new ArrayList<>();
            if (CollectionUtils.isEmpty(entry.getValue())) {
                continue;
            }
            for (TimeFragmentDTO timeSlotDTO : entry.getValue()) {
                if (null == entry.getValue()) {
                    continue;
                }
                timeSlotVOList.add(new TimeSlotVO(timeSlotDTO));
            }
            availableTimeVoMap.put(entry.getKey(), timeSlotVOList);
        }
        return availableTimeVoMap;
    }

    private static List<StoreSkuPropertyVO> convertProperties(List<ProductPropertyDTO> productPropertyDTOList) {
        List<StoreSkuPropertyVO> storeSkuPropertyVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(productPropertyDTOList)) {
            return storeSkuPropertyVOList;
        }

        if (CollectionUtils.isNotEmpty(productPropertyDTOList)) {
            for (ProductPropertyDTO productPropertyDTO : productPropertyDTOList) {
                if (productPropertyDTO != null) {
                    storeSkuPropertyVOList.add(new StoreSkuPropertyVO(productPropertyDTO));
                }
            }
        }
        return storeSkuPropertyVOList;
    }

    private static List<StoreSkuPropertyVO> convertPropertiesForBiz(List<StoreSkuPropertyDTO> productPropertyDTOList) {
        List<StoreSkuPropertyVO> storeSkuPropertyVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(productPropertyDTOList)) {
            return storeSkuPropertyVOList;
        }

        if (CollectionUtils.isNotEmpty(productPropertyDTOList)) {
            for (StoreSkuPropertyDTO productPropertyDTO : productPropertyDTOList) {
                if (productPropertyDTO != null) {
                    storeSkuPropertyVOList.add(new StoreSkuPropertyVO(productPropertyDTO));
                }
            }
        }
        return storeSkuPropertyVOList;
    }


    public Map<ChannelStoreSkuKey, ChannelSkuVO> getChannelSkuMap() {

        if (CollectionUtils.isEmpty(this.channelSpuList)) {
            return Maps.newHashMap();
        }

        Map<ChannelStoreSkuKey, ChannelSkuVO> channelSkuMap = Maps.newHashMap();
        for (ChannelSpuVO channelSpuVO : this.channelSpuList) {

            List<ChannelSkuVO> channelSkuList = channelSpuVO.getChannelSkuList();

            if (CollectionUtils.isEmpty(channelSkuList)) {
                continue;
            }

            for (ChannelSkuVO channelSkuVO : channelSkuList) {
                Long storeId = this.store != null ? this.store.getStoreId() : null;
                ChannelStoreSkuKey key = ChannelStoreSkuKey.of(storeId, channelSpuVO.getChannelId(), channelSkuVO.getSkuId());
                channelSkuMap.put(key, channelSkuVO);
            }
        }

        return channelSkuMap;
    }

    public static List<StoreSpuVO> ofBizDTOList(List<StoreSpuDto> storeSpuDTOList,
                                             Map<StoreSpuKeyDTO, PlatformSoldOutInfoDTO> storeSpuAbnormalInfoMap) {
        if (CollectionUtils.isEmpty(storeSpuDTOList)) {
            return Lists.newArrayList();
        }

        return storeSpuDTOList.stream().filter(Objects::nonNull)
                .map(spuDTO -> StoreSpuVO.ofBizDTO(spuDTO, storeSpuAbnormalInfoMap.get(StoreSpuKeyDTO.builder()
                        .storeId(spuDTO.getStore().getStoreId()).spuId(spuDTO.getSpuId()).build())))
                .collect(Collectors.toList())
                .stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static StoreSpuVO ofBizDTO(StoreSpuDto storeSpuDTO,
                                      PlatformSoldOutInfoDTO soldOutInfoDTO) {
        if (storeSpuDTO == null) {
            return null;
        }
        StoreSpuVO storeSpuVO = new StoreSpuVO();
        storeSpuVO.setTenantId(storeSpuDTO.getTenantId());
        storeSpuVO.setSpuId(storeSpuDTO.getSpuId());
        storeSpuVO.setName(storeSpuDTO.getName());
        storeSpuVO.setStore(StoreVO.ofDTO(storeSpuDTO.getStore()));
        storeSpuVO.setRegion(RegionVO.ofDTO(storeSpuDTO.getRegion()));
        storeSpuVO.setImages(storeSpuDTO.getImageUrls());
        storeSpuVO.setWeightType(storeSpuDTO.getWeightType());

        storeSpuVO.setAvailableTimes(convertAvailableTimeForBiz(storeSpuDTO.getAvailableTimes()));
        storeSpuVO.setSpecialty(storeSpuDTO.getSpecialty());

        // 属性、卖点、描述
        storeSpuVO.setDescription(storeSpuDTO.getDescription());
        storeSpuVO.setCustomizedDescriptionFlag(storeSpuDTO.getCustomizedDescriptionFlag());
        storeSpuVO.setTenantDescription(storeSpuDTO.getTenantDescription());
        storeSpuVO.setCustomizedPropertiesFlag(storeSpuDTO.getCustomizedPropertiesFlag());
        storeSpuVO.setTenantProperties(convertPropertiesForBiz(storeSpuDTO.getTenantProperties()));
        storeSpuVO.setProperties(convertPropertiesForBiz(storeSpuDTO.getProperties()));
        storeSpuVO.setSellPoint(storeSpuDTO.getSellPoint());
        storeSpuVO.setCustomizedSellPointFlag(storeSpuDTO.getCustomizedSellPointFlag());
        storeSpuVO.setTenantSellPoint(storeSpuDTO.getTenantSellPoint());

        storeSpuVO.setChannelId2FirstFrontCategoryNameMap(storeSpuDTO.getChannelId2FirstFrontCategoryNameMap());

        storeSpuVO.setProducingPlace(storeSpuDTO.getProducingPlace());
        storeSpuVO.setCategory(CategoryVO.ofDTO(storeSpuDTO.getCategory()));
        storeSpuVO.setBrand(BrandVO.ofDTO(storeSpuDTO.getBrand()));

        storeSpuVO.setBooth(BoothVO.ofDTO(storeSpuDTO.getBooth()));

        // 标签信息转换
        storeSpuVO.setTagList(SpuTagVO.ofDTOListForBiz(storeSpuDTO.getTagList()));

        storeSpuVO.setStoreSkuList(StoreSkuVO.ofBizDTOList(storeSpuDTO.getStoreSkuList()));
        storeSpuVO.setChannelSpuList(ChannelSpuVO.ofBizDTOList(storeSpuDTO.getChannelSpuList(), soldOutInfoDTO, storeSpuDTO.getChannelAuditDTOList()));

        final boolean qnhRequest = MccConfigUtil.isQnhRequest(storeSpuDTO.getTenantId(),
                Optional.ofNullable(storeSpuDTO.getStore())
                        .map(com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreDTO::getStoreId).orElse(null));

        fillChannelSkuStockDesc(storeSpuVO.getChannelSpuList(), storeSpuVO.getStoreSkuList(), qnhRequest);

        // 商品SPU月销量
        storeSpuVO.setMonthSaleAmount(storeSpuDTO.getMonthSaleAmount() == null ? 0 : storeSpuDTO.getMonthSaleAmount());

        if (storeSpuDTO.getCustomizedNameFlag()) {
            storeSpuVO.setCustomizedName(storeSpuDTO.getName());
        }
        storeSpuVO.setCustomizedNameFlag(storeSpuDTO.getCustomizedNameFlag());
        storeSpuVO.setCustomizedPicFlag(storeSpuDTO.getCustomizedPicFlag());
        storeSpuVO.setCustomizedImageUrlList(storeSpuDTO.getCustomizedImageUrlList());
        storeSpuVO.setTenantImageUrlList(storeSpuDTO.getTenantImageUrlList());
        storeSpuVO.setMerchantSpuName(storeSpuDTO.getMerchantSpuName());
        storeSpuVO.setRegionSpuName(storeSpuDTO.getRegionSpuName());
        storeSpuVO.setStockDesc(qnhRequest ? CommonConstants.STOCK_IGNORE_CHAR : fillStockDesc(storeSpuDTO));
        //视频图详
        storeSpuVO.setVideo(VideoInfoVO.of(storeSpuDTO.getVideo()));
        storeSpuVO.setTenantVideo(VideoInfoVO.of(storeSpuDTO.getTenantVideo()));
        storeSpuVO.setPictureContents(storeSpuDTO.getPictureContents());
        storeSpuVO.setTenantPictureContents(storeSpuDTO.getTenantPictureContents());
        storeSpuVO.setCustomizedVideoFlag(storeSpuDTO.getCustomizedVideoFlag());
        storeSpuVO.setCustomizedPicContentFlag(storeSpuDTO.getCustomizedPicContentsFlag());
        storeSpuVO.setSpecType(storeSpuDTO.getSpecType());
        storeSpuVO.setStoreSaleStatus(storeSpuDTO.getSaleStatus());
        //分渠道定价设置
        fillMultiChannelPriceType(storeSpuVO.getStoreSkuList(), storeSpuVO.getChannelSpuList());

        List<String> delImgList = Optional.ofNullable(storeSpuDTO.getChannelSpuList()).orElse(Lists.newArrayList())
                .stream()
                .map(com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelSpuDTO::getAuditDelImgList)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(List::stream)
                .collect(Collectors.toList());
        List<ImageInfoVO> imageInfoVOS = Optional.ofNullable(storeSpuDTO.getImageUrls()).orElse(Lists.newArrayList())
                .stream()
                .map(imgUrl -> {
                    return ImageInfoVO.builder()
                            .imgUrl(imgUrl)
                            .isDelete(delImgList.contains(imgUrl))
                            .build();
                }).collect(Collectors.toList());
        storeSpuVO.setImageUrlInfos(imageInfoVOS);

        // 自定义图片和门店图片取值一致
        if (BooleanUtils.isTrue(storeSpuDTO.getCustomizedPicFlag())) {
            storeSpuVO.setCustomizedImgInfoList(imageInfoVOS);
        }

        storeSpuVO.setJdSaleAttrList(ConverterUtils.convertList(storeSpuDTO.getSpuSaleAttributeDTOS(), SaleAttrVo::fromDTO));

        if (CollectionUtils.isNotEmpty(storeSpuDTO.getStoreCategories())){
            storeSpuVO.setStoreCategories(Fun.map(storeSpuDTO.getStoreCategories(), FrontCategorySimpleVO::new));
        }
        if (CollectionUtils.isNotEmpty(storeSpuDTO.getMerchantStoreCategories())){
            storeSpuVO.setMerchantStoreCategories(Fun.map(storeSpuDTO.getMerchantStoreCategories(), FrontCategorySimpleVO::new));
        }
        if (CollectionUtils.isNotEmpty(storeSpuDTO.getPoiStoreCategories())){
            storeSpuVO.setPoiStoreCategories(Fun.map(storeSpuDTO.getPoiStoreCategories(), FrontCategorySimpleVO::new));
        }

        //增加加盟主字段
        storeSpuVO.setControlProduct(storeSpuDTO.getControlProduct());
        storeSpuVO.setControlGoods(storeSpuDTO.getControlGoods());
        storeSpuVO.setJoinAreaId(storeSpuDTO.getJoinAreaId());
        storeSpuVO.setJoinAreaName(storeSpuDTO.getJoinAreaName());
        storeSpuVO.setFranchisorControlProductSpuId(storeSpuDTO.getFranchisorControlProductSpuId());
        storeSpuVO.setUnderControl(storeSpuDTO.getUnderControl());
        storeSpuVO.setAbnormalCodes(storeSpuDTO.getAbnormalCodes());
        if (CollectionUtils.isNotEmpty(storeSpuDTO.getNoSaleChannelInfoList())) {
            storeSpuVO.setNoSaleChannelInfoList(NoSaleChannelInfoVO.ofList(storeSpuDTO.getNoSaleChannelInfoList()));
        }
        storeSpuVO.setAbnormalMarkMsg(storeSpuDTO.getAbnormalMarkMsg());
        storeSpuVO.setAbnormalTips(storeSpuDTO.getAbnormalTips());
        storeSpuVO.setJumpPageType(storeSpuDTO.getJumpPageType());
        storeSpuVO.setDiffCompareTypeList(storeSpuDTO.getDiffCompareTypeList());

        // 美团商品标签
        storeSpuVO.setMtLabelList(Fun.map(storeSpuDTO.getMtLabelDTOList(), MtLabelVO::of));
        storeSpuVO.setTenantCreateSource(storeSpuDTO.getTenantCreateSource());

        storeSpuVO.setChannelSaleAttrInfoList(Fun.map(storeSpuDTO.getChannelSaleAttributes(), ChannelSaleAttrInfoVO::of));

        return storeSpuVO;
    }
}
