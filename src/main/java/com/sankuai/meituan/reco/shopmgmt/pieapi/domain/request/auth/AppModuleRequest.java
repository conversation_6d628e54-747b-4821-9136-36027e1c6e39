package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.auth;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@TypeDoc(
        description = "App模块请求"
)
@Data
@ApiModel("App模块请求")
public class AppModuleRequest {

    @FieldDoc(
            description = "门店id"
    )
    @ApiModelProperty(value = "实体Id")
    private long entityId;

    @FieldDoc(
            description = "实体类型，1-前置仓，2-普通仓，3-门店，4-无人仓"
    )
    @ApiModelProperty(value = "实体类型，1-前置仓，2-普通仓，3-门店，4-无人仓")
    private int entityType = 3;
}
