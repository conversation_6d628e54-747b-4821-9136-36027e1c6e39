package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.delivery;

import java.util.List;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.GiftVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.ProductVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.RevenueDetailVo;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.TagInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.exception.DeliveryExceptionSummaryVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 骑手配送单.
 *
 * <AUTHOR>
 * @since 2021/6/11 15:37
 */
@TypeDoc(
        description = "骑手配送单信息"
)
@ApiModel("骑手配送单信息")
@Data
public class RiderDeliveryOrderVo {

    @FieldDoc(
            description = "运单ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "运单ID", required = true)
    private Long deliveryOrderId;

    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "租户ID", required = true)
    private Long tenantId;

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道ID", required = true)
    private Integer channelId;

    @FieldDoc(
            description = "渠道名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道名称", required = true)
    private String channelName;

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店ID", required = true)
    private Long storeId;

    @FieldDoc(
            description = "门店名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店名称", required = true)
    private String storeName;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道订单号", required = true)
    private String channelOrderId;

    @FieldDoc(
            description = "订单流水", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单流水", required = true)
    private Long serialNo;

    @FieldDoc(
            description = "配送订单类型  1-立即单 2-预约单", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "配送订单类型   1-立即单 2-预约单", required = true)
    private Integer deliveryOrderType;

    @FieldDoc(
            description = "配送状态 30-待领取 40-骑手已接单 50-骑手已取货", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "配送状态 30-待领取 40-骑手已接单 50-骑手已取货", required = true)
    private Integer deliveryStatus;

    @FieldDoc(
            description = "配送订单类型名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "配送订单类型名称", required = true)
    private String deliveryOrderTypeName;

    @FieldDoc(
            description = "预计送达时间开始时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "预计送达时间开始时间", required = true)
    private Long estimateArriveTimeStart;

    @FieldDoc(
            description = "预计送达时间截止时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "预计送达时间截止时间", required = true)
    private Long estimateArriveTimeEnd;

    @FieldDoc(
            description = "订单创建时间戳", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单创建时间戳", required = true)
    private Long createTime;

    @FieldDoc(
            description = "配送方式,0-未知，1-配送到家，2-到店自提", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "配送方式,0-未知，1-配送到家，2-到店自提", required = true)
    private Integer deliveryMethod;

    @FieldDoc(
            description = "改派前的骑手姓名", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "改派前的骑手姓名", required = false)
    private String fromRiderName;

    @FieldDoc(
            description = "配送方式描述", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "配送方式描述", required = true)
    private String deliveryMethodDesc;

    @FieldDoc(
            description = "订单用户ID、0或-1为无效ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "订单用户ID、0或-1为无效ID")
    private Long userId;

    // 收货人信息 start

    @FieldDoc(
            description = "收货人姓名", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "收货人姓名", required = true)
    private String receiverName;

    @FieldDoc(
            description = "收货人电话号码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "收货人电话号码", required = true)
    private String receiverPhone;

    @FieldDoc(
            description = "收货人地址", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "收货人地址", required = true)
    private String receiverAddress;

    @FieldDoc(
            description = "收货人定位经度，精确到小数点后六位", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "收货人定位经度", required = true)
    private String receiverLongitude;

    @FieldDoc(
            description = "收货人定位纬度，精确到小数点后六位", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "收货人定位纬度", required = true)
    private String receiverLatitude;

    // 收货人信息 end

    // 商品信息 start

    @FieldDoc(
            description = "商品总数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "商品总数量", required = true)
    private Integer itemCount;

    @FieldDoc(
            description = "备注", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "备注", required = false)
    private String comments;

    @FieldDoc(
            description = "商品信息列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "商品信息列表", required = true)
    private List<ProductVO> productList;

    @FieldDoc(
            description = "订单营收数据", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "订单营收数据")
    private RevenueDetailVo revenueDetail;

    @FieldDoc(
            description = "提报价模式,订单线下价格总和", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "提报价模式,订单线下价格总和", required = false)
    private Integer totalOfflinePrice;

    @FieldDoc(
            description = "赠品信息列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "赠品信息列表", required = false)
    private List<GiftVO> giftVOList;

    @FieldDoc(
            description = "赠品总数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "赠品总数量", required = false)
    private Integer giftCount;

    // 商品信息 end

    @FieldDoc(
            description = "支付时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "支付时间", required = false)
    private Long payTime;


    @FieldDoc(
            description = "用户标签信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "用户标签信息")
    private List<TagInfoVO> userTags;

    @FieldDoc(
            description = "用户类型信息 10-普通用户, 15-会员用户", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "用户类型信息 10-普通用户, 15-会员用户")
    private Integer orderUserType;

    @FieldDoc(
            description = "运单状态是否被锁定：0-未锁定；1-锁定",
            example = {}
    )
    @ApiModelProperty(value = "运单状态是否被锁定：0-未锁定；1-锁定")
    public Integer deliveryStatusLocked;

    @FieldDoc(
            description = "运单状态是否可以被锁定：0-不可以；1-可以",
            example = {}
    )
    @ApiModelProperty(value = "运单状态是否可以被锁定：0-不可以；1-可以")
    public Integer canStatusBeLocked;

    @FieldDoc(
            description = "骑手上报异常",
            example = {}
    )
    @ApiModelProperty(value = "骑手上报异常")
    public DeliveryExceptionSummaryVO deliveryExceptionSummaryVOS;

    @FieldDoc(
            description = "考核送达截止时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "考核送达截止时间")
    private Long evaluateArriveDeadline;

    @FieldDoc(
            description = "考核送达剩余时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "考核送达剩余时间")
    private Long evaluateArriveLeftTime;

    @FieldDoc(
            description = "考核送达超时时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "考核送达超时时间")
    private Long evaluateArriveTimeout;

    @FieldDoc(
            description = "配送距离，单位米", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "配送距离", required = false)
    private Long deliveryDistance;

    @FieldDoc(
            description = "是否能上传送达照片", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否能上传送达照片", required = false)
    private Boolean couldPostDeliveryProofPhoto;

    @FieldDoc(
            description = "可以展示的按钮列表,100-转青云配送 110-转自配", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "可以展示的按钮列表, 100-转青云配送 110-转自配", required = false)
    private List<Integer> couldOperateItemList;

    @FieldDoc(
            description = "是否需要填写问卷", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否需要填写问卷", required = false)
    private Boolean isNeedAnswerQuestionnaire;

    @FieldDoc(
            description = "是否有缺货情况", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否有缺货情况", required = false)
    private Boolean hasLackGoods;

    @FieldDoc(
            description = "代收点", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "代收点", required = false)
    private String signingPoint;

    @FieldDoc(
            description = "是否包含高价值商品", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否包含高价值商品", required = false)
    private Boolean isContainsHighWaxGoods;


    @FieldDoc(
            description = "场景，如:餐馆", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "场景，如:餐馆", required = false)
    private String scene;

    @FieldDoc(
            description = "订单流水（新）"
    )
    @ApiModelProperty(value = "订单流水（新）")
    private String daySeqNum;

    @FieldDoc(
            description = "是否为美团名酒馆订单，true：是"
    )
    private Boolean isMtFamousTavern;

    @FieldDoc(
            description = "收货地址后跳转导航toast提示文案"
    )
    private String jumpNavigationErrorMessage;
}
