package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.purchase;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2024/7/29
 * 供应商货品
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@ToString
@Builder
public class SupplierGoodsVO {
    /**
     * 租户ID
     */
    @FieldDoc(
            description = "租户ID",
            requiredness = Requiredness.REQUIRED
    )
    private Long tenantId;
    /**
     * 供应商货品ID
     */
    @FieldDoc(
            description = "供应商货品ID",
            requiredness = Requiredness.REQUIRED
    )
    private String goodsId;

    @FieldDoc(description = "货品类型；0：渠道货品，1：外部平台货品")
    private Integer type;
    /**
     * 供应链渠道商品
     */
    @FieldDoc(
            description = "供应链渠道商品",
            requiredness = Requiredness.OPTIONAL
    )
    private ScmChannelGoodsVO channelGoods;

    @FieldDoc(description = "供应商商品资料")
    private SupplierGoodsInfoVO supplierGoodsInfo;
}
