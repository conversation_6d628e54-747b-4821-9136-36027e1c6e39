package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022-12-15
 * @email <EMAIL>
 */
@TypeDoc(
        description = "工作类型vo"
)
@Data
@ApiModel("工作类型vo")
@AllArgsConstructor
@NoArgsConstructor
public class WorkTypeVO {

    private Long id;

    private String name;

}
