package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@TypeDoc(
        description = "门店信息"
)
@Data
@ApiModel("门店信息")
public class StoreRequest {
    @FieldDoc(
            description = "门店编号"
    )
    @ApiModelProperty(value = "门店编号", required = true)
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "全部门店列表"
    )
    @ApiModelProperty(value = "门店编号", required = true)
    private List<Long> storeIds;
}
