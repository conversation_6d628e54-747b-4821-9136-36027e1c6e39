package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.dianping.cat.Cat;
import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.linz.boot.util.Fun;
import com.meituan.shangou.saas.tenant.thrift.dto.resource.module.AppModuleDto;
import com.meituan.shangou.sac.dto.model.SacMenuNodeDto;
import com.meituan.shangou.sac.dto.model.SacMenuNodeWithChild;
import com.meituan.shangou.sac.infrastructure.lion.SacLionConfig;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.AppAuthIdConstants;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.AuthResourceType;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.IntegerBooleanConstants;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.auth.AppModuleRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.auth.BatchAuthCodeCheckRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.auth.DataAuthResourceRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.auth.AppModuleResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.auth.AppModuleResult.AppModule;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.auth.BatchAuthCodeCheckResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.FallbackException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.appmodel.workbench.PendingTaskHelper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.PendingTaskAsyncService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.PieApiMccUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.store.saas.infrastructure.shield.common.monitor.CommonMonitorTransaction;
import com.sankuai.meituan.shangou.empower.auth.sdk.bean.AppIdEnum;
import com.sankuai.meituan.shangou.empower.auth.sdk.bean.SessionInfo;
import com.sankuai.meituan.shangou.empower.auth.sdk.context.SessionContext;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.AccountTypeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.PermissionGroupTypeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ValidTypeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.service.AuthThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.*;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.request.AuthPermissionAndDataAuthRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.request.QueryAccountListByPoiAndAuthRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.request.QueryAccountValueVoListByRoleInfoRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.request.UpdateTenantAccountStatusRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.response.AuthPermissionAndDataAuthResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.meituan.reco.shopmgmt.pieapi.constants.MccKeyEnum.OTHER_MODULE_ICON;
import static com.sankuai.meituan.reco.shopmgmt.pieapi.constants.MccKeyEnum.OTHER_MODULE_NAME;
import static com.sankuai.meituan.util.ConfigUtilAdapter.getString;


/**
 * 权限相关
 * Created by yangli on 19/1/30.
 * Mod by linjiayu
 */
@Component
@Slf4j
@Rhino
public class AuthThriftWrapper {

    private static final String DEFAULT_PARENT_MENU_NAME = "其他";

    private static final int MAX_QUERY_COUNT = 5;

    @Resource
    private AuthThriftService.Iface authThriftService;

    @Autowired
    private PendingTaskAsyncService pendingTaskAsyncService;

    @Autowired
    private TenantWrapper tenantWrapper;

    @Autowired
    private SacWrapper sacWrapper;

    @Autowired
    private PendingTaskHelper pendingTaskHelper;


    @Degrade(rhinoKey = "AuthThriftWrapper.dataAuthResource",
            fallBackMethod = "dataAuthResourceFallback",
            timeoutInMilliseconds = 1200,
            ignoreExceptions = {CommonLogicException.class})
    @MethodLog(logRequest = true, logResponse = true)
    @CommonMonitorTransaction
    public List<DataPermissionVo> dataAuthResource(DataAuthResourceRequest req, User user) {
        final String errorMsg = "查询权限失败";
        try {
            SessionInfo sessionInfo = SessionContext.getCurrentSession();
            com.sankuai.meituan.shangou.empower.auth.thrift.vo.DataAuthResourceRequest request = new com.sankuai.meituan.shangou.empower.auth.thrift.vo.DataAuthResourceRequest();
            request.setTenantId(user.getTenantId());
            request.setAccountId(sessionInfo.getAccountId());
            request.setAppId(sessionInfo.getAuthAppId());
            request.setCode(req.getCode());
            request.setType(req.getType());

            DataAuthResourceResponse result = authThriftService.dataAuthResource(request);
            nullResponseCheck(result, errorMsg);
            validateStatus(result.getResult(), errorMsg);
            return result.getDataPermissions();
        } catch (TException e) {
            throw new RuntimeException(errorMsg, e);
        }
    }

    @SuppressWarnings("unused")
    private List<DataPermissionVo> dataAuthResourceFallback(DataAuthResourceRequest req, User user) {
        throw new FallbackException("AuthThriftWrapper.dataAuthResource降级");
    }


    @Degrade(rhinoKey = "AuthThriftWrapper.appModule",
            fallBackMethod = "appModuleFallback",
            timeoutInMilliseconds = 3000, isDegradeOnException = true,
            ignoreExceptions = CommonLogicException.class)
    @MethodLog(logRequest = true, logResponse = true)
    @CommonMonitorTransaction
    public AppModuleResult appModule(AppModuleRequest req, Long tenantId, List<Long> storeIdList, List<String> matchParentCode) {
        try {
            if (MccConfigUtil.useSacAuthentication()) {
                return newAppModule(req, tenantId, storeIdList, matchParentCode);
            }
            // 应用信息
            int appId = SessionContext.getCurrentSession().getAppId();
            //  查询登录账户信息
            AccountInfoVo account = getCurrentAccount();
            //  查询权限code列表
            ResourceData resourceData = getPermissionCodes(account, appId, matchParentCode);

            //  查询权限对应模块
            List<AppModuleDto> modules = getAppModules(storeIdList, resourceData.getAuthCodes());
            //  查询模块对应任务量
            Map<String, PendingTaskResult> pendingTaskMap = getPendingTaskMap(tenantId, storeIdList, req, modules);
            //  转换响应对象
            AppModuleResult appModuleResult =  transform(modules, pendingTaskMap, resourceData);
            // 如果是多门店模式，需要过滤不支持多门店的叶子模块(也过滤不包含任何叶子节点的上层节点)
            filterAppModuleNotSupportMultiPoi(tenantId, appModuleResult.getModules());
            return appModuleResult;
        } catch (Exception e) {
            log.error("查询App模块异常, req:{}", req, e);
            throw new CommonLogicException("查询App模块异常", e);
        }
    }
    @SuppressWarnings("unused")
    private List<DataPermissionVo> appModuleFallback(AppModuleRequest req, Long tenantId, List<Long> storeIdList, List<String> matchParentCode) {
        throw new FallbackException("AuthThriftWrapper.appAuth降级");
    }
    @Degrade(rhinoKey = "AuthThriftWrapper.newAppModule",
            fallBackMethod = "newAppModuleFallback",
            timeoutInMilliseconds = 3000, isDegradeOnException = true,
            ignoreExceptions = CommonLogicException.class)
    @MethodLog(logRequest = true, logResponse = true)
    @CommonMonitorTransaction
    public AppModuleResult newAppModule(AppModuleRequest req, Long tenantId, List<Long> storeIdList, List<String> matchParentCode) {
        try {
            // 获取当前账号信息
            SessionInfo currentSession = SessionContext.getCurrentSession();
            // 应用信息
            int appId = ApiMethodParamThreadLocal.getIdentityInfo().getAuthId();
            //  查询用户菜单code列表
            Map<String, SacMenuNodeWithChild>  sacMenuNodeWithChildMap =
                    sacWrapper.getAccountMenusByMenuCodes(currentSession.getAccountId(), appId, matchParentCode);
            // 获取用户菜单列表
            List<String> accountMenuCodes = getAccountMenuCodes(sacMenuNodeWithChildMap);
            // 查询权限对应模块
            List<AppModuleDto> modules = getAppModules(storeIdList, accountMenuCodes);
            //  查询模块对应任务量
            Map<String, PendingTaskResult> pendingTaskMap = getPendingTaskMap(tenantId, storeIdList, req, modules);
            //  转换响应对象
            AppModuleResult appModuleResult =  transformByMenuNodeMap(modules, pendingTaskMap, sacMenuNodeWithChildMap);
            // 如果是多门店模式，需要过滤不支持多门店的叶子模块(也过滤不包含任何叶子节点的上层节点)
            filterAppModuleNotSupportMultiPoi(tenantId, appModuleResult.getModules());
            return appModuleResult;
        } catch (Exception e) {
            log.error("查询App模块异常, req:{}", req, e);
            throw new CommonLogicException("查询App模块异常", e);
        }
    }


    private AppModuleResult transformByMenuNodeMap(List<AppModuleDto> modules, Map<String, PendingTaskResult> pendingTaskMap, Map<String, SacMenuNodeWithChild> sacMenuNodeWithChildMap) {
        if (CollectionUtils.isEmpty(modules)) {
            return new AppModuleResult();
        }
        AppModuleResult result = new AppModuleResult();
        try {
            //  权限模块聚合
            Map<String, AppModuleDto> moduleDtoMap = modules.stream()
                    .filter(item -> StringUtils.isNotBlank(item.authCode))
                    .collect(Collectors.toMap(AppModuleDto::getAuthCode, item -> item));
            for (Map.Entry<String, SacMenuNodeWithChild> stringSacMenuNodeWithChildEntry : sacMenuNodeWithChildMap.entrySet()) {
                AppModule frontModule = new AppModule();
                recursiveMenuTree(stringSacMenuNodeWithChildEntry.getValue(), frontModule, moduleDtoMap, pendingTaskMap);
                result.getModules().add(frontModule);
            }
            result.getModules().sort(Comparator.comparing(AppModule::getSort));
            return checkRootModule(result);
        } catch (Exception e) {
            log.error("transform modules error", e);
            throw e;
        }
    }

    private void recursiveMenuTree(SacMenuNodeWithChild withChild, AppModule module,
                                   Map<String, AppModuleDto> moduleDtoMap, Map<String, PendingTaskResult> pendingTaskMap) {
        //  转换节点对象
        transform(withChild.getSacMenuNodeDto() , module, moduleDtoMap, pendingTaskMap);
        //  递归叶子节点
        List<SacMenuNodeDto> childSacMenuNodeDtos = withChild.getChildSacMenuNodeDtos();
        if (CollectionUtils.isNotEmpty(childSacMenuNodeDtos)) {
            List<AppModule> subModules = module.getSubModules();
            for (SacMenuNodeDto childSacMenuNodeDto : childSacMenuNodeDtos) {
                AppModule appModule = new AppModule();
                transform(childSacMenuNodeDto , appModule, moduleDtoMap, pendingTaskMap);
                subModules.add(appModule);
            }
            module.setSubModules(subModules);
        }
        //  前端与权限暂未打通sort字段，故合并权限接口先使用权限的rank排序
        module.getSubModules().sort(Comparator.comparingInt(AppModule::getSort));
        module.setHasAvailLeaf(hasAvailLeaf(module));
    }


    /**
     * 权限资源 => 前段目录节点
     *
     * @param sacMenuNodeDto     菜单信息
     * @param module         响应的模块树
     * @param moduleDtoMap   模块数据map
     * @param pendingTaskMap 待处理任务量map
     */
    private void transform(SacMenuNodeDto sacMenuNodeDto, AppModule module,
                           Map<String, AppModuleDto> moduleDtoMap, Map<String, PendingTaskResult> pendingTaskMap) {
        AppModuleDto appModule = moduleDtoMap.get(sacMenuNodeDto.getSacMenuCode());
        module.setCode(sacMenuNodeDto.getSacMenuCode());
        module.setSubModules(new ArrayList<>());
        module.setSort(sacMenuNodeDto.getRank());
        module.setParent(sacMenuNodeDto.getParentSacMenuCode());

        if (appModule != null) {
            PendingTaskResult pendingTask = pendingTaskMap.get(appModule.authCode);
            if (pendingTask != null) {
                module.setPendingTaskCount(pendingTask.getCount());
                module.setPendingTaskType(pendingTask.getType().type);
            }
            module.setJsName(appModule.jsName);
            module.setJsType(appModule.jsType);
            module.setIcon(appModule.iconUrl);
            module.setName(appModule.moduleName);
            module.setSupportMultiPoi(appModule.getSupportMultiPoi());
        } else {
            module.setName(sacMenuNodeDto.getSacMenuName());
        }
    }

    private List<String> getAccountMenuCodes(Map<String, SacMenuNodeWithChild> sacMenuNodeWithChildMap) {
        List<String> accountMenuCodes = new ArrayList<>();
        for (Map.Entry<String, SacMenuNodeWithChild> stringSacMenuNodeWithChildEntry : sacMenuNodeWithChildMap.entrySet()) {
            String key = stringSacMenuNodeWithChildEntry.getKey();
            SacMenuNodeWithChild value = stringSacMenuNodeWithChildEntry.getValue();
            SacMenuNodeDto sacMenuNodeDto = value.getSacMenuNodeDto();
            List<SacMenuNodeDto> childSacMenuNodeDtos = value.getChildSacMenuNodeDtos();
            if (sacMenuNodeDto.isHasAuth()){
                accountMenuCodes.add(value.getSacMenuNodeDto().getSacMenuCode());
            }

            if (CollectionUtils.isEmpty(childSacMenuNodeDtos)) {
                continue;
            }
            Set<String> childMenuCodes =
                    childSacMenuNodeDtos.stream().filter(SacMenuNodeDto::isHasAuth).map(SacMenuNodeDto::getSacMenuCode).collect(Collectors.toSet());
            accountMenuCodes.addAll(childMenuCodes);
        }

        return accountMenuCodes;

    }

    @SuppressWarnings("unused")
    private List<DataPermissionVo> newAppModuleFallback(AppModuleRequest req, Long tenantId, List<Long> storeIdList, List<String> matchParentCode) {
        throw new FallbackException("AuthThriftWrapper.newAppModuleFallback降级");
    }


    /**
     * 过滤不支持多门店的模块
     * @param appModules
     */
    private void filterAppModuleNotSupportMultiPoi(Long tenantId, List<AppModule> appModules) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        if (!identityInfo.isFullStoreMode()) {
            // 不是全部门店模式，就不用进行过滤
            return;
        }
        int appId = identityInfo.getAuthId();
        if (SacLionConfig.isSupportHierarchicalApp(String.valueOf(tenantId)) && appId != AppIdEnum.APP_5.getAuthAppId()) {
            // 如果支持了层级权限特性，则直接使用「全部门店」下有权限的模块即可，无需过滤
            // 全部门店下，当 subAuthId = 5时，说明还是使用的未升级的 MRN 包，或者不支持层级权限特性
            return;
        }
        if (CollectionUtils.isNotEmpty(appModules)) {
            Iterator<AppModule> moduleIterator  = appModules.iterator();
            while (moduleIterator.hasNext()) {
                AppModuleResult.AppModule appModule =  moduleIterator.next();
                if (CollectionUtils.isEmpty(appModule.getSubModules())) {
                    // 只有显性支持多门店才不会被删
                    if (!IntegerBooleanConstants.BOOLEAN_TRUE.equals(appModule.getSupportMultiPoi())) {
                        moduleIterator.remove();
                    }
                } else {
                    filterAppModuleNotSupportMultiPoi(tenantId, appModule.getSubModules());
                }
            }
        }
    }

    @MethodLog(logRequest = true, logResponse = true)
    public <T> List<T> queryPermissionGroupId(User user, PermissionGroupTypeEnum type, Function<String, T> transfer) {
        QueryPermissionGroupRequest request = new QueryPermissionGroupRequest();
        request.setTenantId(user.getTenantId());
        request.setAccountId(user.getAccountId());
        request.setType(type.getValue());

        try {
            log.info("authThriftService, req:{}", request);
            QueryPermissionGroupResponse response = authThriftService.queryPermissionGroupByTokenAndPermissionType(request);
            log.info("authThriftService, response:{}", response);
            if (CollectionUtils.isEmpty(response.getPermissionGroupCodeList())) {
                log.warn("authThriftService, 返回AccountPermissionGroupRelVo为空");
                return Lists.newArrayList();
            }

            return response.getPermissionGroupCodeList().stream().map(PermissionGroupVo::getCode).map(transfer).collect(Collectors.toList());

        } catch (TException e) {
            log.error("authThriftService.queryAccountBindPermissionGroupByResourceCode()错误:", e);
            return Lists.newArrayList();
        }
    }

    /**
     * 查询当前用户拥有的所有权限的所有 POI 实体类型的 poi id，目前包括门店、中心仓、共享前置仓
     * 注：此接口中为 POI 维度的接口，当后续 POI 实体类型增加时，这里也会对应添加
     */
    @MethodLog(logRequest = true, logResponse = true)
    public List<Long> queryAllPoiPermissionOfCurrentUser(User user, int appId) {
        return queryPermissionInfoOfCurrentUser(user, appId,
                Arrays.asList(PermissionGroupTypeEnum.POI, PermissionGroupTypeEnum.SHAREABLE_WAREHOUSE));
    }

    /**
     * 查询有权限的资源编码
     * @return 资源编码列表
     */
    public List<Long> queryPermissionInfoOfCurrentUser(User user, int appId, List<PermissionGroupTypeEnum> typeList) {
        return queryPermissionGroupId(user, appId, typeList, Long::valueOf);
    }

    @MethodLog(logRequest = true, logResponse = true)
    public <T> List<T> queryPermissionGroupId(User user, int appId, List<PermissionGroupTypeEnum> types, Function<String, T> transfer) {
        BatchQueryPermissionGroupRequest request = new BatchQueryPermissionGroupRequest();
        request.setTenantId(user.getTenantId());
        request.setAccountId(user.getAccountId());
        request.setAppId(appId);
        request.setTypeList(Fun.map(types, PermissionGroupTypeEnum::getValue));

        try {
            log.info("authThriftService, req:{}", request);
            QueryPermissionGroupResponse response = authThriftService.batchQueryPermissionGroupByTokenAndPermissionType(request);
            log.info("authThriftService, response:{}", response);
            if (CollectionUtils.isEmpty(response.getPermissionGroupCodeList())) {
                log.warn("authThriftService, 返回AccountPermissionGroupRelVo为空");
                return Lists.newArrayList();
            }

            return response.getPermissionGroupCodeList().stream().map(PermissionGroupVo::getCode).map(transfer).collect(Collectors.toList());

        } catch (TException e) {
            log.error("authThriftService.queryAccountBindPermissionGroupByResourceCode()错误:", e);
            return Lists.newArrayList();
        }
    }


    /**
     * 查询权限code对应模块未完成订单量
     *
     * @param req     http req
     * @param modules 模块信息
     * @return 未完成订单量map
     */

    private Map<String, PendingTaskResult> getPendingTaskMap(Long tenantId, List<Long> storeIdList, AppModuleRequest req, List<AppModuleDto> modules) {
        if (CollectionUtils.isEmpty(modules)) {
            return Maps.newHashMap();
        }
        try {
            List<String> fromLionAuthCodes = pendingTaskHelper.getAuthCodesFromLion();
            //  查询未完成任务code
            Set<String> authCodes = modules.stream()
                    .map(AppModuleDto::getAuthCode)
                    .filter(authCode -> {
                        if(AuthCodeEnum.authOf(authCode) != null){
                            return true;
                        }else {
                            return fromLionAuthCodes.contains(authCode);
                        }
                    }).collect(Collectors.toSet());
            //  异步查询任务未完成量
            return pendingTaskAsyncService.queryPendingTaskCount(
                    tenantId, req.getEntityId(), req.getEntityType(), storeIdList, authCodes
            );
        } catch (Exception e) {
            log.error("查询待处理任务角标异常", e);
            return Maps.newHashMap();
        }
    }

    /**
     * 查询权限对应模块信息
     *
     * @param storeIdList     门店id列表
     * @param permissionCodes 权限codes
     * @return 模块list
     */
    private List<AppModuleDto> getAppModules(List<Long> storeIdList, List<String> permissionCodes) {
        //  支持查询公共模块
        List<AppModuleDto> appModules = tenantWrapper.queryAppModules(storeIdList, permissionCodes);
        return appModules;
    }

    /**
     * 查询登录账户信息
     *
     * @return account info
     * @throws TException Any
     */
    @Degrade(rhinoKey = "AuthThriftWrapper.getCurrentAccount",
            fallBackMethod = "getCurrentAccountFallback",
            timeoutInMilliseconds = 3000,
            ignoreExceptions = CommonLogicException.class)
    @MethodLog(logRequest = true, logResponse = true)
    @CommonMonitorTransaction
    @SuppressWarnings("WeakerAccess")
    public AccountInfoVo getCurrentAccount() throws TException {
        SessionInfo sessionInfo = SessionContext.getCurrentSession();
        QueryAccountInfoResponse resp = authThriftService.queryAccountInfoByAccountIdAndAppId(
                new QueryAccountInfoRequest(sessionInfo.getAccountId(), sessionInfo.getTenantId())
        );
        log.info("account resp, tenantId:{}, accountId:{}, resp:{}", sessionInfo.getTenantId(), sessionInfo.getAccountId(), resp);
        nullResponseCheck(resp, "查询权限异常");
        validateStatus(resp.getResult(), "查询权限异常");
        return resp.getAccountInfo();
    }


    public AccountInfoVo getCurrentAccountWithoutPermission() throws TException {
        SessionInfo sessionInfo = SessionContext.getCurrentSession();

        QuerySimpleAccountInfoRequest queryRequest = new QuerySimpleAccountInfoRequest();
        queryRequest.setAccountIds(Arrays.asList(sessionInfo.getAccountId()));
        QuerySimpleAccountInfoListResponse response = authThriftService.querySimpleAccountInfoList(queryRequest);
        nullResponseCheck(response, "查询权限异常");
        validateStatus(response.getResult(), "查询权限异常");
        if (CollectionUtils.isEmpty(response.getAccountInfoList())) {
            log.error("账号不存在, accountId:{}", sessionInfo.getAccountId());
            throw new CommonLogicException("账号不存在");
        }
        return response.getAccountInfoList().get(0);
    }


    /**
     * 获取登录账号所有权限码
     *
     * @return account info
     * @throws TException Any
     */
    @Degrade(rhinoKey = "AuthThriftWrapper.getCurrentAccountAllPermissions",
            fallBackMethod = "getCurrentAccountAllPermissionCodesFallback",
            timeoutInMilliseconds = 3000,
            ignoreExceptions = CommonLogicException.class)
    @MethodLog(logRequest = true, logResponse = true)
    @CommonMonitorTransaction
    @SuppressWarnings("WeakerAccess")
    @Deprecated
    public List<String> getCurrentAccountAllPermissionCodes() {
        try {
            AccountInfoVo accountInfoVo = getCurrentAccount();

            if (Objects.isNull(accountInfoVo) ||
                    CollectionUtils.isEmpty(accountInfoVo.getRoleList())) {
                return Collections.EMPTY_LIST;
            }

            List<String> permissionCodeList = Lists.newArrayList();
            for (RoleInfoVo roleInfoVo : accountInfoVo.getRoleList()) {
                for (PermissionVo permissionVo : roleInfoVo.getPermissionList()) {
                    if (AppAuthIdConstants.SHU_GUO_PAI.val() == permissionVo.getAppId()) {
                        permissionCodeList.add(permissionVo.getCode());
                    }
                }
            }
            return permissionCodeList;
        } catch (TException e) {
            throw new RuntimeException("查询权限异常", e);
        }
    }


    private List<DataPermissionVo> getCurrentAccountAllPermissionCodesFallback() {
        throw new FallbackException("AuthThriftWrapper.getCurrentAccountAllPermissionCodesFallback");
    }

    /**
     /**
     * 查询租户的所有店长
     *
     * @return account info
     */
    public List<AccountValueVo> queryStoreManagerInfos(Long tenantId, List<Long> accountIds) {
        Set<AccountValueVo> accountInfoSet = new HashSet<>();
        //如果租户对应的店长角色信息存在，取租户的，否则，取超市新供给业务类型对应的
        List<String> tenantStoreManagerTitleList = PieApiMccUtils.getStoreManagerTitleListByTargetKey(String.valueOf(tenantId));
        List<String> storeManagerTitleList = CollectionUtils.isEmpty(tenantStoreManagerTitleList)
                ? PieApiMccUtils.getStoreManagerTitleListByTargetKey(String.valueOf(PieApiMccUtils.getSupermarketNewSupplyBizType()))
                : tenantStoreManagerTitleList;
        for (String storeManger : storeManagerTitleList) {
            QueryAccountValueVoListByRoleInfoRequest request = new QueryAccountValueVoListByRoleInfoRequest();
            request.setTenantId(tenantId);
            request.setRoleName(storeManger);
            request.setAccountIds(accountIds);
            QueryAccountValueVoListByRoleInfoResponse resp = null;
            try {
                resp = authThriftService.queryAccountValueVoListByRoleInfo(request);
                log.info("queryAccountValueVoListByRoleInfo resp, request:{}, resp:{}", request, resp);
                nullResponseCheck(resp, "查询角色" + storeManger + "下的账号信息异常");
                validateStatus(resp.getResult(), "查询角色" + storeManger + "下的账号信息异常");
                if (CollectionUtils.isNotEmpty(resp.getAccountValueVoList())) {
                    accountInfoSet.addAll(resp.getAccountValueVoList());
                }
            } catch (CommonLogicException e) {
                log.warn("查询角色" + storeManger + "下的账号信息异常: request [{}].", request, e);
            } catch (Exception e) {
                log.warn("查询角色" + storeManger + "下的账号信息异常: request [{}].", request, e);
            }
        }
        return accountInfoSet.size() == 0 ? Lists.newArrayList() : new ArrayList<>(accountInfoSet);
    }

    /**
     * 查询拥有某个门店权限的账号列表（最多查200个）
     * @param tenantId
     * @param storeId
     * @return
     */
    public List<AccountInfoVo> queryAccountListWithPoiPermission(Long tenantId, Long storeId) {
        int dataAuthType = PermissionGroupTypeEnum.POI.getValue();
        int authId = ApiMethodParamThreadLocal.getIdentityInfo().getAuthId();
        if (authId == AppIdEnum.APP_9.getAuthAppId()) {
            // 共享前置仓数据权限
            dataAuthType = PermissionGroupTypeEnum.SHAREABLE_WAREHOUSE.getValue();
        }
        QueryAccountListByPoiAndAuthRequest request = new QueryAccountListByPoiAndAuthRequest();
        request.setTenantId(tenantId);
        request.setAppId(authId);
        request.setCodes(Arrays.asList(String.valueOf(storeId)));
        request.setPermissionGroupType(dataAuthType);
        request.setValid(ValidTypeEnum.NORMAL.getValue());
        request.setPageNum(1);
        request.setPageSize(200);

        QueryAccountInfoListResponse response = queryAccountListByPoiAndAuth(request);
        return response.getAccountInfoList();
    }

    /**
     * 全量查询拥有某个门店权限的账号列表
     * @param tenantId
     * @param storeId
     * @return
     */
    public List<AccountInfoVo> batchQueryAccountListWithPoiPermission(Long tenantId, Long storeId) {
        int pageNum = 1;
        int dataAuthType = PermissionGroupTypeEnum.POI.getValue();
        int authId = ApiMethodParamThreadLocal.getIdentityInfo().getAuthId();
        if (authId == AppAuthIdConstants.SHARED_WAREHOUSE.val()) {
            // 共享前置仓数据权限
            dataAuthType = PermissionGroupTypeEnum.SHAREABLE_WAREHOUSE.getValue();
        }
        QueryAccountListByPoiAndAuthRequest request = new QueryAccountListByPoiAndAuthRequest();
        request.setTenantId(tenantId);
        request.setAppId(authId);
        request.setCodes(Arrays.asList(String.valueOf(storeId)));
        request.setPermissionGroupType(dataAuthType);
        request.setValid(ValidTypeEnum.NORMAL.getValue());
        request.setPageSize(200);

        QueryAccountInfoListResponse resp = null;
        List<AccountInfoVo> result = new ArrayList<>();
        do {
            request.setPageNum(pageNum);
            resp = queryAccountListByPoiAndAuth(request);
            if (CollectionUtils.isNotEmpty(resp.getAccountInfoList())) {
                result.addAll(resp.getAccountInfoList());
            }
            pageNum++;
        } while (Objects.nonNull(resp.getPageInfo()) && resp.getPageInfo().getPages() > pageNum - 1
                && pageNum <= MAX_QUERY_COUNT);

        if (Objects.nonNull(resp.getPageInfo()) && resp.getPageInfo().getPages() > MAX_QUERY_COUNT) {
            log.warn("AuthThriftWrapper.batchQueryAccountListWithPoiPermission pages > 5, request:{}", request);
            Cat.logEvent("AUTH_THRIFT_WRAPPER", "PAGES_MORE_THAN_5");
        }
        return result;

    }

    public CommonResponse<BatchAuthCodeCheckResult> batchAuthCodeCheck(BatchAuthCodeCheckRequest request) {
        Map<String, Integer> authCodeResult = Maps.newHashMap();
        BatchAuthCodeCheckResult checkResult = new BatchAuthCodeCheckResult();
        if (CollectionUtils.isEmpty(request.getAuthCode())) {
            checkResult.setAuthCodeCheckResult(authCodeResult);
            return CommonResponse.success(checkResult);
        }

        // 读取MCC 请求新的权限鉴权
        if (MccConfigUtil.useSacAuthentication()) {
            try {
                SessionInfo sessionInfo = SessionContext.getCurrentSession();
                int appId = ApiMethodParamThreadLocal.getIdentityInfo().getAuthId();
                Map<String, Boolean> stringBooleanMap = sacWrapper.accountAuthPermissions(sessionInfo.getAccountId(), appId,
                        request.getAuthCode());
                if (MapUtils.isNotEmpty(stringBooleanMap)) {
                    authCodeResult = stringBooleanMap
                            .entrySet()
                            .stream()
                            .collect(Collectors.toMap(Map.Entry::getKey, m -> (m.getValue() != null && m.getValue())
                                    ? IntegerBooleanConstants.BOOLEAN_TRUE:IntegerBooleanConstants.BOOLEAN_FALSE));
                }
                checkResult.setAuthCodeCheckResult(authCodeResult);
                return  CommonResponse.success(checkResult);
            }
            catch (Exception e) {
                // 异常后 兜底处理为false
                authCodeResult = request.getAuthCode().stream().collect(Collectors.toMap(Function.identity(),
                        s -> IntegerBooleanConstants.BOOLEAN_FALSE));
                checkResult.setAuthCodeCheckResult(authCodeResult);
                return CommonResponse.success(checkResult);
            }
        }
        // 否则使用老的规则鉴权
        /**
         * 获取用户有权限的所有权限码
         */
        List<String> hasAuthCode = getCurrentAccountAllPermissionCodes();
        if (CollectionUtils.isNotEmpty(hasAuthCode)) {
            for (String authCodeInRequest : request.getAuthCode()) {
                if (hasAuthCode.contains(authCodeInRequest)) {
                    authCodeResult.put(authCodeInRequest, IntegerBooleanConstants.BOOLEAN_TRUE);
                } else {
                    authCodeResult.put(authCodeInRequest, IntegerBooleanConstants.BOOLEAN_FALSE);
                }
            }
        }
        checkResult.setAuthCodeCheckResult(authCodeResult);
        return CommonResponse.success(checkResult);
    }


    /**
     * 对指定账号进行元素权限的鉴权
     *
     * @return account info
     * @throws TException Any
     */
    @Degrade(rhinoKey = "AuthThriftWrapper.authPermissionAndDataAuth",
            fallBackMethod = "authPermissionAndDataAuthFallback",
            timeoutInMilliseconds = 3000,
            ignoreExceptions = CommonLogicException.class)
    @MethodLog(logRequest = true, logResponse = true)
    @CommonMonitorTransaction
    @SuppressWarnings("WeakerAccess")
    public Map<String, Boolean> authPermissionAndDataAuth(Long accountId, Long shopId, List<String> permissionCodes) {
        try {
            if (CollectionUtils.isNotEmpty(permissionCodes)){
                int dataAuthType = PermissionGroupTypeEnum.POI.getValue();
                int authId = ApiMethodParamThreadLocal.getIdentityInfo().getAuthId();
                if (authId == AppIdEnum.APP_9.getAuthAppId()) {
                    // 共享前置仓数据权限
                    dataAuthType = PermissionGroupTypeEnum.SHAREABLE_WAREHOUSE.getValue();
                }
                AuthPermissionAndDataAuthRequest request = new AuthPermissionAndDataAuthRequest();
                request.setAccountId(accountId);
                request.setAppId(authId);
                request.setDataAuthCode(String.valueOf(shopId));
                request.setDataAuthType(dataAuthType);
                request.setPermissionCodes(permissionCodes);
                AuthPermissionAndDataAuthResponse response = authThriftService.authPermissionAndDataAuth(request);
                return response.getIsAccountHaveAuth();
            }
        } catch (TException e) {
            log.error("请求元素权限鉴权出错,accountId:{}",accountId,  e);
        }
        return Maps.newHashMap();//默认返回空
    }


    private Map<String, Boolean> authPermissionAndDataAuthFallback(Long accountId, Long shopId, List<String> permissionCodes) {
        return Maps.newHashMap();//默认返回空
    }

    private QueryAccountInfoListResponse queryAccountListByPoiAndAuth(QueryAccountListByPoiAndAuthRequest request) {
        QueryAccountInfoListResponse resp = null;
        try {
            resp = authThriftService.queryAccountListByPoiAndAuth(request);
            log.info("queryAccountListWithPoiPermission resp, request:{}, resp:{}", request, resp);
            nullResponseCheck(resp, "查询拥有某个门店权限的账号列表异常");
            validateStatus(resp.getResult(), "查询拥有某个门店权限的账号列表异常");
            return resp;
        } catch (CommonLogicException e){
            throw e;
        } catch (Exception e) {
            log.warn("查询拥有某个门店权限的账号列表异常: request [{}].", request, e);
            throw new CommonRuntimeException("查询拥有某个门店权限的账号列表异常，请稍后重试.");
        }
    }



    /**
     * 获取权限code列表
     *
     * @param account 账户信息
     * @return code permissions
     */
    private ResourceData getPermissionCodes(AccountInfoVo account, int appId, List<String> matchParentCode) {
        if (account == null || CollectionUtils.isEmpty(account.getRoleList()) || CollectionUtils.isEmpty(matchParentCode)) {
            return new ResourceData();
        }
        ResourceData resourceData = new ResourceData();
        //  收集所有模块
        Pair<List<String>, Map<String, ResourceTree>> treeData = buildResourceTreeData(account, appId, matchParentCode);
        resourceData.authCodes.addAll(treeData.getLeft());
        Map<String, ResourceTree> authResourceMap = treeData.getRight();
        //  建立树形结构
        for (ResourceTree resource : authResourceMap.values()) {
            if (StringUtils.isBlank(resource.getParentCode())) {
                resourceData.getResourceTrees().add(resource);
                continue;
            }
            ResourceTree parent = authResourceMap.get(resource.getParentCode());
            if (parent != null) {
                parent.getLeaves().add(resource);
                parent.getLeaves().sort(Comparator.comparingInt(o -> o.rank));
            } else {
                resourceData.getResourceTrees().add(resource);
            }
        }
        resourceData.resourceTrees.sort(Comparator.comparingInt(o -> o.rank));
        return resourceData;
    }

    private Pair<List<String>, Map<String, ResourceTree>> buildResourceTreeData(AccountInfoVo account, int appId, List<String> matchParentCode) {
        Map<String, ResourceTree> authResourceMap = Maps.newLinkedHashMap();
        List<String> authCodes = Lists.newArrayList();
        account.getRoleList().forEach(role -> role.getPermissionList().forEach(permission -> {
            if (CollectionUtils.isEmpty(permission.getResourceInfoList())) {
                return;
            }
            for (ResourceInfoVo resource : permission.getResourceInfoList()) {
                // 过滤appId
                if (resource.getAppId() != appId) {
                    continue;
                }
                //  过滤非菜单资源,以及不是选中的权限资源
                if ((resource.getType() == AuthResourceType.MENU.type || resource.getType() == AuthResourceType.PAGE.type)
                        && (matchParentCode.contains(resource.getCode()) || matchParentCode.contains(resource.getParentCode()))) {
                    authCodes.add(resource.getCode());
                    authResourceMap.put(resource.getCode(), ResourceTree.transform(resource));
                }
            }
        }));
        return Pair.of(authCodes, authResourceMap);
    }

    /**
     * 转换module对象
     *
     * @param modules        模块信息列表
     * @param pendingTaskMap 未完成订单量map
     * @return 模块数据响应
     */
    private AppModuleResult transform(List<AppModuleDto> modules,
                                      Map<String, PendingTaskResult> pendingTaskMap,
                                      ResourceData resourceData) {
        if (CollectionUtils.isEmpty(modules)) {
            return new AppModuleResult();
        }
        AppModuleResult result = new AppModuleResult();

        try {
            //  权限模块聚合
            Map<String, AppModuleDto> moduleDtoMap = modules.stream()
                    .filter(item -> StringUtils.isNotBlank(item.authCode))
                    .collect(Collectors.toMap(AppModuleDto::getAuthCode, item -> item));
            for (ResourceTree resourceTree : resourceData.getResourceTrees()) {
                AppModule frontModule = new AppModule();
                recursiveMenuTree(resourceTree, frontModule, moduleDtoMap, pendingTaskMap);
                result.getModules().add(frontModule);
            }
            return checkRootModule(result);
        } catch (Exception e) {
            log.error("transform modules error", e);
            throw e;
        }
    }


    /**
     * 校验根节点，将为空的移动到默认根节点里
     *
     * @param result app module result
     * @return filtered list
     */
    private AppModuleResult checkRootModule(AppModuleResult result) {
        if (CollectionUtils.isEmpty(result.getModules())) {
            return result;
        }
        AppModule other = buildOtherModule();
        //  过滤叶子节点为空状态
        result.getModules().forEach(
                module -> removeEmptyNode(module, false, other)
        );
        result.getModules().add(other);
        return result;
    }

    private void removeEmptyNode(AppModule module, boolean independent, AppModule other) {
        int size = module.getSubModules().size();
        for (int i = size - 1; i >= 0; i--) {
            AppModule subModule = module.getSubModules().get(i);
            if (StringUtils.isNotBlank(subModule.getName())) {
                if (independent) {
                    other.getSubModules().add(subModule);
                }
                removeEmptyNode(subModule, independent, other);
            } else {
                module.getSubModules().remove(i);
                if (subModule.isHasAvailLeaf()) {
                    removeEmptyNode(subModule, true, other);
                }
            }
        }
    }

    /**
     * 构建默认根级节点
     *
     * @return 权限节点
     */
    private AppModule buildOtherModule() {
        AppModule otherModule = new AppModule();
        otherModule.setSort(Integer.MAX_VALUE);
        otherModule.setIcon(getString(OTHER_MODULE_ICON.key, ""));
        otherModule.setName(getString(OTHER_MODULE_NAME.key, DEFAULT_PARENT_MENU_NAME));
        otherModule.setHasAvailLeaf(true);
        return otherModule;
    }

    /**
     * 递归构建菜单树
     *
     * @param resource       权限资源
     * @param module         响应的模块树
     * @param moduleDtoMap   模块数据map
     * @param pendingTaskMap 待处理任务量map
     */
    private void recursiveMenuTree(ResourceTree resource, AppModule module,
                                   Map<String, AppModuleDto> moduleDtoMap, Map<String, PendingTaskResult> pendingTaskMap) {
        //  转换节点对象
        transform(resource, module, moduleDtoMap, pendingTaskMap);
        //  递归叶子节点
        int len = resource.leaves.size();
        for (int i = 0; i < len; i++) {
            recursiveMenuTree(
                    resource.leaves.get(i), module.getSubModules().get(i), moduleDtoMap, pendingTaskMap
            );
        }
        //  前端与权限暂未打通sort字段，故合并权限接口先使用权限的rank排序
        module.getSubModules().sort(Comparator.comparingInt(AppModule::getSort));
        module.setHasAvailLeaf(hasAvailLeaf(module));
    }

    /**
     * 是否有可用的叶子节点
     *
     * @param module 模块节点
     * @return flag
     */
    private boolean hasAvailLeaf(AppModule module) {
        return StringUtils.isNotBlank(module.getName())
                || module.getSubModules()
                .stream()
                .anyMatch(AppModule::isHasAvailLeaf);
    }

    /**
     * 权限资源 => 前段目录节点
     *
     * @param resource       权限资源
     * @param module         响应的模块树
     * @param moduleDtoMap   模块数据map
     * @param pendingTaskMap 待处理任务量map
     */
    private void transform(ResourceTree resource, AppModule module,
                           Map<String, AppModuleDto> moduleDtoMap, Map<String, PendingTaskResult> pendingTaskMap) {
        AppModuleDto appModule = moduleDtoMap.get(resource.code);
        module.setCode(resource.code);
        module.setSubModules(createLeaves(resource.leaves));
        module.setSort(resource.getRank());

        if (appModule != null) {
            PendingTaskResult pendingTask = pendingTaskMap.get(appModule.authCode);
            if (pendingTask != null) {
                module.setPendingTaskCount(pendingTask.getCount());
                module.setPendingTaskType(pendingTask.getType().type);
            }
            module.setJsName(appModule.jsName);
            module.setJsType(appModule.jsType);
            module.setIcon(appModule.iconUrl);
            module.setParent(resource.getParentCode());
            module.setName(appModule.moduleName);
            module.setSupportMultiPoi(appModule.getSupportMultiPoi());
        } else {
            module.setParent(resource.getParentCode());
            module.setName(resource.getName());
        }
    }

    /**
     * @param leaves 资源树叶子节点
     * @return 创建目录树叶子节点
     */
    private List<AppModule> createLeaves(List<ResourceTree> leaves) {
        return CollectionUtils.isEmpty(leaves) ?
                Lists.newArrayList() :
                leaves.stream()
                        .map(item -> new AppModule())
                        .collect(Collectors.toList());
    }


    @SuppressWarnings("unused")
    private List<DataPermissionVo> getCurrentAccountFallback() {
        throw new FallbackException("AuthThriftWrapper.appAuth降级");
    }

    private void nullResponseCheck(Object resp, String errorMsg) {
        if (resp == null) {
            throw new CommonLogicException(errorMsg + ", response is null");
        }
    }

    private void validateStatus(Result status, String errorMsg) {
        if (status == null) {
            throw new CommonLogicException(errorMsg + ", status is null");
        }

        if (status.getCode() != ResultCode.SUCCESS.getCode()) {
            throw new CommonLogicException(MessageFormat.format("{0}, code = {1}, detail = {2}",
                    errorMsg, status.getCode(), status.getMsg()));
        }
    }

    @Data
    private class ResourceData {
        private List<String> authCodes = Lists.newArrayList();
        private List<ResourceTree> resourceTrees = Lists.newArrayList();
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    private static class ResourceTree extends ResourceInfoVo {

        private List<ResourceTree> leaves = Lists.newArrayList();

        private static ResourceTree transform(ResourceInfoVo raw) {
            ResourceTree tree = new ResourceTree();
            tree.code = raw.code;
            tree.id = raw.id;
            tree.method = raw.method;
            tree.name = raw.name;
            tree.type = raw.type;
            tree.appId = raw.appId;
            tree.field = raw.field;
            tree.parentCode = raw.parentCode;
            tree.valid = raw.valid;
            tree.rank = raw.rank;
            return tree;
        }
    }


    /**
     * 判断针对
     *
     * @param authCode
     * @return
     */
    public Boolean isCodeHasAuth(String authCode) {
        if (MccConfigUtil.useSacAuthenticationV2()) {
            Map<String, Boolean> codeAuthMap = isHasPermission(Arrays.asList(authCode));
            return BooleanUtils.isTrue(codeAuthMap.get(authCode));
        }
        else {
            List<String> codeHasAuth = getCurrentAccountAllPermissionCodes();
            return CollectionUtils.isNotEmpty(codeHasAuth) ? codeHasAuth.contains(authCode) : false;
        }
    }

    /**
     * 判断针对
     *
     * @param authCodes
     * @return
     */
    @Deprecated
    public Map<String, Boolean> isCodesHasAuth(List<String> authCodes) {
        if (CollectionUtils.isEmpty(authCodes)) {
            return Collections.emptyMap();
        }
        List<String> accountAllPermissionCodes = getCurrentAccountAllPermissionCodes();
        if (CollectionUtils.isEmpty(accountAllPermissionCodes)) {
            return authCodes
                    .stream()
                    .distinct()
                    .collect(Collectors.toMap(Function.identity(),
                            s -> false));
        }
        Map<String, Boolean> resultMap = new HashMap<>();
        for (String authCode : authCodes) {
            resultMap.put(authCode,accountAllPermissionCodes.contains(authCode));
        }
        return resultMap;
    }


    public List<String> queryAuthorizedCodes(List<String> authCodes) {
        if (CollectionUtils.isEmpty(authCodes)) {
            return Collections.emptyList();
        }

        if (MccConfigUtil.useSacAuthenticationV2()) {
            SessionInfo sessionInfo = SessionContext.getCurrentSession();

            Map<String, Boolean> userPermissionCodeMap = sacWrapper.accountAuthPermissions(sessionInfo.getAccountId(),
                    ApiMethodParamThreadLocal.getIdentityInfo().getAuthId(), authCodes);

            return userPermissionCodeMap.entrySet().stream().filter(Map.Entry::getValue).map(Map.Entry::getKey).collect(Collectors.toList());
        }
        else {
            return getCurrentAccountAllPermissionCodes();
        }

    }


    /**
     * 判断登录账号是否有指定权限码的权限
     * @param currPermissionCodes
     * @return
     */
    public Map<String/*permissionCode*/, Boolean> isHasPermission(List<String> currPermissionCodes) {

        if (MccConfigUtil.useSacAuthenticationV2()) {
            SessionInfo sessionInfo = SessionContext.getCurrentSession();
            int appId = ApiMethodParamThreadLocal.getIdentityInfo().getAuthId();
            return sacWrapper.accountAuthPermissions(sessionInfo.getAccountId(), appId, currPermissionCodes);
        }
        else {
            return isHasPermissionByQueryAccount(currPermissionCodes);
        }

    }

    public Map<String, Boolean> isHasPermissionV2(List<String> currPermissionCodes) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        try {
            if (identityInfo != null
                    && identityInfo.getUser() != null){
                User user = identityInfo.getUser();
                long accountId = user.getAccountId();
                int appId = ApiMethodParamThreadLocal.getIdentityInfo().getAuthId();
                return sacWrapper.accountAuthPermissions(accountId, appId, currPermissionCodes);
            }
        }catch (Exception e){
            log.error("查询权限元素权限失败", e);
        }
        log.info("没有配置待检查的元素权限");
        return Maps.newHashMap();
    }


    @Deprecated
    private Map<String, Boolean> isHasPermissionByQueryAccount(List<String> permissionCodes) {
        Map<String, Boolean> permissionMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(permissionCodes)) {
            return permissionMap;
        }

        List<String> accountPermissionCodes = getCurrentAccountAllPermissionCodes();

        if (CollectionUtils.isEmpty(accountPermissionCodes)) {
            permissionCodes.forEach(code -> {
                permissionMap.put(code, false);
            });
            return permissionMap;
        }

        permissionCodes.forEach(code -> {
            if (accountPermissionCodes.contains(code)) {
                permissionMap.put(code, true);
            } else {
                permissionMap.put(code, false);
            }
        });

        return permissionMap;
    }


    /**
     * 获取当前摊位id，若当前账号非摊位账号，返回null
     * @return
     */
    public Long getBoothIdByCurrentUser() {
        List<Long> boothIdList = new ArrayList<>();
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        if (user == null) {
            log.error("user is null, identityInfo:{}", ApiMethodParamThreadLocal.getIdentityInfo());
            return null;
        }
        log.info("user info:{}", user);
        if (AccountTypeEnum.BOOTH.getValue() == user.getAccountType()) {
            List<String> boothIdStrList = queryPermissionGroupId(user, PermissionGroupTypeEnum.BOOTH, String::valueOf);
            for (String boothIdStr : boothIdStrList) {
                boothIdList.add(Long.parseLong(boothIdStr));
            }
        }
        else {
            log.info("current user is not booth");
        }
        return CollectionUtils.isNotEmpty(boothIdList) ? boothIdList.get(0) : null;
    }

    /**
     * 注销/停用账号
     */
    public void deactivationAccount() {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        if (Objects.isNull(user)) {
            throw new CommonLogicException("获取用户信息为空");
        }
        if (Objects.equals(user.getAccountType(), AccountTypeEnum.MT_MANAGER.getValue())) {
            throw new CommonLogicException("美团管理员类型账号不支持注销");
        }
        log.info("用户注销账号：{}", user);
        int offlineOpType = 1;
        UpdateTenantAccountStatusRequest request = new UpdateTenantAccountStatusRequest();
        request.setLoginAccountId(user.getAccountId());
        request.setUpdateAccountId(user.getAccountId());
        request.setOperatorType(offlineOpType);
        request.setTenantId(user.getTenantId());
        request.setOptUser(user.getAccountName());
        try {
            Result result = authThriftService.updateTenantAccountStatusNew(request);
            nullResponseCheck(result, "注销账号异常");
            validateStatus(result, "注销账号异常");
        } catch (Exception e) {
            log.error("注销账号异常，用户：[{}]", user, e);
            throw new CommonRuntimeException("注销账号异常：" + e.getMessage());
        }
    }

}
