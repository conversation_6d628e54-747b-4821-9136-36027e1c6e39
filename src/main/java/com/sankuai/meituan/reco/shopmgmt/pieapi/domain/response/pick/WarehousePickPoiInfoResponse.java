package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.WarehousePoiInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "查询待集单门店列表"
)
@Data
@ApiModel("查询待集单门店列表返回")
public class WarehousePickPoiInfoResponse {

    @FieldDoc(
            description = "待集单门店列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "待集单门店列表", required = true)
    private List<WarehousePoiInfoVO> poiInfoVOList;


}
