package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: zhangjian155
 * @Date: 2022/10/28 14:20
 */
@TypeDoc(
        description = "拣货详情收货方门店信息"
)
@Data
@ApiModel("拣货详情收货方门店信息")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WarehouseReceiveStoreInfoVO {

    @FieldDoc(
            description = "收货方门店ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "收货方门店ID")
    private Long storeId;

    @FieldDoc(
            description = "收货方门店名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "收货方门店名称")
    private String storeName;
}
