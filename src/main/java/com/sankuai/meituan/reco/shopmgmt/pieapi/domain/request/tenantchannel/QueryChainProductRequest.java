package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.tenantchannel;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ChannelTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.ParamException;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/8/3
 */
@Data
@TypeDoc(
        description = "查询是否支持连锁产品库配置请求"
)
public class QueryChainProductRequest {

    @FieldDoc(description = "渠道id")
    private Integer channelId;

    @FieldDoc(description = "门店id")
    private Long storeId;

    public void validate() {
        if (Objects.isNull(channelId)) {
            throw new ParamException("channelId 不能为空");
        }
        if (Objects.isNull(storeId)) {
            throw new ParamException("storeId 不能为空");
        }
    }

}
