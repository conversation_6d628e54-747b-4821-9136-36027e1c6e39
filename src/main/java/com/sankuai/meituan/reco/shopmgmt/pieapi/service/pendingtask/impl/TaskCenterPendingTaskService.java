package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.impl;

import com.google.common.collect.Lists;
import com.meituan.shangou.saas.dto.StatusCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.AbstractSinglePendingTaskService;
import com.sankuai.shangou.common.PageListDTO;
import com.sankuai.shangou.common.PageQueryDTO;
import com.sankuai.shangou.common.Result;
import com.sankuai.shangou.logistics.warehouse.model.WarehouseKeyDTO;
import com.sankuai.shangou.logistics.warehouse.task.TaskQueryServiceV2;
import com.sankuai.shangou.logistics.warehouse.task.enums.type.TaskTypeEnum;
import com.sankuai.shangou.logistics.warehouse.task.model.TaskDTOV2;
import com.sankuai.shangou.logistics.warehouse.task.model.cmd.query.TaskQueryCmdDTOV2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;

/**
 * author hedong07
 * dateTime 2023/4/17
 */
@Slf4j
@Service
public class TaskCenterPendingTaskService extends AbstractSinglePendingTaskService {

	@Resource
	private TaskQueryServiceV2 taskQueryService;

	@Override
	protected PendingTaskResult getPendingTaskCount(PendingTaskParam param) throws Exception {
		try {
			Result<PageListDTO<TaskDTOV2>> result = taskQueryService.pageQueryUndoneTasks(
					new WarehouseKeyDTO(param.getTenantId(), param.getEntityId()),
					new PageQueryDTO<>(
							new TaskQueryCmdDTOV2(
									Lists.newArrayList(
											TaskTypeEnum.DH_VALIDITY_SALE_PROHIBITED_TASK.getCode(),
											TaskTypeEnum.DH_SALE_RETURN_TASK.getCode()
									),
									new ArrayList<>()
							),
							1, 0, null,
							param.getUser().getAccountId()
					)
			);
			if (result.getCode() != StatusCodeEnum.SUCCESS.getCode()) {
				log.error("查询任务待办数量失败，将兜底为0. result:{}", result);
				return PendingTaskResult.createNumberMarker(0);
			}
			return PendingTaskResult.createNumberMarker((int) result.getModule().getTotal());

		} catch (Exception e) {
			log.error("查询任务待办数量异常，将兜底为0", e);
			return PendingTaskResult.createNumberMarker(0);
		}
	}

	@Override
	protected AuthCodeEnum module() {
		return AuthCodeEnum.DH_TASK_CENTER;
	}
}