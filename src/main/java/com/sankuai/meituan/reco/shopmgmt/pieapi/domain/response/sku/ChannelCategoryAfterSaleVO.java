package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelaftersalerule.dto.ChannelAfterSaleDetailDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "渠道类目售后服务信息"
)
@Data
@ApiModel("渠道类目售后服务信息")
public class ChannelCategoryAfterSaleVO {

    @FieldDoc(
            description = "售后服务文案", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "售后服务文案")
    private String text;


    @FieldDoc(
            description = "售后服务类型", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "售后服务类型")
    private String type;




    public static ChannelCategoryAfterSaleVO ofDTO(ChannelAfterSaleDetailDTO dto) {
        ChannelCategoryAfterSaleVO vo = new ChannelCategoryAfterSaleVO();
        vo.setText(dto.getText());
        vo.setType(dto.getType());
        return vo;
    }
}
