package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.auth;

import com.meituan.shangou.saas.tenant.thrift.dto.department.DepartmentDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2018/10/23
 * 部门返回对象
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DepartmentVo {

    /**
     * 部门ID
     */
    private Long departmentId;
    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 部门类型(1部门2门店组3门店)
     */
    private Integer departmentType;

    /**
     * 父节点ID
     */
    private Long parentId;

    private Long relId;

    /**
     * 是否有子节点
     */
    private Boolean hasSubDep;

    /**
     * 此部门下是否有员工
     */
    private Boolean hasSubEmp;

    /**
     * 部门实体类型，和 {@link  #departmentType} 的区别在于区分了门店和中心仓
     * 以便于按 POI 实体类型区分图标
     */
    private Integer departmentEntityType;


    public static DepartmentVo build(DepartmentDto bo) {
        return builder()
                .departmentId(bo.getDepartmentId())
                .departmentName(bo.getDepartmentName())
                .departmentType(bo.getDepartmentType())
                .parentId(bo.getParentId())
                .build();
    }
}
