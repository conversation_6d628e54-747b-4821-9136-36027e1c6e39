package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.wavepick.converter;

import com.sankuai.meituan.reco.pickselect.thrift.wave.dto.WarehouseSeedFuzzySkuDTO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.WarehouseSeedFuzzySkuResponse;
import org.mapstruct.Mapper;

/**
 * @Description:
 * @Author: zhangjian155
 * @Date: 2022/8/25 18:39
 */
@Mapper(componentModel = "spring", uses = {WarehouseProgressConverter.class,WarehouseSeedItemSkuConverter.class})
public abstract class WarehouseSeedFuzzySkuResponseConverter {
    public abstract WarehouseSeedFuzzySkuResponse convert2Response(WarehouseSeedFuzzySkuDTO warehouseSeedFuzzySkuDTO);
}
