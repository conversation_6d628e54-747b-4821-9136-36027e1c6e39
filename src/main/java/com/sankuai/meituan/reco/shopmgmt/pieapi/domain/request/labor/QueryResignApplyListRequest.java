package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022-12-08
 * @email <EMAIL>
 */
@TypeDoc(
        description = "提交申请请求"
)
@Data
@ApiModel("提交申请请求")
@AllArgsConstructor
@NoArgsConstructor
public class QueryResignApplyListRequest {

    @FieldDoc(
            description = "页码"
    )
    private Integer pageNo;

    @FieldDoc(
            description = "一页大小"
    )
    private Integer pageSize;


}
