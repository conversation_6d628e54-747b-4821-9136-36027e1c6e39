package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.StoreSkuPropertyVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.product.container.SpuContainer;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelCategoryDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.RegionSpuDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelType;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelBrandRelationDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreGroupCategoryInfoDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.TenantSpuBizDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @author: liujianghua02
 * @date: 2020-07-23 15:52
 */
@TypeDoc(
        description = "门店商品明细"
)
@Data
@ApiModel("租户城市商品明细")
public class TenantRegionSpuVO extends SpuContainer {

    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户ID", required = true)
    private Long tenantId;
    @FieldDoc(
            description = "区域ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "区域ID")
    private Long regionId;
    @FieldDoc(
            description = "租户商品名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "租户商品名称")
    private String tenantName;
    @FieldDoc(
            description = "区域商品名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "区域商品名称")
    private String regionName;
    @FieldDoc(
            description = "商品spuId", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品spu")
    private String spuId;
    @FieldDoc(
            description = "带入总部商品图片地址", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "租户SPU图片列表")
    private List<String> tenantImageUrls;
    @FieldDoc(
            description = "带入城市商品图片地址", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "城市SPU图片列表")
    private List<String> regionImageUrls;
    @FieldDoc(
            description = "产地", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "产地")
    private String producingPlace;
    @FieldDoc(
            description = "商品分类", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "分类")
    private CategoryVO category;
    @FieldDoc(
            description = "带入商品品牌", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "品牌")
    private BrandVO brand;
    @FieldDoc(
            description = "称重属性"
    )
    @ApiModelProperty(name = "称重属性")
    private Integer standerType;

    /**
     * 统一收敛至channelCategoryVOList
     */
    @Deprecated
    @FieldDoc(
            description = "美团渠道类目信息"
    )
    @ApiModelProperty(name = "美团渠道类目信息")
    private ChannelCategoryDTO mtChannelCategory;

    @FieldDoc(
            description = "商品视频信息"
    )
    @ApiModelProperty(name = "商品视频信息")
    private VideoInfoVO videoInfo;

    @FieldDoc(
            description = "商品图片详情"
    )
    @ApiModelProperty(name = "商品图片详情")
    private List<String> pictureContents;

    @FieldDoc(description = "店内分类列表")
    @ApiModelProperty(name = "店内分类列表")
    private List<MerchantStoreCategoryVO> tenantStoreCategoryList;

    @FieldDoc(
            description = "商品属性 不超过十个属性", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品属性")
    private List<StoreSkuPropertyVO> properties;

    @FieldDoc(
            description = "商品卖点",
            example = {}
    )
    @ApiModelProperty(name = "商品卖点")
    private String sellPoint;

    @FieldDoc(
            description = "商品描述",
            example = {}
    )
    @ApiModelProperty(name = "商品描述")
    private String description;

    @FieldDoc(
            description = "规格类型",
            example = {}
    )
    @ApiModelProperty(name = "规格类型")
    private Integer specType;

    @FieldDoc(
            description = "租户商品规格信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户商品规格信息")
    private List<TenantSkuVO> tenantSkuVOList;

    /**
     * 统一收敛至channelCategoryVOList
     */
    @Deprecated
    @FieldDoc(
            description = "多渠道下-饿了么渠道类目信息"
            , requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "多渠道下-饿了么渠道类目信息")
    private ChannelCategoryDTO elmChannelCategory;

    /**
     * 统一收敛至channelCategoryVOList
     */
    @Deprecated
    @FieldDoc(
            description = "多渠道下-京东渠道类目信息"
            , requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "多渠道下-京东渠道类目信息")
    private ChannelCategoryDTO jdChannelCategory;

    /**
     * 统一收敛至channelCategoryVOList
     */
    @Deprecated
    @FieldDoc(
            description = "多渠道下-抖音渠道类目信息"
            , requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "多渠道下-抖音渠道类目信息")
    private ChannelCategoryDTO douyinChannelCategory;

    @FieldDoc(
            description = "抖音渠道售后服务信息"
            , requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "抖音渠道售后服务信息")
    private String douyinAfterSaleServiceType;

    @FieldDoc(
            description = "抖音渠道资质信息"
            , requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "抖音渠道资质信息")
    private List<QualificationVO> douyinQualificationList;

    @FieldDoc(
            description = "渠道类目信息，所有渠道类目统一管理，后续主键废弃掉散落的渠道类目信息"
    )
    @ApiModelProperty("渠道类目信息")
    private List<ChannelCategoryVO> channelCategoryVOList;

    @FieldDoc(
            description = "医疗器械资质信息"
    )
    @ApiModelProperty("医疗器械资质信息")
    private MedicalDeviceQuaInfoVO medicalDeviceQuaInfo;

    @FieldDoc(
            description = "美团售后服务"
    )
    @ApiModelProperty("美团售后服务")
    private String mtAfterSaleServiceType;

    @FieldDoc(
            description = "货盘归属：1-总部货盘，2-门店货盘"
    )
    @ApiModelProperty("货盘归属")
    private Integer palletSrc;

    @FieldDoc(
            description = "渠道品牌", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "渠道品牌")
    private List<ChannelBrandVO> channelBrandList;


    @FieldDoc(
            description = "京东配送要求", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "京东配送要求")
    private Integer deliveryRequirement;

    @FieldDoc(
            description = "特殊管控商品资质"
    )
    @ApiModelProperty("特殊管控商品资质")
    private List<String> controlQuaPicUrl;

    @FieldDoc(description = "美团特殊图片")
    private List<SpecialPictureVO> mtSpecialPictureList;


    public static TenantRegionSpuVO ofBizDto(TenantSpuBizDTO tenantSpuBizDTO,
                                             Long storeId,
                                             Map<Long, Integer> poiId2GroupIdsMap,
                                             RegionSpuDTO regionSpuDTO) {
        TenantRegionSpuVO tenantRegionSpuVO = new TenantRegionSpuVO();
        tenantRegionSpuVO.setTenantId(tenantSpuBizDTO.getTenantId());
        tenantRegionSpuVO.setTenantName(tenantSpuBizDTO.getName());
        tenantRegionSpuVO.setTenantImageUrls(tenantSpuBizDTO.getImageUrls());
        tenantRegionSpuVO.setBrand(BrandVO.ofDTO(tenantSpuBizDTO.getBrand()));
        tenantRegionSpuVO.setCategory(CategoryVO.ofDTO(tenantSpuBizDTO.getCategory()));
        tenantRegionSpuVO.setSpuId(tenantSpuBizDTO.getSpuId());
        tenantRegionSpuVO.setStanderType(tenantSpuBizDTO.getWeightType());
        List<ChannelCategoryVO> channelCategoryVOList= new ArrayList<>();
        if (tenantSpuBizDTO.getMtChannelCategory() != null) {
            channelCategoryVOList.add(ChannelCategoryVO.buildChannelCategoryVOForBiz(tenantSpuBizDTO.getMtChannelCategory(),
                    false, ChannelType.MEITUAN.getValue()));
        }

        if (Objects.nonNull(tenantSpuBizDTO.getElmChannelCategory())) {
            channelCategoryVOList.add(ChannelCategoryVO.buildChannelCategoryVOForBiz(tenantSpuBizDTO.getElmChannelCategory(),
                    false, ChannelType.ELEM.getValue()));
        }

        if (Objects.nonNull(tenantSpuBizDTO.getJdChannelCategory())) {
            channelCategoryVOList.add(ChannelCategoryVO.buildChannelCategoryVOForBiz(tenantSpuBizDTO.getJdChannelCategory(),
                    false, ChannelType.JD2HOME.getValue()));
        }
        if (Objects.nonNull(tenantSpuBizDTO.getDouyinChannelCategory())) {
            channelCategoryVOList.add(ChannelCategoryVO.buildChannelCategoryVOForBiz(tenantSpuBizDTO.getDouyinChannelCategory(),
                    false, ChannelType.DOU_YIN.getValue()));
        }
        tenantRegionSpuVO.setChannelCategoryVOList(channelCategoryVOList);

        tenantRegionSpuVO.setPictureContents(tenantSpuBizDTO.getPictureContents());
        tenantRegionSpuVO.setVideoInfo(VideoInfoVO.of(tenantSpuBizDTO.getVideoInfo()));
        tenantRegionSpuVO.setTenantStoreCategoryList(ConverterUtils.convertList(tenantSpuBizDTO.getStoreCategoryList(),
                MerchantStoreCategoryVO::ofBiz));
        tenantRegionSpuVO.setSellPoint(tenantSpuBizDTO.getSellingPoint());
        tenantRegionSpuVO.setDescription(tenantSpuBizDTO.getDescription());
        tenantRegionSpuVO.setProperties(Fun.map(tenantSpuBizDTO.getProperties(), StoreSkuPropertyVO::new));
        tenantRegionSpuVO.setSpecType(tenantSpuBizDTO.getSpecType());
        if (CollectionUtils.isNotEmpty(tenantSpuBizDTO.getTenantSkuDTOList())) {
            tenantRegionSpuVO.setTenantSkuVOList(Fun.map(tenantSpuBizDTO.getTenantSkuDTOList(), TenantSkuVO::fromBizDTO));
        }

        if (BooleanUtils.isTrue(tenantSpuBizDTO.getStoreCategoryCleanSuccess())
                && storeId != null
                && MapUtils.isNotEmpty(poiId2GroupIdsMap)
                && poiId2GroupIdsMap.containsKey(storeId)
                && CollectionUtils.isNotEmpty(tenantSpuBizDTO.getStoreGroupCategoryList())) {
            // 查询店内分类时，如果有分组，要选择分组上的店内分类，如果没有分组店内分类，填空列表，后续商品创建失败
            Integer groupId = poiId2GroupIdsMap.get(storeId);
            List<MerchantStoreCategoryVO> tenantStoreCategoryList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(tenantSpuBizDTO.getStoreGroupCategoryInfoList())) {
                StoreGroupCategoryInfoDTO storeGroupCategoryDTO = tenantSpuBizDTO.getStoreGroupCategoryInfoList().stream()
                        .filter(e -> Objects.equals(e.getStoreGroupId(), groupId)).findFirst().orElse(null);
                if (storeGroupCategoryDTO != null
                        && CollectionUtils.isNotEmpty(storeGroupCategoryDTO.getStoreCategoryList())) {
                    storeGroupCategoryDTO.getStoreCategoryList().forEach(category -> {
                        MerchantStoreCategoryVO merchantStoreCategoryVO = new MerchantStoreCategoryVO();
                        merchantStoreCategoryVO.setCategoryId(String.valueOf(category.getCategoryId()));
                        merchantStoreCategoryVO.setName(category.getCategoryName());
                        tenantStoreCategoryList.add(merchantStoreCategoryVO);
                    });
                }
            }
            tenantRegionSpuVO.setTenantStoreCategoryList(tenantStoreCategoryList);
        }
        tenantRegionSpuVO.setDouyinAfterSaleServiceType(tenantSpuBizDTO.getDouyinAfterSaleServiceType());
        if (CollectionUtils.isNotEmpty(tenantSpuBizDTO.getDouyinQualificationList())) {
            tenantRegionSpuVO.setDouyinQualificationList(QualificationVO.ofBizDTOList(tenantSpuBizDTO.getDouyinQualificationList()));
        }
        if (tenantSpuBizDTO.getMedicalDeviceQuaInfo() != null) {
            tenantRegionSpuVO.setMedicalDeviceQuaInfo(MedicalDeviceQuaInfoVO.fromMedicalDeviceQuaDTO(tenantSpuBizDTO.getMedicalDeviceQuaInfo()));
        }
        tenantRegionSpuVO.setMtAfterSaleServiceType(tenantSpuBizDTO.getMtAfterSaleServiceType());
        if (regionSpuDTO != null) {
            tenantRegionSpuVO.setRegionId(regionSpuDTO.getRegionId());
            tenantRegionSpuVO.setRegionName(regionSpuDTO.getName());
            tenantRegionSpuVO.setRegionImageUrls(regionSpuDTO.getImageUris());
        }
        tenantRegionSpuVO.setPalletSrc(tenantSpuBizDTO.getPalletSrc());

        tenantRegionSpuVO.setDeliveryRequirement(tenantSpuBizDTO.getDeliveryRequirement());
        List<ChannelBrandRelationDTO> channelBrandRelationDTOS = tenantSpuBizDTO.getChannelBrandRelationDTOS();
        if (CollectionUtils.isNotEmpty(channelBrandRelationDTOS)) {
            tenantRegionSpuVO.setChannelBrandList(Fun.map(channelBrandRelationDTOS, ChannelBrandVO::ofBiz));
        }


        tenantRegionSpuVO.setChannelSaleAttrInfoList(TenantSpuVO.getChannelSaleAttrInfoVOList(tenantSpuBizDTO));
        tenantRegionSpuVO.setControlQuaPicUrl(tenantSpuBizDTO.getControlQuaPicUrl());
        tenantRegionSpuVO.setMtSpecialPictureList(Fun.map(tenantSpuBizDTO.getMtSpecialPictureList(),  SpecialPictureVO::toVO));

        return tenantRegionSpuVO;
    }
}
