package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.backendcategory;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.domain.SkuSortInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.assertj.core.util.Lists;

import java.util.List;

/**
 * <AUTHOR>
 */
@TypeDoc(
        description = "后台类目树节点"
)
@ApiModel("后台类目树节点")
@Data
@ToString
@NoArgsConstructor
public class BackendCategoryTreeNodeVO {

    @FieldDoc(
            description = "后台品类id"
    )
    @ApiModelProperty(value = "后台品类id", required = true)
    private String id;

    @FieldDoc(
            description = "后台品类名称"
    )
    @ApiModelProperty(value = "后台品类名称", required = true)
    private String title;

    @FieldDoc(
            description = "子类目列表"
    )
    @ApiModelProperty(value = "子类目列表", required = false)
    private List<BackendCategoryTreeNodeVO> children;

    @FieldDoc(
            description = "父类目"
    )
    @ApiModelProperty(value = "父类目", required = false)
    private String parentId;


    @FieldDoc(
            description = "全ID路径"
    )
    @ApiModelProperty(value = "全ID路径", required = false)
    private String fullCateIdPath;


    public BackendCategoryTreeNodeVO(SkuSortInfo skuSortInfo){
        this.id = skuSortInfo.getSortCode();
        this.title = skuSortInfo.getSortName();
        this.parentId = skuSortInfo.getParentSortCode();
        this.fullCateIdPath = skuSortInfo.getSortCodePath();
    }

    public void addChildren(BackendCategoryTreeNodeVO child){
        if (this.children == null) {
            this.children = Lists.newArrayList();
        }
        this.children.add(child);
    }
}
