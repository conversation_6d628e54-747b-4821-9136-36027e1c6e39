package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.tenant;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreGroupCoverageDTO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2024/2/23
 */
@TypeDoc(
        name = "门店分组覆盖情况",
        description = "门店分组覆盖情况"
)
@Getter
@Setter
@NoArgsConstructor
@ToString
public class StoreGroupCoverageVO {

    @FieldDoc(
            description = "覆盖数量"
    )
    private Integer coverageStoreGroupCount;

    @FieldDoc(
            description = "门店分组数量"
    )
    private Integer storeGroupTotal;

    private static final Integer DEFAULT_VALUE = 0;

    public StoreGroupCoverageVO(StoreGroupCoverageDTO storeGroupCoverage) {
        this.coverageStoreGroupCount = StoreGroupCoverageVO.DEFAULT_VALUE;
        this.storeGroupTotal = StoreGroupCoverageVO.DEFAULT_VALUE;

        if (storeGroupCoverage.getCoverageStoreGroupCount() != null) {
            this.coverageStoreGroupCount = storeGroupCoverage.getCoverageStoreGroupCount();
        }
        if (storeGroupCoverage.getStoreGroupTotal() != null) {
            this.storeGroupTotal = storeGroupCoverage.getStoreGroupTotal();
        }
    }

    public static StoreGroupCoverageVO of(StoreGroupCoverageDTO storeGroupCoverage) {
        if (storeGroupCoverage == null) {
            return null;
        }

        return new StoreGroupCoverageVO(storeGroupCoverage);
    }

}
