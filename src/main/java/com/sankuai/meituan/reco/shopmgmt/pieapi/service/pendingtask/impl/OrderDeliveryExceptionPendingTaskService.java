package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.impl;

import com.google.common.collect.Maps;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OCMSListViewIdConditionRequest;
import com.meituan.shangou.saas.order.management.client.dto.request.online.ViewIdCondition;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OCMSListViewIdConditionResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.meituan.shangou.saas.order.management.client.enums.SortByEnum;
import com.meituan.shangou.saas.order.management.client.enums.SortFieldEnum;
import com.meituan.shangou.saas.order.management.client.service.online.OCMSQueryThriftService;
import com.meituan.shangou.saas.order.platform.common.ResultCodeEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order.deliveryexception.DeliveryExceptionOpenListRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.deliveryexception.DeliveryExceptionOrderListVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.AbstractSinglePendingTaskService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.DeliveryExceptionWrapper;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionSubTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.ResponseCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.QueryDeliveryInfoThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TOrderIdentifier;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request.QueryDeliveryExceptionOrderBySubTypeRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.DeliveryExceptionOrdersBySubTypeResponse;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2020-02-18 16:00
 * @Description:
 */
@Slf4j
@Service
public class OrderDeliveryExceptionPendingTaskService extends AbstractSinglePendingTaskService {

    @Resource
    private DeliveryExceptionWrapper deliveryExceptionWrapper;

    @Autowired
    private QueryDeliveryInfoThriftService queryDeliveryInfoThriftService;

    @Resource
    private OCMSQueryThriftService ocmsQueryThriftService;


    @Override
    protected PendingTaskResult getPendingTaskCount(PendingTaskParam param) throws Exception {
        if(CollectionUtils.isEmpty(param.getStoreIds())){
            log.warn("Invalid request:{}", param);
            return PendingTaskResult.createNumberMarker(0);
        }

        if (MccConfigUtil.queryDeliveryExceptionByTmsApi(param.getTenantId())){
            Map<DeliveryExceptionSubTypeEnum, Integer> deliveryExceptionSubTypeEnumIntegerMap = countDeliveryErrorBySubType(param.getTenantId(), param.getStoreIds());
            return PendingTaskResult.createNumberMarker(deliveryExceptionSubTypeEnumIntegerMap.getOrDefault(DeliveryExceptionSubTypeEnum.ALL, 0));
        }

        DeliveryExceptionOpenListRequest request = new DeliveryExceptionOpenListRequest();
        request.setPageNo(1);
        request.setPageSize(0);
        request.setTenantId(param.getTenantId());
        request.setStoreId(param.getStoreIds().get(0));
        DeliveryExceptionOrderListVo deliveryExceptionOrderListVo = deliveryExceptionWrapper.queryOpenOrderList(request);
        if (deliveryExceptionOrderListVo != null) {
            return PendingTaskResult.createNumberMarker(deliveryExceptionOrderListVo.getTotalRrd());
        } else {
            log.warn("无法查询到未处理订单配送异常");
            return PendingTaskResult.createNumberMarker(0);
        }
    }

    public Map<DeliveryExceptionSubTypeEnum, Integer> countDeliveryErrorBySubType(Long tenantId, List<Long> storeIdList) {
        HashMap<DeliveryExceptionSubTypeEnum, Integer> rs = Maps.newHashMap();
        QueryDeliveryExceptionOrderBySubTypeRequest req = new QueryDeliveryExceptionOrderBySubTypeRequest(0, storeIdList.get(0), tenantId);
        DeliveryExceptionOrdersBySubTypeResponse response = queryDeliveryInfoThriftService.queryDeliveryExceptionOrdersBySubType(req);
        if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
            throw new CommonRuntimeException(response.getStatus().getMsg());

        }
        List<TOrderIdentifier> tOrderIdentifiers = response.getOrders();

        if(org.apache.commons.collections4.CollectionUtils.isEmpty(tOrderIdentifiers)){
            return rs;
        }
        Map<String,TOrderIdentifier> tOrderIdentifierMap = new HashMap<>();
        for (TOrderIdentifier orderIdentifier : tOrderIdentifiers){
            tOrderIdentifierMap.put(orderIdentifier.getChannelOrderId(), orderIdentifier);
        }
        List<ViewIdCondition> viewIdConditionRequest = buildOCMSListViewIdConditionRequestByTOrderIdentifier(tOrderIdentifiers);
        List<OCMSOrderVO> ocmsOrderVOList = queryOCMSVoByViewOrderIdWithoutRevenueQuery(tenantId, viewIdConditionRequest);
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(ocmsOrderVOList)){
            return rs;
        }
        for (OCMSOrderVO ocmsOrderVO : ocmsOrderVOList){
            if(ocmsOrderVO.getOrderStatus()== OrderStatusEnum.CANCELED.getValue()){
                continue;
            }
            TOrderIdentifier orderIdentifier=tOrderIdentifierMap.get(ocmsOrderVO.getViewOrderId());
            if(orderIdentifier == null){
                continue;
            }
            DeliveryExceptionSubTypeEnum deliveryExceptionSubTypeEnum = DeliveryExceptionSubTypeEnum.deliveryStatusCodeValueOfWithOutAll(
                    orderIdentifier.getExceptionTypeCode(),
                    orderIdentifier.getDeliveryExceptionCode(),
                    orderIdentifier.getDeliveryStatus().getCode());
            if (deliveryExceptionSubTypeEnum != null) {
                rs.put(deliveryExceptionSubTypeEnum, rs.getOrDefault(deliveryExceptionSubTypeEnum, 0) + 1);
                rs.put(DeliveryExceptionSubTypeEnum.ALL, rs.getOrDefault(DeliveryExceptionSubTypeEnum.ALL, 0) + 1);
            }
        }
        return rs;

    }

    private List<ViewIdCondition> buildOCMSListViewIdConditionRequestByTOrderIdentifier(List<TOrderIdentifier> tOrderIdentifiers) {
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(tOrderIdentifiers)){
            return Lists.newArrayList();
        }
        return tOrderIdentifiers.stream().map(v -> new ViewIdCondition(v.getOrderBizTypeCode(), v.getChannelOrderId())).collect(Collectors.toList());
    }

    public OCMSListViewIdConditionRequest buildOCMSListByViewOrderIdRequest(Long tenantId, List<ViewIdCondition> orders) {
        OCMSListViewIdConditionRequest request = new OCMSListViewIdConditionRequest();
        request.setViewIdConditionList(orders);
        request.setSortField(SortFieldEnum.ESTIMATED_ARRIVAL_TIME);
        request.setSort(SortByEnum.ASC);
        request.setTenantId(tenantId);

        return request;
    }

    public List<OCMSOrderVO> queryOCMSVoByViewOrderIdWithoutRevenueQuery(Long tenantId, List<ViewIdCondition> viewIdConditionList) {
        OCMSListViewIdConditionRequest viewIdConditionRequest = buildOCMSListByViewOrderIdRequest(tenantId, viewIdConditionList);
        log.info("ocmsQueryThriftService.queryOrderByViewIdCondition request:{}",
                viewIdConditionRequest);
        OCMSListViewIdConditionResponse viewIdConditionResponse = ocmsQueryThriftService.queryOrderByViewIdCondition(viewIdConditionRequest);
        log.info("ocmsQueryThriftService.queryOrderByViewIdCondition response:{}", viewIdConditionResponse);
        if (viewIdConditionResponse.getStatus().getCode() != com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum.SUCCESS.getValue()) {
            log.info("获取订单详情失败, viewIdConditionRequest:{}", viewIdConditionRequest);
            throw new BizException(ResultCodeEnum.FAIL.getCode(), viewIdConditionResponse.getStatus().getMessage());
        }
        return viewIdConditionResponse.getOcmsOrderList();
    }

    @Override
    protected AuthCodeEnum module() {
        return AuthCodeEnum.ORDER_DELIVERY_EXCEPTION;
    }
}
