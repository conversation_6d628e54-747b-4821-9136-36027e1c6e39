package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.tenant;


import java.util.List;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/03/14
 */
@Data
public class TenantListResponse {

    @FieldDoc(
            description = "分页信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "分页信息", required = true)
    private PageInfoVO pageInfo;

    @FieldDoc(
            description = "分页信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "分页信息", required = true)
    private List<TenantVo> tenantList;

}
