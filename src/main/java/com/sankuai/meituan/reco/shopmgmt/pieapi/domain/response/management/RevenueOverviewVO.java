package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.management;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @createTime 2019-06-17
 * @description
 */
@TypeDoc(
        description = "摊位营收概况信息",
        version = "1.0"
)
@Data
@ApiModel("营收概况信息")
public class RevenueOverviewVO {

    @FieldDoc(
            description = "营收金额(单位分)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "营收金额(单位分)", required = true)
    @NotNull
    private Long totalRevenue;

    @FieldDoc(
            description = "营收金额名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "营收金额名称", required = true)
    @NotNull
    private String totalRevenueName;


    @FieldDoc(
            description = "完成订单量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "完成订单量", required = true)
    @NotNull
    private Integer completeOrderCount;


    @FieldDoc(
            description = "退款金额(单位分)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "退款金额(单位分)", required = true)
    @NotNull
    private Integer totalRefund;

    @FieldDoc(
            description = "其他营收数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "其他营收数量", required = true)
    @NotNull
    private Integer otherRevenueCount;


    @FieldDoc(
            description = "营收金额说明", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "营收金额说明", required = true)
    @NotNull
    private String revenueTips;
}
