package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery;

import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2025-01-14
 */
@Data
public class QueryOrderDetailLinkRequest {
    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 门店id
     */
    private Long storeId;
    /**
     * 仓id
     */
    private Long warehouseId;

    /**
     * 转单门店id
     */
    private Long dispatchShopId;

    public Long getPoiId() {
        if (dispatchShopId != null) {
            return dispatchShopId;
        }
        if (warehouseId != null) {
            return warehouseId;
        }
        return storeId;
    }

    public void validate() {
        if (orderId == null) {
            throw new BizException("订单id不能为空");
        }
        if (storeId == null && warehouseId == null&& dispatchShopId == null) {
            throw new BizException("门店id、仓id、转单门店id不能同时为空");
        }
    }
}
