package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@TypeDoc(
        description = "货主相关信息"
)
@Data
@Builder
@ApiModel("货主相关信息")
public class WarehouseGoodsOwnerInfoVO {

    @FieldDoc(
            description = "货主名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "货主名称")
    private String goodsOwnerName;

    @FieldDoc(
            description = "货主ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "货主ID")
    private String goodsOwnerId;
}
