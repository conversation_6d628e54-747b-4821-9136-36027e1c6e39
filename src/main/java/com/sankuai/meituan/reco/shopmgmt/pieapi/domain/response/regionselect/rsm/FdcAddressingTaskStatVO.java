package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.regionselect.rsm;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * Email <EMAIL>
 * Date 2022/12/7 6:48 上午
 * Description
 */
@Data
@TypeDoc(description = "寻仓任务统计VO")
@ApiModel("寻仓任务统计VO")
public class FdcAddressingTaskStatVO {

    @ApiModelProperty("待分配任务数")
    @FieldDoc(description = "待分配任务数")
    private Integer unassignedCount;

    @ApiModelProperty("进行中任务数")
    @FieldDoc(description = "进行中任务数")
    private Integer inProgressCount;

    @ApiModelProperty("待执行任务数")
    @FieldDoc(description = "待执行任务数")
    private Integer pendingCount;

    @ApiModelProperty("已完成任务数")
    @FieldDoc(description = "已完成任务数")
    private Integer finishedCount;

    @ApiModelProperty("仓源待签约状态数")
    @FieldDoc(description = "仓源待签约状态数")
    private Integer rentalSourceWaitingForSignCount;

    @ApiModelProperty("待分配任务数(逾期)")
    @FieldDoc(description = "待分配任务数(逾期)")
    private Integer unassignedExpiredCount;

    @ApiModelProperty("进行中任务数(逾期)")
    @FieldDoc(description = "进行中任务数(逾期)")
    private Integer inProgressExpiredCount;

    @ApiModelProperty("待执行任务数(逾期)")
    @FieldDoc(description = "待执行任务数(逾期)")
    private Integer pendingExpiredCount;

    @ApiModelProperty("仓源待签约状态数(逾期)")
    @FieldDoc(description = "仓源待签约状态数(逾期)")
    private Integer rentalSourceWaitingForSignExpiredCount;
}
