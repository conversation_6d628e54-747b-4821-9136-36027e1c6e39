package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.securitydeposit;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2020/3/2 21:36
 * @Description:
 */
@TypeDoc(description = "门店保证金、电子协议信息")
@Data
public class SecurityDepositStatusVO {


    private Long id;

    private Integer status;

    /**
     * 电子协议状态
     * 1-待签署 2-无需签署 3-已签署(纸质) 4-已签署(电子)
     */
    private Integer contractStatus;

    /**
     * 协议ID.
     */
    private Long contractId;

    /**
     * 当前账号是否店长
     * 0-否 1-是
     */
    private Integer isStoreManager;

    /**
     * 当前门店店长
     */
    private List<String> storeManagerList;
    /**
     * 保证金余额是否充足
     */
    private Integer isFundSufficient;
    /**
     * 保证金余额最低比例，20代表20%
     */
    private Double lowestRatio;
    /**
     * 协议页面url
     */
    private String contractUrl;
    /**
     * 待签署协议页面url
     */
    private String unsignedContractUrl;
    /**
     * 协议过期时间
     */
    private String contractExpireTime;

    /**
     * 确认函状态，1：待签署；2：无需签署；3：与协议一并签署
     */
    private Integer confirmLetterStatus;

    /**
     * 确认函id，confirmLetterStatus=1、confirmLetterStatus=3时必返
     */
    private Long confirmLetterId;

    /**
     * 确认函地址，confirmLetterStatus=1、confirmLetterStatus=3时必返
     */
    private String confirmLetterUrl;

    /**
     * 门店创建大于等于1个月，true：大于等于1个月；false：小于一个月
     */
    private Boolean poiCreateGteOneMonth;

    /**
     * 新增门店名称列表，confirmLetterStatus=1、confirmLetterStatus=3时必返
     */
    private List<String> newlyAddedPoiNameList;

    /**
     * 当前协议是否为主协议
     */
    private Boolean isMainContract;

    /**
     * 协议保证金模式
     * @see com.meituan.shangou.saas.tenant.thrift.common.enums.SecurityDepositModeEnum
     */
    private Integer securityDepositMode;

}
