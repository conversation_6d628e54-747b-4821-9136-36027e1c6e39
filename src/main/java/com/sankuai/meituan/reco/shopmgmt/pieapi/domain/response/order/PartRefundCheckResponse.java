package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2020-02-17 16:31
 * @Description:
 */
@TypeDoc(
        description = "部分退款页面检查退款请求响应"
)
@ApiModel("部分退款页面检查退款请求响应")
@Data
public class PartRefundCheckResponse {

    @FieldDoc(
            description = "退款商品列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "退款商品列表", required = true)
    private List<OrderItemPartRefundCheckVO> partRefundCheckVOList;
}
