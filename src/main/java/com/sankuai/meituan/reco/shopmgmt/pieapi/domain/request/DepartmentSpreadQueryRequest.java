package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/12/23
 */

@TypeDoc(
        description = "部门展开请求"
)
@Data
@ApiModel("部门展开请求")
public class DepartmentSpreadQueryRequest {

    @FieldDoc(
            description = "部门id"
    )
    private Long departmentId;

    @FieldDoc(
            description = "是否包含员工列表返回"
    )
    private Integer hasEmployeeList;
}
