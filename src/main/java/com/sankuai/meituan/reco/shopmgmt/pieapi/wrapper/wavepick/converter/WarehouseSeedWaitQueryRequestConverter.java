package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.wavepick.converter;

import com.sankuai.meituan.reco.pickselect.thrift.wave.request.WarehouseQuerySeedWaitRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick.WarehouseSeedWaitQueryRequest;
import org.mapstruct.Mapper;

/**
 * @Description:
 * @Author: zhangjian155
 * @Date: 2022/8/25 17:13
 */
@Mapper(componentModel = "spring")
public abstract class WarehouseSeedWaitQueryRequestConverter {
    public abstract WarehouseQuerySeedWaitRequest convert2ThriftRequest(WarehouseSeedWaitQueryRequest request, Long storeId);
}
