package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.IntegerBooleanConstants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @createTime 2023/08/30
 * @description
 */
@TypeDoc(
        description = "订单首页模块"
)
@Data
@ApiModel("订单首页模块")
public class ThriftPartyModuleVO {

    @FieldDoc(
            description = "是否展示三方配送TAB (0:否, 1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示待接单tab (0:否, 1:是)", required = true)
    @NotNull
    private Integer showThirdPartTab;

    @FieldDoc(
            description = "是否展示待领取tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示待拣货tab (0:否，1:是)", required = true)
    @NotNull
    private Integer showThirdWaitToTakeOrderTab;

    @FieldDoc(
            description = "是否展示待拣货tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示待配送tab (0:否，1:是)", required = true)
    @NotNull
    private Integer showThirdWaitToPickTab;

    @FieldDoc(
            description = "是否展示待配送tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示配送异常tab (0:否，1:是)", required = true)
    @NotNull
    private Integer showThirdWaitToDeliveryTab;

    @FieldDoc(
            description = "是否展示配送中tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示退款tab (0:否，1:是)", required = true)
    @NotNull
    private Integer showThirdExceptionTab;

    public static ThriftPartyModuleVO initThriftPartyModule(){
        ThriftPartyModuleVO thriftPartyModuleVO = new ThriftPartyModuleVO();
        thriftPartyModuleVO.setShowThirdPartTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        thriftPartyModuleVO.setShowThirdWaitToTakeOrderTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        thriftPartyModuleVO.setShowThirdWaitToPickTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        thriftPartyModuleVO.setShowThirdWaitToDeliveryTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        thriftPartyModuleVO.setShowThirdExceptionTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        return thriftPartyModuleVO;
    }

}
