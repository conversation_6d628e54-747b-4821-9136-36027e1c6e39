package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.handler;


import com.meituan.linz.product.channel.EnhanceChannelType;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

import static com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ProjectConstants.MT_PLATFORM_UN_SALE_COUNT;

/**
 * <AUTHOR>
 * @since 2024/7/24
 */
@Component
public class MtPlatformUnSaleCountHandler extends AbstractCountStoreSpuHandler {

    @Override
    public Integer getChannelId() {
        return EnhanceChannelType.MT.getChannelId();
    }

    @Override
    public String getCountCode() {
        return MT_PLATFORM_UN_SALE_COUNT;
    }

    @Override
    public List<String> getAbnormalCodes() {
        // 平台下架（含平台停售）
        return Collections.singletonList("1003");
    }
}
