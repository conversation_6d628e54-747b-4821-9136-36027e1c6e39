package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 */
@TypeDoc(
        description = "波次拣货待集单数量返回"
)
@Data
@ApiModel("波次拣货待集单数量返回")
public class WarehousePickTabNumResponse  {

    @FieldDoc(
            description = "标签类型内数据数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "标签类型内数据数量", required = true)
    public Map<Integer,Long> tabNumMap;

    @FieldDoc(
            description = "待集单子类型内数据数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "待集单子类型内数据数量")
    private Map<String, Integer> subTabNumMap;

    @FieldDoc(
            description = "领取模式, 0:不需要领取，1:需要领取", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "领取模式, 0:不需要领取，1:需要领取", required = true)
    private int pickTaskReceiveMode;

}
