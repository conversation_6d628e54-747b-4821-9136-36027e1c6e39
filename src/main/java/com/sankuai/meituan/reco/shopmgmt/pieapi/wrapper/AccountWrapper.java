package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.annotation.Resource;

import com.meituan.shangou.saas.tenant.thrift.common.enums.SecurityDepositModeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.config.PoiManageModeDto;
import com.meituan.shangou.saas.tenant.thrift.dto.tenant.TenantInfoDto;
import com.meituan.shangou.sac.dto.response.SacCommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.shangou.saas.tenant.thrift.BusinessConfigThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.StatusCodeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.config.AppAccountConfigDto;
import com.meituan.shangou.saas.tenant.thrift.dto.config.response.AppAccountConfigResponse;
import com.meituan.shangou.sac.dto.request.manager.account.CreateManagerAndRelTenantRequest;
import com.meituan.shangou.sac.dto.response.common.StringResponse;
import com.meituan.shangou.sac.thrift.manager.SacAccountManagerService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.IntegerBooleanConstants;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonV2Response;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.account.AccountConfigModuleVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.account.AccountConfigQueryResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.account.AccountConfigVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.account.AccountHomePageVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.account.AccountTenantInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.account.YodaResultVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MaskUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.pullnew.PullNewWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.bean.SessionInfo;
import com.sankuai.meituan.shangou.empower.auth.sdk.context.SessionContext;
import com.sankuai.meituan.shangou.empower.auth.thrift.service.AccountManageThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.Result;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.request.SendBindMobileVerificationCodeRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.request.SendForgetPassWordVerificationCodeRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.request.VerifyAndBindMobileRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.response.BindMobileVerificationCodeResponse;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.response.InfoBindMobileResponse;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.response.InfoForgetPasswordResponse;
import com.sankuai.meituan.util.ConfigUtilAdapter;

import lombok.extern.slf4j.Slf4j;

import static com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil.allStoreShowPullNewSwitch;

/**
 * <AUTHOR>
 * @createTime 2019/11/27
 * @description
 */
@Slf4j
@Component
public class AccountWrapper {


    @Autowired
    private BusinessConfigThriftService businessConfigThriftService;

    @Autowired
    private TenantWrapper tenantWrapper;



    @Autowired
    AuthThriftWrapper authThriftWrapper;

    @Autowired
    private PullNewWrapper pullNewWrapper;

    @Autowired
    private SacWrapper sacWrapper;
    @Autowired
    private SacAccountManagerService sacAccountManagerService;

    @Resource
    private AccountManageThriftService.Iface accountManageThriftService;

    /**
     * 返回"我的"账号首页数据
     * 包括租户命名，是否有打印设置权限
     *
     * @return
     */
    public CommonResponse<AccountHomePageVO> homepage() {
        // 查询租户名称
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        User user = identityInfo.getUser();
        long tenantId = user.getTenantId();

        TenantInfoDto tenantInfoDto = tenantWrapper.getTenantInfo(tenantId);
        AccountTenantInfoVO accountTenantInfoVO = new AccountTenantInfoVO();
        accountTenantInfoVO.setTenantId(tenantId);
        accountTenantInfoVO.setAccountId(user.getAccountId());
        accountTenantInfoVO.setAccountType(user.getAccountType());
        accountTenantInfoVO.setAccountName(user.getAccountName());
        accountTenantInfoVO.setEmpName(user.getOperatorName());
        if (Objects.nonNull(tenantInfoDto)) {
            accountTenantInfoVO.setTenantName(tenantInfoDto.getTenantName());
            accountTenantInfoVO.setLogoUrl(tenantInfoDto.getLogoUrl());
        }
        Map<String, Boolean> hasAuthMap = getHasAuthMap(Arrays.asList(AuthCodeEnum.PRINT_SETTING.getAuthCode(), AuthCodeEnum.POI_MGR.getAuthCode(),
                AuthCodeEnum.POI_SEC_DEPOSIT.getAuthCode(), AuthCodeEnum.WAIMA_WECHAT_PULL_NEW.getAuthCode()));
        Boolean hasPrintSettingAuth = hasAuthMap.get(AuthCodeEnum.PRINT_SETTING.getAuthCode());
        Boolean hasPoiMgrAuth = hasAuthMap.get(AuthCodeEnum.POI_MGR.getAuthCode());
        boolean hasPoiSecDepositAuth = hasPoiSecDepositAuthWithBiz(hasAuthMap.get(AuthCodeEnum.POI_SEC_DEPOSIT.getAuthCode()));
        Boolean hasWeChatPullNew = hasAuthMap.get(AuthCodeEnum.WAIMA_WECHAT_PULL_NEW.getAuthCode());
        AccountHomePageVO accountHomePageVO = new AccountHomePageVO();
        accountHomePageVO.setAccountTenantInfoVO(accountTenantInfoVO);
        // 查询是否有"打印设置"权限
        accountHomePageVO.setShowPrintSetting(hasPrintSettingAuth ? IntegerBooleanConstants.BOOLEAN_TRUE : IntegerBooleanConstants.BOOLEAN_FALSE);
        accountHomePageVO.setShowPoiMgr(hasPoiMgrAuth ? IntegerBooleanConstants.BOOLEAN_TRUE : IntegerBooleanConstants.BOOLEAN_FALSE);
        accountHomePageVO.setShowPoiSecurityDeposit(hasPoiSecDepositAuth ? IntegerBooleanConstants.BOOLEAN_TRUE : IntegerBooleanConstants.BOOLEAN_FALSE);

        // 如果是所有门店，根据开关判断是否返回地推链接 否则直接设置地推链接
        if (identityInfo.isFullStoreMode()) {
            if (allStoreShowPullNewSwitch()) {
                accountHomePageVO.setShowWeChatPullNewUrl(hasWeChatPullNew ? ConfigUtilAdapter.getString("waima_wechat_pullnew.url") : StringUtils.EMPTY);
            }
        } else {
            accountHomePageVO.setShowWeChatPullNewUrl(hasWeChatPullNew ? ConfigUtilAdapter.getString("waima_wechat_pullnew.url") : StringUtils.EMPTY);
        }
        return CommonResponse.success(accountHomePageVO);
    }

    public Map<String, Boolean> getHasAuthMap(List<String> authCodes) {
        SessionInfo currentSession = SessionContext.getCurrentSession();
        Map<String, Boolean> authenticationMap = new HashMap<>();
        // 如果用新的
        if (MccConfigUtil.useSacAuthentication()) {
            int appId = ApiMethodParamThreadLocal.getIdentityInfo().getAuthId();
            Map<String, Boolean> stringBooleanMap = sacWrapper.accountAuthPermissions(currentSession.getAccountId(),
                    appId, authCodes);
            if (MapUtils.isNotEmpty(stringBooleanMap)) {
                authenticationMap = stringBooleanMap;
            }
            return authenticationMap;
        }
        // 如果用老的
        return authThriftWrapper.isCodesHasAuth(authCodes);
    }

    /**
     * 是否展示协议和保证金入口判定
     *
     * @param hasPoiSecDepositAuthByAuthApp 权限应用是否有协议和保证金入口菜单
     * @return 是否展示协议和保证金入口
     */
    private boolean hasPoiSecDepositAuthWithBiz(Boolean hasPoiSecDepositAuthByAuthApp) {
        if (!Boolean.TRUE.equals(hasPoiSecDepositAuthByAuthApp)) {
            return false;
        }
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        int appId = identityInfo.getAuthId();
        long tenantId = identityInfo.getUser().getTenantId();
        int shareableWarehouseAppId = 9;
        PoiManageModeDto poiManageModeDto = tenantWrapper.queryPoiManageMode(tenantId);
        if (poiManageModeDto == null || !poiManageModeDto.supportSecurityDeposit()) {
            // 本身不支持协议保证金
            return false;
        }
        // 当选择“共享前置仓”，且该租户“协议保证金管理模式=门店模式”，不展示协议和保证金入口
        if (appId == shareableWarehouseAppId && Objects.equals(poiManageModeDto.getSecurityDepositMode(), SecurityDepositModeEnum.POI_MODE.code())) {
            return false;
        }
        // 正常返回
        return true;
    }


    /**
     * 返回"我的"账号铃声配置项
     *
     * @return
     */
    public CommonResponse<AccountConfigQueryResponse> queryAccountConfig() {
        // 查询租户名称
        // 查询配置信息
        try {
            IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
            long tenantId = identityInfo.getUser().getTenantId();
            long accountId = identityInfo.getUser().getAccountId();
            List<String> hasPermissionCodes = authThriftWrapper.queryAuthorizedCodes(
                    Arrays.asList(AuthCodeEnum.ORDER_SUB_TAB.getAuthCode(), AuthCodeEnum.FULFILL_PICK_STATISTICS.getAuthCode(),
                            AuthCodeEnum.FULFILL_MERGE_STATISTICS.getAuthCode(), AuthCodeEnum.PHONE_CALL_SETTING.getAuthCode()));
            AppAccountConfigResponse appAccountConfigResponse = businessConfigThriftService.queryAppAccountConfig(tenantId, Lists.newArrayList(accountId));
            List<AppAccountConfigDto> appAccountConfigDtos = appAccountConfigResponse.getAppAccountConfigDtoList();
            AccountConfigVO accountConfigVO = new AccountConfigVO();
            AccountConfigModuleVO accountConfigModuleVO = new AccountConfigModuleVO();
            AccountConfigQueryResponse accountConfigQueryResponse = new AccountConfigQueryResponse();
            accountConfigModuleVO.setShowNewOrderAlertTimes(IntegerBooleanConstants.BOOLEAN_FALSE);
            accountConfigModuleVO.setShowNewPickTaskAlertTimes(IntegerBooleanConstants.BOOLEAN_FALSE);
            accountConfigModuleVO.setShowPickTaskClaimOvertimeAlertTimes(IntegerBooleanConstants.BOOLEAN_FALSE);
            accountConfigModuleVO.setShowPickTaskOvertimeAlertTimes(IntegerBooleanConstants.BOOLEAN_FALSE);
            accountConfigModuleVO.setShowMergeOvertimeAlertTimes(IntegerBooleanConstants.BOOLEAN_FALSE);
            accountConfigModuleVO.setShowPhoneCallSetting(IntegerBooleanConstants.BOOLEAN_FALSE);

            AppAccountConfigDto appAccountConfig = new AppAccountConfigDto();
            for (AppAccountConfigDto appAccountConfigDto : appAccountConfigDtos) {
                if (appAccountConfigDto.getAccountId().equals(accountId)) {
                    appAccountConfig = appAccountConfigDto;
                    break;
                }
            }
            // 是否"订单"子tab,决定是否展示"新订单提醒"
            if (hasPermissionCodes.contains(AuthCodeEnum.ORDER_SUB_TAB.getAuthCode())) {
                accountConfigModuleVO.setShowNewOrderAlertTimes(IntegerBooleanConstants.BOOLEAN_TRUE);
                accountConfigVO.setNewOrderAlertTimes(appAccountConfig.getNewOrderAlertTimes());
            }
            // 判断是否展示拣货相关提醒配置
            if (hasPermissionCodes.contains(AuthCodeEnum.FULFILL_PICK_STATISTICS.getAuthCode())) {
                accountConfigModuleVO.setShowNewPickTaskAlertTimes(IntegerBooleanConstants.BOOLEAN_TRUE);
                accountConfigVO.setNewPickTaskAlertTimes(appAccountConfig.getNewPickTaskAlertTimes());
                accountConfigModuleVO.setShowPickTaskClaimOvertimeAlertTimes(IntegerBooleanConstants.BOOLEAN_TRUE);
                accountConfigVO.setPickTaskClaimOvertimeAlertTimes(appAccountConfig.getPickTaskClaimOvertimeAlertTimes());
                accountConfigModuleVO.setShowPickTaskOvertimeAlertTimes(IntegerBooleanConstants.BOOLEAN_TRUE);
                accountConfigVO.setPickTaskOvertimeAlertTimes(appAccountConfig.getPickTaskOvertimeAlertTimes());
            }
            // 判断是否展示合流相关提醒配置
            if (hasPermissionCodes.contains(AuthCodeEnum.FULFILL_MERGE_STATISTICS.getAuthCode())) {
                accountConfigModuleVO.setShowMergeOvertimeAlertTimes(IntegerBooleanConstants.BOOLEAN_TRUE);
                accountConfigVO.setMergeOvertimeAlertTimes(appAccountConfig.getMergeOvertimeAlertTimes());
            }
            // 判断是否展示电话设置配置  (需要至少展示订单模块,或拣货任务模块,  且有电话设置权限)
            if ((hasPermissionCodes.contains(AuthCodeEnum.ORDER_SUB_TAB.getAuthCode()) || hasPermissionCodes.contains(AuthCodeEnum.FULFILL_PICK_STATISTICS.getAuthCode()))
                    && hasPermissionCodes.contains(AuthCodeEnum.PHONE_CALL_SETTING.getAuthCode())) {

                accountConfigModuleVO.setShowPhoneCallSetting(IntegerBooleanConstants.BOOLEAN_TRUE);
                accountConfigVO.setReceivePhoneCall(appAccountConfig.getReceivePhoneCall());
            }

            accountConfigQueryResponse.setAccountConfigModuleVO(accountConfigModuleVO);
            accountConfigQueryResponse.setAccountConfigVO(accountConfigVO);
            return CommonResponse.success(accountConfigQueryResponse);
        }
        catch (TException ex) {
            throw new CommonRuntimeException("查询账号配置异常", ex);
        }
    }

    public CommonResponse setAccountConfig(AccountConfigVO accountConfigVO) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        long tenantId = identityInfo.getUser().getTenantId();
        long accountId = identityInfo.getUser().getAccountId();
        AppAccountConfigDto appAccountConfigDto = new AppAccountConfigDto();
        appAccountConfigDto.setAccountId(accountId);
        appAccountConfigDto.setNewOrderAlertTimes(accountConfigVO.getNewOrderAlertTimes());
        appAccountConfigDto.setNewPickTaskAlertTimes(accountConfigVO.getNewPickTaskAlertTimes());
        appAccountConfigDto.setPickTaskClaimOvertimeAlertTimes(accountConfigVO.getPickTaskClaimOvertimeAlertTimes());
        appAccountConfigDto.setPickTaskOvertimeAlertTimes(accountConfigVO.getPickTaskOvertimeAlertTimes());
        appAccountConfigDto.setMergeOvertimeAlertTimes(accountConfigVO.getMergeOvertimeAlertTimes());
        appAccountConfigDto.setReceivePhoneCall(accountConfigVO.getReceivePhoneCall());
        try {
            com.meituan.shangou.saas.tenant.thrift.dto.config.response.CommonResponse configResponse = businessConfigThriftService.setAppAccountConfig(tenantId, appAccountConfigDto, accountId);
            if (configResponse.getStatus().getCode() == StatusCodeEnum.SUCCESS.getCode()) {
                return CommonResponse.success(null);
            }
            else {
                return CommonResponse.fail(ResultCode.INTERNAL_SERVER_ERROR.getCode(), configResponse.getStatus().getMessage());
            }
        }
        catch (TException ex) {
            throw new CommonRuntimeException("设置账号异常", ex);
        }
    }

    public CommonResponse createManagerAndRelTenant(CreateManagerAndRelTenantRequest request) {
        try {
            StringResponse response = sacAccountManagerService.createManagerAndRelTenant(request);
            if (response.getSacStatus().getCode() == StatusCodeEnum.SUCCESS.getCode()) {
                return CommonResponse.success(response.getResult());
            }
        }
        catch (TException ex) {
            throw new CommonRuntimeException("大象绑定账号异常", ex);
        }
        return CommonResponse.fail(ResultCode.INTERNAL_SERVER_ERROR, "大象绑定账号异常");
    }

    public CommonResponse sendBindMobileVerificationCode(SendBindMobileVerificationCodeRequest request) {
        try {
            BindMobileVerificationCodeResponse response = accountManageThriftService.sendBindMobileVerificationCode(request);
            if (response.getResult().getCode() == StatusCodeEnum.SUCCESS.getCode()) {
                return CommonResponse.success(response.getYodaRequestCode());
            }
            log.error("绑定手机发送验证码异常", JacksonUtils.toJson(response));
        }
        catch (TException e) {
            log.error("绑定手机发送验证码异常", e);
            throw new CommonRuntimeException("绑定手机发送验证码异常", e);
        }
        return CommonResponse.fail(ResultCode.INTERNAL_SERVER_ERROR, "绑定手机发送验证码异常");
    }

    public YodaResultVO sendBindMobileInterfaceVerificationCode(SendBindMobileVerificationCodeRequest request) {
        try {
            YodaResultVO yodaResultVO = new YodaResultVO();
            InfoBindMobileResponse response = accountManageThriftService.infoBindMobile(request);
            if (response.getResult().getCode() != StatusCodeEnum.SUCCESS.getCode()) {
                log.error("绑定手机发送验证码异常", JacksonUtils.toJson(response));
                throw new CommonRuntimeException("绑定手机发送验证码异常");
            }
            yodaResultVO.setCode(response.getYodaCode());
            yodaResultVO.setMessage(response.getYodaMessage());
            yodaResultVO.setRequestCode(response.getYodaRequestCode());
            return yodaResultVO;
        }
        catch (TException e) {
            throw new CommonRuntimeException("绑定手机发送验证码异常", e);
        }
    }


    public CommonV2Response<YodaResultVO> sendForgetPasswordVerificationCode(SendForgetPassWordVerificationCodeRequest request) {
        try {
            YodaResultVO yodaResultVO = new YodaResultVO();
            InfoForgetPasswordResponse response = accountManageThriftService.infoForgetPassword(request);
            if (response.getResult().getCode() != StatusCodeEnum.SUCCESS.getCode()) {
                return CommonV2Response.fail(response.getResult().getCode(), response.getResult().getMsg());
            }
            yodaResultVO.setCode(response.getYodaCode());
            yodaResultVO.setMessage(response.getYodaMessage());
            yodaResultVO.setRequestCode(response.getYodaRequestCode());
            return CommonV2Response.success(yodaResultVO);
        }
        catch (TException e) {
            log.error("忘记密码发送验证码异常", e);
            throw new CommonRuntimeException("忘记密码发送验证码异常", e);
        }
    }


    public CommonResponse verifyAndBindMobile(VerifyAndBindMobileRequest request) {
        try {
            Result result = accountManageThriftService.verifyAndBindMobile(request);
            if (result.getCode() == StatusCodeEnum.SUCCESS.getCode()) {
                return CommonResponse.success(null);
            }
        }
        catch (TException e) {
            log.error("绑定手机号异常", e);
            throw new CommonRuntimeException("绑定手机号异常", e);
        }
        return CommonResponse.fail(ResultCode.INTERNAL_SERVER_ERROR, "绑定手机号异常");
    }

    public CommonV2Response verifyInterfaceAndBindMobile(SendBindMobileVerificationCodeRequest request) {
        try {
            Result result = accountManageThriftService.verifyBindMobile(request);
            if (result.getCode() == StatusCodeEnum.SUCCESS.getCode()) {
                return CommonV2Response.success(null);
            }
            return CommonV2Response.fail(result.getCode(), result.getMsg());
        }
        catch (TException e) {
            log.error("绑定手机号异常", e);
            throw new CommonRuntimeException("绑定手机号异常", e);
        }
    }

    public CommonV2Response verifyAndModifyPassword(SendForgetPassWordVerificationCodeRequest request) {
        try {
            Result result = accountManageThriftService.verifyForgetPassword(request);
            if (result.getCode() == StatusCodeEnum.SUCCESS.getCode()) {
                return CommonV2Response.success(null);
            }

            return CommonV2Response.fail(result.getCode(), result.getMsg());
        }
        catch (TException e) {
            log.error("忘记密码修改密码异常", e);
            throw new CommonRuntimeException("忘记密码修改密码异常", e);
        }
    }

    public CommonV2Response<String> getMarkMobile(String accountName) {
        try {

            SacCommonResponse<String> response = sacAccountManagerService.getMobileForForgetPassword(accountName);

            if (response.getSacStatus().getCode() == StatusCodeEnum.SUCCESS.getCode()) {

                String mobile = response.getResult();
                if (StringUtils.isBlank(mobile)) {
                    return CommonV2Response.fail(2, "当前账号未绑定手机号，请联系管理员修改密码");
                }

                return CommonV2Response.success(MaskUtils.maskMobile(mobile));
            }

            return CommonV2Response.fail(response.getSacStatus().getCode(), response.getSacStatus().getMessage());
        }
        catch (Exception e) {
            log.error("获取脱敏手机号异常", e);
            throw new CommonRuntimeException("获取脱敏手机号异常", e);
        }
    }


    public void deactivationAccount() {
        // 后续如果有前置检查逻辑可放这里
        authThriftWrapper.deactivationAccount();
    }

}
