package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2022-10-18
 * @email <EMAIL>
 */
@TypeDoc(
        description = "新建/编辑班次请求"
)
@ApiModel("新建/编辑班次请求")
@Data
public class QueryDetailListRequest {

    @FieldDoc(
            description = "规则id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "规则id")
    private Long ruleId;


    @FieldDoc(
            description = "起始时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "起始时间")
    private Long startTime;


    @FieldDoc(
            description = "结束时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "结束时间")
    private Long endTime;
}
