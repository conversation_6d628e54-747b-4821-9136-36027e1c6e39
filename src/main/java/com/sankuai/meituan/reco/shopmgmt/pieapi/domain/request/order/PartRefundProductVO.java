package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2019/7/11
 * desc: 部分退款商品
 */
@TypeDoc(
        description = "部分退款商品"
)
@ApiModel("部分退款商品")
@Data
public class PartRefundProductVO {

    @FieldDoc(
            description = "SKU编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "SKU编码")
    private String skuId;

    @FieldDoc(
            description = "线上渠道sku编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "线上渠道sku编码")
    private String customSkuId;

    @FieldDoc(
            description = "SKU名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "SKU名称")
    @NotNull
    private String skuName;

    @FieldDoc(
            description = "商品数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品数量")
    @NotNull
    private Integer count;

    @FieldDoc(
            description = "商家门店SKU编码 对应channelSkuId", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商家门店SKU编码 对应channelSkuId")
    private String extCustomSkuId;
}
