package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.wavepick.converter;

import com.sankuai.meituan.reco.pickselect.thrift.wave.request.WavePickSkuDetailRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick.WarehousePickSkuDetailRequest;
import org.mapstruct.Mapper;

/**
 * @Description:
 * @Author: zhangjian155
 * @Date: 2022/8/25 15:15
 */
@Mapper(componentModel = "spring")
public abstract class WarehousePickSkuDetailRequestConverter {

    public abstract WavePickSkuDetailRequest convert2ThriftRequest(WarehousePickSkuDetailRequest request,
                                                                   Long tenantId, Long accountId, Long storeId);
}
