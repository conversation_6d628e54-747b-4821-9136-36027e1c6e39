package com.sankuai.meituan.reco.shopmgmt.pieapi.service;

import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.SiteTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.DeliveryOperationThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.QueryAggLinkBaseRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.QueryAggOrderDetailLinkRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.QueryAggStoreSettingsLinkRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response.QueryAggLinkResponse;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2025-01-14
 */
@Slf4j
@Service
public class AggPlatformDeliveryApplicationService {
    @Autowired
    private DeliveryOperationThriftService deliveryOperationThriftService;

    public String queryAggDeliveryOrderDetailLinkUrl(Long orderId, Long poiId) {
        QueryAggOrderDetailLinkRequest request = new QueryAggOrderDetailLinkRequest();
        request.setOrderId(orderId);
        buildQueryAggLinkBaseRequest(request, poiId);
        QueryAggLinkResponse response = deliveryOperationThriftService.queryAggOrderDetailLink(request);
        if (response == null || !Objects.equals(response.getStatus().getCode(), Status.SUCCESS.code) || StringUtils.isBlank(response.getUrl())) {
            log.error("queryAggDeliveryOrderDetailLinkUrl is error, request:{}, response:{}", request, response);
            throw new BizException("获取配送详情链接异常");
        }
        return response.getUrl();
    }

    public String queryAggStoreSettingsLinkUrl(Long poiId, Integer platformCode) {
        QueryAggStoreSettingsLinkRequest request = new QueryAggStoreSettingsLinkRequest();
        request.setPlatformCode(platformCode);
        request.setEToken(ApiMethodParamThreadLocal.getIdentityInfo().getToken());
        buildQueryAggLinkBaseRequest(request, poiId);
        QueryAggLinkResponse response = deliveryOperationThriftService.queryAggStoreSettingsLink(request);
        if (response == null || !Objects.equals(response.getStatus().getCode(), Status.SUCCESS.code) || StringUtils.isBlank(response.getUrl())) {
            log.error("queryAggStoreSettingsLinkUrl is error, request:{}, response:{}", request, response);
            throw new BizException("获取门店配置链接异常");
        }
        return response.getUrl();
    }

    private void buildQueryAggLinkBaseRequest(QueryAggLinkBaseRequest request, Long poiId) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        request.setPoiId(poiId);
        request.setTenantId(user.getTenantId());
        request.setDeviceType(SiteTypeEnum.APP.getCode());
        request.setOperatorAccount(String.valueOf(user.getEmployeeId()));
        request.setOperatorName(user.getOperatorName());
    }
}
