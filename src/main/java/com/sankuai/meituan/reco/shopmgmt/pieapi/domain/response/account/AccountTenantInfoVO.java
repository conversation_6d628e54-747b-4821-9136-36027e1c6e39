package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.account;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @createTime 2019/11/25
 * @description
 */
@TypeDoc(
        description = "账号租户信息"
)
@Data
@ApiModel("账号租户信息")
public class AccountTenantInfoVO {

    @FieldDoc(
            description = "租户名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "租户名称", required = true)
    @NotNull
    private String tenantName;

    @ApiModelProperty(value = "租户 LOGO 地址（S3链接）", required = false)
    private String logoUrl;

    @ApiModelProperty(value = "租户ID", required = false)
    private Long tenantId;

    @ApiModelProperty(value = "账号ID", required = false)
    private Long accountId;

    @ApiModelProperty(value = "账号类型", required = false)
    private Integer accountType;

    @ApiModelProperty(value = "账号名称", required = false)
    private String accountName;

    @ApiModelProperty(value = "员工名称", required = false)
    private String empName;

}
