package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.sn;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sn.SnInfoVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.shangou.logistics.wio.client.common.Page;
import com.sankuai.shangou.logistics.wio.client.common.Result;
import com.sankuai.shangou.logistics.wio.client.sn.SnService;
import com.sankuai.shangou.logistics.wio.client.sn.enums.SnStatus;
import com.sankuai.shangou.logistics.wio.client.sn.model.SnDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/9/14 15:01
 **/
@Rhino
@Slf4j
public class SnCenterServiceWrapper {
    @Resource
    private SnService snService;

    @MethodLog(logRequest = true, logResponse = true)
    @Degrade(rhinoKey = "SnCenterServiceWrapper.checkSnCode", fallBackMethod = "checkSnCodeFallback")
    public boolean checkSnCode(Long tenantId, Long storeId, String snCode, String skuId) {

        log.info("start invoke snService.query, storeId:{}, snCode:{}, skuId:{}", storeId, snCode, skuId);
        Result<Page<SnDTO>> result = snService.query(tenantId, Collections.singletonList(storeId),
                Collections.singletonList(skuId), Collections.singletonList(snCode), null,
                Collections.singletonList(SnStatus.IN_WAREHOUSE), 1, 20);
        log.info("end invoke snService.query, storeId:{}, snCode:{}, skuId:{}, result:{}", storeId, snCode, skuId, result);

        if (result == null) {
            throw new CommonRuntimeException("调用sn服务失败，返回结果不合法");
        }

        if (result.getCode() != ResultCode.SUCCESS.code) {
            throw new CommonRuntimeException("调用sn服务失败," + result.getMessage());
        }

        if (result.getModule() == null) {
            throw new CommonRuntimeException("调用sn服务失败，返回结果不合法");
        }

        if (CollectionUtils.isEmpty(result.getModule().getItems())) {
            return false;
        }

        //正常情况查询接口已经满足条件, 以防万一再check一遍
        Optional<SnDTO> checkedSnCodeOpt = result.getModule().getItems().stream()
                .filter(item -> Objects.equals(item.getLocatedWarehouseId(), storeId))
                .filter(item -> StringUtils.equals(item.getSnCode(), snCode) && StringUtils.equals(item.getSkuId(), skuId))
                .findAny();

        return checkedSnCodeOpt.isPresent();
    }

    public boolean checkSnCodeFallback(Long tenantId, Long storeId, String snCode, String skuId) {
        log.warn("校验sn码已降级, snCode:{}, skuId:{}, ", snCode, skuId);
        return true;
    }

    @MethodLog(logRequest = true, logResponse = true)
    @Degrade(rhinoKey = "SnCenterServiceWrapper.fuzzySearchSnCode", fallBackMethod = "fuzzySearchSnCodeFallback")
    public List<SnInfoVo> fuzzySearchSnCode(Long tenantId, Long storeId, String snCodeKeyWord, String skuId) {

        log.info("start invoke snService.queryByCodeKeyword, storeId:{}, snCodeKeyWord:{}, skuId:{}", storeId, snCodeKeyWord, skuId);
        Result<Page<SnDTO>> result = snService.queryByCodeKeyword(tenantId, storeId, skuId, snCodeKeyWord,
                Collections.singletonList(SnStatus.IN_WAREHOUSE), false, 1, 100);
        log.info("end invoke snService.queryByCodeKeyword, storeId:{}, snCodeKeyWord:{}, skuId:{}, result:{}", storeId, snCodeKeyWord, skuId, result);

        if (result == null) {
            throw new CommonRuntimeException("调用sn服务失败，返回结果不合法");
        }

        if (result.getCode() != ResultCode.SUCCESS.code) {
            throw new CommonRuntimeException("调用sn服务失败," + result.getMessage());
        }

        if (result.getModule() == null) {
            throw new CommonRuntimeException("调用sn服务失败，返回结果不合法");
        }

        if (CollectionUtils.isEmpty(result.getModule().getItems())) {
            return Collections.emptyList();
        }

        return  Optional.ofNullable(result.getModule().getItems())
                .orElse(Collections.emptyList())
                .stream()
                .map(dto -> new SnInfoVo(dto.getLocatedWarehouseId(), dto.getSkuId(), dto.getSnCode()))
                .collect(Collectors.toList());
    }

    public List<SnDTO> fuzzySearchSnCodeFallback(Long tenantId, Long storeId, String snCodeKeyWord, String skuId) {
        log.warn("SnCenterServiceWrapper.fuzzySearchSnCode已被降级, skuId:{}, storeId:{}, snCodeKeyWord:{}", skuId, storeId, snCodeKeyWord);
        return Collections.emptyList();
    }
}
