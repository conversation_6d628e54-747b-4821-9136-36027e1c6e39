package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.contract;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.saas.common.aop.feature.Validatable;

import com.sankuai.meituan.shangou.saas.common.utils.AssertUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@TypeDoc(description = "消息提醒请求")
@ApiModel("消息提醒请求")
@Data
public class RemindRequest implements Validatable {

    @FieldDoc(
            description = "提醒类型", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "提醒类型", required = true)
    private List<String> types;

    @Override
    public void validate() {
        AssertUtil.notEmpty(types, "提醒类型不能为空");
    }
}