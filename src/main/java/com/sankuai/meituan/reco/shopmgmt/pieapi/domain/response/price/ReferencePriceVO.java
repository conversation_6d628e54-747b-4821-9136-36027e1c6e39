package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.price.client.dto.price_effect.ReferencePriceDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Objects;

/**
 * @Author: wangyihao04
 * @Date: 2020-11-04 10:21
 * @Mail: <EMAIL>
 */
@TypeDoc(
        description = "参考价模型"
)
@ApiModel("参考价模型")
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
public class ReferencePriceVO {
    @FieldDoc(
            description = "参考价类型"
    )
    @ApiModelProperty("参考价类型")
    private String referencePriceType;
    @FieldDoc(
            description = "参考价名称"
    )
    @ApiModelProperty("参考价名称")
    private String referencePriceName;
    @FieldDoc(
            description = "参考价(当前规格下)"
    )
    @ApiModelProperty("参考价(当前规格下)")
    private Long referencePrice;
    @FieldDoc(
            description = "参考价(标准规格下)"
    )
    @ApiModelProperty("参考价(标准规格下)")
    private Long standardUnitReferencePrice;

    public static ReferencePriceVO valueOf(ReferencePriceDTO dto){
        if (Objects.isNull(dto)){
            return null;
        }
        return ReferencePriceVO.builder()
                .referencePriceType(dto.getReferencePriceType())
                .referencePriceName(dto.getReferencePriceName())
                .standardUnitReferencePrice(dto.getStandardUnitReferencePrice())
                .build();
    }
}
