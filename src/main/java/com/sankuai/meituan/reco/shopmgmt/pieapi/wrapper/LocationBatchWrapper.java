package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.meituan.shangou.saas.order.management.client.dto.request.revenue.MerchantOrderRevenueDetailRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.FallbackException;
import com.sankuai.meituan.reco.stock.operate.center.thrift.LocationBatchRecommendThriftService;
import com.sankuai.meituan.reco.stock.operate.center.thrift.dto.RecommendLocationBatch;
import com.sankuai.meituan.reco.stock.operate.center.thrift.request.QueryLocationBatchListRequest;
import com.sankuai.meituan.reco.stock.operate.center.thrift.response.QueryLocationBatchListResponse;
import com.sankuai.meituan.reco.store.management.thrift.UserInfo;
import com.sankuai.meituan.reco.store.saas.infrastructure.shield.common.monitor.CommonMonitorTransaction;
import com.sankuai.meituan.reco.store.wms.enums.ResultCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
@Rhino
public class LocationBatchWrapper {

    @Resource
    private LocationBatchRecommendThriftService locationBatchRecommendThriftService;

    @MethodLog(logRequest = true, logResponse = true)
    @Degrade(rhinoKey = "LocationBatchWrapper.queryLocationBatchRecommend",
            fallBackMethod = "queryLocationBatchRecommendFallback",
            timeoutInMilliseconds = 2000)
    public List<RecommendLocationBatch> queryLocationBatchRecommend(QueryLocationBatchListRequest request) {
        QueryLocationBatchListResponse response = null;
        try {
            response = locationBatchRecommendThriftService.queryLocationBatchList(request);
        } catch (Exception e) {
            log.error("invoke LocationBatchRecommendThriftService.queryLocationBatchRecommend error, req{}, resp:{}", request, response);
            throw new CommonRuntimeException(e);
        }

        if (response == null || response.code == null) {
            log.error("invoke LocationBatchRecommendThriftService.queryLocationBatchRecommend error, req{}, resp:{}", request, response);
            throw new CommonRuntimeException("查询库位批次推荐异常");
        }

        if (response.code != ResultCodeEnum.SUCCESS.getCode()) {
            log.warn("invoke LocationBatchRecommendThriftService.queryLocationBatchRecommend fail, req{}, resp:{}", request, response);
            throw new CommonRuntimeException(Optional.ofNullable(response.msg).orElse("查询库位批次推荐异常"));
        }

        return Optional.ofNullable(response.recommendLocationBatchList).orElse(Collections.emptyList());
    }


    public CommonResponse queryLocationBatchRecommendFallback(QueryLocationBatchListRequest request) {
        log.warn("LocationBatchWrapper.queryLocationBatchRecommend->调用降级方法 request:{}", request);
        throw new CommonRuntimeException("查询库位批次推荐异常");
    }

}
