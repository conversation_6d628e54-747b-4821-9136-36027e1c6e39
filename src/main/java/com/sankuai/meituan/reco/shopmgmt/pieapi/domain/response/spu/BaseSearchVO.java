package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.BaseSearchDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.MedicalStandardProductSearchDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StandardProductSearchDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.TenantSpuSearchDTO;
import lombok.Data;

/**
 * 快捷建品商品搜索响应
 *
 * <AUTHOR>
 * @date 2021-09-26 18:59
 */
@Data
public class BaseSearchVO {
    @FieldDoc(
            description = "数据源类型，商品池-1，标品库-2，医药标品库-3", requiredness = Requiredness.REQUIRED
    )
    private int datasourceType;

    public static BaseSearchVO convert(BaseSearchDTO dto) {
        if (dto instanceof TenantSpuSearchDTO) {
            return TenantSpuSearchVO.convert((TenantSpuSearchDTO) dto);
        } else if (dto instanceof StandardProductSearchDTO) {
            return StandardProductSearchVO.convert((StandardProductSearchDTO) dto);
        }
        else if (dto instanceof MedicalStandardProductSearchDTO) {
            return StandardProductSearchVO.convert((MedicalStandardProductSearchDTO) dto);
        }
        else {
            return null;
        }
    }
}
