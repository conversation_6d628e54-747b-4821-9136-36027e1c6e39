package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.auth;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TypeDoc(
        description = "检查设备ID是否在灰度列表中"
)
@Data
@ApiModel("检查设备ID是否在灰度列表中")
public class EquipmentCheckResponse {

    @ApiModelProperty(value = "true-在灰度列表中，false-不在灰度列表中")
    private Boolean grayEquipment;

}
