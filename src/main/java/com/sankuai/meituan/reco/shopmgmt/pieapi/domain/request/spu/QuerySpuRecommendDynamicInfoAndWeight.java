package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;


import com.google.common.base.Preconditions;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.request.ChannelRecommendDynamicInfoAndWeightReq;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.product.request.SpuRecommendWeightRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;
import java.util.Objects;

@TypeDoc(
        description = "获取商品渠道推荐动态信息"
)
@Setter
@Getter
@ApiModel("获取商品渠道推荐动态信息")
@ToString
public class QuerySpuRecommendDynamicInfoAndWeight {

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称")
    private String spuName;

    @FieldDoc(
            description = "渠道id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道id")
    @NotNull
    private Integer channelId;

    @FieldDoc(
            description = "渠道类目id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道类目id")
    private String channelCategoryId;

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店id")
    private String storeId;

    @FieldDoc(
            description = "UPC信息，若存在多个，则传入第一个", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "UPC信息，若存在多个，则传入第一个")
    private String upc;

    @FieldDoc(
            description = "兼容前端逻辑，主档时无法做到storeId不传值", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "兼容前端逻辑，主档时无法做到storeId不传值")
    private Boolean isMerchantSpu;

    public ChannelRecommendDynamicInfoAndWeightReq convertRpcRequest() {
        ChannelRecommendDynamicInfoAndWeightReq req = new ChannelRecommendDynamicInfoAndWeightReq();
        req.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        if (BooleanUtils.isFalse(isMerchantSpu) && StringUtils.isNotBlank(storeId)) {
            req.setStoreId(Long.valueOf(storeId));
        }
        req.setChannelId(channelId);
        req.setSpuName(spuName);
        req.setUpc(upc);
        req.setChannelCategoryId(channelCategoryId);
        return req;
    }

    public void selfCheck() {
        Preconditions.checkArgument(StringUtils.isNotBlank(spuName), "商品名称不能为空");
        Preconditions.checkArgument(Objects.nonNull(channelId), "渠道id不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(channelCategoryId), "渠道类目id不能为空");
    }
}
