package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.ChannelStoreSpuFrontCategoryVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：2020/5/19 2:33 下午
 */
@TypeDoc(
        description = "修改商品店内分类请求"
)
@Data
@ApiModel("修改商品店内分类请求")
public class BatchChangeSpuFrontCategoryRequest {
    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID", required = true)
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "渠道门店商品前台分类参数集合", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道门店商品前台分类参数集合", required = true)
    @NotNull
    private List<ChannelStoreSpuFrontCategoryVO> channelSpuFrontCategoryList;
}
