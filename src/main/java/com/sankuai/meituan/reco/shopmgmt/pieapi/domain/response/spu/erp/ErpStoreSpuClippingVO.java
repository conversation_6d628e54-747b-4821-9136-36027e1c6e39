package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/19
 */
@TypeDoc(
        description = "ERP门店商品SPU信息裁剪后对象集，配合前端完成PDA拆分项目"
)
@Data
@ApiModel("ERP门店商品SPU信息裁剪后对象集")
public class ErpStoreSpuClippingVO {

    @FieldDoc(description = "门店ID", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "门店ID")
    private Long storeId;

    @FieldDoc(description = "spu编码", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "spu编码")
    private String spuId;

    @FieldDoc(description = "商品名称", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "商品名称")
    private String name;

    @FieldDoc(description = "主图", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "主图")
    private String mainImageUrl;

    @FieldDoc(description = "上线状态 1-上线 2-自动下线 3-手动下线", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "上线状态 1-上线 2-自动下线 3-手动下线")
    private Integer onlineStatus;

    @FieldDoc(description = "上架状态 1-上架 2-自动下架 3-手动下架", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "上架状态 1-上架 2-自动下架 3-手动下架")
    private Integer saleStatus;

    @FieldDoc(description = "价格状态", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "价格状态")
    private Integer priceStatus;

    @FieldDoc(description = "库存状态", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "库存状态")
    private Integer stockStatus;

    @FieldDoc(description = "是否必卖 1-必卖 2-非必卖", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "是否必卖 1-必卖 2-非必卖")
    private Integer mustSale;

    @FieldDoc(description = "商品在开通渠道的状态", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "商品在开通渠道的状态")
    private List<ErpChannelSpuStatusVO> channelSpuStatusList;

    @FieldDoc(description = "商品规格信息", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "商品规格信息")
    private List<ErpStoreSkuClippingVO> storeSkuList;

}