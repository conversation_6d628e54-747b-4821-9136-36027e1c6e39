package com.sankuai.meituan.reco.shopmgmt.pieapi.facade.tenantconfig;

import com.dianping.rhino.annotation.Rhino;
import com.meituan.shangou.saas.tenant.config.TenantChannelConfigKey;
import com.meituan.shangou.saas.tenant.thrift.ConfigThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.config.ChannelConfigDto;
import com.meituan.shangou.saas.tenant.thrift.dto.config.response.TenantChannelConfigListResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.service.TenantConfigThriftService;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * @Author: <EMAIL>
 * @Date: 2019/1/8 16:34
 * @Description:
 */
@Rhino
@Service
@Slf4j
public class TenantConfigClient {

    /**
     * 和租户约定好的初始化定价策略key值
     */
    private static final String TENANT_INITIAL_PRICE_STRATEGY_KEY = "tenantInitialPriceStrategy";
    private static final String BIZ_MODE_KEY = "biz_mode";
    private static final Integer MERCHANT_CHARGE_CONFIG_CODE = 44;
    private static final Integer NOT_MERCHANT_CHARGE_GRAY = 2004;
    private static final String MERCHANT_CHARGE_CONFIG_KEY = "zongbuguanpin";
    private static final String NOT_MERCHANT_CHARGE_CONFIG_KEY = "nonZbgpTmpGrayTenant";
    private static final Integer PRODUCT_MANAGEMENT_TYPE_CODE = 29;
    private static final String PRODUCT_MANAGEMENT_TYPE = "product_management_type";

    private static final String TENANT_SWITCH_ON = "1";

    @Autowired
    private ConfigThriftService configThriftService;

    @Autowired
    private TenantConfigThriftService tenantConfigThriftService;

    /**
     * 批量查询单个租户的渠道配置
     * @param tenantChannelConfigKeyList 租户渠道配置键列表
     * @return 租户渠道配置列表
     */
    public List<ChannelConfigDto> batchQueryTenantChannelConfig(List<TenantChannelConfigKey> tenantChannelConfigKeyList) {
        TenantChannelConfigListResponse response;
        try {
            response = configThriftService.batchQueryTenantChannelConfigByKey(tenantChannelConfigKeyList);
            log.info("批量查询租户渠道配置 tenantChannelConfigKeyList:{},response:{}", tenantChannelConfigKeyList, response);
        } catch (Exception e) {
            log.error("批量查询租户渠道配置 tenantChannelConfigKeyList:{}", tenantChannelConfigKeyList, e);
            throw new BizException("查询租户配置异常", e);
        }

        if (response == null || response.getStatus() == null) {
            throw new BizException("查询租户配置未返回结果");
        }

        if (response.getStatus().getCode() != 0) {
            throw new BizException(response.getStatus().getMessage());
        }

        return response.getChannelConfigList();
    }

}
