package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.google.common.collect.Lists;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.storecategory.MerchantStoreCategoryVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.shangou.platform.empower.product.client.dto.MerchantStoreCategoryDTO;
import com.sankuai.meituan.shangou.platform.empower.product.client.enums.StoreCategorySceneIdentityEnum;
import com.sankuai.meituan.shangou.platform.empower.product.client.request.merchant_store_category
        .QueryMerchantStoreCategoryListRequest;
import com.sankuai.meituan.shangou.platform.empower.product.client.response.MerchantStoreCategoryListResponse;
import com.sankuai.meituan.shangou.platform.empower.product.client.service.MerchantStoreCategoryThriftService;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class MerchantStoreCategoryWrapper {

    @Resource
    private MerchantStoreCategoryThriftService merchantStoreCategoryThriftService;

    public List<MerchantStoreCategoryVO> queryAll(Long tenantId) {
        try {
            MerchantStoreCategoryListResponse response = merchantStoreCategoryThriftService.queryMerchantStoreCategoryList
                    (tenantId);
            if (response.getCode() == ResultCode.SUCCESS.getCode()) {
                return buildCategoryList(response.getMerchantStoreCategoryList());
            }
        } catch (Exception e) {
            log.error("Query all merchant store category error, tenantId:{}.", e, tenantId);
        }
        throw new CommonLogicException("获取店内分类异常");
    }

    public List<MerchantStoreCategoryVO> query(Long tenantId, Integer storeGroupId) {
        try {
            QueryMerchantStoreCategoryListRequest request = new QueryMerchantStoreCategoryListRequest();
            request.setMerchantId(tenantId);
            request.setStoreGroupId(storeGroupId);
            request.setNewStoreGroupId(storeGroupId.longValue());
            // 目前该方法的使用的场景一定是总部查询，所以添加总部场景参数
            request.setSceneIdentity(StoreCategorySceneIdentityEnum.MERCHANT.getCode());
            MerchantStoreCategoryListResponse response = merchantStoreCategoryThriftService
                    .queryMerchantStoreCategoryListByCondition(request);
            if (response.getCode() == ResultCode.SUCCESS.getCode()) {
                return buildCategoryList(response.getMerchantStoreCategoryList());
            }
        } catch (Exception e) {
            log.error("Query merchant store category by condition error, tenantId:{}.", e, tenantId);
        }
        throw new CommonLogicException("获取店内分类异常");
    }

    private List<MerchantStoreCategoryVO> buildCategoryList(List<MerchantStoreCategoryDTO> categoryList) {
        if (CollectionUtils.isEmpty(categoryList)) {
            return Collections.emptyList();
        }

        List<MerchantStoreCategoryVO> resultList = Lists.newArrayList();
        Map<Pair<Integer, Long>, MerchantStoreCategoryDTO> categoryMap = categoryList.stream().collect(Collectors.toMap
                (category -> Pair.of(category.getStoreGroupId(), category.getCategoryId()), Function.identity(), (o1, o2) -> o2));
        for (MerchantStoreCategoryDTO dto : categoryList) {
            MerchantStoreCategoryVO vo = new MerchantStoreCategoryVO();
            vo.setName(dto.getCategoryName());
            vo.setCategoryId(dto.getCategoryId());
            vo.setParentCategoryId(dto.getParentCategoryId());
            vo.setLevel(dto.getLevel());
            vo.setStoreGroupId(dto.getStoreGroupId());
            vo.setEnable(dto.getEnable());
            vo.setSequence(dto.getSequence());
            if (dto.getLevel() == 1) {
                vo.setParentName(Strings.EMPTY);
                vo.setParentNamePath(Strings.EMPTY);
                vo.setNamePath(dto.getCategoryName());
            } else {
                // 店内分类只会有两级类目，这里默认level=2
                MerchantStoreCategoryDTO parent = categoryMap.get(Pair.of(dto.getStoreGroupId(), dto.getParentCategoryId()));
                if (parent != null) {
                    vo.setParentName(parent.getCategoryName());
                    vo.setParentNamePath(parent.getCategoryName());
                    vo.setNamePath(parent.getCategoryName() + ">" + dto.getCategoryName());
                } else {
                    vo.setParentName(Strings.EMPTY);
                    vo.setParentNamePath(Strings.EMPTY);
                    vo.setNamePath(dto.getCategoryName());
                }
            }

            resultList.add(vo);
        }
        return resultList;
    }

}
