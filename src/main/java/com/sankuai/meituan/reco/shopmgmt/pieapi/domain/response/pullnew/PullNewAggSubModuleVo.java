package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 4/21/23
 */
@TypeDoc(
        description = "我的推广记录页面子模块数据"
)
@Data
@ApiModel("我的推广记录页面子模块数据")
public class PullNewAggSubModuleVo {

    @FieldDoc(
            description = "子模块名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "子模块名称", required = true)
    private String name;


    @FieldDoc(
            description = "子模块数据，可能包含%", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "子模块数据，可能包含%", required = true)
    private String value;

    @FieldDoc(
            description = "子模块描述", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "子模块描述", required = true)
    private String desc;
}
