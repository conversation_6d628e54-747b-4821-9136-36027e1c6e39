package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.delivery;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
public class StoreAggDeliveryConfigVo {

    @FieldDoc(
            description = "聚合配送门店配置"
    )
    @ApiModelProperty(value = "聚合配送门店配置")
    private List<DeliveryChannelConfigVo> deliveryChannelConfigs;
}
