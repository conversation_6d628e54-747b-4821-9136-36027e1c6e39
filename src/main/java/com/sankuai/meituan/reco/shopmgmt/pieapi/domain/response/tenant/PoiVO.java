package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.tenant;

import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiBaseInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@TypeDoc(
        description = "门店信息"
)
@Data
@ApiModel("门店信息")
public class PoiVO {

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID", required = true)
    private Long poiId;

    @FieldDoc(
            description = "门店名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店名称", required = true)
    private String poiName;

    public static PoiVO ofBase(PoiBaseInfoDto poiBaseInfoDto) {
        PoiVO poiVO = new PoiVO();
        poiVO.setPoiId(poiBaseInfoDto.getPoiId());
        poiVO.setPoiName(poiBaseInfoDto.getPoiName());
        return poiVO;
    }

    public static PoiVO ofDTO(PoiInfoDto poiInfoDto) {
        if (poiInfoDto == null) {
            return null;
        }
        PoiVO poiVO = new PoiVO();
        poiVO.setPoiId(poiInfoDto.getPoiId());
        poiVO.setPoiName(poiInfoDto.getPoiName());
        return poiVO;
    }

    public static List<PoiVO> ofDTO(List<PoiInfoDto> poiInfoDtos) {
        if (CollectionUtils.isEmpty(poiInfoDtos)) {
            return Lists.newArrayList();
        }
        return poiInfoDtos.stream().map(PoiVO::ofDTO).filter(Objects::nonNull).collect(Collectors.toList());
    }

}
