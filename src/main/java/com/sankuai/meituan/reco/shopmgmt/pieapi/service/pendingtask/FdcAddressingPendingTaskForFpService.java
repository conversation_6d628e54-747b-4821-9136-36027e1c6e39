/*
 * Copyright (c) 2019 Meituan.com. All Rights Reserved.
 */
package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.regionselect.rsm.FdcAddressingTaskStatVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.regionselect.FdcAddressingWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 有单收货未完成量查询
 * <br><br>
 * Author: linjianyu <br>
 * Date: 2019-03-27 Time: 15:10
 * @since 2.1 权限迁移版本
 */
@Service
public class FdcAddressingPendingTaskForFpService extends AbstractSinglePendingTaskService {

    @Autowired
    private FdcAddressingWrapper fdcAddressingWrapper;

    @Override
    public PendingTaskResult getPendingTaskCount(PendingTaskParam param) throws Exception {

        CommonResponse<FdcAddressingTaskStatVO> resp = fdcAddressingWrapper.queryStatusStatisticsForFp();
        if(!resp.isSuccess()){
            return PendingTaskResult.createNumberMarker(0);
        }

        FdcAddressingTaskStatVO vo = resp.getData();
        int count = vo == null ? 0 : vo.getInProgressCount() + vo.getPendingCount();

        return PendingTaskResult.createNumberMarker(count);
    }

    @Override
    protected AuthCodeEnum module() {
        return AuthCodeEnum.DH_SEARCH_TASK;
    }
}
