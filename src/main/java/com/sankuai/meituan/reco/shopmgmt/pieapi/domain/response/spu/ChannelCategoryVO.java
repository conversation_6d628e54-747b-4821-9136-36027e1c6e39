package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelCategoryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Title: ChannelCategoryVO
 * @Description: 渠道类目信息
 * @Author: zhaolei12
 * @Date: 2020/5/16 5:57 下午
 */
@TypeDoc(
        description = "渠道类目信息"
)
@Data
@ApiModel("渠道类目信息")
public class ChannelCategoryVO {
    @FieldDoc(
            description = "渠道编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道编码")
    private Integer channelId;

    @FieldDoc(
            description = "渠道类目编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道类目编码")
    private String channelCategoryCode;

    @FieldDoc(
            description = "渠道类目名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道类目名称")
    private String channelCategoryName;

    @FieldDoc(
            description = "渠道类目编码全路径", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道类目编码全路径")
    private String channelCategoryCodePath;

    @FieldDoc(
            description = "渠道类目名称全路径", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道类目名称全路径")
    private String channelCategoryNamePath;

    @FieldDoc(
            description = "渠道类目动态信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道类目动态信息")
    private List<ChannelDynamicInfoVO> channelDynamicInfoVOList;

    @FieldDoc(
            description = "是否校验upc (0:不校验,1:校验，注意，只有三级分类该属性才有效)"
    )
    @ApiModelProperty(name = "是否校验upc (0:不校验,1:校验，注意，只有三级分类该属性才有效")
    private Integer checkUpcStatus;

    @FieldDoc(
            description = "该类目的商品条形码（UPC）是否必填，0-必填，1-选填 (只有三级分类该属性才有效)"
    )
    @ApiModelProperty(name = "该类目的商品条形码（UPC）是否必填，0-必填，1-选填 (只有三级分类该属性才有效)")
    private Integer upcRequired;
    @FieldDoc(
            description = "美团类目，类目来源1-零售，2-医药",
            example = {}
    )
    @ApiModelProperty(name = "美团类目，类目来源1-零售，2-医药")
    private Integer resourceType;

    @FieldDoc(
            description = "医药和零售是否为相同的类目，0-不相同，1-相同",
            example = {}
    )
    @ApiModelProperty(name = "医药和零售是否为相同的类目，0-不相同，1-相同")
    private Integer existedInRetailAndMedicine;

    @FieldDoc(
            description = "渠道类目信息是否可以修改",
            example = {}
    )
    @ApiModelProperty(name = "渠道类目信息是否可以修改")
    protected Boolean channelCategoryCanModify;

    @FieldDoc(
            description = "医疗器械资质信息填写要求，0-无需填写，1-非必填，2-必填",
            example = "1"
    )
    @ApiModelProperty(name = "医疗器械资质信息填写要求，0-无需填写，1-非必填，2-必填")
    private Integer medicalDeviceQuaRequirement;

    public static ChannelCategoryVO ofDTO(ChannelCategoryDTO channelCategoryDTO) {
        if (channelCategoryDTO == null) {
            return null;
        }
        ChannelCategoryVO channelCategoryVO = new ChannelCategoryVO();
        channelCategoryVO.setChannelCategoryCode(channelCategoryDTO.getChannelCategoryCode());
        channelCategoryVO.setChannelCategoryName(channelCategoryDTO.getChannelCategoryName());
        channelCategoryVO.setChannelCategoryCodePath(channelCategoryDTO.getChannelCategoryCodePath());
        channelCategoryVO.setChannelCategoryNamePath(channelCategoryDTO.getChannelCategoryNamePath());
        channelCategoryVO.setChannelDynamicInfoVOList(ChannelDynamicInfoVO.ofDTOList(channelCategoryDTO.getChannelDynamicInfoDTOList()));
        channelCategoryVO.setUpcRequired(channelCategoryDTO.getUpcRequired());
        channelCategoryVO.setCheckUpcStatus(channelCategoryDTO.getCheckUpcStatus());
        channelCategoryVO.setResourceType(channelCategoryDTO.getResourceType());
        channelCategoryVO.setExistedInRetailAndMedicine(channelCategoryDTO.getExistedInRetailAndMedicine());
        channelCategoryVO.setMedicalDeviceQuaRequirement(channelCategoryDTO.getMedicalDeviceQuaRequirement());
        return channelCategoryVO;
    }

    public static ChannelCategoryVO ofBizDTO(com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelCategoryDTO channelCategoryDTO) {
        if (channelCategoryDTO == null) {
            return null;
        }
        ChannelCategoryVO channelCategoryVO = new ChannelCategoryVO();
        channelCategoryVO.setChannelId(channelCategoryDTO.getChannelId());
        channelCategoryVO.setChannelCategoryCode(channelCategoryDTO.getChannelCategoryCode());
        channelCategoryVO.setChannelCategoryName(channelCategoryDTO.getChannelCategoryName());
        channelCategoryVO.setChannelCategoryCodePath(channelCategoryDTO.getChannelCategoryCodePath());
        channelCategoryVO.setChannelCategoryNamePath(channelCategoryDTO.getChannelCategoryNamePath());
        channelCategoryVO.setChannelDynamicInfoVOList(ChannelDynamicInfoVO.ofBizDTOList(channelCategoryDTO.getChannelDynamicInfoDTOList()));
        channelCategoryVO.setUpcRequired(channelCategoryDTO.getUpcRequired());
        channelCategoryVO.setCheckUpcStatus(channelCategoryDTO.getCheckUpcStatus());
        channelCategoryVO.setResourceType(channelCategoryDTO.getResourceType());
        channelCategoryVO.setExistedInRetailAndMedicine(channelCategoryDTO.getExistedInRetailAndMedicine());
        channelCategoryVO.setMedicalDeviceQuaRequirement(channelCategoryDTO.getMedicalDeviceQuaRequirement());
        return channelCategoryVO;
    }

    public static ChannelCategoryVO buildChannelCategoryVO(ChannelCategoryDTO channelCategoryDTO, Boolean channelCategoryCanModify,
                                                           Integer channelId) {
        if (channelCategoryDTO == null) {
            return null;
        }
        ChannelCategoryVO channelCategoryVO = ofDTO(channelCategoryDTO);
        channelCategoryVO.setChannelCategoryCanModify(channelCategoryCanModify);
        channelCategoryVO.setChannelId(channelId);
        return channelCategoryVO;
    }

    public static ChannelCategoryVO buildChannelCategoryVOForBiz(com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelCategoryDTO channelCategoryDTO,
                                                                 Boolean channelCategoryCanModify,
                                                                 Integer channelId) {
        if (channelCategoryDTO == null) {
            return null;
        }
        ChannelCategoryVO channelCategoryVO = ofBizDTO(channelCategoryDTO);
        channelCategoryVO.setChannelCategoryCanModify(channelCategoryCanModify);
        channelCategoryVO.setChannelId(channelId);
        return channelCategoryVO;
    }

    public static ChannelCategoryDTO toDTO(ChannelCategoryVO channelCategoryVO) {
        if (channelCategoryVO == null) {
            return null;
        }
        ChannelCategoryDTO channelCategoryDTO = new ChannelCategoryDTO();
        channelCategoryDTO.setChannelCategoryCode(channelCategoryVO.getChannelCategoryCode());
        channelCategoryDTO.setChannelCategoryName(channelCategoryVO.getChannelCategoryName());
        channelCategoryDTO.setChannelCategoryCodePath(channelCategoryVO.getChannelCategoryCodePath());
        channelCategoryDTO.setChannelCategoryNamePath(channelCategoryVO.getChannelCategoryNamePath());
        return channelCategoryDTO;
    }

}
