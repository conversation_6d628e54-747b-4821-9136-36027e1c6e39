package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.securitydeposit;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: <EMAIL>
 * @Date: 2020/3/2 23:11
 * @Description:
 */
@TypeDoc(description = "保证金支付结果返回对象")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SecurityDepositPaySuccessVO {

    private String tradeNo;


    private String payToken;

}
