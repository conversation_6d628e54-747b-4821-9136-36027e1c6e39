/*
 * Copyright (c) 2019 Meituan.com. All Rights Reserved.
 */
package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.impl;

import com.dianping.cat.util.MetricHelper;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OCMSListWaitAuditOrderRequest;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OCMSQueryOrderQuantityRequest;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OCMSQueryOrderQuantityResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OcmsWaitToRefundGoodsOrderSubTypeCountResponse;
import com.meituan.shangou.saas.order.management.client.enums.QueryOrderTypeQuantityEnum;
import com.meituan.shangou.saas.order.management.client.enums.SortByEnum;
import com.meituan.shangou.saas.order.management.client.service.online.OCMSQueryThriftService;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.AfterSaleApplyStatusEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiEntityTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.AbstractSinglePendingTaskService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.TimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;


@Slf4j
@Service
public class OrderWaitSelfFetchPendingTaskService extends AbstractSinglePendingTaskService {

    @Resource
    private OCMSQueryThriftService ocmsQueryThriftService;

    @Override
    public PendingTaskResult getPendingTaskCount(PendingTaskParam param) throws Exception {
        int count=0;

        OCMSQueryOrderQuantityRequest ocmsQueryOrderQuantityRequest = buildPendingTaskResult(param);
        try {
            log.info("ocmsQueryThriftService.queryOrderQuantity  调用 request:{}", ocmsQueryOrderQuantityRequest);
            OCMSQueryOrderQuantityResponse response = ocmsQueryThriftService.queryOrderQuantityV2(ocmsQueryOrderQuantityRequest);
            log.info("ocmsQueryThriftService.queryOrderQuantity  调用response:{}", response);
            if (ResultCode.SUCCESS.getCode() == response.getStatus().getCode()){
                count= MapUtils.getInteger(response.getOrderTypeQuantityMap(), QueryOrderTypeQuantityEnum.WAIT_SELF_FETCH.getValue(), 0);
            }
            return PendingTaskResult.createNumberMarker(count);
        } catch (Exception e) {
            MetricHelper.build().name("order.buildOCMSListWaitAuditOrderRequest.err").tag("tenantId", String.valueOf(param.getTenantId())).tag("storeId", String.valueOf(firstShopId(param.getStoreIds()))).count();
            log.error("OrderService.queryWaitAuditRefundGoodsBySubtypeCount  调用ocmsQueryThriftService.queryWaitToRefundGoodsBySubtypeCount error", e);
            throw new CommonRuntimeException(e);
        }
    }

    private long firstShopId(List<Long> storeIdList) {
        if (CollectionUtils.isNotEmpty(storeIdList)){
            return storeIdList.get(0);
        }
        return 0;
    }

    private OCMSQueryOrderQuantityRequest buildPendingTaskResult(PendingTaskParam param) {
        //请求订单数据源
        OCMSQueryOrderQuantityRequest request = new OCMSQueryOrderQuantityRequest();
        request.setTenantId(param.getTenantId());
        request.setShopIdList(param.getStoreIds());
        request.setOrderQuantityTypeList(Lists.newArrayList(QueryOrderTypeQuantityEnum.WAIT_SELF_FETCH.getValue()));
        //查询当前时间之前7天的订单
        request.setBeginCreateTime(TimeUtils.getBeforeDayTimeStamp(MccConfigUtil.queryOrderCreateTimeBefore()));
        request.setEndCreateTime(System.currentTimeMillis());
        if(Objects.equals(param.getEntityType(), PoiEntityTypeEnum.SHAREABLE_WAREHOUSE.code())){
            request.setShopIdList(new ArrayList<>());
            request.setWarehouseIdList(param.getStoreIds());
        }
        return request;
    }



    @Override
    protected AuthCodeEnum module() {
        return AuthCodeEnum.WAIT_TO_SELF_FETCH;
    }
}
