package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 竞对门店查询
 * @email <EMAIL>
 * @date 2020-11-30
 */
@ApiModel(
        "竞对门店查询"
)
@TypeDoc(
        description = "竞对门店查询"
)
@Data
public class ContrastStoreQueryRequestVO {

    @FieldDoc(
            description = "门店ID"
    )
    @ApiModelProperty("门店ID")
    @NotNull
    public Long storeId;

    @FieldDoc(
            description = "竞对门店列表"
    )
    @ApiModelProperty("竞对门店列表")
    public List<ContrastStoreKeyVO> contrastStoreList;
}
