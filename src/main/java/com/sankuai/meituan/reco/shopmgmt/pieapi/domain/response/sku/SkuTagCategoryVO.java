package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@TypeDoc(
        description = "商品标签分类信息"
)
@Data
@ApiModel("商品标签分类")
@NoArgsConstructor
public class SkuTagCategoryVO {
    @FieldDoc(
            description = "标签分类ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "标签分类ID", required = true)
    private Long tagCategoryId;

    @FieldDoc(
            description = "标签分类名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "标签分类名称", required = true)
    private String categoryName;

    @FieldDoc(
            description = "标签列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "标签列表", required = true)
    private List<SkuTagSimpleVO> tags;
}
