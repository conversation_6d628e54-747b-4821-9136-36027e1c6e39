package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2019/7/16
 * desc: 检查退款请求
 */
@TypeDoc(
        description = "检查退款请求"
)
@ApiModel("检查退款请求")
@Data
public class CheckRefundRequest {

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID")
    @NotNull
    private Integer channelId;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道订单号")
    @NotNull
    public String channelOrderId;

    @FieldDoc(
            description = "退款类型 1-全单退款 2-部分退款", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "退款类型 1-全单退款 2-部分退款")
    @NotNull
    private Integer refundType;

    @FieldDoc(
            description = "退款操作类型 1-同意 2-驳回", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "退款操作类型 1-同意 2-驳回")
    private Integer refundOperationType;
}
