package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @desc 竞对门店搜索
 * @email <EMAIL>
 * @date 2020-11-30
 */
@ApiModel(
        "竞对门店搜索"
)
@TypeDoc(
        description = "竞对门店搜索"
)
@Data
public class ContrastStoreSearchRequestVO {

    @FieldDoc(
            description = "门店ID"
    )
    @ApiModelProperty("门店ID")
    @NotNull
    public Long storeId;

    @FieldDoc(
            description = "搜索关键词"
    )
    @ApiModelProperty("搜索关键词")
    public String keyword;

    @FieldDoc(
            description = "搜索外卖门店id"
    )
    @ApiModelProperty("搜索外卖门店id")
    public Long wmPoiId;

}
