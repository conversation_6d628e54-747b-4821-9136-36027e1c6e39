package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.purchase;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2024/7/29
 */
@TypeDoc(description = "供应商商品资料")
@Data
public class SupplierGoodsInfoVO {
    @FieldDoc(description = "供应商货品ID", requiredness = Requiredness.OPTIONAL)
    private String goodsId;
    @FieldDoc(description = "名称", requiredness = Requiredness.OPTIONAL)
    private String name;
    @FieldDoc(description = "编码", requiredness = Requiredness.OPTIONAL)
    private String code;
    @FieldDoc(description = "规格", requiredness = Requiredness.OPTIONAL)
    private String spec;
    @FieldDoc(description = "链接", requiredness = Requiredness.OPTIONAL)
    private String url;
    @FieldDoc(description = "备注")
    private String comment;

    /**
     * 判断所有字段是否都为空
     *
     * @return 如果所有字段都为空，则返回 true；否则返回 false
     */
    public boolean isAllFieldsBlank() {
        return StringUtils.isBlank(name)
                && StringUtils.isBlank(code)
                && StringUtils.isBlank(spec)
                && StringUtils.isBlank(url)
                && StringUtils.isBlank(comment);
    }
}
