package com.sankuai.meituan.reco.shopmgmt.pieapi.domain;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price.SkuCanModifyPriceVO;
import com.sankuai.meituan.shangou.empower.price.client.dto.ChannelStoreCustomSkuDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/7/3
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChannelStoreCustomSkuKey {

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID", required = true)
    private Long storeId;

    @FieldDoc(
            description = "渠道id  -1-线下 100-美团 200-饿了么 300-京东到家", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道id", required = true)
    private Integer channelId;
    @FieldDoc(
            description = "商品customSpuId编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品customSpuId编码", required = true)
    private String customSpuId;
    @FieldDoc(
            description = "商品customSkuId编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品customSkuId编码", required = true)
    private String customSkuId;

    public ChannelStoreCustomSkuDTO toChannelStoreCustomSkuDTO() {
        ChannelStoreCustomSkuDTO channelStoreCustomSkuDTO = new ChannelStoreCustomSkuDTO();
        channelStoreCustomSkuDTO.setStoreId(storeId);
        channelStoreCustomSkuDTO.setChannelId(channelId);
        channelStoreCustomSkuDTO.setCustomSpuId(customSpuId);
        channelStoreCustomSkuDTO.setCustomSkuId(customSkuId);
        return channelStoreCustomSkuDTO;
    }

    public static ChannelStoreCustomSkuKey of (SkuCanModifyPriceVO skuCanModifyPriceVO) {
        return ChannelStoreCustomSkuKey.builder()
                .storeId(skuCanModifyPriceVO.getStoreId())
                .channelId(skuCanModifyPriceVO.getChannelId())
                .customSpuId(skuCanModifyPriceVO.getCustomSpuId())
                .customSkuId(skuCanModifyPriceVO.getCustomSkuId())
                .build();
    }
}
