package com.sankuai.meituan.reco.shopmgmt.pieapi.domain;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 作者：guohuqi
 * 时间：2022/11/3 3:22 PM
 * 功能：
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChannelStoreCustomSpuKey {

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID", required = true)
    private Long storeId;

    @FieldDoc(
            description = "渠道id  -1-线下 100-美团 200-饿了么 300-京东到家", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道id", required = true)
    private Integer channelId;
    @FieldDoc(
            description = "商品customSpuId编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品customSpuId编码", required = true)
    private String customSpuId;
}
