package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.realtimesettle;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.order.platform.common.SelfCheckable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@TypeDoc(
        description = "现结交易已确认接口"
)
@Data
@ApiModel("现结交易已确认接口")
public class RealTimeTradeConfirmedRequest implements SelfCheckable {
    @FieldDoc(
            description = "交易单号"
    )
    @ApiModelProperty(value = "交易单号", required = true)
    @NotNull(message = "交易单号不能为空")
    private String tradeNo;

}
