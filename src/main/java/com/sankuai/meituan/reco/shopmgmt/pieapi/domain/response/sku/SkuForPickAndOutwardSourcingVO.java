package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@TypeDoc(
        description = "待外拣货及采现结商品"
)
@Data
@ApiModel("待外拣货及采现结商品")
@NoArgsConstructor
public class SkuForPickAndOutwardSourcingVO {

    @FieldDoc(
            description = "履约工单id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "履约工单id", required = true)
    public Long fulFillWorkOrderId;

    @FieldDoc(
            description = "摊位Id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "摊位Id", required = true)
    public Long boothId;

    @FieldDoc(
            description = "摊位名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "摊位名称", required = true)
    public String boothName;

    @FieldDoc(
            description = "摊位类型  自采、外采", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "摊位类型  自采、外采", required = true)
    public Integer boothType;

    @FieldDoc(
            description = "摊位排序值", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "摊位排序值", required = true)
    public Integer boothSortValue;

    @FieldDoc(
            description = "商品名称(原始)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称(原始)", required = true)
    public String skuName;


    @FieldDoc(
            description = "商品名称(用于展示)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称(用于展示)", required = true)
    public String itemName;

    @FieldDoc(
            description = "商品数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品数量", required = true)
    public Integer itemNum;

    @FieldDoc(
            description = "商品图片", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品图片", required = true)
    public String picUrl;

    @FieldDoc(
            description = "进货价", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "进货单价", required = true)
    public Integer offlinePrice;

    @FieldDoc(
            description = "拣货任务id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "拣货任务id", required = true)
    public Long pickingtaskId;

    @FieldDoc(
            description = "拣货工单id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "拣货工单id", required = true)
    public Long pickingWorkOrderId;

    @FieldDoc(
            description = "规格", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "规格", required = true)
    public String spec;

    @FieldDoc(
            description = "商品属性信息"
    )
    @ApiModelProperty(name = "商品属性信息", required = true)
    public String attribute;

    @FieldDoc(
            description = "闪购赋能skuId", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "闪购赋能skuId", required = true)
    public String skuId;

    @FieldDoc(
            description = "是否赠品", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "是否赠品", required = true)
    public Integer giftType;

    @FieldDoc(
            description = "标签列表(目前包括履约标签/挑选标准)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "标签列表(目前包括履约标签/挑选标准)", required = true)
    public List<TagInfoVO> tagInfoList;
}
