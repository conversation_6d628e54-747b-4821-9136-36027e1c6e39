/*
 * Copyright (c) 2019 Meituan.com. All Rights Reserved.
 */
package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order.revenue;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 摊位历史账单按日查询请求——汇总信息
 * <br><br>
 * <AUTHOR> shihuifeng
 * @date : 2020-02-20
 * @version  v1.0
 */
@TypeDoc(
        description = "摊位历史账单按日查询请求——汇总信息",
        version = "1.0"
)
@ApiModel("摊位历史账单按日查询请求——汇总信息")
@Data
public class BoothHistorySettlementDailySummaryReq {

    @FieldDoc(description = "门店id", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "门店id", required = true)
    @NotNull(message = "门店不合法")
    @Min(value = 1, message = "门店不合法")
    private Long storeId;

    @FieldDoc(description = "统计起始日期：YYYY-MM-dd", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "统计起始日期", required = true)
    @NotBlank(message = "日期不为空")
    private String startDate;

    @FieldDoc(description = "统计结束日期：YYYY-MM-dd", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "统计结束日期", required = true)
    @NotBlank(message = "日期不为空")
    private String endDate;

}
