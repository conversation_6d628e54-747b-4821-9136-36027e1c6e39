package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2023/2/17 09:45
 * @Description:
 */
@TypeDoc(
        name = "渠道渠道品牌信息",
        description = "渠道渠道品牌信息"
)
@Data
public class BrandGroupByChannelVO {

    private Integer channelId;

    private List<BrandVO>  channelBrands;

}
