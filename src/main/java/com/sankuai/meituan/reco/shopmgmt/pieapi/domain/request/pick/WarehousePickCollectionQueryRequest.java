package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.shangou.saas.common.aop.feature.Validatable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;

@TypeDoc(
        description = "波次拣货集单请求"
)
@ApiModel("波次拣货集单请求")
@Data
public class WarehousePickCollectionQueryRequest implements Validatable {

    @FieldDoc(
            description = "页码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "页码")
    @NotNull
    private Long pageNum;

    @FieldDoc(
            description = "个数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "个数")
    @NotNull
    private Integer pageSize;

    @FieldDoc(
            description = "收货方门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "收货方门店id")
    private List<Long> poiList;

    // todo msl 详细说明 done
    @FieldDoc(
            description = "当前操作单据标识，参考OrderTagConstant", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "单据标识")
    private String orderTag;

    @FieldDoc(
            description = "货主IdList", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "货主id")
    private List<String> skuOwnerIdList;

    @FieldDoc(
            description = "网格名称List", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "网格名称")
    private List<String> gridList;

    @FieldDoc(
            description = "推单开始时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "推单开始时间")
    private Long pushOrderStartTime;

    @FieldDoc(
            description = "推单结束时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "推单结束时间")
    private Long pushOrderEndTime;

    @FieldDoc(
            description = "计划生产开始时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "计划生产开始时间")
    private Long planProductStartTime;

    @FieldDoc(
            description = "计划生产结束时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "计划生产结束时间")
    private Long planProductEndTime;

    @FieldDoc(
            description = "计划运抵开始时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "计划运抵开始时间")
    private Long planArriveStartTime;

    @FieldDoc(
            description = "计划运抵结束时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "计划运抵结束时间")
    private Long planArriveEndTime;

    @Override
    public void validate() {
        if (Objects.nonNull(gridList) && gridList.size() > 500) {
            throw new CommonLogicException("网格名称数量不能超过500!", ResultCode.CHECK_PARAM_ERR);
        }
    }
}
