package com.sankuai.meituan.reco.shopmgmt.pieapi.domain;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class StoreServiceRemindVo implements Serializable {

    private static final long serialVersionUID = 1L;
    @FieldDoc(
        description = "是否提醒"
    )
    private boolean remind;

    @FieldDoc(
        description = "门店数"
    )
    private Integer storeAmount;

    @FieldDoc(
        description = "通知类型"
    )
    private String type;

    @FieldDoc(
        description = "提醒天数"
    )
    private Integer remindDays;

    @FieldDoc(
            description = "门店ID集合"
    )
    private List<Long> storeIds = new ArrayList<>();
}
