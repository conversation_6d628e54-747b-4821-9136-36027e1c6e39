package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.product.field;

import java.util.List;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ChannelSaleAttrValueInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.product.IProductContainer;

/**
 * <AUTHOR>
 * @since 2024-12-26
 */
public interface Field_ChannelSaleAttrValueInfoList extends IProductContainer {
    @SuppressWarnings("unchecked")
    default List<ChannelSaleAttrValueInfoVO> getChannelSaleAttrValueInfoList() {
        return (List<ChannelSaleAttrValueInfoVO>) get("channelSaleAttrValueInfoList");
    }

    default void setChannelSaleAttrValueInfoList(List<ChannelSaleAttrValueInfoVO> channelSaleAttrValueInfoList) {
        put("channelSaleAttrValueInfoList", channelSaleAttrValueInfoList);
    }
}
