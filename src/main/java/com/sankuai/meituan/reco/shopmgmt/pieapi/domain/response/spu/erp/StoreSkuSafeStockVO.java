package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp;

import java.util.List;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.SpuStoreConfigDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @created 2024/09/29 19:42
 **/
@TypeDoc(
        description = "门店商品安全库存相关详情VO"
)
@Getter
@Setter
public class StoreSkuSafeStockVO {

    @FieldDoc(
            description = "门店维度安全库存配置值"
    )
    @ApiModelProperty(value = "门店维度安全库存配置值")
    private List<SafeStockConfig> storeSafeStockConfigList;

    @FieldDoc(
            description = "门店商品安全库存值"
    )
    @ApiModelProperty(value = "门店商品安全库存值")
    private Integer onlineSafeStock;

    @FieldDoc(
            description = "门店商品库存共享率"
    )
    @ApiModelProperty(value = "门店商品库存共享率")
    private Integer stockShareRate;



    public void buildAndSetStoreSafeStockConfigList(List<SpuStoreConfigDTO> spuStoreConfigDTOList) {
        List<SafeStockConfig> safeStockConfigList = Fun.map(spuStoreConfigDTOList, configDTO -> {
            SafeStockConfig safeStockConfig = new SafeStockConfig();
            safeStockConfig.setConfigTypeName(configDTO.getConfigTypeName());
            safeStockConfig.setConfigValue(configDTO.getConfigValue());
            safeStockConfig.setComment(configDTO.getComment());
            safeStockConfig.setConfigType(configDTO.getConfigType());
            return safeStockConfig;
        });

        setStoreSafeStockConfigList(safeStockConfigList);
    }


    @ApiModel("安全库存配置查询信息")
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SafeStockConfig {

        @FieldDoc(
                description = "配置类型"
        )
        @ApiModelProperty(value = "配置类型")
        private String configType;

        @FieldDoc(
                description = "配置类型"
        )
        @ApiModelProperty(value = "配置类型名称")
        private String configTypeName;

        @FieldDoc(
                description = "配置值"
        )
        @ApiModelProperty(value = "配置值")
        private String configValue;

        @FieldDoc(
                description = "配置描述"
        )
        @ApiModelProperty(value = "配置描述")
        private String comment;

    }
}
