package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.meituan.shangou.saas.order.management.client.dto.request.online.OrderDeliveryExceptionListRequest;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OrderListPageResponse;
import com.meituan.shangou.saas.order.management.client.service.online.OnlineDeliveryExceptionQueryThriftService;
import com.meituan.shangou.saas.service.ocms.OCMSOrderThriftService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.DeliveryExceptionOperateType;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.converters.deliveryexception.DeliveryExceptionConverter;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order.deliveryexception.DeliveryExceptionOpenListRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.deliveryexception.DeliveryExceptionOrderListVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.OrderKey;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.OrderOperateCheckRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.OrderOperateCheckResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.OrderCouldOperateItem;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.ChannelOrderOperateThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @Date: 2020-02-18 16:06
 * @Description:
 */
@Slf4j
@Service
public class DeliveryExceptionWrapper {

    @Resource
    OnlineDeliveryExceptionQueryThriftService onlineDeliveryExceptionQueryThriftService;

    @Resource
    OCMSOrderThriftService ocmsOrderThriftService;

    @Resource
    ChannelOrderOperateThriftService.Iface channelOrderOperateThriftService;




    public DeliveryExceptionOrderListVo queryOpenOrderList(DeliveryExceptionOpenListRequest request) {
        DeliveryExceptionOrderListVo vo;
        try {
            //查询配送异常待处理订单
            OrderDeliveryExceptionListRequest query = DeliveryExceptionConverter.convert2Request(request);
            log.info("queryOpenOrderPageList request:{}", request);
            OrderListPageResponse response = onlineDeliveryExceptionQueryThriftService.queryOpenOrderPageList(query);
            log.info("queryOpenOrderPageList response:{}", response);
            if (response.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
                throw new CommonRuntimeException(response.getStatus().getMessage(), ResultCode.FAIL);
            }
            vo = DeliveryExceptionConverter.convert2ListVo(response, true);
            //查询订单是否可以取消
            orderCancelCheck(vo);
        } catch (TException e) {
            log.error("queryOpenOrderList exception!", e);
            throw new CommonRuntimeException(e, ResultCode.INTERNAL_SERVER_ERROR);
        }
        return vo;
    }


    private void orderCancelCheck(DeliveryExceptionOrderListVo vo) {
        if (CollectionUtils.isEmpty(vo.getOrderList())) {
            return;
        }
        //查询订单是否可以取消
        try {
            OrderOperateCheckRequest checkRequest = DeliveryExceptionConverter.convert2CheckRequest(vo.getOrderList());
            OrderOperateCheckResponse checkResponse = channelOrderOperateThriftService.checkOrderCouldOperateItems(checkRequest);
            log.info("checkOrderCouldOperateItems checkResponse{}", checkResponse);
            //异常降级
            if (checkResponse.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
                return;
            }
            buildOrderOperateList(vo.getOrderList(), checkResponse.getCouldOperateItems());
        } catch (Exception e) {
            log.error("checkOrderCouldOperateItems exception.", e);
        }
    }

    private void buildOrderOperateList(List<DeliveryExceptionOrderListVo.DeliveryExceptionOrderVo> orderList, Map<OrderKey, List<Integer>> orderOperateList) {
        if (CollectionUtils.isEmpty(orderList) || MapUtils.isEmpty(orderOperateList)) {
            return;
        }
        orderList.forEach(e -> {
            OrderKey orderKey = new OrderKey();
            orderKey.setChannelType(e.getChannelId());
            orderKey.setChannelOrderId(e.getChannelOrderId());
            orderKey.setTenantId(e.getTenantId());
            List<Integer> optList = orderOperateList.get(orderKey);
            if (CollectionUtils.isEmpty(optList) || !optList.contains(OrderCouldOperateItem.FULL_ORDER_REFUND.getValue())) {
                //移除已取消
                e.getOperationList().remove(DeliveryExceptionOperateType.CANCEL.type);
            }
        });
    }
}
