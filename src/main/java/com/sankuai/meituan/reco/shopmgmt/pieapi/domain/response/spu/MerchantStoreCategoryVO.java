package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.TenantStoreCategoryDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.TenantStoreCategoryBizDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class MerchantStoreCategoryVO {

    private String categoryId;

    private String name;

    public static MerchantStoreCategoryVO of(TenantStoreCategoryDTO tenantStoreCategoryDTO) {
        MerchantStoreCategoryVO merchantStoreCategoryVO = new MerchantStoreCategoryVO();
        merchantStoreCategoryVO.setCategoryId(String.valueOf(tenantStoreCategoryDTO.getCategoryId()));
        merchantStoreCategoryVO.setName(tenantStoreCategoryDTO.getCategoryName());
        return merchantStoreCategoryVO;
    }

    public static MerchantStoreCategoryVO ofBiz(TenantStoreCategoryBizDTO tenantStoreCategoryDTO) {
        MerchantStoreCategoryVO merchantStoreCategoryVO = new MerchantStoreCategoryVO();
        merchantStoreCategoryVO.setCategoryId(String.valueOf(tenantStoreCategoryDTO.getCategoryId()));
        merchantStoreCategoryVO.setName(tenantStoreCategoryDTO.getCategoryName());
        return merchantStoreCategoryVO;
    }

}
