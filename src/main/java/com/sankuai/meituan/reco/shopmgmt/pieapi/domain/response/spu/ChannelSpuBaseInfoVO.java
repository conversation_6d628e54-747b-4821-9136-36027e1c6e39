package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "渠道商品SPU基础信息"
)
@Data
@ApiModel("渠道商品SPU信息")
public class ChannelSpuBaseInfoVO {
    private Long tenantId;

    private Long storeId;

    @FieldDoc(
            description = "渠道id  -1-线下 100-美团 200-饿了么 300-京东到家", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道id", required = true)
    private Integer channelId;

    @FieldDoc(
            description = "SPU编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "SPU编码", required = true)
    private String spuId;

    @FieldDoc(
            description = "图片", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "图片", required = true)
    private List<String> images;

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称", required = true)
    private String name;

    @FieldDoc(
            description = "月销量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "月销量", required = true)
    private Integer monthSaleAmount;

    @FieldDoc(
            description = "价格", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "价格", required = true)
    private Long price;

    @FieldDoc(
            description = "价格格式化信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "价格格式化信息", required = true)
    private String formatPrice;
}
