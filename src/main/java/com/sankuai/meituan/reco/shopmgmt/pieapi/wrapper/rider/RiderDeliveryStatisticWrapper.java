package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.rider;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.statistic.RiderDeliveryStatisticResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.statistic.RiderDeliveryStatisticVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.RiderDeliveryStatisticThriftService;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDurationDeliveryStatistic;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.DurationsStatisticsRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.DurationsStatisticsResponse;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/8/24 21:49
 **/

@Slf4j
@Rhino
@Service
public class RiderDeliveryStatisticWrapper {
    @Resource
    private RiderDeliveryStatisticThriftService riderDeliveryStatisticThriftService;

    private final String yyyy_MM_dd = "yyyy-MM-dd";
    private final String yyyyMMdd= "yyyyMMdd";

    /**
     * 查询多天的骑手配送数据
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return
     */
    @Degrade(rhinoKey = "RiderDeliveryStatisticWrapper.queryDaysDeliveryStatistics",
            fallBackMethod = "queryDaysDeliveryStatisticsFallback",
            timeoutInMilliseconds = 2000)
    @CatTransaction
    public RiderDeliveryStatisticResponse queryDaysDeliveryStatistics(String beginDate, String endDate) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        long tenantId = identityInfo.getUser().getTenantId();
        long accountId = identityInfo.getUser().getAccountId();


        List<DurationsStatisticsRequest.TDuration> durations =
                Collections.singletonList(new DurationsStatisticsRequest.TDuration(convertDateFormat(beginDate, yyyy_MM_dd, yyyyMMdd),
                        convertDateFormat(endDate, yyyy_MM_dd, yyyyMMdd)));
        List<TRiderDurationDeliveryStatistic> statisticList = queryDeliveryStatisticByDurations(tenantId, accountId, durations);

        return buildRiderDeliveryStatisticResponse(tenantId, accountId, statisticList);
    }

    /**
     * 查询当前三个月内的骑手配送数据
     *
     * @return
     */
    @Degrade(rhinoKey = "RiderDeliveryStatisticWrapper.queryMonthsDeliveryStatistics",
            fallBackMethod = "queryMonthsDeliveryStatisticsFallback",
            timeoutInMilliseconds = 2000)
    @CatTransaction
    public RiderDeliveryStatisticResponse queryMonthsDeliveryStatistics() {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        long tenantId = identityInfo.getUser().getTenantId();
        long accountId = identityInfo.getUser().getAccountId();
        LocalDate yesterday = LocalDate.now().minusDays(1);
        List<DurationsStatisticsRequest.TDuration> durations;

        durations = Arrays.asList(getMonthDuration(yesterday, 0),
                getMonthDuration(yesterday, -1), getMonthDuration(yesterday, -2));

        List<TRiderDurationDeliveryStatistic> statisticList = queryDeliveryStatisticByDurations(tenantId, accountId, durations);

        return buildRiderDeliveryStatisticResponse(tenantId, accountId, statisticList);
    }

    @Degrade(rhinoKey = "RiderDeliveryStatisticWrapper.queryDeliveryStatisticByDurations",
            fallBackMethod = "queryDeliveryStatisticByDurationsFallback",
            timeoutInMilliseconds = 2000)
    @CatTransaction
    public List<TRiderDurationDeliveryStatistic> queryDeliveryStatisticByDurations(Long tenantId, Long riderAccountId,
                                                                                   List<DurationsStatisticsRequest.TDuration> durations) {
        DurationsStatisticsRequest request = new DurationsStatisticsRequest();
        request.setDurationList(durations);
        request.setRiderAccountId(riderAccountId);
        request.setTenantId(tenantId);

        DurationsStatisticsResponse response;
        try {
            response = riderDeliveryStatisticThriftService.queryDurationsStatistics(request);
            log.info("riderDeliveryStatisticThriftService.queryDurationsStatistics end,req:{}, resp:{}", request, response);
        } catch (IllegalArgumentException e) {
            log.error("riderDeliveryStatisticThriftService.queryDurationsStatistics param error, req:{}", request, e);
            throw new CommonRuntimeException(e.getMessage());
        } catch (Exception e) {
            log.error("riderDeliveryStatisticThriftService.queryDurationsStatistics error, req:{}", request, e);
            throw new CommonRuntimeException("查询骑手配送统计数据失败");
        }

        if (response == null || response.getStatus() == null ) {
            log.error("riderDeliveryStatisticThriftService.queryDurationsStatistics error, req:{}", request);
            throw new CommonRuntimeException("查询骑手配送统计数据失败");
        }

        if (response.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
            log.error("riderDeliveryStatisticThriftService.queryDurationsStatistics fail, req:{},resp:{}", request, response);
            throw new CommonRuntimeException("查询骑手配送统计数据失败");
        }

        return Optional.ofNullable(response.getDurationDeliveryStatistics()).orElse(Collections.emptyList());
    }

    private List<TRiderDurationDeliveryStatistic> queryDaysDeliveryStatisticsFallback(String beginDate, String endDate) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        long tenantId = identityInfo.getUser().getTenantId();
        long accountId = identityInfo.getUser().getAccountId();
        log.error("RiderDeliveryStatisticWrapper.queryDaysDeliveryStatisticsFallback 调用降级方法," +
                "tenantId:{}, riderAccountId:{}, beginDate:{}, endDate{}", tenantId, accountId, beginDate, endDate);
        return Collections.emptyList();
    }

    private List<TRiderDurationDeliveryStatistic> queryMonthsDeliveryStatisticsFallback() {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        long tenantId = identityInfo.getUser().getTenantId();
        long accountId = identityInfo.getUser().getAccountId();
        log.error("RiderDeliveryStatisticWrapper.queryMonthsDeliveryStatisticsFallback 调用降级方法," +
                "tenantId:{}, riderAccountId:{}", tenantId, accountId);
        return Collections.emptyList();
    }

    private List<TRiderDurationDeliveryStatistic> queryDeliveryStatisticByDurationsFallback(Long tenantId, Long riderAccountId,
                                              List<DurationsStatisticsRequest.TDuration> durations) {
        log.error("RiderDeliveryStatisticWrapper.queryDeliveryStatisticByDurationsFallback 调用降级方法," +
                "tenantId:{}, riderAccountId:{}, durations{}", tenantId, riderAccountId, durations);
        return Collections.emptyList();
    }

    private RiderDeliveryStatisticResponse buildRiderDeliveryStatisticResponse(Long tenantId, Long riderAccountId,
                                                                               List<TRiderDurationDeliveryStatistic> statisticList) {
        RiderDeliveryStatisticResponse response = new RiderDeliveryStatisticResponse();
        response.setTenantId(tenantId);
        response.setRiderAccountId(riderAccountId);
        response.setDeliveryStatisticVOS(statisticList.stream().map(this::transform2RiderDeliveryStatisticVO).collect(Collectors.toList()));

        return response;
    }

    private RiderDeliveryStatisticVO transform2RiderDeliveryStatisticVO(TRiderDurationDeliveryStatistic tRiderStatistic) {
        Objects.requireNonNull(tRiderStatistic);

        RiderDeliveryStatisticVO riderDeliveryStatisticVO = new RiderDeliveryStatisticVO();
        riderDeliveryStatisticVO.setAvgFulfillDuration(tRiderStatistic.getAvgFulfillDuration());
        riderDeliveryStatisticVO.setBeginTime(convertDateFormat(tRiderStatistic.getBeginDate(), yyyyMMdd, yyyy_MM_dd));
        riderDeliveryStatisticVO.setEndTime(convertDateFormat(tRiderStatistic.getEndDate(), yyyyMMdd, yyyy_MM_dd));
        riderDeliveryStatisticVO.setCancelBeforeDeliveredCount(tRiderStatistic.getCancelBeforeDeliveredCount());
        riderDeliveryStatisticVO.setCompleteCount(tRiderStatistic.getDeliveredCount());
        riderDeliveryStatisticVO.setTimeoutCount(tRiderStatistic.getTimeoutCount());
        riderDeliveryStatisticVO.setTimeoutRate(tRiderStatistic.getTimeoutRate());
        riderDeliveryStatisticVO.setEarlyClickDeliveredCount(tRiderStatistic.getEarlyClickDeliveryCount());
        riderDeliveryStatisticVO.setDeliveredRateIn25min(tRiderStatistic.getDeliveredRateIn25min());
        riderDeliveryStatisticVO.setRiskControlCount(tRiderStatistic.getRiskControlCount());
        riderDeliveryStatisticVO.setEtaOvertimeOrdNumV2(tRiderStatistic.getEtaOvertimeOrdNumV2());
        riderDeliveryStatisticVO.setEtaOvertimeRatioV2(tRiderStatistic.getEtaOvertimeRatioV2());

        return riderDeliveryStatisticVO;
    }

    /**
     * 获取指定日期之前或之后的月份日期范围
     *
     * @param monthOffset 偏移量 例如：-1 表示获取上个月的日期范围
     * @return 开始日期和结束日期 格式"yyyyMMdd"
     */
    private DurationsStatisticsRequest.TDuration getMonthDuration(LocalDate originalDate, int monthOffset) {
        DurationsStatisticsRequest.TDuration monthDuration = new DurationsStatisticsRequest.TDuration();
        DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern("yyyyMMdd");

        LocalDate offsetMonth = originalDate.plusMonths(monthOffset);
        LocalDate offsetMonthFirstDay = offsetMonth.withDayOfMonth(1);
        monthDuration.beginDate = offsetMonthFirstDay.format(dateFormat);

        //本月第一天 先加一个月再减一天 ==> 得到上个月最后一天的日期
        monthDuration.endDate = offsetMonthFirstDay.plusMonths(1).minusDays(1).format(dateFormat);
        return monthDuration;
    }

    private String convertDateFormat(String original, String originalFormat, String targetFormat) {
        LocalDate localDate;
        try {
            localDate = LocalDate.parse(original, DateTimeFormatter.ofPattern(originalFormat));
        } catch (Exception e) {
            log.error("日期格式转换失败, input:{}", original);
            throw new CommonRuntimeException("日期格式转换失败");
        }
        return localDate.format(DateTimeFormatter.ofPattern(targetFormat));
    }
}
