package com.sankuai.meituan.reco.shopmgmt.pieapi.constants;

import lombok.Getter;

/**
 * @Title: ErrorCodeEnum
 * @Description:
 * <AUTHOR>
 * @Date 2020/3/31 15:33
 */
public enum ErrorCodeEnum {
    SUCCESS(0, "成功"),

    QUOTE_REVIEWING(1001, "报价审核中，审核通过后请及时上架"),
    QUOTE_FAILED(1003, "报价失败，请手动提交报价"),

    ORDER_CANT_BE_CONTACT(1001002, "已超过联系期限，如有需要请联系总部"),

    SPU_PUSH_CHANNEL_FAILED(11004, "商品推送渠道失败"),
    ;
    @Getter
    private final int code;

    @Getter
    private final String message;

    ErrorCodeEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }
}
