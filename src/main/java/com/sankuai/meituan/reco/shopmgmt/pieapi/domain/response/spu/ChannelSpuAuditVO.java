package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelSpuAuditDTO;
import lombok.Builder;
import lombok.Data;

/**
 * 渠道Spu审核信息
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022/11/23.
 */
@Data
@Builder
public class ChannelSpuAuditVO {
    @FieldDoc(
        description = "渠道ID, 100:美团, 200:饿了么, 300:京东到家",
        example = "100"
    )
    public Integer channelId;
    @FieldDoc(
        description = "审核状态, 0:未送审, 1:先审后发审核中, 2:审核通过, 3:审核驳回, 4:纠错驳回, 5:审核撤销, 6:先发后审审核中, 7宽限期驳回, 60:限期驳回逾期",
        example = "3"
    )
    public Integer auditStatus;
    @FieldDoc(
        description = "审核不通过原因",
        example = "1.商品、主图和标题非正常商品，请您上传需要上架售卖某商品"
    )
    public String auditComment;
    @FieldDoc(
        description = "合规审核状态, 0:不送审, 1:先审后发审核中, 2:审核通过, 3:审核驳回, 4:先发后审审核中, 5:审核驳回删除",
        example = "2"
    )
    public Integer normAuditStatus;
    @FieldDoc(
        description = "合格审核不通过原因",
        example = ""
    )
    public String normAuditComment;

    public static ChannelSpuAuditVO of(ChannelSpuAuditDTO channelSpuAuditDTO){
        return ChannelSpuAuditVO.builder()
            .channelId(channelSpuAuditDTO.getChannelId())
            .auditStatus(channelSpuAuditDTO.getAuditStatus())
            .auditComment(channelSpuAuditDTO.getAuditComment())
            .normAuditStatus(channelSpuAuditDTO.getNormAuditStatus())
            .normAuditComment(channelSpuAuditDTO.getNormAuditComment())
            .build();
    }
}
