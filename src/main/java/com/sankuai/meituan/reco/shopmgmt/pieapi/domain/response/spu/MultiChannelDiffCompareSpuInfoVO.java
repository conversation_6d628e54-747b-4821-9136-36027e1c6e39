package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.diffcompare.DiffCompareSpuInfoDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.enums.DiffCompareSpuOperateTypeEnum;
import lombok.Builder;
import lombok.Data;

import java.util.List;


@TypeDoc(description = "不一致商品")
@Data
@Builder
public class MultiChannelDiffCompareSpuInfoVO {
    @FieldDoc(
            description = "不一致类型"
    )
    private Integer diffCompareType;

    @FieldDoc(
            description = "门店ID"
    )
    private Long storeId;

    @FieldDoc(
            description = "需要修复的渠道信息"
    )
    private List<Integer> needRepairChannelIds;

    @FieldDoc(
            description = "商品ID"
    )
    private String spuId;

    @FieldDoc(
            description = "商品ID"
    )
    private String customSpuId;

    @FieldDoc(
            description = "商品状态 -1-未上线 1-已上架 2-已下架"
    )
    private Integer onSaleStatus;

    @FieldDoc(
            description = "商品名称"
    )
    private String spuName;

    @FieldDoc(
            description = "门店分类"
    )
    private String storeCategoryName;

    @FieldDoc(
            description = "商品图片URL"
    )
    private String imageUrl;

    @FieldDoc(
            description = "比对商品字段信息，目前比对的这段包括：1-上下架；2-商品名称；3-商品描述；4-店内分类；5-商品属性；6-力荐标识；7-规格；8-可售时间；"
                    +"9-规格售价；12-upc；15-渠道分类；16-商品卖点；17-规格描述"
    )
    private List<MultiChannelDiffCompareInfoVO> diffCompareInfoList;

    @FieldDoc(
            description = "可操作列表，详见：DiffCompareSpuOperateTypeEnum"
    )
    private List<Integer> optList;

    @FieldDoc(
        description = "渠道商品审核信息",
        example = ""
    )
    private List<ChannelSpuAuditVO> channelSpuAuditVOList;

    @FieldDoc(
            description = "是否可以进行修复",
            example = ""
    )
    private Integer canFix;

    @FieldDoc(
            description = "商品的标签，展示商品的标签，目前仅支持“美补活动品”的标签"
    )
    private List<SpuTagVO> tagList;

    public static MultiChannelDiffCompareSpuInfoVO of(DiffCompareSpuInfoDTO spuInfoDTO) {
        return MultiChannelDiffCompareSpuInfoVO.builder()
                .diffCompareType(spuInfoDTO.getSpuDiffCompareType().getType())
                .storeId(spuInfoDTO.getStoreId())
                .needRepairChannelIds(spuInfoDTO.getNeedRepairChannelIds())
                .customSpuId(spuInfoDTO.getCustomSpuId())
                .spuId(spuInfoDTO.getSpuId())
                .spuName(spuInfoDTO.getSpuName())
                .onSaleStatus(spuInfoDTO.getOnSaleStatus())
                .storeCategoryName(spuInfoDTO.getStoreCategoryName())
                .imageUrl(spuInfoDTO.getImgUrl())
                .optList(ConverterUtils.convertList(spuInfoDTO.getOptList(), DiffCompareSpuOperateTypeEnum::getCode))
                .diffCompareInfoList(ConverterUtils.convertList(spuInfoDTO.getDiffCompareInfoList(), MultiChannelDiffCompareInfoVO::of))
                .channelSpuAuditVOList(ConverterUtils.convertList(spuInfoDTO.getChannelSpuAuditDTOList(), ChannelSpuAuditVO::of))
                .canFix(spuInfoDTO.getCanFix())
                .tagList(SpuTagVO.ofDTOListForBiz(spuInfoDTO.getTagList()))
                .build();
    }
}
