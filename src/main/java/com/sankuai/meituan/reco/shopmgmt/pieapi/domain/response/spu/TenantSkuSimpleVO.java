package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.CombineChildSkuVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.SaleAttrValueVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.product.container.SkuContainer;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.SaleAttributeValueDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.TenantSpuAddSpecDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Title: TenantSkuSimpleVO
 * @Description: 总部商品SKU信息
 * @Author: zhaolei12
 * @Date: 2020-05-16 17:34
 */
@EqualsAndHashCode(callSuper = true)
@TypeDoc(
        description = "总部商品SKU信息"
)
@Data
@ApiModel("总部商品SKU信息")
public class TenantSkuSimpleVO extends SkuContainer {

    @FieldDoc(description = "SKU编码")
    protected String skuId;

    @FieldDoc(description = "规格")
    protected String spec;

    @FieldDoc(description = "重量，映射渠道字段")
    protected Integer weight;

    @FieldDoc(description = "带单位的重量")
    protected String weightForUnit;

    @FieldDoc(description = "重量单位")
    protected String weightUnit;

    @FieldDoc(description = "售卖单位")
    @NonNull
    protected String saleUnit;

    @FieldDoc(description = "upc")
    protected String upc;


    @FieldDoc(description = "upc列表")
    protected List<String> upcList;

    @FieldDoc(description = "最小重量，内部使用")
    protected String minWeight;

    @FieldDoc(description = "建议零售价")
    protected Double suggestPrice;

    @FieldDoc(description = "京东销售属性")
    protected List<SaleAttrValueVo> jdAttrValues;

    @FieldDoc(description = "外部编码")
    private String externalCode;

    @FieldDoc(description = "加盟商总部编码")
    private String franchiseeHeadquartersCode;

    @FieldDoc(description = "采购平台编码")
    private String purchasePlatformCode;

    @FieldDoc(
            description = "箱规单位转换系数"
    )
    @ApiModelProperty(value = "箱规单位转换系数")
    private List<CartonMeasureConvertFactorVO> cartonMeasureConvertFactorList;

    @FieldDoc(
            description = "是否自定义无upc编码  0-否，1-是"
    )
    @ApiModelProperty(value = "是否自定义无upc编码  0-否，1-是")
    private Integer customizeNoUpcCode;

    @FieldDoc(
            description = "是否使用SKU填充UPC 0-否，1-是"
    )
    @ApiModelProperty(value = "是否使用SKU填充UPC  0-否，1-是")
    private Integer useSkuAsUpc;

    @FieldDoc(
            description = "商品类型，1-单品 2-组合品"
    )
    @ApiModelProperty(value = "商品类型，1-单品 2-组合品")
    private Integer skuSaleType;

    @FieldDoc(
            description = "子商品列表"
    )
    @ApiModelProperty(value = "子商品列表")
    private List<CombineChildSkuVo> childSkuList;

    public static List<TenantSpuAddSpecDTO> toDTOList(List<TenantSkuSimpleVO> tenantSkuSimpleVOList) {
        if (CollectionUtils.isEmpty(tenantSkuSimpleVOList)) {
            return Lists.newArrayList();
        }

        return tenantSkuSimpleVOList.stream().filter(Objects::nonNull).map(TenantSkuSimpleVO::toDTO).collect(Collectors.toList())
                .stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static TenantSpuAddSpecDTO toDTO(TenantSkuSimpleVO tenantSkuSimpleVO) {
        if (tenantSkuSimpleVO == null) {
            return null;
        }

        TenantSpuAddSpecDTO spuAddSpecDTO = new TenantSpuAddSpecDTO();
        spuAddSpecDTO.setSpec(tenantSkuSimpleVO.getSpec());
        spuAddSpecDTO.setNetWeight(tenantSkuSimpleVO.getWeight());
        spuAddSpecDTO.setWeightForUnit(tenantSkuSimpleVO.getWeightForUnit());
        spuAddSpecDTO.setWeightUnit(tenantSkuSimpleVO.getWeightUnit());
        spuAddSpecDTO.setSaleUnit(tenantSkuSimpleVO.getSaleUnit());

        if (CollectionUtils.isNotEmpty(tenantSkuSimpleVO.getUpcList())) {
            spuAddSpecDTO.setUpcList(tenantSkuSimpleVO.getUpcList());
        } else if (StringUtils.isNotBlank(tenantSkuSimpleVO.getUpc())) {
            spuAddSpecDTO.setUpcList(Lists.newArrayList(tenantSkuSimpleVO.getUpc()));
        } else {
            spuAddSpecDTO.setUpcList(Lists.newArrayList());
        }

        if (StringUtils.isNotBlank(tenantSkuSimpleVO.getMinWeight())) {
            spuAddSpecDTO.setMinWeight(Integer.parseInt(tenantSkuSimpleVO.getMinWeight()));
        }

        if (CollectionUtils.isNotEmpty(tenantSkuSimpleVO.getJdAttrValues())) {
            spuAddSpecDTO.setSaleAttributeValueDTOS(tenantSkuSimpleVO.toSaleAttributeValueDTOS());
        }
        spuAddSpecDTO.setChannelSaleAttributeValues(Fun.map(tenantSkuSimpleVO.getChannelSaleAttrValueInfoList(),
                channelSaleAttrValueInfoVO -> channelSaleAttrValueInfoVO.toOcmsDTO(tenantSkuSimpleVO.getImageInfo())));
        if (CollectionUtils.isNotEmpty(tenantSkuSimpleVO.getCartonMeasureConvertFactorList())) {
            spuAddSpecDTO.setCartonMeasureList(Fun.map(tenantSkuSimpleVO.getCartonMeasureConvertFactorList(), CartonMeasureConvertFactorVO::toCartonMeasureDto));
        }

        spuAddSpecDTO.setSuggestPrice(tenantSkuSimpleVO.getSuggestPrice());
        spuAddSpecDTO.setExternalCode(tenantSkuSimpleVO.getExternalCode());
        spuAddSpecDTO.setFranchiseeHeadquartersCode(tenantSkuSimpleVO.getFranchiseeHeadquartersCode());
        spuAddSpecDTO.setPurchasePlatformCode(tenantSkuSimpleVO.getPurchasePlatformCode());
        spuAddSpecDTO.setCustomizeNoUpcCode(tenantSkuSimpleVO.getCustomizeNoUpcCode());
        spuAddSpecDTO.setUseSkuAsUpc(tenantSkuSimpleVO.getUseSkuAsUpc());
        spuAddSpecDTO.setSkuSaleType(tenantSkuSimpleVO.getSkuSaleType());
        spuAddSpecDTO.setChildSkuDtoList(Fun.map(tenantSkuSimpleVO.getChildSkuList(), CombineChildSkuVo::convertDto));
        return spuAddSpecDTO;
    }


    public List<SaleAttributeValueDTO> toSaleAttributeValueDTOS() {
        List<SaleAttributeValueDTO> saleAttributeValueDTOS = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(jdAttrValues)) {
            jdAttrValues.forEach(attr -> {
                SaleAttributeValueDTO saleAttributeValueDTO = new SaleAttributeValueDTO();
                saleAttributeValueDTO.setAttrValue(attr.getAttrValue());
                saleAttributeValueDTO.setAttrName(attr.getAttrName());
                saleAttributeValueDTOS.add(saleAttributeValueDTO);
            });
        }
        return saleAttributeValueDTOS;
    }
}
