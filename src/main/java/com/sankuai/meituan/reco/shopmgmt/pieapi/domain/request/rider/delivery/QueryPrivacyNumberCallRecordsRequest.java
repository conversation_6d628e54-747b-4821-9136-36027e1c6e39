package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.delivery;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/12/22 16:08
 **/

@Data
public class QueryPrivacyNumberCallRecordsRequest {
    /**
     * 渠道订单id
     */
    private String channelOrderId;

    /**
     * 渠道id
     */
    private Integer channelId;

    public String validate() {
        if (StringUtils.isBlank(channelOrderId)) {
            return "渠道订单id不能为空";
        }

        if (channelId == null) {
            return "渠道id不能为空";
        }

        return null;
    }
}
