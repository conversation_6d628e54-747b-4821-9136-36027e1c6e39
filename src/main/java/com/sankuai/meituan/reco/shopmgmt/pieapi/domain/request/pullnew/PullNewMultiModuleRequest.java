package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pullnew;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 地推多模块信息查询请求
 *
 * <AUTHOR>
 * @since 5/8/23
 */
@TypeDoc(
        description = "地推多模块信息查询请求",
        version = "V1.0"
)
@Data
@ApiModel("地推多模块信息查询请求")
public class PullNewMultiModuleRequest {

    @FieldDoc(
            description = "门店ID"
    )
    @ApiModelProperty(value = "门店ID")
    private Long storeId;

    @FieldDoc(
            description = "开始时间"
    )
    @ApiModelProperty(value = "开始时间")
    private Long startTime;

    @FieldDoc(
            description = "截止时间"
    )
    @ApiModelProperty(value = "截止时间")
    private Long endTime;

}
