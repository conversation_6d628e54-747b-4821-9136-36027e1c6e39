package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.PickPackSeedWaitDetailVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.SeedProgressVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.WarehouseContainerBoxModuleVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.WarehouseReceiveStoreInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@TypeDoc(
        description = "查询待分拣详情返回"
)
@Data
@ApiModel("查询待分拣详情返回")
public class WarehousePickPackSeedWaitDetailResponse {

    @FieldDoc(
            description = "装箱进度", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "装箱进度")
    private SeedProgressVO seedProgress;

    @FieldDoc(
            description = "分拣商品列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "分拣商品列表")
    private List<PickPackSeedWaitDetailVO> dataList;

    @FieldDoc(
            description = "是否还有数据", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否还有数据")
    private Boolean hasMore;
}
