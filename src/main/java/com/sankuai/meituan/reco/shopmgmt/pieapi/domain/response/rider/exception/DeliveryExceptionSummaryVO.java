package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.exception;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@TypeDoc(
        description = "配送异常概要VO"
)
@ApiModel("配送异常概要VO")
@NoArgsConstructor
@AllArgsConstructor
@Data
public class DeliveryExceptionSummaryVO {

    @FieldDoc(
            description = "运单id"
    )
    @ApiModelProperty(value = "运单id", required = true)
    private String deliveryOrderId;

    @FieldDoc(
            description = "渠道id"
    )
    @ApiModelProperty(value = "渠道id", required = true)
    private Integer channelId;

    @FieldDoc(
            description = "渠道订单id"
    )
    @ApiModelProperty(value = "渠道订单id", required = true)
    private String channelOrderId;

    @FieldDoc(
            description = "渠道日流水号"
    )
    @ApiModelProperty(value = "渠道日流水号", required = true)
    private Integer daySeq;

    @FieldDoc(
            description = "门店id"
    )
    @ApiModelProperty(value = "门店id", required = true)
    private Long storeId;

    @FieldDoc(
            description = "订单支付时间"
    )
    @ApiModelProperty(value = "订单支付时间", required = true)
    private Long payTime;

    @FieldDoc(
            description = "配送异常明细列表"
    )
    @ApiModelProperty(value = "配送异常列表", required = true)
    private List<DeliveryExceptionInfoVO> deliveryExceptionInfoVOS;
}
