package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.product.field;

import java.util.List;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.product.IProductContainer;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelSaleAttrInfoVO;

/**
 * <AUTHOR>
 * @since 2024-12-26
 */
public interface Field_ChannelSaleAttrInfoList extends IProductContainer {

    @SuppressWarnings("unchecked")
    default List<ChannelSaleAttrInfoVO> getChannelSaleAttrInfoList() {
        return (List<ChannelSaleAttrInfoVO>) get("channelSaleAttrInfoList");
    }

    default void setChannelSaleAttrInfoList(List<ChannelSaleAttrInfoVO> channelSaleAttrInfoList) {
        put("channelSaleAttrInfoList", channelSaleAttrInfoList);
    }
}
