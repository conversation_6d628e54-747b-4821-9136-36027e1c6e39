package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "门店商品信息"
)
@Data
@ApiModel("门店商品信息")
public class StoreSkuVO {

    @FieldDoc(
            description = "SKU编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "SKU编码", required = true)
    private String skuId;

    @FieldDoc(
            description = "UPC编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "UPC编码", required = true)
    private List<String> upcInfo;

    @FieldDoc(
            description = "规格", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "规格", required = true)
    private String spec;

    @FieldDoc(
            description = "重量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "重量", required = true)
    private Integer weight;

    @FieldDoc(
            description = "门店售价", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店售价", required = true)
    private Double storePrice;

    @FieldDoc(
            description = "基本单位", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "基本单位", required = true)
    private String basicUnit;

    @FieldDoc(
            description = "自定义库存标记", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "自定义库存标记", required = true)
    private Integer customizeStockFlag;

    @FieldDoc(
            description = "自定义库存数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "自定义库存数量", required = true)
    private Integer customizeStockQuantity;

    @FieldDoc(
            description = "第二天是否自动恢复无限库存，0-不自动恢复 1-自动恢复", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "第二天是否自动恢复无限库存，0-不自动恢复 1-自动恢复", required = true)
    private Integer autoResumeInfiniteStock;

    @FieldDoc(
            description = "月销量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "月销量", required = true)
    private Integer monthSaleAmount;

    @FieldDoc(
            description = "报价审核状态 1-待审核", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "报价审核状态 1-待审核", required = true)
    private Integer reviewStatus;
}
