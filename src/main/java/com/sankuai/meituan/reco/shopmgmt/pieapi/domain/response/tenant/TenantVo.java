package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.tenant;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/03/14
 */
@Data
public class TenantVo {

    @FieldDoc(
            description = "租户id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "租户id", required = true)
    private Integer tenantId;

    @FieldDoc(
            description = "租户名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "租户名称", required = true)
    private String tenantName;

    @FieldDoc(
            description = "epassport账号id"
    )
    @ApiModelProperty
    public Integer epAccountId;

}
