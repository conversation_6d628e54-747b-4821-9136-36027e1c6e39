package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Title: SkuStoreToReviewCountGetResponse
 * @Description:
 * <AUTHOR>
 * @Date 2020/7/27 10:58
 */
@TypeDoc(
        description = "获取待审核数量响应"
)
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("获取待审核数量响应")
public class SkuStoreToReviewCountGetResponse {
    @FieldDoc(
            description = "待审核数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "待审核数量")
    private int count;


    public SkuStoreToReviewCountGetResponse build(int count){
        SkuStoreToReviewCountGetResponse response = new SkuStoreToReviewCountGetResponse(count);
        return response;
    }
}
