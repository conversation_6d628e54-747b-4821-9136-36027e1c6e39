package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: wanghongzhen
 * @date: 2020-05-11 15:46
 */
@TypeDoc(
        description = "门店SPU名称联想查询分页查询请求参数",
        authors = {"wanghongzhen"}
)
@Data
@ApiModel("门店SPU名称联想查询分页查询请求参数")
public class QueryTenantRegionSpuByNameRequest {
    @FieldDoc(
            description = "商品名称，支持总库商品和区域商品名称联线查询", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称")
    private String keyword;
    @FieldDoc(
            description = "传入目标门店后，回包中会返回该商品在门店商品是否已经存在", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "目标门店", required = true)
    @NotNull
    private Long storeId;
    @FieldDoc(
            description = "页大小,最大50", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "页大小")
    private Integer size = 20;
    @FieldDoc(
            description = "页码，下标从1开始", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "页码")
    private Integer page = 1;
    @FieldDoc(
            description = "游标，查询非第一页时需要传入", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "游标")
    private String scrollId;
}
