package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.picking;

import java.util.List;
import java.util.Set;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.ParsedPropertiesVO;
import com.sankuai.meituan.reco.stock.operate.center.thrift.dto.RecommendLocationBatch;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 骑手拣货商品信息.
 *
 * <AUTHOR>
 * @since 2021/11/15 17:03
 */
@TypeDoc(
        description = "骑手拣货任务信息"
)
@ApiModel("骑手拣货任务信息")
@Data
public class RiderPickTaskInfoVO {

    // 商品的拣货信息 start

    @FieldDoc(
            description = "拣货任务 ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "拣货任务 ID", required = true)
    private Long pickTaskId;

    @FieldDoc(
            description = "拣货任务状态", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "拣货任务状态", required = true)
    private Integer pickTaskStatus;

    @FieldDoc(
            description = "拣货任务状态描述", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "拣货任务状态描述", required = true)
    private String pickTaskStatusDesc;

    // 商品的拣货信息 end

    // 商品基本信息 start

    @FieldDoc(
            description = "skuId", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "skuId")
    private String skuId;

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "商品名称", required = true)
    private String skuName;

    @FieldDoc(
            description = "upc码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "upc码", required = true)
    private Set<String> upcCodes;

    @FieldDoc(
            description = "商品图片URL", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "商品图片URL", required = true)
    private String picUrl;

    @FieldDoc(
            description = "规格", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "规格", required = true)
    private String specification;

    @FieldDoc(
            description = "购买数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "购买数量", required = true)
    private Integer count;

    @FieldDoc(
            description = "温度属性",requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "温度属性", required = false)
    private String temperatureAttributeType;

    @FieldDoc(
            description = "推荐库位列表",requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "推荐库位列表", required = false)
    private List<RecommendLocationBatch.LocationInfo> locationInfos ;

    @FieldDoc(
            description = "推荐批次列表",requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "推荐批次列表", required = false)
    private List<RecommendLocationBatch.BatchInfo> batchInfos ;

    @FieldDoc(
            description = "所需温区库存是否充足",requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "所需温区库存是否充足", required = false)
    private Boolean requiredTemperatureAreaStockIsEnough ;

    @FieldDoc(
            description = "商品是否是sn商品",requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "商品是否是sn商品", required = false)
    private Boolean isSnProduct;

    // 商品基本信息 end

    @FieldDoc(
            description = "该拣货任务明细是否有部分退款标记",requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "该拣货任务明细是否有部分退款标记", required = false)
    private boolean havePartRefundFlag;

    @FieldDoc(
            description = "该拣货任务明细是否有部分退款标记",requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "该拣货任务明细是否有部分退款标记", required = false)
    private Boolean isIncludeStockLackGoods;

    @FieldDoc(
            description = "是否是高采购价货品",requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否是高采购价货品", required = false)
    private Boolean isHighWacGoods ;


    @FieldDoc(
            description = "是否是短保货品",requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否是短保货品", required = false)
    private Boolean isShortExpirationGoods;

    @FieldDoc(
            description = "实拍图Url",requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "实拍图Url", required = false)
    private List<String> realPicUrlList;

    @FieldDoc(
            description = "是否展示实拍图",requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否展示实拍图", required = false)
    private Boolean showRealPic;
}
