package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.meituan.shangou.saas.dto.StatusCodeEnum;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OCMSQueryOrderQuantityRequest;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OCMSQueryOrderQuantityResponse;
import com.meituan.shangou.saas.order.management.client.enums.QueryOrderTypeQuantityEnum;
import com.meituan.shangou.saas.order.management.client.service.online.OCMSQueryThriftService;
import com.meituan.shangou.saas.order.platform.enums.OrderBizTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.IntegerBooleanConstants;
import com.sankuai.meituan.reco.shopmgmt.pieapi.converters.channelorder.ChannelOrderConverter;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.auth.AppModuleRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.auth.AppModuleResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.management.ManagementHomepageResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.management.ManagementRevenueVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.management.ManagementReversionHomepageResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.management.RevenueOverviewVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.MenuCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MoneyUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.TimeUtils;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.common.CommonParam;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.common.ThriftResult;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.ordermerchandise.OrderMerchandiseThriftService;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.ordermerchandise.request.OrderMerchandiseCountRequest;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.ordermerchandise.response.OrderMerchandiseCountResp;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @createTime 2019/11/29
 * @description
 */
@Slf4j
@Component
public class ManagementWrapper {

    @Autowired
    private AuthThriftWrapper authThriftWrapper;

    @Autowired
    private BoothCurrentlyRevenueWrapper boothCurrentlyRevenueWrapper;

    @Autowired
    private StockAuthWrapper stockAuthWrapper;

    @Autowired
    private OCMSQueryThriftService ocmsQueryThriftService;

    @Autowired
    @Qualifier("purchaseOrderMerchandiseThriftService")
    private OrderMerchandiseThriftService orderMerchandiseThriftService;

    //订单查询起始天数（截止时间前推天数）
    private static final String CONFIG_QUERY_ORDER_CREATE_TIME_BEFORE = "query.order.create.time.before";

    private static final Integer WAIT_CONFIRM_ORDER_STATUS = 1;

    @Deprecated
    public CommonResponse<ManagementHomepageResponse> homepage(Long tenantId, List<Long> storeIdList, AppModuleRequest req) {

        AppModuleResult result = authThriftWrapper.appModule(req, tenantId, storeIdList, Lists.newArrayList(AuthCodeEnum.MANAGEMENT_PRODUCT.getAuthCode(), AuthCodeEnum.MANAGEMENT_AFTER_SALE.getAuthCode(), AuthCodeEnum.MANAGEMENT_WAREHOUSE.getAuthCode()));
        ManagementHomepageResponse response = new ManagementHomepageResponse();
        response.setAppModuleResult(result);

        ManagementRevenueVO managementRevenueVO = new ManagementRevenueVO();
        //判断当前账号有没有查看经营
        boolean hasRevenueAuth = authThriftWrapper.isCodeHasAuth(AuthCodeEnum.MANAGEMENT_STATISTICS.getAuthCode());
        if (hasRevenueAuth) {
            managementRevenueVO.setShowRevenue(IntegerBooleanConstants.BOOLEAN_TRUE);
            RevenueOverviewVO revenueOverviewVO = null;
            // 根据mcc配置，如果租户配置了使用新模式的营收金额计算，查看新模式
            Long boothId = authThriftWrapper.getBoothIdByCurrentUser();
            if (MccConfigUtil.getNewRevenueTenantIds().contains(String.valueOf(tenantId))) {
                revenueOverviewVO = boothCurrentlyRevenueWrapper.overview(tenantId, storeIdList, boothId);
            } else {
                revenueOverviewVO = boothCurrentlyRevenueWrapper.overviewOfflinePriceRevenue(tenantId, storeIdList, boothId);
            }

            managementRevenueVO.setTodayTotalRevenue(MoneyUtils.centToYuan(revenueOverviewVO.getTotalRevenue()) + "元");
            managementRevenueVO.setTodayOrderCount(revenueOverviewVO.getCompleteOrderCount() + "单");
            managementRevenueVO.setRevenueFormulaTips(revenueOverviewVO.getRevenueTips());
            managementRevenueVO.setTodayTotalRevenueName(revenueOverviewVO.getTotalRevenueName());

            //请求订单数据源
            OCMSQueryOrderQuantityResponse orderQuantityResponse = queryOrderQuantity(tenantId, storeIdList, req);
            if (orderQuantityResponse.getStatus() != null
                    && StatusCodeEnum.SUCCESS.getCode() == orderQuantityResponse.getStatus().getCode()
                    && MapUtils.isNotEmpty(orderQuantityResponse.getOrderTypeQuantityMap())) {
                //设置待审批退款数量
                int wait2Audit = MapUtils.getInteger(orderQuantityResponse.getOrderTypeQuantityMap(), QueryOrderTypeQuantityEnum.WAIT_REFUND_AUDIT.getValue(), 0);
                recurseSetPendingTaskCount(response.getAppModuleResult().getModules(), AuthCodeEnum.REFUND_AUDIT, wait2Audit, 0);
            }
        } else {
            managementRevenueVO.setShowRevenue(IntegerBooleanConstants.BOOLEAN_FALSE);
        }
        response.setManagementRevenueVO(managementRevenueVO);
        return CommonResponse.success(response);
    }

    public CommonResponse<ManagementReversionHomepageResponse> revisionHomepage(Long tenantId, Long poiId, List<Long> storeIdList, AppModuleRequest req) {

        List<String> authCodes = Arrays.asList(AuthCodeEnum.MANAGEMENT_PRODUCT.getAuthCode(),
                AuthCodeEnum.MANAGEMENT_AFTER_SALE.getAuthCode(),
                AuthCodeEnum.MANAGEMENT_WAREHOUSE.getAuthCode(),
                AuthCodeEnum.DELIVERY_MENU.getAuthCode(),
                AuthCodeEnum.MANAGEMENT_EMPLOYEE.getAuthCode(),
                AuthCodeEnum.PREPARATIONS.getAuthCode());

        List<String> menuCodes = new ArrayList<>(authCodes);
        // 后续新增经营下的一级模块，使用动态配置
        menuCodes.addAll(MccConfigUtil.dynamicRevisionHomepageMenuCodes());
        AppModuleResult result = authThriftWrapper.appModule(req, tenantId, storeIdList, menuCodes);

        // 请求订单数据源
        OCMSQueryOrderQuantityResponse orderQuantityResponse = queryOrderQuantity(tenantId, storeIdList, req);
        if (orderQuantityResponse.getStatus() != null
                && StatusCodeEnum.SUCCESS.getCode() == orderQuantityResponse.getStatus().getCode()
                && MapUtils.isNotEmpty(orderQuantityResponse.getOrderTypeQuantityMap())) {
            // 设置待审批退款数量
            int wait2Audit = MapUtils.getInteger(orderQuantityResponse.getOrderTypeQuantityMap(), QueryOrderTypeQuantityEnum.WAIT_REFUND_AUDIT.getValue(), 0);
            recurseSetPendingTaskCount(result.getModules(), AuthCodeEnum.REFUND_AUDIT, wait2Audit, 0);
        }
        queryOrderMerchandiseNumber(tenantId, storeIdList, req, result);

        // 根据库存对接配置过滤相关仓管经营模块权限
        stockAuthWrapper.filterManagementModuleResultByStockConfig(tenantId, poiId, result);

        ManagementReversionHomepageResponse response = new ManagementReversionHomepageResponse();
        response.setAppModuleResult(result);

        return CommonResponse.success(response);
    }

    private void queryOrderMerchandiseNumber(Long tenantId, List<Long> storeIdList, AppModuleRequest req, AppModuleResult result) {
        OrderMerchandiseCountResp orderMerchandiseCountResp = countOrderMerchandise(tenantId, storeIdList, req);
        if (orderMerchandiseCountResp != null) {
            setPendingTaskCount(result.getModules(), MenuCodeEnum.SUPPLY_CHAIN_ORDER.getCode(),
                orderMerchandiseCountResp.getTotalCount().intValue());
        }
    }

    private OrderMerchandiseCountResp countOrderMerchandise(Long tenantId, List<Long> storeIdList, AppModuleRequest req) {
        if (CollectionUtils.isEmpty(storeIdList)) {
            return OrderMerchandiseCountResp.builder().totalCount(0L).build();
        }
        OrderMerchandiseCountRequest countRequest = buildOrderMerchandiseCountReq(tenantId, storeIdList);
        try {
            ThriftResult<OrderMerchandiseCountResp> thriftResult = orderMerchandiseThriftService.countOrderMerchandise(countRequest);
            if (thriftResult.isFailure()) {
                log.error("查询待处理要货单数量失败，request:{}, response:{}", countRequest, thriftResult);
                return null;
            }
            return thriftResult.getData();
        } catch (Exception e) {
            log.error("查询待处理要货单数量失败，request:{}", countRequest, e);
        }
        return null;
    }

    private static OrderMerchandiseCountRequest buildOrderMerchandiseCountReq(Long tenantId, List<Long> storeIdList) {
        CommonParam commonParam = new CommonParam();
        commonParam.setTenantId(tenantId);
        commonParam.setStoreId(storeIdList.get(0));
        return OrderMerchandiseCountRequest.builder()
            .commonParam(commonParam)
            .orderStatus(WAIT_CONFIRM_ORDER_STATUS)
            .build();
    }

    private void setPendingTaskCount(List<AppModuleResult.AppModule> modules, String code, Integer pendingTaskCount) {
        org.apache.commons.collections4.CollectionUtils.emptyIfNull(modules).forEach(
            appModule -> {
                if (Objects.equals(appModule.getCode(), code)) {
                    appModule.setPendingTaskCount(pendingTaskCount);
                    return;
                }
                setPendingTaskCount(appModule.getSubModules(), code, pendingTaskCount);
            }
        );
    }

    private void recurseSetPendingTaskCount(List<AppModuleResult.AppModule> modules, AuthCodeEnum authCode, int val, int level) {
        if (CollectionUtils.isNotEmpty(modules) && level < 5){
            Optional.ofNullable(modules).map(List::stream).orElse(Stream.empty())
                    .filter(model-> StringUtils.equals(authCode.getAuthCode(), model.getCode()))
                    .findFirst()
                    .ifPresent(model->{
                        model.setPendingTaskCount(val);
                    });
            //递归
            modules.stream().forEach(model->{
                recurseSetPendingTaskCount(model.getSubModules(), authCode, val, level + 1);
            });
        }
    }

    private OCMSQueryOrderQuantityResponse queryOrderQuantity(Long tenantId, List<Long> storeIdList, AppModuleRequest req) {
        OCMSQueryOrderQuantityRequest request = new OCMSQueryOrderQuantityRequest();
        List<Integer> orderQuantityType = Lists.newArrayList();
        orderQuantityType.add(QueryOrderTypeQuantityEnum.WAIT_REFUND_AUDIT.getValue());
        request.setTenantId(tenantId);
        request.setShopIdList(storeIdList);
        request.setOrderBizTypeList(MccConfigUtil.getRefundOrderbizTypeList());
        request.setOrderQuantityTypeList(orderQuantityType);
        //查询当前时间之前7天的订单
        request.setBeginCreateTime(TimeUtils.getBeforeDayTimeStamp(ConfigUtilAdapter.getInt(CONFIG_QUERY_ORDER_CREATE_TIME_BEFORE, 7)));
        request.setEndCreateTime(System.currentTimeMillis());
        OCMSQueryOrderQuantityResponse response = null;
        try {
            response = ocmsQueryThriftService.queryOrderQuantity(request);
            log.info("请求订单待审批数量,request:{}, response:{}", request, response);
        }catch (Exception e){
            log.error("查询订单待审批数量失败，request:{}", request, e);
        }
        return response;
    }

}
