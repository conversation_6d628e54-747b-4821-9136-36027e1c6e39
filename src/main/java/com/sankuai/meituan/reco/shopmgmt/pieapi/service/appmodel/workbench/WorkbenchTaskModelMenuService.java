package com.sankuai.meituan.reco.shopmgmt.pieapi.service.appmodel.workbench;

import com.meituan.linz.boot.util.Fun;
import com.meituan.shangou.munich.assistant.client.constant.ExtraMapKey;
import com.meituan.shangou.munich.assistant.client.enums.TaskStatusEnum;
import com.meituan.shangou.munich.assistant.client.enums.TaskTypeEnum;
import com.meituan.shangou.munich.assistant.client.to.task.TaskSummaryTo;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiEntityTypeEnum;
import com.meituan.shangou.sac.dto.model.SacMenuNodeDto;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.Store;
import com.sankuai.meituan.reco.shopmgmt.pieapi.converters.assistant.AssistantTaskConverter;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.config.GoodsSpecialMenuConfig;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.appmodel.QueryMenuInfoRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.appmodel.MenuInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.MenuCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.MenuValidException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.JacksonUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.AssistantTaskWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.StoreFulfillConfigWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.TenantWrapper;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 工作台待处理任务模块菜单service
 *
 * <AUTHOR>
 * @since 2021/7/5
 */
@Service
@Slf4j
public class WorkbenchTaskModelMenuService extends AbstractWorkBenchMenuService {

    /**
     * 针对不存在待处理任务（任务数为0）的任务类型，是否需要展示任务模块
     */
    private static final Set<String> SHOW_NOT_EXIST_UN_HANDEL_TASK = new HashSet<>();

    static {
        SHOW_NOT_EXIST_UN_HANDEL_TASK.add(MenuCodeEnum.UN_PICKED.getCode());
        SHOW_NOT_EXIST_UN_HANDEL_TASK.add(MenuCodeEnum.UN_PICK_SETTLED.getCode());
        SHOW_NOT_EXIST_UN_HANDEL_TASK.add(MenuCodeEnum.UN_REVIEWED.getCode());
        SHOW_NOT_EXIST_UN_HANDEL_TASK.add(MenuCodeEnum.UN_STOCKED.getCode());
        SHOW_NOT_EXIST_UN_HANDEL_TASK.add(MenuCodeEnum.ABNORMAL_WAIT_TO_STOCK_TAKE.getCode());
        SHOW_NOT_EXIST_UN_HANDEL_TASK.add(MenuCodeEnum.ALLOCATE_PICKING_MENU.getCode());
        SHOW_NOT_EXIST_UN_HANDEL_TASK.add(MenuCodeEnum.ALLOCATE_STOCK_OUT_MENU.getCode());
        SHOW_NOT_EXIST_UN_HANDEL_TASK.add(MenuCodeEnum.SELF_DELIVERY_TASK_MENU.getCode());
        SHOW_NOT_EXIST_UN_HANDEL_TASK.add(MenuCodeEnum.ALLOCATE_WAIT_RECEIVE_MENU.getCode());
        SHOW_NOT_EXIST_UN_HANDEL_TASK.add(MenuCodeEnum.ALLOCATE_MERGE_ORDER_MENU.getCode());
        SHOW_NOT_EXIST_UN_HANDEL_TASK.add(MenuCodeEnum.ALLOCATE_WAIT_SEED_MENU.getCode());
        SHOW_NOT_EXIST_UN_HANDEL_TASK.add(MenuCodeEnum.PACKING_TASK.getCode());

    }

    /**
     * 需要展示超时数目的任务
     */
    private static final Set<String> TASK_NEED_TIMEOUT_COUNT = new HashSet<>();

    static {
        TASK_NEED_TIMEOUT_COUNT.add(MenuCodeEnum.UN_PICKED.getCode());
        TASK_NEED_TIMEOUT_COUNT.add(MenuCodeEnum.UN_PICK_SETTLED.getCode());
        TASK_NEED_TIMEOUT_COUNT.add(MenuCodeEnum.UN_REVIEWED.getCode());
        TASK_NEED_TIMEOUT_COUNT.add(MenuCodeEnum.SELF_DELIVERY_TASK_MENU.getCode());
        TASK_NEED_TIMEOUT_COUNT.add(MenuCodeEnum.DELIVERY_SELF_TASK_WORKBENCH_MENU.getCode());
        TASK_NEED_TIMEOUT_COUNT.add(MenuCodeEnum.ABNORMAL_WAIT_TO_STOCK_TAKE.getCode());
    }

    private static final int NO_TASK_COUNT = 0;

    // 0：未开启合流位指引，走旧页面，1；未开启合流位指引，走新页面，2：开启了合流位指引，走新页面
    private static final int OLD_PAGE = 0;

    /**
     * 1:专人打包，0：非专人打包
     */
    private static final int SPECIAL_ASSIGN_MERGE = 1;

    @Autowired
    private AssistantTaskWrapper assistantTaskWrapper;

    @Autowired
    private StoreFulfillConfigWrapper storeFulfillConfigWrapper;

    @Autowired
    private TenantWrapper tenantWrapper;

    /**
     * 测试menu的dataValue的值，是否是0
     *
     * @param menuInfo
     * @return true 不是0，false 是0
     */
    public static boolean testDataValueNotZero(MenuInfo menuInfo) {
        return !String.valueOf(NO_TASK_COUNT)
                .equals(menuInfo.getExtraInfoMap().get(AssistantTaskConverter.TASK_COUNT_KEY));
    }

    /**
     * 测试是否需要删除该menu
     *
     * @param menuInfo
     * @return true 不删除， false 删除
     */
    public static boolean testDataValueNotZeroNotPick(MenuInfo menuInfo) {
        return MenuCodeEnum.UN_PICKED.getCode().equals(menuInfo.getMenuCode())
                || MenuCodeEnum.UN_STOCKED.getCode().equals(menuInfo.getMenuCode()) || testDataValueNotZero(menuInfo);
    }

    /**
     * 测试menu的是否应该显示
     *
     * @param menuInfo 要进行验证的menu信息
     * @return true 显示该待办；false 不显示该待办
     */
    public static boolean testShowMenuInfo(MenuInfo menuInfo) {
        return !String.valueOf(NO_TASK_COUNT)
                .equals(menuInfo.getExtraInfoMap().get(AssistantTaskConverter.TASK_COUNT_KEY))
                || SHOW_NOT_EXIST_UN_HANDEL_TASK.contains(menuInfo.getMenuCode());
    }

    @Override
    protected MenuCodeEnum getMenuCode() {
        return MenuCodeEnum.WORKBENCH_PENDING_TASK;
    }

    @Override
    protected List<MenuInfo> getSubMenuInfos(Map<String, SacMenuNodeDto> menuCodeWithNodeMap, IdentityInfo identityInfo,
                                             QueryMenuInfoRequest request, Boolean possibleNewQueryGray) {
        // 针对任务菜单code，获取任务类型，并查询对应的数据
        Set<String> subMenuCodes = new HashSet<>(menuCodeWithNodeMap.keySet());
        List<TaskTypeEnum> taskTypeList = getNeedTaskTypes(subMenuCodes);
        taskTypeList = filterTaskByPoiType(taskTypeList, identityInfo);

        Pair<Boolean, List<TaskSummaryTo>> taskPair = assistantTaskWrapper.getTaskSummaryByType(
                identityInfo.getUser().getTenantId(), identityInfo.getStoreId(), identityInfo.getUser().getAccountId(),
                taskTypeList, possibleNewQueryGray);

        boolean isNewQueryGray = BooleanUtils.isTrue(taskPair.getLeft());
        List<TaskSummaryTo> taskSummaryList = taskPair.getRight();

        List<MenuInfo> subMenuInfos = new ArrayList<>();
        Map<String, GoodsSpecialMenuConfig> specialMenuConfigMap = MccConfigUtil.goodsSpecialMenuConfigMap();
        Set<Integer> poiChannels = Collections.emptySet();
        if (isNewQueryGray) {
            try {
                poiChannels = tenantWrapper.getPoiChannels(identityInfo.getUser().getTenantId(), identityInfo.getStoreId());
            } catch (Exception e) {
                log.error("查询门店开通渠道列表异常 tenantId: {}, storeId: {}", identityInfo.getUser().getTenantId(), identityInfo.getStoreId(), e);
            }
        }

        // etops中配置的任务
        for (String pendingTaskMenuCode : subMenuCodes) {
            // 获取当前菜单code对应的任务汇总信息
            TaskTypeEnum taskType = MenuCodeEnum.ofAuthCode(pendingTaskMenuCode).getTaskTypeEnum();
            List<TaskSummaryTo> taskSummaries = Fun.filter(taskSummaryList,
                    taskSummary -> taskType.getCode() == taskSummary.getTaskType()
                            && taskSummary.getStatus().equals(TaskStatusEnum.PENDING.code()));

            if (CollectionUtils.isEmpty(taskSummaries)) {
                log.info("任务不存在汇总信息，可能是查询失败，也可能是数量为0未返回:pendingTaskMenuCode:{}", pendingTaskMenuCode);
                continue;
            }

            if (!isNewQueryGray && taskSummaries.size() > 1) {
                log.error("单个任务存在多个汇总信息，不展示:taskSummaries:{}", JacksonUtils.toJson(taskSummaries));
                throw new BizException("单个任务存在多个汇总信息");
            }

            // 非灰度 曝光异常不展示
            if (!isNewQueryGray && taskType == TaskTypeEnum.EXPOSURE_ABNORMAL){
                continue;
            }

            // 非灰度或 灰度无特殊处理菜单
            if (!isNewQueryGray || !specialMenuConfigMap.containsKey(pendingTaskMenuCode)) {
                MenuInfo subMenuInfo = buildSubMenuInfo(menuCodeWithNodeMap, identityInfo, pendingTaskMenuCode, taskSummaries.get(0));
                if (Objects.nonNull(subMenuInfo)){
                    subMenuInfos.add(subMenuInfo);
                }
            }else {
                // 灰度逻辑 且 有特殊处理菜单
                GoodsSpecialMenuConfig goodsSpecialMenuConfig = specialMenuConfigMap.get(pendingTaskMenuCode);
                // 无需渠道裂变
                if (CollectionUtils.isEmpty(goodsSpecialMenuConfig.getFissionChannels())) {
                    MenuInfo subMenuInfo = buildSubMenuInfo(menuCodeWithNodeMap, identityInfo, pendingTaskMenuCode, taskSummaries.get(0));
                    if (subMenuInfo == null) {
                        continue;
                    }
                    if (StringUtils.isNotBlank(goodsSpecialMenuConfig.getMainTitle())) {
                        subMenuInfo.setMenuName(goodsSpecialMenuConfig.getMainTitle());
                    }
                    subMenuInfos.add(subMenuInfo);
                }else {
                    Map<String, TaskSummaryTo> taskSummaryToMap = Fun.toMapQuietly(taskSummaries, TaskSummaryTo::getTaskTypeName);
                    // 渠道裂变
                    for (GoodsSpecialMenuConfig.FissionChannel fissionChannel : goodsSpecialMenuConfig.getFissionChannels()) {
                        TaskSummaryTo taskSummaryTo = taskSummaryToMap.get(fissionChannel.getChannelId() + taskType.getName());
                        if (!poiChannels.contains(fissionChannel.getChannelId()) || taskSummaryTo == null) {
                            continue;
                        }

                        MenuInfo subMenuInfo = buildSubMenuInfo(menuCodeWithNodeMap, identityInfo, pendingTaskMenuCode, taskSummaryTo);
                        if (subMenuInfo == null) {
                            continue;
                        }

                        if (StringUtils.isNotBlank(goodsSpecialMenuConfig.getMainTitle())) {
                            subMenuInfo.setMenuName(fissionChannel.getChannelName() + goodsSpecialMenuConfig.getMainTitle());
                        } else {
                            subMenuInfo.setMenuName(fissionChannel.getChannelName() + subMenuInfo.getMenuName());
                        }
                        if (fissionChannel.getSort() != null){
                            subMenuInfo.setRank(fissionChannel.getSort());
                        }
                        subMenuInfo.setUrl(fissionChannel.getJumpUrl());
                        subMenuInfos.add(subMenuInfo);
                    }
                }
            }
        }

        // 任务中心返回的任务
        List<MenuInfo> taskCenterMenuInfo = buildTaskCenterMenuInfo(subMenuInfos, taskSummaryList);
        if (CollectionUtils.isNotEmpty(taskCenterMenuInfo)) {
            subMenuInfos.addAll(taskCenterMenuInfo);
        }
        return subMenuInfos;
    }

    private List<MenuInfo> buildTaskCenterMenuInfo(List<MenuInfo> authSubMenuInfos, List<TaskSummaryTo> taskSummaryTos) {
        ArrayList<MenuInfo> result = new ArrayList<>();

        Set<Integer> authMenuTaskCodes = authSubMenuInfos.stream().map(MenuInfo::getMenuCode)
                .map(MenuCodeEnum::ofAuthCode)
                .filter(Objects::nonNull)
                .map(MenuCodeEnum::getTaskTypeEnum)
                .map(TaskTypeEnum::getCode)
                .collect(Collectors.toSet());
        for (TaskSummaryTo taskSummaryTo : taskSummaryTos) {
            if (!authMenuTaskCodes.contains(taskSummaryTo.getTaskType())
                    && MapUtils.isNotEmpty(taskSummaryTo.getExtraInfoMap())
                    && taskSummaryTo.getExtraInfoMap().containsKey(ExtraMapKey.TASK_CENTER_TYPE)
                    && Boolean.parseBoolean(taskSummaryTo.getExtraInfoMap().get(ExtraMapKey.TASK_CENTER_TYPE))) {
                result.add(AssistantTaskConverter.convertTaskCenter2AssistantMenu(taskSummaryTo));
            }
        }
        return result;
    }

    private MenuInfo buildSubMenuInfo(Map<String, SacMenuNodeDto> menuCodeWithNodeMap, IdentityInfo identityInfo, String pendingTaskMenuCode, TaskSummaryTo taskSummary) {
        // 构造子菜单信息，用菜单的扩展信息初始化extraInfoMap，并往map中填充业务信息
        MenuInfo subMenuInfo = buildMenuInfoBySacMenuNodeDto(menuCodeWithNodeMap.get(pendingTaskMenuCode));
        try {
            buildExternalInfoAndValid(subMenuInfo, taskSummary, pendingTaskMenuCode, identityInfo);
            return subMenuInfo;
        } catch (MenuValidException exception) {
            log.info(exception.getErrMsg());
            // 不展示菜单
            return null;
        }
    }

    protected void sortSubMenuInfos(List<MenuInfo> subMenuInfos) {
        // 将为0待办显示逻辑删除，修改为全部显示，因此将非0待办放在前面
        super.sortSubMenuInfos(subMenuInfos);
        List<MenuInfo> zeroSubMenu = subMenuInfos.stream().filter(m -> !testDataValueNotZeroNotPick(m))
                .collect(Collectors.toList());
        subMenuInfos.removeAll(zeroSubMenu);
        subMenuInfos.addAll(zeroSubMenu);
    }

    private void buildExternalInfoAndValid(MenuInfo subMenuInfo, TaskSummaryTo taskSummary, String pendingTaskMenuCode,
            IdentityInfo identityInfo) {
        Map<String, Object> menuExtraInfoMap = subMenuInfo.getExtraInfoMap();

        // 填充业务信息：待处理任务数量
        int taskCount = taskSummary.getTaskCount() == null ? NO_TASK_COUNT : taskSummary.getTaskCount();
        menuExtraInfoMap.put(AssistantTaskConverter.TASK_COUNT_KEY, String.valueOf(taskCount));

        // 填充业务信息：填充颜色，如果是0，则填充默认颜色
        menuExtraInfoMap.put(AssistantTaskConverter.COLOR_KEY,
                NO_TASK_COUNT == taskCount ? AssistantTaskConverter.DEFAULT_COLOR
                        : MenuCodeEnum.ofAuthCode(pendingTaskMenuCode).getGroupEnum().getColor());

        // 填充业务信息：超时任务数量（菜单项需要超时数据，且任务汇总数据中包含了超时数据、且超时数据不为0，才填充）
        Map<String, String> summaryExtraInfoMap = taskSummary.getExtraInfoMap();
        if (needPutTimeOutTaskCount(pendingTaskMenuCode, summaryExtraInfoMap)) {
            menuExtraInfoMap.put(AssistantTaskConverter.TIME_OUT_TASK_COUNT_KEY,
                    summaryExtraInfoMap.get(AssistantTaskConverter.TIME_OUT_TASK_COUNT_KEY));
        }


        if (MccConfigUtil.storeMessageUpdate()) {
            updateFulfillStore(identityInfo, subMenuInfo, pendingTaskMenuCode);
        } else {
            //旧代码，后续废弃
            updateFulfillStoreDeprecated(identityInfo, subMenuInfo, pendingTaskMenuCode);
        }
    }

    //后续废弃该代码
    @Deprecated
    private void updateFulfillStoreDeprecated(IdentityInfo identityInfo, MenuInfo subMenuInfo, String pendingTaskMenuCode) {
        Store store = storeFulfillConfigWrapper.getStore(identityInfo.getStoreId());
        // mergeGuideMode为0，返回待复核，mergeGuideMode为1，2，返回待合流新页面的URl
        if (StringUtils.equals(pendingTaskMenuCode, MenuCodeEnum.UN_REVIEWED.getCode())
                && Objects.nonNull(store.getProcessModeConfig())
                && store.getProcessModeConfig().getMergeGuideMode() != OLD_PAGE) {
            updateSubmenuInfo(subMenuInfo, store);
        }

        if (StringUtils.equals(pendingTaskMenuCode, MenuCodeEnum.ALLOCATE_WAIT_RECEIVE_MENU.getCode())
                && Objects.nonNull(store.getWarehouseProcessModeConfig())
                && store.getWarehouseProcessModeConfig().getReceiveMode() == 0) {
            throw new MenuValidException("无需领取模式、不展示待领取入口");
        }
    }


    private void updateFulfillStore(IdentityInfo identityInfo, MenuInfo subMenuInfo, String pendingTaskMenuCode) {
        // mergeGuideMode为0，返回待复核，mergeGuideMode为1，2，返回待合流新页面的URl
        if (StringUtils.equals(pendingTaskMenuCode, MenuCodeEnum.UN_REVIEWED.getCode())) {
            Store store = storeFulfillConfigWrapper.getStore(identityInfo.getStoreId());
            if (Objects.nonNull(store.getProcessModeConfig()) && store.getProcessModeConfig().getMergeGuideMode() != OLD_PAGE) {
                updateSubmenuInfo(subMenuInfo, store);
            }

        }
        // 出库拣货无需领取不展示待领取入口
        if (StringUtils.equals(pendingTaskMenuCode, MenuCodeEnum.ALLOCATE_WAIT_RECEIVE_MENU.getCode())) {
            Store store = storeFulfillConfigWrapper.getStore(identityInfo.getStoreId());
            if (Objects.nonNull(store.getWarehouseProcessModeConfig())
                    && store.getWarehouseProcessModeConfig().getReceiveMode() == 0) {
                throw new MenuValidException("无需领取模式、不展示待领取入口");
            }
        }
    }

    private void updateSubmenuInfo(MenuInfo subMenuInfo, Store store) {
        subMenuInfo
                .setMenuName(store.getProcessModeConfig().getPackageTaskMode() == SPECIAL_ASSIGN_MERGE ? "待打包" : "待合流");
        subMenuInfo.setUrl(
                "shuguopai://mrn?class=RPCSMRNViewController&mrn_biz=supermarket&mrn_entry=pick-select-confluence&mrn_component=PSConfluenceApp");
    }

    @Override
    protected Boolean hideParentMenu(Set<String> hasAuthSubMenuCodes) {
        // 如果是托盘中心仓，不展示代办
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        Map<Long, Boolean> outRepository = poiServiceFacade.isOutRepository(identityInfo.getUser().getTenantId(),
                Collections.singletonList(identityInfo.getStoreId()));

        if (Objects.nonNull(outRepository) && outRepository.getOrDefault(identityInfo.getStoreId(), false)) {
            log.info("门店为托盘中心仓，不展示代办, poiId:{}", identityInfo.getStoreId());
            return true;
        }

        Set<String> showTaskMenuCodes = Arrays.stream(MenuCodeEnum.values())
                .filter(c -> Objects.nonNull(c.getTaskTypeEnum())).map(MenuCodeEnum::getCode)
                .collect(Collectors.toSet());
        showTaskMenuCodes.retainAll(hasAuthSubMenuCodes);
        return CollectionUtils.isEmpty(showTaskMenuCodes);
    }

    /**
     * 根据有权限的菜单code，获取需要查询的任务类型
     *
     * @param hasAuthMenuCode
     * @return
     */
    private List<TaskTypeEnum> getNeedTaskTypes(Set<String> hasAuthMenuCode) {
        // 存在已经etops中配置了菜单，但是没有在代码中实现的情况，取并集。
        hasAuthMenuCode
                .retainAll(Arrays.stream(MenuCodeEnum.values()).map(MenuCodeEnum::getCode).collect(Collectors.toSet()));
        return hasAuthMenuCode.stream().map(MenuCodeEnum::ofAuthCode).filter(Objects::nonNull)
                .map(MenuCodeEnum::getTaskTypeEnum).collect(Collectors.toList());
    }

    private List<TaskTypeEnum> filterTaskByPoiType(List<TaskTypeEnum> tasks, IdentityInfo identityInfo) {
        Long poiId = identityInfo.getStoreId();
        Map<Long, Integer> entityMap = tenantWrapper.queryEntityTypeMapByPoiIds(identityInfo.getUser().getTenantId(),
                Collections.singletonList(poiId));

        if (Objects.isNull(entityMap.get(poiId))) {
            log.warn("没有获取到门店配置信息，使用普通门店类别，返回所有的数据 poiId:{}", poiId);
            return tasks;
        }

        Integer entityType = entityMap.get(poiId);
        // 非普通门店，只查询支持仓的task类别
        if (PoiEntityTypeEnum.STORE.code() != entityType) {
            return tasks.stream().filter(TaskTypeEnum::isSupportStorehouse).collect(Collectors.toList());
        }
        return tasks;
    }

    /**
     * 是否需要返回超时任务
     * 当前任务需要返回超时数量 && 任务存在超时数量 && 超时数量不为0
     *
     * @param pendingTaskMenuCode
     * @param summaryExtraInfoMap
     * @return
     */
    private boolean needPutTimeOutTaskCount(String pendingTaskMenuCode, Map<String, String> summaryExtraInfoMap) {
        return taskNeedTimeOutCount(pendingTaskMenuCode) && summaryExtraInfoMap != null
                && summaryExtraInfoMap.containsKey(AssistantTaskConverter.TIME_OUT_TASK_COUNT_KEY)
                && summaryExtraInfoMap.get(AssistantTaskConverter.TIME_OUT_TASK_COUNT_KEY) != null
                && !String.valueOf(NO_TASK_COUNT)
                        .equals(summaryExtraInfoMap.get(AssistantTaskConverter.TIME_OUT_TASK_COUNT_KEY));
    }

    private boolean taskNeedTimeOutCount(String menuCode) {
        return TASK_NEED_TIMEOUT_COUNT.contains(menuCode);
    }

    private boolean hideChildTaskMenu(TaskSummaryTo taskSummary, String pendingTaskMenuCode) {
        return (taskSummary.getTaskCount() == null || taskSummary.getTaskCount() == NO_TASK_COUNT)
                && !showNotExistUnHandelTask(pendingTaskMenuCode);
    }

    /**
     * 针对不存在待处理任务（任务数为0）的任务类型，是否需要展示任务模块
     * true：展示，false：不展示
     *
     * @param menuCode 任务menuCode
     * @return
     */
    private boolean showNotExistUnHandelTask(String menuCode) {
        return SHOW_NOT_EXIST_UN_HANDEL_TASK.contains(menuCode);
    }

}
