package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pendingtask;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Set;

/**
 * <AUTHOR>
 * @createTime 2019/11/19
 * @description
 */
@TypeDoc(
        description = "代办任务查询请求"
)
@Data
@ApiModel("代办任务查询请求")
public class PendingTaskQueryRequest {

    @FieldDoc(
            description = "任务码,多个码用逗号分隔(值等于权限码,前后端约定https://km.sankuai.com/page/222769177)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "任务码,多个码用逗号分隔(值等于权限码,前后端约定https://km.sankuai.com/page/222769177)", required = true)
    @NotNull
    private Set<String> authCodes;

    @FieldDoc(
            description = "实体类型", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "实体类型", required = true)
    @NotNull
    private Integer entityType;

    @FieldDoc(
            description = "实体Id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "实体Id", required = true)
    @NotNull
    private Long entityId;

}
