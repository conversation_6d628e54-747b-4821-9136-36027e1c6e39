package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2022/4/12 5:39 下午
 **/
@TypeDoc(
        description = "商品信息异常，不可售商品，平台停售商品统计详情"
)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProblemSpuCountDetailVO {

    @FieldDoc(
            description = "展示名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "展示名称", required = true)
    private String title;

    @FieldDoc(
            description = "问题商品统计数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "问题商品统计数量", required = true)
    private Integer value;

    @FieldDoc(
            description = "问题类型标识", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "问题类型标识", required = true)
    private String code;

    @FieldDoc(
            description = "跳转链接", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "跳转链接", required = true)
    private String jumpUrl;

    @FieldDoc(
            description = "跳转参数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "跳转参数", required = true)
    private String jumpParam;

}
