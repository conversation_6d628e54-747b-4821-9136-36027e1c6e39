package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.wavepick.converter;

import com.sankuai.meituan.reco.pickselect.thrift.wave.request.WavePickDetailRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick.WarehouseWaveTaskDetailRequest;
import org.mapstruct.Mapper;

/**
 * @Description:
 * @Author: zhangjian155
 * @Date: 2022/8/25 16:08
 */
@Mapper(componentModel = "spring")
public abstract class WarehouseWaveTaskDetailRequestConverter {
    public abstract WavePickDetailRequest convert2ThriftRequest(WarehouseWaveTaskDetailRequest request,
                                                                Long accountId, Long storeId, Long tenantId);
}
