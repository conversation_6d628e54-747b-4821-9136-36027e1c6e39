package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.SpecialPictureDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@TypeDoc(
        name = "美团类目特殊图",
        description = "美团类目特殊图"
)
@Getter
@Setter
@ToString
public class TenantSpecialPictureVO {

    @FieldDoc(
            description = "图片类型"
    )
    @ApiModelProperty(name = "图片类型")
    private Integer pictureType;

    @FieldDoc(
            description = "标题"
    )
    @ApiModelProperty("标题")
    private String pictureTitle;

    @FieldDoc(
            description = "图片描述"
    )
    @ApiModelProperty("图片描述")
    private String pictureDesc;

    @FieldDoc(
            description = "示例图"
    )
    @ApiModelProperty("示例图")
    private List<String> pictureExample;

    @FieldDoc(
            description = "是否必传：1-必传，2-非必传"
    )
    @ApiModelProperty("是否必传：1-必传，2-非必传")
    private Integer isRequired;

    @FieldDoc(
            description = "是否c端展示：1-展示，2-不展示"
    )
    @ApiModelProperty("是否c端展示：1-展示，2-不展示")
    private Integer isDisplay;

    @FieldDoc(
            description = "数量限制"
    )
    @ApiModelProperty("数量限制")
    private Integer numLimit;

    @FieldDoc(
            description = "图片URL列表"
    )
    @ApiModelProperty("图片URL列表")
    private List<String> pictureUrl;

    @FieldDoc(
            description = "是否删除"
    )
    @ApiModelProperty("是否删除")
    private Boolean deleted;

    public static SpecialPictureDTO toMtSpecialPictureDTO(TenantSpecialPictureVO mtSpecialPictureVO) {
        SpecialPictureDTO dto = new SpecialPictureDTO();
        dto.setPictureType(mtSpecialPictureVO.getPictureType());
        dto.setPictureTitle(mtSpecialPictureVO.getPictureTitle());
        dto.setPictureDesc(mtSpecialPictureVO.getPictureDesc());
        dto.setPictureExample(mtSpecialPictureVO.getPictureExample());
        dto.setIsRequired(mtSpecialPictureVO.getIsRequired());
        dto.setIsDisplay(mtSpecialPictureVO.getIsDisplay());
        dto.setNumLimit(mtSpecialPictureVO.getNumLimit());
        dto.setPictureUrl(mtSpecialPictureVO.getPictureUrl());
        dto.setDeleted(mtSpecialPictureVO.getDeleted());
        return dto;
    }

    public static com.sankuai.meituan.shangou.empower.productbiz.client.dto.SpecialPictureDTO toDTO(TenantSpecialPictureVO mtSpecialPictureVO) {
        com.sankuai.meituan.shangou.empower.productbiz.client.dto.SpecialPictureDTO dto = new com.sankuai.meituan.shangou.empower.productbiz.client.dto.SpecialPictureDTO();
        dto.setPictureType(mtSpecialPictureVO.getPictureType());
        dto.setPictureTitle(mtSpecialPictureVO.getPictureTitle());
        dto.setPictureDesc(mtSpecialPictureVO.getPictureDesc());
        dto.setPictureExample(mtSpecialPictureVO.getPictureExample());
        dto.setIsRequired(mtSpecialPictureVO.getIsRequired());
        dto.setIsDisplay(mtSpecialPictureVO.getIsDisplay());
        dto.setNumLimit(mtSpecialPictureVO.getNumLimit());
        dto.setPictureUrl(mtSpecialPictureVO.getPictureUrl());
        dto.setDeleted(mtSpecialPictureVO.getDeleted());
        return dto;
    }

    public static TenantSpecialPictureVO toVO(com.sankuai.meituan.shangou.empower.productbiz.client.dto.SpecialPictureDTO dto) {
        TenantSpecialPictureVO vo = new TenantSpecialPictureVO();
        vo.setPictureType(dto.getPictureType());
        vo.setPictureTitle(dto.getPictureTitle());
        vo.setPictureDesc(dto.getPictureDesc());
        vo.setPictureExample(dto.getPictureExample());
        vo.setIsRequired(dto.getIsRequired());
        vo.setIsDisplay(dto.getIsDisplay());
        vo.setNumLimit(dto.getNumLimit());
        vo.setPictureUrl(dto.getPictureUrl());
        vo.setDeleted(dto.getDeleted());
        return vo;
    }

    public static TenantSpecialPictureVO ocmsToVO(com.sankuai.meituan.shangou.empower.ocms.client.product.dto.SpecialPictureDTO dto) {
        TenantSpecialPictureVO vo = new TenantSpecialPictureVO();
        vo.setPictureType(dto.getPictureType());
        vo.setPictureTitle(dto.getPictureTitle());
        vo.setPictureDesc(dto.getPictureDesc());
        vo.setPictureExample(dto.getPictureExample());
        vo.setIsRequired(dto.getIsRequired());
        vo.setIsDisplay(dto.getIsDisplay());
        vo.setNumLimit(dto.getNumLimit());
        vo.setPictureUrl(dto.getPictureUrl());
        vo.setDeleted(dto.getDeleted());
        return vo;
    }
}
