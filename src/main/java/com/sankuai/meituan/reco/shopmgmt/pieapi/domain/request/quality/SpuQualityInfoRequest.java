package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.quality;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.product.request.CalSpuQualityDetailInfoRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/9/20
 */
@TypeDoc(
        name = "推荐重量请求",
        description = "推荐重量请求"
)
@Data
public class SpuQualityInfoRequest {

    @FieldDoc(
            description = "是否全字段计算质量分"
    )
    @ApiModelProperty(name = "是否全字段计算质量分")
    private boolean fullCheck;

    @FieldDoc(
            description = "门店id"
    )
    @ApiModelProperty(name = "门店id")
    private String storeId;

    @FieldDoc(
            description = "商品id"
    )
    @ApiModelProperty(name = "商品id")
    private String spuId;

    @FieldDoc(
            description = "商品名称"
    )
    @ApiModelProperty(name = "商品名称")
    private String name;

    @FieldDoc(
            description = "类目id路径"
    )
    @ApiModelProperty(name = "类目id路径")
    private String channelCategoryIdPath;

    @FieldDoc(
            description = "类目名称路径"
    )
    @ApiModelProperty(name = "类目名称路径")
    private String channelCategoryNamePath;

    @FieldDoc(
            description = "商品图片"
    )
    @ApiModelProperty(name = "商品图片")
    private List<String> imageUrlList;

    @FieldDoc(
            description = "品牌信息"
    )
    @ApiModelProperty(name = "品牌信息")
    private String brandName;

    @FieldDoc(
            description = "商品卖点文本"
    )
    @ApiModelProperty(name = "商品卖点文本")
    private String sellingPoint;

    @FieldDoc(
            description = "商品图详"
    )
    @ApiModelProperty(name = "商品图详")
    private List<String> pictureContents;

    @FieldDoc(
            description = "商品视频"
    )
    @ApiModelProperty(name = "商品视频")
    private String videoInfo;

    @FieldDoc(
            description = "商品用户所选的动态属性信息"
    )
    @ApiModelProperty(name = "商品用户所选的动态属性信息")
    private List<SelectedCategoryAttrInfoVo> dynamicProperties;

    @FieldDoc(
            description = "规格信息"
    )
    @ApiModelProperty(name = "规格信息")
    private List<SkuSimpleInfoVo> skus ;

    @FieldDoc(
            description = "兼容前端逻辑，主档时无法做到storeId不传值", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "兼容前端逻辑，主档时无法做到storeId不传值")
    private Boolean isMerchantSpu;

    public CalSpuQualityDetailInfoRequest convertRpcRequest() {
        CalSpuQualityDetailInfoRequest request = new CalSpuQualityDetailInfoRequest();
        request.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        request.setCalculateAllFields(fullCheck);
        if (BooleanUtils.isFalse(isMerchantSpu) && StringUtils.isNotBlank(storeId)) {
            request.setStoreId(Long.valueOf(storeId));
        }
        request.setSpuId(spuId);
        request.setSpuName(name);
        request.setChannelCategoryIdPath(channelCategoryIdPath);
        request.setChannelCategoryNamePath(channelCategoryNamePath);
        request.setImgUrlList(imageUrlList);
        request.setBrandName(brandName);
        request.setSellingPoint(sellingPoint);
        request.setPictureContentList(pictureContents);
        request.setVideoInfo(videoInfo);
        request.setPropertyInfoList(Fun.map(dynamicProperties, SelectedCategoryAttrInfoVo::convert2SpuPropertyInfoDto));
        request.setSkuDetailInfoList(Fun.map(skus, SkuSimpleInfoVo::convert2SkuDetailInfoDto));

        return request;
    }
}
