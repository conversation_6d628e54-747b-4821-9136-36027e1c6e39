package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.management;

import java.util.List;

import com.meituan.servicecatalog.api.annotations.FieldDoc;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/4/19
 */
@Data
public class OpenBizModuleVo {


    @FieldDoc(
            description = "业务模块id"
    )
    public Long bizModuleId;


    @FieldDoc(
            description = "业务模块code"
    )
    public String bizModuleCode;

    @FieldDoc(
            description = "模块名称"
    )
    public String bizModuleName;

    @FieldDoc(
            description = "配置信息"
    )
    public List<ConfigContent> configs;
}
