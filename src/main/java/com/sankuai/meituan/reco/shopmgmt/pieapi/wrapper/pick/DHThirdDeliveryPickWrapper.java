package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.pick;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.sankuai.meituan.reco.pickselect.dto.OperatorDTO;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.RiderPickingThriftService;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.dto.RiderPickOrderDTO;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.request.QueryRiderWaitPickOrderListRequest;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.request.QueryStoreUnacceptedOrderRequest;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.request.RiderAcceptThirdDeliveryPickOrderRequest;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.response.QueryRiderPickOrderResponse;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.response.RiderOperationResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.RpcInvokeUtils;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @since 2023/8/28 10:53
 **/
@Service
@Slf4j
public class DHThirdDeliveryPickWrapper {

    @Resource
    private RiderPickingThriftService riderPickingRpcService;


    public QueryRiderPickOrderResponse queryStoreUnAcceptedPickOrders(Long tenantId, Long storeId, int page, int pageSize) {
        QueryStoreUnacceptedOrderRequest request = new QueryStoreUnacceptedOrderRequest(tenantId, storeId, page, pageSize);


        return RpcInvokeUtils.rpcInvokeTemplate(() -> riderPickingRpcService.queryStoreUnAcceptedPickOrders(request), "查询未领取拣货单失败",
                resp -> resp.getStatus().getCode(),
                resp -> resp.getStatus().getMessage());
    }

    public QueryRiderPickOrderResponse queryRiderWaitToPickOrders(Long tenantId, Long storeId, Long accountId, int page, int pageSize) {
        QueryRiderWaitPickOrderListRequest request = new QueryRiderWaitPickOrderListRequest(tenantId, storeId, accountId, page, pageSize);


        return RpcInvokeUtils.rpcInvokeTemplate(() -> riderPickingRpcService.queryRiderWaitToPickOrders(request), "查询待拣货单失败",
                resp -> resp.getStatus().getCode(),
                resp -> resp.getStatus().getMessage());
    }

    @MethodLog(logResponse = true, logRequest = true)
    public Integer queryStoreUnAcceptedPickOrderCount(Long tenantId, Long storeId) {
        return riderPickingRpcService.queryStoreUnAcceptedPickOrderCount(tenantId, storeId);
    }

    @MethodLog(logResponse = true, logRequest = true)
    public Integer queryRiderWaitToPickOrderCount(Long tenantId, Long storeId, Long accountId) {
        return riderPickingRpcService.queryRiderWaitToPickOrderCount(tenantId, storeId, accountId);
    }
    @MethodLog(logResponse = true, logRequest = true)
    public void acceptThirdDeliveryPickOrder(Long tenantId, Long storeId,String viewOrderId, Integer bizType,
                                             Long operatorAccountId, String operatorName) {
        RiderAcceptThirdDeliveryPickOrderRequest request = new RiderAcceptThirdDeliveryPickOrderRequest();
        request.setStoreId(storeId);
        request.setTenantId(tenantId);
        request.setViewOrderId(viewOrderId);
        request.setBizType(bizType);
        request.setOperatorDTO(new OperatorDTO(operatorAccountId, operatorName));
        RpcInvokeUtils.rpcInvokeTemplate(() -> riderPickingRpcService.acceptThirdDeliveryPickOrder(request), "调用领取三方配送拣货单接口失败",
                resp -> resp.getStatus().getCode(),
                resp -> resp.getStatus().getMessage());
    }

}
