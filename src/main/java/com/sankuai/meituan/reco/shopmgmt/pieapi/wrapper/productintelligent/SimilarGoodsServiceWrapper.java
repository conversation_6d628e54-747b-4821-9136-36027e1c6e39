package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.productintelligent;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.productintelligent.SimilarGoodsRequestVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.productintelligent.SimilarGoodsResponseVO;
import com.sankuai.meituan.shangou.empower.productintelligent.thrift.model.SimilarGoodsRequestDTO;
import com.sankuai.meituan.shangou.empower.productintelligent.thrift.model.SimilarGoodsResultListDTO;
import com.sankuai.meituan.shangou.empower.productintelligent.thrift.service.SimilarGoodsThriftService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class SimilarGoodsServiceWrapper {

    @Resource
    private SimilarGoodsThriftService similarGoodsThriftService;

    public CommonResponse<SimilarGoodsResponseVO> marketSimilarGoods(SimilarGoodsRequestVO similarGoodsRequestVO) {
        SimilarGoodsRequestDTO rpcRequest = SimilarGoodsRequestVO.toRpcRequest(similarGoodsRequestVO);
        log.info("SimilarGoodsServiceWrapper.marketSimilarGoods, 调用similarGoodsThriftService.querySimilarGoods() request:{}", rpcRequest);

        SimilarGoodsResultListDTO similarGoodsResultListDTO = similarGoodsThriftService.querySimilarGoods(rpcRequest);
        log.info("SimilarGoodsServiceWrapper.marketSimilarGoods, 调用similarGoodsThriftService.querySimilarGoods() response:{}", similarGoodsResultListDTO);

        SimilarGoodsResponseVO similarGoodsResponseVO = SimilarGoodsResponseVO.convertResponse(similarGoodsResultListDTO);

        return new CommonResponse(similarGoodsResultListDTO.getCode(), similarGoodsResultListDTO.getMsg(), similarGoodsResponseVO);
    }
}
