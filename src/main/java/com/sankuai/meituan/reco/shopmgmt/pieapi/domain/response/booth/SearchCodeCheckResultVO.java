package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.booth;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/10/15 17:38
 * Description: 摊位检索码校验结果VO
 */
@TypeDoc(
        description = "摊位检索码校验结果VO",
        authors = {
                "liyang176"
        },
        version = "V1.0"
)
@Data
public class SearchCodeCheckResultVO {

    @FieldDoc(
            description = "是否检查通过"
    )
    private Boolean isPass;

    @FieldDoc(
            description = "检查未通过时的错误信息"
    )
    private String errorMsg;
}
