package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.management;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @createTime 2019/11/19
 * @description 拣货首页聚合数据
 */
@TypeDoc(
        description = "拣货首页聚合数据"
)
@Data
@ApiModel("拣货首页聚合数据")
public class ManagementPendingTaskVO {

    // 今日拣货数据

    @FieldDoc(
            description = "整单用时(秒)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "整单用时(秒)", required = true)
    @NotNull
    private Integer pickUseSeconds;

    @FieldDoc(
            description = "整单超时率(万分比)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "整单超时率(万分比)", required = true)
    private Integer pickOvertimeRate;

    // 缺货订单

    @FieldDoc(
            description = "缺货订单待处理数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "缺货订单待处理数量", required = true)
    private Integer lackStockPendingCount;

    @FieldDoc(
            description = "缺货订单已处理数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "缺货订单已处理数量", required = true)
    private Integer lackStockHandledCount;

    // 备货订单

    @FieldDoc(
            description = "备货订单待备货数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "备货订单待备货数量", required = true)
    private Integer stockUpPendingCount;

    @FieldDoc(
            description = "备货订单待取货数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "备货订单待取货数量", required = true)
    private Integer stockUpToTakeCount;

    @FieldDoc(
            description = "备货订单已备货数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "备货订单已备货数量", required = true)
    private Integer stockUpDoneCount;

    // 拣货订单

    @FieldDoc(
            description = "拣货订单待领取数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "拣货订单待领取数量", required = true)
    private Integer pickUnclaimedCount;

    @FieldDoc(
            description = "拣货订单待拣货数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "拣货订单待拣货数量", required = true)
    private Integer toPickCount;

    @FieldDoc(
            description = "拣货订单已拣货数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "拣货订单已拣货数量", required = true)
    private Integer pickedCount;

    // 合流订单

    @FieldDoc(
            description = "合流订单待合流数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "合流订单待合流数量", required = true)
    private Integer toMergeCount;

    @FieldDoc(
            description = "合流订单已合流数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "合流订单已合流数量", required = true)
    private Integer mergedCount;

}
