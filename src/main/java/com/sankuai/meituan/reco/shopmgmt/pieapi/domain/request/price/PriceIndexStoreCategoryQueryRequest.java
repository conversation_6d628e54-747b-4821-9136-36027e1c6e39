package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.price;
// Copyright (C) 2020 Meituan
// All rights reserved

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: <EMAIL>
 * @class: MessageTaskQueryRequest
 * @date: 2020-03-13 18:08:46
 * @desc:
 */
@Data
@ApiModel(
        "门店品类价格指数查询接口"
)
@TypeDoc(
        description = "消息任务模块查询任务请求"
)
public class PriceIndexStoreCategoryQueryRequest {

    @FieldDoc(
            description = "门店id"
    )
    @ApiModelProperty("门店Id")
    private Long storeId;
}
