package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.assistant;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/7/26 20:59
 */
@Data
public class SpuTaskPageVo {
    @FieldDoc(
            description = "任务信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "任务信息", required = true)
    private List<SpuTaskVo> taskInfo;

    @FieldDoc(
            description = "分页信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "分页信息", required = true)
    private PageInfoVO pageInfo;
}
