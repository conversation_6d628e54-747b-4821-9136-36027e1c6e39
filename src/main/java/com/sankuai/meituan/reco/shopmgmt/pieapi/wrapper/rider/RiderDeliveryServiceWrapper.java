package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.rider;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import javax.annotation.Nullable;
import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.google.common.collect.Maps;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.meituan.shangou.saas.common.enums.OrderCanOperateItem;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.*;
import com.meituan.shangou.saas.order.management.client.utils.param.DesensitizeReceiverBaseInfoParam;
import com.meituan.shangou.saas.order.management.client.utils.result.DesensitizeReceiverInfoResult;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.meituan.shangou.saas.utils.AddressSceneConvertUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.delivery.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.locating.BatchPostLocatingLogRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.locating.PostLocatingExceptionRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.locating.RiderLocatingLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.delivery.DeliveryCompleteConfigVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.delivery.PrivacyNumberCallRecordVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.ParsedPropertiesVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.exception.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.exception.DeliveryExceptionSummaryVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.UserTagTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.osw.OSWServiceWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.pick.FulfillmentOrderServiceWrapper;
import com.sankuai.meituan.shangou.empower.rider.client.enums.RiderDeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request.*;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request.RiderArrivalLocationTRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request.RiderImmediatelyDeliveryTRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request.RiderLocationRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDeliveryException;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.*;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.tsp.privacy.thrift.iface.privacyphone.model.response.PrivateNumberCallBill;
import com.sankuai.qnh.ofc.ofw.client.thrift.dto.order.CombinationProductItemDTO;
import com.sankuai.qnh.ofc.ofw.client.thrift.dto.order.FulfillmentOrderDetailDTO;
import com.sankuai.qnh.ofc.ofw.client.thrift.dto.order.FulfillmentOrderItemDetailDTO;
import com.sankuai.qnh.ofc.ofw.client.thrift.request.order.ChannelOrderIdKeyReq;
import com.sankuai.sgfnqnh.finance.tax.thrift.dto.ShopInfo;
import com.sankuai.sgfnqnh.finance.tax.thrift.dto.response.ResultResponse;
import com.sankuai.sgfnqnh.finance.tax.thrift.dto.response.ShopInfoQueryResponse;
import com.sankuai.sgfnqnh.finance.tax.thrift.service.InvoiceQueryThriftService;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.infra.osw.api.poi.dto.response.BusinessPoiDTO;
import com.sankuai.shangou.logistics.delivery.poi.SelfDeliveryPoiConfigThriftService;
import com.sankuai.shangou.logistics.delivery.poi.dto.SelfDeliveryPoiConfigDTO;
import com.sankuai.shangou.logistics.delivery.questionnaire.dto.DeliveryQuestionnaireDTO;
import com.sankuai.shangou.logistics.warehouse.TradeShippingOrderService;
import com.sankuai.shangou.logistics.warehouse.dto.AbnOrderDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.stereotype.Service;

import com.dianping.cat.util.MetricHelper;
import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OCMSListViewIdConditionRequest;
import com.meituan.shangou.saas.order.management.client.dto.request.online.ViewIdCondition;
import com.meituan.shangou.saas.order.management.client.dto.request.revenue.MerchantOrderRevenueDetailRequest;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OCMSListViewIdConditionResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.revenue.MerchantOrderListRevenueDetailResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.revenue.OrderAmountInfo;
import com.meituan.shangou.saas.order.management.client.dto.response.revenue.OrderRevenueDetailResponse;
import com.meituan.shangou.saas.order.management.client.enums.SortByEnum;
import com.meituan.shangou.saas.order.management.client.enums.SortFieldEnum;
import com.meituan.shangou.saas.order.management.client.enums.StatusCodeEnum;
import com.meituan.shangou.saas.order.management.client.service.online.OCMSQueryThriftService;
import com.meituan.shangou.saas.order.management.client.service.revenue.MerChantRevenueQueryService;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.GiftVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.ProductVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.RevenueDetailVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.TagInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.delivery.RiderDeliveryOrderListResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.delivery.RiderDeliveryOrderVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.FallbackException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.delivery.TmsDeliveryStatusDesc;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.DeliveryOrderType;
import com.sankuai.meituan.shangou.empower.rider.client.common.ResponseCodeEnum;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.RiderOperateThriftService;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.response.RiderOperateTResponse;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.RiderQueryThriftService;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TReceiver;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDeliveryOrder;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;

import lombok.extern.slf4j.Slf4j;

import static com.sankuai.meituan.reco.shopmgmt.pieapi.constants.RiderDeliveryOrderExtInfoType.*;
import static com.meituan.shangou.saas.order.management.client.utils.DesensitizeReceiverInfoUtil.desensitizeReceiverInfo;

/**
 * 骑手配送相关服务.
 *
 * <AUTHOR>
 * @since 2021/6/11 17:09
 */
@Service
@Slf4j
@Rhino
public class RiderDeliveryServiceWrapper {

    @Resource
    private RiderOperateThriftService riderOperateRpcService;

    @Resource
    private RiderQueryThriftService riderQueryRpcService;

    @Resource
    private OCMSQueryThriftService ocmsQueryThriftService;

    @Resource
    private AuthThriftWrapper authThriftWrapper;

    @Autowired
    private MerChantRevenueQueryService merChantRevenueQueryService;
    @Resource
    private SelfDeliveryPoiConfigThriftService selfDeliveryPoiConfigThriftService;
    @Resource
    private DeliveryQuestionnaireWrapper deliveryQuestionnaireWrapper;
    @Resource
    private AbnOrderServiceWrapper abnOrderServiceWrapper;

    @Resource
    private OSWServiceWrapper oswServiceWrapper;

    @Resource
    private PrivacyNumberWrapper privacyNumberWrapper;

    @Resource
    private InvoiceQueryThriftService invoiceQueryThriftService;

    @Resource
    private SupplyProductTagWrapper supplyProductTagWrapper;

    @Resource
    private FulfillmentOrderServiceWrapper fulfillmentOrderServiceWrapper;

    @Resource
    private TenantWrapper tenantWrapper;

    @Resource
    private TradeShippingOrderService tradeShippingOrderService;

    // 履约标签
    private static final int FULFILLMENT_TAG = 1;
    // 拣货标签
    private static final int PICK_TAG = 2;
    //支付时间默认查询间隔 单位:月
    private static final int DEFAULT_PAY_TIME_QUERY_DURATION = 3;

    @Degrade(rhinoKey = "RiderDeliveryServiceWrapper-accept",
            fallBackMethod = "acceptFallback",
            timeoutInMilliseconds = 2000)
    @CatTransaction
    public CommonResponse<Void> accept(RiderOperateRequest request) {

        RiderOperateTRequest rpcRequest = convertToTRequest(request);
        log.info("RiderDeliveryServiceWrapper-accept start, req={}", rpcRequest);
        RiderOperateTResponse tResponse = riderOperateRpcService.accept(rpcRequest);
        log.info("RiderDeliveryServiceWrapper-accept end, req={}, res={}", rpcRequest, tResponse);
        if (!Objects.equals(tResponse.getStatus().getCode(), ResultCode.SUCCESS.getCode())) {
            return CommonResponse.fail(tResponse.getStatus().getCode(), tResponse.getStatus().getMsg());
        }

        //领取拣货单
        return tryToAcceptPickTask(request);

    }

    private CommonResponse<Void> tryToAcceptPickTask(RiderOperateRequest request) {
        try {
            Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
            Long storeId = request.getStoreId();
            if (MccConfigUtil.isDrunkHorseTenant(tenantId) && MccConfigUtil.acceptPickOrderSwitch(storeId)) {
                if(Objects.isNull(request.getAcceptType()) || Objects.equals(request.getAcceptType(), 1)) {
                    String channelOrderId ;
                    Integer orderBizType ;
                    //如果前端传了,直接用前端传的
                    if (StringUtils.isBlank(request.getChannelOrderId()) || Objects.isNull(request.getChannelId())) {
                        QueryDeliveryOrderByIdListRequest queryByIdReq = new QueryDeliveryOrderByIdListRequest();
                        queryByIdReq.setTenantId(tenantId);
                        queryByIdReq.setStoreId(storeId);
                        queryByIdReq.setNeedReturnPricingRouteInfo(false);
                        queryByIdReq.setDeliveryOrderIds(Collections.singletonList(request.getDeliveryOrderId()));
                        BatchQueryDeliveryOrderResponse queryDeliveryOrderByIdsResp = riderQueryRpcService.queryDeliveryOrderByIds(queryByIdReq);
                        log.info("invoke riderQueryRpcService.queryDeliveryOrderByIds end, request: {}, response: {}", JSON.toJSONString(queryByIdReq), JSON.toJSONString(queryDeliveryOrderByIdsResp));
                        TRiderDeliveryOrder tRiderDeliveryOrder = queryDeliveryOrderByIdsResp.getTRiderDeliveryOrders().get(0);
                        channelOrderId = tRiderDeliveryOrder.getChannelOrderId();
                        orderBizType = tRiderDeliveryOrder.getOrderBizTypeCode();
                    } else {
                        channelOrderId = request.getChannelOrderId();
                        orderBizType = ChannelOrderConvertUtils.convertBizType(request.getChannelId());
                    }

                    TResult<Void> tResult = RetryTemplateUtil.simpleWithFixedRetry(3, 100).execute(new RetryCallback<TResult<Void>, Exception>() {
                        @Override
                        public TResult<Void> doWithRetry(RetryContext context) throws Exception {
                            return tradeShippingOrderService.startShipByTradeOrderNo(storeId,
                                    channelOrderId, orderBizType,
                                    ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId(),
                                    ApiMethodParamThreadLocal.getIdentityInfo().getUser().getOperatorName());
                        }
                    });

                    if (!tResult.isSuccess()) {
                        if(Objects.equals(tResult.getCode(), com.sankuai.shangou.logistics.warehouse.enums.ResponseCodeEnum.SHIP_TASK_ALREADY_BE_ACCEPTED.getCode())) {
                            log.warn("拣货任务已被领取");
                            Cat.logEvent("PICK_DELIVERY_SPLIT", "PICK_TASK_ALREADY_BE_ACCEPTED");
                            return new CommonResponse<>(ResultCode.PICK_TASK_ALREADY_BE_ACCEPTED.getCode(), ResultCode.PICK_TASK_ALREADY_BE_ACCEPTED.getErrorMessage(), null);
                        } else {
                            throw new BizException("拣货任务领取失败," + tResult.getMsg());
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("拣货单领取失败", e);
            Cat.logEvent("PICK_DELIVERY_SPLIT", "PICK_TASK_ACCEPT_FAIL");
        }

        return CommonResponse.success(null);
    }

    private void checkResponse(RiderOperateTResponse tResponse) {
        if (tResponse == null || tResponse.getStatus() == null) {
            throw new CommonRuntimeException(ResponseCodeEnum.FAILED.name(), ResultCode.FAIL);
        }
        if (tResponse.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
            throw new CommonRuntimeException(tResponse.getStatus().getMsg(), ResultCode.FAIL);
        }
    }

    private RiderOperateTRequest convertToTRequest(RiderOperateRequest request) {
        RiderOperateTRequest tRequest = new RiderOperateTRequest();
        tRequest.setDeliveryOrderId(request.getDeliveryOrderId());
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        if (identityInfo == null || identityInfo.getUser() == null) {
            log.error("请求人信息不完整");
            throw new CommonRuntimeException(ResultCode.PARAM_ERR);
        }

        tRequest.setTenantId(identityInfo.getUser().getTenantId());
        tRequest.setStoreId(request.getStoreId());
        tRequest.setLongitude(request.getLongitude());
        tRequest.setLatitude(request.getLatitude());
        tRequest.setOperatorId(identityInfo.getUser().getAccountId());
        tRequest.setOperatorName(identityInfo.getUser().getOperatorName());
        tRequest.setOperatorPhone(identityInfo.getUser().getOperatorPhone());
        if(request.getLocationInfo() != null) {
            tRequest.setLongitude(request.getLocationInfo().getLongitude());
            tRequest.setLatitude(request.getLocationInfo().getLatitude());
        }
        return tRequest;
    }

    private PageQueryDeliveryExceptionRequest convertToTRequest(QueryDeliveryExceptionListRequest request) {

        PageQueryDeliveryExceptionRequest tRequest = new PageQueryDeliveryExceptionRequest();
        tRequest.setPageNum(request.getPageNum());
        tRequest.setPageSize(request.getPageSize());
        tRequest.setExceptionTypeList(request.getExceptionTypeList());

        if (CollectionUtils.isNotEmpty(request.getChannelIds())) {
            List<Integer> orderBizTypes = request.getChannelIds().stream()
                    .map(channel -> DynamicOrderBizType.findOf(ChannelOrderConvertUtils.sourceMid2Biz(channel)))
                    .filter(Objects::nonNull)
                    .map(DynamicOrderBizType::getValue)
                    .collect(Collectors.toList());
            tRequest.setOrderBizTypeList(orderBizTypes);
        }

        //默认筛选三个月内的异常
        if (request.getReportStartTimeStamp() == null || request.getReportEndTimeStamp() == null) {
            tRequest.setReportTimeStartTimeStamp(TimeUtils.localDateTimeToMills(LocalDateTime.now().minusMonths(DEFAULT_PAY_TIME_QUERY_DURATION)));
            tRequest.setReportTimeEndTimeStamp(System.currentTimeMillis());
        } else {
            tRequest.setReportTimeStartTimeStamp(request.getReportStartTimeStamp());
            tRequest.setReportTimeEndTimeStamp(request.getReportEndTimeStamp());
        }

        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        if (identityInfo == null || identityInfo.getUser() == null) {
            log.error("请求人信息不完整");
            throw new CommonRuntimeException(ResultCode.PARAM_ERR);
        }
        if (identityInfo.getStoreId() == null) {
            log.error("门店id为空");
            throw new CommonRuntimeException(ResultCode.PARAM_ERR);
        }
        tRequest.setTenantId(identityInfo.getUser().getTenantId());
        tRequest.setRiderAccountId(identityInfo.getUser().getAccountId());
        tRequest.setStoreIds(Collections.singletonList(identityInfo.getStoreId()));

        return tRequest;
    }

    private RiderDeliveryExceptionTRequest convertToTRequest(DeliveryExceptionReportRequest request, OCMSOrderVO ocmsOrderVO) {
        RiderDeliveryExceptionTRequest tRequest = new RiderDeliveryExceptionTRequest();

        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        if (identityInfo == null || identityInfo.getUser() == null) {
            log.error("请求人信息不完整");
            throw new CommonRuntimeException(ResultCode.PARAM_ERR);
        }
        tRequest.setStoreId(ApiMethodParamThreadLocal.getIdentityInfo().getStoreId());
        tRequest.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        tRequest.setRiderAccountId(identityInfo.getUser().getAccountId());
        tRequest.setRiderAccountName(identityInfo.getUser().getOperatorName());

        tRequest.setChannelOrderId(request.getChannelOrderId());
        tRequest.setDeliveryOrderId(request.getDeliveryOrderId());
        tRequest.setExceptionType(request.getExceptionType());
        tRequest.setExceptionSubType(request.getExceptionSubType());
        tRequest.setPicUrls(request.getPicUrls());
        tRequest.setUserRealAddress(request.getUserRealAddress());
        tRequest.setComment(request.getComment());
        tRequest.setOrderBizType(ChannelOrderConvertUtils.convertBizType(request.getChannelId()));
        tRequest.setModifiedAddress(request.getModifiedAddress());

        tRequest.setDaySeq(ocmsOrderVO.getOrderSerialNumber().intValue());
        tRequest.setPayTime(ocmsOrderVO.getPayTime());

        return tRequest;
    }

    private RiderLocationRequest convertToLocationRequest(RiderLocationSyncRequest request){
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        if (identityInfo == null || identityInfo.getUser() == null) {
            log.error("请求人信息不完整");
            throw new CommonRuntimeException(ResultCode.PARAM_ERR);
        }
        RiderLocationRequest locationRequest=new RiderLocationRequest();
        locationRequest.setTenantId(identityInfo.getUser().getTenantId());
        locationRequest.setStoreId(request.getStoreId());
        locationRequest.setRiderAccountId(identityInfo.getUser().getAccountId());
        locationRequest.setRiderName(identityInfo.getUser().getAccountName());
        if(StringUtils.isEmpty(locationRequest.getRiderName())){
            locationRequest.setRiderName(identityInfo.getUser().getOperatorName());
        }
        locationRequest.setRiderPhone(identityInfo.getUser().getOperatorPhone());
        locationRequest.setAccuracy(request.getAccuracy());
        locationRequest.setBearing(request.getBearing());
        locationRequest.setLatitude(request.getLatitude());
        locationRequest.setLongitude(request.getLongitude());
        locationRequest.setProvider(request.getProvider());
        locationRequest.setSpeed(request.getSpeed());
        locationRequest.setTime(request.getTime());
        try {
            if(Objects.nonNull(ApiMethodParamThreadLocal.getInstance()) && Objects.nonNull(ApiMethodParamThreadLocal.getInstance().get())) {
                locationRequest.setOs(ApiMethodParamThreadLocal.getInstance().get().getOs());
                locationRequest.setAppVersion(ApiMethodParamThreadLocal.getInstance().get().getAppVersion());
                locationRequest.setUuid(ApiMethodParamThreadLocal.getInstance().get().getUuid());
            }
        } catch (Exception e) {
            log.error("获取手机os失败", e);
        }

        return locationRequest;
    }

    private RiderPostDeliveryProofPhotoTRequest convertToTRequest(PostDeliveryProofPhotoRequest request) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        if (identityInfo == null || identityInfo.getUser() == null) {
            log.error("请求人信息不完整");
            throw new CommonRuntimeException(ResultCode.PARAM_ERR);
        }
        RiderPostDeliveryProofPhotoTRequest tRequest = new RiderPostDeliveryProofPhotoTRequest();
        tRequest.setDeliveryOrderId(request.getDeliveryOrderId());
        tRequest.setOperatorAccountId(identityInfo.getUser().getAccountId());
        tRequest.setDeliveryProofPhotoUrls(request.getDeliveryProofPhotoList());
        tRequest.setRiderIP(RequestContextUtils.getClientIp());
        tRequest.setTenantId(identityInfo.getUser().getTenantId());
        tRequest.setStoreId(identityInfo.getStoreId());
        return tRequest;
    }

    @Degrade(rhinoKey = "RiderDeliveryServiceWrapper-takeAway",
            fallBackMethod = "takeAwayFallback",
            timeoutInMilliseconds = 2000)
    @CatTransaction
    public void takeAway(RiderOperateRequest request) {

        RiderOperateTRequest rpcRequest = convertToTRequest(request);
        log.info("RiderDeliveryServiceWrapper-takeAway start, req={}", rpcRequest);
        RiderOperateTResponse tResponse = null;
        try {
            tResponse = riderOperateRpcService.takeAway(rpcRequest);
            log.info("RiderDeliveryServiceWrapper-takeAway end, req={}, res={}", rpcRequest, tResponse);
            checkResponse(tResponse);
        } catch (Exception e) {
            log.error("RiderDeliveryServiceWrapper-takeAway exception, req={}", rpcRequest, e);
            if (e instanceof CommonRuntimeException) {
                throw e;
            } else {
                throw new CommonRuntimeException(ResultCode.OPERATING_FAIL.defaultMessage);
            }
        }
    }

    @Degrade(rhinoKey = "RiderDeliveryServiceWrapper-changeDeliveryStatusLock",
            fallBackMethod = "changeDeliveryStatusLockFallback",
            timeoutInMilliseconds = 2000)
    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public CommonResponse<Void> changeDeliveryStatusLock(RiderOperateRequest request, int changeType) {

        RiderOperateTRequest rpcRequest = convertToTRequest(request);
        RiderOperateTResponse tResponse;
        if (changeType == 0) {
            tResponse = riderOperateRpcService.unlockDeliveryStatus(rpcRequest);
        } else {
            tResponse = riderOperateRpcService.lockDeliveryStatus(rpcRequest);
        }
        if (!Objects.equals(tResponse.getStatus().getCode(), ResultCode.FAIL.getCode())) {
            return CommonResponse.fail(tResponse.getStatus().getCode(), tResponse.getStatus().getMsg());
        }

        return CommonResponse.success(null);
    }

    @Degrade(rhinoKey = "RiderDeliveryServiceWrapper-complete",
            fallBackMethod = "completeFallback",
            timeoutInMilliseconds = 2000)
    @CatTransaction
    public void complete(RiderOperateRequest request) {

        RiderOperateTRequest rpcRequest = convertToTRequest(request);
        log.info("RiderDeliveryServiceWrapper-complete start, req={}", rpcRequest);
        RiderOperateTResponse tResponse = null;
        try {
            tResponse = riderOperateRpcService.complete(rpcRequest);
            log.info("RiderDeliveryServiceWrapper-complete end, req={}, res={}", rpcRequest, tResponse);
            checkResponse(tResponse);
        } catch (Exception e) {
            log.error("RiderDeliveryServiceWrapper-complete exception, req={}", rpcRequest, e);
            if (e instanceof CommonRuntimeException) {
                throw e;
            } else {
                throw new CommonRuntimeException(ResultCode.OPERATING_FAIL.defaultMessage);
            }
        }
    }


    public CommonResponse<Void> completeWithProofPhoto(CompleteWithProofPhotoRequest request) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        if (identityInfo == null || identityInfo.getUser() == null) {
            log.error("请求人信息不完整");
            throw new CommonRuntimeException(ResultCode.PARAM_ERR);
        }
        CompleteWithProofPhotoTRequest tRequest = new CompleteWithProofPhotoTRequest();
        tRequest.setDeliveryOrderId(request.getDeliveryOrderId());
        tRequest.setOperatorId(identityInfo.getUser().getAccountId());
        tRequest.setProofPhotoList(request.getPicList());
        tRequest.setOperatorName(identityInfo.getUser().getOperatorName());
        tRequest.setOperatorPhone(identityInfo.getUser().getOperatorPhone());
        tRequest.setSignType(request.getSignType());
        tRequest.setTenantId(identityInfo.getUser().getTenantId());
        tRequest.setIsWeakNetWork(request.getIsWeakNetwork());
        if (request.getLocationInfo() != null) {
            tRequest.setLongitude(request.getLocationInfo().getLongitude());
            tRequest.setLatitude(request.getLocationInfo().getLatitude());
        }
        log.info("start invoke riderOperateRpcService.completeWithProofPhoto, request: {}", request);
        RiderOperateTResponse response = riderOperateRpcService.completeWithProofPhoto(tRequest);
        log.info("end invoke riderOperateRpcService.completeWithProofPhoto, response: {}", response);

        if (!Objects.equals(response.getStatus().getCode(), ResultCode.SUCCESS.code)) {
            return CommonResponse.fail(response.getStatus().getCode(), response.getStatus().getMsg());
        }

        return CommonResponse.success(null);
    }

    @Degrade(rhinoKey = "RiderDeliveryServiceWrapper-reportException",
            fallBackMethod = "reportExceptionFallback",
            timeoutInMilliseconds = 3000)
    @CatTransaction
    public void reportException(DeliveryExceptionReportRequest request) {
        RiderOperateTResponse tResponse = null;

        OCMSOrderVO ocmsOrderVO = getOCMSOrderVO(request.getChannelOrderId().toString(), request.getChannelId());

        RiderDeliveryExceptionTRequest rpcRequest = convertToTRequest(request, ocmsOrderVO);
        try {
            log.info("RiderDeliveryServiceWrapper.reportException start, req={}", rpcRequest);
            tResponse = riderOperateRpcService.reportDeliveryException(rpcRequest);
            log.info("RiderDeliveryServiceWrapper.reportException end, req={}, res={}", rpcRequest, tResponse);
            checkResponse(tResponse);
        } catch (Exception e) {
            log.error("RiderDeliveryServiceWrapper.reportException exception, req={}", rpcRequest, e);
            if (e instanceof CommonRuntimeException) {
                throw e;
            } else {
                throw new CommonRuntimeException("上报失败,请重试", ResultCode.FAIL);
            }
        }
    }

    private OCMSOrderVO getOCMSOrderVO(String channelOrderId, Integer channelId) {
        OCMSListViewIdConditionRequest viewIdConditionRequest = new OCMSListViewIdConditionRequest();
        viewIdConditionRequest.setViewIdConditionList(Collections.singletonList(new ViewIdCondition(ChannelOrderConvertUtils.convertBizType(channelId),channelOrderId)));
        OCMSListViewIdConditionResponse response = null;
        try {
            log.info("RiderDeliveryServiceWrapper call OcmsQueryThriftService.queryOrderByViewIdCondition request:{}", viewIdConditionRequest);
            response = ocmsQueryThriftService.queryOrderByViewIdCondition(viewIdConditionRequest);
            log.info("RiderDeliveryServiceWrapper call ocmsQueryThriftService.queryOrderByViewIdCondition response:{}", response);
        } catch (Exception e) {
            log.error("RiderDeliveryServiceWrapper call ocmsQueryThriftService.queryOrderByViewIdCondition error.requset:{},response:{}", viewIdConditionRequest, response);
            throw new CommonRuntimeException("查询订单详情失败", ResultCode.FAIL);
        }
        if (response == null || response.getStatus() == null || response.getStatus().getCode() != StatusCodeEnum.SUCCESS.getCode()) {
            log.error("RiderDeliveryServiceWrapper call ocmsQueryThriftService.queryOrderByViewIdCondition error.request:{},response:{}", viewIdConditionRequest, response);
            throw new CommonRuntimeException("查询订单详情失败", ResultCode.FAIL);
        }

        if(CollectionUtils.isEmpty(response.getOcmsOrderList())) {
            throw new CommonRuntimeException("订单不存在", ResultCode.FAIL);
        }

        return response.getOcmsOrderList().get(0);
    }

    @Degrade(rhinoKey = "RiderDeliveryServiceWrapper-queryExceptionList",
            fallBackMethod = "queryExceptionListFallback",
            timeoutInMilliseconds = 2000)
    @CatTransaction
    public CommonResponse<QueryDeliveryExceptionListResponse> queryExceptionList(QueryDeliveryExceptionListRequest request) {
        PageQueryDeliveryExceptionRequest rpcRequest = convertToTRequest(request);
        PageQueryDeliveryExceptionListResponse tResponse = null;
        try {
            log.info("RiderDeliveryServiceWrapper.queryExceptionList start, req={}", rpcRequest);
            tResponse = riderQueryRpcService.pageQueryDeliveryExceptionList(rpcRequest);
            log.info("RiderDeliveryServiceWrapper.queryExceptionList end, req={}, res={}", rpcRequest, tResponse);
        } catch (Exception e) {
            log.error("RiderDeliveryServiceWrapper.queryExceptionList exception, req={}", rpcRequest, e);
            throw new CommonRuntimeException("查询配送异常失败", ResultCode.FAIL);
        }

        if (tResponse == null || tResponse.getStatus() == null || tResponse.getPageInfo() == null) {
            log.error("RiderDeliveryServiceWrapper.queryExceptionList error, req:{}, resp:{}", rpcRequest,tResponse);
            throw new CommonRuntimeException("查询配送异常失败", ResultCode.FAIL);
        }

        if (tResponse.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
            log.warn("RiderDeliveryServiceWrapper.queryExceptionList fail, req:{}, resp:{}", rpcRequest, tResponse);
            throw new CommonRuntimeException(tResponse.getStatus().getMsg(), ResultCode.FAIL);
        }

        return CommonResponse.success(buildQueryDeliveryExceptionListResponse(tResponse));
    }

    @Degrade(rhinoKey = "RiderDeliveryServiceWrapper-queryExceptionDetail",
            fallBackMethod = "queryExceptionDetailFallback",
            timeoutInMilliseconds = 2000)
    @CatTransaction
    public CommonResponse<QueryDeliveryExceptionDetailListResponse> queryExceptionDetail(String channelOrderId, Integer channelId) {
        QueryDeliveryExceptionByChannelOrderRequest request = new QueryDeliveryExceptionByChannelOrderRequest();
        if(ApiMethodParamThreadLocal.getIdentityInfo() == null || ApiMethodParamThreadLocal.getIdentityInfo().getUser() == null) {
            log.error("请求人信息不完整");
            throw new CommonRuntimeException(ResultCode.PARAM_ERR);
        }

        request.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        request.setStoreId(ApiMethodParamThreadLocal.getIdentityInfo().getStoreId());
        Integer orderBizType = ChannelOrderConvertUtils.convertBizType(channelId);
        request.setChannelOrderInfoList(Collections.singletonList(new QueryDeliveryExceptionByChannelOrderRequest.ChannelOrderInfo(channelOrderId, orderBizType)));

        DeliveryExceptionResponse tResponse = null;

        try {
            log.info("RiderDeliveryServiceWrapper.queryExceptionList start, req={}", request);
            tResponse = riderQueryRpcService.queryDeliveryExceptionByChannelOrder(request);
            log.info("RiderDeliveryServiceWrapper.queryExceptionList end, req={}, res={}", request, tResponse);
        } catch (Exception e) {
            log.error("RiderDeliveryServiceWrapper.queryExceptionList exception, req={}", request, e);
            throw new CommonRuntimeException(ResultCode.OPERATING_FAIL.defaultMessage);
        }

        if (tResponse == null || tResponse.getStatus() == null ) {
            log.error("RiderDeliveryServiceWrapper.queryExceptionList error, req:{}, resp:{}", request,tResponse);
            throw new CommonRuntimeException("查询配送异常失败", ResultCode.FAIL);
        }

        if (tResponse.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
            log.warn("RiderDeliveryServiceWrapper.queryExceptionList fail, req:{}, resp:{}", request, tResponse);
            throw new CommonRuntimeException(tResponse.getStatus().getMsg(), ResultCode.FAIL);
        }
        return CommonResponse.success(buildQueryDeliveryExceptionDetailListResponse(tResponse));
    }


    public void location(RiderLocationSyncRequest request){
        if(request==null){
            throw new CommonLogicException(ResultCode.PARAM_ERR);
        }
        RiderLocationRequest locationRequest= convertToLocationRequest(request);
        RiderOperateTResponse tResponse = null;
        try {
            tResponse = riderOperateRpcService.riderLocation(locationRequest);
            log.info("RiderDeliveryServiceWrapper-location end, req={}, res={}", locationRequest, tResponse);
            checkResponse(tResponse);
        } catch (Exception e) {
            log.error("RiderDeliveryServiceWrapper-location exception, req={}", locationRequest, e);
            if (e instanceof CommonRuntimeException) {
                throw new CommonLogicException(((CommonRuntimeException) e).getResultCode(),e.getMessage());
            } else {
                throw new CommonRuntimeException(ResultCode.OPERATING_FAIL.defaultMessage);
            }
        }
    }

    private CommonResponse<Void> changeDeliveryStatusLockFallback(RiderOperateRequest request, int changeType) {
        log.error("changeDeliveryStatusLockFallback {} {}", request, changeType);
        throw new FallbackException("RiderDeliveryServiceWrapper-changeDeliveryStatusLock fallback");
    }

    private CommonResponse<Void> acceptFallback(RiderOperateRequest request, Throwable t) {
        log.error("acceptFallback {} {}", request, t.getMessage(), t);
        throw new FallbackException("RiderDeliveryServiceWrapper-accept fallback");
    }

    private void takeAwayFallback(RiderOperateRequest request, Throwable t) {
        log.error("takeAwayFallback {} {}", request, t.getMessage(), t);
        throw new FallbackException("RiderDeliveryServiceWrapper-takeAway fallback");
    }

    private void completeFallback(RiderOperateRequest request, Throwable t) {
        log.error("completeFallback {} {}", request, t.getMessage(), t);
        throw new FallbackException("RiderDeliveryServiceWrapper-complete fallback");
    }

    private void reportExceptionFallback(DeliveryExceptionReportRequest request) {
        log.error("reportExceptionFallback {}", request);
        throw new FallbackException("RiderDeliveryServiceWrapper-reportException fallback");
    }

    private void queryExceptionListFallback(QueryDeliveryExceptionListRequest request) {
        log.error("queryExceptionListFallback {}", request);
        throw new FallbackException("RiderDeliveryServiceWrapper-queryExceptionList fallback");
    }


    private void queryExceptionDetailFallback(String channelOrderId, Integer orderBizType) {
        log.error("queryExceptionDetailFallback {}",channelOrderId);
        throw new FallbackException("RiderDeliveryServiceWrapper-queryExceptionDetail fallback");
    }


    @Degrade(rhinoKey = "RiderDeliveryServiceWrapper.queryWaitGetOrder",
            fallBackMethod = "queryWaitGetOrderFallback",
            isDegradeOnException = true,
            timeoutInMilliseconds = 5000)
    public CommonResponse<RiderDeliveryOrderListResponse> queryWaitGetOrder(QueryRiderWaitToGetOrderRequest request) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        List<Long> storeIdList = identityInfo.getStoreIdList();
        long tenantId = identityInfo.getUser().getTenantId();
        if (CollectionUtils.isEmpty(storeIdList) || storeIdList.size() > 1) {
            log.error("骑手订单展示只支持单门店/仓模式：storeIdList={}", storeIdList);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "只支持单门店/仓模式");
        }
        PageQueryDeliveryOrderRequest tRequest = buildQueryWaitGetRequest(request, tenantId, storeIdList.get(0));
        return queryRiderOrderTemplate(tenantId, tRequest);
    }

    private CommonResponse<RiderDeliveryOrderListResponse> queryWaitGetOrderFallback(QueryRiderWaitToGetOrderRequest request, Throwable t) {
        log.error("queryWaitGetOrderFallback {} {}", request, t.getMessage(), t);
        throw new FallbackException("RiderDeliveryServiceWrapper-queryWaitGetOrder fallback");
    }

    /**
     * 构造"查询骑手可领取运单"的请求.
     *
     * @param request  请求
     * @param tenantId 租户 ID
     * @param storeId  门店 ID
     * @return PageQueryDeliveryOrderRequest
     */
    private PageQueryDeliveryOrderRequest buildQueryWaitGetRequest(QueryRiderWaitToGetOrderRequest request, long tenantId, long storeId) {
        PageQueryDeliveryOrderRequest tRequest = new PageQueryDeliveryOrderRequest();
        tRequest.setPage(request.getPage());
        tRequest.setPageSize(request.getSize());
        tRequest.setTenantId(tenantId);
        tRequest.setStoreId(storeId);
        tRequest.setDeliveryStatus(TmsDeliveryStatusDesc.WAITING_TO_ASSIGN_RIDER.getCode());
        return tRequest;
    }



    @Degrade(rhinoKey = "RiderDeliveryServiceWrapper.queryWaitTakeGoodsOrder",
            fallBackMethod = "queryWaitTakeGoodsOrderFallback",
            isDegradeOnException = true,
            timeoutInMilliseconds = 5000)
    public CommonResponse<RiderDeliveryOrderListResponse> queryWaitTakeGoodsOrder(QueryRiderWaitToTakeGoodsOrderRequest request) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        List<Long> storeIdList = identityInfo.getStoreIdList();
        long tenantId = identityInfo.getUser().getTenantId();
        if (CollectionUtils.isEmpty(storeIdList) || storeIdList.size() > 1) {
            log.error("骑手订单展示只支持单门店/仓模式：storeIdList={}", storeIdList);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "只支持单门店/仓模式");
        }
        long operatorAccountId = identityInfo.getUser().getAccountId();
        PageQueryDeliveryOrderRequest tRequest = buildQueryWaitTakeGoodsRequest(request, tenantId, storeIdList.get(0), operatorAccountId);
        return queryRiderOrderTemplate(tenantId, tRequest);
    }

    private CommonResponse<RiderDeliveryOrderListResponse> queryWaitTakeGoodsOrderFallback(QueryRiderWaitToTakeGoodsOrderRequest request, Throwable t) {
        log.error("queryWaitTakeGoodsOrderFallback {} {}", request, t.getMessage(), t);
        throw new FallbackException("RiderDeliveryServiceWrapper-queryWaitTakeGoodsOrder fallback");
    }

    /**
     * 构造"查询骑手待取货运单"的请求.
     *
     * @param request   请求
     * @param tenantId  租户 ID
     * @param storeId   门店 ID
     * @param accountId 操作人账号 ID
     * @return PageQueryDeliveryOrderRequest
     */
    private PageQueryDeliveryOrderRequest buildQueryWaitTakeGoodsRequest(QueryRiderWaitToTakeGoodsOrderRequest request, long tenantId,
                                                                         long storeId, long accountId) {
        PageQueryDeliveryOrderRequest tRequest = new PageQueryDeliveryOrderRequest();
        tRequest.setPage(request.getPage());
        tRequest.setPageSize(request.getSize());
        tRequest.setTenantId(tenantId);
        tRequest.setStoreId(storeId);
        tRequest.setDeliveryStatus(TmsDeliveryStatusDesc.RIDER_ASSIGNED.getCode());
        tRequest.setRiderAccountId(accountId);
        return tRequest;
    }



    @Degrade(rhinoKey = "RiderDeliveryServiceWrapper.queryInDeliveryOrder",
            fallBackMethod = "queryInDeliveryOrderFallback",
            isDegradeOnException = true,
            timeoutInMilliseconds = 5000)
    public CommonResponse<RiderDeliveryOrderListResponse> queryInDeliveryOrder(QueryRiderInDeliveryOrderRequest request) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        List<Long> storeIdList = identityInfo.getStoreIdList();
        long tenantId = identityInfo.getUser().getTenantId();
        if (CollectionUtils.isEmpty(storeIdList) || storeIdList.size() > 1) {
            log.error("骑手订单展示只支持单门店/仓模式：storeIdList={}", storeIdList);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "只支持单门店/仓模式");
        }
        long operatorAccountId = identityInfo.getUser().getAccountId();
        PageQueryDeliveryOrderRequest tRequest = buildQueryInDeliveryRequest(request, tenantId, storeIdList.get(0), operatorAccountId);
        CommonResponse<RiderDeliveryOrderListResponse> response = queryRiderOrderTemplate(tenantId, tRequest);


        //如果是新版本的拍照送达灰度门店 && 前端拉到了新包 ==> 屏蔽以前的拍照入口
        if (MccConfigUtil.isDeliveryPhotoConfigurableGrayStore(storeIdList.get(0)) && StringUtils.isNotBlank(request.getMrnVersion())) {
            if (response != null && response.getData() != null && CollectionUtils.isNotEmpty(response.getData().getOrderList())) {
                response.getData().getOrderList().forEach(
                        deliveryOrderVo -> deliveryOrderVo.setCouldPostDeliveryProofPhoto(false)
                );
            }
        }

        return response;
    }

    private CommonResponse<RiderDeliveryOrderListResponse> queryInDeliveryOrderFallback(QueryRiderInDeliveryOrderRequest request, Throwable t) {
        log.error("queryInDeliveryOrderFallback {} {}", request, t.getMessage(), t);
        throw new FallbackException("RiderDeliveryServiceWrapper-queryInDeliveryOrder fallback");
    }

    /**
     * 构造"查询骑手配送中运单"的请求.
     *
     * @param request   请求
     * @param tenantId  租户 ID
     * @param storeId   门店 ID
     * @param accountId 操作人账号 ID
     * @return PageQueryDeliveryOrderRequest
     */
    private PageQueryDeliveryOrderRequest buildQueryInDeliveryRequest(QueryRiderInDeliveryOrderRequest request, long tenantId,
                                                                      long storeId, long accountId) {
        PageQueryDeliveryOrderRequest tRequest = new PageQueryDeliveryOrderRequest();
        tRequest.setPage(request.getPage());
        tRequest.setPageSize(request.getSize());
        tRequest.setTenantId(tenantId);
        tRequest.setStoreId(storeId);
        tRequest.setDeliveryStatus(TmsDeliveryStatusDesc.RIDER_TAKEN_GOODS.getCode());
        tRequest.setRiderAccountId(accountId);
        return tRequest;
    }

    private PageQueryCompletedDeliveryOrderRequest buildQueryCompletedRequest(QueryRiderCompletedOrderRequest request, long tenantId,
                                                                     long storeId, long accountId) {
        PageQueryCompletedDeliveryOrderRequest tRequest = new PageQueryCompletedDeliveryOrderRequest();
        tRequest.setPage(request.getPage());
        tRequest.setPageSize(request.getSize());
        tRequest.setTenantId(tenantId);
        tRequest.setStoreId(storeId);
        tRequest.setRiderAccountId(accountId);
        return tRequest;
    }

    @Degrade(rhinoKey = "RiderDeliveryServiceWrapper.queryCompletedOrder",
            fallBackMethod = "queryCompletedOrderFallback",
            isDegradeOnException = true,
            timeoutInMilliseconds = 5000)
    public CommonResponse<RiderDeliveryOrderListResponse> queryCompletedOrder(QueryRiderCompletedOrderRequest request) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        List<Long> storeIdList = identityInfo.getStoreIdList();
        long tenantId = identityInfo.getUser().getTenantId();
        if (CollectionUtils.isEmpty(storeIdList) || storeIdList.size() > 1) {
            log.error("骑手订单展示只支持单门店/仓模式：storeIdList={}", storeIdList);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "只支持单门店/仓模式");
        }
        long operatorAccountId = identityInfo.getUser().getAccountId();
        PageQueryCompletedDeliveryOrderRequest tRequest = buildQueryCompletedRequest(request, tenantId, storeIdList.get(0), operatorAccountId);
        CommonResponse<RiderDeliveryOrderListResponse> response = queryCompletedRiderOrderTemplate(tenantId, tRequest);

        //如果是新版本的拍照送达灰度门店 && 前端拉到了新包 ==> 屏蔽以前的拍照入口
        if (MccConfigUtil.isDeliveryPhotoConfigurableGrayStore(storeIdList.get(0)) && StringUtils.isNotBlank(request.getMrnVersion())) {
            if (response != null && response.getData() != null && CollectionUtils.isNotEmpty(response.getData().getOrderList())) {
                response.getData().getOrderList().forEach(
                        deliveryOrderVo -> deliveryOrderVo.setCouldPostDeliveryProofPhoto(false)
                );
            }
        }

        return response;
    }

    private CommonResponse<RiderDeliveryOrderListResponse> queryCompletedOrderFallback(QueryRiderCompletedOrderRequest request, Throwable t) {
        log.error("OCMSOrderServiceWrapper.queryCompletedOrderFallback request:{} 调用降级方法", request, t);
        throw new FallbackException("RiderDeliveryServiceWrapper-queryCompletedOrderFallback fallback");
    }

    @Degrade(rhinoKey = "RiderDeliveryServiceWrapper.queryInProgressOrder",
            fallBackMethod = "queryInProgressOrderFallback",
            isDegradeOnException = true,
            timeoutInMilliseconds = 5000)
    public CommonResponse<RiderDeliveryOrderListResponse> queryInProgressOrder(QueryRiderInProgressOrderRequest request){
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        List<Long> storeIdList = getStoreIdList(request.getStoreId());
        long tenantId = identityInfo.getUser().getTenantId();
        if (CollectionUtils.isEmpty(storeIdList) || storeIdList.size() > 1) {
            log.error("骑手订单展示只支持单门店模式：storeIdList={}", storeIdList);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "只支持单门店模式");
        }
        long operatorAccountId = identityInfo.getUser().getAccountId();
        PageQueryInProgressDeliveryOrderRequest tRequest = new PageQueryInProgressDeliveryOrderRequest();
        tRequest.setPage(request.getPage());
        tRequest.setPageSize(request.getSize());
        tRequest.setTenantId(tenantId);
        tRequest.setStoreId(storeIdList.get(0));
        tRequest.setRiderAccountId(operatorAccountId);

        return queryInProgressRiderOrderTemplate(tenantId, tRequest, Objects.equals(request.getNeedReturnHighPriceTag(), true));
    }

    private CommonResponse<RiderDeliveryOrderListResponse> queryInProgressOrderFallback(QueryRiderInProgressOrderRequest request, Throwable t) {
        log.error("RiderDeliveryServiceWrapper.queryInProgressOrderFallback request:{} 调用降级方法", request, t);
        throw new FallbackException("RiderDeliveryServiceWrapper-queryInProgressOrderFallback fallback");
    }

    private  List<Long> getStoreIdList(String storeIds) {
        if (StringUtils.isBlank(storeIds)) {
            return Collections.emptyList();
        }
        return Arrays.stream(storeIds.split(",")).distinct().filter(e -> !e.isEmpty()).map(Long::valueOf).collect(Collectors.toList());
    }
    /**
     * 查询骑手各状态的运单数量.
     *
     * @param statusList 待查询状态列表
     * @return Map<Integer, Integer> deliveryStatus-quantity
     */
    public Map<Integer, Integer> queryDeliveryOrderQuantity(List<TmsDeliveryStatusDesc> statusList) {
        if (CollectionUtils.isEmpty(statusList)) {
            return Collections.emptyMap();
        }
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        List<Long> storeIdList = identityInfo.getStoreIdList();
        long tenantId = identityInfo.getUser().getTenantId();
        if (CollectionUtils.isEmpty(storeIdList) || storeIdList.size() > 1) {
            log.error("骑手订单数量查询只支持单门店模式：storeIdList={}", storeIdList);
            return Collections.emptyMap();
        }
        long operatorAccountId = identityInfo.getUser().getAccountId();

        QueryDeliveryQuantityRequest request = new QueryDeliveryQuantityRequest();
        request.setTenantId(tenantId);
        request.setStoreId(storeIdList.get(0));
        request.setDeliveryStatusList(statusList.stream().map(TmsDeliveryStatusDesc::getCode).collect(Collectors.toList()));
        request.setRiderAccountId(operatorAccountId);
        try {
            log.info("RiderDeliveryServiceWrapper call RiderQueryThriftService-queryDeliveryQuantity begin. request:{}", request);
            QueryDeliveryQuantityResponse response = riderQueryRpcService.queryDeliveryQuantity(request);
            log.info("RiderDeliveryServiceWrapper call RiderQueryThriftService-queryDeliveryQuantity end. request:{}, resonse:{}",
                    request, response);
            if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
                log.warn("RiderDeliveryServiceWrapper call RiderQueryThriftService-queryDeliveryQuantity wrong. request:{}, response:{}",
                        request, response);
                return Collections.emptyMap();
            }
            return response.getDeliveryStatusQuantityMap();
        } catch (Exception e) {
            log.error("RiderDeliveryServiceWrapper call RiderQueryThriftService-queryDeliveryQuantity error.", e);
            return Collections.emptyMap();
        }
    }

    private CommonResponse<RiderDeliveryOrderListResponse> queryRiderOrderTemplate(long tenantId,
                                                                                   PageQueryDeliveryOrderRequest queryDeliveryOrderRequest) {
        List<RiderDeliveryOrderExtInfoType> riderDeliveryOrderExtInfoTypes = new ArrayList<>();
        riderDeliveryOrderExtInfoTypes.addAll(Arrays.asList(DELIVERY_EXCEPTION_INFO, ORDER_REVENUE_INFO, TURN_DELIVERY_BUTTON_INFO, LACK_STOCK));
        //配送中列表需要展示高价值标签
        if (Objects.equals(queryDeliveryOrderRequest.getDeliveryStatus(), DeliveryStatusEnum.RIDER_TAKEN_GOODS.getCode())) {
            riderDeliveryOrderExtInfoTypes.add(HIGH_PRICE_TAG);
        }
        return queryRiderOrderTemplate(tenantId, queryDeliveryOrderRequest.getStoreId(), riderDeliveryOrderExtInfoTypes,
                () -> getRiderDeliveryOrders(queryDeliveryOrderRequest));
    }

    private CommonResponse<RiderDeliveryOrderListResponse> queryCompletedRiderOrderTemplate(long tenantId,
                                                                                   PageQueryCompletedDeliveryOrderRequest queryCompletedDeliveryOrderRequest) {
        return queryRiderOrderTemplate(tenantId, queryCompletedDeliveryOrderRequest.getStoreId(),
                Arrays.asList(DELIVERY_EXCEPTION_INFO, ORDER_REVENUE_INFO),
                () -> getRiderCompletedDeliveryOrders(queryCompletedDeliveryOrderRequest));
    }

    private CommonResponse<RiderDeliveryOrderListResponse> queryInProgressRiderOrderTemplate(long tenantId,
                                                                                            PageQueryInProgressDeliveryOrderRequest queryInProgressDeliveryOrderRequest,
                                                                                             boolean needReturnHighPriceTag) {
        if(needReturnHighPriceTag) {
            return queryRiderOrderTemplate(tenantId, queryInProgressDeliveryOrderRequest.getStoreId(),
                    Collections.singletonList(HIGH_PRICE_TAG),
                    () -> getRiderInProgressDeliveryOrders(queryInProgressDeliveryOrderRequest));
        } else {
            return queryRiderOrderTemplate(tenantId, queryInProgressDeliveryOrderRequest.getStoreId(),
                    Collections.emptyList(),
                    () -> getRiderInProgressDeliveryOrders(queryInProgressDeliveryOrderRequest));
        }

    }

    /**
     * 查询骑手配送单模版.
     *
     * @param tenantId                  租户 ID
     * @param queryDeliveryOrderSupplier 查询骑手运单
     * @return CommonResponse<RiderDeliveryOrderListResponse>
     */
    private CommonResponse<RiderDeliveryOrderListResponse> queryRiderOrderTemplate(long tenantId, long storeId, List<RiderDeliveryOrderExtInfoType> extInfoEnums,
                                                                                   Supplier<PageQueryDeliveryOrderResponse> queryDeliveryOrderSupplier) {
        PageInfoVO pageInfoVO;
        List<TRiderDeliveryOrder> deliveryOrders;
        // 分页查询等待分配骑手的运单
        try {
            PageQueryDeliveryOrderResponse deliveryOrderResponse = queryDeliveryOrderSupplier.get();
            pageInfoVO = new PageInfoVO(deliveryOrderResponse.getPageInfo());
            deliveryOrders = deliveryOrderResponse.getTRiderDeliveryOrders();
        } catch (CommonRuntimeException e) {
            log.error("RiderDeliveryServiceWrapper.getRiderDeliveryOrders error.", e);
            return CommonResponse.fail(ResultCode.INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }

        if (CollectionUtils.isEmpty(deliveryOrders)) {
            return CommonResponse.success(new RiderDeliveryOrderListResponse(pageInfoVO, Collections.emptyList()));
        }

        List<OCMSOrderVO> ocmsOrderList;
        List<DeliveryExceptionSummaryVO> summaryVOList = Collections.emptyList();
        List<OrderRevenueDetailResponse> orderRevenueDetailList = Collections.emptyList();
        List<Integer> couldOperateItemList = Lists.newArrayList();

        // 查询订单详情
        try {
            ocmsOrderList = getOCMSOrderList(deliveryOrders);
        } catch (CommonRuntimeException e) {
            log.error("RiderDeliveryServiceWrapper.getOCMSOrderList error.", e);
            return CommonResponse.fail(ResultCode.INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }


        if (extInfoEnums.contains(ORDER_REVENUE_INFO)) {
            Map<String, Boolean> permissions = authThriftWrapper.isHasPermission(ImmutableList.of(AuthCodeEnum.SHOW_SALE_PRICE.getAuthCode()));
            boolean showSalePrice = permissions.getOrDefault(AuthCodeEnum.SHOW_SALE_PRICE.getAuthCode(), Boolean.FALSE);
            // 查询订单营收
            orderRevenueDetailList = showSalePrice ? getOrderListRevenueDetail(tenantId, ocmsOrderList) :
                    Collections.emptyList();
        }


        if (extInfoEnums.contains(DELIVERY_EXCEPTION_INFO) && MccConfigUtil.isDrunkHorseTenant(tenantId)) {
            //查询骑手上报异常，查询不到不影响主流程
            summaryVOList = this.queryRiderReportException(tenantId, storeId,
                    deliveryOrders.stream().map(order -> Pair.of(order.getChannelOrderId(), order.getOrderBizTypeCode()))
                            .collect(Collectors.toList()));
        }

        if (extInfoEnums.contains(TURN_DELIVERY_BUTTON_INFO)) {
            //查询是否可以转配送，这里是自营，目前是直接转青云
            couldOperateItemList = queryTurnDeliveryButton(tenantId, storeId);
        }

        List<AbnOrderDTO> unprocessedAbnOrder = Lists.newArrayList();
        if (extInfoEnums.contains(LACK_STOCK) && MccConfigUtil.isNewPickGrayStore(storeId)) {
            unprocessedAbnOrder = abnOrderServiceWrapper.getUnprocessedAbnOrder(storeId);
        }

        // 非歪马租户批量查询渠道名称缩写
        Map<Integer, String> channelAbbrMap = Maps.newHashMap();
        if (!MccConfigUtil.checkIsDHTenant(tenantId)) {
            List<Integer> channelIds = ocmsOrderList.stream()
                    .map(ocmsOrderVO -> ChannelOrderConvertUtils.sourceBiz2Mid(ocmsOrderVO.getOrderBizType()))
                    .distinct().collect(Collectors.toList());
            channelAbbrMap = tenantWrapper.queryChannelAbbrByChannelIds(channelIds);
        }

        RiderDeliveryOrderListResponse response = this.buildRiderDeliveryOrderListResponse(deliveryOrders, summaryVOList,
                ocmsOrderList, orderRevenueDetailList, couldOperateItemList, unprocessedAbnOrder, pageInfoVO, channelAbbrMap);

        if(MccConfigUtil.isDrunkHorseTenant(tenantId) && MccConfigUtil.queryQuestionnaireSwitch()) {
            Map<Long, List<DeliveryQuestionnaireDTO>> questionnaireMap = deliveryQuestionnaireWrapper.queryDeliveryQuestionnaireMap(deliveryOrders);
            fillQuestionnaireInfo(response, questionnaireMap);
        }

        if (extInfoEnums.contains(HIGH_PRICE_TAG)) {
            fillHighPriceTag(tenantId, storeId, response);
        }
        return CommonResponse.success(response);
    }

    /**
     * 获取租户下具有开票权限的门店ID
     * @param tenantId
     * @return
     */
    private List<Long> getCreateInvoiceShopList(Long tenantId){
        try {
            ResultResponse<ShopInfoQueryResponse> response = invoiceQueryThriftService.queryAllShopByTenantId(tenantId);
            log.info("invoke queryAllShopByTenantId,tenantId = {},response = {}", tenantId, response);
            if(response.isSuccess() && Objects.nonNull(response.getData()) &&  CollectionUtils.isNotEmpty(response.getData().getShopInfoList())) {
                return response.getData().getShopInfoList().stream().map(ShopInfo::getShopId).collect(Collectors.toList());
            }
            return Collections.emptyList();
        }catch (Exception e){
            log.error("invoke FuseOrderServiceImpl.getCreateInvoiceShopList error!", e);
            throw new com.sankuai.meituan.shangou.saas.common.exception.BizException("FuseOrderServiceImpl.getCreateInvoiceShopList error!, tenantId:" + tenantId );
        }
    }

    private void fillHighPriceTag(Long tenantId, Long storeId, RiderDeliveryOrderListResponse response) {
        try {
            if (!MccConfigUtil.isDrunkHorseTenant(tenantId) || !MccConfigUtil.isHighPriceTagGrayStore(storeId)) {
                return;
            }

            List<ChannelOrderIdKeyReq> channelOrderIdKeyReqs = response.getOrderList().stream()
                    .map(riderDeliveryOrderVo -> new ChannelOrderIdKeyReq(ChannelOrderConvertUtils.convertBizType(riderDeliveryOrderVo.getChannelId()), riderDeliveryOrderVo.getChannelOrderId()))
                    .collect(Collectors.toList());

            if(CollectionUtils.isEmpty(channelOrderIdKeyReqs)) {
                return ;
            }

            List<FulfillmentOrderDetailDTO> fulfillmentOrderDetailDTOS = fulfillmentOrderServiceWrapper.searchFulfillmentOrderListByBatchFulfillmentOrderId(tenantId, storeId, channelOrderIdKeyReqs);

            List<String> skuIds = fulfillmentOrderDetailDTOS.stream()
                    .map(FulfillmentOrderDetailDTO::getItemList)
                    .flatMap(Collection::stream)
                    .filter(item -> new BigDecimal(item.getQuantity()).subtract(new BigDecimal(item.getRefundCount())).compareTo(BigDecimal.ZERO) > 0)
                    .map(FulfillmentOrderItemDetailDTO::getCombinationProductItemList)
                    .flatMap(Collection::stream)
                    .map(CombinationProductItemDTO::getSkuId)
                    .distinct()
                    .collect(Collectors.toList());

            Map<String, Boolean> isHighPriceMap = supplyProductTagWrapper.batchGetProductHighPriceTag(tenantId, skuIds);

            Map<String, Boolean> orderIsContainHighPriceMap = new HashMap<>();
            for (FulfillmentOrderDetailDTO fulfillmentOrderDetailDTO : fulfillmentOrderDetailDTOS) {
                boolean containHighPrice = fulfillmentOrderDetailDTO.getItemList()
                        .stream()
                        .filter(item -> new BigDecimal(item.getQuantity()).subtract(new BigDecimal(item.getRefundCount())).compareTo(BigDecimal.ZERO) > 0)
                        .map(FulfillmentOrderItemDetailDTO::getCombinationProductItemList)
                        .flatMap(Collection::stream)
                        .map(CombinationProductItemDTO::getSkuId)
                        .anyMatch(skuId -> Objects.equals(isHighPriceMap.get(skuId), true));
                orderIsContainHighPriceMap.putIfAbsent(fulfillmentOrderDetailDTO.getChannelOrderId(), containHighPrice);
            }

            for (RiderDeliveryOrderVo riderDeliveryOrderVo : response.getOrderList()) {
                riderDeliveryOrderVo.setIsContainsHighWaxGoods(orderIsContainHighPriceMap.getOrDefault(riderDeliveryOrderVo.getChannelOrderId(), false));
            }
        } catch (Exception e) {
            log.error("查询高价值标签失败", e);
            Cat.logEvent("HIGH_PRICE_TAG", "QUERY_FAIL");
        }

    }

    private void fillQuestionnaireInfo(RiderDeliveryOrderListResponse response,
                                       Map<Long, List<DeliveryQuestionnaireDTO>> questionnaireMap) {
        try {
            List<RiderDeliveryOrderVo> orderList = response.getOrderList();
            orderList.forEach(order -> {
                //被投放了问卷并且问题还没填完
                order.setIsNeedAnswerQuestionnaire(questionnaireMap.containsKey(order.getDeliveryOrderId())
                        && questionnaireMap.get(order.getDeliveryOrderId())
                        .stream()
                        .anyMatch(questionnaire -> StringUtils.isBlank(questionnaire.getAnswer())));
            });
        } catch (Exception e) {
            log.warn("fillQuestionnaireInfo error",  e);
            Cat.logEvent("DELIVERY_QUESTIONNAIRE", "FILL_QUESTIONNAIRE_INFO_ERROR");
        }
    }

    private List<Integer> queryTurnDeliveryButton(long tenantId, long storeId) {
        try {
            if(!MccConfigUtil.getDHTenantIdList().contains(String.valueOf(tenantId))) {
                log.info("非歪马租户，不展示按钮。tenant ={}", tenantId);
                return Lists.newArrayList();
            }
            //1.门店需要开启转配送
            TResult<SelfDeliveryPoiConfigDTO> selfDeliveryPoiConfigDTOTResult = selfDeliveryPoiConfigThriftService.querySelfDeliveryConfig(tenantId, storeId);
            if (!selfDeliveryPoiConfigDTOTResult.isSuccess() || Objects.isNull(selfDeliveryPoiConfigDTOTResult.getData())) {
                return Lists.newArrayList();
            }
            if (!Objects.equals(selfDeliveryPoiConfigDTOTResult.getData().getEnableTurnDelivery(), IntegerBooleanConstants.BOOLEAN_TRUE)) {
                return Lists.newArrayList();
            }
            //2.操作人要有权限
            Map<String, Boolean> permissions = authThriftWrapper.isHasPermission(ImmutableList.of(AuthCodeEnum.TURN_AGG_DELIVERY.getAuthCode()));
            boolean hasAuth = permissions.getOrDefault(AuthCodeEnum.TURN_AGG_DELIVERY.getAuthCode(), Boolean.FALSE);
            if (!hasAuth) {
                return Lists.newArrayList();
            }
            //NOTE: 这里还规定订单实付金额不能大于150元，这个在转换为VO的时候处理的。这里不再次查订单了
            return Lists.newArrayList(OrderCouldOperateItem.TURN_AGG_DELIVERY.getValue());
        } catch (Exception e) {
            log.error("queryTurnDeliveryButton error", e);
            return Lists.newArrayList();
        }
    }


    /**
     * 查询骑手上报异常
     * @return
     */
    public List<DeliveryExceptionSummaryVO> queryRiderReportException(long tenantId, long storeId,
                                                                      List<Pair<String, Integer>> channelOrderPairList) {
        QueryDeliveryExceptionByChannelOrderRequest request = new QueryDeliveryExceptionByChannelOrderRequest();
        request.setTenantId(tenantId);
        request.setStoreId(storeId);
        request.setChannelOrderInfoList(channelOrderPairList.stream().map(pair ->
                new QueryDeliveryExceptionByChannelOrderRequest.ChannelOrderInfo(pair.getLeft(), pair.getRight()))
                .collect(Collectors.toList()));
        DeliveryExceptionResponse response = null;
        try {
            response = riderQueryRpcService.queryDeliveryExceptionByChannelOrder(request);
            if (response == null || response.getStatus() == null ||
                    response.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
                log.warn("RiderDeliveryServiceWrapper call riderQueryRpcService.queryRiderReportException error. request:{}, response:{}", request, response);
                return Collections.emptyList();
            }
            return buildDeliveryExceptionSummaryVOList(response.getTRiderDeliveryExceptionList());
        } catch (Exception e) {
            log.warn("RiderDeliveryServiceWrapper call riderQueryRpcService.queryRiderReportException error. request:{}, response:{}", request, response, e);
            return Collections.emptyList();
        }
    }

    /**
     * 查询骑手运单.
     *
     * @param tRequest thrift 请求
     * @return PageQueryDeliveryOrderResponse
     */
    private PageQueryDeliveryOrderResponse getRiderDeliveryOrders(PageQueryDeliveryOrderRequest tRequest) {
        try {
            log.info("RiderDeliveryServiceWrapper call RiderQueryThriftService-pageQueryDeliveryOrder begin. request:{}", tRequest);
            PageQueryDeliveryOrderResponse response = riderQueryRpcService.pageQueryDeliveryOrder(tRequest);
            log.info("RiderDeliveryServiceWrapper call RiderQueryThriftService-pageQueryDeliveryOrder end. request:{}, response:{}",
                    tRequest, response);
            if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
                log.warn("RiderDeliveryServiceWrapper call RiderQueryThriftService-pageQueryDeliveryOrder wrong. request:{}, response:{}",
                        tRequest, response);
                throw new CommonRuntimeException(response.getStatus().getMsg());
            }
            return response;
        } catch (CommonRuntimeException e) {
            throw e;
        } catch (Exception e) {
            log.error("RiderDeliveryServiceWrapper call RiderQueryThriftService-pageQueryDeliveryOrder error.", e);
            throw new CommonRuntimeException(ResultCode.INTERNAL_SERVER_ERROR.defaultMessage);
        }
    }

    /**
     * 查询骑手已完成运单(已送达+已取消).
     *
     * @param tRequest thrift 请求
     * @return PageQueryDeliveryOrderResponse
     */
    private PageQueryDeliveryOrderResponse getRiderCompletedDeliveryOrders(PageQueryCompletedDeliveryOrderRequest tRequest) {
        PageQueryDeliveryOrderResponse response;
        try {
            log.info("RiderDeliveryServiceWrapper call RiderQueryThriftService-pageQueryCompletedDeliveryOrder begin. request:{}", tRequest);
            response = riderQueryRpcService.pageQueryCompletedDeliveryOrder(tRequest);
            log.info("RiderDeliveryServiceWrapper call RiderQueryThriftService-pageQueryCompletedDeliveryOrder end. request:{}, response:{}", tRequest, response);
        } catch (Exception e) {
            log.error("RiderDeliveryServiceWrapper call RiderQueryThriftService-pageQueryCompletedDeliveryOrder error.", e);
            throw new CommonRuntimeException(ResultCode.INTERNAL_SERVER_ERROR.defaultMessage);
        }
        if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
            log.warn("RiderDeliveryServiceWrapper call RiderQueryThriftService-pageQueryCompletedDeliveryOrder wrong. request:{}, response:{}",
                    tRequest, response);
            throw new CommonRuntimeException(response.getStatus().getMsg());
        }
        return response;
    }

    /**
     * 查询骑手进行中运单(已接单+已取货).
     *
     * @param tRequest thrift 请求
     * @return PageQueryDeliveryOrderResponse
     */
    private PageQueryDeliveryOrderResponse getRiderInProgressDeliveryOrders(PageQueryInProgressDeliveryOrderRequest tRequest) {
        PageQueryDeliveryOrderResponse response;
        try {
            log.info("RiderDeliveryServiceWrapper call RiderQueryThriftService-PageQueryInProgressDeliveryOrder begin. request:{}", tRequest);
            response = riderQueryRpcService.pageQueryInProgressDeliveryOrder(tRequest);
            log.info("RiderDeliveryServiceWrapper call RiderQueryThriftService-PageQueryInProgressDeliveryOrder end. request:{}, response:{}", tRequest, response);
        } catch (Exception e) {
            log.error("RiderDeliveryServiceWrapper call RiderQueryThriftService-PageQueryInProgressDeliveryOrder error.", e);
            throw new CommonRuntimeException(ResultCode.INTERNAL_SERVER_ERROR.defaultMessage);
        }
        if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
            log.warn("RiderDeliveryServiceWrapper call RiderQueryThriftService-PageQueryInProgressDeliveryOrder wrong. request:{}, response:{}",
                    tRequest, response);
            throw new CommonRuntimeException(response.getStatus().getMsg());
        }
        return response;
    }

    /**
     * 获取订单详情.
     *
     * @param deliveryOrders 运单信息
     * @return List<OCMSOrderVO>
     */
    private List<OCMSOrderVO> getOCMSOrderList(List<TRiderDeliveryOrder> deliveryOrders) {
        try {
            OCMSListViewIdConditionRequest viewIdConditionRequest = buildOCMSListViewIdConditionRequestByRiderOrders(deliveryOrders);
            log.info("RiderDeliveryServiceWrapper call OcmsQueryThriftService.queryOrderByViewIdCondition request:{}",
                    viewIdConditionRequest);
            OCMSListViewIdConditionResponse viewIdConditionResponse =
                    ocmsQueryThriftService.queryOrderByViewIdCondition(viewIdConditionRequest);
            log.info("RiderDeliveryServiceWrapper call ocmsQueryThriftService.queryOrderByViewIdCondition response:{}",
                    viewIdConditionResponse);
            if (viewIdConditionResponse.getStatus().getCode() != ResultCodeEnum.SUCCESS.getValue()) {
                log.warn("获取订单详情失败, viewOrderId:{}",
                        viewIdConditionRequest.getViewIdConditionList().stream().map(e->e.getViewOrderId()).collect(Collectors.toList()));
                throw new CommonRuntimeException(viewIdConditionResponse.getStatus().getMessage());
            }
            // 检查目标订单数和返回订单数是否相等，若不等则埋点
            checkViewResponseCount(viewIdConditionRequest, viewIdConditionResponse);
            return viewIdConditionResponse.getOcmsOrderList();
        } catch (CommonRuntimeException e) {
            throw e;
        } catch (Exception e) {
            log.error("RiderDeliveryServiceWrapper call ocmsQueryThriftService.queryOrderByViewIdCondition error", e);
            throw new CommonRuntimeException(ResultCode.INTERNAL_SERVER_ERROR.defaultMessage);
        }
    }

    /**
     * 根据骑手运单，构造查询订单信息的请求.
     *
     * @param riderOrders 骑手运单
     * @return OCMSListViewIdConditionRequest
     */
    private OCMSListViewIdConditionRequest buildOCMSListViewIdConditionRequestByRiderOrders(List<TRiderDeliveryOrder> riderOrders) {
        OCMSListViewIdConditionRequest request = new OCMSListViewIdConditionRequest();
        request.setViewIdConditionList(riderOrders.stream().filter(Objects::nonNull)
                .map(riderOrder -> new ViewIdCondition(riderOrder.getOrderBizTypeCode(), riderOrder.getChannelOrderId()))
                .collect(Collectors.toList()));
        request.setSortField(SortFieldEnum.ORDER_CREATE_TIME);
        request.setSort(SortByEnum.DESC);
        return request;
    }

    /**
     * 检查请求订单数和返回订单是否一致，若不一致，进行埋点.
     *
     * @param viewIdConditionRequest  请求订单
     * @param viewIdConditionResponse 返回订单
     */
    private void checkViewResponseCount(OCMSListViewIdConditionRequest viewIdConditionRequest,
                                        OCMSListViewIdConditionResponse viewIdConditionResponse) {
        int requestOrderCount = viewIdConditionRequest.getViewIdConditionList().size();
        int retunOrderCount = viewIdConditionResponse.getOcmsOrderList().size();
        if (requestOrderCount != retunOrderCount) {
            MetricHelper.build().name("riderOrdersNotEqual").count(Math.abs(requestOrderCount - retunOrderCount));
            List<String> requestViewIds = viewIdConditionRequest.getViewIdConditionList().stream().map(ViewIdCondition::getViewOrderId)
                    .collect(Collectors.toList());
            List<String> responseViewIds = viewIdConditionResponse.getOcmsOrderList().stream().map(OnlineBaseOrderVO::getViewOrderId)
                    .collect(Collectors.toList());
            ArrayList<String> lackViewIds = Lists.newArrayList(requestViewIds);
            lackViewIds.removeAll(responseViewIds);
            log.warn("骑手查询订单详情返回数量不等. requestViewIds:{}, responseViewIds:{}, lackViewIds:{}",
                    requestViewIds, responseViewIds, lackViewIds);
        }
    }

    /**
     * 查询订单营收信息.
     *
     * @param tenantId      租户 ID
     * @param ocmsOrderList 订单列表
     * @return List<OrderRevenueDetailResponse>
     */
    private List<OrderRevenueDetailResponse> getOrderListRevenueDetail(long tenantId, List<OCMSOrderVO> ocmsOrderList) {
        if (CollectionUtils.isEmpty(ocmsOrderList)) {
            return Collections.emptyList();
        }

        MerchantOrderRevenueDetailRequest request = new MerchantOrderRevenueDetailRequest();
        request.setTenantId(tenantId);
        List<ViewIdCondition> conditions = ocmsOrderList.stream().map(order -> ViewIdCondition.builder()
                .orderBizType(order.getOrderBizType())
                .viewOrderId(order.getViewOrderId()).build())
                .collect(Collectors.toList());
        request.setViewIdConditionList(conditions);

        try {
            log.info("RiderDeliveryServiceWrapper call MerChantRevenueQueryService.orderRevenueDetail request:{}", request);
            MerchantOrderListRevenueDetailResponse response = merChantRevenueQueryService
                    .orderListRevenueDetail(request);
            log.info("RiderDeliveryServiceWrapper call MerChantRevenueQueryService.orderRevenueDetail esponse:{}", response);
            if (response == null) {
                log.warn("RiderDeliveryServiceWrapper call MerChantRevenueQueryService.orderRevenueDetail wrong. request:{}, response:{}"
                        , request, response);
                return Collections.emptyList();
            }
            if (Integer.valueOf(StatusCodeEnum.SUCCESS.getCode()).equals(response.getStatus())) {
                List<OrderRevenueDetailResponse> orderListRevenueDetailResponse = response.getOrderListRevenueDetailResponse();
                if (orderListRevenueDetailResponse == null) {
                    return Collections.emptyList();
                }
                return orderListRevenueDetailResponse;
            } else {
                log.warn("RiderDeliveryServiceWrapper call MerChantRevenueQueryService.orderRevenueDetail fail. request:{}, response:{}"
                        , request, response);
                return Collections.emptyList();
            }
        } catch (Exception e) {
            log.warn("RiderDeliveryServiceWrapper call MerChantRevenueQueryService.orderRevenueDetail error. request:{}", request);
            return Collections.emptyList();
        }
    }

    /**
     * 构造骑手配送单返回结果.
     *
     * @param deliveryOrders         骑手运单
     * @param ocmsOrderList          订单列表
     * @param orderRevenueDetailList 订单营收信息
     * @param pageInfoVO             分页信息
     * @return RiderDeliveryOrderListResponse
     */
    private RiderDeliveryOrderListResponse buildRiderDeliveryOrderListResponse(List<TRiderDeliveryOrder> deliveryOrders,
                                                                               List<DeliveryExceptionSummaryVO> summaryVOList,
                                                                               List<OCMSOrderVO> ocmsOrderList,
                                                                               List<OrderRevenueDetailResponse> orderRevenueDetailList,
                                                                               List<Integer> couldOperateItemList,
                                                                               List<AbnOrderDTO> unprocessedAbnOrder,
                                                                               PageInfoVO pageInfoVO,
                                                                               Map<Integer, String> channelAbbrMap) {
        RiderDeliveryOrderListResponse riderResponse = new RiderDeliveryOrderListResponse();
        riderResponse.setPageInfo(pageInfoVO);
        Map<String, OCMSOrderVO> orderMap = ocmsOrderList.stream()
                .collect(Collectors.toMap(OCMSOrderVO::getViewOrderId, revenue -> revenue, (ov, nv) -> ov));
        Map<String, OrderRevenueDetailResponse> orderRevenueMap = orderRevenueDetailList.stream()
                .collect(Collectors.toMap(OrderRevenueDetailResponse::getOrderViewId, revenue -> revenue, (ov, nv) -> ov));
        Map<String, AbnOrderDTO> orderAbnMap = Optional.ofNullable(unprocessedAbnOrder).orElse(Lists.newArrayList()).stream()
                .collect(Collectors.toMap(AbnOrderDTO::getSourceOrderNo, Function.identity(), (older, newer) -> newer));

        List<RiderDeliveryOrderVo> riderDeliveryOrderVoList = Lists.newArrayListWithExpectedSize(deliveryOrders.size());
        Map<String, DeliveryExceptionSummaryVO> exceptionMap = summaryVOList.stream().collect(
                Collectors.toMap(DeliveryExceptionSummaryVO::getDeliveryOrderId, vo -> vo));

        List<Long> createInvoiceShopList = CollectionUtils.isNotEmpty(ocmsOrderList) ? getCreateInvoiceShopList(ocmsOrderList.get(0).getTenantId()) : Lists.newArrayList();

        for (TRiderDeliveryOrder deliveryOrder : deliveryOrders) {
            String channelOrderId = deliveryOrder.getChannelOrderId();
            OCMSOrderVO ocmsOrderVO = orderMap.get(channelOrderId);
            AbnOrderDTO abnOrderDTO = orderAbnMap.get(channelOrderId);

            OrderRevenueDetailResponse orderRevenue = orderRevenueMap.get(channelOrderId);
            if (ocmsOrderVO == null) {
                continue;
            }
            RiderDeliveryOrderVo riderDeliveryOrder = this.buildRiderDeliveryOrderVo(deliveryOrder,
                    exceptionMap.get(String.valueOf(deliveryOrder.getDeliveryOrderId())), ocmsOrderVO, orderRevenue, couldOperateItemList, abnOrderDTO, channelAbbrMap);
            if(createInvoiceShopList.contains(ocmsOrderVO.getShopId()) && !Objects.equals(OrderStatusEnum.CANCELED.getValue(), ocmsOrderVO.getOrderStatus())){
                riderDeliveryOrder.getCouldOperateItemList().add(OrderCanOperateItem.CREATE_INVOICE.getValue());
            }
            riderDeliveryOrderVoList.add(riderDeliveryOrder);
        }
        riderResponse.setOrderList(riderDeliveryOrderVoList);
        return riderResponse;
    }

    private QueryDeliveryExceptionListResponse buildQueryDeliveryExceptionListResponse(PageQueryDeliveryExceptionListResponse tResponse) {
        QueryDeliveryExceptionListResponse response = new QueryDeliveryExceptionListResponse();
        response.setPageNum(tResponse.getPageInfo().getPage());
        response.setPageSize(tResponse.getPageInfo().getPageSize());
        response.setTotalCount(tResponse.getPageInfo().getTotal());
        response.setHasMore(tResponse.getPageInfo().getTotal() > tResponse.getPageInfo().getPage() * tResponse.getPageInfo().getPageSize());


        List<DeliveryExceptionSummaryVO> deliveryExceptionSummaryVOS = buildDeliveryExceptionSummaryVOList(tResponse.getTRiderDeliveryExceptionList());
        response.setDeliveryExceptionSummaryVOList(deliveryExceptionSummaryVOS);

        return response;
    }

    private List<DeliveryExceptionSummaryVO> buildDeliveryExceptionSummaryVOList(List<TRiderDeliveryException> tRiderDeliveryExceptionList) {

        List<DeliveryExceptionInfoVO> deliveryExceptionInfoVOS = tRiderDeliveryExceptionList.stream()
                .map(this::transform2DeliveryExceptionInfoVO)
                .collect(Collectors.toList());

        //Map化 deliveryOrderId --> DeliveryExceptionInfoVOList
        Map<String, List<DeliveryExceptionInfoVO>> deliveryExceptionInfoVOMap = new HashMap<>();
        for (DeliveryExceptionInfoVO deliveryExceptionInfoVO : deliveryExceptionInfoVOS) {
            if (!deliveryExceptionInfoVOMap.containsKey(deliveryExceptionInfoVO.getDeliveryOrderId())) {
                deliveryExceptionInfoVOMap.put(deliveryExceptionInfoVO.getDeliveryOrderId(), new ArrayList<>());
            }

            deliveryExceptionInfoVOMap.get(deliveryExceptionInfoVO.getDeliveryOrderId()).add(deliveryExceptionInfoVO);
        }

        //Map化 deliveryOrderId --> TRiderDeliveryException
        Map<String, TRiderDeliveryException> tRiderDeliveryExceptionMap = tRiderDeliveryExceptionList.stream()
                .collect(Collectors.toMap(tRiderDeliveryException -> tRiderDeliveryException.getDeliveryOrderId().toString(),
                        Function.identity(), (k1, k2) -> k1));


        return transform2DeliveryExceptionSummaryVO(tRiderDeliveryExceptionMap, deliveryExceptionInfoVOMap);
    }

    private QueryDeliveryExceptionDetailListResponse buildQueryDeliveryExceptionDetailListResponse(DeliveryExceptionResponse deliveryExceptionResponse) {
        QueryDeliveryExceptionDetailListResponse response = new QueryDeliveryExceptionDetailListResponse();
        response.setDeliveryExceptionDetailVos(deliveryExceptionResponse.getTRiderDeliveryExceptionList().stream()
                .map(this::transform2DeliveryExceptionDetailVO)
                .sorted(Comparator.comparing(DeliveryExceptionDetailVO::getReportTimeStamp))
                .collect(Collectors.toList()));

        return response;
    }

    private DeliveryExceptionDetailVO transform2DeliveryExceptionDetailVO(TRiderDeliveryException tRiderDeliveryException) {

        DeliveryExceptionDetailVO deliveryExceptionDetailVO = new DeliveryExceptionDetailVO();
        deliveryExceptionDetailVO.setChannelId(ChannelOrderConvertUtils.convertChannelId(tRiderDeliveryException.getOrderBizType()));
        deliveryExceptionDetailVO.setChannelOrderId(tRiderDeliveryException.getChannelOrderId().toString());
        deliveryExceptionDetailVO.setDaySeq(tRiderDeliveryException.getDaySeq());
        deliveryExceptionDetailVO.setComment(tRiderDeliveryException.getComment());
        deliveryExceptionDetailVO.setExceptionSubType(tRiderDeliveryException.getExceptionSubType());
        deliveryExceptionDetailVO.setExceptionType(tRiderDeliveryException.getExceptionType());
        deliveryExceptionDetailVO.setPayTime(tRiderDeliveryException.getPayTime());
        deliveryExceptionDetailVO.setUserRealAddress(tRiderDeliveryException.getUserRealAddress());

        deliveryExceptionDetailVO.setStoreId(tRiderDeliveryException.getStoreId().toString());
        deliveryExceptionDetailVO.setPicUrls(tRiderDeliveryException.getPicUrls());
        deliveryExceptionDetailVO.setReportTimeStamp(tRiderDeliveryException.getCreateTime());
        deliveryExceptionDetailVO.setRiderAccountId(tRiderDeliveryException.getRiderAccountId().toString());
        deliveryExceptionDetailVO.setRiderAccountName(tRiderDeliveryException.getRiderAccountName());
        deliveryExceptionDetailVO.setExceptionTypeDesc(tRiderDeliveryException.getExceptionTypeDesc());
        deliveryExceptionDetailVO.setExceptionSubTypeDesc(tRiderDeliveryException.getExceptionSubTypeDesc());
        deliveryExceptionDetailVO.setModifiedAddress(tRiderDeliveryException.getModifiedAddress());
        deliveryExceptionDetailVO.setDeliveryOrderId(tRiderDeliveryException.getDeliveryOrderId().toString());

        return deliveryExceptionDetailVO;
    }

    private DeliveryExceptionInfoVO transform2DeliveryExceptionInfoVO(TRiderDeliveryException tRiderDeliveryException) {

        DeliveryExceptionInfoVO deliveryExceptionInfoVO = new DeliveryExceptionInfoVO();

        deliveryExceptionInfoVO.setExceptionType(tRiderDeliveryException.getExceptionType());
        deliveryExceptionInfoVO.setReportTimeStamp(tRiderDeliveryException.getCreateTime());
        deliveryExceptionInfoVO.setRiderAccountId(tRiderDeliveryException.getRiderAccountId().toString());
        deliveryExceptionInfoVO.setRiderAccountName(tRiderDeliveryException.getRiderAccountName());
        deliveryExceptionInfoVO.setExceptionTypeDesc(tRiderDeliveryException.getExceptionTypeDesc());
        deliveryExceptionInfoVO.setDeliveryOrderId(tRiderDeliveryException.getDeliveryOrderId().toString());

        return deliveryExceptionInfoVO;
    }

    private List<DeliveryExceptionSummaryVO> transform2DeliveryExceptionSummaryVO(Map<String, TRiderDeliveryException> tRiderDeliveryExceptionMap,
                                                                                  Map<String, List<DeliveryExceptionInfoVO>> exceptionInfoVOMap) {
        return exceptionInfoVOMap.entrySet().stream().map(entry -> {
            TRiderDeliveryException tRiderDeliveryException = tRiderDeliveryExceptionMap.get(entry.getKey());

            if (tRiderDeliveryException == null) {
                return null;
            }

            DeliveryExceptionSummaryVO exceptionSummaryVO = new DeliveryExceptionSummaryVO();
            exceptionSummaryVO.setChannelId(ChannelOrderConvertUtils.convertChannelId(tRiderDeliveryException.getOrderBizType()));
            exceptionSummaryVO.setChannelOrderId(tRiderDeliveryException.getChannelOrderId().toString());
            exceptionSummaryVO.setPayTime(tRiderDeliveryException.getPayTime());
            exceptionSummaryVO.setDaySeq(tRiderDeliveryException.getDaySeq());
            exceptionSummaryVO.setStoreId(tRiderDeliveryException.getStoreId());
            exceptionSummaryVO.setDeliveryOrderId(tRiderDeliveryException.getDeliveryOrderId().toString());
            exceptionSummaryVO.setDeliveryExceptionInfoVOS(entry.getValue());

            return exceptionSummaryVO;
        }).filter(Objects::nonNull)
                .sorted((o1, o2) -> -(o1.getPayTime().compareTo(o2.getPayTime())))
                .collect(Collectors.toList());
    }

    /**
     * 构造骑手配送单信息.
     *
     * @param deliveryOrder 运单信息
     * @param ocmsOrderVO   订单信息
     * @param orderRevenue  订单营收数据
     * @return RiderDeliveryOrderVo
     */
    private RiderDeliveryOrderVo buildRiderDeliveryOrderVo(TRiderDeliveryOrder deliveryOrder,
                                                           DeliveryExceptionSummaryVO summaryVO,
                                                           OCMSOrderVO ocmsOrderVO,
                                                           OrderRevenueDetailResponse orderRevenue,
                                                           List<Integer> couldOperateItemList,
                                                           @Nullable AbnOrderDTO abnOrderDTO,
                                                           Map<Integer, String> channelAbbrMap) {
        RiderDeliveryOrderVo riderDeliveryOrderVo = new RiderDeliveryOrderVo();
        // 骑手配送单基础信息 start
        riderDeliveryOrderVo.setDeliveryOrderId(deliveryOrder.getDeliveryOrderId());
        riderDeliveryOrderVo.setTenantId(deliveryOrder.getTenantId());
        Integer channelId = ChannelOrderConvertUtils.sourceBiz2Mid(ocmsOrderVO.getOrderBizType());
        riderDeliveryOrderVo.setChannelId(channelId);
        riderDeliveryOrderVo.setChannelName(getChannelName(channelId, deliveryOrder.getTenantId(), channelAbbrMap));
        riderDeliveryOrderVo.setStoreId(ocmsOrderVO.getShopId());
        riderDeliveryOrderVo.setStoreName(ocmsOrderVO.getShopName());
        riderDeliveryOrderVo.setChannelOrderId(ocmsOrderVO.getViewOrderId());
        riderDeliveryOrderVo.setSerialNo(ocmsOrderVO.getOrderSerialNumber());

        // 名酒馆标识
        riderDeliveryOrderVo.setIsMtFamousTavern(ocmsOrderVO.getIsMtFamousTavern());
        if (!MccConfigUtil.getDHTenantIdList().contains(String.valueOf(deliveryOrder.getTenantId()))) {
            riderDeliveryOrderVo.setDaySeqNum(DaySeqNumUtil.getDaySeqNum(ocmsOrderVO.getOrderSerialNumber(), ocmsOrderVO.getOrderSerialNumberStr()));
        }
        DeliveryOrderType deliveryOrderType = ocmsOrderVO.getIsBooking() == 1 ?
                DeliveryOrderType.DELIVERY_BY_BOOK_TIME : DeliveryOrderType.DELIVERY_RIGHT_NOW;
        riderDeliveryOrderVo.setDeliveryOrderType(deliveryOrderType.getValue());
        riderDeliveryOrderVo.setDeliveryStatus(deliveryOrder.getDeliveryStatus());
        riderDeliveryOrderVo.setDeliveryOrderTypeName(getDeliveryOrderTypeName(deliveryOrderType.getValue()));
        riderDeliveryOrderVo.setEstimateArriveTimeStart(deliveryOrder.getEstimatedDeliveryTime());
        riderDeliveryOrderVo.setEstimateArriveTimeEnd(deliveryOrder.getEstimatedDeliveryEndTime());
        riderDeliveryOrderVo.setCreateTime(ocmsOrderVO.getCreateTime());
        riderDeliveryOrderVo.setDeliveryDistance(deliveryOrder.getDistance());
        // 设置支付时间
        riderDeliveryOrderVo.setPayTime(ocmsOrderVO.getPayTime() != null && ocmsOrderVO.getPayTime() > 0 ? ocmsOrderVO.getPayTime() : ocmsOrderVO.getCreateTime());
        OCMSDeliveryInfoVO ocmsDeliveryInfoVO = ocmsOrderVO.getOcmsDeliveryInfoVO();
        if (ocmsDeliveryInfoVO != null) {
            // 自提配送 or 送货上门
            riderDeliveryOrderVo.setDeliveryMethod(ocmsDeliveryInfoVO.getDistributeMethod());
            riderDeliveryOrderVo.setDeliveryMethodDesc(ocmsDeliveryInfoVO.getDistributeMethodName());
        }
        riderDeliveryOrderVo.setUserId(ocmsOrderVO.getUserId());
        if (deliveryOrder.getChangeFromRider() != null) {
            riderDeliveryOrderVo.setFromRiderName(deliveryOrder.getChangeFromRider().getName());
        }
        // 骑手配送单基础信息 end

        // 是否需要脱敏收货人信息
        boolean isNeedDesensitize = isNeedDesensitize(ocmsOrderVO.getTenantId(), deliveryOrder);

        // 配送超时考核信息 start
        buildDeliveryTimeoutInfo(deliveryOrder, deliveryOrderType, isNeedDesensitize, riderDeliveryOrderVo);
        // 配送超时考核信息 end


        // 收货人信息 start
        buildReceiverInfo(deliveryOrder.getReceiver(), isNeedDesensitize, riderDeliveryOrderVo);
        // 收货人信息 end

        // 商品信息 start
        List<OCMSOrderItemVO> ocmsOrderItemVOList = ocmsOrderVO.getOcmsOrderItemVOList();
        Integer itemCount = ocmsOrderItemVOList == null ? 0 : ocmsOrderItemVOList.stream().filter(Objects::nonNull)
                .mapToInt(OCMSOrderItemVO::getQuantity).sum();
        riderDeliveryOrderVo.setItemCount(itemCount);
        // 备注不为空且不为0才展示
        if (StringUtils.isNotEmpty(ocmsOrderVO.getComments()) && !ocmsOrderVO.getComments().equals("0")) {
            riderDeliveryOrderVo.setComments(ocmsOrderVO.getComments());
        }
        // 商品列表
        if (CollectionUtils.isNotEmpty(ocmsOrderItemVOList)) {
            riderDeliveryOrderVo.setProductList(ocmsOrderItemVOList.stream().filter(Objects::nonNull)
                    .map(ocmsOrderItemVO -> buildProductVO(ocmsOrderVO.getTenantId(), ocmsOrderVO.getShopId(), ocmsOrderItemVO)).collect(Collectors.toList()));
        }
        // 订单营收信息
        if(orderRevenue != null && orderRevenue.getOrderAmountInfo() != null){
            OrderAmountInfo orderAmountInfo = orderRevenue.getOrderAmountInfo();
            RevenueDetailVo revenueDetailVo = new RevenueDetailVo();
            revenueDetailVo.setPromotionInfos(orderRevenue.getPromotionInfos());
            revenueDetailVo.setActualPayAmount(orderAmountInfo.getActualPayAmt());
            revenueDetailVo.setBizActivityAmount(orderAmountInfo.getBizCharge());
            revenueDetailVo.setDeliveryAmount(orderAmountInfo.getDeliveryFee());
            revenueDetailVo.setPackageAmount(orderAmountInfo.getPackageAmount());
            revenueDetailVo.setRevenueAmount(orderAmountInfo.getBizReceiveAmount());
            riderDeliveryOrderVo.setRevenueDetail(revenueDetailVo);
        }
        riderDeliveryOrderVo.setTotalOfflinePrice(ocmsOrderVO.getTotalOfflinePrice());
        // 赠品信息
        if (CollectionUtils.isNotEmpty(ocmsOrderVO.getOnlineGiftVOS())) {
            riderDeliveryOrderVo.setGiftVOList(ocmsOrderVO.getOnlineGiftVOS().stream().filter(Objects::nonNull)
                    .map(onlineGiftVO -> buildGiftVO(deliveryOrder.getStoreId(), onlineGiftVO)).collect(Collectors.toList()));
            riderDeliveryOrderVo.setGiftCount(ocmsOrderVO.getOnlineGiftVOS().stream().filter(Objects::nonNull)
                    .mapToInt(OnlineGiftVO::getGiftQuantity).sum());
        }
        // 商品信息 end

        // 展示用户标签
        riderDeliveryOrderVo.setUserTags(UserTagTypeEnum.getTags(ocmsOrderVO.getTags()));
        riderDeliveryOrderVo.setOrderUserType(ocmsOrderVO.getUserType());

        riderDeliveryOrderVo.setDeliveryStatusLocked(deliveryOrder.getStatusLocked());
        riderDeliveryOrderVo.setCanStatusBeLocked(deliveryOrder.getCanStatusBeLocked());
        riderDeliveryOrderVo.setDeliveryExceptionSummaryVOS(summaryVO);
        boolean displayPostProofPhotoButton = false;
        if (Objects.equals(deliveryOrder.getDeliveryStatus(), DeliveryStatusEnum.DELIVERY_DONE.getCode()) && deliveryOrder.getDeliveryDoneTime() != null) {
            LocalDateTime deliveryDoneTime = Instant.ofEpochMilli(deliveryOrder.getDeliveryDoneTime()).atZone(ZoneId.systemDefault()).toLocalDateTime();
            displayPostProofPhotoButton = deliveryOrder.getCouldPostDeliveryProofPhoto() != null
                    && deliveryOrder.getCouldPostDeliveryProofPhoto()
                    && LocalDateTime.now().isBefore(deliveryDoneTime.plusMinutes(MccConfigUtil.postDeliveryProofPhotoTimeLimit()));
        } else {
            displayPostProofPhotoButton = Optional.ofNullable(deliveryOrder.getCouldPostDeliveryProofPhoto()).orElse(false);
        }

        riderDeliveryOrderVo.setCouldPostDeliveryProofPhoto(displayPostProofPhotoButton);

        //大于150元不展示转三方
        if (Objects.nonNull(ocmsOrderVO.getActualPayAmt()) && ocmsOrderVO.getActualPayAmt() >= MccConfigUtil.getMaxActualPayAmtForThirdDelivery()) {
            List<Integer> couldOperateItemListCopy = Lists.newArrayList(couldOperateItemList);
            couldOperateItemListCopy.remove(new Integer(OrderCouldOperateItem.TURN_AGG_DELIVERY.getValue()));
            riderDeliveryOrderVo.setCouldOperateItemList(couldOperateItemListCopy);
        }  else {
            riderDeliveryOrderVo.setCouldOperateItemList(couldOperateItemList);
        }

        riderDeliveryOrderVo.setHasLackGoods(Objects.nonNull(abnOrderDTO) && CollectionUtils.isNotEmpty(abnOrderDTO.getItems()));

        if (MccConfigUtil.isDrunkHorseTenant(deliveryOrder.getTenantId())) {
            riderDeliveryOrderVo.setSigningPoint(deliveryOrder.getSignPosition());
        }

        try {
            //场景信息补充
            String scene = null;
            if (Objects.nonNull(ocmsOrderVO.getOcmsDeliveryInfoVO())
                    && StringUtils.isNotBlank(ocmsOrderVO.getOcmsDeliveryInfoVO().getCategory())
                    && MccConfigUtil.isDhScenePoi(ocmsOrderVO.getShopId())
            ) {
                scene = buildScene(ocmsOrderVO.getOcmsDeliveryInfoVO().getCategory());
                riderDeliveryOrderVo.setScene(scene);
            }
            //是餐馆也不展示
            boolean isRestaurant = StringUtils.isNotBlank(scene) && MccConfigUtil.getDhTurnAggLimitSceneList().contains(scene);
            if (isRestaurant) {
                List<Integer> couldOperateItemListCopy = Lists.newArrayList(couldOperateItemList);
                couldOperateItemListCopy.remove(new Integer(OrderCouldOperateItem.TURN_AGG_DELIVERY.getValue()));
                riderDeliveryOrderVo.setCouldOperateItemList(couldOperateItemListCopy);
            }
        } catch (Exception e) {
            log.error("setCouldOperateItemList error", e);
        }

        return riderDeliveryOrderVo;
    }

    private void buildDeliveryTimeoutInfo(TRiderDeliveryOrder deliveryOrder, DeliveryOrderType deliveryOrderType, boolean isNeedDesensitize, RiderDeliveryOrderVo riderDeliveryOrderVo) {
        long evaluateArriveDeadline;
        if (deliveryOrderType.equals(DeliveryOrderType.DELIVERY_BY_BOOK_TIME)) {
            // 预订单：考核时间=预计送达时间+5分钟
            evaluateArriveDeadline = deliveryOrder.getEstimatedDeliveryEndTime() + (5 * 60 * 1000);
        } else {
            // 实时单：考核时间=支付时间+25分钟
            evaluateArriveDeadline = riderDeliveryOrderVo.getPayTime() + (25 * 60 * 1000);
        }
        riderDeliveryOrderVo.setEvaluateArriveDeadline(evaluateArriveDeadline);

        long tsNow = System.currentTimeMillis();
        if (Objects.equals(RiderDeliveryStatusEnum.DELIVERY_DONE.getCode(), deliveryOrder.getDeliveryStatus())) {
            riderDeliveryOrderVo.setEvaluateArriveLeftTime(0L);
            Long deliveryDoneTime = deliveryOrder.getDeliveryDoneTime();
            if (deliveryDoneTime != null
                    && deliveryDoneTime > evaluateArriveDeadline) {
                // 已送达，已超时
                riderDeliveryOrderVo.setEvaluateArriveTimeout(deliveryDoneTime - evaluateArriveDeadline);
                if (isNeedDesensitize) {
                    String errorMessageFormat = "配送完成超过%.2f小时，无法使用导航";
                    String errorMsg = String.format(errorMessageFormat, MccConfigUtil.getDesensitizeReceiverInfoTime() / 3600.0);
                    riderDeliveryOrderVo.setJumpNavigationErrorMessage(errorMsg);
                }
            } else {
                // 已送达，未超时
                riderDeliveryOrderVo.setEvaluateArriveTimeout(0L);
            }
        } else if (deliveryOrder.getDeliveryStatus() != null
                && (RiderDeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER.getCode() == deliveryOrder.getDeliveryStatus()
                    || RiderDeliveryStatusEnum.RIDER_ASSIGNED.getCode() == deliveryOrder.getDeliveryStatus()
                    || RiderDeliveryStatusEnum.RIDER_TAKEN_GOODS.getCode() == deliveryOrder.getDeliveryStatus())) {
            if (tsNow > evaluateArriveDeadline) {
                // 配送中，已经超时
                riderDeliveryOrderVo.setEvaluateArriveLeftTime(0L);
                riderDeliveryOrderVo.setEvaluateArriveTimeout(tsNow - evaluateArriveDeadline);
            } else {
                // 配送中，还未超时
                riderDeliveryOrderVo.setEvaluateArriveLeftTime(evaluateArriveDeadline - tsNow);
                riderDeliveryOrderVo.setEvaluateArriveTimeout(0L);
            }
        } else if (Objects.equals(RiderDeliveryStatusEnum.DELIVERY_CANCELLED.getCode(), deliveryOrder.getDeliveryStatus())) {
            if (isNeedDesensitize) {
                String errorMessageFormat = "配送取消超过%.2f小时，无法使用导航";
                String errorMsg = String.format(errorMessageFormat, MccConfigUtil.getDesensitizeReceiverInfoTime() / 3600.0);
                riderDeliveryOrderVo.setJumpNavigationErrorMessage(errorMsg);
            }
        } else {
            // 其他状态暂时无需处理
        }
    }

    private void buildReceiverInfo(TReceiver tReceiver, boolean isNeedDesensitize, RiderDeliveryOrderVo riderDeliveryOrderVo) {
        if (Objects.isNull(tReceiver)) {
            return;
        }
        DesensitizeReceiverInfoResult receiverInfoResult = new DesensitizeReceiverInfoResult(tReceiver.getReceiverName(), tReceiver.getAddressDetail(), tReceiver.getReceiverPhone(), tReceiver.getReceiverPrivacyPhone());
        if (isNeedDesensitize) {
            DesensitizeReceiverBaseInfoParam receiverBaseInfoParam = new DesensitizeReceiverBaseInfoParam(tReceiver.getReceiverName(), tReceiver.getAddressDetail(), tReceiver.getReceiverPhone(), tReceiver.getReceiverPrivacyPhone());
            DesensitizeReceiverInfoResult result = desensitizeReceiverInfo(receiverBaseInfoParam);
            if (Objects.nonNull(result)) {
                // 脱敏时发生异常 result可能为空 故判空
                receiverInfoResult = result;
            }
        }
        riderDeliveryOrderVo.setReceiverName(receiverInfoResult.getReceiverName());
        riderDeliveryOrderVo.setReceiverPhone(receiverInfoResult.getReceiverPhone());
        riderDeliveryOrderVo.setReceiverAddress(receiverInfoResult.getReceiverAddress());
        riderDeliveryOrderVo.setReceiverLatitude(isNeedDesensitize ? null : tReceiver.getLatitude());
        riderDeliveryOrderVo.setReceiverLongitude(isNeedDesensitize ? null : tReceiver.getLongitude());
    }

    private boolean isNeedDesensitize(Long tenantId, TRiderDeliveryOrder deliveryOrder) {
        if (! MccConfigUtil.isSupportDesensitizeReceiverInfoTenants(tenantId)) {
            return false;
        }
        if (Objects.equals(RiderDeliveryStatusEnum.DELIVERY_DONE.getCode(), deliveryOrder.getDeliveryStatus())) {
            return System.currentTimeMillis() - deliveryOrder.getDeliveryDoneTime() > 1000 * MccConfigUtil.getDesensitizeReceiverInfoTime();
        } else if (Objects.equals(RiderDeliveryStatusEnum.DELIVERY_CANCELLED.getCode(), deliveryOrder.getDeliveryStatus())) {
            return System.currentTimeMillis() - deliveryOrder.getLastEventTime() > 1000 * MccConfigUtil.getDesensitizeReceiverInfoTime();
        }
        return false;
    }

    /**
     * 获取渠道名称,歪马租户从枚举值获取渠道全称,非歪马租户动态查询获取渠道名称缩写
     */
    private String getChannelName(Integer channelId, Long tenantId, Map<Integer, String> channelAbbrMap) {
        if (MccConfigUtil.checkIsDHTenant(tenantId)) {
            return ChannelTypeEnum.findChannelNameByChannelId(channelId);
        } else {
            if (MapUtils.isEmpty(channelAbbrMap) ) {
                log.error("getChannelName channelAbbrMap is empty, tenantId: {}, channelId: {}", tenantId, channelId);
                return null;
            }
            return channelAbbrMap.get(channelId);
        }
    }

    private String getDeliveryOrderTypeName(Integer deliveryOrderType) {
        DeliveryOrderType deliveryOrderTypeEnum = DeliveryOrderType.findByValue(deliveryOrderType);
        if (Objects.isNull(deliveryOrderTypeEnum)) {
            return "未知";
        }
        switch (deliveryOrderTypeEnum) {
            case DELIVERY_RIGHT_NOW:
                return "立即送达";
            case DELIVERY_BY_BOOK_TIME:
                return "预订";
            default:
                return "未知";
        }
    }

    private ProductVO buildProductVO(long tenantId, long storeId, OCMSOrderItemVO ocmsOrderItemVO) {
        ProductVO productVO = new ProductVO();
        productVO.setSkuId(ocmsOrderItemVO.getCustomerSkuId());
        productVO.setUpcCode(ocmsOrderItemVO.getSkuCode());
        productVO.setSkuName(ocmsOrderItemVO.getSkuName());
        productVO.setPicUrl(ocmsOrderItemVO.getPicUrl());
        productVO.setSpecification(ocmsOrderItemVO.getSpecification());
        productVO.setSellUnit(ocmsOrderItemVO.getSellUnit());
        if (Objects.nonNull(ocmsOrderItemVO.getOriginalPrice())) {
            productVO.setOriginalTotalPrice(ocmsOrderItemVO.getOriginalPrice() * ocmsOrderItemVO.getQuantity());
        }
        productVO.setTotalPayAmount(ocmsOrderItemVO.getTotalPayAmount());
        productVO.setUnitPrice(ocmsOrderItemVO.getUnitPrice());
        productVO.setCount(ocmsOrderItemVO.getQuantity());
        productVO.setOrderItemOfflinePrice(ocmsOrderItemVO.getOfflinePrice());
        List<TagInfoVO> tagInfoVOS = org.assertj.core.util.Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(ocmsOrderItemVO.getFulfillmentTagList())) {
            for (String fulfillmentTag : ocmsOrderItemVO.getFulfillmentTagList()) {
                TagInfoVO tagInfoVO = new TagInfoVO();
                tagInfoVO.setName(fulfillmentTag);
                tagInfoVO.setType(FULFILLMENT_TAG);
                tagInfoVOS.add(tagInfoVO);
            }
        }
        if (CollectionUtils.isNotEmpty(ocmsOrderItemVO.getPickTagList())) {
            for (String pickTag : ocmsOrderItemVO.getPickTagList()) {
                TagInfoVO tagInfoVO = new TagInfoVO();
                tagInfoVO.setName(pickTag);
                tagInfoVO.setType(PICK_TAG);
                tagInfoVOS.add(tagInfoVO);

            }
        }
        productVO.setTagInfoList(tagInfoVOS);

        //商品标签(需要排除履约和拣货标签)
        //之前逻辑是前端过滤为商品属性的，这次只替换tagInfos
        if (MccConfigUtil.isDrunkHorseTenant(tenantId) && MccConfigUtil.isNewPickGrayStore(storeId)) {
            if (CollectionUtils.isNotEmpty(ocmsOrderItemVO.getPropertyList())) {
                List<ParsedPropertiesVO> propertiesViewVOList = ocmsOrderItemVO.getPropertyList().stream()
                        .filter(property -> MccConfigUtil.getPropertyColorMap().containsKey(property))
                        .map(property -> new ParsedPropertiesVO(property, MccConfigUtil.getPropertyColorMap().get(property)))
                        .collect(Collectors.toList());
                productVO.setParsedProperties(propertiesViewVOList);
            }
        } else {
            productVO.setTagInfos(Optional.ofNullable(ocmsOrderItemVO.getTagInfos())
                    .orElse(Collections.emptyList()).stream().map(tag -> {
                        TagInfoVO tagInfoVO = new TagInfoVO();
                        tagInfoVO.setName(tag.getName());
                        tagInfoVO.setType(tag.getType());
                        return tagInfoVO;
                    }).collect(Collectors.toList()));
        }

        productVO.setCurrentPrice(ocmsOrderItemVO.getCurrentPrice());
        return productVO;
    }

    private GiftVO buildGiftVO(Long storeId, OnlineGiftVO onlineGiftVO) {
        if (onlineGiftVO != null) {
            GiftVO giftVO = new GiftVO();
            giftVO.setGiftName(onlineGiftVO.getGiftName());
            giftVO.setGiftQuantity(onlineGiftVO.getGiftQuantity());
            giftVO.setSku(onlineGiftVO.getGiftSku());
            giftVO.setBelongSkuId(onlineGiftVO.getMainSkuId());
            if(StringUtils.isNotBlank(onlineGiftVO.getGiftSpec()) && MccConfigUtil.isNewPickGrayStore(storeId)) {
                giftVO.setSpecification(onlineGiftVO.getGiftSpec());
            }
            return giftVO;
        }
        return null;
    }

    /**
     * 上报骑手送达时候位置
     *
     * @param request 骑手送达时刻位置
     */
    public void riderArrivalLocation(RiderArrivalLocationRequest request){
        if(request == null){
            throw new CommonLogicException(ResultCode.PARAM_ERR);
        }
        RiderArrivalLocationTRequest locationTRequest = convertToTRequest(request);
        RiderOperateTResponse tResponse = riderOperateRpcService.riderArrivalLocation(locationTRequest);
        log.info("RiderDeliveryServiceWrapper-riderArrivalLocation end, req={}, res={}", locationTRequest, tResponse);
        checkResponse(tResponse);
    }

    /**
     * 上报骑手定位异常日志
     * @param request
     * @return
     */
    public void riderLocatingException(PostLocatingExceptionRequest request) {
        if (request == null) {
            throw new CommonLogicException(ResultCode.PARAM_ERR);
        }
        RiderLocatingExceptionTRequest exceptionTRequest = convertToRiderLocatingExceptionTRequest(request);
        RiderOperateTResponse tResponse = riderOperateRpcService.riderLocatingException(exceptionTRequest);
        log.info("RiderDeliveryServiceWrapper-riderLocatingException end, req={}, res={}", exceptionTRequest, tResponse);
        checkResponse(tResponse);
    }

    /**
     * 批量上报骑手定位日志（正常日志+异常日志）
     * @param request
     * @return
     */
    public void batchPostRiderLocatingLog(BatchPostLocatingLogRequest request) {
        if (request == null) {
            throw new CommonLogicException(ResultCode.PARAM_ERR);
        }
        BatchPostRiderLocatingLogTRequest batchPostRequest = convertToBatchPostRiderLocatingLogTRequest(request);
        RiderOperateTResponse tResponse = riderOperateRpcService.batchPostRiderLocatingLog(batchPostRequest);
        checkResponse(tResponse);
    }

    /**
     * 预订单立即发配送
     *
     * @param req 立即发配送请求
     */
    public void immediatelyDelivery(ImmediatelyDeliveryRequest req){
        if(req == null){
            throw new CommonLogicException(ResultCode.PARAM_ERR);
        }
        RiderImmediatelyDeliveryTRequest immediatelyDeliveryTRequest = convertToTRequest(req);
        RiderOperateTResponse tResponse = riderOperateRpcService.immediatelyDelivery(immediatelyDeliveryTRequest);
        log.info("RiderDeliveryServiceWrapper-immediatelyDelivery end, req={}, res={}", immediatelyDeliveryTRequest, tResponse);
        checkResponse(tResponse);
    }


    /**
     * 上报送达照片
     *
     * @param req 上报送达照片请求
     */
    public void postDeliveryProofPhoto(PostDeliveryProofPhotoRequest req){
        if(req == null){
            throw new CommonLogicException(ResultCode.PARAM_ERR);
        }
        RiderPostDeliveryProofPhotoTRequest tRequest = convertToTRequest(req);
        RiderOperateTResponse tResponse = riderOperateRpcService.postDeliveryProofPhoto(tRequest);
        log.info("RiderDeliveryServiceWrapper-postDeliveryProofPhoto end, req={}, res={}", tRequest, tResponse);
        checkResponse(tResponse);
    }


    /**
     * 查询送达配置
     * @param
     * @return
     */
    public DeliveryCompleteConfigVO getDeliveryCompleteConfig() throws TException {
        Long storeId = ApiMethodParamThreadLocal.getIdentityInfo().getStoreId();

        //非灰度门店返回null
        if (!MccConfigUtil.isDeliveryPhotoConfigurableGrayStore(storeId)) {
            return null;
        }

        //获取送达配置并map化
        List<DeliveryCompleteConfigVO> deliveryCompleteConfigVO = MccConfigUtil.getDeliveryCompleteConfig();
        Map<Integer, DeliveryCompleteConfigVO> configMap = deliveryCompleteConfigVO.stream()
                .collect(Collectors.toMap(DeliveryCompleteConfigVO::getOperationMode, Function.identity(), (k1, k2) -> k2));

        long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        long accountId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId();

        //查询是加盟店还是直营店
        BusinessPoiDTO poiDTO = oswServiceWrapper.queryOperatePoiByPoiId(tenantId, storeId, accountId);

        if (!configMap.containsKey(poiDTO.getOperationMode())) {
            log.error("未获取到对应经营模式下的送达配置, poiDto: {}", poiDTO);
            throw new RuntimeException("未获取到对应经营模式下的送达配置");
        }
        return configMap.get(poiDTO.getOperationMode());
    }

    /**
     * 查询虚拟号通话记录
     * @param
     * @return
     */
    public List<PrivacyNumberCallRecordVO> queryPrivacyNumberCallRecords(String viewOrderId, Integer channelId) throws TException {
        OCMSOrderVO ocmsOrderVO = getOCMSOrderVO(viewOrderId, channelId);
        if(ocmsOrderVO == null) {
            log.error("订单不存在");
            throw new BizException("订单不存在");
        }

        if(ocmsOrderVO.getCreateTime() == null) {
            log.error("订单创建时间不存在");
            throw new BizException("订单创建时间不存在");
        }

        long createTime = ocmsOrderVO.getCreateTime() / 1000;
        List<PrivateNumberCallBill> privateNumberCallBills = privacyNumberWrapper.queryCallBillAndRecording(viewOrderId, (int) createTime);


        return Optional.ofNullable(privateNumberCallBills).orElse(Collections.emptyList())
                .stream()
                .map(this::buildPrivacyNumberCallRecordVO)
                .collect(Collectors.toList());
    }

    private PrivacyNumberCallRecordVO buildPrivacyNumberCallRecordVO(PrivateNumberCallBill privateNumberCallBill) {
        return new PrivacyNumberCallRecordVO(privateNumberCallBill.getRecordId(), privateNumberCallBill.getBeginTime());
    }

    private RiderArrivalLocationTRequest convertToTRequest(RiderArrivalLocationRequest req) {
        if (req == null) {
            return null;
        }
        RiderArrivalLocationTRequest request = new RiderArrivalLocationTRequest();
        try{
            request.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
            request.setStoreId(ApiMethodParamThreadLocal.getIdentityInfo().getStoreId());
        }catch (Exception e){
            log.error("location convert error",e);
        }

        request.setLatitude(req.getLatitude());
        request.setLongitude(req.getLongitude());
        request.setDeliveryOrderId(req.getDeliveryOrderId());
        request.setRiderAccountId(req.getRiderAccountId());

        return request;
    }

    private RiderImmediatelyDeliveryTRequest convertToTRequest(ImmediatelyDeliveryRequest req) {
        RiderImmediatelyDeliveryTRequest request = new RiderImmediatelyDeliveryTRequest();
        request.setTenantId(req.getTenantId());
        request.setStoreId(req.getStoreId());
        request.setOrderId(req.getOrderId());
        request.setOperatorAccountId(req.getUserAccountId());
        return request;
    }

    private RiderLocatingExceptionTRequest convertToRiderLocatingExceptionTRequest(PostLocatingExceptionRequest request) {
        if (request == null) {
            return null;
        }
        RiderLocatingExceptionTRequest exceptionTRequest = new RiderLocatingExceptionTRequest();
        exceptionTRequest.setRiderAccountId(request.getRiderAccountId());
        exceptionTRequest.setUuid(request.getUuid());
        exceptionTRequest.setUtime(request.getUtime());
        exceptionTRequest.setManufacturer(request.getManufacturer());
        exceptionTRequest.setPhoneOS(request.getPhoneOS());
        exceptionTRequest.setExceptionType(request.getExceptionType());

        return exceptionTRequest;
    }

    private BatchPostRiderLocatingLogTRequest convertToBatchPostRiderLocatingLogTRequest(BatchPostLocatingLogRequest request) {
        if (request == null) {
            return null;
        }
        BatchPostRiderLocatingLogTRequest batchPostRequest = new BatchPostRiderLocatingLogTRequest();
        batchPostRequest.setLocatingLogList(request.getLocatingLogList().stream()
                .filter(Objects::nonNull)
                .map(this::convertToRiderLocatingLogTRequest)
                .collect(Collectors.toList()));
        return batchPostRequest;
    }


    private RiderLocatingLogTRequest convertToRiderLocatingLogTRequest(RiderLocatingLog request) {
        if (request == null) {
            return null;
        }
        RiderLocatingLogTRequest logTRequest = new RiderLocatingLogTRequest();
        logTRequest.setRiderAccountId(request.getRiderAccountId());
        logTRequest.setLocationIsUsed(request.getLocationIsUsed());
        logTRequest.setExceptionType(request.getExceptionType());
        logTRequest.setPhoneOS(request.getPhoneOS());
        logTRequest.setUuid(request.getUuid());
        logTRequest.setManufacturer(request.getManufacturer());
        logTRequest.setLogType(request.getLogType());
        logTRequest.setLatitude(request.getLatitude());
        logTRequest.setLongitude(request.getLongitude());
        logTRequest.setUtime(request.getUtime());
        logTRequest.setAccuracy(request.getAccuracy());
        logTRequest.setBearing(request.getBearing());
        logTRequest.setSpeed(request.getSpeed());
        return logTRequest;
    }

    private String buildScene(String category) {
        try {
            if (StringUtils.isBlank(category)) {
                return null;
            }
            return AddressSceneConvertUtils.convertCategory2Scene(category);
        } catch (Exception e) {
            log.error("buildScene error", e);
        }
        return null;
    }

}
