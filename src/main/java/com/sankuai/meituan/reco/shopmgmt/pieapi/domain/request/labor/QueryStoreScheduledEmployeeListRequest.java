package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/2/7 16:38
 **/
@TypeDoc(
        description = "查询门店当前有排班的人员名单"
)
@Data
@ApiModel("查询门店当前有排班的人员名单")
@AllArgsConstructor
@NoArgsConstructor
public class QueryStoreScheduledEmployeeListRequest {
    @FieldDoc(description = "门店id")
    @ApiModelProperty("门店id")
    private Long storeId;
}
