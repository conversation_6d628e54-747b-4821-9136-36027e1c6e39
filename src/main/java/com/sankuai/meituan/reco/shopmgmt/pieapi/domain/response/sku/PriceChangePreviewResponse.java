package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TypeDoc(
        description = "变价预览响应"
)
@Data
@ApiModel("变价预览响应")
public class PriceChangePreviewResponse {
    @FieldDoc(
            description = "预览门店商品价格", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "预览门店商品价格", required = true)
    private Double previewPrice;

    @FieldDoc(
            description = "是否存在待审核报价", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "是否存在待审核报价", required = true)
    private boolean existToReviewQuotes;
}
