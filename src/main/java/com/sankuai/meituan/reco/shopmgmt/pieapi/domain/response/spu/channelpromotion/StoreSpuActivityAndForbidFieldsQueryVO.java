package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.channelpromotion;

import java.util.List;
import java.util.Map;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/9/12
 */
@TypeDoc(
        description = "门店商品活动信息以及活动维度拦截变更属性列表"
)
@Data
public class StoreSpuActivityAndForbidFieldsQueryVO {
    @FieldDoc(
            description = "门店商品所有活动信息"
    )
    private List<StoreSkuActivityVO> storeSpuActivityVOList;
    @FieldDoc(
            description = "SPU禁止修改的属性枚举列表"
    )
    private Map<String, List<ForbidFieldAndActivityVO>> spuForbiddenOperationMap;
    @FieldDoc(
            description = "SkU禁止修改的属性枚举列表"
    )
    private Map<String, List<ForbidFieldAndActivityVO>> skuForbiddenOperationMap;
}
