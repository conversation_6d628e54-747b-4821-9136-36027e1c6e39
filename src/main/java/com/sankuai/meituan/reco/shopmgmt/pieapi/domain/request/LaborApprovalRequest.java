package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;
import java.util.Optional;

/**
 * <AUTHOR>
 * Email <EMAIL>
 * Date 2022/10/14 5:56 下午
 * Description
 */

@TypeDoc(
        description = "考勤异常申报请求"
)
@ApiModel("考勤异常申报请求")
@Data
public class LaborApprovalRequest {

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店ID")
    @NotNull
    private Long approvalId;

    @FieldDoc(
            description = "考勤统计id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "考勤统计id")
    @NotNull
    private Integer action;

    @FieldDoc(
            description = "申诉场景", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "申诉场景")
    private String remark;

}
