package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.WarehousePoiInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "返回"
)
@Data
@ApiModel("返回")
public class WarehousePickCompleteResponse {

    @FieldDoc(
            description = "是否已完成", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否已完成", required = true)
    private boolean isFinish;

    @FieldDoc(
            description = "是否需要分拣、true需要，false不需要", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否需要分拣", required = true)
    private boolean needSeed;

}
