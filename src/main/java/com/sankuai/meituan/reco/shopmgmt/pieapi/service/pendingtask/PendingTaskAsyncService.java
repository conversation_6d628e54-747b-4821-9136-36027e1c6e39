/*
 * Copyright (c) 2019 Meituan.com. All Rights Reserved.
 */
package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.config.PendingTaskConfig;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.appmodel.workbench.PendingTaskHelper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.store.saas.infrastructure.shield.common.monitor.CommonMonitorTransaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.*;


/**
 * <br><br>
 * Author: linjianyu <br>
 * Date: 2019-04-01 Time: 13:07
 * @since 2.1 权限迁移版本
 */
@Service
@Slf4j
@Rhino
public class PendingTaskAsyncService {

    @Autowired
    private PendingTaskHelper pendingTaskHelper;

    private static final Integer CORE_THREAD_SIZE = 4;
    private static ExecutorService executor = TraceExecutors.getTraceExecutorService(new ThreadPoolExecutor(
            CORE_THREAD_SIZE, Runtime.getRuntime().availableProcessors() * 2,
            10, TimeUnit.SECONDS, new LinkedBlockingQueue<>(2000),
            new ThreadFactoryBuilder().setNameFormat("PendingTask Pool thread-%d").build()
    ));

    @Degrade(rhinoKey = "PendingTaskAsyncService.queryPendingTaskCount",
            fallBackMethod = "queryPendingTaskCountFallback",
            timeoutInMilliseconds = 2000,isDegradeOnException = true,
            ignoreExceptions = CommonLogicException.class)
    @MethodLog(logRequest = true, logResponse = true)
    @CommonMonitorTransaction
    public Map<String, PendingTaskResult> queryPendingTaskCount(long tenantId, long entityId, int entityType,
                                                                List<Long> storeIdList, Set<String> authCodes) {
        if (CollectionUtils.isEmpty(authCodes)) {
            return Maps.newHashMap();
        }
        try {
            // 异步查询多模块任务量
            Set<AbstractBatchPendingTaskService> taskServices = getTaskServiceSet(authCodes);
            // 获取lion配置的模块
            int fromLionTasks = pendingTaskHelper.fromLionPendingTasks(authCodes);
            CountDownLatch latch = new CountDownLatch(taskServices.size()+fromLionTasks);
            Map<String, PendingTaskResult> countMap = Maps.newConcurrentMap();
            User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
            taskServices.forEach(service ->
                    executor.execute(new PendingTaskRunnable(()->{
                        try {
                            PendingTaskParam param = PendingTaskParam.builder()
                                    .tenantId(tenantId)
                                    .entityId(entityId)
                                    .entityType(entityType)
                                    .storeIds(storeIdList)
                                    .user(user)
                                    .authCodes(authCodes)
                                    .build();
                            Map<String, PendingTaskResult> batchMap = queryPendingTask(param, authCodes, service);
                            countMap.putAll(batchMap);
                        } finally {
                            latch.countDown();
                        }
                    }))
            );
            if (fromLionTasks > 0) {
                List<PendingTaskConfig> configList = pendingTaskHelper.filterSubMenuCodes(authCodes,storeIdList);
                configList.forEach(config->{
                    executor.execute(new PendingTaskRunnable(()->{
                        try {
                            PendingTaskParam param = PendingTaskParam.builder()
                                    .tenantId(tenantId)
                                    .entityId(entityId)
                                    .entityType(entityType)
                                    .storeIds(storeIdList)
                                    .user(user)
                                    .authCodes(authCodes)
                                    .build();
                            Map<String, PendingTaskResult> batchMap = pendingTaskHelper.queryPendingTaskFromLion(param, config);
                            countMap.putAll(batchMap);
                        } finally {
                            latch.countDown();
                        }
                    }));
                });
            }

            //  设置最大等待超时
            try {
                latch.await(1000, TimeUnit.MILLISECONDS);
            } catch (InterruptedException e) {
                log.error("query pending task error, msg:{}", e.getMessage(), e);
            }
            return countMap;
        } catch (Exception e) {
            log.error("异步查询首页角标异常", e);
            return Maps.newHashMap();
        }
    }

    @SuppressWarnings("unused")
    public Map<String, Integer> queryPendingTaskCountFallback(long tenantId, long entityId, int entityType,
                                                              List<Long> storeIdList, Set<String> authCodes) {
        return Maps.newHashMap();
    }


    /**
     * 执行查询待处理任务
     * @param param param
     * @param authCodes all authCodes
     * @param service query services
     * @return result map
     */
    private Map<String, PendingTaskResult> queryPendingTask(PendingTaskParam param,
                                                            Set<String> authCodes,
                                                            AbstractBatchPendingTaskService service) {
        Map<AuthCodeEnum, PendingTaskResult> countMap = service.pendingTaskCount(param);
        log.info("queryPendingTask， countMap：{}", countMap);
        Map<String, PendingTaskResult> taskMap = Maps.newHashMap();
        for (AuthCodeEnum authModule : countMap.keySet()) {
            if (!authCodes.contains(authModule.getAuthCode())) {
                continue;
            }
            PendingTaskResult result = countMap.get(authModule);
            if (authCodes.contains(authModule.getAuthCode()) && result.getCount() > 0) {
                taskMap.put(authModule.getAuthCode(), result);
            }
        }
        return taskMap;
    }

    /**
     * 查询权限codes对应的角标查询服务列表
     * @param authCodes 权限codes
     * @return 角标查询服务列表
     */
    private Set<AbstractBatchPendingTaskService> getTaskServiceSet(Set<String> authCodes) {
        if (CollectionUtils.isEmpty(authCodes)) {
            return Sets.newHashSet();
        }
        Map<AbstractBatchPendingTaskService, String> serviceMap = Maps.newHashMap();
        authCodes.forEach(authCode -> {
            AbstractBatchPendingTaskService service = AbstractBatchPendingTaskService.PENDING_TASK_SERVICE_MAP.get(AuthCodeEnum.authOf(authCode));
            if (service != null) {
                serviceMap.putIfAbsent(service, authCode);
            }
        });
        return serviceMap.keySet();
    }

}
