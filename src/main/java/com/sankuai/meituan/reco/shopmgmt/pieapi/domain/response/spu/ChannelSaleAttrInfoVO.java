package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import java.util.List;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.SaleAttrVo;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelSaleAttributeDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.SpuChannelSaleAttributeDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-12-26
 */
@TypeDoc(
        description = "销售属性"
)
@Data
@ApiModel("销售属性")
public class ChannelSaleAttrInfoVO {
    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID")
    private int channelId;

    @FieldDoc(
            description = "销售属性", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "销售属性")
    private List<SaleAttrVo> saleAttrInfoList;

    @FieldDoc(
            description = "SKU关联图片设置，仅京东渠道有效", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "SKU关联图片设置")
    private Boolean skuImageSetting;

    public static ChannelSaleAttrInfoVO of(SpuChannelSaleAttributeDTO spuChannelSaleAttributeDTO) {
        ChannelSaleAttrInfoVO channelSaleAttrInfoVO = new ChannelSaleAttrInfoVO();
        channelSaleAttrInfoVO.setChannelId(spuChannelSaleAttributeDTO.getChannelId());
        channelSaleAttrInfoVO.setSaleAttrInfoList(Fun.map(spuChannelSaleAttributeDTO.getSpuSaleAttributes(), SaleAttrVo::fromDTO));
        channelSaleAttrInfoVO.setSkuImageSetting(spuChannelSaleAttributeDTO.getSkuImageSetting());
        return channelSaleAttrInfoVO;
    }

    public static ChannelSaleAttrInfoVO of(ChannelSaleAttributeDTO channelSaleAttributeDTO) {
        ChannelSaleAttrInfoVO channelSaleAttrInfoVO = new ChannelSaleAttrInfoVO();
        channelSaleAttrInfoVO.setChannelId(channelSaleAttributeDTO.getChannelId());
        channelSaleAttrInfoVO.setSaleAttrInfoList(Fun.map(channelSaleAttributeDTO.getSaleAttributes(), SaleAttrVo::fromDTO));
        channelSaleAttrInfoVO.setSkuImageSetting(channelSaleAttributeDTO.getSkuImageSetting());
        return channelSaleAttrInfoVO;
    }

    public static ChannelSaleAttrInfoVO build(int channelId, List<SaleAttrVo> saleAttrInfoList) {
        ChannelSaleAttrInfoVO channelSaleAttrInfoVO = new ChannelSaleAttrInfoVO();
        channelSaleAttrInfoVO.setChannelId(channelId);
        channelSaleAttrInfoVO.setSaleAttrInfoList(saleAttrInfoList);
        return channelSaleAttrInfoVO;
    }

    public static ChannelSaleAttrInfoVO build(int channelId, List<SaleAttrVo> saleAttrInfoList, Boolean skuImageSetting) {
        ChannelSaleAttrInfoVO channelSaleAttrInfoVO = new ChannelSaleAttrInfoVO();
        channelSaleAttrInfoVO.setChannelId(channelId);
        channelSaleAttrInfoVO.setSaleAttrInfoList(saleAttrInfoList);
        channelSaleAttrInfoVO.setSkuImageSetting(skuImageSetting);
        return channelSaleAttrInfoVO;
    }

    public ChannelSaleAttributeDTO toOcmsDTO() {
        ChannelSaleAttributeDTO channelSaleAttributeDTO = new ChannelSaleAttributeDTO();
        channelSaleAttributeDTO.setChannelId(channelId);
        channelSaleAttributeDTO.setSaleAttributes(Fun.map(saleAttrInfoList, SaleAttrVo::toOcmsDTO));
        channelSaleAttributeDTO.setSkuImageSetting(skuImageSetting);
        return channelSaleAttributeDTO;
    }

    public SpuChannelSaleAttributeDTO toBizDTO() {
        SpuChannelSaleAttributeDTO spuChannelSaleAttributeDTO = new SpuChannelSaleAttributeDTO();
        spuChannelSaleAttributeDTO.setChannelId(channelId);
        spuChannelSaleAttributeDTO.setSpuSaleAttributes(Fun.map(saleAttrInfoList, SaleAttrVo::convertBizDTO));
        spuChannelSaleAttributeDTO.setSkuImageSetting(skuImageSetting);
        return spuChannelSaleAttributeDTO;
    }

}
