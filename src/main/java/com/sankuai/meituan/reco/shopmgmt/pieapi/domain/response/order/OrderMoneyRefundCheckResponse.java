package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.util.List;

/**
 * Created by <PERSON>xiaoyu on 2023/3/7 14:19
 */
@TypeDoc(
        description = "金额退页面检查的response"
)
@Data
public class OrderMoneyRefundCheckResponse {
    @FieldDoc(
            description = "金额退页面检查的商品信息"
    )
    private List<OrderItemMoneyRefundCheckVO> moneyRefundCheckVOList;

}
