package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "波次拣货待集单数量请求"
)
@ApiModel("波次拣货待集单数量请求")
@Data
public class WarehousePickTabNumRequest {

    @FieldDoc(
            description = "tab标签类型列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "tab标签类型列表")
    private List<Integer> tabTypeList;

}
