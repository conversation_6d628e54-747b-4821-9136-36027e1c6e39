package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.wavepick.converter;

import com.sankuai.meituan.reco.pickselect.thrift.wave.dto.WarehouseSeedWaitModuleDTO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.WarehouseSeedWaitModuleVO;
import org.mapstruct.Mapper;

/**
 * @Description:
 * @Author: zhangjian155
 * @Date: 2022/8/25 17:32
 */
@Mapper(componentModel = "spring", uses = {WarehouseSeedWaitTaskModuleConverter.class, WarehouseGoodsOwnerModuleConverter.class, WarehouseReceiveStoreInfoConverter.class})
public abstract class WarehouseSeedWaitModuleConverter {
    public abstract WarehouseSeedWaitModuleVO convert2Response(WarehouseSeedWaitModuleDTO warehouseSeedWaitModuleDTO);
}
