package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pullnew;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date 2021/06/09 16:31
 * Description: 摊位操作请求，包括新建、编辑摊位操作
 */
@TypeDoc(
        description = "展示二维码服务",
        authors = {
                "liyang176"
        },
        version = "V1.0"
)
@Data
@ApiModel("拼接海报")
public class CombinePictureRequest {

    @FieldDoc(
            description = "x坐标"
    )
    @ApiModelProperty(value = "x坐标")
    private Integer x;

    @FieldDoc(
            description = "y坐标"
    )
    @ApiModelProperty(value = "y坐标")
    private Integer y;

    @FieldDoc(
            description = "文件"
    )
    @ApiModelProperty(value = "文件")
    private MultipartFile file;
}
