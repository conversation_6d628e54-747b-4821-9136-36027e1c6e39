package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 售后订单子类型数量响应
 * @author: caimengluan
 * @date: 2022/8/11
 * @time: 16:37
 * Copyright (C) 2019 Meituan
 * All rights reserved
 */
@TypeDoc(
        description = "售后订单子类型数量响应"
)
@Data
@ApiModel("售后订单子类型数量响应")
public class RefundGoodsSubTypeCountResponse {

    @FieldDoc(
            description = "全部数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "全部数量", required = true)
    private Integer allSubTypeCount;

    @FieldDoc(
            description = "仅退款数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "仅退款数量", required = true)
    private Integer onlyRefundCount;

    @FieldDoc(
            description = "退货退款数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "退货退款数量", required = true)
    private Integer returnAndRefundCount;

    @FieldDoc(
            description = "用户拒收数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "用户拒收数量", required = true)
    private Integer rejectByCustomerCount;

    @FieldDoc(
            description = "申诉单数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "申诉单数量", required = true)
    private Integer appealCount;

}
