package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.push;
// Copyright (C) 2019 Meituan
// All rights reserved

import com.alibaba.fastjson.JSON;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: <EMAIL>
 * @class: PushReceivedRequest
 * @date: 2019-12-30 16:17:07
 * @desc:
 */
@TypeDoc(
        description = "push接收到的请求"
)
@ApiModel("push接收到的请求")
@Data
public class PushReceivedRequest {


    @FieldDoc(
            description = "平台msgId"
    )
    @ApiModelProperty(value = "平台msgId")
    private String platMsgId;

    @FieldDoc(
            description = "push类型SHARK, NOTIFICATION"
    )
    @ApiModelProperty(value = "push类型SHARK, NOTIFICATION")
    private String pushType;

    @FieldDoc(
            description = "消息ID"
    )
    @ApiModelProperty(value = "消息ID")
    private Long msgId;

    @FieldDoc(
            description = "消息内部序列ID"
    )
    @ApiModelProperty(value = "消息内部序列ID")
    private Integer serialNum;

    @FieldDoc(
            description = "子状态码"
    )
    @ApiModelProperty(value = "子状态码")
    private Integer subCode;


    @FieldDoc(
            description = "设备Id"
    )
    @ApiModelProperty(value = "设备Id")
    private String uuid;

}
