package com.sankuai.meituan.reco.shopmgmt.pieapi.facade;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.ImageServiceCircuitBreakerListener;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.osw.OSWServiceWrapper;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.shangou.saas.common.file.ImageUtil;
import com.sankuai.shangou.commons.exception.common.exceptions.SystemException;
import com.sankuai.shangou.infra.osw.api.poi.dto.response.BusinessPoiDTO;
import com.sankuai.shangou.infra.osw.api.poi.warehouse.dto.response.WarehouseDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.DependsOn;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;

/**
 * <AUTHOR>
 * @since 2023/7/18 16:40
 * @description 上传拣货图片，依赖集团内部的图片服务，内部有特殊的降级逻辑,勿复用
 **/
@Rhino
@Slf4j
@DependsOn({"fulfillWarningMessagePusher", "pushMessageService"})
public class PickImageServiceWrapper {
    @Resource
    private OSWServiceWrapper oswServiceWrapper;
    private static final String DRUNK_HORSE_MARK_URL = "/sgfn/8b9538a98c070590d3a46088987d7f074999.png" + "@24P";
    private static final String DATE_TIME_FORMATTER_PATTERN = "yyyy.MM.dd HH:mm";
    private static final String PIPE_CHAR = "|";
    private static final String ZOOM_WITH = "720w";

    @Degrade(rhinoKey = "PickImageServiceWrapper.uploadImage", fallBackMethod = "uploadImageFallBack",
            isDegradeOnException = true, circuitBreakerListener = ImageServiceCircuitBreakerListener.class, timeoutInMilliseconds = 5000)
    @MethodLog(logResponse = true, logRequest = false)
    public String uploadImage(byte[] content, String originFileName) {
        return ImageUtil.upload(content, originFileName);
    }

    @Degrade(rhinoKey = "PickImageServiceWrapper.watermark", fallBackMethod = "watermarkFallBack",
            isDegradeOnException = true, timeoutInMilliseconds = 2000)
    @MethodLog(logResponse = true, logRequest = true)
    public String watermark(String originalImageLink) {
        //获取门店信息
        WarehouseDTO warehouseDTO = oswServiceWrapper.queryWarehouseInfoById(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId(),
                ApiMethodParamThreadLocal.getIdentityInfo().getStoreId());
        String poiName = warehouseDTO.getName();

        //对图片处理参数编码
        String uploadTimeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern(DATE_TIME_FORMATTER_PATTERN));

        String processParam = ZOOM_WITH + urlEncode(PIPE_CHAR)
                + "watermark=1&&object=" + base64AndUrlEncode(DRUNK_HORSE_MARK_URL) + "&p=7&x=20&y=20" + urlEncode(PIPE_CHAR)
                + "watermark=2&&text=" + base64AndUrlEncode(poiName) + "&size=20&p=7&x=56&y=64" + urlEncode(PIPE_CHAR)
                + "watermark=2&&text=" + base64AndUrlEncode(uploadTimeStr) + "&size=20&p=7&x=56&y=27";


        //拼接水印
        return originalImageLink + "@" + processParam;
    }

    private String base64AndUrlEncode(String originalStr) {
        if (StringUtils.isBlank(originalStr)) {
            return "";
        }

        try {
            return URLEncoder.encode(Base64.getEncoder().encodeToString(originalStr.getBytes()), "UTF-8");
        } catch (Exception e) {
            throw new SystemException("base64 and url encode error", e);
        }
    }

    private String urlEncode(String originalStr) {
        if (StringUtils.isBlank(originalStr)) {
            return "";
        }

        try {
            return URLEncoder.encode(originalStr, "UTF-8");
        } catch (Exception e) {
            throw new SystemException("url encode error", e);
        }

    }

    public String uploadImageFallBack(byte[] content, String originFileName) {
        log.warn("上传图片接口已被降级");
        throw new BizException("上传图片功能暂不可用，可直接出库，如有已上传失败的图片先删除。高价值商品建议水印相机拍照留存!");
    }

    public String watermarkFallBack(String originalImageLink) {
        log.warn("图片加水印接口已被降级");
        return originalImageLink;
    }
}
