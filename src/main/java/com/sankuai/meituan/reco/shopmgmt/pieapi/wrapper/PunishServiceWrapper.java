package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.shangou.commons.thrift.publisher.request.UserContext;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.waima.support.api.service.punish.TPunishService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: hezhengyu
 * @create: 2023-11-17 14:30
 */
@Slf4j
@Service
public class PunishServiceWrapper {
    @Resource
    private TPunishService punishService;

    public CommonResponse<Integer> countWaitingAppealTicket(Long tenantId, String accountName) {
        CommonResponse<Integer> response = CommonResponse.success(0);

        UserContext userContext = new UserContext();
        userContext.setTenantId(tenantId);
        userContext.setAccountName(accountName);
        try {
            TResult<Integer> result = punishService.countWaitingAppealTicket(userContext);
            log.info("countWaitingAppealTicket, req:{}, resp:{}", userContext, JSON.toJSONString(result));
            if (result != null && result.isSuccess()) {
                response = CommonResponse.success(result.getData());
            }
        } catch (Exception e) {
            log.error("Failed to count waiting appeal ticket", e);
            response = CommonResponse.fail(ResultCode.FAIL.getCode(), "查询待申述罚单统计出错");
        }
        return response;
    }
}
