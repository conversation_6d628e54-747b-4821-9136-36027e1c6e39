package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.assistant;

import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.ParamException;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2021/1/28 17:37
 */
@Setter
@Getter
public class TaskListQueryRequest {

    private Integer taskType;

    private Integer page;

    private Integer pageSize;


    public void validate() {
        if (this.taskType == null) {
            throw new ParamException("任务类型不能为空");
        }
    }

}
