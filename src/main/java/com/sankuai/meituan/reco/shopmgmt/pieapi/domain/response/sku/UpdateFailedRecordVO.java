package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TypeDoc(
        description = "更新错误信息"
)
@Data
@ApiModel("更新错误信息")
public class UpdateFailedRecordVO {

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID", required = true)
    private Long storeId;

    @FieldDoc(
            description = "商品编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品编码", required = true)
    private String skuId;

    @FieldDoc(
            description = "错误类型 1-商品 2-库存", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "错误类型 1-商品 2-库存", required = true)
    private Integer errorType;

    @FieldDoc(
            description = "错误描述", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "错误描述", required = true)
    private String errorMsg;

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID", required = true)
    private Integer channelId;
}

