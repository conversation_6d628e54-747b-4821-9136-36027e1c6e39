package com.sankuai.meituan.reco.shopmgmt.pieapi.domain;
// Copyright (C) 2019 Meituan
// All rights reserved

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: <EMAIL>
 * @class: XmAccountInfoVO
 * @date: 2019-09-25 11:44:45
 * @desc:
 */
@TypeDoc(
        description = "大象账号信息"
)
@Data
@ApiModel("大象账号信息")
public class XmAccountInfoVO {

    @FieldDoc(
            description = "登录账号"
    )
    @ApiModelProperty(
            value = "登录账号"
    )
    private String account;

    @FieldDoc(
            description = "登录token"
    )
    @ApiModelProperty(
            value = "登录token"
    )
    private String xmToken;


    @FieldDoc(
            description = "外卖门店Id"
    )
    @ApiModelProperty(
            value = "外卖门店Id"
    )
    private String wmPoiId;

    @FieldDoc(
            description = "外卖门店名称"
    )
    @ApiModelProperty(
            value = "外卖门店名称"
    )
    private String wmPoiName;

    public XmAccountInfoVO(String account, String xmToken, String wmPoiId, String wmPoiName) {
        this.account = account;
        this.xmToken = xmToken;
        this.wmPoiId = wmPoiId;
        this.wmPoiName = wmPoiName;
    }
}
