package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/10/19 17:34
 **/
@ApiModel("查看考勤统计响应")
@TypeDoc(description = "查看考勤统计响应")
@Data
public class AttendanceStatisticsVO {
    @ApiModelProperty("日期 格式yyyy-MM-dd")
    @FieldDoc(description = "日期 格式yyyy-MM-dd")
    private String date;

    @ApiModelProperty("考勤是否异常")
    @FieldDoc(description = "考勤是否异常")
    private Boolean isAbnormal;

    @ApiModelProperty("考勤是否结算")
    @FieldDoc(description = "考勤是否结算")
    private Boolean isSettled;

    @ApiModelProperty("考勤结果")
    @FieldDoc(description = "考勤结果")
    private List<AttendanceResultVO> attendanceResults;

    @FieldDoc(description = "加班申请id")
    @ApiModelProperty("加班申请id")
    public Long extraWorkAttendanceApplyId;

    @FieldDoc(description = "加班申请状态")
    @ApiModelProperty("加班申请状态")
    public Integer extraWorkAttendanceApplyStatus;
}
