package com.sankuai.meituan.reco.shopmgmt.pieapi.domain;

import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MoneyUtils;
import com.sankuai.meituan.shangou.empower.price.client.dto.present_price.PresentChannelPriceEqualSignDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * @Author: wangyihao04
 * @Date: 2021-04-13 20:32
 * @Mail: <EMAIL>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PriceConsistencyVO {
    private ChannelStoreSkuKey skuKey;
    private Boolean priceConsistency;
    private Boolean atPromotion;
    private String presentPrice;

    public static PriceConsistencyVO valueOf(PresentChannelPriceEqualSignDTO dto) {
        return PriceConsistencyVO.builder()
                .skuKey(ChannelStoreSkuKey.convertTo(dto.getSkuKey()))
                .priceConsistency(dto.getPriceEqualSign())
                .atPromotion(dto.getAtPromotion())
                .presentPrice(MoneyUtils.centToYuanStringByDown(dto.getPresentPrice()))
                .build();

    }
}

