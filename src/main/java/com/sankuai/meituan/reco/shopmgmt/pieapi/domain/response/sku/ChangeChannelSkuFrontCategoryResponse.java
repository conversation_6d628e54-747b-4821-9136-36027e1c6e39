package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description: 修改线上商品前台分类响应
 * @author: liyu44
 * @create: 2020-02-04
 **/
@TypeDoc(
        description = "修改线上商品前台分类响应"
)
@Data
@ApiModel("修改线上商品前台分类响应")

public class ChangeChannelSkuFrontCategoryResponse {

    @FieldDoc(
            description = "任务ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "任务ID")
    private Long taskId;

    @FieldDoc(
            description = "错误记录", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "错误记录")
    private List<FailedRecordVO> errorRecordList;
}
