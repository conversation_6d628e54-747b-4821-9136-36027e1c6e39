package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

@TypeDoc(
        description = "分拣商品对应门店信息"
)
@Data
@ApiModel("分拣商品对应门店信息")
@Builder
public class WarehouseSeedStoreItemModuleVO {

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "门店id")
    private Long storeId;

    @FieldDoc(
            description = "门店名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @FieldDoc(
            description = "数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "分拣数量")
    private Integer seedNum;
}
