package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@TypeDoc(
        description = "待拣货请求"
)
@ApiModel("待拣货请")
@Data
public class WarehouseWaitPickQueryRequest {

    @FieldDoc(
            description = "页码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "页码")
    private Long pageNum;

    @FieldDoc(
            description = "个数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "个数")
    private Integer pageSize;

}
