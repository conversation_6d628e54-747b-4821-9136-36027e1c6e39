package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: zhangpeijin
 * @Date: 2020-12-01 19:31
 */
@ApiModel(
        "门店间类目接口"
)
@TypeDoc(
        description = "门店间类目接口"
)
@Data
public class ContrastStoreCategoryRequestVO {
    @FieldDoc(
            description = "门店id"
    )
    @ApiModelProperty("门店id")
    @NotNull
    public Long storeId;

    @FieldDoc(
            description = "竞对门店列表"
    )
    @ApiModelProperty("竞对门店列表")
    @NotEmpty
    public List<ContrastStoreKeyVO> contrastStoreList;

    @FieldDoc(
            description = "父类目id"
    )
    @ApiModelProperty("父类目id")
    public String parentCategoryId;

    @FieldDoc(
            description = "父类目级别"
    )
    @ApiModelProperty("父类目级别")
    public String parentCategoryLevel;
}
