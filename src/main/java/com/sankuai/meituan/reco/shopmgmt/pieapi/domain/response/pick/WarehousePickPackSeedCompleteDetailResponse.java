package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.PickPackSeedCompleteDetailVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.PickPackSeedWaitDetailVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.SeedProgressVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description:
 * @Auther: nifei
 * @Date: 2023/8/21 16:30
 */
@TypeDoc(
        description = "查询已分拣详情返回"
)
@Data
@ApiModel("查询已分拣详情返回")
public class WarehousePickPackSeedCompleteDetailResponse {

    @FieldDoc(
            description = "装箱进度", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "装箱进度")
    private SeedProgressVO seedProgress;

    @FieldDoc(
            description = "是否还有数据", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否还有数据")
    private Boolean hasMore;

    @FieldDoc(
            description = "分拣商品列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "分拣商品列表")
    private List<PickPackSeedCompleteDetailVO> dataList;
}
