package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TypeDoc(
        description = "容器信息更新请求"
)
@ApiModel("容器信息更新请求")
@Data
public class WarehouseContainerBoxUpdateRequest {

    @FieldDoc(
            description = "波次号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "波次号")
    private String waveId;

    @FieldDoc(
            description = "波次任务号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "波次任务号")
    private Integer taskId;

    @FieldDoc(
            description = "容器码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "容器码")
    private String containerBoxId;

    @FieldDoc(
            description = "操作类型 0 新增  1删除", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "操作类型 0 新增  1删除")
    private Integer operateType;
}
