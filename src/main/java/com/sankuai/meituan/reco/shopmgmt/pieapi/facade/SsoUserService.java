package com.sankuai.meituan.reco.shopmgmt.pieapi.facade;

import java.sql.Timestamp;

import javax.annotation.Resource;

import com.sankuai.it.sso.sdk.vo.SessionContextVO;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import com.sankuai.it.iam.configscenter.bos.AccessPointBO;
import com.sankuai.it.iam.configscenter.bos.ThriftAccessPointBO;
import com.sankuai.it.iam.configscenter.structs.BaInfoDTO;
import com.sankuai.it.iam.ssoopenapi.rpc.session.dto.BaInfo;
import com.sankuai.it.iam.ssoopenapi.rpc.session.dto.SessionDto;
import com.sankuai.it.iam.ssoopenapi.rpc.session.dto.SessionResponseDto;
import com.sankuai.it.iam.ssoopenapi.rpc.session.dto.XmAlTokenDTO;
import com.sankuai.it.iam.ssoopenapi.rpc.session.service.SessionOpenBaService;
import com.sankuai.it.iam.ssoopenapi.rpc.util.BaUtil;
import com.sankuai.it.sso.sdk.configuration.RuntimeSSOConfigs;
import com.sankuai.it.sso.sdk.service.RemoteSessionOpenService;
import com.sankuai.it.sso.sdk.utils.EnvUtil;
import com.sankuai.it.sso.sdk.vo.SessionResponseVO;
import com.sankuai.it.sso.sdk.vo.SessionVO;
import com.sankuai.it.sso.sdk.vo.XmAltokenVO;
import com.sankuai.meituan.auth.vo.User;

import lombok.extern.slf4j.Slf4j;
import net.jcip.annotations.ThreadSafe;

/**
 * <AUTHOR>
 * @since 2022/2/28
 */
@Service
@Slf4j
public class SsoUserService {
    private static RuntimeSSOConfigs runtimeSSOConfigs = RuntimeSSOConfigs.getInstance();
    private static volatile LocalThriftSessionOpenServiceImpl thriftImpl;

    @Value("${sso.clientId}")
    private String localSsoClientId;
    @Resource(name = "ssoSecret")
    private String localClientSecret;

    public User getUserInfoByToken(String accessToken) {
        if (StringUtils.isEmpty(accessToken)) {
            return null;
        }
        initThriftService();
        try {
            return convertSessionVO2User(thriftImpl.getUserInfoByToken(accessToken, null));
        }
        catch (Exception e) {
            log.info("Thrift getUserInfoByToken failed", e);
        }
        return null;
    }

    private static User convertSessionVO2User(SessionVO sessionVO){
        if(sessionVO == null){
            return null;
        }
        User user = new User();
        user.setTenantId(String.valueOf(sessionVO.getTenantId()));
        user.setName(sessionVO.getName());
        user.setLogin(sessionVO.getLoginName());
        user.setEmail(sessionVO.getEmail());
        user.setCode(sessionVO.getStaffId());
        user.setId((int) (long) sessionVO.getUid());
        return user;
    }

    private void initThriftService() {
        if (thriftImpl == null) {
            synchronized (this) {
                if (thriftImpl == null) {
                    thriftImpl = new LocalThriftSessionOpenServiceImpl(localSsoClientId, localClientSecret);
                }
            }
        }
        AccessPointBO accessPointBO = runtimeSSOConfigs.getAccessPoint();
        thriftImpl.setThriftAccessPointBO(accessPointBO.getThrift());
    }


    @ThreadSafe
    private static class LocalThriftSessionOpenServiceImpl implements RemoteSessionOpenService {
        private ThriftAccessPointBO thriftAccessPointBO;

        private SessionOpenBaService.Iface sessionOpenService;

        private String localSsoClientId;
        private String localClientSecret;

        public LocalThriftSessionOpenServiceImpl(String localSsoClientId, String localClientSecret) {
            this.localSsoClientId = localSsoClientId;
            this.localClientSecret = localClientSecret;
        }

        public boolean setThriftAccessPointBO(ThriftAccessPointBO thriftAccessPointBO) {
            if (thriftAccessPointBO == null) {
                return false;
            }
            if (thriftAccessPointBO.logicEquals(this.thriftAccessPointBO)
                    && sessionOpenService != null) {
                //之前已经设置过，返回true
                return true;
            }
            this.thriftAccessPointBO = thriftAccessPointBO;
            try {
                ThriftClientProxy proxy = new ThriftClientProxy();
                proxy.setAppKey(EnvUtil.loadAppName(RuntimeSSOConfigs.getInstance().getClientId()));
                String remoteAppkey = this.thriftAccessPointBO.getOpenApiThrift().getRemoteAppkey();
                int remoteServerPort = this.thriftAccessPointBO.getOpenApiThrift().getRemoteServerPort();
                String serverIpPorts = this.thriftAccessPointBO.getOpenApiThrift().getServerIpPorts();

                if (!StringUtils.isEmpty(serverIpPorts)) {
                    proxy.setServerIpPorts(serverIpPorts);
                    log.info("SSO接入点配置，env:{}, SessionOpenService Thrift调用地址：{}", EnvUtil.getEnv(), serverIpPorts);
                }
                else {
                    proxy.setRemoteAppkey(remoteAppkey);
                    proxy.setRemoteServerPort(remoteServerPort);
                    log.info("SSO接入点配置，env:{}, SessionOpenService Thrift调用地址：{},{}", EnvUtil.getEnv(), remoteAppkey, remoteServerPort);
                }

                proxy.setServiceInterface(SessionOpenBaService.class);
                proxy.afterPropertiesSet();
                sessionOpenService = (SessionOpenBaService.Iface) proxy.getObject();
                return true;
            }
            catch (Exception e) {
                log.error(e.getMessage(), e);
                return false;
            }
        }

        @Override
        public SessionVO getUserInfoByToken(String accessToken, String tokenPairs) throws TException {
            BaInfoDTO baInfoDTO = signForRpc("getUserInfoByToken");
            BaInfo baInfo = new BaInfo();
            baInfo.setSign(baInfoDTO.getSign());
            baInfo.setClientId(baInfoDTO.getClientId());
            baInfo.setClientSecret(baInfoDTO.getClientSecret());
            baInfo.setTimeStamp(baInfoDTO.getTimeStamp());
            baInfo.setMethodName(baInfoDTO.getMethodName());
            SessionDto sessionDto = sessionOpenService.getUserInfoByToken(baInfo, accessToken, tokenPairs);
            if (sessionDto == null || sessionDto.getUid() <= 0) {
                log.info("Thrift getUserInfoByToken failed: baInfo={}, ret={}", baInfo, sessionDto);
                return null;
            }
            return sessionDto2SessionVO(sessionDto);
        }

        public BaInfoDTO signForRpc(String method) {
            BaInfoDTO baInfo = new BaInfoDTO();
            baInfo.setClientId(localSsoClientId);
            baInfo.setClientSecret(localClientSecret);
            baInfo.setMethodName(method);
            long timeMillis = System.currentTimeMillis();
            String date = BaUtil.getAuthDate(new Timestamp(timeMillis));
            // 时间戳，精确到毫秒
            baInfo.setTimeStamp(timeMillis);
            String authorization = BaUtil.getAuthorization(
                    null, org.apache.commons.lang.StringUtils.upperCase(baInfo.getMethodName()),
                    date, baInfo.getClientId(), baInfo.getClientSecret()
            );
            String dateAuth = String.format("%d#%s", baInfo.getTimeStamp(), authorization);
            baInfo.setSign(dateAuth);
            return baInfo;
        }

        private SessionVO sessionDto2SessionVO(SessionDto sessionDto) {
            SessionVO sessionVO = new SessionVO();
            sessionVO.setEmail(sessionDto.getEmail());
            sessionVO.setExpired(sessionDto.isIsExpired());
            sessionVO.setLoginName(sessionDto.getLoginName());
            sessionVO.setName(sessionDto.getName());
            sessionVO.setStaffId(sessionDto.getStaffId());
            sessionVO.setTenantId(sessionDto.getTenantId());
            sessionVO.setUid(sessionDto.getUid());
            sessionVO.setVerifyType(sessionDto.getVerifyType());
            sessionVO.setIsVerified(sessionDto.getIsVerified());
            sessionVO.setVerifyExpireTime(sessionDto.getVerifyExpireTime());
            return sessionVO;
        }

        @Override
        public SessionVO getUserInfoByAlToken(XmAltokenVO altokenVO) throws Exception {
            BaInfoDTO baInfoDTO = signForRpc("getUserInfoByAlToken");
            BaInfo baInfo = new BaInfo();
            baInfo.setSign(baInfoDTO.getSign());
            baInfo.setClientId(baInfoDTO.getClientId());
            baInfo.setClientSecret(baInfoDTO.getClientSecret());
            baInfo.setTimeStamp(baInfoDTO.getTimeStamp());
            baInfo.setMethodName(baInfoDTO.getMethodName());

            XmAlTokenDTO xmAlTokenDTO = new XmAlTokenDTO();
            xmAlTokenDTO.setDeviceUUID(altokenVO.getDeviceUUID());
            xmAlTokenDTO.setUid(altokenVO.getUid() == null ? -1 : altokenVO.getUid());
            xmAlTokenDTO.setAlToken(altokenVO.getAlToken());

            SessionDto sessionDto = sessionOpenService.getUserInfoByAlToken(baInfo, xmAlTokenDTO);
            if (sessionDto == null || sessionDto.getUid() <= 0) {
                log.info("Thrift getUserInfoByAlToken failed: baInfo={}, ret={}", baInfo, sessionDto);
                return null;
            }
            return sessionDto2SessionVO(sessionDto);
        }

        @Override
        public SessionResponseVO getUserInfoByTokenV2(String accessToken, String tokenPairs,SessionContextVO sessionContextVO) throws Exception {
            BaInfoDTO baInfoDTO = signForRpc("getUserInfoByTokenV2");
            BaInfo baInfo = new BaInfo();
            baInfo.setSign(baInfoDTO.getSign());
            baInfo.setClientId(baInfoDTO.getClientId());
            baInfo.setClientSecret(baInfoDTO.getClientSecret());
            baInfo.setTimeStamp(baInfoDTO.getTimeStamp());
            baInfo.setMethodName(baInfoDTO.getMethodName());

            SessionResponseDto sessionResponseDto = sessionOpenService.getUserInfoByTokenV2(baInfo, accessToken, tokenPairs);
            if (sessionResponseDto == null) {
                log.info("Thrift getUserInfoByTokenV2 failed: baInfo={}, ret={}", baInfo, sessionResponseDto);
                return null;
            }

            return sessionResponseDto2SessionResponseVO(sessionResponseDto);
        }

        private SessionResponseVO sessionResponseDto2SessionResponseVO(SessionResponseDto sessionResponseDto) {
            SessionResponseVO sessionResponseVO = new SessionResponseVO();
            sessionResponseVO.setCode(sessionResponseDto.getCode());
            sessionResponseVO.setMsg(sessionResponseDto.getMsg());
            sessionResponseVO.setTraceId(sessionResponseDto.getTraceId());
            if (null != sessionResponseDto.getData()) {
                sessionResponseVO.setSessionVO(sessionDto2SessionVO(sessionResponseDto.getData()));
            }

            if (null != sessionResponseDto.getExtraInfo()) {
                sessionResponseVO.setExtraInfo(sessionResponseDto.getExtraInfo());
            }
            return sessionResponseVO;
        }
    }
}
