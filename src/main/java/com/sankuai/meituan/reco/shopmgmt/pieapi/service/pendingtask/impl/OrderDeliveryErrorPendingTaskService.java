package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.impl;


import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.AbstractSinglePendingTaskService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionSubTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @Date: 2020-02-18 16:00
 * @Description:
 */
@Slf4j
@Service
public class OrderDeliveryErrorPendingTaskService extends AbstractSinglePendingTaskService {

    @Resource
    private OrderDeliveryExceptionPendingTaskService orderDeliveryExceptionPendingTaskService;

    @Override
    protected PendingTaskResult getPendingTaskCount(PendingTaskParam param) throws Exception {
        if(CollectionUtils.isEmpty(param.getStoreIds())){
            log.warn("Invalid request:{}", param);
            return PendingTaskResult.createNumberMarker(0);
        }
        Map<DeliveryExceptionSubTypeEnum, Integer> deliveryExceptionSubTypeEnumIntegerMap = orderDeliveryExceptionPendingTaskService.countDeliveryErrorBySubType(param.getTenantId(), param.getStoreIds());
        return PendingTaskResult.createNumberMarker(deliveryExceptionSubTypeEnumIntegerMap.getOrDefault(DeliveryExceptionSubTypeEnum.ALL, 0));
    }

    @Override
    protected AuthCodeEnum module() {
        return AuthCodeEnum.ORDER_DELIVERY_ERROR;
    }
}
