package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.handler;


import com.meituan.linz.product.channel.EnhanceChannelType;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

import static com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ProjectConstants.JDDJ_PLATFORM_UN_SALE_COUNT;

/**
 * <AUTHOR>
 * @since 2024/7/24
 */
@Component
public class JddjPlatformUnSaleCountHandler extends AbstractCountStoreSpuHandler {

    @Override
    public Integer getChannelId() {
        return EnhanceChannelType.JDDJ.getChannelId();
    }

    @Override
    public String getCountCode() {
        return JDDJ_PLATFORM_UN_SALE_COUNT;
    }

    @Override
    public List<String> getAbnormalCodes() {
        // 平台停售
        return Collections.singletonList("3002");
    }
}
