package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.wavepick.converter;

import com.sankuai.meituan.reco.pickselect.thrift.wave.request.WarehouseContainerBoxOperateRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick.WarehouseContainerBoxUpdateRequest;
import org.mapstruct.Mapper;

/**
 * @Description:
 * @Author: zhangjian155
 * @Date: 2022/8/25 16:29
 */
@Mapper(componentModel = "spring")
public abstract class WarehouseContainerBoxUpdateRequestConverter {
    public abstract WarehouseContainerBoxOperateRequest convert2ThriftRequest(WarehouseContainerBoxUpdateRequest request, Long tenantId, Long storeId, Long operatorId);
}
