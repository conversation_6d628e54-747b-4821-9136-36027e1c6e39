package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.linz.boot.util.Fun;
import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.FrontCategorySimpleVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.StoreSkuPropertyVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.TimeSlotVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.SupplyRelationAndPurchaseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ChannelSkuAttrValueInfoVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.store.BoothVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.store.StoreVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MoneyUtils;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ProductPropertyDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ProductTimeSlotDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.StoreSkuDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.StoreSpuDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.*;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.sku.CombineChildSkuDto;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.shangou.saas.common.money.PriceUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;

import javax.validation.constraints.NotNull;
import java.util.*;

/**
 * @Title: StoreSpuCreateVO
 * @Description: 保存门店商品SPU信息
 * @Author: zhaolei12
 * @Date: 2020/5/16 5:02 下午
 */
@TypeDoc(
        description = "保存门店商品SPU信息"
)
@Data
@ApiModel("保存门店商品SPU信息")
public class StoreSpuCreateVO {

    @FieldDoc(
            description = "SPU编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "SPU编码", required = true)
    @NotNull
    private String spuId;
    @FieldDoc(
            description = "称重类型 1-称重计量 2-称重计件 3-非称重"
    )
    @ApiModelProperty(name = "称重类型")
    @NotNull
    private Integer weightType;
    @FieldDoc(
            description = "可售时间，如果为无限，此字段为空。\n" +
                    "\n" +
                    "key:工作日 见@Enum WeekDayEnum\n" +
                    "* value:时间段 时间段不允许有交集，个数不超过5个\n" +
                    "* {\"09:00-09:30\"},{\"13:30-15:00\"},{\"20:00-21:00\"}", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "可售时间")
    private Map<Integer, List<TimeSlotVO>> availableTimes;
    @FieldDoc(
            description = "是否为“力荐”商品，字段取值范围：0-否， 1-是", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否为“力荐”商品")
    private Integer specialty;
    @FieldDoc(
            description = "商品描述 200字以内", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品描述")
    private String description;
    @FieldDoc(
            description = "商品属性 不超过十个属性", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品属性")
    private List<StoreSkuPropertyVO> properties;

    @FieldDoc(
            description = "商品卖点 10字以内", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品卖点")
    private String sellPoint;

    @FieldDoc(
            description = "摊位编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "摊位编码")
    private Long boothId;

    @FieldDoc(
            description = "原摊位编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "原摊位编码")
    private Long orgBoothId;

    @FieldDoc(
            description = "门店商品SKU信息(修改前)", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店商品SKU信息(修改前)")
    private List<StoreSkuCreateVO> orgStoreSkuList;

    @FieldDoc(
            description = "门店商品SKU信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店商品SKU信息")
    private List<StoreSkuCreateVO> storeSkuList;

    @FieldDoc(
            description = "渠道商品SPU信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道商品SPU信息")
    private List<ChannelSpuCreateVO> channelSpuList;

    @FieldDoc(
            description = "自定义名称，新增编辑接口入参使用", requiredness = Requiredness.OPTIONAL
    )
    private String customizedName;

    @FieldDoc(
            description = "自定义图片，编辑接口入参使用", requiredness = Requiredness.OPTIONAL
    )
    private List<String> customizedImageUrlList;

    @FieldDoc(
            description = "商品图详", requiredness = Requiredness.OPTIONAL
    )
    private List<String> pictureContents;

    @FieldDoc(
            description = "商品视频", requiredness = Requiredness.OPTIONAL
    )
    private VideoInfoVO video;

    @FieldDoc(
            description = "是否自定义图片详情", requiredness = Requiredness.OPTIONAL
    )
    private Boolean customizedPicContentFlag;

    @FieldDoc(
            description = "是否自定义商品视频", requiredness = Requiredness.OPTIONAL
    )
    private Boolean customizedVideoFlag;

    @FieldDoc(
            description = "是否自定义商品属性", requiredness = Requiredness.OPTIONAL
    )
    private Boolean customizedPropertiesFlag;

    @FieldDoc(
            description = "是否自定义商品卖点", requiredness = Requiredness.OPTIONAL
    )
    private Boolean customizedSellPointFlag;

    @FieldDoc(
            description = "是否自定义商品描述", requiredness = Requiredness.OPTIONAL
    )
    private Boolean customizedDescriptionFlag;


    @FieldDoc(
            description = "绑定采购供应商"
    )
    @ApiModelProperty(name = "绑定采购供应商")
    private List<SupplyRelationAndPurchaseVO> purchaseInfo;

    @FieldDoc(
            description = "门店商品分类", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店商品分类")
    private List<FrontCategorySimpleVO> storeCategories = new ArrayList<>();

    @FieldDoc(
            description = "医药器械资质图信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "医药器械资质图信息")
    private MedicalDeviceQuaInfoVO medicalDeviceQuaInfo;

    @FieldDoc(
            description = "是否门店自定义资质图信息标识", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否门店自定义资质图信息标识")
    private Boolean customizedMedicalDeviceQuaInfo;

    @FieldDoc(
            description = "美团售后服务类型", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "美团售后服务类型")
    private String mtAfterSaleServiceType;

    @FieldDoc(
            description = "是否门店自定义美团售后服务类型标识", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否门店自定义美团售后服务类型标识")
    private Boolean customizedMtAfterSaleServiceType;

    @FieldDoc(
            description = "商品参与活动时的处理方式，1-保留活动，更新失败；2-下线活动，更新成功", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品参与活动时的处理方式")
    private Integer actCheckType;

    @FieldDoc(description = "同步渠道分组Id列表")
    @ApiModelProperty(value = "同步渠道分组Id列表")
    private List<Integer> poiGroupIdList;

    @FieldDoc(
            description = "自定义京东品牌标识", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "自定义京东品牌标识")
    private Boolean customizedJdBrandIdFlag;


    @FieldDoc(
            description = "京东到家品牌", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "京东品牌id 京东到家品牌")
    private String jdBrandId;

    @FieldDoc(
            description = "自定义京东配送要求标识", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "自定义京东配送要求标识")
    private Boolean customizedDeliveryRequirementFlag;

    @FieldDoc(
            description = "京东配送要求", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "京东配送要求")
    private Integer deliveryRequirement;

    @FieldDoc(
            description = "AI推荐信息", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "AI推荐信息")
    private AiRecommendVO aiRecommendVO;



    @FieldDoc(
            description = "特殊管控商品资质"
    )
    @ApiModelProperty("特殊管控商品资质")
    private List<String> controlQuaPicUrl;

    @FieldDoc(
            description = "特殊管控商品资质自定义标"
    )
    @ApiModelProperty("特殊管控商品资质自定义标")
    private Boolean customizedControlQuaPicUrlFlag;

    @FieldDoc(
            description = "名称补充语自定义标", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "名称补充语自定义标")
    private Boolean customizedNameSupplementFlag;

    @FieldDoc(
            description = "门店商品名称补充语", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店商品名称补充语")
    private NameSupplementInfoVO nameSupplementInfo;

    @FieldDoc(description = "门店商品保存特殊图片")
    @ApiModelProperty(value = "门店商品保存特殊图片")
    private List<SpecialPictureVO> mtSpecialPictureList;


    public static StoreSpuDTO toDTO(Long tenantId, Long storeId, StoreSpuCreateVO storeSpuCreateVO) {
        StoreSpuDTO storeSpuDTO = new StoreSpuDTO();
        storeSpuDTO.setTenantId(tenantId);
        storeSpuDTO.setSpuId(storeSpuCreateVO.getSpuId());
        storeSpuDTO.setWeightType(storeSpuCreateVO.getWeightType());

        storeSpuDTO.setProperties(convertToProductPropertyDTO(storeSpuCreateVO.getProperties()));
        storeSpuDTO.setAvailableTimes(convert2ProductTimeSlotDTO(storeSpuCreateVO.getAvailableTimes()));
        storeSpuDTO.setSpecialty(storeSpuCreateVO.getSpecialty());
        storeSpuDTO.setDescription(storeSpuCreateVO.getDescription());
        storeSpuDTO.setSellPoint(storeSpuCreateVO.getSellPoint());

        storeSpuDTO.setStore(StoreVO.toDTO(storeId));
        storeSpuDTO.setBooth(BoothVO.toDTO(storeSpuCreateVO.getBoothId()));
        storeSpuDTO.setOrgBooth(BoothVO.toDTO(storeSpuCreateVO.getOrgBoothId()));

        storeSpuDTO.setStoreSkuList(compareAndConvertToStoreSkuDTOS(storeSpuCreateVO.getOrgStoreSkuList(), storeSpuCreateVO.getStoreSkuList()));
        storeSpuDTO.setChannelSpuList(ChannelSpuCreateVO.toDTOList(storeSpuCreateVO.getChannelSpuList(),
                storeSpuCreateVO.getStoreSkuList()));
        storeSpuDTO.setCustomizedNameFlag(false);
        if (StringUtils.isNotBlank(storeSpuCreateVO.getCustomizedName())) {
            storeSpuDTO.setName(storeSpuCreateVO.getCustomizedName());
            storeSpuDTO.setCustomizedNameFlag(true);
        }
        if (CollectionUtils.isNotEmpty(storeSpuCreateVO.getCustomizedImageUrlList())) {
            storeSpuDTO.setCustomizedPicFlag(true);
            storeSpuDTO.setImageUrls(storeSpuCreateVO.getCustomizedImageUrlList());
        } else {
            storeSpuDTO.setCustomizedPicFlag(false);
            storeSpuDTO.setImageUrls(null);
        }
        if (storeSpuCreateVO.getVideo() != null) {
            storeSpuDTO.setVideo(storeSpuCreateVO.getVideo().toVideoInfoDTO());
        }
        storeSpuDTO.setPictureContents(storeSpuCreateVO.getPictureContents());
        storeSpuDTO.setCustomizedVideoFlag(storeSpuCreateVO.getCustomizedVideoFlag());
        storeSpuDTO.setCustomizedPicContentsFlag(storeSpuCreateVO.getCustomizedPicContentFlag());
        storeSpuDTO.setCustomizedPropertiesFlag(storeSpuCreateVO.getCustomizedPropertiesFlag());
        storeSpuDTO.setCustomizedSellPointFlag(storeSpuCreateVO.getCustomizedSellPointFlag());
        storeSpuDTO.setCustomizedDescriptionFlag(storeSpuCreateVO.getCustomizedDescriptionFlag());
        if (storeSpuCreateVO.getMedicalDeviceQuaInfo() != null) {
            storeSpuDTO.setMedicalDeviceQuaInfo(storeSpuCreateVO.getMedicalDeviceQuaInfo().toOcmsMedicalDeviceQuaInfoDTO());
        }
        storeSpuDTO.setCustomizedMedicalDeviceQuaInfo(storeSpuCreateVO.getCustomizedMedicalDeviceQuaInfo());
        storeSpuDTO.setMtAfterSaleServiceType(storeSpuCreateVO.getMtAfterSaleServiceType());
        storeSpuDTO.setCustomizedMtAfterSaleServiceType(storeSpuCreateVO.getCustomizedMtAfterSaleServiceType());
        storeSpuDTO.setActCheckType(storeSpuCreateVO.getActCheckType());
        storeSpuDTO.setAiRecommendInfo(AiRecommendVO.convertAiRecommendOCMSDTO(storeSpuCreateVO.getAiRecommendVO()));
        storeSpuDTO.setCustomizedNameSupplementFlag(storeSpuCreateVO.getCustomizedNameSupplementFlag());
        storeSpuDTO.setNameSupplementInfo(NameSupplementInfoVO.convertToDTO(storeSpuCreateVO.getNameSupplementInfo()));
        return storeSpuDTO;
    }

    private static Map<Integer, List<ProductTimeSlotDTO>> convert2ProductTimeSlotDTO(Map<Integer, List<TimeSlotVO>> availableTimeDtoMap) {
        Map<Integer, List<ProductTimeSlotDTO>> availableTimeVoMap = Maps.newHashMap();
        if (MapUtils.isEmpty(availableTimeDtoMap)) {
            return availableTimeVoMap;
        }
        for (Map.Entry<Integer, List<TimeSlotVO>> entry : availableTimeDtoMap.entrySet()) {
            List<ProductTimeSlotDTO> productTimeSlotDTOList = new ArrayList<>();
            if (CollectionUtils.isEmpty(entry.getValue())) {
                continue;
            }
            for (TimeSlotVO timeSlotVO : entry.getValue()) {
                if (null == entry.getValue()) {
                    continue;
                }
                productTimeSlotDTOList.add(timeSlotVO.buildProductTimeSlotDTO());
            }
            availableTimeVoMap.put(entry.getKey(), productTimeSlotDTOList);
        }
        return availableTimeVoMap;
    }

    private static List<ProductPropertyDTO> convertToProductPropertyDTO(List<StoreSkuPropertyVO> storeSkuPropertyVOList) {
        List<ProductPropertyDTO> productPropertyDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(storeSkuPropertyVOList)) {
            return productPropertyDTOList;
        }

        if (CollectionUtils.isNotEmpty(storeSkuPropertyVOList)) {
            for (StoreSkuPropertyVO storeSkuPropertyVO : storeSkuPropertyVOList) {
                if (storeSkuPropertyVO != null) {
                    productPropertyDTOList.add(storeSkuPropertyVO.buildProductPropertyDTO());
                }
            }
        }
        return productPropertyDTOList;
    }

    /**
     * 对比修改前和修改后的规格
     * 当修改后的规格数量＜修改前的规格数量时：按照销量数据从小到大删除平台appfoodcode对应关系
     * 当修改后的规格数量＞修改前的规格数量：修改后的规格按照重量大小顺序依次继承原appfoodcode对应关系，无对应关系的新增
     *
     * @param orgStoreSkuCreateVOS
     * @param storeSkuCreateVOS
     * @return
     */
    public static List<StoreSkuDTO> compareAndConvertToStoreSkuDTOS(List<StoreSkuCreateVO> orgStoreSkuCreateVOS, List<StoreSkuCreateVO> storeSkuCreateVOS) {
        // 新增规格
        if (CollectionUtils.isEmpty(orgStoreSkuCreateVOS)) {
            return StoreSkuCreateVO.toDTOList(storeSkuCreateVOS);
        }

        Set<StoreSkuCreateVO> orgStoreSkuCreateSet = Sets.newHashSet(orgStoreSkuCreateVOS);
        Set<StoreSkuCreateVO> storeSkuCreateSet = Sets.newHashSet(storeSkuCreateVOS);
        // 求交集，没有变化的集合
        Sets.SetView unchangedSet = Sets.intersection(orgStoreSkuCreateSet, storeSkuCreateSet);
        // 求差集，原始-当前的差集，找出变化的集合
        List<StoreSkuCreateVO> orgChangedList = Lists.newArrayList(Sets.difference(orgStoreSkuCreateSet, storeSkuCreateSet));
        // 按重量排序-降序
        storeSkuCreateVOS.sort(Comparator.comparing(StoreSkuCreateVO::getWeight).reversed());
        // 按月销量排序-降序
        orgChangedList.forEach(e -> {
            if (e.getMonthSaleAmount() == null) {
                e.setMonthSaleAmount(0);
            }
        });
        orgChangedList.sort(Comparator.comparing(StoreSkuCreateVO::getMonthSaleAmount).reversed());

        List<StoreSkuDTO> storeSkuDTOS = Lists.newArrayList();

        int index = 0;
        for (StoreSkuCreateVO storeSkuCreateVO : storeSkuCreateVOS) {
            String orgSkuId;
            // 未变化
            if (unchangedSet.contains(storeSkuCreateVO)) {
                orgSkuId = storeSkuCreateVO.getSkuId();
            } else {
                orgSkuId = index < orgChangedList.size() ? orgChangedList.get(index).getSkuId() : null;
                index++;
            }
            storeSkuDTOS.add(StoreSkuCreateVO.toDTO(storeSkuCreateVO, orgSkuId));
        }

        return storeSkuDTOS;
    }


    public static StoreSpuOnlineDTO toStoreSpuOnlineDTO(Long tenantId, Long storeId, StoreSpuCreateVO storeSpuCreateVO,
                                                        boolean notMerchantChargeGray,
                                                        boolean isStoreManagementTenant,
                                                        boolean merchantCharge) {
        StoreSpuOnlineDTO storeSpuOnlineDTO = new StoreSpuOnlineDTO();
        storeSpuOnlineDTO.setTenantId(tenantId);
        storeSpuOnlineDTO.setSpuId(storeSpuCreateVO.getSpuId());
        storeSpuOnlineDTO.setWeightType(storeSpuCreateVO.getWeightType());
        storeSpuOnlineDTO.setStore(StoreVO.toBizDTO(storeId));

        //可自定义信息
        //名称
        if (StringUtils.isNotBlank(storeSpuCreateVO.getCustomizedName())) {
            storeSpuOnlineDTO.setName(storeSpuCreateVO.getCustomizedName());
            storeSpuOnlineDTO.setCustomizedNameFlag(true);
        } else {
            storeSpuOnlineDTO.setCustomizedNameFlag(false);
        }
        //图片
        if (CollectionUtils.isNotEmpty(storeSpuCreateVO.getCustomizedImageUrlList())) {
            storeSpuOnlineDTO.setCustomizedImageUrlList(storeSpuCreateVO.getCustomizedImageUrlList());
            storeSpuOnlineDTO.setCustomizedPicFlag(true);
        } else {
            storeSpuOnlineDTO.setCustomizedPicFlag(false);
        }
        //图片详情
        storeSpuOnlineDTO.setPictureContents(storeSpuCreateVO.getPictureContents());
        storeSpuOnlineDTO.setCustomizedPicContentsFlag(storeSpuCreateVO.getCustomizedPicContentFlag());
        //视频
        if (Objects.nonNull(storeSpuCreateVO.getVideo())) {
            storeSpuOnlineDTO.setVideo(convertToVideoInfoDTO(storeSpuCreateVO.getVideo()));
        }
        storeSpuOnlineDTO.setCustomizedVideoFlag(storeSpuCreateVO.getCustomizedVideoFlag());

        // 商品属性、卖点、描述
        storeSpuOnlineDTO.setProperties(convertToStoreSkuPropertyDTO(storeSpuCreateVO.getProperties()));
        storeSpuOnlineDTO.setCustomizedPropertiesFlag(storeSpuCreateVO.getCustomizedPropertiesFlag());
        storeSpuOnlineDTO.setSellPoint(storeSpuCreateVO.getSellPoint());
        storeSpuOnlineDTO.setCustomizedSellPointFlag(storeSpuCreateVO.getCustomizedSellPointFlag());
        storeSpuOnlineDTO.setDescription(storeSpuCreateVO.getDescription());
        storeSpuOnlineDTO.setCustomizedDescriptionFlag(storeSpuCreateVO.getCustomizedDescriptionFlag());

        //可售时间等
        storeSpuOnlineDTO.setAvailableTimes(convert2TimeFragmentDTO(storeSpuCreateVO.getAvailableTimes()));
        storeSpuOnlineDTO.setSpecialty(storeSpuCreateVO.getSpecialty());
        ChannelSpuCreateVO channelSpuCreateVO = storeSpuCreateVO.getChannelSpuList().iterator().next();
        if (Objects.nonNull(channelSpuCreateVO)) {
            if (channelSpuCreateVO.getSpuStatus() != null) {
                storeSpuOnlineDTO.setSaleStatus(channelSpuCreateVO.getSpuStatus());
            }
        }

        //sku
        storeSpuOnlineDTO.setStoreSkuList(convertToStoreSkuDetailDTOList(storeSpuCreateVO.getStoreSkuList(), storeSpuCreateVO.getChannelSpuList()));

        //渠道属性
        //渠道类目
        storeSpuOnlineDTO.setChannelCategory(convertToChannelCategoryDTOList(storeSpuCreateVO.getChannelSpuList()));

        //店内分类
        if (notMerchantChargeGray || isStoreManagementTenant || merchantCharge) {
            List<StoreCategoryDTO> categoryDTOList = Fun
                    .map(storeSpuCreateVO.getStoreCategories(), FrontCategorySimpleVO::convertToStoreCategoryDTO);
            storeSpuOnlineDTO.setStoreCategoryList(categoryDTOList);
        }
        else {
            storeSpuOnlineDTO.setStoreCategoryList(convertToStoreCategoryDTOList(storeSpuCreateVO.getChannelSpuList()));
        }
        if (storeSpuCreateVO.getMedicalDeviceQuaInfo() != null) {
            storeSpuOnlineDTO.setMedicalDeviceQuaInfo(storeSpuCreateVO.getMedicalDeviceQuaInfo().toMedicalDeviceQuaInfoDTO());
        }
        storeSpuOnlineDTO.setCustomizedMedicalDeviceQuaInfo(storeSpuCreateVO.getCustomizedMedicalDeviceQuaInfo());
        storeSpuOnlineDTO.setMtAfterSaleServiceType(storeSpuCreateVO.getMtAfterSaleServiceType());
        storeSpuOnlineDTO.setCustomizedMtAfterSaleServiceType(storeSpuCreateVO.getCustomizedMtAfterSaleServiceType());
        storeSpuOnlineDTO.setActCheckType(storeSpuCreateVO.getActCheckType());

        // 摊位信息
        storeSpuOnlineDTO.setBooth(BoothVO.toBizBoothDto(storeSpuCreateVO.getBoothId()));
        storeSpuOnlineDTO.setOrgBooth(BoothVO.toBizBoothDto(storeSpuCreateVO.getOrgBoothId()));
        storeSpuOnlineDTO.setPoiGroupIdList(storeSpuCreateVO.getPoiGroupIdList());
        storeSpuOnlineDTO.setControlQuaPicUrl(storeSpuCreateVO.getControlQuaPicUrl());
        storeSpuOnlineDTO.setCustomizedControlQuaPicUrlFlag(storeSpuCreateVO.getCustomizedControlQuaPicUrlFlag());

        // 名称补充语
        storeSpuOnlineDTO.setCustomizedNameSupplementFlag(storeSpuCreateVO.getCustomizedNameSupplementFlag());
        storeSpuOnlineDTO.setNameSupplementInfo(NameSupplementInfoVO.convertToBizDTO(storeSpuCreateVO.getNameSupplementInfo()));

        //京东自定以属性
        storeSpuOnlineDTO.setCustomizedJdBrandFlag(storeSpuCreateVO.getCustomizedJdBrandIdFlag());
        storeSpuOnlineDTO.setJdBrandId(storeSpuCreateVO.getJdBrandId());
        storeSpuOnlineDTO.setCustomizedDeliveryRequirementFlag(storeSpuCreateVO.getCustomizedDeliveryRequirementFlag());
        storeSpuOnlineDTO.setDeliveryRequirement(storeSpuCreateVO.getDeliveryRequirement());
        storeSpuOnlineDTO.setAiRecommendInfo(AiRecommendVO.convertAiRecommendBizDTO(storeSpuCreateVO.getAiRecommendVO()));

        if(CollectionUtils.isNotEmpty(storeSpuCreateVO.getMtSpecialPictureList())) {
            storeSpuOnlineDTO.setMtSpecialPictureList(Fun.map(storeSpuCreateVO.getMtSpecialPictureList(), SpecialPictureVO::toDTO));
        }
        return storeSpuOnlineDTO;
    }

    private static List<StoreCategoryDTO> convertToStoreCategoryDTOList(List<ChannelSpuCreateVO> channelSpuList) {
        List<StoreCategoryDTO> storeCategoryDTOS = Lists.newArrayList();
        ChannelSpuCreateVO channelSpuCreateVO1 = channelSpuList.stream()
                .filter(channelSpuCreateVO -> CollectionUtils.isNotEmpty(channelSpuCreateVO.getFrontCategories()))
                .findFirst().orElse(null);
        if (Objects.nonNull(channelSpuCreateVO1)) {
            channelSpuCreateVO1.getFrontCategories().forEach(storeCategory -> {
                StoreCategoryDTO storeCategoryDTO = new StoreCategoryDTO();
                storeCategoryDTO.setStoreCategoryCode(storeCategory.getFrontCategoryCode());
                storeCategoryDTO.setStoreCategoryName(storeCategory.getFrontCategoryName());
                storeCategoryDTO.setStoreCategoryCodePath(storeCategory.getFrontCategoryCodePath());
                storeCategoryDTO.setStoreCategoryNamePath(storeCategory.getFrontCategoryNamePath());
                storeCategoryDTOS.add(storeCategoryDTO);
            });
        }
        return storeCategoryDTOS;

    }

    private static List<ChannelCategoryDTO> convertToChannelCategoryDTOList(List<ChannelSpuCreateVO> channelSpuList) {
        List<ChannelCategoryDTO> channelCategoryDTOS = Lists.newArrayList();

        if (CollectionUtils.isEmpty(channelSpuList)) {
            return channelCategoryDTOS;
        }

        channelSpuList.forEach(channelSpuCreateVO -> {
            if (Objects.nonNull(channelSpuCreateVO.getChannelCategory())) {
                ChannelCategoryDTO channelCategoryDTO = new ChannelCategoryDTO();
                channelCategoryDTO.setChannelCategoryCode(channelSpuCreateVO.getChannelCategory().getChannelCategoryCode());
                channelCategoryDTO.setChannelCategoryName(channelSpuCreateVO.getChannelCategory().getChannelCategoryName());
                channelCategoryDTO.setChannelCategoryCodePath(channelSpuCreateVO.getChannelCategory().getChannelCategoryCodePath());
                channelCategoryDTO.setChannelCategoryNamePath(channelSpuCreateVO.getChannelCategory().getChannelCategoryNamePath());
                channelCategoryDTO.setChannelId(channelSpuCreateVO.getChannelId());
                //动态信息
                channelCategoryDTO.setChannelDynamicInfoDTOList(convertToDynamicInfoBizDTOList(channelSpuCreateVO.getChannelCategory().getChannelDynamicInfoVOList()));
                channelCategoryDTOS.add(channelCategoryDTO);
            }
        });
        return channelCategoryDTOS;
    }

    private static List<ChannelDynamicInfoBizDTO> convertToDynamicInfoBizDTOList(List<ChannelDynamicInfoVO> channelDynamicInfoVOList) {
        List<ChannelDynamicInfoBizDTO> channelDynamicInfoBizDTOList = Lists.newArrayList();

        if (CollectionUtils.isEmpty(channelDynamicInfoVOList)) {
            return channelDynamicInfoBizDTOList;
        }

        channelDynamicInfoVOList.forEach(channelDynamicInfoVO -> {
            ChannelDynamicInfoBizDTO channelDynamicInfoBizDTO = new ChannelDynamicInfoBizDTO();
            channelDynamicInfoBizDTO.setAttrId(channelDynamicInfoVO.getAttrId().toString());
            channelDynamicInfoBizDTO.setAttrName(channelDynamicInfoVO.getAttrName());
            channelDynamicInfoBizDTO.setAttrSequence(channelDynamicInfoVO.getAttrSequence());
            channelDynamicInfoBizDTO.setCharacterType(channelDynamicInfoVO.getCharacterType());
            channelDynamicInfoBizDTO.setAttrValueList(convertToDynamicInfoValueBizDTO(channelDynamicInfoVO.getAttrValueList()));
            channelDynamicInfoBizDTO.setAttrValueType(channelDynamicInfoVO.getAttrValueType());
            channelDynamicInfoBizDTO.setIsRequired(channelDynamicInfoVO.getIsRequired());
            channelDynamicInfoBizDTO.setMaxTextLength(channelDynamicInfoVO.getMaxTextLength());
            channelDynamicInfoBizDTO.setCustomValue(convertToDynamicInfoValueBizDTO(channelDynamicInfoVO.getCustomValue()));
            channelDynamicInfoBizDTOList.add(channelDynamicInfoBizDTO);
        });
        return channelDynamicInfoBizDTOList;
    }

    private static List<ChannelDynamicInfoValueBizDTO> convertToDynamicInfoValueBizDTO(List<ChannelDynamicInfoVO.ChannelDynamicInfoValueVO> channelDynamicInfoValueVOS) {
        List<ChannelDynamicInfoValueBizDTO> channelDynamicInfoValueBizDTOS = Lists.newArrayList();

        if(CollectionUtils.isEmpty(channelDynamicInfoValueVOS)){
            return channelDynamicInfoValueBizDTOS;
        }

        channelDynamicInfoValueVOS.forEach(channelDynamicInfoValueVO -> {
            ChannelDynamicInfoValueBizDTO channelDynamicInfoValueBizDTO = new ChannelDynamicInfoValueBizDTO();
            channelDynamicInfoValueBizDTO.setAttrValue(channelDynamicInfoValueVO.getAttrValue());
            channelDynamicInfoValueBizDTO.setAttrValueId(channelDynamicInfoValueVO.getAttrValueId());
            channelDynamicInfoValueBizDTOS.add(channelDynamicInfoValueBizDTO);
        });
        return channelDynamicInfoValueBizDTOS;
    }


    private static List<StoreSkuDetailDTO> convertToStoreSkuDetailDTOList(List<StoreSkuCreateVO> storeSkuCreateVOs, List<ChannelSpuCreateVO> channelSpuList) {
        List<StoreSkuDetailDTO> storeSkuOnlineDTOS = Lists.newArrayList();
        storeSkuCreateVOs.forEach(storeSkuCreateVO -> {
            StoreSkuDetailDTO storeSkuOnlineDTO = new StoreSkuDetailDTO();
            storeSkuOnlineDTO.setSkuId(storeSkuCreateVO.getSkuId());
            storeSkuOnlineDTO.setSpec(storeSkuCreateVO.getSpec());
            storeSkuOnlineDTO.setWeight(storeSkuCreateVO.getWeight());
            storeSkuOnlineDTO.setWeightForUnit(storeSkuCreateVO.getWeightForUnit());
            storeSkuOnlineDTO.setWeightUnit(storeSkuCreateVO.getWeightUnit());
            //库存
            storeSkuOnlineDTO.setAutoResumeInfiniteStock(storeSkuCreateVO.getAutoResumeInfiniteStock());
            storeSkuOnlineDTO.setStock(storeSkuCreateVO.getStock());
            storeSkuOnlineDTO.setCustomizedStockFlag(storeSkuCreateVO.getCustomizeStockFlag());
            //价格
            if (StringUtils.isNotBlank(storeSkuCreateVO.getStorePrice())) {
                storeSkuOnlineDTO.setStorePrice(MoneyUtils.yuanToCent(storeSkuCreateVO.getStorePrice()));
            }
            List<OnlinePriceDTO> onlinePriceDTOS = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(storeSkuCreateVO.getChannelOnlinePriceList())) {
                storeSkuCreateVO.getChannelOnlinePriceList().forEach(channelPriceVO -> {
                    OnlinePriceDTO onlinePriceDTO = new OnlinePriceDTO();
                    if (StringUtils.isNotBlank(channelPriceVO.getOnlinePrice())) {
                        onlinePriceDTO.setOnlinePrice(Double.valueOf(channelPriceVO.getOnlinePrice()));
                    }
                    onlinePriceDTO.setPriceStrategy(storeSkuCreateVO.getPriceStrategy());
                    onlinePriceDTO.setChannelId(channelPriceVO.getChannelId());
                    onlinePriceDTOS.add(onlinePriceDTO);
                });
            } else {
                Integer channelId;
                if (CollectionUtils.isNotEmpty(channelSpuList)) {
                    channelId = channelSpuList.get(0).getChannelId();
                } else {
                    throw new BizException("渠道参数为空");
                }
                OnlinePriceDTO onlinePriceDTO = new OnlinePriceDTO();
                if (Objects.nonNull(storeSkuCreateVO.getOnlinePrice())) {
                    onlinePriceDTO.setOnlinePrice(PriceUtils.fen2Yuan(storeSkuCreateVO.getOnlinePrice()));
                }
                onlinePriceDTO.setPriceStrategy(storeSkuCreateVO.getPriceStrategy());
                onlinePriceDTO.setChannelId(channelId);
                onlinePriceDTOS.add(onlinePriceDTO);
            }
            storeSkuOnlineDTO.setOnlinePrices(onlinePriceDTOS);

            //包装盒等
            storeSkuOnlineDTO.setBoxNum(storeSkuCreateVO.getBoxNum());
            storeSkuOnlineDTO.setBoxPrice(storeSkuCreateVO.getBoxPrice());
            storeSkuOnlineDTO.setMinOrderCount(storeSkuCreateVO.getMinNum());

            //售卖状态
            StoreSkuErpDTO storeSkuErpDTO = new StoreSkuErpDTO();
            storeSkuErpDTO.setOnlineStatus(storeSkuCreateVO.getOnlineStatus() == null ? 1 : storeSkuCreateVO.getOnlineStatus());
            storeSkuOnlineDTO.setStoreSkuErpDTO(storeSkuErpDTO);

            // 渠道sku属性
            storeSkuOnlineDTO.setChannelSkuAttrValueInfoList(ChannelSkuAttrValueInfoVo.toDtoList(storeSkuCreateVO.getChannelSkuAttrValueInfoList()));
            storeSkuOnlineDTO.setSkuSaleType(storeSkuCreateVO.getSkuSaleType());
            if (CollectionUtils.isNotEmpty(storeSkuCreateVO.getChildSkuList())) {
                storeSkuOnlineDTO.setChildSkuList(JacksonUtils.convertList(storeSkuCreateVO.getChildSkuList(), CombineChildSkuDto.class));
            }
            storeSkuOnlineDTOS.add(storeSkuOnlineDTO);
        });
        return storeSkuOnlineDTOS;
    }


    private static VideoInfoDTO convertToVideoInfoDTO(VideoInfoVO video) {
        VideoInfoDTO videoInfoDTO = new VideoInfoDTO();
        videoInfoDTO.setVideoUrl(video.getVideoUrl());
        videoInfoDTO.setCoverImageUrl(video.getCoverImageUrl());
        return videoInfoDTO;
    }

    private static Map<Integer, List<TimeFragmentDTO>> convert2TimeFragmentDTO(Map<Integer, List<TimeSlotVO>> availableTimeDtoMap) {
        Map<Integer, List<TimeFragmentDTO>> availableTimeVoMap = Maps.newHashMap();
        if (MapUtils.isEmpty(availableTimeDtoMap)) {
            return availableTimeVoMap;
        }
        for (Map.Entry<Integer, List<TimeSlotVO>> entry : availableTimeDtoMap.entrySet()) {
            List<TimeFragmentDTO> timeFragmentDTOS = new ArrayList<>();
            if (CollectionUtils.isEmpty(entry.getValue())) {
                continue;
            }
            for (TimeSlotVO timeSlotVO : entry.getValue()) {
                if (null == entry.getValue()) {
                    continue;
                }
                timeFragmentDTOS.add(timeSlotVO.buildTimeFragmentDTO());
            }
            availableTimeVoMap.put(entry.getKey(), timeFragmentDTOS);
        }
        return availableTimeVoMap;
    }

    private static List<StoreSkuPropertyDTO> convertToStoreSkuPropertyDTO(List<StoreSkuPropertyVO> storeSkuPropertyVOList) {
        List<StoreSkuPropertyDTO> storeSkuPropertyDTOS = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(storeSkuPropertyVOList)) {
            for (StoreSkuPropertyVO storeSkuPropertyVO : storeSkuPropertyVOList) {
                if (storeSkuPropertyVO != null) {
                    storeSkuPropertyDTOS.add(buildProductPropertyDTO(storeSkuPropertyVO));
                }
            }
        }
        return storeSkuPropertyDTOS;
    }

    private static StoreSkuPropertyDTO buildProductPropertyDTO(StoreSkuPropertyVO storeSkuPropertyVO) {
        StoreSkuPropertyDTO storeSkuPropertyDTO = new StoreSkuPropertyDTO();
        storeSkuPropertyDTO.setPropertyName(storeSkuPropertyVO.getPropertyName());
        storeSkuPropertyDTO.setPropertyValues(storeSkuPropertyVO.getPropertyValues());
        return storeSkuPropertyDTO;
    }


}
