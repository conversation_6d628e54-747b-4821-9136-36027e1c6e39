package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.ResignApplyVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/10/19 15:00
 **/
@ApiModel("查询待申请列表返回")
@TypeDoc(description = "查询待申请列表返回")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryResignApprovalListResp {

    @FieldDoc(description = "员工姓名")
    @ApiModelProperty("员工姓名")
    private List<ResignApplyVO> resignApprovalList;

    @FieldDoc(description = "迟到时间点（上班打卡时有值）")
    @ApiModelProperty("迟到时间点（上班打卡时有值）")
    public Boolean hasMore;

    @FieldDoc(description = "早退时间点（下班打卡时有值）")
    @ApiModelProperty("早退时间点（下班打卡时有值）")
    public Long totalCount;

}
