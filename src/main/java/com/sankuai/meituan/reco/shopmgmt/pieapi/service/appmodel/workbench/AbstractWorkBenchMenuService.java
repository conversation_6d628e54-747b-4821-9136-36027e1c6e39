package com.sankuai.meituan.reco.shopmgmt.pieapi.service.appmodel.workbench;

import com.meituan.shangou.sac.dto.model.SacMenuNodeDto;
import com.meituan.shangou.sac.dto.model.SacMenuNodeWithChild;
import com.sankuai.meituan.reco.shopmgmt.pieapi.converters.assistant.AssistantTaskConverter;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.appmodel.QueryMenuInfoRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.appmodel.AppModelMenuInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.appmodel.MenuInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.facade.PoiServiceFacade;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.appmodel.AbstractAppModelMenuService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.SacWrapper;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 抽象工作台菜单service
 *
 * <AUTHOR>
 * @since 2021/7/7
 */
@Slf4j
public abstract class AbstractWorkBenchMenuService extends AbstractAppModelMenuService {

    @Autowired
    PoiServiceFacade poiServiceFacade;

    @Autowired
    SacWrapper sacWrapper;

    @Override
    protected AppModelMenuInfo queryMenuInfo(QueryMenuInfoRequest request, Boolean possibleNewQueryGray) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        Long accountId = identityInfo.getUser().getAccountId();
        Integer authId = ApiMethodParamThreadLocal.getIdentityInfo().getAuthId();
        List<Long> storeIdList = identityInfo.getStoreIdList();
        // 根据菜单code查询菜单配置信息
        String menuCode = getMenuCode().getCode();
        Map<String, SacMenuNodeWithChild> menuNodeWithChildMap = sacWrapper.getAccountMenusByMenuCodes(accountId, authId,
                Arrays.asList(menuCode));

        if (menuNodeWithChildMap == null || !menuNodeWithChildMap.containsKey(menuCode) || menuNodeWithChildMap.get(menuCode) == null) {
            throw new BizException("根据菜单code查询菜单信息失败");
        }

        // 构造当前菜单信息
        SacMenuNodeWithChild menuNodeWithChild = menuNodeWithChildMap.get(menuCode);
        AppModelMenuInfo appModelMenuInfo = new AppModelMenuInfo();
        MenuInfo parentMenu = AssistantTaskConverter.convertSacMenu2AssistantMenu(menuNodeWithChild.getSacMenuNodeDto());
        appModelMenuInfo.setParentMenu(parentMenu);

        List<SacMenuNodeDto> childSacMenuNodeDto = menuNodeWithChild.getChildSacMenuNodeDtos();
        // 若当前菜单没有权限，则无需查询子菜单信息，直接返回
        if (!parentMenu.isHasAuth() || CollectionUtils.isEmpty(childSacMenuNodeDto)) {
            log.info("当前菜单无权限，或不存在子菜单:poiId:{},accountId:{},menuCode:{}", storeIdList, accountId, getMenuCode());
            appModelMenuInfo.setHideParentMenu(Boolean.TRUE);
            appModelMenuInfo.setSubMenus(Collections.emptyList());
            return appModelMenuInfo;
        }

        // 构造子菜单信息Map，只保留有权限的子菜单
        Map<String, SacMenuNodeDto> menuCodeWithNodeMap = childSacMenuNodeDto.stream()
                .filter(SacMenuNodeDto::isHasAuth)
                .collect(Collectors.toMap(SacMenuNodeDto::getSacMenuCode, menuNodeDto -> menuNodeDto));

        if (MapUtils.isEmpty(menuCodeWithNodeMap) || hideParentMenu(menuCodeWithNodeMap.keySet())) {
            log.info("需要展示的子菜单都没有权限，不展示父菜单模块:poiId:{},accountId:{},menuCode:{}", storeIdList, accountId, getMenuCode());
            appModelMenuInfo.setHideParentMenu(Boolean.TRUE);
            appModelMenuInfo.setSubMenus(Collections.emptyList());
            return appModelMenuInfo;
        }

        // 构造子菜单列表，并排序
        List<MenuInfo> subMenus = getSubMenuInfos(menuCodeWithNodeMap, identityInfo, request, possibleNewQueryGray);
        sortSubMenuInfos(subMenus);
        appModelMenuInfo.setHideParentMenu(Boolean.FALSE);
        appModelMenuInfo.setSubMenus(subMenus);

        return appModelMenuInfo;
    }

    /**
     * 对 subMenuInfo 排序
     *
     * @param subMenuInfos
     * @return
     */
    protected void sortSubMenuInfos(List<MenuInfo> subMenuInfos) {
        subMenuInfos.sort(Comparator.comparingInt(MenuInfo::getRank));
    }

    /**
     * 根据当前有权限的子菜单code，判断是否需要隐藏当前菜单
     *
     * @param hasAuthSubMenuCodes 有权限的子菜单code
     * @return
     */
    protected abstract Boolean hideParentMenu(Set<String> hasAuthSubMenuCodes);

    /**
     * 查询子菜单信息
     *
     * @param menuCodeWithNodeMap
     * @param identityInfo
     * @param possibleNewQueryGray
     * @return
     */
    protected abstract List<MenuInfo> getSubMenuInfos(Map<String, SacMenuNodeDto> menuCodeWithNodeMap,
                                                      IdentityInfo identityInfo, QueryMenuInfoRequest request, Boolean possibleNewQueryGray);

}
