package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel(
        "渠道价格不一致问题商品查询"
)
@TypeDoc(
        description = "渠道价格不一致问题商品查询"
)
public class QueryNotEqualSignSkuRequest {

    @FieldDoc(
            description = "门店id"
    )
    @ApiModelProperty("门店Id")
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "页码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "页码")
    private Integer page = 1;

    @FieldDoc(
            description = "每页大小", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "每页大小")
    private Integer size = 10;
}
