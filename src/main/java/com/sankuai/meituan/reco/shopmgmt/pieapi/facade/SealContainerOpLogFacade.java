package com.sankuai.meituan.reco.shopmgmt.pieapi.facade;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.logistics.hu.api.constants.enums.SealContainerOperateType;
import com.sankuai.shangou.logistics.hu.api.dto.Result;
import com.sankuai.shangou.logistics.hu.api.dto.SealContainerLogDTO;
import com.sankuai.shangou.logistics.hu.api.service.SealContainerLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/6/28 18:02
 **/
@Rhino
@Slf4j
public class SealContainerOpLogFacade {
    @Resource
    private SealContainerLogService sealContainerLogService;

    private final Integer ZERO = 0;

    @Degrade(rhinoKey = "SealContainerOpLogFacade.queryWarehouseUsingSealContainerCount", fallBackMethod = "queryWarehouseUsingSealContainerCountFallback", timeoutInMilliseconds = 2000)
    public Integer queryWarehouseUsingSealContainerCount(Long merchantId, Long warehouseId) {
        Result<List<SealContainerLogDTO>> result;
        try {
            log.info("start invoke sealContainerLogService.queryLatestSealContainerOperateLog, warehouseId: {}", warehouseId);
            result = sealContainerLogService.queryLatestSealContainerOperateLog(Collections.singletonList(warehouseId), merchantId, Collections.emptyList());
            log.info("end invoke sealContainerLogService.queryLatestSealContainerOperateLog, result: {}", result);
        } catch (Exception e) {
            log.error("查询容具使用记录失败", e);
            throw new BizException("查询容具使用记录失败");
        }

        if (result.getCode() != 0) {
            throw new BizException(result.getMessage());
        }

        if (CollectionUtils.isEmpty(result.getModule())) {
            return ZERO;
        }

        //只看现在是使用中的容具
        return (int) result.getModule().stream()
                .filter(opLog -> Objects.equals(opLog.getOpType(), SealContainerOperateType.STOCK_OUT.getCode())
                        || Objects.equals(opLog.getOpType(), SealContainerOperateType.TRANSFER.getCode())).count();

    }

    public Integer queryWarehouseUsingSealContainerCountFallback(Long merchantId, Long warehouseId) {
        log.error("SealContainerOpLogFacade.queryWarehouseUsingSealContainerCount 发生降级");
        return 0;
    }
}
