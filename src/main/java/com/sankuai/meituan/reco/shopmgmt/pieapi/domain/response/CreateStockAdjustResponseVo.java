package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreateStockAdjustResponseVo {

    @FieldDoc(
            description = "错误码"
    )
    @ApiModelProperty(value = "错误码", required = false)
    private Integer code;

    @FieldDoc(
            description = "错误信息"
    )
    @ApiModelProperty(value = "错误信息", required = false)
    private String msg;

    @FieldDoc(
            description = "调整单号"
    )
    @ApiModelProperty(value = "调整单号", required = true)
    private String receiptNo;

    @FieldDoc(
            description = "失败sku列表"
    )
    @ApiModelProperty(value = "失败sku列表", required = true)
    private List<StockAdjustFailSku> failSkuList;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StockAdjustFailSku{
        @FieldDoc(
                description = "sku id"
        )
        @ApiModelProperty(value = "sku id", required = true)
        private String skuId;

        @FieldDoc(
                description = "错误信息"
        )
        @ApiModelProperty(value = "错误信息", required = true)
        private String failText;
    }
}
