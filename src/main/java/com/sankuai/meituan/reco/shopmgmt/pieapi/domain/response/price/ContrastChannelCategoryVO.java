package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.shangou.empower.price.client.dto.contrast.ContrastChannelCategoryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

/**
 * @Author: wangyihao04
 * @Date: 2020-12-02 10:50
 * @Mail: <EMAIL>
 */
@TypeDoc(
        description = "渠道类目"
)
@ApiModel("分类指标")
@Getter
@AllArgsConstructor
@ToString
public class ContrastChannelCategoryVO {

    @FieldDoc(
            description = "类目id"
    )
    @ApiModelProperty("类目id")
    public String categoryId;

    @FieldDoc(
            description = "类目名称"
    )
    @ApiModelProperty("类目名称")
    public String categoryName;

    @FieldDoc(
            description = "竞对门店该类目指标列表"
    )
    @ApiModelProperty("竞对门店该类目指标列表")
    public Integer categoryLevel;

    public static ContrastChannelCategoryVO valueOf(ContrastChannelCategoryDTO dto){
        if (Objects.isNull(dto)){
            return null;
        }
        return new ContrastChannelCategoryVO(dto.getCategoryId(), dto.getCategoryName(), dto.getLevel());
    }
}
