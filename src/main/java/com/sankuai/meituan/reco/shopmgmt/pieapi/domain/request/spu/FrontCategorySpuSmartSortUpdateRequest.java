package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * FileName: FrontCategorySpuSmartSortUpdateRequest
 * Author:   wangjiawei31
 * Date:     2021/2/25 4:05 下午
 * Description:
 */
@TypeDoc(
        description = "店内分类下商品排序更新请求"
)
@Data
@NoArgsConstructor
public class FrontCategorySpuSmartSortUpdateRequest {

    @FieldDoc(
            description = "租户id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户id")
    private Long tenantId;

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    private Long storeId;

    @FieldDoc(
            description = "渠道id", requiredness = Requiredness.REQUIRED
    )
    private Integer channelId;

    @FieldDoc(
            description = "店内分类id", requiredness = Requiredness.REQUIRED
    )
    private Long frontCategoryId;

    @FieldDoc(
            description = "智能排序", requiredness = Requiredness.REQUIRED
    )
    private Boolean smartSort;

    public void validate() {
        if (this.tenantId == null) {
            throw new CommonLogicException("商户id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (this.storeId == null) {
            throw new CommonLogicException("门店id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (this.channelId == null) {
            throw new CommonLogicException("渠道id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (this.frontCategoryId == null) {
            throw new CommonLogicException("店内分类id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (this.smartSort == null) {
            throw new CommonLogicException("开关字段信息不能为空", ResultCode.CHECK_PARAM_ERR);
        }
    }
}
