package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @Auther: wb_nifei
 * @Date: 2023/8/18 16:44
 */
@TypeDoc(
        description = "取消装箱请求"
)
@ApiModel("取消装箱请求")
@Data
public class WarehousePickPackCancelRequest {
    @FieldDoc(
            description = "skuId", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "skuId")
    private String skuId;

    @FieldDoc(
            description = "箱ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "箱ID")
    private String huCode;

    @FieldDoc(
            description = "取消装箱数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "取消装箱数量")
    private Integer packNum;

    @FieldDoc(
            description = "拣货任务ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "拣货任务ID")
    private Long pickTaskId;

    @FieldDoc(
            description = "拣货订单ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "拣货订单ID")
    private String orderId;
}
