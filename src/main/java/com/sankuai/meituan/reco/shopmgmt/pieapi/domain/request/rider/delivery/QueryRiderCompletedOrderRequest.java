package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.delivery;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 查询骑手正在已送达+已取消的订单的请求.
 */
@TypeDoc(
        description = "查询骑手已送达+已取消的订单的请求"
)
@ApiModel("查询骑手已送达+已取消的订单的请求")
@Data
public class QueryRiderCompletedOrderRequest {

    @FieldDoc(
            description = "第几页", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "第几页", required = true)
    @NotNull
    private Integer page;

    @FieldDoc(
            description = "每页行数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "每页行数", required = true)
    @NotNull
    private Integer size;

    @FieldDoc(
            description = "前端版本号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "前端版本号", required = true)
    private String mrnVersion;
}
