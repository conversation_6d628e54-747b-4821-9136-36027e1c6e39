package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.appraise;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.shangou.saas.resource.management.common.enums.AppraisePlanTypeEnum;
import com.meituan.shangou.saas.resource.management.common.enums.BonusStatusEnum;
import com.meituan.shangou.saas.resource.management.dto.AppraiseResultWithIndicatorDTO;
import com.meituan.shangou.saas.resource.management.dto.appraise.AppraiseRulePreviewDTO;
import com.meituan.shangou.saas.resource.management.dto.bonus.BonusDTO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.management.AppraisePeriodVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.management.AppraiseRuleVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.RulePassedEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2020/9/15 16:41
 * @Description:
 */
@Data
@ToString
public class AppraiseResultHistoryItemVO {

    private Long planResultId;

    private String periodText;

    private AppraisePeriodVO period;

    private Integer rulePassed;

    private Double amount;

    private String status;

    private String memo;

    private Integer ruleFailedResultNum;

    private Integer rulePassedResultNum;

    private String statusDescription;



    private List<AppraiseRuleVO> indicators;


    public AppraiseResultHistoryItemVO(AppraiseResultWithIndicatorDTO resultDTO,List<String> permissionCodes) {
        this.periodText = DateFormatUtils.format(resultDTO.getBonus().getStatisticalStartTime(), "MM.dd")
                +"~" + DateFormatUtils.format(resultDTO.getBonus().getStatisticalEndTime(), "MM.dd");
        AppraisePeriodVO appraisePeriod = new AppraisePeriodVO();
        appraisePeriod.setPeriodName(resultDTO.getBonus().getStageName());
        appraisePeriod.setPeriodStart(DateFormatUtils.format(resultDTO.getBonus().getStatisticalStartTime(), "yyyy-MM-dd"));
        appraisePeriod.setStatisticalStartDate(DateFormatUtils.format(resultDTO.getBonus().getStatisticalStartTime(), "yyyy-MM-dd"));
        appraisePeriod.setPeriodEnd(DateFormatUtils.format(resultDTO.getBonus().getStatisticalEndTime(), "yyyy-MM-dd"));
        appraisePeriod.setStatisticalEndDate(DateFormatUtils.format(resultDTO.getBonus().getStatisticalEndTime(), "yyyy-MM-dd"));
        this.period = appraisePeriod;
        this.planResultId = resultDTO.getBonus().getBonusId();
        this.rulePassed = isPassed(resultDTO.getBonus());
        this.amount = rulePassed.equals(RulePassedEnum.NOT_FIXED.getCode()) ? null :
                BigDecimal.valueOf(resultDTO.getBonus().getAmount()).divide(BigDecimal.valueOf(100)).setScale(2, BigDecimal.ROUND_HALF_UP).abs().doubleValue();
        this.indicators = ConverterUtils.convertList(resultDTO.getDetails(),d -> new AppraiseRuleVO(d, permissionCodes));
        this.status = BonusStatusEnum.getByType(resultDTO.getBonus().getStatus()).name();
        this.statusDescription = statusDesc(BonusStatusEnum.getByType(resultDTO.getBonus().getStatus()));
        this.memo = resultDTO.getBonus().getMemo();
        this.rulePassedResultNum = (int) this.indicators.stream().filter(r -> r.getRulePassed() != null && r.getRulePassed() == 1).count();
        this.ruleFailedResultNum = (int) this.indicators.stream().filter(r -> r.getRulePassed() != null && r.getRulePassed() == 0).count();
    }


    /**
     * 结算状态描述
     * @param statusEnum
     * @return
     */
    private String statusDesc(BonusStatusEnum statusEnum) {

        if (statusEnum == null) {
            return "-";
        }

        if (statusEnum == BonusStatusEnum.SETTLE_COMPLETED) {
            return "已结算";
        }

        if (statusEnum == BonusStatusEnum.UNSETTLEED) {
            return "待结算";
        }

        if (statusEnum == BonusStatusEnum.DEDUCTED) {
            return "已扣款";
        }

        return "-";

    }


    /**
     *
     * @param bonus
     * @return
     */
    private Integer isPassed(BonusDTO bonus) {
        //红线试用期
        if (bonus.getPlanType() == AppraisePlanTypeEnum.MEET_CRITERION_APPRAISAL.getType()
                && bonus.getQualified() == null) {
            return RulePassedEnum.NOT_FIXED.getCode();
        }
        if (StringUtils.isEmpty(bonus.getMemo())) {
            return RulePassedEnum.NOT_FIXED.getCode();
        }
        if (bonus.getPlanType() == AppraisePlanTypeEnum.GOAL_BASED_INSPIRATION.getType()
        || bonus.getPlanType() == AppraisePlanTypeEnum.SERVICE_STAR.getType()) {
            return bonus.getAmount() > 0 ? RulePassedEnum.PASSED.getCode() : RulePassedEnum.NOT_PASSED.getCode();
        } else if (bonus.getPlanType() == AppraisePlanTypeEnum.MEET_CRITERION_APPRAISAL.getType()){
            return bonus.getQualified() ? RulePassedEnum.PASSED.getCode() : RulePassedEnum.NOT_PASSED.getCode();
        } else {
            return bonus.getAmount() < 0 ? RulePassedEnum.NOT_PASSED.getCode():RulePassedEnum.PASSED.getCode();
        }

    }


    /**
     * 判断红线是否处于试用期
     * @param bonus
     * @return
     */
    private boolean isMeetCriterionProbationary(BonusDTO bonus) {
        return bonus.getPlanType() == AppraisePlanTypeEnum.MEET_CRITERION_APPRAISAL.getType()
                && "试用期".equals(bonus.getStageName());

    }

}
