package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.tenant;

import com.alibaba.druid.filter.AutoLoad;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * @Title: RegionCoverageVO
 * @Description: 城市覆盖情况
 * @Author: wuyongjiang
 * @Date: 2022/8/23 11:40
 */
@TypeDoc(
        name = "城市覆盖情况",
        description = "城市覆盖情况"
)
@Getter
@Setter
@AutoLoad
@NoArgsConstructor
@ToString
public class RegionCoverageVO {

    @FieldDoc(
            description = "覆盖数量"
    )
    @ApiModelProperty("覆盖数量")
    private Integer coverageRegionCount;

    @FieldDoc(
            description = "城市数量"
    )
    @ApiModelProperty("城市数量")
    private Integer regionTotal;
}
