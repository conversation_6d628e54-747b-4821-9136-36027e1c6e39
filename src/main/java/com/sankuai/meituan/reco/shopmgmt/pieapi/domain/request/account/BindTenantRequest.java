package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.account;

import javax.validation.constraints.NotNull;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import lombok.Data;

@Data
@TypeDoc(
        description = "大象账号绑定租户请求"
)
public class BindTenantRequest {

    @FieldDoc(description = "eToken")
    @NotNull
    private String eToken;
}
