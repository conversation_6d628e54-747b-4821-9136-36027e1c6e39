package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.impl;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.AbstractSinglePendingTaskService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.EquipmentServiceWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: hezhengyu
 * @create: 2023-11-17 14:19
 */
@Slf4j
@Service
public class EquipmentReceivePendingTaskService extends AbstractSinglePendingTaskService {

    @Resource
    private EquipmentServiceWrapper equipmentServiceWrapper;

    @Override
    protected PendingTaskResult getPendingTaskCount(PendingTaskParam param) throws Exception {
        Long accountId = param.getUser().getAccountId();
        if (CollectionUtils.isEmpty(param.getStoreIds())) {
            log.warn("无门店： {}", param);
            return PendingTaskResult.createNumberMarker(0);
        }
        CommonResponse<Integer> resp = equipmentServiceWrapper.countTodoOrderForEmp(param.getTenantId(), accountId, param.getStoreIds().get(0));
        if (!resp.isSuccess()) {
            return PendingTaskResult.createNumberMarker(0);
        }

        return PendingTaskResult.createNumberMarker(resp.getData());
    }

    @Override
    protected AuthCodeEnum module() {
        return AuthCodeEnum.MY_EQUIPMENT;
    }
}
