package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.price;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price.AdjustPriceStrategyVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price.CompetitivenessSkuListVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price.CompetitivenessSkuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price.CompetitorSkuListVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price.CompetitorSkuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price.PriceEffectIndexReferencePriceVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price.RegionStoreSkuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price.PriceEffectIndexToBeImprovedVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price.PriceEffectIndexVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price.ReferencePriceVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price.SkuWithPriceEffectIndexVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.SkuTagCategoryVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.SkuTagSimpleVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MoneyUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ocms.OCMSUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.AuthThriftWrapper;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.PermissionGroupTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.OCMSServiceWrapper;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.QueryTenantTagRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.TagThriftService;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.TenantTagDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.TenantTagResponse;
import com.sankuai.meituan.shangou.empower.price.client.dto.PageInfoDTO;
import com.sankuai.meituan.shangou.empower.price.client.dto.price_effect.CompetitivenessSkuDTO;
import com.sankuai.meituan.shangou.empower.price.client.dto.price_effect.CompetitivenessSkuPageDTO;
import com.sankuai.meituan.shangou.empower.price.client.dto.price_effect.CompetitorSkuDTO;
import com.sankuai.meituan.shangou.empower.price.client.enums.PriceEffectIndexEnum;
import com.sankuai.meituan.shangou.empower.price.client.dto.price_effect.PriceEffectIndexDTO;
import com.sankuai.meituan.shangou.empower.price.client.dto.price_effect.SkuWithPriceEffectIndexDTO;
import com.sankuai.meituan.shangou.empower.price.client.request.price_effect.CompetitivenessSkuRequest;
import com.sankuai.meituan.shangou.empower.price.client.request.price_effect.CompetitorSkuRequest;
import com.sankuai.meituan.shangou.empower.price.client.request.price_effect.FollowSkuRequest;
import com.sankuai.meituan.shangou.empower.price.client.request.price_effect.PriceEffectIndexReferencePriceQueryRequest;
import com.sankuai.meituan.shangou.empower.price.client.request.price_effect.RegionStoreSkuQueryRequest;
import com.sankuai.meituan.shangou.empower.price.client.request.price_effect.SkuPriceEffectIndexRequest;
import com.sankuai.meituan.shangou.empower.price.client.request.price_effect.StorePriceEffectIndexRequest;
import com.sankuai.meituan.shangou.empower.price.client.response.price_effect.CompetitivenessSkuResponse;
import com.sankuai.meituan.shangou.empower.price.client.response.price_effect.CompetitorSkuResponse;
import com.sankuai.meituan.shangou.empower.price.client.response.price_effect.FollowSkuResponse;
import com.sankuai.meituan.shangou.empower.price.client.response.price_effect.PriceEffectIndexReferencePriceQueryResponse;
import com.sankuai.meituan.shangou.empower.price.client.response.price_effect.RegionStoreSkuQueryResponse;
import com.sankuai.meituan.shangou.empower.price.client.response.price_effect.SkuWithPriceEffectIndexResponse;
import com.sankuai.meituan.shangou.empower.price.client.service.PriceEffectThriftService;
import com.sankuai.meituan.shangou.saas.common.money.PriceUtils;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Author: wangyihao04
 * @Date: 2020-08-14 17:14
 * @Mail: <EMAIL>
 */
@Rhino
@Service
@Slf4j
public class PriceEffectWrapper {
    @Resource
    private PriceEffectThriftService priceEffectThriftService;
    @Resource
    private TagThriftService.Iface tagThriftService;
    @Resource
    private AuthThriftWrapper authThriftWrapper;

    public CommonResponse<Void> followSku(Long tenantId, Long storeId, String skuId, String spuId, Boolean follow) {
        FollowSkuRequest request = new FollowSkuRequest(tenantId, storeId, skuId, spuId, follow);
        try {
            FollowSkuResponse response = priceEffectThriftService.followSku(request);
            if (Objects.isNull(response) || Objects.isNull(response.getStatus())) {
                throw new CommonLogicException("调用价格评估服务关注接口返回为null");
            }
            if (response.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
                throw new CommonLogicException("调用价格评估服务关注接口错误,msg " + response.getStatus().getMsg());
            }
            return CommonResponse.success(null);
        } catch (CommonLogicException e) {
            log.error("调用关注功能内部异常", e);
            return CommonResponse.fail2(ResultCode.INTERNAL_SERVER_ERROR);
        } catch (TException e) {
            log.error("调用关注功能网络异常", e);
            return CommonResponse.fail2(ResultCode.RETRY_INNER_FAIL);
        }
    }

    public CommonResponse<CompetitorSkuListVO> queryCompetitorSkuList(Long tenantId, Long storeId, String skuId, String spuId) {
        CompetitorSkuRequest request = new CompetitorSkuRequest(tenantId, storeId, skuId, spuId);
        try {
            CompetitorSkuResponse response = priceEffectThriftService.queryCompetitorSkuList(request);
            if (Objects.isNull(response) || Objects.isNull(response.getStatus())) {
                throw new CommonLogicException("调用价格评估服务查询平台竞品列表返回为null");
            }
            if (response.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
                throw new CommonLogicException("调用价格评估服务查询平台竞品列表错误,msg " + response.getStatus().getMsg());
            }
            CompetitorSkuListVO competitorSkuListVO = Convert.convertToCompetitorSkuListVO(response.getCompetitorSkuDTOList());
            return CommonResponse.success(competitorSkuListVO);
        } catch (CommonLogicException e) {
            log.error("调用价格评估服务查询平台竞品列表内部异常", e);
            return CommonResponse.fail2(ResultCode.INTERNAL_SERVER_ERROR);
        } catch (TException e) {
            log.error("调用价格评估服务查询平台竞品列表错误网络异常", e);
            return CommonResponse.fail2(ResultCode.RETRY_INNER_FAIL);
        }
    }

    public CommonResponse<CompetitivenessSkuListVO> queryCompetitivenessStoreSkuList(Long tenantId, Long storeId,
                                                                                     String keyword, Integer pageSize,
                                                                                     Integer pageNum) {
        CompetitivenessSkuRequest request = new CompetitivenessSkuRequest(tenantId, storeId,
                keyword, pageSize, pageNum);
        try {
            CompetitivenessSkuResponse response = priceEffectThriftService.queryCompetitivenessStoreSkuList(request);
            if (Objects.isNull(response) || Objects.isNull(response.getStatus())) {
                throw new CommonLogicException("调用价格评估服务查询商品价格竞争力列表返回为null");
            }
            if (response.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
                throw new CommonLogicException("调用价格评估服务查询商品价格竞争力列表错误,msg " + response.getStatus().getMsg());
            }
            CompetitivenessSkuListVO skuListVO = Convert.convertToCompetitivenessSkuVO(response.getData());
            return CommonResponse.success(skuListVO);
        } catch (CommonLogicException e) {
            log.error("调用价格评估服务查询商品价格竞争力列表内部异常", e);
            return CommonResponse.fail2(ResultCode.INTERNAL_SERVER_ERROR);
        } catch (TException e) {
            log.error("调用价格评估服务查询商品价格竞争力列表网络异常", e);
            return CommonResponse.fail2(ResultCode.RETRY_INNER_FAIL);
        }
    }


    public CommonResponse<PriceEffectIndexToBeImprovedVO> queryPriceEffectToBeImprovedSkuList(
            Long tenantId, Long storeId, Boolean needStoreAggregation, String priceEffectIndexType,
            Boolean filterCore, Integer pageSize, Integer pageNum, Integer sortType) {

        Long tagId = null;
        if (Objects.nonNull(filterCore) && filterCore) {
            tagId = queryCoreTagId();
        }

        StorePriceEffectIndexRequest request = new StorePriceEffectIndexRequest(
                tenantId, storeId, needStoreAggregation, priceEffectIndexType,
                tagId, pageSize, pageNum, sortType);
        try {
            SkuWithPriceEffectIndexResponse response = priceEffectThriftService.pageQueryStorePriceEffectIndexSkuList(request);
            if (Objects.isNull(response) || Objects.isNull(response.getStatus())) {
                throw new CommonLogicException("调用价格评估服务查询待优化商品列表返回为null");
            }
            if (response.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
                throw new CommonLogicException("调用价格评估服务查询待优化商品列表错误,msg " + response.getStatus().getMsg());
            }
            PriceEffectIndexToBeImprovedVO skuListVO = Convert.convertToPriceEffectIndexToBeImprovedVO(
                    response.getAggregation(), response.getSkuList(), response.getPageInfoDTO());
            return CommonResponse.success(skuListVO);
        } catch (CommonLogicException e) {
            log.error("调用价格评估服务查询待优化商品列表内部异常", e);
            return CommonResponse.fail2(ResultCode.INTERNAL_SERVER_ERROR);
        } catch (TException e) {
            log.error("调用价格评估服务查询待优化商品列表网络异常", e);
            return CommonResponse.fail2(ResultCode.RETRY_INNER_FAIL);
        }
    }

    public Long queryCoreTagId() {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        QueryTenantTagRequest queryTenantTagRequest = new QueryTenantTagRequest(user.getTenantId());
        log.info("OCMSServiceWrapper.queryTags  tagThriftService.queryTags() request:{}", queryTenantTagRequest);
        try {
            TenantTagResponse response = tagThriftService.queryTags(queryTenantTagRequest);
            log.info("OCMSServiceWrapper.queryTags  tagThriftService.queryTags() response:{}", response);
            if (Objects.isNull(response) || Objects.isNull(response.getStatus()) || response.getStatus().getCode() != 0){
                return null;
            }

            return Optional.ofNullable(response.getData())
                    .map(List::stream)
                    .orElse(Stream.empty())
                    .flatMap(tagCategoryDTO -> tagCategoryDTO.getTagList().stream())
                    .filter(tagDTO -> tagDTO.getName().equals(MccConfigUtil.getCoreTagName()))
                    .findAny()
                    .map(TenantTagDTO::getId)
                    .orElse(null);
        }catch (TException e) {
            throw new CommonLogicException(e);
        }
    }

    @Degrade(rhinoKey = "PriceEffectWrapper.queryRegionStoreSkuList",
            fallBackMethod = "fallbackQueryRegionStoreSkuList",
            ignoreExceptions = {CommonLogicException.class},
            isDegradeOnException = true)
    public CommonResponse<List<RegionStoreSkuVO>> queryRegionStoreSkuList(User user, Long storeId, String spuId, String skuId) {

        try {
            // 构建查询参数
            RegionStoreSkuQueryRequest request = new RegionStoreSkuQueryRequest();
            request.setTenantId(user.getTenantId());
            request.setStoreId(storeId);
            request.setSpuId(spuId);
            request.setSkuId(skuId);

            // 查询同城门店商品列表
            RegionStoreSkuQueryResponse response = priceEffectThriftService.queryRegionStoreSkuList(request);
            log.info("PriceEffectThriftService queryRegionStoreSkuList request:{}, response:{}", request, response);

            if (Objects.isNull(response) || Objects.isNull(response.getStatus())) {
                throw new CommonLogicException("查询同城门店商品列表未返回结果", ResultCode.FAIL);
            }
            if (response.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
                throw new CommonLogicException(response.getStatus().getMsg(), ResultCode.FAIL);
            }

            // 查询用户有权限的门店
            List<Long> hasPermissionStoreIds = authThriftWrapper.queryPermissionGroupId(user, PermissionGroupTypeEnum.POI, Long::valueOf);

            // 构建返回结果
            List<RegionStoreSkuVO> regionStoreSkuVOList = ConverterUtils.convertList(response.getRegionStoreSkuDTOList(),
                    regionStoreSkuDTO -> RegionStoreSkuVO.build(regionStoreSkuDTO, storeId, hasPermissionStoreIds));

            return CommonResponse.success(regionStoreSkuVOList);
        } catch (TException e) {
            log.error("查询同城门店商品列表异常, tenantId:{}, storeId:{}, spuId:{}, skuId:{}", user.getTenantId(), storeId, spuId, skuId, e);
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse<List<RegionStoreSkuVO>> fallbackQueryRegionStoreSkuList(User user, Long storeId, String spuId, String skuId, Throwable e) {
        log.warn("查询同城门店商品信息降级, PriceEffectWrapper.queryRegionStoreSkuList降级, tenantId:{}, storeId:{}, spuId:{}, skuId:{}", user.getTenantId(), storeId, spuId, skuId, e);
        return CommonResponse.success(Lists.newArrayList());
    }

    @Degrade(rhinoKey = "PriceEffectWrapper.querySkuPriceEffectIndexReferencePrice",
            fallBackMethod = "fallbackQuerySkuPriceEffectIndexReferencePrice",
            ignoreExceptions = {CommonLogicException.class},
            isDegradeOnException = true)
    public CommonResponse<Map<String, List<PriceEffectIndexReferencePriceVO>>> querySkuPriceEffectIndexReferencePrice(
            User user, Long storeId, List<String> skuIds) {

        try {

            // 构建查询参数
            PriceEffectIndexReferencePriceQueryRequest request = new PriceEffectIndexReferencePriceQueryRequest();
            request.setTenantId(user.getTenantId());
            request.setStoreId(storeId);
            request.setSkuIds(skuIds);
            request.setPriceEffectIndexList(Lists.newArrayList(PriceEffectIndexEnum.PRICE_INDEX, PriceEffectIndexEnum.RESEARCH_PREMIUM_RATE,
                    PriceEffectIndexEnum.MT_COMPETITOR_PERMIUM_RATE, PriceEffectIndexEnum.GROSS_PROFIT));

            // 查询价格评估指标参考价
            PriceEffectIndexReferencePriceQueryResponse response = priceEffectThriftService.querySkuPriceEffectIndexReferencePrice(request);
            log.info("PriceEffectThriftService querySkuPriceEffectIndexReferencePrice request:{}, response:{}", request, response);

            if (Objects.isNull(response) || Objects.isNull(response.getStatus())) {
                throw new CommonLogicException("查询价格评估指标参考价未返回结果", ResultCode.FAIL);
            }

            if (response.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
                throw new CommonLogicException(response.getStatus().getMsg(), ResultCode.FAIL);
            }

            Map<String, List<PriceEffectIndexReferencePriceVO>> skuId2PriceEffectReferencePriceListMap = Maps.newHashMap();

            if (MapUtils.isNotEmpty(response.getSkuIdPriceEffectIndexReferencePriceMap())) {
                response.getSkuIdPriceEffectIndexReferencePriceMap().forEach((skuId, dtoList) ->
                        skuId2PriceEffectReferencePriceListMap.put(skuId, ConverterUtils.convertList(dtoList, PriceEffectIndexReferencePriceVO::build)));
            }
            return CommonResponse.success(skuId2PriceEffectReferencePriceListMap);
        } catch (TException e) {
            log.error("查询价格评估指标参考价异常, tenantId:{}, storeId:{}, skuIds:{}", user.getTenantId(), storeId, skuIds, e);
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse<Map<String, List<PriceEffectIndexReferencePriceVO>>> fallbackQuerySkuPriceEffectIndexReferencePrice(
            User user, Long storeId, List<String> skuIds, Throwable e) {
        log.warn("查询价格评估指标参考价降级, PriceEffectWrapper.querySkuPriceEffectIndexReferencePrice降级, tenantId:{}, storeId:{}, skuIds:{}", user.getTenantId(), storeId, skuIds, e);
        return CommonResponse.success(Maps.newHashMap());
    }

    static class Convert {
        static PriceEffectIndexToBeImprovedVO convertToPriceEffectIndexToBeImprovedVO(
                PriceEffectIndexDTO aggregation, List<SkuWithPriceEffectIndexDTO> skuList, PageInfoDTO pageInfoDTO) {
            PriceEffectIndexVO storeAggregation = PriceEffectIndexVO.valueOf(aggregation);

            List<SkuWithPriceEffectIndexVO> resultSkuList = Optional.ofNullable(skuList)
                    .map(List::stream).orElse(Stream.empty())
                    .map(skuPriceEffectIndex ->
                            SkuWithPriceEffectIndexVO.builder()
                                    .skuId(skuPriceEffectIndex.getSkuId())
                                    .spuId(skuPriceEffectIndex.getSpuId())
                                    .imageList(skuPriceEffectIndex.getImageList())
                                    .weightType(skuPriceEffectIndex.getWeightType())
                                    .weight(skuPriceEffectIndex.getWeight())
                                    .spec(skuPriceEffectIndex.getSpec())
                                    .skuName(skuPriceEffectIndex.getSkuName())
                                    .saleUnit(skuPriceEffectIndex.getSaleUnit())
                                    .sales30Day(skuPriceEffectIndex.getThirtySales())
                                    .channelPrice(skuPriceEffectIndex.getChannelPrice())
                                    .standardUnitChannelPrice(skuPriceEffectIndex.getStandardUnitChannelPrice())
                                    .offlinePrice(skuPriceEffectIndex.getOfflinePrice())
                                    .standardUnitOfflinePrice(skuPriceEffectIndex.getStandardUnitOfflinePrice())
                                    .reviewPrice(skuPriceEffectIndex.getReviewPrice())
                                    .standardUnitReviewPrice(skuPriceEffectIndex.getStandardUnitReviewPrice())
                                    .referencePrice(ReferencePriceVO.valueOf(skuPriceEffectIndex.getReferencePrice()))
                                    .hitStrategyDTO(AdjustPriceStrategyVO.of(skuPriceEffectIndex.getPriceStrategy()))
                                    .priceEffectIndex(PriceEffectIndexVO.valueOf(skuPriceEffectIndex.getPriceEffectIndex()))
                                    .build()
                    ).collect(Collectors.toList());

            PageInfoVO pageInfoVO = convertToPageInfoVO(pageInfoDTO);
            return new PriceEffectIndexToBeImprovedVO(storeAggregation, resultSkuList, pageInfoVO);
        }


        static CompetitorSkuListVO convertToCompetitorSkuListVO(List<CompetitorSkuDTO> list) {
            List<CompetitorSkuVO> competitorSkuVOS = Optional.ofNullable(list)
                    .map(List::stream)
                    .orElse(Stream.empty())
                    .map(Convert::convertToCompetitorSkuVO)
                    .collect(Collectors.toList());
            CompetitorSkuListVO result = new CompetitorSkuListVO();
            result.setCompetitorSkuVOList(competitorSkuVOS);
            return result;
        }

        static CompetitorSkuVO convertToCompetitorSkuVO(CompetitorSkuDTO dto) {
            return CompetitorSkuVO.builder()
                    .storeId(dto.getStoreId())
                    .storeName(dto.getStoreName())
                    .skuName(dto.getSkuName())
                    .spec(dto.getSpec())
                    .onlinePrice(parsePrice(dto.getOriginalPrice()))
                    .discountPrice(parsePrice(dto.getActualPrice()))
                    .onlinePriceOf500g(parsePrice(dto.getActualPriceOf500() == null ? dto.getOriginalPriceOf500() : dto.getActualPriceOf500()))
                    .sales30day(dto.getMonthSale())
                    .oneself(dto.getOneself())
                    .build();
        }

        static CompetitivenessSkuListVO convertToCompetitivenessSkuVO(CompetitivenessSkuPageDTO page) {
            List<CompetitivenessSkuVO> dataList = Optional
                    .ofNullable(page.getCompetitivenessSkuDTOList())
                    .map(List::stream)
                    .orElse(Stream.empty())
                    .map(Convert::convertToCompetitivenessSkuVO)
                    .collect(Collectors.toList());
            PageInfoVO pageInfoVO = convertToPageInfoVO(page.getPageInfoDTO());
            return new CompetitivenessSkuListVO(dataList, pageInfoVO);
        }

        static CompetitivenessSkuVO convertToCompetitivenessSkuVO(CompetitivenessSkuDTO dto) {
            return CompetitivenessSkuVO.builder()
                    .skuId(dto.getSkuId())
                    .spuId(dto.getSpuId())
                    .skuName(dto.getSkuName())
                    .competitivenessRate(dto.getCompetitivenessRate())
                    .displayRate(displayRate(dto.getCompetitivenessRate()))
                    .aroundLowestStoreName(dto.getAroundLowestStoreName())
                    .aroundStoreCount(dto.getAroundStoreCount())
                    .isFollow(dto.getIsFollow())
                    .images(dto.getImages())
                    .spec(dto.getSpec())
                    .weight(dto.getWeight())
                    .weightType(dto.getWeightType())
                    .saleUnit(dto.getSaleUnit())
                    .monthSaleAmount(dto.getMonthSaleAmount())
                    .offlinePrice(parsePrice(dto.getOfflinePrice()))
                    .offlinePriceOf500g(convertToPriceOf500g(dto, CompetitivenessSkuDTO::getWeightType, CompetitivenessSkuDTO::getWeight, CompetitivenessSkuDTO::getOfflinePrice))
                    .onlinePrice(parsePrice(dto.getOnlinePrice()))
                    .onlinePriceOf500g(convertToPriceOf500g(dto, CompetitivenessSkuDTO::getWeightType, CompetitivenessSkuDTO::getWeight, CompetitivenessSkuDTO::getOnlinePrice))
                    .onlineDiscountPrice(parsePrice(dto.getOnlineDiscountPrice()))
                    .onlineDiscountPriceOf500g(parsePrice((dto.getWeightType() == 3 || dto.getWeight() == 0)
                            ? null : dto.getOnlineDiscountPrice()))
                    .aroundLowestPrice(parsePrice(dto.getAroundLowestOriginPrice()))
                    .aroundLowestPriceOf500g(parsePrice((dto.getWeightType() == 3 || dto.getWeight() == 0)
                            ? null : dto.getAroundLowestOriginPrice()))
                    .aroundLowestDiscountPrice(parsePrice(dto.getAroundLowestPrice()))
                    .aroundLowestDiscountPriceOf500g(parsePrice((dto.getWeightType() == 3 || dto.getWeight() == 0)
                            ? null : dto.getAroundLowestPrice()))
                    .adjustPriceStrategy(AdjustPriceStrategyVO.of(dto.getAdjustPriceStrategy()))
                    .reviewPrice(parsePrice(dto.getReviewPrice()))
                    .reviewPriceOf500g(convertToPriceOf500g(dto, CompetitivenessSkuDTO::getWeightType, CompetitivenessSkuDTO::getWeight, CompetitivenessSkuDTO::getReviewPrice))
                    .build();
        }


        static <T> Double convertToPriceOf500g(T t, Function<T, Integer> weightTypeFunc,
                                               Function<T, Integer> weightFunc,
                                               Function<T, Long> priceFunc) {
            if (weightTypeFunc.apply(t) == 3 || weightFunc.apply(t) == 0) {
                return null;
            }
            Long origin = priceFunc.apply(t);
            if (Objects.isNull(origin)) {
                return null;
            }
            //转市斤
            return parsePrice(
                    PriceUtils.calculatePriceByWeight(origin, weightFunc.apply(t), 500));
        }


        static Double parsePrice(Long price) {
            return Optional
                    .ofNullable(price)
                    .map(MoneyUtils::centToYuanByDown)
                    .orElse(null);
        }

        static PageInfoVO convertToPageInfoVO(PageInfoDTO dto) {
            if (dto == null) {
                throw new CommonLogicException("返回值分页信息为空");
            }
            PageInfoVO pageInfo = new PageInfoVO();
            pageInfo.setPage(dto.getPage());
            pageInfo.setSize(dto.getSize());
            pageInfo.setTotalPage(dto.getTotalPage());
            pageInfo.setTotalSize(Math.toIntExact(dto.getTotalSize()));
            return pageInfo;
        }

        static String displayRate(Double rate) {
            if (rate <= 0) {
                return "0";
            } else if (rate >= 1) {
                return "100";
            } else {
                return BigDecimal.valueOf(rate).multiply(BigDecimal.valueOf(100)).setScale(0, BigDecimal.ROUND_HALF_UP).toPlainString();
            }
        }
    }
}
