package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelCategoryVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "查询商品渠道类目响应"
)
@Data
@ApiModel("查询商品渠道类目响应")
public class ChannelCategoryInfoResponse {

    @FieldDoc(
            description = "渠道类目信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道类目信息")
    private List<ChannelCategoryLevelVO> categoryLevelVOList;
}
