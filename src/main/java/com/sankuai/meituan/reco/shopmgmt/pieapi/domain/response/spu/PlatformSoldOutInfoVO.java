package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.PlatformSoldOutInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

@TypeDoc(
        description = "平台下架信息集"
)
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class PlatformSoldOutInfoVO {

    @FieldDoc(
            description = "是否为平台下架商品 true:是，false:否", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "是否为平台下架商品 true:是，false:否", required = true)
    private boolean platformSoldOutFlag;

    @FieldDoc(
            description = "平台下架提示语", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "平台下架提示语", required = true)
    private String platformSoldOutTips;

    @FieldDoc(
            description = "平台下架原因", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "平台下架原因", required = true)
    private String platformSoldOutReason;

    @FieldDoc(
            description = "平台下架周期提示语", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "平台下架周期提示语", required = true)
    private String platformSoldOutCycleTips;

    @FieldDoc(
            description = "平台下架类型", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "平台下架类型")
    private Integer platformSoldOutType;

    public static PlatformSoldOutInfoVO of(PlatformSoldOutInfoDTO soldOutInfoDTO) {
        if (Objects.isNull(soldOutInfoDTO)) {
            return null;
        }
        return PlatformSoldOutInfoVO.builder()
                .platformSoldOutFlag(true)
                .platformSoldOutTips(soldOutInfoDTO.getPlatformSoldOutTips())
                .platformSoldOutReason(soldOutInfoDTO.getSoldOutReason())
                .platformSoldOutCycleTips(soldOutInfoDTO.getPlatformSoldOutCycleTips())
                .platformSoldOutType(soldOutInfoDTO.getSoldOutType())
                .build();
    }

}
