package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * FileName: FastCreateSpuVO
 * Author:   wangjiawei31
 * Date:     2021/3/17 3:17 下午
 * Description:
 */
@Data
@TypeDoc(description = "快速创建商品的返回值")
public class FastCreateSpuVO {

    @FieldDoc(description = "2级业务错误码")
    private int errorCode;

    @FieldDoc(description = "2级业务消息")
    private String errorMsg;

    @FieldDoc(description = "是否需要跳转改价页面")
    private boolean needJumpPrice;

    @FieldDoc(description = "spuId")
    private String spuId;

}
