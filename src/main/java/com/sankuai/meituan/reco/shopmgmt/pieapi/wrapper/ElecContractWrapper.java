package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.meituan.shangou.saas.tenant.thrift.ContractThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.config.response.CommonResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.contract.SignerInfoDTO;
import com.meituan.shangou.saas.tenant.thrift.dto.contract.request.SendSmsCodeRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.contract.request.SignContractRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.contract.request.SignerInfoQueryRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.contract.response.SendSmsCodeResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.contract.response.SignerInfoQueryResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.contract.response.SignerInfosQueryResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.converters.ElecContractConverter;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.eleccontract.ContractSignRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ResponseHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2020/3/16 18:52
 * @Description:
 */
@Slf4j
@Component
public class ElecContractWrapper {

    @Autowired
    private ContractThriftService contractThriftService;

    /**
     * 查询门店电子协议签署人信息
     * @param tenantId
     * @param poiId
     * @param accountId
     * @return
     */
    public SignerInfoDTO querySignerInfo(Long tenantId, Long poiId, Long accountId) {
        SignerInfoQueryRequest request = new SignerInfoQueryRequest();
        request.setTenantId(tenantId);
        request.setPoiId(poiId);
        request.setAccountId(accountId);
        try {
            SignerInfoQueryResponse response = contractThriftService.querySignerInfo(request);
            log.info("ContractThriftService.querySignerInfo, request:{}, response:{}", request, response);
            ResponseHandler.checkResponseAndStatus(response, rr -> rr.getStatus().getCode(),
                    rr -> rr.getStatus().getMessage(), ResultCode.ELEC_CONTRACT_ERROR);
            return response.getSignerInfo();
        } catch (CommonRuntimeException e) {
            throw new CommonRuntimeException(e.getMessage(), ResultCode.CONTRACT_SINGER_ERROR);
        } catch (Exception e) {
            log.warn("查询门店电子协议签署人信息异常: request [{}].", request, e);
            throw new CommonRuntimeException("查询门店电子协议签署人信息异常，请稍后重试.", ResultCode.ELEC_CONTRACT_ERROR);
        }
    }

    /**
     * 查询门店的所有可选的签署主体信息
     *
     * @param tenantId
     * @param poiId
     * @param accountId
     * @return
     */
    public List<SignerInfoDTO> queryAllPossibleSignerInfos(Long tenantId, Long poiId, Long accountId) {
        SignerInfoQueryRequest request = new SignerInfoQueryRequest();
        request.setTenantId(tenantId);
        request.setPoiId(poiId);
        request.setAccountId(accountId);
        try {
            SignerInfosQueryResponse response = contractThriftService.queryAllPossibleSignerInfos(request);
            log.info("ContractThriftService.queryAllPossibleSignerInfos, request:{}, response:{}", request, response);
            ResponseHandler.checkResponseAndStatus(response, rr -> rr.getStatus().getCode(),
                    rr -> rr.getStatus().getMessage(), ResultCode.ELEC_CONTRACT_ERROR);
            return response.getSignerInfos();
        } catch (CommonRuntimeException e) {
            throw new CommonRuntimeException(e.getMessage(), ResultCode.CONTRACT_SINGER_ERROR);
        } catch (Exception e) {
            log.warn("查询门店的所有可选的签署主体信息异常: request [{}].", request, e);
            throw new CommonRuntimeException("查询门店的所有可选的签署主体信息，请稍后重试.", ResultCode.ELEC_CONTRACT_ERROR);
        }
    }


    /**
     * 签署电子协议
     *
     * @param tenantId
     * @param request
     */
    public void signContract(Long tenantId, Long operatorId, String operatorAccount, ContractSignRequest request) {
        SignContractRequest thriftRequest = ElecContractConverter.signRequestConvert(tenantId, operatorId,
                operatorAccount, request);

        try {
            CommonResponse response = contractThriftService.signContract(thriftRequest);
            log.info("ContractThriftService.signContract, request:{}, response:{}", request, response);
            ResponseHandler.checkResponseAndStatus(response, rr -> rr.getStatus().getCode(),
                    rr -> rr.getStatus().getMessage(), ResultCode.ELEC_CONTRACT_ERROR);
        } catch (CommonRuntimeException e) {
            throw e;
        } catch (Exception e) {
            log.warn("签署电子协议异常: request [{}].", request, e);
            throw new CommonRuntimeException("签署电子协议异常，请稍后重试.", ResultCode.ELEC_CONTRACT_ERROR);
        }
    }



    public String sendVerificationCode(String mobile, String ip, String userAgent, long accountId) {
        SendSmsCodeRequest request = new SendSmsCodeRequest();
        request.setMobile(mobile);
        request.setIp(ip);
        request.setUserAgent(userAgent);
        request.setAccountId(accountId);

        try {
            SendSmsCodeResponse response = contractThriftService.sendVerificationCode(request);

            ResponseHandler.checkResponseAndStatus(response, rr -> rr.getStatus().getCode(),
                    rr -> rr.getStatus().getMessage(), ResultCode.ELEC_CONTRACT_ERROR);

            return response.getRequestCode();
        }
        catch (CommonRuntimeException e) {
            throw e;
        }
        catch (Exception e) {
            log.error("发送短信验证码异常: request [{}].", request, e);
            throw new CommonRuntimeException("发送短信验证码异常，请稍后重试.", ResultCode.ELEC_CONTRACT_ERROR);
        }

    }


}
