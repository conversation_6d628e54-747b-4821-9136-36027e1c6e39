package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.management;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @createTime 2019/11/18
 * @description
 */
@TypeDoc(
        description = "经营首页请求"
)
@ApiModel("经营首页请求")
@Data
public class ManagementHomePageRequest {

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店ID")
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "是否返回今日营收数据", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否返回今日营收数据")
    @NotNull
    private Boolean returnTodayRevenue;

    @FieldDoc(
            description = "是否返回缺货数据", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否返回缺货数据")
    @NotNull
    private Boolean returnStockLackData;

    @FieldDoc(
            description = "是否返回备货数据", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否返回备货数据")
    @NotNull
    private Boolean returnStockUpData;

    @FieldDoc(
            description = "是否返回拣货数据", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否返回拣货数据")
    @NotNull
    private Boolean returnPickData;

    @FieldDoc(
            description = "是否返回合流数据", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否返回合流数据")
    @NotNull
    private Boolean returnMergeData;
}
