package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.WarehousePickCountVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@TypeDoc(
        description = "波次拣货出库商品数量和类型查询返回"
)
@Data
@ApiModel("波次拣货出库商品数量和类型查询返回")
public class WarehousePickQueryCountResponse {

    @FieldDoc(
            description = "商品列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "商品列表", required = true)
    private WarehousePickCountVo countList;
}
