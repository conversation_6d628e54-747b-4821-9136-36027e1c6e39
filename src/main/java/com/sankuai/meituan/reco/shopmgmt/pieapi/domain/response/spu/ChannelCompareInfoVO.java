package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SaleAttrValueInfoVO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.diffcompare.ChannelProductPropertyInfoDTO;
import lombok.Builder;
import lombok.Data;

@TypeDoc(
        description = "不一致商品"
)
@Data
@Builder
public class ChannelCompareInfoVO {
    @FieldDoc(
            description = "渠道信息：0-牵牛花；100-美团；200-饿了么"
    )
    private Integer channelId;

    @FieldDoc(
            description = "当前渠道是否存在对应的值"
    )
    private Boolean existValue;

    @FieldDoc(
            description = "当前渠道对应值信息"
    )
    private String channelValueInfo;

    @FieldDoc(
            description = "规格ID"
    )
    private String skuId;

    @FieldDoc(
            description = "牵牛花渠道对应值信息"
    )
    private String baiChuanValueInfo;


    @FieldDoc(
            description = "牵牛花销售属性"
    )
    private List<SaleAttrValueInfoVO> baichuanAttrValueList;

    @FieldDoc(
            description = "渠道销售属性"
    )
    private List<SaleAttrValueInfoVO> channelAttrValueList;

    public static ChannelCompareInfoVO of(ChannelProductPropertyInfoDTO propertyInfoDTO) {
        return ChannelCompareInfoVO.builder()
                .channelId(propertyInfoDTO.getChannelId())
                .channelValueInfo(propertyInfoDTO.getChannelProductFieldInfo())
                .existValue(propertyInfoDTO.getExistValue())
                .skuId(propertyInfoDTO.getSkuId())
                .baiChuanValueInfo(propertyInfoDTO.getBaiChuanProductFieldInfo())
                .baichuanAttrValueList(CollectionUtils.isNotEmpty(propertyInfoDTO.getBaichuanAttrValueList()) ?
                        propertyInfoDTO.getBaichuanAttrValueList().stream().map(SaleAttrValueInfoVO::ofBizDTO).collect(Collectors.toList()) : null)
                .channelAttrValueList(CollectionUtils.isNotEmpty(propertyInfoDTO.getChannelAttrValueList()) ?
                        propertyInfoDTO.getChannelAttrValueList().stream().map(SaleAttrValueInfoVO::ofBizDTO).collect(Collectors.toList()) : null)
                .build();
    }
}
