package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.appmodel;

import java.util.List;
import java.util.Map;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 菜单信息
 *
 * <AUTHOR>
 * @since 2021/7/2
 */
@TypeDoc(
        description = "菜单信息"
)
@Data
@NoArgsConstructor
@ApiModel("菜单信息")
public class MenuInfo {

    @FieldDoc(
            description = "菜单code", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "菜单code")
    private String menuCode;

    @FieldDoc(
            description = "菜单名", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "菜单名")
    private String menuName;

    @FieldDoc(
            description = "是否有访问权限", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "是否有访问权限")
    private boolean hasAuth;

    @FieldDoc(
            description = "跳转url", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "跳转url")
    private String url;

    @FieldDoc(
            description = "排序", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "排序")
    private Integer rank;

    @FieldDoc(
            description = "扩展信息（包括界面配置的菜单扩展信息和业务侧数据信息）", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "扩展信息（包括界面配置的菜单扩展信息和业务侧数据信息）")
    private Map<String, Object> extraInfoMap;

    @FieldDoc(
            description = "任务数", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "任务数")
    private Integer taskCount;

    @FieldDoc(
            description = "延时任务数", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "延时任务数")
    private Integer delayTaskCount;

    @FieldDoc(
            description = "子节点信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "子节点信息")
    private List<MenuInfo> children;

    @FieldDoc(
            description = "任务查询类型", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "任务查询类型，1为默认类型，当任务被点击时，有url时，跳转落地页；没有则显示tips；" +
            "2为查询类型，当任务被点击时，会调用/pieapi/appmodel/queryTableMenuInfo")
    private Integer taskQueryType;

}