package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.storecategory;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class MerchantStoreCategoryVO {

    @FieldDoc(
            description = "名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "名称", required = true)
    private String name;

    @FieldDoc(
            description = "上级店内分类名称 （当前分类是顶级分类时为空）", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "上级店内分类名称", required = true)
    private String parentName;

    @FieldDoc(
            description = "层级", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "层级", required = true)
    private Integer level;

    @FieldDoc(
            description = "店内分类id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "店内分类id", required = true)
    private Long categoryId;

    @FieldDoc(
            description = "父店内分类id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "父店内分类id", required = true)
    private Long parentCategoryId;

    @FieldDoc(
            description = "店内分类名称全路径", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "店内分类名称全路径", required = true)
    private String namePath;

    @FieldDoc(
            description = "父店内分类名称全路径 (一级分类时为空字符串)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "父店内分类名称全路径", required = true)
    private String parentNamePath;

    @FieldDoc(
            description = "门店分组ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店分组ID", required = true)
    private Integer storeGroupId;

    @FieldDoc(
            description = "分类状态：1-启用，2-禁用", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "分类状态：1-启用，2-禁用")
    private Integer enable;

    @FieldDoc(
            description = "分类排序值", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "分类排序值")
    private Integer sequence;
}
