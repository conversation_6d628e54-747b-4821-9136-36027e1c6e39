package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.inventorycount;

import com.google.common.collect.Lists;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.store.wms.thrift.inventorycount.InventoryCountThriftService;
import com.sankuai.meituan.reco.store.wms.thrift.inventorycount.req.InventoryCountOrderListQryCondition;
import com.sankuai.meituan.reco.store.wms.thrift.inventorycount.req.InventoryCountOrderListReq;
import com.sankuai.meituan.reco.store.wms.thrift.inventorycount.resp.InventoryCountOrderListResp;
import com.sankuai.shangou.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * author xujunfeng02
 * dateTime 2023/2/23 4:26 PM
 * description 歪马盘点wrapper
 */
@Component
@Slf4j
public class InventoryCountWrapper {

    // 盲盘状态：10-待确认, 20-待盘点, 30-盘点中
    private static final List<Integer> BLIND_UNFINISHED_STATUS = Lists.newArrayList(10, 20, 30);

    // 明盘状态：2-待开始 3-盘点中, 4-确认中, 9-提交中, 10-审核中, 11-已驳回, 15-待确认
    private static final List<Integer> UNFINISHED_STATUS = Lists.newArrayList(2, 3, 4, 9, 10, 11, 15);

    @Resource
    private InventoryCountThriftService thriftService;

    @MethodLog(logRequest = true, logResponse = true)
    public int count(PendingTaskParam param) {

        try {

            InventoryCountOrderListQryCondition condition = new InventoryCountOrderListQryCondition();
            condition.setWarehouseIds(Lists.newArrayList(param.getEntityId()));
            condition.setStatusList(BLIND_UNFINISHED_STATUS);
            condition.setStockTakeTaskStatusList(UNFINISHED_STATUS);
            long now = System.currentTimeMillis();
            condition.setEndTime(now);
            condition.setStartTime(now - 92L * 24 * 3600 * 1000); // 最多查询92天之前的单据
            InventoryCountOrderListReq req = new InventoryCountOrderListReq(1, 1, condition);
            Result<InventoryCountOrderListResp> result = thriftService.orderList(req);
            result.checkBizException();
            return result.getModule().getTotalCount();
        } catch (Exception e) {

            log.error("InventoryCountWrapper count error", e);
            return 0;
        }
    }
}