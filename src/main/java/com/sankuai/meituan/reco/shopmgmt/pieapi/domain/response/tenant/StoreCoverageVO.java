package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.tenant;

import com.alibaba.druid.filter.AutoLoad;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * @Title: StoreCoverageVO
 * @Description: 门店覆盖情况
 * @Author: wuyongjiang
 * @Date: 2022/8/23 11:40
 */
@TypeDoc(
        name = "门店覆盖情况",
        description = "门店覆盖情况"
)
@Getter
@Setter
@AutoLoad
@NoArgsConstructor
@ToString
public class StoreCoverageVO {

    @FieldDoc(
            description = "覆盖数量"
    )
    @ApiModelProperty("覆盖数量")
    private Integer coverageStoreCount;

    @FieldDoc(
            description = "门店数量"
    )
    @ApiModelProperty("门店数量")
    private Integer storeTotal;

}