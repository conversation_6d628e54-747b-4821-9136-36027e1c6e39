package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@TypeDoc(
        description = "门店信息"
)
@Data
@ApiModel("门店信息")
public class AllStoreRequest {
    @FieldDoc(
            description = "门店编号"
    )
    @ApiModelProperty(value = "门店编号", required = true)
    @NotNull
    private String storeId;
}
