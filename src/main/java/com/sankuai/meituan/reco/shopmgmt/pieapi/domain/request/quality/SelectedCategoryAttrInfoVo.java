package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.quality;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.product.dto.SpuPropertyInfoDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/9/20
 */
@TypeDoc(
        name = "已选择的商品动态属性",
        description = "已选择的商品动态属性"
)
@Data
public class SelectedCategoryAttrInfoVo {

    @FieldDoc(
            description = "属性ID"
    )
    @ApiModelProperty(name = "属性ID")
    private Long attrId;

    @FieldDoc(
            description = "属性名称"
    )
    @ApiModelProperty(name = "属性名称")
    private String attrName;

    @FieldDoc(
            description = "属性值ID"
    )
    @ApiModelProperty(name = "属性值ID")
    private Long attrValueId;

    @FieldDoc(
            description = "属性值"
    )
    @ApiModelProperty(name = "属性值")
    private String attrValue;

    public SpuPropertyInfoDto convert2SpuPropertyInfoDto() {
        SpuPropertyInfoDto infoDto = new SpuPropertyInfoDto();
        infoDto.setPropertyId(attrId);
        infoDto.setPropertyName(attrName);
        infoDto.setPropertyValueId(attrValueId);
        infoDto.setPropertyValueName(attrValue);

        return infoDto;
    }
}
