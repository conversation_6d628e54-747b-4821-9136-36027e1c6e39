package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.management;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.auth.AppModuleResult;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 运营首页查询返回-改版后
 *
 * <AUTHOR>
 * @since  2021/7/8
 */
@TypeDoc(
        description = "运营首页查询返回-改版后"
)
@Data
@ApiModel("运营首页查询返回")
public class ManagementReversionHomepageResponse {

    @FieldDoc(
            description = "运营数据模块", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty("运营数据模块")
    private AppModuleResult appModuleResult;
}
