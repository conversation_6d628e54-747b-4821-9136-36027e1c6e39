package com.sankuai.meituan.reco.shopmgmt.pieapi.domain;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.io.Serializable;

@Data
public class BillPayRemindVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @FieldDoc(description = "是否提醒")
    private boolean remind;

    @FieldDoc(description = "待支付账单数")
    private Integer pendingBillAmount;

    @FieldDoc(description = "通知类型")
    private String type;
}