package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ReviewFailedDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ReviewResultDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 商品提报审核信息
 * @author: WangSukuan
 * @create: 2020-03-15
 **/
@TypeDoc(
        description = "商品提报审核信息"
)
@Data
@ApiModel("商品提报审核信息")
@NoArgsConstructor
public class ReviewResultVO {

    @FieldDoc(
            description = "审核成功数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "审核成功数", required = true)
    private Long successCount;

    @FieldDoc(
            description = "提报申请id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "提报申请id", required = true)
    private Long failedCount;

    @FieldDoc(
            description = "失败明细", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "失败明细")
    private List<ReviewFailedVO> failedList;

    public ReviewResultVO buildReviewResultVO(ReviewResultDTO reviewResultDTO){

        this.successCount = reviewResultDTO.getSuccessCount();
        this.failedCount = reviewResultDTO.getFailedCount();
        if (CollectionUtils.isEmpty(reviewResultDTO.getFailedList())){
            return this;
        }
        failedList = new ArrayList<>();
        for (ReviewFailedDTO reviewFailedDTO : reviewResultDTO.getFailedList()){
            ReviewFailedVO reviewFailedVO = new ReviewFailedVO().buildReviewFailedVO(reviewFailedDTO);
            this.failedList.add(reviewFailedVO);
        }
        return this;

    }

}
