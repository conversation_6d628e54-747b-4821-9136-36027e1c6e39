/*
 * Copyright (c) 2019 Meituan.com. All Rights Reserved.
 */
package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.client.Lion;
import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Preconditions;
import com.google.common.base.Splitter;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.linz.boot.exception.ServiceRpcException;
import com.meituan.linz.boot.util.Bssert;
import com.meituan.linz.boot.util.Fun;
import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.linz.boot.util.Strings;
import com.meituan.linz.product.channel.EnhanceChannelType;
import com.meituan.shangou.saas.tenant.api.dto.tenant.BoothSettleModeDto;
import com.meituan.shangou.saas.tenant.config.TenantChannelConfigKey;
import com.meituan.shangou.saas.tenant.thrift.*;
import com.meituan.shangou.saas.tenant.thrift.common.Status;
import com.meituan.shangou.saas.tenant.thrift.common.StatusCodeEnum;
import com.meituan.shangou.saas.tenant.thrift.common.constants.TenantConfigConstants;
import com.meituan.shangou.saas.tenant.thrift.common.enums.ChainRelationEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.ConfigItemEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiRelationTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.TenantBusinessModeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.biz.TenantBizModuleDto;
import com.meituan.shangou.saas.tenant.thrift.dto.biz.request.QueryTenantBizModuleRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.biz.response.TenantBizModuleResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.booth.BoothInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.booth.response.BoothListResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.chain.request.TenantChainRelationRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.chain.response.ChainRelationModeResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.channel.request.ChannelBatchRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.channel.request.ChannelQueryRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.channel.request.QueryChannelByTenantRelRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.channel.response.ChannelBaseListResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.channel.response.ChannelDetailListResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.channel.response.ChannelInfoListResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.request.PoiInfoListQueryRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.response.PoiInfoListResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.config.ChannelConfigDto;
import com.meituan.shangou.saas.tenant.thrift.dto.config.ConfigDto;
import com.meituan.shangou.saas.tenant.thrift.dto.config.ConfigWithTimeIntervalDto;
import com.meituan.shangou.saas.tenant.thrift.dto.config.PoiManageModeDto;
import com.meituan.shangou.saas.tenant.thrift.dto.config.request.*;
import com.meituan.shangou.saas.tenant.thrift.dto.config.response.*;
import com.meituan.shangou.saas.tenant.thrift.dto.employee.EmployeeDepDto;
import com.meituan.shangou.saas.tenant.thrift.dto.employee.response.EmployDepInfoResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.employee.response.EmployDepMapResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiRelationDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.request.PoiRelationQueryRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.*;
import com.meituan.shangou.saas.tenant.thrift.dto.poigroup.PoiGroupListResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.resource.module.AppModuleDto;
import com.meituan.shangou.saas.tenant.thrift.dto.resource.module.response.AppModuleListResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.tenant.TenantBaseDto;
import com.meituan.shangou.saas.tenant.thrift.dto.tenant.TenantInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.tenant.request.TenantPageListRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.tenant.response.TenantBasePageResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.tenant.response.TenantInfoResponse;
import com.meituan.shangou.sac.dto.model.SacAccountDto;
import com.meituan.shangou.sac.dto.response.search.AccountListResponse;
import com.meituan.shangou.sac.infrastructure.lion.SacLionConfig;
import com.meituan.shangou.sac.thrift.manager.SacAccountManagerService;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.EmployeeBaseInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ProjectConstants;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.TenantConfigEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.controller.poi.request.GetPoiOperationModeRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.controller.poi.response.GetPoiOperationModeResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.controller.poi.response.PoiGroupVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.bo.ChannelInfoBo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.bo.ChannelTypeEnumBo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.tenant.BatchQueryTenantChannelConfigRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.tenant.BatchQueryTenantConfigRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.tenant.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.ProductManagementTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.TenantBizModeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.TenantPriceType;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.FallbackException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ResponseHandler;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.store.management.enums.EntityTypeEnum;
import com.sankuai.meituan.shangou.empower.auth.sdk.bean.AppIdEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.service.AuthThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.AccountInfoVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QuerySimpleAccountInfoListResponse;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QuerySimpleAccountInfoRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ChannelThriftResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.ChannelThriftService;
import com.sankuai.meituan.shangou.empower.productbiz.client.enums.TenantConfigTypeEnum;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.TenantConfigQueryRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.TenantConfigUpdateRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.QueryTenantConfigResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.TenantHsPurchaseShopInfoResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.UpdateTenantConfigResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.service.TenantConfigThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.utils.JsonUtil;
import com.sankuai.meituan.shangou.platform.empower.product.client.request.MerchantConfigQueryRequest;
import com.sankuai.meituan.shangou.platform.empower.product.client.request.TenantStoreSelfPropertiesConfigQueryRequest;
import com.sankuai.meituan.shangou.platform.empower.product.client.response.MerchantConfigQueryResponse;
import com.sankuai.meituan.shangou.platform.empower.product.client.response.TenantStoreSelfPropertiesConfigQueryResponse;
import com.sankuai.meituan.shangou.platform.empower.product.client.service.EmpowerMerchantThriftService;
import com.sankuai.meituan.shangou.platform.empower.product.client.service.ProductTenantConfigThriftService;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.enums.MerchantConfigEnum;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.shangou.saas.common.runtime.RpcInvoker;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@Rhino
public class TenantWrapper {

    /**
     * 租户系统门店维度配置id
     */
    private static final int STORE_ID_CONFIG_ID = 6;

    /**
     * 门店类型
     */
    private static final int POI_DIRECTOR_OPERATION_MODE = 1;
    private static final int POI_FRANCHISEE_OPERATION_MODE = 2;

    @Resource
    private AppModuleThriftService appModuleThriftService;

    @Resource
    private BoothThriftService boothThriftService;

    @Resource
    private ChannelThriftService.Iface channelThriftService;

    @Resource
    private ChannelManageThriftService channelManageThriftService;

    @Autowired
    private TenantThriftService tenantThriftService;

    @Autowired
    private EmployeeThriftService employeeThriftService;

    @Resource
    private ConfigThriftService configThriftService;

    @Resource
    private AuthThriftService.Iface authThriftService;

    @Autowired
    private BizModuleThriftService bizModuleThriftService;

    @Autowired
    private TenantManageThriftService tenantManageThriftService;

    @Autowired
    private SacAccountManagerService sacAccountManagerService;

    @Autowired
    private PoiRelationThriftService poiRelationThriftService;

    @Resource
    private PoiThriftService poiThriftService;

    @Resource
    private PoiGroupThriftService poiGroupThriftService;

    @Autowired
    private TenantConfigThriftService tenantConfigThriftService;

    @Autowired
    private MarketChainRelationThriftService marketChainRelationThriftService;
    @Autowired
    private EmpowerMerchantThriftService merchantThriftService;
    @Autowired
    private ProductTenantConfigThriftService productTenantConfigThriftService;
    @Autowired
    private ChannelPoiManageThriftService channelPoiManageThriftService;

    public static final String SEMI_SELF_NOT_MERCHANT_CHARGE_KEY = "SEMI_SELF_NOT_MERCHANT_CHARGE_KEY";
    public static final String PRODUCT_MANAGE_TYPE_KEY = "PRODUCT_MANAGE_TYPE_KEY";

    private final Cache<String, Object> LOCAL_CACHE = CacheBuilder.newBuilder()
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .build();

    private static final String CHAIN_MODE_KEY = "CHAIN_MODE";

    @MethodLog(logRequest = true, logResponse = true)
    @SuppressWarnings("WeakerAccess")
    public List<AppModuleDto> queryAppModules(List<Long> storeIdList, List<String> permissionCodes) {

        if (CollectionUtils.isEmpty(permissionCodes)) {
            return Lists.newArrayList();
        }
        // 应用信息
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        int appId = identityInfo.getAuthId();
        long tenantId = identityInfo.getUser().getTenantId();
        boolean supportHierarchicalApp = SacLionConfig.isSupportHierarchicalApp(String.valueOf(tenantId));
        // 如果存在父应用，则基于父应用查询模块信息
        int parentAppId = appId;
        if (AppIdEnum.isChildApp(appId) && supportHierarchicalApp) {
            parentAppId = AppIdEnum.parentAppId(appId);
        }
        AppModuleListResponse resp = null;
        boolean fullStore = identityInfo.isFullStoreMode();
        if (!fullStore) {
            // 非全部门店下，直接查询父模块即可
            resp = appModuleThriftService.queryAppModuleListByAuthCodesWithAppId(permissionCodes, parentAppId);
        } else if (supportHierarchicalApp && appId != AppIdEnum.APP_5.getAuthAppId()) {
            // 如果支持层级权限应用，则直接查询模块即可
            resp = appModuleThriftService.queryAppModuleListByAuthCodesWithAppId(permissionCodes, parentAppId);
        } else {
            // 注：选择全部门店，当 appId 为 5 时，此时说明客户端上仍是老版本的 MRN 包，或者不支持层级权限，没有透传全部门店的子应用 ID，此时仍仅查询支持多门店的模块
            resp = appModuleThriftService.queryAppModuleListBySupportMulPoiWithAppId(permissionCodes, 1, parentAppId);
        }
        nullResponseCheck(resp, "查询模块异常");
        validateModuleStatus(resp.getStatus(), "查询模块异常");
        List<AppModuleDto> modules = resp.getAppModuleList() == null ?
                Lists.newArrayList() : resp.getAppModuleList();
        return modules;
    }

    @MethodLog(logRequest = true, logResponse = true)
    public List<Long> queryBoothListByStoreId(Long tenantId, Long storeId) {
        BoothListResponse response = boothThriftService.queryBoothListByPoiId(tenantId, storeId);
        List<Long> boothIdList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(response.getBoothList())) {
            return boothIdList;
        }
        for (BoothInfoDto boothInfoDto : response.getBoothList()) {
            boothIdList.add(boothInfoDto.getBoothId());
        }
        return boothIdList;
    }

    /**
     * 查询所有渠道
     *
     * @param channelIds   渠道ID，不填查询全部
     * @param orderBizTypes 订单枚举ID，可以为空/null
     * @param statusList   状态集合，不填查询启用的渠道
     * @param standardList 是否标准API，不填查询全部
     */
    public List<ChannelInfoBo> queryAllChannelInfo(List<Integer> channelIds, List<Integer> orderBizTypes, List<Integer> statusList, List<Integer> standardList) {
        ChannelQueryRequest request = new ChannelQueryRequest();
        request.setChannelIds(channelIds);
        request.setStatusList(statusList);
        request.setStandardList(standardList);
        request.setOrderBizTypes(orderBizTypes);
        ChannelInfoListResponse response = RpcInvoker.invoke(() -> channelManageThriftService.queryAllChannelsWithCondition(request));

        ResponseHandler.checkResponseAndStatus(response, r -> r.getStatus().getCode(), r -> r.getStatus().getMessage());
        return ConverterUtils.convertList(response.getChannelList(), ChannelInfoBo::fromChannelInfoDto);
    }

    /**
     * 批量查询渠道信息
     * TODO 此方法后续可替换为租户客户端中带缓存的方法
     *
     * @param channelIds 渠道ID集合
     */
    public List<ChannelInfoBo> batchQueryChannelByChannelIds(List<Integer> channelIds) {
        if (CollectionUtils.isEmpty(channelIds)) {
            return Collections.emptyList();
        }
        ChannelBatchRequest request = new ChannelBatchRequest();
        request.setChannelIds(channelIds);
        ChannelDetailListResponse response = RpcInvoker.invoke(() -> channelManageThriftService.batchQueryChannelDetails(request));
        ResponseHandler.checkResponseAndStatus(response, r -> r.getStatus().getCode(), r -> r.getStatus().getMessage());
        return ConverterUtils.convertList(response.getChannelList(), ChannelInfoBo::fromChannelInfoDto);
    }

    public Map<Integer, String> queryChannelAbbrByChannelIds(List<Integer> channelIds) {
        if (CollectionUtils.isEmpty(channelIds)) {
            return Collections.emptyMap();
        }

        List<ChannelInfoBo> channelInfoBoList = batchQueryChannelByChannelIds(channelIds);
        if (CollectionUtils.isEmpty(channelInfoBoList)) {
            return Collections.emptyMap();
        }

        return channelInfoBoList.stream().collect(Collectors.toMap(ChannelInfoBo::getChannelId, ChannelInfoBo::getChannelAbbr));
    }

    /**
     * 查询渠道名
     */
    public String queryChannelNameByChannelId(Integer channelId) {
        List<ChannelInfoBo> channelInfoBoList = batchQueryChannelByChannelIds(Collections.singletonList(channelId));
        if (CollectionUtils.isNotEmpty(channelInfoBoList)) {
            return channelInfoBoList.stream()
                    .filter(bo -> Objects.equals(bo.getChannelId(), channelId))
                    .findAny()
                    .map(ChannelInfoBo::getChannelName)
                    .orElse(null);
        }
        return null;
    }

    public List<ChannelTypeEnumBo> queryChannelIds(Long tenantId) {

        //牵牛花租户获取渠道的一个临时方案
        final List<Integer> channelType = getChannelType(String.valueOf(tenantId));
        List<ChannelInfoBo> channelInfoBoList = null;
        if (CollectionUtils.isNotEmpty(channelType)) {
            channelInfoBoList = queryChannelByTenantRel(tenantId, channelType);
        } else {
            channelInfoBoList = queryChannelByTenantRel(tenantId, null);
        }
        return channelInfoBoList.stream().map(item -> new ChannelTypeEnumBo(item.getChannelId(), item.getChannelName())).collect(Collectors.toList());
    }

    private List<Integer> getChannelType(String tenantId) {

        try {
            //牵牛花相关租户暂时用mcc配置来获取渠道
            log.info("queryChannelIds tenantId:{}", tenantId);
            final String qnhChannelString = Lion.getConfigRepository("com.sankuai.sgfulfillment.tms").get("qnh.tenant.channel");
            log.info("qnhChannelString:{}", qnhChannelString);
            Map<String, List<Integer>> configMap = new HashMap<>();
            if (StringUtils.isNotEmpty(qnhChannelString)) {
                List<String> channelList = Splitter.on("|").splitToList(qnhChannelString);
                channelList.stream().filter(str -> StringUtils.isNotEmpty(str)).forEach(str -> {
                    List<String> strList = Splitter.on("=").splitToList(str);
                    configMap.put(strList.get(0), JsonUtil.fromJson(strList.get(1), new TypeReference<List<Integer>>() {
                    }));
                });
            }

            if (MapUtils.isNotEmpty(configMap) && configMap.containsKey(tenantId)) {
                final List<Integer> channelIdList = configMap.get(tenantId);
                if (CollectionUtils.isNotEmpty(channelIdList)) {
                    return channelIdList;
                }
            }
        } catch (Exception e) {
            log.error("get channel type from tenant error", e);
        }
        return Collections.emptyList();
    }

    public List<ChannelTypeEnumBo> queryTenantStoreChannelIds(Long tenantId, Long storeId) {
        try {
            ChannelThriftResponse response = channelThriftService.getTenantStoreChannels(tenantId, storeId);
            log.info("ChannelThriftService.getTenantStoreChannels, tenantId:{}, response:{}", tenantId, response);

            return ConverterUtils.convertList(response.getChannelList(), e -> new ChannelTypeEnumBo(e.getId(), e.getName()));
        } catch (TException e) {
            log.error("ChannelThriftService.getTenantStoreChannels, tenantId:{}", tenantId, e);
            throw new CommonRuntimeException(e);
        }
    }

    public TenantListResponse pageQueryTenant(TenantPageListRequest tenantPageListRequest) {
        try {
            TenantBasePageResponse response = tenantManageThriftService.pageQueryTenant(tenantPageListRequest);
            TenantListResponse tenantListResponse = new TenantListResponse();
            PageInfoVO pageInfoVO = new PageInfoVO(response.getPage(), response.getPageSize()
                    , response.getTotalCount());
            tenantListResponse.setPageInfo(pageInfoVO);
            List<TenantBaseDto> tenantBaseDtoList = response.getTenantBaseDtoList();
            List<TenantVo> tenantVos = Fun.map(tenantBaseDtoList, tenantBaseDto -> {
                TenantVo tenantVo = new TenantVo();
                tenantVo.setTenantId(tenantBaseDto.getTenantId().intValue());
                tenantVo.setTenantName(tenantBaseDto.getTenantName());
                return tenantVo;
            });
            tenantListResponse.setTenantList(tenantVos);
            List<Long> tenantIds = tenantVos.stream().map(TenantVo::getTenantId).map(Integer::longValue).collect(Collectors.toList());
            AccountListResponse accountListResponse = sacAccountManagerService.queryAdminAccountList(tenantIds);
            List<SacAccountDto> sacAccountDtoList = accountListResponse.getSacAccountDtoList();
            Map<Long, SacAccountDto> sacAccountDtoMap = Fun.toMap(sacAccountDtoList, SacAccountDto::getTenantId);
            for (TenantVo tenantVo : tenantVos) {
                SacAccountDto sacAccountDto = sacAccountDtoMap.get(tenantVo.getTenantId().longValue());
                if (sacAccountDto != null) {
                    tenantVo.setEpAccountId(sacAccountDto.getEpAccountId());
                }
            }
            log.info("tenantManageThriftService.pageQueryTenant, request:{}, response:{}", tenantPageListRequest,
                    response);
            return tenantListResponse;
        } catch (Exception e) {
            log.error("ChannelThriftService.getTenantChannels, request:{}", tenantPageListRequest, e);
            throw new CommonRuntimeException(e);
        }
    }

    /**
     * 查询租户名称
     *
     * @param tenantId
     * @return
     */
    public String getTenantName(Long tenantId) {
        log.info("tenantThriftService.queryTenantInfoByTenantId, request:{}", tenantId);
        TenantInfoResponse tenantInfoResponse = tenantThriftService.queryTenantInfoByTenantId(tenantId);
        log.info("tenantThriftService.queryTenantInfoByTenantId, response:{}", tenantInfoResponse);
        if (tenantInfoResponse != null
                && tenantInfoResponse.getStatus() != null
                && StatusCodeEnum.SUCCESS.getCode() == tenantInfoResponse.getStatus().getCode()
                && tenantInfoResponse.getTenantInfo() != null) {
            return tenantInfoResponse.getTenantInfo().getTenantName();
        } else {
            log.error("tenantThriftService.queryTenantInfoByTenantId error,request:{},response:{}", tenantId, tenantInfoResponse);
            return "";
        }

    }

    public Map<Long, String> getTenantIdNameMap(Set<Long> tenantIds) {
        if (CollectionUtils.isEmpty(tenantIds)) {
            return Maps.newHashMap();
        }

        com.meituan.shangou.saas.tenant.thrift.dto.tenant.response.TenantListResponse response = tenantThriftService.queryTenantListByTenantIds(new ArrayList<>(tenantIds));
        if(response == null || response.getStatus() == null ||
                StatusCodeEnum.SUCCESS.getCode() != response.getStatus().getCode()){
            return Maps.newHashMap();
        }

        return Optional.ofNullable(response.getTenantList()).map(tenants ->
                        tenants.stream().filter(tenant -> Objects.nonNull(tenant) && Objects.nonNull(tenant.getTenantId()))
                                .collect(Collectors.toMap(TenantInfoDto::getTenantId, TenantInfoDto::getTenantName, (k1, k2) -> k2)))
                .orElse(Maps.newHashMap());
    }


    public TenantInfoDto getTenantInfo(long tenantId) {
        TenantInfoResponse tenantInfoResponse = tenantThriftService.queryTenantInfoByTenantId(tenantId);

        if (tenantInfoResponse != null
                && tenantInfoResponse.getStatus() != null
                && StatusCodeEnum.SUCCESS.getCode() == tenantInfoResponse.getStatus().getCode()) {
            return tenantInfoResponse.getTenantInfo();
        } else {
            return null;
        }
    }

    /**
     * 根据员工id获取员工姓名
     *
     * @param tenantId   租户id
     * @param employeeId 员工id
     * @return
     */
    @Degrade(rhinoKey = "TenantWrapper.getEmployeeName",
            fallBackMethod = "getEmployeeNameFallback", isDegradeOnException = true,
            timeoutInMilliseconds = 1200)
    public String getEmployeeName(Long tenantId, Long employeeId) {
        log.info("employeeThriftService.queryEmployeeById, tenantId:{}, employeeId:{}", tenantId, employeeId);
        EmployDepInfoResponse response = employeeThriftService.queryEmployeeById(employeeId, tenantId);
        log.info("employeeThriftService.queryEmployeeById, response:{}", response);
        if (response != null && response.getEmployeeInfo() != null) {
            return response.getEmployeeInfo().getEmployeeName();
        } else {
            log.error("employeeThriftService.queryEmployeeById return no employeeInfo, tenantId:{}, employeeId:{}", tenantId, employeeId);
            return getEmployeeNameFallback(tenantId, employeeId);
        }
    }

    /**
     * 根据员工id获取员工信息
     *
     * @param tenantId   租户id
     * @param employeeId 员工id
     * @return
     */
    @Degrade(rhinoKey = "TenantWrapper.getEmployeeInfo",
            fallBackMethod = "getEmployeeInfoFallback", isDegradeOnException = true,
            timeoutInMilliseconds = 1200)
    public EmployeeBaseInfo getEmployeeInfo(Long tenantId, Long employeeId) {
        log.info("employeeThriftService.queryEmployeeById, tenantId:{}, employeeId:{}", tenantId, employeeId);
        EmployDepInfoResponse response = employeeThriftService.queryEmployeeById(employeeId, tenantId);
        log.info("employeeThriftService.queryEmployeeById, response:{}", response);
        if (response != null && response.getEmployeeInfo() != null) {
            return new EmployeeBaseInfo(response.getEmployeeInfo().getEmployeeName(), response.getEmployeeInfo().getEmployeePhone());
        } else {
            log.error("employeeThriftService.queryEmployeeById return no employeeInfo, tenantId:{}, employeeId:{}", tenantId, employeeId);
            return getEmployeeInfoFallback(tenantId, employeeId);
        }
    }

    /**
     * 根据员工id获取员工姓名降级方法
     *
     * @param tenantId
     * @param employeeId
     * @return
     */
    public String getEmployeeNameFallback(Long tenantId, Long employeeId) {
        log.error("执行getEmployeeName降级方法");
        return "";
    }

    /**
     * 根据员工id获取员工信息降级方法
     *
     * @param tenantId
     * @param employeeId
     * @return
     */
    public EmployeeBaseInfo getEmployeeInfoFallback(Long tenantId, Long employeeId) {
        log.error("执行getEmployeeName降级方法");
        return new EmployeeBaseInfo("", "");
    }

    /**
     * 根据员工id列表获取员工姓名
     *
     * @param tenantId    租户id
     * @param employeeIds 员工id列表
     * @return
     */
    @Degrade(rhinoKey = "TenantWrapper.getEmployeeNames",
            fallBackMethod = "getEmployeeNamesFallback", isDegradeOnException = true,
            timeoutInMilliseconds = 1200)
    public List<String> getEmployeeNames(Long tenantId, List<Long> employeeIds) {
        log.info("employeeThriftService.queryEmployeeByIds, tenantId:{}, employeeIds:{}", tenantId, employeeIds);
        EmployDepMapResponse response = employeeThriftService.queryEmployeeByIds(employeeIds, tenantId);
        log.info("employeeThriftService.queryEmployeeByIds, response:{}", response);
        if (response != null && response.getEmployeeInfoMap() != null
                && CollectionUtils.isNotEmpty(response.getEmployeeInfoMap().values())) {
            Collection<EmployeeDepDto> values = response.getEmployeeInfoMap().values();
            List<String> employeeNames = values.stream().map(dto -> dto.getEmployeeName()).collect(Collectors.toList());
            return employeeNames;
        } else {
            log.error("employeeThriftService.queryEmployeeByIds return no employeeInfo, tenantId:{}, employeeIds:{}", tenantId, employeeIds);
            return getEmployeeNamesFallback(tenantId, employeeIds);
        }
    }

    /**
     * 根据员工id列表获取员工姓名降级方法
     *
     * @param tenantId
     * @param employeeIds
     * @return
     */
    public List<String> getEmployeeNamesFallback(Long tenantId, List<Long> employeeIds) {
        log.error("执行getEmployeeNames降级方法");
        return Lists.newArrayList();
    }

    /**
     * 根据员工 ID 列表批量查询员工部门
     *
     * @param tenantId    租户 ID
     * @param employeeIds 员工 ID 列表
     * @return
     */
    @Degrade(rhinoKey = "TenantWrapper.getEmployeeByIds",
            fallBackMethod = "getEmployeeByIdsFallback", isDegradeOnException = true,
            timeoutInMilliseconds = 1200)
    public Map<Long, EmployeeDepDto> getEmployeeByIds(Long tenantId, List<Long> employeeIds) {
        log.info("employeeThriftService.queryEmployeeByIds, tenantId:{}, employeeIds:{}", tenantId, employeeIds);
        EmployDepMapResponse response = employeeThriftService.queryEmployeeByIds(employeeIds, tenantId);
        log.info("employeeThriftService.queryEmployeeByIds, response:{}", response);
        if (response != null && response.getEmployeeInfoMap() != null
                && MapUtils.isNotEmpty(response.getEmployeeInfoMap())) {
            return response.getEmployeeInfoMap();
        } else {
            log.error("employeeThriftService.queryEmployeeByIds return no employeeInfo, tenantId:{}, employeeIds:{}", tenantId, employeeIds);
            return getEmployeeByIdsFallback(tenantId, employeeIds);
        }
    }

    /**
     * 根据员工 ID 列表批量查询员工部门降级方法
     *
     * @param tenantId
     * @param employeeIds
     * @return
     */
    public Map<Long, EmployeeDepDto> getEmployeeByIdsFallback(Long tenantId, List<Long> employeeIds) {
        log.warn("执行getEmployeeByIds降级方法");
        return Collections.emptyMap();
    }

    @SuppressWarnings("SameParameterValue")
    private void nullResponseCheck(Object resp, String errorMsg) {
        if (resp == null) {
            throw new CommonLogicException(errorMsg + ", response is null");
        }
    }

    @SuppressWarnings("SameParameterValue")
    private void validateModuleStatus(Status status, String errorMsg) {
        if (status == null) {
            throw new CommonLogicException(errorMsg + ", status is null");
        }

        if (status.getCode() != ResultCode.SUCCESS.getCode()) {
            throw new CommonLogicException(MessageFormat.format("{0}, code = {1}, detail = {2}",
                    errorMsg, status.getCode(), status.getMessage()));
        }
    }

    public boolean isSpuGray(Long tenantId) {

        Map<String, String> configMap = this.queryTenantSwitch(tenantId, Lists.newArrayList(ProjectConstants.SPU_GRAY_KEY));

        if (MapUtils.isEmpty(configMap)) {
            return false;
        }

        return configMap.containsKey(ProjectConstants.SPU_GRAY_KEY)
                && configMap.get(ProjectConstants.SPU_GRAY_KEY).equals(ProjectConstants.SPU_GRAY_SWITCH_ON);
    }

    public Map<String, String> queryTenantSwitch(Long tenantId, List<String> switchKeyList) {

        try {
            if (CollectionUtils.isEmpty(switchKeyList)) {
                return Collections.emptyMap();
            }

            TenantSwitchGetRequest request = new TenantSwitchGetRequest();
            request.setTenantId(tenantId);
            request.setSwitchKey(switchKeyList);

            TenantSwitchGetResponse response = configThriftService.getTenantSwitch(request);
            log.info("查询租户开关配置, request:{}, response:{}", request, response);

            return response.getSwitchValue();
        } catch (Exception e) {
            log.error("查询租户开关配置错误, tenantId:{}, switchKeyList:{}", tenantId, switchKeyList, e);
            throw new CommonRuntimeException(e);
        }
    }

    private static final Integer PRICE_CONFIG_ID = 25;


    //1-手动定价 2-通用提价
    public TenantPriceType queryTenantPriceConfig(Long tenantId) {
        Preconditions.checkNotNull(tenantId);
        try {
            ConfigDto configDto = queryTenantConfig(tenantId, tenantId, PRICE_CONFIG_ID);
            return TenantPriceType.findByCode(JSON.parseObject(configDto.getConfigContent(), TenantPriceConfig.class).getCostStyle());
        } catch (Exception e) {
            throw new CommonRuntimeException("查询租户价格配置失败");
        }
    }

    private static final String PRODUCT_MANAGEMENT_TYPE = "product_management_type";
    private static final Integer MANAGEMENT_MODE_CONFIG_ID = 29;


    public ProductManagementTypeEnum getProductManagementType(Long tenantId) {
        try {
            ConfigDto configDto = queryTenantConfig(tenantId, tenantId, MANAGEMENT_MODE_CONFIG_ID);
            JSONObject config = JSON.parseObject(configDto.getConfigContent());
            String configValue = config.getString(PRODUCT_MANAGEMENT_TYPE);
            return ProductManagementTypeEnum.ofConfigValue(configValue);
        } catch (Exception e) {
            throw new CommonRuntimeException("查询商品管理模式失败");

        }
    }

    private static final String TENANT_BIZ_MODE = "biz_mode";
    private final static String CONVENIENCE_STORE_BIZ_MODE = "convenience_store";
    private static final Integer BIZ_MODE_CONFIG_ID = 32;

    public Boolean isConvenienceStoreMode(long tenantId) {

        try {
            ConfigDto configDto = queryTenantConfig(tenantId, tenantId, BIZ_MODE_CONFIG_ID);
            JSONObject config = JSON.parseObject(configDto.getConfigContent());
            return CONVENIENCE_STORE_BIZ_MODE.equals(config.getString(TENANT_BIZ_MODE));
        } catch (Exception e) {
            throw new CommonRuntimeException("查询商品业务模式失败");
        }
    }

    public Boolean isCdqStoreMode(long tenantId) {

        try {
            ConfigDto configDto = queryTenantConfig(tenantId, tenantId, BIZ_MODE_CONFIG_ID);
            JSONObject config = JSON.parseObject(configDto.getConfigContent());
            return TenantBizModeEnum.CAI_DA_QUAN.getCode().equals(config.getString(TENANT_BIZ_MODE));
        } catch (Exception e) {
            throw new CommonRuntimeException("查询商品业务模式失败");
        }
    }


    public ConfigDto queryTenantConfig(Long tenantId, Long subjectId, Integer configId) {
        Preconditions.checkArgument(tenantId != null, "tenantId is empty");
        Preconditions.checkArgument(subjectId != null, "subjectId is empty");
        Preconditions.checkArgument(configId != null, "tenantConfigEnum is empty");

        ConfigQueryRequest request = new ConfigQueryRequest();
        request.setConfigId(configId);
        request.setSubjectId(subjectId);
        request.setTenantId(tenantId);

        TenantConfigResponse response = configThriftService.queryTenantConfig(request);

        log.info("TenantRpcServiceImpl.queryTenantConfig(), 租户配置信息, tenantId:{}, subjectId:{}, tenantConfigEnum:{}",
                tenantId, subjectId, configId);

        if (response == null) {
            log.error("response is empty. tenantId:{}, config:{}, subjectId:{}", tenantId, configId, subjectId);
            return null;
        }
        log.info("response:{}", response);

        return response.getConfig();
    }

    public GetPoiOperationModeResponse getPoiOperationMode(GetPoiOperationModeRequest request, long tenantId) {
        PoiMapResponse mapResponse = poiThriftService.queryTenantPoiInfoMapByPoiIds(Lists.newArrayList(request.getPoiId()), tenantId);
        if (Objects.isNull(mapResponse) || !mapResponse.getStatus().isSuccess()) {
            throw new BizException("获取门店信息失败");
        }

        PoiInfoDto poiInfoDto = mapResponse.getPoiInfoMap().get(request.getPoiId());
        if (Objects.isNull(poiInfoDto)) {
            throw new BizException("门店不存在");
        }

        // 没值的都是直营
        if (Objects.isNull(poiInfoDto.getPoiExtendContentDto())
                || Objects.isNull(poiInfoDto.getPoiExtendContentDto().getOperationMode())) {
            return new GetPoiOperationModeResponse(POI_DIRECTOR_OPERATION_MODE);
        }

        return new GetPoiOperationModeResponse(poiInfoDto.getPoiExtendContentDto().getOperationMode());
    }

    public Set<Integer> getPoiChannels(Long tenantId, Long storeId) {
        PoiInfoListQueryRequest request = new PoiInfoListQueryRequest();
        request.setTenantId(tenantId);
        request.setPoiNameOrId(storeId.toString());
        PoiInfoListResponse response = channelPoiManageThriftService.queryPoiInfoListUnderCond(request);
        if (response == null || response.getStatus() == null || !response.getStatus().isSuccess()) {
            log.error("queryStoreChannelPoiList occurred error! request:{} response:{}", request, response);
            return Collections.emptySet();
        }
        if (CollectionUtils.isEmpty(response.getPoiInfoList()) || CollectionUtils.isEmpty(response.getPoiInfoList().get(0).getChannelPoiInfoList())) {
            return Collections.emptySet();
        }

        return Fun.map(response.getPoiInfoList().get(0).getChannelPoiInfoList(), t -> t.getChannelId(), Collectors.toSet());
    }

    @Data
    static class TenantPriceConfig {
        private Integer channel;
        private Integer costStyle;
    }

    /**
     * 查询租户门店管理模式
     *
     * @param tenantId
     * @return
     */
    @Degrade(rhinoKey = "TenantWrapper.queryPoiManageMode",
            fallBackMethod = "queryPoiManageModeFallback", isDegradeOnException = true,
            timeoutInMilliseconds = 1200)
    public PoiManageModeDto queryPoiManageMode(long tenantId) {
        try {
            PoiManageModeConfigResponse response = configThriftService.queryPoiManageMode(tenantId);
            nullResponseCheck(response, "查询租户门店管理模式异常");
            validateModuleStatus(response.getStatus(), "查询租户门店管理模式异常");
            return response.getManageModeDto();
        } catch (Exception e) {
            log.error("查询租户门店管理模式配置错误, tenantId:{}", tenantId, e);
            return null;
        }
    }

    /**
     * 查询租户门店管理模式降级方法
     *
     * @param tenantId
     * @return
     */
    public PoiManageModeDto queryPoiManageModeFallback(long tenantId) {
        log.error("执行queryPoiManageMode降级方法");
        return null;
    }

    public static class EmployeeBaseInfo {
        private String employeeName;

        private String employeePhone;

        public EmployeeBaseInfo(String employeeName, String employeePhone) {
            this.employeeName = employeeName;
            this.employeePhone = employeePhone;
        }

        public String getEmployeeName() {
            return employeeName;
        }

        public void setEmployeeName(String employeeName) {
            this.employeeName = employeeName;
        }

        public String getEmployeePhone() {
            return employeePhone;
        }

        public void setEmployeePhone(String employeePhone) {
            this.employeePhone = employeePhone;
        }
    }

    @Degrade(rhinoKey = "TenantWrapper.queryEmployeeInfo",
            fallBackMethod = "queryEmployeeInfoFallback",
            isDegradeOnException = true,
            timeoutInMilliseconds = 2000)
    public Optional<EmployeeBaseInfo> queryEmployeeInfo(Long accountId) throws TException {
        QuerySimpleAccountInfoListResponse querySimpleAccountInfoListResponse = authThriftService
                .querySimpleAccountInfoList(new QuerySimpleAccountInfoRequest(Lists.newArrayList(accountId)));
        log.info("authThriftService.querySimpleAccountInfoList REQ: {} RESP: {}", accountId, querySimpleAccountInfoListResponse);
        if (querySimpleAccountInfoListResponse == null || querySimpleAccountInfoListResponse.getResult() == null
                || !Objects.equals(querySimpleAccountInfoListResponse.getResult().getCode(), 0)) {
            throw new CommonRuntimeException("查询账号信息异常");
        }
        List<AccountInfoVo> accountInfoList = querySimpleAccountInfoListResponse.getAccountInfoList();
        if (CollectionUtils.isNotEmpty(accountInfoList)) {
            AccountInfoVo accountInfoVo = accountInfoList.get(0);
            EmployDepInfoResponse employDepInfoResponse = employeeThriftService.queryEmployeeById(accountInfoVo.getStaffId(), accountInfoVo.getTenantId());
            log.info("employeeThriftService.queryEmployeeById REQ: {} {} RESP: {}", accountInfoVo.getStaffId(), accountInfoVo.getTenantId(), employDepInfoResponse);
            if (employDepInfoResponse == null || employDepInfoResponse.getStatus() == null || !Objects.equals(employDepInfoResponse.getStatus().getCode(), StatusCodeEnum.SUCCESS.getCode())) {
                throw new CommonRuntimeException("查询员工信息异常");
            }
            EmployeeDepDto employeeInfo = employDepInfoResponse.getEmployeeInfo();
            if (employeeInfo != null) {
                return Optional.of(new EmployeeBaseInfo(employeeInfo.getEmployeeName(), employeeInfo.getEmployeePhone()));
            }
        }
        return Optional.empty();
    }

    public Optional<EmployeeBaseInfo> queryEmployeeInfoFallback(Long accountId, Throwable t) throws TException {
        log.warn("queryEmployeeInfoFallback accountId:{} t: {}", accountId, t);
        throw new FallbackException("queryEmployeeInfoFallback", t);
    }

    public BoothSettleModeDto getBoothSettlementConfig(Long tenantId, Long storeId, Long timeStamp) {
        QueryConifgByTimeIntervalRequest req = new QueryConifgByTimeIntervalRequest();
        req.setTenantId(tenantId);
        req.setConfigId(STORE_ID_CONFIG_ID);
        req.setSubjectId(storeId);
        req.setStartTime(timeStamp);
        req.setEndTime(timeStamp);
        TenantConfigWithTimeIntervalResponse resp;
        try {
            resp = configThriftService.queryTenantConfigByTimeInterval(req);
            log.info("获取门店结算模式，req:{}, resp:{}", req, JacksonUtils.toJson(resp));
        } catch (Exception e) {
            log.error("获取门店结算模式异常 req:{}", req, e);
            throw new ServiceRpcException("获取门店结算模式异常", e);
        }

        if (resp == null) {
            log.error("获取门店结算模式失败 req:{},resp:{}", req, resp);
            throw new ServiceRpcException("获取门店结算模式失败");
        }

        if (CollectionUtils.isEmpty(resp.getConfigList())) {
            log.warn("获取门店未配置结算模式，默认非摊位结算模式 req:{},resp:{}", req, resp);
            return null;
        }

        ConfigWithTimeIntervalDto dto = resp.getConfigList().get(0);
        return JacksonUtils.parse(dto.configContent, BoothSettleModeDto.class);
    }


    public List<TenantBizModuleDto> getTenantModules(long tenantId) {
        QueryTenantBizModuleRequest request = new QueryTenantBizModuleRequest();
        request.setTenantId(tenantId);
        request.setContainsModuleConfig(true);
        try {
            TenantBizModuleResponse tenantBizModuleResponse = bizModuleThriftService.getTenantBizModuleList(request);
            if (tenantBizModuleResponse.getStatus() == null || tenantBizModuleResponse.getStatus().getCode() != 0) {
                log.error("getTenantModules error, status:{}", tenantBizModuleResponse.getStatus());
                throw new CommonRuntimeException("获取租户模块化配置异常");
            }
            return tenantBizModuleResponse.getTenantBizModuleDtoList();
        } catch (CommonRuntimeException e) {
            throw e;
        } catch (Exception e) {
            log.error("getTenantModules error, tenantId:{}", tenantId, e);
            throw new ServiceRpcException("获取租户模块化配置异常", e);
        }
    }

    /**
     * 查询租户业务模式、异常降级为未知模式
     *
     * @param tenantId 租户id
     * @return 业务模式
     */
    public TenantBizModeEnum getTenantBizMode(Long tenantId) {
        try {
            if (tenantId == null) {
                return null;
            }
            ConfigQueryRequest configQueryRequest = new ConfigQueryRequest();
            configQueryRequest.setConfigId(ConfigItemEnum.TENANT_BIZ_MODE.getKey());
            configQueryRequest.setTenantId(tenantId);
            configQueryRequest.setSubjectId(tenantId);
            TenantConfigResponse configResponse = configThriftService.queryTenantConfig(configQueryRequest);
            Status status = Optional.ofNullable(configResponse).map(TenantConfigResponse::getStatus).orElse(null);
            if (status == null || status.getCode() != ResultCodeEnum.SUCCESS.getValue()) {
                log.error("search tenant config info error,tenantId:{},msg:{}", tenantId,
                        Optional.ofNullable(status).map(Status::getMessage).orElse(null));
                return TenantBizModeEnum.UNKNOWN;
            }
            String config = Optional.ofNullable(configResponse.getConfig())
                    .map(ConfigDto::getConfigContent)
                    .orElse(null);
            if (StringUtils.isEmpty(config)) {
                return TenantBizModeEnum.UNKNOWN;
            }
            TenantBizModeBo tenantBizModeBo =
                    JacksonUtils.parse(configResponse.getConfig().getConfigContent(),
                            new TypeReference<TenantBizModeBo>() {
                            });
            return tenantBizModeBo != null ? TenantBizModeEnum.codeOf(tenantBizModeBo.getBizMode()) : TenantBizModeEnum.UNKNOWN;
        } catch (Exception e) {
            log.error("search tenant config info exception,tenantId:{}, stack:", tenantId, e);
            return TenantBizModeEnum.UNKNOWN;
        }
    }

    /**
     * 新版本，返回值替换为新枚举，但是逻辑保持一致
     * 查询租户业务模式、异常降级为未知模式
     *
     * @param tenantId 租户id
     * @return 业务模式
     */
    public TenantBusinessModeEnum getTenantBizModeV2(Long tenantId) {
        try {
            if (tenantId == null) {
                return null;
            }
            ConfigQueryRequest configQueryRequest = new ConfigQueryRequest();
            configQueryRequest.setConfigId(ConfigItemEnum.TENANT_BIZ_MODE.getKey());
            configQueryRequest.setTenantId(tenantId);
            configQueryRequest.setSubjectId(tenantId);
            TenantConfigResponse configResponse = configThriftService.queryTenantConfig(configQueryRequest);
            Status status = Optional.ofNullable(configResponse).map(TenantConfigResponse::getStatus).orElse(null);
            if (status == null || status.getCode() != ResultCodeEnum.SUCCESS.getValue()) {
                log.error("search tenant config info error,tenantId:{},msg:{}", tenantId,
                        Optional.ofNullable(status).map(Status::getMessage).orElse(null));
                return TenantBusinessModeEnum.UNKNOWN;
            }
            String config = Optional.ofNullable(configResponse.getConfig())
                    .map(ConfigDto::getConfigContent)
                    .orElse(null);
            if (StringUtils.isEmpty(config)) {
                return TenantBusinessModeEnum.UNKNOWN;
            }
            TenantBizModeBo tenantBizModeBo =
                    JacksonUtils.parse(configResponse.getConfig().getConfigContent(),
                            new TypeReference<TenantBizModeBo>() {
                    });

            if(tenantBizModeBo == null) {
                return TenantBusinessModeEnum.UNKNOWN;
            }

            return StringUtils.isNotEmpty(tenantBizModeBo.getBizMode()) ? TenantBusinessModeEnum.codeOf(tenantBizModeBo.getBizMode()) : TenantBusinessModeEnum.UNKNOWN;
        } catch (Exception e) {
            log.error("new search tenant config info exception,tenantId:{}, stack:", tenantId, e);
            return TenantBusinessModeEnum.UNKNOWN;
        }
    }

    public ChainRelationEnum getTenantChainRelationMode(Long tenantId) {
        TenantChainRelationRequest request = new TenantChainRelationRequest();
        request.setTenantId(tenantId);
        ChainRelationModeResponse response = RpcInvoker.invoke(() -> marketChainRelationThriftService.queryTenantChainMode(request));
        if (!response.getStatus().isSuccess() || Objects.isNull(response.getChainRelationMap())
                || Objects.isNull(response.getChainRelationMap().get(tenantId))) {
            log.error("查询租户加盟模式失败，request：{}，response：{}", request, response);
            throw new BizException("查询租户加盟模式失败");
        }
        return response.getChainRelationMap().get(tenantId);
    }

    public ChainRelationEnum getTenantChainRelationModeWithCache(Long tenantId) {
        String key = String.format("%s_%d", CHAIN_MODE_KEY, tenantId);
        try {
            Object result = LOCAL_CACHE.get(key, () -> getTenantChainRelationMode(tenantId));
            return (ChainRelationEnum)result;
        } catch (Exception e) {
            log.error("获取租户加盟模式异常tenantId:{}", tenantId, e);
            throw new IllegalStateException("获取租户加盟模式异常", e);
        }
    }


    public List<HsPurchasePlatformInfoVO> getHsPurchaseShopInfo(Long tenantId) {
        TenantHsPurchaseShopInfoResponse response = tenantConfigThriftService.getHsPurchaseShopInfo(tenantId);
        if (response == null || !response.getStatus().isSuccess() || response.getHsPurchasePlatformInfoDTOS() == null) {
            log.error("查询海商店铺信息失败，tenantId：{}，response：{}", tenantId, response);
            throw new BizException("查询海商店铺信息失败");
        }

        return ConverterUtils.convertList(response.getHsPurchasePlatformInfoDTOS(), HsPurchasePlatformInfoVO::of);
    }


    public List<PoiInfoDto> queryPoiByWarehouseId(Long tenantId, Long warehouseId) {
        List<PoiRelationDto> poiRelationDtos = queryPoiByWarehouseId(tenantId, Lists.newArrayList(warehouseId));
        return poiRelationDtos.stream().findFirst().map(PoiRelationDto::getTargetPoiInfoList).orElse(Lists.newArrayList());
    }

    public List<PoiRelationDto> queryPoiByWarehouseId(Long tenantId, List<Long> poiIds) {
        PoiRelationQueryRequest relationQueryRequest = new PoiRelationQueryRequest();
        relationQueryRequest.setTenantId(tenantId);
        relationQueryRequest.setPoiIdList(poiIds);
        relationQueryRequest.setRelationType(PoiRelationTypeEnum.STORE_SHAREABLE_WAREHOUSE_RELATION.code());
        relationQueryRequest.setReverseRelation(Boolean.TRUE);
        PoiRelationResponse relationResponse = poiRelationThriftService.batchQueryRelationsByPoiIds(relationQueryRequest);
        Status status = Optional.ofNullable(relationResponse).map(PoiRelationResponse::getStatus).orElse(null);
        if (status == null || status.getCode() != ResultCodeEnum.SUCCESS.getValue()) {
            log.error("queryPoiByWarehouseId error,tenantId:{}, poiIds: {}, msg:{}",
                    tenantId,
                    poiIds,
                    Optional.ofNullable(status).map(Status::getMessage).orElse(null));
            return Lists.newArrayList();
        }
        return relationResponse.getPoiRelationDtoList();
    }

    /**
     * 获取门店的类别
     *
     * @param tenantId 租户id
     * @param poiIds   门店id列表
     * @return 门店id和类别map
     */
    public Map<Long, Integer> queryEntityTypeMapByPoiIds(Long tenantId, List<Long> poiIds) {
        PoiEntityTypeMapResponse resp = poiThriftService.queryEntityTypeMapByPoiIds(poiIds, tenantId);
        if (Objects.isNull(resp.getStatus()) || resp.getStatus().getCode() != 0) {
            Bssert.throwIfTrue((Objects.isNull(resp.getStatus()) || resp.getStatus().getCode() != 0),
                    "获取门店类别信息失败, tenantId: {}, poiIds:{}, error message:{}",
                    tenantId, poiIds, Optional.ofNullable(resp.getStatus()).map(Status::getMessage).orElse(null));
        }
        return resp.getPoi2EntityTypeMap();
    }

    /**
     * 查询门店
     *
     * @param tenantId
     * @param poiIds
     * @return
     */
    public Map<Long, PoiInfoDto> queryPoiByIds(Long tenantId, List<Long> poiIds) {
        if (CollectionUtils.isEmpty(poiIds)) {
            return new HashMap<>();
        }

        PoiMapResponse resp = poiThriftService.queryTenantPoiInfoMapByPoiIds(poiIds, tenantId);
        if (Objects.isNull(resp.getStatus()) || resp.getStatus().getCode() != 0) {
            Bssert.throwIfTrue((Objects.isNull(resp.getStatus()) || resp.getStatus().getCode() != 0),
                    "获取门店类别信息失败, tenantId: {}, poiIds:{}, error message:{}",
                    tenantId, poiIds, Optional.ofNullable(resp.getStatus()).map(Status::getMessage).orElse(null));
        }
        return resp.getPoiInfoMap();
    }

    public boolean isMerchantChargeSpu(Long tenantId) {
        try {
            if (tenantId == null) {
                return false;
            }
            ConfigQueryRequest configQueryRequest = new ConfigQueryRequest();
            configQueryRequest.setConfigId(TenantConfigEnum.MERCHANT_CHARGE.getConfigId());
            configQueryRequest.setTenantId(tenantId);
            configQueryRequest.setSubjectId(tenantId);
            TenantConfigResponse configResponse = configThriftService.queryTenantConfig(configQueryRequest);
            log.info("search merchant charge config success, tenantId:{}, response:{}", tenantId, configResponse);
            Status status = Optional.ofNullable(configResponse).map(TenantConfigResponse::getStatus).orElse(null);
            if (status == null || status.getCode() != ResultCodeEnum.SUCCESS.getValue()) {
                log.error("search merchant charge config error,tenantId:{}, msg:{}", tenantId,
                        Optional.ofNullable(status).map(Status::getMessage).orElse(null));
                throw new RuntimeException("查询租户商品模式异常");
            }
            String config = Optional.ofNullable(configResponse.getConfig())
                    .map(ConfigDto::getConfigContent)
                    .orElse(null);
            if (StringUtils.isEmpty(config)) {
                return false;
            }
            MerchantChargeConfigVo merchantChargeConfigVo =
                    JacksonUtils.parse(configResponse.getConfig().getConfigContent(),
                            new TypeReference<MerchantChargeConfigVo>() {
                            });
            return merchantChargeConfigVo != null && "YES".equals(merchantChargeConfigVo.getZongbuguanpin());
        } catch (Exception e) {
            log.error("search merchant charge config exception,tenantId:{}, stack:", tenantId, e);
            throw new RuntimeException("查询租户商品模式异常");
        }
    }

    public boolean isEnableMultiLocation(Long tenantId, Long storeId) {
        ConfigQueryRequest configQueryRequest = new ConfigQueryRequest();
        configQueryRequest.setTenantId(tenantId);
        configQueryRequest.setConfigId(ConfigItemEnum.ONE_PRODUCT_MULTI_LOCATION.getKey());
        configQueryRequest.setSubjectId(storeId);
        TenantConfigResponse tenantConfigResponse = configThriftService.queryTenantConfig(configQueryRequest);
        ResponseHandler.checkResponseAndStatus(tenantConfigResponse, it -> it.getStatus().getCode(), it -> it.getStatus().getMessage());

        return Optional.ofNullable(tenantConfigResponse.getConfig())
                .map(ConfigDto::getConfigContent)
                .map(com.meituan.reco.pickselect.common.utils.JacksonUtils::fromJsonToMap)
                .map(it -> it.get(TenantConfigConstants.K_ONE_PRODUCT_MULTI_LOCATION))
                .map(it -> (String) it)
                .filter(it -> StringUtils.equals(it, TenantConfigConstants.V_YES_STR))
                .isPresent();
    }

    public List<PoiVO> getTenantPoiList(Long tenantId) {
        PoiListQueryResponse response = poiThriftService.queryPoiListByTenantIds(tenantId);
        if (response.status.code != 0) {
            throw new BizException(response.status.getMessage());
        }
        if (CollectionUtils.isEmpty(response.getPoiList())) {
            return Collections.emptyList();
        }

        return response.getPoiList().stream().map(PoiVO::ofBase).collect(Collectors.toList());
    }

    public List<PoiGroupVo> getTenantAllPoiGroups(Long tenantId) {
        PoiGroupListResponse response = poiGroupThriftService.queryTenantGroupListByTenantId(tenantId);
        if (response.status.code != 0) {
            throw new BizException(response.status.getMessage());
        }

        Map<Integer, List<Long>> groupPoiMap = getAllGroupPoiMap(tenantId);

        return Fun.map(response.getGroupList(), poiGroupDto -> {
            PoiGroupVo poiGroupVo = new PoiGroupVo();
            poiGroupVo.setPoiGroupId(poiGroupDto.getPoiGroupId());
            poiGroupVo.setPoiGroupViewId(poiGroupDto.getPoiGroupViewId());
            poiGroupVo.setPoiGroupName(poiGroupDto.getPoiGroupName());
            poiGroupVo.setPoiGroupSize(poiGroupDto.getPoiGroupSize());
            poiGroupVo.setPoiIds(Optional.ofNullable(groupPoiMap.get(poiGroupDto.getPoiGroupId())).orElse(Collections.emptyList()));
            return poiGroupVo;
        });
    }

    private Map<Integer, List<Long>> getAllGroupPoiMap(Long tenantId) {
        Map<Integer, List<Long>> groupPoiMap = Maps.newHashMap();
        List<PoiVO> poiVOS = getTenantPoiList(tenantId);
        if (CollectionUtils.isEmpty(poiVOS)) {
            return groupPoiMap;
        }

        Map<Long, Integer> poiGroupMap = batchQueryBindStoreGroupIdsByPoiIds(tenantId,
                poiVOS.stream().map(PoiVO::getPoiId).collect(Collectors.toList()));
        if (MapUtils.isEmpty(poiGroupMap)) {
            return groupPoiMap;
        }

        poiGroupMap.forEach((poiId, groupId) -> groupPoiMap.computeIfAbsent(groupId, poiIds -> Lists.newArrayList()).add(poiId));
        return groupPoiMap;
    }


    public Map<Long, Integer> batchQueryBindStoreGroupIdsByPoiIds(Long tenantId, List<Long> poiIds) {
        Map<Long, Integer> poiGroupMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(poiIds)) {
            return poiGroupMap;
        }

        try {
            PoiRelationQueryRequest request = new PoiRelationQueryRequest();
            request.setTenantId(tenantId);
            request.setPoiIdList(poiIds);
            request.setRelationType(PoiRelationTypeEnum.STORE_POIGROUP_RELATION.code());
            PoiRelationSimpleMapResponse response = poiRelationThriftService.batchQueryRelationMapByPoiIds(request);
            if (response.getStatus().getCode() != StatusCodeEnum.SUCCESS.getCode()) {
                log.error("Query bind store group id list failed, request:{}, response:{}.", request, response);
                throw new BizException(String.format("%s: %s", "查询门店绑定分组失败", response.getStatus().getMessage()));
            }
            if (MapUtils.isEmpty(response.getPoiRelationMap())) {
                return poiGroupMap;
            }
            response.getPoiRelationMap().forEach((poiId, relationIds) -> {
                if (CollectionUtils.isEmpty(relationIds)) {
                    return;
                }
                poiGroupMap.put(poiId, relationIds.stream().findFirst().map(Long::intValue).orElse(null));
            });
            return poiGroupMap;
        } catch (Exception e) {
            log.error("Query bind store group id list exception, tenantId:{}, poiIds:{}.", tenantId, poiIds, e);
            throw new BizException("查询门店绑定分组异常", e);
        }
    }

    /**
     * 批量查询租户配置
     *
     * @param request 查询请求
     * @return 配置列表
     */
    public List<TenantConfigVo> batchQueryTenantConfig(BatchQueryTenantConfigRequest request) {
        if (request.emptyRequest()) {
            return Collections.emptyList();
        }
        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        MultiTenantBatchQueryConfigRequest batchQuery = request.toMultiRequest(tenantId);
        List<ConfigDto> configList = batchQueryMultiTenantConfigList(batchQuery);
        return configList.stream().map(TenantConfigVo::of).collect(Collectors.toList());
    }


    /**
     * 批量查询租户渠道配置
     *
     * @param request 查询请求
     * @return 渠道配置列表
     */
    public List<TenantChannelConfigVo> batchQueryTenantChannelConfig(BatchQueryTenantChannelConfigRequest request) {
        if (request.emptyRequest()) {
            return Collections.emptyList();
        }
        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        MultiTenantBatchQueryChannelConfigRequest batchQuery = request.toMultiRequest(tenantId);
        List<ChannelConfigDto> channelConfigList = batchQueryMultiTenantChannelConfigList(batchQuery);
        return channelConfigList.stream().map(TenantChannelConfigVo::of).collect(Collectors.toList());
    }

    /**
     * 租户维度配置整合查询
     *
     * @return 租户维度整合配置
     */
    public AggTenantLevelConfigVo aggTenantLevelConfig() {
        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        AggTenantLevelConfigRequest request = new AggTenantLevelConfigRequest();
        request.setTenantId(tenantId);
        AggTenantLevelConfigResponse response = configThriftService.aggTenantLevelConfig(request);
        handleTenantResponse(response, AggTenantLevelConfigResponse::getStatus, "租户维度配置整合查询失败：");
        return AggTenantLevelConfigVo.fromDto(response.getAggTenantLevelConfigDto());
    }

    /**
     * 批量查询多组租户配置
     *
     * @param request 请求
     * @return 配置列表
     */
    private List<ConfigDto> batchQueryMultiTenantConfigList(MultiTenantBatchQueryConfigRequest request) {
        TenantConfigListResponse response = configThriftService.batchQueryMultiTenantConfigList(request);
        handleTenantResponse(response, TenantConfigListResponse::getStatus, "查询租户配置失败：");
        return response.getConfigList();
    }

    /**
     * 批量查询多组租户渠道配置
     *
     * @param request 请求
     * @return 渠道配置列表
     */
    private List<ChannelConfigDto> batchQueryMultiTenantChannelConfigList(MultiTenantBatchQueryChannelConfigRequest request) {
        TenantChannelConfigListResponse response = configThriftService.batchQueryMultiTenantChannelConfigList(request);
        handleTenantResponse(response, TenantChannelConfigListResponse::getStatus, "查询租户渠道配置失败：");
        return response.getChannelConfigList();
    }

    private static <T> void handleTenantResponse(T response, Function<T, Status> getStatusFn, String messagePrefix) {
        Status status = Optional.ofNullable(response).map(getStatusFn).orElse(null);
        if (status == null || status.getCode() != StatusCodeEnum.SUCCESS.getCode()) {
            String message = Optional.ofNullable(status).map(Status::getMessage).orElse("");
            throw new BizException(messagePrefix + message);
        }
    }


    /**
     * 从productBiz查询租户相关的配置
     *
     * @param tenantId
     * @return
     */
    public Integer queryTenantConfigSwitchForBiz(long tenantId, Integer type) {
        if (Objects.isNull(type)) {
            return null;
        }

        TenantConfigQueryRequest request = new TenantConfigQueryRequest();
        request.setTenantId(tenantId);
        request.setTypeList(Lists.newArrayList(type));
        QueryTenantConfigResponse response = RpcInvoker.invoke(() -> tenantConfigThriftService.queryTenantConfig(request));
        ResponseHandler.checkResponseAndStatus(response, r -> r.getStatus().getCode(), r ->r.getStatus().getMsg());
        return CollectionUtils.isNotEmpty(response.getTenantConfigDTOList()) ?
                response.getTenantConfigDTOList().get(0).getConfigSwitch() : null;
    }

    public Map<Integer, TenantConfigForBizVo> queryTenantConfigForBiz(long tenantId) {
        TenantConfigQueryRequest request = new TenantConfigQueryRequest();
        request.setTypeList(Arrays.asList(TenantConfigTypeEnum.TYPE_TENANT_SPU_WEIGH.getCode()));
        request.setTenantId(tenantId);

        QueryTenantConfigResponse response = tenantConfigThriftService.queryTenantConfig(request);
        if (response.getStatus().getCode().equals(com.meituan.linz.thrift.response.Status.SUCCESS_CODE)) {
            List<TenantConfigForBizVo> tenantConfigDTOList = response.getTenantConfigDTOList().stream().map(t -> {
                TenantConfigForBizVo vo = new TenantConfigForBizVo();
                vo.setTenantId(t.getTenantId());
                vo.setConfigValue(t.getConfigValue());
                vo.setType(t.getType());
                return vo;
            }).collect(Collectors.toList());

            return tenantConfigDTOList.stream().collect(Collectors.toMap(TenantConfigForBizVo::getType, Function.identity()));
        } else {
            throw new BizException("查询租户配置失败");
        }
    }

    /**
     * 修改租户相关的配置
     *
     * @param tenantId
     * @param type
     * @param configValue 是一个json串
     * @param operatorId
     * @param operator
     */
    public void updateTenantConfigForBiz(long tenantId, int type, String configValue, String operatorId, String operator) {
        TenantConfigUpdateRequest request = new TenantConfigUpdateRequest();
        request.setType(type);
        request.setOperator(operator);
        request.setConfigValue(configValue);
        request.setOperatorId(operatorId);
        request.setTenantId(tenantId);
        UpdateTenantConfigResponse response = tenantConfigThriftService.updateTenantConfig(request);

        if (!response.getStatus().getCode().equals(com.meituan.linz.thrift.response.Status.SUCCESS_CODE)) {
            throw new BizException("更新租户配置失败");
        }
    }



    /**
     * 批量查询渠道信息
     */
    public List<ChannelInfoBo> queryChannelByTenantRel(Long tenantId, List<Integer> channelIds) {
        QueryChannelByTenantRelRequest request = new QueryChannelByTenantRelRequest();
        request.setChannelIds(channelIds);
        request.setTenantId(tenantId);
        ChannelDetailListResponse response = RpcInvoker.invoke(() -> channelManageThriftService.queryChannelByTenantRel(request));
        ResponseHandler.checkResponseAndStatus(response, r -> r.getStatus().getCode(), r -> r.getStatus().getMessage());
        return ConverterUtils.convertList(response.getChannelList(), ChannelInfoBo::fromChannelDetailDto);
    }

    public boolean isNotMerchantChargeGray(Long tenantId) {
        MerchantConfigQueryRequest request = new MerchantConfigQueryRequest();
        request.setMerchantId(tenantId);
        request.setConfigTypeList(Lists.newArrayList(MerchantConfigEnum.NOT_MERCHANT_CHARGE.getCode()));
        MerchantConfigQueryResponse response = null;
        try {
            response = merchantThriftService.merchantConfigQuery(request);
        } catch (TException e) {
            log.error("查询商品租户配置调用异常", e);
            throw new RuntimeException(e);
        }
        if (response == null || response.getStatus() == null) {
            throw new BizException("调用基础服务查询租户配置未返回值");
        }
        if (response.getStatus().getCode() != com.sankuai.meituan.shangou.empower.productbiz.client.enums.ResultCode.SUCCESS.getCode()) {
            throw new BizException(Strings.of("调用基础服务查询租户配置失败，{}", response.getStatus().getMsg()));
        }
        return Fun.anyMatch(response.getList(),
                config -> config.getConfigType().equals(MerchantConfigEnum.NOT_MERCHANT_CHARGE.getCode()));
    }

    public List<ChannelConfigDto> batchQueryTenantChannelConfigByKey(List<TenantChannelConfigKey> configKeys) {
        TenantChannelConfigListResponse resp;
        try {
            resp = configThriftService.batchQueryTenantChannelConfigByKey(configKeys);
        } catch (Exception e) {
            log.error("查询租户租户渠道配置异常", e);
            throw new BizException("查询租户配置异常");
        }

        if (resp.getStatus().code != 0) {
            String message = resp.getStatus().getMessage();
            log.error("查询租户租户渠道配置失败,msg:{}", message);
            throw new BizException("查询租户租户渠道配置失败 。" + message);
        }

        return resp.getChannelConfigList();
    }

    /**
     * 判断仓是否为托管仓
     * @param repoId 仓id
     * @return
     */
    @MethodLog(logRequest = true, logResponse = true)
    public Boolean isTrusteeshipRepoByRepoId(Long repoId){
        try {
            PoiMapResponse poiMapResponse = poiThriftService.queryPoiInfoMapByPoiIds(Lists.newArrayList(repoId));
            ResponseHandler.checkResponseAndStatus(poiMapResponse, res->res.getStatus().getCode(),res->res.getStatus().getMessage());
            Map<Long, PoiInfoDto> poiInfoMap = poiMapResponse.getPoiInfoMap();

            return Optional.ofNullable(poiInfoMap.get(repoId))
                    .filter(dto -> EntityTypeEnum.AREA_CENTRAL_WAREHOUSE.equals(EntityTypeEnum.getEnumByValue(dto.getEntityType())) && Boolean.TRUE.equals(dto.getOutRepository()))
                    .isPresent();
        } catch (Exception e) {
            log.error("判断仓是否为托管仓失败，repoId:{}", repoId, e);
            throw new BizException("判断仓是否为托管仓失败");
        }
    }

    public TenantStoreConfigInfoVo queryTenantStoreSelfPropertiesConfigList(Long tenantId, List<Long> storeIdList) {
        TenantStoreSelfPropertiesConfigQueryRequest request = new TenantStoreSelfPropertiesConfigQueryRequest();
        request.setTenantId(tenantId);
        request.setStoreIdList(storeIdList);
        TenantStoreSelfPropertiesConfigQueryResponse response =
                RpcInvoker.invoke(() -> productTenantConfigThriftService.queryTenantStoreSelfPropertiesConfigList(request));

        if (Objects.isNull(response) || Objects.isNull(response.getStatus())
                || response.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
            log.error("查询租户门店自定义属性配置失败：request {}, response {}.", request, response);
            throw new BizException("查询租户配置失败");
        }

        TenantStoreConfigInfoVo configInfoVo = new TenantStoreConfigInfoVo();
        if (Objects.nonNull(response.getProductManageType())) {
            configInfoVo.setStoreManageType(response.getProductManageType().getCode());
        }

        if (CollectionUtils.isNotEmpty(response.getConfigList())) {
            configInfoVo.setStoreConfigInfoList(Fun.map(response.getConfigList(), StoreConfigInfoVo::convert));
        }

        return configInfoVo;
    }

    /**
     * 饿了么非总部管品模式
     * @param tenantId
     * @return
     */
    public boolean isSemiSelfNotMerchantChargeWithCache(Long tenantId) {
        String cacheKey = String.format("%s_%d", SEMI_SELF_NOT_MERCHANT_CHARGE_KEY, tenantId);
        try {
            return (boolean)LOCAL_CACHE.get(cacheKey,  () -> {
                MerchantConfigQueryRequest request = new MerchantConfigQueryRequest();
                request.setMerchantId(tenantId);
                request.setConfigTypeList(Lists.newArrayList(MerchantConfigEnum.SEMI_SELF_NOT_MERCHANT_CHARGE.getCode()));
                MerchantConfigQueryResponse response =
                        RpcInvoker.invoke(() -> merchantThriftService.merchantConfigQuery(request));
                if (response == null || response.getStatus() == null) {
                    throw new BizException("调用商品基础服务查询租户配置未返回值");
                }
                if (response.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
                    throw new BizException(Strings.of("调用商品基础服务查询租户配置失败，{}", response.getStatus().getMsg()));
                }
                return Fun.anyMatch(response.getList(),
                        config -> config.getConfigType().equals(MerchantConfigEnum.SEMI_SELF_NOT_MERCHANT_CHARGE.getCode()));
            });
        } catch (Exception e) {
            log.error("调用商品基础服务查询租户配置异常， tenantId {}.", tenantId, e);
            throw new BizException(Strings.of("获取租户配置出错 {}", cacheKey), e);
        }
    }

    public boolean isStoreManagementTenant(Long tenantId) {
        if (isMerchantChargeSpu(tenantId)) {
            return false;
        }
        //获取租户开通渠道
        List<Integer> openChannels = queryChannelIds(tenantId).stream().filter(Objects::nonNull)
                .map(ChannelTypeEnumBo::getChannelId).collect(Collectors.toList());


        if (CollectionUtils.containsAny(openChannels,
                Lists.newArrayList(EnhanceChannelType.ELEM.getChannelId(), EnhanceChannelType.JDDJ.getChannelId()))) {
            return true;
        }

        return false;
    }
}
