package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.wavepick.converter;

import com.sankuai.meituan.reco.pickselect.thrift.wave.dto.WarehouseGoodsOwnerInfoDTO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.WarehouseGoodsOwnerInfoVO;
import org.mapstruct.Mapper;

/**
 * @Description:
 * @Author: zhangjian155
 * @Date: 2022/8/25 16:23
 */
@Mapper(componentModel = "spring")
public abstract class WarehouseGoodsOwnerModuleConverter {
    public abstract WarehouseGoodsOwnerInfoVO convert2Vo(WarehouseGoodsOwnerInfoDTO warehouseGoodsOwnerInfoDTO);
}
