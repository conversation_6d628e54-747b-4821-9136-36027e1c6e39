package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 4/21/23
 */
@TypeDoc(
        description = "我的推广记录页面模块数据"
)
@Data
@ApiModel("我的推广记录页面模块数据")
public class PullNewAggModuleVo {

    @FieldDoc(
            description = "模块名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "模块名称", required = true)
    private String name;

    @FieldDoc(
            description = "子模块列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "子模块列表", required = true)
    private List<PullNewAggSubModuleVo> pullNewAggSubModuleVos;

    @FieldDoc(
            description = "子模块一行有多少个,默认3个", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "子模块一行有多少个，默认3个", required = true)
    private int oneLineCount = 3;
}
