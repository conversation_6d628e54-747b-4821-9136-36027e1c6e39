package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pricetrend.StoreSkuWithChannelPriceTrendVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;

@TypeDoc(
        description = "渠道商品信息"
)
@Data
@ApiModel("渠道商品信息")
public class ChannelSkuForAppVO {

    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户ID", required = true)
    private Long tenantId;

    @FieldDoc(
            description = "sku编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "sku编码", required = true)
    private String sku;

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称", required = true)
    private String name;

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID", required = true)
    private Long storeId;

    @FieldDoc(
            description = "门店名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店名称", required = true)
    private String storeName;

    @FieldDoc(
            description = "商品图片", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品图片", required = true)
    private List<String> images;

    @FieldDoc(
            description = "规格", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "规格", required = true)
    private String spec;

    @FieldDoc(
            description = "重量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "重量", required = true)
    private Integer weight;

    @FieldDoc(
            description = "称重类型 1-称重计量 2-称重计件 3-非称重"
    )
    @ApiModelProperty(name = "称重类型")
    private Integer weightType;

    @FieldDoc(
            description = "基本单位", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "基本单位", required = true)
    private String basicUnit;

    @FieldDoc(
            description = "UPC信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "UPC信息", required = true)
    private List<String> upcInfo;

    @FieldDoc(
            description = "渠道价格和库存", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道价格和库存", required = true)
    private List<ChannelPriceAndStockVO> channels;

    @FieldDoc(
            description = "自定义库存标记  0：非自定义库存 1：自定义库存, 非无限库存模式下传0,非无限库存模式下这个字段不需要读取", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "自定义库存标记 ", required = true)
    private Integer customizeStockFlag;

    @FieldDoc(
            description = "第二天是否自动恢复无限库存 0-不自动恢复 1-自动恢复", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "第二天是否自动恢复无限库存", required = true)
    private Integer autoResumeInfiniteStock;

    @FieldDoc(
            description = "自定义库存数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "自定义库存数量", required = true)
    private Integer customizeStockQuantity;

    @FieldDoc(
            description = "各渠道一级前台分类", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "各渠道一级前台分类")
    private Map<Integer, String> channelId2FirstFrontCategoryNameMap;

    @FieldDoc(
            description = "月销量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "月销量")
    private Integer monthSaleAmount;

    @FieldDoc(
            description = "报价审核状态 1-待审核", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "报价审核状态")
    private Integer reviewStatus;

    @FieldDoc(
            description = "是否力荐 1-力荐 0-否", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否力荐")
    private Integer specialty;

    @FieldDoc(
            description = "报价价格，单位：分", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "报价价格")
    private Double quotePrice;

    @FieldDoc(
            description = "标签列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "标签列表")
    private List<SkuTagSimpleVO> skuTagSimpleVOList;

    @FieldDoc(
            description = "spuId", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "spuId")
    private String spuId;

    /**
     * 设置门店商品各渠道价格趋势信息
     * @param storeSkuWithChannelPriceTrendVO 门店商品价格趋势
     */
    public void fillStoreSkuWithChannelPriceTrendInfo(
            StoreSkuWithChannelPriceTrendVO storeSkuWithChannelPriceTrendVO) {

        if (CollectionUtils.isEmpty(this.channels)) {
            return;
        }

        this.channels.forEach(channelSku -> {
            // 计算是否展示价格趋势图标
            // 若降级到直接展示价格趋势图标, 直接返回可以展示图标; 反之, 则需要判断是否有价格趋势数据
            // 备注：趋势图标展示不判断权限, 只判断是否有数据
            boolean priceTrendIconDirectShow = MccConfigUtil.isPriceTrendIconDirectShow();
            boolean hasPriceTrend = storeSkuWithChannelPriceTrendVO != null
                    && storeSkuWithChannelPriceTrendVO.isHasPriceTrend(channelSku.getChannelId());
            boolean showPriceTrendIcon = priceTrendIconDirectShow || hasPriceTrend;
            channelSku.setShowPriceTrendIcon(showPriceTrendIcon);
        });
    }
}

