package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.impl;

import com.google.common.collect.Lists;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.AbstractSinglePendingTaskService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.out.warehouse.OutWarehouseWorkOrderWrapper;
import com.sankuai.meituan.reco.store.wms.thrift.outwarehouse.workorder.request.OutWarehouseWorkOrderCountThriftRequest;
import com.sankuai.meituan.reco.store.wms.thrift.outwarehouse.workorder.response.OutWarehouseWorkOrderCountThriftResponse;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * author xujunfeng02
 * dateTime 2022/5/11 4:16 PM
 * description 调拨出库拣货工单服务
 */
@Service
public class OutWarehousePickWorkOrderService extends AbstractSinglePendingTaskService {

    /**
     * 出库拣货类型工单状态，20-拣货单已生成，22-待拣货，24-拣货中，31-复核未通过
     */
    private static final List<Integer> PICK_INITIALIZED = Lists.newArrayList(20, 22, 24, 31);

    /**
     * 工单类型：61-出库拣货类型工单
     */
    private static final int OUT_WAREHOUSE_PICK = 61;

    @Resource
    private OutWarehouseWorkOrderWrapper outWarehouseWorkOrderWrapper;

    @Override
    protected PendingTaskResult getPendingTaskCount(PendingTaskParam param) throws Exception {

        OutWarehouseWorkOrderCountThriftRequest thriftRequest = OutWarehouseWorkOrderCountThriftRequest.builder()
                .entityId(param.getEntityId())
                .status(PICK_INITIALIZED)
                .operatorId(param.getUser().getAccountId())
                .type(OUT_WAREHOUSE_PICK)
                .merchantId(param.getTenantId())
                .build();
        OutWarehouseWorkOrderCountThriftResponse thriftResponse = outWarehouseWorkOrderWrapper.count(thriftRequest);
        return PendingTaskResult.createNumberMarker(thriftResponse.getCount().intValue());
    }

    @Override
    protected AuthCodeEnum module() {
        return AuthCodeEnum.DH_TASK_PICKING;
    }
}