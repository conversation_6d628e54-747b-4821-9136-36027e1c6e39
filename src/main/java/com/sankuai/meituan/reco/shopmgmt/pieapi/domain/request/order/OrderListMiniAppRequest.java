package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order;

import com.dianping.lion.client.Lion;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.MiniAppConstants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-07-11
 */
@TypeDoc(
        description = "全部订单列表分页查询请求"
)
@ApiModel("全部订单列表分页查询请求")
@Data
@Slf4j
public class OrderListMiniAppRequest {
    @FieldDoc(
            description = "门店 ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店 ID")
    private List<Long> storeIds;

    @FieldDoc(
            description = "订单创建起始日期 格式:2021-07-01", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单创建起始日期 格式:2021-07-01")
    @NotNull
    private String beginCreateDate;

    @FieldDoc(
            description = "订单创建结束日期 格式:2021-07-01", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单创建结束日期 格式:2021-07-01")
    @NotNull
    private String endCreateDate;

    @FieldDoc(
            description = "渠道ID列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道ID列表")
    private List<Integer> channelIdList;

    @FieldDoc(
            description = "配送订单类型  1-立即单 2-预约单", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "配送订单类型  1-立即单 2-预约单")
    private Integer deliveryOrderType;

    @FieldDoc(
            description = "聚合订单状态  1-进行中订单 2-已完成订单 3-已取消订单", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "聚合订单状态")
    private Integer aggregationOrderStatus;

    @FieldDoc(
            description = "第几页", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "第几页", required = true)
    @NotNull
    private Integer page;

    @FieldDoc(
            description = "每页行数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "每页行数", required = true)
    @NotNull
    private Integer pageSize;

    @FieldDoc(
            description = "搜索关键词", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "搜索关键词", required = true)
    private String keyword;

    @FieldDoc(
            description = "门店类型 3-门店 5-中心仓 6-前置仓", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店类型")
    private Integer entityType;


    @FieldDoc(
            description = "配送方式  1-平台配送  2-聚合配送 3-自提", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "配送方式")
    private List<Integer> distributeTypeList;


    @FieldDoc(
            description = "配送订单类型  1-立即单 2-预约单", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "配送订单类型")
    private List<Integer> deliveryOrderTypeList;

    @FieldDoc(
            description = "聚合订单状态  1-进行中订单 2-已完成订单 3-已取消订单", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "聚合订单状态 - 支持多选")
    private List<Integer> aggregationOrderStatusList;

    public String validate() {
        if (CollectionUtils.isEmpty(storeIds)) {
            return "门店ID和前置仓ID不能都为空";
        }
        // 判断时间范围有效性
        try {
            LocalDate beginDateTime = LocalDate.parse(beginCreateDate);
            LocalDate endDateTime = LocalDate.parse(endCreateDate);
            // 仅允许从 5 个月前的 1 号选择
            LocalDate earliestDateRange = LocalDate.now().minusMonths(5L).withDayOfMonth(1);
            LocalDate now = LocalDate.now();
            if (beginDateTime.isBefore(earliestDateRange)) {
                return "6 个月之前的时间不可选择";
            }
            if (endDateTime.isAfter(now)) {
                return "结束时间不能超过当天";
            }
            long days = ChronoUnit.DAYS.between(beginDateTime, endDateTime);
            if (days < 0) {
                return "开始日期不能大于结束日期";
            }
            Integer maximumDay = Lion.getConfigRepository().getIntValue(MiniAppConstants.ORDER_SEARCH_MAXIMUM_DAY, MiniAppConstants.ORDER_SEARCH_DEFAULT_MAXIMUM_DAY);
            if (days > maximumDay) {
                return "最多可选择 " + maximumDay + " 天跨度的订单";
            }
        } catch (Exception e) {
            log.error("OrderListMiniAppRequest 时间范围错误 : beginCreateDate:{}, endCreateDate:{}", beginCreateDate, endCreateDate, e);
            return "时间范围错误，请联系管理员";
        }

        if (page == null || page < 1 || pageSize == null || pageSize < 1) {
            return "分页参数不合法";
        }
        return null;
    }
}
