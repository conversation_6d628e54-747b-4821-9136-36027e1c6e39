package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Optional;

/**
 * <AUTHOR>
 * Email <EMAIL>
 * Date 2022/10/14 5:56 下午
 * Description
 */

@TypeDoc(
        description = "考勤异常申报审批提交请求"
)
@ApiModel("考勤异常申报审批提交请求")
@Data
public class AttendanceApprovalSubmitRequest {

    @FieldDoc(
            description = "审批详情Id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "审批详情Id")
    @NotNull
    private Long baseInfoId;

    @FieldDoc(
            description = "审批结果（2通过，4驳回）", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "审批结果（2通过，4驳回）")
    @NotNull
    private Integer approvalStatus;

    @FieldDoc(
            description = "审批结果说明", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "审批结果说明")
    private String approvalStatusText;

    public Optional<String> validate() {
        if (this.baseInfoId == null || this.baseInfoId <= 0) {
            return Optional.of("审批详情Id错误");
        } else if (this.approvalStatus == null) {
            return Optional.of("审批结果为空");
        }
        return Optional.empty();
    }
}
