package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.auth;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;


@TypeDoc(
        description = "批量查询权限码是否有权限"
)
@Data
@ApiModel("批量查询权限码是否有权限")
public class BatchAuthCodeCheckRequest {

    @FieldDoc(
            description = "权限码"
    )
    @ApiModelProperty(value = "权限码", required = true)
    @NotEmpty
    private List<String> authCode;

}
