package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.wpt.user.retrieve.thrift.message.RpcUserRetrieveService;
import com.sankuai.wpt.user.retrieve.thrift.message.UserFields;
import com.sankuai.wpt.user.retrieve.thrift.message.UserModel;
import com.sankuai.wpt.user.retrieve.thrift.message.UserRespMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2022/8/19
 **/
@Service
@Slf4j
public class UserRetrieveWrapper {

    @Resource
    private RpcUserRetrieveService.Iface rpcUserRetrieveService;

    public String getUserNameById(Long userId) {
        UserFields userFields = new UserFields().setUsername(true);
        UserModel userModel = getUserByIdWithMsg(userId, userFields);
        return Objects.nonNull(userModel) ? userModel.getUsername() : null;
    }

    @MethodLog(logRequest = true, logResponse = true)
    public UserModel getUserByIdWithMsg(Long userId, UserFields userFields) {

        try {
            UserRespMsg userRespMsg = rpcUserRetrieveService.getUserByIdWithMsg(userId, userFields);
            if (userRespMsg.isSuccess()) {
                return userRespMsg.getUser();
            }
            log.warn("getUserByIdWithMsg fail, error msg: {}", userRespMsg.getError());
        } catch (TException e) {
            log.error("getUserByIdWithMsg error: {}", e);
        }
        return null;
    }
}
