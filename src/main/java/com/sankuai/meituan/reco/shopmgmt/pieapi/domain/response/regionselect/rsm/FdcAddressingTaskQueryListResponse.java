package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.regionselect.rsm;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * Email <EMAIL>
 * Date 2022/12/7 3:35 下午
 * Description
 */

@TypeDoc(
        description = "任务列表查询响应"
)
@Data
@ApiModel("任务列表查询响应")
public class FdcAddressingTaskQueryListResponse {

    @FieldDoc(
            description = "任务详情", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty("任务详情")
    private List<FdcAddressingTaskVO> list;

    @FieldDoc(
            description = "总数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty("总数量")
    private Long totalCount;

    @FieldDoc(
            description = "是否还有更多数据", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty("是否还有更多数据")
    private Integer hasMore;
}
