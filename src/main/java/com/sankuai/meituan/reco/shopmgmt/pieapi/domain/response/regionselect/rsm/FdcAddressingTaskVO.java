package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.regionselect.rsm;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * Email <EMAIL>
 * Date 2022/12/7 12:09 下午
 * Description
 */

@Data
@TypeDoc(description = "寻仓任务详情(")
@ApiModel("寻仓任务详情")
public class FdcAddressingTaskVO {

    @ApiModelProperty("寻仓任务id")
    @FieldDoc(description = "寻仓任务id")
    private Long id;

    @ApiModelProperty("任务名")
    @FieldDoc(description = "任务名")
    private String taskName;

    @ApiModelProperty("省份名")
    @FieldDoc(description = "省份名")
    private String provinceName;

    @ApiModelProperty("城市ID")
    @FieldDoc(description = "城市ID")
    private Integer cityId;

    @ApiModelProperty("城市名")
    @FieldDoc(description = "城市名")
    private String cityName;

    @ApiModelProperty("测算区域名")
    @FieldDoc(description = "测算区域名")
    private String aoiName;

    @ApiModelProperty("测试区域Id")
    @FieldDoc(description = "测试区域Id")
    private Long aoiId;

    @ApiModelProperty("测算区域围栏")
    @FieldDoc(description = "测算区域围栏")
    private String aoiRegion;

    @ApiModelProperty("测算区域中心点坐标 精度,纬度")
    @FieldDoc(description = "测算区域中心点坐标 精度,纬度")
    private String aoiCoordinate;

    @ApiModelProperty("任务属性， （寻仓人员属性）0:三方，1:自有")
    @FieldDoc(description = "任务属性， （寻仓人员属性）0:三方，1:自有")
    private Integer ownerType;

    @ApiModelProperty("任务状态，40:待分配，30:待执行，20:进行中，10:仓源待签约，1:已完成，0:已作废")
    @FieldDoc(description = "任务状态，40:待分配，30:待执行，20:进行中，10:仓源待签约，1:已完成，0:已作废")
    private Integer taskStatus;

    @ApiModelProperty("归属机构Id(寻仓人员归属组织结构)")
    @FieldDoc(description = "归属机构Id(寻仓人员归属组织结构)")
    private Long ownerOrgId;

    @ApiModelProperty("归属机构(寻仓人员归属组织结构)")
    @FieldDoc(description = "归属机构(寻仓人员归属组织结构)")
    private String ownerOrgName;

    @ApiModelProperty("寻仓人员员工姓名")
    @FieldDoc(description = "寻仓人员员工姓名")
    private String ownerEmployeeName;

    @ApiModelProperty("寻仓人员账号id")
    @FieldDoc(description = "寻仓人员账号id")
    private Long ownerEmployeeId;

    @ApiModelProperty("期望完成时间（单位毫秒）")
    @FieldDoc(description = "期望完成时间（单位毫秒）")
    private Long expectFinishTime;

    @ApiModelProperty("实际完成时间（单位毫秒）")
    @FieldDoc(description = "实际完成时间（单位毫秒）")
    private Long actualFinishTime;

    @ApiModelProperty("创建时间（单位毫秒）")
    @FieldDoc(description = "创建时间（单位毫秒）")
    private Long createTime;

    @ApiModelProperty("寻仓重点圆形区域")
    @FieldDoc(description = "寻仓重点圆形区域")
    private List<FocusCircleVO> focusCircles;

    @ApiModelProperty("可执行的操作")
    @FieldDoc(description = "可执行的操作")
    private List<Integer> executableEventList;

    @ApiModelProperty("仓源列表")
    @FieldDoc(description = "仓源列表")
    private List<FdcRentalSourceVO> rentalSourceList;
}

