package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery.orderplatform;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * OrderPlatformDeliveryReq
 *
 * <AUTHOR>
 * @since 2023/2/27
 */
@Data
public class OrderPlatformDeliveryReq {

    /**
     * 百川订单id
     */
    @NotNull
    private Long empowerOrderId;

    /**
     * 渠道号
     *
     * @see com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ChannelTypeEnum
     */
    @NotNull
    private Integer orderChannel;

    /**
     * 门店ID
     */
    //@NotNull
    private Long storeId;
}
