package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description:
 * @Auther: nifei
 * @Date: 2023/8/21 17:53
 */
@TypeDoc(
        description = "已分拣商品列表信息"
)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WarehousePackItemVO {

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.OPTIONAL
    )
    private String itemName;

    @FieldDoc(
            description = "商品sku", requiredness = Requiredness.OPTIONAL
    )
    private String skuId;

    @FieldDoc(
            description = "规格", requiredness = Requiredness.OPTIONAL
    )
    private String spec;

    @FieldDoc(
            description = "品类信息", requiredness = Requiredness.OPTIONAL
    )
    private String categoryName;

    @FieldDoc(
            description = "商品图片列表", requiredness = Requiredness.OPTIONAL
    )
    private List<String> picList;

    @FieldDoc(
            description = "upc列表", requiredness = Requiredness.OPTIONAL
    )
    private List<String> upcList;

    @FieldDoc(
            description = "已装箱数量", requiredness = Requiredness.OPTIONAL
    )
    private Integer packNum;

    @FieldDoc(
            description = "拣货任务id", requiredness = Requiredness.OPTIONAL
    )
    private Long pickTaskId;

    @FieldDoc(
            description = "已拣货账号id"
    )
    private Long pickedAccountId;

    @FieldDoc(
            description = "已拣货操作人", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "已拣货操作人")
    private String pickedOperateName;

    @FieldDoc(
            description = "已拣货账号id"
    )
    private PickPackingSpecVO pickPackingSpec;

    @FieldDoc(
            description = "库存基本单位名称"
    )
    private String basicUnit;
}
