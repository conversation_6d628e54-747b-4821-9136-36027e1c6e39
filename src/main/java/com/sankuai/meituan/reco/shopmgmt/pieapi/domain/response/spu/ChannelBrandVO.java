package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelBrandBindingDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelBrandDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelBrandRelationDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.spu.ChannelBrandDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Title: ChannelBrandVO
 * @Description: 渠道品牌信息
 * @Author: zhaolei12
 * @Date: 2020/5/16 5:58 下午
 */
@TypeDoc(
        description = "渠道品牌信息"
)
@Data
@ApiModel("渠道品牌信息")
public class ChannelBrandVO {

    @FieldDoc(
            description = "渠道id", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道id")
    private Integer channelId;

    @FieldDoc(
            description = "渠道品牌编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道品牌编码")
    private String channelBrandCode;

    @FieldDoc(
            description = "渠道品牌名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道品牌名称")
    private String channelBrandName;

    public static ChannelBrandVO ofDTO(ChannelBrandDTO channelBrandDTO) {
        if (channelBrandDTO == null) {
            return null;
        }
        ChannelBrandVO channelBrandVO = new ChannelBrandVO();
        channelBrandVO.setChannelBrandCode(channelBrandDTO.getChannelBrandCode());
        channelBrandVO.setChannelBrandName(channelBrandDTO.getChannelBrandName());
        return channelBrandVO;
    }

    public static ChannelBrandVO ofBizDTO(ChannelBrandDto channelBrandDTO) {
        if (channelBrandDTO == null) {
            return null;
        }
        ChannelBrandVO channelBrandVO = new ChannelBrandVO();
        channelBrandVO.setChannelBrandCode(channelBrandDTO.getChannelBrandCode());
        channelBrandVO.setChannelBrandName(channelBrandDTO.getChannelBrandName());
        return channelBrandVO;
    }

    public static ChannelBrandDTO toDTO(ChannelBrandVO channelBrandVO) {
        if (channelBrandVO == null) {
            return null;
        }
        ChannelBrandDTO channelBrandDTO = new ChannelBrandDTO();
        channelBrandDTO.setChannelBrandCode(channelBrandVO.getChannelBrandCode());
        channelBrandDTO.setChannelBrandName(channelBrandVO.getChannelBrandName());
        return channelBrandDTO;
    }


    public static ChannelBrandVO ofOcms(ChannelBrandBindingDTO channelBrandBindingDTO) {
        if (channelBrandBindingDTO == null) {
            return null;
        }
        ChannelBrandVO channelBrandVO = new ChannelBrandVO();
        channelBrandVO.setChannelId(channelBrandBindingDTO.getChannelId());
        channelBrandVO.setChannelBrandCode(channelBrandBindingDTO.getBrandCode());
        channelBrandVO.setChannelBrandName(channelBrandBindingDTO.getBrandName());
        return channelBrandVO;
    }

    public static ChannelBrandVO ofBiz(ChannelBrandRelationDTO channelBrandRelationDTO) {
        if (channelBrandRelationDTO == null) {
            return null;
        }
        ChannelBrandVO channelBrandVO = new ChannelBrandVO();
        channelBrandVO.setChannelId(channelBrandRelationDTO.getChannelId());
        channelBrandVO.setChannelBrandCode(channelBrandRelationDTO.getBrandCode());
        channelBrandVO.setChannelBrandName(channelBrandRelationDTO.getBrandName());
        return channelBrandVO;
    }
}
