package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiShippingModeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.converters.price.PriceThriftInfoConverter;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.SkuPurchasePriceVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.StoreSkuKey;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.bo.ChannelTypeEnumBo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.PriceChangePreviewRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.priceconfig.StoreSkuPriceFilter;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.priceconfig.StoreSkuPriceFilterCondition;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.quote.QueryQuoteReviewingCountResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.PriceChangePreviewResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelSkuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelSpuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.StoreSkuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.StoreSpuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.FallbackException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MoneyUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ocms.OCMSUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.price.PriceEffectWrapper;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.common.Status;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.relations.SupplyRelationQueryThriftService;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.relations.request.PurchasePriceBatchQueryReq;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.relations.response.PurchasePriceBatchQueryResp;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.relations.response.dto.PurchasePriceDto;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.work.purchasesku.dto.PurchaseSkuPropertyDto;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.work.purchasesku.response.PurchaseSkuQueryResp;
import com.sankuai.meituan.shangou.empower.ocms.client.price.request.quote.QueryQuoteReviewsRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.price.response.quote.QueryQuoteReviewsResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.price.service.QuoteReviewThriftService;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.StoreSpuDetailRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.product.response.StoreSpuDetailResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.product.service.StoreSpuThriftService;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelType;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ResponseCodeEnum;
import com.sankuai.meituan.shangou.empower.price.client.dto.config.SkuPriceChangeInfoDTO;
import com.sankuai.meituan.shangou.empower.price.client.dto.strategy.ChannelSyncStrategyDTO;
import com.sankuai.meituan.shangou.empower.price.client.dto.strategy.SkuBaseInfoDTO;
import com.sankuai.meituan.shangou.empower.price.client.dto.strategy.SkuHitSyncStrategyDTO;
import com.sankuai.meituan.shangou.empower.price.client.request.config.SkuPriceChangeByWeightReqeust;
import com.sankuai.meituan.shangou.empower.price.client.request.config.StoreSkuPriceFilterQueryRequest;
import com.sankuai.meituan.shangou.empower.price.client.request.quote.QuoteReviewCountRequest;
import com.sankuai.meituan.shangou.empower.price.client.request.strategy.QuerySkuHitSyncStrategyDetailRequest;
import com.sankuai.meituan.shangou.empower.price.client.response.PriceCommonResponse;
import com.sankuai.meituan.shangou.empower.price.client.response.config.SkuPriceChangeByWeightResponse;
import com.sankuai.meituan.shangou.empower.price.client.response.config.StoreSkuPriceFilterQueryResponse;
import com.sankuai.meituan.shangou.empower.price.client.response.quote.QuoteReviewCountResponse;
import com.sankuai.meituan.shangou.empower.price.client.response.strategy.QueryHitPriceStrategyResponse;
import com.sankuai.meituan.shangou.empower.price.client.service.OfflinePriceQuoteThriftService;
import com.sankuai.meituan.shangou.empower.price.client.service.PriceConfigThriftService;
import com.sankuai.meituan.shangou.empower.price.client.service.PriceStrategyThriftService;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.command.PoiSkuIdCheckCommand;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.result.PoiSkuIdListCheckResult;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.service.EmpowerPoiComposeSkuThriftService;
import com.sankuai.meituan.shangou.saas.common.datetime.DateUtil;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.sgfulfillment.purchase.common.api.thrift.ThriftResult;
import com.sankuai.sgfulfillment.purchase.common.api.thrift.UserContext;
import com.sankuai.sgfulfillment.scm.monitor.api.thrift.pricehistory.PurchasePriceHistoryQueryThriftService;
import com.sankuai.sgfulfillment.scm.monitor.api.thrift.pricehistory.request.QuerySkuPurchasePriceInfoRequest;
import com.sankuai.sgfulfillment.scm.monitor.api.thrift.pricehistory.response.QuerySkuPurchasePriceInfoResponse;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Rhino
@Service
@Slf4j
public class SaasPriceServiceWrapper {

    @Resource
    private OfflinePriceQuoteThriftService offlinePriceQuoteThriftService;
    @Resource
    private StoreSpuThriftService storeSpuThriftService;
    @Autowired
    private PriceStrategyThriftService priceStrategyThriftService;

    @Resource(name = "priceAppPriceConfigThriftService")
    private PriceConfigThriftService priceConfigThriftService;

    @Resource
    private QuoteReviewThriftService quoteReviewThriftService;

    @Resource
    private PriceEffectWrapper priceEffectWrapper;

    @Resource
    private PriceConsistencyHelper priceConsistencyHelper;

    @Resource
    private SupplyRelationQueryThriftService supplyRelationQueryThriftServiceClient;
    @Autowired
    private EmpowerPoiComposeSkuThriftService.Iface empowerPoiComposeSkuThriftService;
    @Resource
    private TenantWrapper tenantWrapper;

    @Resource
    private PurchasePriceHistoryQueryThriftService purchasePriceHistoryQueryThriftService;

    private static final Integer SKU_QUERY_BATCH_SIZE = 50;


    @MethodLog(logRequest = true, logResponse = true)
    public CommonResponse<QueryQuoteReviewingCountResponse> queryQuoteReviewingCountList(List<Long> storeIds, User user) {
        try {
            QuoteReviewCountRequest request = new QuoteReviewCountRequest();
            request.setTenantId(user.getTenantId());
            request.setStoreIds(storeIds);
            log.info("offlinePriceQuoteThriftService.queryQuoteReviewingCount() request:{}", request);
            QuoteReviewCountResponse queryResponse = offlinePriceQuoteThriftService.queryQuoteReviewingCount(request);
            log.info("offlinePriceQuoteThriftService.queryQuoteReviewingCount() response:{}", queryResponse);

            QueryQuoteReviewingCountResponse countResponse = new QueryQuoteReviewingCountResponse();
            countResponse.setCount(queryResponse.getQuoteReviewingCount());
            return new CommonResponse(queryResponse.getCode(), queryResponse.getMsg(), countResponse);
        } catch (TException e) {
            log.error("SaasPriceServiceWrapper.queryQuoteReviewingNumList() TException ", e);
            throw new CommonRuntimeException(e);
        }
    }


    @MethodLog
    public CommonResponse<PriceChangePreviewResponse> previewPriceChange(PriceChangePreviewRequest request) {
        SkuPriceChangeByWeightReqeust skuPriceChangeByWeightReqeust = OCMSUtils.convert2SkuPriceChangeByWeightRequest(request);
        try {
            log.info("SaasPriceServiceWrapper.previewPriceChange changeSkuPriceByWeightInfo request:{}", skuPriceChangeByWeightReqeust);
            SkuPriceChangeByWeightResponse skuPriceChangeByWeightResponse = priceConfigThriftService.changeSkuPriceByWeightInfo(skuPriceChangeByWeightReqeust);
            log.info("SaasPriceServiceWrapper.previewPriceChange changeSkuPriceByWeightInfo response:{}", skuPriceChangeByWeightResponse);
            CommonResponse<PriceChangePreviewResponse> commonResponse = new CommonResponse<>();
            if (skuPriceChangeByWeightResponse.getCode() == ResponseCodeEnum.SUCCESS.getValue() && CollectionUtils.isNotEmpty(skuPriceChangeByWeightResponse.getSuccessTransformInfos())) {
                SkuPriceChangeInfoDTO skuPriceChangeInfoDTO = skuPriceChangeByWeightResponse.getSuccessTransformInfos().get(0);
                commonResponse.setCode(ResultCode.SUCCESS.getCode());
                commonResponse.setMessage("获取预览价格成功");
                PriceChangePreviewResponse priceChangePreviewResponse = new PriceChangePreviewResponse();
                priceChangePreviewResponse.setPreviewPrice(MoneyUtils.centToYuan(skuPriceChangeInfoDTO.getChangePrice().intValue()));

                // 获取是否有待审核报价
                boolean existToReviewQuotes = existToReviewQuotes(request);
                priceChangePreviewResponse.setExistToReviewQuotes(existToReviewQuotes);
                commonResponse.setData(priceChangePreviewResponse);
                return commonResponse;
            }
            return new CommonResponse<>(ResponseCodeEnum.FAILED.getValue(), "价格预览查询失败，请稍后重试", null);
        } catch (TException e) {
            throw new CommonRuntimeException(e);
        }
    }


    private boolean existToReviewQuotes(PriceChangePreviewRequest request) {
        try {
            QueryQuoteReviewsRequest queryQuoteReviewsRequest = OCMSUtils.convert2ToReviewQuoteQueryRequest(request.getTenantId(), request.getStoreId(), request.getSkuId());
            log.info("SaasPriceServiceWrapper.existToReviewQuote queryQuoteReviews request:{}", queryQuoteReviewsRequest);
            QueryQuoteReviewsResponse queryQuoteReviewsResponse = quoteReviewThriftService.queryQuoteReviews(queryQuoteReviewsRequest);
            log.info("SaasPriceServiceWrapper.existToReviewQuote queryQuoteReviews response:{}", queryQuoteReviewsResponse);
            return queryQuoteReviewsResponse.getCode() == ResponseCodeEnum.SUCCESS.getValue() && CollectionUtils.isNotEmpty(queryQuoteReviewsResponse.getQuoteReviewList());
        } catch (Exception e) {
            log.error("SaasPriceServiceWrapper.existToReviewQuote queryQuoteReviewForAppList error.", e);
            return false;// 异常情况默认无待审核报价
        }
    }

    @com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog(logRequest = true, logResponse = true)
    public StoreSkuPriceFilter queryStoreSkuPriceFilter(Long tenantId, StoreSkuPriceFilterCondition condition) {

        if (condition == null) {
            return null;
        }

        // 查询一个门店商品的价格过滤器
        List<StoreSkuPriceFilterCondition> filterConditionList = Lists.newArrayList(condition);

        List<StoreSkuPriceFilter> filterList = queryStoreSkuPriceFilter(tenantId, filterConditionList);

        if (CollectionUtils.isEmpty(filterList)) {
            return null;
        }

        return filterList.get(0);
    }

    /**
     * 批量查询门店商品价格过滤器
     *
     * @param tenantId      租户id
     * @param conditionList 门店商品价格过滤器的查询条件
     * @return 过滤器, 按照门店商品key分组
     */
    @MethodLog(logRequest = true, logResponse = true)
    public Map<StoreSkuKey, StoreSkuPriceFilter> queryStoreSkuPriceFilterMap(
            Long tenantId, List<StoreSkuPriceFilterCondition> conditionList) {

        List<StoreSkuPriceFilter> filterList =
                queryStoreSkuPriceFilter(tenantId, conditionList);

        return Optional.ofNullable(filterList).map(List::stream).orElse(Stream.empty())
                .collect(Collectors.toMap(domain -> new StoreSkuKey(domain.getStoreId(), domain.getSkuId()),
                        Function.identity(), (oldFilter, newFilter) -> newFilter));
    }

    @MethodLog(logRequest = true, logResponse = true)
    public List<StoreSkuPriceFilter> queryStoreSkuPriceFilter(
            Long tenantId, List<StoreSkuPriceFilterCondition> conditionList) {

        // 如果门店商品价格过滤器查询条件为空, 直接返回空列表
        if (CollectionUtils.isEmpty(conditionList)) {
            return Collections.emptyList();
        }

        try {
            // 构建查询条件
            StoreSkuPriceFilterQueryRequest request = new StoreSkuPriceFilterQueryRequest();
            request.setTenantId(tenantId);
            request.setConditionDTOList(ConverterUtils.convertList(conditionList,
                    StoreSkuPriceFilterCondition::convert2StoreSkuPriceFilterConditionDTO));

            // 查询门店商品价格过滤器
            StoreSkuPriceFilterQueryResponse response = priceConfigThriftService.queryStoreSkuPriceFilter(request);
            log.info("查询门店商品价格过滤器, request:{}, response:{}", request, response);

            if (response.getCode() != ResultCode.SUCCESS.getCode()) {
                throw new CommonRuntimeException("查询门店商品价格过滤器失败, 请稍后重试！", ResultCode.FAIL);
            }

            return ConverterUtils.convertList(response.getFilterDTOList(), StoreSkuPriceFilter::buildStoreSkuPriceFilter);
        } catch (TException e) {
            throw new CommonRuntimeException(e);
        }
    }


    @Degrade(rhinoKey = "SaasPriceServiceWrapper.queryStoreSpuAdjustPriceInfo",
            fallBackMethod = "fallbackQueryStoreSpuAdjustPriceInfo",
            ignoreExceptions = {CommonLogicException.class})
    public CommonResponse<StoreSpuAdjustPriceInfoVO> queryStoreSpuAdjustPriceInfo(User user, Long storeId, String spuId) {

        // 查询门店spu信息
        StoreSpuVO storeSpuVO = findStoreSpuDetail(user, storeId, spuId);
        if (storeSpuVO == null) {
            throw new CommonLogicException("门店商品不存在", ResultCode.FAIL);
        }

        // 若spu无规格, 直接返回商品
        if (CollectionUtils.isEmpty(storeSpuVO.getStoreSkuList())) {
            return CommonResponse.success(StoreSpuAdjustPriceInfoVO.build(storeSpuVO));
        }

        // 查询商品定价方式
        QueryHitPriceStrategyResponse response = querySkuSyncStrategyDetails(user, storeId, storeSpuVO);

        // 查询采购信息
        List<SkuPurchasePriceVo> purchasePriceList = querySkuPurchasePriceInfo(storeSpuVO);

        // 查询当前门店开通的渠道
        List<ChannelTypeEnumBo> channelTypeEnums = tenantWrapper.queryTenantStoreChannelIds(user.getTenantId(), storeId);

        // 构建商品改价信息
        return CommonResponse.success(StoreSpuAdjustPriceInfoVO.build(storeSpuVO, response.getSkuHitSyncStrategyDTOS(),
                purchasePriceList, channelTypeEnums, response.getPriceCentMode()));
    }

    public CommonResponse<StoreSpuAdjustPriceInfoVO> fallbackQueryStoreSpuAdjustPriceInfo(User user, Long storeId, String spuId, Throwable e) {
        log.warn("查询改价信息降级, SaasPriceServiceWrapper.queryStoreSpuAdjustPriceInfo降级, tenantId:{}, storeId:{}, spuId:{}", user.getTenantId(), storeId, spuId, e);
        throw new FallbackException("SaasPriceServiceWrapper.queryStoreSpuAdjustPriceInfo降级");
    }

    public CommonResponse<SkuAdjustStrategyAndReferencePriceVO> querySingleSkuAdjustStrategyAndReferencePrice(
            User user, Long storeId, String spuId, String skuId) {
        try {
            Long tenantId = Optional.ofNullable(user).map(User::getTenantId)
                    .orElseThrow(() -> new CommonLogicException("获取登录信息失败", ResultCode.FAIL));

            // 查询门店spu信息
            StoreSpuVO storeSpuVO = findStoreSpuDetail(user, storeId, spuId);
            StoreSkuVO selectedStoreSku = selectAdjustPriceSku(storeSpuVO, skuId);

            SkuHitSyncStrategyDTO skuHitSyncStrategyDTO = getSkuHitSyncStrategyDTO(storeId, tenantId, selectedStoreSku);

            StoreSkuAdjustPriceDetailVO skuAdjustPriceDetail = StoreSkuAdjustPriceDetailVO
                    .buildStoreSkuAdjustPriceDetailVO(storeSpuVO, selectedStoreSku, skuHitSyncStrategyDTO);

            SkuAdjustStrategyAndReferencePriceVO result = new SkuAdjustStrategyAndReferencePriceVO();
            result.setStoreSkuAdjustPriceDetail(skuAdjustPriceDetail);

            addReferencePriceInfo(user, storeId, selectedStoreSku, result);
            return CommonResponse.success(result);
        } catch (CommonLogicException e) {
            log.error("定价助手快捷改价逻辑异常", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("定价助手快捷改价运行时未知异常", e);
            return CommonResponse.fail2(ResultCode.FAIL);
        }
    }

    private void addReferencePriceInfo(User user, Long storeId, StoreSkuVO selectedStoreSku, SkuAdjustStrategyAndReferencePriceVO result) {
        try {
            CommonResponse<Map<String, List<PriceEffectIndexReferencePriceVO>>> referencePriceResp = priceEffectWrapper
                    .querySkuPriceEffectIndexReferencePrice(user, storeId, Lists.newArrayList(selectedStoreSku.getSkuId()));

            if (referencePriceResp.getCode() == ResultCode.SUCCESS.getCode() &&
                    referencePriceResp.getData().containsKey(selectedStoreSku.getSkuId())) {
                List<PriceEffectIndexReferencePriceVO> priceEffectIndexReferencePriceVOS = referencePriceResp.getData().get(selectedStoreSku.getSkuId());
                Map<String, List<ReferencePriceDataVO>> referencePriceMap = priceEffectIndexReferencePriceVOS.stream()
                        .collect(Collectors.toMap(PriceEffectIndexReferencePriceVO::getPriceEffectIndexType, PriceEffectIndexReferencePriceVO::getReferencePriceVOList));
                result.setReferencePriceIndexMap(referencePriceMap);
            }
        } catch (Exception e) {
            //忽略查询定价助手指标的异常，异常时保证价格策略正确返回。
            log.warn("查询参考价指标异常", e);
        }
    }

    private StoreSkuVO selectAdjustPriceSku(StoreSpuVO storeSpuVO, String skuId) {
        List<StoreSkuVO> storeSkuList = storeSpuVO.getStoreSkuList();
        if (storeSpuVO == null ||
                CollectionUtils.isEmpty(storeSkuList) ||
                CollectionUtils.isEmpty(storeSpuVO.getChannelSpuList())) {
            throw new CommonLogicException("没有可以改价的商品", ResultCode.FAIL);
        }

        Map<String, ChannelSkuVO> skuIdAndChannelSkuVO = storeSpuVO.getChannelSpuList()
                .stream()
                .filter(channelSpu -> ChannelType.MEITUAN.getValue() == channelSpu.getChannelId() &&
                        CollectionUtils.isNotEmpty(channelSpu.getChannelSkuList()))
                .map(ChannelSpuVO::getChannelSkuList)
                .flatMap(Collection::stream)
                .collect(Collectors.toMap(ChannelSkuVO::getSkuId, Function.identity(), (o1, o2) -> o2));
        List<StoreSkuVO> filterSkus = storeSkuList.stream()
                .filter(storeSkuVO -> skuIdAndChannelSkuVO.containsKey(storeSkuVO.getSkuId()))
                .collect(Collectors.toList());

        if (StringUtils.isNotBlank(skuId)) {
            return filterSkus.stream()
                    .filter(storeSku -> storeSku.getSkuId().equals(skuId))
                    .findFirst()
                    .orElseThrow(() -> new CommonLogicException("找不到传入sku对应的商品", ResultCode.FAIL));
        }

        //单规格直接返回
        if (filterSkus.size() == 1) {
            return filterSkus.get(0);
        }
        //多规格 均无合法重量 提示当前商品信息异常，商品重量为空，请维护后再改价
        if (filterSkus.size() > 1) {
            return filterSkus.stream()
                    .filter(storeSkuVO -> Objects.nonNull(storeSkuVO.getWeight()))
                    .sorted(Comparator.comparing(StoreSkuVO::getWeight))
                    .findFirst()
                    .orElseThrow(() -> new CommonLogicException("当前商品信息异常，商品重量为空，请维护后再改价", ResultCode.FAIL));
        }
        throw new CommonLogicException("没有可以改价的商品", ResultCode.FAIL);
    }

    private SkuHitSyncStrategyDTO getSkuHitSyncStrategyDTO(Long storeId, Long tenantId, StoreSkuVO selectedStoreSku) {
        SkuBaseInfoDTO skuBaseInfoDTO = new SkuBaseInfoDTO();
        skuBaseInfoDTO.setStoreId(storeId);
        skuBaseInfoDTO.setChannelId(ChannelType.MEITUAN.getValue());
        skuBaseInfoDTO.setSkuId(selectedStoreSku.getSkuId());
        skuBaseInfoDTO.setOfflinePrice(selectedStoreSku.getStorePrice());

        QuerySkuHitSyncStrategyDetailRequest request = new QuerySkuHitSyncStrategyDetailRequest();
        request.setTenantId(tenantId);
        request.setSkuDTOS(Lists.newArrayList(skuBaseInfoDTO));

        try {
            QueryHitPriceStrategyResponse priceStrategyResponse = priceStrategyThriftService.querySyncStrategyDetails(request);
            if (priceStrategyResponse.getCode() != ResultCode.SUCCESS.getCode()) {
                throw new CommonLogicException(priceStrategyResponse.getMsg(), ResultCode.FAIL);
            }
            return Optional.ofNullable(priceStrategyResponse)
                    .map(QueryHitPriceStrategyResponse::getSkuHitSyncStrategyDTOS)
                    .map(List::iterator).map(Iterator::next)
                    .orElseThrow(() -> new CommonLogicException("获取sku提价策略失败", ResultCode.FAIL));
        } catch (Exception e) {
            log.error("获取sku提价策略失败", e);
            throw new CommonLogicException(e);
        }
    }


    private StoreSpuVO findStoreSpuDetail(User user, Long storeId, String spuId) {

        try {
            // 构建查询参数
            StoreSpuDetailRequest storeSpuDetailRequest = PriceThriftInfoConverter.buildStoreSpuDetailRpcRequest(user, storeId, spuId);

            // 查询门店SPU详情
            StoreSpuDetailResponse storeSpuDetailResponse = storeSpuThriftService.findStoreSpuDetail(storeSpuDetailRequest);
            log.info("StoreSpuThriftService findStoreSpuDetail, request:{}, response:{}", storeSpuDetailRequest, storeSpuDetailResponse);

            if (storeSpuDetailResponse.getCode() != ResultCode.SUCCESS.getCode()) {
                throw new CommonLogicException(storeSpuDetailResponse.getMsg(), ResultCode.FAIL);
            }
            StoreSpuVO storeSpuVO = StoreSpuVO.ofDTO(storeSpuDetailResponse.getStoreSpu(), null);
            try {
                priceConsistencyHelper.addPriceConsistencyAndPromotionInfo(user.getTenantId(), storeSpuVO);
            } catch (Exception e) {
                log.warn("添加价格不一致信息失败", e);
            }
            return storeSpuVO;
        } catch (TException e) {
            log.error("StoreSpuThriftService findStoreSpuDetail exception", e);
            throw new CommonRuntimeException(e);
        }
    }

    private QueryHitPriceStrategyResponse querySkuSyncStrategyDetails(User user, Long storeId, StoreSpuVO storeSpuVO) {

        if (storeSpuVO == null || CollectionUtils.isEmpty(storeSpuVO.getStoreSkuList())
                || CollectionUtils.isEmpty(storeSpuVO.getChannelSpuList())) {
            return new QueryHitPriceStrategyResponse();
        }

        try {
            // 构建查询条件
            QuerySkuHitSyncStrategyDetailRequest detailRequest =
                    PriceThriftInfoConverter.buildSkuHitSyncStrategyDetailRequest(user, storeId, storeSpuVO);

            //
            if (detailRequest == null || CollectionUtils.isEmpty(detailRequest.getSkuDTOS())) {
                log.info("无满足条件的渠道信息");
                return new QueryHitPriceStrategyResponse();
            }

            // 查询商品定价策略
            QueryHitPriceStrategyResponse response = priceStrategyThriftService.querySyncStrategyDetails(detailRequest);
            if (response.getCode() != ResultCode.SUCCESS.getCode()) {
                throw new CommonLogicException(response.getMsg(), ResultCode.FAIL);
            }

            return response;
        } catch (Exception e) {
            log.error("PriceStrategyThriftService querySyncStrategyDetails exception", e);
            throw new CommonRuntimeException(e);
        }
    }

    public List<SkuPurchasePriceVo> querySkuPurchasePriceInfo(StoreSpuVO storeSpuVO) {
        if (storeSpuVO == null || CollectionUtils.isEmpty(storeSpuVO.getStoreSkuList())) {
            return Collections.emptyList();
        }

        List<String> skuIds = storeSpuVO.getStoreSkuList().stream().map(StoreSkuVO::getSkuId).collect(Collectors.toList());

        return querySkuPurchasePriceInfo(storeSpuVO.getTenantId(), storeSpuVO.getStore().getStoreId(), skuIds,true);
    }

    public List<SkuPurchasePriceVo> querySkuPurchasePriceInfo(Long tenantId, Long storeId, List<String> skuIds, Boolean distributionRelationFirst) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyList();
        }

        Map<String, SkuPurchasePriceVo> skuIdPriceMap = ConverterUtils.listStreamMapToMap(skuIds, Function.identity(), skuId -> SkuPurchasePriceVo.build(storeId, skuId));
        try {
            // 检测共享仓门店
            Long maybeShareWmsId = storeId;
            Map<Long, PoiInfoDto> poiInfoDtoMap = tenantWrapper.queryPoiByIds(tenantId, Lists.newArrayList(storeId));
            PoiInfoDto poiInfoDto = poiInfoDtoMap.get(storeId);
            if (poiInfoDto!=null && poiInfoDto.getShippingMode() == PoiShippingModeEnum.SHIP_BY_SHAREABLE_WAREHOUSE.code()) {
                maybeShareWmsId = poiInfoDto.getShareableWarehouseId();
            }
            //查询组合商品
            List<String> composeSkuIds = queryComposeSkuIds(tenantId, maybeShareWmsId, skuIds);
            if (CollectionUtils.isNotEmpty(composeSkuIds)) {
                composeSkuIds.forEach(skuId -> {
                    SkuPurchasePriceVo vo = skuIdPriceMap.get(skuId);
                    if (vo != null) {
                        vo.setIsComposeSku(true);
                    }
                });
            }
            // 查询非组合商品采购价
            List<String> unComposeSkuIds = skuIdPriceMap.values().stream()
                    .filter(sku -> !sku.getIsComposeSku())
                    .map(SkuPurchasePriceVo::getSkuId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(unComposeSkuIds)) {
                return Lists.newArrayList(skuIdPriceMap.values());
            }


            List<PurchasePriceDto> purchasePriceList = queryStoreSkuIdsSupplyPrice(tenantId, maybeShareWmsId, unComposeSkuIds,distributionRelationFirst);
            if (CollectionUtils.isNotEmpty(purchasePriceList)) {
                purchasePriceList.forEach(priceDto -> {
                    SkuPurchasePriceVo vo = skuIdPriceMap.get(priceDto.getSkuId());
                    if (vo == null) {
                        return;
                    }
                    vo.setMasterPurchasePrice(priceDto.getMasterPurchasePrice());
                    vo.setBaseChannelGoodsPrice(priceDto.getChannelGoodsPrice());
                    vo.setLatestPurchasePrice(priceDto.getLatestPurchasePrice());
                    vo.setDefaultSupplierType(priceDto.getChannel());
                    vo.setSupplierName(priceDto.getSupplierName());
                    vo.setLatestTime(DateUtil.getStringDateFromMilliSeconds(priceDto.getLatestTime(), DateUtil.YYYY_MM_DD));
                    // 若入参为共享仓门店，则转为共享仓ID查询采购域，输出需要还原为入参门店id
                    vo.setStoreId(storeId);
                });
            }
            //填充最近下单价和加权库存成本价
            Map<String, QuerySkuPurchasePriceInfoResponse.SkuPurchasePriceInfo> skuCostAndReceiptPriceInfoMap = querySkuCostAndReceiptPriceInfo(tenantId, maybeShareWmsId, unComposeSkuIds);
            if(MapUtils.isNotEmpty(skuCostAndReceiptPriceInfoMap)){
                for(String skuId:skuCostAndReceiptPriceInfoMap.keySet()){
                    SkuPurchasePriceVo vo = skuIdPriceMap.get(skuId);
                    if (vo == null) {
                        continue;
                    }
                    QuerySkuPurchasePriceInfoResponse.SkuPurchasePriceInfo skuPurchasePriceInfo = skuCostAndReceiptPriceInfoMap.get(skuId);
                    vo.setLastDeliverPrice(skuPurchasePriceInfo.getReceiptPrice());
                    vo.setLastDeliverPriceUnit(skuPurchasePriceInfo.getReceiptPriceUnit());
                    vo.setWeightedInventoryCostPrice(skuPurchasePriceInfo.getCostPrice());
                    vo.setWeightedInventoryCostPriceUnit(skuPurchasePriceInfo.getCostPriceUnit());
                }
            }
        } catch (Exception e) {
            log.error("querySkuPurchasePriceInfo exception", e);
        }
        return Lists.newArrayList(skuIdPriceMap.values());
    }

    private List<String> queryComposeSkuIds(Long tenantId, Long storeId, List<String> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyList();
        }
        List<String> composeSkuIds = Lists.newArrayList();
        Lists.partition(skuIds, SKU_QUERY_BATCH_SIZE).forEach(partKeys -> {
            PoiSkuIdCheckCommand checkCommand = new PoiSkuIdCheckCommand();
            checkCommand.setTenantId(tenantId);
            checkCommand.setStoreId(storeId);
            checkCommand.setSkuIdList(partKeys);
            try {
                PoiSkuIdListCheckResult result = empowerPoiComposeSkuThriftService.checkSkuIdsForComposeSku(checkCommand);
                log.info("checkSkuIdsForComposeSku command:{} result:{}", checkCommand, result);
                if (result == null
                        || result.getStatus() == null
                        || result.getStatus().getCode() != 0) {
                    return;
                }
                if (CollectionUtils.isNotEmpty(result.getParentSkuIds())) {
                    composeSkuIds.addAll(result.getParentSkuIds());
                }
            } catch (Exception e) {
                log.error("checkSkuIdsForComposeSku error, command:{}", checkCommand, e);
            }
        });
        return composeSkuIds;
    }

    private List<PurchasePriceDto> queryStoreSkuIdsSupplyPrice(Long tenantId, Long storeId, List<String> skuIds,Boolean distributionRelationFirst) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyList();
        }
        List<PurchasePriceDto> priceList = Lists.newArrayList();
        Lists.partition(skuIds, SKU_QUERY_BATCH_SIZE).forEach(partKeys -> {
            PurchasePriceBatchQueryReq req = new PurchasePriceBatchQueryReq();
            req.setTenantId(tenantId);
            req.setStoreId(storeId);
            req.setSkuIds(partKeys);
            req.setDistributionRelationFirst(distributionRelationFirst);
            PurchasePriceBatchQueryResp resp = supplyRelationQueryThriftServiceClient.batchQuerySupplyPrice(req);
            log.info("batchQuerySupplyPrice req:{} resp:{}", req, resp);
            if (resp == null
                    || resp.getStatus() == null
                    || !Status.SUCCESS.getCode().equals(resp.getStatus().getCode())
                    || CollectionUtils.isEmpty(resp.getPurchasePriceDtoList())) {
                return;
            }
            priceList.addAll(resp.getPurchasePriceDtoList());
        });
        return priceList;
    }


    /**
     * 查询最近下单价与库存成本价
     * @param tenantId
     * @param storeId
     * @param skuIds
     * @return
     */
    private Map<String, QuerySkuPurchasePriceInfoResponse.SkuPurchasePriceInfo> querySkuCostAndReceiptPriceInfo(Long tenantId,Long storeId,
                                                                                                   List<String> skuIds) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        Map<String, QuerySkuPurchasePriceInfoResponse.SkuPurchasePriceInfo> skuPurchaseInfoMap = new HashMap<>();
        try {
            //查询加权成本价
            UserContext userContext = new UserContext();
            userContext.setTenantId(tenantId);
            userContext.setAccountId(user.getAccountId());
            userContext.setAccountName(user.getAccountName());
            ThriftResult<QuerySkuPurchasePriceInfoResponse> querySkuPurchasePriceInfoResponseThriftResult = purchasePriceHistoryQueryThriftService.querySkuPurchasePriceInfo(QuerySkuPurchasePriceInfoRequest.builder()
                    .storeId(storeId)
                    .skuIdList(skuIds).build(),
                    userContext);

            Optional.ofNullable(querySkuPurchasePriceInfoResponseThriftResult).map(ThriftResult::getData)
                    .map(QuerySkuPurchasePriceInfoResponse::getSkuList).ifPresent(list -> {
                        list.forEach(sku -> skuPurchaseInfoMap.put(sku.getSkuId(), sku));
                    });
        } catch (Exception e) {
            log.error("querySkuCostAndReceiptPriceInfo is error,tenantId={},storeId={},skuIds={}",tenantId,storeId,skuIds, e);
        }
        return skuPurchaseInfoMap;
    }


    public void updateSkuPriceSyncStrategy(Long tenantId, Long storeId, Long operatorId, List<ChannelSyncStrategyDTO>
            channelSyncStrategyList) {
        com.sankuai.meituan.shangou.empower.price.client.request.strategy.UpdateSkuPriceSyncStrategyRequest request = new com
                .sankuai.meituan.shangou.empower.price.client.request.strategy.UpdateSkuPriceSyncStrategyRequest();
        request.setTenantId(tenantId);
        request.setStoreId(storeId);
        request.setOperatorId(operatorId);
        request.setChannelSyncStrategyList(channelSyncStrategyList);
        try {
            PriceCommonResponse response = priceStrategyThriftService.updateSkuPriceSyncStrategy(request);
            log.info("priceAppPriceStrategyThriftService.updateSkuPriceSyncStrategy, command:{}, result:{}", request, response);
            if (response == null) {
                throw new BizException(-1, "保存价格策略失败, 请稍后重试！");
            }
            if (response.getCode() != 0) {
                throw new BizException(-1, response.getMsg());
            }
        } catch (Exception e) {
            log.error("priceAppPriceStrategyThriftService.updateSkuPriceSyncStrategy exception, command:{}", request, e);
            throw new BizException(-1, "保存价格策略失败, 请稍后重试！");
        }
    }


}
