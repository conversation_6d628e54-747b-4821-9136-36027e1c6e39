package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.category.StoreGroupCategoryDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreGroupCategoryCodeDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreGroupCategoryInfoDTO;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/2/23
 */
@Data
@TypeDoc(
        description = "门店分组的店内分类"
)
public class StoreGroupMerchantStoreCategoryVO {

    @FieldDoc(
            description = "门店分组ID"
    )
    private Integer storeGroupId;

    @FieldDoc(
            description = "店内分类"
    )
    private List<MerchantStoreCategoryVO> storeCategoryList;

    public static StoreGroupMerchantStoreCategoryVO of(StoreGroupCategoryDTO storeGroupCategoryCodeDTO) {
        StoreGroupMerchantStoreCategoryVO storeGroupMerchantStoreCategoryVo = new StoreGroupMerchantStoreCategoryVO();
        storeGroupMerchantStoreCategoryVo.setStoreGroupId(storeGroupCategoryCodeDTO.getStoreGroupId());
        if (CollectionUtils.isNotEmpty(storeGroupCategoryCodeDTO.getStoreCategoryList())) {
            storeGroupMerchantStoreCategoryVo.setStoreCategoryList(storeGroupCategoryCodeDTO.getStoreCategoryList().stream()
                    .map(MerchantStoreCategoryVO::of).collect(Collectors.toList()));
        }
        return storeGroupMerchantStoreCategoryVo;
    }

    public static StoreGroupMerchantStoreCategoryVO ofBiz(StoreGroupCategoryInfoDTO storeGroupCategoryInfoDTO) {
        StoreGroupMerchantStoreCategoryVO storeGroupMerchantStoreCategoryVo = new StoreGroupMerchantStoreCategoryVO();
        storeGroupMerchantStoreCategoryVo.setStoreGroupId(storeGroupCategoryInfoDTO.getStoreGroupId());
        if (CollectionUtils.isNotEmpty(storeGroupCategoryInfoDTO.getStoreCategoryList())) {
            storeGroupMerchantStoreCategoryVo.setStoreCategoryList(storeGroupCategoryInfoDTO.getStoreCategoryList().stream()
                    .map(MerchantStoreCategoryVO::ofBiz).collect(Collectors.toList()));
        }
        return storeGroupMerchantStoreCategoryVo;
    }
}
