package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.revenue;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2019-06-18
 * @description
 */
@TypeDoc(
        description = "摊位历史账单按月查询响应——分页查询",
        version = "1.0"
)
@ApiModel("摊位历史账单按月查询响应——分页查询")
@Data
public class BoothHistorySettlementMonthlyPageVO {

    @FieldDoc(
            description = "是否有更多数据", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否有更多数据", required = true)
    @NotNull
    private Boolean hasMore;

    @FieldDoc(description = "门店摊位按月账单汇总")
    @ApiModelProperty(value = "门店摊位按月账单汇总")
    private List<BoothMonthlySettlementSummary> boothMonthlySettlementSummaryList;


    @FieldDoc(description = "下一页有数据的页码")
    @ApiModelProperty(value = "下一页有数据的页码")
    private Integer nextPageNo;

    @TypeDoc(
            description = "门店摊位按月营收汇总",
            version = "1.0"
    )
    @ApiModel("门店摊位按月营收汇总")
    @Data
    public static class BoothMonthlySettlementSummary {

        @FieldDoc(description = "月份")
        @ApiModelProperty(value = "月份")
        private String settleMonth;

        @FieldDoc(description = "营收金额(分)")
        @ApiModelProperty(value = "营收金额(分)")
        private Long revenueAmount;

        @FieldDoc(description = "有效订单数量")
        @ApiModelProperty(value = "有效订单数量")
        private Long totalOrderCount;

        @FieldDoc(description = "门店摊位单日营收明细")
        @ApiModelProperty(value = "门店摊位按月账单明细")
        private List<BoothMonthlySettlementDetail> boothMonthlySettlementDetailList;

        @FieldDoc(description = "营收金额文案(非摊主账号时显示在摊位明细的表头)")
        @ApiModelProperty(value = "营收金额文案")
        private String totalRevenueComment;
    }


    @TypeDoc(
            description = "门店摊位按月账单明细",
            version = "1.0"
    )
    @ApiModel("门店摊位按月账单明细")
    @Data
    public static class BoothMonthlySettlementDetail {

        @FieldDoc(description = "摊位id")
        @ApiModelProperty(value = "摊位id")
        private Long boothId;

        @FieldDoc(description = "摊位名称")
        @ApiModelProperty(value = "摊位名称")
        private String boothName;

        @FieldDoc(description = "营收金额(分)")
        @ApiModelProperty(value = "营收金额(分)")
        private Long revenueAmount;

        @FieldDoc(description = "商品金额(分)")
        @ApiModelProperty(value = "商品金额(分)")
        private Long productTotalAmount;

        @FieldDoc(description = "有效订单数量")
        @ApiModelProperty(value = "有效订单数量")
        private Long totalOrderCount;

        @FieldDoc(description = "结算状态")
        @ApiModelProperty(value = "结算状态")
        private Integer settleStatus;

        @FieldDoc(description = "结算状态描述")
        @ApiModelProperty(value = "结算状态描述")
        private String settleStatusDesc;
    }
}
