/*
 * Copyright (c) 2019 Meituan.com. All Rights Reserved.
 */
package com.sankuai.meituan.reco.shopmgmt.pieapi.constants;

/**
 * <br><br>
 * Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <br>
 * Date: 2019-04-08 Time: 16:25
 */
public enum MccKeyEnum {

    MRN_HOST("mrn.check.url.host", "首页模块mrn校验host"),
    OTHER_MODULE_NAME("other.module.name", "其他模块名称"),
    OTHER_MODULE_ICON("other.module.icon", "其他模块图标"),
    MARKET_SURVEY_DEFAULT_UNIT("market.survey.default.unit", "市调默认价格单位"),
    PICKTASK_PENDING_TASK_SWITCH("picktask.pending.task.switch", "首页模块拣货开关"),
    PICKTASK_PENDING_TASK_RED_DOT("picktask.pending.task.red.dot", "首页模块拣货角标类型"),
    CASHIER_REVENUE_HOME_TITLE("cashier.revenue.home.title", "线下收银营收首页标题"),
    CHANNEL_ABBREVIATION("channel_abbreviation", "渠道简称"),
    FULL_STORE_STRATEGY_SWITCH("full.store.strategy", "全部门店策略（默认按超过两个门店展示逻辑）"),
    FULL_STORE_APP_VERSION("full.store.app.version", "全部门店支持的app版本"),
    COMMON_MODULE_NAME("common.module.name", "首页公共模块名称"),
    QUOTE_AUDIT_RESOURCE_CODE("quote.audit.resource.code", "提报价审核权限code"),
    STORE_MANAGER_TITLE("store.manager.title.config", "门店管理者的称呼，如：店长"),
    SHOW_ORDER_NET_PROFIT_CHANNEL("show.order.net.profit.channel.config", "展示订单毛利渠道配置")
    ;

    public final String key;
    public final String desc;

    MccKeyEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
