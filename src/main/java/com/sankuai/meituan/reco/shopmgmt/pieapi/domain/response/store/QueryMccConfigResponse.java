package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.barcode.dto.BarcodeConfig;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@TypeDoc(
        description = "获取mcc库位配置信息响应"
)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryMccConfigResponse {
    @FieldDoc(
            description = "mcc配置信息列表"
    )
    private List<MccConfigVO> mccConfigVOList;

    @FieldDoc(
            description = "扫码解析规则"
    )
    private List<BarcodeConfig> barcodeConfigList;

    @TypeDoc(
            description = "mcc库位配置信息"
    )
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MccConfigVO {
        @FieldDoc(
                description = "配置名字"
        )
        private String configName;
        @FieldDoc(
                description = "枚举值"
        )
        private Integer enumValue;
        @FieldDoc(
                description = "枚举集合"
        )
        private List<Integer> enumCollection;
    }
}
