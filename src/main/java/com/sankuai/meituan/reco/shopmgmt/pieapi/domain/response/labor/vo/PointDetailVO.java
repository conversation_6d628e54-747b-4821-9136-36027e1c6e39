package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023-03-15
 * @email <EMAIL>
 */
@ApiModel("积分明细vo")
@TypeDoc(description = "积分明细vo")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PointDetailVO {

    @FieldDoc(description = "文案")
    @ApiModelProperty("文案")
    public String pointText;


    @FieldDoc(description = "时间")
    @ApiModelProperty("时间")
    public Long pointTime;


    @FieldDoc(description = "获得积分")
    @ApiModelProperty("获得积分")
    public String gainPoints;


    @FieldDoc(description = "加上本次积分后的积分")
    @ApiModelProperty("加上本次积分后的积分")
    public String nowTotalPoints;

}
