package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.wavepick.converter;

import com.sankuai.meituan.reco.pickselect.thrift.wave.request.WarehouseFuzzySeedSkuRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick.WarehouseSeedFuzzySkuRequest;
import org.mapstruct.Mapper;

/**
 * @Description:
 * @Author: zhangjian155
 * @Date: 2022/8/25 18:27
 */
@Mapper(componentModel = "spring")
public abstract class WarehouseSeedFuzzySkuRequestConverter {
    public abstract WarehouseFuzzySeedSkuRequest convert2ThriftRequest(WarehouseSeedFuzzySkuRequest request,
                                                                       Long tenantId, Long accountId, Long storeId);
}
