package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.impl;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.facade.SealContainerOpLogFacade;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.AbstractSinglePendingTaskService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/8 15:09
 **/
@Slf4j
@Service
public class SealContainerReturnPendingTaskService extends AbstractSinglePendingTaskService {
    @Resource
    private SealContainerOpLogFacade sealContainerOpLogFacade;

    @Override
    protected PendingTaskResult getPendingTaskCount(PendingTaskParam param) throws Exception {
        Integer usingSealContainerCount = 0;
        if (MccConfigUtil.isSealContainerReturnTimeoutNotifyGrayStore(param.getStoreIds().get(0))) {
            usingSealContainerCount = sealContainerOpLogFacade.queryWarehouseUsingSealContainerCount(param.getTenantId(), param.getStoreIds().get(0));
        }


        return PendingTaskResult.createNumberMarker(usingSealContainerCount);
    }

    @Override
    protected AuthCodeEnum module() {
        return AuthCodeEnum.RETURN_SEAL_CONTAINER;
    }
}
