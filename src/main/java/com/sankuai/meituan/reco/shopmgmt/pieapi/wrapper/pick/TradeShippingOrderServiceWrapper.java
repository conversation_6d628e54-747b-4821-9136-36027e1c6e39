package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.pick;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.order.management.client.dto.request.online.ViewIdCondition;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.logistics.warehouse.TradeShippingOrderService;
import com.sankuai.shangou.logistics.warehouse.dto.TradeShippingOrderDTO;
import com.sankuai.shangou.logistics.warehouse.enums.TradeShippingOrderStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-01-28
 * @email <EMAIL>
 */
@Slf4j
@Rhino
public class TradeShippingOrderServiceWrapper {

    @Resource
    private TradeShippingOrderService tradeShippingOrderService;

    @MethodLog(logRequest = true, logResponse = true)
    public List<TradeShippingOrderDTO> getRecentListByOperatorIdAndStatusList(long warehouseId, @Nullable Long accountId, List<Integer> statusList) {
        TResult<List<TradeShippingOrderDTO>> recentListResult = tradeShippingOrderService.getRecentListByOperatorIdAndStatusList(
                warehouseId, accountId, statusList
        );
        if (!recentListResult.isSuccess()) {
            throw new CommonRuntimeException("查询出库单失败");
        }
        return recentListResult.getData();
    }

    @MethodLog(logRequest = true, logResponse = true)
    public List<TradeShippingOrderDTO> getByTradeOrderNos(long warehouseId, int orderBizType, List<String> channelOrderIds) {
        TResult<List<TradeShippingOrderDTO>> recentListResult = tradeShippingOrderService.getByTradeOrderNos(
                warehouseId, orderBizType, channelOrderIds
        );
        if (!recentListResult.isSuccess()) {
            throw new CommonRuntimeException("查询出库单失败");
        }
        return recentListResult.getData();
    }

    @Degrade(rhinoKey = "TradeShippingOrderServiceWrapper.queryByTradeOrderIds", fallBackMethod = "queryByTradeOrderIdsFallback", timeoutInMilliseconds = 2000)
    @MethodLog(logRequest = true, logResponse = true)
    public List<TradeShippingOrderDTO> queryByTradeOrderIds(long warehouseId, List<ViewIdCondition> viewIdConditionList) {
        if (CollectionUtils.isEmpty(viewIdConditionList)) {
            return Lists.newArrayList();
        }
        Map<Integer, List<ViewIdCondition>> orderBizTypeMap = viewIdConditionList.stream().collect(Collectors.groupingBy(ViewIdCondition::getOrderBizType));
        List<TradeShippingOrderDTO> res = Lists.newArrayList();
        for (Map.Entry<Integer, List<ViewIdCondition>> entry : orderBizTypeMap.entrySet()) {
            TResult<List<TradeShippingOrderDTO>> result = tradeShippingOrderService.getByTradeOrderNos(warehouseId, entry.getKey(),
                    IListUtils.mapTo(entry.getValue(), ViewIdCondition::getViewOrderId));
            if (!result.isSuccess()) {
                throw new ThirdPartyException("查询拣货出库单失败");
            }
            res.addAll(Optional.ofNullable(result.getData()).orElse(Lists.newArrayList()));
        }
        return res;
    }

    public List<TradeShippingOrderDTO> queryByTradeOrderIdsFallback(long warehouseId, List<ViewIdCondition> viewIdConditionList) {
        log.error("TradeShippingOrderServiceWrapper.queryByTradeOrderIds 发生降级");
        return Collections.emptyList();
    }

    @MethodLog(logRequest = true, logResponse = true)
    public void startShip(Long warehouseId, String tradeOrderNo, Integer tradeChannelType,Long operatorAccountId, String operatorEmpName) {
        tradeShippingOrderService.startShipByTradeOrderNo(warehouseId, tradeOrderNo, tradeChannelType, operatorAccountId, operatorEmpName);
    }


}
