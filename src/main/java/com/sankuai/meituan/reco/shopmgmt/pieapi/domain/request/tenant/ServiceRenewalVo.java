package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.tenant;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.order.platform.common.SelfCheckable;
import lombok.Data;

import javax.validation.constraints.NotNull;


/**
 * <AUTHOR>
 */
@Data
@TypeDoc(
        description = "续费详情"
)
public class ServiceRenewalVo implements SelfCheckable {

    @FieldDoc(description = "服务id")
    @NotNull(message = "服务id不能为空")
    public Long serviceId;

    @FieldDoc(description = "服务类型")
    @NotNull(message = "服务类型不能为空")
    public Integer serviceType;

    @FieldDoc(description = "子服务类型")
    @NotNull(message = "子服务类型不能为空")
    public Integer subType;

    @FieldDoc(description = "购买数量")
    @NotNull(message = "购买数量不能为空")
    public Integer unitNum;
}
