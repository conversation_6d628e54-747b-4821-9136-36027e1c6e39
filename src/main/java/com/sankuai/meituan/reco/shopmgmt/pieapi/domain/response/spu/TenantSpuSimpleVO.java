package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.TenantSpuBizDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2020-12-09 14:20
 * @Description:
 */
@TypeDoc(
        description = "总部商品SpU简略信息"
)
@Data
@ApiModel("总部商品SpU简略信息")
public class TenantSpuSimpleVO {

    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户ID", required = true)
    private Long tenantId;

    @FieldDoc(
            description = "商品spuId", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品spu")
    private String spuId;

    @FieldDoc(
            description = "租户商品规格信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户商品规格信息")
    private List<TenantSkuVO> tenantSkuVOList;

    @FieldDoc(
            description = "是否允许自定义规格描述", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "是否允许自定义规格描述")
    private Boolean canCustomizeSpec;

    public static TenantSpuSimpleVO of(TenantSpuBizDTO tenantSpuBizDTO) {
        TenantSpuSimpleVO tenantSpuSimpleVO = new TenantSpuSimpleVO();
        tenantSpuSimpleVO.setTenantId(tenantSpuBizDTO.getTenantId());
        tenantSpuSimpleVO.setSpuId(tenantSpuBizDTO.getSpuId());
        tenantSpuSimpleVO.setCanCustomizeSpec(tenantSpuBizDTO.getAllowCustomizeSpec());
        if (CollectionUtils.isNotEmpty(tenantSpuBizDTO.getTenantSkuDTOList())) {
            tenantSpuSimpleVO.setTenantSkuVOList(Fun.map(tenantSpuBizDTO.getTenantSkuDTOList(), TenantSkuVO::fromBizDTO));
        }
        return tenantSpuSimpleVO;
    }
}
