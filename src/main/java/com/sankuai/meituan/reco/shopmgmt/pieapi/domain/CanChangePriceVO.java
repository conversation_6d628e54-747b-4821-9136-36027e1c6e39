package com.sankuai.meituan.reco.shopmgmt.pieapi.domain;

import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MoneyUtils;
import com.sankuai.meituan.shangou.empower.price.client.dto.CanChangePriceSkuDTO;
import com.sankuai.meituan.shangou.empower.price.client.dto.present_price.PresentChannelPriceEqualSignDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: wangyihao04
 * @Date: 2021-05-20 16:01
 * @Mail: <EMAIL>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CanChangePriceVO {
    private ChannelStoreSkuKey skuKey;
    private Boolean canChangePrice;

    public static CanChangePriceVO valueOf(CanChangePriceSkuDTO dto) {
        return CanChangePriceVO.builder()
                .skuKey(ChannelStoreSkuKey.convertTo(dto.getSkuKey()))
                .canChangePrice(dto.getCanChangePrice())
                .build();

    }
}
