package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.google.common.collect.Maps;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.CompareSpuTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2022/2/28 3:45 下午
 **/
@TypeDoc(
        description = "二级商品类型统计"
)
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class ProblemSpuTypeVo {

    @FieldDoc(
            description = "二级分类：key:类型（101商家端商品缺失;102蔬果派商品缺失;201商家端规格缺失;202蔬果派规格缺失;301基本信息不一致;401价格不一致) value:数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "类型数量统计", required = true)
    private Map<Integer, Integer> typeCount;

    @FieldDoc(
            description = "一级分类统计数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "一级分类统计数量", required = true)
    private Integer count;


    public static ProblemSpuTypeVo ofSpuMiss(Integer waimaiSpuMiss, Integer sgpSpuMiss) {
        Map<Integer, Integer> spuMissMap = Maps.newHashMap();
        spuMissMap.put(CompareSpuTypeEnum.WAIMAI_SPU_MISS.getCode(), waimaiSpuMiss);
        spuMissMap.put(CompareSpuTypeEnum.SGP_SPU_MISS.getCode(), sgpSpuMiss);
        return new ProblemSpuTypeVo(spuMissMap, waimaiSpuMiss + sgpSpuMiss);
    }

    public static ProblemSpuTypeVo ofSpecMiss(Integer waimaiSpecMiss, Integer sgpSpecMiss) {
        Map<Integer, Integer> specMissMap = Maps.newHashMap();
        specMissMap.put(CompareSpuTypeEnum.WAIMAI_SPEC_MISS.getCode(), waimaiSpecMiss);
        specMissMap.put(CompareSpuTypeEnum.SGP_SPEC_MISS.getCode(), sgpSpecMiss);
        return new ProblemSpuTypeVo(specMissMap, waimaiSpecMiss + sgpSpecMiss);
    }


    /**
     *
     * @param baseDiff 基础信息不一致数量
     * @param salesDiff 销售信息不一致数量
     * @return
     */
    public static ProblemSpuTypeVo ofBaseDiff(Integer baseDiff, Integer salesDiff) {
        Map<Integer, Integer> baseDiffMap = Maps.newHashMap();
        baseDiffMap.put(CompareSpuTypeEnum.BASE_INFO_DIFF.getCode(), baseDiff);
        baseDiffMap.put(CompareSpuTypeEnum.SALSE_INFO_DIFF.getCode(), salesDiff);
        return new ProblemSpuTypeVo(baseDiffMap, baseDiff + salesDiff);

    }

    public static ProblemSpuTypeVo ofPriceDiff(Integer priceDiff) {
        Map<Integer, Integer> specMissMap = Maps.newHashMap();
        specMissMap.put(CompareSpuTypeEnum.PRICE_DIFF.getCode(), priceDiff);
        return new ProblemSpuTypeVo(specMissMap, priceDiff);

    }
}
