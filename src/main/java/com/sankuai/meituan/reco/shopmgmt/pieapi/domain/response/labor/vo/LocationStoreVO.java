package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022-10-19
 * @email <EMAIL>
 */
@TypeDoc(
        description = "绑定门店位置信息"
)
@ApiModel("绑定门店位置信息")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LocationStoreVO {

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店id")
    private Long storeId;

    @FieldDoc(
            description = "门店名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @FieldDoc(
            description = "经度", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "经度")
    private String longitude;

    @FieldDoc(
            description = "纬度", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "纬度")
    private String latitude;

    @FieldDoc(
            description = "允许打卡范围", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "允许打卡范围")
    private int allowCheckinRange;

    @FieldDoc(
            description = "地址描述", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "地址描述")
    private String description;
}
