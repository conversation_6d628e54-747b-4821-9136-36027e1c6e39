package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.statistic;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/8/23 18:28
 **/
@ApiModel("指定时间段内的骑手配送统计数据 VO")
@TypeDoc(description = "指定时间段内的骑手配送统计数据 VO")
@Data
public class RiderDeliveryStatisticVO {

    @ApiModelProperty("数据时间范围-开始时间 格式:YYYYMMDD")
    @FieldDoc(description = "数据时间范围-开始时间 格式:YYYYMMDD")
    private String beginTime;

    @ApiModelProperty("数据时间范围-结束时间 格式:YYYYMMDD")
    @FieldDoc(description = "数据时间范围-结束时间 格式:YYYYMMDD")
    private String endTime;

    @ApiModelProperty("已完成订单数")
    @FieldDoc(description = "已完成订单数")
    private Integer completeCount;

    @ApiModelProperty("超时订单数")
    @FieldDoc(description = "超时订单数")
    private Integer timeoutCount;

    @ApiModelProperty("25分钟送达率 单位:% 结果保留两位小数")
    @FieldDoc(description = "25分钟送达率 单位:% 结果保留两位小数")
    private String deliveredRateIn25min;

    @ApiModelProperty("送达前取消订单数")
    @FieldDoc(description = "送达前取消订单数")
    private Integer cancelBeforeDeliveredCount;


    @ApiModelProperty("提前点送达订单数")
    @FieldDoc(description = "提前点送达订单数")
    private Integer earlyClickDeliveredCount;

    @ApiModelProperty("配送风控订单数")
    @FieldDoc(description = "配送风控订单数")
    private Integer riskControlCount;


    @ApiModelProperty("平均履约时长 单位:秒 结果保留六位小数")
    @FieldDoc(description = "平均履约时长 单位:秒")
    private String avgFulfillDuration;


    @ApiModelProperty("超时率 单位:% 结果保留两位小数")
    @FieldDoc(description = "超时率 单位:% 结果保留两位小数")
    private String timeoutRate;

    @ApiModelProperty("ETA超时订单量")
    @FieldDoc(description = "ETA超时订单量")
    private Integer etaOvertimeOrdNumV2;

    @ApiModelProperty("平均履约时长 单位:秒 结果保留六位小数")
    @FieldDoc(description = "ETA超时率 单位:% 结果保留两位小数")
    private String etaOvertimeRatioV2;


}
