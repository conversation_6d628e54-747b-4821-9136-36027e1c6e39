package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.dianping.cat.Cat;
import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDeliveryOrder;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.delivery.questionnaire.DeliveryQuestionnaireThriftService;
import com.sankuai.shangou.logistics.delivery.questionnaire.dto.DeliveryQuestionnaireDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/12/1 18:00
 **/
@Slf4j
@Rhino
public class DeliveryQuestionnaireWrapper {
    @Resource
    DeliveryQuestionnaireThriftService deliveryQuestionnaireThriftService;

    @Degrade(rhinoKey = "DeliveryQuestionnaireWrapper.queryDeliveryQuestionnaireMap", fallBackMethod = "queryDeliveryQuestionnaireMapFallBack", timeoutInMilliseconds = 1000)
    public Map<Long, List<DeliveryQuestionnaireDTO>> queryDeliveryQuestionnaireMap(List<TRiderDeliveryOrder> deliveryOrderlist) {
        try {
            List<Long> deliveryOrderIdList = deliveryOrderlist.stream()
                    .map(TRiderDeliveryOrder::getDeliveryOrderId)
                    .collect(Collectors.toList());

            TResult<List<DeliveryQuestionnaireDTO>> tResult = deliveryQuestionnaireThriftService.queryQuestionnaireByDeliveryOrderIds(deliveryOrderIdList);

            if (!tResult.isSuccess()) {
                log.warn("查询问卷信息失败");
                Cat.logEvent("DELIVERY_QUESTIONNAIRE", "QUERY_FAIL");
                return Collections.emptyMap();
            }

            return tResult.getData().stream().collect(Collectors.groupingBy(DeliveryQuestionnaireDTO::getDeliveryOrderId));
        } catch (Exception e) {
            log.error("查询问卷信息失败", e);
            Cat.logEvent("DELIVERY_QUESTIONNAIRE", "QUERY_ERROR");
            return Collections.emptyMap();
        }
    }

    public Map<Long, List<DeliveryQuestionnaireDTO>> queryDeliveryQuestionnaireMapFallBack(List<TRiderDeliveryOrder> deliveryOrderlist) {
        log.warn("查问卷信息已被降级");
        return Collections.emptyMap();
    }
}
