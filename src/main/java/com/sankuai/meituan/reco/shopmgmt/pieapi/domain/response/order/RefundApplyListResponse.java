package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/11/18
 * desc: 分页查询退款申请列表响应
 */
@TypeDoc(
        description = "分页查询退款申请列表响应"
)
@Data
@ApiModel("分页查询退款申请列表响应")
public class RefundApplyListResponse {

    @FieldDoc(
            description = "分页信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "分页信息", required = true)
    private PageInfoVO pageInfo;

    @FieldDoc(
            description = "退款申请列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "退款申请列表", required = true)
    private List<RefundApplyRecordVO> refundApplyRecordVOList;
}
