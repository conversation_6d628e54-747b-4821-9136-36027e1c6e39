package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.labor;

import com.google.common.collect.Lists;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.sankuai.drunkhorsemgmt.labor.thrift.LaborHireApprovalThriftService;
import com.sankuai.drunkhorsemgmt.labor.thrift.LaborHireThriftService;
import com.sankuai.drunkhorsemgmt.labor.thrift.ResignThriftService;
import com.sankuai.drunkhorsemgmt.labor.thrift.dto.HireApprovalDetailDTO;
import com.sankuai.drunkhorsemgmt.labor.thrift.dto.HireApprovalInfoDTO;
import com.sankuai.drunkhorsemgmt.labor.thrift.page.PageRequest;
import com.sankuai.drunkhorsemgmt.labor.thrift.request.*;
import com.sankuai.drunkhorsemgmt.labor.thrift.response.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor.EditCandidateRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor.QueryApprovalDetailRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor.QueryApprovalListRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor.SearchStoreRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.HireApprovalDetailVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.HireApprovalVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.RoleVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.WorkTypeVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.store.StoreVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.labor.convertor.ApprovalConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-12-08
 * @email <EMAIL>
 */
@Slf4j
@Service
public class LaborHireApprovalServiceWrapper {

    @Resource
    private LaborHireApprovalThriftService laborHireApprovalThriftService;
    @Resource
    private LaborHireThriftService laborHireThriftService;
    @Resource
    private ApprovalConverter approvalConverter;
    @Resource
    private ResignThriftService resignThriftService;

    @MethodLog(logRequest = true, logResponse = true)
    public CommonResponse<QueryApprovalListResponse> queryHireApprovalList(QueryApprovalListRequest request) {
        QueryHireApprovalListReq req = new QueryHireApprovalListReq();
        req.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        req.setAccountId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId());
        req.setEmployeeId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getEmployeeId());
        req.setType(request.getType());
        req.setKeyword(request.getKeyword());
        req.setPageRequest(new PageRequest(request.getPageNo(), request.getPageSize()));
        req.setPoiIds(ApiMethodParamThreadLocal.getIdentityInfo().getStoreIdList());
        QueryHireApprovalListResp resp = laborHireApprovalThriftService.queryHireApprovalList(req);
        if (!resp.getStatus().successful()) {
            return CommonResponse.fail(resp.getStatus().getCode(), resp.getStatus().getMsg());
        }
        return CommonResponse.success(new QueryApprovalListResponse(
                convertToVO(resp.getHireApprovalInfoDTOList()), resp.isHasMore(), resp.getTotalCount()
        ));
    }

    @MethodLog(logRequest = true, logResponse = true)
    public CommonResponse<QueryApprovalDetailResponse> queryHireApprovalDetail(QueryApprovalDetailRequest request) {
        QueryHireApprovalDetailReq req = new QueryHireApprovalDetailReq();
        req.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        req.setEmployeeId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getEmployeeId());
        req.setApprovalId(request.getApprovalId());
        QueryHireApprovalDetailResp resp = laborHireApprovalThriftService.queryHireApprovalDetail(req);
        if (!resp.getStatus().successful()) {
            return CommonResponse.fail(resp.getStatus().getCode(), resp.getStatus().getMsg());
        }

        return CommonResponse.success(
                convertToVO(resp.getHireApprovalDetailDTO())
        );
    }

    @MethodLog(logRequest = true, logResponse = true)
    public CommonResponse<SearchStoreListResponse> searchStore(SearchStoreRequest request) {
        SearchStoreListReq req = new SearchStoreListReq();
        req.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        req.setAccountId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId());
        req.setCityId(request.getCityId());
        req.setStoreNameKeyword(request.getStoreNameKeyword());
        SearchStoreListResp resp = laborHireThriftService.searchStoreList(req);
        if (!resp.getStatus().successful()) {
            return CommonResponse.fail(resp.getStatus().getCode(), resp.getStatus().getMsg());
        }
        return CommonResponse.success(
                new SearchStoreListResponse(
                        Optional.ofNullable(resp.getStoreList())
                                .orElse(Lists.newArrayList())
                                .stream()
                                .map(
                                        storeDTO -> {
                                            StoreVO vo = new StoreVO();
                                            vo.setStoreId(storeDTO.getStoreId());
                                            vo.setStoreName(storeDTO.getStoreName());
                                            return vo;
                                        }
                                ).collect(Collectors.toList())
                )
        );
    }

    @MethodLog(logRequest = true, logResponse = true)
    public CommonResponse<QueryRoleListResponse> roleList() {
        QueryRoleListReq req = new QueryRoleListReq();
        req.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        QueryRoleListResp resp = laborHireThriftService.queryRoleList(req);
        if (!resp.getStatus().successful()) {
            return CommonResponse.fail(resp.getStatus().getCode(), resp.getStatus().getMsg());
        }
        return CommonResponse.success(
                new QueryRoleListResponse(
                        Optional.ofNullable(resp.getStoreList())
                                .orElse(Lists.newArrayList())
                                .stream()
                                .map(
                                        roleDTO -> {
                                            RoleVO vo = new RoleVO();
                                            vo.setRoleId(roleDTO.getRoleId());
                                            vo.setRoleName(roleDTO.getRoleName());
                                            return vo;
                                        }
                                ).collect(Collectors.toList())
                )
        );
    }

    @MethodLog(logRequest = true, logResponse = true)
    public CommonResponse<Void> editCandidate(EditCandidateRequest request) {
        EditCandidateReq req = new EditCandidateReq();
        req.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        req.setApprovalId(request.getApprovalId());
        req.setWorkTypeId(request.getWorkTypeId());
        req.setBelongStoreId(request.getBelongStoreId());
        req.setBankcardNumber(request.getBankcardNumber());
        req.setOpeningBank(request.getOpeningBank());
        BaseResponse resp = laborHireApprovalThriftService.editCandidate(req);
        if (!resp.getStatus().successful()) {
            return CommonResponse.fail(resp.getStatus().getCode(), resp.getStatus().getMsg());
        }
        return CommonResponse.success(null);

    }

    @MethodLog(logRequest = true, logResponse = true)
    public CommonResponse<QueryWorkTypeListResponse> queryAllWorkType(Integer poiOperationMode) {
        QueryAllWorkTypesReq req = new QueryAllWorkTypesReq();
        req.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        req.setOperationMode(poiOperationMode);
        QueryAllWorkTypesResp resp = laborHireThriftService.queryAllWorkTypes(req);
        if (!resp.getStatus().successful()) {
            return CommonResponse.fail(resp.getStatus().getCode(), resp.getStatus().getMsg());
        }
        return CommonResponse.success(
                new QueryWorkTypeListResponse(
                        Optional.ofNullable(resp.getWorkTypeList())
                                .orElse(Lists.newArrayList())
                                .stream()
                                .map(
                                        workTypeDTO -> {
                                            WorkTypeVO vo = new WorkTypeVO();
                                            vo.setId(workTypeDTO.getId());
                                            vo.setName(workTypeDTO.getName());
                                            return vo;
                                        }
                                ).collect(Collectors.toList())
                )
        );
    }

    @MethodLog(logRequest = true, logResponse = true)
    public CommonResponse<Long> count(long tenantId, long storeId, long employeeId, long accountId) {
        QueryHireApprovalListReq req = new QueryHireApprovalListReq();
        req.setTenantId(tenantId);
        req.setPoiId(storeId);
        req.setAccountId(accountId);
        req.setEmployeeId(employeeId);
        req.setType(1);
        req.setPageRequest(new PageRequest(1, 1));
        QueryHireApprovalListResp resp = laborHireApprovalThriftService.queryHireApprovalList(req);
        if (!resp.getStatus().successful()) {
            return CommonResponse.fail(resp.getStatus().getCode(), resp.getStatus().getMsg());
        }

        QueryResignApplyReq resignApplyReq = new QueryResignApplyReq();
        resignApplyReq.setTenantId(tenantId);
        resignApplyReq.setStoreId(storeId);
        resignApplyReq.setAccountId(accountId);
        resignApplyReq.setQueryType(2);
        resignApplyReq.setApprovalType(1);
        resignApplyReq.setPageRequest(new PageRequest(1, 1));
        resignApplyReq.setNeedDetail(false);
        QueryAlreadyResignApplyResp resignApplyResp = resignThriftService.queryAlreadyResignApplyList(resignApplyReq);
        if (!resignApplyResp.getStatus().successful()) {
            return CommonResponse.fail(resp.getStatus().getCode(), resp.getStatus().getMsg());
        }
        return CommonResponse.success(resp.getTotalCount() + resignApplyResp.getTotalCount());
    }

    private List<HireApprovalVO> convertToVO(List<HireApprovalInfoDTO> dtoList) {
        return Optional
                .ofNullable(dtoList)
                .orElse(Lists.newArrayList())
                .stream()
                .map(
                        dto -> {
                            HireApprovalVO hireApprovalVO = new HireApprovalVO();
                            hireApprovalVO.setApprovalId(dto.getApprovalId());
                            hireApprovalVO.setEmployeeId(dto.getHireAndResignEmployeeDTO().getEmployeeId());
                            hireApprovalVO.setEmployeeName(dto.getHireAndResignEmployeeDTO().getEmployeeName());
                            hireApprovalVO.setBelongStoreNameList(Lists.newArrayList(dto.getHireAndResignEmployeeDTO().getStoreName()));
                            hireApprovalVO.setPhoneNumber(dto.getHireAndResignEmployeeDTO().getPhoneNumber());
                            hireApprovalVO.setStatus(dto.getApprovalStatus());
                            hireApprovalVO.setWorkTypeNameList(Lists.newArrayList(dto.getLaborCandidateDTO().getWorkTypeName()));
                            return hireApprovalVO;
                        }
                ).collect(Collectors.toList());
    }

    private QueryApprovalDetailResponse convertToVO(HireApprovalDetailDTO dto) {
        QueryApprovalDetailResponse response = new QueryApprovalDetailResponse();

        HireApprovalDetailVO vo = new HireApprovalDetailVO();
        vo.setEmployeeName(dto.getEmployeeName());
        vo.setBelongStoreName(dto.getStoreName());
        vo.setApplyDate(dto.getApplyDate());
        vo.setPhoneNumber(dto.getPhoneNumber());
        vo.setIdCardNumber(dto.getIdCardNumber());
        vo.setBankcardNumber(dto.getBankcardNumber());
        vo.setOpeningBank(dto.getOpeningBank());
        vo.setCityName(dto.getCityName());
        vo.setCityId(dto.getCityId());
        vo.setWorkTypeName(dto.getWorkTypeName());
        vo.setGender(dto.getGender());
        vo.setGenderName(dto.getGenderName());
        vo.setHireChannel(dto.getHireChannel());
        vo.setHireChannelName(dto.getHireChannelName());
        vo.setEducation(dto.getEducation());
        vo.setEducationName(dto.getEducationName());
        vo.setPoiOperationMode(dto.getPoiOperationMode());
        response.setApprovalList(approvalConverter.convertToApprovalVO(dto.getApprovalNodeDTOList()));
        response.setHireInfo(vo);
        response.setApprovalStatus(dto.getApprovalStatus());
        response.setCanApproval(dto.getCanApproval());
        return response;
    }


}
