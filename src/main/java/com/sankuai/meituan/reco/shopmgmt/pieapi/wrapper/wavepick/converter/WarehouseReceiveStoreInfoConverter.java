package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.wavepick.converter;

import com.sankuai.meituan.reco.pickselect.thrift.wave.dto.WarehouseReceiveStoreInfoDTO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.WarehouseReceiveStoreInfoVO;
import org.mapstruct.Mapper;

/**
 * @Description:
 * @Author: zhangjian155
 * @Date: 2022/8/25 16:23
 */
@Mapper(componentModel = "spring")
public abstract class WarehouseReceiveStoreInfoConverter {
    public abstract WarehouseReceiveStoreInfoVO convert2Vo(WarehouseReceiveStoreInfoDTO warehouseReceiveStoreInfoDTO);
}
