package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @Auther: nifei
 * @Date: 2023/8/21 16:19
 */
@TypeDoc(
        description = "装箱进度"
)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SeedProgressVO {
    @FieldDoc(
            description = "已装箱商品总数"
    )
    private Integer actualNum;

    @FieldDoc(
            description = "应拣商品总数"
    )
    private Integer needNum;

    @FieldDoc(
            description = "已装箱商品种类数"
    )
    private Integer actualSkuNum;

    @FieldDoc(
            description = "应拣商品种类数"
    )
    private Integer needSkuNum;
}
