package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pendingtask;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2019/11/19
 * @description
 */
@TypeDoc(
        description = "代办任务查询响应数据"
)
@Data
@ApiModel("代办任务查询响应数据")
public class PendingTaskQueryResponse {

    @FieldDoc(
            description = "代办任务列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "代办任务列表", required = true)
    @NotNull
    private List<PendingTaskCountVO> pendingTaskCountVOList;

}
