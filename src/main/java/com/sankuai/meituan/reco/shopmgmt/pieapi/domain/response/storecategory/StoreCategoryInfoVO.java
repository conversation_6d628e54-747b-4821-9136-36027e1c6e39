package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.storecategory;

import com.alibaba.druid.filter.AutoLoad;
import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreCategoryBasicInfoDTO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/10/30
 */
@TypeDoc(
        name = "牵牛花门店店内分类树VO对象",
        description = "牵牛花门店店内分类树VO对象"
)
@Getter
@Setter
@AutoLoad
@NoArgsConstructor
@ToString
public class StoreCategoryInfoVO {

    @FieldDoc(
            description = "分类编码"
    )
    private String categoryId;

    @FieldDoc(
            description = "分类名称"
    )
    private String name;

    @FieldDoc(
            description = "分类层级"
    )
    private int level;

    @FieldDoc(
            description = "分类是否关联商品"
    )
    private int hasProduct;

    @FieldDoc(
            description = "子分类数量"
    )
    private int subAmount;

    @FieldDoc(
            description = "是否含有子分类"
    )
    private int hasChildren;

    @FieldDoc(
            description = "父分类编码"
    )
    private String parentCategoryId;

    @FieldDoc(
            description = "门店组编码"
    )
    private Long storeGroupId;

    @FieldDoc(
            description = "分类状态：1-启用，2-禁用"
    )
    private Integer enable;

    @FieldDoc(
            description = "分类关联商品数（注意这儿同时也会包括子分类关联商品数）"
    )
    private int productAmount;

    @FieldDoc(
            description = "是否置顶"
    )
    public Integer topFlag;

    @FieldDoc(
            description = "置顶周期"
    )
    private String weeksTime;

    @FieldDoc(
            description = "置顶时段"
    )
    private List<String> period;

    @FieldDoc(
            description = "子分类信息"
    )
    private List<StoreCategoryInfoVO> children = Lists.newArrayList();

    public static Integer LEVEL_1 = 1;
    public static StoreCategoryInfoVO build(StoreCategoryBasicInfoDTO dto) {
        StoreCategoryInfoVO vo = new StoreCategoryInfoVO();
        vo.setCategoryId(dto.getCategoryId().toString());
        vo.setName(dto.getCategoryName());
        vo.setLevel(dto.getLevel());
        vo.setParentCategoryId(dto.getParentCategoryId().toString());
        vo.setStoreGroupId(dto.getStoreGroupId());
        vo.setProductAmount(dto.getSpuCount());
        vo.setTopFlag(dto.getTopFlag());
        vo.setWeeksTime(dto.getWeeksTime());
        vo.setPeriod(dto.getPeriod());
        vo.setEnable(dto.getEnable());
        return vo;
    }

    public static List<StoreCategoryInfoVO> buildStoreCategoryTreeList(List<StoreCategoryBasicInfoDTO> storeCategoryList) {
        if (CollectionUtils.isEmpty(storeCategoryList)) {
            return Collections.emptyList();
        }
        // 分类按层级重新排序
        storeCategoryList.sort(Comparator.comparing(StoreCategoryBasicInfoDTO::getLevel).thenComparing(StoreCategoryBasicInfoDTO::getSequence));
        Map<Pair<Long, Long>, StoreCategoryInfoVO> poiStoreCategoryTreeMap = new LinkedHashMap<>();
        for (StoreCategoryBasicInfoDTO dto : storeCategoryList) {
            StoreCategoryInfoVO treeVo = StoreCategoryInfoVO.build(dto);
            // 分类关联商品数量
            treeVo.setHasProduct(dto.getSpuCount() > 0 ? 1 : 0);
            treeVo.setProductAmount(dto.getSpuCount());
            poiStoreCategoryTreeMap.put(Pair.of(dto.getStoreGroupId(), dto.getCategoryId()), treeVo);
        }
        // 遍历构建分类树
        List<StoreCategoryInfoVO> treeList = Lists.newArrayList();
        storeCategoryList.forEach(m -> {
            if (LEVEL_1.equals(m.getLevel())) {
                treeList.add(poiStoreCategoryTreeMap.get(Pair.of(m.getStoreGroupId(), m.getCategoryId())));
            } else {
                StoreCategoryInfoVO parent = poiStoreCategoryTreeMap.get(Pair.of(m.getStoreGroupId(), m.getParentCategoryId()));
                if (parent == null || parent.getLevel() != LEVEL_1) {
                    return;
                }
                parent.getChildren().add(poiStoreCategoryTreeMap.get(Pair.of(m.getStoreGroupId(), m.getCategoryId())));
            }
        });
        // 添加子类统计信息
        treeList.forEach(tree -> {
            if (CollectionUtils.isEmpty(tree.getChildren())) {
                return;
            }
            tree.setHasChildren(1);
            tree.setSubAmount(tree.getChildren().size());
            // 子分类关联商品数量同时会体现在父分类关联商品数上
            tree.setProductAmount(tree.getProductAmount() + tree.getChildren().stream().mapToInt(StoreCategoryInfoVO::getProductAmount).sum());
        });
        return treeList;
    }

}
