package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.spu;

import com.alibaba.fastjson.JSON;
import com.meituan.shangou.saas.tenant.thrift.common.StatusCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ChannelTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.BrandGroupByChannelVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.BrandQueryResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.BrandVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelBrandDomainVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.TenantWrapper;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.GetChannelBrandInfoByChannelKeywordRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.GetChannelBrandInfoByChannelKeywordResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.ChannelThriftService;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelbrand.request.RecommendBrandQueryRequest;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelbrand.response.RecommendBrandQueryResponse;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelbrand.service.ChannelBrandThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @createTime 2023/2/16
 * @description
 */
@Slf4j
@Component
public class BrandWrapper {

    @Resource(type = ChannelThriftService.Iface.class)
    private ChannelThriftService.Iface channelThriftService;

    @Resource
    private ChannelBrandThriftService channelBrandThriftService;

    @Resource
    private TenantWrapper tenantWrapper;

    public CommonResponse listByChannel(Integer channelId, String searchBrandName) {
        BrandQueryResponseVO brandQueryResponseVO = new BrandQueryResponseVO();
        brandQueryResponseVO.setBrandList(new ArrayList());
        GetChannelBrandInfoByChannelKeywordRequest request = new GetChannelBrandInfoByChannelKeywordRequest();
        try {
            Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
            request.setTenantId(tenantId);
            request.setChannel(channelId);
            request.setKeyword(searchBrandName);
            GetChannelBrandInfoByChannelKeywordResponse response = channelThriftService.getChannelBrandInfoByChannelKeyword(request);
            if (response.getCode() == StatusCodeEnum.SUCCESS.getCode()) {
                brandQueryResponseVO.setBrandList(response.getChannelBrandInfo().stream().map(s->{
                    ChannelBrandDomainVO channelBrandDomainVO = new ChannelBrandDomainVO();
                    channelBrandDomainVO.setBrandCode(s.getBrandId());
                    channelBrandDomainVO.setBrandName(s.getBrandName());
                    channelBrandDomainVO.setChannelId(s.getChannel());
                    channelBrandDomainVO.setChannelName( ChannelTypeEnum.findChannelNameByChannelId(s.getChannel()) );
                    return channelBrandDomainVO;
                }).collect(Collectors.toList()));

            }
        }
        catch (TException ex) {
            String message = String.format("查询推荐品牌出现异常,请求参数:%s,异常信息:%s", JSON.toJSONString(request), ex.getMessage());
            log.warn(message, ex);
        }
        return CommonResponse.success(brandQueryResponseVO);
    }

    public List<BrandGroupByChannelVO> queryRecommendBrand(List<Integer> channelIds, String spuName){

        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        if(CollectionUtils.isEmpty(channelIds)){
            channelIds = tenantWrapper.queryChannelIds(tenantId).stream().map(m -> m.getChannelId()).collect(Collectors.toList());
        }
        List<BrandGroupByChannelVO> brandGroupByChannelVOS = new ArrayList();
        for(Integer channelId : channelIds) {
            RecommendBrandQueryRequest request = new RecommendBrandQueryRequest();
            request.setChannelId(channelId);
            request.setSpuName(spuName);
            request.setTenantId(tenantId);
            try {
                RecommendBrandQueryResponse response = channelBrandThriftService.queryRecommendBrand(request);
                if(response.getCode() == 0 && CollectionUtils.isNotEmpty(response.getChannelBrands())){
                    BrandGroupByChannelVO brandGroupByChannelVO = new BrandGroupByChannelVO();
                    brandGroupByChannelVO.setChannelId(channelId);
                    brandGroupByChannelVO.setChannelBrands(response.getChannelBrands().stream().map(c ->{
                        BrandVO brandVO = new BrandVO();
                        brandVO.setBrandCode(c.getChannelBrandId());
                        brandVO.setBrandName(c.getChannelBrandName());
                        return brandVO;
                    }).collect(Collectors.toList()));
                    brandGroupByChannelVOS.add(brandGroupByChannelVO);
                }
            }catch(Exception e){
                String message = String.format("查询渠道品牌出现异常,请求参数%s,异常信息:%s", JSON.toJSONString(request), e.getMessage());
                log.warn(message,e);
            }
        }
        return brandGroupByChannelVOS;
    }
}
