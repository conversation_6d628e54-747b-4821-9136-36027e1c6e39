package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.saas.crm.promotion.dto.pullnew.AppUserPullNewStat;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.request.pullnew.AppSubordinateStatEsRequest;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.response.dto.AppUserPullNewStatDto;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2021/11/1 5:44 下午
 **/
@TypeDoc(
        description = "下级拉新统计"
)
@Data
@ApiModel("展示下级微信拉新统计")
@NoArgsConstructor
@AllArgsConstructor
public class SubordinateStatVo {

    @FieldDoc(
            description = "下级账号id"
    )
    private Long accountId;

    @FieldDoc(
            description = "下级账号名称"
    )
    private String accountName;

    @FieldDoc(
            description = "下级用户名"
    )
    private String userName;

    @FieldDoc(
            description = "下级手机号"
    )
    private String phone;

    @FieldDoc(
            description = "下级拉新统计"
    )
    private PullNewStatVo stat;

    public static SubordinateStatVo instanceOf(AppUserPullNewStat appUserPullNewStat) {
        SubordinateStatVo subordinateStatVo = new SubordinateStatVo();
        subordinateStatVo.setAccountId(appUserPullNewStat.getUserInfo().getAccountId());
        subordinateStatVo.setAccountName(appUserPullNewStat.getUserInfo().getAccountName());
        subordinateStatVo.setUserName(appUserPullNewStat.getUserInfo().getEmployeeName());
        subordinateStatVo.setStat(PullNewStatVo.instanceOf(appUserPullNewStat.getStat()));
        return subordinateStatVo;
    }

    public static SubordinateStatVo instanceOf(AppUserPullNewStatDto appUserPullNewStat) {
        SubordinateStatVo subordinateStatVo = new SubordinateStatVo();
        subordinateStatVo.setAccountId(appUserPullNewStat.getUserInfo().getAccountId());
        subordinateStatVo.setAccountName(appUserPullNewStat.getUserInfo().getAccountName());
        subordinateStatVo.setUserName(appUserPullNewStat.getUserInfo().getEmployeeName());
        subordinateStatVo.setStat(PullNewStatVo.instanceOf(appUserPullNewStat.getStat()));
        return subordinateStatVo;
    }
}
