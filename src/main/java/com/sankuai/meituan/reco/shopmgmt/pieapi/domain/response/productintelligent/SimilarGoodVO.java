package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.productintelligent;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productintelligent.thrift.model.SimilarGoodDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@TypeDoc(
        description = "相似商品信息",
        authors = {"lixingzhong"}
)
@Data
@ApiModel("相似商品信息")
public class SimilarGoodVO {

    @FieldDoc(
            description = "来源名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "来源名称", required = true)
    private String sourceName;

    @FieldDoc(
            description = "外部门店名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "外部门店名称", required = true)
    private String outStoreName;

    @FieldDoc(
            description = "外部门店名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "外部门店名称", required = true)
    private String outSkuId;

    @FieldDoc(
            description = "外部sku名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "外部sku名称", required = true)
    private String outSkuName;

    @FieldDoc(
            description = "规格信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "规格信息", required = true)
    private String spec;

    @FieldDoc(
            description = "进货价", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "进货价", required = true)
    private Long originPrice;

    @FieldDoc(
            description = "零售价", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "零售价", required = true)
    private Long salePrice;

    @FieldDoc(
            description = "月销售量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "月销售量", required = true)
    private Integer monthSales;

    @FieldDoc(
            description = "总销售量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "总销售量", required = true)
    private Integer totalSales;

    public static List<SimilarGoodVO> ofDTOList(List<SimilarGoodDTO> similarGoodDTOList) {
        if (CollectionUtils.isEmpty(similarGoodDTOList)) {
            return Lists.newArrayList();
        }

        return similarGoodDTOList.stream().filter(Objects::nonNull).map(SimilarGoodVO::ofDTO).collect(Collectors.toList())
                .stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static SimilarGoodVO ofDTO(SimilarGoodDTO similarGoodDTO) {
        if (similarGoodDTO == null) {
            return null;
        }

        SimilarGoodVO similarGoodVO = new SimilarGoodVO();
        similarGoodVO.setSourceName(similarGoodDTO.getSourceName());
        similarGoodVO.setOutStoreName(similarGoodDTO.getOutStoreName());
        similarGoodVO.setOutSkuId(similarGoodDTO.getOutSkuId());
        similarGoodVO.setOutSkuName(similarGoodDTO.getOutSkuName());
        similarGoodVO.setSpec(similarGoodDTO.getSpec());
        similarGoodVO.setOriginPrice(similarGoodDTO.getOriginPrice());
        similarGoodVO.setSalePrice(similarGoodDTO.getSalePrice());
        similarGoodVO.setMonthSales(similarGoodDTO.getMonthSales());
        similarGoodVO.setTotalSales(similarGoodDTO.getTotalSales());

        return similarGoodVO;
    }
}
