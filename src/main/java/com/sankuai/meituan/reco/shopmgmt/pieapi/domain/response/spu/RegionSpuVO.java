package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.StoreSkuPropertyVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.TimeSlotVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.store.BoothVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.store.RegionVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.store.StoreVO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ProductPropertyDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ProductTimeSlotDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.RegionSpuDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.StoreSpuDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.assertj.core.util.Lists;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: <EMAIL>
 * @class: RegionSpuVO
 * @date: 2020-06-10 19:27:03
 *
 * @desc: 区域商品SPU信息
 */
@TypeDoc(
        description = "门店商品SPU信息"
)
@Data
@ApiModel("区域商品SPU信息")
public class RegionSpuVO {

    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户ID", required = true)
    private Long tenantId;
    @FieldDoc(
            description = "区域信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "区域信息")
    private RegionVO region;
    @FieldDoc(
            description = "spu编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "spu编码", required = true)
    private String spuId;
    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称", required = true)
    private String name;
    @FieldDoc(
            description = "商品图片", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品图片", required = true)
    private List<String> images;
    @FieldDoc(
            description = "称重类型 1-称重计量 2-称重计件 3-非称重"
    )
    @ApiModelProperty(name = "称重类型")
    private Integer weightType;
    @FieldDoc(
            description = "产地", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "产地")
    private String producingPlace;
    @FieldDoc(
            description = "ERP类目信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "ERP类目信息")
    private CategoryVO category;
    @FieldDoc(
            description = "品牌信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "品牌信息")
    private BrandVO brand;

    @FieldDoc(
            description = "标签列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "标签列表")
    private List<SpuTagVO> tagList;

    @FieldDoc(
            description = "城市月售", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "城市月售")
    private Integer cityMonthSaleAmount;

    public static List<RegionSpuVO> ofDTOList(List<RegionSpuDTO> storeSpuDTOList) {
        if (CollectionUtils.isEmpty(storeSpuDTOList)) {
            return Lists.newArrayList();
        }

        return storeSpuDTOList.stream().filter(Objects::nonNull).map(RegionSpuVO::ofDTO).collect(Collectors.toList());

    }

    public static RegionSpuVO ofDTO(RegionSpuDTO regionSpu) {
        if (regionSpu == null) {
            return null;
        }
        RegionSpuVO storeSpuVO = new RegionSpuVO();
        storeSpuVO.setTenantId(regionSpu.getTenantId());
        storeSpuVO.setSpuId(regionSpu.getSpuId());
        storeSpuVO.setName(regionSpu.getName());
        storeSpuVO.setRegion(new RegionVO(regionSpu.getRegionId(), regionSpu.getRegionName()));
        storeSpuVO.setImages(regionSpu.getImageUris());
        storeSpuVO.setWeightType(regionSpu.getWeightType());

        storeSpuVO.setProducingPlace(regionSpu.getOrigin());
        storeSpuVO.setCategory(CategoryVO.ofDTO(regionSpu.getErpCategory()));
        storeSpuVO.setBrand(BrandVO.ofDTO(regionSpu.getBrandDTO()));

        // 标签信息转换
        storeSpuVO.setTagList(SpuTagVO.ofDTOList(regionSpu.getTags()));

        return storeSpuVO;
    }

}
