package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.delivery;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@TypeDoc(
        description = "操作按钮返回体"
)
@Data
@ApiModel("操作按钮返回体")
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryOperateItemResponse {

    @FieldDoc(
            description = "操作按钮code", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "操作按钮code", required = true)
    private List<Integer> deliveryOperateCode;
}
