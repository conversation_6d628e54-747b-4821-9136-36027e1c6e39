package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp;

import java.util.List;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ERP门店商品SPU分页查询响应结果
 *
 * <AUTHOR>
 * @since 2023/05/12
 */
@TypeDoc(
        description = "ERP门店商品SPU分页查询响应结果"
)
@Data
@ApiModel("ERP门店商品SPU分页查询响应结果")
public class ErpStoreSpuPageQueryResponseVO {

    @FieldDoc(description = "商品信息列表", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "商品信息列表")
    private List<ErpStoreSpuVO> storeSpuList;

    @FieldDoc(description = "分页信息", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "分页信息")
    private PageInfoVO pageInfo;

}
