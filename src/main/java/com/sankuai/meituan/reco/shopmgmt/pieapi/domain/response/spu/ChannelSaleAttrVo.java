package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.dto.ChannelSaleAttrDTO;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-02-12
 */
@TypeDoc(
        name = "销售属性值对象",
        description = "销售属性值对象"
)
@Data
@Builder
public class ChannelSaleAttrVo {
    @FieldDoc(description = "属性ID")
    private String attrId;

    @FieldDoc(description = "属性名称")
    private String attrName;

    @FieldDoc(description = "属性的顺序值")
    private Integer attrSequence;

    @FieldDoc(description = "属性值类型：1-单选,2-多选,3-文本,8-日期")
    private String attrValueType;

    @FieldDoc(description = "属性值可录入的字符类型。 参考值：1-中文，2-字母，3-数字，4-标点符号（美团）")
    private String characterType;

    @FieldDoc(description = "是否必填：1-必填")
    private Integer isRequired;

    @FieldDoc(description = "是否支持自定义")
    private Boolean supportExtend;

    @FieldDoc(description = "是否支持图片")
    private Boolean supportPicture;

    @FieldDoc(description = "销售属性值列表")
    private List<ChannelSaleAttrValueVo> valueList;

    @FieldDoc(description = "销售属性值最大长度")
    private Integer maxAttrValueLength;

    public static ChannelSaleAttrVo build(ChannelSaleAttrDTO dto) {
        if (dto == null) {
            return null;
        }
        return ChannelSaleAttrVo.builder()
                .attrId(dto.getAttrId())
                .attrName(dto.getAttrName())
                .attrSequence(dto.getAttrSequence())
                .attrValueType(dto.getAttrValueType())
                .characterType(dto.getCharacterType())
                .isRequired(dto.getIsRequired())
                .supportExtend(dto.getSupportExtend())
                .supportPicture(dto.getSupportPicture())
                .valueList(ChannelSaleAttrValueVo.buildList(dto.getValueList()))
                .maxAttrValueLength(20)
                .build();
    }

    public static List<ChannelSaleAttrVo> buildList(List<ChannelSaleAttrDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        return dtoList.stream()
                .map(attrDto -> build(attrDto))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public void appendValueList(ChannelSaleAttrValueVo channelSaleAttrValueVo) {
        if (valueList == null) {
            valueList = new ArrayList<>();
        }
        // 避免添加重复的销售属性值，性能比较差，不过实际影响不大
        Set<String> valueSet = Fun.map(valueList, ChannelSaleAttrValueVo::getAttrValue, Collectors.toSet());
        if (valueSet.contains(channelSaleAttrValueVo.getAttrValue())) {
            return;
        }
        valueList.add(channelSaleAttrValueVo);
    }
}