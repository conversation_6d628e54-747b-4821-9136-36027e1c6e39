package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.StoreSkuPropertyVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.TimeSlotVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.store.BoothVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.store.RegionVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.store.StoreVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ProductPropertyDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ProductTimeSlotDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.SpuTagDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.StoreSpuDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.response.StoreSpuDetailResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Title: StoreSpuDetailVO
 * @Description: 门店商品SPU详情信息
 * @Author: zhaolei12
 * @Date: 2020/5/16 5:02 下午
 */
@TypeDoc(
        description = "门店商品SPU详情信息"
)
@Data
@ApiModel("门店商品SPU详情信息")
public class StoreSpuDetailVO {

    @FieldDoc(
            description = "租户id", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "租户id")
    private Long tenantId;
    @FieldDoc(
            description = "SPU编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "SPU编码", required = true)
    @NotNull
    private String spuId;
    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称", required = true)
    @NotNull
    private String name;
    @FieldDoc(
            description = "商品类型"
    )
    @ApiModelProperty(name = "商品类型")
    private Integer spuType;
    @FieldDoc(
            description = "称重类型 1-称重计量 2-称重计件 3-非称重"
    )
    @ApiModelProperty(name = "称重类型")
    private Integer weightType;
    @FieldDoc(
            description = "图片url列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "图片url列表", required = true)
    @NotNull
    private List<String> images;
    @FieldDoc(
            description = "产地", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "产地")
    private String producingPlace;
    @FieldDoc(
            description = "可售时间，如果为无限，此字段为空。\n" +
                    "\n" +
                    "key:工作日 见@Enum WeekDayEnum\n" +
                    "* value:时间段 时间段不允许有交集，个数不超过5个\n" +
                    "* {\"09:00-09:30\"},{\"13:30-15:00\"},{\"20:00-21:00\"}", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "可售时间")
    private Map<Integer,List<TimeSlotVO>> availableTimes;
    @FieldDoc(
            description = "是否为“力荐”商品，字段取值范围：0-否， 1-是", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否为“力荐”商品")
    private Integer specialty;
    @FieldDoc(
            description = "商品描述 200字以内", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品描述")
    private String description;
    @FieldDoc(
            description = "商品属性 不超过十个属性", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品属性")
    private List<StoreSkuPropertyVO> properties;
    @FieldDoc(
            description = "ERP类目信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "ERP类目信息")
    private CategoryVO category;
    @FieldDoc(
            description = "品牌信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "品牌信息")
    private BrandVO brand;
    @FieldDoc(
            description = "摊位信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "摊位信息")
    private BoothVO booth;
    @FieldDoc(
            description = "原摊位信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "原摊位信息")
    private BoothVO orgBooth;
    @FieldDoc(
            description = "标签列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "标签列表")
    private List<SpuTagVO> tagList;
    @FieldDoc(
            description = "门店信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店信息")
    private StoreVO store;
    @FieldDoc(
            description = "区域信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "区域信息")
    private RegionVO region;

    @FieldDoc(
            description = "门店商品SKU信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店商品SKU信息")
    private List<StoreSkuDetailVO> storeSkuList;

    @FieldDoc(
            description = "渠道商品SPU信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道商品SPU信息")
    private List<ChannelSpuDetailVO> channelSpuList;


    public static StoreSpuDetailVO convertResponse(StoreSpuDetailResponse storeSpuDetailResponse, User user) {
        if (storeSpuDetailResponse == null || storeSpuDetailResponse.getStoreSpu() == null) {
            return null;
        }
        StoreSpuDTO storeSpu = storeSpuDetailResponse.getStoreSpu();
        StoreSpuDetailVO storeSpuDetailVO = new StoreSpuDetailVO();
        storeSpuDetailVO.setTenantId(storeSpu.getTenantId());
        storeSpuDetailVO.setSpuId(storeSpu.getSpuId());
        storeSpuDetailVO.setName(storeSpu.getName());
        storeSpuDetailVO.setSpuType(storeSpu.getSpuType());
        storeSpuDetailVO.setWeightType(storeSpu.getWeightType());
        storeSpuDetailVO.setImages(storeSpu.getImageUrls());
        storeSpuDetailVO.setProducingPlace(storeSpu.getProducingPlace());
        storeSpuDetailVO.setSpecialty(storeSpu.getSpecialty());
        storeSpuDetailVO.setDescription(storeSpu.getDescription());

        storeSpuDetailVO.setCategory(CategoryVO.ofDTO(storeSpu.getCategory()));
        storeSpuDetailVO.setBrand(BrandVO.ofDTO(storeSpu.getBrand()));
        storeSpuDetailVO.setBooth(BoothVO.ofDTO(storeSpu.getBooth()));
        storeSpuDetailVO.setOrgBooth(BoothVO.ofDTO(storeSpu.getOrgBooth()));
        storeSpuDetailVO.setStore(StoreVO.ofDTO(storeSpu.getStore()));
        storeSpuDetailVO.setRegion(RegionVO.ofDTO(storeSpu.getRegion()));

        storeSpuDetailVO.setStoreSkuList(StoreSkuDetailVO.ofDTOList(storeSpu.getStoreSkuList()));
        storeSpuDetailVO.setChannelSpuList(ChannelSpuDetailVO.ofDTOList(storeSpu.getChannelSpuList()));

        // 商品可售时间数据赋值
        storeSpuDetailVO.setAvailableTimes(convertAvailableTime(storeSpu.getAvailableTimes()));

        // 商品属性数据赋值
        storeSpuDetailVO.setProperties(convertProperties(storeSpu.getProperties()));

        // 商品标签数据赋值
        storeSpuDetailVO.setTagList(convertSpuTag(storeSpu.getTagList()));

        return storeSpuDetailVO;
    }

    private static Map<Integer,List<TimeSlotVO>> convertAvailableTime(Map<Integer, List<ProductTimeSlotDTO>> availableTimeDtoMap) {
        Map<Integer,List<TimeSlotVO>> availableTimeVoMap = new HashMap<>();
        if (MapUtils.isEmpty(availableTimeDtoMap)){
            return availableTimeVoMap;
        }
        for (Map.Entry<Integer, List<ProductTimeSlotDTO>> entry : availableTimeDtoMap.entrySet()) {
            List<TimeSlotVO> timeSlotVOList = new ArrayList<>();
            if (CollectionUtils.isEmpty(entry.getValue())){
                continue;
            }
            for (ProductTimeSlotDTO timeSlotDTO : entry.getValue()) {
                if (null == entry.getValue()){
                    continue;
                }
                timeSlotVOList.add(new TimeSlotVO(timeSlotDTO));
            }
            availableTimeVoMap.put(entry.getKey(), timeSlotVOList);
        }
        return availableTimeVoMap;
    }

    private static List<StoreSkuPropertyVO> convertProperties(List<ProductPropertyDTO> productPropertyDTOList) {
        List<StoreSkuPropertyVO> storeSkuPropertyVOList =  new ArrayList<>();
        if (CollectionUtils.isEmpty(productPropertyDTOList)) {
            return storeSkuPropertyVOList;
        }

        if (CollectionUtils.isNotEmpty(productPropertyDTOList)){
            for (ProductPropertyDTO productPropertyDTO : productPropertyDTOList) {
                if (productPropertyDTO != null){
                    storeSkuPropertyVOList.add(new StoreSkuPropertyVO(productPropertyDTO));
                }
            }
        }
        return storeSkuPropertyVOList;
    }

    private static List<SpuTagVO> convertSpuTag(List<SpuTagDTO> spuTagDTOList) {
        List<SpuTagVO> spuTagVOList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(spuTagDTOList)) {
            return spuTagVOList;
        }

        if(CollectionUtils.isNotEmpty(spuTagDTOList)){
            for(SpuTagDTO spuTagDTO:spuTagDTOList){
                SpuTagVO spuTagVO = new SpuTagVO();
                spuTagVO.setTagId(spuTagDTO.getTagId());
                spuTagVO.setTagName(spuTagDTO.getTagName());
                spuTagVOList.add(spuTagVO);
            }
        }
        return spuTagVOList;
    }
}
