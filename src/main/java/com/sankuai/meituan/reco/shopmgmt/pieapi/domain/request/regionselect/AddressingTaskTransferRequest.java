package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.regionselect;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/12/7
 */
@TypeDoc(
        description = "寻仓任务转派请求"
)
@Data
public class AddressingTaskTransferRequest {
    @FieldDoc(description = "任务Id")
    private Long taskId;

    @FieldDoc(description = "寻仓改派人员账号Id")
    private Long ownerEmployeeId;
}
