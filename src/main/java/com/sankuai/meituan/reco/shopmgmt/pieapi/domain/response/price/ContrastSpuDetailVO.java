package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.price.client.dto.contrast.ContrastSpuDetailDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

/**
 * @Author: wangyihao04
 * @Date: 2020-12-02 14:19
 * @Mail: <EMAIL>
 */
@TypeDoc(
        description = "对比商品详情"
)
@ApiModel("对比商品详情")
@Getter
@AllArgsConstructor
@ToString
public class ContrastSpuDetailVO {
    @FieldDoc(
            description = "SPUID"
    )
    @ApiModelProperty("SPUID")
    public String spuId;

    @FieldDoc(
            description = "名称"
    )
    @ApiModelProperty("名称")
    public String spuName;

    @FieldDoc(
            description = "月销量"
    )
    @ApiModelProperty("月销量")
    public Long monthSale;

    @FieldDoc(
            description = "规格"
    )
    @ApiModelProperty("规格")
    public String spec;

    @FieldDoc(
            description = "图像连接"
    )
    @ApiModelProperty("图像连接")
    public String spuPicUrl;

    @FieldDoc(
            description = "原价"
    )
    @ApiModelProperty("原价")
    public String unifyOriginalPrice;

    @FieldDoc(
            description = "现价"
    )
    @ApiModelProperty("现价")
    public String unifyActualPrice;

    @FieldDoc(
            description = "是否标品， 1是，0否"
    )
    @ApiModelProperty("是否标品， 1是，0否")
    public Integer isSp;

    public static ContrastSpuDetailVO valueOf(ContrastSpuDetailDTO detailDTO){
        if (Objects.isNull(detailDTO)){
            return null;
        }
        return new ContrastSpuDetailVO(
                detailDTO.getSpuId(),
                detailDTO.getSpuName(),
                detailDTO.getMonthSale(),
                detailDTO.getSpec(),
                detailDTO.getSpuPicUrl(),
                detailDTO.getUnifyOriginalPrice(),
                detailDTO.getUnifyActualPrice(),
                detailDTO.getIsSp());
    }
}
