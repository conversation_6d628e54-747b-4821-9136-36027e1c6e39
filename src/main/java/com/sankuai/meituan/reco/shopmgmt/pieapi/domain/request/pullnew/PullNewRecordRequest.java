package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pullnew;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@TypeDoc(
        description = "展示拉新记录",
        version = "V1.0"
)
@Data
@ApiModel("展示拉新记录")
public class PullNewRecordRequest {

    @FieldDoc(
            description = "门店ID"
    )
    @ApiModelProperty(value = "门店ID")
    private Long storeId;

    @FieldDoc(
            description = "开始时间"
    )
    @ApiModelProperty(value = "开始时间")
    private Long startTime;

    @FieldDoc(
            description = "截止时间"
    )
    @ApiModelProperty(value = "截止时间")
    private Long endTime;

    @FieldDoc(
            description = "分页符"
    )
    @ApiModelProperty(value = "分页符")
    private Long markId;

    @FieldDoc(
            description = "分页符"
    )
    @ApiModelProperty(value = "分页符")
    private int pageSize = 20;


}
