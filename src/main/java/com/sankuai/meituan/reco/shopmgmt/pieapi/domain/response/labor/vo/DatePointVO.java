package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-03-15
 * @email <EMAIL>
 */
@ApiModel("日积分明细vo")
@TypeDoc(description = "日积分明细vo")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DatePointVO {

    @FieldDoc(description = "日期")
    @ApiModelProperty("日期")
    public Long date;

    @FieldDoc(description = "日度总积分")
    @ApiModelProperty("日度总积分")
    public String dateTotalPoints;


    @FieldDoc(description = "积分明细list")
    @ApiModelProperty("积分明细list")
    public List<PointDetailVO> detailList;
}
