package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.alibaba.fastjson.JSONObject;
import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.FallbackException;
import com.sankuai.meituan.reco.store.management.thrift.EmpowerTaskLogicException;
import com.sankuai.meituan.reco.store.management.thrift.delivery.DeliveryThriftService;
import com.sankuai.meituan.reco.store.management.thrift.delivery.ObtainWaitCountRequest;
import com.sankuai.meituan.reco.store.management.thrift.delivery.ObtainWaitCountResult;
import com.sankuai.meituan.reco.store.saas.infrastructure.shield.common.monitor.CommonMonitorTransaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by yangli on 18/9/28.
 */
@Slf4j
@Rhino
@Service
public class DeliveryWrapper {

    @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
    @Autowired
    private DeliveryThriftService.Iface deliveryThriftService;


    @Degrade(rhinoKey = "DeliveryWrapper.obtainWaitCount",
            fallBackMethod = "obtainWaitCountFallback",
            timeoutInMilliseconds = 1200,
            ignoreExceptions = {EmpowerTaskLogicException.class})
    @MethodLog(logRequest = true, logResponse = true)
    public ObtainWaitCountResult obtainWaitCount(ObtainWaitCountRequest request) throws EmpowerTaskLogicException {
        try {
            log.info("DeliveryWrapper.obtainWaitCount, request:{}", request);
            ObtainWaitCountResult obtainWaitCountResult =  deliveryThriftService.obtainWaitCount(request);
            log.info("DeliveryWrapper.obtainWaitCount, response:{}", obtainWaitCountResult);
            return obtainWaitCountResult;
        } catch (TException e) {
            log.error("DeliveryWrapper.obtainWaitCount error, req = {}", JSONObject.toJSONString(request), e);
            throw new CommonRuntimeException(e);
        }
    }

    private ObtainWaitCountResult obtainWaitCountFallback(ObtainWaitCountRequest request) throws EmpowerTaskLogicException {
        throw new FallbackException("DeliveryWrapper.obtainWaitCount接口降级");
    }

}
