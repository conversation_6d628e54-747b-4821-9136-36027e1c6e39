package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.dto.ProductSpecialPictureRuleDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@TypeDoc(
        name = "类目特殊图规则信息",
        description = "类目特殊图规则信息"
)
@Getter
@Setter
@ToString
public class SpecialPictureRuleVO {
    @FieldDoc(
            description = "图片类型"
    )
    @ApiModelProperty(name = "图片类型")
    private Integer pictureType;

    @FieldDoc(
            description = "标题"
    )
    @ApiModelProperty("标题")
    private String pictureTitle;

    @FieldDoc(
            description = "图片描述"
    )
    @ApiModelProperty("图片描述")
    private String pictureDesc;

    @FieldDoc(
            description = "示例图"
    )
    @ApiModelProperty("示例图")
    private List<String> pictureExample;

    @FieldDoc(
            description = "是否必传：1-必传，2-非必传"
    )
    @ApiModelProperty("是否必传：1-必传，2-非必传")
    private Integer isRequired;

    @FieldDoc(
            description = "是否c端展示：1-展示，2-不展示"
    )
    @ApiModelProperty("是否c端展示：1-展示，2-不展示")
    private Integer isDisplay;

    @FieldDoc(
            description = "数量限制"
    )
    @ApiModelProperty("数量限制")
    private Integer numLimit;

    public static List<SpecialPictureRuleVO> ofSpecialPictureRuleVoList(List<ProductSpecialPictureRuleDTO> specialPictureDTOList) {
        return Fun.map(specialPictureDTOList, SpecialPictureRuleVO::of);
    }

    public static SpecialPictureRuleVO of(ProductSpecialPictureRuleDTO specialPictureRuleDTO) {
        SpecialPictureRuleVO specialPictureRuleVo = new SpecialPictureRuleVO();
        specialPictureRuleVo.setPictureType(specialPictureRuleDTO.getSpecialPictureType());
        specialPictureRuleVo.setPictureTitle(specialPictureRuleDTO.getSpecialPictureTitle());
        specialPictureRuleVo.setPictureDesc(specialPictureRuleDTO.getSpecialPictureDescription());
        specialPictureRuleVo.setPictureExample(specialPictureRuleDTO.getSpecialPictureExample());
        specialPictureRuleVo.setIsRequired(specialPictureRuleDTO.getIsRequired());
        specialPictureRuleVo.setIsDisplay(specialPictureRuleDTO.getIsDisplayed());
        specialPictureRuleVo.setNumLimit(specialPictureRuleDTO.getNumLimit());
        return specialPictureRuleVo;
    }
}
