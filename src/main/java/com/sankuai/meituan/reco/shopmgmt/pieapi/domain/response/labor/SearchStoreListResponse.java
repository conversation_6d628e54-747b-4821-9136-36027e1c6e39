package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.store.StoreVO;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-08-01
 * @email <EMAIL>
 */
@TypeDoc(
        description = "提交员工信息返回"
)
@Data
@ApiModel("提交员工信息返回")
@AllArgsConstructor
@NoArgsConstructor
public class SearchStoreListResponse {

    @FieldDoc(
            description = "工作类型列表"
    )
    private List<StoreVO> storeList;

}
