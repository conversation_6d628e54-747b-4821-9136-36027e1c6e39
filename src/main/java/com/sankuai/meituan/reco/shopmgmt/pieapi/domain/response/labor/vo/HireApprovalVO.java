package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-12-08
 * @email <EMAIL>
 */
@TypeDoc(
        description = "入职待审批/已审批列表请求返回"
)
@Data
@ApiModel("入职待审批/已审批列表请求返回")
@AllArgsConstructor
@NoArgsConstructor
public class HireApprovalVO {

    @FieldDoc(
            description = "申请id"
    )
    private Long approvalId;


    @FieldDoc(
            description = "员工id"
    )
    private Long employeeId;

    @FieldDoc(
            description = "员工姓名"
    )
    private String employeeName;

    @FieldDoc(
            description = "归属门店名称列表"
    )
    private List<String> belongStoreNameList;

    @FieldDoc(
            description = "手机号"
    )
    private String phoneNumber;

    @FieldDoc(
            description = "审核状态"
    )
    private Integer status;

    @FieldDoc(
            description = "审核状态"
    )
    private List<String> workTypeNameList;

}
