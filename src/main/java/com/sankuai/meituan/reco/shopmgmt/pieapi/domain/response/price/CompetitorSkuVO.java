package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @Author: wangyihao04
 * @Date: 2020-08-14 17:50
 * @Mail: <EMAIL>
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
public class CompetitorSkuVO {
    private Long storeId;
    private String storeName;
    private String skuName;
    private String spec;
    private Double onlinePrice;
    private Double discountPrice;
    private Double onlinePriceOf500g;
    private Integer sales30day;
    private Boolean oneself;
}
