package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.handler;


import com.meituan.linz.product.channel.EnhanceChannelType;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

import static com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ProjectConstants.EBLS_SYNC_ABNORMAL_COUNT;

/**
 * <AUTHOR>
 * @since 2024/7/24
 */
@Component
public class ElmSyncAbnormalCountHandler extends AbstractCountStoreSpuHandler {

    @Override
    public Integer getChannelId() {
        return EnhanceChannelType.ELEM.getChannelId();
    }

    @Override
    public String getCountCode() {
        return EBLS_SYNC_ABNORMAL_COUNT;
    }

    @Override
    public List<String> getAbnormalCodes() {
        // 同步异常
        return Collections.singletonList("2001");
    }
}
