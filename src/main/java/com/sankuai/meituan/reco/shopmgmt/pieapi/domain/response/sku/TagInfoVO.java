package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@TypeDoc(
        description = "商品标签"
)
@Data
@ApiModel("商品标签")
@NoArgsConstructor
public class TagInfoVO {

    @FieldDoc(
            description = "标签类型", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "标签类型", required = true)
    private int type;

    @FieldDoc(
            description = "标签名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "标签名称", required = true)
    private String name;
}
