package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.realtimesettle;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order.ChannelOrderKey;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: renhao
 * @Date: 2020/10/15
 * @Description:
 */
@TypeDoc(
        description = "商品支付信息"
)
@Data
public class SkuPayModel {

    @FieldDoc(
            description = "商品编码(赋能商品编码)"
    )
    private String skuId;

    @FieldDoc(
            description = "商品名称"
    )
    @NotNull(message = "商品名称不能为空")
    private String skuName;

    @FieldDoc(
            description = "规格"
    )
    @NotNull(message = "商品属性不能为空")
    private String attributes;

    @FieldDoc(
            description = "数量"
    )
    @NotNull(message = "数量不能为空")
    private Integer quantity;

    @FieldDoc(
            description = "支付金额(单位:分)"
    )
    @NotNull(message = "支付金额不能为空")
    private Long payAmount;

    @FieldDoc(
            description = "所属渠道订单key列表"
    )
    @Valid
    @NotEmpty(message = "渠道订单列表不能为空")
    private List<ChannelOrderKey> orderKeyList;
}