// Copyright (C) 2021 Meituan
// All rights reserved
package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.ProblemSpuDetailDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2021/2/25 下午2:27
 **/
@TypeDoc(
        description = "不一致商品"
)
@Data
public class ProblemSpuVO {
//    @FieldDoc(
//            description = "租户ID"
//    )
//    private Long tenantId;
//    @FieldDoc(
//            description = "门店ID"
//    )
//    private Long storeId;
    @FieldDoc(
            description = "SPUID"
    )
    private String spuId;
//    @FieldDoc(
//            description = "门店名称"
//    )
//    private String storeName;
    @FieldDoc(
            description = "商品状态 -1-未上线 1-已上架 2-已下架"
    )
    private Integer spuStatus;
    @FieldDoc(
            description = "商品名称"
    )
    private String spuName;
    @FieldDoc(
            description = "标签列表"
    )
    private List<String> tags;
    @FieldDoc(
            description = "不一致信息列表"
    )
    private List<ProblemInfoVO> problemInfoList;

    public static ProblemSpuVO of(ProblemSpuDetailDTO detailDTO, List<String> tags) {
        ProblemSpuVO spuVO = new ProblemSpuVO();
//        spuVO.setStoreId(detailDTO.getStoreId());
//        spuVO.setStoreName(storeNameMap.get(detailDTO.getStoreId()));
        spuVO.setSpuId(detailDTO.getSpuId());
        spuVO.setSpuName(detailDTO.getSpuName());
        spuVO.setSpuStatus(detailDTO.getStatus());
        spuVO.setTags(tags);
        spuVO.setProblemInfoList(ConverterUtils.convertList(detailDTO.getProblemSpuCompareInfoDTOS(), ProblemInfoVO::of));

        return spuVO;
    }
}
