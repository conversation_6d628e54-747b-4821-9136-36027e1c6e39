package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.google.common.base.Preconditions;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * @Author: wb_chengliang
 * @Date 2024/2/5
 */
@TypeDoc(
        description = "租户商品规格查询接口请求参数"
)
@Data
@ApiModel("租户商品规格查询接口请求参数")
public class QueryFranchiseTenantSkuBySpuIdApiRequest {

    @FieldDoc(
            description = "加盟商spuId"
    )
    @ApiModelProperty(value = "加盟商spuId")
    private String franchiseeSpuId;
}
