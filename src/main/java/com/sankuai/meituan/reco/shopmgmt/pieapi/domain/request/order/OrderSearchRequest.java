package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order;

import java.math.BigInteger;
import java.time.LocalDate;

import com.google.common.collect.Lists;

import javax.validation.constraints.NotNull;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.IntegerBooleanConstants;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.DateUtils;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 全部订单列表分页查询请求.
 *
 * <AUTHOR>
 * @since 2021/11/8 11:16
 */
@TypeDoc(
        description = "全部订单列表分页查询请求"
)
@ApiModel("全部订单列表分页查询请求")
@Data
public class OrderSearchRequest {

    @FieldDoc(
            description = "门店 ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店 ID")
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "第几页", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "第几页", required = true)
    @NotNull
    private Integer page;

    @FieldDoc(
            description = "每页行数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "每页行数", required = true)
    @NotNull
    private Integer size = 10;

    @FieldDoc(
            description = "关键词搜索", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "关键词搜索")
    @NotNull(message = "搜索词不能为空")
    private String keyword;

    @FieldDoc(
            description = "门店类型 3-门店 5-中心仓 6-前置仓", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店类型")
    private Integer entityType;

    public OrderListRequest build() {
        OrderListRequest orderListRequest = new OrderListRequest();
        orderListRequest.setStoreId(storeId);
        orderListRequest.setBeginCreateDate(DateUtils.minusDaysFormatter(IntegerBooleanConstants.SIXTY));
        orderListRequest.setEndCreateDate(DateUtils.addDaysFormatter());
        orderListRequest.setKeyword(keyword);
        orderListRequest.setPage(page);
        orderListRequest.setPageSize(size);
        orderListRequest.setEntityType(entityType);
        return orderListRequest;
    }

}
