package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-12-08
 * @email <EMAIL>
 */
@TypeDoc(
        description = "入职待审批/已审批详情请求返回"
)
@Data
@ApiModel("入职待审批/已审批详情请求返回")
@AllArgsConstructor
@NoArgsConstructor
public class HireApprovalDetailVO {

    @FieldDoc(
            description = "员工姓名"
    )
    private String employeeName;

    @FieldDoc(
            description = "归属门店名称列表"
    )
    private String belongStoreName;

    @FieldDoc(
            description = "申请时间，unix毫秒时间戳"
    )
    private Long applyDate;

    @FieldDoc(
            description = "手机号"
    )
    private String phoneNumber;

    @FieldDoc(
            description = "身份证号"
    )
    private String idCardNumber;

    @FieldDoc(
            description = "城市名称"
    )
    private String cityName;

    @FieldDoc(
            description = "城市名称"
    )
    private Long cityId;

    @FieldDoc(
            description = "工作类型名称"
    )
    private String workTypeName;

    @FieldDoc(
            description = "性别"
    )
    private String genderName;

    @FieldDoc(
            description = "渠道"
    )
    private String hireChannelName;

    @FieldDoc(
            description = "学历"
    )
    private String educationName;

    @FieldDoc(
            description = "性别"
    )
    private Integer gender;

    @FieldDoc(
            description = "渠道"
    )
    private Integer hireChannel;

    @FieldDoc(
            description = "学历"
    )
    private Integer education;

    @FieldDoc(
            description = "银行卡号"
    )
    private String bankcardNumber;

    @FieldDoc(
            description = "开户行"
    )
    private String openingBank;

    @FieldDoc(
            description = "所属门店类型"
    )
    private Integer poiOperationMode;

}
