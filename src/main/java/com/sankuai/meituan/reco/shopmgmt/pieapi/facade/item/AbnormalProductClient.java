package com.sankuai.meituan.reco.shopmgmt.pieapi.facade.item;

import com.meituan.linz.boot.util.Fun;
import com.sankuai.meituan.reco.shopmgmt.pieapi.converters.abnormal.AbnormalConverter;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.AbnormalRuleNodeVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ResponseHandler;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.AbnormalRuleNodeDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.SpuAbnormalInfoDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.SpuNoSaleChannelInfoDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreSpuKeyDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.BatchQueryPoiSpuAbnormalRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.BatchQuerySpuNoSaleChannelInfoRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.QueryAllAbnormalRuleRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.QueryLeafAbnormalRuleRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.BatchQuerySpuAbnormalResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.BatchQuerySpuNoSaleChannelInfoResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.QueryAbnormalRulesResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.service.AbnormalProductBizThriftService;
import com.sankuai.meituan.shangou.empower.productbiz.client.service.AbnormalRuleBizThriftService;
import com.sankuai.meituan.shangou.saas.common.runtime.RpcInvoker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Slf4j
@Service
public class AbnormalProductClient {
    private static final Integer FIRST_LEVEL = 1;

    @Resource
    private AbnormalRuleBizThriftService abnormalRuleBizThriftService;

    @Resource
    private AbnormalProductBizThriftService abnormalProductBizThriftService;

    public List<AbnormalRuleNodeDTO> queryAllAbnormalRule(Long merchantId, Long poiId) {
        QueryAllAbnormalRuleRequest request = new QueryAllAbnormalRuleRequest();
        request.setMerchantId(merchantId);
        request.setPoiId(poiId);
        QueryAbnormalRulesResponse response = RpcInvoker.invoke(() -> abnormalRuleBizThriftService.queryAllAbnormalRule(request));
        ResponseHandler.checkResponseAndStatus(response, r -> r.getStatus().getCode(), r -> r.getStatus().getMsg());

        return response.getAbnormalRuleNodes();
    }

    public Map<String, AbnormalRuleNodeVO> getAbnormalRuleMap(Long tenantId, Long poiId){
        List<AbnormalRuleNodeVO> allAbnormalRuleTree = getAllAbnormalRuleTree(tenantId, poiId);
        Map<String, AbnormalRuleNodeVO> abnormalRuleNodeMap = new HashMap<>();
        putToMap(abnormalRuleNodeMap, allAbnormalRuleTree);
        return abnormalRuleNodeMap;
    }

    public void putToMap(Map<String, AbnormalRuleNodeVO> abnormalRuleNodeMap, List<AbnormalRuleNodeVO> allAbnormalRuleTree){
        if (CollectionUtils.isEmpty(allAbnormalRuleTree)){
            return;
        }
        for (AbnormalRuleNodeVO abnormalRuleNode : allAbnormalRuleTree) {
            abnormalRuleNodeMap.put(abnormalRuleNode.getAbnormalCode(), abnormalRuleNode);
            putToMap(abnormalRuleNodeMap, abnormalRuleNode.getChildren());
        }
    }

    public List<AbnormalRuleNodeVO> getAllAbnormalRuleTree(Long merchantId, Long poiId) {
        List<AbnormalRuleNodeDTO> abnormalRuleDtos = queryAllAbnormalRule(merchantId, poiId);
        if (CollectionUtils.isEmpty(abnormalRuleDtos)) {
            return Collections.emptyList();
        }

        // 找到第一层的异常，构造异常类型树
        List<AbnormalRuleNodeDTO> firstLevelRules = Fun.filter(abnormalRuleDtos, rule -> rule.getLevel().equals(FIRST_LEVEL));
        Map<String, List<AbnormalRuleNodeDTO>> parentCodeMap = Fun.groupingBy(abnormalRuleDtos, AbnormalRuleNodeDTO::getParentCode);

        return Fun.map(firstLevelRules, rule -> AbnormalConverter.convert(rule, parentCodeMap));
    }

    public List<SpuNoSaleChannelInfoDTO> querySpuNoSaleChannelInfo(Long tenantId, List<StoreSpuKeyDTO> storeSpuKeyDTOs, boolean isStoreAbnormalQuery, Boolean filterByAbnormalCode) {
        if (CollectionUtils.isEmpty(storeSpuKeyDTOs)){
            return Collections.emptyList();
        }

        BatchQuerySpuNoSaleChannelInfoRequest request = new BatchQuerySpuNoSaleChannelInfoRequest();
        request.setTenantId(tenantId);
        request.setStoreSpuKeyDTOS(storeSpuKeyDTOs);
        request.setIsStoreAbnormalQuery(isStoreAbnormalQuery);
        request.setFilterByAbnormalCode(filterByAbnormalCode);
        BatchQuerySpuNoSaleChannelInfoResponse response = RpcInvoker.invoke(() -> abnormalProductBizThriftService.batchQuerySpuNoSaleChannelInfo(request));
        ResponseHandler.checkResponseAndStatus(response, r -> r.getStatus().getCode(), r -> r.getStatus().getMsg());

        return response.getSpuNoSaleChannelInfoDTOList();
    }

    public static List<String> matchAbnormalCodes(List<String> abnormalCodes, Map<String, AbnormalRuleNodeVO> abnormalRuleNodeMap) {
        Set<String> matchAbnormalCodes = new HashSet<>();
        for (String abnormalCode : abnormalCodes) {
            AbnormalRuleNodeVO abnormalRuleNode = abnormalRuleNodeMap.get(abnormalCode);
            if (Objects.nonNull(abnormalRuleNode)) {
                addToSet(matchAbnormalCodes, abnormalRuleNode);
            }
        }
        return new ArrayList<>(matchAbnormalCodes);
    }

    private static void addToSet(Set<String> matchAbnormalCodes, AbnormalRuleNodeVO abnormalRuleNode) {
        matchAbnormalCodes.add(abnormalRuleNode.getAbnormalCode());
        if (CollectionUtils.isNotEmpty(abnormalRuleNode.getChildren())){
            for (AbnormalRuleNodeVO childAbnormalRule : abnormalRuleNode.getChildren()) {
                addToSet(matchAbnormalCodes, childAbnormalRule);
            }
        }
    }
}
