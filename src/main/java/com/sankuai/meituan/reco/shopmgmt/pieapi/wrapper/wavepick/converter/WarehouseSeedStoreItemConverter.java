package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.wavepick.converter;

/**
 * @Description:
 * @Author: zhangjian155
 * @Date: 2022/8/25 18:16
 */

import com.sankuai.meituan.reco.pickselect.thrift.wave.dto.WarehouseSeedStoreItemDTO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.WarehouseSeedStoreItemModuleVO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public abstract class WarehouseSeedStoreItemConverter {
    public abstract WarehouseSeedStoreItemModuleVO convert2Vo(WarehouseSeedStoreItemDTO warehouseSeedStoreItemDTO);
}
