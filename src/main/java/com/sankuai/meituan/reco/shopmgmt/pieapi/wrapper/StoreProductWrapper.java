package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import java.util.List;

import com.meituan.linz.boot.util.Fun;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.CreateStoreSpuApiRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.FastCreateSpuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.StoreSpuConfigDetailVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.SpuStoreConfigQueryRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.StoreSpuFastCreateRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.SpuStoreConfigQueryResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.StoreSpuFastCreateResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.service.SpuStoreConfigThriftService;
import com.sankuai.meituan.shangou.empower.productbiz.client.service.StoreSpuBizThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * FileName: StoreProductWrapper
 * Author:   wangjiawei31
 * Date:     2021/3/11 2:55 下午
 * Description:
 */
@Service
@Slf4j
public class StoreProductWrapper {
    @Resource
    private StoreSpuBizThriftService storeSpuBizThriftService;
    @Resource
    private SpuStoreConfigThriftService storeConfigThriftService;

    public CommonResponse<FastCreateSpuVO> fastCreateStoreSpu(CreateStoreSpuApiRequest request) {
        try {
            CommonResponse<FastCreateSpuVO> commonResponse = new CommonResponse<>();
            User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
            StoreSpuFastCreateRequest rpcRequest = request.toRpcRequest(user);
            log.info("调用storeSpuThriftService.fastCreateStoreSpu() request:{}", rpcRequest);
            StoreSpuFastCreateResponse response = storeSpuBizThriftService.fastCreateStoreSpu(rpcRequest);

            FastCreateSpuVO vo = new FastCreateSpuVO();
            if (request.getPriceInitType() == 0) {
                vo.setNeedJumpPrice(true);
            }
            vo.setSpuId(response.getSpuId());
            commonResponse.setData(vo);
            commonResponse.setCode(response.getStatus().getCode());
            commonResponse.setMessage(response.getStatus().getMsg());
            return commonResponse;
        } catch (TException e) {
            throw new CommonRuntimeException(e);
        }
    }

    public List<StoreSpuConfigDetailVO> queryStoreSpuConfig(Long tenantId, Long storeId, String configPageType) {
        SpuStoreConfigQueryRequest spuStoreConfigQueryRequest = new SpuStoreConfigQueryRequest();
        spuStoreConfigQueryRequest.setTenantId(tenantId);
        spuStoreConfigQueryRequest.setStoreId(storeId);
        spuStoreConfigQueryRequest.setConfigPageType(configPageType);
        SpuStoreConfigQueryResponse response = storeConfigThriftService.querySpuStoreConfigList(spuStoreConfigQueryRequest);
        if (response.getStatus().getCode() != 0) {
            throw new RuntimeException(response.getStatus().getMsg());
        }
        return Fun.map(response.getSpuStoreConfigDTOList(), StoreSpuConfigDetailVO::ofDTO);
    }
}
