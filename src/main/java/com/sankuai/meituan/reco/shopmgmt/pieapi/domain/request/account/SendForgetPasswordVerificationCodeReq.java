package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.account;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.request.SendForgetPassWordVerificationCodeRequest;
import com.sankuai.meituan.shangou.saas.common.aop.feature.Validatable;
import com.sankuai.meituan.shangou.saas.common.utils.AssertUtil;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/05/04
 */
@TypeDoc(
        description = "发送忘记密码短信验证码请求"
)
@Data
public class SendForgetPasswordVerificationCodeReq implements Validatable {

    @FieldDoc(
            description = "web灵犀sdk获取的uuid"

    )
    private String uuid;

    @FieldDoc(
            description = "账号名"
    )
    private String accountName;


    @FieldDoc(
            description = "脱敏手机号"
    )
    private String maskMobile;

    @Override
    public void validate() {
        AssertUtil.isTrue(StringUtils.isNotBlank(uuid), "uuid 不能为空");
        AssertUtil.isTrue(StringUtils.isNotBlank(accountName), "accountName 不能为空");
        AssertUtil.isTrue(StringUtils.isNotBlank(maskMobile), "脱敏手机号 不能为空");
    }

    public SendForgetPassWordVerificationCodeRequest toThriftRequest() {
        SendForgetPassWordVerificationCodeRequest request = new SendForgetPassWordVerificationCodeRequest();
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        String os = identityInfo.getOs();
        request.setAccountName(accountName);
        HttpServletRequest httpRequest =
                ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String ip = httpRequest.getHeader("x-real-ip");
        String ua = httpRequest.getHeader("user-agent");
        request.setUuid(uuid);
        request.setIp(ip);
        request.setUa(ua);
        request.setPlatform(os);
        request.setVersion(identityInfo.getAppVersion());
        request.setMaskMobile(maskMobile);
        return request;
    }

}
