// Copyright (C) 2021 Meituan
// All rights reserved
package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2021/2/25 下午2:27
 **/
@TypeDoc(
        description = "不一致商品"
)
@Data
public class ProblemSpuQueryResponseVO {
    @FieldDoc(
            description = "商品信息列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品信息列表", required = true)
    private List<ProblemSpuVO> problemSpuList;

    @FieldDoc(
            description = "分页信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "分页信息", required = true)
    private PageInfoVO pageInfo;
}
