package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.appraise;

import com.meituan.shangou.saas.resource.management.common.enums.AppraisePlanTypeEnum;
import com.meituan.shangou.saas.resource.management.dto.AppraiseResultWithIndicatorDTO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.RulePassedEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import lombok.Data;
import lombok.ToString;

import java.util.Arrays;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2020/9/15 16:33
 * @Description:
 */
@Data
@ToString
public class AppraiseResultHistoryByPlanTypeVO {




    private String planType;

    private String planTypeName;

    private Integer total;

    private Integer passedCount;

    private Integer failCount;

    private Integer detailQueryType;

    private String amountTitle;


    private List<AppraiseResultHistoryItemVO> details;



    public AppraiseResultHistoryByPlanTypeVO(Integer planTpe, List<AppraiseResultWithIndicatorDTO> results,List<String> permissionCodes) {

        this.planType = AppraisePlanTypeEnum.getByType(planTpe).name();
        this.planTypeName = AppraisePlanTypeEnum.getByType(planTpe).getDesc();

        this.detailQueryType = 2;

        this.amountTitle = Arrays.asList(AppraisePlanTypeEnum.SERVICE_STAR.name(), AppraisePlanTypeEnum.GOAL_BASED_INSPIRATION.name())
                .contains(planType) ? "奖励金额" : "惩罚金额";

        this.details =  ConverterUtils.convertList(results, r -> new AppraiseResultHistoryItemVO(r,permissionCodes));

        total = this.details.size();
        passedCount = (int)details.stream().filter(d -> d.getRulePassed() != null && d.getRulePassed() == RulePassedEnum.PASSED.getCode()).count();
        failCount = (int)details.stream().filter(d -> d.getRulePassed() != null && d.getRulePassed() == RulePassedEnum.NOT_PASSED.getCode()).count();

    }


}
