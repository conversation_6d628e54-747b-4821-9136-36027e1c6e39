package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "分拣商品信息"
)
@Data
@ApiModel("分拣商品信息")
public class WarehouseSeedSkuItemVO {

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @FieldDoc(
            description = "商品sku", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "商品sku")
    private String skuId;

    @FieldDoc(
            description = "规格", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "规格")
    private String spec;

    @FieldDoc(
            description = "品类信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "品类信息")
    private String categoryName;

    @FieldDoc(
            description = "商品图片列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "商品图片列表")
    private List<String> picList;

    @FieldDoc(
            description = "upc列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "upc列表")
    private List<String> upcList;

    @FieldDoc(
            description = "待分拣/已分拣商品数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "待分拣/已分拣商品数量")
    private Integer seedNum;

    @FieldDoc(
            description = "分拣商品对应门店信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "分拣商品对应门店信息")
    List<WarehouseSeedStoreItemModuleVO> storeItemList;

    @FieldDoc(
            description = "分拣任务id", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "分拣任务id")
    Long pickTaskId;

    @FieldDoc(
            description = "已拣货账号id", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "已拣货账号id")
    private Long pickedAccountId;

    @FieldDoc(
            description = "已拣货操作人", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "已拣货操作人")
    private String pickedOperateName;

    @FieldDoc(
            description = "拣货箱规单位", requiredness = Requiredness.OPTIONAL
    )
    private SkuPackingSpecVO pickPackingSpec;

    @FieldDoc(
            description = "库存基本单位名称", requiredness = Requiredness.OPTIONAL
    )
    private String basicUnit;

    @FieldDoc(
            description = "装箱模式已装箱数量", requiredness = Requiredness.OPTIONAL
    )
    private Integer packNum;
}
