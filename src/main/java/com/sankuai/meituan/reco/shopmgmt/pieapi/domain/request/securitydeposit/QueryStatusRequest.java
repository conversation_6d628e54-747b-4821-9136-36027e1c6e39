package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.securitydeposit;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * @Author: <EMAIL>
 * @Date: 2020/3/2 21:28
 * @Description:
 */
@TypeDoc(
        description = "StoreId请求"
)
@Data
public class QueryStatusRequest {


    @FieldDoc(
            description = "门店ID"
    )
    private String storeId;

    @FieldDoc(
            description = "是否需要生成确认函，true：需要，false：不需要（默认）"
    )
    private Boolean needGenerateConfirmLetter;

}
