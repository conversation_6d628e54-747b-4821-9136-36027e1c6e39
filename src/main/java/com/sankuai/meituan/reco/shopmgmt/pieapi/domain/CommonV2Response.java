package com.sankuai.meituan.reco.shopmgmt.pieapi.domain;

import java.text.MessageFormat;
import java.util.Objects;

import org.apache.commons.lang.StringUtils;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ParamCheckUtils;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * wangjian146
 * @param <T>
 */
@TypeDoc(
        description = "通用返回结果"
)
@ApiModel("通用返回结构")
public class CommonV2Response<T> {

    @FieldDoc(
            description = "错误码"
    )
    @ApiModelProperty(value = "错误码", required = true)
    private int code;

    @FieldDoc(
            description = "错误消息"
    )
    @ApiModelProperty(value = "错误消息")
    private String msg;

    @FieldDoc(
            description = "返回内容"
    )
    private T data;

    public CommonV2Response(int code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public CommonV2Response() {
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public static <U> CommonV2Response<U> success(U val) {
        return new CommonV2Response<>(ResultCode.SUCCESS.getCode(), "", val);
    }

    public static <U> CommonV2Response<U> fail(int resultCode, String msg) {
        return new CommonV2Response<>(resultCode, msg, null);
    }

    public static <U> CommonV2Response<U> fail(int resultCode, String msg, U val) {
        return new CommonV2Response<>(resultCode, msg, val);
    }

    public static CommonV2Response<Void> fail(ResultCode resultCode) {
        return fail(resultCode, (String) null);
    }

    public static <T> CommonV2Response<T> fail2(ResultCode resultCode) {
        return fail(resultCode.code, resultCode.getErrorMessage());
    }

    public static CommonV2Response<Void> fail(ResultCode resultCode, String msg) {
        ParamCheckUtils.nullCheck(resultCode, "resultCode cannot be null");

        String errorMsg = msg;
        if (StringUtils.isEmpty(errorMsg)) {
            errorMsg = resultCode.getErrorMessage();
        }
        return new CommonV2Response<>(resultCode.getCode(), errorMsg, null);
    }

    public static CommonV2Response<Void> fail(ResultCode resultCode, Throwable e) {
        Objects.requireNonNull(e, "exception must not be null");
        return fail(resultCode, e.getMessage());
    }

    public static CommonV2Response<Void> fail(ResultCode resultCode, Object[] params) {
        ParamCheckUtils.nullCheck(resultCode, "resultCode cannot be null");

        return new CommonV2Response<>(resultCode.getCode(), MessageFormat.format(resultCode.getErrorMessage(), params), null);
    }

    public boolean isSuccess() {
        return this.code == ResultCode.SUCCESS.getCode();
    }

}
