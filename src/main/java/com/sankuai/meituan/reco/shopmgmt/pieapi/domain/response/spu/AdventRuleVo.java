package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.dto.AdventRuleDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/3/27
 * @Description
 */
@TypeDoc(
        description = "类目临期推荐规则"
)
@ApiModel("类目临期推荐规则")
@Getter
@Setter
@ToString
public class AdventRuleVo {
    @FieldDoc(
            description = "保质期最小值（闭区间"
    )
    @ApiModelProperty(name = "保质期最小值（闭区间")
    private Integer lowerLimit;

    @FieldDoc(
            description = "保质期最大值（开区间）"
    )
    @ApiModelProperty(name = "保质期最大值（开区间）")
    private Integer upperLimit;

    @FieldDoc(
            description = "临期标准，单位天"
    )
    @ApiModelProperty(name = "临期标准，单位天")
    private Integer advent;

    @FieldDoc(
            description = "临期标准类型"
    )
    private Integer type;

    public static AdventRuleVo of(AdventRuleDTO dto) {
        AdventRuleVo vo = new AdventRuleVo();
        vo.setAdvent(dto.getAdvent());
        vo.setLowerLimit(dto.getLowerLimit());
        vo.setUpperLimit(dto.getUpperLimit());
        vo.setType(dto.getType());
        return vo;
    }
}
