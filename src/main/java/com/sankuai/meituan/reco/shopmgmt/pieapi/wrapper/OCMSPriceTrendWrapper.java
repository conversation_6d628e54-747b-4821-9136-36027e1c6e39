package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.google.common.collect.Maps;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.PriceTrendConstants;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ProjectConstants;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.StoreSkuKey;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.priceconfig.StoreSkuPriceFilter;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.priceconfig.StoreSkuPriceFilterCondition;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pricetrend.ChannelSkuPriceTrendVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pricetrend.ChannelSkuPriceWithPriceTrendVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pricetrend.StoreSkuWithChannelPriceTrendVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.StoreSkuBaseDetailVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.DateUtils;
import com.sankuai.meituan.shangou.empower.price.client.dto.ChannelOnlinePriceDTO;
import com.sankuai.meituan.shangou.empower.price.client.request.ChannelOnlinePriceQueryRequest;
import com.sankuai.meituan.shangou.empower.price.client.response.ChannelOnlinePriceQueryResponse;
import com.sankuai.meituan.shangou.empower.price.client.service.ChannelPriceThriftService;
import com.sankuai.meituan.shangou.saas.crm.data.client.dto.price.StoreSkuPriceTrendDTO;
import com.sankuai.meituan.shangou.saas.crm.data.client.dto.price.StoreSkuPriceTrendDetailDTO;
import com.sankuai.meituan.shangou.saas.crm.data.client.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.saas.crm.data.client.request.price.StoreSkuPriceTrendQueryRequest;
import com.sankuai.meituan.shangou.saas.crm.data.client.response.price.StoreSkuPriceTrendQueryResponse;
import com.sankuai.meituan.shangou.saas.crm.data.client.service.price.PriceTrendThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 价格趋势
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class OCMSPriceTrendWrapper {

    @Resource
    private PriceTrendThriftService priceTrendThriftService;

    @Resource(name = "priceAppChannelPriceThriftService")
    private ChannelPriceThriftService channelPriceThriftService;

    @Resource
    private SaasPriceServiceWrapper saasPriceServiceWrapper;

    @Resource
    private AuthThriftWrapper authThriftWrapper;

    public List<ChannelSkuPriceWithPriceTrendVO> batchQueryStoreSkuChannelPriceTrend(
            Long tenantId, Long storeId, Integer channelId, List<String> skuIds,
            List<StoreSkuBaseDetailVO> storeSkuBaseDetailVOList) {

        // 查询账号价格趋势权限
        Map<String, Boolean> priceTrendPermissionMap = authThriftWrapper.isHasPermission(
                PriceTrendConstants.getPriceTrendPermissionCodes());

        // 查询门店商品价格过滤器
        List<StoreSkuPriceFilterCondition> filterConditionList =
                ConverterUtils.convertList(storeSkuBaseDetailVOList, StoreSkuPriceFilterCondition::build);
        Map<StoreSkuKey, StoreSkuPriceFilter> storeSkuKe2FilterMap =
                saasPriceServiceWrapper.queryStoreSkuPriceFilterMap(tenantId, filterConditionList);

        // 查询价格趋势
        Map<StoreSkuKey, StoreSkuWithChannelPriceTrendVO> storeSkuKey2PriceTrendVOMap =
                queryStoreSkuPriceTrendMapByFilterCondition(tenantId, filterConditionList, 
                        PriceTrendConstants.STORE_SKU_PRICE_TREND_DEFAULT_QUERY_DAYS);
        Map<StoreSkuKey, ChannelSkuPriceTrendVO> storeSkuKey2ChannelSkuPriceTrendVOMap = Maps.newHashMap();
        storeSkuKey2PriceTrendVOMap.forEach((storeSkuKey, priceTrendVO) -> {
            if (priceTrendVO == null) {
                return;
            }
            ChannelSkuPriceTrendVO channelSkuPriceTrendVO =
                    priceTrendVO.generateChannelSkuPriceTrendVO(channelId, priceTrendPermissionMap);
            storeSkuKey2ChannelSkuPriceTrendVOMap.put(storeSkuKey, channelSkuPriceTrendVO);
        });
        
        // 查询商品价格
        Map<String, Long> skuId2PriceMap = getSkuId2PriceMap(tenantId, storeId, channelId, skuIds, storeSkuBaseDetailVOList);

        // 构建商品信息、价格信息、价格趋势
        List<ChannelSkuPriceWithPriceTrendVO> channelSkuPriceWithPriceTrendVOList = Lists.newArrayList();
        for (StoreSkuBaseDetailVO storeSkuBaseDetailVO : storeSkuBaseDetailVOList) {
            String skuId = storeSkuBaseDetailVO.getSkuId();
            StoreSkuKey storeSkuKey = new StoreSkuKey(storeId, skuId);
            Long price = skuId2PriceMap.get(storeSkuBaseDetailVO.getSkuId()) != null ?
                    skuId2PriceMap.get(storeSkuBaseDetailVO.getSkuId()) : 0L;
            ChannelSkuPriceWithPriceTrendVO channelSkuPriceTrendVO = ChannelSkuPriceWithPriceTrendVO.build(
                    storeSkuBaseDetailVO, storeSkuKey2ChannelSkuPriceTrendVOMap.get(storeSkuKey),
                    storeSkuKe2FilterMap.get(storeSkuKey), price, storeSkuBaseDetailVO.getWeight());
            channelSkuPriceWithPriceTrendVOList.add(channelSkuPriceTrendVO);
        }

        return channelSkuPriceWithPriceTrendVOList;
    }


    @MethodLog(logRequest = true, logResponse = true)
    public StoreSkuWithChannelPriceTrendVO queryStoreSkuPriceTrend(
            Long tenantId, StoreSkuPriceFilter filter, int queryDays) {

        // 查询一个门店商品的价格趋势
        Map<StoreSkuKey, StoreSkuPriceFilter> storeSkuKey2FilterMap = Maps.newHashMap();
        storeSkuKey2FilterMap.put(new StoreSkuKey(filter.getStoreId(), filter.getSkuId()), filter);

        List<StoreSkuWithChannelPriceTrendVO> priceTrendDomainList =
                queryStoreSkuPriceTrendList(tenantId, storeSkuKey2FilterMap, queryDays);
        if (CollectionUtils.isEmpty(priceTrendDomainList)) {
            return null;
        }

        return priceTrendDomainList.get(0);
    }

    @MethodLog(logRequest = true, logResponse = true)
    public Map<StoreSkuKey, StoreSkuWithChannelPriceTrendVO> queryStoreSkuPriceTrendMapByFilterCondition(
            Long tenantId, List<StoreSkuPriceFilterCondition> filterConditionList, int queryDays) {

        if (CollectionUtils.isEmpty(filterConditionList)) {
            return Collections.emptyMap();
        }

        // 查询门店商品价格过滤器
        List<StoreSkuPriceFilter> StoreSkuPriceFilterList =
                saasPriceServiceWrapper.queryStoreSkuPriceFilter(tenantId, filterConditionList);

        Map<StoreSkuKey, StoreSkuPriceFilter> storeSkuKey2FilterMap =
                Optional.ofNullable(StoreSkuPriceFilterList).map(List::stream).orElse(Stream.empty())
                        .collect(Collectors.toMap(domain -> new StoreSkuKey(domain.getStoreId(), domain.getSkuId()),
                                Function.identity(), (oldDomain, newDomain) -> newDomain));

        // 查询门店商品价格趋势
        return queryStoreSkuPriceTrendMapByFilter(tenantId, storeSkuKey2FilterMap, queryDays);
    }

    @MethodLog(logRequest = true, logResponse = true)
    public Map<StoreSkuKey, StoreSkuWithChannelPriceTrendVO> queryStoreSkuPriceTrendMapByFilter(
            Long tenantId, Map<StoreSkuKey, StoreSkuPriceFilter> storeSkuKey2FilterMap, int queryDays) {

        // 查询门店商品价格趋势列表
        List<StoreSkuWithChannelPriceTrendVO> priceTrendDomainList =
                queryStoreSkuPriceTrendList(tenantId, storeSkuKey2FilterMap, queryDays);
        if (CollectionUtils.isEmpty(priceTrendDomainList)) {
            return Collections.emptyMap();
        }

        // 将门店商品价格趋势列表转为map
        return priceTrendDomainList.stream()
                .collect(Collectors.toMap(domain -> new StoreSkuKey(domain.getStoreId(), domain.getSkuId()),
                        Function.identity(), (oldDomain, newDomain) -> newDomain));
    }

    @MethodLog(logRequest = true, logResponse = true)
    public List<StoreSkuWithChannelPriceTrendVO> queryStoreSkuPriceTrendList(
            Long tenantId, Map<StoreSkuKey, StoreSkuPriceFilter> storeSkuKey2FilterMap, int queryDays) {

        if (MapUtils.isEmpty(storeSkuKey2FilterMap)) {
            return Collections.emptyList();
        }

        // 计算查询开始日期和结束日期, 结束日期为当前日期减1天
        LocalDate currentDay = LocalDate.now();
        String beginDate = DateUtils.format(currentDay.minusDays(queryDays), DateUtils.YYYY_MM_DD);
        String endDate = DateUtils.format(currentDay.minusDays(1), DateUtils.YYYY_MM_DD);

        // 按照门店id对商品编码分组
        Map<Long, List<String>> storeId2SkuIdsMap = storeSkuKey2FilterMap.keySet().stream()
                .collect(Collectors.groupingBy(StoreSkuKey::getStoreId,
                        Collectors.mapping(StoreSkuKey::getSkuId, Collectors.toList())));

        // 查询门店商品价格趋势
        StoreSkuPriceTrendDTO storeSkuPriceTrendDTO =
                queryStoreSkuPriceTrend(tenantId, storeId2SkuIdsMap, beginDate, endDate);

        if (storeSkuPriceTrendDTO == null || CollectionUtils.isEmpty(storeSkuPriceTrendDTO.getPriceTrendDetailDTOList())) {
            return Collections.emptyList();
        }

        // 转换结果, 根据门店商品价格过滤器对价格趋势信息过滤
        List<StoreSkuPriceTrendDetailDTO> priceTrendDetailDTOList = storeSkuPriceTrendDTO.getPriceTrendDetailDTOList();
        List<String> dateList = storeSkuPriceTrendDTO.getDateList();
        return ConverterUtils.convertList(priceTrendDetailDTOList, dto -> {
            StoreSkuKey storeSkuKey = new StoreSkuKey(dto.getStoreId(), dto.getSkuId());
            StoreSkuPriceFilter filter = storeSkuKey2FilterMap.get(storeSkuKey);
            return StoreSkuWithChannelPriceTrendVO.buildStoreSkuWithChannelPriceTrendVO(dto, dateList, filter);
        });
    }

    private StoreSkuPriceTrendDTO queryStoreSkuPriceTrend(Long tenantId, Map<Long, List<String>> storeId2SkuIdsMap,
                                                         String beginDate, String endDate) {
        try {
            StoreSkuPriceTrendQueryRequest request = new StoreSkuPriceTrendQueryRequest();
            request.setTenantId(tenantId);
            request.setStoreId2SkuIdsMap(storeId2SkuIdsMap);
            request.setBeginDate(beginDate);
            request.setEndDate(endDate);

            StoreSkuPriceTrendQueryResponse response = priceTrendThriftService.queryStoreSkuPriceTrend(request);
            log.info("查询门店商品价格趋势, request:{}, response:{}", request, response);

            if (response.getCode() != ResultCodeEnum.SUCCESS) {
                throw new CommonRuntimeException(response.getMsg(), ResultCode.FAIL);
            }

            return response.getData();
        } catch (TException e) {
            throw new CommonRuntimeException(e);
        }
    }

    private Map<String, Long> getSkuId2PriceMap(Long tenantId, Long storeId, Integer channelId, List<String> skuIds,
                                                List<StoreSkuBaseDetailVO> storeSkuBaseDetailVOList) {
        Map<String, Long> skuId2PriceMap;
        if (ProjectConstants.OFFLINE_CHANNEL_ID == channelId) {
            skuId2PriceMap = Maps.newHashMap();
            for (StoreSkuBaseDetailVO skuBaseDetailVO : storeSkuBaseDetailVOList) {
                Double storePrice = skuBaseDetailVO.getStorePrice();
                Long price = storePrice != null ? BigDecimal.valueOf(storePrice).multiply(BigDecimal.valueOf(100)).longValue() : null;
                skuId2PriceMap.put(skuBaseDetailVO.getSkuId(), price);
            }
        } else {
            skuId2PriceMap = queryChannelOnlinePrice(tenantId, storeId, channelId, skuIds);
        }
        return MapUtils.isNotEmpty(skuId2PriceMap) ? skuId2PriceMap: Collections.emptyMap();
    }

    public Map<String, Long> queryChannelOnlinePrice(Long tenantId, Long storeId, Integer channelId, List<String> skuIds) {

        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyMap();
        }

        List<ChannelOnlinePriceDTO> channelOnlinePriceDTOList = queryChannelOnlinePriceBySkuIds(tenantId, storeId,
                channelId, skuIds);

        if (CollectionUtils.isEmpty(channelOnlinePriceDTOList)) {
            return Collections.emptyMap();
        }

        return channelOnlinePriceDTOList.stream().collect(
                Collectors.toMap(ChannelOnlinePriceDTO::getSkuId, ChannelOnlinePriceDTO::getPrice));
    }

    private List<ChannelOnlinePriceDTO> queryChannelOnlinePriceBySkuIds(Long tenantId, Long storeId, Integer channelId, List<String> skuIds) {
        ChannelOnlinePriceQueryRequest request = new ChannelOnlinePriceQueryRequest();
        request.setTenantId(tenantId);
        request.setStoreId(storeId);
        request.setChannelId(channelId);
        request.setSkuIds(skuIds);
        try {
            ChannelOnlinePriceQueryResponse response = channelPriceThriftService.queryChannelOnlinePriceBySkuIds(request);
            log.info("查询渠道价格, request:{}, response:{}", request, response);
            if (response.getCode() != 0) {
                throw new CommonRuntimeException(response.getMsg(), ResultCode.FAIL);
            }
            return response.getChannelOnlinePriceDTOList();
        } catch (TException e) {
            throw new CommonRuntimeException(e);
        }
    }
}
