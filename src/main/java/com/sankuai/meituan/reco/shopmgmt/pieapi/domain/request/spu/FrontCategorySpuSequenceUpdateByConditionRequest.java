package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.FieldSort;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import lombok.Data;
import lombok.NoArgsConstructor;

@TypeDoc(
        description = "店内分类下商品排序更新请求"
)
@Data
@NoArgsConstructor
public class FrontCategorySpuSequenceUpdateByConditionRequest {

    @FieldDoc(
            description = "门店id"
    )
    private Long storeId;

    @FieldDoc(
            description = "渠道id"
    )
    private Integer channelId;

    @FieldDoc(
            description = "店内分类id"
    )
    private Long frontCategoryId;

    @FieldDoc(
            description = "排序字段信息"
    )
    private FieldSort fieldSort;

    public void validate() {
        if (this.storeId == null) {
            throw new CommonLogicException("门店id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (this.channelId == null) {
            throw new CommonLogicException("渠道id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (this.frontCategoryId == null) {
            throw new CommonLogicException("店内分类id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (this.fieldSort == null) {
            throw new CommonLogicException("排序字段信息不能为空", ResultCode.CHECK_PARAM_ERR);
        }
    }
}
