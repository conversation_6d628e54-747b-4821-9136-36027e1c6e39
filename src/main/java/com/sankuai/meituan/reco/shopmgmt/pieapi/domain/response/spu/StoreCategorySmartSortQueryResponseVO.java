package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import lombok.Data;


/**
 * <AUTHOR>
 * @since 2023/10/30
 */

@TypeDoc(
        description = "分类智能排序设置"
)
@Data
@ApiModel("分类智能排序设置")
public class StoreCategorySmartSortQueryResponseVO {
    @FieldDoc(
            description = "店内分类id", requiredness = Requiredness.REQUIRED
    )
    private String categoryId;

    @FieldDoc(
            description = "智能排序", requiredness = Requiredness.REQUIRED
    )
    private Boolean smartSort;
}
