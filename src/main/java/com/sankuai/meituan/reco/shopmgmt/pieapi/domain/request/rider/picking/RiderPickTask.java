package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.picking;

import java.util.List;
import java.util.Optional;

import javax.validation.constraints.NotNull;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 骑手拣货任务.
 *
 * <AUTHOR>
 * @since 2021/11/12 16:30
 */
@TypeDoc(
        description = "骑手拣货任务"
)
@ApiModel("骑手拣货任务")
@Data
public class RiderPickTask {

    @FieldDoc(
            description = "拣货任务 ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "拣货任务 ID", required = true)
    @NotNull
    private Long pickingTaskId;

    @FieldDoc(
            description = "实际拣货数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "实际拣货数量", required = true)
    @NotNull
    private Integer pickedCount;

    @FieldDoc(
            description = "拣货出库录入方式 1-扫码出库 2-标记出库", requiredness = Requiredness.OPTIONAL
    )
    private Integer pickItemStockOutEnteringType;

    @FieldDoc(
            description = "sn信息", requiredness = Requiredness.OPTIONAL
    )
    private List<SnInfo> snInfoList;

    public Optional<String> validate() {
        if (pickingTaskId == null || pickingTaskId <= 0) {
            return Optional.of("存在拣货任务 ID 无效");
        }
        if (pickedCount == null || pickedCount < 0) {
            return Optional.of("存在拣货任务的拣货数量无效");
        }
        return Optional.empty();
    }
}
