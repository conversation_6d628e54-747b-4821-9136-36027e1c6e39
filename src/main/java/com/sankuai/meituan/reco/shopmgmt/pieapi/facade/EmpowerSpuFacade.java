package com.sankuai.meituan.reco.shopmgmt.pieapi.facade;


import com.meituan.linz.boot.exception.ServiceRpcException;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.command.PageQueryPoiSpuCommand;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.result.PageQueryPoiSpuResult;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.service.EmpowerPoiSpuThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class EmpowerSpuFacade {

    @Resource
    private EmpowerPoiSpuThriftService.Iface empowerPoiSpuThriftService;


    public PageQueryPoiSpuResult pageQueryStoreSpu(PageQueryPoiSpuCommand command) {

        try {
            PageQueryPoiSpuResult poiSpuResult = empowerPoiSpuThriftService.pageQueryPoiSpu(command);
            if (poiSpuResult.getStatus().getCode() != 0) {
                log.error("查询商品失败, queryCmd:{}, msg:{}", command, poiSpuResult.getStatus().getMsg());
                throw new ServiceRpcException("查询商品失败");
            }

            return poiSpuResult;
        }
        catch (TException e) {
            log.error("pageQueryPoiSpu error, queryCmd:{}", command, e);
            throw new ServiceRpcException("查询商品失败", e);
        }
    }
}
