package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TypeDoc(
        description = "查询已拣货请求"
)
@ApiModel("查询已拣货请求")
@Data
public class WarehousePickCompleteQueryRequest {

    @FieldDoc(
            description = "关键词", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "关键词")
    private String keyword;

    @FieldDoc(
            description = "页码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "页码")
    private Integer pageNo;

    @FieldDoc(
            description = "分页大小", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "分页大小")
    private Integer pageSize;


}
