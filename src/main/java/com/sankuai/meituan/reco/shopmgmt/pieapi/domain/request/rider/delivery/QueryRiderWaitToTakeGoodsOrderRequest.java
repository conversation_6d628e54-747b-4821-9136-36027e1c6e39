package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.delivery;

import javax.validation.constraints.NotNull;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 查询等待骑手取货的订单的请求.
 *
 * <AUTHOR>
 * @since 2021/6/11 17:12
 */
@TypeDoc(
        description = "查询等待骑手取货的订单的请求"
)
@ApiModel("查询等待骑手取货的订单的请求")
@Data
public class QueryRiderWaitToTakeGoodsOrderRequest {

    @FieldDoc(
            description = "第几页", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "第几页", required = true)
    @NotNull
    private Integer page;

    @FieldDoc(
            description = "每页行数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "每页行数", required = true)
    @NotNull
    private Integer size;
}
