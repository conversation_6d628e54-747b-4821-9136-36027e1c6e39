package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/11/18 17:19
 **/

@TypeDoc(
        name = "推荐渠道动态信息VO对象",
        description = "推荐渠道动态信息VO对象"
)
@Data
@ToString
@Builder
public class RecommendDynamicInfoVo {

    @FieldDoc(
            description = "商品名称"
    )
    private String spuName;

    @FieldDoc(
            description = "渠道id"
    )
    private Integer channelId;

    @FieldDoc(
            description = "渠道类目id"
    )
    private String channelCategoryId;

    @FieldDoc(
            description = "动态信息列表"
    )
    private List<ChannelDynamicInfoVO> channelDynamicInfoVOList;
}
