package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.regionselect.rsm;

import com.facebook.swift.codec.ThriftField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * Email <EMAIL>
 * Date 2022/12/7 12:08 下午
 * Description
 */
@Data
@TypeDoc(description = "仓源信息详情")
@ApiModel("仓源信息详情")
public class FdcRentalSourceVO {

    @ApiModelProperty("仓源id")
    @FieldDoc(description = "仓源id")
    private Long id;

    @ApiModelProperty("仓源关联的任务id")
    @FieldDoc(description = "仓源关联的任务id")
    private Long fdcAddressingTaskTaskId;

    @ApiModelProperty("仓源类别,0:竞店，1:新址")
    @FieldDoc(description = "仓源类别,0:竞店，1:新址")
    private Integer sourceType;

    @ApiModelProperty("仓源名称")
    @FieldDoc(description = "仓源名称")
    private String sourceName;

    @ApiModelProperty("仓源状态, 10:草稿，20:待复核，30:已复核，40:意向中，50: 待评审，60:评审中，70:评审通过待签约，1:已签约，0:已作废")
    @FieldDoc(description = "仓源状态, 10:草稿，20:待复核，30:已复核，40:意向中，50: 待评审，60:评审中，70:评审通过待签约，1:已签约，0:已作废")
    private Integer sourceStatus;

    @ApiModelProperty("仓源坐标，经纬度")
    @FieldDoc(description = "仓源坐标，经纬度")
    private String coordinate;

    @ApiModelProperty("仓源表单项")
    @FieldDoc(description = "仓源表单项")
    private Map<String, String> formItems;

    @ApiModelProperty("可执行的操作")
    @FieldDoc(description = "可执行的操作")
    private List<Integer> executableEventList =  new ArrayList<>();

    @ApiModelProperty("共识状态")
    @FieldDoc(description = "共识状态")
    private Integer consensusStatus;

    @ApiModelProperty("表单版本")
    @FieldDoc(description = "表单版本")
    private Long formVersion;
}