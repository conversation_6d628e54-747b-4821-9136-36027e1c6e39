package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.WarehousePickModuleVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "待领取查询返回"
)
@Data
@ApiModel("待领取查询返回")
public class WarehousePickWaitReceiveResponse {

    @FieldDoc(
            description = "是否还有数据", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否还有数据", required = true)
    private Boolean hasMore;

    @FieldDoc(
            description = "待领取数据列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "待领取数据列表", required = true)
    private List<WarehousePickModuleVO> dataList;

}
