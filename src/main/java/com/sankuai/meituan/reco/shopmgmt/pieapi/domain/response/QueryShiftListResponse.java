package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.ScheduleRuleVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.ScheduleShiftVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-10-19
 * @email <EMAIL>
 */
@TypeDoc(
        description = "新建/编辑班次请求"
)
@ApiModel("新建/编辑班次请求")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryShiftListResponse {

    @FieldDoc(
            description = "班次id列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "班次id列表")
    @NotNull
    private List<ScheduleShiftVO> shiftList;
}
