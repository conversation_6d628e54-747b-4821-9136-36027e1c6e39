package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.dianping.cat.Cat;
import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Lists;
import com.meituan.shangou.goodscenter.dto.DepotGoodsDetailDto;
import com.meituan.shangou.goodscenter.request.DepotGoodsBatchQueryRequest;
import com.meituan.shangou.goodscenter.response.DepotGoodsDetailListResponse;
import com.meituan.shangou.goodscenter.thrift.DepotGoodsThriftService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/12/14 10:46
 **/
@Rhino
@Slf4j
public class GoodsCenterWrapper {
    @Resource
    private DepotGoodsThriftService depotGoodsThriftService;

    private static final int BATCH_SIZE = 50;

    @Degrade(rhinoKey = "GoodsCenterWrapper.queryGoodsInfo", fallBackMethod = "queryGoodsInfoFallBack", timeoutInMilliseconds = 2000L)
    public List<DepotGoodsDetailDto> queryGoodsInfo(Long tenantId, Long offlineStoreId, List<String> goodsIdList) {
        if(CollectionUtils.isEmpty(goodsIdList)) {
            return Collections.emptyList();
        }

        DepotGoodsBatchQueryRequest request = new DepotGoodsBatchQueryRequest();
        request.setTenantId(tenantId);
        request.setDepotId(offlineStoreId);

        List<List<String>> goodsIdLists = Lists.partition(goodsIdList.stream().distinct().collect(Collectors.toList()), BATCH_SIZE);

        List<DepotGoodsDetailDto> depotGoodsDetailDtos = new ArrayList<>();
            for (List<String> goodsIds : goodsIdLists) {
                request.setGoodsIdList(goodsIds);
                log.info("start invoke goods center, request: {}", request);
                DepotGoodsDetailListResponse response = depotGoodsThriftService.batchQueryDepotGoodsListByGoodsId(request);
                log.info("end invoke goods center, response: {}", response);
                if (response == null || response.getCode() == null) {
                    throw new CommonRuntimeException("调用货品中心查询货品信息失败");
                }

                if (response.getCode() != ResultCode.SUCCESS.getCode()) {
                    throw new CommonRuntimeException("调用货品中心查询货品信息失败");
                }

                depotGoodsDetailDtos.addAll(response.getData());
            }

        return depotGoodsDetailDtos;
    }


    public List<DepotGoodsDetailDto> queryGoodsInfoFallBack(Long tenantId, Long offlineStoreId, List<String> goodsIdList) {
        log.warn("GoodsCenterWrapper.queryGoodsInfo 发生降级");
        return Collections.emptyList();
    }
}
