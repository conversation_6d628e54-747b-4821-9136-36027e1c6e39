package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SkuPackingSpecVO {

    @FieldDoc(
            description = "箱规单位"
    )
    @ApiModelProperty(value = "箱规单位")
    public String packingSpecUnit;

    @FieldDoc(
            description = "需求箱规单位与基本单位的转换系数"
    )
    @ApiModelProperty(value = "需求箱规单位与基本单位的转换系数")
    public String packingSpecRatio;
}
