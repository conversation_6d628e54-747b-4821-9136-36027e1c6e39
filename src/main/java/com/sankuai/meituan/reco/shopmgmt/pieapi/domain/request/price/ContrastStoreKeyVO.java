package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: wangyihao04
 * @Date: 2020-12-02 11:03
 * @Mail: <EMAIL>
 */
@ApiModel(
        "对比门店key"
)
@TypeDoc(
        description = "对比门店key"
)
@Data
public class ContrastStoreKeyVO {
    @FieldDoc(
            description = "门店类型"
    )
    @ApiModelProperty("门店类型")
    public Integer contrastStoreType;

    @FieldDoc(
            description = "门店ID"
    )
    @ApiModelProperty("门店ID")
    public Long contrastStoreId;
}
