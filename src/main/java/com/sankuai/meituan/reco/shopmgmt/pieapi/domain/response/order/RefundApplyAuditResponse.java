package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@TypeDoc(
        description = "订单退款审核操作响应"
)
@ApiModel("订单退款审核操作响应")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RefundApplyAuditResponse {
    @FieldDoc(
            description = "是否会创建销退单", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否会创建销退单", required = false)
    private boolean canCreateSaleReturnOrder;

    @FieldDoc(
            description = "销退单号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "销退单号", required = false)
    private String saleReturnOrderNo;
}
