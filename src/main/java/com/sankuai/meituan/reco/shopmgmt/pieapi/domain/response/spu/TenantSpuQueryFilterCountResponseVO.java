package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.TenantSpuFilterCountInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Title: TenantSpuQueryFilterCountResponseVO
 * @Description: 查询筛选项商品数量响应结果
 * @Author: wuyongjiang
 * @Date: 2022/9/1 15:03
 */
@TypeDoc(
        description = "查询筛选项商品数量响应结果"
)
@Data
@ApiModel("查询筛选项商品数量响应结果")
public class TenantSpuQueryFilterCountResponseVO {
    @FieldDoc(
            description = "无图商品数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "无图商品数量", required = true)
    private Long noPic;

    @FieldDoc(
            description = "美团不可售商品数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "美团不可售商品数量", required = true)
    private Long notAllowSale;

    @FieldDoc(
            description = "美团不可售商品数量(新逻辑)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "美团不可售商品数量(新逻辑)", required = true)
    private Long noSale;

    @FieldDoc(
        description = "美团无动态信息数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "美团无动态信息数量", required = true)
    private Long noDynamicInfo;

    @FieldDoc(
            description = "饿了么无动态信息数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "饿了么无动态信息数量", required = true)
    private Long noEleCategoryProperties;

    @FieldDoc(
            description = "美团审核中数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "美团审核中数量", required = false)
    public Long mtAuditingCount;

    @FieldDoc(
            description = "美团审核驳回数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "美团审核驳回数量", required = false)
    public Long mtAuditRejectCount;

    @FieldDoc(
            description = "美团平台停售数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "美团平台停售数量", required = false)
    public Long mtStopSellingCount;

    @FieldDoc(
            description = "美团平台下架数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "美团平台下架数量", required = false)
    public Long mtSoldOutCount;

    public static TenantSpuQueryFilterCountResponseVO convertResponse(TenantSpuFilterCountInfoDTO countInfoDTO) {
        TenantSpuQueryFilterCountResponseVO responseVO = new TenantSpuQueryFilterCountResponseVO();
        responseVO.setNoPic(countInfoDTO.getNoPicCount());
        responseVO.setNotAllowSale(countInfoDTO.getNotAllowSaleCount());
        responseVO.setNoSale(countInfoDTO.getNoSaleCount());
        responseVO.setNoDynamicInfo(countInfoDTO.getNoDynamicInfoCount());
        responseVO.setNoEleCategoryProperties(countInfoDTO.getNoEleCategoryPropertiesCount());
        responseVO.setMtAuditingCount(countInfoDTO.getMtAuditingCount());
        responseVO.setMtAuditRejectCount(countInfoDTO.getMtAuditRejectCount());
        responseVO.setMtStopSellingCount(countInfoDTO.getMtStopSellingCount());
        responseVO.setMtSoldOutCount(countInfoDTO.getMtSoldOutCount());
        return responseVO;
    }
}
