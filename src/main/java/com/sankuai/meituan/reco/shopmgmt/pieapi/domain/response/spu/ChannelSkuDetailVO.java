package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pricetrend.ChannelSkuPriceTrendVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MoneyUtils;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelSkuDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Title: ChannelSkuDetailVO
 * @Description: 渠道商品SKU详情信息
 * @Author: zhaolei12
 * @Date: 2020/5/16 5:59 下午
 */
@TypeDoc(
        description = "渠道商品SKU详情信息"
)
@Data
@ApiModel("渠道商品SKU详情信息")
public class ChannelSkuDetailVO {

    @FieldDoc(
            description = "SKU编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "SKU编码", required = true)
    @NotNull
    private String skuId;

    @FieldDoc(
            description = "线上售价 单位：元", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "线上售价", required = true)
    @NotNull
    private Long price;

    @FieldDoc(
            description = "线上库存", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "线上库存")
    private Integer stock;

    @FieldDoc(
            description = "价格来源(定价方式)，价格来源(定价方式), 0-全部 1-手动定价 2-按提价策略定价 3-等于提报价", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "价格来源(定价方式)，价格来源(定价方式), 0-全部 1-手动定价 2-按提价策略定价 3-等于提报价")
    private Integer priceSource;

    @FieldDoc(
            description = "市斤价, 单位:元", requiredness = Requiredness.NONE
    )
    @ApiModelProperty(name = "市斤价")
    private Double pricePer500g;

    @FieldDoc(
            description = "显示价格趋势图标 true-显示 false-不展示", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "显示价格趋势图标 true-显示 false-不展示", required = true)
    private Boolean showPriceTrendIcon;

    @FieldDoc(
            description = "价格趋势", requiredness = Requiredness.NONE
    )
    @ApiModelProperty(name = "价格趋势", required = true)
    private ChannelSkuPriceTrendVO priceTrend;

    public static List<ChannelSkuDetailVO> ofDTOList(List<ChannelSkuDTO> channelSkuDetailDTOList) {
        if (CollectionUtils.isEmpty(channelSkuDetailDTOList)) {
            return Lists.newArrayList();
        }

        return channelSkuDetailDTOList.stream().filter(Objects::nonNull).map(ChannelSkuDetailVO::ofDTO).collect(Collectors.toList())
                .stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static ChannelSkuDetailVO ofDTO(ChannelSkuDTO channelSkuDTO) {
        if (channelSkuDTO == null) {
            return null;
        }

        ChannelSkuDetailVO channelSkuDetailVO = new ChannelSkuDetailVO();
        channelSkuDetailVO.setSkuId(channelSkuDTO.getSkuId());
        if (channelSkuDTO.getPrice() != null) {
            channelSkuDetailVO.setPrice(MoneyUtils.yuanToCent(channelSkuDTO.getPrice()));
        }
        channelSkuDetailVO.setStock(channelSkuDTO.getStock());
        channelSkuDetailVO.setPriceSource(channelSkuDTO.getPriceSource());

        return channelSkuDetailVO;
    }

    public static List<ChannelSkuDTO> toDTOList(List<ChannelSkuDetailVO> channelSkuDetailVOList) {
        if (CollectionUtils.isEmpty(channelSkuDetailVOList)) {
            return Lists.newArrayList();
        }

        return channelSkuDetailVOList.stream().filter(Objects::nonNull).map(ChannelSkuDetailVO::toDTO).collect(Collectors.toList())
                .stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static ChannelSkuDTO toDTO(ChannelSkuDetailVO channelSkuDetailVO) {
        if (channelSkuDetailVO == null) {
            return null;
        }

        ChannelSkuDTO channelSkuDTO = new ChannelSkuDTO();
        channelSkuDTO.setSkuId(channelSkuDetailVO.getSkuId());
        if (channelSkuDetailVO.getPrice() != null) {
            channelSkuDTO.setPrice(MoneyUtils.centToYuan(channelSkuDetailVO.getPrice()));
        }
        channelSkuDTO.setStock(channelSkuDetailVO.getStock());
        channelSkuDTO.setPriceSource(channelSkuDetailVO.getPriceSource());

        return channelSkuDTO;
    }
}
