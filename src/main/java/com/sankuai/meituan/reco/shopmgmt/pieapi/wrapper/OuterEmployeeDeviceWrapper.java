package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.OuterEmployeeThriftService;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.ServiceType;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.TOuterEmployeeDeviceRequest;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.exception.EbaseLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.TenantEmployeeDto;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.store.saas.infrastructure.shield.common.monitor.CommonMonitorTransaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * Create by yujing10 on 2018/10/13.
 */
@Component
@Rhino
@Slf4j
public class OuterEmployeeDeviceWrapper {

    @Resource
    private OuterEmployeeThriftService.Iface outerEmployeeThriftService;

    @Degrade(rhinoKey = "OuterEmployeeDeviceWrapper.saveOrUpdateTenantEmployeeDevice",
            fallBackMethod = "saveOrUpdateTenantEmployeeDeviceFallback",
            timeoutInMilliseconds = 1000,
            ignoreExceptions = {CommonLogicException.class})
    @MethodLog(logRequest = true, logResponse = true)
    @CommonMonitorTransaction
    //绑定/更新租户门店员工设备
    public CommonResponse<Void> saveOrUpdateTenantEmployeeDevice(TenantEmployeeDto dto, String uuid, ServiceType serviceType) {
        try {
            com.sankuai.meituan.reco.pickselect.ebase.thrift.CommonResponse commonResponse
                    = outerEmployeeThriftService.saveOrUpdateOuterEmployeeDevice(generateTOuterEmployeeDeviceRequest(dto, uuid, serviceType));
            if (commonResponse.isResult()) {
                return CommonResponse.success(null);
            } else {
                return CommonResponse.fail(ResultCode.RETRY_INNER_FAIL);
            }
        } catch (EbaseLogicException e) {
            log.error("绑定/更新设备对应门店员工异常, error:{}", e.getMessage(), e);
            return CommonResponse.fail(e.getCode(), e.getMessage());
        } catch (TException e) {
            log.error("绑定/更新设备对应门店员工异常, error:{}", e.getMessage(), e);
            throw new CommonRuntimeException("绑定/更新设备对应门店员工异常", ResultCode.RETRY_INNER_FAIL);
        }
    }

    @SuppressWarnings("unused")
    private CommonResponse<Void> saveOrUpdateTenantEmployeeDeviceFallback(TenantEmployeeDto request, String uuid, ServiceType serviceType) {
        throw new CommonLogicException(ResultCode.RETRY_INNER_FAIL);
    }

    @Degrade(rhinoKey = "OuterEmployeeDeviceWrapper.batchSaveOrUpdateTenantEmployeeDevice",
            fallBackMethod = "batchSaveOrUpdateTenantEmployeeDeviceFallback",
            timeoutInMilliseconds = 1000,
            ignoreExceptions = {CommonLogicException.class})
    @MethodLog(logRequest = true, logResponse = true)
    @CommonMonitorTransaction
    //批量绑定/更新租户门店员工设备
    public CommonResponse<Void> batchSaveOrUpdateTenantEmployeeDevice(List<TenantEmployeeDto> dtoList, String uuid, ServiceType serviceType) {
        try {
            CommonResponse<Void> allResp = CommonResponse.success(null);
            for (TenantEmployeeDto dto : dtoList) {
                CommonResponse<Void> resp = saveOrUpdateTenantEmployeeDevice(dto, uuid, serviceType);
                if (allResp.getCode() != resp.getCode()) {
                    return resp;
                }
            }
            return allResp;
        } catch (Exception e) {
            log.error("绑定/更新设备对应门店员工异常, error:{}", e.getMessage(), e);
            throw new CommonRuntimeException("绑定/更新设备对应门店员工异常", ResultCode.RETRY_INNER_FAIL);
        }
    }

    @SuppressWarnings("unused")
    private CommonResponse<Void> batchSaveOrUpdateTenantEmployeeDeviceFallback(List<TenantEmployeeDto> request, String uuid, ServiceType serviceType) {
        throw new CommonLogicException(ResultCode.RETRY_INNER_FAIL);
    }

    @Degrade(rhinoKey = "OuterEmployeeDeviceWrapper.unbindDevice",
            fallBackMethod = "unbindDeviceFallback",
            timeoutInMilliseconds = 1000,
            ignoreExceptions = {CommonLogicException.class})
    @MethodLog(logRequest = true, logResponse = true)
    @CommonMonitorTransaction
    public CommonResponse<Void> unbindDevice(String uuid, ServiceType serviceType) {
        try {
             com.sankuai.meituan.reco.pickselect.ebase.thrift.CommonResponse commonResponse
                     = outerEmployeeThriftService.unbindUserDevice(new TOuterEmployeeDeviceRequest().setUuid(uuid).setServiceType(serviceType.getValue()));
            if (commonResponse.isResult()) {
                return CommonResponse.success(null);
            } else {
                return CommonResponse.fail(ResultCode.RETRY_INNER_FAIL);
            }
        } catch (EbaseLogicException e) {
            log.error("解绑设备异常, error:{}", e.getMessage(), e);
            return CommonResponse.fail(e.getCode(), e.getMessage());
        } catch (TException e) {
            log.error("解绑设备异常, error:{}", e.getMessage(), e);
            throw new CommonRuntimeException("解绑设备异常", ResultCode.RETRY_INNER_FAIL);
        }
    }

    @SuppressWarnings("unused")
    private CommonResponse<Void> unbindDeviceFallback(String uuid, ServiceType serviceType) {
        throw new CommonRuntimeException("解绑门店员工设备接口异常");
    }

    private TOuterEmployeeDeviceRequest generateTOuterEmployeeDeviceRequest(TenantEmployeeDto request, String uuid, ServiceType serviceType) {
        TOuterEmployeeDeviceRequest deviceRequest = new TOuterEmployeeDeviceRequest();
        deviceRequest.setUserCode(request.getTenantId() + "_" + request.getStoreId() + "_" + request.getStaffId());
        deviceRequest.setUuid(uuid);
        deviceRequest.setServiceType(serviceType.getValue());
        return deviceRequest;
    }

}
