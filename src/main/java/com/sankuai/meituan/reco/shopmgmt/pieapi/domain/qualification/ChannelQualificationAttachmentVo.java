package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.qualification;


import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelqualification.dto.QualificationAttachmentDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/7/1 17:54
 **/
@TypeDoc(
        name = "资质vo对象"
)
@Data
@ToString
@EqualsAndHashCode
public class ChannelQualificationAttachmentVo {
    private int mediaType;
    private String url;
    public static ChannelQualificationAttachmentVo fromDto(QualificationAttachmentDTO dto){
        if(dto == null){
            return null;
        }
        ChannelQualificationAttachmentVo vo = new ChannelQualificationAttachmentVo();
        vo.setMediaType(dto.getMediaType());
        vo.setUrl(dto.getUrl());
        return vo;

    }
}
