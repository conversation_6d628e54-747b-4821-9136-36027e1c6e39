package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.facebook.swift.codec.ThriftField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.QualificationDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 抖音资质VO对象
 *
 * <AUTHOR>
 * @since 2024/1/8
 */
@TypeDoc(
        name = "抖音资质VO对象",
        description = "抖音资质VO对象"
)
@Data
@ToString
@EqualsAndHashCode
public class QualificationVO {

    @FieldDoc(
            description = "资质key"
    )
    private String key;

    @FieldDoc(
            description = "资质名称"
    )
    private String name;

    @FieldDoc(
            description = "资质地址"
    )
    private List<String> urls;

    @FieldDoc(
            description = "是否必填"
    )
    private Boolean isRequired;

    @FieldDoc(
            description = "描述"
    )
    private List<String> text;

    public static List<QualificationVO> ofOcmsDTOList(List<QualificationDTO> douyinQualificationDTOList){
        if(CollectionUtils.isEmpty(douyinQualificationDTOList)){
            return Lists.newArrayList();
        }
        List<QualificationVO> qualificationVOList = new ArrayList<>();
        for(QualificationDTO douyinQualificationDTO : douyinQualificationDTOList){
            QualificationVO qualificationVO = new QualificationVO();
            qualificationVO.setKey(douyinQualificationDTO.getKey());
            Optional.ofNullable(douyinQualificationDTO.getUrl()).ifPresent(url -> {
                qualificationVO.setUrls(douyinQualificationDTO.getUrl());
            });
            qualificationVO.setIsRequired(douyinQualificationDTO.getIsRequired());
            qualificationVO.setName(douyinQualificationDTO.getName());
            qualificationVO.setText(douyinQualificationDTO.getText());

            qualificationVOList.add(qualificationVO);
        }
        return qualificationVOList;
    }

    public static List<QualificationVO> ofBizDTOList(List<com.sankuai.meituan.shangou.empower.productbiz.client.dto.QualificationDTO> douyinQualificationDTOList){
        if(CollectionUtils.isEmpty(douyinQualificationDTOList)){
            return Lists.newArrayList();
        }
        List<QualificationVO> qualificationVOList = new ArrayList<>();
        for(com.sankuai.meituan.shangou.empower.productbiz.client.dto.QualificationDTO douyinQualificationDTO : douyinQualificationDTOList){
            QualificationVO qualificationVO = new QualificationVO();
            qualificationVO.setKey(douyinQualificationDTO.getKey());
            Optional.ofNullable(douyinQualificationDTO.getUrls()).ifPresent(url -> {
                qualificationVO.setUrls(douyinQualificationDTO.getUrls());
            });
            qualificationVO.setIsRequired(douyinQualificationDTO.getIsRequired());
            qualificationVO.setName(douyinQualificationDTO.getName());
            qualificationVO.setText(douyinQualificationDTO.getText());

            qualificationVOList.add(qualificationVO);
        }
        return qualificationVOList;
    }

    public static QualificationVO ofDTO(QualificationDTO qualificationDTO) {
        QualificationVO qualificationVO = new QualificationVO();
        qualificationVO.setKey(qualificationDTO.getKey());
        qualificationVO.setUrls(qualificationDTO.getUrl());
        qualificationVO.setIsRequired(qualificationDTO.getIsRequired());
        return qualificationVO;
    }

}
