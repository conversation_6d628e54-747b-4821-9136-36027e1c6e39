package com.sankuai.meituan.reco.shopmgmt.pieapi.service.salereturn;

import com.meituan.reco.pickselect.common.cat.CatTransaction;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.converters.channelorder.ChannelOrderConverter;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.CloseSaleReturnOrderRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order.RefundApplyAuditRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.PermissionCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.AuthThriftWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.SaleReturnOrderWrapper;
import com.sankuai.meituan.reco.store.management.enums.ResultCodeEnum;
import com.sankuai.meituan.reco.store.management.thrift.common.BaseResult;
import com.sankuai.meituan.reco.store.management.thrift.scmdelivery.dto.TSaleReturnOrderInfo;
import com.sankuai.meituan.reco.store.management.thrift.scmdelivery.resp.SaleReturnOrderQueryBySaleOrderResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/12/14
 */
@Slf4j
@Service
public class SaleReturnService {

	@Resource
	private SaleReturnOrderWrapper saleReturnOrderWrapper;
	@Resource
	private AuthThriftWrapper authThriftWrapper;

	@CatTransaction
	@MethodLog(logRequest = true, logResponse = true)
	public boolean checkCanCreateSaleReturnOrder(RefundApplyAuditRequest request) {
		try {
			//判断当前用户是否有销退模块权限
			if(!authThriftWrapper.isCodeHasAuth(PermissionCodeEnum.SALE_RETURN_TASK.getCode())){
				return false;
			}

			//判断是否会创建销退单
			return saleReturnOrderWrapper.canCreateSaleReturnOrder(
					ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId(),
					ApiMethodParamThreadLocal.getIdentityInfo().getStoreId(),
					ChannelOrderConverter.convertChannelId2OrderBizType(request.getChannelId()),
					request.getChannelOrderId()
			);
		} catch (Exception e) {
			log.error("判断订单退款后是否会触发销退流程异常，将默认降级至不会", e);
			return false;
		}
	}

	@CatTransaction
	@MethodLog(logRequest = true, logResponse = true)
	public String getSaleReturnOrderNo(RefundApplyAuditRequest request) {
		try {
			//判断当前用户是否有销退模块权限
			if(!authThriftWrapper.isCodeHasAuth(PermissionCodeEnum.SALE_RETURN_TASK.getCode())){
				return null;
			}

			//获取销退单号
			SaleReturnOrderQueryBySaleOrderResp response = saleReturnOrderWrapper.getSaleReturnOrderNo(
					ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId(),
					ApiMethodParamThreadLocal.getIdentityInfo().getStoreId(),
					ChannelOrderConverter.convertChannelId2OrderBizType(request.getChannelId()),
					request.getChannelOrderId(),
					String.valueOf(request.getAfterSaleId())
			);
			checkResponse(response.getStatus());
			return response.getSaleReturnOrders() != null ? response.getSaleReturnOrders().get(0).getSaleReturnOrderNo() : null;

		} catch (Exception e) {
			log.error("获取销退单号异常", e);
			return null;
		}
	}

	@CatTransaction
	@MethodLog(logRequest = true, logResponse = true)
	public void closeSaleReturnOrder(RefundApplyAuditRequest request, String saleReturnOrderNo) {
		CloseSaleReturnOrderRequest closeSaleReturnOReq = new CloseSaleReturnOrderRequest();
		closeSaleReturnOReq.setSaleReturnOrderNo(saleReturnOrderNo);
		closeSaleReturnOReq.setEntityId(request.getEntityId());
		closeSaleReturnOReq.setEntityType(request.getEntityType());
		closeSaleReturnOReq.setStoreId(ApiMethodParamThreadLocal.getIdentityInfo().getStoreId());
		closeSaleReturnOReq.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
		try {
			//关闭建销退单
			saleReturnOrderWrapper.closeSaleReturnOrder(closeSaleReturnOReq);
		} catch (Exception e) {
			log.error("关闭销退单异常", e);
		}
	}

	private void checkResponse(BaseResult result) {
		if (result.getCode() != ResultCodeEnum.SUCCESS.getCode()) {
			throw new CommonRuntimeException(result.getMsg());
		}
	}
}
