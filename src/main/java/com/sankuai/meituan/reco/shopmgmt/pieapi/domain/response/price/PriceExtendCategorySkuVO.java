package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * @Author: wangyihao04
 * @Date: 2020-06-11 17:35
 * @Mail: <EMAIL>
 */
@TypeDoc(
        description = "品类商品价格扩展值对象结果"
)
@ApiModel("品类商品价格扩展值结果")
@Data
@AllArgsConstructor
public class PriceExtendCategorySkuVO {

    @FieldDoc(
            description = "品类价格扩展值信息"
    )
    @ApiModelProperty("品类价格扩展值信息")
    private PriceExtendVO categoryPriceExtend;


    @FieldDoc(
            description = "商品价格扩展值信息"
    )
    @ApiModelProperty("商品价格扩展值信息")
    private List<SkuPriceExtendVO> categoryPriceExtendList;


    @FieldDoc(
            description = "分页信息"
    )
    @ApiModelProperty("分页信息")
    private PageInfoVO pageInfo;

}
