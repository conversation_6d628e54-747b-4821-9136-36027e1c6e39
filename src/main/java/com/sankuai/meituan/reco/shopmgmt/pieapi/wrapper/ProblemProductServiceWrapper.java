package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.linz.boot.util.Fun;
import com.meituan.mtrace.thread.pool.ExecutorServiceTraceWrapper;
import com.meituan.shangou.saas.tenant.thrift.common.enums.ChainRelationEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ProjectConstants;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.ChannelSpuKeyVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.CompareSpuTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.ProblemSpuOperateTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.facade.EmpowerSpuFacade;
import com.sankuai.meituan.reco.shopmgmt.pieapi.facade.item.AbnormalProductClient;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.SpringAppContext;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.handler.AbstractCountStoreSpuHandler;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.PermissionGroupTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.client.product.service.StoreSpuThriftService;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.*;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.diffcompare.QueryDiffCompareSpuRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.*;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.diffcompare.DiffCompareSpuRepairOptResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.diffcompare.QueryDiffCompareSpuResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.service.ProblemSpuThriftService;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.command.PageQueryPoiSpuCommand;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.result.PageQueryPoiSpuResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Title: ProblemProductServiceWrapper
 * @Description:
 * @Author: zhouyan32
 * @Date: 2022/2/22 11:51 上午
 */
@Service
@Slf4j
public class ProblemProductServiceWrapper {

    private static ExecutorService executorService;

    private static ExecutorService cntStoreSpuExecutorService;

    private static Map<String, AbstractCountStoreSpuHandler> allCountStoreSpuHandlerMap;

    @Resource
    private ProblemSpuThriftService problemSpuThriftService;

    @Resource
    private StoreSpuThriftService storeSpuThriftService;

    @Resource
    private AuthThriftWrapper authThriftWrapper;

    @Resource
    private OCMSProductServiceWrapper ocmsProductServiceWrapper;

    @Resource
    private EmpowerSpuFacade empowerSpuFacade;

    @Resource
    private AbnormalProductClient abnormalProductClient;

    @Resource
    private TenantWrapper tenantWrapper;

    @Resource
    private OCMSServiceWrapper ocmsServiceWrapper;

    static {
        executorService = new ExecutorServiceTraceWrapper(
                new ThreadPoolExecutor(10, 20, 5,
                        TimeUnit.MINUTES, new ArrayBlockingQueue<>(200),
                        new ThreadFactoryBuilder().setNameFormat("store-spu-problem-manager-thread-pool-%d").build(),
                        new ThreadPoolExecutor.CallerRunsPolicy()));
        cntStoreSpuExecutorService = new ExecutorServiceTraceWrapper(
                new ThreadPoolExecutor(10, 20, 5,
                        TimeUnit.MINUTES, new ArrayBlockingQueue<>(200),
                        new ThreadFactoryBuilder().setNameFormat("store-spu-problem-count-thread-pool-%d").build(),
                        new ThreadPoolExecutor.CallerRunsPolicy()));
    }
    public static Map<String, AbstractCountStoreSpuHandler> getCountStoreSpuHandlerMap() {
        synchronized (ProblemProductServiceWrapper.class){
            if (allCountStoreSpuHandlerMap != null) {
                return allCountStoreSpuHandlerMap;
            }
            allCountStoreSpuHandlerMap = new HashMap<>();
            SpringAppContext.AppContext.getBeanOfType(AbstractCountStoreSpuHandler.class)
                    .forEach((name, bean) -> {
                        allCountStoreSpuHandlerMap.put(bean.getCountCode(), bean);
                    });
            return allCountStoreSpuHandlerMap;
        }
    }


    /**
     * 统计店铺异常商品，不可售商品，平台停售商品数量。
     * @param user
     * @param request
     */
    public CommonResponse<ProblemSpuCountResponseVO> queryProblemSpuCount(User user, ProblemSpuCountQueryRequest request) throws TException, InterruptedException {
        // 获取当前用户有权限的门店列表
        List<Long> authStoreIds = authThriftWrapper.queryPermissionGroupId(user, PermissionGroupTypeEnum.POI,
                Long::valueOf);
        if (!authStoreIds.contains(request.getStoreId())) {
            log.warn("当前用户无门店权限，无请求数据");
            return CommonResponse.fail2(ResultCode.AUTHORIZE_ERROR);
        }

        boolean newQueryAbnormalGray = getIsNewQueryAbnormalGray(user);

        HashMap<String,Integer> problemValueMap = Maps.newHashMap();
        List<ProblemSpuCountDetailVO>  problemSpuCountDetailList = newQueryAbnormalGray
                ? MccConfigUtil.getProblemSpuCountNewDetailList() : MccConfigUtil.getProblemSpuCountDetailList();
        if (CollectionUtils.isEmpty(problemSpuCountDetailList)) {
            log.error("查询问题商品统计，无展示模板");
            return CommonResponse.fail2(ResultCode.INTERNAL_SERVER_ERROR);
        }

        if (newQueryAbnormalGray) {
            Set<Integer> poiChannels = tenantWrapper.getPoiChannels(user.getTenantId(), request.getStoreId());

            // 过滤出需要展示的问题项
            problemSpuCountDetailList = filterSpuCountDetailListByPoiChannels(problemSpuCountDetailList, poiChannels);

            if (CollectionUtils.isEmpty(problemSpuCountDetailList)){
                return CommonResponse.success(new ProblemSpuCountResponseVO(Collections.emptyList()));
            }

            // 并发查询问题项
            Map<String, AbnormalRuleNodeVO> abnormalRuleMap = abnormalProductClient.getAbnormalRuleMap(null, null);
            CountDownLatch latch = new CountDownLatch(problemSpuCountDetailList.size());
            for (ProblemSpuCountDetailVO spuCountDetailVO : problemSpuCountDetailList) {
                cntStoreSpuExecutorService.execute(()->{
                    try {
                        AbstractCountStoreSpuHandler handler = getCountStoreSpuHandlerMap().get(spuCountDetailVO.getCode());
                        Integer count = handler.process(user.getTenantId(), request, abnormalRuleMap);
                        spuCountDetailVO.setValue(count);
                    }catch (Exception e){
                        log.error("单个异常类型门店商品数统计失败", e);
                    }finally {
                        latch.countDown();
                    }
                });
            }
            latch.await();
        }else {
            problemValueMap.put(ProjectConstants.INCONSISTENT_SPU_COUNT, countSpuCompareRecord(user.getTenantId(), request));
            problemValueMap.put(ProjectConstants.UN_SALE_COUNT, countStoreSpuCount(request.toRequest(user.getTenantId())));
            problemValueMap.put(ProjectConstants.STOP_SALE_COUNT, countStoreSpuCount(request.toStopSellRequest(user.getTenantId())));
            problemValueMap.put(ProjectConstants.AUDITING_COUNT, countStoreSpuCount(request.toAuditingRequest(user.getTenantId())));
            problemValueMap.put(ProjectConstants.PLATFORM_SOLD_OUT_COUNT, countStoreSpuCount(request.toPlatformSoldOutRequest(user.getTenantId())));
            problemSpuCountDetailList.forEach(problemSpuCountDetailVO ->
                    problemSpuCountDetailVO.setValue(problemValueMap.get(problemSpuCountDetailVO.getCode())));
        }

        return CommonResponse.success(new ProblemSpuCountResponseVO(problemSpuCountDetailList));
    }

    private boolean getIsNewQueryAbnormalGray(User user) {
        try {
            return ocmsServiceWrapper.isErpTenantWithCache(user.getTenantId()) ? MccConfigUtil.isErpNewQueryAbnormalGray(user.getTenantId()) : MccConfigUtil.isNoErpNewQueryAbnormalGray(user.getTenantId());
        }catch (Exception e){
            log.error("isNewQueryAbnormalGray 异常, user: {}", user, e);
            return false;
        }
    }

    private static List<ProblemSpuCountDetailVO> filterSpuCountDetailListByPoiChannels(List<ProblemSpuCountDetailVO> problemSpuCountDetailList, Set<Integer> poiChannels) {
        return Fun.filter(problemSpuCountDetailList, spuCountDetailVO -> {
            AbstractCountStoreSpuHandler cntStoreSpuHandler = getCountStoreSpuHandlerMap().get(spuCountDetailVO.getCode());
            if (cntStoreSpuHandler == null) {
                log.error("匹配异常统计执行器失败 spuCountDetailVO: {}", spuCountDetailVO);
                return false;
            }
            // 门店未开通的渠道，则该渠道异常统计不展示
            return cntStoreSpuHandler.isMatch(poiChannels);
        });
    }

    private Integer countSpuCompareRecord(Long tenantId, ProblemSpuCountQueryRequest queryRequest){
        //计算商品异常信息统计
        QueryDiffCompareSpuRequest request = queryRequest.toDiffCompareSpuRequest(tenantId);
        QueryCompareTypeCountResponse compareTypeCountResponse = problemSpuThriftService.queryDiffCompareTypeCount(request);

        if (Objects.isNull(compareTypeCountResponse)
                || !compareTypeCountResponse.getStatus().getCode().equals(ResultCode.SUCCESS.getCode())) {
            log.error("查询店铺问题商品类型统计异常， request [{}], compareTypeCountResponse [{}].",
                    queryRequest, compareTypeCountResponse);
            throw new CommonRuntimeException("查询店铺问题商品类型统计异常");
        }
        return compareTypeCountResponse.getCompareTypeCount().values()
                .stream()
                .reduce(0, Integer::sum);
    }

    private int countStoreSpuCount(PageQueryPoiSpuCommand command) {
        Integer spuCount = 0;
        try {
            PageQueryPoiSpuResult result = empowerSpuFacade.pageQueryStoreSpu(command);

            if(Objects.nonNull(result) && Objects.nonNull(result.getStatus())
                    && result.getStatus().getCode() == ResultCode.SUCCESS.getCode()){
                spuCount = result.getPage().getTotalCount();
            }
        }catch (Exception e){
            log.error("商品统计失败 request:{}", command, e);
            throw new CommonRuntimeException(e);
        }
        return spuCount;
    }


    /**
     * 分类统计店铺问题商品数量
     * @param user
     * @param request
     */
    public CommonResponse<ProblemSpuCountListResponseVO> queryProblemSpuTypeCount(User user, ProblemSpuCountQueryRequest request) {
        // 获取当前用户有权限的门店列表
        List<Long> authStoreIds = authThriftWrapper.queryPermissionGroupId(user, PermissionGroupTypeEnum.POI,
                Long::valueOf);
        if (!authStoreIds.contains(request.getStoreId())) {
            log.warn("当前用户无门店权限，无请求数据");
            return CommonResponse.fail2(ResultCode.AUTHORIZE_ERROR);
        }
        QueryCompareTypeCountResponse response = problemSpuThriftService.queryCompareTypeCount(request.to(user.getTenantId()));
        if (!response.getStatus().getCode().equals(ResultCode.SUCCESS.getCode())) {
            log.error("查询店铺问题商品类型统计异常， request [{}], response [{}].", request, response);
            return CommonResponse.fail(response.getStatus().getCode(), response.getStatus().getMsg());
        }
        return CommonResponse.success(ProblemSpuCountListResponseVO.of(response));
    }

    public CommonResponse<ProblemListInfoResponseVO> queryProblemSpuListByType(User user, ProblemSpuTypeQueryRequest request) {
        // 获取当前用户有权限的门店列表
        List<Long> authStoreIds = authThriftWrapper.queryPermissionGroupId(user, PermissionGroupTypeEnum.POI,
                Long::valueOf);
        if (!authStoreIds.contains(request.getStoreId())) {
            log.warn("当前用户无门店权限，无请求数据");
            return CommonResponse.fail2(ResultCode.AUTHORIZE_ERROR);
        }
        QueryCompareRecordByTypeResponse response = problemSpuThriftService.queryCompareRecordListByType(request.to(user.getTenantId()));
        if (!response.getStatus().getCode().equals(ResultCode.SUCCESS.getCode())) {
            log.error("查询用户问题商品列表异常， request [{}], response [{}].", request, response);
            return CommonResponse.fail(response.getStatus().getCode(), response.getStatus().getMsg());
        }
        ProblemListInfoResponseVO problemListInfoResponseVO = new ProblemListInfoResponseVO();
        problemListInfoResponseVO.setPageInfo(new PageInfoVO(response.getPageInfoDTO()));

        List<ProblemListInfoVO> problemListInfoVOS = Optional.ofNullable(response.getSpuCompareRecordDetailInfoDTOS())
                .map(List::stream).orElse(Stream.empty()).map(ProblemListInfoVO::of).collect(Collectors.toList());
        problemListInfoResponseVO.setProblemSpuInfoList(problemListInfoVOS);
        return CommonResponse.success(problemListInfoResponseVO);
    }

    public CommonResponse<CompareRecordDetailResponseVO> queryStoreSpuProblemBySpuId(User user,StoreSpuProblemInfoRequest request) {
        // 获取当前用户有权限的门店列表
        List<Long> authStoreIds = authThriftWrapper.queryPermissionGroupId(user, PermissionGroupTypeEnum.POI,
                Long::valueOf);
        if (!authStoreIds.contains(request.getStoreId())) {
            log.warn("当前用户无门店权限，无请求数据");
            return CommonResponse.fail2(ResultCode.AUTHORIZE_ERROR);
        }
        QueryCompareRecordDetailResponse response = problemSpuThriftService.queryStoreSpuCompareDetail(request.to(user));
        if (!response.getStatus().getCode().equals(ResultCode.SUCCESS.getCode())) {
            log.error("查询用户问题商品列表异常， request [{}], response [{}].", request, response);
            return CommonResponse.fail(response.getStatus().getCode(), response.getStatus().getMsg());
        }
        return CommonResponse.success(CompareRecordDetailResponseVO.of(response.getCompareSpuDetailDTO()));
    }

    private List<SpuRepairResultVO> syncOperate(ProblemSpuOperateTypeEnum operateTypeEnum,
                                                List<Callable<List<SpuRepairResultVO>>> callableList) {

        if (CollectionUtils.isEmpty(callableList)) {
            return Collections.emptyList();
        }

        // 若callableList数量为1, 则同步执行
        if (callableList.size() == 1) {
            try {
                List<SpuRepairResultVO> resultVOList = callableList.get(0).call();
                return CollectionUtils.isNotEmpty(resultVOList) ? resultVOList : Collections.emptyList();
            } catch (Exception e) {
                log.error("一致性问题修复, 同步执行操作 : {}", operateTypeEnum.getDesc(), e);
                throw new CommonRuntimeException(ResultCode.INTERNAL_SERVER_ERROR);
            }
        }

        // 若callableList数量大于1, 则并发执行
        List<SpuRepairResultVO> operateResultList = Lists.newArrayList();
        List<Future<List<SpuRepairResultVO>>> futureList = Lists.newArrayList();
        callableList.forEach(callable -> {
            Future<List<SpuRepairResultVO>> future = executorService.submit(callable);
            futureList.add(future);
        });
        try {
            for (Future<List<SpuRepairResultVO>> future : futureList) {
                List<SpuRepairResultVO> resultVOList = future.get();
                if (CollectionUtils.isNotEmpty(resultVOList)) {
                    operateResultList.addAll(resultVOList);
                }
            }
        } catch (Exception e) {
            log.error("一致性问题修复, 并发执行操作 : {}", operateTypeEnum.getDesc(), e);
            throw new CommonRuntimeException(ResultCode.INTERNAL_SERVER_ERROR);
        }
        return operateResultList;

    }

    private List<Callable<List<SpuRepairResultVO>>> calculateOperateCallableList(
            Long tenantId, Long storeId, List<ChannelSpuKeyVO> channelStoreCustomSpuKeyList, ProblemSpuOperateTypeEnum operateTypeEnum,
            Long operatorId, String operatorName, CompareSpuTypeEnum compareSpuTypeEnum){

        if (CollectionUtils.isEmpty(channelStoreCustomSpuKeyList)) {
            return Collections.emptyList();
        }
        // 按渠道分组
        Map<Integer, List<String>> storeChannelCustomSpuIdsMap = channelStoreCustomSpuKeyList.stream()
                .collect(Collectors.groupingBy(ChannelSpuKeyVO::getChannelId,
                                Collectors.mapping(ChannelSpuKeyVO::getCustomSpuId, Collectors.toList())));

        // 根据修复类型按照门店维度构建修复操作任务
        List<Callable<List<SpuRepairResultVO>>> operateCallableList = Lists.newArrayList();
        storeChannelCustomSpuIdsMap.forEach((channelId, customSpuIds) -> {
            Callable<List<SpuRepairResultVO>> operateCallable;
            switch (operateTypeEnum) {
                case DELETE_FROM_WAIMAI:
                    operateCallable = () -> deleteFromWaimai(tenantId, storeId, channelId,
                            customSpuIds, operatorId, operatorName);
                    break;
                case DELETE_FROM_SGP:
                    operateCallable = () -> deleteFromSgp(tenantId, storeId, channelId,
                            customSpuIds, operatorId, operatorName);
                    break;
                case CREATE_IN_WAIMAI:
                    operateCallable = () -> pushSgpToWaimai(tenantId, storeId, channelId,
                            customSpuIds, operatorId, operatorName);
                    break;
                case CREATE_IN_SGP:
                    operateCallable = () -> createSgpFromWaimai(tenantId, storeId, channelId,
                            customSpuIds, operatorId, operatorName);
                    break;
                case REPAIR_BASE_INFO_BY_WAIMAI:
                    operateCallable = () -> repairSpuBaseInfoByWaimai(tenantId, storeId, channelId,
                            customSpuIds, operatorId, operatorName, compareSpuTypeEnum);
                    break;
                case REPAIR_BASE_INFO_BY_SGP:
                    operateCallable = () -> repairSpuBaseInfoBySgp(tenantId, storeId, channelId,
                            customSpuIds, operatorId, operatorName, compareSpuTypeEnum);
                    break;
                case REPAIR_DETAIL_PRICE_BY_WAIMAI:
                    operateCallable = () -> repairSpuRetailPriceByWaimai(tenantId, storeId, channelId,
                            customSpuIds, operatorId, operatorName);
                    break;
                case REPAIR_DETAIL_PRICE_BY_SGP:
                    operateCallable = () -> repairOnlinePriceBySgp(tenantId, storeId, channelId,
                            customSpuIds, operatorId, operatorName);
                    break;
                default:
                    throw new CommonRuntimeException("不支持该操作");
            }
            operateCallableList.add(operateCallable);
        });
        return operateCallableList;
    }

    public CommonResponse<ProblemSpuOptResponseVO> syncOperate(ProblemSpuOptRequest request) {

        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        // 获取当前用户有权限的门店列表
        List<Long> authStoreIds = authThriftWrapper.queryPermissionGroupId(user, PermissionGroupTypeEnum.POI,
                Long::valueOf);
        if (!authStoreIds.contains(request.getStoreId())) {
            log.warn("当前用户无门店权限，无请求数据");
            return CommonResponse.fail2(ResultCode.AUTHORIZE_ERROR);
        }
        Integer optType = request.getOptType();
        List<ChannelSpuKeyVO> channelStoreCustomSpuKeyList = request.getCustomSpuKeys();

        // 不一致类型 目前基础信息和销售信息场景下必须传递该参数, 其余场景默认为null
        CompareSpuTypeEnum compareSpuTypeEnum = null;
        if (Objects.nonNull(request.getCompareType())) {
            compareSpuTypeEnum = CompareSpuTypeEnum.findByCode(request.getCompareType());
            Preconditions.checkNotNull(compareSpuTypeEnum, "app端不一致类型 " + request.getCompareType() + " 无法转换为后端不一致类型");
        }

        // 1从商家端删除;2从蔬果派删除;3向商家端新增;4向蔬果派新增;6信息与商家端一致;7信息与蔬果派一致;9价格与商家端一致;10价格与蔬果派一致
        ProblemSpuOperateTypeEnum operateTypeEnum = ProblemSpuOperateTypeEnum.findByCode(optType);
        // 构建修复操作任务列表
        List<Callable<List<SpuRepairResultVO>>> operateCallableList = calculateOperateCallableList(
                user.getTenantId(), request.getStoreId(), channelStoreCustomSpuKeyList, operateTypeEnum,
                user.getAccountId(), user.getAccountName(), compareSpuTypeEnum);

        // 执行修复操作（任务数量=1同步执行, 任务数量大于1并发执行）
        List<SpuRepairResultVO> resultVOList = syncOperate(operateTypeEnum, operateCallableList);

        // 执行修复操作失败的商品列表
        List<SpuRepairResultVO> failureList = resultVOList.stream()
                .filter(resultVO -> resultVO.getCode() != null && resultVO.getCode() != 0)
                .collect(Collectors.toList());

        return CommonResponse.success(new ProblemSpuOptResponseVO(failureList));
    }


    /**
     * 删除商家端商品
     * @param tenantId
     * @param storeId
     * @param channelId
     * @param customSpuIds
     * @param operatorId
     * @param operatorName
     * @return
     */
    private List<SpuRepairResultVO> deleteFromWaimai(
            Long tenantId, Long storeId, Integer channelId, List<String> customSpuIds,
            Long operatorId, String operatorName) {

        SpuRepairDeleteFromWaimaiRequest request = new SpuRepairDeleteFromWaimaiRequest();
        request.setTenantId(tenantId);
        request.setStoreId(storeId);
        request.setChannelId(channelId);
        request.setCustomSpuIds(customSpuIds);
        request.setOperatorId(operatorId);
        request.setOperatorName(operatorName);

        try {
            log.info("ProblemSpuThriftService deleteFromWaimai, request:{}", request);
            SpuRepairDeleteFromWaimaiResponse response = problemSpuThriftService.deleteFromWaimai(request);
            log.info("ProblemSpuThriftService deleteFromWaimai, response:{}", response);

            if (response == null || response.getStatus() == null) {
                return SpuRepairResultVO.of(channelId, customSpuIds, ResultCode.FAIL.getCode(),
                        "删除商家端商品错误, 未返回结果");
            }

            if (response.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
                return SpuRepairResultVO.of(channelId, customSpuIds, ResultCode.FAIL.getCode(),
                        response.getStatus().getMsg());
            }

            return SpuRepairResultVO.of(response.getResultDTOList());
        } catch (Exception e) {
            return SpuRepairResultVO.of(channelId, customSpuIds, ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

    /**
     * 删除牵牛花商品
     * @param tenantId
     * @param storeId
     * @param channelId
     * @param customSpuIds
     * @param operatorId
     * @param operatorName
     * @return
     */
    private List<SpuRepairResultVO> deleteFromSgp(
            Long tenantId, Long storeId, Integer channelId, List<String> customSpuIds,
            Long operatorId, String operatorName) {

        SpuRepairDeleteFromSgpRequest request = new SpuRepairDeleteFromSgpRequest();
        request.setTenantId(tenantId);
        request.setStoreId(storeId);
        request.setChannelId(channelId);
        request.setCustomSpuIds(customSpuIds);
        request.setOperatorId(operatorId);
        request.setOperatorName(operatorName);

        try {
            log.info("ProblemSpuThriftService deleteFromSgp, request:{}", request);
            SpuRepairDeleteFromSgpResponse response = problemSpuThriftService.deleteFromSgp(request);
            log.info("ProblemSpuThriftService deleteFromSgp, response:{}", response);

            if (response == null || response.getStatus() == null) {
                return SpuRepairResultVO.of(channelId, customSpuIds, ResultCode.FAIL.getCode(),
                        "删除牵牛花商品错误, 未返回结果");
            }

            if (response.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
                return SpuRepairResultVO.of(channelId, customSpuIds, ResultCode.FAIL.getCode(),
                        response.getStatus().getMsg());
            }

            return SpuRepairResultVO.of(response.getResultDTOList());
        } catch (Exception e) {
            return SpuRepairResultVO.of(channelId, customSpuIds, ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

    /**
     * 推送牵牛花商品到商家端
     * @param tenantId
     * @param storeId
     * @param channelId
     * @param customSpuIds
     * @param operatorId
     * @param operatorName
     * @return
     */
    private List<SpuRepairResultVO> pushSgpToWaimai(
            Long tenantId, Long storeId, Integer channelId, List<String> customSpuIds,
            Long operatorId, String operatorName) {

        SpuRepairPushSgpToWaimaiRequest request = new SpuRepairPushSgpToWaimaiRequest();
        request.setTenantId(tenantId);
        request.setStoreId(storeId);
        request.setChannelId(channelId);
        request.setCustomSpuIds(customSpuIds);
        request.setOperatorId(operatorId);
        request.setOperatorName(operatorName);

        try {
            log.info("ProblemSpuThriftService pushSgpToWaimai, request:{}", request);
            SpuRepairPushSgpToWaimaiResponse response = problemSpuThriftService.pushSgpToWaimai(request);
            log.info("ProblemSpuThriftService pushSgpToWaimai, response:{}", response);

            if (response == null || response.getStatus() == null) {
                return SpuRepairResultVO.of(channelId, customSpuIds, ResultCode.FAIL.getCode(),
                        "使用牵牛花商品向商家端推送错误, 未返回结果");
            }

            if (response.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
                return SpuRepairResultVO.of(channelId, customSpuIds, ResultCode.FAIL.getCode(),
                        response.getStatus().getMsg());
            }

            return SpuRepairResultVO.of(response.getResultDTOList());
        } catch (Exception e) {
            return SpuRepairResultVO.of(channelId, customSpuIds, ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

    /**
     * 通过商家端创建牵牛花商品
     * @param tenantId
     * @param storeId
     * @param channelId
     * @param customSpuIds
     * @param operatorId
     * @param operatorName
     * @return
     */
    private List<SpuRepairResultVO> createSgpFromWaimai(
            Long tenantId, Long storeId, Integer channelId, List<String> customSpuIds,
            Long operatorId, String operatorName) {

        SpuRepairCreateSgpFromWaimaiRequest request = new SpuRepairCreateSgpFromWaimaiRequest();
        request.setTenantId(tenantId);
        request.setStoreId(storeId);
        request.setChannelId(channelId);
        request.setCustomSpuIds(customSpuIds);
        request.setOperatorId(operatorId);
        request.setOperatorName(operatorName);

        try {
            log.info("ProblemSpuThriftService createSgpFromWaimai, request:{}", request);
            SpuRepairCreateSgpFromWaimaiResponse response = problemSpuThriftService.createSgpFromWaimai(request);
            log.info("ProblemSpuThriftService createSgpFromWaimai, response:{}", response);

            if (response == null || response.getStatus() == null) {
                return SpuRepairResultVO.of(channelId, customSpuIds, ResultCode.FAIL.getCode(),
                        "使用商家端商品向蔬果派新增错误, 未返回结果");
            }

            if (response.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
                return SpuRepairResultVO.of(channelId, customSpuIds, ResultCode.FAIL.getCode(),
                        response.getStatus().getMsg());
            }

            return SpuRepairResultVO.of(response.getResultDTOList());
        } catch (Exception e) {
            return SpuRepairResultVO.of(channelId, customSpuIds, ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

    /**
     * 使用商家端商品信息修复牵牛花基础信息
     * @param tenantId
     * @param storeId
     * @param channelId
     * @param customSpuIds
     * @param operatorId
     * @param operatorName
     * @return
     */
    private List<SpuRepairResultVO> repairSpuBaseInfoByWaimai(
            Long tenantId, Long storeId, Integer channelId, List<String> customSpuIds,
            Long operatorId, String operatorName, CompareSpuTypeEnum compareSpuTypeEnum) {

        SpuBaseInfoRepairByWaimaiRequest request = new SpuBaseInfoRepairByWaimaiRequest();
        request.setTenantId(tenantId);
        request.setStoreId(storeId);
        request.setChannelId(channelId);
        request.setCustomSpuIds(customSpuIds);
        request.setOperatorId(operatorId);
        request.setOperatorName(operatorName);

        // 设置后端不一致类型 目前基础信息和销售信息修复必须传值（其余场景可为null) ， app端的不一致类型 CompareSpuTypeEnum 转为 SpuCompareTypeEnum （如301->7)
        Integer compareType = Optional.ofNullable(compareSpuTypeEnum).map(CompareSpuTypeEnum::getWebCode).orElse(null);
        request.setCompareType(compareType);


        try {
            log.info("ProblemSpuThriftService repairSpuBaseInfoByWaimai, request:{}", request);
            SpuBaseInfoRepairByWaimaiResponse response = problemSpuThriftService.repairSpuBaseInfoByWaimai(request);
            log.info("ProblemSpuThriftService repairSpuBaseInfoByWaimai, response:{}", response);

            if (response == null || response.getStatus() == null) {
                return SpuRepairResultVO.of(channelId, customSpuIds, ResultCode.FAIL.getCode(),
                        "使用商家端商品信息修复牵牛花商品信息错误, 未返回结果");
            }

            if (response.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
                return SpuRepairResultVO.of(channelId, customSpuIds, ResultCode.FAIL.getCode(),
                        response.getStatus().getMsg());
            }

            return SpuRepairResultVO.of(response.getResultDTOList());
        } catch (Exception e) {
            return SpuRepairResultVO.of(channelId, customSpuIds, ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

    /**
     * 使用牵牛花基础信息修复商家端信息
     * @param tenantId
     * @param storeId
     * @param channelId
     * @param customSpuIds
     * @param operatorId
     * @param operatorName
     * @return
     */
    private List<SpuRepairResultVO> repairSpuBaseInfoBySgp(
            Long tenantId, Long storeId, Integer channelId, List<String> customSpuIds,
            Long operatorId, String operatorName, CompareSpuTypeEnum compareSpuTypeEnum) {

        SpuBaseInfoRepairBySgpRequest request = new SpuBaseInfoRepairBySgpRequest();
        request.setTenantId(tenantId);
        request.setStoreId(storeId);
        request.setChannelId(channelId);
        request.setCustomSpuIds(customSpuIds);
        request.setOperatorId(operatorId);
        request.setOperatorName(operatorName);

        // 设置后端不一致类型 目前基础信息和销售信息修复必须传值（其余场景可为null) ， app端的不一致类型 CompareSpuTypeEnum 转为 SpuCompareTypeEnum （如301->7)
        Integer compareType = Optional.ofNullable(compareSpuTypeEnum).map(CompareSpuTypeEnum::getWebCode).orElse(null);
        request.setCompareType(compareType);

        try {
            log.info("ProblemSpuThriftService repairSpuBaseInfoBySgp, request:{}", request);
            SpuBaseInfoRepairBySgpResponse response = problemSpuThriftService.repairSpuBaseInfoBySgp(request);
            log.info("ProblemSpuThriftService repairSpuBaseInfoBySgp, response:{}", response);

            if (response == null || response.getStatus() == null) {
                return SpuRepairResultVO.of(channelId, customSpuIds, ResultCode.FAIL.getCode(),
                        "使用蔬果派商品信息修复商家端商品信息错误, 未返回结果");
            }

            if (response.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
                return SpuRepairResultVO.of(channelId, customSpuIds, ResultCode.FAIL.getCode(),
                        response.getStatus().getMsg());
            }

            return SpuRepairResultVO.of(response.getResultDTOList());
        } catch (Exception e) {
            return SpuRepairResultVO.of(channelId, customSpuIds, ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

    /**
     * 使用商家端金额修改牵牛花金额
     * @param tenantId
     * @param storeId
     * @param channelId
     * @param customSpuIds
     * @param operatorId
     * @param operatorName
     * @return
     */
    private List<SpuRepairResultVO> repairSpuRetailPriceByWaimai(
            Long tenantId, Long storeId, Integer channelId, List<String> customSpuIds,
            Long operatorId, String operatorName) {

        SpuRetailPriceRepairByWaimaiRequest request = new SpuRetailPriceRepairByWaimaiRequest();
        request.setTenantId(tenantId);
        request.setStoreId(storeId);
        request.setChannelId(channelId);
        request.setCustomSpuIds(customSpuIds);
        request.setOperatorId(operatorId);
        request.setOperatorName(operatorName);

        try {
            log.info("ProblemSpuThriftService repairSpuRetailPriceByWaimai, request:{}", request);
            SpuRetailPriceRepairByWaimaiResponse response = problemSpuThriftService.repairSpuRetailPriceByWaimai(request);
            log.info("ProblemSpuThriftService repairSpuRetailPriceByWaimai, response:{}", response);

            if (response == null || response.getStatus() == null) {
                return SpuRepairResultVO.of(channelId, customSpuIds, ResultCode.FAIL.getCode(),
                        "使用商家端商品价格修复蔬果派商品价格错误, 未返回结果");
            }

            if (response.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
                return SpuRepairResultVO.of(channelId, customSpuIds, ResultCode.FAIL.getCode(),
                        response.getStatus().getMsg());
            }

            return SpuRepairResultVO.of(response.getResultDTOList());
        } catch (Exception e) {
            return SpuRepairResultVO.of(channelId, customSpuIds, ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

    /**
     * 使用牵牛花金额修改商家端金额
     * @param tenantId
     * @param storeId
     * @param channelId
     * @param customSpuIds
     * @param operatorId
     * @param operatorName
     * @return
     */
    private List<SpuRepairResultVO> repairOnlinePriceBySgp(
            Long tenantId, Long storeId, Integer channelId, List<String> customSpuIds,
            Long operatorId, String operatorName) {

        OnlinePriceRepairBySgpRequest request = new OnlinePriceRepairBySgpRequest();
        request.setTenantId(tenantId);
        request.setStoreId(storeId);
        request.setChannelId(channelId);
        request.setCustomSpuIds(customSpuIds);
        request.setOperatorId(operatorId);
        request.setOperatorName(operatorName);

        try {
            log.info("ProblemSpuThriftService repairOnlinePriceBySgp, request:{}", request);
            OnlinePriceRepairBySgpResponse response = problemSpuThriftService.repairOnlinePriceBySgp(request);
            log.info("ProblemSpuThriftService repairOnlinePriceBySgp, response:{}", response);

            if (response == null || response.getStatus() == null) {
                return SpuRepairResultVO.of(channelId, customSpuIds, ResultCode.FAIL.getCode(),
                        "使用蔬果派商品价格修复商家端商品价格错误, 未返回结果");
            }

            if (response.getStatus().getCode() != 0) {
                return SpuRepairResultVO.of(channelId, customSpuIds, ResultCode.FAIL.getCode(),
                        response.getStatus().getMsg());
            }

            return SpuRepairResultVO.of(response.getResultDTOList());
        } catch (Exception e) {
            return SpuRepairResultVO.of(channelId, customSpuIds, ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

    public CommonResponse<SpuProblemListResponseVO> querySpuProblemList(SpuProblemListRequest request) {

        CommonResponse<StoreSpuVO> storeSpuVOCommonResponse = ocmsProductServiceWrapper.detailStoreSpu(request.toDetail());

        if (storeSpuVOCommonResponse == null || storeSpuVOCommonResponse.getData() == null
                || storeSpuVOCommonResponse.getCode() != 0) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "查询商品问题列表失败");
        }
        return CommonResponse.success(SpuProblemListResponseVO.of(storeSpuVOCommonResponse.getData()));
    }

    public CommonResponse<DiffCompareSpuResponseVO> queryDiffCompareSpuListByType(User user, DiffCompareSpuQueryRequest request) {
        // 获取当前用户有权限的门店列表
        List<Long> authStoreIds = authThriftWrapper.queryPermissionGroupId(user, PermissionGroupTypeEnum.POI,
                Long::valueOf);
        if (!authStoreIds.contains(request.getStoreId())) {
            log.warn("当前用户无门店权限，无请求数据");
            return CommonResponse.fail2(ResultCode.AUTHORIZE_ERROR);
        }

        QueryDiffCompareSpuResponse response = problemSpuThriftService.queryDiffCompareSpuList(request.to(user.getTenantId()));
        if (!response.getStatus().getCode().equals(ResultCode.SUCCESS.getCode())) {
            log.error("查询用户问题商品列表异常， request [{}], response [{}].", request, response);
            return CommonResponse.fail(response.getStatus().getCode(), response.getStatus().getMsg());
        }
        DiffCompareSpuResponseVO diffCompareSpuResponseVO = DiffCompareSpuResponseVO.builder()
                .pageInfo(new PageInfoVO(response.getPageInfoDTO()))
                .diffCompareSpuInfoVOS(ConverterUtils.convertList(response.getDiffCompareSpuInfoDTOS(), MultiChannelDiffCompareSpuInfoVO::of))
                .build();

        return CommonResponse.success(diffCompareSpuResponseVO);
    }

    /**
     * 分类统计店铺问题商品数量
     * @param user
     * @param request
     */
    public CommonResponse<ProblemSpuTypeVo> queryDiffCompareSpuTypeCount(User user, DiffCompareSpuQueryRequest request) {
        // 获取当前用户有权限的门店列表
        List<Long> authStoreIds = authThriftWrapper.queryPermissionGroupId(user, PermissionGroupTypeEnum.POI,
                Long::valueOf);
        if (!authStoreIds.contains(request.getStoreId())) {
            log.warn("当前用户无门店权限，无请求数据");
            return CommonResponse.fail2(ResultCode.AUTHORIZE_ERROR);
        }
        QueryCompareTypeCountResponse response = problemSpuThriftService.queryDiffCompareTypeCount(request.to(user.getTenantId()));
        if (!response.getStatus().getCode().equals(ResultCode.SUCCESS.getCode())) {
            log.error("查询店铺问题商品类型统计异常， request [{}], response [{}].", request, response);
            return CommonResponse.fail(response.getStatus().getCode(), response.getStatus().getMsg());
        }
        return CommonResponse.success(ProblemSpuTypeVo.builder().typeCount(response.getCompareTypeCount()).count(response.getTotal()).build());
    }

    public CommonResponse<ProblemSpuOptResponseVO> syncDiffCompareOperate(DiffCompareSpuOptRequest request) {

        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        // 获取当前用户有权限的门店列表
        List<Long> authStoreIds = authThriftWrapper.queryPermissionGroupId(user, PermissionGroupTypeEnum.POI,
                Long::valueOf);
        if (!authStoreIds.contains(request.getStoreId())) {
            log.warn("当前用户无门店权限，无请求数据");
            return CommonResponse.fail2(ResultCode.AUTHORIZE_ERROR);
        }

        List<SpuRepairResultVO> failureList = Lists.newArrayList();

        try {
            log.info("ProblemSpuThriftService batchRepairDiffCompareInfo, request:{}", request);
            DiffCompareSpuRepairOptResponse optResponse = problemSpuThriftService.batchRepairDiffCompareInfo(request.convert2Request(user));
            log.info("ProblemSpuThriftService batchRepairDiffCompareInfo, response:{}", optResponse);

            if (optResponse == null || optResponse.getStatus() == null) {
                return CommonResponse.success(new ProblemSpuOptResponseVO(SpuRepairResultVO.of(request.getCustomSpuKeys(),
                        ResultCode.FAIL.getCode(), "未返回结果，数据修复结果未知")));
            }

            if (optResponse.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
                failureList = SpuRepairResultVO.of(request.getCustomSpuKeys(), ResultCode.FAIL.getCode(), optResponse.getStatus().getMsg());
            } else {
                failureList = SpuRepairResultVO.of(optResponse.getResultDTOList()).stream()
                        .filter(resultVO -> resultVO.getCode() != null && resultVO.getCode() != 0)
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("不一致数据执行修复任务失败，request {}.", request, e);
            failureList = SpuRepairResultVO.of(request.getCustomSpuKeys(), ResultCode.FAIL.getCode(), e.getMessage());
        }

        return CommonResponse.success(new ProblemSpuOptResponseVO(failureList));
    }
}

