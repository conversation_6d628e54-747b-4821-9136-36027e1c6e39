package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * ERP门店商品渠道价格VO
 *
 * <AUTHOR>
 * @since 2023/08/07
 */
@TypeDoc(
        description = "ERP门店商品渠道价格VO"
)
@ApiModel("ERP门店商品渠道价格VO")
@Getter
@Setter
@ToString
public class ErpStoreSpuChannelPriceVO {

    @FieldDoc(description = "sku编码", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "sku编码")
    private String skuId;

    @FieldDoc(description = "渠道ID", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "渠道ID")
    private Integer channelId;

    @FieldDoc(description = "渠道加价率", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "渠道加价率")
    private Float channelAdjustRate;

    @FieldDoc(description = "商品加价率", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "商品加价率")
    private Float skuAdjustRate;

    @FieldDoc(description = "渠道售价（手动设置的价格）", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "渠道售价（手动设置的价格）")
    private String channelPrice;

    @FieldDoc(description = "商品根据渠道配置所使用的牵牛花售价/原价", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "商品根据渠道配置所使用的牵牛花售价/原价")
    private String price;

    @FieldDoc(description = "商品渠道实际售价", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "商品渠道实际售价")
    private String onlineChannelPrice;

    @FieldDoc(description = "商品渠道促销价", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "商品渠道促销价")
    private String promotionPrice;
}