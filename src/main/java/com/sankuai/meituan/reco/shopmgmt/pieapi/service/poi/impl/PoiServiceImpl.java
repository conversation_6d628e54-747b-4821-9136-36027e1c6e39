package com.sankuai.meituan.reco.shopmgmt.pieapi.service.poi.impl;

import com.meituan.linz.boot.util.Fun;
import com.meituan.shangou.saas.tenant.thrift.dto.DistrictQueryDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiConditionDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.request.PoiListQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.facade.PoiServiceFacade;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.area.bo.AreaBO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.poi.PoiService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.poi.bo.PoiInfoBO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * Email <EMAIL>
 * Date 2022/9/7 8:13 下午
 * Description
 */
@Service
public class PoiServiceImpl implements PoiService {

    @Resource
    private PoiServiceFacade poiServiceFacade;

    @Override
    public List<PoiInfoBO> queryPois(Long tenantId, AreaBO areaInfo, String searchKey) {
        return queryPois(tenantId, areaInfo, searchKey, null, false);
    }

    @Override
    public List<PoiInfoBO> queryPois(Long tenantId, AreaBO areaInfo, String searchKey, List<Integer> entityTypes, boolean excludeShareableWarehouseBindingStore) {
        PoiListQueryRequest request = new PoiListQueryRequest();

        request.setTenantId(tenantId);
        request.setEntityTypes(entityTypes);
        request.setExcludeShareableWarehouseBindingStore(excludeShareableWarehouseBindingStore);

        if (StringUtils.isNotEmpty(searchKey)) {
            PoiConditionDto conditionDto = new PoiConditionDto();
            conditionDto.setPoiAddress(searchKey);
            conditionDto.setPoiName(searchKey);
            conditionDto.setCondition(2);
            request.setNameCondition(conditionDto);
        }

        if (areaInfo != null) {
            DistrictQueryDto districtQueryDto = new DistrictQueryDto();
            districtQueryDto.setProvinceId(ConverterUtils.nonNullConvert(areaInfo.getProvinceCode(), Integer::valueOf));
            districtQueryDto.setCityId(ConverterUtils.nonNullConvert(areaInfo.getCityCode(), Integer::valueOf));
            request.setDistrict(districtQueryDto);
        }

        List<PoiInfoDto> poiInfoDtos = poiServiceFacade.queryPoiListWithCondition(request);

        return Fun.map(poiInfoDtos, PoiInfoBO::new);
    }

    @Override
    public PoiInfoBO getPoiById(Long tenantId, Long poiId) {
        PoiInfoDto poiInfoDto = poiServiceFacade.queryPoiInfo(tenantId, poiId);
        if (poiInfoDto == null) {
            return null;
        }
        return new PoiInfoBO(poiInfoDto);
    }
}
