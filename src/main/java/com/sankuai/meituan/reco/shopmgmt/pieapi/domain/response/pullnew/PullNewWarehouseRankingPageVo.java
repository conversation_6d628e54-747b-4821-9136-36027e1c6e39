package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/9/22
 */
@TypeDoc(
        description = "地推列表分页信息"
)
@Data
@ApiModel("地推列表分页信息")
public class PullNewWarehouseRankingPageVo {

    public List<PullNewWarehouseRankingVo>  pullNewWarehouseRankingVoList;

    public boolean hasMore;
    public String dataUpdateTime;
}
