package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.PriceExtendEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ProjectConstants;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.price.PriceExtendCategorySkuReq;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.SkuTagCategoryVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.SkuTagSimpleVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MoneyUtils;
import com.sankuai.meituan.reco.store.saas.infrastructure.shield.common.monitor.CommonMonitorTransaction;
import com.sankuai.meituan.shangou.empower.ocms.client.price.dto.index.PriceIndexDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.price.dto.index.SkuPriceIndexDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.price.dto.index.SkuPriceIndexListDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.price.dto.index.StoreCategoryPriceIndexDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.price.dto.premiumrate.PremiumRateDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.price.dto.premiumrate.SkuPremiumRateDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.price.dto.premiumrate.SkuPremiumRateListDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.price.dto.premiumrate.StoreCategoryPremiumRateDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.price.dto.strategy.SkuHitSyncStrategyDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.price.enums.ChannelPriceResultEnum;
import com.sankuai.meituan.shangou.empower.ocms.client.price.request.StoreBaseQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.price.request.index.CategorySkuPriceIndexQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.price.request.premiumrate.CategorySkuPremiumRateRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.price.response.OcmsCommonResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.price.response.index.CategorySkuPriceIndexResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.price.response.index.StoreCategoryPriceIndexResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.price.response.premiumrate.CategorySkuPremiumRateResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.price.response.premiumrate.StoreCategoryPremiumRateResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.price.service.PremiumRateThriftService;
import com.sankuai.meituan.shangou.empower.ocms.client.price.service.PriceIndexThriftService;
import com.sankuai.meituan.shangou.empower.price.client.request.index.StorePriceIndexRequest;
import com.sankuai.meituan.shangou.empower.price.client.request.premiumrate.StorePremiumRateRequest;
import com.sankuai.meituan.shangou.empower.price.client.response.PriceCommonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.sankuai.meituan.shangou.empower.ocms.client.price.enums.strategy.SyncStrategyTypeEnum.*;

/**
 * @Author: wangyihao04
 * @Date: 2020-06-11 16:54
 * @Mail: <EMAIL>
 */
@Slf4j
@Service
public class PriceExtendWrapper {
    @Resource
    private PremiumRateThriftService premiumRateThriftService;
    @Resource
    private PriceIndexThriftService priceIndexThriftService;
    @Resource
    private OCMSServiceWrapper ocmsServiceWrapper;

    @Resource(name = "newPremiumRateThriftService")
    private com.sankuai.meituan.shangou.empower.price.client.service.
            PremiumRateThriftService newPremiumRateThriftService;

    @Resource(name = "newPriceIndexThriftService")
    private com.sankuai.meituan.shangou.empower.price.client.service.
            PriceIndexThriftService newPriceIndexThriftService;

    @MethodLog(logResponse = true, logRequest = true)
    @CommonMonitorTransaction
    public CommonResponse<PriceExtendStoreCategoryVO> queryStoreCategoryPriceExtend(Long tenantId, Long storeId,
                                                                                    Integer type, Boolean tagCoreFilter,
                                                                                    Boolean tagCommonFilter) {
        try {
            Long tagId = null;
            if (Integer.valueOf(PriceExtendEnum.PRICE_INDEX.getCode()).equals(type)) {
                //todo 灰度开关
                if (MccConfigUtil.isPriceMigrateGraySwitch(ProjectConstants.PRICE_INDEX_MIGRATE_SWITCH, tenantId, storeId)){
                    if (BooleanUtils.isTrue(tagCommonFilter)) {
                        tagId = queryCommonTagId();
                    }
                    StorePriceIndexRequest request = new StorePriceIndexRequest(tenantId, storeId, tagId);
                    com.sankuai.meituan.shangou.empower.price.client.response.index
                            .StoreCategoryPriceIndexResponse response = newPriceIndexThriftService.queryStoreCategoryPriceIndex(request);
                    if (Objects.isNull(response)){
                        throw new CommonLogicException("价格指数返回null");
                    }
                    checkResponseStatusNew(response.getStatus());
                    PriceExtendStoreCategoryVO priceExtendStoreCategoryVO = Convert.storeCategoryPriceIndexToVONew(response.getData());
                    return CommonResponse.success(priceExtendStoreCategoryVO);
                } else {
                    StoreBaseQueryRequest priceIndexRequest = new StoreBaseQueryRequest(tenantId, storeId, null);
                    StoreCategoryPriceIndexResponse response = priceIndexThriftService.queryStoreCategoryPriceIndex(priceIndexRequest);
                    if (Objects.isNull(response)){
                        throw new CommonLogicException("价格指数返回null");
                    }
                    checkResponseStatus(response.getStatus());
                    PriceExtendStoreCategoryVO priceExtendStoreCategoryVO = Convert.storeCategoryPriceIndexToVO(response.getData());
                    return CommonResponse.success(priceExtendStoreCategoryVO);
                }
            } else if (Integer.valueOf(PriceExtendEnum.PREMIUM_RATE.getCode()).equals(type)) {
                if (tagCoreFilter){
                    tagId = queryCoreTagId();
                }
                if (MccConfigUtil.isPriceMigrateGraySwitch(ProjectConstants.PREMIUM_RATE_MIGRATE_SWITCH, tenantId, storeId)){
                    StorePremiumRateRequest request = new StorePremiumRateRequest(tenantId, storeId, tagId);
                    com.sankuai.meituan.shangou.empower.price.client.response.premiumrate.
                            StoreCategoryPremiumRateResponse response = newPremiumRateThriftService.queryStoreCategoryPremiumRate(request);
                    if (Objects.isNull(response)){
                        throw new CommonLogicException("溢价率返回null");
                    }
                    checkResponseStatusNew(response.getStatus());
                    PriceExtendStoreCategoryVO priceExtendStoreCategoryVO = Convert.storeCategoryPremiumRateToVONew(response.getData());
                    return CommonResponse.success(priceExtendStoreCategoryVO);
                }else {
                    StoreBaseQueryRequest premiumRateRequest = new StoreBaseQueryRequest(tenantId, storeId, tagId);
                    StoreCategoryPremiumRateResponse response = premiumRateThriftService.queryStoreCategoryPremiumRate(premiumRateRequest);
                    if (Objects.isNull(response)){
                        throw new CommonLogicException("溢价率返回null");
                    }
                    checkResponseStatus(response.getStatus());
                    PriceExtendStoreCategoryVO priceExtendStoreCategoryVO = Convert.storeCategoryPremiumRateToVO(response.getData());
                    return CommonResponse.success(priceExtendStoreCategoryVO);
                }
            } else {
                log.error("queryStoreCategoryPriceExtend 参数错误");
                return CommonResponse.fail2(ResultCode.PARAM_ERR);
            }
        } catch (CommonLogicException e) {
            log.error("查询门店价格扩展指标服务端异常", e);
            return CommonResponse.fail2(ResultCode.FAIL);
        } catch (TException e) {
            log.error("调用门店价格扩展指标接口异常", e);
            return CommonResponse.fail2(ResultCode.FAIL);
        } catch (Exception e) {
            log.error("查询门店价格扩展指标未知异常", e);
            return CommonResponse.fail2(ResultCode.FAIL);
        }
    }


    @MethodLog(logResponse = true, logRequest = true)
    @CommonMonitorTransaction
    public CommonResponse<PriceExtendCategorySkuVO> queryCategorySkuPriceExtend(Long tenantId, PriceExtendCategorySkuReq req) {
        try {
            Long tagId = null;
            if (Integer.valueOf(PriceExtendEnum.PRICE_INDEX.getCode()).equals(req.getType())) {
                if (BooleanUtils.isTrue(req.getFilterCommon())){
                    tagId = queryCommonTagId();
                }
                if (MccConfigUtil.isPriceMigrateGraySwitch(ProjectConstants.PRICE_INDEX_MIGRATE_SWITCH, tenantId, req.getStoreId() )){
                    com.sankuai.meituan.shangou.empower.price.client.response.index.
                            CategorySkuPriceIndexResponse response = newPriceIndexThriftService.queryCategoryProductPriceIndex(Convert.buildCategorySkuPriceIndexRequestNew(tenantId, tagId, req));
                    if (Objects.isNull(response)){
                        throw new CommonLogicException("价格指数返回null");
                    }
                    checkResponseStatusNew(response.getStatus());
                    return CommonResponse.success(Convert.categorySkuPriceIndexToVONew(response.getData()));
                }else {
                    CategorySkuPriceIndexResponse response = priceIndexThriftService
                            .queryCategoryProductPriceIndex(Convert.buildCategorySkuPriceIndexRequest(tenantId, req));
                    if (Objects.isNull(response)){
                        throw new CommonLogicException("价格指数返回null");
                    }
                    checkResponseStatus(response.getStatus());
                    return CommonResponse.success(Convert.categorySkuPriceIndexToVO(response.getData()));
                }
            } else if (Integer.valueOf(PriceExtendEnum.PREMIUM_RATE.getCode()).equals(req.getType())) {
                if (req.getFilterCore()){
                    tagId = queryCoreTagId();
                }
                if (MccConfigUtil.isPriceMigrateGraySwitch(ProjectConstants.PREMIUM_RATE_MIGRATE_SWITCH,tenantId, req.getStoreId())){
                    com.sankuai.meituan.shangou.empower.price.client.response.premiumrate.
                            CategorySkuPremiumRateResponse response = newPremiumRateThriftService
                            .queryCategorySkuPremiumRate(Convert.buildCategorySkuPriceExtendRequestNew(tenantId, req, tagId));
                    if (Objects.isNull(response)){
                        throw new CommonLogicException("溢价率返回null");
                    }
                    checkResponseStatusNew(response.getStatus());
                    return CommonResponse.success(Convert.categorySkuPremiumRateToVONew(response.getData()));
                }else {
                    CategorySkuPremiumRateResponse response = premiumRateThriftService
                            .queryCategorySkuPremiumRate(Convert.buildCategorySkuPriceExtendRequest(tenantId, req, tagId));
                    if (Objects.isNull(response)){
                        throw new CommonLogicException("溢价率返回null");
                    }
                    checkResponseStatus(response.getStatus());
                    return CommonResponse.success(Convert.categorySkuPremiumRateToVO(response.getData()));
                }
            } else {
                log.error("queryStoreCategoryPriceExtend 参数错误");
                return CommonResponse.fail2(ResultCode.PARAM_ERR);
            }
        } catch (CommonLogicException e) {
            log.error("查询品类商品价格扩展指标服务端异常", e);
            return CommonResponse.fail2(ResultCode.FAIL);
        } catch (TException e) {
            log.error("调用品类商品价格扩展指标接口异常", e);
            return CommonResponse.fail2(ResultCode.FAIL);
        } catch (Exception e) {
            log.error("查询品类商品价格扩展指标未知异常", e);
            return CommonResponse.fail2(ResultCode.FAIL);
        }
    }

    public Long queryCoreTagId(){
        CommonResponse<List<SkuTagCategoryVO>> tagResponse = ocmsServiceWrapper.queryTags();
        if (tagResponse.getCode() == 0){
            return Optional.ofNullable(tagResponse.getData())
                    .map(List::stream)
                    .orElse(Stream.empty())
                    .flatMap(skuTagCategoryVO -> skuTagCategoryVO.getTags().stream())
                    .filter(skuTagSimpleVO -> skuTagSimpleVO.getTagName().equals(MccConfigUtil.getCoreTagName()))
                    .findAny()
                    .map(SkuTagSimpleVO::getTagId)
                    .orElse(null);
        }
        return null;
    }

    public Long queryCommonTagId(){
        CommonResponse<List<SkuTagCategoryVO>> tagResponse = ocmsServiceWrapper.queryTags();
        if (tagResponse.getCode() == 0){
            return Optional.ofNullable(tagResponse.getData())
                    .map(List::stream)
                    .orElse(Stream.empty())
                    .flatMap(skuTagCategoryVO -> skuTagCategoryVO.getTags().stream())
                    .filter(skuTagSimpleVO -> skuTagSimpleVO.getTagName().equals(MccConfigUtil.getCommonTagName()))
                    .findAny()
                    .map(SkuTagSimpleVO::getTagId)
                    .orElse(null);
        }
        return null;
    }

    public void checkResponseStatus(OcmsCommonResponse status) {
        if (Objects.isNull(status) || status.getCode() != ChannelPriceResultEnum.SUCCESS.getCode()) {
            throw new CommonLogicException();
        }
    }

    public void checkResponseStatusNew(PriceCommonResponse status) {
        if (Objects.isNull(status) || status.getCode() != ChannelPriceResultEnum.SUCCESS.getCode()) {
            throw new CommonLogicException();
        }
    }

    static class Convert {
        static PriceExtendStoreCategoryVO storeCategoryPremiumRateToVO(StoreCategoryPremiumRateDTO dto) {
            PriceExtendVO storePriceExtend = premiumRateDTOToVO(dto.getStorePremiumRate());
            List<PriceExtendVO> categoryPriceExtendList = Optional
                    .ofNullable(dto.getCategoryPremiumRate())
                    .map(List::stream)
                    .orElse(Stream.empty())
                    .map(Convert::premiumRateDTOToVO)
                    .collect(Collectors.toList());
            return new PriceExtendStoreCategoryVO(storePriceExtend, categoryPriceExtendList);
        }

        static PriceExtendStoreCategoryVO storeCategoryPremiumRateToVONew(com.sankuai.meituan.shangou.empower.price.client.dto.premiumrate.
                                                                                  StoreCategoryPremiumRateDTO dto) {
            PriceExtendVO storePriceExtend = premiumRateDTOToVONew(dto.getStorePremiumRate());
            List<PriceExtendVO> categoryPriceExtendList = Optional
                    .ofNullable(dto.getCategoryPremiumRate())
                    .map(List::stream)
                    .orElse(Stream.empty())
                    .map(Convert::premiumRateDTOToVONew)
                    .collect(Collectors.toList());
            return new PriceExtendStoreCategoryVO(storePriceExtend, categoryPriceExtendList);
        }

        static PriceExtendStoreCategoryVO storeCategoryPriceIndexToVO(StoreCategoryPriceIndexDTO dto) {
            PriceExtendVO storePriceExtend = priceIndexDTOToVO(dto.getStorePriceIndex());
            List<PriceExtendVO> categoryPriceExtendList = Optional
                    .ofNullable(dto.getCategoryPriceIndex())
                    .map(List::stream)
                    .orElse(Stream.empty())
                    .map(Convert::priceIndexDTOToVO)
                    .collect(Collectors.toList());
            return new PriceExtendStoreCategoryVO(storePriceExtend, categoryPriceExtendList);
        }
        static PriceExtendStoreCategoryVO storeCategoryPriceIndexToVONew(com.sankuai.meituan.shangou.empower.price.client.dto.index.
                                                                                 StoreCategoryPriceIndexDTO dto) {
            PriceExtendVO storePriceExtend = priceIndexDTOToVONew(dto.getStorePriceIndex());
            List<PriceExtendVO> categoryPriceExtendList = Optional
                    .ofNullable(dto.getCategoryPriceIndex())
                    .map(List::stream)
                    .orElse(Stream.empty())
                    .map(Convert::priceIndexDTOToVONew)
                    .collect(Collectors.toList());
            return new PriceExtendStoreCategoryVO(storePriceExtend, categoryPriceExtendList);
        }


        static CategorySkuPremiumRateRequest buildCategorySkuPriceExtendRequest(Long tenantId, PriceExtendCategorySkuReq req, Long tagId) {
            return CategorySkuPremiumRateRequest.builder()
                    .tenantId(tenantId)
                    .storeId(req.getStoreId())
                    .firstLevelCategoryId(req.getFirstLevelCategoryId())
                    .needCategoryPremiumRate(req.isNeedCategoryPriceExtend())
                    .pageNum(req.getPageNum())
                    .pageSize(req.getPageSize())
                    .tagId(tagId)
                    .build();
        }

        static com.sankuai.meituan.shangou.empower.price.client.request.premiumrate.
                CategorySkuPremiumRateRequest buildCategorySkuPriceExtendRequestNew(Long tenantId, PriceExtendCategorySkuReq req, Long tagId) {
            return com.sankuai.meituan.shangou.empower.price.client.request.premiumrate.
                    CategorySkuPremiumRateRequest.builder()
                    .tenantId(tenantId)
                    .storeId(req.getStoreId())
                    .firstLevelCategoryId(req.getFirstLevelCategoryId())
                    .needCategoryPremiumRate(req.isNeedCategoryPriceExtend())
                    .pageNum(req.getPageNum())
                    .pageSize(req.getPageSize())
                    .tagId(tagId)
                    .build();
        }

        static CategorySkuPriceIndexQueryRequest buildCategorySkuPriceIndexRequest(Long tenantId, PriceExtendCategorySkuReq req) {
            return CategorySkuPriceIndexQueryRequest.builder()
                    .tenantId(tenantId)
                    .storeId(req.getStoreId())
                    .firstLevelCategoryId(req.getFirstLevelCategoryId())
                    .needCategoryPriceIndex(req.isNeedCategoryPriceExtend())
                    .pageNum(req.getPageNum())
                    .pageSize(req.getPageSize())
                    .build();
        }

        static com.sankuai.meituan.shangou.empower.price.client.request.index.
                CategorySkuPriceIndexQueryRequest buildCategorySkuPriceIndexRequestNew(Long tenantId, Long tagId,
                                                                                       PriceExtendCategorySkuReq req) {
            return com.sankuai.meituan.shangou.empower.price.client.request.index.
                    CategorySkuPriceIndexQueryRequest.builder()
                    .tenantId(tenantId)
                    .storeId(req.getStoreId())
                    .firstLevelCategoryId(req.getFirstLevelCategoryId())
                    .needCategoryPriceIndex(req.isNeedCategoryPriceExtend())
                    .pageNum(req.getPageNum())
                    .pageSize(req.getPageSize())
                    .tagId(tagId)
                    .build();
        }

        static PriceExtendCategorySkuVO categorySkuPremiumRateToVO(SkuPremiumRateListDTO dto) {
            PriceExtendVO categoryPriceExtend = premiumRateDTOToVO(dto.getCategoryPremiumRate());
            List<SkuPriceExtendVO> skuPriceExtendVOS = dto.getSkuPremiumRateList().stream()
                    .map(skuPremiumRate -> {
                        SkuPriceExtendVO vo = new SkuPriceExtendVO();
                        vo.setUniqueId(skuPremiumRate.getSkuId());
                        vo.setName(skuPremiumRate.getSkuName());
                        vo.setPriceExtend(skuPremiumRate.getPremiumRateDTO().getPremiumRate());
                        vo.setThreshold(skuPremiumRate.getPremiumRateDTO().getReference());
                        vo.setDisplayInfo(skuPremiumRate.getPremiumRateDTO().getDisplayInfo());
                        vo.setSpuId(skuPremiumRate.getSpuId());
                        vo.setSpec(skuPremiumRate.getSpec());
                        vo.setImages(skuPremiumRate.getImageList());
                        vo.setMonthSaleAmount(skuPremiumRate.getSales30Day());
                        vo.setWeight(skuPremiumRate.getWeight());
                        vo.setSurveyPrice(parsePrice(skuPremiumRate.getSurveyPrice()));
                        vo.setSurveyPriceOf500g(parsePrice((skuPremiumRate.getWeightType() == 3 || skuPremiumRate.getWeight() == 0)
                                ? null : skuPremiumRate.getSurveyPrice()));
                        vo.setOfflinePrice(parsePrice(skuPremiumRate.getOfflinePrice()));
                        vo.setOfflinePriceOf500g(convertToPriceOf500g(skuPremiumRate, SkuPremiumRateDTO::getWeightType, SkuPremiumRateDTO::getWeight, SkuPremiumRateDTO::getOfflinePrice));
                        vo.setOnlinePrice(parsePrice(skuPremiumRate.getOnlinePrice()));
                        vo.setOnlinePriceOf500g(convertToPriceOf500g(skuPremiumRate, SkuPremiumRateDTO::getWeightType, SkuPremiumRateDTO::getWeight, SkuPremiumRateDTO::getOnlinePrice));
                        vo.setAdjustPriceStrategy(convertToVo(skuPremiumRate.getHitStrategyDTO()));
                        vo.setSaleUnit(skuPremiumRate.getSaleUnit());
                        vo.setWeightType(skuPremiumRate.getWeightType());
                        return vo;
                    }).collect(Collectors.toList());
            PageInfoVO pageInfoVO = new PageInfoVO();
            pageInfoVO.setPage(dto.getPageInfo().getPage());
            pageInfoVO.setSize(dto.getPageInfo().getSize());
            pageInfoVO.setTotalPage(dto.getPageInfo().getTotalPage());
            pageInfoVO.setTotalSize(Math.toIntExact(dto.getPageInfo().getTotalSize()));
            return new PriceExtendCategorySkuVO(categoryPriceExtend, skuPriceExtendVOS, pageInfoVO);

        }

        static PriceExtendCategorySkuVO categorySkuPremiumRateToVONew(com.sankuai.meituan.shangou.empower.price.client.dto.premiumrate.
                                                                              SkuPremiumRateListDTO dto) {
            PriceExtendVO categoryPriceExtend = premiumRateDTOToVONew(dto.getCategoryPremiumRate());
            List<SkuPriceExtendVO> skuPriceExtendVOS = dto.getSkuPremiumRateList().stream()
                    .map(skuPremiumRate -> {
                        SkuPriceExtendVO vo = new SkuPriceExtendVO();
                        vo.setUniqueId(skuPremiumRate.getSkuId());
                        vo.setName(skuPremiumRate.getSkuName());
                        vo.setPriceExtend(skuPremiumRate.getPremiumRateDTO().getPremiumRate());
                        vo.setThreshold(skuPremiumRate.getPremiumRateDTO().getReference());
                        vo.setDisplayInfo(skuPremiumRate.getPremiumRateDTO().getDisplayInfo());
                        vo.setSpuId(skuPremiumRate.getSpuId());
                        vo.setSpec(skuPremiumRate.getSpec());
                        vo.setImages(skuPremiumRate.getImageList());
                        vo.setMonthSaleAmount(skuPremiumRate.getSales30Day());
                        vo.setWeight(skuPremiumRate.getWeight());
                        vo.setSurveyPrice(parsePrice(skuPremiumRate.getSurveyPrice()));
                        vo.setSurveyPriceOf500g(parsePrice((skuPremiumRate.getWeightType() == 3 || skuPremiumRate.getWeight() == 0)
                                ? null : skuPremiumRate.getSurveyPrice()));
                        vo.setOfflinePrice(parsePrice(skuPremiumRate.getOfflinePrice()));
                        vo.setOfflinePriceOf500g(convertToPriceOf500g(skuPremiumRate, com.sankuai.meituan.shangou.empower.price.client.dto.premiumrate.
                                SkuPremiumRateDTO::getWeightType, com.sankuai.meituan.shangou.empower.price.client.dto.premiumrate.
                                SkuPremiumRateDTO::getWeight, com.sankuai.meituan.shangou.empower.price.client.dto.premiumrate.SkuPremiumRateDTO::getOfflinePrice));
                        vo.setOnlinePrice(parsePrice(skuPremiumRate.getOnlinePrice()));
                        vo.setOnlinePriceOf500g(convertToPriceOf500g(skuPremiumRate, com.sankuai.meituan.shangou.empower.price.client.dto.premiumrate.
                                SkuPremiumRateDTO::getWeightType, com.sankuai.meituan.shangou.empower.price.client.dto.premiumrate.
                                SkuPremiumRateDTO::getWeight, com.sankuai.meituan.shangou.empower.price.client.dto.premiumrate.SkuPremiumRateDTO::getOnlinePrice));
                        vo.setAdjustPriceStrategy(convertToVoNew(skuPremiumRate.getHitStrategyDTO()));
                        vo.setSaleUnit(skuPremiumRate.getSaleUnit());
                        vo.setWeightType(skuPremiumRate.getWeightType());
                        vo.setReviewPrice(parsePrice(skuPremiumRate.getReviewPrice()));
                        vo.setReviewPriceOf500g(convertToPriceOf500g(skuPremiumRate, com.sankuai.meituan.shangou.empower.price.client.dto.premiumrate.
                                SkuPremiumRateDTO::getWeightType, com.sankuai.meituan.shangou.empower.price.client.dto.premiumrate.
                                SkuPremiumRateDTO::getWeight, com.sankuai.meituan.shangou.empower.price.client.dto.premiumrate.SkuPremiumRateDTO::getReviewPrice));
                        return vo;
                    }).collect(Collectors.toList());
            PageInfoVO pageInfoVO = new PageInfoVO();
            pageInfoVO.setPage(dto.getPageInfo().getPage());
            pageInfoVO.setSize(dto.getPageInfo().getSize());
            pageInfoVO.setTotalPage(dto.getPageInfo().getTotalPage());
            pageInfoVO.setTotalSize(Math.toIntExact(dto.getPageInfo().getTotalSize()));
            return new PriceExtendCategorySkuVO(categoryPriceExtend, skuPriceExtendVOS, pageInfoVO);

        }

        static PriceExtendCategorySkuVO categorySkuPriceIndexToVO(SkuPriceIndexListDTO dto) {
            PriceExtendVO categoryPriceExtend = priceIndexDTOToVO(dto.getCategoryPriceIndex());
            List<SkuPriceExtendVO> skuPriceExtendVOS = dto.getSkuPriceIndexList().stream()
                    .map(skuPriceIndex -> {
                        SkuPriceExtendVO vo = new SkuPriceExtendVO();
                        vo.setUniqueId(skuPriceIndex.getSkuId());
                        vo.setName(skuPriceIndex.getSkuName());
                        vo.setPriceExtend(skuPriceIndex.getPriceIndex().getPriceIndex());
                        vo.setThreshold(skuPriceIndex.getPriceIndex().getReference());
                        vo.setDisplayInfo(skuPriceIndex.getPriceIndex().getDisplayIndex());
                        vo.setSpuId(skuPriceIndex.getSpuId());
                        vo.setSpec(skuPriceIndex.getSpec());
                        vo.setImages(skuPriceIndex.getImageList());
                        vo.setMonthSaleAmount(skuPriceIndex.getSales30Day());
                        vo.setWeight(skuPriceIndex.getWeight());
                        vo.setCityBasePrice(parsePrice(skuPriceIndex.getCityBasePrice()));
                        vo.setCityBasePriceOf500g(parsePrice((skuPriceIndex.getWeightType() == 3 || skuPriceIndex.getWeight() == 0)
                                ? null : skuPriceIndex.getCityBasePrice()));
                        vo.setOfflinePrice(parsePrice(skuPriceIndex.getOfflinePrice()));
                        vo.setOfflinePriceOf500g(convertToPriceOf500g(skuPriceIndex, SkuPriceIndexDTO::getWeightType, SkuPriceIndexDTO::getWeight, SkuPriceIndexDTO::getOfflinePrice));
                        vo.setOnlinePrice(parsePrice(skuPriceIndex.getChannelPrice()));
                        vo.setOnlinePriceOf500g(convertToPriceOf500g(skuPriceIndex, SkuPriceIndexDTO::getWeightType, SkuPriceIndexDTO::getWeight, SkuPriceIndexDTO::getChannelPrice));
                        vo.setAdjustPriceStrategy(convertToVo(skuPriceIndex.getHitStrategyDTO()));
                        vo.setSaleUnit(skuPriceIndex.getSaleUnit());
                        vo.setWeightType(skuPriceIndex.getWeightType());
                        return vo;
                    }).collect(Collectors.toList());
            PageInfoVO pageInfoVO = new PageInfoVO();
            pageInfoVO.setPage(dto.getPageInfo().getPage());
            pageInfoVO.setSize(dto.getPageInfo().getSize());
            pageInfoVO.setTotalPage(dto.getPageInfo().getTotalPage());
            pageInfoVO.setTotalSize(Math.toIntExact(dto.getPageInfo().getTotalSize()));
            return new PriceExtendCategorySkuVO(categoryPriceExtend, skuPriceExtendVOS, pageInfoVO);

        }

        static PriceExtendCategorySkuVO categorySkuPriceIndexToVONew(com.sankuai.meituan.shangou.empower.price.client.dto.index.
                                                                             SkuPriceIndexListDTO dto) {
            PriceExtendVO categoryPriceExtend = priceIndexDTOToVONew(dto.getCategoryPriceIndex());
            List<SkuPriceExtendVO> skuPriceExtendVOS = dto.getSkuPriceIndexList().stream()
                    .map(skuPriceIndex -> {
                        SkuPriceExtendVO vo = new SkuPriceExtendVO();
                        vo.setUniqueId(skuPriceIndex.getSkuId());
                        vo.setName(skuPriceIndex.getSkuName());
                        vo.setPriceExtend(skuPriceIndex.getPriceIndex().getPriceIndex());
                        vo.setThreshold(skuPriceIndex.getPriceIndex().getReference());
                        vo.setDisplayInfo(skuPriceIndex.getPriceIndex().getDisplayIndex());
                        vo.setSpuId(skuPriceIndex.getSpuId());
                        vo.setSpec(skuPriceIndex.getSpec());
                        vo.setImages(skuPriceIndex.getImageList());
                        vo.setMonthSaleAmount(skuPriceIndex.getSales30Day());
                        vo.setWeight(skuPriceIndex.getWeight());
                        vo.setCityBasePrice(parsePrice(skuPriceIndex.getCityBasePrice()));
                        vo.setCityBasePriceOf500g(parsePrice((skuPriceIndex.getWeightType() == 3 || skuPriceIndex.getWeight() == 0)
                                ? null : skuPriceIndex.getCityBasePrice()));
                        vo.setOfflinePrice(parsePrice(skuPriceIndex.getOfflinePrice()));
                        vo.setOfflinePriceOf500g(convertToPriceOf500g(skuPriceIndex, com.sankuai.meituan.shangou.empower.price.client.dto.index.
                                SkuPriceIndexDTO::getWeightType, com.sankuai.meituan.shangou.empower.price.client.dto.index.
                                SkuPriceIndexDTO::getWeight, com.sankuai.meituan.shangou.empower.price.client.dto.index.SkuPriceIndexDTO::getOfflinePrice));
                        vo.setOnlinePrice(parsePrice(skuPriceIndex.getChannelPrice()));
                        vo.setOnlinePriceOf500g(convertToPriceOf500g(skuPriceIndex, com.sankuai.meituan.shangou.empower.price.client.dto.index.
                                SkuPriceIndexDTO::getWeightType, com.sankuai.meituan.shangou.empower.price.client.dto.index.
                                SkuPriceIndexDTO::getWeight, com.sankuai.meituan.shangou.empower.price.client.dto.index.SkuPriceIndexDTO::getChannelPrice));
                        vo.setAdjustPriceStrategy(convertToVoNew(skuPriceIndex.getHitStrategyDTO()));
                        vo.setSaleUnit(skuPriceIndex.getSaleUnit());
                        vo.setWeightType(skuPriceIndex.getWeightType());
                        vo.setReviewPrice(parsePrice(skuPriceIndex.getReviewPrice()));
                        vo.setReviewPriceOf500g(convertToPriceOf500g(skuPriceIndex, com.sankuai.meituan.shangou.empower.price.client.dto.index.
                                SkuPriceIndexDTO::getWeightType, com.sankuai.meituan.shangou.empower.price.client.dto.index.
                                SkuPriceIndexDTO::getWeight, com.sankuai.meituan.shangou.empower.price.client.dto.index.SkuPriceIndexDTO::getReviewPrice));
                        return vo;
                    }).collect(Collectors.toList());
            PageInfoVO pageInfoVO = new PageInfoVO();
            pageInfoVO.setPage(dto.getPageInfo().getPage());
            pageInfoVO.setSize(dto.getPageInfo().getSize());
            pageInfoVO.setTotalPage(dto.getPageInfo().getTotalPage());
            pageInfoVO.setTotalSize(Math.toIntExact(dto.getPageInfo().getTotalSize()));
            return new PriceExtendCategorySkuVO(categoryPriceExtend, skuPriceExtendVOS, pageInfoVO);

        }

        static Double parsePrice(Long price) {
            return Optional
                    .ofNullable(price)
                    .map(MoneyUtils::centToYuanByDown)
                    .orElse(null);
        }


        static AdjustPriceStrategyVO convertToVo(SkuHitSyncStrategyDTO dto) {
            if (Objects.isNull(dto)){
                return null;
            }
            if (dto.getSyncStrategyType() == RAISE_STORE_PRICE
                    || dto.getSyncStrategyType() == SINGLE_SKU_FIX_STRATEGY_PRICE) {
                AdjustPriceStrategyVO adjustPriceVo = new AdjustPriceStrategyVO();
                adjustPriceVo.setStrategyType(dto.getSyncStrategyType().getValue());
                adjustPriceVo.setRaisePercent(Optional.ofNullable(dto.getPriceStrategyDTO().getAdjustPercent())
                        .map(x -> BigDecimal.valueOf(x).divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN).doubleValue())
                        .orElse(null));
                adjustPriceVo.setRaisePrice(parsePrice(dto.getPriceStrategyDTO().getAdjustMoney()));
                return adjustPriceVo;
            } else if (dto.getSyncStrategyType() == FIX_PRICE) {
                AdjustPriceStrategyVO adjustPriceVo = new AdjustPriceStrategyVO();
                adjustPriceVo.setStrategyType(dto.getSyncStrategyType().getValue());
                adjustPriceVo.setFixPrice(parsePrice(dto.getFixedPrice()));
                return adjustPriceVo;
            }
            return null;

        }

        static AdjustPriceStrategyVO convertToVoNew(com.sankuai.meituan.shangou.empower.price.client.dto.strategy.
                                                            SkuHitSyncStrategyDTO dto) {
            if (Objects.isNull(dto)){
                return null;
            }
            if (dto.getSyncStrategyType() == com.sankuai.meituan.shangou.empower.price.client.enums.strategy.SyncStrategyTypeEnum.
                    RAISE_STORE_PRICE
                    || dto.getSyncStrategyType() == com.sankuai.meituan.shangou.empower.price.client.enums.strategy.SyncStrategyTypeEnum.
                    SINGLE_SKU_FIX_STRATEGY_PRICE) {
                AdjustPriceStrategyVO adjustPriceVo = new AdjustPriceStrategyVO();
                adjustPriceVo.setStrategyType(dto.getSyncStrategyType().getValue());
                adjustPriceVo.setRaisePercent(Optional.ofNullable(dto.getPriceStrategyDTO().getAdjustPercent())
                        .map(x -> BigDecimal.valueOf(x).divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN).doubleValue())
                        .orElse(null));
                adjustPriceVo.setRaisePrice(parsePrice(dto.getPriceStrategyDTO().getAdjustMoney()));
                return adjustPriceVo;
            } else if (dto.getSyncStrategyType() == com.sankuai.meituan.shangou.empower.price.client.enums.strategy.SyncStrategyTypeEnum.
                    FIX_PRICE) {
                AdjustPriceStrategyVO adjustPriceVo = new AdjustPriceStrategyVO();
                adjustPriceVo.setStrategyType(dto.getSyncStrategyType().getValue());
                adjustPriceVo.setFixPrice(parsePrice(dto.getFixedPrice()));
                return adjustPriceVo;
            }
            return null;

        }


        static <T> Double convertToPriceOf500g(T t, Function<T, Integer> weightTypeFunc,
                                               Function<T, Integer> weightFunc,
                                               Function<T, Long> priceFunc) {
            if (weightTypeFunc.apply(t) == 3 || weightFunc.apply(t) == 0) {
                return null;
            }
            Long origin = priceFunc.apply(t);
            if (Objects.isNull(origin)) {
                return null;
            }
            //转元
            Double price = MoneyUtils.centToYuanByDown(origin);
            //转市斤
            return BigDecimal.valueOf(price)
                    .multiply(BigDecimal.valueOf(500))
                    .divide(BigDecimal.valueOf(weightFunc.apply(t)), 2, RoundingMode.DOWN)
                    .doubleValue();
        }

        static PriceExtendVO premiumRateDTOToVO(PremiumRateDTO dto) {
            if (Objects.isNull(dto)) {
                return null;
            }
            PriceExtendVO vo = new PriceExtendVO();
            vo.setUniqueId(dto.getUniqueId());
            vo.setName(dto.getName());
            vo.setPriceExtend(dto.getPremiumRate());
            vo.setThreshold(dto.getReference());
            vo.setDisplayInfo(dto.getDisplayInfo());
            return vo;
        }

        static PriceExtendVO premiumRateDTOToVONew(com.sankuai.meituan.shangou.empower.price.client.dto.premiumrate.
                                                           PremiumRateDTO dto) {
            if (Objects.isNull(dto)) {
                return null;
            }
            PriceExtendVO vo = new PriceExtendVO();
            vo.setUniqueId(dto.getUniqueId());
            vo.setName(dto.getName());
            vo.setPriceExtend(dto.getPremiumRate());
            vo.setThreshold(dto.getReference());
            vo.setDisplayInfo(dto.getDisplayInfo());
            return vo;
        }

        static PriceExtendVO priceIndexDTOToVO(PriceIndexDTO dto) {
            if (Objects.isNull(dto)) {
                return null;
            }
            PriceExtendVO vo = new PriceExtendVO();
            vo.setUniqueId(dto.getUniqueId());
            vo.setName(dto.getName());
            vo.setPriceExtend(dto.getPriceIndex());
            vo.setThreshold(dto.getReference());
            vo.setDisplayInfo(dto.getDisplayIndex());
            return vo;
        }

        static PriceExtendVO priceIndexDTOToVONew(com.sankuai.meituan.shangou.empower.price.client.dto.index.
                                                          PriceIndexDTO dto) {
            if (Objects.isNull(dto)) {
                return null;
            }
            PriceExtendVO vo = new PriceExtendVO();
            vo.setUniqueId(dto.getUniqueId());
            vo.setName(dto.getName());
            vo.setPriceExtend(dto.getPriceIndex());
            vo.setThreshold(dto.getReference());
            vo.setDisplayInfo(dto.getDisplayIndex());
            return vo;
        }
    }

}
