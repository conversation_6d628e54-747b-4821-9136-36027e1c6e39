package com.sankuai.meituan.reco.shopmgmt.pieapi.domain;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 2024/8/14
 */
@Data
@ApiModel("货主信息")
@AllArgsConstructor
@NoArgsConstructor
public class GoodsOwnerVO {
    @FieldDoc(
            description = "货主id", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "货主id")
    private String goodsOwnerId;

    @FieldDoc(
            description = "货主名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "货主名称")
    private String goodsOwnerName;
}
