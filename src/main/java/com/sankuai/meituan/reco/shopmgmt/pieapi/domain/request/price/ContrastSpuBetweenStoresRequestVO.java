package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;


/**
 * @Author: wangyihao04
 * @Date: 2020-12-01 19:35
 * @Mail: <EMAIL>
 */
@ApiModel(
        "门店间商品对比查询请求"
)
@TypeDoc(
        description = "门店间商品对比查询请求"
)
@Data
public class ContrastSpuBetweenStoresRequestVO {
    @FieldDoc(
            description = "门店id"
    )
    @ApiModelProperty("门店id")
    @NotNull
    public Long storeId;

    @FieldDoc(
            description = "竞对门店"
    )
    @ApiModelProperty("竞对门店")
    @NotNull
    public Long contrastStoreId;

    @FieldDoc(
            description = "竞对门店类型"
    )
    @ApiModelProperty("竞对门店类型")
    @NotNull
    public Integer contrastStoreType;

    @FieldDoc(
            description = "平台一级类目id"
    )
    @ApiModelProperty("平台一级类目id")
    public String firstCategoryId;

    @FieldDoc(
            description = "平台二级类目id"
    )
    @ApiModelProperty("平台二级类目id")
    public String secondCategoryId;

    @FieldDoc(
            description = "平台三级类目id"
    )
    @ApiModelProperty("平台三级类目id")
    public String thirdCategoryId;

    @FieldDoc(
            description = "上次查询最后一个平台三级类目id"
    )
    @ApiModelProperty("上次查询最后一个平台三级类目id")
    public String lastEntityId;

    @FieldDoc(
            description = "排序方式"
    )
    @ApiModelProperty("排序方式")
    @NotNull
    public String sortType;
}
