package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TypeDoc(
        description = "渠道SKU信息"
)
@Data
@ApiModel("渠道SKU信息")
public class ChannelSkuVO {

    @FieldDoc(
            description = "SKU编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "SKU编码", required = true)
    private String skuId;

    @FieldDoc(
            description = "渠道价格", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道价格", required = true)
    private Double price;

    @FieldDoc(
            description = "渠道库存", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道库存", required = true)
    private Double stock;

}
