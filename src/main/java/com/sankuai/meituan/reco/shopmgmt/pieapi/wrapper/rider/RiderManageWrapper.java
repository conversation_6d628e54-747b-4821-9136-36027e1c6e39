package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.rider;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Nullable;
import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.RetryTemplateUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.SacWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.labor.LaborManagementServiceWrapper;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.AccountTypeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.*;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.RiderQueryThriftService;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDeliveryOrder;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderMonitorInfo;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.BatchQueryDeliveryOrderResponse;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.infra.osw.api.org.TEmployeeService;
import com.sankuai.shangou.infra.osw.api.org.dto.request.QueryEmpByFuzzyNameRequest;
import com.sankuai.shangou.infra.osw.api.org.dto.response.EmpAccountWithRoleDTO;
import com.sankuai.shangou.logistics.delivery.gray.enums.GrayKeyEnum;
import com.sankuai.shangou.logistics.delivery.gray.utils.GrayConfigUtils;
import com.sankuai.shangou.logistics.warehouse.TradeShippingOrderService;
import com.sankuai.shangou.logistics.warehouse.enums.ResponseCodeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.stereotype.Service;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.tenant.thrift.EmployeeThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.Status;
import com.meituan.shangou.saas.tenant.thrift.common.StatusCodeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.employee.EmployeeDepDto;
import com.meituan.shangou.saas.tenant.thrift.dto.employee.EmployeeDto;
import com.meituan.shangou.saas.tenant.thrift.dto.employee.response.EmployListResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.manage.SelfRiderInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.AuthThriftWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.TenantWrapper;
import com.sankuai.meituan.shangou.empower.auth.sdk.context.SessionContext;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.service.AuthThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.service.MApiPermissionThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.request.PageQueryAccountListRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.RiderOperateThriftService;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request.RiderChangeRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.response.RiderChangeResponse;
import com.sankuai.meituan.shangou.saas.common.mobile.MobileUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 骑手人员管理服务封装.
 *
 * <AUTHOR>
 * @since 2021/7/30 14:35
 */
@Service
@Slf4j
@Rhino
public class RiderManageWrapper {

    // 固定最多展示 20 条待转单骑手信息
    private static final Integer RIDER_SEARCH_MAX_NUM = 20;

    @Resource
    private AuthThriftWrapper authThriftWrapper;
    @Resource
    private TenantWrapper tenantWrapper;
    @Resource
    private LaborManagementServiceWrapper laborManagementServiceWrapper;
    /**
     * 租户员工服务
     */
    @Resource
    private EmployeeThriftService employThriftService;
    @Resource
    private AuthThriftService.Iface authThriftService;
    @Resource
    private MApiPermissionThriftService.Iface mApiPermissionThriftService;
    @Resource
    private RiderOperateThriftService riderOperateThriftService;
    @Resource
    private TEmployeeService tEmployeeService;

    @Resource
    private RiderQueryThriftService riderQueryThriftService;

    @Resource
    private TradeShippingOrderService tradeShippingOrderService;


    public List<SelfRiderInfo> queryTransferableRiderBySearchKey(long storeId, String searchKey, String currentRiderPhone) {
        if (!MccConfigUtil.isNewTransferRideStore(storeId)) {
            return oldQueryTransferableRiderBySearchKey(storeId, searchKey, currentRiderPhone);
        }
        boolean isPhone = MobileUtil.checkMobile(searchKey);
        long tenantId = SessionContext.getCurrentSession().getTenantId();
        List<SelfRiderInfo> riders;
        if (isPhone) {
            // 按电话搜索骑手（员工账号）
            riders = buildSelfRiderInfosByPhone(storeId, searchKey, currentRiderPhone, tenantId);
        } else {
            // 按姓名搜索骑手（员工账号）
            riders = buildSelfRiderInfosByName(storeId, searchKey, currentRiderPhone, tenantId);

        }

        //append培训结果
        appendTrainingResult(tenantId, storeId, riders);

        return riders;
    }

    private List<SelfRiderInfo> buildSelfRiderInfosByName(long storeId, String searchKey, String currentRiderPhone, long tenantId) {
        List<EmpAccountWithRoleDTO> empAccountWithRoleDTOS = queryEmpByFuzzyNameWithStorePermission(storeId, searchKey, tenantId);
        // 过滤掉当前骑手
        return IListUtils.mapTo(
                empAccountWithRoleDTOS.stream().filter(v -> !StringUtils.equals(v.getPhoneNumber(), currentRiderPhone)).collect(Collectors.toList()),
                empAccountWithRoleDTO -> {
                    SelfRiderInfo selfRiderInfo = new SelfRiderInfo();
                    selfRiderInfo.setAccountId(empAccountWithRoleDTO.getAccountId());
                    selfRiderInfo.setName(empAccountWithRoleDTO.getEmpName());
                    selfRiderInfo.setPhone(empAccountWithRoleDTO.getPhoneNumber());
                    selfRiderInfo.setTempRiderFlag(isTempRider(empAccountWithRoleDTO));
                    selfRiderInfo.setIsCompleteTrain(true);
                    return selfRiderInfo;
                });
    }

    private List<EmpAccountWithRoleDTO> queryEmpByFuzzyNameWithStorePermission(long storeId, String searchKey, long tenantId) {
        try {
            QueryEmpByFuzzyNameRequest request = new QueryEmpByFuzzyNameRequest();
            request.setTenantId(tenantId);
            request.setEmpName(searchKey);
            request.setStoreId(storeId);
            TResult<List<EmpAccountWithRoleDTO>> result = tEmployeeService.queryEmpByFuzzyNameWithStorePermission(request);
            if (!result.isSuccess()) {
                throw new ThirdPartyException("模糊查询员工信息失败");
            }
            return Optional.ofNullable(result.getData()).orElse(Lists.newArrayList());
        } catch (Exception e) {
            log.error("tEmployeeService.queryEmpByFuzzyNameWithStorePermission error", e);
            throw new ThirdPartyException("模糊查询员工信息失败");
        }
    }

    private List<SelfRiderInfo> buildSelfRiderInfosByPhone(long storeId, String searchKey, String currentRiderPhone, long tenantId) {
        List<SelfRiderInfo> riders = Lists.newArrayList();
        List<AccountInfoVo> accountInfoVos = queryAccountByPhone(tenantId, searchKey);
        if (CollectionUtils.isEmpty(accountInfoVos)) {
            return Collections.emptyList();
        }
        // 根据门店权限过滤
        List<AccountInfoVo> accountsWithPermission = authThriftWrapper.batchQueryAccountListWithPoiPermission(tenantId, storeId);
        List<Long> accountIdsWithPermission = accountsWithPermission.stream().map(AccountInfoVo::getAccountId).collect(Collectors.toList());
        List<AccountInfoVo> finalAccounts = Lists.newArrayListWithExpectedSize(RIDER_SEARCH_MAX_NUM);
        for (AccountInfoVo account : accountInfoVos) {
            if (!accountIdsWithPermission.contains(account.getAccountId())) {
                continue;
            }
            if (StringUtils.equals(account.getMobile(), currentRiderPhone)) {
                continue;
            }
            finalAccounts.add(account);
            if (finalAccounts.size() >= RIDER_SEARCH_MAX_NUM) {
                break;
            }
        }
        // 查询员工详细信息，并填充给 SelfRiderInfo
        Map<Long, AccountInfoVo> empIdAccountMap = finalAccounts.stream().collect(Collectors.toMap(AccountInfoVo::getStaffId,
                Function.identity(), (ov, nv) -> nv));
        Map<Long, EmployeeDepDto> empMap = tenantWrapper.getEmployeeByIds(tenantId, Lists.newArrayList(empIdAccountMap.keySet()));

        for (Map.Entry<Long, AccountInfoVo> empId2Account : empIdAccountMap.entrySet()) {
            EmployeeDepDto employee = empMap.get(empId2Account.getKey());

            //因为培训不通过会阻止转单 所以这里默认为培训通过 防止影响配送效率
            riders.add(new SelfRiderInfo(empId2Account.getValue().getAccountId(), employee.getEmployeeName(),
                    employee.getEmployeePhone(), isTempRider(empId2Account.getValue()), true));
        }
        return riders;
    }

    /**
     * 查询可转单骑手.
     *
     * @param storeId
     * @param searchKey
     * @return
     */
    @Deprecated
    public List<SelfRiderInfo> oldQueryTransferableRiderBySearchKey(long storeId, String searchKey, String currentRiderPhone) {
        // 检查搜索 key 是否为电话号码
        boolean isPhone = MobileUtil.checkMobile(searchKey);
        long tenantId = SessionContext.getCurrentSession().getTenantId();
        List<AccountInfoVo> accountInfoVos = null;
        if (isPhone) {
            // 按电话搜索骑手（员工账号）
            accountInfoVos = queryAccountByPhone(tenantId, searchKey);
        } else {
            // 按姓名搜索骑手（员工账号）
            accountInfoVos = queryAccountByFuzzyEmpName(tenantId, searchKey);
        }
        if (CollectionUtils.isEmpty(accountInfoVos)) {
            return Collections.emptyList();
        }
        // 根据门店权限过滤
        List<AccountInfoVo> accountsWithPermission = authThriftWrapper.batchQueryAccountListWithPoiPermission(tenantId, storeId);
        List<Long> accountIdsWithPermission = accountsWithPermission.stream().map(AccountInfoVo::getAccountId).collect(Collectors.toList());
        List<AccountInfoVo> finalAccounts = Lists.newArrayListWithExpectedSize(RIDER_SEARCH_MAX_NUM);
        for (AccountInfoVo account : accountInfoVos) {
            if (!accountIdsWithPermission.contains(account.getAccountId())) {
                continue;
            }
            if (StringUtils.equals(account.getMobile(), currentRiderPhone)) {
                continue;
            }
            finalAccounts.add(account);
            if (finalAccounts.size() >= RIDER_SEARCH_MAX_NUM) {
                break;
            }
        }
        // 查询员工详细信息，并填充给 SelfRiderInfo
        Map<Long, AccountInfoVo> empIdAccountMap = finalAccounts.stream().collect(Collectors.toMap(AccountInfoVo::getStaffId,
                Function.identity(), (ov, nv) -> nv));
        Map<Long, EmployeeDepDto> empMap = tenantWrapper.getEmployeeByIds(tenantId, Lists.newArrayList(empIdAccountMap.keySet()));
        List<SelfRiderInfo> riders = Lists.newArrayListWithExpectedSize(finalAccounts.size());
        for (Map.Entry<Long, AccountInfoVo> empId2Account : empIdAccountMap.entrySet()) {
            EmployeeDepDto employee = empMap.get(empId2Account.getKey());

            //因为培训不通过会阻止转单 所以这里默认为培训通过 防止影响配送效率
            riders.add(new SelfRiderInfo(empId2Account.getValue().getAccountId(), employee.getEmployeeName(),
                    employee.getEmployeePhone(), isTempRider(empId2Account.getValue()), true));
        }

        //append培训结果
        appendTrainingResult(tenantId, storeId, riders);

        return riders;
    }


    private void appendTrainingResult(Long tenantId, Long storeId, List<SelfRiderInfo> riderInfos) {
        try {
            if (!MccConfigUtil.isDrunkHorseTenant(tenantId) || !MccConfigUtil.isTrainingGrayStore(storeId)) {
                return;
            }

            if (CollectionUtils.isEmpty(riderInfos)) {
                return;
            }

            Map<Long, Boolean> trainingResultMap = laborManagementServiceWrapper.batchQueryTrainingResult(tenantId, riderInfos.stream().map(SelfRiderInfo::getAccountId).collect(Collectors.toList()));

            //因为培训不通过会阻止转单 所以这里默认为培训通过 防止影响配送效率
            for (SelfRiderInfo selfRiderInfo : riderInfos) {
                selfRiderInfo.setIsCompleteTrain(trainingResultMap.getOrDefault(selfRiderInfo.getAccountId(), true));
            }
        } catch (Exception e) {
            log.error("查询培训结果失败", e);
            Cat.logEvent("QUERY_TRAINING_RESULT", "ERROR");
        }
    }

    /**
     * 根据租户 ID 和电话号码哈查询账号信息.
     *
     * @param tenantId 租户 ID
     * @param phoneNum 电话号码
     * @return List<AccountInfoVo> NotEmpty
     */
    private List<AccountInfoVo> queryAccountByPhone(long tenantId, String phoneNum) {
        PageQueryAccountListRequest tRequest = new PageQueryAccountListRequest();
        tRequest.setTenantId(tenantId);
        tRequest.setMobile(phoneNum);
        tRequest.setValid(1);
        try {
            log.info("RiderManageWrapper call MApiPermissionThriftService#queryAccountInfoList. request:{}", tRequest);
            AccountInfoListResponse response = mApiPermissionThriftService.queryAccountInfoList(tRequest);
            log.info("RiderManageWrapper call MApiPermissionThriftService#queryAccountInfoList. response:{}", response);
            if (!Objects.equals(
                    Optional.ofNullable(response).map(AccountInfoListResponse::getCode).orElse(null),
                    ResultCodeEnum.SUCCESS.getValue())) {
                log.warn("RiderManageWrapper call MApiPermissionThriftService#queryAccountInfoList fail. request:{}, response:{}", tRequest,
                        response);
                String errMsg = Optional.ofNullable(response).map(AccountInfoListResponse::getMsg)
                        .orElse(ResultCode.INTERNAL_SERVER_ERROR.defaultMessage);
                throw new CommonRuntimeException(errMsg, ResultCode.FAIL);
            }
            List<AccountInfoVo> accountInfoList = response.getAccountInfoList();
            if (CollectionUtils.isEmpty(accountInfoList)) {
                return Collections.emptyList();
            }
            return accountInfoList;
        } catch (Exception e) {
            log.error("RiderManageWrapper call MApiPermissionThriftService#queryAccountInfoList error. request:{}", tRequest, e);
            throw new CommonRuntimeException(ResultCode.INTERNAL_SERVER_ERROR.defaultMessage, ResultCode.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 根据租户 ID  和员工名称进行账号的模糊搜索
     *
     * @param tenantId     租户 ID
     * @param fuzzyEmpName 员工名称的子字符串
     * @return List<AccountInfoVo> NotEmpty
     */
    private List<AccountInfoVo> queryAccountByFuzzyEmpName(long tenantId, String fuzzyEmpName) {
        // 根据姓名查询员工信息
        List<EmployeeDto> empList = null;
        try {
            log.info("RiderManageWrapper call EmployeeThriftService#fuzzyQueryEmployeesByName. tenantId:{}, name:{}", tenantId,
                    fuzzyEmpName);
            EmployListResponse employListResponse = employThriftService.fuzzyQueryEmployeesByName(tenantId, fuzzyEmpName);
            log.info("RiderManageWrapper call EmployeeThriftService#fuzzyQueryEmployeesByName. response:{}", employListResponse);
            if (!Objects.equals(
                    Optional.ofNullable(employListResponse).map(EmployListResponse::getStatus).map(Status::getCode).orElse(null),
                    StatusCodeEnum.SUCCESS.getCode())) {
                log.warn("RiderManageWrapper call EmployeeThriftService#fuzzyQueryEmployeesByName fail. tenantId:{}, name:{}, " +
                        "response:{}", tenantId, fuzzyEmpName, employListResponse);
                String errMsg = Optional.ofNullable(employListResponse).map(EmployListResponse::getStatus).map(Status::getMessage)
                        .orElse(ResultCode.INTERNAL_SERVER_ERROR.defaultMessage);
                throw new CommonRuntimeException(errMsg, ResultCode.FAIL);
            }
            empList = employListResponse.getEmployeeInfos();
            if (CollectionUtils.isEmpty(empList)) {
                return Collections.emptyList();
            }
        } catch (Exception e) {
            log.error("RiderManageWrapper call EmployeeThriftService#fuzzyQueryEmployeesByName error. tenantId:{}, name:{}",
                    tenantId, fuzzyEmpName, e);
            throw new CommonRuntimeException(ResultCode.INTERNAL_SERVER_ERROR.defaultMessage, ResultCode.INTERNAL_SERVER_ERROR);
        }
        List<Long> staffIds = empList.stream().map(EmployeeDto::getEmployeeId).collect(Collectors.toList());
        // 根据员工 ID 信息查询账号信息
        QueryAccountDetailByTenantIdAndStaffIdsRequest staffIdsRequest = new QueryAccountDetailByTenantIdAndStaffIdsRequest(tenantId,
                staffIds);
        try {
            log.info("RiderManageWrapper call AuthThriftService#queryAccountDetailByTenantIdAndStaffIds. request{}", staffIdsRequest);
            QueryAccountInfoListResponse response = authThriftService.queryAccountDetailByTenantIdAndStaffIds(staffIdsRequest);
            log.info("RiderManageWrapper call AuthThriftService#queryAccountDetailByTenantIdAndStaffIds. response:{}", response);
            if (!Objects.equals(
                    Optional.ofNullable(response).map(QueryAccountInfoListResponse::getResult).map(Result::getCode).orElse(null),
                    ResultCodeEnum.SUCCESS.getValue())) {
                log.warn("RiderManageWrapper call AuthThriftService#queryAccountDetailByTenantIdAndStaffIds fail. request:{}, response:{}"
                        , staffIdsRequest, response);
                String errMsg = Optional.ofNullable(response).map(QueryAccountInfoListResponse::getResult).map(Result::getMsg)
                        .orElse(ResultCode.INTERNAL_SERVER_ERROR.defaultMessage);
                throw new CommonRuntimeException(errMsg, ResultCode.FAIL);
            }
            List<AccountInfoVo> accountInfoList = response.getAccountInfoList();
            if (CollectionUtils.isEmpty(accountInfoList)) {
                return Collections.emptyList();
            }
            return accountInfoList;
        } catch (Exception e) {
            log.error("RiderManageWrapper call AuthThriftService#queryAccountDetailByTenantIdAndStaffIds error. request:{}",
                    staffIdsRequest, e);
            throw new CommonRuntimeException(ResultCode.INTERNAL_SERVER_ERROR.defaultMessage, ResultCode.INTERNAL_SERVER_ERROR);
        }
    }

    @Degrade(rhinoKey = "RiderManageWrapper.riderChange",
            fallBackMethod = "riderChangerFallback",
            isDegradeOnException = true,
            timeoutInMilliseconds = 2000)
    public CommonResponse<Void> riderChange(com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.manage.RiderChangeRequest request,
                                            User currentUser, TenantWrapper.EmployeeBaseInfo employeeBaseInfo) {
        //转配送单
        RiderChangeRequest tRequest = new RiderChangeRequest(currentUser.getTenantId(), request.getStoreId(), request.getOrderId(),
                currentUser.getAccountId(), request.getRiderAccountId(), employeeBaseInfo.getEmployeeName(), employeeBaseInfo.getEmployeePhone(),null);

        RiderChangeResponse riderChangeResponse = riderOperateThriftService.riderChange(tRequest);

        if (!Objects.equals(riderChangeResponse.getStatus().getCode(), 0)) {
            return new CommonResponse<>(riderChangeResponse.getStatus().getCode(), riderChangeResponse.getStatus().getMsg(), null);
        }

        //转拣货单
        tryToTransPickTask(request, employeeBaseInfo);

        return CommonResponse.success(null);
    }


    private void tryToTransPickTask(com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.manage.RiderChangeRequest request,
                                    TenantWrapper.EmployeeBaseInfo employeeBaseInfo) {
        try {
            long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
            long storeId = ApiMethodParamThreadLocal.getIdentityInfo().getStoreId();
            if (MccConfigUtil.isDrunkHorseTenant(tenantId) && MccConfigUtil.acceptPickOrderSwitch(storeId)) {
                BatchQueryDeliveryOrderResponse response = RetryTemplateUtil.simpleWithFixedRetry(3, 100).execute(new RetryCallback<BatchQueryDeliveryOrderResponse, Exception>() {
                    @Override
                    public BatchQueryDeliveryOrderResponse doWithRetry(RetryContext context) throws Exception {
                        BatchQueryDeliveryOrderResponse response = riderQueryThriftService.queryDeliveryOrderByOrderIdListV2(Collections.singletonList(request.getOrderId()));
                        log.info("end invoke riderQueryThriftService.queryDeliveryOrderByOrderIdListV2, orderId: {}, response: {}", request.getOrderId(), JSON.toJSONString(response));
                        return response;
                    }
                });

                if (CollectionUtils.isEmpty(response.getTRiderDeliveryOrders())) {
                    log.warn("无生效中的运单");
                    return;
                }
                TRiderDeliveryOrder tRiderDeliveryOrder = response.getTRiderDeliveryOrders().get(0);

                //过滤拣配分离订单
                if (Objects.equals(tRiderDeliveryOrder.getPickDeliverySplitTag(), true)) {
                    return;
                }

                TResult<Void> tResult = RetryTemplateUtil.simpleWithFixedRetry(3, 100).execute(new RetryCallback<TResult<Void>, Exception>() {
                    @Override
                    public TResult<Void> doWithRetry(RetryContext context) throws Exception {
                        TResult<Void> tResult = tradeShippingOrderService.changeOperator(tRiderDeliveryOrder.getStoreId(),
                                tRiderDeliveryOrder.getChannelOrderId(),
                                tRiderDeliveryOrder.getOrderBizTypeCode(),
                                request.getRiderAccountId(),
                                employeeBaseInfo.getEmployeeName());
                        log.info("end invoke tradeShippingOrderService.changeOperator, tradeOrderNo: {}, response: {}", tRiderDeliveryOrder.getChannelOrderId(), tResult);
                        return tResult;
                    }
                });

                if (!tResult.isSuccess()) {
                    if (Objects.equals(tResult.getCode(), ResponseCodeEnum.SHIP_TASK_ALREADY_FINISH.getCode())) {
                        log.warn("拣货任务已结束,不报错");
                        return;
                    }

                    throw new BizException("转拣货任务失败," + tResult.getMsg());
                }
            }
        } catch (Exception e) {
            log.error("转拣货任务失败", e);
            Cat.logEvent("PICK_DELIVERY_SPLIT", "TRANS_PICK_TASK_FAIL");
        }
    }

    public CommonResponse<Void> riderChangerFallback(com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.manage.RiderChangeRequest request,
                                                     User currentUser, TenantWrapper.EmployeeBaseInfo employeeBaseInfo, Throwable t) {
        log.warn("request: {}, currentUser: {} employeeBaseInfo: {}", request, currentUser, employeeBaseInfo, t);
        return CommonResponse.fail(ResultCode.INTERNAL_SERVER_ERROR);
    }

    /**
     * 判断账号是否为临时骑手账号
     * 临时账号的判断条件：账号类型为外部账号 && 角色包含临时骑手角色
     * @param accountInfoVo 骑手信息
     * @return 是否为临时骑手账号
     */
    private static Boolean isTempRider(AccountInfoVo accountInfoVo) {
        Objects.requireNonNull(accountInfoVo);

        if (CollectionUtils.isEmpty(accountInfoVo.getRoleList())) {
            return false;
        }

        return accountInfoVo.getAccountType() == AccountTypeEnum.OUTSOURCING.getValue()
                && accountInfoVo.getRoleList().stream()
                .anyMatch(roleInfoVo -> MccConfigUtil.tempRiderRoleIds().contains(String.valueOf(roleInfoVo.getId())));
    }

    private static Boolean isTempRider(EmpAccountWithRoleDTO empAccountWithRoleDTO) {

        if (CollectionUtils.isEmpty(empAccountWithRoleDTO.getRoleList())) {
            return false;
        }

        return empAccountWithRoleDTO.getAccountType() == AccountTypeEnum.OUTSOURCING.getValue()
                && empAccountWithRoleDTO.getRoleList().stream()
                .anyMatch(roleInfoVo -> MccConfigUtil.tempRiderRoleIds().contains(String.valueOf(roleInfoVo.getRoleId())));
    }
}
