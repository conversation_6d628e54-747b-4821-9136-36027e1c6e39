package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TypeDoc(
        description = "已拣货任务数据"
)
@Data
@ApiModel("已拣货任务数据")
public class WarehousePickCompleteTaskModuleVO {

    @FieldDoc(
            description = "任务号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "任务号")
    private Integer taskNo;

    @FieldDoc(
            description = "sku数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "sku数量")
    private Integer skuNum;

    @FieldDoc(
            description = "实拣sku数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "实拣sku数量")
    private Integer actSkuNum;

    @FieldDoc(
            description = "商品数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "商品数量")
    private Integer itemNum;

    @FieldDoc(
            description = "实拣商品数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "实拣商品数量")
    private Integer actItemNum;

    @FieldDoc(
            description = "波次任务完成时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "波次任务完成时间")
    private String taskFinishedTime;
}
