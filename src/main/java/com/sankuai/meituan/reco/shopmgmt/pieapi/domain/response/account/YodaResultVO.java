package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.account;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @createTime 2023/07/20
 * @description
 */
@TypeDoc(
        description = "yoda info接口响应内容"
)
@Data
@ApiModel("yoda info接口响应内容")
public class YodaResultVO {

    /**
     * 消息和铃声设置项
     */
    @FieldDoc(
            description = "0 正常  此时已发送短信\n" +
                    "\n" +
                    "非0不正常码表见https://km.sankuai.com/page/**********", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "yoda响应code", required = true)
    private Integer code;


    @FieldDoc(
            description = "yoda响应message", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "yoda响应message", required = true)
    private String message;

    @FieldDoc(
            description = "yoda响应requestCode", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "yoda响应requestCode", required = true)
    private String requestCode;
}
