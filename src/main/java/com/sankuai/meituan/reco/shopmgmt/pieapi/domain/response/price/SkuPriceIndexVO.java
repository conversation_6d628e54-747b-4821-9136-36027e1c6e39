package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price;
// Copyright (C) 2020 Meituan
// All rights reserved

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ChannelSkuForAppVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: <EMAIL>
 * @class: PriceIndexVO
 * @date: 2020-03-23 17:51:25
 * @desc:
 */
@Data
@TypeDoc(
        description = "商品价格指数对象"
)
@ApiModel("商品价格指数对象")
public class SkuPriceIndexVO extends PriceIndexVO {

    @FieldDoc(
            description = "商品信息"
    )
    @ApiModelProperty("商品信息")
    private ChannelSkuForAppVO skuInfo;


}
