package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.platform.empower.product.client.enums.StoreCategorySceneIdentityEnum;
import com.sankuai.meituan.shangou.platform.empower.product.client.request.merchant_store_category.UpdateMerchantStoreCategoryRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/10/30
 */
@TypeDoc(
        description = "店内分类智能排序更新请求"
)
@Data
@NoArgsConstructor
public class StoreCategorySmartSortUpdateRequest {

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    private Long storeId;

    @FieldDoc(
            description = "店内分类id", requiredness = Requiredness.REQUIRED
    )
    private String categoryId;

    @FieldDoc(
            description = "智能排序", requiredness = Requiredness.REQUIRED
    )
    private Boolean smartSort;

    public void validate() {
        if (this.storeId == null) {
            throw new CommonLogicException("门店id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (this.categoryId == null) {
            throw new CommonLogicException("店内分类id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (this.smartSort == null) {
            throw new CommonLogicException("开关字段信息不能为空", ResultCode.CHECK_PARAM_ERR);
        }
    }
    public UpdateMerchantStoreCategoryRequest to(User user) {
        UpdateMerchantStoreCategoryRequest rpcReq = new UpdateMerchantStoreCategoryRequest();
        rpcReq.setUpdateType(4);
        rpcReq.setSmartSort(smartSort);
        rpcReq.setCategoryId(Long.valueOf(categoryId));
        rpcReq.setMerchantId(user.getTenantId());
        rpcReq.setNewStoreGroupId(storeId);
        rpcReq.setOperatorId(user.getAccountId());
        rpcReq.setOperatorName(user.getOperatorName());
        rpcReq.setOperatorAccount(user.getAccountName());
        rpcReq.setSceneIdentity(StoreCategorySceneIdentityEnum.POI.getCode());
        rpcReq.setPushChannel(true);
        return rpcReq;
    }
}
