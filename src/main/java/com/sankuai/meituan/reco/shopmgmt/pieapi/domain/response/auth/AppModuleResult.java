package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.auth;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.codehaus.jackson.annotate.JsonIgnore;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 18/9/28.
 */
@Data
@ApiModel("查询App模块信息响应")
public class AppModuleResult {

    @ApiModelProperty("权限列表，层级展示")
    private List<AppModule> modules = Lists.newArrayList();

    @ApiModel("App模块信息")
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AppModule {

        @ApiModelProperty("模块待处理任务数")
        private Integer pendingTaskCount;
        @ApiModelProperty("模块待处理任务角标类型, 0数字 1红点")
        private Integer pendingTaskType;

        @ApiModelProperty("模块编码")
        private String code;
        @ApiModelProperty("模块名称")
        private String name;
        @ApiModelProperty("js名称")
        private String jsName;
        @ApiModelProperty("js类型")
        private Integer jsType;
        @ApiModelProperty("图标链接")
        private String icon;
        @ApiModelProperty("是否支持多门店(1:支持/0:不支持)")
        private Integer supportMultiPoi;

        /**
         * 排序字段，无需返回前段
         */
        @JsonIgnore
        @com.fasterxml.jackson.annotation.JsonIgnore
        private int sort;
        @JsonIgnore
        @com.fasterxml.jackson.annotation.JsonIgnore
        private String parent;
        @JsonIgnore
        @com.fasterxml.jackson.annotation.JsonIgnore
        private boolean hasAvailLeaf = false;

        //  模块树结构
        @ApiModelProperty("子模块列表")
        private List<AppModule> subModules = Lists.newArrayList();
    }
}
