package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.auth;

import com.meituan.shangou.saas.tenant.thrift.dto.department.response.DepartmentSpreadResponse;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2018/10/23
 **/
@Data
public class DepartmentSpreadVo {

    /**
     * 员工列表
     */
    private List<EmployeeVo> employeeList;

    /**
     * 部门列表
     */
    private List<DepartmentVo> departmentList;

    public DepartmentSpreadVo  build(DepartmentSpreadResponse response) {
        this.departmentList =
                response.getDepartmentList().stream().map(DepartmentVo::build).collect(Collectors.toList());
        this.employeeList = response.getEmployeeList().stream().map(EmployeeVo::build).collect(Collectors.toList());
        return this;
    }
}
