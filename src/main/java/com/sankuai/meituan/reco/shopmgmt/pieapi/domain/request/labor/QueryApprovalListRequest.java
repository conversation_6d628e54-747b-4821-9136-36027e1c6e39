package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022-12-08
 * @email <EMAIL>
 */
@TypeDoc(
        description = "校验手机请求"
)
@Data
@ApiModel("校验手机请求")
@AllArgsConstructor
@NoArgsConstructor
public class QueryApprovalListRequest {

    @FieldDoc(
            description = "1-未审批，2-已审批"
    )
    @NotNull
    private Integer type;

    @FieldDoc(
            description = "1-未审批，2-已审批"
    )
    @NotNull
    private Integer pageNo;

    @FieldDoc(
            description = "1-未审批，2-已审批"
    )
    @NotNull
    private Integer pageSize;


    @FieldDoc(
            description = "关键字 门店/姓名"
    )
    private String keyword;

}
