package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.WarehouseSeedWaitModuleVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "待分拣查询返回"
)
@Data
@Builder
@ApiModel("待分拣查询返回")
public class WarehouseSeedWaitQueryResponse {

    @FieldDoc(
            description = "待分拣数据列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "待分拣数据列表", required = true)
    private List<WarehouseSeedWaitModuleVO> dataList;
}
