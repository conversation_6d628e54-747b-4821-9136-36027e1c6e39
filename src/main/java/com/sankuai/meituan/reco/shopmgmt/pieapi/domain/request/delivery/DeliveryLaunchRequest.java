package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@TypeDoc(
		description = "配送发起请求体"
)
@ApiModel("配送发起请求体")
@Data
public class DeliveryLaunchRequest {

	@FieldDoc(
			description = "门店id", requiredness = Requiredness.REQUIRED
	)
	@ApiModelProperty(value = "门店id", required = true)
	@NotNull
	private Long storeId;

	@FieldDoc(
			description = "赋能订单号", requiredness = Requiredness.REQUIRED
	)
	@ApiModelProperty(value = "赋能订单号", required = true)
	@NotNull
	private Long orderId;

	@FieldDoc(
			description = "配送渠道标识", requiredness = Requiredness.REQUIRED
	)
	@ApiModelProperty(value = "配送渠道标识", required = true)
	@NotNull
	private Integer deliveryChannelId;

	@FieldDoc(
			description = "服务包", requiredness = Requiredness.REQUIRED
	)
	@ApiModelProperty(value = "服务包", required = true)
	@NotNull
	private String servicePackage;

	@FieldDoc(
			description = "询价所得预估配送费", requiredness = Requiredness.REQUIRED
	)
	@ApiModelProperty(value = "询价所得预估配送费", required = true)
	@NotNull
	private Double estimatedDeliveryFee;
}
