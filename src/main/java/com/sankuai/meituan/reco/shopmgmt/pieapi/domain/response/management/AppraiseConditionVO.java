package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.management;
// Copyright (C) 2020 Meituan
// All rights reserved

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.resource.management.dto.appraise.AppraiseConditionPreviewDTO;
import lombok.Data;

@TypeDoc(
        description = "考核条件"
)
@Data
public class AppraiseConditionVO {

    @FieldDoc(
            description = "比较类型 EQ等于, LT小于, GT大于, GTE大于等于, LTE小于等于, NE不等于",
            example = {}
    )
    private String logicOperate;

    @FieldDoc(
            description = "目标值",
            example = {}
    )
    private String targetValue;

    @FieldDoc(
            description = "条件通过",
            example = {}
    )
    private Integer conditionPassed = -1;

    public AppraiseConditionVO(AppraiseConditionPreviewDTO conditionPreview) {
        this.logicOperate = conditionPreview.getOp();
        this.targetValue = conditionPreview.getTargetValue();
        if (conditionPreview.isConditionPassed() != null) {
            this.conditionPassed = conditionPreview.isConditionPassed() ? 1 : 0;
        }
    }
}
