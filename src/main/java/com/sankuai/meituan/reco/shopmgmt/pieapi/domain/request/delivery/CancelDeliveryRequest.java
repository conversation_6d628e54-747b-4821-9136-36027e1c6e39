package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@TypeDoc(
        description = "转商家自配送请求体"
)
@ApiModel("转商家自配送请求体")
@Data
public class CancelDeliveryRequest {

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店id", required = true)
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "赋能订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "赋能订单号", required = true)
    @NotNull
    private Long orderId;
}
