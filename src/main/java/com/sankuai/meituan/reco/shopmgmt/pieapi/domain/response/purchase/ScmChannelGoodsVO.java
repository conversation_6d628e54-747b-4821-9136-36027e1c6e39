package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.purchase;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/29
 * 供应链渠道商品
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@ToString
@Builder
public class ScmChannelGoodsVO {
    /**
     * 渠道ID
     */
    @FieldDoc(
            description = "渠道ID",
            requiredness = Requiredness.REQUIRED
    )
    private Integer channelId;
    /**
     * 图片地址
     */
    @FieldDoc(
            description = "图片地址",
            requiredness = Requiredness.REQUIRED
    )
    private List<String> picUrlList;
    /**
     * 商品id
     */
    @FieldDoc(
            description = "商品id",
            requiredness = Requiredness.REQUIRED
    )
    private String goodsId;
    /**
     * 三方渠道商品id
     */
    @FieldDoc(
            description = "三方渠道商品id",
            requiredness = Requiredness.REQUIRED
    )
    private String channelGoodsId;
    /**
     * 渠道商品名称
     */
    @FieldDoc(
            description = "渠道商品名称",
            requiredness = Requiredness.REQUIRED
    )
    private String channelGoodsName;
    /**
     * 三方渠道商品规格id
     */
    @FieldDoc(
            description = "三方渠道商品规格id",
            requiredness = Requiredness.OPTIONAL
    )
    private String channelSpecId;
    /**
     * 三方渠道商品规格名称
     */
    @FieldDoc(
            description = "三方渠道商品规格名称",
            requiredness = Requiredness.OPTIONAL
    )
    private String channelSpecName;
    /**
     * 最小起订量
     */
    @FieldDoc(
            description = "最小起订量",
            requiredness = Requiredness.OPTIONAL
    )
    private Integer minOrderQuantity;
    /**
     * 最低价
     */
    @FieldDoc(
            description = "最低价",
            requiredness = Requiredness.OPTIONAL
    )
    private String goodsMinPrice;
    /**
     * 库存
     */
    @FieldDoc(
            description = "库存",
            requiredness = Requiredness.OPTIONAL
    )
    private String stock;
    /**
     * 商品状态
     */
    @FieldDoc(
            description = "商品状态",
            requiredness = Requiredness.REQUIRED
    )
    private Integer status;
    /**
     * 详情地址
     */
    @FieldDoc(
            description = "详情地址",
            requiredness = Requiredness.OPTIONAL
    )
    private String detailUrl;
    /**
     * 销量
     */
    @FieldDoc(
            description = "销量",
            requiredness = Requiredness.OPTIONAL
    )
    private String saleQuantity;
    /**
     * 计量单位
     */
    @FieldDoc(
            description = "计量单位",
            requiredness = Requiredness.OPTIONAL
    )
    private String unit;
    /**
     * 商城价格,分
     */
    @FieldDoc(
            description = "商城价格",
            requiredness = Requiredness.OPTIONAL
    )
    private String specPrice;
    /**
     * 外部渠道供应商Id
     */
    @FieldDoc(
            description = "外部渠道供应商Id",
            requiredness = Requiredness.OPTIONAL
    )
    private String channelSupplierId;

    /**
     * 外部渠道供应商名称
     */
    @FieldDoc(
            description = "外部渠道供应商名称",
            requiredness = Requiredness.OPTIONAL
    )
    private String channelSupplierName;

    @FieldDoc(
            description = " 是否开启阶梯价格",
            requiredness = Requiredness.OPTIONAL
    )
    private Boolean openLadderPrice;

    @FieldDoc(
            description = "海商渠道库存",
            requiredness = Requiredness.OPTIONAL
    )
    private String channelStock;

    @FieldDoc(
            description = "每批数量,对应unit",
            requiredness = Requiredness.OPTIONAL
    )
    private Integer batchNumber;

    @FieldDoc(
            description = "是否包邮",
            requiredness = Requiredness.OPTIONAL
    )
    private Boolean serviceChargeType;

    @FieldDoc(
            description = "首重（单位：克）或首件（单位：件）",
            requiredness = Requiredness.OPTIONAL
    )
    private Long firstUnit;

    @FieldDoc(
            description = "首重或首件的价格（单位：分）",
            requiredness = Requiredness.OPTIONAL
    )
    private Long firstUnitFee;

    @FieldDoc(
            description = "续重件单位",
            requiredness = Requiredness.OPTIONAL
    )
    private Long nextUnit;

    @FieldDoc(
            description = "续重件价格（单位：分）",
            requiredness = Requiredness.OPTIONAL
    )
    private Long nextUnitFee;

    @FieldDoc(
            description = "首重/件运费",
            requiredness = Requiredness.OPTIONAL
    )
    private String firstUnitPrice;

    @FieldDoc(
            description = "续重/件运费",
            requiredness = Requiredness.OPTIONAL
    )
    private String nextUnitPrice;
}
