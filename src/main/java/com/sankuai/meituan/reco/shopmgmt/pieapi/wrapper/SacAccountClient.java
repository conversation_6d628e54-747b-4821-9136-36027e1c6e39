package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;

import com.meituan.shangou.sac.dto.model.SacMenuDetailDTO;
import com.meituan.shangou.sac.dto.model.SacPermissionDTO;
import com.meituan.shangou.sac.dto.request.manager.QuerySacMenuListRequest;
import com.meituan.shangou.sac.dto.request.search.QueryNewCsInfoRequest;
import com.meituan.shangou.sac.dto.request.search.QuerySacAccountPermissionRequest;
import com.meituan.shangou.sac.dto.response.SacStatus;
import com.meituan.shangou.sac.dto.response.manager.QuerySacMenuDetailListResponse;
import com.meituan.shangou.sac.dto.response.search.CsAccessInfoResponse;
import com.meituan.shangou.sac.dto.response.search.QuerySacAccountPermissionResponse;
import com.meituan.shangou.sac.thrift.search.SacAccountSearchThriftService;
import com.meituan.shangou.sac.thrift.search.SacMenuSearchThriftService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.auth.SacAccountPermissionVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.auth.SacMenuDetailVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.auth.SacPermissionVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.shangou.empower.auth.sdk.bean.SessionInfo;
import com.sankuai.meituan.shangou.empower.auth.sdk.context.SessionContext;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * @description:
 * @author: WangSukuan
 * @create: 2020-12-01
 **/
@Component
@Slf4j
public class SacAccountClient {

    @Autowired
    private SacAccountSearchThriftService sacAccountSearchThriftService;

    @Resource
    private SacMenuSearchThriftService sacMenuSearchThriftService;

    public QuerySacAccountPermissionResponse querySacAccountPermission(Long accountId) {

        QuerySacAccountPermissionRequest querySacAccountPermissionRequest = new QuerySacAccountPermissionRequest();
        querySacAccountPermissionRequest.setAccountId(accountId);

        QuerySacAccountPermissionResponse response = sacAccountSearchThriftService
                .querySacAccountPermission(querySacAccountPermissionRequest);
        handleSacStatus(response.getSacStatus());

        return response;

    }


    public SacAccountPermissionVO querySacAccountPermission() {
        SessionInfo sessionInfo = SessionContext.getCurrentSession();
        int authAppId = sessionInfo.getAuthAppId();
        int authSubAppId = sessionInfo.getAuthSubAppId();

        QuerySacAccountPermissionRequest querySacAccountPermissionRequest = new QuerySacAccountPermissionRequest();
        querySacAccountPermissionRequest.setAccountId(sessionInfo.getAccountId());
        querySacAccountPermissionRequest.setAuthAppId(authAppId);
        querySacAccountPermissionRequest.setAuthSubAppId(authSubAppId);


        QuerySacAccountPermissionResponse response = sacAccountSearchThriftService
                .querySacAccountPermission(querySacAccountPermissionRequest);
        handleSacStatus(response.getSacStatus());

        SacAccountPermissionVO sacAccountPermissionVO = new SacAccountPermissionVO();
        sacAccountPermissionVO.setAccountId(response.getSacAccountId());
        List<SacPermissionDTO> sacPermissionDtos = response.getSacPermissionDTOS();
        if (CollectionUtils.isEmpty(sacPermissionDtos)) {
            sacAccountPermissionVO.setSacPermissionVos(Collections.emptyList());
            return sacAccountPermissionVO;
        }
        List<SacPermissionVo> sacPermissionVoList = SacPermissionVo.buildBySacPermissionDTOList(sacPermissionDtos);
        sacAccountPermissionVO.setSacPermissionVos(sacPermissionVoList);
        return sacAccountPermissionVO;

    }


    public List<SacMenuDetailVo> querySacMenuListByAppId() {
        Integer authId = ApiMethodParamThreadLocal.getIdentityInfo().getAuthId();
        QuerySacMenuListRequest request = new QuerySacMenuListRequest();
        request.setAppId(authId);
        SessionInfo currentSession = SessionContext.getCurrentSession();
        request.setTenantId(currentSession.getTenantId());
        QuerySacMenuDetailListResponse response = sacMenuSearchThriftService.querySacMenuList(request);
        handleSacStatus(response.getSacStatus());
        List<SacMenuDetailDTO> sacMenuDetailDTOList = response.getSacMenuDetailDTOList();
        if (CollectionUtils.isEmpty(sacMenuDetailDTOList)){
            return Collections.emptyList();
        }
        return SacMenuDetailVo.buildBySacMenuDetailDTOList(sacMenuDetailDTOList);

    }

    private void handleSacStatus(SacStatus sacStatus) {
        if (sacStatus.getCode() != ResultCodeEnum.SUCCESS.getValue()) {
            throw new BizException(sacStatus.getCode(), sacStatus.getMessage());
        }
    }

    public String getCounselUrl(Long accountId, Long tenantId, String type) {
        QueryNewCsInfoRequest request = new QueryNewCsInfoRequest();
        request.setAccountId(accountId);
        request.setTenantId(tenantId);
        request.setType(type);
        CsAccessInfoResponse response = sacAccountSearchThriftService.getCsAccessInfoNew(request);
        handleSacStatus(response.getSacStatus());
        return response.getCsAccessInfoDto().getUrl();
    }
}
