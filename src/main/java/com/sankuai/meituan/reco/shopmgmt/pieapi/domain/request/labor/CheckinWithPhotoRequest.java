package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2022/12/21 21:01
 **/
@Data
public class CheckinWithPhotoRequest extends CheckinRequest {

    @FieldDoc(description = "照片文件url")
    @ApiModelProperty("照片文件url")
    private String photoUrl;

    public Optional<String> selfValid() {
        try {
            super.valid();
        } catch (Exception e) {
            return Optional.of("经纬度参数不合法");
        }
        if (StringUtils.isBlank(photoUrl)) {
            return Optional.of("需要上传打卡照片");
        }
        return Optional.empty();
    }
}
