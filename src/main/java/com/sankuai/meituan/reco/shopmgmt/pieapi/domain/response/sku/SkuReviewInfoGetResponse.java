package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SkuReviewVO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.GetSkuReviewInfoResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: 获取单个商品审核信息
 * @author: Wang<PERSON>ukuan
 * @create: 2020-03-10
 **/
@TypeDoc(
        description = "获取单个商品审核信息"
)
@Data
@ApiModel("获取单个商品审核信息")
@NoArgsConstructor
public class SkuReviewInfoGetResponse {

    @FieldDoc(
            description = "商品审核信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品审核信息")
    private SkuReviewVO skuReviewVO;


    public SkuReviewInfoGetResponse buildSkuReviewInfoGetResponse(GetSkuReviewInfoResponse getSkuReviewInfoResponse){

        SkuReviewInfoGetResponse skuReviewInfoGetResponse = new SkuReviewInfoGetResponse();
        SkuReviewVO skuReviewVO = new SkuReviewVO().buildSkuReviewVO(getSkuReviewInfoResponse.getData());
        skuReviewInfoGetResponse.setSkuReviewVO(skuReviewVO);
        return skuReviewInfoGetResponse;

    }

}
