package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew;

import com.facebook.swift.codec.ThriftField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/7/11
 */
@TypeDoc(
		description = "展示微信二维码"
)
@Data
@ApiModel("展示微信二维码")
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PullNewQrCodeVo {

	@FieldDoc(
			description = "微信二维码ticket", requiredness = Requiredness.REQUIRED
	)
	@ApiModelProperty(value = "微信二维码ticket", required = true)
	private String weixinQrTicket;

	@FieldDoc(
			description = "微信二维码URL", requiredness = Requiredness.REQUIRED
	)
	@ApiModelProperty(value = "微信二维码URL", required = true)
	private String weixinQrUrl;

	@FieldDoc(
			description = "微信二维码过期时间"
	)
	@ApiModelProperty(value = "微信二维码过期时间")
	private Integer expireSeconds;

	@FieldDoc(
			description = "小程序二维码URL", requiredness = Requiredness.REQUIRED
	)
	@ApiModelProperty(value = "小程序二维码URL", required = true)
	private String miniProgramQrUrl;

	@FieldDoc(
			description = "小程序短链L", requiredness = Requiredness.REQUIRED
	)
	@ApiModelProperty(value = "小程序短链L", required = true)
	private String miniShortLink;


	@FieldDoc(
			description = "企微码", requiredness = Requiredness.REQUIRED
	)
	@ApiModelProperty(value = "企微码", required = true)
	private String wechatWorkUrl;

	@FieldDoc(
			description = "是否是手动绑定企微", requiredness = Requiredness.REQUIRED
	)
	@ApiModelProperty(value = "是否是手动绑定企微", required = true)
	private boolean manualBindWeCom;

	@FieldDoc(
			description = "外卖推广短链"
	)
	@ApiModelProperty(value = "外卖推广短链")
	private String waimaiShortLink;

	@FieldDoc(
			description = "会员推广二维码"
	)
    @ApiModelProperty(value = "会员推广二维码")
	private String memberQrUrl;

	@FieldDoc(
			description = "会员推广短链"
	)
    @ApiModelProperty(value = "会员推广短链")
	private String memberShortLink;

    @FieldDoc(
            description = "美团小程序deeplink"
    )
    @ApiModelProperty(value = "美团小程序deeplink")
	private String mtDpLink;
}
