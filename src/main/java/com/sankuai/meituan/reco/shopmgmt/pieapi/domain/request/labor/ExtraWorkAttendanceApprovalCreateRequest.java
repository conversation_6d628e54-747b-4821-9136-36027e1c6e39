package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Optional;

@TypeDoc(
        description = "加班申请请求"
)
@ApiModel("加班申请请求")
@Data
public class ExtraWorkAttendanceApprovalCreateRequest {

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店ID")
    @NotNull
    private Long poiId;

    @FieldDoc(
            description = "申诉说明", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "申诉说明")
    private String applyText;

    @FieldDoc(
            description = "申述照片", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "申述照片")
    private List<String> photoList;

    @FieldDoc(
            description = "申请加班出勤日期，格式：yyyy-MM-dd", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "申请加班出勤日期，格式：yyyy-MM-dd")
    private String applyAttendanceDay;

    @FieldDoc(
            description = "加班上线打卡时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "加班上线打卡时间")
    private Long checkinTime;

    @FieldDoc(
            description = "加班下线打卡时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "加班下线打卡时间")
    private Long checkoutTime;

    public Optional<String> validate() {
        if (this.poiId == null || this.poiId <= 0) {
            return Optional.of("门店Id异常");
        } else if (applyAttendanceDay == null || StringUtils.isBlank(applyAttendanceDay) || checkinTime == null || checkinTime <= 0
                || checkoutTime == null || checkoutTime <= 0 || checkinTime >= checkoutTime) {
            return Optional.of("上线时间段异常");
        }
        return Optional.empty();
    }
}
