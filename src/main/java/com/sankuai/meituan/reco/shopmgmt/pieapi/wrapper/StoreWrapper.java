package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.dianping.rhino.annotation.Rhino;
import com.meituan.shangou.saas.tenant.thrift.PoiThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiMapResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.GetStoreByIdsRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.FallbackException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.store.management.thrift.ObtainStoreInfoResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
@Rhino
@Slf4j
public class StoreWrapper {

    @Resource
    private PoiThriftService poiThriftService;

    /* Rhino需要pubic */
    @SuppressWarnings("WeakerAccess")
    @MethodLog(logRequest = true, logResponse = true)
    public Map<Long, PoiInfoDto> queryPoiInfoById(Long tenantId, List<Long> storeIds) {
        if (tenantId == null) {
            throw new CommonRuntimeException("租户id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (CollectionUtils.isEmpty(storeIds)) {
            throw new CommonRuntimeException("门店id不能为空", ResultCode.CHECK_PARAM_ERR);
        }

        PoiMapResponse response = poiThriftService.queryTenantPoiInfoMapByPoiIds(storeIds, tenantId);
        log.info("PoiThriftService.queryTenantPoiInfoMapByPoiIds, storeIds:{}, tenantId:{}, response:{}",
                storeIds, tenantId, response);
        return response.getPoiInfoMap();
    }
}
