package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.wavepick.converter;

import com.sankuai.meituan.reco.pickselect.thrift.wave.dto.WarehousePickDetailFuzzyDTO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.WarehousePickDetailFuzzyResponse;
import org.mapstruct.Mapper;

/**
 * @Description:
 * @Author: zhangjian155
 * @Date: 2022/8/25 18:39
 */
@Mapper(componentModel = "spring", uses = {WarehouseProgressConverter.class,WarehousePickItemSkuConverter.class})
public abstract class WarehousePickDetailFuzzyResponseConverter {
    public abstract WarehousePickDetailFuzzyResponse convert2Response(WarehousePickDetailFuzzyDTO warehousePickDetailFuzzyDTO);
}
