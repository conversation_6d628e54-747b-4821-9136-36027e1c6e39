package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "波次拣货领取请求"
)
@ApiModel("波次拣货领取请求")
@Data
public class WarehousePickReceiveRequest {

    @FieldDoc(
            description = "波次任务号列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "波次任务号列表")
    private List<String> taskOrderIdList;

}
