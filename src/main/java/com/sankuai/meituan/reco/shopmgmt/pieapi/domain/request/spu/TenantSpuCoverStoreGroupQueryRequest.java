package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.dianping.rhino.cluster.common.util.AssertUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/10/29
 */
@TypeDoc(
        description = "查询商品主档商品分组覆盖的门店"
)
@Data
@ApiModel("查询商品主档商品分组覆盖的门店")
public class TenantSpuCoverStoreGroupQueryRequest {
    @FieldDoc(
            description = "spuId"
    )
    @ApiModelProperty(value = "spuId")
    private String spuId;

    @FieldDoc(
            description = "门店分组id列表"
    )
    @ApiModelProperty(value = "门店分组id列表")
    private List<Integer> poiGroupIdList;

    public void selfCheck() {
        AssertUtil.isTrue(StringUtils.isNotBlank(spuId), "spuId不能为空");
        AssertUtil.isTrue(CollectionUtils.isNotEmpty(poiGroupIdList), "门店分组列表不能为空");
    }
}
