package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @createTime 2019/11/26
 * @description
 */
@TypeDoc(
        description = "拣货首页模块"
)
@Data
@ApiModel("拣货首页模块")
public class PickHomePageModuleVO {

    @FieldDoc(
            description = "是否展示整单时长和超时率 (0:否, 1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示整单时长和超时率 (0:否, 1:是)", required = true)
    @NotNull
    private Integer showPickStatisticsTab;

    @FieldDoc(
            description = "是否展示数据罗盘入口 (0:否, 1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示数据罗盘入口 (0:否, 1:是)", required = true)
    @NotNull
    private Integer showPickStatisticsCompassTab;

    @FieldDoc(
            description = "是否展示缺货tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示缺货tab (0:否，1:是)", required = true)
    @NotNull
    private Integer showLackStockTab;


    @FieldDoc(
            description = "是否展示备货tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示备货tab (0:否，1:是)", required = true)
    @NotNull
    private Integer showStockUpTab;


    @FieldDoc(
            description = "是否展示拣货tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示拣货tab (0:否，1:是)", required = true)
    @NotNull
    private Integer showPickTab;

    @FieldDoc(
            description = "是否展示合流tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示合流tab (0:否，1:是)", required = true)
    @NotNull
    private Integer showMergeTab;

    @FieldDoc(
            description = "是否展示拣货外采tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示拣货外采tab (0:否，1:是)", required = true)
    @NotNull
    private Integer showPickAndOutwardSourcingTab;

}
