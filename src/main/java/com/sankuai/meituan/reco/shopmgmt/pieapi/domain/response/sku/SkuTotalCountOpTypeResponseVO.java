package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * @Author: <EMAIL>
 * @Date: 2020-01-10 15:36
 * @Description:
 */
@TypeDoc(
        description = "查询分类下商品总数响应"
)
@Data
@ApiModel("查询分类下商品总数响应")
public class SkuTotalCountOpTypeResponseVO {
    @FieldDoc(
            description = "天数-商品总数映射，0代表所有天数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "天数-商品总数映射", required = true)
    private Map<Integer,Integer> skuDayCountMap;
}
