package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.channelpromotion;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelpromotion.dto.ForbidFieldAndActivityDTO;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/9/12
 */
@TypeDoc(
        description = "禁止变更属性关联的活动信息"
)
@Data
public class ForbidFieldAndActivityVO {
    @FieldDoc(description = "活动维度拦截变更属性列表:0-修改商品名称; 1-修改商品图片; 2-修改售卖方式; 3-新增规格; 4-修改UPC;5-修改店内码/货号;6-修改价格;" +
            "7-修改商品规格名称;8-修改商品重量;9-修改起购数;10-修改组包商品件数;11-删除规格;12-新增属性")
    private Integer forbidField;
    @FieldDoc(description = "活动信息")
    private List<StoreSkuActivityVO> activityList;

    public static ForbidFieldAndActivityVO fromDto(ForbidFieldAndActivityDTO dto) {
        if(dto==null){
            return null;
        }
        ForbidFieldAndActivityVO vo = new ForbidFieldAndActivityVO();
        vo.setForbidField(dto.getForbidField());
        List<StoreSkuActivityVO>activityList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(dto.getActivityIdList())){
            dto.getActivityIdList().forEach(activity->activityList.add(StoreSkuActivityVO.fromDto(activity)));
        }
        vo.setActivityList(activityList);
        return vo;
    }
}
