package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.google.common.collect.Lists;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.CommonThreadPoolHolder;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.store.wms.enums.OtherStockTaskStatus;
import com.sankuai.meituan.reco.store.wms.thrift.otherstock.*;
import com.sankuai.meituan.reco.store.wms.thrift.common.PageParam;
import com.sankuai.meituan.reco.store.wms.thrift.common.UserParam;
import com.sankuai.meituan.reco.store.wms.thrift.otherstock.OtherStockListQueryRequest;
import com.sankuai.meituan.reco.store.wms.thrift.otherstock.OtherStockTaskThriftService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.function.Supplier;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @create 2021/09/22
 */
@Slf4j
@Component
public class OtherStockReceiptClient {

    @Resource(name = "wmsOtherStockTaskThriftService")
    private OtherStockTaskThriftService otherStockTaskThriftService;

    private Map<Integer, List<String>> BORROW_RETURN_2_ABBR_NAMES;
    private Map<Integer, List<Integer>> BORROW_RETURN_2_OTHER_TASK_STATUS_LIST;

    public final static int BORROW_LIST_TYPE = 1;
    public final static int RETURN_LIST_TYPE = 2;
    public final static int HANDLE_LIST_TYPE = 3;

    @PostConstruct
    public void init() {
        BORROW_RETURN_2_ABBR_NAMES = new HashMap<>();
        BORROW_RETURN_2_ABBR_NAMES.put(BORROW_LIST_TYPE, Lists.newArrayList("WLLY", "ZBLY")); // 物料领用、装备领用
        BORROW_RETURN_2_ABBR_NAMES.put(RETURN_LIST_TYPE, Lists.newArrayList("WLGH", "ZBGH")); // 物料归还、装备归还
        BORROW_RETURN_2_ABBR_NAMES.put(HANDLE_LIST_TYPE, Lists.newArrayList("WLLY", "WLGH", "ZBLY", "ZBGH"));
        // 领用列表页-若有待领用的单据则需要在tab名称旁边红点标记数量
        // 归还列表页-若有待归还的单据则需要在tab名称旁边红点标记数量
        // 处理列表页-若有待归还、待确认、已驳回的单据则需要在tab名称旁边红点标记数量
        BORROW_RETURN_2_OTHER_TASK_STATUS_LIST = new HashMap<>();
        BORROW_RETURN_2_OTHER_TASK_STATUS_LIST.put(BORROW_LIST_TYPE, Lists.newArrayList(OtherStockTaskStatus.AUDITING.getType()));
        BORROW_RETURN_2_OTHER_TASK_STATUS_LIST.put(RETURN_LIST_TYPE, Lists.newArrayList(OtherStockTaskStatus.WAIT_TO_SUBMIT.getType()));
        BORROW_RETURN_2_OTHER_TASK_STATUS_LIST.put(HANDLE_LIST_TYPE, Lists.newArrayList(OtherStockTaskStatus.WAIT_TO_SUBMIT.getType(), OtherStockTaskStatus.REJECTED.getType()));
    }

    public long borrowReturnRedPoint(PendingTaskParam request, boolean hasPermissionDisplayHandleList) throws ExecutionException, InterruptedException {

        ExecutorService concurrentRpcInvokeExecutor = CommonThreadPoolHolder.getConcurrentRpcInvokeExecutor();

        Future<Long> borrowListRedPointFuture = concurrentRpcInvokeExecutor.submit(
                () -> borrowReturnRedPoint(request, BORROW_LIST_TYPE)
        );
        Future<Long> returnListRedPointFuture = concurrentRpcInvokeExecutor.submit(
                () -> borrowReturnRedPoint(request, RETURN_LIST_TYPE)
        );
        Future<Long> handleListRedPointFuture = concurrentRpcInvokeExecutor.submit(
                () -> {
                    if (!hasPermissionDisplayHandleList) {
                        return 0L;
                    }
                    return borrowReturnRedPoint(request, HANDLE_LIST_TYPE);
                }
        );

        return borrowListRedPointFuture.get() + returnListRedPointFuture.get() + handleListRedPointFuture.get();
    }

    private long borrowReturnRedPoint(PendingTaskParam request, int listType) {
        Long entityId = request.getEntityId();
        OtherStockListQueryRequest thriftReq = new OtherStockListQueryRequest();
        thriftReq.setTenantId(request.getTenantId());
        thriftReq.setUserParam(new UserParam(
                String.valueOf(request.getUser().getAccountId()),
                request.getUser().getOperatorName(),
                request.getUser().getEmployeeId()));
        // 1领用列表 2归还列表 : 当前仓提出申请的领用或归还单。 3处理列表 : 当前仓收到申请的领用或归还单
        if (listType == BORROW_LIST_TYPE || listType == RETURN_LIST_TYPE) {
            thriftReq.setApplicantEntityIds(Collections.singletonList(entityId));
            thriftReq.setCreatorName(request.getUser().getOperatorName());
        } else {
            thriftReq.setStoreIds(Collections.singletonList(entityId));
        }

        List<Long> typeIds = queryBorrowReturnTypeIds(request.getTenantId(), listType);
        thriftReq.setTypeIdList(typeIds);

        thriftReq.setStatusIdList(BORROW_RETURN_2_OTHER_TASK_STATUS_LIST.get(listType));

        thriftReq.setPageParam(new PageParam(1, 1));

        ReceiptListCountResult response;
        try {
            response = otherStockTaskThriftService.countReceiptList(thriftReq);
        } catch (Exception e) {
            log.warn("otherStockTaskThriftService.countReceiptList invoke error, req: {}", thriftReq);
            return 0;
        }

        return Optional.ofNullable(response).map(ReceiptListCountResult::getCount).orElseGet(new Supplier<Long>() {
            @Override
            public Long get() {
                log.warn("otherStockTaskThriftService.countReceiptList error: {}", response);
                return 0L;
            }
        });
    }

    private List<Long> queryBorrowReturnTypeIds(Long tenantId, int borrowReturnListType) {
        List<String> abbrNames = BORROW_RETURN_2_ABBR_NAMES.get(borrowReturnListType);
        QueryOtherTaskTypeReq queryOtherTaskTypeReq = new QueryOtherTaskTypeReq();
        queryOtherTaskTypeReq.setTenantId(tenantId);
        OtherTaskTypeResult result;
        try {
            result = otherStockTaskThriftService.queryTaskType(queryOtherTaskTypeReq);
        } catch (Exception e) {
            throw new CommonRuntimeException(e);
        }
        return Optional.ofNullable(result)
                .map(OtherTaskTypeResult::getOtherTaskTypeItemList)
                .orElse(Collections.emptyList())
                .stream()
                .filter(it -> abbrNames.contains(it.getAbbrName()))
                .map(OtherTaskTypeResult.OtherTaskTypeItem::getTypeId)
                .collect(Collectors.toList());
    }

}