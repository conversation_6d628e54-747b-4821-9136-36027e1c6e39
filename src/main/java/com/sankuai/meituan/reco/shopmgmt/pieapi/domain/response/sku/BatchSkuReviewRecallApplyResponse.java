package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.BatchRecallResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ReviewRecallFaliedDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.review.BatchRecallProductReviewResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 撤回提交申请响应
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-03-11
 **/
@TypeDoc(
        description = "撤回提交申请响应"
)
@Data
@ApiModel("撤回提交申请响应")
public class BatchSkuReviewRecallApplyResponse {

    @FieldDoc(
            description = "失败数据", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "失败数据")
    List<ReviewRecallFailedVO> dataList = new ArrayList<>();


    public BatchSkuReviewRecallApplyResponse buildBatchSkuReviewRecallApplyResponse(BatchRecallResponse batchRecallResponse){

        List<ReviewRecallFaliedDTO> reviewRecallFaliedDTOS = batchRecallResponse.getDataList();
        if (CollectionUtils.isEmpty(reviewRecallFaliedDTOS)){
            return this;
        }
        for (ReviewRecallFaliedDTO reviewRecallFaliedDTO : reviewRecallFaliedDTOS){
            ReviewRecallFailedVO reviewRecallFailedVO = new ReviewRecallFailedVO().buildReviewRecallFailedVO(reviewRecallFaliedDTO);
            this.dataList.add(reviewRecallFailedVO);
        }
        return this;
    }

    public static BatchSkuReviewRecallApplyResponse fromBatchRecallProductReviewResponse(BatchRecallProductReviewResponse batchRecallProductReviewResponse){
        BatchSkuReviewRecallApplyResponse batchSkuReviewRecallApplyResponse = new BatchSkuReviewRecallApplyResponse();

        List<BatchRecallProductReviewResponse.RecallFailItem> reviewRecallFailedDTOS = batchRecallProductReviewResponse.getFailedList();
        batchSkuReviewRecallApplyResponse.getDataList().addAll(Fun.map(reviewRecallFailedDTOS, ReviewRecallFailedVO::buildReviewRecallFailedVO));

        return batchSkuReviewRecallApplyResponse;
    }
}
