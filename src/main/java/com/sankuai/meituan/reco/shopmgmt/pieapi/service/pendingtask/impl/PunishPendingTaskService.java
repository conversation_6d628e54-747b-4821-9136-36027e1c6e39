package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.impl;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.AbstractSinglePendingTaskService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.PunishServiceWrapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: hezhengyu
 * @create: 2023-11-17 14:19
 */
@Service
public class PunishPendingTaskService extends AbstractSinglePendingTaskService  {

    @Resource
    private PunishServiceWrapper punishServiceWrapper;

    @Override
    protected PendingTaskResult getPendingTaskCount(PendingTaskParam param) throws Exception {
        String accountName = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountName();
        CommonResponse<Integer> resp = punishServiceWrapper.countWaitingAppealTicket(MccConfigUtil.getWaimaTenantId(), accountName);
        if(!resp.isSuccess()){
            return PendingTaskResult.createNumberMarker(0);
        }

        return PendingTaskResult.createNumberMarker(resp.getData());
    }

    @Override
    protected AuthCodeEnum module() {
        return AuthCodeEnum.PUNISH_MINE;
    }
}
