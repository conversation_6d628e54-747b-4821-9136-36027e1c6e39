package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.account;

import org.apache.commons.lang3.StringUtils;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.request.VerifyAndBindMobileRequest;
import com.sankuai.meituan.shangou.saas.common.aop.feature.Validatable;
import com.sankuai.meituan.shangou.saas.common.utils.AssertUtil;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/05/04
 */
@TypeDoc(
        description = "验证并绑定手机请求"
)
@Data
public class VerifyAndBindMobileReq implements Validatable {

    @FieldDoc(
            description = "短信验证请求编码，authorize时请求结果对应的request_code"
    )
    private String requestCode;

    @FieldDoc(
            description = "Yoda 32位随机码，10min有效，仅可使用一次，用于获取验证成功的结果，由前端callback时返回"
    )
    private String responseCode;

    @Override
    public void validate() {
        AssertUtil.isTrue(StringUtils.isNotBlank(requestCode), "requestCode 参数非法");
        AssertUtil.isTrue(StringUtils.isNotBlank(responseCode), "responseCode 参数非法");
    }

    public VerifyAndBindMobileRequest toThriftRequest() {
        VerifyAndBindMobileRequest request = new VerifyAndBindMobileRequest();
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        User user = identityInfo.getUser();
        request.setTenantId(user.getTenantId());
        request.setAccountId(user.getAccountId());
        request.setRequestCode(requestCode);
        request.setResponseCode(responseCode);
        return request;
    }

}
