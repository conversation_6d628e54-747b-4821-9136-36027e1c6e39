package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.tenant;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * FileName: QueryTenantConfigVO
 * Author:   wangjiawei31
 * Date:     2021/3/8 8:06 下午
 * Description:
 */
@Data
@TypeDoc(
        description = "商户配置查询结果"
)
public class QueryTenantConfigVO {

    @FieldDoc(description = "手动定价")
    private Boolean manualPrice;

    @FieldDoc(description = "无线库存")
    private Boolean infiniteInventory;

    @FieldDoc(description = "商品审核")
    private Boolean productAudit;
}
