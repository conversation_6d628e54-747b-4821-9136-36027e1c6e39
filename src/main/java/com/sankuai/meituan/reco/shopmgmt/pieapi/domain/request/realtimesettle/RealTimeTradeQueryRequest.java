package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.realtimesettle;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.order.platform.common.SelfCheckable;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: <EMAIL>
 * @Date: 2020/3/2 21:28
 * @Description:
 */
@TypeDoc(
        description = "实时结算查询请求"
)
@Data
public class RealTimeTradeQueryRequest implements SelfCheckable {


    @FieldDoc(
            description = "门店ID"
    )
    @NotNull(message = "门店ID不能为空")
    private Long storeId;

    @FieldDoc(
            description = "交易单号"
    )
    @NotNull(message = "交易单号不能为空")
    private String tradeNo;
    
}
