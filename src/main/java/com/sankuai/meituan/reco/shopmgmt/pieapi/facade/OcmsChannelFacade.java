package com.sankuai.meituan.reco.shopmgmt.pieapi.facade;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import com.dianping.cat.util.MetricHelper;
import com.dianping.lion.client.Lion;
import com.meituan.shangou.saas.common.enums.ChannelTypeEnum;
import com.meituan.shangou.saas.dto.StatusCodeEnum;
import com.meituan.shangou.saas.dto.response.PrivacyPhoneResponse;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderModel;
import com.meituan.shangou.saas.o2o.dto.request.BizOrderQueryRequest;
import com.meituan.shangou.saas.o2o.dto.response.BizOrderQueryResponse;
import com.meituan.shangou.saas.o2o.service.BizOrderThriftService;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OcmsOrderDetailReq;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OcmsOrderDetailResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OrderBaseVo;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OrderDetailVo;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OrderOptLogVo;
import com.meituan.shangou.saas.order.management.client.service.online.OcmsOrderSearchService;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.enums.OrderSourceEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiEntityTypeEnum;
import com.sankuai.meituan.common.time.TimeUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ErrorCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.converters.channelorder.ChannelOrderConverter;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order.QueryOrderDetailRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order.QueryVirtualPhoneRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.QueryVirtualPhoneResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ocms.OCMSUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.AuthThriftWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.MtUserWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.PrivacyNumberWrapper;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.AccountInfoVo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.ChannelOrderDetailDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GetChannelOrderDetailRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GetChannelOrderDetailResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GetLogisticsStatusRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.GetLogisticsStatusResult;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.LogisticsStatusDTO;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelOrderDockingThriftService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.QnhOrderDockingThriftService;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.QueryPhoneType;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/***
 * author : <EMAIL>
 * data : 2021/3/16
 * time : 下午4:42
 **/
@Service
@Slf4j
public class OcmsChannelFacade {

    @Resource
    ChannelOrderDockingThriftService.Iface channelOrderDockingThriftService;

    @Resource
    private QnhOrderDockingThriftService.Iface qnhOrderThriftService;

    @Resource
    private BizOrderThriftService bizOrderThriftService;

    @Resource
    private OcmsOrderSearchService ocmsOrderSearchService;

    @Resource
    private AuthThriftWrapper authThriftWrapper;

    @Resource
    private MtUserWrapper mtUserWrapper;

    @Resource
    private PrivacyNumberWrapper privacyNumberWrapper;

    public CommonResponse<QueryVirtualPhoneResponse> queryVirtualPhoneThroughChannel(QueryVirtualPhoneRequest virtualPhoneReq) throws TException {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        long tenantId = user.getTenantId();
        //直接请求channel
        CommonResponse<QueryVirtualPhoneResponse> resp = CommonResponse.fail(ResultCode.FAIL.code, ResultCode.FAIL.getErrorMessage(), null);
        if (virtualPhoneReq.getPhoneType() == QueryPhoneType.CUSTOMER.getValue()){
            // 属于歪马租户 && (全部门店使用AXB || 门店白名单包含当前门店)
            // 使用新逻辑
            if (MccConfigUtil.getAxbPrivacyPhoneTenantIds().contains(String.valueOf(tenantId))
                    && (MccConfigUtil.isAllUserAxBPrivacyPhone()
                    || MccConfigUtil.getAxBPrivacyPhonePoiIds().contains(String.valueOf(virtualPhoneReq.getStoreId()))) ) {
                callCustomerThroughAxB(virtualPhoneReq, tenantId, resp);
                // 否则使用老逻辑
            } else {
                callCustomerThroughChannel(virtualPhoneReq, tenantId, resp);
            }
        }else if (virtualPhoneReq.getPhoneType() == QueryPhoneType.RIDER.getValue()){
            Integer orderBizType = ChannelOrderConverter.convertChannelId2OrderBizType(virtualPhoneReq.getChannelId());
            //请求渠道网关，获取配送订单
            MetricHelper.build().name("ocms.query_virtual_phone.query").tag("type", "rider").tag("tenantId", String.valueOf(tenantId)).tag("channel", String.valueOf(virtualPhoneReq.getChannelId())).count();
            LogisticsStatusDTO logisticsStatusDTO = queryLogisticsStatus(virtualPhoneReq.getChannelOrderId(), DynamicChannelType.findOf(virtualPhoneReq.getChannelId()), tenantId);
            log.info("查询线上骑手电话,order:{},phone:{}", virtualPhoneReq.getChannelOrderId(), logisticsStatusDTO);
            String phone = null;
            if (logisticsStatusDTO != null){
                phone = logisticsStatusDTO.getRiderPhone();
            } else {
                //牵牛花/京东查不到配送状态，查询订单中的骑手信息
                QueryOrderDetailRequest queryOrderDetailRequest = new QueryOrderDetailRequest();
                queryOrderDetailRequest.setChannelOrderId(virtualPhoneReq.getChannelOrderId());
                queryOrderDetailRequest.setChannelId(virtualPhoneReq.getChannelId());
                BizOrderModel bizOrderModel = queryOfflineOrderModel(orderBizType, virtualPhoneReq.getChannelOrderId(), tenantId);
                phone = bizOrderModel.getDeliveryModel().getRiderPhone();
            }
            log.info("骑手电话,order:{},phone:{}", virtualPhoneReq.getChannelOrderId(), phone);
            if (StringUtils.isNotBlank(phone) && !OCMSUtils.isPhoneHasMusk(phone)){
                QueryVirtualPhoneResponse virtualPhoneResponse = new QueryVirtualPhoneResponse();
                virtualPhoneResponse.setPhoneNo(phone);
                resp = CommonResponse.success(virtualPhoneResponse);
                MetricHelper.build().name("ocms.query_virtual_phone.suc").tag("type", "rider").tag("tenantId", String.valueOf(tenantId)).tag("channel", String.valueOf(virtualPhoneReq.getChannelId())).count();
            }

            //如果是接入第三方配送订单，尝试直接从配送信息中获取
            if (resp.getData() == null || StringUtils.isEmpty(resp.getData().getPhoneNo())) {
                try {
                    BizOrderModel bizOrderModel = queryOfflineOrderModel(orderBizType, virtualPhoneReq.getChannelOrderId(), tenantId);
                    //接入了第三方配送的订单
                    if (bizOrderModel != null && bizOrderModel.getDeliveryModel() != null && StringUtils.isNotEmpty(bizOrderModel.getDeliveryModel().getChannelDeliveryId()) &&
                            StringUtils.isNotEmpty(bizOrderModel.getDeliveryModel().getRiderPhone())) {
                        log.info("此单为接入第三方配送订单");
                        QueryVirtualPhoneResponse virtualPhoneResponse = new QueryVirtualPhoneResponse();
                        virtualPhoneResponse.setPhoneNo(phone);
                        resp = CommonResponse.success(virtualPhoneResponse);
                        log.info("此单为接入第三方配送订单 返回:{}", resp);
                        MetricHelper.build().name("ocms.query_virtual_phone.suc").tag("type", "rider").tag("tenantId", String.valueOf(tenantId)).tag("channel", String.valueOf(virtualPhoneReq.getChannelId())).count();
                        return resp;
                    }
                } catch (Exception e) {
                    log.warn("查询线下订单信息失败", e);
                }
            }
        }
        log.info("[订单]查询手机号,req：{}, resp:{}", virtualPhoneReq, resp);
        return resp;
    }

    /**
     * 从ocms channel获取拨打号码
     * @param virtualPhoneReq 获取虚拟号请求体
     * @param tenantId 租户id
     * @param resp 请求体
     * @throws TException
     */
    private void callCustomerThroughChannel(QueryVirtualPhoneRequest virtualPhoneReq, long tenantId,
                                            CommonResponse<QueryVirtualPhoneResponse> resp) throws TException {
        // 共享仓模式清除门店id
        if(Objects.equals(virtualPhoneReq.getEntityType(), PoiEntityTypeEnum.SHAREABLE_WAREHOUSE.code())){
            virtualPhoneReq.setStoreId(null);
        }
        MetricHelper.build().name("ocms.query_virtual_phone.query").tag("type", "customer").tag("tenantId", String.valueOf(tenantId)).tag("channel", String.valueOf(virtualPhoneReq.getChannelId())).count();
        ChannelOrderDetailDTO channelOrderDetailDTO = queryOnlineOrderDetail(virtualPhoneReq.getChannelOrderId(), DynamicChannelType.findOf(virtualPhoneReq.getChannelId()), tenantId, virtualPhoneReq.getStoreId());
        if (channelOrderDetailDTO != null && channelOrderDetailDTO.getDeliveryDetail().isUserPhoneIsValid()
                && !OCMSUtils.isPhoneHasMusk(channelOrderDetailDTO.getDeliveryDetail().getUserPhone())){
            QueryVirtualPhoneResponse virtualPhoneResponse = new QueryVirtualPhoneResponse();
            virtualPhoneResponse.setPhoneNo(channelOrderDetailDTO.getDeliveryDetail().getUserPhone());
            // 如果是歪马渠道 && 使用了隐私号 && 当前城市隐私号未降级
            if (virtualPhoneReq.getChannelId().equals(ChannelTypeEnum.MT_DRUNK_HORSE.getValue())
                    && channelOrderDetailDTO.getDeliveryDetail().getUsePrivacyPhone() == NumberUtils.INTEGER_ONE
                    && StringUtils.isNotBlank(channelOrderDetailDTO.getDeliveryDetail().getUserPrivacyPhone())
                    && !channelOrderDetailDTO.isCityPrivacyDegrade()) {
                virtualPhoneResponse.setPhoneNo(channelOrderDetailDTO.getDeliveryDetail().getUserPrivacyPhone());
            }
            if (CollectionUtils.isNotEmpty(channelOrderDetailDTO.getDeliveryDetail().getBackUpUserPrivacyPhone())) {
                virtualPhoneResponse.setBackUpPhoneNo(channelOrderDetailDTO.getDeliveryDetail().getBackUpUserPrivacyPhone());
            }
            resp.setCode(ErrorCodeEnum.SUCCESS.getCode());
            resp.setData(virtualPhoneResponse);
            MetricHelper.build().name("ocms.query_virtual_phone.suc").tag("type", "customer").tag("tenantId", String.valueOf(tenantId)).tag("channel", String.valueOf(virtualPhoneReq.getChannelId())).count();
        }
    }

    /**
     * 获取axb隐私号拨打号码
     * @param virtualPhoneReq 获取虚拟号请求体
     * @param tenantId 租户id
     * @param resp 请求体
     */
    private void callCustomerThroughAxB(QueryVirtualPhoneRequest virtualPhoneReq, long tenantId,
                                        CommonResponse<QueryVirtualPhoneResponse> resp) throws TException {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        OrderDetailVo orderDetail = getOrderDetailFromMng(virtualPhoneReq, tenantId, user);
        if (orderDetail == null) {
            throw new BizException("获取订单详情失败");
        }

        // 判断订单是否超过终态指定长度，若是不能联系用户
        if (!isOrderCanCallUser(orderDetail.getOrderOpLogList())) {
            resp.setCode(ErrorCodeEnum.ORDER_CANT_BE_CONTACT.getCode());
            resp.setMessage(ErrorCodeEnum.ORDER_CANT_BE_CONTACT.getMessage());
            return;
        }

        // 如果没有使用隐私号，直接返回真实手机号
        OrderBaseVo orderBase = orderDetail.getOrderBaseDto();
        if (NumberUtils.INTEGER_ZERO.equals(orderBase.getUsePrivacyPhone())) {
            // 歪马订单，没有选中隐私号保护的订单，订单完成后24小时后隐藏手机号
            if (MccConfigUtil.isDrunkHorseTenant(tenantId) && isOrderCompleteOverTime(orderBase)){
                resp.setCode(ErrorCodeEnum.ORDER_CANT_BE_CONTACT.getCode());
                resp.setMessage(ErrorCodeEnum.ORDER_CANT_BE_CONTACT.getMessage());
                return;
            }
            QueryVirtualPhoneResponse virtualPhoneResponse = new QueryVirtualPhoneResponse();
            virtualPhoneResponse.setPhoneNo(orderBase.getReceiverPhone());
            resp.setCode(ErrorCodeEnum.SUCCESS.getCode());
            resp.setData(virtualPhoneResponse);
            return;
        }

        // 使用了隐私号，去获取真实手机号
        String receiverPhone = null;
        if (virtualPhoneReq.getChannelId() == ChannelTypeEnum.MT_DRUNK_HORSE.getValue()) {
            receiverPhone = orderBase.getReceiverPhone();
        } else if (virtualPhoneReq.getChannelId() == ChannelTypeEnum.MEITUAN.getValue()) {
            receiverPhone = orderBase.getReceiverPrivacyPhone();
        }
        if (StringUtils.isBlank(receiverPhone)) {
            throw new BizException("订单收货号码为空");
        }
        // 获取百川登录账号手机号
        AccountInfoVo accountInfo = authThriftWrapper.getCurrentAccountWithoutPermission();
        if (accountInfo == null || StringUtils.isBlank(accountInfo.getMobile())) {
            throw new BizException("获取登录账号信息失败--账号或其手机号为空");
        }
        QueryVirtualPhoneResponse data = new QueryVirtualPhoneResponse();
        PrivacyPhoneResponse riderReceiverResponse = privacyNumberWrapper.applyAxB(orderBase, accountInfo.getMobile(), receiverPhone);
        // 获取隐私号失败--使用原逻辑返回拨打号码
        if (!riderReceiverResponse.getStatus().code.equals(StatusCodeEnum.SUCCESS.getCode())) {
            callCustomerThroughChannel(virtualPhoneReq, tenantId, resp);
            return;
        } else {
            data.setPhoneNo(riderReceiverResponse.getPrivacyPhone());
            data.setBackUpPhoneNo(riderReceiverResponse.getBackupPrivacyPhones());
        }
        String mtBindPhone = mtUserWrapper.getMtUserInfoByUserId(orderBase.getUserId()).getPhone();
        if (!receiverPhone.equals(mtBindPhone)) {
            // 调用AXB获取备用隐私号
            PrivacyPhoneResponse riderBindPhoneResponse = privacyNumberWrapper.applyAxB(orderBase, accountInfo.getMobile(), mtBindPhone);
            // 调用成功
            if (riderBindPhoneResponse.getStatus().code.equals(StatusCodeEnum.SUCCESS.getCode())) {
                data.setBindPhoneNo(riderBindPhoneResponse.getPrivacyPhone());
                data.setBindBackupPhoneNo(riderBindPhoneResponse.getBackupPrivacyPhones());
            }
        }
        resp.setCode(ErrorCodeEnum.SUCCESS.getCode());
        resp.setData(data);
    }

    private boolean isOrderCompleteOverTime(OrderBaseVo orderBase) {
        Integer orderStatus = orderBase.getOrderStatus();
        if (Objects.equals(OrderStatusEnum.COMPLETED.getValue(), orderStatus) || Objects.equals(OrderStatusEnum.CANCELED.getValue(), orderStatus)){
            Long completeTime = Objects.equals(OrderStatusEnum.COMPLETED.getValue(), orderStatus) && orderBase.getCompleteTime() != null
                    ? orderBase.getCompleteTime() : orderBase.getUpdateTime();
            int thresholdHour = Lion.getConfigRepository().getIntValue("drunkhorse.private.phone.show.hour", 24);
            long now = System.currentTimeMillis();
            return completeTime != null && TimeUnit.MILLISECONDS.toHours( now - completeTime) >= thresholdHour;
        }
        return false;
    }

    /**
     * 从ordermng获取订单详情
     * @param virtualPhoneReq 请求
     * @param tenantId 租户ID
     * @param user 当前登录用户
     */
    private OrderDetailVo getOrderDetailFromMng(QueryVirtualPhoneRequest virtualPhoneReq, long tenantId, User user) {
        OcmsOrderDetailReq orderDetailReq = new OcmsOrderDetailReq();
        orderDetailReq.setChannelId(virtualPhoneReq.getChannelId());
        orderDetailReq.setTenantId(tenantId);
        orderDetailReq.setChannelOrderId(virtualPhoneReq.getChannelOrderId());
        orderDetailReq.setOperator(user.getAccountId());
        try {
            OcmsOrderDetailResponse orderDetailResponse = ocmsOrderSearchService.orderDetail(orderDetailReq);
            if (orderDetailResponse.getResponseStatus() != 0) {
                log.warn("get orderDetail from ordermng fail, channelOrderId:{}, tenantId:{}, channelId:{}, reason:{}",
                        virtualPhoneReq.getChannelOrderId(), tenantId, virtualPhoneReq.getChannelId(), orderDetailResponse.getMsg());
                return null;
            }
            return orderDetailResponse.order;
        } catch (Exception ex) {
            log.warn("get orderDetail from ordermng fail, channelOrderId:{}, tenantId:{}, channelId:{}, reason:{}",
                    virtualPhoneReq.getChannelOrderId(), tenantId, virtualPhoneReq.getChannelId(), ex.getMessage());
            return null;
        }
    }


    /**
     * 判断订单是否没超过联系期限
     *
     * @param orderOpLogList 订单状态流转
     * @return true-未超过；false-超过
     */
    private boolean isOrderCanCallUser(List<OrderOptLogVo> orderOpLogList) {
        // 若为空，肯定未超过联系期限
        if (CollectionUtils.isEmpty(orderOpLogList)) {
            return true;
        }
        // 获取订单取消/完成时间
        long cancelTime = 0, doneTime = 0;
        for (OrderOptLogVo ele: orderOpLogList) {
            if (OrderStatusEnum.CANCELED.getDesc().equals(ele.getOptContent())) {
                cancelTime = ele.getOptTime() / 1000;
            } else if (OrderStatusEnum.COMPLETED.getDesc().equals(ele.getOptContent())) {
                doneTime = ele.getOptTime() / 1000;
            }
        }

        long currTime = System.currentTimeMillis() / 1000;
        // 取消时间距离当前超过7天
        if (cancelTime != 0 && (currTime - cancelTime) > MccConfigUtil.contactUserDuration()) {
            return false;
        }
        // 完成时间距离当前超过7天
        return doneTime == 0 || (currTime - doneTime) <= MccConfigUtil.contactUserDuration();
    }

    public BizOrderModel queryOfflineOrderModel(Integer orderBizType, String channelOrderId, Long tenantId)throws TException{
        BizOrderQueryRequest queryRequest = new BizOrderQueryRequest();
        queryRequest.setOrderBizType(orderBizType);
        queryRequest.setTenantId(tenantId);
        queryRequest.setOrderSource(OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue());
        queryRequest.setViewOrderId(channelOrderId);
        BizOrderQueryResponse response = bizOrderThriftService.query(queryRequest);
        if (response == null || response.getBizOrderModel() == null || response.getBizOrderModel().getOrderStatus() == null) {
            throw new TException("查询订单失败,order:" + channelOrderId + ",渠道:" + orderBizType);
        }
        return  response.getBizOrderModel();
    }


    public ChannelOrderDetailDTO queryOnlineOrderDetail(String channelOrderId, DynamicChannelType channelType, long tenantId, Long shopId) throws TException {
        GetChannelOrderDetailRequest request = new GetChannelOrderDetailRequest();
        request.setChannelId(channelType.getChannelId());
        request.setOrderId(channelOrderId);
        request.setTenantId(tenantId);
        BizOrderModel orderModel = queryOfflineOrderModel(ChannelOrderConverter.convertChannelId2OrderBizType(channelType.getChannelId()), channelOrderId, tenantId);
        if (shopId != null) {
            request.setSotreId(shopId);
        } else {
            request.setSotreId(orderModel.getShopId());
        }
        boolean ocmsChannel = !Integer.valueOf(OrderSourceEnum.GLORY.getValue()).equals(orderModel.getOrderSource());
        GetChannelOrderDetailResult result = ocmsChannel ? channelOrderDockingThriftService.getChannelOrderDetail(request)
                : qnhOrderThriftService.getChannelOrderDetail(request);
        log.info("查询渠道订单详情：request={},result={}", request, result);

        if (result.getChannelOrderDetail() == null)
            throw new TException("查询线上订单信息失败,orderId:" + channelOrderId + ",渠道:" + channelType.getChannelId());
        return result.getChannelOrderDetail();
    }

    public LogisticsStatusDTO queryLogisticsStatus(String channelOrderId, DynamicChannelType channelType, long tenantId) throws TException {
        BizOrderModel orderModel = queryOfflineOrderModel(ChannelOrderConverter.convertChannelId2OrderBizType(channelType.getChannelId()), channelOrderId, tenantId);
        if(Integer.valueOf(OrderSourceEnum.GLORY.getValue()).equals(orderModel.getOrderSource())){
            // 牵牛花订单详情不返回骑手信息、需要查询线下订单获取
            LogisticsStatusDTO logisticsStatusDTO = new LogisticsStatusDTO();
            logisticsStatusDTO.setOrderId(orderModel.getViewOrderId());
            logisticsStatusDTO.setStatus(orderModel.getDeliveryModel().getDeliveryStatus());
            logisticsStatusDTO.setRiderPhone(orderModel.getDeliveryModel().getRiderPhone());
            logisticsStatusDTO.setRiderName(orderModel.getDeliveryModel().getRiderName());
            return logisticsStatusDTO;
        }
        GetLogisticsStatusRequest request = new GetLogisticsStatusRequest();
        request.setChannelId(channelType.getChannelId());
        request.setOrderId(channelOrderId);
        request.setTenantId(tenantId);
        request.setStoreId(orderModel.getShopId());
        GetLogisticsStatusResult result = channelOrderDockingThriftService.getLogisticsStatus(request);
        log.info("查询渠道配送状态：request={},result={}", request, result);

        return result.getLogisticsStatus();
    }
}
