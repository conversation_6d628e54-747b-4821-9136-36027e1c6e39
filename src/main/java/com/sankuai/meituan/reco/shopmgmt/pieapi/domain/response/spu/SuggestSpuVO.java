package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.WeightTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.BrandDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.CategoryDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelCategoryDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.SuggestStoreSpuDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wanghongzhen
 * @date: 2020-05-11 15:52
 */
@TypeDoc(
        description = "门店商品明细"
)
@Data
@ApiModel("门店商品明细")
public class SuggestSpuVO {
    @FieldDoc(
            description = "租户商品名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "租户商品名称")
    private String tenantName;
    @FieldDoc(
            description = "区域商品名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "区域商品名称")
    private String regionName;
    @FieldDoc(
            description = "商品spuId", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品spu")
    private String spuId;
    @FieldDoc(
            description = "带入总部商品图片地址", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "租户SPU图片列表")
    private List<String> tenantImageUrls;
    @FieldDoc(
            description = "带入城市商品图片地址", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "城市SPU图片列表")
    private List<String> regionImageUrls;
    @FieldDoc(
            description = "产地", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "产地")
    private String producingPlace;
    @FieldDoc(
            description = "商品分类", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "分类")
    private CategoryDTO category;
    @FieldDoc(
            description = "带入商品品牌", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "品牌")
    private BrandDTO brand;
    @FieldDoc(
            description = "门店商品是否已经存在", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店商品是否已经存在")
    private Boolean isExistInStore;
    @FieldDoc(
            description = "称重属性"
    )
    @ApiModelProperty(name = "称重属性")
    private Integer standerType;
    @FieldDoc(
            description = "美团渠道类目信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "分类")
    private ChannelCategoryDTO mtChannelCategory;

    public SuggestSpuVO(SuggestStoreSpuDTO suggestStoreSpuDTO) {
        this.tenantName = suggestStoreSpuDTO.getTenantSpuName();
        this.regionName = suggestStoreSpuDTO.getRegionSpuName();
        this.spuId = suggestStoreSpuDTO.getSpuId();
        this.producingPlace = suggestStoreSpuDTO.getProducingPlace();
        this.tenantImageUrls = suggestStoreSpuDTO.getTenantSpuImageUrls();
        this.regionImageUrls = suggestStoreSpuDTO.getRegionSpuImageUrls();
        this.category = suggestStoreSpuDTO.getCategory();
        this.brand = suggestStoreSpuDTO.getBrand();
        toStanderType(suggestStoreSpuDTO.getWeightType());
        this.isExistInStore = suggestStoreSpuDTO.getIsExistInStore();
        this.mtChannelCategory = suggestStoreSpuDTO.getMtChannelCategory();
    }

    /**
     * 初始化称重类型
     * @param weightType
     */
    protected void toStanderType(int weightType) {
        WeightTypeEnum weightTypeEnum = WeightTypeEnum.getByCode(weightType);
        if (weightTypeEnum != null) {
            this.setStanderType(weightTypeEnum.toStanderType().getCode());
        }
    }
}
