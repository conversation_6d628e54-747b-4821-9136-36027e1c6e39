package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.delivery.DeliveryRedirectModuleVo;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.exception.DeliveryExceptionSummaryVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

import lombok.Data;

/**
 * 订单信息
 *
 * <AUTHOR>
 * @since 2019/11/18
 */
@TypeDoc(
        description = "订单信息"
)
@ApiModel("订单信息")
@Data
public class OrderVO {
    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "租户ID", required = true)
    private Long tenantId;

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道ID", required = true)
    private Integer channelId;

    @FieldDoc(
            description = "渠道名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道名称", required = true)
    private String channelName;

    @FieldDoc(
            description = "仓ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "仓ID", required = true)
    private Long storeId;

    @FieldDoc(
            description = "仓名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "仓名称", required = true)
    private String storeName;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道订单号", required = true)
    private String channelOrderId;

    @FieldDoc(
            description = "订单用户ID、0或-1为无效ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "订单用户ID、0或-1为无效ID")
    private Long userId;

    @FieldDoc(
            description = "订单流水", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单流水", required = true)
    private Long serialNo;

    @FieldDoc(
            description = "商品总数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "商品总数量", required = true)
    private Integer itemCount;

    @FieldDoc(
            description = "订单实付金额  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单实付金额  单位:分", required = true)
    private Integer actualPayAmt;

    @FieldDoc(
            description = "商家实收金额  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "商家实收金额   单位:分", required = true)
    private Integer bizReceiveAmt;

    @FieldDoc(
            description = "配送状态，走的es查询的旧的配送状态", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "配送状态", required = true)
    private Integer distributeStatus;

    @FieldDoc(
            description = "配送状态描述", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "配送状态描述", required = true)
    private String distributeStatusDesc;

    //对接聚合运力平台需求，添加的赋能订单id，为了在发配送时可以回传给后端进行发配送；
    // 可能为空，前端会进行兼容
    @FieldDoc(
            description = "赋能订单id", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "赋能订单id", required = false)
    private Long empowerOrderId;

    //对接聚合运力平台需求，添加的配送信息字段，字段可能为空，前端会进行兼容 ---start

    @FieldDoc(
            description = "配送状态，对接聚合运力平台后，从tms取的实时配送状态", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "配送状态", required = true)
    private Integer realDistributeStatus;

    @FieldDoc(
            description = "取消状态，目前用于是否在取消中", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "配送状态", required = true)
    private Integer cancelStatus;

    @FieldDoc(
            description = "配送运力渠道", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "配送运力渠道", required = false)
    private String deliveryChannel;

    @FieldDoc(
            description = "配送运力渠道-ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "配送运力渠道-ID", required = false)
    private Integer deliveryChannelId;

    @FieldDoc(
            description = "迁移订单", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "迁移订单", required = false)
    private Integer migrateFlag;


    @FieldDoc(
            description = "配送类型 平台：0 三方：1 自配送：2 其他：-1"
    )
    @ApiModelProperty(value = "配送类型 平台：0 三方：1 自配送：2 其他：-1")
    private Integer deliveryChannelType;


    @FieldDoc(
            description = "配送可操作按钮；101-发三方配送，102-重发配送，103-商家自配送", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "配送可操作按钮", required = false)
    private Integer deliveryOperateItem;

    @FieldDoc(
            description = "配送异常描述", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "配送异常描述", required = false)
    private String deliveryExceptionDesc;

    @FieldDoc(
            description = "配送异常类型编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "配送异常类型编码", required = false)
    private Integer deliveryExceptionType;

    @FieldDoc(
            description = "配送距离，单位米", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "配送距离", required = false)
    private Double deliveryDistance;

    @FieldDoc(
            description = "配送费用，单位元", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "配送费用", required = false)
    private Double deliveryFee;

    @FieldDoc(
            description = "进入当前配送状态的毫秒时间戳，目前只表示等待骑手分配开始时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "进入当前配送状态的毫秒时间戳，目前只表示等待骑手分配开始时间", required = false)
    private Long currentDeliveryStatusStartTime;

    @FieldDoc(
            description = "配送信息来源，0-订单，1-tms", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "配送信息来源", required = false)
    private int deliveryInfoSource;

    //对接聚合运力平台需求，添加的配送信息字段---end
    @FieldDoc(
            description = "配送方式", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "配送方式", required = true)
    private Integer deliveryMethod;

    @FieldDoc(
            description = "是否自配送，0-平台配，1-自配送", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "配送方式", required = true)
    private Integer selfDelivery;

    @FieldDoc(
            description = "是否支持骑手电话使用隐私号"
    )
    @ApiModelProperty(value = "是否支持骑手电话使用隐私号")
    private Boolean supportDeliveryUserPrivacyPhone;

    @FieldDoc(
            description = "订单来源", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单来源", required = true)
    private Integer orderSource;

    @FieldDoc(
            description = "配送方式描述", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "配送方式描述", required = true)
    private String deliveryMethodDesc;

    @FieldDoc(
            description = "自提码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "自提码", required = false)
    private String selfFetchCode;

    @FieldDoc(
            description = "自提状态", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "自提状态", required = false)
    private Integer selfFetchStatus;

    @FieldDoc(
            description = "配送人姓名", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "配送人姓名", required = true)
    private String deliveryUserName;

    @FieldDoc(
            description = "配送人电话", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "配送人电话", required = true)
    private String deliveryUserPhone;

    @FieldDoc(
            description = "收货人姓名", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "收货人姓名", required = true)
    private String receiverName;

    @FieldDoc(
            description = "收货人电话号码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "收货人电话号码", required = true)
    private String receiverPhone;

    @FieldDoc(
            description = "收货人地址", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "收货人地址", required = true)
    private String receiverAddress;

    @FieldDoc(
            description = "订单状态", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单状态", required = true)
    private Integer orderStatus;

    @FieldDoc(
            description = "订单状态描述", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单状态描述", required = true)
    private String orderStatusDesc;

    @FieldDoc(
            description = "订单创建时间戳", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单创建时间戳", required = true)
    private Long createTime;


    @FieldDoc(
            description = "配送订单类型  1-立即单 2-预约单", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "配送订单类型   1-立即单 2-预约单", required = true)
    private Integer deliveryOrderType;

    @FieldDoc(
            description = "配送订单类型名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "配送订单类型名称", required = true)
    private String deliveryOrderTypeName;

    @FieldDoc(
            description = "预计送达时间开始时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "预计送达时间开始时间", required = true)
    private Long estimateArriveTimeStart;

    @FieldDoc(
            description = "预计送达时间截止时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "预计送达时间截止时间", required = true)
    private Long estimateArriveTimeEnd;

    @FieldDoc(
            description = "拣货状态", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "拣货状态", required = true)
    private Integer pickStatus;

    @FieldDoc(
            description = "拣货完成时间戳", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "拣货完成时间戳", required = true)
    private Long pickCompleteTime;

    @FieldDoc(
            description = "订单更新时间戳", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单更新时间戳", required = true)
    private Long updateTime;

    @FieldDoc(
            description = "渠道第二订单号 饿百渠道的饿了么订单号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "渠道第二订单号 饿百渠道的饿了么订单号", required = false)
    private String channelExtraOrderId;

    @FieldDoc(
            description = "备注", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "备注", required = false)
    private String comments;

    @FieldDoc(
            description = "商品信息列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "商品信息列表", required = true)
    private List<ProductVO> productList;

    @FieldDoc(
            description = "提报价模式,订单线下价格总和", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "提报价模式,订单线下价格总和", required = false)
    private Integer totalOfflinePrice;


    @FieldDoc(
            description = "赠品信息列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "赠品信息列表", required = false)
    private List<GiftVO> giftVOList;

    @FieldDoc(
            description = "赠品总数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "赠品总数量", required = false)
    private Integer giftCount;

    @FieldDoc(
            description = "订单打印状态，只有待拣货页面才不为空", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "订单打印状态", required = false)
    private OrderPrintStatusVo printStatus;

    @FieldDoc(
            description = "待处理退款信息及退款日志", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "待处理退款信息及退款日志", required = false)
    private OrderRefundInfo orderRefundInfo;


    @FieldDoc(
            description = "订单可操作列表：10-接单操作，20-完成拣货，30-补打小票，40-全单退款，50-部分退款，60-收到退货 ，70-退差价", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "订单可操作列表：10-接单操作，20-完成拣货，30-补打小票，40-全单退款，50-部分退款，60-收到退货，70-退差价", required = false)
    private List<Integer> orderCouldOperateItems;

    @FieldDoc(
            description = "订单营收数据", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "订单营收数据")
    private RevenueDetailVo revenueDetail;

    @FieldDoc(
            description = "用户标签信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "用户标签信息")
    private List<TagInfoVO> userTags;

    @FieldDoc(
            description = "聚合运力平台code", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "聚合运力平台code")
    private Integer deliveryPlatformCode;

    @FieldDoc(
            description = "聚合运力配送模块跳转信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "聚合运力配送模块跳转信息")
    private DeliveryRedirectModuleVo deliveryRedirectModule;

    @FieldDoc(
            description = "支付时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "支付时间", required = true)
    private Long payTime;

    @FieldDoc(
            description = "是否开发票", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否开发票")
    private Integer isNeedInvoice;

    @FieldDoc(
            description = "行为标签", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "行为标签")
    private List<TagInfoVO> actionTagList;

    @FieldDoc(
            description = "展示状态", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "展示状态(1 待商家接单\n" +
            "2 待拣货\n" +
            "3 待发单\n" +
            "4 待接单\n" +
            "5 待到店\n" +
            "6 待取货\n" +
            "7 配送中\n" +
            "8 未接单\n" +
            "9 未到店\n" +
            "10 未取货\n" +
            "11 配送超时\n" +
            "12 顾客申请退款（整单）\n" +
            "13 顾客申请退款（部分）)"+
            "14 系统异常\n")
    private Integer viewStatus;

    @FieldDoc(
            description = "运单状态变更时间",
            requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "运单状态变更时间")
    public Long deliveryStatusChangeTime;

    @FieldDoc(
            description = "几次配送",
            requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "几次配送")
    public Integer deliveryCount;

    @FieldDoc(
            description = "仓库id",
            requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "仓库id")
    private Long warehouseId;

    @FieldDoc(
            description = "仓库名",
            requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "仓库名")
    private String warehouseName;

    @FieldDoc(
            description = "运单状态是否被锁定：0-未锁定；1-锁定",
            example = {}
    )
    @ApiModelProperty(value = "运单状态是否被锁定：0-未锁定；1-锁定")
    public Integer deliveryStatusLocked;


    @FieldDoc(
            description = "骑手上报异常",
            example = {}
    )
    @ApiModelProperty(value = "骑手上报异常")
    public DeliveryExceptionSummaryVO deliveryExceptionSummaryVOS;

    @FieldDoc(
            description = "是否含有可退商品"
    )
    @ApiModelProperty(value = "是否含有可退商品")
    public Integer isHasCanRefundGoods;


    @FieldDoc(
            description = "订单用户类型， "
    )
    @ApiModelProperty(value = "订单用户类型")
    /**
     * @see com.meituan.shangou.saas.order.platform.enums.OrderTypeEnum
     */
    public Integer orderUserType;
    @FieldDoc(
            description = "整单超时",
            example = {}
    )
    @ApiModelProperty(value = "整单超时")
    public String wholeTimeout;

    @FieldDoc(
            description = "配送操作按钮",
            example = {}
    )
    @ApiModelProperty(value = "配送操作按钮")
    public List<Integer> deliveryOperateItems = new ArrayList<>();

    @FieldDoc(
            description = "审核最后时间",
            example = {}
    )
    @ApiModelProperty(value = "审核最后时间")
    public String allowLatestAuditTime;

    @FieldDoc(
            description = "异常code",
            example = {}
    )
    @ApiModelProperty(value = "异常code")
    public Integer exceptionCode;

    @FieldDoc(
            description = "erp门店code",
            example = {}
    )
    @ApiModelProperty(value = "erp门店code")
    public String erpShopCode;

    @FieldDoc(
            description = "歪马地推自提订单标记",
            example = {}
    )
    @ApiModelProperty(value = "歪马地推自提订单标记")
    public Boolean selfPickPullNewOrder;

    @FieldDoc(
            description = "发票抬头", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "发票抬头")
    private String invoiceTitle;

    @FieldDoc(
            description = "发票税号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "发票税号")
    private String invoiceTaxNo;

    @FieldDoc(
            description = "发票类型", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "发票类型")
    private Integer invoiceType;

    @FieldDoc(
            description = "配送承运商",
            example = {}
    )
    @ApiModelProperty(value = "配送承运商")
    public String deliveryPlatform;

    @FieldDoc(
            description = "转单门店ID"
    )
    @ApiModelProperty(value = "转单门店ID")
    private Long dispatchShopId;

    @FieldDoc(
            description = "转单门店后的订单序列编码"
    )
    @ApiModelProperty(value = "转单门店后的订单序列编码")
    private String dispatchSerialNo;

    @FieldDoc(
            description = "转单门店名称"
    )
    @ApiModelProperty(value = "转单门店名称")
    private String dispatchShopName;

    @FieldDoc(
            description = "转单门店所在租户ID"
    )
    @ApiModelProperty(value = "转单门店所在租户ID")
    private Long dispatchTenantId;

    @FieldDoc(
            description = "转单时间"
    )
    @ApiModelProperty(value = "转单时间")
    private Long dispatchTime;

    @FieldDoc(
            description = "是否是平台配送", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否是平台配送", required = true)
    private boolean isPlatformDelivery;

    @FieldDoc(
            description = "原始配送方式"
    )
    @ApiModelProperty(value = "原始配送方式")
    private Integer originalDistributeType;

    @FieldDoc(
            description = "签收点"
    )
    @ApiModelProperty(value = "签收点")
    private String signingPoint;

    @FieldDoc(
            description = "是否有缺货情况", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否有缺货情况", required = false)
    private Boolean hasLackGoods = false;

    @FieldDoc(
            description = "是否是大范围配送", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否是大范围配送", required = false)
    private Boolean isWiderShippingArea = false;

    @FieldDoc(
            description = "收货人后4位尾号", requiredness = Requiredness.OPTIONAL
    )
    private String receiverTailPhoneNumber;

    @FieldDoc(
            description = "货品项列表",
            example = {}
    )
    private List<GoodsItemVO> goodsItemList;

    @FieldDoc(
            description = "是否需要红酒开瓶器",
            example = {}
    )
    private Boolean needWineBottleOpener;


    @FieldDoc(
            description = "是否为美团名酒馆订单，true：是"
    )
    private Boolean isMtFamousTavern;
}

