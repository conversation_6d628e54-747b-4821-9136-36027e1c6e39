package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.meituan.linz.boot.exception.ServiceRpcException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.management.RevenueOverviewVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.DateUtils;
import com.sankuai.meituan.shangou.empower.settlement.dto.request.revenue.BoothTotalRevenueRequest;
import com.sankuai.meituan.shangou.empower.settlement.dto.response.revenue.BoothTotalRevenueResponse;
import com.sankuai.meituan.shangou.empower.settlement.services.BoothRevenueQueryService;

import lombok.extern.slf4j.Slf4j;

/**
 * 结算系统今日营收
 *
 * <AUTHOR>
 * @since 2021/7/7
 */
@Service
@Slf4j
public class SettlementCurrentRevenueWrapper {

    @Autowired
    private BoothRevenueQueryService settlementBoothRevenueQueryService;

    @Autowired
    private SaasCrmDataWrapper saasCrmDataWrapper;

    /**
     * 总营业额视图code
     */
    private static final String TOTAL_TURNOVER_MODEL_CODE = "real_biz";

    /**
     * 预计收入Id
     */
    private static final int MERCHANT_RECEIVE_AMT_ID = 106;

    /**
     * 查看营收金额、有效订单数
     * 非摊主账号：查看所有摊位的营收金额，摊主账号: 查看摊位的营收金额
     * 营收金额，提报价模式：进货价之和，扣点模式：扣点公式
     *
     * @param tenantId
     * @param storeId
     * @param boothId
     * @return
     */
    public RevenueOverviewVO overview(long tenantId, long storeId, Long boothId) {

        BoothTotalRevenueRequest requestParam = new BoothTotalRevenueRequest();
        requestParam.setTenantId(tenantId);
        requestParam.setShopId(storeId);
        requestParam.setBoothId(boothId);
        String today = DateUtils.format(new Date(), DateUtils.YYYY_MM_DD);
        requestParam.setStartDate(today);
        requestParam.setEndDate(today);

        BoothTotalRevenueResponse response;
        try {
            log.info("查询结算系统摊位营收金额 request:{}", requestParam);
            response = settlementBoothRevenueQueryService.totalRevenue(requestParam);
            log.info("查询结算系统摊位营收金额 response:{}", response);
        } catch (Exception e) {
            log.error("查询结算系统摊位营收金额异常 requestParam:{}", requestParam);
            throw new ServiceRpcException("查询结算系统摊位营收金额异常", e);
        }

        if (response == null || response.getResponseStatus() != 0) {
            log.error("查询结算系统摊位营收金额失败 requestParam:{}", requestParam);
            throw new ServiceRpcException("查询结算系统摊位营收金额失败");
        }

        return buildRevenueOverviewVO(response);
    }

    private RevenueOverviewVO buildRevenueOverviewVO(BoothTotalRevenueResponse response) {
        RevenueOverviewVO boothRevenueOverviewVO = new RevenueOverviewVO();
        boothRevenueOverviewVO.setCompleteOrderCount(response.getCompleteOrderCount());
        boothRevenueOverviewVO.setTotalRefund(response.getRefundAmountLong().intValue());

        boothRevenueOverviewVO.setTotalRevenue(response.getRevenueAmountLong());
        boothRevenueOverviewVO.setRevenueTips(response.getRevenueFormulaTips());
        boothRevenueOverviewVO.setTotalRevenueName("今日营收金额");

        return boothRevenueOverviewVO;
    }

    private Long getMerchantReceiveAmt(long tenantId, long storeId) {
        try {
            Map<Integer, Double> indicatorDataMap = saasCrmDataWrapper.getRealTimeBoxedIndicatorData(tenantId, storeId, TOTAL_TURNOVER_MODEL_CODE);

            if (indicatorDataMap.containsKey(MERCHANT_RECEIVE_AMT_ID)
                    && indicatorDataMap.get(MERCHANT_RECEIVE_AMT_ID) != null ) {
                return BigDecimal.valueOf(indicatorDataMap.get(MERCHANT_RECEIVE_AMT_ID))
                        .multiply(BigDecimal.valueOf(100))
                        .setScale(0, RoundingMode.HALF_UP)
                        .longValue();
            }
        }
        catch (Exception e){
            log.error("获取商家实收金额失败 tenantId:{},storeId:{}", tenantId, storeId, e);
        }
        return null;
    }
}