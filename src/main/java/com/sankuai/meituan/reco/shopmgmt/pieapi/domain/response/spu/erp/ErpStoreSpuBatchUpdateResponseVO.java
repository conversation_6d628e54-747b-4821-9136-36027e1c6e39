package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp;

import java.util.List;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 有ERP门店商品批量设置结果
 *
 * <AUTHOR>
 * @since 2023/05/15
 */
@TypeDoc(
        description = "有ERP门店商品批量设置结果"
)
@Data
@ApiModel("有ERP门店商品批量设置结果")
public class ErpStoreSpuBatchUpdateResponseVO {

    @FieldDoc(description = "操作总数", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "操作总数")
    public Integer totalNum;

    @FieldDoc(description = "成功条数", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "成功条数")
    public Integer successNum;

    @FieldDoc(description = "失败条数", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "失败条数")
    public Integer failNum;

    @FieldDoc(description = "失败具体原因", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "失败具体原因")
    public List<ErpStoreSpuUpdateResponseDetailVO> failList;

}
