package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022-10-19
 * @email <EMAIL>
 */
@TypeDoc(
        description = "规则绑定的员工信息"
)
@ApiModel("规则绑定的员工信息")
@Data
public class RuleEmployeeVO {

    @FieldDoc(
            description = "员工id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "员工id")
    private Long employeeId;

    @FieldDoc(
            description = "员工名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "员工名称")
    private String employeeName;

}
