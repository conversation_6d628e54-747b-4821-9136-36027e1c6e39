package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.google.common.collect.Maps;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.backendcategory.BackendCategoryTreeNodeVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.domain.SkuSortInfo;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.result.SkuSortInfoResult;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.service.EmpowerProductFrontCategoryThriftService;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @createTime 2021/05/31
 * @description
 */
@Slf4j
@Service
public class BackendCategoryWrapper {

    @Autowired
    EmpowerProductFrontCategoryThriftService.Iface tenantSkuSortThriftService;


    public List<BackendCategoryTreeNodeVO> getBackendCategoryList(long tenantId) {
        try {
            SkuSortInfoResult result = tenantSkuSortThriftService.querySkuSortInfo(tenantId, null);
            if (result.getStatus().getCode() == 0) {
                return buildCategoryList(result.getSkuSortInfoList());
            }
        } catch (Exception e) {
            log.error("getBackendCategoryList error", e);
        }
        throw new CommonLogicException("获取后台类目信息异常");

    }

    private List<BackendCategoryTreeNodeVO> buildCategoryList(List<SkuSortInfo> skuSortInfoList) {
        List<BackendCategoryTreeNodeVO> res = Lists.newArrayList();
        if (CollectionUtils.isEmpty(skuSortInfoList)){
            return res;
        }

        Map<String, BackendCategoryTreeNodeVO> categoryVOMap = skuSortInfoList.stream().collect(Collectors.toMap(SkuSortInfo::getSortCode, BackendCategoryTreeNodeVO::new, (k1, k2) -> k1));

        for (BackendCategoryTreeNodeVO categoryVO : categoryVOMap.values()) {
            String parentId = categoryVO.getParentId();

            //parentId不存在或parentId对应类目不存在时，作为一级品类
            if (StringUtils.isEmpty(parentId)) {
                res.add(categoryVO);
            } else {
                BackendCategoryTreeNodeVO parentVO = categoryVOMap.get(parentId);
                if (parentVO == null) {
                    res.add(categoryVO);
                } else {
                    //根据需求，只提供到二级类目为止的数据
                    BackendCategoryTreeNodeVO grandParent = categoryVOMap.get(parentVO.getParentId());
                    if (grandParent == null) {
                        parentVO.addChildren(categoryVO);
                    }
                }
            }
        }

        return res;
    }

}
