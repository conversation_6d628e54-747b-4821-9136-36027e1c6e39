package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.messagetask;
// Copyright (C) 2020 Meituan
// All rights reserved

import com.alibaba.fastjson.JSON;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.saas.task.dto.simple.SimpleTaskTemplateDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TypeDoc(
        description = "消息任务查询结果对象"
)
@ApiModel("消息任务查询结果对象")
@Data
public class MessageTaskQueryVO {

    @FieldDoc(
            description = "跳转链接"
    )
    @ApiModelProperty("跳转链接")
    private String jumpUrl;

    @FieldDoc(
            description = "task业务编码"
    )
    @ApiModelProperty("task业务编码")
    private String taskBizCode;

    @FieldDoc(
            description = "展示名称"
    )
    @ApiModelProperty("展示名称")
    private String displayName;

    @FieldDoc(
            description = "展示类型"
    )
    @ApiModelProperty("展示类型")
    private Integer displayMode;


    @FieldDoc(
            description = "数量"
    )
    @ApiModelProperty("跳转链接")
    private Integer badgeNum;


    public MessageTaskQueryVO(SimpleTaskTemplateDTO template, Integer badgeNum) {
        this.jumpUrl = template.getJumpUrl();
        this.taskBizCode = template.getTaskBizCode();
        this.displayMode = template.getDisplayMode();
        this.displayName = template.getDisplayName();
        this.badgeNum = badgeNum;
    }

    public static void main(String[] args) {
        SimpleTaskTemplateDTO template = new SimpleTaskTemplateDTO();
        template.setJumpUrl("http://www.google.com");
        template.setTaskBizCode("BAIL");
        template.setDisplayName("未缴纳保证金");
        template.setDisplayMode(2);
        MessageTaskQueryVO taskQueryVO = new MessageTaskQueryVO(template, 1);
        System.out.println(JSON.toJSONString(taskQueryVO));
    }
}
