package com.sankuai.meituan.reco.shopmgmt.pieapi.facade;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.meituan.shangou.saas.tenant.thrift.UserThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.user.UserDto;
import com.meituan.shangou.saas.tenant.thrift.dto.user.response.UserListResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.AccountUserDto;
import com.sankuai.meituan.shangou.empower.auth.thrift.service.LoginThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.TokenTypeEmum;
import com.sankuai.meituan.shangou.empower.auth.thrift.service.AccountThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.AccountSessionVO;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryAccountSessionResponse;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.Result;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import com.sankuai.meituan.shangou.empower.auth.thrift.vo.request.QueryAccountSessionReq;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@Rhino
public class AccountService {

    @Resource
    private AccountThriftService.Iface authAccountThriftService;

    @Resource(name = "loginThriftService")
    private LoginThriftService.Iface loginThriftService;

    @Resource
    private UserThriftService userThriftService;

    @Degrade(rhinoKey = "AccountService.queryAccountSession", fallBackMethod = "queryAccountSessionFallback", isDegradeOnException = true)
    public AccountSessionVO queryAccountSession(String token) {
        try {
            QueryAccountSessionReq req = new QueryAccountSessionReq();
            req.setToken(token);
            req.setTokenType(TokenTypeEmum.E_TOKEN.getValue());
            QueryAccountSessionResponse sessionResponse =
                    authAccountThriftService.queryAccountSessionInfo(req);
            if (Objects.isNull(sessionResponse) || sessionResponse.getResult().getCode() != ResultCodeEnum.SUCCESS.getValue()) {
                log.info("根据token 查询账号信息 失败, token:{} , response:{}", token, sessionResponse);
                return null;
            }
            return sessionResponse.getAccountSessionVO();
        } catch (TException e) {
            log.info("AccountService.queryAccountSession 根据token 查询账号信息 失败, token:{}, exception->", token, e);
            return null;
        }
    }

    private AccountSessionVO queryAccountSessionFallback(String token, Throwable throwable) {
        log.info(
                "AccountService.queryAccountSessionFallback 根据token 查询账号信息 失败token:{}, exception->", token,
                throwable);
        return null;
    }

    @Degrade(rhinoKey = "AccountService.clearSession", fallBackMethod = "clearSessionFallback",
            isDegradeOnException = true)
    public boolean clearSession(String token) {
        try {
            Result result = loginThriftService.loginOut(TokenTypeEmum.E_TOKEN.getValue(), token);

            if (Optional.ofNullable(result).map(Result::getCode).orElse(BigInteger.ONE.intValue())
                    != ResultCodeEnum.SUCCESS.getValue()) {
                log.info("AccountService.clearSession 登出失败 token:{}, result->{}", token, result);
                return false;
            }
            return true;
        } catch (TException e) {
            log.info("AccountService.clearSession 登出失败 token:{}, exception->", token, e);
            return false;
        }
    }

    private boolean clearSessionFallback(String token, Throwable throwable) {
        log.info("AccountService.clearSessionFallback 登出失败 token:{}, exception->", token, throwable);
        return false;
    }

    @Degrade(rhinoKey = "AccountService.queryTenantUserByAccountIds", fallBackMethod = "queryTenantUserByAccountIdsFallback",
            isDegradeOnException = true)
    @MethodLog(logRequest = true, logResponse = true)
    public Map<Long, AccountUserDto> queryTenantUserByAccountIds(Long tenantId, List<Long> accountIdList){
        if(CollectionUtils.isEmpty(accountIdList)){
            return Collections.emptyMap();
        }

        try {

            UserListResponse userListResponse = userThriftService.queryTenantUserByAccountIds(tenantId,accountIdList);
            if(userListResponse == null || CollectionUtils.isEmpty(userListResponse.getUserList())){
                return Collections.emptyMap();
            }

            return userListResponse.getUserList().stream().collect(Collectors.toMap(UserDto::getAccountId,
                    it -> new AccountUserDto(it.getAccountId(), it.getEmpName(), it.getEmpId(), it.getAccountName()), (a, b) -> b));
        }catch (Exception e){
            log.error("queryTenantUserByAccountIds error",e);
        }
        return Collections.emptyMap();
    }

    private Map<Long, AccountUserDto> queryTenantUserByAccountIdsFallback(Long tenantId, List<Long> accountIdList){
        log.info("queryTenantUserByAccountIdsFallback tenantId:{},accountIdList:{}",tenantId,accountIdList);
        return Collections.emptyMap();
    }


}
