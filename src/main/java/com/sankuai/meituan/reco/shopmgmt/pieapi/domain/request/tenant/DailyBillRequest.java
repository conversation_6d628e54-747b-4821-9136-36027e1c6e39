package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.tenant;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;


@ApiModel(value = "查询日账单明细请求")
@Setter
@Getter
@ToString
public class DailyBillRequest {


    @FieldDoc(
            description = "账单ID列表"
    )
    @ApiModelProperty(value = "账单ID列表")
    private List<Long> billIdList;

    @FieldDoc(
            description = "账单开始日期（包含当天），格式 yyyy.MM.dd  参数2：账单结束日期（包含当天）"
    )
    @ApiModelProperty(value = "账单开始日期（包含当天），格式 yyyy.MM.dd  参数2：账单结束日期（包含当天）")
    private List<String> billDateRange;

    @FieldDoc(
            description = "账单状态列表"
    )
    @ApiModelProperty(value = "账单状态列表")
    private List<Integer> billStatusList;

    @FieldDoc(
            description = "账单类型"
    )
    @ApiModelProperty(value = "账单类型")
    private Integer billType;


    @FieldDoc(
            description = "查询页号"
    )
    @ApiModelProperty(value = "查询页号")
    private Integer page;

    @FieldDoc(
            description = "每页记录数"
    )
    @ApiModelProperty(value = "每页记录数")
    private Integer pageSize;

}
