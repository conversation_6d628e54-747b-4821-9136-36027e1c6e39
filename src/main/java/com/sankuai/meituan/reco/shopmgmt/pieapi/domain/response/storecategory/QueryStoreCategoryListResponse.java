package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.storecategory;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/10/30
 */
@TypeDoc(
        description = "牵牛花门店店内分类响应"
)
@ApiModel("牵牛花门店店内分类响应")
@Getter
@Setter
public class QueryStoreCategoryListResponse {

    @FieldDoc(
            description = "牵牛花门店店内分类列表"
    )
    private List<StoreCategoryInfoVO> categories;

}
