// Copyright (C) 2020 Meituan
// All rights reserved
package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.linz.boot.util.Fun;
import com.meituan.linz.product.channel.EnhanceChannelType;
import com.meituan.mtrace.thread.pool.ExecutorServiceTraceWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ChannelTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.FrontCategorySpuConstants;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.SortTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.FieldSort;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.BatchChannelCategoryPropertyRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.ChannelCategoryPropertyRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.ChannelSpecAttrRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.ChannelSpuKeyRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.PageQueryChannelSpuRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.AdventRuleVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelAdventRuleRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelCategoryPropertyVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelSaleAttrValueVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelSaleAttrVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelSpecialAttrVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelSpuBaseInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelSpuPageQueryResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MoneyUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.shangou.empower.price.client.dto.ChannelOnlinePriceDTO;
import com.sankuai.meituan.shangou.empower.price.client.request.ChannelOnlinePriceQueryRequest;
import com.sankuai.meituan.shangou.empower.price.client.response.ChannelOnlinePriceQueryResponse;
import com.sankuai.meituan.shangou.empower.price.client.service.ChannelPriceThriftService;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.ChannelSpuRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.ChannelSpuOperationResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.service.ChannelSpuBizThriftService;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.dto.ChannelCatePropertyDTO;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.request.ChannelCatePropertyRequest;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.request.ChannelSpecialAttrRequest;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.response.ChannelAdventRuleResponse;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.response.ChannelCatePropertyListResponse;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.response.ChannelSpecialAttrResponse;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.service.ChannelCategoryThriftService;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.enums.ResponseCodeEnum;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.product.request.ChannelProductsPushRequest;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.product.response.ChannelProductOperateResponse;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.product.response.ChannelProductPageQueryResponse;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.product.response.ChannelProductsOperateResponse;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.product.service.ChannelProductPublishThriftService;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.product.service.ChannelProductQueryThriftService;
import com.sankuai.meituan.shangou.empower.productplatform.util.SwitchUtils;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.command.MerchantSpuIdListCommand;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.command.PoiSpuIdListCommand;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.domain.ChannelSaleAttributeValueInfo;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.domain.PoiSkuInfo;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.domain.PoiSpuInfo;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.domain.SaleAttributeValueInfo;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.domain.SpuAndSkuSaleAttrInfo;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.result.QueryPoiSpuListResult;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.result.SpuSaleAttrValueResult;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.service.EmpowerMerchantSpuThriftService;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.service.EmpowerPoiSpuThriftService;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.shangou.saas.crm.data.third.client.request.SpuSalesVolumeRequest;
import com.sankuai.meituan.shangou.saas.crm.data.third.client.response.SaasDataCode;
import com.sankuai.meituan.shangou.saas.crm.data.third.client.response.SalesVolumeResponse;
import com.sankuai.meituan.shangou.saas.crm.data.third.client.service.ItemDataThriftService;
import deps.redis.clients.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.OptionalLong;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2020/11/26 下午12:03
 **/
@Service
@Slf4j
public class ChannelSpuWrapper {

    @Resource
    private ItemDataThriftService itemDataThriftService;

    @Resource
    private ChannelProductQueryThriftService channelProductQueryThriftService;

    @Resource
    private EmpowerPoiSpuThriftService.Iface empowerPoiSpuThriftService;

    @Resource
    private ChannelPriceThriftService priceAppChannelPriceThriftService;

    @Resource
    private ChannelProductPublishThriftService channelProductPublishThriftService;

    @Resource
    private ChannelSpuBizThriftService channelSpuBizThriftService;

    @Resource
    private ChannelCategoryThriftService channelCategoryThriftService;

    @Resource
    private EmpowerMerchantSpuThriftService.Iface empowerMerchantSpuThriftService;

    private ExecutorService itemThreadPool = new ExecutorServiceTraceWrapper(
            new ThreadPoolExecutor(5, 5, 10, TimeUnit.SECONDS,
                    new LinkedBlockingQueue<>(5),
                    new ThreadFactoryBuilder().setNameFormat("ItemThreadPool-%d").build(),
                    new ThreadPoolExecutor.CallerRunsPolicy()
            )
    );

    public ChannelSpuPageQueryResponseVO pageQueryChannelSpu(PageQueryChannelSpuRequest request) {
        ChannelProductPageQueryResponse response = null;
        try {
            response = channelProductQueryThriftService.pageQueryByStoreCategoryIds(request
                    .toChannelProductPageQueryByStoreCategoryIdsRequest());
            if (response == null || response.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                throw new IllegalStateException("channelProductQueryThriftService.pageQueryByStoreCategoryIds 返回码失败");
            }
        } catch (Exception e) {
            log.error("channelProductQueryThriftService.pageQueryByStoreCategoryIds error", e);
            throw new IllegalStateException("查询失败");
        }
        ChannelSpuPageQueryResponseVO channelSpuPageQueryResponseVO = new ChannelSpuPageQueryResponseVO(response);
        List<String> spuIds = channelSpuPageQueryResponseVO.getList().stream().map(ChannelSpuBaseInfoVO::getSpuId).collect
                (Collectors.toList());
        fillSpuBaseInfo(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId(), request.getStoreId(), spuIds,
                channelSpuPageQueryResponseVO.getList());
        fillMonthSale(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId(), request.getStoreId(), spuIds,
                channelSpuPageQueryResponseVO.getList());
        return channelSpuPageQueryResponseVO;
    }

    public List<ChannelSpuBaseInfoVO> queryChannelSpuList(
            Long storeId, Integer channelId, Long frontCategoryId, FieldSort fieldSort) {

        Map<String, ChannelSpuBaseInfoVO> spuId2ChannelSpuBaseInfoVOMap = Maps.newHashMap();

        PageQueryChannelSpuRequest pageQuery = new PageQueryChannelSpuRequest();
        pageQuery.setStoreId(storeId);
        pageQuery.setChannelId(channelId);
        pageQuery.setFrontCategoryIds(Lists.newArrayList(frontCategoryId));

        for (int page = 1; page <= FrontCategorySpuConstants.QUERY_CHANNEL_SPU_BY_FRONT_CATEGORY_PAGE_MAX; page++) {

            // 分页查询门店spu信息
            pageQuery.setPage(page);
            pageQuery.setPageSize(FrontCategorySpuConstants.QUERY_CHANNEL_SPU_BY_FRONT_CATEGORY_PAGE_SIZE);
            ChannelSpuPageQueryResponseVO pageResult = pageQueryChannelSpu(pageQuery);

            if (CollectionUtils.isEmpty(pageResult.getList())) {
                break;
            }

            for (ChannelSpuBaseInfoVO vo : pageResult.getList()) {
                spuId2ChannelSpuBaseInfoVOMap.put(vo.getSpuId(), vo);
            }

            // 若本次分页查询已经是最后一页, 则跳出循环; 否则, 查询下一页数据
            PageInfoVO pageInfo = pageResult.getPageInfo();
            if (pageInfo == null || pageInfo.getPage().compareTo(pageInfo.getTotalPage()) >= 0) {
                break;
            }
        }

        // 按照指定字段排序后返回spu列表
        return sortStoreCategorySpuList(Lists.newArrayList(spuId2ChannelSpuBaseInfoVOMap.values()), fieldSort);
    }

    private List<ChannelSpuBaseInfoVO> sortStoreCategorySpuList(List<ChannelSpuBaseInfoVO> channelSpuBaseInfoVOList,
                                                                FieldSort fieldSort) {
        final String sortField = fieldSort.getField();
        final SortTypeEnum sortType = SortTypeEnum.findBySortType(fieldSort.getSortType());

        Collections.sort(channelSpuBaseInfoVOList, (o1, o2) -> {

            if (Objects.equals(FrontCategorySpuConstants.SORT_FIELD_PRICE, sortField)) {
                Long o1Price = o1.getPrice() == null ? Long.MAX_VALUE : o1.getPrice();
                Long o2Price = o2.getPrice() == null ? Long.MAX_VALUE : o2.getPrice();

                if (sortType == SortTypeEnum.ASC) {
                    return o1Price.compareTo(o2Price);
                }
                if (sortType == SortTypeEnum.DESC) {
                    return o2Price.compareTo(o1Price);
                }
            }

            if (Objects.equals(FrontCategorySpuConstants.SORT_FIELD_MONTH_SALE_AMOUNT, sortField)) {
                Integer o1MonthSaleAmount = o1.getMonthSaleAmount() == null ? 0 : o1.getMonthSaleAmount();
                Integer o2MonthSaleAmount = o2.getMonthSaleAmount() == null ? 0 : o2.getMonthSaleAmount();

                if (sortType == SortTypeEnum.ASC) {
                    return o1MonthSaleAmount.compareTo(o2MonthSaleAmount);
                }
                if (sortType == SortTypeEnum.DESC) {
                    return o2MonthSaleAmount.compareTo(o1MonthSaleAmount);
                }
            }

            return 0;
        });

        return channelSpuBaseInfoVOList;
    }

    private void fillSpuBaseInfo(long tenantId, Long storeId, List<String> spuIds, List<ChannelSpuBaseInfoVO> list) {
        PoiSpuIdListCommand poiSpuIdListCommand = new PoiSpuIdListCommand();
        poiSpuIdListCommand.setMerchantId(tenantId);
        poiSpuIdListCommand.setPoiId(storeId);
        poiSpuIdListCommand.setSpuIds(spuIds);
        try {
            QueryPoiSpuListResult result = empowerPoiSpuThriftService.queryPoiSpuByIds(poiSpuIdListCommand);
            if (result != null && CollectionUtils.isNotEmpty(result.getSpuList())) {
                Map<String, PoiSpuInfo> spuMap = result.getSpuList().stream().collect(Collectors.toMap(PoiSpuInfo::getSpuId,
                        Function.identity()));
                Map<String, List<String>> skuMap = new HashMap<>();
                Set<String> skuSet = new HashSet<>();
                for (PoiSpuInfo poiSpuInfo : result.getSpuList()) {
                    spuMap.put(poiSpuInfo.getSpuId(), poiSpuInfo);
                    if (CollectionUtils.isEmpty(poiSpuInfo.getSkuList())) {
                        continue;
                    }
                    skuMap.put(poiSpuInfo.getSpuId(), new ArrayList<>());
                    for (PoiSkuInfo poiSkuInfo : poiSpuInfo.getSkuList()) {
                        skuMap.get(poiSpuInfo.getSpuId()).add(poiSkuInfo.getSkuId());
                        skuSet.add(poiSkuInfo.getSkuId());
                    }
                }
                Map<String, Long> priceMap = fetchChannelSkuPrice(tenantId, storeId, skuSet);
                for (ChannelSpuBaseInfoVO channelSpuBaseInfoVO : list) {
                    if (spuMap.containsKey(channelSpuBaseInfoVO.getSpuId())) {
                        Pair<Long/*最小价格*/, String/*最小价格格式化信息*/> priceFormatPair = getPriceInfo(skuMap.get(channelSpuBaseInfoVO
                                .getSpuId()), priceMap);
                        channelSpuBaseInfoVO.setName(spuMap.get(channelSpuBaseInfoVO.getSpuId()).getName());
                        String picUrl = spuMap.get(channelSpuBaseInfoVO.getSpuId()).getPicUrl();
                        if (StringUtils.isNotEmpty(picUrl)) {
                            channelSpuBaseInfoVO.setImages(Lists.newArrayList(Splitter.on(",").split(picUrl)));
                        }
                        channelSpuBaseInfoVO.setPrice(priceFormatPair.getLeft());
                        channelSpuBaseInfoVO.setFormatPrice(priceFormatPair.getRight());
                    }
                }
            }
        } catch (TException e) {
            log.error("empowerPoiSpuThriftService.queryPoiSpuByIds error", e);
        }
    }

    private Pair<Long/*最小价格*/, String/*最小价格格式化信息*/> getPriceInfo(List<String> skuIds, Map<String, Long> priceMap) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Pair.of(null, "");
        }
        if (skuIds.size() == 1) {
            if (priceMap.containsKey(skuIds.get(0))) {
                return Pair.of(priceMap.get(skuIds.get(0)), MoneyUtils.centToYuan(priceMap.get(skuIds.get(0))) + "元");
            } else {
                return Pair.of(null, "");
            }
        }
        List<String> hasPriceSkus = skuIds.stream().filter(each -> priceMap.containsKey(each)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(hasPriceSkus)) {
            return Pair.of(null, "");
        }
        OptionalLong lowestPrice = hasPriceSkus.stream().mapToLong(each -> priceMap.get(each)).min();
        if (lowestPrice.isPresent()) {
            return Pair.of(lowestPrice.getAsLong(), MoneyUtils.centToYuan(lowestPrice.getAsLong()) + "元起");
        } else {
            return Pair.of(null, "");
        }
    }

    private Map<String, Long> fetchChannelSkuPrice(long tenantId, Long storeId, Set<String> skuSet) {
        ChannelOnlinePriceQueryRequest request = new ChannelOnlinePriceQueryRequest();
        request.setTenantId(tenantId);
        request.setStoreId(storeId);
        request.setChannelId(ChannelTypeEnum.MEITUAN.getChannelId());
        request.setSkuIds(new ArrayList<>(skuSet));
        try {
            ChannelOnlinePriceQueryResponse response = priceAppChannelPriceThriftService.queryChannelOnlinePriceBySkuIds(request);
            if (response != null && response.getCode() != null && response.getCode() == 0) {
                return response.getChannelOnlinePriceDTOList().stream().collect(Collectors.toMap
                        (ChannelOnlinePriceDTO::getSkuId, ChannelOnlinePriceDTO::getPrice));
            }
        } catch (TException e) {
            log.error("priceAppChannelPriceThriftService.queryChannelOnlinePriceBySkuIds error", e);
        }
        return new HashMap<>();
    }

    private void fillMonthSale(Long tenantId, Long storeId, List<String> spuIds, List<ChannelSpuBaseInfoVO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        SpuSalesVolumeRequest request = new SpuSalesVolumeRequest();
        request.setTenantId(tenantId);
        request.setPoiId(storeId);
        request.setSpuIdList(spuIds);
        Map<String, Integer> salesMap = Maps.newHashMap();
        try {
            SalesVolumeResponse response = itemDataThriftService.querySpuSalesVolumeLatest30(request);
            if (response != null && response.getCode() != null && response.getCode().getCode() == SaasDataCode.SUCCESS.getCode
                    ()) {
                salesMap = response.getSalesMap();
            }
        } catch (TException e) {
            log.error("itemDataThriftService.querySpuSalesVolumeLatest30 error", e);
        }
        for (ChannelSpuBaseInfoVO channelSpuBaseInfoVO : list) {
            if (salesMap.containsKey(channelSpuBaseInfoVO.getSpuId())) {
                channelSpuBaseInfoVO.setMonthSaleAmount(salesMap.get(channelSpuBaseInfoVO.getSpuId()));
            }
        }
    }

    /**
     * author: <EMAIL>
     * date: 2021-03-24 20:18:35
     * <p>
     * method: syncChannelSpu
     * params: [request]
     * return: com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse<java.lang.Boolean>
     * desc: 同步不一致商品信息
     */
    public CommonResponse<Boolean> syncChannelSpu(ChannelSpuKeyRequest request) {

        ChannelProductsPushRequest pushRequest = new ChannelProductsPushRequest();
        pushRequest.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        pushRequest.setChannelId(request.getChannelId());
        pushRequest.setStoreId(request.getStoreId());
        pushRequest.setSpuIds(Lists.newArrayList(request.getSpuId()));

        ChannelProductsOperateResponse response = channelProductPublishThriftService.pushChannelProducts(pushRequest);
        if (response.getCode().intValue() == ResultCode.SUCCESS.getCode()) {
            return CommonResponse.success(true);

        }

        int code = response.getCode();
        String errMsg = StringUtils.isBlank(response.getMsg()) ? "推送失败" : response.getMsg();
        if (CollectionUtils.isNotEmpty(response.getChannelProductsDetail())) {
            ChannelProductOperateResponse spuOptRes = response.getChannelProductsDetail().get(0);
            if (Objects.nonNull(spuOptRes) && spuOptRes.getCode().intValue() != ResultCode.SUCCESS.getCode()) {
                errMsg = StringUtils.isBlank(spuOptRes.getMsg()) ? errMsg : spuOptRes.getMsg();
                code = spuOptRes.getCode();
            }
        }

        return CommonResponse.fail(code, errMsg);
    }

    public CommonResponse<Void> createChannelSpu(ChannelSpuRequest request) {
        try {
            ChannelSpuOperationResponse response = channelSpuBizThriftService.batchCreateChannelSpu(request);
            log.info("channelSpuBizThriftService.channelSpuBizThriftService invoke request:{}, response:{}.", request, response);
            if (response == null || response.getStatus() == null) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), "创建渠道商品失败");
            }
            if (response.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
                return CommonResponse.fail(response.getStatus().getCode(), response.getStatus().getMsg());
            }
            return CommonResponse.success(null);
        } catch (Exception e) {
            log.error("channelSpuBizThriftService.channelSpuBizThriftService error", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

    public ChannelCategoryPropertyVo queryChannelCategoryProperty(ChannelCategoryPropertyRequest request){
        ChannelCatePropertyRequest req = new ChannelCatePropertyRequest();
        req.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        req.setChannelId(request.getChannelId());
        req.setChannelCategoryIds(Lists.newArrayList(request.getChannelCategoryId()));
        ChannelCatePropertyListResponse response;
        try {
             response = channelCategoryThriftService.queryPropertyList(req);
        }catch (Exception e){
            log.error("channelCategoryThriftService.queryPropertyList error. request:{}. reason:{}", req, e);
            throw new IllegalStateException("获取动态信息失败，请稍后再试");
        }
        if(response.getCode() != ResultCode.SUCCESS.getCode()){
            throw new BizException(response.getCode(), "获取动态信息失败，请稍后再试");
        }
        if(CollectionUtils.isNotEmpty(response.getChannelCatePropertyList())) {
            return ChannelCategoryPropertyVo.build(response.getChannelCatePropertyList().get(0));
        }
        return ChannelCategoryPropertyVo.buildDefault(request.getChannelId(), request.getChannelCategoryId());
    }

    public Map<Integer, List<ChannelCategoryPropertyVo>> batchQueryChannelCategoryProperty(BatchChannelCategoryPropertyRequest request) throws Exception {
        long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        List<Future<List<ChannelCategoryPropertyVo>>> futureList = Fun.map(request.getChannelCategoryCodeMap().entrySet(), entry -> itemThreadPool.submit(() -> {
            ChannelCatePropertyRequest req = new ChannelCatePropertyRequest();
            req.setTenantId(tenantId);
            req.setChannelId(entry.getKey());
            req.setChannelCategoryIds(entry.getValue());
            try {
                ChannelCatePropertyListResponse response = channelCategoryThriftService.queryPropertyList(req);
                if (ResultCode.SUCCESS.getCode() != response.getCode()) {
                    log.error("查询渠道类目属性异常，request: {}，response: {}", req, response);
                    throw new BizException(String.format("查询%s渠道%s类目属性信息异常", req.getChannelId(), req.getChannelCategoryIds()));
                }
                List<ChannelCatePropertyDTO> channelCatePropertyList = response.getChannelCatePropertyList();
                if (CollectionUtils.isNotEmpty(channelCatePropertyList)) {
                    List<ChannelCategoryPropertyVo> categoryPropertyVoList = Fun.map(channelCatePropertyList, ChannelCategoryPropertyVo::build);
                    if (!SwitchUtils.useSaleAttr(tenantId)) {
                        categoryPropertyVoList.forEach(cat -> cat.setSaleAttrSupportExtend(false));
                    }
                    return categoryPropertyVoList;
                }
                // 返回默认属性
                return Fun.map(entry.getValue(), code -> ChannelCategoryPropertyVo.buildDefault(entry.getKey(), code));
            }
            catch (Exception e) {
                log.error("channelCategoryThriftService.queryPropertyList req:{}", req, e);
            }
            return Collections.emptyList();
        }));

        try {
            Map<Integer, List<ChannelCategoryPropertyVo>> result = new HashMap<>();

            for (Future<List<ChannelCategoryPropertyVo>> future : futureList) {
                List<ChannelCategoryPropertyVo> channelCategoryPropertyVos = future.get();
                if (CollectionUtils.isNotEmpty(channelCategoryPropertyVos)) {
                    result.put(channelCategoryPropertyVos.get(0).getChannelId(), channelCategoryPropertyVos);
                }
            }

            if (StringUtils.isNotBlank(request.getSpuId())) {
                fillWithSpuSaleAttr(tenantId, request.getSpuId(), result);
            }

            // 统一填充属性值最大长度
            fillMaxSaleAttrLength(result);

            return result;
        }
        catch (Exception e) {
            log.error("batchQueryChannelCategoryProperty error", e);
            throw e;
        }
    }

    private void fillMaxSaleAttrLength(Map<Integer, List<ChannelCategoryPropertyVo>> resultMap) {
        for (Map.Entry<Integer, List<ChannelCategoryPropertyVo>> entry : resultMap.entrySet()) {
            if (entry.getValue() == null) {
                continue;
            }

            for (ChannelCategoryPropertyVo vo : entry.getValue()) {
                vo.setMaxAttrNameLength(MccConfigUtil.getSaleAttrLimitConfig().getAttrNameMaxLength(entry.getKey()));
                if (vo.getChannelSaleAttrList() == null) {
                    continue;
                }
                for (ChannelSaleAttrVo channelSaleAttrVo : vo.getChannelSaleAttrList()) {
                    channelSaleAttrVo.setMaxAttrValueLength(MccConfigUtil.getSaleAttrLimitConfig().getAttrValueMaxLength(entry.getKey()));
                }
            }
        }

    }

    private void fillWithSpuSaleAttr(long tenantId, String spuId, Map<Integer, List<ChannelCategoryPropertyVo>> result) {
        try {
            MerchantSpuIdListCommand command = new MerchantSpuIdListCommand();
            command.setMerchantId(tenantId);
            command.setSpuIds(Collections.singletonList(spuId));

            SpuSaleAttrValueResult spuSaleAttrValueResult = empowerMerchantSpuThriftService.querySpuSaleAttrValue(command);
            if (spuSaleAttrValueResult == null || spuSaleAttrValueResult.getStatus() == null || spuSaleAttrValueResult.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
                log.error("查询商品销售属性异常，request: {}，response: {}", command, spuSaleAttrValueResult);
                return;
            }
            List<SpuAndSkuSaleAttrInfo> spuAndSkuSaleAttrInfoList = spuSaleAttrValueResult.getSpuSaleAttrValueList();
            if (CollectionUtils.isEmpty(spuAndSkuSaleAttrInfoList)) {
                return;
            }

            SpuAndSkuSaleAttrInfo spuAndSkuSaleAttrInfo = spuAndSkuSaleAttrInfoList.get(0);
            Map<String, List<ChannelSaleAttributeValueInfo>> skuSaleAttrValueMap = Optional.ofNullable(spuAndSkuSaleAttrInfo.getSkuSaleAttrValueListMap()).orElse(Collections.emptyMap());

            for (List<ChannelSaleAttributeValueInfo> channelSkuSaleAttrValueList : skuSaleAttrValueMap.values()) {
                for (ChannelSaleAttributeValueInfo channelSaleAttributeValueInfo : channelSkuSaleAttrValueList) {
                    int channelId = channelSaleAttributeValueInfo.getChannelId();
                    List<ChannelCategoryPropertyVo> channelCategoryPropertyVos = result.get(channelId);
                    // 仅当点查的时候拼接自定义销售属性值
                    if (channelCategoryPropertyVos == null || channelCategoryPropertyVos.size() != 1) {
                        continue;
                    }
                    ChannelCategoryPropertyVo channelCategoryPropertyVo = channelCategoryPropertyVos.get(0);
                    List<ChannelSaleAttrVo> channelSaleAttrVoList = channelCategoryPropertyVo.getChannelSaleAttrList();
                    Map<String, ChannelSaleAttrVo> name2SaleAttrVoMap = ConverterUtils.listStreamMapToMap(channelSaleAttrVoList, ChannelSaleAttrVo::getAttrName, Function.identity());

                    List<SaleAttributeValueInfo> saleAttributeValueInfoList = channelSaleAttributeValueInfo.getSaleAttributeValueInfoList();
                    for (SaleAttributeValueInfo saleAttributeValueInfo : saleAttributeValueInfoList) {
                        ChannelSaleAttrVo channelSaleAttrVo = name2SaleAttrVoMap.get(saleAttributeValueInfo.getAttrName());
                        if (StringUtils.isNotBlank(saleAttributeValueInfo.getAttrValueId())) {
                            // 仅拼接自定义销售属性值
                            continue;
                        }
                        if (channelSaleAttrVo == null) {
                            // 抖音需要拼接自定义销售属性
                            if (channelId == EnhanceChannelType.DY.getChannelId()) {
                                channelSaleAttrVo = buildDyCustomSaleAttr(saleAttributeValueInfo);
                                channelSaleAttrVoList.add(channelSaleAttrVo);
                            }
                            else {
                                continue;
                            }
                        }
                        // 拼接自定义销售属性
                        channelSaleAttrVo.appendValueList(ChannelSaleAttrValueVo.builder()
                                .attrValue(saleAttributeValueInfo.getAttrValue())
                                .attrValueId(saleAttributeValueInfo.getAttrValueId()).build());
                    }
                }
            }

        }
        catch (Exception e) {
            log.error("fillWithSpuSaleAttr error", e);
        }
    }

    private ChannelSaleAttrVo buildDyCustomSaleAttr(SaleAttributeValueInfo saleAttributeValueInfo) {
        return ChannelSaleAttrVo.builder()
                .attrId(saleAttributeValueInfo.isSetAttrId() ? String.valueOf(saleAttributeValueInfo.getAttrId()) : null)
                .attrName(saleAttributeValueInfo.getAttrName())
                .supportExtend(true)
                .supportPicture(false)
                .valueList(new ArrayList<>())
                .build();
    }

    public ChannelSpecialAttrVo queryChannelSpecialProperty(ChannelSpecAttrRequest request){
        ChannelSpecialAttrRequest req = new ChannelSpecialAttrRequest();
        req.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        req.setChannelId(request.getChannelId());
        req.setAttrId(request.getAttrId());
        req.setKeyword(request.getKeyword());
        req.setPage(request.getPage());
        req.setPageSize(request.getPageSize());
        req.setCategoryId(request.getCategoryId());
        ChannelSpecialAttrResponse response;
        try{
            response = channelCategoryThriftService.querySpecProperty(req);
        }catch (Exception e){
            log.error("channelCategoryThriftService.querySpecProperty error. request:{}. reason:{}", req, e);
            throw new IllegalStateException("获取动态信息失败，请稍后再试");
        }
        if(response.getCode() != ResultCode.SUCCESS.getCode()){
            throw new BizException(response.getCode(), "获取动态信息失败，请稍后再试");
        }
        if(CollectionUtils.isNotEmpty(response.channelSpecialAttrDTOList)){
            return ChannelSpecialAttrVo.build(response.channelSpecialAttrDTOList.get(0));
        }
        return ChannelSpecialAttrVo.buildDefault(request);
    }

    public List<AdventRuleVo> queryChannelAdventRule(ChannelAdventRuleRequest request) {
        ChannelCatePropertyRequest propertyRequest = new ChannelCatePropertyRequest();
        propertyRequest.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        propertyRequest.setChannelId(request.getChannelId());
        propertyRequest.setChannelCategoryIds(Lists.newArrayList(request.getChannelCategoryId()));

        ChannelAdventRuleResponse response;
        try {
            response = channelCategoryThriftService.queryAdventRuleList(propertyRequest);
        } catch (Exception e) {
            log.error("channelCategoryThriftService.queryAdventRuleList error. request:{}. reason:{}", request, e);
            throw new IllegalStateException("获取临期规则失败，请稍后再试");
        }
        if(!Objects.equals(response.getCode(), ResultCode.SUCCESS.getCode())){
            throw new BizException(response.getCode(), "获取临期规则失败，请稍后再试");
        }
        if (CollectionUtils.isNotEmpty(response.getChannelCategoryAdventRuleList())){
            return Fun.map(response.getChannelCategoryAdventRuleList().get(0).getAdventRuleList(), AdventRuleVo::of);
        }
        return Collections.emptyList();
    }
}
