package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.wavepick.converter;

import com.sankuai.meituan.reco.pickselect.thrift.wave.dto.WarehouseSeedCompleteTaskModuleDTO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.WarehouseSeedCompleteTaskModuleVO;
import org.mapstruct.Mapper;

/**
 * @Description:
 * @Author: zhangjian155
 * @Date: 2022/8/25 18:55
 */
@Mapper(componentModel = "spring", uses = {WarehouseContainerBoxModuleConverter.class})
public abstract class WarehouseSeedCompleteTaskModuleConverter {
    public abstract WarehouseSeedCompleteTaskModuleVO convert2Vo(WarehouseSeedCompleteTaskModuleDTO warehouseSeedCompleteTaskModuleDTO);
}
