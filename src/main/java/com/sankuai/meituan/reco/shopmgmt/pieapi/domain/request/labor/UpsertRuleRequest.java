package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.LocationStoreVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.RuleEmployeeVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.ScheduleDetailVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-10-18
 * @email <EMAIL>
 */
@TypeDoc(
        description = "新建/编辑班次请求"
)
@ApiModel("新建/编辑班次请求")
@Data
public class UpsertRuleRequest {

    @FieldDoc(
            description = "规则id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "规则id")
    private Long ruleId;

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店id")
    private Long belongStoreId;

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店id")
    private String storeName;

    @FieldDoc(
            description = "名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "名称")
    private String name;

    @FieldDoc(
            description = "类型", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "类型")
    private String type;

    @FieldDoc(
            description = "员工列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "员工列表")
    private List<RuleEmployeeVO> employeeList;

    @FieldDoc(
            description = "班次id列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "班次id列表")
    private List<Long> shiftIdList;

    @FieldDoc(
            description = "新建/编辑班次请求", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "新建/编辑班次请求")
    private List<ScheduleDetailVO> scheduleDetailList;

    @FieldDoc(
            description = "绑定门店位置信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "绑定门店位置信息")
    private List<LocationStoreVO> locationStoreList;

}
