package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.ApprovalVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.AttendanceApprovalItemVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.ExtraWorkApplyVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * Email <EMAIL>
 * Date 2022/10/14 6:27 下午
 * Description
 */

@TypeDoc(
        description = "考勤审批详情响应"
)
@ApiModel("查询考勤审批详情响应")
@Data
public class AttendanceApprovalDetailResponse {


    @FieldDoc(
            description = "排班申诉详情", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty("排班申诉详情")
    private AttendanceApprovalItemVO item;

    @FieldDoc(
            description = "加班申请详情", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty("加班申请详情")
    private ExtraWorkApplyVO extraWorkApply;

    @FieldDoc(
            description = "审批信息list"
    )
    @ApiModelProperty("审批信息list")
    private List<ApprovalVO> approvalList;
}
