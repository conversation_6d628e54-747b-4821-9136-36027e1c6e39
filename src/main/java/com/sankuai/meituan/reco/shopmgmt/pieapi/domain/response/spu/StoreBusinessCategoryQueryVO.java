package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/3/4
 */
@TypeDoc(
        description = "查询门店在美团渠道一级经营品类结果对象"
)
@Data
@ApiModel("查询门店在美团渠道一级经营品类结果对象")
public class StoreBusinessCategoryQueryVO {
    @FieldDoc(
            description = "门店经营品类", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店经营品类", required = true)
    private Integer businessCategory;

}
