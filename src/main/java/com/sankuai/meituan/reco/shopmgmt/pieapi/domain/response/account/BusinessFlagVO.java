package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.account;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @createTime 2019/11/19
 * @description
 */
@TypeDoc(
        description = "账号业务相关的值,比如是否开启了自动接单，是否开启了领取拣货任务等"
)
@Data
@ApiModel("账号配置")
public class BusinessFlagVO {

    @FieldDoc(
            description = "接单方式 (0:不接单，1:自动接单, 2:手动接单)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "接单方式 (0:不接单，1:自动接单, 2:手动接单)", required = true)
    @NotNull
    private Integer autoTakeOrder;


    @FieldDoc(
            description = "新拣货任务提醒 (0:不提醒，n:n次)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "新拣货任务提醒 (0:不提醒，n:n次)", required = true)
    @NotNull
    private Integer newPickTaskAlertTimes;


    @FieldDoc(
            description = "订单领取超时提醒 (0:不提醒，n:n次)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单领取超时提醒 (0:不提醒，n:n次)", required = true)
    @NotNull
    private Integer pickTaskClaimOvertimeAlertTimes;


    @FieldDoc(
            description = "订单拣货超时提醒 (0:不提醒，n:n次)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单拣货超时提醒 (0:不提醒，n:n次)", required = true)
    @NotNull
    private Integer pickTaskOvertimeAlertTimes;

    @FieldDoc(
            description = "订单合流超时提醒 (0:不提醒，n:n次)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单合流超时提醒 (0:不提醒，n:n次)", required = true)
    @NotNull
    private Integer mergeOvertimeAlertTimes;
}
