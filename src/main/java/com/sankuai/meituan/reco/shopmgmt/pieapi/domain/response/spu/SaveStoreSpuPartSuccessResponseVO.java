package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Title: SaveStoreSpuPartSuccessResponseVO
 * @Description: 保存门店商品部分成功信息
 * @Author: zhaolei12
 * @Date: 2020/5/16 8:21 下午
 */
@TypeDoc(
        description = "保存门店商品部分成功信息"
)
@Data
@ApiModel("保存门店商品部分成功信息")
public class SaveStoreSpuPartSuccessResponseVO {

    @FieldDoc(
            description = "错误文案", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "错误文案")
    private String errorMsg;

    @FieldDoc(
            description = "错误编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "错误编码")
    private Integer errorCode;

    @FieldDoc(
            description = "是否包含0价格", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否包含0价格")
    private boolean hasZeroPrice;
}
