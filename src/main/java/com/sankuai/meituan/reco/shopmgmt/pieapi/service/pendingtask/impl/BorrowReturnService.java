package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.impl;

import com.google.common.collect.Sets;
import com.meituan.shangou.sac.dto.model.SacPermissionDTO;
import com.meituan.shangou.sac.dto.response.search.QuerySacAccountPermissionResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.AbstractSinglePendingTaskService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.OtherStockReceiptClient;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.SacAccountClient;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Service
public class BorrowReturnService extends AbstractSinglePendingTaskService {

    @Resource
    private OtherStockReceiptClient otherStockReceiptClient;
    @Resource
    private SacAccountClient sacAccountClient;

    @Override
    protected PendingTaskResult getPendingTaskCount(PendingTaskParam request) throws Exception {
        long count = otherStockReceiptClient.borrowReturnRedPoint(request, hasPermissionDisplayHandleList(request.getUser().getAccountId()));
        return PendingTaskResult.createNumberMarker((int) count);
    }

    private boolean hasPermissionDisplayHandleList(Long accountId) {
        Set<String> permissionCodes = Sets.newHashSet("DH-MATERIAL-IO-WAITING");
        if (CollectionUtils.isEmpty(permissionCodes)) {
            return true;
        }

        QuerySacAccountPermissionResponse permissionResponse = sacAccountClient.querySacAccountPermission(accountId);
        List<SacPermissionDTO> userPermissionDTOS = Optional.ofNullable(permissionResponse.getSacPermissionDTOS()).orElse(Collections.emptyList());

        return userPermissionDTOS.stream()
                .map(SacPermissionDTO::getSacPermissionCode)
                .anyMatch(permissionCodes::contains);
    }

    @Override
    protected AuthCodeEnum module() {
        return AuthCodeEnum.BORROW_RETURN;
    }
}