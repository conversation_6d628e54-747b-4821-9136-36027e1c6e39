package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: <EMAIL>
 * @Date: 2020-01-15 14:57
 * @Description:
 */
@TypeDoc(
        description = "赠品信息"
)
@ApiModel("赠品信息")
@Data
public class GiftVO {
    @FieldDoc(
            description = "赠品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "赠品名称",required = true)
    private String giftName;

    @FieldDoc(
            description = "赠品数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "赠品数量", required = false)
    private Integer GiftQuantity;

    @FieldDoc(
            description = "赠品sku", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "赠品sku")
    private String sku;
    @FieldDoc(
            description = "属于哪一个主品", requiredness = Requiredness.OPTIONAL
    )
    private String belongSkuId;

    @FieldDoc(
            description = "赠品规格", requiredness = Requiredness.OPTIONAL
    )
    private String specification;
}
