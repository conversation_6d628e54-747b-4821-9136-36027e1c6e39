package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.manage;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@TypeDoc(
        description = "骑手改派（商家自营配送）请求"
)
@ApiModel("骑手改派（商家自营配送）请求")
@Data
public class RiderChangeRequest {

    @FieldDoc(description = "门店id", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "门店id", required = true)
    @NotNull
    private Long storeId;

    @FieldDoc(description = "赋能订单号", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "赋能订单号", required = true)
    @NotNull
    private Long orderId;

    @FieldDoc(description = "改派后的骑手账号", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "改派后的骑手账号", required = true)
    @NotNull
    private Long riderAccountId;
}
