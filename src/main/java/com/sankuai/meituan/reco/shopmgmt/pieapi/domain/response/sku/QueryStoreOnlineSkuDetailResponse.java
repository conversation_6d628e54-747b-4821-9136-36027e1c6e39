package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.CdqStoreSkuWithChannelInfoVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 获取门店商品详情响应
 * @author: liyu44
 * @create: 2020-02-03
 **/
@TypeDoc(
        description = "获取门店商品详情响应"
)
@Data
@ApiModel("获取门店商品详情响应")
public class QueryStoreOnlineSkuDetailResponse {

    @FieldDoc(
            description = "门店商品明细", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店商品明细", required = true)
    private CdqStoreSkuWithChannelInfoVo storeSku;

}
