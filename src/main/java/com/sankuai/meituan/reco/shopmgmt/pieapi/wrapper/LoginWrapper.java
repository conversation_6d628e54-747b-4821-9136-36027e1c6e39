package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;

import com.meituan.linz.boot.util.Fun;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.FallbackException;
import com.sankuai.meituan.reco.store.management.thrift.EmpowerTaskLogicException;
import com.sankuai.meituan.reco.store.management.thrift.LoginCertification;
import com.sankuai.meituan.reco.store.management.thrift.LoginThriftService;
import com.sankuai.meituan.reco.store.management.thrift.LogoutCertification;
import com.sankuai.meituan.reco.store.management.thrift.ObtainStoreInfoRequest;
import com.sankuai.meituan.reco.store.management.thrift.ObtainStoreInfoResult;
import com.sankuai.meituan.reco.store.management.thrift.ObtainUserInfoRequest;
import com.sankuai.meituan.reco.store.management.thrift.StoreInfo;
import com.sankuai.meituan.reco.store.management.thrift.UserInfo;
import com.sankuai.meituan.reco.store.saas.infrastructure.shield.common.monitor.CommonMonitorTransaction;
import com.sankuai.meituan.shangou.empower.auth.sdk.bean.SessionInfo;
import com.sankuai.meituan.shangou.empower.auth.sdk.context.SessionContext;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.List;

@Service
@Rhino
@Slf4j
public class LoginWrapper {

    @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
    @Resource(name = "empowerLoginThriftService")
    private LoginThriftService.Iface loginThriftService;



    @Degrade(rhinoKey = "LoginWrapper.resolveToken",
            fallBackMethod = "obtainUserInfoFallback",
            timeoutInMilliseconds = 1200,
            ignoreExceptions = {CommonLogicException.class})
    @MethodLog(logRequest = true, logResponse = true)
    @CommonMonitorTransaction
    public UserInfo obtainUserInfo(long tenantId, String token, long accountId, long employeeId, int appId, boolean needNewest) {
        ObtainUserInfoRequest request = new ObtainUserInfoRequest();
        request.setTenantId(tenantId);
        request.setAccountId(accountId);
        request.setEmployeeId(employeeId);
        request.setAppId(appId);
        request.setToken(token);
        request.setNeedNewest(needNewest);
        try {
            return loginThriftService.obtainUserInfo(request);
        } catch (EmpowerTaskLogicException e) {
            log.error("loginThriftService.obtainUserInfo error", e);
            throw new CommonLogicException(e.getMessage(), getResultCode(e.code));
        } catch (TException e) {
            throw new CommonRuntimeException(e);
        }
    }


    public UserInfo obtainUserInfoFallback(long tenantId, String token, long accountId, long employeeId, int appId, boolean needNewest) {
        throw new FallbackException(MessageFormat.format("token解析接口降级，token = {0}", token));
    }

    public List<Long> getCurrentUserFullStoreList() {
        // 获取当前账户信息
        SessionInfo sessionInfo = SessionContext.getCurrentSession();
        ObtainStoreInfoRequest request = new ObtainStoreInfoRequest();
        request.setTenantId(sessionInfo.getTenantId());
        request.setAccountId(sessionInfo.getAccountId());
        request.setAppId(sessionInfo.getAuthAppId());
        try {
            ObtainStoreInfoResult result = loginThriftService.obtainStoreInfo(request);
            return Fun.map(result.getStoreInfoList(), StoreInfo::getPoiId);
        } catch (Exception e) {
            log.error("loginThriftService.obtainStoreInfo error", e);
            throw new CommonRuntimeException(e);
        }
    }


    private ResultCode getResultCode(int code) {
        ResultCode retCode = ResultCode.fromCode(code);
        return retCode == null ? ResultCode.FAIL : retCode;
    }
}
