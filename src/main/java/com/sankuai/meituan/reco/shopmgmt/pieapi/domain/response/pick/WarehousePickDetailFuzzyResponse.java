package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.WarehouseItemModuleVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.WarehouseWaveProgressVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "模糊查询拣货任务详情里商品信息的返回体"
)
@Data
@Builder
@ApiModel("模糊查询拣货任务详情里商品信息的返回体")
public class WarehousePickDetailFuzzyResponse {

    @FieldDoc(
            description = "是否还有数据", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否还有数据", required = true)
    private Boolean hasMore;

    @FieldDoc(
            description = "分页总数", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "分页总数")
    private Integer total;

    @FieldDoc(
            description = "拣货进度信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "拣货进度信息", required = true)
    private WarehouseWaveProgressVO pickProgress;

    @FieldDoc(
            description = "拣货商品列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "拣货商品列表")
    private List<WarehouseItemModuleVO> dataList;
}
