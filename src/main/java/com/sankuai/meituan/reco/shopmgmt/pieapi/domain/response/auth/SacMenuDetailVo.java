package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.auth;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.sac.dto.model.SacMenuDetailDTO;

/**
 * 菜单详情
 *
 * <AUTHOR>
 * @since 2020-12-09
 */
@TypeDoc(
        description = "菜单详情",
        since = "2020-12-09",
        authors = {"WangSukuan"}
)
public class SacMenuDetailVo {

    /**
     * 菜单id
     */
    @FieldDoc(
            description = "菜单id"
    )
    public Long sacMenuId;

    /**
     * 菜单code
     */
    @FieldDoc(
            description = "菜单code"
    )
    public String sacMenuCode;

    /**
     * 菜单名称
     */
    @FieldDoc(
            description = "菜单名称"
    )
    public String sacMenuName;

    /**
     * 路由url
     */
    @FieldDoc(
            description = "路由url"
    )
    public String routeUrl;

    /**
     * 排序
     */
    @FieldDoc(
            description = "排序"
    )
    public Integer rank;

    /**
     * 父菜单code
     */
    @FieldDoc(
            description = "父菜单code"
    )
    public String parentSacMenuCode;

    /**
     * 菜单扩展属性
     */
    @FieldDoc(
            description = "菜单扩展属性"
    )
    public String metaData;

    /**
     * 创建时间
     */
    @FieldDoc(
            description = "创建时间"
    )
    public Long createTime;

    /**
     * 必须的功能权限id列表
     */
    @FieldDoc(
            description = "控制菜单展示的功能"
    )
    public List<Long> menuVisibleControlPermissionIds;

    /**
     * 关联的功能权限列表
     */
    @FieldDoc(
            description = "关联的功能权限列表"
    )
    public List<SacPermissionVo> sacPermissionVos;


    public static List<SacMenuDetailVo> buildBySacMenuDetailDTOList(List<SacMenuDetailDTO> sacMenuDetailDTOList){

        List<SacMenuDetailVo> sacMenuDetailVos = new ArrayList<>();
        if (CollectionUtils.isEmpty(sacMenuDetailDTOList)){
            return sacMenuDetailVos;
        }
        for (SacMenuDetailDTO sacMenuDetailDTO : sacMenuDetailDTOList) {
            SacMenuDetailVo sacMenuDetailVo = new SacMenuDetailVo();
            sacMenuDetailVo.build(sacMenuDetailDTO);
            sacMenuDetailVos.add(sacMenuDetailVo);
        }
        return sacMenuDetailVos;

    }

    public SacMenuDetailVo build(SacMenuDetailDTO sacMenuDetailDTO) {

        this.sacMenuId = sacMenuDetailDTO.getSacMenuId();
        this.sacMenuName = sacMenuDetailDTO.getSacMenuName();
        this.sacMenuCode = sacMenuDetailDTO.getSacMenuCode();
        this.parentSacMenuCode = sacMenuDetailDTO.getParentSacMenuCode();
        this.routeUrl = sacMenuDetailDTO.getRouteUrl();
        this.rank = sacMenuDetailDTO.getRank();
        this.metaData = sacMenuDetailDTO.getMetaData();
        this.createTime = sacMenuDetailDTO.getCreateTime();
        this.menuVisibleControlPermissionIds = sacMenuDetailDTO.getMenuVisibleControlPermissionIds();
        this.sacPermissionVos = SacPermissionVo.buildBySacPermissionDTOList(sacMenuDetailDTO.getRelSacPermissionDTOList());
        return this;

    }

}
