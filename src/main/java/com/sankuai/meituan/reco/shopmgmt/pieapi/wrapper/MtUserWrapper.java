package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.meituan.shangou.saas.dto.StatusCodeEnum;
import com.meituan.shangou.saas.dto.response.UserDataResponse;
import com.meituan.shangou.saas.service.MtUserThriftService;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/7/18 11:13 AM
 */
@Component
@Slf4j
public class MtUserWrapper {

    @Autowired
    private MtUserThriftService mtUserThriftService;

    /**
     * 通过用户ID获取美团用户信息
     *
     * @param userId 用户ID
     * @return .
     */
    public UserDataResponse getMtUserInfoByUserId(Long userId) {
        UserDataResponse response = mtUserThriftService.getMtUserInfo(userId);
        if (response == null
                || response.getStatus() == null
                || !response.getStatus().code.equals(StatusCodeEnum.SUCCESS.getCode())) {
            throw new BizException("获取美团用户信息失败");
        }
        return response;
    }
}
