package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.dto.ChannelSaleAttrValueDTO;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-02-12
 */
@TypeDoc(
        name = "渠道销售属性值",
        description = "渠道销售属性值"
)
@Data
@Builder
public class ChannelSaleAttrValueVo {

    @FieldDoc(description = "属性值ID")
    private String attrValueId;

    @FieldDoc(description = "属性值")
    private String attrValue;

    public static ChannelSaleAttrValueVo build(ChannelSaleAttrValueDTO dto) {
        if (dto == null) {
            return null;
        }
        return ChannelSaleAttrValueVo.builder()
                .attrValueId(dto.getAttrValueId())
                .attrValue(dto.getAttrValue())
                .build();
    }

    public static List<ChannelSaleAttrValueVo> buildList(List<ChannelSaleAttrValueDTO> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return new ArrayList<>();
        }
        return dtos.stream()
                .map(ChannelSaleAttrValueVo::build)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
