package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.WarehousePickCompleteModuleVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;
@TypeDoc(
        description = "查询已拣货列表返回"
)
@Data
@Builder
@ApiModel("查询已拣货列表返回")
public class WarehousePickCompleteQueryResponse {

    @FieldDoc(
            description = "已拣货数据列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "已拣货数据列表", required = true)
    private List<WarehousePickCompleteModuleVO> dataList;
}
