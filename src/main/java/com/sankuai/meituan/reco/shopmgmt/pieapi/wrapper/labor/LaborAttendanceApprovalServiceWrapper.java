package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.labor;

import com.sankuai.drunkhorsemgmt.labor.common.Status;
import com.sankuai.drunkhorsemgmt.labor.thrift.approval.AttendanceApprovalThriftService;
import com.sankuai.drunkhorsemgmt.labor.thrift.approval.dto.AttendanceApprovalBaseInfoDTO;
import com.sankuai.drunkhorsemgmt.labor.thrift.approval.dto.AttendanceApprovalDTO;
import com.sankuai.drunkhorsemgmt.labor.thrift.approval.dto.AttendanceApprovalStatDTO;
import com.sankuai.drunkhorsemgmt.labor.thrift.approval.dto.ExtraWorkApprovalDTO;
import com.sankuai.drunkhorsemgmt.labor.thrift.approval.request.*;
import com.sankuai.drunkhorsemgmt.labor.thrift.approval.response.AttendanceApprovalCreateResp;
import com.sankuai.drunkhorsemgmt.labor.thrift.approval.response.AttendanceApprovalDetailResp;
import com.sankuai.drunkhorsemgmt.labor.thrift.approval.response.AttendanceApprovalListResp;
import com.sankuai.drunkhorsemgmt.labor.thrift.approval.response.AttendanceApprovalSubmitResp;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.AttendanceApprovalDetailResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.AttendanceApprovalItemVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.AttendanceApprovalListResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.ExtraWorkApplyVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.TimeUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.labor.convertor.ApprovalConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * Email <EMAIL>
 * Date 2022/10/17 10:39 上午
 * Description
 */
@Slf4j
@Service
public class LaborAttendanceApprovalServiceWrapper {

    @Resource
    private AttendanceApprovalThriftService attendanceApprovalThriftService;

    @Resource
    private ApprovalConverter approvalConverter;

    /**
     * 创建新的审批流程
     * @param request
     * @return
     */
    public CommonResponse<Long> createNew(AttendanceApprovalCreateRequest request) {
        AttendanceApprovalCreateReq createReq = new AttendanceApprovalCreateReq();
        createReq.setPoiId(request.getPoiId());
        try {
            long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
            long employeeId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getEmployeeId();
            createReq.setTenantId(tenantId);
            createReq.setApplyUserId(employeeId);
            createReq.setResultStatisticsId(request.getResultStatisticsId());
            createReq.setEventScene(request.getEventScene());
            createReq.setApplyTextContent(request.getApplyText());
            createReq.setPhotoList(request.getPhotoList());
            AttendanceApprovalCreateResp resp = attendanceApprovalThriftService.createNew(createReq);
            if (resp.getStatus().getCode() == Status.FAILED.getCode()) {
                return CommonResponse.fail(resp.getStatus().getCode(), resp.getStatus().getMsg());
            } else {
                return CommonResponse.success(resp.getBaseInfoId());
            }
        } catch (Exception exp) {
            log.error(exp.getMessage(), exp);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), ResultCode.FAIL.getDefaultMessage(), null);
        }
    }

    /**
     * 提交审批结果
     * @param request
     * @return
     */
    public CommonResponse<Void> submitStatus(AttendanceApprovalSubmitRequest request) {
        AttendanceApprovalSubmitReq submitReq = new AttendanceApprovalSubmitReq();
        submitReq.setBaseInfoId(request.getBaseInfoId());
        submitReq.setApprovalStatus(request.getApprovalStatus());
        submitReq.setApprovalStatusText(request.getApprovalStatusText());
        try {
            long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
            long employeeId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getEmployeeId();
            submitReq.setSubmitUserId(String.valueOf(employeeId));
            submitReq.setTenantId(tenantId);
            AttendanceApprovalSubmitResp resp = attendanceApprovalThriftService.submitStatus(submitReq);
            if (resp.getStatus().getCode() == Status.FAILED.getCode()) {
                return CommonResponse.fail(resp.getStatus().getCode(), resp.getStatus().getMsg());
            } else {
                return CommonResponse.success(null);
            }
        } catch (Exception exp) {
            log.error(exp.getMessage(), exp);
            return CommonResponse.fail(ResultCode.FAIL);
        }
    }

    /**
     * 获取申诉详情
     * @param request
     * @return
     */
    public CommonResponse<AttendanceApprovalDetailResponse> queryDetail(AttendanceApprovalDetailRequest request) {
        AttendanceApprovalDetailReq detailReq = new AttendanceApprovalDetailReq();
        detailReq.setBaseInfoId(request.getBaseInfoId());
        try {
            long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
            detailReq.setTenantId(tenantId);
            AttendanceApprovalDetailResp resp = attendanceApprovalThriftService.queryDetail(detailReq);
            if (resp.getStatus().getCode() == Status.FAILED.getCode()) {
                return CommonResponse.fail(resp.getStatus().getCode(), resp.getStatus().getMsg());
            } else {
                AttendanceApprovalDetailResponse response = new AttendanceApprovalDetailResponse();
                response.setItem(this.convertToVO(resp.getAttendanceApprovalDTO()));
                response.setExtraWorkApply(this.convertToVO(resp.getAttendanceApprovalDTO().getBaseInfoId(),
                        resp.getAttendanceApprovalDTO().getExtraWorkDTO()));
                response.setApprovalList(approvalConverter.convertToApprovalVO(resp.getAttendanceApprovalDTO().getApprovalNodeDTOList()));
                return CommonResponse.success(response);
            }
        } catch (Exception exp) {
            log.error(exp.getMessage(), exp);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "系统错误", null);
        }
    }

    /**
     * 查询审批流程列表
     * @param request
     * @return
     */
    public CommonResponse<AttendanceApprovalListResponse> queryList(AttendanceApprovalListRequest request) {
        AttendanceApprovalListReq listReq = new AttendanceApprovalListReq();

        long employeeId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getEmployeeId();
        if (request.getIsApprovalViewed() != null && request.getIsApprovalViewed() == 1) {
            // 如果是管理视角，设置审批候选人
            listReq.setApprovalCandidate(String.valueOf(employeeId));
        } else {
            listReq.setApplyUserId(employeeId);
        }
        if (request.getIsFinished() != null) {
            listReq.setIsFinished(request.getIsFinished() == 1);
        }
        if (request.getPageNo() != null && request.getPageNo() > 0) {
            listReq.setPageNo(request.getPageNo());
        } else {
            listReq.setPageNo(1);
        }
        if (request.getPageSize() != null && request.getPageSize() > 0 && request.getPageSize() <= 200) {
            listReq.setPageSize(request.getPageSize());
        } else {
            listReq.setPageSize(20);
        }

        try {
            long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
            listReq.setTenantId(tenantId);
            AttendanceApprovalListResp resp = attendanceApprovalThriftService.queryPageList(listReq);
            if (resp.getStatus().getCode() == Status.FAILED.getCode()) {
                return CommonResponse.fail(resp.getStatus().getCode(), resp.getStatus().getMsg());
            } else {
                AttendanceApprovalListResponse response = new AttendanceApprovalListResponse();
                if (CollectionUtils.isNotEmpty(resp.getAttendanceApprovalDTOs())) {
                    List<AttendanceApprovalItemVO> itemVOS = resp.getAttendanceApprovalDTOs().stream()
                            .map(this::convertToVO).collect(Collectors.toList());
                    response.setList(itemVOS);
                }
                response.setTotalCount(resp.getTotalCount().intValue());
                response.setHasMore(resp.getHasMore() != null && resp.getHasMore() ? 1 : 0);
                return CommonResponse.success(response);
            }
        } catch (Exception exp) {
            log.error(exp.getMessage(), exp);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "系统错误", null);
        }
    }

    private ExtraWorkApplyVO convertToVO(long baseInfoId,  ExtraWorkApprovalDTO dto) {
        if (dto == null) {
            return null;
        }
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        ExtraWorkApplyVO vo = new ExtraWorkApplyVO();
        vo.setId(baseInfoId);
        vo.setApplyAttendanceDay(dto.getApplyAttendanceDay().format(df));
        vo.setCreateTime(TimeUtils.localDateTimeToMills(dto.getCreateTime()));
        vo.setPhotoList(dto.getPhotoList());
        vo.setCheckinTime(TimeUtils.localDateTimeToMills(dto.getCheckinTime()));
        vo.setCheckoutTime(TimeUtils.localDateTimeToMills(dto.getCheckoutTime()));
        vo.setApplyUserId(dto.getApplyUserId());
        vo.setApplyUserName(dto.getApplyUserName());
        vo.setFinishTime(TimeUtils.localDateTimeToMills(dto.getFinishTime()));
        vo.setApplyText(dto.getApplyTextContent());
        vo.setPoiId(dto.getStoreId());
        vo.setPoiName(dto.getStoreName());
        vo.setApprovalStatus(dto.getApprovalStatus());
        vo.setApprovalStatusText(dto.getApprovalStatusText());
        vo.setApprovalTimes(dto.getApprovalTimes());
        vo.setMaxApprovalTimes(dto.getMaxApprovalTimes());
        return vo;
    }

    private AttendanceApprovalItemVO convertToVO(AttendanceApprovalDTO dto) {
        if (dto == null) {
            return null;
        }
        AttendanceApprovalItemVO vo = new AttendanceApprovalItemVO();
        vo.setId(dto.getBaseInfoId());

        if (dto.getBaseInfoDTO() != null) {
            AttendanceApprovalBaseInfoDTO baseInfoDTO = dto.getBaseInfoDTO();
            vo.setResultStatisticsId(baseInfoDTO.getResultStatisticsId());
            vo.setApplyUserId(baseInfoDTO.getApplyUserId());
            vo.setApplyUserName(baseInfoDTO.getApplyUserName());
            vo.setEventScene(baseInfoDTO.getEventScene());
            vo.setApplyText(baseInfoDTO.getApplyText());
            vo.setApprovalStatus(baseInfoDTO.getApprovalStatus());
            vo.setApprovalStatusText(baseInfoDTO.getApprovalStatusText());
            vo.setCreateTime(baseInfoDTO.getCreateTime());
            if (baseInfoDTO.getFinishTime() != null && baseInfoDTO.getFinishTime() > 0) {
                vo.setFinishTime(baseInfoDTO.getFinishTime());
            }
            vo.setPhotoList(baseInfoDTO.getPhotoList());
            vo.setApprovalTimes(baseInfoDTO.getApprovalTimes());
            vo.setMaxApprovalTimes(baseInfoDTO.getMaxApprovalTimes());
        }
        if (dto.getStatDTO() != null) {
            AttendanceApprovalStatDTO statDTO = dto.getStatDTO();
            vo.setResultDate(statDTO.getResultStatisticsDate());
            vo.setPoiId(statDTO.getPoiId());
            vo.setPoiName(statDTO.getPoiName());
            vo.setAttendanceExceptions(statDTO.getAttendanceExceptions());
            vo.setStartWorkCheckinTime(statDTO.getStartWorkCheckinTime());
            vo.setEndWorkCheckinTime(statDTO.getEndWorkCheckinTime());
            vo.setCheckinDistanceToPoi(statDTO.getCheckinDistanceToPoi());
            if (statDTO.getCheckinJsonContentDTO() != null) {
                vo.setCheckinPhotoUrl(statDTO.getCheckinJsonContentDTO().getPhotoUrl());
            }
            vo.setExceptionDuration(statDTO.getExceptionDuration());
        }
        return vo;
    }
}
