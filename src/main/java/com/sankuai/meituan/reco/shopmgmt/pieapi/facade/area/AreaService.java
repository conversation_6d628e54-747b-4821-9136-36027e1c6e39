package com.sankuai.meituan.reco.shopmgmt.pieapi.facade.area;

import com.meituan.linz.boot.util.Fun;
import com.meituan.linz.product.channel.EnhanceChannelType;
import com.meituan.linz.thrift.response.Status;
import com.sankuai.meituan.reco.shopmgmt.pieapi.controller.area.request.QuerySingleLevelAreaRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.bo.SingleAreaBo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.BaseRequestSimple;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.GetAreaListRequest;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.common.GetAreaListResponse;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelBaseMsgThriftService;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.waimai.thrift.command.ProductLibOriginQueryCommand;
import com.sankuai.meituan.waimai.thrift.domain.WmProductLibOrigin;
import com.sankuai.meituan.waimai.thrift.service.ProductLibOriginThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/10/31
 */
@Slf4j
@Service
public class AreaService {

    private static final Integer QUERY_FROM_OPEN = 2;
    @Autowired
    private ChannelBaseMsgThriftService.Iface channelBaseMsgThriftService;
    @Autowired
    private ProductLibOriginThriftService.Iface productLibOriginThriftService;

    /**
     * 查询单一层级区域，用于美团渠道类目属性中的产地下拉框加载
     */
    public List<SingleAreaBo> querySingleLevelArea(QuerySingleLevelAreaRequest request) {
        List<SingleAreaBo> singleAreaList;
        if (QUERY_FROM_OPEN.equals(request.getDataSource())) {
            singleAreaList = querySingleLevelAreaFromOpen(request);
        } else {
            singleAreaList = querySingleLevelAreaFromWaimai(request);
        }
        return singleAreaList;
    }

    /**
     * 从外卖标品库接口查询单一层级区域
     */
    private List<SingleAreaBo> querySingleLevelAreaFromWaimai(QuerySingleLevelAreaRequest request) {
        ProductLibOriginQueryCommand command = new ProductLibOriginQueryCommand();
        if (null != request.getParentId()) {
            command.setParentId(request.getParentId());
        }
        command.setOriginName(request.getAreaName());
        command.setLevel(request.getLevel());
        command.setValid(request.getValid());
        log.info("ProductLibOriginThriftService.getOriginByCommand request:{}", command);
        try {
            List<WmProductLibOrigin> wmProductLibOrigins = productLibOriginThriftService.getOriginByCommand(command);
            return Fun.map(wmProductLibOrigins, SingleAreaBo::of);
        } catch (Exception e) {
            log.error("查询单一层级地区信息失败, request:{}, reason:{}", command, e);
            throw new BizException("查询单一层级地区信息失败");
        }
    }

    /**
     * 从开放平台接口查询单一层级区域
     */
    private List<SingleAreaBo> querySingleLevelAreaFromOpen(QuerySingleLevelAreaRequest request) {
        GetAreaListRequest rpcRequest = new GetAreaListRequest();
        if (null != request.getParentId()) {
            rpcRequest.setParentAreaId(request.getParentId());
        }
        if (StringUtils.isNotBlank(request.getAreaName())) {
            rpcRequest.setKeyword(request.getAreaName());
        }
        rpcRequest.setBaseInfo(new BaseRequestSimple(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId(),
                EnhanceChannelType.MT.getChannelId()));
        log.info("OnlineCommonInfoFacade.queryAreaList request:{}", request);
        GetAreaListResponse response;
        try {
            response = channelBaseMsgThriftService.getAreaList(rpcRequest);
        } catch (Exception e) {
            log.error("查询单一层级地区信息失败, request:{}, reason:{}", rpcRequest, e);
            throw new BizException("查询单一层级地区信息失败");
        }
        if (Objects.isNull(response) || Status.SUCCESS_CODE != response.getStatus().getCode()) {
            log.error("查询单一层级地区信息失败, request:{}, reason:{}", rpcRequest, response.getStatus().getMsg());
            throw new BizException("查询单一层级地区信息失败");
        }
        return Fun.map(response.getAreaList(), SingleAreaBo::of);
    }

}