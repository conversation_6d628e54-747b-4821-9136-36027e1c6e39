package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.WarehousePickCompleteModuleVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@TypeDoc(
        description = "查询已拣货列表V2返回"
)
@Data
@Builder
@ApiModel("查询已拣货列表V2返回")
public class WarehousePickCompleteQueryResponseV2 {

    @FieldDoc(
            description = "已拣货数据列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "已拣货数据列表", required = true)
    private List<WarehousePickCompleteModuleVO> dataList;

    @FieldDoc(
            description = "是否还有下一页", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否还有下一页", required = true)
    private Boolean hasMore;

    @FieldDoc(
            description = "是否走新版本查询", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否走新版本查询", required = true)
    private Boolean isNewVersion;
}
