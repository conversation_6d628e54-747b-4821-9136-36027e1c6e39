package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.qualification;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelqualification.dto.ChannelCategoryQualificationDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/7/1 17:54
 **/
@TypeDoc(
        name = "资质vo对象"
)
@Data
@ToString
@EqualsAndHashCode
public class ChannelCategoryQualificationVo {

    private Boolean isAllUploaded;

    private List<ChannelQualificationVo> qualificationList;


    public static ChannelCategoryQualificationVo fromDto(ChannelCategoryQualificationDTO dto){
        if(dto == null){
            return null;
        }
        ChannelCategoryQualificationVo vo = new ChannelCategoryQualificationVo();
        vo.setIsAllUploaded(dto.getIsAllUploaded());
        if(CollectionUtils.isNotEmpty(dto.getQualificationList())){
            vo.setQualificationList(dto.getQualificationList().stream().map(ChannelQualificationVo::fromDto).collect(Collectors.toList()));
        }
        return vo;
    }

}
