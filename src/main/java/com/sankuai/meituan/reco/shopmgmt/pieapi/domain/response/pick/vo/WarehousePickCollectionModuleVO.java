package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo;

import com.facebook.swift.codec.ThriftField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.GoodsOwnerVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "待集单数据"
)
@Data
@ApiModel("待集单数据")
public class WarehousePickCollectionModuleVO {

    @FieldDoc(
            description = "出库单号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "出库单号")
    private String outWarehouseOrderNo;

    @FieldDoc(
            description = "收货门店号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "收货门店号")
    private Long poiId;

    @FieldDoc(
            description = "收货门店名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "收货门店名称")
    private String poiName;

    @FieldDoc(
            description = "创建时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @FieldDoc(
            description = "货主id", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "货主id")
    private String skuOwnerId;

    @FieldDoc(
            description = "货主名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "货主名称")
    private String skuOwnerName;

    @FieldDoc(
            description = "出库单类型", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "出库单类型")
    private Integer type;

    @FieldDoc(
            description = "推单时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "推单时间")
    private String pushOrderTime;

    @FieldDoc(
            description = "计划生产时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "计划生产时间")
    private String planProductTime;

    @FieldDoc(
            description = "计划运抵时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "计划运抵时间")
    private String planArriveTime;

    @FieldDoc(
            description = "sku种类数",
            requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "sku种类数")
    private Long totalSkuKind;

    @FieldDoc(
            description = "sku件数",
            requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "sku件数")
    private Long totalSkuCount;

    @FieldDoc(
            description = "网格名称",
            requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "网格名称")
    private String gridName;

    @FieldDoc(
            description = "货主信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "货主信息")
    private List<GoodsOwnerVO> goodsOwnerList;

    @FieldDoc(
            description = "波次单号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "波次单号")
    private String waveOrderNo;

    @FieldDoc(
            description = "出库单状态", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "出库单状态")
    private Integer outboundOrderStatus;
}
