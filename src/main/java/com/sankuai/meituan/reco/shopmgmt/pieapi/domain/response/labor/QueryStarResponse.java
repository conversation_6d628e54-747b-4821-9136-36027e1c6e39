package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.HireApprovalVO;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-08-01
 * @email <EMAIL>
 */
@TypeDoc(
        description = "查询星级"
)
@Data
@ApiModel("查询星级")
@AllArgsConstructor
@NoArgsConstructor
public class QueryStarResponse {

    @FieldDoc(
            description = "是否有更多"
    )
    private Integer star;

    @FieldDoc(
            description = "总分"
    )
    private String totalPoints;

    @FieldDoc(
            description = "距离下一个等级的分数，-1为达到了上限"
    )
    private String nextStarPoints;

}
