package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.picking;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/12/14 16:17
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PickConfigVO {
    private Integer maxUploadPicCount;

    private List<ExamplePicInfo> allExamplePicInfoList;

    private List<GoodsTypeConfig> goodsTypeConfigList;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ExamplePicInfo {
        private Integer type;

        private String name;

        private String url;

        /**
         * 展示顺序
         */
        private Integer order;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class GoodsTypeConfig {
        private Integer type;

        private String typeName;

        private List<Integer> examplePicTypeList;
    }
}
