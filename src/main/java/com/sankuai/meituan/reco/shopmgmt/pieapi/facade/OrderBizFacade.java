package com.sankuai.meituan.reco.shopmgmt.pieapi.facade;

import com.dianping.rhino.annotation.Degrade;
import com.google.common.collect.Maps;
import com.meituan.shangou.saas.common.enums.ChannelTypeEnum;
import com.meituan.shangou.saas.dto.StatusCodeEnum;
import com.meituan.shangou.saas.dto.request.ocms.OCMSOrderCancelRequest;
import com.meituan.shangou.saas.dto.request.ocms.OCMSOrderPartRefundRequest;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderItemMoneyRefundCheckModel;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderModel;
import com.meituan.shangou.saas.o2o.dto.model.OCMSOrderKey;
import com.meituan.shangou.saas.o2o.dto.model.OCMSOrderPartRefundProductModel;
import com.meituan.shangou.saas.o2o.dto.request.BizOrderMoneyRefundCheckRequest;
import com.meituan.shangou.saas.o2o.dto.request.BizOrderMoneyRefundRequest;
import com.meituan.shangou.saas.o2o.dto.request.BizOrderQueryRequest;
import com.meituan.shangou.saas.o2o.dto.request.OCMSOperateCheckRequest;
import com.meituan.shangou.saas.o2o.dto.request.OCMSTenantAgreeRefundRequest;
import com.meituan.shangou.saas.o2o.dto.request.OCMSTenantRejectRefundRequest;
import com.meituan.shangou.saas.o2o.dto.response.BizOrderMoneyRefundCheckResponse;
import com.meituan.shangou.saas.o2o.dto.response.BizOrderQueryResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.meituan.shangou.saas.o2o.dto.response.OCMSOperateCheckResponse;
import com.meituan.shangou.saas.o2o.service.BizOrderThriftService;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.meituan.shangou.saas.order.platform.enums.OrderSourceEnum;
import com.meituan.shangou.saas.service.ocms.OCMSOrderOperateThriftService;
import com.meituan.shangou.saas.service.ocms.OCMSOrderThriftService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order.ApproveRefundRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order.CancelOrderRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order.OrderMoneyRefundCheckRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order.OrderMoneyRefundRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order.PartRefundRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @createTime 2020/4/19
 * @description
 */
@Service
@Slf4j
public class OrderBizFacade {

    @Autowired
    private BizOrderThriftService bizOrderThriftService;

    @Resource
    OCMSOrderOperateThriftService ocmsOrderOperateThriftService;

    @Autowired
    private OCMSOrderThriftService ocmsOrderThriftService;

    /**
     * 查询订单基本信息
     *
     * @param orderId
     * @param channelId
     * @param tenantId
     * @return
     */
    @MethodLog(logRequest = true, logResponse = true)
    public BizOrderModel queryOrderModel(String orderId, int channelId, Long tenantId) throws TException {
        BizOrderQueryRequest req = new BizOrderQueryRequest();
        req.setOrderBizType(ChannelOrderConvertUtils.sourceMid2Biz(channelId));
        req.setViewOrderId(orderId);
        req.setTenantId(tenantId);
        req.setOrderSource(OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue());
        req.setFromMaster(true);

        BizOrderQueryResponse resp = bizOrderThriftService.query(req);

        if (resp.getBizOrderModel() == null) {
            throw new BizException("订单不存在");
        }
        return resp.getBizOrderModel();
    }

    /**
     * 查询订单基本信息
     *
    */
    @MethodLog(logRequest = true, logResponse = true)
    public BizOrderModel queryOrderModelByOrderId(Long orderId){
        BizOrderQueryResponse resp = null;
        try {
            resp = bizOrderThriftService.query4OrderId(orderId);
            log.info("invoke OrderBizFacade.queryOrderModel,orderId:{},result:{}", orderId, resp);
        } catch (TException e) {
            throw new BizException("查询订单失败");
        }

        if (resp == null || resp.getStatus() == null || resp.getStatus().getCode() != StatusCodeEnum.SUCCESS.getCode()) {
            log.error("OrderBizFacade.queryOrderModel error,orderId:{},result:{}", orderId, resp);
            throw new BizException("查询订单失败");
        }

        if (resp.getBizOrderModel() == null) {
            throw new BizException("订单不存在");
        }

        return resp.getBizOrderModel();
    }
    /**
     * 订单调整
     *
     * @param adjustBO
     * @return
     */
//    public CommonResultBO adjustOrder(ChannelOrderAdjustBO adjustBO) {
//        OCMSOrderAdjustRequest request = new OCMSOrderAdjustRequest();
//        request.setTenantId(adjustBO.getTenantId());
//        request.setOrderBizType(ChannelOrderConvertUtils.sourceMid2Biz(adjustBO.getChannelId()));
//        request.setRequestId(adjustBO.getRequestId());
//        request.setViewOrderId(adjustBO.getOrderId());
//        request.setOcmsOrderAdjustProductModelList(convertAdjustProduct(adjustBO.getOrderItemAdjustBOList()));
//        request.setReason(adjustBO.getComments());
//        request.setOperatorAccount(adjustBO.getOperatorAccount());
//        request.setOperatorName(adjustBO.getOperatorUserName());
//        try {
//            log.info("ocmsOrderThriftService.adjustOrder request:{}", request);
//            CommonResponse response = ocmsOrderThriftService.adjustOrder(request);
//            log.info("ocmsOrderThriftService.adjustOrder response:{}", response);
//            if (response == null || response.getStatus() == null) {
//                return CommonResultBO.builder().success(false).message(SYSTEM_ERROR).build();
//            }
//            if (StatusCodeEnum.SUCCESS.getCode() == response.getStatus().getCode()) {
//                return CommonResultBO.builder().success(true).message("").build();
//            } else {
//                return CommonResultBO.builder().success(false).message(response.getStatus().getMessage()).build();
//            }
//
//        } catch (TException tex) {
//            log.error("ocmsOrderThriftService.adjustOrder TException request:{}", request, tex);
//            return CommonResultBO.builder().success(false).message(SYSTEM_ERROR).build();
//        }
//    }

//    public CommonDataBO<List<OCMSOrderAdjustLog>> queryOrderAdjustLog(Long tenantId, Integer channelId, String orderId) {
//        OCMSOrderQueryAdjustRecordRequest request = new OCMSOrderQueryAdjustRecordRequest();
//        request.setTenantId(tenantId);
//        request.setOrderBizType(ChannelOrderConvertUtils.sourceMid2Biz(channelId));
//        request.setViewOrderId(orderId);
//        CommonDataBO<List<OCMSOrderAdjustLog>> dataBO = new CommonDataBO<List<OCMSOrderAdjustLog>>();
//        try {
//            CommonDataResponse<List<OCMSOrderAdjustLog>> response = ocmsOrderThriftService.queryAdjustOrderRecord(request);
//
//            if (response == null || response.getStatus() == null || StatusCodeEnum.SUCCESS.getCode() != response.getStatus().getCode()) {
//                dataBO.setSuccess(Boolean.FALSE);
//                dataBO.setMessage(SYSTEM_ERROR);
//            } else {
//                dataBO.setSuccess(Boolean.TRUE);
//                dataBO.setData(response.getData());
//            }
//        } catch (TException tex) {
//            log.error("ocmsOrderThriftService.queryAdjustOrderRecord TException request:{}", request, tex);
//            dataBO.setSuccess(Boolean.FALSE);
//            dataBO.setMessage(SYSTEM_ERROR);
//        }
//        return dataBO;
//    }

    /**
     * 转换调节商品
     *
     * @param orderItemAdjustBOList
     * @return
     */
//    private List<OCMSOrderAdjustProductModel> convertAdjustProduct(List<ChannelOrderAdjustBO.OrderItemAdjustBO> orderItemAdjustBOList) {
//        List<OCMSOrderAdjustProductModel> adjustProductModelList = Lists.newArrayList();
//        if (CollectionUtils.isEmpty(orderItemAdjustBOList)) {
//            return adjustProductModelList;
//        }
//        for (ChannelOrderAdjustBO.OrderItemAdjustBO orderItemAdjustBO : orderItemAdjustBOList) {
//            OCMSOrderAdjustProductModel adjustProductModel = new OCMSOrderAdjustProductModel();
//            adjustProductModel.setOrderItemId(orderItemAdjustBO.getOrderItemId());
//            adjustProductModel.setBoothId(orderItemAdjustBO.getBoothId());
//            adjustProductModel.setOfflinePrice(orderItemAdjustBO.getOfflinePrice());
//            adjustProductModel.setLastUpdateTime(orderItemAdjustBO.getLastUpdateTime());
//            adjustProductModelList.add(adjustProductModel);
//        }
//        return adjustProductModelList;
//    }


    /**
     * 查询订单可操作列表
     *
     * @param tenantId  租户ID
     * @param orderKeys 渠道订单号
     * @return 可操作列表
     */
    public Map<OCMSOrderKey, List<Integer>> queryOrderOperateItems(Long tenantId, List<OCMSOrderKey> orderKeys) throws TException {
        return queryOrderOperateItems(tenantId, orderKeys, Collections.emptyList());
    }

    /**
     * 查询订单可操作列表
     *
     * @param tenantId  租户ID
     * @param orderKeys 渠道订单号
     * @param operateItems 待检查的操作项
     * @return 可操作列表
     */
    public Map<OCMSOrderKey, List<Integer>> queryOrderOperateItems(Long tenantId, List<OCMSOrderKey> orderKeys,
                                                                   List<Integer> operateItems) throws TException {
        OCMSOperateCheckRequest request = new OCMSOperateCheckRequest();
        request.setTenantId(tenantId);
        request.setOrderList(orderKeys);
        request.setToCheckOperateItems(operateItems);
        OCMSOperateCheckResponse response = ocmsOrderOperateThriftService.checkOrderCouldOperateItems(request);
        if (response == null) {
            return Maps.newHashMap();
        }
        return Objects.isNull(response.getCouldOperateItems()) ? new HashMap<>() : response.getCouldOperateItems();
    }


    public CommonResponse tenantPartRefund(PartRefundRequest request) {
        try {
            OCMSOrderPartRefundRequest bizRequest = buildPartRefundRequest(request);
            com.meituan.shangou.saas.o2o.dto.response.CommonResponse response = ocmsOrderOperateThriftService.tenantPartRefund(bizRequest);
            log.info("商家部分退款接口调用request：{}, response:{}", bizRequest, response);
            if (success(response)){
                return CommonResponse.success(null);
            }
            return CommonResponse.fail(response.getStatus().getCode(), response.getStatus().getMessage());
        }catch (Exception e){
            log.error("orderbiz tenantPartRefund TException request:{}", request, e);
            return CommonResponse.fail(ResultCode.FAIL);
        }
    }

    public CommonResponse tenantAfterSaleRefund(PartRefundRequest request) {
        try {
            OCMSOrderPartRefundRequest bizRequest = buildPartRefundRequest(request);
            com.meituan.shangou.saas.o2o.dto.response.CommonResponse response = ocmsOrderOperateThriftService.tenantAfterSaleRefund(bizRequest);
            log.info("商家售后退款接口调用request：{}, response:{}", bizRequest, response);
            if (success(response)){
                return CommonResponse.success(null);
            }
            return CommonResponse.fail(response.getStatus().getCode(), response.getStatus().getMessage());
        }catch (Exception e){
            log.error("orderbiz tenantAfterSaleRefund TException request:{}", request, e);
            return CommonResponse.fail(ResultCode.FAIL);
        }
    }


    public CommonResponse tenantCancelOrder(CancelOrderRequest request) {
        try {
            OCMSOrderCancelRequest bizRequest = buildCancelOrderRequest(request);
            com.meituan.shangou.saas.o2o.dto.response.CommonResponse response = ocmsOrderOperateThriftService.tenantCancelOrder(bizRequest);
            log.info("商家全单取消接口调用request：{}, response:{}", bizRequest, response);
            if (success(response)){
                return CommonResponse.success(null);
            }
            return  CommonResponse.fail(ResultCode.FAIL, responseMsg(response));
        }catch (Exception e){
            log.error("orderbiz tenantCancelOrder TException request:{}", request, e);
            return  CommonResponse.fail(ResultCode.FAIL, "未知异常");
        }
    }


//    public CommonDataBO<List<UiOption>> refundReasonList(QueryRefundReasonRequest request) {
//        CommonDataBO<List<UiOption>> responseData = new CommonDataBO();
//        try {
//            OCMSRefundReasonListRequest bizRequest = new OCMSRefundReasonListRequest();
//            bizRequest.setRefundType(request.getOrderType());
//            bizRequest.setTenantId(ContextHolder.currentUserTenantId());
//            OCMSRefundReasonListResponse response = ocmsOrderOperateThriftService.refundReasonCodeList(bizRequest);
//            if (success(response)){
//                responseData.setSuccess(true);
//                responseData.setData(response.getPossibleRefundReasons().stream()
//                        .map(model-> new UiOption(String.valueOf(model.getCode()), model.getReason()))
//                        .collect(Collectors.toList()));
//            }
//            return responseData;
//        }catch (Exception e){
//            log.error("orderbiz refundReasonList TException request:{}", request, e);
//            responseData.setSuccess(false);
//            responseData.setMessage(SYSTEM_ERROR);
//            return responseData;
//        }
//
//    }

    public void agreeRefund(ApproveRefundRequest approveRefundRequest) {
        OCMSTenantAgreeRefundRequest bizRequest = new OCMSTenantAgreeRefundRequest();
        bizRequest.setAfsApplyType(approveRefundRequest.getAfsApplyType());
        bizRequest.setAfterSaleId(approveRefundRequest.getAfterSaleId());
        bizRequest.setOperatorUserId(approveRefundRequest.getOptUserId());
        bizRequest.setOperatorUserName(approveRefundRequest.getOptUserName());
        bizRequest.setOrderBizType(
                ChannelOrderConvertUtils.sourceMid2Biz(approveRefundRequest.getChannelType())
        );
        bizRequest.setReason(StringUtils.defaultString(approveRefundRequest.getReason(), "OK"));
        bizRequest.setTenantId(approveRefundRequest.getTenantId());
        bizRequest.setViewOrderId(approveRefundRequest.getChannelOrderId());
        try {
            com.meituan.shangou.saas.o2o.dto.response.CommonResponse resp = ocmsOrderOperateThriftService.agreeRefundByTenant(bizRequest);
            if (!success(resp)){
                throw new BizException(resp.getStatus().getCode(), resp.getStatus().getMessage());
            }
        }catch (BizException bizException){
            log.error("orderBiz agreeRefund 失败 request:{}", approveRefundRequest, bizException);
            throw bizException;
        }catch (Exception e){
            log.error("orderBiz agreeRefund TException request:{}", approveRefundRequest, e);
            throw new BizException("审批退款异常", e);
        }
    }

    public void rejectRefund(ApproveRefundRequest approveRefundBO) {
        OCMSTenantRejectRefundRequest bizRequest = new OCMSTenantRejectRefundRequest();
        bizRequest.setAfsApplyType(approveRefundBO.getAfsApplyType());
        bizRequest.setAfterSaleId(approveRefundBO.getAfterSaleId());
        bizRequest.setOperatorUserId(approveRefundBO.getOptUserId());
        bizRequest.setOperatorUserName(approveRefundBO.getOptUserName());
        bizRequest.setOrderBizType(
                ChannelOrderConvertUtils.sourceMid2Biz(approveRefundBO.getChannelType())
        );
        bizRequest.setReason(StringUtils.defaultString(approveRefundBO.getReason(), "OK"));
        bizRequest.setTenantId(approveRefundBO.getTenantId());
        bizRequest.setViewOrderId(approveRefundBO.getChannelOrderId());
        try {
            com.meituan.shangou.saas.o2o.dto.response.CommonResponse resp = ocmsOrderOperateThriftService.rejectRefundByTenant(bizRequest);
            if (!success(resp)){
                throw new BizException(resp.getStatus().getCode(), resp.getStatus().getMessage());
            }
        }catch (BizException bizException){
            log.error("orderBiz rejectRefund 失败 request:{}", approveRefundBO, bizException);
            throw bizException;
        }catch (Exception e){
            log.error("orderBiz rejectRefund TException request:{}", approveRefundBO, e);
            throw new BizException("审批退款异常", e);
        }
    }


    private OCMSOrderCancelRequest buildCancelOrderRequest(CancelOrderRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        OCMSOrderCancelRequest bizRequest = new OCMSOrderCancelRequest();
        bizRequest.setOperatorUserId(user.getAccountId());
        bizRequest.setOperatorUserName(user.getOperatorName());
        bizRequest.setAppId(ApiMethodParamThreadLocal.getIdentityInfo().getAppId());
        bizRequest.setOperatorAccountId(user.getAccountId());
        bizRequest.setOrderBizType(ChannelOrderConvertUtils.sourceMid2Biz(
                request.getChannelId()
        ));
        bizRequest.setReason(request.getReason());
        bizRequest.setReasonCode(request.getReasonCode());
        bizRequest.setTenantId(user.getTenantId());
        bizRequest.setViewOrderId(request.getChannelOrderId());
        return bizRequest;
    }


    private String responseMsg(com.meituan.shangou.saas.o2o.dto.response.CommonResponse response) {
        return response != null && response.getStatus() != null ? response.getStatus().getMessage() : StatusCodeEnum.FAIL.getMessage();
    }

    private boolean success(com.meituan.shangou.saas.o2o.dto.response.CommonResponse response) {
        return response != null && response.getStatus() != null
                && Objects.equals(response.getStatus().getCode(), StatusCodeEnum.SUCCESS.getCode());
    }

    private OCMSOrderPartRefundRequest buildPartRefundRequest(PartRefundRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        OCMSOrderPartRefundRequest bizRequest = new OCMSOrderPartRefundRequest();
        bizRequest.setOperatorUserId(user.getAccountId());
        bizRequest.setOperatorUserName(user.getOperatorName());
        bizRequest.setAppId(ApiMethodParamThreadLocal.getIdentityInfo().getAppId());
        bizRequest.setOperatorAccountId(user.getAccountId());
        bizRequest.setOrderBizType(
                ChannelOrderConvertUtils.sourceMid2Biz(
                    request.getChannelId()
                ));
        bizRequest.setPartRefundProductModelList(request.getPartRefundProductList().stream().map(refundItem->{
            OCMSOrderPartRefundProductModel model = new OCMSOrderPartRefundProductModel();
            model.setCount(refundItem.getCount());
            model.setCustomSkuId(refundItem.getCustomSkuId());
            model.setSkuId2(refundItem.getSkuId());
            model.setSkuName(refundItem.getSkuName());
            if(StringUtils.isNotBlank(refundItem.getExtCustomSkuId())){
                model.setExtCustomSkuId(refundItem.getExtCustomSkuId());
            }
            return model;
        }).collect(Collectors.toList()));
        bizRequest.setReason(request.getReason());
        bizRequest.setReasonCode(request.getReasonCode());
        bizRequest.setShopId(request.getStoreId());
        bizRequest.setTenantId(user.getTenantId());
        bizRequest.setViewOrderId(request.getChannelOrderId());
        bizRequest.setRequestId(System.currentTimeMillis() + request.getChannelOrderId());
        return bizRequest;
    }

    public List<BizOrderItemMoneyRefundCheckModel> moneyRefundCheck(OrderMoneyRefundCheckRequest request) {
        try {
            BizOrderMoneyRefundCheckRequest moneyRefundCheckRequest = request.convertToBizOrderMoneyRefundCheckRequest();
            moneyRefundCheckRequest.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
            BizOrderMoneyRefundCheckResponse response = ocmsOrderOperateThriftService.moneyRefundCheck(moneyRefundCheckRequest);
            log.info("moneyRefundCheck request:{}, response:{}", moneyRefundCheckRequest, response);
            if (response != null && response.getStatus() != null
                    && Objects.equals(response.getStatus().getCode(), StatusCodeEnum.SUCCESS.getCode())) {
                return response.getBizOrderItemMoneyRefundCheckModelList();
            }
            return Lists.newArrayList();
        } catch (Exception e) {
            log.error("moneyRefundCheck error request:{}", request, e);
            return Lists.newArrayList();
        }
    }

    public CommonResponse moneyRefund(OrderMoneyRefundRequest request) {
        try {
            BizOrderMoneyRefundRequest moneyRefundRequest = request.convertToBizOrderMoneyRefundRequest();
            moneyRefundRequest.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
            moneyRefundRequest.setOperatorAccountId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId());
            moneyRefundRequest.setAppId(ApiMethodParamThreadLocal.getIdentityInfo().getAppId());
            com.meituan.shangou.saas.o2o.dto.response.CommonResponse response = ocmsOrderOperateThriftService.moneyRefund(moneyRefundRequest);
            log.info("moneyRefund request:{}, response:{}", moneyRefundRequest, response);
            if (success(response)) {
                return CommonResponse.success(null);
            }
            return CommonResponse.fail(response.getStatus().getCode(), response.getStatus().getMessage());
        } catch (Exception e) {
            log.error("moneyRefund error request:{}", request, e);
            return CommonResponse.fail(ResultCode.FAIL);
        }
    }


    @Degrade(rhinoKey = "OrderBizRemoteService.queryOrderDetail",
            fallBackMethod = "queryOrderDetailFallback",
            timeoutInMilliseconds = 2000)
    public BizOrderModel queryOrderDetailWithComposeSku(Long tenantId, Long storeId, Integer channelId, String viewOrderId) throws TException {

        BizOrderQueryRequest request = new BizOrderQueryRequest();
        request.setTenantId(tenantId);
        request.setViewOrderId(viewOrderId);
        request.setShopId(storeId);
        request.setOrderBizType(ChannelOrderConvertUtils.convertBizType(channelId));
        request.setContainsComposeSku(true);
        log.info("start invoke bizOrderThriftService.query, request: {}", request);
        BizOrderQueryResponse response = bizOrderThriftService.query(request);
        log.info("end invoke bizOrderThriftService.query, response: {}", response);

        if (!Objects.equals(response.getStatus().getCode(), com.meituan.reco.pickselect.common.constants.ResultCode.SUCCESS.getCode())) {
            throw new ThirdPartyException("调用订单服务查询订单明细失败");
        }

        return response.getBizOrderModel();
    }

    public BizOrderModel queryOrderDetailFallback(Long tenantId, Long storeId, Integer orderSource, String viewOrderId) throws TException {
        log.warn("OrderBizRemoteService.queryOrderDetail 发生降级");
        return null;
    }
}