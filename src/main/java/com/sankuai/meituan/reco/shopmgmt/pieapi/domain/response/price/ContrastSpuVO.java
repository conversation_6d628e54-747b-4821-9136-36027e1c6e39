package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.shangou.empower.price.client.dto.contrast.ContrastSpuDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.List;
import java.util.Objects;

/**
 * @Author: wangyihao04
 * @Date: 2020-12-02 14:14
 * @Mail: <EMAIL>
 */
@TypeDoc(
        description = "门店间商品对比"
)
@ApiModel("门店间商品对比")
@Getter
@AllArgsConstructor
@ToString
public class ContrastSpuVO {
    @FieldDoc(
            description = "spu完整的一级/二级/三级类目名称"
    )
    @ApiModelProperty("spu完整的一级/二级/三级类目名称")
    public String fullCategoryName;

    @FieldDoc(
            description = "三级类目id"
    )
    @ApiModelProperty("三级类目id")
    public String thirdCategoryId;

    @FieldDoc(
            description = "当前门店匹配商品列表"
    )
    @ApiModelProperty("当前门店匹配商品列表")
    public List<ContrastSpuDetailVO> selfMatchedSpus;

    @FieldDoc(
            description = "对比门店匹配商品列表"
    )
    @ApiModelProperty("对比门店匹配商品列表")
    public List<ContrastSpuDetailVO> contrastMatchedSpus;

    @FieldDoc(
            description = "当前门店不匹配商品列表"
    )
    @ApiModelProperty("当前门店不匹配商品列表")
    public List<ContrastSpuDetailVO> selfUnmatchedSpus;

    @FieldDoc(
            description = "对比门店不匹配商品列表"
    )
    @ApiModelProperty("对比门店不匹配商品列表")
    public List<ContrastSpuDetailVO> contrastUnmatchedSpus;

    public static ContrastSpuVO valueOf(ContrastSpuDTO dto){
        if (Objects.isNull(dto)){
            return null;
        }

        return new ContrastSpuVO(
                dto.getFullCategoryName(),
                dto.getThirdCategoryId(),
                ConverterUtils.convertList(dto.getSelfMatchedSpus(), ContrastSpuDetailVO::valueOf),
                ConverterUtils.convertList(dto.getContrastMatchedSpus(), ContrastSpuDetailVO::valueOf),
                ConverterUtils.convertList(dto.getSelfUnmatchedSpus(), ContrastSpuDetailVO::valueOf),
                ConverterUtils.convertList(dto.getContrastUnmatchedSpus(), ContrastSpuDetailVO::valueOf));

    }
}
