package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.AutoPickDoneConfig;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.ResvAutoPickDoneConfig;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.ChannelAutoPickDoneConfig;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.ResvChannelAutoPickDoneConfig;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.ActionAfterLackConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/2/5
 */
@TypeDoc(
        description = "拣货配置更新请求"
)
@ApiModel("拣货配置更新请求")
@Data
public class PickSelectConfigUpdateReq {
    @FieldDoc(
            description = "缺货处理配置", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "缺货处理配置")
    private ActionAfterLackConfig actionAfterLackConfig;
    @FieldDoc(
            description = "自动拣货完成配置", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "自动拣货完成配置")
    private AutoPickDoneConfig autoPickDoneConfig;

    @ApiModelProperty(value = "渠道自动拣货完成配置")
    private List<ChannelAutoPickDoneConfig> channelAutoPickDoneConfigList;

    @FieldDoc(
            description = "自动拣货完成类型", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "自动拣货完成类型")
    private Integer autoPickDoneType;

    @FieldDoc(
            description = "预约单自动拣货完成类型", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "预约单自动拣货完成类型")
    private Integer resvAutoPickDoneType;

    @FieldDoc(
            description = "自动拣货完成开关", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "自动拣货完成开关")
    private Integer autoPickDoneSwitch;
    @FieldDoc(
            description = "预约单自动拣货完成开关", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "预约单自动拣货完成开关")
    private Integer resvAutoPickDoneSwitch;
    @FieldDoc(
            description = "预约单自动拣货完成配置", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "预约单自动拣货完成配置")
    private ResvAutoPickDoneConfig resvAutoPickDoneConfig;

    @ApiModelProperty(value = "预约单渠道自动拣货完成配置")
    private List<ResvChannelAutoPickDoneConfig> resvChannelAutoPickDoneConfigList;

}
