package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.realtimesettle;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@TypeDoc(
        description = "实时结算交易值对象"
)
@Data
@ApiModel("实时结算交易值对象")
public class RealTimeSettleTradeVO {

    @FieldDoc(
            description = "交易单号", requiredness = Requiredness.REQUIRED
    )
    private String tradeNo;

}
