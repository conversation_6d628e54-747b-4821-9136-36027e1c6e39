package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.price.client.dto.contrast.ContrastCategoryIndexDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

/**
 * @Author: wangyihao04
 * @Date: 2020-12-02 10:53
 * @Mail: <EMAIL>
 */
@TypeDoc(
        description = "分类指标"
)
@ApiModel("分类指标")
@Getter
@AllArgsConstructor
@ToString
public class ContrastCategoryIndexVO {
    @FieldDoc(
            description = "竞对门店名称"
    )
    @ApiModelProperty("竞对门店名称")
    public String contrastStoreName;

    @FieldDoc(
            description = "竞对门店id"
    )
    @ApiModelProperty("竞对门店id")
    public Long contrastStoreId;

    @FieldDoc(
            description = "竞对门店类型"
    )
    @ApiModelProperty("竞对门店类型")
    public Integer contrastStoreType;

    @FieldDoc(
            description = "指标类型"
    )
    @ApiModelProperty("指标类型")
    public String type;

    @FieldDoc(
            description = "指标值"
    )
    @ApiModelProperty("指标值")
    public String index;

    public static ContrastCategoryIndexVO valueOf(ContrastCategoryIndexDTO dto){
        if(Objects.isNull(dto)){
            return null;
        }
        return new ContrastCategoryIndexVO(dto.getContrastStoreName(), dto.getContrastStoreId(),
                dto.getContrastStoreType(), dto.getType(), dto.getIndex());
    }
}
