package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.wavepick.converter;

import com.sankuai.meituan.reco.pickselect.thrift.wave.dto.WarehousePickItemSkuDTO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.WarehouseItemModuleVO;
import org.mapstruct.Mapper;

/**
 * @Description:
 * @Author: zhangjian155
 * @Date: 2022/8/25 18:20
 */
@Mapper(componentModel = "spring", uses = {WarehousePickItemSkuBatchConverter.class, WarehousePickItemSkuSpecConverter.class})
public abstract class WarehousePickItemSkuConverter {
    public abstract WarehouseItemModuleVO convert2Vo(WarehousePickItemSkuDTO warehousePickItemSkuDTO);
}
