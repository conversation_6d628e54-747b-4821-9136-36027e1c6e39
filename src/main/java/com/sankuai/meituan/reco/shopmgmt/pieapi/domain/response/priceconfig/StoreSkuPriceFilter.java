package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.priceconfig;

import com.sankuai.meituan.shangou.empower.price.client.dto.config.StoreSkuPriceFilterDTO;
import lombok.Data;

/**
 * 门店商品价格过滤器
 *
 * <AUTHOR>
 */
@Data
public class StoreSkuPriceFilter {

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * skuId
     */
    private String skuId;

    /**
     * 是否启用重量装换
     */
    private Boolean enableWeightConversion;

    /**
     * 是否启用称重类型
     */
    private Boolean enableWeightType;

    /**
     * 规则重量是否大于0, 两种情况:(1)启用称重类型, 称重类型为称重且重量大于0; (2)不启用称重类型, 重量大于0
     */
    private Boolean ruleWeightGtZero;

    /**
     * 是否可以根据重量转换价格
     */
    private Boolean canCalculatePriceByWeight;


    public static StoreSkuPriceFilter buildStoreSkuPriceFilter(StoreSkuPriceFilterDTO filterDTO) {
        if (filterDTO == null) {
            return null;
        }
        StoreSkuPriceFilter filter = new StoreSkuPriceFilter();
        filter.setStoreId(filterDTO.getStoreId());
        filter.setSkuId(filterDTO.getSkuId());
        filter.setEnableWeightConversion(filterDTO.getEnableWeightConversion());
        filter.setEnableWeightType(filterDTO.getEnableWeightType());
        filter.setRuleWeightGtZero(filterDTO.getRuleWeightGtZero());
        filter.setCanCalculatePriceByWeight(filterDTO.getCanCalculatePriceByWeight());

        return filter;
    }
}
