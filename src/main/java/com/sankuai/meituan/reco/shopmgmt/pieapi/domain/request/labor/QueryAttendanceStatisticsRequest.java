package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.TimeUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2022/10/19 17:24
 **/
@Data
@ApiModel("查看考勤统计请求")
@TypeDoc(description = "查看考勤统计请求")
public class QueryAttendanceStatisticsRequest {
    @FieldDoc(description = "开始日期 格式yyyy-MM-dd")
    @ApiModelProperty("开始日期 格式yyyy-MM-dd")
    private String beginDate;

    @FieldDoc(description = "结束日期 格式yyyy-MM-dd")
    @ApiModelProperty("结束日期 格式yyyy-MM-dd")
    private String endDate;

    @FieldDoc(description = "是否需要打卡扩展信息")
    @ApiModelProperty("是否需要打卡扩展信息")
    private Integer needCheckinJsonContent;

    public Optional<String> valid() {
        if (StringUtils.isBlank(beginDate) || StringUtils.isBlank(endDate)) {
            return Optional.of("开始日期或结束日期不能为空");
        }

        try {
            LocalDate beginDate = TimeUtils.stringFormatToLocalDate(this.beginDate);
            LocalDate endDate = TimeUtils.stringFormatToLocalDate(this.endDate);
            long days = beginDate.until(endDate, ChronoUnit.DAYS);
            if (days > 186) {
                return Optional.of("时间跨度不能超过6个月");
            } else {
                return Optional.empty();
            }
        } catch (Exception e) {
            return Optional.of("无效的日期格式");
        }
    }
}
