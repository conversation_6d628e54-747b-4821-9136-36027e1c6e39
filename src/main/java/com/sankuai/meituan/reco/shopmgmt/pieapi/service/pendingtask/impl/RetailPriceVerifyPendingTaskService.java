package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.impl;

import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ProjectConstants;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.AbstractSinglePendingTaskService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.AuthThriftWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.ChannelPriceServiceWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.OCMSServiceWrapper;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.PermissionGroupTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class RetailPriceVerifyPendingTaskService extends AbstractSinglePendingTaskService {

    @Resource
    private ChannelPriceServiceWrapper channelPriceServiceWrapper;
    @Resource
    private OCMSServiceWrapper ocmsServiceWrapper;
    @Resource
    private AuthThriftWrapper authThriftWrapper;

    @Override
    protected PendingTaskResult getPendingTaskCount(PendingTaskParam param) throws Exception {
        int count = 0;
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        List<Long> poiIds = authThriftWrapper.queryPermissionGroupId(user, PermissionGroupTypeEnum.POI, Long::valueOf);
        if (CollectionUtils.isNotEmpty(poiIds)){
            param.setStoreIds(poiIds);
            count = channelPriceServiceWrapper.queryRetailPriceQuoteReviewingCountList(param);
        }

        if (Objects.isNull(count)) {
            return null;
        }
        return PendingTaskResult.createNumberMarker(count);
    }

    @Override
    protected AuthCodeEnum module() {
        return AuthCodeEnum.RETAIL_PRICE_VERIFY;
    }
}
