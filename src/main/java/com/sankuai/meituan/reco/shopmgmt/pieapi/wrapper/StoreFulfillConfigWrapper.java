package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.google.common.collect.Lists;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.AutoPickDoneConfig;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.AutoPickDoneConfigInfo;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.ChannelAutoPickDoneConfig;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.Employee;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.EmployeeThriftService;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.FulfillConfig;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.FulfillConfigQueryResponse;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.OperateConfig;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.ResvAutoPickDoneConfig;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.ResvChannelAutoPickDoneConfig;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.Store;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.StoreFulfillConfigThriftService;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.StoreThriftService;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.exception.EbaseLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.config.AccountStoreFulfillConfigVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick.PickSelectConfigUpdateReq;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.store.saas.infrastructure.shield.common.monitor.CommonMonitorTransaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2019/11/20
 * @description
 */
@Slf4j
@Component
public class StoreFulfillConfigWrapper {

    @Resource
    private StoreFulfillConfigThriftService.Iface storeFulfillConfigThriftService;

    @Resource
    private StoreThriftService.Iface storeThriftService;

    @Resource
    private EmployeeThriftService.Iface ebaseEmployeeThriftService;

    @CommonMonitorTransaction
    public AccountStoreFulfillConfigVO getAccountStoreFulfillConfig(Long tenantId, Long storeId, Long accountId) {
        try {
            FulfillConfigQueryResponse fulfillConfigQueryResponse = storeFulfillConfigThriftService.batchQuery(tenantId, Lists.newArrayList(storeId), accountId);
            List<FulfillConfig> fulfillConfigList = fulfillConfigQueryResponse.getFulfillConfigList();
            if (CollectionUtils.isNotEmpty(fulfillConfigList)) {
                return convertAccountStoreFulfill(fulfillConfigList.get(0));
            } else {
                throw new CommonRuntimeException("查询门店履约配置异常", ResultCode.EBASE_EMPLOYEE_NO_AUTH);
            }
        } catch (TException e) {
            log.error("查询门店履约配置异常, error:{}", e.getMessage(), e);
            throw new CommonRuntimeException("查询门店履约配置异常", ResultCode.RETRY_INNER_FAIL);
        }
    }

    private AccountStoreFulfillConfigVO convertAccountStoreFulfill(FulfillConfig fulfillConfig) {
        AccountStoreFulfillConfigVO configVO = new AccountStoreFulfillConfigVO();
        configVO.setStoreId(fulfillConfig.getStoreId());
        configVO.setOrderAcceptMode(fulfillConfig.getOrderAcceptMode());
        configVO.setPickTaskReceiveMode(fulfillConfig.getPickTaskReceiveMode());
        configVO.setPickTaskMode(fulfillConfig.getPickTaskMode());
        configVO.setMergeTaskMode(fulfillConfig.getMergeTaskMode());
        return configVO;
    }

    @CommonMonitorTransaction
    public Store getStore(Long offlineStoreId) {

        try {
            Store store = storeThriftService.getStoreInfoByOfflineStoreId(offlineStoreId);
            if (store == null) {
                throw new CommonRuntimeException("查询履约门店信息异常", ResultCode.EBASE_EMPLOYEE_NO_AUTH);
            }
            return store;
        } catch (TException e) {
            log.error("查询履约门店信息异常,", e);
            throw new CommonRuntimeException("查询履约门店信息异常", ResultCode.RETRY_INNER_FAIL);

        }
    }

    @CommonMonitorTransaction
    public void updateStoreLackOperateConfig(Long offlineStoreId, OperateConfig operateConfig) {

        try {
            boolean isSuccess = storeThriftService.modifyStoreOperateConfig(offlineStoreId, operateConfig);
            if (!isSuccess) {
                throw new CommonRuntimeException("更新履约门店信息异常", ResultCode.EBASE_EMPLOYEE_NO_AUTH);
            }
        } catch (TException e) {
            log.error("更新履约门店信息异常,", e);
            throw new CommonRuntimeException("更新履约门店信息异常", ResultCode.RETRY_INNER_FAIL);

        }
    }

    @CommonMonitorTransaction
    public void updateAutoPickDoneConfig(Long offlineStoreId, AutoPickDoneConfig autoPickDoneConfig) {
        try {
            boolean isSuccess = storeThriftService.modifyAutoPickDoneConfig(offlineStoreId, autoPickDoneConfig);
            if (!isSuccess) {
                throw new CommonRuntimeException("更新自动拣货完成配置异常", ResultCode.EBASE_EMPLOYEE_NO_AUTH);
            }
        } catch (TException e) {
            log.error("更新自动拣货完成配置异常,", e);
            throw new CommonRuntimeException("更新自动拣货完成配置异常", ResultCode.RETRY_INNER_FAIL);
        }
    }

    public boolean checkUserBelongToStore(long accountId, Long offlineStoreId) {
        try {
            Employee employee = ebaseEmployeeThriftService.selectEmployeeInfoByAccountIdOfflineStoreId(accountId, offlineStoreId);
            return employee != null;

        } catch (EbaseLogicException e) {
            log.error("查询账号信息异常,", e);
            return false;
        } catch (TException e) {
            log.error("查询账号信息异常,", e);
            throw new CommonRuntimeException("查询账号信息异常", ResultCode.RETRY_INNER_FAIL);

        }

    }

    @MethodLog(logRequest = false, logResponse = true)
    @CommonMonitorTransaction
    public void updateAutoPickDoneConfigInfos(Long offlineStoreId, AutoPickDoneConfigInfo autoPickDoneConfigInfo) {
        try {
            boolean isSuccess = storeThriftService.modifyAutoPickDoneConfigInfos(offlineStoreId, autoPickDoneConfigInfo);
            if (!isSuccess) {
                throw new CommonRuntimeException("更新自动拣货完成配置异常", ResultCode.EBASE_EMPLOYEE_NO_AUTH);
            }
        } catch (TException e) {
            log.error("更新自动拣货配置异常,", e);
            throw new CommonRuntimeException("更新自动拣货配置异常", ResultCode.RETRY_INNER_FAIL);
        }
    }
}
