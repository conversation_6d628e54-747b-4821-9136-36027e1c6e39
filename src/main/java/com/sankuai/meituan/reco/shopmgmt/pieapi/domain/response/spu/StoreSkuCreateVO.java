package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.CombineChildSkuVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.SaleAttrValueVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.product.container.SkuContainer;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ChannelSkuAttrValueInfoVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MoneyUtils;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.StoreSkuDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.sku.CombineChildSkuDto;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Title: StoreSkuCreateVO
 * @Description: 创建门店商品SKU信息
 * @Author: zhaolei12
 * @Date: 2020/5/16 5:41 下午
 */
@TypeDoc(
        description = "创建门店商品SKU信息"
)
@Data
@ApiModel("创建门店商品SKU信息")
@EqualsAndHashCode(of = {"skuId"}, callSuper = false)
public class StoreSkuCreateVO extends SkuContainer {

    @FieldDoc(
            description = "SKU编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "SKU编码", required = true)
    @NotNull
    private String skuId;
    @FieldDoc(
            description = "规格", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "规格")
    private String spec;


    @FieldDoc(
            description = "月销量"
    )
    @ApiModelProperty(name = "月销量")
    private Integer monthSaleAmount;

    @FieldDoc(
            description = "重量"
    )
    @ApiModelProperty(name = "重量")
    private Integer weight;

    @FieldDoc(
            description = "带单位的重量"
    )
    @ApiModelProperty(name = "带单位的重量")
    private String weightForUnit;

    @FieldDoc(
            description = "重量单位"
    )
    @ApiModelProperty(name = "重量单位")
    private String weightUnit;

    @FieldDoc(
            description = "售卖单位"
    )
    @ApiModelProperty(name = "售卖单位")
    private String saleUnit;

    @FieldDoc(
            description = "零售价，单位：分。后续渠道价格全部使用channelOnlinePriceList传参"
    )
    @ApiModelProperty(name = "零售价，单位：分")
    @Deprecated
    private Integer onlinePrice;

    @FieldDoc(
            description = "自定义库存", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "自定义库存")
    private Integer stock;

    @FieldDoc(
            description = "自定义库存标记,0无限库存，1自定义库存", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "自定义库存标记", required = true)
    private Integer customizeStockFlag;

    @FieldDoc(
            description = "第二天是否自动恢复无限库存，0否，1是", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "第二天是否自动恢复无限库存", required = true)
    private Integer autoResumeInfiniteStock;

    @FieldDoc(
            description = "upc", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "upc", required = true)
    private String upc;

    @FieldDoc(
            description = "upc列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "upcList", required = true)
    private List<String> upcList;

    @FieldDoc(
            description = "价格策略 1-手动定价 2-通用提价 4-单品提价", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "priceStrategy", required = true)
    private Integer priceStrategy;

    @FieldDoc(
            description = "最小起购数", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "minNum", required = true)
    private Integer minNum;

    @Deprecated
    @FieldDoc(
            description = "包装盒数量, 已废弃", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "boxNum", required = true)
    private Integer boxNum = 1;

    @Deprecated
    @FieldDoc(
            description = "包装盒价格, 已废弃", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "boxPrice", required = true)
    private Double boxPrice = 0d;

    @FieldDoc(
            description = "饿了么零售价，单位：分，app端快速建品的时候通过这个字段上传价格，其他时候通过channelOnlinePriceList上传"
    )
    @ApiModelProperty(name = "饿了么零售价，单位：分")
    @Deprecated
    private Integer elemOnlinePrice;

    @FieldDoc(
            description = "京东零售价，单位：分，app端快速建品的时候通过这个字段上传价格，其他时候通过channelOnlinePriceList上传"
    )
    @ApiModelProperty(name = "京东零售价，单位：分")
    @Deprecated
    private Integer jdOnlinePrice;

    @FieldDoc(
            description = "有赞零售价，单位：分，app端快速建品的时候通过这个字段上传价格，其他时候通过channelOnlinePriceList上传"
    )
    @ApiModelProperty(name = "有赞零售价，单位：分")
    @Deprecated
    private Integer yzOnlinePrice;

    @FieldDoc(
            description = "分渠道定价(1不分渠道2分渠道)", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "分渠道定价(1不分渠道2分渠道)")
    private Integer channelPriceType;

    @FieldDoc(
            description = "统一渠道价格channelPriceType=1时存在", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "统一渠道价格channelPriceType=1时存在")
    private String channelOnlinePrice;

    @FieldDoc(
            description = "分渠道价格。后续渠道价格都通过该参数传递。不再使用onlinePrice、elemOnlinePrice、jdOnlinePrice等专用字段传递渠道价格参数",
            requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "分渠道价格")
    private List<ChannelPriceVO> channelOnlinePriceList;

    @FieldDoc(
            description = "进货价(元)"
    )
    @ApiModelProperty(value = "进货价(元)")
    private String storePrice;

    @FieldDoc(
            description = "京东类目属性值", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "京东类目属性值")
    private List<SaleAttrValueVo> jdAttrValues;

    @FieldDoc(
            description = "建议零售价单位元", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "建议零售价单位元")
    private Double suggestPrice;

    @FieldDoc(
            description = "外部编码"
    )
    @ApiModelProperty(value = "外部编码")
    private String externalCode;

    @FieldDoc(
            description = "加盟商总部编码"
    )
    @ApiModelProperty(value = "加盟商总部编码")
    private String franchiseeHeadquartersCode;

    @FieldDoc(
            description = "采购平台编码"
    )
    @ApiModelProperty(value = "采购平台编码")
    private String purchasePlatformCode;

    @FieldDoc(
            description = "箱规列表"
    )
    @ApiModelProperty(value = "箱规列表")
    private List<CartonMeasureConvertFactorVO> cartonMeasureConvertFactorList;

    @FieldDoc(
            description = "是否自定义无upc编码  0-否，1-是"
    )
    @ApiModelProperty(value = "是否自定义无upc编码  0-否，1-是")
    private Integer customizeNoUpcCode;

    @FieldDoc(
            description = "是否使用SKU填充UPC 0-否，1-是"
    )
    @ApiModelProperty(value = "是否使用SKU填充UPC  0-否，1-是")
    private Integer useSkuAsUpc;

    @FieldDoc(
            description = "渠道sku属性值信息"
    )
    @ApiModelProperty(value = "渠道sku属性值信息")
    private List<ChannelSkuAttrValueInfoVo> channelSkuAttrValueInfoList;

    @FieldDoc(
            description = "sku类型 1单品 2组合品"
    )
    @ApiModelProperty(name = "sku类型 1单品 2组合品")
    private Integer skuSaleType;

    @FieldDoc(
            description = "组合品的子sku信息"
    )
    @ApiModelProperty(name = "组合品的子sku信息")
    private List<CombineChildSkuVo> childSkuList;

    @FieldDoc(
            description = "售卖状态 1-售卖 2-自动停售 3-手工停售"
    )
    @ApiModelProperty(value = "售卖状态")
    private Integer onlineStatus;

    public static List<StoreSkuDTO> toDTOList(List<StoreSkuCreateVO> storeSkuCreateVOList) {
        if (CollectionUtils.isEmpty(storeSkuCreateVOList)) {
            return Lists.newArrayList();
        }

        return storeSkuCreateVOList.stream().filter(Objects::nonNull).map(StoreSkuCreateVO::toDTO).collect(Collectors.toList())
                .stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static StoreSkuDTO toDTO(StoreSkuCreateVO storeSkuCreateVO) {
        return toDTO(storeSkuCreateVO, null);
    }

    public static StoreSkuDTO toDTO(StoreSkuCreateVO storeSkuCreateVO, String orgSkuId) {
        if (storeSkuCreateVO == null) {
            return null;
        }
        StoreSkuDTO storeSkuDTO = new StoreSkuDTO();
        storeSkuDTO.setSkuId(storeSkuCreateVO.getSkuId());
        storeSkuDTO.setSpec(storeSkuCreateVO.getSpec());
        storeSkuDTO.setOrgSkuId(orgSkuId);
        storeSkuDTO.setWeight(storeSkuCreateVO.getWeight());
        storeSkuDTO.setWeightForUnit(storeSkuCreateVO.getWeightForUnit());
        storeSkuDTO.setWeightUnit(storeSkuCreateVO.getWeightUnit());
        //线下库存
        storeSkuDTO.setStock(storeSkuCreateVO.getStock());
        storeSkuDTO.setCustomizeStockFlag(storeSkuCreateVO.getCustomizeStockFlag());
        storeSkuDTO.setAutoResumeInfiniteStock(storeSkuCreateVO.getAutoResumeInfiniteStock());
        //自定义库存数量
        storeSkuDTO.setCustomizeStockQuantity(storeSkuCreateVO.getStock());
        // orgSku和sku相等时，需要更新进货价
        storeSkuDTO.setUpdateStoreSkuPriceForUpdate(!storeSkuCreateVO.getSkuId().equals(orgSkuId));

        //包装盒信息
        if (Objects.nonNull(storeSkuCreateVO.getMinNum())) {
            storeSkuDTO.setMinOrderCount(String.valueOf(storeSkuCreateVO.getMinNum()));
        }
        if (Objects.nonNull(storeSkuCreateVO.getBoxNum())) {
            storeSkuDTO.setBoxNum(String.valueOf(storeSkuCreateVO.getBoxNum()));
        }
        if (Objects.nonNull(storeSkuCreateVO.getBoxPrice())) {
            storeSkuDTO.setBoxPrice(String.valueOf(storeSkuCreateVO.getBoxPrice()));
        }
        if (CollectionUtils.isNotEmpty(storeSkuCreateVO.getChannelOnlinePriceList())) {
            storeSkuCreateVO.getChannelOnlinePriceList().forEach(channelPriceVO -> {
                if (channelPriceVO.getChannelId() == ChannelType.MEITUAN.getValue()) {
                    storeSkuDTO.setMtOnlinePrice(MoneyUtils.yuanToCent(channelPriceVO.getOnlinePrice()));
                }
                if (channelPriceVO.getChannelId() == ChannelType.ELEM.getValue()) {
                    storeSkuDTO.setElemOnlinePrice(MoneyUtils.yuanToCent(channelPriceVO.getOnlinePrice()));
                }
                if (channelPriceVO.getChannelId() == ChannelType.JD2HOME.getValue()) {
                    storeSkuDTO.setJddjOnlinePrice(MoneyUtils.yuanToCent(channelPriceVO.getOnlinePrice()));
                }
                if (channelPriceVO.getChannelId() == ChannelType.YOU_ZAN.getValue()) {
                    storeSkuDTO.setYzOnlinePrice(MoneyUtils.yuanToCent(channelPriceVO.getOnlinePrice()));
                }
            });
        }

        storeSkuDTO.setSkuSaleType(storeSkuCreateVO.getSkuSaleType());
        if (CollectionUtils.isNotEmpty(storeSkuCreateVO.getChildSkuList())) {
            storeSkuDTO.setChildSkuList(JacksonUtils.convertList(storeSkuCreateVO.getChildSkuList(), CombineChildSkuDto.class));
        }

        // 设置售卖状态
        storeSkuDTO.setOnlineStatus(storeSkuCreateVO.getOnlineStatus() == null ? 1 : storeSkuCreateVO.getOnlineStatus());
        return storeSkuDTO;
    }
}
