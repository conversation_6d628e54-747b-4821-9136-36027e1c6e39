package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.delivery;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@TypeDoc(
        description = "我的配送管理响应体"
)
@Data
@ApiModel("我的配送管理响应体")
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryManagementConfigResponse {

    @FieldDoc(
            description = "租户id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "租户id", required = true)
    private Long tenantId;

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店id", required = true)
    private Long storeId;

    @FieldDoc(
            description = "聚合运力配送平台信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "聚合运力配送平台信息", required = true)
    private AggDeliveryPlatformConfigVo aggDeliveryPlatformConfig;

    @FieldDoc(
            description = "聚合运力配送平台信息列表"
    )
    @ApiModelProperty(value = "聚合运力配送平台信息列表")
    private List<AggDeliveryPlatformConfigVo> aggDeliveryPlatformConfigList;

    @FieldDoc(
            description = "门店授权信息"
    )
    @ApiModelProperty(value = "门店授权信息")
    private ShopAuth shopAuth;

}
