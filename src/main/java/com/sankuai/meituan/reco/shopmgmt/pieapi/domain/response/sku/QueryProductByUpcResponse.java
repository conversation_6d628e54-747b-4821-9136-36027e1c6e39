package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: <EMAIL>
 * @Date: 2021-03-12 11:46
 * @Description:
 */
@TypeDoc(
        description = "查询标品库、租户商品池、门店商品返回"
)
@Data
@ApiModel("查询标品库、租户商品池、门店商品返回")
public class QueryProductByUpcResponse {

    @FieldDoc(
            description = "业务码，默认0不处理"
    )
    @ApiModelProperty(value = "业务码", required = true)
    private int bizCode;

    @FieldDoc(
            description = "简单sku"
    )
    @ApiModelProperty(value = "简单sku", required = false)
    private SimpleSkuVO simpleSkuVO;

}
