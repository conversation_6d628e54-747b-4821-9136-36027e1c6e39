package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.account;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @createTime 2019/11/19
 * @description
 */
@TypeDoc(
        description = "账号消息和铃声配置模块权限"
)
@Data
@ApiModel("账号消息和铃声配置模块权限")
public class AccountConfigModuleVO {

    /**
     * 消息和铃声设置权限
     */
    @FieldDoc(
            description = "是否展示新订单提醒 (0:不展示，1:展示)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示新订单提醒 (0:不展示，1:展示)", required = true)
    @NotNull
    private Integer showNewOrderAlertTimes;

    @FieldDoc(
            description = "是否展示新拣货任务提醒 (0:不展示，1:展示)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示新拣货任务提醒 (0:不展示，1:展示)", required = true)
    @NotNull
    private Integer showNewPickTaskAlertTimes;

    @FieldDoc(
            description = "是否展示订单领取超时提醒 (0:不展示，1:展示)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示订单领取超时提醒 (0:不展示，1:展示)", required = true)
    @NotNull
    private Integer showPickTaskClaimOvertimeAlertTimes;

    @FieldDoc(
            description = "是否展示订单拣货超时提醒 (0:不展示，1:展示)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示订单拣货超时提醒 (0:不展示，1:展示)", required = true)
    @NotNull
    private Integer showPickTaskOvertimeAlertTimes;


    @FieldDoc(
            description = "是否展示订单合流超时提醒 (0:不展示，1:展示)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示订单合流超时提醒 (0:不展示，1:展示)", required = true)
    @NotNull
    private Integer showMergeOvertimeAlertTimes;

    @FieldDoc(
            description = "是否展示电话设置 (0:不展示，1:展示)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示电话设置 (0:不展示，1:展示)", required = true)
    @NotNull
    private Integer showPhoneCallSetting;

}
