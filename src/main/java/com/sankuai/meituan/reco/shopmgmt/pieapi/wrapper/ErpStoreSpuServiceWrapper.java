package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.erp.ErpQueryStoreSafeStockRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.erp.ErpUpdateSkuSafeStockRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp.ErpStoreSpuClippingPageQueryResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp.StoreSkuSafeStockVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.linz.product.channel.EnhanceChannelType;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ProjectConstants;
import com.sankuai.meituan.reco.shopmgmt.pieapi.converters.ErpStoreSpuConverter;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.erp.ErpStoreSpuBatchUpdateRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.erp.ErpStoreSpuPageQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.erp.ErpStoreSpuQueryChannelPriceRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.erp.ErpStoreSpuQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.erp.ErpStoreSpuSyncStatusRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp.ErpChannelSpuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp.ErpStoreSkuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp.ErpStoreSpuBatchUpdateResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp.ErpStoreSpuPageQueryResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp.ErpStoreSpuQueryChannelPriceResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp.ErpStoreSpuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp.ErpTabCountVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ResponseCodeEnum;
import com.meituan.linz.thrift.response.Status;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.SkuSafeStockDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.enums.ConfigPageTypeEnum;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.BatchUpdateStoreSpuRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.QueryPriceAdjustRateRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.SpuStoreConfigQueryRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.StoreSpuPageQueryRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.SyncStoreSpuSaleStatusRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.SyncStoreSpuStatusRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.UpdateSkuSafeStockRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.BatchUpdateStoreSpuResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.PageQueryStoreSpuResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.QueryPriceAdjustRateResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.SpuStoreConfigQueryResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.UpdateSkuSafeStockResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.service.SpuStoreConfigThriftService;
import com.sankuai.meituan.shangou.empower.productbiz.client.service.StoreSpuBizThriftService;
import com.sankuai.meituan.shangou.platform.empower.product.client.dto.StoreCategoryPoiRouteConfigDTO;
import com.sankuai.meituan.shangou.platform.empower.product.client.request.PoiSkuIdQuerySafeStockRequest;
import com.sankuai.meituan.shangou.platform.empower.product.client.request.store_category_poi_route_config.QueryStoreCategoryPoiRouteConfigRequest;
import com.sankuai.meituan.shangou.platform.empower.product.client.response.PoiSkuSafeStockQueryResponse;
import com.sankuai.meituan.shangou.platform.empower.product.client.response.store_category_poi_route_config.QueryStoreCategoryPoiRouteConfigResponse;
import com.sankuai.meituan.shangou.platform.empower.product.client.service.PoiSkuThriftService;
import com.sankuai.meituan.shangou.platform.empower.product.client.service.ProductStoreConfigThriftService;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;

import lombok.extern.slf4j.Slf4j;

/**
 * ERP门店商品服务wrapper
 *
 * <AUTHOR>
 * @since 2023/05/15
 */
@Service
@Slf4j
public class ErpStoreSpuServiceWrapper {

    /**
     * 必卖商品可下架权限
     */
    public final static String MUST_SALE_OFF_SHELVES = "MUST_SALE_OFF_SHELVES";

    // 以下为一些错误信息枚举
    public static final String QUERY_STORE_SPU_FAIL = "查询门店商品失败";

    public static final String QUERY_STORE_TAB_COUNT_FAIL = "查询门店商品TAB页数量失败";
    public static final String UPDATE_STORE_SPU_FAIL = "修改门店商品失败";
    public static final String SYNC_OFFLINE_PRICE_FAIL = "同步线下价格失败";
    public static final String SYNC_OFFLINE_STOCK_FAIL = "同步线下库存失败";
    public static final String SYNC_ONLINE_PRICE_FAIL = "同步线上价格失败";
    public static final String SYNC_ONLINE_STOCK_FAIL = "同步线上库存失败";
    public static final String SYNC_SALE_STATUS = "同步上下架状态失败";
    public static final String QUERY_PRICE_ADJUST_RATE_FAIL = "查询渠道价格和加价率失败";

    @Autowired
    private SacWrapper sacWrapper;
    @Autowired
    private StoreSpuBizThriftService storeSpuBizThriftService;

    @Autowired
    private OCMSPriceTrendWrapper ocmsPriceTrendWrapper;

    @Autowired
    private SpuStoreConfigThriftService spuStoreConfigThriftService;

    @Autowired
    private ProductStoreConfigThriftService productStoreConfigThriftService;

    @Autowired
    private PoiSkuThriftService poiSkuThriftService;

    /**
     * erp商品分页查询
     *
     * @param identityInfo
     * @param request
     * @return
     */
    public CommonResponse<ErpStoreSpuPageQueryResponseVO> erpPageQuery(IdentityInfo identityInfo, ErpStoreSpuPageQueryRequest request) {
        try {
            StoreSpuPageQueryRequest rpcReq = ErpStoreSpuConverter.erpStoreSpuPageQueryRequestConvert(identityInfo, request);

            PageQueryStoreSpuResponse rpcResp = storeSpuBizThriftService.pageQueryStoreSpu(rpcReq);
            if (Objects.isNull(rpcResp) || Objects.isNull(rpcResp.getStatus()) || Objects.isNull(rpcResp.getStatus().getCode())) {
                log.error("erpPageQuery fail, rpcReq:{}, rpcResp:{}", JacksonUtils.toJson(rpcReq), JacksonUtils.toJson(rpcResp));
                return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), QUERY_STORE_SPU_FAIL);
            }
            if (!Objects.equals(Status.SUCCESS_CODE, rpcResp.getStatus().getCode())) {
                log.error("erpPageQuery fail, rpcReq:{}, rpcResp:{}", JacksonUtils.toJson(rpcReq), JacksonUtils.toJson(rpcResp));
                return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), Optional.ofNullable(rpcResp.getStatus().getMsg()).orElse(QUERY_STORE_SPU_FAIL));
            }
            StoreCategoryPoiRouteConfigDTO storeCategoryConfig = queryStoreCategoryPoiRouteConfig(identityInfo.getUser().getTenantId(),
                    request.getStoreId());
            ErpStoreSpuPageQueryResponseVO erpStoreSpuPageQueryResponseVO =
                    ErpStoreSpuConverter.erpStoreSpuPageQueryResponseConvert(identityInfo.getUser().getTenantId(), rpcResp, storeCategoryConfig);
            fillStoreSpuVoMtChannelPrice(identityInfo.getUser().getTenantId(), erpStoreSpuPageQueryResponseVO.getStoreSpuList(), request);
            return CommonResponse.success(erpStoreSpuPageQueryResponseVO);
        }
        catch (Exception e) {
            log.error("erpPageQuery fail, request:{}", JacksonUtils.toJson(request), e);
            return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), Optional.ofNullable(e.getMessage()).orElse(QUERY_STORE_SPU_FAIL));
        }
    }

    private StoreCategoryPoiRouteConfigDTO queryStoreCategoryPoiRouteConfig(long tenantId, long storeId) {
        QueryStoreCategoryPoiRouteConfigRequest rpcReq = new QueryStoreCategoryPoiRouteConfigRequest();
        rpcReq.setTenantId(tenantId);
        rpcReq.setStoreIdList(Collections.singletonList(storeId));
        QueryStoreCategoryPoiRouteConfigResponse rpcResp = productStoreConfigThriftService.queryStoreCategoryPoiRouteConfig(rpcReq);
        if (Objects.isNull(rpcResp) || Objects.isNull(rpcResp.getStatus()) || Objects.isNull(rpcResp.getStatus().getCode())) {
            log.error("queryStoreCategoryPoiRouteConfig fail, rpcReq:{}, rpcResp:{}", JacksonUtils.toJson(rpcReq), JacksonUtils.toJson(rpcResp));
            throw new BizException("查询门店分类配置失败");
        }
        if (!Objects.equals(Status.SUCCESS_CODE, rpcResp.getStatus().getCode())) {
            log.error("queryStoreCategoryPoiRouteConfig fail, rpcReq:{}, rpcResp:{}", JacksonUtils.toJson(rpcReq), JacksonUtils.toJson(rpcResp));
            throw new BizException("查询门店分类配置失败");
        }
        if (MapUtils.isEmpty(rpcResp.getRouteConfigMap())) {
            return null;
        }
        return rpcResp.getRouteConfigMap()
                .values()
                .iterator()
                .next();
    }

    /**
     * 查询并填充渠道商品的在线价格
     */
    private void fillStoreSpuVoMtChannelPrice(Long tenantId, List<ErpStoreSpuVO> storeSpuList, ErpStoreSpuPageQueryRequest request) {
        if (CollectionUtils.isEmpty(storeSpuList) || !Boolean.TRUE.equals(request.getNeedMtChannelPrice())) {
            return;
        }

        try {
            // 门店商品的全部skuId
            List<String> skuIdList = new ArrayList<>();
            // 门店商品spuId到skuIds的映射关系
            Map<String, List<String>> spuIdToSkuIdsMap = new HashMap<>();
            // 获取skuId，构造映射关系
            storeSpuList.stream()
                    .filter(erpStoreSpuVO -> Objects.nonNull(erpStoreSpuVO) && CollectionUtils.isNotEmpty(erpStoreSpuVO.getStoreSkuList()))
                    .forEach(erpStoreSpuVO -> {
                        List<String> curSkuIds = erpStoreSpuVO.getStoreSkuList().stream()
                                .filter(erpStoreSkuVO -> Objects.nonNull(erpStoreSkuVO) && StringUtils.isNotBlank(erpStoreSkuVO.getSkuId()))
                                .map(ErpStoreSkuVO::getSkuId)
                                .distinct()
                                .collect(Collectors.toList());
                        spuIdToSkuIdsMap.put(erpStoreSpuVO.getSpuId(), curSkuIds);
                        skuIdList.addAll(curSkuIds);
                    });

            // 门店商品的去重后的全部skuId
            List<String> skuIds = skuIdList.stream().distinct().collect(Collectors.toList());

            // 查询门店商品对应美团渠道价格
            Map<String, Long> skuIdToMtPriceMap = new HashMap<>();
            Lists.partition(skuIds, ProjectConstants.BATCH_LIMIT_20_AMOUNT).forEach(skuIdsPartition -> {
                Map<String, Long> curMap = ocmsPriceTrendWrapper.queryChannelOnlinePrice(
                        tenantId,
                        request.getStoreId(),
                        EnhanceChannelType.MT.getChannelId(),
                        skuIdsPartition);
                skuIdToMtPriceMap.putAll(curMap);
            });

            if (MapUtils.isEmpty(spuIdToSkuIdsMap) || MapUtils.isEmpty(skuIdToMtPriceMap)) {
                return;
            }

            // 筛选美团渠道spu
            List<ErpChannelSpuVO> erpChannelSpuVOList = storeSpuList.stream()
                    .filter(erpStoreSpuVO -> Objects.nonNull(erpStoreSpuVO) && CollectionUtils.isNotEmpty(erpStoreSpuVO.getChannelSpuList()))
                    .flatMap(erpStoreSpuVO -> erpStoreSpuVO.getChannelSpuList().stream())
                    .filter(erpChannelSpuVO -> Objects.nonNull(erpChannelSpuVO) && Objects.equals(erpChannelSpuVO.getChannelId(), EnhanceChannelType.MT.getChannelId()))
                    .collect(Collectors.toList());

            // 补充门店商品渠道价格列表
            for (ErpChannelSpuVO erpChannelSpuVO : erpChannelSpuVOList) {
                List<ErpChannelSpuVO.ChannelPriceVO> channelPriceList = new ArrayList<>();
                List<String> curSkuIds = spuIdToSkuIdsMap.get(erpChannelSpuVO.getSpuId());
                if (CollectionUtils.isNotEmpty(curSkuIds)) {
                    curSkuIds.forEach(skuId -> {
                        Long curMtChannelPrice = skuIdToMtPriceMap.get(skuId);
                        if (Objects.nonNull(curMtChannelPrice)) {
                            ErpChannelSpuVO.ChannelPriceVO channelPriceVO = ErpChannelSpuVO.ChannelPriceVO.builder()
                                    .channelId(erpChannelSpuVO.getChannelId())
                                    .skuId(skuId)
                                    .channelPrice(curMtChannelPrice)
                                    .build();
                            channelPriceList.add(channelPriceVO);
                        }
                    });
                }
                erpChannelSpuVO.setChannelPriceList(channelPriceList);
            }

        } catch (Exception e) {
            log.error("列表查询erp门店商品时补充渠道商品价格异常", e);
        }
    }

    /**
     * erp商品分页查询
     *
     * @param request
     * @return
     */
    public CommonResponse<ErpStoreSpuClippingPageQueryResponseVO> erpPageQueryForClipping(ErpStoreSpuPageQueryRequest request) {

        try {
            IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
            StoreSpuPageQueryRequest rpcReq = ErpStoreSpuConverter.erpStoreSpuPageQueryRequestConvert(identityInfo, request);

            PageQueryStoreSpuResponse rpcResp = storeSpuBizThriftService.pageQueryStoreSpu(rpcReq);
            if (Objects.isNull(rpcResp) || Objects.isNull(rpcResp.getStatus()) || Objects.isNull(rpcResp.getStatus().getCode())) {
                log.error("erpPageQuery fail, rpcReq:{}, rpcResp:{}", JacksonUtils.toJson(rpcReq), JacksonUtils.toJson(rpcResp));
                return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), QUERY_STORE_SPU_FAIL);
            }
            if (!Objects.equals(Status.SUCCESS_CODE, rpcResp.getStatus().getCode())) {
                log.error("erpPageQuery fail, rpcReq:{}, rpcResp:{}", JacksonUtils.toJson(rpcReq), JacksonUtils.toJson(rpcResp));
                String errMsg = Optional.ofNullable(rpcResp.getStatus().getMsg()).orElse(QUERY_STORE_SPU_FAIL);
                return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), errMsg);
            }

            ErpStoreSpuClippingPageQueryResponseVO erpStoreSpuPageQueryResponseVO =
                    ErpStoreSpuConverter.erpStoreSpuClippingPageQueryResponseConvert(rpcResp);
            return CommonResponse.success(erpStoreSpuPageQueryResponseVO);
        }
        catch (Exception e) {
            log.error("erpPageQueryForClipping fail, request:{}", JacksonUtils.toJson(request), e);
            return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), Optional.ofNullable(e.getMessage()).orElse(QUERY_STORE_SPU_FAIL));
        }
    }
    /**
     * erp tab页数量统计
     *
     * @param identityInfo
     * @param request
     * @return
     */
    public CommonResponse<ErpTabCountVO> erpTabCount(IdentityInfo identityInfo, ErpStoreSpuQueryRequest request) {
        try {
            // 注意：上下架数量只能分两次查询，不能使用底层的tab查询接口，因为售卖条件不会影响上架数量统计
            StoreSpuPageQueryRequest onShelfRpcReq = ErpStoreSpuConverter.erpStoreSpuQueryRequestConvert4TabCount(identityInfo, request, Collections.singletonList(1));
            StoreSpuPageQueryRequest offShelfRpcReq = ErpStoreSpuConverter.erpStoreSpuQueryRequestConvert4TabCount(identityInfo, request, Arrays.asList(2, 3));

            // 新增手动下架的计数查询
            StoreSpuPageQueryRequest manualOffShelfRpcReq = ErpStoreSpuConverter.erpStoreSpuQueryRequestConvert4TabCount(identityInfo, request, Collections.singletonList(3));

            // 上架数量查询
            PageQueryStoreSpuResponse onShelfRpcResp = storeSpuBizThriftService.pageQueryStoreSpu(onShelfRpcReq);
            if (Objects.isNull(onShelfRpcResp) || Objects.isNull(onShelfRpcResp.getStatus()) || Objects.isNull(onShelfRpcResp.getStatus().getCode())) {
                log.error("erpTabCount4OnShelf fail, rpcReq:{}, rpcResp:{}", JacksonUtils.toJson(onShelfRpcReq), JacksonUtils.toJson(onShelfRpcResp));
                return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), QUERY_STORE_TAB_COUNT_FAIL);
            }
            if (!Objects.equals(Status.SUCCESS_CODE, onShelfRpcResp.getStatus().getCode())) {
                log.error("erpTabCount4OnShelf fail, rpcReq:{}, rpcResp:{}", JacksonUtils.toJson(onShelfRpcReq), JacksonUtils.toJson(onShelfRpcResp));
                return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), Optional.ofNullable(onShelfRpcResp.getStatus().getMsg()).orElse(QUERY_STORE_TAB_COUNT_FAIL));
            }

            // 下架数量查询
            PageQueryStoreSpuResponse offShelfRpcResp = storeSpuBizThriftService.pageQueryStoreSpu(offShelfRpcReq);
            if (Objects.isNull(offShelfRpcResp) || Objects.isNull(offShelfRpcResp.getStatus()) || Objects.isNull(offShelfRpcResp.getStatus().getCode())) {
                log.error("erpTabCount4OffShelf fail, rpcReq:{}, rpcResp:{}", JacksonUtils.toJson(offShelfRpcReq), JacksonUtils.toJson(offShelfRpcResp));
                return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), QUERY_STORE_TAB_COUNT_FAIL);
            }
            if (!Objects.equals(Status.SUCCESS_CODE, offShelfRpcResp.getStatus().getCode())) {
                log.error("erpTabCount4OffShelf fail, rpcReq:{}, rpcResp:{}", JacksonUtils.toJson(offShelfRpcReq), JacksonUtils.toJson(offShelfRpcResp));
                return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), Optional.ofNullable(offShelfRpcResp.getStatus().getMsg()).orElse(QUERY_STORE_TAB_COUNT_FAIL));
            }

            // 手动下架数量查询
            PageQueryStoreSpuResponse manualOffShelfRpcResp =  storeSpuBizThriftService.pageQueryStoreSpu(manualOffShelfRpcReq);
            if (Objects.isNull(manualOffShelfRpcResp) || Objects.isNull(manualOffShelfRpcResp.getStatus()) || Objects.isNull(manualOffShelfRpcResp.getStatus().getCode())) {
                log.error("erpTabCount4ManualOffShelf fail, rpcReq:{}, rpcResp:{}", JacksonUtils.toJson(manualOffShelfRpcReq), JacksonUtils.toJson(manualOffShelfRpcResp));
                return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), QUERY_STORE_TAB_COUNT_FAIL);
            }
            if (!Objects.equals(Status.SUCCESS_CODE, manualOffShelfRpcResp.getStatus().getCode())) {
                log.error("erpTabCount4ManualOffShelf fail, rpcReq:{}, rpcResp:{}", JacksonUtils.toJson(manualOffShelfRpcReq), JacksonUtils.toJson(manualOffShelfRpcResp));
                return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), Optional.ofNullable(manualOffShelfRpcResp.getStatus().getMsg()).orElse(QUERY_STORE_TAB_COUNT_FAIL));
            }


            return CommonResponse.success(ErpStoreSpuConverter.erpStoreSpuPageQueryResponseConvert4TabCount(onShelfRpcResp, offShelfRpcResp,manualOffShelfRpcResp));
        }
        catch (Exception e) {
            log.error("erpTabCount fail, request:{}", JacksonUtils.toJson(request), e);
            return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), Optional.ofNullable(e.getMessage()).orElse(QUERY_STORE_TAB_COUNT_FAIL));
        }
    }


    /**
     * 修改门店商品（价格、库存、上下架等）
     *
     * @param identityInfo
     * @param batchUpdateRequest
     * @return
     */
    public CommonResponse<ErpStoreSpuBatchUpdateResponseVO> batchUpdateSpu(IdentityInfo identityInfo, ErpStoreSpuBatchUpdateRequest batchUpdateRequest) {
        try {
            // step1: 更新上下架状态时需要查询用户是否有 "必卖商品可下架" 权限，
            // 这儿注意mustSaleOffShelves为封装类型，权限异常会造成null的情况，这儿参考eapi不做拆箱和降级，全部下沉到productbiz进行处理
            Boolean mustSaleOffShelves = null;
            if (batchUpdateRequest.getSaleStatus() != null) {
                Map<String, Boolean> permissionMap = sacWrapper.accountAuthPermissions(identityInfo.getUser().getAccountId(), identityInfo.getAuthId(), Lists.newArrayList(MUST_SALE_OFF_SHELVES));
                mustSaleOffShelves = permissionMap.get(MUST_SALE_OFF_SHELVES);
            }

            // step2: call rpc
            BatchUpdateStoreSpuRequest rpcReq = ErpStoreSpuConverter.batchUpdateSpuRequestConvert(identityInfo, batchUpdateRequest, mustSaleOffShelves);
            BatchUpdateStoreSpuResponse rpcResp = storeSpuBizThriftService.batchUpdateStoreSpuCompat(rpcReq);

            // step3: 解析结果，这儿需要注意batchUpdateStoreSpu的错误处理和正常接口不太一样，这儿参考eapi做个定制处理
            if (Objects.isNull(rpcResp) || Objects.isNull(rpcResp.getStatus()) || Objects.isNull(rpcResp.getStatus().getCode())) {
                log.error("batchUpdateStoreSpu fail, rpcReq:{}, rpcResp:{}", JacksonUtils.toJson(rpcReq), JacksonUtils.toJson(rpcResp));
                return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), UPDATE_STORE_SPU_FAIL);
            }
            // 优先取外层的信息
            String msg = rpcResp.getMessage();
            if (StringUtils.isEmpty(msg)) {
                msg = rpcResp.getStatus().getMsg();
            }
            // 注意这儿虽然是使用fail方法构造结果，但不一定代表是fail，code有可能是0（代表success)
            return new CommonResponse(rpcResp.getStatus().getCode(), msg, ErpStoreSpuConverter.batchUpdateSpuResponseConvert(rpcResp));
        }
        catch (Exception e) {
            log.error("batchUpdateStoreSpu fail, request:{}", JacksonUtils.toJson(batchUpdateRequest), e);
            return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), Optional.ofNullable(e.getMessage()).orElse(UPDATE_STORE_SPU_FAIL));
        }
    }


    /**
     * 同步线下价格
     *
     * @param identityInfo
     * @param request
     * @return
     */
    public CommonResponse<Void> syncOfflinePrice(IdentityInfo identityInfo, ErpStoreSpuSyncStatusRequest request) {
        try {
            SyncStoreSpuStatusRequest rpcReq = ErpStoreSpuConverter.syncStoreSpuStatusRequestConvert(identityInfo, request);
            com.sankuai.meituan.shangou.empower.productbiz.client.response.CommonResponse rpcResp = storeSpuBizThriftService.syncOfflinePrice(rpcReq);
            return parseSyncStatusResult(rpcReq, rpcResp, "syncOfflinePrice", SYNC_OFFLINE_PRICE_FAIL);
        }
        catch (Exception e) {
            log.error("syncOfflinePrice fail, request:{}", JacksonUtils.toJson(request), e);
            return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), Optional.ofNullable(e.getMessage()).orElse(SYNC_OFFLINE_PRICE_FAIL));
        }
    }

    /**
     * 同步线下库存
     *
     * @param identityInfo
     * @param request
     * @return
     */
    public CommonResponse<Void> syncOfflineStock(IdentityInfo identityInfo, ErpStoreSpuSyncStatusRequest request) {
        try {
            SyncStoreSpuStatusRequest rpcReq = ErpStoreSpuConverter.syncStoreSpuStatusRequestConvert(identityInfo, request);
            com.sankuai.meituan.shangou.empower.productbiz.client.response.CommonResponse rpcResp = storeSpuBizThriftService.syncOfflineStock(rpcReq);
            return parseSyncStatusResult(rpcReq, rpcResp, "syncOfflineStock", SYNC_OFFLINE_STOCK_FAIL);
        }
        catch (Exception e) {
            log.error("syncOfflineStock fail, request:{}", JacksonUtils.toJson(request), e);
            return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), Optional.ofNullable(e.getMessage()).orElse(SYNC_OFFLINE_STOCK_FAIL));
        }
    }

    /**
     * 同步线上价格
     *
     * @param identityInfo
     * @param request
     * @return
     */
    public CommonResponse<Void> syncOnlinePrice(IdentityInfo identityInfo, ErpStoreSpuSyncStatusRequest request) {
        try {
            SyncStoreSpuStatusRequest rpcReq = ErpStoreSpuConverter.syncStoreSpuStatusRequestConvert(identityInfo, request);
            com.sankuai.meituan.shangou.empower.productbiz.client.response.CommonResponse rpcResp = storeSpuBizThriftService.syncOnlinePrice(rpcReq);
            return parseSyncStatusResult(rpcReq, rpcResp, "syncOnlinePrice", SYNC_ONLINE_PRICE_FAIL);
        }
        catch (Exception e) {
            log.error("syncOnlinePrice fail, request:{}", JacksonUtils.toJson(request), e);
            return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), Optional.ofNullable(e.getMessage()).orElse(SYNC_ONLINE_PRICE_FAIL));
        }
    }

    /**
     * 同步线上库存
     *
     * @param identityInfo
     * @param request
     * @return
     */
    public CommonResponse<Void> syncOnlineStock(IdentityInfo identityInfo, ErpStoreSpuSyncStatusRequest request) {
        try {
            SyncStoreSpuStatusRequest rpcReq = ErpStoreSpuConverter.syncStoreSpuStatusRequestConvert(identityInfo, request);
            com.sankuai.meituan.shangou.empower.productbiz.client.response.CommonResponse rpcResp = storeSpuBizThriftService.syncOnlineStock(rpcReq);
            return parseSyncStatusResult(rpcReq, rpcResp, "syncOnlineStock", SYNC_ONLINE_STOCK_FAIL);
        }
        catch (Exception e) {
            log.error("syncOnlineStock fail, request:{}", JacksonUtils.toJson(request), e);
            return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), Optional.ofNullable(e.getMessage()).orElse(SYNC_ONLINE_STOCK_FAIL));
        }
    }

    /**
     * 同步上下架状态
     *
     * @param identityInfo
     * @param request
     * @return
     */
    public CommonResponse<Void> syncSaleStatus(IdentityInfo identityInfo, ErpStoreSpuSyncStatusRequest request) {
        try {
            SyncStoreSpuSaleStatusRequest rpcReq = ErpStoreSpuConverter.syncStoreSpuSaleStatusRequestConvert(identityInfo, request);
            com.sankuai.meituan.shangou.empower.productbiz.client.response.CommonResponse rpcResp = storeSpuBizThriftService.syncSaleStatus(rpcReq);
            return parseSyncStatusResult(rpcReq, rpcResp, "syncSaleStatus", SYNC_SALE_STATUS);
        }
        catch (Exception e) {
            log.error("syncSaleStatus fail, request:{}", JacksonUtils.toJson(request), e);
            return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), Optional.ofNullable(e.getMessage()).orElse(SYNC_SALE_STATUS));
        }
    }

    /**
     * 解析状态（价格、库存、上下架）同步的结果
     *
     * @param rpcReq
     * @param rpcResp
     * @param logPrefix
     * @param defaultErrorMessage
     * @return
     */
    private CommonResponse<Void> parseSyncStatusResult(Object rpcReq,
                                                       com.sankuai.meituan.shangou.empower.productbiz.client.response.CommonResponse rpcResp,
                                                       String logPrefix,
                                                       String defaultErrorMessage) {
        if (Objects.isNull(rpcResp) || Objects.isNull(rpcResp.getStatus()) || Objects.isNull(rpcResp.getStatus().getCode())) {
            log.error("{} fail, rpcReq:{}, rpcResp:{}", logPrefix, JacksonUtils.toJson(rpcReq), JacksonUtils.toJson(rpcResp));
            return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), defaultErrorMessage);
        }
        if (!Objects.equals(Status.SUCCESS_CODE, rpcResp.getStatus().getCode())) {
            log.error("{} fail, rpcReq:{}, rpcResp:{}", logPrefix, JacksonUtils.toJson(rpcReq), JacksonUtils.toJson(rpcResp));
            return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), Optional.ofNullable(rpcResp.getStatus().getMsg()).orElse(defaultErrorMessage));
        }
        return CommonResponse.success(null);
    }

    /**
     * 查询门店商品渠道价格和加价率
     *
     * @param identityInfo
     * @param request
     * @return
     */
    public CommonResponse<ErpStoreSpuQueryChannelPriceResponseVO> queryChannelPriceWithAdjustRate(IdentityInfo identityInfo, ErpStoreSpuQueryChannelPriceRequest request) {
        try {
            QueryPriceAdjustRateRequest rpcReq = ErpStoreSpuConverter.queryChannelPriceWithAdjustRateRequestConvert(identityInfo, request);

            QueryPriceAdjustRateResponse rpcResp = storeSpuBizThriftService.queryPriceAdjustRate(rpcReq);
            if (Objects.isNull(rpcResp) || Objects.isNull(rpcResp.getStatus()) || Objects.isNull(rpcResp.getStatus().getCode())) {
                log.error("queryPriceAdjustRate fail, rpcReq:{}, rpcResp:{}", JacksonUtils.toJson(rpcReq), JacksonUtils.toJson(rpcResp));
                return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), QUERY_PRICE_ADJUST_RATE_FAIL);
            }
            if (!Objects.equals(Status.SUCCESS_CODE, rpcResp.getStatus().getCode())) {
                log.error("queryPriceAdjustRate fail, rpcReq:{}, rpcResp:{}", JacksonUtils.toJson(rpcReq), JacksonUtils.toJson(rpcResp));
                return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), Optional.ofNullable(rpcResp.getStatus().getMsg()).orElse(QUERY_PRICE_ADJUST_RATE_FAIL));
            }
            return CommonResponse.success(ErpStoreSpuConverter.queryChannelPriceWithAdjustRateResponseConvert(rpcResp));
        }
        catch (TException e) {
            log.error("queryPriceAdjustRate fail, request:{}", JacksonUtils.toJson(request), e);
            return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), Optional.ofNullable(e.getMessage()).orElse(QUERY_PRICE_ADJUST_RATE_FAIL));
        }
    }


    /**
     * 门店安全库存相关查询
     *
     * @return
     */
    public CommonResponse<StoreSkuSafeStockVO> querySafeStock(long tenantId, ErpQueryStoreSafeStockRequest request) {
        StoreSkuSafeStockVO safeStockVO = new StoreSkuSafeStockVO();

        // 查询 门店维度安全库存配置值
        SpuStoreConfigQueryRequest rpcReq = new SpuStoreConfigQueryRequest();
        rpcReq.setTenantId(tenantId);
        rpcReq.setStoreId(request.getStoreId());
        rpcReq.setConfigPageType(ConfigPageTypeEnum.STOCK_CONFIG_PAGE.getCode());
        SpuStoreConfigQueryResponse configResponse = spuStoreConfigThriftService.querySpuStoreConfigList(rpcReq);
        if (Objects.isNull(configResponse) || Objects.isNull(configResponse.getStatus()) || !configResponse.getStatus().isSuccess()) {
            log.error("查询门店安全库存配置失败, rpcReq:{}, configResponse:{}", JacksonUtils.toJson(rpcReq), JacksonUtils.toJson(configResponse));
            return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), "门店安全库存配置获取失败");
        }
        safeStockVO.buildAndSetStoreSafeStockConfigList(configResponse.getSpuStoreConfigDTOList());

        // 查询 门店商品安全库存值
        PoiSkuIdQuerySafeStockRequest poiSkuQueryRequest = new PoiSkuIdQuerySafeStockRequest(tenantId, request.getStoreId(), Arrays.asList(request.getSkuId()));
        PoiSkuSafeStockQueryResponse poiSkuSafeStockResponse = poiSkuThriftService.queryPoiSkuSafeStockBySkuIds(poiSkuQueryRequest);
        if (Objects.isNull(poiSkuSafeStockResponse) || Objects.isNull(poiSkuSafeStockResponse.getStatus())
                || !poiSkuSafeStockResponse.getStatus().isSuccess()
                || CollectionUtils.isEmpty(poiSkuSafeStockResponse.getPoiSkuSafeStockDTOList())) {
            log.error("查询门店安全库存配置失败, poiSkuQueryRequest:{}, poiSkuSafeStockResponse:{}", JacksonUtils.toJson(poiSkuQueryRequest), JacksonUtils.toJson(poiSkuSafeStockResponse));
            return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), "门店商品安全库存获取失败");
        }
        poiSkuSafeStockResponse.getPoiSkuSafeStockDTOList().forEach(skuSafeStock -> {
            if (Objects.equals(request.getSkuId(), skuSafeStock.getSkuId())) {
                safeStockVO.setOnlineSafeStock(skuSafeStock.getSafeStock());
                safeStockVO.setStockShareRate(skuSafeStock.getStockShareRate());
            }
        });

        return CommonResponse.success(safeStockVO);
    }


    /**
     * 门店安全库存相关查询
     *
     * @return
     */
    public CommonResponse<Void> updateOnlineSafeStock(User user, ErpUpdateSkuSafeStockRequest request) {
        UpdateSkuSafeStockRequest updateSkuSafeStockRequest = request.toUpdateSkuSafeStockRequest(user);
        SkuSafeStockDTO skuSafeStockDTO = new SkuSafeStockDTO();
        skuSafeStockDTO.setSkuId(request.getSkuId());
        skuSafeStockDTO.setSafeStock(request.getOnlineSafeStock());

        // 查询 门店商品库存共享率，填充，复用PC端的biz接口
        PoiSkuIdQuerySafeStockRequest poiSkuQueryRequest = new PoiSkuIdQuerySafeStockRequest(user.getTenantId(), request.getStoreId(), Arrays.asList(request.getSkuId()));
        PoiSkuSafeStockQueryResponse poiSkuSafeStockResponse = poiSkuThriftService.queryPoiSkuSafeStockBySkuIds(poiSkuQueryRequest);
        if (Objects.isNull(poiSkuSafeStockResponse) || Objects.isNull(poiSkuSafeStockResponse.getStatus())
                || !poiSkuSafeStockResponse.getStatus().isSuccess()
                || CollectionUtils.isEmpty(poiSkuSafeStockResponse.getPoiSkuSafeStockDTOList())) {
            log.error("查询门店安全库存配置失败, poiSkuQueryRequest:{}, poiSkuSafeStockResponse:{}", JacksonUtils.toJson(poiSkuQueryRequest), JacksonUtils.toJson(poiSkuSafeStockResponse));
            return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), "门店商品安全库存获取失败");
        }

        poiSkuSafeStockResponse.getPoiSkuSafeStockDTOList().forEach(skuSafeStock -> {
            if (Objects.equals(request.getSkuId(), skuSafeStock.getSkuId())) {
                skuSafeStockDTO.setStockShareRate(skuSafeStock.getStockShareRate());
            }
        });
        updateSkuSafeStockRequest.setSkuSafeStockDTOList(Arrays.asList(skuSafeStockDTO));

        try {
            UpdateSkuSafeStockResponse updateResponse = storeSpuBizThriftService.updateStoreSkuSafeStock(updateSkuSafeStockRequest);
            return new CommonResponse<>(updateResponse.getStatus().getCode(), updateResponse.getStatus().getMsg(), null);
        }
        catch (TException e) {
            log.error("更新商品安全库存失败, updateSkuSafeStockRequest:{}", JacksonUtils.toJson(updateSkuSafeStockRequest));
            return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), "更新商品安全库存失败");
        }
    }
}
