// Copyright (C) 2019 Meituan
// All rights reserved
package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.Lion;
import com.dianping.lion.common.util.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.meituan.dorado.common.exception.RpcException;
import com.meituan.shangou.saas.tenant.thrift.PoiThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.Status;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiEntityTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiMapResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ResponseHandler;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.command.QueryMerchantSkuListBySkuIds;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.command.QuerySpuCartonMeasureInfoBySpuIdCommand;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.domain.MerchantSkuCartonMeasureInfo;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.domain.MerchantSkuInfo;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.result.MerchantSkuInfoListResult;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.result.SpuCartonMeasureInfoBySpuIdResult;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.service.EmpowerProductThriftService;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.common.Result;
import com.sankuai.shangou.logistics.warehouse.WarehouseStateService;
import com.sankuai.shangou.logistics.warehouse.inventory.GetSkuStateDTO;
import com.sankuai.shangou.logistics.warehouse.inventory.SkuState;
import com.sankuai.shangou.logistics.warehouse.inventory.enums.SkuBatchState;
import com.sankuai.shangou.logistics.warehouse.model.WarehouseKeyDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EmpowerProductWrapper {

	private static final int SINGLE_REQUEST_MAX_BATCH_SIZE = 50;
	private static final int ENABLE_BATCH = 1;

	private final static Long ALL_TENANT = -1L;

	@Autowired
	private EmpowerProductThriftService.Iface empowerProductThriftService;

	@Resource
	private PoiThriftService poiThriftService;

	@Autowired
	private WarehouseStateService warehouseStateService;

    /**
	 * 是否开启批次
	 *
	 * @param tenantId
	 * @param storeId
	 * @param skuIds
	 * @return
	 */
	public Map<String, Boolean> querySkuBatchEnableSwitchMap(Long tenantId, Long storeId, Set<String> skuIds) {
		Map<String, Boolean> resultMap = new HashMap<>();
		if (CollectionUtils.isEmpty(skuIds)) {
			return resultMap;
		}

		Lists.partition(new ArrayList<>(skuIds), SINGLE_REQUEST_MAX_BATCH_SIZE)
				.forEach(pageList -> {
					// 如果是托管仓 需要转换成共享中心仓去库存侧查询批次属性
					WarehouseKeyDTO operationWarehouseKey = outRepositoryConvert(new WarehouseKeyDTO(tenantId, storeId));
						Result<Map<String, SkuState>> skuStateRsp = warehouseStateService
								.batchGetSkuState(operationWarehouseKey.getMerchantId(), operationWarehouseKey.getWarehouseId(), pageList.stream().map(GetSkuStateDTO::new).collect(Collectors.toList()));
						if (!Objects.equals(skuStateRsp.getCode(), 0)) {
							throw new RpcException("查询货品批次属性失败");
						}
						if (MapUtils.isNotEmpty(skuStateRsp.getModule())) {
							resultMap.putAll(skuStateRsp.getModule()
									.entrySet()
									.stream()
									.collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().getBatchState() == SkuBatchState.BATCH)));
						}
				});
		return resultMap;
	}

	/**
	 * 按照租户维度+门店/仓位维度+sku维度查询货品的批次属性
	 *
	 * @param tenantId 租户id
	 * @return true 按照租户维度+门店/仓位维度+sku维度查询货品的批次属性 false 按照租户维度+sku维度查询货品的批次属性
	 */
	public static boolean useNewTenantGoodBatchInfoQuery(Long tenantId) {
		try {
			List<Long> allowTenantIdList = Optional
					.ofNullable(Lion.getConfigRepository().get("use.new.tenant.good.batch.info.query", "[]")).
					map(it -> JsonUtils.fromJson(it, new TypeReference<List<Long>>() {})).orElse(new ArrayList<>());
			if (allowTenantIdList.contains(ALL_TENANT)){
				log.info("[useNewTenantGoodBatchInfoQuery] is all allow");
				return true;
			}
			boolean allow = allowTenantIdList.contains(tenantId);
			log.info("[useNewTenantGoodBatchInfoQuery] return:{}", allow);
			return allow;
		}catch (Exception e){
			log.error("[useNewTenantGoodBatchInfoQuery] occur err", e);
			return false;
		}
	}

	private WarehouseKeyDTO outRepositoryConvert(WarehouseKeyDTO warehouseKey) {
		log.info("outRepositoryConvert req:{}", JSON.toJSONString(warehouseKey));
		PoiMapResponse response = poiThriftService.queryTenantPoiInfoMapByPoiIds(Lists.newArrayList(warehouseKey.getWarehouseId()), warehouseKey.getMerchantId());
		checkResult(response.getStatus());
		Map<Long, PoiInfoDto> poiInfoMap = response.getPoiInfoMap();
		PoiInfoDto poiInfoDto = poiInfoMap.get(warehouseKey.getWarehouseId());
		if (Objects.isNull(poiInfoDto)){
			throw new CommonRuntimeException("查询门店/仓信息失败");
		}
		// 如果是托管仓,转换为共享中心仓
		if (Objects.equals(poiInfoDto.getEntityType(), PoiEntityTypeEnum.REGIONAL_WAREHOUSE.code())
				&& Objects.equals(poiInfoDto.getOutRepository(), true)) {
			warehouseKey = new WarehouseKeyDTO(Long.parseLong(poiInfoDto.getOutRepositoryInfo().getOutRepositoryTenantId()),
					poiInfoDto.getOutRepositoryInfo().getOutRepositoryId());
		}
		log.info("outRepositoryConvert rsp:{}",JSON.toJSONString(warehouseKey));
		return warehouseKey;
	}

	private void checkResult(Status status) {
		if (status.getCode() != 0) {
			throw new RpcException("查询租户店/仓信息接口失败");
		}
	}

	public List<MerchantSkuCartonMeasureInfo> querySkuCartonMeasureInfo(long tenantId, String spuId) throws TException {
		QuerySpuCartonMeasureInfoBySpuIdCommand command = new QuerySpuCartonMeasureInfoBySpuIdCommand();
		command.setMerchantId(tenantId);
		command.setSpuId(spuId);
		SpuCartonMeasureInfoBySpuIdResult cartonInfoResult =
				empowerProductThriftService.querySpuCartonMeasureInfoBySpuId(command);
		// 无商品信息时
		if (Objects.isNull(cartonInfoResult) || Objects.isNull(cartonInfoResult.getStatus())
				|| cartonInfoResult.getStatus().getCode() != 0) {
			throw new BizException("商品无规格信息，查询异常");
		}

		return cartonInfoResult.getSkuCartonMersureList();
	}
}
