package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;


import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.StockDockingType;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.appmodel.QueryMenuInfoRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.appmodel.AppModelMenuInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.auth.AppModuleResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.MenuGroupEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.store.management.wms.api.config.WmsConfigurationThriftService;
import com.sankuai.meituan.reco.store.management.wms.api.config.model.StockDockingConfigDTO;
import com.sankuai.meituan.reco.store.management.wms.api.config.req.BatchQueryStockConfigReq;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.ResultCodeEnum;
import com.sankuai.shangou.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @since 2024/03/06
 */
@Slf4j
@Component
public class StockAuthWrapper {

    @Autowired
    private WmsConfigurationThriftService wmsConfigurationThriftService;

    /**
     * 获取门店库存对接配置
     * @param tenantId
     * @param storeId
     * @return
     */
    public Map<Long, Integer> batchQueryStockConfig(Long tenantId, Long storeId) {
        Map<Long, Integer> stockConfigMap = new HashMap<>();
        if (storeId == null || tenantId == null) {
            log.error("查询库存对接配置参数错误, tenantId:{}, storeId:{}", tenantId, storeId);
            return stockConfigMap;
        }

        BatchQueryStockConfigReq req = new BatchQueryStockConfigReq();
        req.setMerchantId(tenantId);
        req.setWarehouseIdList(Lists.newArrayList(storeId));
        try {
            Result<StockDockingConfigDTO> resp = wmsConfigurationThriftService.batchQueryStockDockingConfig(req);
            if (resp.getCode() != ResultCodeEnum.SUCCESS.getValue() || Objects.isNull(resp.getModule())) {
                log.error("查询库存对接配置失败, req:{}, resp:{}", JacksonUtils.toJson(req), JacksonUtils.toJson(resp));
            } else if (Objects.nonNull(resp.getModule())
                    && MapUtils.isNotEmpty(resp.getModule().getStockDockingTypeMap())) {
                return resp.getModule().getStockDockingTypeMap();
            }
        } catch (Exception e) {
            log.error("查询库存对接配置异常, req:{}", JacksonUtils.toJson(req), e);
        }

        return stockConfigMap;
    }

    /**
     * 过滤仓管经营模块权限（如果门店配置为外部系统对接，过滤相关子模块权限）
     * @param tenantId
     * @param poiId
     * @param result
     */
    public void filterManagementModuleResultByStockConfig(Long tenantId, Long poiId, AppModuleResult result) {
        if (Objects.isNull(result) || CollectionUtils.isEmpty(result.getModules())) {
            return;
        }

        // 获取门店是否开启外部系统库存配置
        Map<Long, Integer> stockConfigMap = batchQueryStockConfig(tenantId, poiId);

        // 已开启外部系统对接库存门店:需过滤库存模块相关权限，由lion控制过滤库存哪些模块code
        if (MapUtils.isNotEmpty(stockConfigMap)
                && StockDockingType.EXTERNAL_MANAGEMENT.getType().equals(stockConfigMap.get(poiId))) {
            result.setModules(result.getModules().stream()
                    .peek(module -> {
                        // 只过滤库存相关子模块
                        if ("MANAGEMENT_WAREHOUSE".equals(module.getCode())
                                && CollectionUtils.isNotEmpty(module.getSubModules())) {
                            module.setSubModules(module.getSubModules().stream()
                                    .filter(subModule -> !MccConfigUtil.getStockDockingFilterModuleConfig().contains(subModule.getCode()))
                                    .collect(Collectors.toList()));
                        }
                    }).collect(Collectors.toList()));
        }
    }

    /**
     * 过滤库存待办模块权限（如果门店配置为外部系统对接，过滤相关子模块权限）
     * @param tenantId
     * @param poiId
     * @param appModelMenuInfoList
     */
    public void filterAppMenuResultByStockConfig(Long tenantId, Long poiId, List<AppModelMenuInfo> appModelMenuInfoList) {
        if (CollectionUtils.isEmpty(appModelMenuInfoList)) {
            return;
        }

        // 获取门店是否开启外部系统库存配置
        Map<Long, Integer> stockConfigMap = batchQueryStockConfig(tenantId, poiId);
        // 已开启外部系统对接库存门店:需过滤待办、库存模块相关子模块权限，由lion控制过滤待办、库存两个模块哪些子模块code
        if (MapUtils.isNotEmpty(stockConfigMap)
                && StockDockingType.EXTERNAL_MANAGEMENT.getType().equals(stockConfigMap.get(poiId))) {
            appModelMenuInfoList.forEach(module -> {
                if (Objects.nonNull(module.getParentMenu()) && CollectionUtils.isNotEmpty(module.getSubMenus())) {
                    if ((MenuGroupEnum.ALL.getDesc().equals(module.getParentMenu().getMenuName())
                            || MenuGroupEnum.INVENTORY.getDesc().equals(module.getParentMenu().getMenuName()))) {
                        module.setSubMenus(module.getSubMenus().stream()
                            .filter(subModule -> !MccConfigUtil.getStockDockingFilterWorkbenchConfig().contains(subModule.getMenuCode()))
                            .collect(Collectors.toList()));
                    }
                }
            });
        }
    }
}
