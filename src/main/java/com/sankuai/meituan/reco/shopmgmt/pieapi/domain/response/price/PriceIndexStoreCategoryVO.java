package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price;
// Copyright (C) 2020 Meituan
// All rights reserved

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "门店品类价格指数对象结果"
)
@ApiModel("门店品类价格指数对象结果")
@Data
@AllArgsConstructor
public class PriceIndexStoreCategoryVO {


    /**
     * 门店价格指数信息
     */
    @FieldDoc(
            description = "门店价格指数信息"
    )
    @ApiModelProperty("门店价格指数信息")
    private PriceIndexVO storePriceIndexInfo;


    /**
     * 品类价格指数列表
     */
    @FieldDoc(
            description = "品类价格指数信息"
    )
    @ApiModelProperty("品类价格指数信息")
    private List<PriceIndexVO> categoryPriceIndexList;




}
