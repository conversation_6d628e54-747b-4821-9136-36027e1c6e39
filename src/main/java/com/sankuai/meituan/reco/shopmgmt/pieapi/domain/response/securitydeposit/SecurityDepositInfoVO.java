package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.securitydeposit;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2020/3/2 21:36
 * @Description:
 */
@TypeDoc(description = "门店保证金、电子协议信息")
@Data
public class SecurityDepositInfoVO {

    /**
     * 保证金ID
     */
    private Long id;

    /**
     * 保证金金额
     */
    private Double amount;

    /**
     * 保证金剩余金额
     */
    private Double remainAmount;

    /**
     * 保证金状态
     */
    private Integer status;


    private String contractUrl;


    private Long budgetId;

    /**
     * 保证金变更记录
     */
    private List<SecurityDepositChangeRecordVO> changeRecords;

    // 电子协议相关信息
    /**
     * 是否展示协议按钮
     * 1:要展示/0:不展示
     */
    private Integer showProtocolButton;

    /**
     * 电子协议状态
     * 1-待签署 2-无需签署 3-已签署(纸质) 4-已签署(电子)
     */
    private Integer contractStatus;

    /**
     * 协议签署时间
     */
    private String contractSignTime;

    /**
     * 协议过期时间
     */
    private String contractExpireTime;

    /**
     * 当前账号是否店长 0-否 1-是
     */
    private Integer isStoreManager;
    /**
     * 当前门店店长名字
     */
    private List<String> storeManagerList;
    /**
     * 保证金余额是否充足
     */
    private Integer isFundSufficient;
    /**
     * 保证金余额最低比例，20代表20%
     */
    private Double lowestRatio;
    /**
     * 待签署协议页面url
     */
    private String unsignedContractUrl;


    private Long contractId;

    /**
     * 追加了确认函的协议url
     */
    private String appendConfirmLetterContractUrl;

    /**
     * 当前协议是否为主协议
     */
    private Boolean isMainContract;

    /**
     * 协议保证金模式
     * @see com.meituan.shangou.saas.tenant.thrift.common.enums.SecurityDepositModeEnum
     */
    private Integer securityDepositMode;

}
