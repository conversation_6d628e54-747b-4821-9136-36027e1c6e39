package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;
// Copyright (C) 2019 Meituan
// All rights reserved

import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.XmAccountInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.shangou.saas.message.request.im.GetWmPoiAccountInfoRequest;
import com.sankuai.meituan.shangou.saas.message.request.im.ReportDeviceToWmRequest;
import com.sankuai.meituan.shangou.saas.message.response.MessageCommonResponse;
import com.sankuai.meituan.shangou.saas.message.response.im.GetWmPoiAccountInfoResponse;
import com.sankuai.meituan.shangou.saas.message.service.ImThriftService;
import com.sankuai.meituan.waimai.poisearch.thrift.domain.WmPoiSearchParam;
import com.sankuai.meituan.waimai.poisearch.thrift.domain.WmPoiSearchResult;
import com.sankuai.meituan.waimai.poisearch.thrift.service.WmPoiSearchThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: <EMAIL>
 * @class: XmImWapper
 * @date: 2019-09-25 11:25:36
 * @desc: 大象im相关调用
 */
@Slf4j
@Service
public class XmImWrapper {

    @Autowired
    private ImThriftService imThriftService;

    @Autowired
    private WmPoiSearchThriftService.Iface wmPoiService;

    /**
     * 门店名称查询SQL
     */
    private static final String POI_NAME_SEARCH_SQL = "select name from poi.poi_index_rw where wmPoiId = %s";

    @MethodLog(logResponse = true, logRequest = true)
    public CommonResponse<XmAccountInfoVO> genWmPoiXmAccount(String wmPoiId, String deviceId, Long accountId, String osType, String shangouPoiName) {
        GetWmPoiAccountInfoRequest request = new GetWmPoiAccountInfoRequest();
        request.setWmPoiId(wmPoiId);
        request.setOsType(osType2PushOsType(osType));
        request.setAccountId(accountId);
        request.setDeviceId(deviceId);
        try {
            GetWmPoiAccountInfoResponse response = imThriftService.getWmPoiXmAccountInfo(request);
            if (response.getCode() != 0) {
                return CommonResponse.fail(response.getCode(), response.getMsg());
            }
            String wmPoiName = getWmPoiNameById(wmPoiId);
            if (StringUtils.isEmpty(wmPoiName)) {
                wmPoiName = shangouPoiName;
            }
            return CommonResponse.success(new XmAccountInfoVO(response.getAccountInfo().getAccount(), response.getAccountInfo().getXmToken(), wmPoiId, wmPoiName));
        } catch (TException e) {
            log.error("获取外卖门店账号失败",e);
            throw new CommonRuntimeException("获取外卖门店账号失败");
        }
    }

    @MethodLog(logResponse = true, logRequest = true)
    public CommonResponse<Void> reportDeviceToWm(String wmPoiId, String deviceId, String osType) {
        ReportDeviceToWmRequest request = new ReportDeviceToWmRequest();
        request.setDeviceId(deviceId);
        request.setWmPoiId(wmPoiId);
        request.setOsType(osType2PushOsType(osType));
        try {
            MessageCommonResponse response = imThriftService.reportDeviceToWm(request);
            if (response.getCode() != 0) {
                return CommonResponse.fail(response.getCode(), response.getMsg());
            }
            return CommonResponse.success(null);
        } catch (TException e) {
            log.error("向外卖上报设备信息失败",e);
            throw new CommonRuntimeException("向外卖上报设备信息失败");
        }
    }

    private String getWmPoiNameById(String wmPoiId) {
        WmPoiSearchParam wmPoiSearchParam = new WmPoiSearchParam();
        wmPoiSearchParam.setSql(String.format(POI_NAME_SEARCH_SQL, wmPoiId));
        try {
            WmPoiSearchResult searchResult = wmPoiService.search(wmPoiSearchParam);
            if (searchResult.getTotal() == 1) {
                return searchResult.getFields().get(0).getName();
            }
        } catch (Exception e) {
            log.error("外卖门店查询异常",e);
        }
        return null;
    }

    private String osType2PushOsType(String osType) {
        return StringUtils.isNotEmpty(osType) ? osType.toUpperCase() : null;
    }
}
