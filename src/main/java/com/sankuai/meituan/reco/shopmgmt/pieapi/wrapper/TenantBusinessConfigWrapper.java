package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.dianping.zebra.util.StringUtils;
import com.meituan.shangou.saas.tenant.thrift.BusinessConfigThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.StatusCodeEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.AcceptOrderModeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.config.response.AcceptOrderModeQueryResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @createTime 2019/11/26
 * @description
 */
@Slf4j
@Component
public class TenantBusinessConfigWrapper {

    @Autowired
    private BusinessConfigThriftService businessConfigThriftService;
    /**
     * 查询门店是否是手动接单
     * @param tenantId
     * @param shopId
     * @return
     */
    @MethodLog(logRequest = true, logResponse = true)
    public boolean isManualTakeOrder(Long tenantId, Long shopId) {

        String acceptOrderMode = queryAcceptOrderMode(tenantId, shopId);
        if (StringUtils.isBlank(acceptOrderMode)) {
            return isManualTakeOrderFallback(tenantId, shopId);
        } else {
            return AcceptOrderModeEnum.MANUAL.getKey().equals(acceptOrderMode);
        }
    }

    /**
     * 查询门店是否是自动接单
     * @param tenantId
     * @param shopId
     * @return
     */
    @MethodLog(logRequest = true, logResponse = true)
    public boolean isAutoTakeOrder(Long tenantId, Long shopId) {
        String acceptOrderMode = queryAcceptOrderMode(tenantId, shopId);
        if (StringUtils.isNotBlank(acceptOrderMode)) {
            return AcceptOrderModeEnum.AUTO.getKey().equals(acceptOrderMode);
        } else {
            return isAutoTakeOrderFallback(tenantId, shopId);
        }
    }

    private String queryAcceptOrderMode(Long tenantId, Long shopId) {
        try {
            AcceptOrderModeQueryResponse acceptOrderModeQueryResponse = businessConfigThriftService.queryAcceptOrderMode(tenantId, shopId);
            log.info("查询门店自动接单配置 tenantId={}, shopId={}, acceptOrderModeQueryResponse={}", tenantId, shopId, acceptOrderModeQueryResponse);
            if (acceptOrderModeQueryResponse != null
                    && acceptOrderModeQueryResponse.getStatus() != null
                    && acceptOrderModeQueryResponse.getStatus().getCode() != null
                    && StatusCodeEnum.SUCCESS.getCode() == acceptOrderModeQueryResponse.getStatus().getCode()) {
                // 非自动接单&非，视为手动接单，防止自动接单的定义过于宽泛，所以采取安全的判断方式,手动接单的错判比自动接单的错判影响更小
                String acceptOrderMode = acceptOrderModeQueryResponse.getAcceptOrderMode();
                return acceptOrderMode;
            } else {
                // 非正常返回, 返回空值
                log.error("查询门店自动接单配置异常,请求参数,tenantId:{}, shopId:{},response:{}", tenantId, shopId, acceptOrderModeQueryResponse);
                return "";
            }
        } catch (TException ex) {
            log.error("businessConfigThriftService.queryAcceptOrderMode TException", ex);
            // 发生调用异常，返回空值
            return "";
        }
    }

    /**
     * 查询门店是否是手动接单降级方法,默认都是手动接单，可以给商家展示待接单
     * @param tenantId
     * @param shopId
     * @return
     */
    private boolean isManualTakeOrderFallback(Long tenantId, Long shopId) {
        return true;
    }

    /**
     * 查询门店是否是自动接单降级方法,默认都不是自动接单，防止丢失订单
     * @param tenantId
     * @param shopId
     * @return
     */
    private boolean isAutoTakeOrderFallback(Long tenantId, Long shopId) {
        return false;
    }


}
