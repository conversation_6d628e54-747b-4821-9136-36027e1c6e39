package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.drunkhorsemgmt.labor.thrift.dto.ScheduleRuleDTO;
import com.sankuai.drunkhorsemgmt.labor.thrift.dto.ScheduleRuleLocationDTO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.LocationStoreVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.ScheduleRuleVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-10-19
 * @email <EMAIL>
 */
@TypeDoc(
        description = "新建/编辑班次请求"
)
@ApiModel("新建/编辑班次请求")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SearchRuleLocationResponse {

    @FieldDoc(
            description = "排班规则列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "规则id")
    private List<LocationStoreVO> locationStoreInfoList;

    @FieldDoc(
            description = "是否有更多", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否有更多")
    private boolean hasMore;

    @FieldDoc(
            description = "总数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "总数")
    private long totalCount;

}
