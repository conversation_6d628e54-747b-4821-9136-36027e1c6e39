package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.handler;


import com.meituan.linz.product.channel.EnhanceChannelType;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.ProblemSpuCountQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.AbnormalRuleNodeVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.diffcompare.QueryDiffCompareSpuRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.QueryCompareTypeCountResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.service.ProblemSpuThriftService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import static com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ProjectConstants.EBLS_SYNC_ABNORMAL_COUNT;
import static com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ProjectConstants.INCONSISTENT_SPU_COUNT;

/**
 * <AUTHOR>
 * @since 2024/7/24
 */
@Slf4j
@Component
public class DiffCompareCountHandler extends AbstractCountStoreSpuHandler {

    @Resource
    private ProblemSpuThriftService problemSpuThriftService;

    @Override
    public Integer getChannelId() {
        return null;
    }

    @Override
    public String getCountCode() {
        // 对比不一致
        return INCONSISTENT_SPU_COUNT;
    }

    @Override
    public boolean isMatch(Set<Integer> poiChannels){
        return true;
    }

    @Override
    public List<String> getAbnormalCodes() {
        return null;
    }

    @Override
    public Integer process(Long tenantId, ProblemSpuCountQueryRequest queryRequest, Map<String, AbnormalRuleNodeVO> abnormalRuleMap){
        //计算商品异常信息统计
        QueryDiffCompareSpuRequest request = queryRequest.toDiffCompareSpuRequest(tenantId);
        QueryCompareTypeCountResponse compareTypeCountResponse = problemSpuThriftService.queryDiffCompareTypeCount(request);

        if (Objects.isNull(compareTypeCountResponse)
                || !compareTypeCountResponse.getStatus().getCode().equals(ResultCode.SUCCESS.getCode())) {
            log.error("查询店铺问题商品类型统计异常， request [{}], compareTypeCountResponse [{}].",
                    queryRequest, compareTypeCountResponse);
            throw new CommonRuntimeException("查询店铺问题商品类型统计异常");
        }
        return compareTypeCountResponse.getCompareTypeCount().values()
                .stream()
                .reduce(0, Integer::sum);
    }
}
