package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.price.client.dto.contrast.ContrastStoreWithIndexDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

/**
 * <AUTHOR>
 * @desc 竞对门店带指标模型
 * @email <EMAIL>
 * @date 2020-12-02
 */
@TypeDoc(
        description = "竞对门店带指标模型"
)
@ApiModel("竞对门店带指标模型")
@Getter
@AllArgsConstructor
@ToString
public class ContrastStoreWithIndexVO {
    @FieldDoc(
            description = "门店名"
    )
    @ApiModelProperty("门店名")
    public String contrastStoreName;
    @FieldDoc(
            description = "门店类型"
    )
    @ApiModelProperty("门店类型")
    public Integer contrastStoreType;
    @FieldDoc(
            description = "门店id"
    )
    @ApiModelProperty("门店id")
    public Long contrastStoreId;
    @FieldDoc(
            description = "月销量"
    )
    @ApiModelProperty("月销量")
    public Integer monthSale;
    @FieldDoc(
            description = "spu数"
    )
    @ApiModelProperty("spu数")
    public Integer spuCount;
    @FieldDoc(
            description = "价格指数"
    )
    @ApiModelProperty("价格指数")
    public Double priceIndex;
    @FieldDoc(
            description = "是否计算完成"
    )
    @ApiModelProperty("是否计算完成")
    public Boolean calculateDone;
    @FieldDoc(
            description = "低价商品数"
    )
    @ApiModelProperty("低价商品数")
    public Integer lowPriceSpuCount;
    @FieldDoc(
            description = "匹配商品数"
    )
    @ApiModelProperty("匹配商品数")
    public Integer allMatchedSpuCount;
    @FieldDoc(
            description = "低价商品率"
    )
    @ApiModelProperty("低价商品率")
    public Integer lowPriceRate;
    @FieldDoc(
            description = "价格对比"
    )
    @ApiModelProperty("价格对比")
    public Integer comparePriceRate;

}
