package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import java.util.Objects;

import com.google.common.base.Preconditions;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2024/3/4
 */

@TypeDoc(
        description = "查询门店在美团渠道一级经营品类请求对象"
)
@Data
@ApiModel("查询门店在美团渠道一级经营品类请求对象")
@ToString
public class StoreBusinessCategoryQueryRequest {
    @FieldDoc(
            description = "渠道id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道id")
    private Integer channelId;

    public void selfCheck(){
        Preconditions.checkArgument(Objects.nonNull(channelId), "渠道id不能为空");
    }
}
