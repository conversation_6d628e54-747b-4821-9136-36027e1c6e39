package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.labor.convertor;

import com.google.common.collect.Lists;
import com.sankuai.drunkhorsemgmt.labor.thrift.dto.ApprovalNodeDTO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.ApprovalVO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-12-09
 * @email <EMAIL>
 */
@Component
public class ApprovalConverter {

    public List<ApprovalVO> convertToApprovalVO(List<ApprovalNodeDTO> approvalNodeDTOList) {
        return Optional
                .ofNullable(approvalNodeDTOList)
                .orElse(Lists.newArrayList())
                .stream()
                .map(
                        dto -> {
                            ApprovalVO vo = new ApprovalVO();
                            vo.setApprovalText(dto.getText());
                            vo.setApprovalNameList(dto.getNodeNameList());
                            vo.setStatus(dto.getApprovalStatus());
                            vo.setDoneTime(dto.getFinishTime());
                            vo.setFinishNodeName(dto.getFinishNodeName());
                            vo.setRemark(dto.getRemark());
                            return vo;
                        }
                )
                .collect(Collectors.toList());
    }

}
