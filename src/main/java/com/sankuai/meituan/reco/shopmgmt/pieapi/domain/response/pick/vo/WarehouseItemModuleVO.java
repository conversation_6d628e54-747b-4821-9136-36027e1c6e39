package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "拣货商品信息"
)
@Data
@ApiModel("拣货商品信息")
public class WarehouseItemModuleVO {

    @FieldDoc(
            description = "库位信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "库位信息")
    private String locationName;

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @FieldDoc(
            description = "商品sku", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "商品sku")
    private String skuId;

    @FieldDoc(
            description = "规格", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "规格")
    private String spec;

    @FieldDoc(
            description = "品类信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "品类信息")
    private String categoryName;

    @FieldDoc(
            description = "商品图片列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "商品图片列表")
    private List<String> picList;

    @FieldDoc(
            description = "upc列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "upc列表")
    private List<String> upcList;

    @FieldDoc(
            description = "应拣数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "应拣数量")
    private Integer needNum;

    @FieldDoc(
            description = "实拣数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "实拣数量")
    private Integer actPickNum;

    @FieldDoc(
            description = "拣货任务id", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "拣货任务id")
    private Long pickTaskId;

    @FieldDoc(
            description = "批次状态 1正常、2临期预警、3临期禁售、4超期预警"
    )
    private Integer periodStatus;

    @FieldDoc(
            description = "批次属性", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "批次属性")
    private List<BatchInfoVO> batchList;

    @FieldDoc(
            description = "拣货箱规单位", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "拣货箱规单位")
    private SkuPackingSpecVO pickPackingSpec;

    @FieldDoc(
            description = "箱规单位列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "箱规单位列表")
    private List<SkuPackingSpecVO> packingSpecList;

    @FieldDoc(
            description = "库存基本单位名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "库存基本单位名称")
    private String basicUnit;

    @FieldDoc(
            description = "已装箱数量，修改拣货数量时作为调整下限", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "已装箱数量，修改拣货数量时作为调整下限")
    private Integer packNum;
}
