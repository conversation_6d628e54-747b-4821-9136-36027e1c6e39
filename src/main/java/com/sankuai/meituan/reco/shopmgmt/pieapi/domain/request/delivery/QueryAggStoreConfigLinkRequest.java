package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery;

import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2025-01-14
 */
@Data
public class QueryAggStoreConfigLinkRequest {

    private Long entityId;

    private Integer platformCode;


    public void validate() {
        if(entityId == null) {
            throw new BizException("门店id不能为空");
        }
        if(platformCode == null) {
            throw new BizException("平台code不能为空");
        }
    }
}
