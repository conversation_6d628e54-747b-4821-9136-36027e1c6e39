package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.productintelligent;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productintelligent.thrift.model.SimilarGoodsResultListDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "相似商品查询相应结果",
        authors = {"lixingzhong"}
)
@Data
@ApiModel("相似商品查询相应结果")
public class SimilarGoodsResponseVO {
    @FieldDoc(
            description = "相似商品信息列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "相似商品信息列表", required = true)
    private List<SimilarGoodVO> similarGoodVOList;

    public static SimilarGoodsResponseVO convertResponse(SimilarGoodsResultListDTO similarGoodsResultListDTO) {
        SimilarGoodsResponseVO similarGoodsResponseVO = new SimilarGoodsResponseVO();
        similarGoodsResponseVO.setSimilarGoodVOList(SimilarGoodVO.ofDTOList(similarGoodsResultListDTO.getSimilarGoods()));
        return similarGoodsResponseVO;
    }
}
