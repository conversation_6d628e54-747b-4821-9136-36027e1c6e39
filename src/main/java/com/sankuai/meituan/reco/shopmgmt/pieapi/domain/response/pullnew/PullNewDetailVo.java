package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/06/09
 */
@TypeDoc(
		description = "展示微信二维码"
)
@Data
@ApiModel("展示微信拉新记录")
@NoArgsConstructor
@AllArgsConstructor
public class PullNewDetailVo {

	@FieldDoc(
			description = "昵称", requiredness = Requiredness.REQUIRED
	)
	@ApiModelProperty(value = "昵称", required = true)
	private String userName;

	@FieldDoc(
			description = "头像", requiredness = Requiredness.REQUIRED
	)
	@ApiModelProperty(value = "头像", required = true)
	private String userPic;

	@FieldDoc(
			description = "事件描述", requiredness = Requiredness.REQUIRED
	)
	@ApiModelProperty(value = "事件描述", required = true)
	private String eventName;

	@FieldDoc(
			description = "事件类型", requiredness = Requiredness.REQUIRED
	)
	@ApiModelProperty(value = "事件类型", required = true)
	private Integer eventType;

	@FieldDoc(
			description = "时间戳", requiredness = Requiredness.REQUIRED
	)
	@ApiModelProperty(value = "时间戳", required = true)
	private Long eventTime;

	@FieldDoc(
			description = "时间戳", requiredness = Requiredness.REQUIRED
	)
	@ApiModelProperty(value = "订单id", required = true)
	private String orderNo;
	@FieldDoc(
			description = "订单业务类型", requiredness = Requiredness.REQUIRED
	)
	@ApiModelProperty(value = "订单id", required = true)
	private Integer orderBizType;

	@FieldDoc(
			description = "备注", requiredness = Requiredness.REQUIRED
	)
	@ApiModelProperty(value = "备注", required = true)
	private String note;

}
