package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description: 自提码校验参数
 * @author: fanxiaolin
 * @date: 2023/2/20
 * @time: 14:21
 * Copyright (C) 2019 Meituan
 * All rights reserved
 */
@TypeDoc(
        description = "自提码核验参数"
)
@ApiModel("自提码核验参数")
@Data
public class VerifySelfFetchCodeRequest {

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道ID")
    @NotNull
    private Integer channelId;

    @FieldDoc(
            description = "数字自提码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "数字自提码")
    private String selfFetchCode;

    @FieldDoc(
            description = "地址取货码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "地址取货码")
    private String qrSelfFetchCode;

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店id")
    @NotNull
    private String storeId;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道订单号")
    private String channelOrderId;
}
