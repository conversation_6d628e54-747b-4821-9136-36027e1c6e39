package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.alibaba.fastjson.JSONObject;
import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Lists;
import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.AdjustContainerBatchStockRequestInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.AdjustSkuRequestInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.CreateStockAdjustRequestVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.CreateStockAdjustResponseVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.FallbackException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.store.management.stock.biz.base.thrift.query.*;
import com.sankuai.meituan.reco.store.management.stock.biz.commons.constants.ContainerType;
import com.sankuai.meituan.reco.store.management.stock.biz.commons.constants.RepositoryType;
import com.sankuai.meituan.reco.store.management.stock.biz.commons.constants.StatusEnum;
import com.sankuai.meituan.reco.store.management.stock.biz.commons.constants.StockAdjustType;
import com.sankuai.meituan.reco.store.management.stock.biz.commons.constants.StockZoneType;
import com.sankuai.meituan.reco.store.management.stock.biz.comprehensive.query.stockquery.BatchStockQueryThriftService;
import com.sankuai.meituan.reco.store.management.stock.biz.comprehensive.query.stockquery.rep.BatchStockWarningQueryReq;
import com.sankuai.meituan.reco.store.management.stock.biz.comprehensive.query.stockquery.resp.BatchStockWarningQueryResp;
import com.sankuai.meituan.reco.store.management.thrift.EmpowerTaskLogicException;
import com.sankuai.meituan.reco.store.management.thrift.delivery.ObtainWaitCountResult;
import com.sankuai.meituan.reco.store.management.thrift.stockdjustment.StockAdjustmentThriftService;
import com.sankuai.meituan.reco.store.management.thrift.stockdjustment.request.StockAdjustOrderCreateReq;
import com.sankuai.meituan.reco.store.management.thrift.stockdjustment.response.StockAdjustOrderCreateResp;
import com.sankuai.meituan.reco.store.management.thrift.warehousemanagesku.req.BatchSkuProperty;
import com.sankuai.meituan.reco.store.management.thrift.warehousemanagesku.req.BatchSkuPropertyReq;
import com.sankuai.meituan.reco.store.management.thrift.warehousemanagesku.resp.SkuBatchProperty;
import com.sankuai.meituan.reco.store.management.thrift.warehousemanagesku.resp.SkuBatchPropertyDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.BatchUpdateStoreStockRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.CommonResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.StoreSkuSaleStockDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.AutoResumeInfiniteStockFlag;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.CustomizeStockFlag;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.SkuStoreStockThriftService;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.shangou.common.Result;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.utils.collection.IListUtils;
import com.sankuai.shangou.logistics.warehouse.InventoryQueryService;
import com.sankuai.shangou.logistics.warehouse.inventory.dto.LocationInventoryDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by yangli on 18/9/28.
 */
@Slf4j
@Rhino
@Service
public class StockWrapper {

    @Resource
    private BatchStockQueryThriftService batchStockQueryThriftService;

    @Resource
    private QueryStockThriftService.Iface queryStockThriftService;

    @Resource
    private InventoryQueryService inventoryQueryService;

    @Resource
    private TenantWrapper tenantWrapper;

    @Resource
    private SkuStoreStockThriftService.Iface skuStoreStockThriftService;

    @Resource
    private StockAdjustmentThriftService stockAdjustmentThriftService;


    // 存捡区库位类型
    private static List<ContainerType> saleZoneType = Arrays.asList(
            StockZoneType.SALES_ZONE.getSysContainerType(),
            StockZoneType.SALES_ZONE.getManualContainerType(),
            ContainerType.MANUAL_DEFINED_STORAGE_LOCATION
    );


    @Degrade(rhinoKey = "StockWrapper.batchStockWarningCount",
            fallBackMethod = "batchStockWarningCountFallback",
            timeoutInMilliseconds = 1200,
            ignoreExceptions = {EmpowerTaskLogicException.class})
    @MethodLog(logRequest = true, logResponse = true)
    public BatchStockWarningQueryResp batchStockWarningCount(BatchStockWarningQueryReq request) throws EmpowerTaskLogicException {
        try {
            log.info("StockWrapper.batchStockWarningCount, request:{}", request);
            BatchStockWarningQueryResp rpcResp = batchStockQueryThriftService.queryBatchStockWarnings(request);
            log.info("StockWrapper.batchStockWarningCount, response:{}", rpcResp);
            return rpcResp;
        } catch (TException e) {
            log.error("StockWrapper.batchStockWarningCount error, req = {}", JSONObject.toJSONString(request), e);
            throw new CommonRuntimeException(e);
        }
    }

    @SuppressWarnings("unused")
    private ObtainWaitCountResult batchStockWarningCountFallback(BatchStockWarningQueryReq request) {
        throw new FallbackException("StockWrapper.batchStockWarningCount接口降级");
    }

    @Degrade(rhinoKey = "StockWrapper.queryStockInfo",
            fallBackMethod = "queryStockInfoFallback",
            timeoutInMilliseconds = 2000)
    @MethodLog(logResponse = true, logRequest = true)
    @CatTransaction
    public List<SkuStockInfo> queryStockInfo(StockInfoRequest request) {
        try {
            return queryStockThriftService.queryCentralAndWmsStockInfo(request);
        } catch (TException e) {
            log.error("StockWrapper queryStockInfo exception, request = {}", JacksonUtils.toJson(request), e);
            throw new CommonRuntimeException("StockWrapper queryStockInfo exception", e);
        }
    }

    private List<SkuStockInfo> queryStockInfoFallback(StockInfoRequest request) {
        throw new FallbackException("StockWrapper queryStockInfo接口降级");
    }


    /**
     * 根据sku查找仓内存捡区总实物库存情况
     */
    @MethodLog(logRequest = true, logResponse = true)
    @Degrade(rhinoKey = "StockWrapper.oldQuerySaleZoneSkuStockAvailable", fallBackMethod = "oldQuerySaleZoneSkuStockAvailableFallback", timeoutInMilliseconds = 2000)
    public Map<String, BigDecimal> oldQuerySaleZoneSkuStockAvailable(Long warehouseId, Long merchantId, List<String> skuIds) {
        try {
            List<ContainerStockInfo> stocks = queryStockThriftService.queryContainerStockBySkuIdsForceMaster(new QueryBySkuIdRequest()
                    .setTenantId(merchantId)
                    .setRepositoryId(warehouseId)
                    .setRepositoryType(RepositoryType.STORE.val())
                    .setSkuIds(skuIds)
            );
            stocks = stocks.stream().filter(stock -> saleZoneType.contains(ContainerType.valueOf(stock.getContainerType())))
                    .collect(Collectors.toList());
            return stocks.stream().collect(Collectors.toMap(
                    ContainerStockInfo::getSkuId,
                    stock -> BigDecimal.valueOf(stock.getValidQuantity()),
                    BigDecimal::add
            ));
        } catch (Exception e) {
            throw new ThirdPartyException("queryStockThriftService.queryStockInfo failed");
        }
    }

    public Map<String, BigDecimal> oldQuerySaleZoneSkuStockAvailableFallback(Long warehouseId, Long merchantId, List<String> skuIds) {
        log.error("StockWrapper.oldQuerySaleZoneSkuStockAvailable 发生降级");
        return Collections.emptyMap();
    }


    /**
     * 根据sku查找仓内存捡区总实物库存情况
     */
    @MethodLog(logRequest = true, logResponse = true)
    @Degrade(rhinoKey = "StockWrapper.querySaleZoneSkuStockAvailable", fallBackMethod = "querySaleZoneSkuStockAvailableFallback", timeoutInMilliseconds = 2000)
    public Map<String, BigDecimal> querySaleZoneSkuStockAvailable(Long warehouseId, Long merchantId, List<String> skuIds) {
        try {
            Result<List<LocationInventoryDTO>> result = inventoryQueryService.queryInventoryBySku(
                    warehouseId, skuIds, IListUtils.mapTo(StockZoneType.SALES_ZONE.getContainerType(), ContainerType::val)
            );
            log.info("inventoryQueryService.queryInventoryBySku response = {}", result);
            if (!Objects.equals(result.getCode(), StatusEnum.SUCCESS.getCode())) {
                throw new ThirdPartyException();
            }

            Map<String, BigDecimal> stockMap = IListUtils.nullSafeStream(result.getModule()).collect(Collectors.toMap(
                    LocationInventoryDTO::getSkuId,
                    locationInventoryDTO -> locationInventoryDTO.getQuantity().subtract(locationInventoryDTO.getLockQuantity()),
                    BigDecimal::add
            ));

            skuIds.forEach(skuId -> stockMap.putIfAbsent(skuId, BigDecimal.ZERO));

            return stockMap;

        } catch (Exception e) {
            throw new ThirdPartyException("queryStockThriftService.queryStockInfo failed");
        }
    }

    public Map<String, BigDecimal> querySaleZoneSkuStockAvailableFallback(Long warehouseId, Long merchantId, List<String> skuIds) {
        log.error("StockWrapper.querySaleZoneSkuStockAvailable 发生降级");
        return Collections.emptyMap();
    }


    /**
     * 保存库存，无限库存暂时走商品；
     * 自定义库存走库存调拨
     *
     * @param req
     * @return
     * @throws TException
     */
    @MethodLog(logRequest = true, logResponse = true)
    public CreateStockAdjustResponseVo saveSkuStock(CreateStockAdjustRequestVo req) {
        CreateStockAdjustResponseVo responseVo = new CreateStockAdjustResponseVo();
        responseVo.setCode(0);
        if (CollectionUtils.isEmpty(req.getSkuList())) {
            return responseVo;
        }

        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        User user = identityInfo.getUser();

        //设置无限库存的sku
        List<AdjustSkuRequestInfo> unCustomStockSkuList = req.getSkuList().stream()
                .filter(item -> Objects.equals(item.getCustomizeStockFlag(), CustomizeStockFlag.UNCUSTOMIZE.getValue()))
                .collect(Collectors.toList());

        //自定义库存的sku
        List<AdjustSkuRequestInfo> customStockSkuList = req.getSkuList().stream()
                .filter(sku -> Objects.equals(sku.getCustomizeStockFlag(), CustomizeStockFlag.CUSTOMIZE.getValue()))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(unCustomStockSkuList)) {
            try {
                BatchUpdateStoreStockRequest batchUpdateStoreStockRequest = convertBatchUpdateStoreStockRequest(req, unCustomStockSkuList, user, true);
                CommonResponse commonResponse = skuStoreStockThriftService.batchUpdateStoreStock(batchUpdateStoreStockRequest);
                if (commonResponse == null || commonResponse.getCode() != 0) {
                    throw new CommonRuntimeException();
                }
            } catch (Exception e) {
                log.error("saveSkuStock batchUpdateStoreStock error for un custom stock, request: {}", unCustomStockSkuList, e);
                responseVo.setCode(-1);
                responseVo.setMsg("无限库存更新失败");
            }
        }

        //过滤无变更的情况
        if (CollectionUtils.isEmpty(customStockSkuList)) {
            return responseVo;
        }

        try {
            //特殊逻辑，为了保存前端自定以库存、无限库存展示状态。
            BatchUpdateStoreStockRequest batchUpdateStoreStockRequest = convertBatchUpdateStoreStockRequest(req, customStockSkuList, user, false);
            CommonResponse commonResponse = skuStoreStockThriftService.batchUpdateStoreStock(batchUpdateStoreStockRequest);
            if (commonResponse == null || commonResponse.getCode() != 0) {
                throw new CommonRuntimeException();
            }
        } catch (Exception e) {
            log.error("saveSkuStock batchUpdateStoreStock error for custom stock, request: {}", unCustomStockSkuList, e);
            responseVo.setCode(-1);
            responseVo.setMsg("库存调整标识更新失败");
        }

        //过滤空参数的调整请求
        customStockSkuList = customStockSkuList.stream().filter(sku -> CollectionUtils.isNotEmpty(sku.getBatchContainerInfoList())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(customStockSkuList)) {
            return responseVo;
        }

        //调整库存
        StockAdjustOrderCreateResp resp = stockAdjustmentThriftService.create(toStockAdjustOrderCreateReq(user, req, customStockSkuList));
        if (resp == null || resp.getCode() != 0) {
            responseVo.setCode(-1);
            responseVo.setMsg(resp != null ? resp.getMsg() : "库存调整失败");
        }

        return responseVo;
    }


    private BatchUpdateStoreStockRequest convertBatchUpdateStoreStockRequest(CreateStockAdjustRequestVo req, List<AdjustSkuRequestInfo> skuList, User user, boolean isInfiniteStock) {
        BatchUpdateStoreStockRequest batchUpdateStoreStockRequest = new BatchUpdateStoreStockRequest();

        batchUpdateStoreStockRequest.setTenantId(user.getTenantId());

        batchUpdateStoreStockRequest.setStoreSkuSaleStockList(convertInfiniteStockSkuList(skuList, req.getEntityId(), isInfiniteStock));
        batchUpdateStoreStockRequest.setOperatorId(user.getAccountId());
        if (Objects.nonNull(user.getOperatorName())) {
            batchUpdateStoreStockRequest.setOperatorName(user.getAccountName());
        }
        batchUpdateStoreStockRequest.setComment(req.getComment());

        batchUpdateStoreStockRequest.setAdjustType(StockAdjustType.VALID_QUANTITY.val());
        return batchUpdateStoreStockRequest;

    }

    private List<StoreSkuSaleStockDTO> convertInfiniteStockSkuList(List<AdjustSkuRequestInfo> skuStockList, Long storeId, boolean isInfiniteStock) {
        if (Objects.isNull(skuStockList))
            return Lists.newArrayList();
        List<StoreSkuSaleStockDTO> storeSkuSaleStockDTOList = Lists.newArrayList();
        for (AdjustSkuRequestInfo storeSkuStockVO : skuStockList) {
            StoreSkuSaleStockDTO storeSkuSaleStockDTO = new StoreSkuSaleStockDTO();
            if (Objects.nonNull(storeId)) {
                storeSkuSaleStockDTO.setStoreId(storeId);
            }
            long newQuantity = storeSkuStockVO.getBatchContainerInfoList().stream().filter(Objects::nonNull)
                    .mapToLong(AdjustContainerBatchStockRequestInfo::getNewQuantity)
                    .sum();
            storeSkuSaleStockDTO.setQuantity(newQuantity);
            storeSkuSaleStockDTO.setSkuCode(storeSkuStockVO.getSkuId());
            if (Objects.nonNull(storeSkuStockVO.getCustomizeStockFlag())) {
                storeSkuSaleStockDTO.setCustomizeStockFlag(storeSkuStockVO.getCustomizeStockFlag());
            }
            //无限库存设置为false
            storeSkuSaleStockDTO.setIsAdjustStockQuantity(!isInfiniteStock);
            storeSkuSaleStockDTO.setAutoResumeInfiniteStock(storeSkuStockVO.getAutoResumeInfiniteStock() == null ?
                    AutoResumeInfiniteStockFlag.UNAUTORESUME.getValue() : storeSkuStockVO.getAutoResumeInfiniteStock());
            //无限库存设置为0, 自定义库存为2
            storeSkuSaleStockDTO.setEnforceStrategy(isInfiniteStock ? 0 : 2);
            storeSkuSaleStockDTOList.add(storeSkuSaleStockDTO);
        }
        return storeSkuSaleStockDTOList;
    }


    private StockAdjustOrderCreateReq toStockAdjustOrderCreateReq(User user, CreateStockAdjustRequestVo param, List<AdjustSkuRequestInfo> skuList) {
        StockAdjustOrderCreateReq req = new StockAdjustOrderCreateReq();
        req.setTenantId(user.getTenantId());
        req.setEntityId(param.getEntityId());
        req.setEntityType(param.getEntityType());
        req.setOperatorId(String.valueOf(user.getAccountId()));
        req.setOperatorName(user.getOperatorName());
        req.setOrderComment(param.getComment());
        req.setAdjustSkuItemList(skuList.stream()
                .filter(sku -> Objects.equals(sku.getCustomizeStockFlag(), CustomizeStockFlag.CUSTOMIZE.getValue()))
                .map(sku -> {
                    StockAdjustOrderCreateReq.AdjustSkuItem adjustSkuItem = new StockAdjustOrderCreateReq.AdjustSkuItem();
                    adjustSkuItem.setSkuId(sku.getSkuId());
                    adjustSkuItem.setSkuName(sku.getSkuName());
                    adjustSkuItem.setUnit(sku.getUnit());
                    adjustSkuItem.setSkuComment(sku.getComment());
                    adjustSkuItem.setOldStock(sku.getOldQuantity() == null ? "0" : sku.getOldQuantity());
                    adjustSkuItem.setNewStock(sku.getNewQuantity() == null ? "0" : sku.getNewQuantity());
                    adjustSkuItem.setEnableExpiredCheck(sku.getEnableExpiredCheck());

                    if (CollectionUtils.isNotEmpty(sku.getBatchContainerInfoList())) {
                        adjustSkuItem.setContainerBatchItemList(sku.getBatchContainerInfoList().stream().map(it -> {
                            StockAdjustOrderCreateReq.AdjustSkuContainerBatchItem batchItem = new StockAdjustOrderCreateReq.AdjustSkuContainerBatchItem();
                            batchItem.setLocationId(it.getOriginLocationId());
                            batchItem.setLocationType(it.getOriginLocationType());
                            batchItem.setLocationCode(it.getOriginLocationCode());
                            batchItem.setOldStock(it.getOldQuantity().toString());
                            batchItem.setNewStock(it.getNewQuantity().toString());
                            return batchItem;
                        }).collect(Collectors.toList()));
                    }

                    return adjustSkuItem;
                }).collect(Collectors.toList()));
        return req;
    }


    private CreateStockAdjustResponseVo toCreateStockAdjustResponseVo(StockAdjustOrderCreateResp result) {
        CreateStockAdjustResponseVo responseVo = new CreateStockAdjustResponseVo();
        responseVo.setCode(result.getCode());
        responseVo.setMsg(result.getMsg());
        responseVo.setReceiptNo(result.getOrderNo());
        if (CollectionUtils.isNotEmpty(result.getFailSkuItemList())) {
            responseVo.setFailSkuList(result.getFailSkuItemList().stream().map(it -> new CreateStockAdjustResponseVo.StockAdjustFailSku(it.getSkuId(), it.getFailText())).collect(Collectors.toList()));
        }
        return responseVo;
    }

}
