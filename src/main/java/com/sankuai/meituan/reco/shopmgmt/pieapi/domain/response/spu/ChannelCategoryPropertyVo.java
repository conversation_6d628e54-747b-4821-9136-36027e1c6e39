package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.linz.boot.util.Fun;
import com.meituan.linz.product.channel.EnhanceChannelType;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.ControlQuaTipVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.SaleAttrVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.dto.ChannelCatePropertyDTO;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.dto.QualificationPicturesRule;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@TypeDoc(
        name = "渠道后台类目属性VO对象",
        description = "渠道后台类目属性VO对象"
)
@Data
@ToString
@EqualsAndHashCode
public class ChannelCategoryPropertyVo {

    //默认支持单规格
    private final static int DEFAULT_SPEC_TYPE = 1;

    @FieldDoc(
            description = "渠道ID"
    )
    private Integer channelId;

    @FieldDoc(
            description = "渠道类目编码"
    )
    private String channelCategoryId;

    @FieldDoc(
            description = "支持规格类型(1单规格2单规格和多规格)"
    )
    private Integer supportSpecType;

    @FieldDoc(
            description = "美团渠道类目属性"
    )
    @ApiModelProperty("美团渠道类目属性")
    @Deprecated
    private List<ChannelDynamicInfoVO> mtChannelDynamicInfoVOList;

    @FieldDoc(
            description = "渠道类目属性"
    )
    @ApiModelProperty("渠道类目属性")
    private List<ChannelDynamicInfoVO> channelDynamicInfoVOList;

    @FieldDoc(
            description = "是否校验upc (0:不校验,1:校验，注意，只有三级分类该属性才有效)"
    )
    @ApiModelProperty(name = "是否校验upc (0:不校验,1:校验，注意，只有三级分类该属性才有效")
    private Integer checkUpcStatus;

    @FieldDoc(
            description = "京东售卖属性列表"
    )
    @ApiModelProperty(name = "京东售卖属性列表")
    private List<SaleAttrVo> jdSaleAttrList;

    @FieldDoc(
            description = "渠道销售属性，支持美团，饿了么，京东，抖音。逐步代替 jdSaleAttrList。"
    )
    @ApiModelProperty("渠道销售属性")
    private List<ChannelSaleAttrVo> channelSaleAttrList;

    @FieldDoc(
            description = "渠道销售属性是否允许自定义"
    )
    @ApiModelProperty("渠道销售属性是否允许自定义")
    private Boolean saleAttrSupportExtend;

    @FieldDoc(
            description = "销售属性名称长度"
    )
    @ApiModelProperty("销售属性名称长度")
    public Integer maxAttrNameLength;


    @FieldDoc(
            description = "特殊管控商品资质"
    )
    @ApiModelProperty("特殊管控商品资质")
    public List<ControlQuaTipVo> controlQuaTip;

    @FieldDoc(
            description = "sku渠道类目属性"
    )
    @ApiModelProperty("sku渠道类目属性")
    private List<ChannelDynamicInfoVO> channelSkuAttrList;

    @FieldDoc(
            description = "临期规则"
    )
    @ApiModelProperty("临期规则")
    private List<AdventRuleVo> adventRuleList;

    @FieldDoc(
            description = "类目特殊图片"
    )
    @ApiModelProperty("类目特殊图片")
    private List<SpecialPictureRuleVO> specialPictureRuleList;

    public static List<ChannelCategoryPropertyVo> buildList(List<ChannelCatePropertyDTO> dtoList){
        if(CollectionUtils.isEmpty(dtoList)){
            return Lists.newArrayList();
        }
        return dtoList.stream().map(ChannelCategoryPropertyVo::build).collect(Collectors.toList());
    }

    public static ChannelCategoryPropertyVo build(ChannelCatePropertyDTO dto) {
        ChannelCategoryPropertyVo vo = new ChannelCategoryPropertyVo();
        vo.setChannelId(dto.getChannelId());
        vo.setChannelCategoryId(dto.getChannelCategoryId());
        vo.setSupportSpecType(dto.getSupportSpecType());
        vo.setCheckUpcStatus(dto.getCheckUpcStatus());
        vo.setMtChannelDynamicInfoVOList(ChannelDynamicInfoVO.ofPlatformDTOList(dto.getChannelDynamicInfoDTOList()));
        vo.setChannelDynamicInfoVOList(ChannelDynamicInfoVO.ofPlatformDTOList(dto.getChannelDynamicInfoDTOList()));
        if (EnhanceChannelType.JDDJ.getChannelId().equals(dto.getChannelId())) {
            if (CollectionUtils.isNotEmpty(dto.getChannelSaleAttrList())) {
                vo.setJdSaleAttrList(Fun.map(dto.getChannelSaleAttrList(), SaleAttrVo::ofDTO));
            }
        }
        vo.setChannelSaleAttrList(ChannelSaleAttrVo.buildList(dto.getChannelSaleAttrList()));
        vo.setSaleAttrSupportExtend(Objects.equals(dto.getChannelId(), EnhanceChannelType.DY.getChannelId()));
        vo.setMaxAttrNameLength(MccConfigUtil.getSaleAttrLimitConfig().getAttrNameMaxLength(dto.getChannelId()));

        QualificationPicturesRule qualificationPicturesRule = dto.getQualificationPicturesRule();

        vo.setControlQuaTip(ControlQuaTipVo.ofQualificationPicturesRule(qualificationPicturesRule));
        vo.setChannelSkuAttrList(ChannelDynamicInfoVO.ofPlatformSkuDTOList(dto.getChannelSkuAttrList()));
        vo.setSpecialPictureRuleList(SpecialPictureRuleVO.ofSpecialPictureRuleVoList(dto.getSpecialPictureRules()));
        return vo;
    }

    public static ChannelCategoryPropertyVo buildDefault(Integer channelId, String channelCategoryId) {
        ChannelCategoryPropertyVo vo = new ChannelCategoryPropertyVo();
        vo.setChannelId(channelId);
        vo.setChannelCategoryId(channelCategoryId);
        vo.setSupportSpecType(DEFAULT_SPEC_TYPE);
        vo.setSaleAttrSupportExtend(Objects.equals(channelId, EnhanceChannelType.DY.getChannelId()));
        return vo;
    }

}
