package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description:
 * @author: caimengluan
 * @date: 2022/8/12
 * @time: 11:17
 * Copyright (C) 2019 Meituan
 * All rights reserved
 */
@TypeDoc(
        description = "根据子类型查询售后订单列表"
)
@ApiModel("根据子类型查询售后订单列表")
@Data
public class QueryWaitAuditRefundGoodsBySubTypeRequest {
    @FieldDoc(
            description = "第几页", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "第几页", required = true)
    @NotNull
    private Integer page;

    @FieldDoc(
            description = "每页行数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "每页行数", required = true)
    @NotNull
    private Integer size;

    @FieldDoc(
            description = "子类型", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "子类型", required = true)
    @NotNull
    private Integer subType;

    @FieldDoc(
            description = "门店类型 3-门店 5-中心仓 6-前置仓", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店类型")
    private Integer entityType;
}
