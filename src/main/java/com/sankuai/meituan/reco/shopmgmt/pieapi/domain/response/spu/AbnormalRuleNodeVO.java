package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@TypeDoc(
        description = "商品异常类型节点"
)
@Setter
@Getter
@ToString
public class AbnormalRuleNodeVO {
    @FieldDoc(
            description = "渠道"
    )
    private Integer channelId;
    @FieldDoc(
            description = "异常类型code"
    )
    private String abnormalCode;
    @FieldDoc(
            description = "异常类型描述"
    )
    private String abnormalDesc;
    @FieldDoc(
            description = "父异常类型code"
    )
    private Integer level;
    @FieldDoc(
            description = "父异常类型code"
    )
    private String parentCode;
    @FieldDoc(
            description = "异常类型子节点列表"
    )
    private List<AbnormalRuleNodeVO> children;
}
