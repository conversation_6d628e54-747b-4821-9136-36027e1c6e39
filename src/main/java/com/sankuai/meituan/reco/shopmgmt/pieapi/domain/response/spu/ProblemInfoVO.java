// Copyright (C) 2021 Meituan
// All rights reserved
package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.ProblemSpuCompareInfoDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2021/2/25 下午2:27
 **/
@TypeDoc(
        description = "不一致信息"
)
@Data
public class ProblemInfoVO {
    @FieldDoc(
            description = "商品中心的不一致信息key"
    )
    private String productProblemInfoKey;
    @FieldDoc(
            description = "商品中心不一致信息值"
    )
    private List<String> productProblemInfoValues;
    @FieldDoc(
            description = "渠道的不一致信息key"
    )
    private String channelProblemInfoKey;
    @FieldDoc(
            description = "渠道的不一致信息值"
    )
    private List<String> channelProblemInfoValues;

    public static ProblemInfoVO of(ProblemSpuCompareInfoDTO compareInfoDTO) {
        ProblemInfoVO infoVO = new ProblemInfoVO();
        infoVO.setProductProblemInfoKey(compareInfoDTO.getOcmsProductProblemKey());
        infoVO.setProductProblemInfoValues(compareInfoDTO.getOcmsProductProblemValues());
        infoVO.setChannelProblemInfoKey(compareInfoDTO.getChannelProductProblemKey());
        infoVO.setChannelProblemInfoValues(compareInfoDTO.getChannelProductProblemValues());

        return infoVO;
    }
}
