package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: wangyihao04
 * @Date: 2020-12-01 19:31
 * @Mail: <EMAIL>
 */
@ApiModel(
        "门店间类目对比查询请求"
)
@TypeDoc(
        description = "门店间类目对比查询请求"
)
@Data
public class ContrastCategoryBetweenStoresRequestVO {
    @FieldDoc(
            description = "门店id"
    )
    @ApiModelProperty("门店id")
    @NotNull
    public Long storeId;

    @FieldDoc(
            description = "指标类型"
    )
    @ApiModelProperty("指标类型")
    @NotNull
    public String type;

    @FieldDoc(
            description = "竞对门店列表"
    )
    @ApiModelProperty("竞对门店列表")
    @NotEmpty
    public List<ContrastStoreKeyVO> contrastStoreList;

    @FieldDoc(
            description = "一级类目id"
    )
    @ApiModelProperty("一级类目id")
    public String firstCategoryId;

    @FieldDoc(
            description = "二级类目id"
    )
    @ApiModelProperty("二级类目id")
    public String secondCategoryId;
}
