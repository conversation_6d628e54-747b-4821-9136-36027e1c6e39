package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: wangyihao04
 * @Date: 2020-08-17 22:20
 * @Mail: <EMAIL>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CompetitivenessSkuVO {
    private String skuId;
    private String skuName;
    private Double competitivenessRate;
    private String displayRate;
    private String aroundLowestStoreName;
    private Integer aroundStoreCount;
    private Boolean isFollow;
    private List<String> images;
    private String spec;
    private Integer weight;
    private Integer weightType;
    private String  saleUnit;
    private Integer monthSaleAmount;

    private Double offlinePrice;
    private Double offlinePriceOf500g;
    private Double onlinePrice;
    private Double onlinePriceOf500g;
    private Double onlineDiscountPrice;
    private Double onlineDiscountPriceOf500g;
    private Double aroundLowestPrice;
    private Double aroundLowestPriceOf500g;
    private Double aroundLowestDiscountPrice;
    private Double aroundLowestDiscountPriceOf500g;

    private AdjustPriceStrategyVO adjustPriceStrategy;
    private String spuId;
    @FieldDoc(
            description = "待审核价"
    )
    @ApiModelProperty(name = "待审核价")
    private Double reviewPrice;
    @FieldDoc(
            description = "待审核价/500g"
    )
    @ApiModelProperty(name = "待审核价/500g")
    private Double reviewPriceOf500g;
}
