package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TypeDoc(
        description = "查询波次拣货详情的时间容器的请求"
)
@ApiModel("查询波次拣货详情的时间容器的请求")
@Data
public class WarehouseWaveTaskDetailRequest {

    @FieldDoc(
            description = "波次号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "波次号")
    private String waveId;

    @FieldDoc(
            description = "波次任务号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "波次任务号")
    private Integer taskId;

    @FieldDoc(
            description = "查询模式，1代表拣货，2代表分拣", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "查询模式，1代表拣货，2代表分拣")
    private Integer wavePickModeCode;
}
