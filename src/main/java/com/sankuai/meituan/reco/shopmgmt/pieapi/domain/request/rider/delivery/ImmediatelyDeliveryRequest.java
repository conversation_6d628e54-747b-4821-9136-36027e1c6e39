package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.delivery;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@TypeDoc(
        description = "预订单立即配请求"
)
@ApiModel("预订单立即配请求")
@Data
public class ImmediatelyDeliveryRequest {
    @FieldDoc(
            description = "租户id"
    )
    @NotNull
    @ApiModelProperty(value = "租户id", required = true)
    private Long tenantId;

    @FieldDoc(
            description = "门店id"
    )
    @ApiModelProperty(value = "门店id", required = true)
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "订单id"
    )
    @ApiModelProperty(value = "订单id", required = true)
    @NotNull
    private Long orderId;

    @FieldDoc(
            description = "操作人账号id"
    )
    @ApiModelProperty(value = "操作人账号id", required = true)
    @NotNull
    private Long userAccountId;

}
