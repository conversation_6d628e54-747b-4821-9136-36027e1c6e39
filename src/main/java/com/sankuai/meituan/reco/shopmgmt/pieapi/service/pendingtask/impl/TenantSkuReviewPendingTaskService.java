package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.impl;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.AbstractSinglePendingTaskService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.OCMSServiceWrapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Title:
 * @Description:
 * <AUTHOR>
 * @Date 2020-03-27 14:07
 */
@Service
public class TenantSkuReviewPendingTaskService extends AbstractSinglePendingTaskService {
    @Resource
    private OCMSServiceWrapper ocmsServiceWrapper;

    @Override
    protected PendingTaskResult getPendingTaskCount(PendingTaskParam param) throws Exception {
        CommonResponse<Integer> resp = ocmsServiceWrapper.querySkuReviewingCountList(param.getStoreIds(), param.getUser());
        if (resp == null || resp.getData() == null) {
            return null;
        }
        return PendingTaskResult.createNumberMarker(resp.getData());
    }

    @Override
    protected AuthCodeEnum module() {
        return AuthCodeEnum.TENANT_SKU_REVIEW;
    }
}
