package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.auth;

import java.util.List;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import lombok.Data;

/**
 * 账号权限对象
 *
 * <AUTHOR>
 * @since 2023/06/28
 */
@TypeDoc(
        description = "账号权限对象",
        since = "2020/12/23",
        authors = {"zhangshengyue"}
)
@Data
public class SacAccountPermissionVO {

    /**
     * 账号id
     */
    @FieldDoc(
            description = "账号id"
    )
    private Long accountId;

    /**
     * 权限信息
     */
    @FieldDoc(
            description = "权限信息"
    )
    private List<SacPermissionVo> sacPermissionVos;
}
