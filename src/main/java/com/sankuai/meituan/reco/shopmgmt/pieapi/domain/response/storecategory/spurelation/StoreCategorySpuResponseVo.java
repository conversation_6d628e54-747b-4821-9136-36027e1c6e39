package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.storecategory.spurelation;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.PoiStoreCategorySpuListResponse;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class StoreCategorySpuResponseVo {
    @FieldDoc(
            description = "分页信息", requiredness = Requiredness.REQUIRED
    )
    private PageInfoVO pageInfo;
    @FieldDoc(
            description = "店内分类商品", requiredness = Requiredness.REQUIRED
    )
    private List<StoreCategorySpuVo> list;

    public static StoreCategorySpuResponseVo convert2ResponseVo(PoiStoreCategorySpuListResponse response) {
        StoreCategorySpuResponseVo responseVo = new StoreCategorySpuResponseVo();
        responseVo.setPageInfo(PageInfoVO.buildPageInfoVO(response.getPageInfo()));
        if (CollectionUtils.isNotEmpty(response.getPoiStoreCategorySpuList())) {
            responseVo.setList(ConverterUtils.convertList(response.getPoiStoreCategorySpuList(),
                    StoreCategorySpuVo::convert2Vo));
        } else {
            responseVo.setList(Collections.emptyList());
        }
        return responseVo;
    }
}
