package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.tenant;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * FileName: QueryTenantConfigRequest
 * Author:   wangjiawei31
 * Date:     2021/3/8 7:43 下午
 * Description:
 */
@Data
@TypeDoc(
        description = "商户配置查询参数"
)
public class QueryTenantConfigRequest {

    @FieldDoc(description = "商户id")
    private Long tenantId;
}
