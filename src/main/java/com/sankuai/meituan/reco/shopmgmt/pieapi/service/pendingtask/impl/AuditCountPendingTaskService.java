package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.impl;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.AbstractSinglePendingTaskService;
import com.sankuai.meituan.reco.store.wms.thrift.count.audit.AuditCountService;
import com.sankuai.meituan.reco.store.wms.thrift.count.audit.req.GetPendingOrderQtyReq;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 稽核盘点的角标数量
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AuditCountPendingTaskService extends AbstractSinglePendingTaskService {

    @Resource
    private AuditCountService auditCountService;

    @Override
    protected PendingTaskResult getPendingTaskCount(PendingTaskParam param) throws Exception {
        try {

            GetPendingOrderQtyReq getPendingOrderQtyReq = new GetPendingOrderQtyReq(param.getTenantId(),
                    param.getEntityId(), param.getUser().getAccountId());
            TResult<Long> result = auditCountService.getPendingOrderQty(getPendingOrderQtyReq);
            if (!result.isSuccess()) {
                log.error("查询稽核盘点待办数量失败，将兜底为0. result:{}", result);
                return PendingTaskResult.createNumberMarker(0);
            }
            return PendingTaskResult.createNumberMarker(result.getData().intValue());

        } catch (Exception e) {
            log.error("查询稽核盘点待办数量异常，将兜底为0", e);
            return PendingTaskResult.createNumberMarker(0);
        }
    }

    @Override
    protected AuthCodeEnum module() {
        return AuthCodeEnum.DH_AUDIT_COUNT;
    }
}