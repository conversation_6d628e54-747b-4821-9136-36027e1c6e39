package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.alibaba.druid.filter.AutoLoad;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.StoreCoverageDTO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date 2024/3/13
 * @Description
 */
@TypeDoc(
        name = "加盟门店覆盖情况",
        description = "加盟门店覆盖情况"
)
@Getter
@Setter
@AutoLoad
@NoArgsConstructor
@ToString
public class FranchiseeStoreCoverageVO {

    @FieldDoc(
            description = "覆盖数量"
    )
    private Integer coverageFranchiseeStoreCount;

    @FieldDoc(
            description = "门店数量"
    )
    private Integer storeTotal;
}
