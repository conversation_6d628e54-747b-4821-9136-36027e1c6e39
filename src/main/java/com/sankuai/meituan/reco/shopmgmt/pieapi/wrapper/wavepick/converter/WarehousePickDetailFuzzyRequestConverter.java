package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.wavepick.converter;

import com.sankuai.meituan.reco.pickselect.thrift.wave.request.WarehouseFuzzyPickDetailRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick.WarehousePickDetailFuzzyRequest;
import org.mapstruct.Mapper;

/**
 * @Description:
 * @Author: zhangjian155
 * @Date: 2022/8/25 18:27
 */
@Mapper(componentModel = "spring")
public abstract class WarehousePickDetailFuzzyRequestConverter {
    public abstract WarehouseFuzzyPickDetailRequest convert2ThriftRequest(WarehousePickDetailFuzzyRequest request,
                                                                          Long tenantId, Long accountId, Long storeId);
}
