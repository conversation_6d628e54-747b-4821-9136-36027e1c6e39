package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.productintelligent;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productintelligent.thrift.model.SimilarGoodsRequestDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@TypeDoc(
        description = "相似商品查询请求参数",
        authors = {"lixingzhong"}
)
@Data
@ApiModel("相似商品查询请求参数")
public class SimilarGoodsRequestVO {

    @FieldDoc(
            description = "租户id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户id", required = true)
    @NotNull
    private Long tenantId;

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店id", required = true)
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "渠道id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道id", required = true)
    @NotNull
    private Integer channelId;

    @FieldDoc(
            description = "spu id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "spu id", required = true)
    @NotNull
    private String spuId;

    public static SimilarGoodsRequestDTO toRpcRequest(SimilarGoodsRequestVO similarGoodsRequestVO) {
        SimilarGoodsRequestDTO rpcRequest = new SimilarGoodsRequestDTO();
        rpcRequest.setTenantId(similarGoodsRequestVO.getTenantId());
        rpcRequest.setStoreId(similarGoodsRequestVO.getStoreId());
        rpcRequest.setChannelId(similarGoodsRequestVO.getChannelId());
        rpcRequest.setSpuId(similarGoodsRequestVO.getSpuId());
        rpcRequest.setMarket(true);
        return rpcRequest;
    }
}
