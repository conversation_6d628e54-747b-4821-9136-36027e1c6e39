package com.sankuai.meituan.reco.shopmgmt.pieapi.service.appmodel.workbench;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.Environment;
import com.dianping.lion.client.ConfigRepository;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Maps;
import com.meituan.service.mobile.mtthrift.generic.GenericService;
import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import com.meituan.service.mobile.mtthrift.util.json.JacksonUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.config.PendingTaskConfig;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.PoiAccountInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.shangou.commons.thrift.publisher.request.UserContext;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.commons.utils.json.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/3/28 14:30
 */
@Component
@Slf4j
public class PendingTaskHelper {

    @Value("${app.name}")
    private String appKey;

    private static Map<String, ThriftClientProxy> thriftClientMap = new HashMap<>();

    @PostConstruct
    public void init() {
        updateThriftClientMap();
        ConfigRepository configRepository = Lion.getConfigRepository(Environment.getAppName());
        configRepository.addConfigListener(configEvent -> {
            log.info("config changed event: {}", configEvent);
            if (Objects.equals(PendingTaskConfig.CONFIG_KEY, configEvent.getKey())) {
                updateThriftClientMap();
            }
        });
    }

    public void updateThriftClientMap() {
        List<PendingTaskConfig> configList = PendingTaskConfig.getConfigList();
        log.info("configList:{}", JsonUtils.toJson(configList));
        for (PendingTaskConfig config : configList) {
            String key = buildMapKey(config);
            if (thriftClientMap.containsKey(key)) {
                continue;
            }
            try {
                ThriftClientProxy clientProxy = new ThriftClientProxy();
                //声明
                clientProxy.setAppKey(appKey);
                clientProxy.setRemoteAppkey(config.getRemoteAppkey());
                clientProxy.setGenericServiceName(config.getGenericServiceName());
                clientProxy.setFilterByServiceName(true);
                clientProxy.setGeneric("json-simple");
                clientProxy.setTimeout(5000);
                clientProxy.setNettyIO(true);
                clientProxy.afterPropertiesSet();
                thriftClientMap.put(key, clientProxy);
            } catch (Exception e) {
                log.error("Load PendingTaskConfig error, config:{}", config, e);
            }
        }
        log.info("updatePendingThriftClientMap configMap size:{}, thriftClientMap size:{}", configList.size(), thriftClientMap.size());
    }

    private String buildMapKey(PendingTaskConfig config) {
        return config.getRemoteAppkey() + "-" + config.getGenericServiceName();
    }

    public List<String> getAuthCodesFromLion() {
        List<String> fromLionAuthCodes = PendingTaskConfig.getConfigList().stream()
                .map(PendingTaskConfig::getAuthCode)
                .collect(Collectors.toList());
        return fromLionAuthCodes;
    }

    public int fromLionPendingTasks(Set<String> authCodes) {
        List<PendingTaskConfig> fromLionTasks = PendingTaskConfig.getConfigList().stream()
                .filter(config -> authCodes.contains(config.getAuthCode()))
                .collect(Collectors.toList());
        return fromLionTasks.size();
    }

    public List<PendingTaskConfig> filterSubMenuCodes(Set<String> authCodes, List<Long> storeIds) {
        List<PendingTaskConfig> configList = PendingTaskConfig.getConfigList()
                .stream()
                .filter(config -> authCodes.contains(config.getAuthCode()))
                .collect(Collectors.toList());
        // 门店数 > 1，过滤掉不支持多门店的taskType
        if (storeIds.size() > 1) {
            configList = configList.stream().filter(PendingTaskConfig::isSupportMultiPoi).collect(Collectors.toList());
        }
        return configList;
    }

    public Map<String, PendingTaskResult> queryPendingTaskFromLion(PendingTaskParam param, PendingTaskConfig config) {
        Map<String, PendingTaskResult> map = Maps.newHashMap();
        map.put(config.getAuthCode(), queryTaskDetail(param, config));
        return map;
    }

    private PendingTaskResult queryTaskDetail(PendingTaskParam param, PendingTaskConfig config) {
        PoiAccountInfo poiAccountInfo = buildUserContext(param);
        ThriftClientProxy clientProxy = thriftClientMap.get(buildMapKey(config));
        if (clientProxy == null) {
            log.error("config:{}, clientProxy为空", config);
            return PendingTaskResult.createNumberMarker(0);
        }

        try {
            //注意GenericService是com.meituan.service.mobile.mtthrift.generic.GenericService
            GenericService genericService = (GenericService) clientProxy.getObject();

            //使用

            List<String> paramTypes = new ArrayList<>();
            paramTypes.add("com.sankuai.meituan.reco.shopmgmt.pieapi.domain.PoiAccountInfo");

            List<String> paramValues = new ArrayList<>();
            String paramValuesStr = JSON.toJSONString(poiAccountInfo);
            paramValues.add(paramValuesStr);

            //调用服务端方法
            String result = genericService.$invoke(config.getMethodName(), paramTypes, paramValues);
            log.info("genericService invoke config:{}, poiAccountInfo:{}, result:{}", config, poiAccountInfo, result);
            if (StringUtils.isBlank(result)) {
                return PendingTaskResult.createNumberMarker(0);
            }

            //反序列化
            TResult<Integer> resp = JSON.parseObject(result, TResult.class);
            if (resp == null || !resp.isSuccess() || Objects.isNull(resp.getData())) {
                log.error("genericService invoke exception, config:{}", config);
                return PendingTaskResult.createNumberMarker(0);
            }
            return PendingTaskResult.createNumberMarker(resp.getData());
        } catch (Exception e) {
            log.error("genericService invoke error, config:{}", config, e);
            return PendingTaskResult.createNumberMarker(0);
        }
    }

    private PoiAccountInfo buildUserContext(PendingTaskParam param) {
        PoiAccountInfo poiAccountInfo = new PoiAccountInfo();
        poiAccountInfo.setTenantId(param.getTenantId());
        poiAccountInfo.setAccountId(param.getUser().getAccountId());
        poiAccountInfo.setPoiIds(param.getStoreIds());
        poiAccountInfo.setEmpId(param.getUser().getEmployeeId());
        return poiAccountInfo;
    }
}
