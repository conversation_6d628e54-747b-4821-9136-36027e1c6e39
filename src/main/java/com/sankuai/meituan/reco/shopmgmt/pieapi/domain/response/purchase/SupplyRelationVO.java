package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.purchase;

import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/29
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@ToString
@Builder
@TypeDoc(description = "供货关系")
public class SupplyRelationVO {
    @FieldDoc(
            description = "id",
            requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "id")
    private String relationId;
    @FieldDoc(
            description = "商品sku id",
            requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "商品sku id")
    private String skuId;

    @ApiModelProperty(value = "订货方id")
    @FieldDoc(description = "订货方id")
    private Long orderId;

    @ApiModelProperty(value = "供货方信息")
    @FieldDoc(description = "供货方信息")
    private SupplierVO supplier;

    @ApiModelProperty(value = "兼容供货方名称")
    @FieldDoc(description = "兼容供货方名称")
    private String supplierName;

    @FieldDoc(description = "结算方式")
    @ApiModelProperty(value = "结算方式")
    private Integer settleMethod;

    @FieldDoc(description = "付款方式")
    @ApiModelProperty(value = "付款方式")
    private Integer payMethod;

    @ApiModelProperty(value = "采购单位")
    @FieldDoc(description = "采购单位")
    private String supplyUnit;

    @ApiModelProperty(value = "最小起订量")
    @FieldDoc(description = "最小起订量")
    private Integer minOrderQuantity;

    @ApiModelProperty(value = "采购单价")
    @FieldDoc(description = "采购单价")
    private String supplyUnitPrice;

    @ApiModelProperty(value = "是否默认主供 1 默认主供，0 否")
    @FieldDoc(description = "是否默认主供 1 默认主供，0 否")
    private Integer masterFlag;

    @ApiModelProperty(value = "单位转换比例")
    @FieldDoc(description = "单位转换比例")
    private String supplyUnitRatio;

    @FieldDoc(description = "是否开启供应商商品资料维护功能", requiredness = Requiredness.OPTIONAL, defaultValue = "false")
    private boolean enableSupplierGoodsInfo;

    @ApiModelProperty(value = "供应商货品")
    @FieldDoc(description = "供应商货品")
    private SupplierGoodsVO supplierGoods;

    @ApiModelProperty(value = "渠道商品转换比例")
    @FieldDoc(description = "渠道商品转换比例")
    private String scmChannelGoodsRatio;

    @FieldDoc(description = "上次同步门店列表")
    private LastSyncStores lastSyncStores;

    @FieldDoc(description = "基本单位")
    private String baseUnit;

    @FieldDoc(description = "箱规编码")
    private String supplyUnitCode;

    @ApiModelProperty(value = "管控类型 1 普通非管控，2 强管控供货关系")
    @FieldDoc(description = "管控类型 1 普通非管控，2 强管控供货关系")
    private Integer controlType;

    @NoArgsConstructor
    @AllArgsConstructor
    @Getter
    @Setter
    @ToString
    @Builder
    @TypeDoc(description = "上次同步门店列表对象")
    public static class LastSyncStores {
        @FieldDoc(description = "有权限的门店")
        private List<Long> authPoiList;

        @FieldDoc(description = "没有权限的门店")
        private List<Long> lackAuthPoiList;


        public static LastSyncStores getDefault() {
            return new LastSyncStores(
                    Lists.newArrayList(),
                    Lists.newArrayList()
            );
        }
    }
}
