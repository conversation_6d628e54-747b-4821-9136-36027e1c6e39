package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.price;

import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.price.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.price.client.dto.contrast.ContrastStoreKeyDTO;
import com.sankuai.meituan.shangou.empower.price.client.dto.contrast.ContrastStoreWithIndexDTO;
import com.sankuai.meituan.shangou.empower.price.client.request.contrast.*;
import com.sankuai.meituan.shangou.empower.price.client.response.contrast.*;
import com.sankuai.meituan.shangou.empower.price.client.service.ContrastStoreThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Author: wangyihao04
 * @Date: 2020-12-01 19:24
 * @Mail: <EMAIL>
 */
@Slf4j
@Service
public class ContrastStoreServiceWrapper {
    @Resource
    private ContrastStoreThriftService contrastStoreThriftService;

    public CommonResponse<ContrastStoreQueryVO> queryContrastStore(ContrastStoreQueryRequestVO requestVO) {
        try {

            ContrastStoreQueryRequest queryRequest = ContrastStoreQueryRequest.builder()
                    .tenantId(getTenantId())
                    .storeId(requestVO.getStoreId())
                    .accountId(getAccountId())
                    .contrastStoreList(Optional.ofNullable(requestVO.getContrastStoreList()).orElse(Collections.emptyList()).stream().map(this::fromVO2DTO).collect(Collectors.toList()))
                    .build();

            ContrastStoreQueryResponse response = contrastStoreThriftService.query(queryRequest);
            if (Objects.isNull(response) || Objects.isNull(response.getCode())){
                throw new IllegalStateException("调用对比门店查询接口返回状态未知");
            }
            if (response.getCode() != ResultCode.SUCCESS.getCode()){
                throw new IllegalStateException(String.format("调用对比门店查询接口内部异常，msg %s", response.getMessage()));
            }

            return CommonResponse.success(new ContrastStoreQueryVO(response.getStoreIndexes().stream().map(this::fromDTO2VO).collect(Collectors.toList())));

        }catch (IllegalStateException e){
            log.error("调用对比门店查询接口内部异常", e);
            return CommonResponse.fail2(ResultCode.FAIL);
        } catch (TException e) {
            log.error("调用对比门店查询接口网络异常", e);
            return CommonResponse.fail2(ResultCode.FAIL);
        }
    }

    public CommonResponse<ContrastStoreSearchVO> searchContrastStore(ContrastStoreSearchRequestVO requestVO) {
        try {
            ContrastStoreSearchRequest searchRequest = ContrastStoreSearchRequest.builder()
                    .tenantId(getTenantId())
                    .storeId(requestVO.getStoreId())
                    .accountId(getAccountId())
                    .keyword(Optional.ofNullable(requestVO.getKeyword()).orElse(""))
                    .wmPoiId(requestVO.getWmPoiId())
                    .build();

            ContrastStoreSearchResponse response = contrastStoreThriftService.search(searchRequest);
            if (Objects.isNull(response) || Objects.isNull(response.getCode())){
                throw new IllegalStateException("调用对比门店搜索接口返回状态未知");
            }
            if (response.getCode() != ResultCode.SUCCESS.getCode()){
                throw new IllegalStateException(String.format("调用对比门店搜索接口内部异常，msg %s", response.getMessage()));
            }

            return CommonResponse.success(new ContrastStoreSearchVO(response.getStoreIndexes().stream().map(this::fromDTO2VO).collect(Collectors.toList())));

        }catch (IllegalStateException e){
            log.error("调用对比门店搜索接口内部异常", e);
            return CommonResponse.fail2(ResultCode.FAIL);
        } catch (TException e) {
            log.error("调用对比门店搜索接口网络异常", e);
            return CommonResponse.fail2(ResultCode.FAIL);
        }
    }

    public CommonResponse<Boolean> updateContrastStore(ContrastStoreUpdateRequestVO requestVO) {
        try {
            ContrastStoreUpdateRequest updateRequest = ContrastStoreUpdateRequest.builder()
                    .tenantId(getTenantId())
                    .storeId(requestVO.getStoreId())
                    .accountId(getAccountId())
                    .actionType(requestVO.getActionType())
                    .contrastStoreList(requestVO.getContrastStoreList().stream().map(this::fromVO2DTO).collect(Collectors.toList()))
                    .build();

            ContrastStoreUpdateResponse response = contrastStoreThriftService.update(updateRequest);
            if (Objects.isNull(response) || Objects.isNull(response.getCode())){
                throw new IllegalStateException("调用对比门店更新接口返回状态未知");
            }
            if (response.getCode() != ResultCode.SUCCESS.getCode()){
                throw new IllegalStateException(String.format("调用对比门店更新接口内部异常，msg %s", response.getMessage()));
            }

            return CommonResponse.success(true);

        }catch (IllegalStateException e){
            log.error("调用对比门店更新接口内部异常", e);
            return CommonResponse.fail2(ResultCode.FAIL);
        } catch (TException e) {
            log.error("调用对比门店更新接口网络异常", e);
            return CommonResponse.fail2(ResultCode.FAIL);
        }
    }

    public CommonResponse<ContrastCategoryBetweenStoresVO> queryContrastCategoryBetweenStores(ContrastCategoryBetweenStoresRequestVO requestVO){
        try {

            ContrastCategoryBetweenStoresRequest request = ContrastCategoryBetweenStoresRequest.builder()
                    .tenantId(getTenantId())
                    .storeId(requestVO.getStoreId())
                    .type(requestVO.getType())
                    .contrastStoreList(Optional.ofNullable(requestVO.getContrastStoreList())
                            .map(List::stream).orElse(Stream.empty())
                            .map(contrastStore -> ContrastStoreKeyDTO.builder()
                                    .contrastStoreType(contrastStore.getContrastStoreType())
                                    .contrastStoreId(contrastStore.getContrastStoreId())
                                    .build())
                            .collect(Collectors.toList()))
                    .firstCategoryId(requestVO.getFirstCategoryId())
                    .secondCategoryId(requestVO.getSecondCategoryId())
                    .build();
            ContrastCategoryBetweenStoresResponse response = contrastStoreThriftService.contrastCategoryBetweenStores(request);
            if (Objects.isNull(response) || Objects.isNull(response.getStatus())){
                throw new IllegalStateException("调用门店类目对比接口返回状态未知");
            }
            if (response.getStatus().getCode() != ResultCode.SUCCESS.getCode()){
                throw new IllegalStateException(String.format("调用门店类目对比接口服务内部异常，msg %s", response.getStatus().getMsg()));
            }
            return CommonResponse.success(ContrastCategoryBetweenStoresVO.valueOf(response));
        }catch (IllegalStateException e){
            log.error("调用门店类目对比接口服务内部异常",e);
            return CommonResponse.fail2(ResultCode.FAIL);
        } catch (TException e){
            log.error("调用门店间类目对比接口网络异常", e);
            return CommonResponse.fail2(ResultCode.FAIL);
        }
    }

    public CommonResponse<ContrastSpuBetweenStoresVO> queryContrastSpuBetweenStores(ContrastSpuBetweenStoresRequestVO requestVO){
        try {

            ContrastSpuBetweenStoresRequest request = ContrastSpuBetweenStoresRequest.builder()
                    .tenantId(getTenantId())
                    .storeId(requestVO.getStoreId())
                    .contrastStore(ContrastStoreKeyDTO.builder()
                            .contrastStoreId(requestVO.getContrastStoreId())
                            .contrastStoreType(requestVO.getContrastStoreType())
                            .build())
                    .firstCategoryId(requestVO.getFirstCategoryId())
                    .secondCategoryId(requestVO.getSecondCategoryId())
                    .thirdCategoryId(requestVO.getThirdCategoryId())
                    .lastThirdCategoryId(requestVO.getLastEntityId())
                    .sortType(requestVO.getSortType())
                    .build();

            ContrastSpuBetweenStoresResponse response = contrastStoreThriftService.contrastSpuBetweenStores(request);
            if (Objects.isNull(response) || Objects.isNull(response.getStatus())){
                throw new IllegalStateException("调用门店商品对比接口返回状态未知");
            }
            if (response.getStatus().getCode() != ResultCode.SUCCESS.getCode()){
                throw new IllegalStateException(String.format("调用门店商品对比接口服务内部异常，msg %s", response.getStatus().getMsg()));
            }
            return CommonResponse.success(ContrastSpuBetweenStoresVO.valueOf(response));
        }catch (IllegalStateException e){
            log.error("调用门店类目商品接口服务内部异常",e);
            return CommonResponse.fail2(ResultCode.FAIL);
        } catch (TException e){
            log.error("调用门店间商品对比接口网络异常", e);
            return CommonResponse.fail2(ResultCode.FAIL);
        }
    }

    public CommonResponse<ContrastChannelCategoryResponseVO> queryContrastCategory(ContrastStoreCategoryRequestVO requestVO ){
        try {
            ContrastStoreCategoryRequest request = ContrastStoreCategoryRequest.builder()
                    .contrastStoreList(requestVO.getContrastStoreList().stream().map(this::fromVO2DTO).collect(Collectors.toList()))
                    .pId(requestVO.getParentCategoryId())
                    .depth(Integer.valueOf(requestVO.getParentCategoryLevel()) + 1)
                    .build();

            // 把当前门店也加到门店列表中
            request.getContrastStoreList().add(ContrastStoreKeyDTO.builder().contrastStoreType(1).contrastStoreId(requestVO.getStoreId()).build());
            ContrastStoreCategoryResponse response = contrastStoreThriftService.queryContrastStoreCategory(request);

            if (Objects.isNull(response) || Objects.isNull(response.getCode())){
                throw new IllegalStateException("查对比门店类目接口返回状态未知");
            }
            if (response.getCode() != ResultCode.SUCCESS.getCode()){
                throw new IllegalStateException(String.format("查对比门店类目接口内部异常，msg %s", response.getMessage()));
            }

            return CommonResponse.success(new ContrastChannelCategoryResponseVO(response.getCategoryList().stream().map(ContrastChannelCategoryVO::valueOf).collect(Collectors.toList())));
        } catch (TException e) {
            log.error("查对比门店类目接口网络异常", e);
            return CommonResponse.fail2(ResultCode.FAIL);
        }
    }

    private Long getTenantId() {
        return Optional.ofNullable(ApiMethodParamThreadLocal.getIdentityInfo())
                .map(IdentityInfo::getUser).map(User::getTenantId)
                .orElseThrow(() -> new IllegalStateException("获取租户id异常"));
    }

    private Long getAccountId() {
        return Optional.ofNullable(ApiMethodParamThreadLocal.getIdentityInfo())
                .map(IdentityInfo::getUser).map(User::getAccountId)
                .orElseThrow(() -> new IllegalStateException("获取账号异常"));
    }

    private ContrastStoreKeyDTO fromVO2DTO(ContrastStoreKeyVO vo) {
        return ContrastStoreKeyDTO.builder()
                .contrastStoreId(vo.getContrastStoreId())
                .contrastStoreType(vo.getContrastStoreType())
                .build();
    }

    public ContrastStoreWithIndexVO fromDTO2VO(ContrastStoreWithIndexDTO dto) {
        if(Objects.isNull(dto)){
            return null;
        }

        return new ContrastStoreWithIndexVO(
                dto.getContrastStoreName(),
                dto.getContrastStoreType(),
                dto.getContrastStoreId(),
                dto.getMonthSale(),
                dto.getSpuCount(),
                dto.getPriceIndex(),
                dto.getCalculateDone(),
                dto.getLowPriceSpuCount(),
                dto.getAllMatchedSpuCount(),
                dto.getLowPriceRate(),
                dto.getComparePriceRate()
        );
    }
}
