package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2020-12-01 11:30
 * @Description:
 */
@TypeDoc(
        description = "租户spu预览接口返回"
)
@Data
@ApiModel("租户spu预览接口返回")
public class TenantSpuPreviewVO {
    @FieldDoc(
            description = "数量单位", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "数量单位")
    private List<String> quantityUnit;
}
