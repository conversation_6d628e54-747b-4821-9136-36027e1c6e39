package com.sankuai.meituan.reco.shopmgmt.pieapi.domain;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;


@TypeDoc(
        description = "门店合同到期提醒信息",
        version = "V1.0"
)
@Data
public class ContractExpirationReminderVo {


    /**
     * 是否需要提醒
     */
    @FieldDoc(description = "提醒")
    public Boolean remind;

    /**
     * 是否需要弹窗
     */
    @FieldDoc(description = "弹窗")
    public Boolean popUp;

    /**
     * 合同剩余天数
     */
    @FieldDoc(description = "合同剩余天数")
    public Integer remainDays;

    /**
     * 合同截止日期
     */
    @FieldDoc(description = "合同截止日期")
    public String effectiveEndDate;

    /**
     * 管理员名称
     */
    @FieldDoc(description = "管理员名称")
    public String adminUserName;

    /**
     * 账号类型
     */
    @FieldDoc(description = "账号类型")
    public Integer accountType;

}
