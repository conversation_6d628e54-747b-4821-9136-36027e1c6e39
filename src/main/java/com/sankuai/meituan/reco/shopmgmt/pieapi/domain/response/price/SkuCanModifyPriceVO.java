package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.empower.price.client.dto.StoreSkuPromotionDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


/**
 * <AUTHOR>
 * @since 2024/7/3
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SkuCanModifyPriceVO {

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID", required = true)
    private Long storeId;

    @FieldDoc(
            description = "渠道id  -1-线下 100-美团 200-饿了么 300-京东到家", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道id", required = true)
    private Integer channelId;

    @FieldDoc(
            description = "商品customSpuId编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品customSpuId编码", required = true)
    private String customSpuId;

    @FieldDoc(
            description = "商品customSkuId编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品customSkuId编码", required = true)
    private String customSkuId;

    @FieldDoc(
            description = "是否活动中"
    )
    @ApiModelProperty(value = "是否活动中", required = true)
    private Boolean atPromotion;

    @FieldDoc(
            description = "是否能修改价格"
    )
    @ApiModelProperty(value = "是否能修改价格", required = true)
    private Boolean canModifyPrice;

    public static SkuCanModifyPriceVO valueOf(StoreSkuPromotionDTO promotionDTO) {
        return SkuCanModifyPriceVO.builder()
                .channelId(promotionDTO.getCustomSkuKey().getChannelId())
                .storeId(promotionDTO.getCustomSkuKey().getStoreId())
                .customSpuId(promotionDTO.getCustomSkuKey().getCustomSpuId())
                .customSkuId(promotionDTO.getCustomSkuKey().getCustomSkuId())
                .atPromotion(promotionDTO.getAtPromotion())
                .canModifyPrice(promotionDTO.getCanModifyPrice())
                .build();
    }
}
