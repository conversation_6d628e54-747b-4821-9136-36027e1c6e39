package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.WarehousePickCollectionModuleVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@TypeDoc(
        description = "波次拣货待集单查询返回"
)
@Data
@ApiModel("波次拣货待集单查询返回")
public class WarehousePickCollectionQueryResponse {

    @FieldDoc(
            description = "是否还有数据", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否还有数据", required = true)
    private Boolean hasMore;

    @FieldDoc(
            description = "页码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "页码")
    @NotNull
    private Integer pageNum;

    @FieldDoc(
            description = "待集单数据列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "待集单数据列表", required = true)
    private List<WarehousePickCollectionModuleVO> dataList;

    @FieldDoc(
            description = "总条目", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "总条目", required = true)
    private Integer total;


}
