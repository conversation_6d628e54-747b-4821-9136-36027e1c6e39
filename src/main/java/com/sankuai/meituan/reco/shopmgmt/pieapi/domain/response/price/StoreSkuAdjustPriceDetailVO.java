package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.converters.price.PriceSyncStrategyConverter;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.ChannelStoreSkuKey;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.SkuPurchasePriceVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.bo.ChannelTypeEnumBo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelSkuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.StoreSkuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.StoreSpuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.store.StoreVO;
import com.sankuai.meituan.shangou.empower.price.client.dto.strategy.SkuHitSyncStrategyDTO;
import com.sankuai.meituan.shangou.empower.price.client.enums.strategy.SyncStrategyTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@TypeDoc(
        description = "SPU商品改价SKU明细",
        authors = "hejunliang"
)
@Data
@ApiModel("SPU商品改价SKU明细")
public class StoreSkuAdjustPriceDetailVO {

    @FieldDoc(
            description = "渠道ID"
    )
    @ApiModelProperty(name = "渠道ID")
    private Integer channelId;

    @FieldDoc(
            description = "sku编码"
    )
    @ApiModelProperty(name = "sku编码")
    private String skuId;

    @FieldDoc(
            description = "规格"
    )
    @ApiModelProperty(name = "规格")
    private String spec;

    @FieldDoc(
            description = "以克(g)为单位的重量"
    )
    @ApiModelProperty(name = "以克(g)为单位的重量")
    private Integer weight;

    @FieldDoc(
            description = "带单位的重量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "带单位的重量", required = true)
    private String weightForUnit;

    @FieldDoc(
            description = "重量单位", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "重量单位", required = true)
    private String weightUnit;

    @FieldDoc(
            description = "商品类型"
    )
    @ApiModelProperty(name = "商品类型")
    private Integer weightType;
    @FieldDoc(
            description = "单位"
    )
    @ApiModelProperty(name = "单位")
    private String unit;

    @FieldDoc(
            description = "进货价"
    )
    @ApiModelProperty(name = "进货价")
    private String offlinePrice;

    @FieldDoc(
            description = "定价策略：1-手动定价，2-通用策略，4-单品提价策略"
    )
    @ApiModelProperty(name = "定价策略")
    private Integer syncStrategyType;

    @FieldDoc(
            description = "提价金额"
    )
    @ApiModelProperty(name = "提价金额")
    private String raisePrice;

    @FieldDoc(
            description = "提价比例"
    )
    @ApiModelProperty(name = "提价比例")
    private String raisePercent;

    @FieldDoc(
            description = "固定金额"
    )
    @ApiModelProperty(name = "固定金额")
    private String fixedPrice;

    @FieldDoc(
            description = "零售价"
    )
    @ApiModelProperty(name = "零售价")
    private String retailPrice;

    @FieldDoc(
            description = "策略描述"
    )
    @ApiModelProperty(name = "策略描述")
    private String description;

    @FieldDoc(
            description = "是否在促销中", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否在促销中")
    private Boolean atPromotion;

    @FieldDoc(
            description = "是否能改价", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否能改价")
    private Boolean canModifyPrice;

    @FieldDoc(
            description = "是否为组合商品", requiredness = Requiredness.OPTIONAL
    )
    private Boolean isComposeSku = false;

    @FieldDoc(
            description = "最近采购价，单位元", requiredness = Requiredness.OPTIONAL
    )
    private String latestPurchasePrice;

    @FieldDoc(
            description = "默认主供采购价，单位元", requiredness = Requiredness.OPTIONAL
    )
    private String masterPurchasePrice;

    @FieldDoc(
            description = "加权库存成本价（基本单位），单位元", requiredness = Requiredness.OPTIONAL
    )
    private String weightedInventoryCostPrice;

    @FieldDoc(
            description = "加权库存成本价单位（基本单位）", requiredness = Requiredness.OPTIONAL
    )
    private String weightedInventoryCostPriceUnit;

    @FieldDoc(
            description = "最近下单价（基本单位），单位元", requiredness = Requiredness.OPTIONAL
    )
    private String lastDeliverPrice;

    @FieldDoc(
            description = "最近下单价单位（基本单位）", requiredness = Requiredness.OPTIONAL
    )
    private String lastDeliverPriceUnit;

    @FieldDoc(
            description = "链接最新价（渠道商品价）（基本单位），单位元", requiredness = Requiredness.OPTIONAL
    )
    private String baseChannelGoodsPrice;

    @FieldDoc(
            description = "默认供应商渠道类型，0或空-线下渠道，1-闪电仓，2-1688", requiredness = Requiredness.OPTIONAL
    )
    private Integer defaultSupplierType;

    @FieldDoc(
            description = "采购最近更新时间", requiredness = Requiredness.OPTIONAL
    )
    private String latestTime;

    @FieldDoc(
            description = "采购商名称", requiredness = Requiredness.OPTIONAL
    )
    private String supplierName;

    @FieldDoc(
            description = "是否上线", requiredness = Requiredness.OPTIONAL
    )
    private Boolean isOnline = false;

    public static StoreSkuAdjustPriceDetailVO buildStoreSkuAdjustPriceDetailVO(
            StoreSpuVO storeSpuVO, StoreSkuVO storeSkuVO, SkuHitSyncStrategyDTO strategyDTO) {

        StoreSkuAdjustPriceDetailVO detailVO = new StoreSkuAdjustPriceDetailVO();

        Map<ChannelStoreSkuKey, ChannelSkuVO> channelSkuMap = storeSpuVO.getChannelSkuMap();
        ChannelStoreSkuKey key = ChannelStoreSkuKey.of(strategyDTO.getStoreId(), strategyDTO.getChannelId(), strategyDTO.getSkuId());
        ChannelSkuVO channelSkuVO = channelSkuMap.get(key);

        detailVO.setChannelId(strategyDTO.getChannelId());
        detailVO.setSkuId(strategyDTO.getSkuId());
        detailVO.setSyncStrategyType(strategyDTO.getSyncStrategyType().getValue());

        if (Objects.nonNull(storeSkuVO)) {
            detailVO.setOfflinePrice(PriceSyncStrategyConverter.smaller100Times(storeSkuVO.getStorePrice()));
            detailVO.setSpec(storeSkuVO.getSpec());
            detailVO.setUnit(storeSkuVO.getSaleUnit());
            detailVO.setWeight(storeSkuVO.getWeight());
            detailVO.setWeightForUnit(storeSkuVO.getWeightForUnit());
            detailVO.setWeightUnit(storeSkuVO.getWeightUnit());
            detailVO.setWeightType(storeSpuVO.getWeightType());
            detailVO.setRetailPrice(PriceSyncStrategyConverter.calculateRetailPrice(strategyDTO, storeSkuVO.getStorePrice()));
            detailVO.setAtPromotion(channelSkuVO != null ? channelSkuVO.getAtPromotion() : false);
            detailVO.setCanModifyPrice(channelSkuVO != null ? channelSkuVO.getCanModifyPrice() : true);
        }

        String storeName = storeSpuVO.getStore() != null ? storeSpuVO.getStore().getStoreName() : "当前门店";
        if (strategyDTO.getSyncStrategyType() == SyncStrategyTypeEnum.FIX_PRICE) {
            detailVO.setFixedPrice(PriceSyncStrategyConverter.smaller100Times(strategyDTO.getFixedPrice()));
        } else if (strategyDTO.getSyncStrategyType() == SyncStrategyTypeEnum.SINGLE_SKU_FIX_STRATEGY_PRICE) {
            detailVO.setRaisePrice(PriceSyncStrategyConverter.smaller100Times(strategyDTO.getPriceStrategyDTO().getAdjustMoney()));
            detailVO.setRaisePercent(PriceSyncStrategyConverter.smaller100Times(strategyDTO.getPriceStrategyDTO().getAdjustPercent()));
        } else if (strategyDTO.getSyncStrategyType() == SyncStrategyTypeEnum.RAISE_STORE_PRICE) {
            detailVO.setRaisePrice(PriceSyncStrategyConverter.smaller100Times(strategyDTO.getPriceStrategyDTO().getAdjustMoney()));
            detailVO.setRaisePercent(PriceSyncStrategyConverter.smaller100Times(strategyDTO.getPriceStrategyDTO().getAdjustPercent()));
            detailVO.setDescription(PriceSyncStrategyConverter.genDescription(strategyDTO, storeName));
        }

        return detailVO;
    }

    public static StoreSkuAdjustPriceDetailVO buildStoreSkuAdjustPriceDetailVO(StoreSpuVO storeSpuVO,
                                                                               StoreSkuVO storeSkuVO,
                                                                               ChannelTypeEnumBo channelTypeEnum,
                                                                               SkuHitSyncStrategyDTO strategyDTO,
                                                                               SkuPurchasePriceVo priceDto,
                                                                               Integer priceCentMode) {

        StoreSkuAdjustPriceDetailVO detailVO = buildStoreSkuAdjustPriceDetailVO(storeSpuVO, storeSkuVO, channelTypeEnum, priceDto);

        detailVO.setSyncStrategyType(strategyDTO.getSyncStrategyType().getValue());
        detailVO.setRetailPrice(PriceSyncStrategyConverter.calculateRetailPriceWithCentMode(storeSpuVO.getTenantId(),
                strategyDTO, storeSkuVO.getStorePrice(), priceCentMode));

        if (strategyDTO.getSyncStrategyType() == SyncStrategyTypeEnum.FIX_PRICE) {
            detailVO.setFixedPrice(PriceSyncStrategyConverter.smaller100Times(strategyDTO.getFixedPrice()));
        } else if (strategyDTO.getSyncStrategyType() == SyncStrategyTypeEnum.SINGLE_SKU_FIX_STRATEGY_PRICE) {
            detailVO.setRaisePrice(PriceSyncStrategyConverter.smaller100Times(strategyDTO.getPriceStrategyDTO().getAdjustMoney()));
            detailVO.setRaisePercent(PriceSyncStrategyConverter.smaller100Times(strategyDTO.getPriceStrategyDTO().getAdjustPercent()));
        } else if (strategyDTO.getSyncStrategyType() == SyncStrategyTypeEnum.RAISE_STORE_PRICE) {
            String storeName = Optional.ofNullable(storeSpuVO.getStore()).map(StoreVO::getStoreName).orElse("当前门店");
            detailVO.setRaisePrice(PriceSyncStrategyConverter.smaller100Times(strategyDTO.getPriceStrategyDTO().getAdjustMoney()));
            detailVO.setRaisePercent(PriceSyncStrategyConverter.smaller100Times(strategyDTO.getPriceStrategyDTO().getAdjustPercent()));
            detailVO.setDescription(PriceSyncStrategyConverter.genDescription(strategyDTO, storeName));
        }

        return detailVO;
    }

    public static StoreSkuAdjustPriceDetailVO buildStoreSkuAdjustPriceDetailVO(StoreSpuVO storeSpuVO,
                                                                               StoreSkuVO storeSkuVO,
                                                                               ChannelTypeEnumBo channelTypeEnum,
                                                                               SkuPurchasePriceVo priceDto) {

        StoreSkuAdjustPriceDetailVO detailVO = new StoreSkuAdjustPriceDetailVO();
        detailVO.setSpec(storeSkuVO.getSpec());
        detailVO.setUnit(storeSkuVO.getSaleUnit());
        detailVO.setWeight(storeSkuVO.getWeight());
        detailVO.setWeightForUnit(storeSkuVO.getWeightForUnit());
        detailVO.setWeightUnit(storeSkuVO.getWeightUnit());
        detailVO.setOfflinePrice(PriceSyncStrategyConverter.smaller100Times(storeSkuVO.getStorePrice()));
        detailVO.setWeightType(storeSpuVO.getWeightType());
        detailVO.setSkuId(storeSkuVO.getSkuId());

        if (Objects.nonNull(channelTypeEnum)) {
            detailVO.setChannelId(channelTypeEnum.getChannelId());
            ChannelStoreSkuKey key = ChannelStoreSkuKey.of(storeSkuVO.getStoreId(), channelTypeEnum.getChannelId(), storeSkuVO.getSkuId());
            Map<ChannelStoreSkuKey, ChannelSkuVO> channelSkuMap = storeSpuVO.getChannelSkuMap();
            ChannelSkuVO channelSkuVO = channelSkuMap.get(key);
            detailVO.setAtPromotion(channelSkuVO != null ? channelSkuVO.getAtPromotion() : false);
            detailVO.setCanModifyPrice(channelSkuVO != null ? channelSkuVO.getCanModifyPrice() : true);
            detailVO.setIsOnline(Objects.nonNull(channelSkuVO));
        } else {
            detailVO.setIsOnline(Boolean.FALSE);
        }

        if (priceDto != null) {
            detailVO.setIsComposeSku(priceDto.getIsComposeSku());
            detailVO.setMasterPurchasePrice(priceDto.getMasterPurchasePrice());
            detailVO.setLastDeliverPrice(priceDto.getLastDeliverPrice());
            detailVO.setLastDeliverPriceUnit(priceDto.getLastDeliverPriceUnit());
            detailVO.setBaseChannelGoodsPrice(priceDto.getBaseChannelGoodsPrice());
            detailVO.setDefaultSupplierType(priceDto.getDefaultSupplierType());
            detailVO.setWeightedInventoryCostPrice(priceDto.getWeightedInventoryCostPrice());
            detailVO.setWeightedInventoryCostPriceUnit(priceDto.getWeightedInventoryCostPriceUnit());
            detailVO.setSupplierName(priceDto.getSupplierName());
            detailVO.setLatestPurchasePrice(priceDto.getLatestPurchasePrice());
            detailVO.setLatestTime(priceDto.getLatestTime());
        }

        return detailVO;
    }
}
