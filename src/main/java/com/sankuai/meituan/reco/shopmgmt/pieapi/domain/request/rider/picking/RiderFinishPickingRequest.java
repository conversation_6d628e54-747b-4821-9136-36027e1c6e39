package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.picking;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 骑手完成拣货请求.
 *
 * <AUTHOR>
 * @since 2021/11/12 16:11
 */
@TypeDoc(
        description = "骑手完成拣货请求"
)
@ApiModel("骑手完成拣货请求")
@Data
public class RiderFinishPickingRequest {

    @FieldDoc(
            description = "订单渠道", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单渠道", required = true)
    private Integer channelId;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道订单号", required = true)
    private String channelOrderId;

    @FieldDoc(
            description = "运单 ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "运单 ID", required = true)
    private Long deliveryOrderId;

    @FieldDoc(
            description = "拣货工单 ID",
            requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "拣货工单 ID", required = true)
    private Long pickingWoId;

    @FieldDoc(
            description = "拣货任务列表",
            requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "拣货任务列表", required = true)
    private List<RiderPickTask> pickTasks;

    @FieldDoc(
            description = "订单是否出现过 UPC 扫码错误，用于统计扫码错误订单率",
            requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单是否出现过 UPC 扫码错误，用于统计扫码错误订单率", required = true)
    private Boolean upcScanErrorOccurred = false;

    @FieldDoc(
            description = "拣货复核照片Url（废弃）",
            requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "拣货复核照片Url")
    @Deprecated
    private String pickingCheckPictureUrl;

    @FieldDoc(
            description = "拣货复核照片Url（支持多张照片）",
            requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "拣货复核照片Url")
    private List<String> pickingCheckPictureUrlList;

    @FieldDoc(
            description = "是否是三方配送", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否是三方配送", required = false)
    private Boolean isThirdDelivery;

    @FieldDoc(
            description = "mrn版本号",
            requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "mrn版本号")
    private String mrnVersion;

    public Optional<String> validate() {
        if (channelId == null || ChannelOrderConvertUtils.sourceMid2Biz(channelId) == null) {
            return Optional.of("订单渠道无效");
        }
        if (StringUtils.isBlank(channelOrderId)) {
            return Optional.of("渠道订单号无效");
        }
        //非三方配送时必传deliveryOrderId
        if (!Objects.equals(isThirdDelivery, true) && (deliveryOrderId == null || deliveryOrderId <= 0)) {
            return Optional.of("运单 ID 无效");
        }
        if (pickingWoId == null || pickingWoId <= 0) {
            return Optional.of("拣货工单 ID 无效");
        }
        if (CollectionUtils.isEmpty(pickTasks)) {
            return Optional.of("拣货任务列表不能为空");
        }
        if (upcScanErrorOccurred == null) {
            return Optional.of("参数错误");
        }
        for (RiderPickTask pickTask : pickTasks) {
            if (pickTask == null) {
                return Optional.of("存在无效拣货任务数据");
            }
            Optional<String> taskValidate = pickTask.validate();
            if (taskValidate.isPresent()) {
                return taskValidate;
            }
        }
        return Optional.empty();
    }
}
