package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.quality;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.product.dto.RuleInterestInfoDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/9/20
 */
@TypeDoc(
        name = "权益规则详情",
        description = "权益规则详情"
)
@Data
public class RuleInterestInfoVo {
    @FieldDoc(
            description = "规则权益code，此处的code，为牵牛花转换后的code，具体转换规则可以参考mcc配置"
    )
    @ApiModelProperty(name = "规则权益code，此处的code，为牵牛花转换后的code，具体转换规则可以参考mcc配置")
    private int ruleCode;

    @FieldDoc(
            description = "权益规则描述"
    )
    @ApiModelProperty(name = "权益规则描述")
    private String ruleDesc;

    public static RuleInterestInfoVo of(RuleInterestInfoDto infoDto) {
        RuleInterestInfoVo vo = new RuleInterestInfoVo();
        vo.setRuleCode(infoDto.getRuleCode());
        vo.setRuleDesc(infoDto.getRuleDesc());

        return vo;
    }
}
