package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelqualification.request.ChannelCategoryQualificationRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/1/24
 */
@TypeDoc(
        name = "渠道特殊属性请求",
        description = "渠道特殊属性请求"
)
@Data
@EqualsAndHashCode
@ToString
public class ChannelQualificationByCategoryRequest {

    @FieldDoc(
            description = "租户id",
            requiredness = Requiredness.REQUIRED
    )
    public Long tenantId;

    @FieldDoc(
            name = "渠道ID",
            description = "渠道ID",
            requiredness = Requiredness.REQUIRED
    )
    public Integer channelId;
    @FieldDoc(
            name = "类目ID",
            description = "类目ID",
            requiredness = Requiredness.REQUIRED
    )
    private String categoryId;

    private List<ChannelCategroyPropValueBO> propValueList;



    public ChannelCategoryQualificationRequest toChannelCategoryQualificationRequest() {
        ChannelCategoryQualificationRequest request = new ChannelCategoryQualificationRequest();
        request.setCategoryId(this.categoryId);
        request.setChannelId(this.channelId);
        request.setTenantId(this.tenantId);
        if(CollectionUtils.isNotEmpty(propValueList)){
            request.setCategoryPropList(this.propValueList.stream().map(ChannelCategroyPropValueBO::toCategoryPropDTO).collect(Collectors.toList()));
        }
        return request;
    }
}
