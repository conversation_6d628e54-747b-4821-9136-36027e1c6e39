package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.facebook.swift.codec.ThriftField;
import com.meituan.linz.boot.util.Fun;
import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ChannelSaleAttrValueInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.CombineChildSkuVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SaleAttrValueInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SkuImageInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.product.container.SkuContainer;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MoneyUtils;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.TenantSkuDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.sku.CombineChildSkuDto;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.TenantSkuBizDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Title: TenantSkuVO
 * @Description:
 * @Author: zhaolei12
 * @Date: 2020/5/24 9:29 上午
 */
@EqualsAndHashCode(callSuper = true)
@TypeDoc(
        description = "租户商品规格查询接口请求参数"
)
@Data
@ApiModel("租户商品规格查询接口请求参数")
public class TenantSkuVO extends SkuContainer {

    @FieldDoc(description = "租户ID")
    protected Long tenantId;

    @FieldDoc(description = "SPU_ID")
    protected String spuId;

    @FieldDoc(description = "SKU_ID")
    protected String skuId;

    @Deprecated
    @FieldDoc(description = "UPC, 后续请使用upcList字段, 支持一品多码")
    protected String upc;

    @FieldDoc(description = "UPC列表")
    protected List<String> upcList;

    @FieldDoc(description = "规格")
    protected String spec;

    @FieldDoc(description = "基本价格")
    protected Long basePrice;

    @FieldDoc(description = "重量")
    protected Integer weight;

    @FieldDoc(description = "带单位的重量")
    protected String weightForUnit;

    @FieldDoc(description = "重量单位")
    protected String weightUnit;

    @FieldDoc(description = "售卖单位")
    protected String saleUnit;

    @FieldDoc(description = "最小购买数量")
    protected String minOrderCount;

    @Deprecated
    @FieldDoc(description = "包装盒数量， 已废弃")
    protected String boxNum = "1";

    @Deprecated
    @FieldDoc(description = "包装盒价格， 已废弃")
    protected String boxPrice = "0";

    @FieldDoc(description = "覆盖门店数")
    protected Integer coveredStores;

    @FieldDoc(description = "是否允许自定义规格描述")
    protected Boolean canCustomizeSpec;

    @FieldDoc(description = "京东建议零售价")
    public Double suggestPrice;

    @FieldDoc(description = "京东类目属性值")
    public List<SaleAttrValueInfoVO> jdAttrValueList;

    @FieldDoc(
            description = "进项税率"
    )
    private Integer importRate;

    @FieldDoc(
            description = "销项税率"
    )
    private Integer exportRate;

    @FieldDoc(
            description = "税收类目编码"
    )
    private String taxCategoryCode;

    @FieldDoc(
            description = "是否开启效期检查，0-不检查 1-检查"
    )
    @ApiModelProperty(value = "是否开启效期检查，0-不检查 1-检查")
    private Integer enableExpiredCheck;

    @FieldDoc(
            description = "产地 1-国产 2-进口"
    )
    @ApiModelProperty(value = "产地 1-国产 2-进口")
    private Integer originPlace;

    @FieldDoc(
            description = "保质期天数"
    )
    @ApiModelProperty(value = "保质期天数")
    private Integer expirationDays;

    @FieldDoc(
            description = "是否是组合商品主商品 0-否 1-是"
    )
    @ApiModelProperty(value = "是否是组合商品主商品 0-否 1-是")
    private Integer composeSku;

    @FieldDoc(
            description = "外部编码"
    )
    @ApiModelProperty(value = "外部编码")
    private String externalCode;

    @FieldDoc(
            description = "加盟商总部编码"
    )
    @ApiModelProperty(value = "加盟商总部编码")
    private String franchiseeHeadquartersCode;

    @FieldDoc(
            description = "采购平台编码"
    )
    @ApiModelProperty(value = "采购平台编码")
    private String purchasePlatformCode;

    @FieldDoc(
            description = "加盟主采购平台编码信息"
    )
    @ApiModelProperty(value = "采购平台编码")
    private List<FranchisorPurchasePlatformInfoVO> franchisorPurchasePlatformInfoList;

    @FieldDoc(
            description = "中心仓配送的b端sku列表"
    )
    @ApiModelProperty(value = "中心仓配送的b端sku列表")
    private List<SupplierSkuVO> supplierSkuList;

    @FieldDoc(
            description = "供货商规格ID"
    )
    @ApiModelProperty(value = "供货商规格ID")
    private String supplierSkuId;

    @FieldDoc(
            description = "供货商下单比例"
    )
    @ApiModelProperty(value = "供货商下单比例")
    private Integer orderedRate;

    @FieldDoc(
            description = "供货商箱规名称"
    )
    @ApiModelProperty(value = "供货商箱规名称")
    private String cartonMeasureName;

    @FieldDoc(
            description = "控货方式  0 中心仓配送   1 供应商直送",
            requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "控货方式 0 中心仓配送   1 供应商直送")
    private Integer controlGoodsType = 0;

    @FieldDoc(
            description = "备货区域列表"
    )
    @ApiModelProperty(value = "备货区域列表")
    private List<Integer> controlGoodsAreaIdList;

    @FieldDoc(
            description = "箱规单位转换系数"
    )
    @ApiModelProperty(value = "箱规单位转换系数")
    private List<CartonMeasureConvertFactorVO> cartonMeasureConvertFactorList;

    @FieldDoc(
            description = "是否自定义无upc编码  0-否，1-是"
    )
    @ApiModelProperty(value = "是否自定义无upc编码  0-否，1-是")
    private Integer customizeNoUpcCode;

    @FieldDoc(
            description = "是否使用SKU填充UPC 0-否，1-是"
    )
    @ApiModelProperty(value = "是否使用SKU填充UPC  0-否，1-是")
    private Integer useSkuAsUpc;

    @FieldDoc(
            description = "sku类型 1单品 2组合品"
    )
    @ApiModelProperty(name = "sku类型 1单品 2组合品")
    private Integer skuSaleType;

    @FieldDoc(
            description = "组合品的子sku信息"
    )
    @ApiModelProperty(name = "组合品的子sku信息")
    private List<CombineChildSkuVo> childSkuList;


    public static List<TenantSkuVO> ofDTOList(List<TenantSkuDTO> tenantSkuDTOList) {
        if (CollectionUtils.isEmpty(tenantSkuDTOList)) {
            return Lists.newArrayList();
        }

        return tenantSkuDTOList.stream().filter(Objects::nonNull).map(TenantSkuVO::ofDTO).collect(Collectors.toList())
                .stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static TenantSkuVO ofDTO(TenantSkuDTO tenantSkuDTO) {
        if (tenantSkuDTO == null) {
            return null;
        }

        TenantSkuVO tenantSkuVO = new TenantSkuVO();
        tenantSkuVO.setTenantId(tenantSkuDTO.getTenantId());
        tenantSkuVO.setSpuId(tenantSkuDTO.getSpuId());
        tenantSkuVO.setSkuId(tenantSkuDTO.getSkuId());
        if (CollectionUtils.isNotEmpty(tenantSkuDTO.getUpcList())) {
            tenantSkuVO.setUpc(tenantSkuDTO.getUpcList().get(0));
        }
        tenantSkuVO.setUpcList(tenantSkuDTO.getUpcList());
        tenantSkuVO.setSpec(tenantSkuDTO.getSpec());
        if (tenantSkuDTO.getBasicPrice() != null) {
            //DTO中的basicPrice单位已经为分，不明确为什么再做一次转换
            tenantSkuVO.setBasePrice(MoneyUtils.yuanToCent(tenantSkuDTO.getBasicPrice()));
        }
        tenantSkuVO.setWeight(tenantSkuDTO.getWeight());
        tenantSkuVO.setWeightForUnit(tenantSkuDTO.getWeightForUnit());
        tenantSkuVO.setWeightUnit(tenantSkuDTO.getWeightUnit());
        tenantSkuVO.setSaleUnit(tenantSkuDTO.getUnit());
        if (tenantSkuDTO.getMinNum() != null) {
            tenantSkuVO.setMinOrderCount(String.valueOf(tenantSkuDTO.getMinNum()));
        }
        if (tenantSkuDTO.getBoxNum() != null) {
            tenantSkuVO.setBoxNum(String.valueOf(tenantSkuDTO.getBoxNum()));
        }
        if (tenantSkuDTO.getBoxPrice() != null) {
            tenantSkuVO.setBoxPrice(String.valueOf(tenantSkuDTO.getBoxPrice()));
        }
        if (tenantSkuDTO.getCoveredStores() != null) {
            tenantSkuVO.setCoveredStores(tenantSkuDTO.getCoveredStores());
        }

        if (tenantSkuDTO.getSuggestPrice() != null) {
            tenantSkuVO.setSuggestPrice(tenantSkuDTO.getSuggestPrice());
        }

        if (CollectionUtils.isNotEmpty(tenantSkuDTO.getJdAttrValueList())) {
            tenantSkuVO.setJdAttrValueList(ConverterUtils.convertList(tenantSkuDTO.getJdAttrValueList(), SaleAttrValueInfoVO::ofDTO));
        }
        if (tenantSkuDTO.getImportRate() != null) {
            tenantSkuVO.setImportRate(tenantSkuDTO.getImportRate());
        }
        if (tenantSkuDTO.getExportRate() != null) {
            tenantSkuVO.setExportRate(tenantSkuDTO.getExportRate());
        }
        if (tenantSkuDTO.getTaxCategoryCode() != null) {
            tenantSkuVO.setTaxCategoryCode(tenantSkuDTO.getTaxCategoryCode());
        }
        if (tenantSkuDTO.getEnableExpiredCheck() != null) {
            tenantSkuVO.setEnableExpiredCheck(tenantSkuDTO.getEnableExpiredCheck());
        }
        if (tenantSkuDTO.getOriginPlace() != null) {
            tenantSkuVO.setOriginPlace(tenantSkuDTO.getOriginPlace());
        }
        if (tenantSkuDTO.getExpirationDays() != null) {
            tenantSkuVO.setExpirationDays(tenantSkuDTO.getExpirationDays());
        }
        tenantSkuVO.setExternalCode(tenantSkuDTO.getExternalCode());
        tenantSkuVO.setFranchiseeHeadquartersCode(tenantSkuDTO.getFranchiseeHeadquartersCode());
        tenantSkuVO.setPurchasePlatformCode(tenantSkuDTO.getPurchasePlatformCode());
        tenantSkuVO.setFranchisorPurchasePlatformInfoList(ConverterUtils.convertList(tenantSkuDTO.franchisorPurchasePlatformInfoList,
                FranchisorPurchasePlatformInfoVO::of));
        tenantSkuVO.setControlGoodsAreaIdList(tenantSkuDTO.getControlGoodsAreaIdList());
        if (CollectionUtils.isNotEmpty(tenantSkuDTO.getCartonMeasureDtoList())) {
            tenantSkuVO.setCartonMeasureConvertFactorList(tenantSkuDTO.getCartonMeasureDtoList().stream().map(cm ->
                        new CartonMeasureConvertFactorVO(cm.getCartonMeasureCode(), cm.getCartonMeasureName(), cm.getBasicUnitConvertFactor())
                    ).collect(Collectors.toList()));
        }
        tenantSkuVO.setChannelSaleAttrValueInfoList(Fun.map(tenantSkuDTO.getChannelSaleAttributeValues(), ChannelSaleAttrValueInfoVO::of));
        tenantSkuVO.setImageInfo(SkuImageInfo.of(tenantSkuDTO));

        tenantSkuVO.setSkuSaleType(tenantSkuDTO.getSkuSaleType());
        if(CollectionUtils.isNotEmpty(tenantSkuDTO.getChildSkuList())) {
            tenantSkuVO.setChildSkuList(JacksonUtils.convertList(tenantSkuDTO.getChildSkuList(), CombineChildSkuVo.class));
        }
        return tenantSkuVO;
    }

    public static TenantSkuVO ofDTO(TenantSkuBizDTO tenantSkuDTO) {
        if (tenantSkuDTO == null) {
            return null;
        }

        TenantSkuVO tenantSkuVO = new TenantSkuVO();
        tenantSkuVO.setTenantId(tenantSkuDTO.getTenantId());
        tenantSkuVO.setSpuId(tenantSkuDTO.getSpuId());
        tenantSkuVO.setSkuId(tenantSkuDTO.getSkuId());
        if (CollectionUtils.isNotEmpty(tenantSkuDTO.getUpcList())) {
            tenantSkuVO.setUpc(tenantSkuDTO.getUpcList().get(0));
        }
        tenantSkuVO.setUpcList(tenantSkuDTO.getUpcList());
        tenantSkuVO.setSpec(tenantSkuDTO.getSpec());
        if (tenantSkuDTO.getBasicPrice() != null) {
            //DTO中的basicPrice单位已经为分，不明确为什么再做一次转换
            tenantSkuVO.setBasePrice(MoneyUtils.yuanToCent(tenantSkuDTO.getBasicPrice()));
        }
        tenantSkuVO.setWeight(tenantSkuDTO.getWeight());
        tenantSkuVO.setWeightForUnit(tenantSkuDTO.getWeightForUnit());
        tenantSkuVO.setWeightUnit(tenantSkuDTO.getWeightUnit());
        tenantSkuVO.setSaleUnit(tenantSkuDTO.getUnit());
        if (tenantSkuDTO.getMinNum() != null) {
            tenantSkuVO.setMinOrderCount(String.valueOf(tenantSkuDTO.getMinNum()));
        }
        if (tenantSkuDTO.getBoxNum() != null) {
            tenantSkuVO.setBoxNum(String.valueOf(tenantSkuDTO.getBoxNum()));
        }
        if (tenantSkuDTO.getBoxPrice() != null) {
            tenantSkuVO.setBoxPrice(String.valueOf(tenantSkuDTO.getBoxPrice()));
        }
        if (tenantSkuDTO.getCoveredStores() != null) {
            tenantSkuVO.setCoveredStores(tenantSkuDTO.getCoveredStores());
        }
        if (tenantSkuDTO.getImportRate() != null) {
            tenantSkuVO.setImportRate(tenantSkuDTO.getImportRate());
        }
        if (tenantSkuDTO.getExportRate() != null) {
            tenantSkuVO.setExportRate(tenantSkuDTO.getExportRate());
        }
        if (tenantSkuDTO.getEnableExpiredCheck() != null) {
            tenantSkuVO.setEnableExpiredCheck(tenantSkuDTO.getEnableExpiredCheck());
        }
        if (tenantSkuDTO.getOriginPlace() != null) {
            tenantSkuVO.setOriginPlace(tenantSkuDTO.getOriginPlace());
        }
        if (tenantSkuDTO.getExpirationDays() != null) {
            tenantSkuVO.setExpirationDays(tenantSkuDTO.getExpirationDays());
        }
        tenantSkuVO.setExternalCode(tenantSkuDTO.getExternalCode());
        tenantSkuVO.setFranchiseeHeadquartersCode(tenantSkuDTO.getFranchiseeHeadquartersCode());
        tenantSkuVO.setPurchasePlatformCode(tenantSkuDTO.getPurchasePlatformCode());
        tenantSkuVO.setFranchisorPurchasePlatformInfoList(ConverterUtils.convertList(tenantSkuDTO.franchisorPurchasePlatformInfoList,
                FranchisorPurchasePlatformInfoVO::of));
        tenantSkuVO.setControlGoodsType(tenantSkuDTO.getControlGoodsType());
        tenantSkuVO.setControlGoodsAreaIdList(tenantSkuDTO.getControlGoodsAreaIdList());
        if (CollectionUtils.isNotEmpty(tenantSkuDTO.getCartonMeasureDtoList())) {
            tenantSkuVO.setCartonMeasureConvertFactorList(tenantSkuDTO.getCartonMeasureDtoList().stream().map(cm ->
                    new CartonMeasureConvertFactorVO(cm.getCartonMeasureCode(), cm.getCartonMeasureName(), cm.getBasicUnitConvertFactor())
            ).collect(Collectors.toList()));
        }
        tenantSkuVO.setChannelSaleAttrValueInfoList(Fun.map(tenantSkuDTO.getChannelSaleAttrValueList(), ChannelSaleAttrValueInfoVO::of));
        tenantSkuVO.setImageInfo(SkuImageInfo.of(tenantSkuDTO));

        tenantSkuVO.setSkuSaleType(tenantSkuDTO.getSkuSaleType());
        if(CollectionUtils.isNotEmpty(tenantSkuDTO.getChildSkuList())) {
            tenantSkuVO.setChildSkuList(JacksonUtils.convertList(tenantSkuDTO.getChildSkuList(), CombineChildSkuVo.class));
        }

        return tenantSkuVO;
    }

    public static TenantSkuDTO toDTO(TenantSkuVO tenantSkuVO) {
        if (tenantSkuVO == null) {
            return null;
        }
        TenantSkuDTO tenantSkuDTO = new TenantSkuDTO();
        tenantSkuDTO.setTenantId(tenantSkuVO.getTenantId());
        tenantSkuDTO.setSpuId(tenantSkuVO.getSpuId());
        tenantSkuDTO.setSkuId(tenantSkuVO.getSkuId());
        tenantSkuDTO.setUpcList(tenantSkuVO.getUpcList());
        tenantSkuDTO.setSpec(tenantSkuVO.getSpec());
        if (tenantSkuVO.getBasePrice() != null) {
            //DTO 入参单位要求为分
            tenantSkuDTO.setBasicPrice(tenantSkuVO.getBasePrice().intValue());
        }
        tenantSkuDTO.setWeight(tenantSkuVO.getWeight());
        tenantSkuDTO.setWeightForUnit(tenantSkuVO.getWeightForUnit());
        tenantSkuDTO.setWeightUnit(tenantSkuVO.getWeightUnit());
        tenantSkuDTO.setUnit(tenantSkuVO.getSaleUnit());
        if (tenantSkuVO.getMinOrderCount() != null) {
            tenantSkuDTO.setMinNum(Integer.valueOf(tenantSkuVO.getMinOrderCount()));
        }
        if (tenantSkuVO.getBoxNum() != null) {
            tenantSkuDTO.setBoxNum(Integer.valueOf(tenantSkuVO.getBoxNum()));
        }
        if (tenantSkuVO.getBoxPrice() != null) {
            tenantSkuDTO.setBoxPrice(Integer.valueOf(tenantSkuVO.getBoxPrice()));
        }
        if (tenantSkuVO.getCoveredStores() != null) {
            tenantSkuDTO.setCoveredStores(tenantSkuVO.getCoveredStores());
        }
        if (tenantSkuVO.getImportRate() != null) {
            tenantSkuDTO.setImportRate(tenantSkuVO.getImportRate());
        }
        if (tenantSkuVO.getExportRate() != null) {
            tenantSkuDTO.setExportRate(tenantSkuVO.getExportRate());
        }
        if (tenantSkuVO.getTaxCategoryCode() != null) {
            tenantSkuDTO.setTaxCategoryCode(tenantSkuVO.getTaxCategoryCode());
        }
        if (tenantSkuVO.getEnableExpiredCheck() != null) {
            tenantSkuDTO.setEnableExpiredCheck(tenantSkuVO.getEnableExpiredCheck());
        }
        if (tenantSkuVO.getOriginPlace() != null) {
            tenantSkuDTO.setOriginPlace(tenantSkuVO.getOriginPlace());
        }
        if (tenantSkuVO.getExpirationDays() != null) {
            tenantSkuDTO.setExpirationDays(tenantSkuVO.getExpirationDays());
        }
        if (tenantSkuVO.getSuggestPrice() != null) {
            tenantSkuDTO.setSuggestPrice(tenantSkuVO.getSuggestPrice());
        }

        tenantSkuDTO.setExternalCode(tenantSkuVO.getExternalCode());
        tenantSkuDTO.setFranchiseeHeadquartersCode(tenantSkuVO.getFranchiseeHeadquartersCode());
        tenantSkuDTO.setPurchasePlatformCode(tenantSkuVO.getPurchasePlatformCode());
        tenantSkuDTO.setCustomizeNoUpcCode(tenantSkuVO.getCustomizeNoUpcCode());
        tenantSkuDTO.setUseSkuAsUpc(tenantSkuVO.getUseSkuAsUpc());

        if(tenantSkuVO.getSupplierSkuId() != null){
            tenantSkuDTO.setSupplierSkuId(tenantSkuVO.getSupplierSkuId());
        }
        if(tenantSkuVO.getOrderedRate() != null){
            tenantSkuDTO.setOrderedRate(tenantSkuVO.getOrderedRate());
        }
        if(tenantSkuVO.getCartonMeasureName() != null){
            tenantSkuDTO.setCartonMeasureName(tenantSkuVO.getCartonMeasureName());
        }
        tenantSkuDTO.setControlGoodsType(tenantSkuVO.getControlGoodsType());
        if (CollectionUtils.isNotEmpty(tenantSkuVO.getControlGoodsAreaIdList())) {
            tenantSkuDTO.setControlGoodsAreaIdList(tenantSkuVO.getControlGoodsAreaIdList());
        }

        tenantSkuDTO.setSupplierSkuList(Fun.map(tenantSkuVO.getSupplierSkuList(), SupplierSkuVO::toSupplierSkuDTO));

        tenantSkuDTO.setFranchisorPurchasePlatformInfoList(Fun.map(tenantSkuVO.getFranchisorPurchasePlatformInfoList(), FranchisorPurchasePlatformInfoVO::toFranchisorPurchasePlatformInfoDTO));

        if (CollectionUtils.isNotEmpty(tenantSkuVO.getCartonMeasureConvertFactorList())) {
            tenantSkuDTO.setCartonMeasureDtoList(
                    Fun.map(tenantSkuVO.getCartonMeasureConvertFactorList(), CartonMeasureConvertFactorVO::toCartonMeasureDto)
            );
        }
        tenantSkuDTO.setSuggestPrice(tenantSkuVO.getSuggestPrice());
        if (CollectionUtils.isNotEmpty(tenantSkuVO.getJdAttrValueList())) {
            tenantSkuDTO.setJdAttrValueList(Fun.map(tenantSkuVO.getJdAttrValueList(), SaleAttrValueInfoVO::toDTO));
        }
        tenantSkuDTO.setChannelSaleAttributeValues(Fun.map(tenantSkuVO.getChannelSaleAttrValueInfoList(),
                channelSaleAttrValueInfoVO -> channelSaleAttrValueInfoVO.toOcmsDTO(tenantSkuVO.getImageInfo())));

        if(tenantSkuVO.getSkuSaleType() != null) {
            tenantSkuDTO.setSkuSaleType(tenantSkuVO.getSkuSaleType());
        }
        if(CollectionUtils.isNotEmpty(tenantSkuVO.getChildSkuList())) {
            tenantSkuDTO.setChildSkuList(JacksonUtils.convertList(tenantSkuVO.getChildSkuList(), CombineChildSkuDto.class));
        }

        return tenantSkuDTO;
    }

    public static com.sankuai.meituan.shangou.empower.productbiz.client.dto.TenantSkuDTO toBizDTO(TenantSkuVO tenantSkuVO) {
        if (tenantSkuVO == null) {
            return null;
        }
        com.sankuai.meituan.shangou.empower.productbiz.client.dto.TenantSkuDTO tenantSkuDTO = new com.sankuai.meituan.shangou.empower.productbiz.client.dto.TenantSkuDTO();
        tenantSkuDTO.setTenantId(tenantSkuVO.getTenantId());
        tenantSkuDTO.setSpuId(tenantSkuVO.getSpuId());
        tenantSkuDTO.setSkuId(tenantSkuVO.getSkuId());
        tenantSkuDTO.setUpcList(tenantSkuVO.getUpcList());
        tenantSkuDTO.setSpec(tenantSkuVO.getSpec());
        if (tenantSkuVO.getBasePrice() != null) {
            //DTO 入参单位要求为分
            tenantSkuDTO.setBasicPrice(tenantSkuVO.getBasePrice());
        }
        tenantSkuDTO.setWeight(tenantSkuVO.getWeight());
        tenantSkuDTO.setWeightForUnit(tenantSkuVO.getWeightForUnit());
        tenantSkuDTO.setWeightUnit(tenantSkuVO.getWeightUnit());
        tenantSkuDTO.setUnit(tenantSkuVO.getSaleUnit());
        if (tenantSkuVO.getMinOrderCount() != null) {
            tenantSkuDTO.setMinNum(Integer.valueOf(tenantSkuVO.getMinOrderCount()));
        }
        if (tenantSkuVO.getBoxNum() != null) {
            tenantSkuDTO.setBoxNum(Integer.valueOf(tenantSkuVO.getBoxNum()));
        }
        if (tenantSkuVO.getBoxPrice() != null) {
            tenantSkuDTO.setBoxPrice(Long.valueOf(tenantSkuVO.getBoxPrice()));
        }
        if (tenantSkuVO.getCoveredStores() != null) {
            tenantSkuDTO.setCoveredStores(tenantSkuVO.getCoveredStores());
        }
        if (tenantSkuVO.getImportRate() != null) {
            tenantSkuDTO.setImportRate(tenantSkuVO.getImportRate());
        }
        if (tenantSkuVO.getExportRate() != null) {
            tenantSkuDTO.setExportRate(tenantSkuVO.getExportRate());
        }
        if (tenantSkuVO.getTaxCategoryCode() != null) {
            tenantSkuDTO.setTaxCategoryCode(tenantSkuVO.getTaxCategoryCode());
        }
        if (tenantSkuVO.getEnableExpiredCheck() != null) {
            tenantSkuDTO.setEnableExpiredCheck(tenantSkuVO.getEnableExpiredCheck());
        }
        if (tenantSkuVO.getOriginPlace() != null) {
            tenantSkuDTO.setOriginPlace(tenantSkuVO.getOriginPlace());
        }
        if (tenantSkuVO.getExpirationDays() != null) {
            tenantSkuDTO.setExpirationDays(tenantSkuVO.getExpirationDays());
        }
        if (tenantSkuVO.getSuggestPrice() != null) {
            tenantSkuDTO.setSuggestPrice(tenantSkuVO.getSuggestPrice());
        }

        tenantSkuDTO.setExternalCode(tenantSkuVO.getExternalCode());
        tenantSkuDTO.setFranchiseeHeadquartersCode(tenantSkuVO.getFranchiseeHeadquartersCode());
        tenantSkuDTO.setPurchasePlatformCode(tenantSkuVO.getPurchasePlatformCode());
        tenantSkuDTO.setCustomizeNoUpcCode(tenantSkuVO.getCustomizeNoUpcCode());

        if(tenantSkuVO.getSupplierSkuId() != null){
            tenantSkuDTO.setSupplierSkuId(tenantSkuVO.getSupplierSkuId());
        }
        if(tenantSkuVO.getOrderedRate() != null){
            tenantSkuDTO.setOrderedRate(tenantSkuVO.getOrderedRate());
        }
        if(tenantSkuVO.getCartonMeasureName() != null){
            tenantSkuDTO.setCartonMeasureName(tenantSkuVO.getCartonMeasureName());
        }
        tenantSkuDTO.setControlGoodsType(tenantSkuVO.getControlGoodsType());
        if (CollectionUtils.isNotEmpty(tenantSkuVO.getControlGoodsAreaIdList())) {
            tenantSkuDTO.setControlGoodsAreaIdList(tenantSkuVO.getControlGoodsAreaIdList());
        }

        tenantSkuDTO.setSupplierSkuList(Fun.map(tenantSkuVO.getSupplierSkuList(), SupplierSkuVO::toBizSupplierSkuDTO));

        tenantSkuDTO.setFranchisorPurchasePlatformInfoList(Fun.map(tenantSkuVO.getFranchisorPurchasePlatformInfoList(), FranchisorPurchasePlatformInfoVO::toBizDTO));

        if (CollectionUtils.isNotEmpty(tenantSkuVO.getCartonMeasureConvertFactorList())) {
            tenantSkuDTO.setCartonMeasureDtoList(
                    Fun.map(tenantSkuVO.getCartonMeasureConvertFactorList(), CartonMeasureConvertFactorVO::toBizDTO)
            );
        }
        tenantSkuDTO.setSuggestPrice(tenantSkuVO.getSuggestPrice());
        if (CollectionUtils.isNotEmpty(tenantSkuVO.getJdAttrValueList())) {
            tenantSkuDTO.setJdAttrValueList(Fun.map(tenantSkuVO.getJdAttrValueList(), SaleAttrValueInfoVO::toBizDTO));
        }
        tenantSkuDTO.setChannelSaleAttrValueList(Fun.map(tenantSkuVO.getChannelSaleAttrValueInfoList(),
                channelSaleAttrValueInfoVO -> channelSaleAttrValueInfoVO.toBizDTO(tenantSkuVO.getImageInfo())));

        if(tenantSkuVO.getSkuSaleType() != null) {
            tenantSkuDTO.setSkuSaleType(tenantSkuVO.getSkuSaleType());
        }
        if(CollectionUtils.isNotEmpty(tenantSkuVO.getChildSkuList())) {
            tenantSkuDTO.setChildSkuList(JacksonUtils.convertList(tenantSkuVO.getChildSkuList(), com.sankuai.meituan.shangou.empower.productbiz.client.dto.sku.CombineChildSkuDto.class));
        }

        return tenantSkuDTO;
    }

    public static List<TenantSkuVO> ofBizDTOList(List<TenantSkuBizDTO> tenantSkuDTOList) {
        if (CollectionUtils.isEmpty(tenantSkuDTOList)) {
            return Lists.newArrayList();
        }

        return tenantSkuDTOList.stream().filter(Objects::nonNull).map(TenantSkuVO::ofDTO).collect(Collectors.toList())
                .stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static TenantSkuVO fromBizDTO(com.sankuai.meituan.shangou.empower.productbiz.client.dto.TenantSkuDTO tenantSkuDTO) {
        if (tenantSkuDTO == null) {
            return null;
        }
        TenantSkuVO tenantSkuVO = new TenantSkuVO();
        tenantSkuVO.setTenantId(tenantSkuDTO.getTenantId());
        tenantSkuVO.setSpuId(tenantSkuDTO.getSpuId());
        tenantSkuVO.setSkuId(tenantSkuDTO.getSkuId());
        if (CollectionUtils.isNotEmpty(tenantSkuDTO.getUpcList())) {
            tenantSkuVO.setUpc(tenantSkuDTO.getUpcList().get(0));
        }
        tenantSkuVO.setUpcList(tenantSkuDTO.getUpcList());
        tenantSkuVO.setSpec(tenantSkuDTO.getSpec());
        if (tenantSkuDTO.getBasicPrice() != null) {
            tenantSkuVO.setBasePrice(tenantSkuDTO.getBasicPrice());
        }
        tenantSkuVO.setWeight(tenantSkuDTO.getWeight());
        tenantSkuVO.setWeightForUnit(tenantSkuDTO.getWeightForUnit());
        tenantSkuVO.setWeightUnit(tenantSkuDTO.getWeightUnit());
        tenantSkuVO.setSaleUnit(tenantSkuDTO.getUnit());
        if (tenantSkuDTO.getMinNum() != null) {
            tenantSkuVO.setMinOrderCount(String.valueOf(tenantSkuDTO.getMinNum()));
        }
        if (tenantSkuDTO.getBoxNum() != null) {
            tenantSkuVO.setBoxNum(String.valueOf(tenantSkuDTO.getBoxNum()));
        }
        if (tenantSkuDTO.getBoxPrice() != null) {
            tenantSkuVO.setBoxPrice(String.valueOf(tenantSkuDTO.getBoxPrice()));
        }
        if (tenantSkuDTO.getCoveredStores() != null) {
            tenantSkuVO.setCoveredStores(tenantSkuDTO.getCoveredStores());
        }
        if (tenantSkuDTO.getImportRate() != null) {
            tenantSkuVO.setImportRate(tenantSkuDTO.getImportRate());
        }
        if (tenantSkuDTO.getExportRate() != null) {
            tenantSkuVO.setExportRate(tenantSkuDTO.getExportRate());
        }
        if (tenantSkuDTO.getTaxCategoryCode() != null) {
            tenantSkuVO.setTaxCategoryCode(tenantSkuDTO.getTaxCategoryCode());
        }
        if (tenantSkuDTO.getEnableExpiredCheck() != null) {
            tenantSkuVO.setEnableExpiredCheck(tenantSkuDTO.getEnableExpiredCheck());
        }
        if (tenantSkuDTO.getOriginPlace() != null) {
            tenantSkuVO.setOriginPlace(tenantSkuDTO.getOriginPlace());
        }
        if (tenantSkuDTO.getExpirationDays() != null) {
            tenantSkuVO.setExpirationDays(tenantSkuDTO.getExpirationDays());
        }
        if (tenantSkuDTO.getComposeSku() != null) {
            tenantSkuVO.setComposeSku(tenantSkuDTO.getComposeSku());
        }

        if (tenantSkuDTO.getSuggestPrice() != null) {
            tenantSkuVO.setSuggestPrice(tenantSkuDTO.getSuggestPrice());
        }

        tenantSkuVO.setExternalCode(tenantSkuDTO.getExternalCode());
        tenantSkuVO.setFranchiseeHeadquartersCode(tenantSkuDTO.getFranchiseeHeadquartersCode());
        tenantSkuVO.setPurchasePlatformCode(tenantSkuDTO.getPurchasePlatformCode());
        tenantSkuVO.setCustomizeNoUpcCode(tenantSkuDTO.getCustomizeNoUpcCode());
        tenantSkuVO.setSupplierSkuId(tenantSkuDTO.getSupplierSkuId());
        tenantSkuVO.setOrderedRate(tenantSkuDTO.getOrderedRate());
        tenantSkuVO.setCartonMeasureName(tenantSkuDTO.getCartonMeasureName());
        if (CollectionUtils.isNotEmpty(tenantSkuDTO.getFranchisorPurchasePlatformInfoList())) {
            tenantSkuVO.setFranchisorPurchasePlatformInfoList(ConverterUtils.convertList(tenantSkuDTO.getFranchisorPurchasePlatformInfoList(),
                    FranchisorPurchasePlatformInfoVO::of));
        }
        tenantSkuVO.setControlGoodsType(tenantSkuDTO.getControlGoodsType());
        if (CollectionUtils.isNotEmpty(tenantSkuDTO.getControlGoodsAreaIdList())) {
            tenantSkuVO.setControlGoodsAreaIdList(tenantSkuDTO.getControlGoodsAreaIdList());
        }
        tenantSkuVO.setSupplierSkuList(ConverterUtils.convertList(tenantSkuDTO.getSupplierSkuList(), SupplierSkuVO::of));
        if (CollectionUtils.isNotEmpty(tenantSkuDTO.getCartonMeasureDtoList())) {
            tenantSkuVO.setCartonMeasureConvertFactorList(tenantSkuDTO.getCartonMeasureDtoList().stream().map(cm ->
                    new CartonMeasureConvertFactorVO(cm.getCartonMeasureCode(), cm.getCartonMeasureName(), cm.getBasicUnitConvertFactor())
            ).collect(Collectors.toList()));
        }
        tenantSkuVO.setSuggestPrice(tenantSkuDTO.getSuggestPrice());
        if (CollectionUtils.isNotEmpty(tenantSkuDTO.getJdAttrValueList())) {
            tenantSkuVO.setJdAttrValueList(Fun.map(tenantSkuDTO.getJdAttrValueList(), SaleAttrValueInfoVO::ofBizDTO));
        }
        tenantSkuVO.setChannelSaleAttrValueInfoList(Fun.map(tenantSkuDTO.getChannelSaleAttrValueList(), ChannelSaleAttrValueInfoVO::of));
        tenantSkuVO.setImageInfo(SkuImageInfo.of(tenantSkuDTO));

        tenantSkuVO.setSkuSaleType(tenantSkuDTO.getSkuSaleType());
        if(CollectionUtils.isNotEmpty(tenantSkuDTO.getChildSkuList())) {
            tenantSkuVO.setChildSkuList(JacksonUtils.convertList(tenantSkuDTO.getChildSkuList(), CombineChildSkuVo.class));
        }

        return tenantSkuVO;
    }
}
