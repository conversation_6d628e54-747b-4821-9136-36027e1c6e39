package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2022-10-18
 * @email <EMAIL>
 */
@TypeDoc(
        description = "新建/编辑班次请求"
)
@ApiModel("新建/编辑班次请求")
@Data
public class QueryRuleListRequest {

    @FieldDoc(
            description = "页码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "页码")
    private Integer pageNo;

    @FieldDoc(
            description = "页大小", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "页大小")
    private Integer pageSize;

    @FieldDoc(
            description = "门店名称关键字", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店名称关键字")
    private String poiNameKeyword;

}
