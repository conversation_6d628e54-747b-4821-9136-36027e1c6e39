package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.delivery;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/11
 */
@TypeDoc(
		description = "单配送渠道预发结果"
)
@Data
@ApiModel("单配送渠道预发结果")
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryChannelPreLaunchResponse {

	@FieldDoc(
			description = "配送渠道标识", requiredness = Requiredness.REQUIRED
	)
	@ApiModelProperty(value = "配送渠道标识", required = true)
	private Integer deliveryChannelId;

	@FieldDoc(
			description = "配送渠道名称", requiredness = Requiredness.REQUIRED
	)
	@ApiModelProperty(value = "配送渠道名称", required = true)
	private String deliveryChannelName;

	@FieldDoc(
			description = "配送渠道图标url"
	)
	@ApiModelProperty(value = "配送渠道图标url")
	private String deliveryChannelIcon;

	@FieldDoc(
			description = "服务包", requiredness = Requiredness.REQUIRED
	)
	@ApiModelProperty(value = "服务包", required = true)
	private String servicePackage;

	@FieldDoc(
			description = "是否可以发起配送", requiredness = Requiredness.REQUIRED
	)
	@ApiModelProperty(value = "是否可以发起配送", required = true)
	private Boolean available;

	@FieldDoc(
			description = "不可发起配送的原因", requiredness = Requiredness.REQUIRED
	)
	@ApiModelProperty(value = "不可发起配送的原因", required = true)
	private String exceptionDesc;

	@FieldDoc(
			description = "预估配送费用", requiredness = Requiredness.REQUIRED
	)
	@ApiModelProperty(value = "预估配送费用", required = true)
	private Double estimatedDeliveryFee;

	@FieldDoc(
			description = "优惠金额"
	)
	@ApiModelProperty(value = "优惠金额")
	private Double discountAmount;
}
