package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.meituan.shangou.saas.tenant.thrift.BoothManageThriftService;
import com.meituan.shangou.saas.tenant.thrift.BoothThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.Status;
import com.meituan.shangou.saas.tenant.thrift.common.StatusCodeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.booth.request.BoothAddRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.booth.request.BoothUpdateRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.booth.response.BoothInfoResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.booth.response.BoothInfoWithSettlementResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.booth.response.BoothWithSettleListResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.config.response.CommonResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.converters.booth.BoothConverter;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.booth.BoothOperateRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.booth.BoothMngVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.booth.SearchCodeCheckResultVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/10/14 16:46
 * Description: 摊位操作Wrapper
 */
@Slf4j
@Component
public class BoothWrapper {

    @Autowired
    private BoothManageThriftService boothManageThriftService;

    @Autowired
    private BoothThriftService boothThriftService;

    @Autowired
    private StoreWrapper storeWrapper;

    /**
     * 创建摊位
     *
     * @param tenantId 租户Id
     * @param optId    操作人Id
     * @param optName  操作人账户名
     * @param request
     */
    public void boothCreate(long tenantId, long optId, String optName, BoothOperateRequest request) {
        try {
            Map<Long, PoiInfoDto> poiMap = storeWrapper.queryPoiInfoById(tenantId, Arrays.asList(request.getPoiId()));
            if (MapUtils.isEmpty(poiMap)) {
                throw new CommonRuntimeException("门店不存在");
            }
            Long poiDepId = poiMap.get(request.getPoiId()).getDepartmentId();
            BoothAddRequest thriftRequest = BoothConverter.toBoothAddRequest(tenantId, optId, optName, poiDepId, request);
            BoothInfoResponse response = boothManageThriftService.addOneBooth(thriftRequest);
            log.info("BoothManageThriftService.addOneBooth. request:{}, response:{}", thriftRequest, response);
            checkResponse(response.getStatus());
        } catch (CommonRuntimeException e) {
            throw e;
        } catch (Exception e) {
            log.error("BoothManageThriftService.addOneBooth Exception. request:{}", request, e);
            throw new CommonRuntimeException("摊位创建错误", ResultCode.BOOTH_ERROR);
        }
    }


    /**
     * 门店的摊位模式
     * @param tenantId
     * @param poiId
     * @return
     */
    public Integer getBoothMode(Long tenantId, Long poiId) {
        Map<Long, PoiInfoDto> poiMap = storeWrapper.queryPoiInfoById(tenantId, Arrays.asList(poiId));
        if (MapUtils.isEmpty(poiMap)) {
            throw new CommonRuntimeException("门店不存在");
        }

        PoiInfoDto poiInfoDto = poiMap.get(poiId);

        return poiInfoDto.getBoothMode();

    }


    /**
     * 编辑摊位
     *
     * @param tenantId 租户Id
     * @param optId    操作人Id
     * @param optName  操作人账户名
     * @param request
     */
    public void boothModify(long tenantId, long optId, String optName, BoothOperateRequest request) {
        try {
            Map<Long, PoiInfoDto> poiMap = storeWrapper.queryPoiInfoById(tenantId, Arrays.asList(request.getPoiId()));
            if (MapUtils.isEmpty(poiMap)) {
                throw new CommonRuntimeException("门店不存在");
            }
            Long poiDepId = poiMap.get(request.getPoiId()).getDepartmentId();
            BoothUpdateRequest thriftRequest = BoothConverter.toBoothUpdateRequest(tenantId, optId, optName, poiDepId, request);
            BoothInfoResponse response = boothManageThriftService.updateBoothInfoByBoothId(thriftRequest);
            log.info("BoothManageThriftService.updateBoothInfoByBoothId. request:{}, response:{}", thriftRequest, response);
            checkResponse(response.getStatus());
        } catch (CommonRuntimeException e) {
            throw e;
        } catch (Exception e) {
            log.error("BoothManageThriftService.updateBoothInfoByBoothId Exception. request:{}", request, e);
            throw new CommonRuntimeException("摊位编辑错误", ResultCode.BOOTH_ERROR);
        }
    }

    public List<BoothMngVO> queryPoiBooths(long tenantId, Long poiId) {
        try {
            BoothWithSettleListResponse response = boothThriftService.queryBoothWithSettleListByPoiId(tenantId, poiId);
            log.info("BoothThriftService.queryBoothWithSettleListByPoiId. tenantId:{}, poiId:{}, response:{}", tenantId, poiId, response);
            checkResponse(response.getStatus());
            return response.getBoothWithSettleList().stream().map(BoothConverter::buildBoothMngVO).collect(Collectors.toList());
        } catch (CommonRuntimeException e) {
            throw e;
        } catch (Exception e) {
            log.error("BoothThriftService.queryBoothWithSettleListByPoiId Exception. tenantId:{}, poiId:{}", tenantId, poiId, e);
            throw new CommonRuntimeException("查询门店摊位列表错误", ResultCode.BOOTH_ERROR);
        }
    }

    public SearchCodeCheckResultVO checkSearchCode(long tenantId, Long poiId, String boothSearchCode) {
        try {
            CommonResponse response = boothThriftService.checkBoothSearchCode(tenantId, poiId, boothSearchCode);
            log.info("BoothThriftService.checkBoothSearchCode. tenantId:{}, poiId:{}, boothSearchCode:{}, response:{}", tenantId, poiId, boothSearchCode, response);
            SearchCodeCheckResultVO resultVO = new SearchCodeCheckResultVO();
            if (response.getStatus() == null) {
                throw new CommonRuntimeException("检查摊位检索码错误，请稍后重试.", ResultCode.BOOTH_ERROR);
            }
            if (response.getStatus().getCode() != 0) {
                resultVO.setIsPass(false);
                resultVO.setErrorMsg(response.getStatus().getMessage());
            } else {
                resultVO.setIsPass(true);
            }
            return resultVO;
        } catch (CommonRuntimeException e) {
            throw e;
        } catch (Exception e) {
            log.error("BoothThriftService.queryBoothWithSettleListByPoiId Exception. tenantId:{}, poiId:{}", tenantId, poiId, e);
            throw new CommonRuntimeException("检查摊位检索码错误", ResultCode.BOOTH_ERROR);
        }
    }

    public BoothMngVO queryBySearchCode(long tenantId, Long poiId, String boothSearchCode) {
        try {
            BoothInfoWithSettlementResponse response = boothThriftService.queryBoothWithSettleBySearchCode(tenantId, poiId, boothSearchCode);
            log.info("BoothThriftService.queryBoothWithSettleBySearchCode. tenantId:{}, poiId:{}, boothSearchCode:{}, response:{}", tenantId, poiId, boothSearchCode, response);
            Status status = response.getStatus();
            if (status == null) {
                throw new CommonRuntimeException("摊位相关功能异常，请稍后重试.", ResultCode.BOOTH_ERROR);
            }
            // 该接口错误信息需要返回错误码
            if (status.getCode() != 0) {
                switch (status.getCode()) {
                    // tenant 中 StatusCodeEnum.BOOTH_SEARCH_CODE_ERROR.getCode()
                    case 6006:
                        throw new CommonRuntimeException(status.getMessage(), ResultCode.BOOTH_SEARCH_CODE_ERROR);
                    // tenant 中 StatusCodeEnum.SEARCH_CODE_WITH_NON_BOOTH.getCode()
                    case 6008:
                        throw new CommonRuntimeException(status.getMessage(), ResultCode.SEARCH_CODE_WITH_NON_BOOTH);
                    // tenant 中 StatusCodeEnum.BOOTH_WITH_NON_ONSITE_ALIPAY_ACCOUNT.getCode()
                    case 6009:
                        throw new CommonRuntimeException(status.getMessage(), ResultCode.BOOTH_WITH_NON_ONSITE_ALIPAY_ACCOUNT);
                    default:
                        throw new CommonRuntimeException(status.getMessage(), ResultCode.BOOTH_ERROR);
                }
            }
            return BoothConverter.buildBoothMngVO(response.getBoothInfoWithSettleAccountsDto());
        } catch (CommonRuntimeException e) {
            throw e;
        } catch (Exception e) {
            log.error("BoothThriftService.queryBoothWithSettleBySearchCode Exception. tenantId:{}, poiId:{}, boothSearchCode:{}", tenantId, poiId, boothSearchCode, e);
            throw new CommonRuntimeException("根据摊位检索码查询门店摊位错误", ResultCode.BOOTH_ERROR);
        }
    }

    /**
     * 检查返回
     *
     * @param status
     */
    private void checkResponse(Status status) {
        if (status == null) {
            throw new CommonRuntimeException("摊位相关功能异常，请稍后重试.", ResultCode.BOOTH_ERROR);
        }

        if (status.getCode() != 0) {
            throw new CommonRuntimeException(status.getMessage(), ResultCode.BOOTH_ERROR);
        }
    }
}
