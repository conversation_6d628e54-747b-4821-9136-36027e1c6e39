package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.wavepick;

import com.google.common.collect.Lists;
import com.meituan.shangou.saas.tenant.thrift.PoiThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiEntityTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiMapResponse;
import com.sankuai.meituan.reco.pickselect.common.Status;
import com.sankuai.meituan.reco.pickselect.consts.ResultCodeEnum;
import com.sankuai.meituan.reco.pickselect.consts.WavehouseCountEnum;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.store.StorePickConfigThriftService;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.store.dto.StoreSortInfo;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.store.dto.TransferPickConfigQueryReq;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.store.dto.TransferPickConfigQueryResponse;
import com.sankuai.meituan.reco.pickselect.thrift.SkuPackingSpecDTO;
import com.sankuai.meituan.reco.pickselect.thrift.outwarehouse.AllotOutWarehouseThriftService;
import com.sankuai.meituan.reco.pickselect.thrift.outwarehouse.dto.OutWarehouseItemDTO;
import com.sankuai.meituan.reco.pickselect.thrift.outwarehouse.dto.OutWarehouseOrderBaseDTO;
import com.sankuai.meituan.reco.pickselect.thrift.outwarehouse.dto.OutWarehouseOrderDetailDTO;
import com.sankuai.meituan.reco.pickselect.thrift.outwarehouse.request.AllocationOutWarehouseOrderDetailsQueryRequest;
import com.sankuai.meituan.reco.pickselect.thrift.outwarehouse.request.AllocationOutWarehouseOrderPageQueryRequest;
import com.sankuai.meituan.reco.pickselect.thrift.outwarehouse.request.AllocationOutWarehouseOrderQueryRequest;
import com.sankuai.meituan.reco.pickselect.thrift.outwarehouse.response.OutWarehouseOrderDetailsResponse;
import com.sankuai.meituan.reco.pickselect.thrift.outwarehouse.response.PagedOutWarehouseOrdersResponse;
import com.sankuai.meituan.reco.pickselect.thrift.wave.WarehouseOperateThriftService;
import com.sankuai.meituan.reco.pickselect.thrift.wave.WarehouseThriftService;
import com.sankuai.meituan.reco.pickselect.thrift.wave.dto.*;
import com.sankuai.meituan.reco.pickselect.thrift.wave.request.*;
import com.sankuai.meituan.reco.pickselect.thrift.wave.response.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.OrderTagConstant;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.OutWarehouseWorkOrderStatusEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.GoodsOwnerVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.AccountUserDto;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.facade.AccountService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.JacksonUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.TenantWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.wavepick.converter.*;
import com.sankuai.meituan.reco.store.management.enums.OutboundSourceTypeEnum;
import com.sankuai.meituan.reco.store.management.enums.OutboundStatusEnum;
import com.sankuai.meituan.reco.store.management.enums.OutboundSubTypeEnum;
import com.sankuai.meituan.reco.store.management.enums.OutboundTypeEnum;
import com.sankuai.meituan.reco.store.management.thrift.common.PageParam;
import com.sankuai.meituan.reco.store.management.thrift.common.StoreParam;
import com.sankuai.meituan.reco.store.management.thrift.outbound.OutboundDistributionOrderThriftService;
import com.sankuai.meituan.reco.store.management.thrift.outbound.OutboundOrderThriftService;
import com.sankuai.meituan.reco.store.management.thrift.outbound.dto.OutboundOrderInfo;
import com.sankuai.meituan.reco.store.management.thrift.outbound.req.SearchOutboundReq;
import com.sankuai.meituan.reco.store.management.thrift.outbound.req.distribution.OutboundOrderQueryDetailsRequest;
import com.sankuai.meituan.reco.store.management.thrift.outbound.resp.TPageResp;
import com.sankuai.meituan.reco.store.management.thrift.outbound.resp.TResp;
import com.sankuai.meituan.reco.store.management.thrift.outbound.resp.distribution.OutboundOrderQueryDetailsResp;
import com.sankuai.meituan.reco.store.management.thrift.outbound.resp.distribution.OutboundOrderQueryReceiverResp;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * @Description: 波次拣货
 * @Author: zhangjian155
 * @Date: 2022/8/9 20:09
 */
@Service
@Slf4j
public class WarehouseWrapper {

    private static final String DEFAULT_NAME = "系统";

    private static final String TAB_NUM_ALLOCCOUNT = "allocCount";
    private static final String TAB_NUM_SALECOUNT = "saleCount";

    // todo msl 改名 done
    private static final String TAB_NUM_PURCHASERETURNCOUNT = "purchaseReturnCount";

    //销售出库
    private static final Integer TO_B_SALL = 4001;
    //配销出库
    private static final Integer DISTRIBUTION = 4002;

    @Autowired
    private WarehouseThriftService warehouseThriftService;
    @Autowired
    private WarehouseOperateThriftService warehouseOperateThriftService;

    @Autowired
    private WarehousePickSkuDetailRequestConverter warehousePickSkuDetailRequestConverter;

    @Autowired
    private WarehousePickSkuDetailResponseConverter warehousePickSkuDetailResponseConverter;

    @Autowired
    private WarehouseWaveTaskDetailRequestConverter warehouseWaveTaskDetailRequestConverter;

    @Autowired
    private WarehouseWaveTaskDetailResponseConverter warehouseWaveTaskDetailResponseConverter;

    @Autowired
    private WarehouseContainerBoxUpdateRequestConverter warehouseContainerBoxUpdateRequestConverter;

    @Autowired
    private WarehouseSeedWaitQueryRequestConverter warehouseSeedWaitQueryRequestConverter;

    @Autowired
    private WarehouseSeedWaitModuleConverter warehouseSeedWaitModuleConverter;

    @Autowired
    private WarehouseSeedSkuDetailRequestConverter warehouseSeedSkuDetailRequestConverter;

    @Autowired
    private WarehouseSeedSkuDetailResponseConverter warehouseSeedSkuDetailResponseConverter;

    @Autowired
    private WarehouseSeedFuzzySkuRequestConverter warehouseSeedFuzzySkuRequestConverter;

    @Autowired
    private WarehouseSeedFuzzySkuResponseConverter warehouseSeedFuzzySkuResponseConverter;

    @Autowired
    private WarehouseSeedCompleteQueryRequestConverter warehouseSeedCompleteQueryRequestConverter;

    @Autowired
    private WarehouseSeedCompleteModuleConverter warehouseSeedCompleteModuleConverter;

    @Autowired
    private WarehousePickCompleteQueryRequestConverter warehousePickCompleteQueryRequestConverter;

    @Autowired
    private WarehousePickCompleteQueryRequestV2Converter warehousePickCompleteQueryRequestV2Converter;

    @Autowired
    private WarehousePickCompleteModuleConverter warehousePickCompleteModuleConverter;

    @Autowired
    private WarehousePickDetailFuzzyRequestConverter warehousePickDetailFuzzyRequestConverter;

    @Autowired
    private WarehousePickDetailFuzzyResponseConverter warehousePickDetailFuzzyResponseConverter;

    @Autowired
    private WarehousePickPackSeedWaitDetailRequestConverter warehousePickPackSeedWaitDetailRequestConverter;

    @Autowired
    private PoiThriftService poiThriftService;

    @Resource
    private AllotOutWarehouseThriftService allotOutWarehouseThriftService;

    @Resource
    private StorePickConfigThriftService storePickConfigThriftService;

    @Resource
    private OutboundOrderThriftService outboundOrderThriftService;

    @Resource
    private OutboundDistributionOrderThriftService outboundDistributionOrderThriftService;

    @Resource
    private AccountService accountService;

    @Autowired
    private TenantWrapper tenantWrapper;

    // todo msl 方法瘦身 done
    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public CommonResponse<WarehousePickCollectionQueryResponse> queryWarehouseCollection(WarehousePickCollectionQueryRequest request) {

        // 支持配销出库集单、采退出库集单，需要根据单据标识决定走pickselect还是empower
//        if ((MccConfigUtil.isDistributionOrderEnable() && OrderTagConstant.TAG_DISTRIBUTION.equals(request.getOrderTag()))
//                || (MccConfigUtil.isPurchaseOrderEnable() && OrderTagConstant.TAG_PURCHASE.equals(request.getOrderTag()))) {
//            return queryWarehouseCollectionFromEmpower(request);
//        }
//        return queryWarehouseCollectionFromPickSelect(request);

        //统一走storeempower接口
        return queryWarehouseCollectionFromEmpower(request);
    }

    private CommonResponse<WarehousePickCollectionQueryResponse> queryWarehouseCollectionFromPickSelect(WarehousePickCollectionQueryRequest request) {
        WarehousePickCollectionQueryResponse response = new WarehousePickCollectionQueryResponse();
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        List<Long> storeList = identityInfo.getStoreIdList();
        long tenantId = identityInfo.getUser().getTenantId();

        AllocationOutWarehouseOrderPageQueryRequest orderPageQueryRequest = new AllocationOutWarehouseOrderPageQueryRequest();
        orderPageQueryRequest.setStatusCodes(Collections.singletonList(OutWarehouseWorkOrderStatusEnum.INITIAL.getcode()));
        orderPageQueryRequest.setTenantId(tenantId);
        orderPageQueryRequest.setReceivingStoreIds(request.getPoiList());
        orderPageQueryRequest.setPageSize(request.getPageSize());
        orderPageQueryRequest.setPageNo(request.getPageNum().intValue());
        orderPageQueryRequest.setStoreIds(storeList);
        Optional.ofNullable(request.getSkuOwnerIdList()).ifPresent(orderPageQueryRequest::setSkuOwnerIdList);

        try {
            log.info("调用allotOutWarehouseThriftService.pageQueryOrderBase request:{}", orderPageQueryRequest);
            PagedOutWarehouseOrdersResponse ordersResponse = allotOutWarehouseThriftService.pageQueryOrderBase(orderPageQueryRequest);
            log.info("调用allotOutWarehouseThriftService.pageQueryOrderBase request:{} response:{}", orderPageQueryRequest, ordersResponse);
            if (!Status.SUCCESS.getCode().equals(ordersResponse.getStatus().getCode())) {
                log.warn("查询待集单任务失败 ordersResponse:{}", ordersResponse);
                return CommonResponse.fail(ResultCode.FAIL.getCode(), ordersResponse.getStatus().getMessage());
            }
            if (CollectionUtils.isNotEmpty(ordersResponse.getOutWarehouseOrders())) {
//                PoiSearchRequest poiSearchRequest = new PoiSearchRequest();
//                poiSearchRequest.setTenantId(tenantId);
//                log.info("调用poiThriftService.search request:{}", poiSearchRequest);
//                PoiSearchResponse poiSearchResponse = poiThriftService.search(poiSearchRequest);
//                log.info("调用poiThriftService.search request:{} response:{}", poiSearchRequest, poiSearchResponse);
                // 由于多货主的问题，这里查询门店信息的时候要直接查
                List<Long> poiIdList = ordersResponse.getOutWarehouseOrders().stream().map(OutWarehouseOrderBaseDTO::getReceivingWarehouseId).collect(Collectors.toList());
                PoiMapResponse poiMapResponse = poiThriftService.queryPoiInfoMapByPoiIds(poiIdList);
                if (!Status.SUCCESS.getCode().equals(poiMapResponse.getStatus().getCode()) || poiMapResponse == null) {
                    log.warn("查询门店名称失败 poiMapResponse:{}",poiMapResponse);
                }
                if (Objects.nonNull(poiMapResponse.getPoiInfoMap())) {
                    Map<Long, PoiInfoDto> poiInfoMap = poiMapResponse.getPoiInfoMap();
                    List<WarehousePickCollectionModuleVO> dataList = ordersResponse.getOutWarehouseOrders().stream().map(order -> {
                        WarehousePickCollectionModuleVO pickCollectionModuleVO = new WarehousePickCollectionModuleVO();
                        // 兼容多货主逻辑
                        pickCollectionModuleVO.setOutWarehouseOrderNo(Optional.ofNullable(order.getSyncOutWarehouseNo()).orElse(order.getOutWarehouseOrderNo()));
                        pickCollectionModuleVO.setPoiId(order.getReceivingWarehouseId());
                        SimpleDateFormat format =  new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        pickCollectionModuleVO.setCreateTime(format.format(new Date(order.getCreateTimeInMillis())));
                        pickCollectionModuleVO.setPoiName(poiInfoMap.containsKey(order.getReceivingWarehouseId()) ? poiInfoMap.get(order.getReceivingWarehouseId()).getPoiName() : null);
                        pickCollectionModuleVO.setSkuOwnerId(order.getSkuOwnerId() == null ? null : order.getSkuOwnerId());
                        pickCollectionModuleVO.setSkuOwnerName(order.getSkuOwnerName() == null ? null : order.getSkuOwnerName());
                        pickCollectionModuleVO.setType(order.getOrderType());
                        return pickCollectionModuleVO;
                    }).collect(Collectors.toList());

                    response.setHasMore(ordersResponse.getPageInfo().getHasMore());
                    response.setPageNum(request.getPageNum().intValue());
                    response.setDataList(dataList.stream().sorted((x1,x2) -> x2.getCreateTime().compareTo(x1.getCreateTime())).collect(Collectors.toList()));
                    response.setTotal(ordersResponse.getPageInfo().getTotalCount());
                }
            } else {
                log.warn("查询待集单列表为空 storeList:{}", storeList);
                response.setHasMore(false);
                response.setTotal(0);
                response.setPageNum(request.getPageNum().intValue());
                response.setDataList(new ArrayList<>());
            }
        } catch (Exception e) {
            log.error("查询待集单详情失败", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
        return CommonResponse.success(response);
    }

    private CommonResponse<WarehousePickCollectionQueryResponse> queryWarehouseCollectionFromEmpower(WarehousePickCollectionQueryRequest request) {

        WarehousePickCollectionQueryResponse response = new WarehousePickCollectionQueryResponse();
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        List<Long> storeList = identityInfo.getStoreIdList();
        long tenantId = identityInfo.getUser().getTenantId();

        SearchOutboundReq searchReq = new SearchOutboundReq();
        searchReq.setTenantId(tenantId);
        searchReq.setEntityidList(storeList);
        searchReq.setReceivePoiIdList(request.getPoiList());
        searchReq.setOutboundOrderStatusList(Lists.newArrayList(OutboundStatusEnum.INITIAL.getCode()));
        searchReq.setOutboundOrderTypeList(Lists.newArrayList(getOutboundType(request.getOrderTag())));
        searchReq.setPageParam(new PageParam(request.getPageNum().intValue(), request.getPageSize()));
        Optional.ofNullable(request.getSkuOwnerIdList()).ifPresent(searchReq::setSkuOwnerIdList);
        searchReq.setPushOrderStartTime(request.getPushOrderStartTime());
        searchReq.setPushOrderEndTime(request.getPushOrderEndTime());
        searchReq.setPlanProductStartTime(request.getPlanProductStartTime());
        searchReq.setPlanProductEndTime(request.getPlanProductEndTime());
        searchReq.setPlanArriveStartTime(request.getPlanArriveStartTime());
        searchReq.setPlanArriveEndTime(request.getPlanArriveEndTime());
        searchReq.setGridList(request.getGridList());

        try {
            log.info("调用outboundOrderThriftService.searchOrderList request:{}", JacksonUtils.toJson(searchReq));
            TPageResp<OutboundOrderInfo> resp = outboundOrderThriftService.searchOrderList(searchReq);
            log.info("调用outboundOrderThriftService.searchOrderList resp:{}", JacksonUtils.toJson(resp));
            if (ResultCodeEnum.SUCCESS.getCode() != resp.getCode()) {
                log.error("查询配销/采退待集单任务失败 resp:{}", JacksonUtils.toJson(resp));
                return CommonResponse.fail(ResultCode.FAIL.getCode(), resp.getMessage());
            }

            if (CollectionUtils.isEmpty(resp.getList())) {
                log.warn("查询待集单列表为空 storeList:{}", storeList);
                response.setHasMore(false);
                response.setTotal(0);
                response.setPageNum(request.getPageNum().intValue());
                response.setDataList(new ArrayList<>());
                return CommonResponse.success(response);
            }

//            Set<String> skuOwnerIdSet = resp.getList().stream().map(OutboundOrderInfo::getSkuOwnerId).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
//            Map<Long, String> tenantIdNameMap = this.getTenantIdNameMap(skuOwnerIdSet);

            List<WarehousePickCollectionModuleVO> dataList = resp.getList().stream().map(order -> {
                WarehousePickCollectionModuleVO pickCollectionModuleVO = new WarehousePickCollectionModuleVO();
                pickCollectionModuleVO.setOutWarehouseOrderNo(Optional.ofNullable(order.getSyncOutboundNo()).orElse(order.getOutboundOrderNo()));
                pickCollectionModuleVO.setPoiId(order.getReceivePoiId());
                pickCollectionModuleVO.setCreateTime(formatDate(order.getCreateTime()));
                pickCollectionModuleVO.setPushOrderTime(formatDate(order.getPushOrderTime()));
                // 历史单据兼容，上线前创建的出库单没有推单时间，需要赋值为创建时间
                if (StringUtils.isBlank(pickCollectionModuleVO.getPushOrderTime())) {
                    pickCollectionModuleVO.setPushOrderTime(pickCollectionModuleVO.getCreateTime());
                }
                pickCollectionModuleVO.setPlanProductTime(formatDate(order.getPlanProductTime()));
                pickCollectionModuleVO.setPlanArriveTime(formatDate(order.getPlanArriveTime()));
                pickCollectionModuleVO.setPoiName(order.getReceiveName());
//                Optional.ofNullable(order.getSkuOwnerId()).ifPresent(pickCollectionModuleVO::setSkuOwnerId);
//                Optional.ofNullable(order.getSkuOwnerId()).ifPresent(id->pickCollectionModuleVO.setSkuOwnerName(tenantIdNameMap.get(Long.valueOf(id))));
                List<GoodsOwnerVO> goodsOwnerVOList = order.getGoodsOwnerList().stream().map(i -> new GoodsOwnerVO(i.getGoodsOwnerId(), i.getGoodsOwnerName())).collect(Collectors.toList());
                pickCollectionModuleVO.setGoodsOwnerList(goodsOwnerVOList);

                if (OutboundTypeEnum.SALE_OUTBOUND.getCode() == order.getOutboundType()) {
                    if (OutboundSourceTypeEnum.DISTRIBUTION_SOURCE.getCode() == order.getOuterOrderType()) {
                        pickCollectionModuleVO.setType(DISTRIBUTION);
                    }else if (OutboundSourceTypeEnum.SALE_SOURCE.getCode() == order.getOuterOrderType()) {
                        pickCollectionModuleVO.setType(TO_B_SALL);
                    }
                }else {
                    pickCollectionModuleVO.setType(order.getOutboundSubType() != null ? order.getOutboundSubType():order.getOutboundType());
                }
                pickCollectionModuleVO.setTotalSkuKind(Long.valueOf(order.getExpectGoodsKind()));
                pickCollectionModuleVO.setTotalSkuCount(Long.valueOf(order.getExpectTotalQuantity()));
                pickCollectionModuleVO.setGridName(order.getGridName());
                pickCollectionModuleVO.setWaveOrderNo(order.getWaveOrderNo());
                pickCollectionModuleVO.setOutboundOrderStatus(order.getOutboundOrderStatus());
                return pickCollectionModuleVO;
            }).collect(Collectors.toList());

            response.setHasMore(resp.getPageResult().isHasMore());
            response.setPageNum(request.getPageNum().intValue());
            response.setDataList(dataList.stream().sorted((x1, x2) -> x2.getCreateTime().compareTo(x1.getCreateTime())).collect(Collectors.toList()));
            response.setTotal(resp.getPageResult().getTotalCount());
            return CommonResponse.success(response);
        } catch (Exception e) {
            log.error("查询配销/采退待集单详情失败", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
    }

    private String formatDate(Long time) {
        if (null == time) {
            return null;
        }

        SimpleDateFormat formater = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return formater.format(new Date(time));
    }

    private Integer getOutboundType(String orderTag) {
        Integer outboundType = null;
        if (OrderTagConstant.TAG_DISTRIBUTION.equals(orderTag)) {
            outboundType = OutboundTypeEnum.SALE_OUTBOUND.getCode();
        }
        if(OrderTagConstant.TAG_ALLOT.equals(orderTag)){
            outboundType = OutboundTypeEnum.ALLOT_OUTBOUND.getCode();
        }
        if(OrderTagConstant.TAG_PURCHASE.equals(orderTag)){
            outboundType = OutboundTypeEnum.PURCHASE_OUTBOUND.getCode();
        }
        return outboundType;
    }

    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public CommonResponse<WarehousePickQueryCountResponse> queryWarehouseCount(WarehousePickQueryCountRequest request) {
        WarehousePickQueryCountResponse response = new WarehousePickQueryCountResponse();

        long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        Long poiId = ApiMethodParamThreadLocal.getIdentityInfo().getStoreId();

        // 支持配销出库集单，需要根据单据标识决定走pickselect还是empower
        if ((MccConfigUtil.isDistributionOrderEnable() && OrderTagConstant.TAG_DISTRIBUTION.equals(request.getOrderTag())) ||
                (MccConfigUtil.isPurchaseOrderEnable() && OrderTagConstant.TAG_PURCHASE.equals(request.getOrderTag()))) {
            if (request.getOutWarehouseOrderNo().size() > 100) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), "集单操作合并出库单数量大于100");
            }

            try {
                int poiType = 0;
                PoiMapResponse poiResp = poiThriftService.queryTenantPoiInfoMapByPoiIds(Lists.newArrayList(poiId), tenantId);
                if (poiResp.getStatus().isSuccess() && MapUtils.isNotEmpty(poiResp.getPoiInfoMap())) {
                    poiType = poiResp.getPoiInfoMap().get(poiId) == null ? 0 : poiResp.getPoiInfoMap().get(poiId).getEntityType();
                }
                OutboundOrderQueryDetailsRequest req = new OutboundOrderQueryDetailsRequest();
                req.setStoreParam(new StoreParam(tenantId, poiId, poiType));
                req.setOutboundOrderNos(request.getOutWarehouseOrderNo());

                log.info("outboundDistributionOrderThriftService.queryOrderDetails request:{}", JacksonUtils.toJson(req));
                OutboundOrderQueryDetailsResp resp = outboundDistributionOrderThriftService.queryOrderDetails(req);
                log.info("outboundDistributionOrderThriftService.queryOrderDetails resp:{}", JacksonUtils.toJson(resp));
                if (ResultCodeEnum.SUCCESS.getCode() != resp.getCode()) {
                    log.error("查询配销待集单任务失败 resp:{}", JacksonUtils.toJson(resp));
                    return CommonResponse.fail(ResultCode.FAIL.getCode(), resp.getMessage());
                }

                if (CollectionUtils.isEmpty(resp.getOutboundOrders())) {
                    log.warn("查询待集单列表为空 storeList:{}", request.getOutWarehouseOrderNo());
                    WarehousePickCountVo vo = new WarehousePickCountVo();
                    vo.setGoodsCount(0);
                    vo.setSkuCount(0);
                    response.setCountList(vo);
                    return CommonResponse.success(response);
                }

                Set<String> skuIdSet = new HashSet<>();
                List<Integer> skuGoodsCount = new ArrayList<>();
                resp.getOutboundOrders().forEach(order -> {
                    if (CollectionUtils.isNotEmpty(order.getOutboundSkuInfo())) {
                        order.getOutboundSkuInfo().forEach(sku -> {
                            skuIdSet.add(sku.getGoodsInfo().getSkuID());
                            skuGoodsCount.add(sku.getExpectQuantity());
                        });
                    }
                });

                int goodsCount = skuGoodsCount.stream()
                        .filter(Objects::nonNull)
                        .mapToInt(Integer::intValue)
                        .sum();

                WarehousePickCountVo vo = new WarehousePickCountVo();
                vo.setGoodsCount(goodsCount);
                vo.setSkuCount(skuIdSet.size());
                response.setCountList(vo);
                return CommonResponse.success(response);
            } catch (Exception e) {
                log.error("查询配销待集单详情失败", e);
                return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
            }
        }

        List<AllocationOutWarehouseOrderQueryRequest> queryRequestList=new ArrayList<>();
        request.getOutWarehouseOrderNo().forEach(w->{
            AllocationOutWarehouseOrderQueryRequest queryRequest=new AllocationOutWarehouseOrderQueryRequest();
            queryRequest.setOutWarehouseId(poiId);
            queryRequest.setTenantId(tenantId);
            queryRequest.setOutWarehouseOrderNo(w);
            queryRequestList.add(queryRequest);
        });

        AllocationOutWarehouseOrderDetailsQueryRequest detailsQueryRequest = new AllocationOutWarehouseOrderDetailsQueryRequest(queryRequestList);


        try {
            log.info("调用allotOutWarehouseThriftService.queryOrderDetails request:{}", detailsQueryRequest);
            OutWarehouseOrderDetailsResponse detailsResponse = allotOutWarehouseThriftService.queryOrderDetails(detailsQueryRequest);
            log.info("调用allotOutWarehouseThriftService.queryOrderDetails request:{} response:{}", detailsQueryRequest, detailsResponse);
            if (!Status.SUCCESS.getCode().equals(detailsResponse.getStatus().getCode()) || detailsResponse == null) {
                log.warn("查询出库商品数量和类型任务失败 detailsResponse:{}", detailsResponse);
                return CommonResponse.fail(ResultCode.FAIL.getCode(), detailsResponse.getStatus().getMessage());
            }
            Set<String> sku = new HashSet<>();
            List<Integer> goodsCount = new ArrayList<>();
            List<OutWarehouseOrderDetailDTO> outWarehouseOrders = detailsResponse.getOutWarehouseOrders();
            if (CollectionUtils.isNotEmpty(outWarehouseOrders)) {
                outWarehouseOrders.forEach(order->{

                    List<OutWarehouseItemDTO> itemList=order.getOutWarehouseItems();
                    if(CollectionUtils.isNotEmpty(itemList)){
                        itemList.forEach(item ->{
                            sku.add(item.getSkuId());
                            int count = item.getPlanOutNum();
                            goodsCount.add(count);
                        });
                    }
                });
            }

            Integer goodsCountSum = goodsCount.stream().reduce(Integer::sum).orElse(0);

            WarehousePickCountVo countVo = new WarehousePickCountVo();
            countVo.setSkuCount(sku.size());
            countVo.setGoodsCount(goodsCountSum);
            response.setCountList(countVo);
        } catch (Exception e) {
            log.error("查询出库商品数量和类型任务失败", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
        return CommonResponse.success(response);
    }

    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public CommonResponse<WarehousePickTabNumResponse> queryWarehousePickTabNum(WarehousePickTabNumRequest request) {
        WarehousePickTabNumResponse response = new WarehousePickTabNumResponse();

        long accountId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId();
        long storeId = ApiMethodParamThreadLocal.getIdentityInfo().getStoreId();
        long tenantedId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();

        int poiType = 0;
        PoiMapResponse poiResp = poiThriftService.queryTenantPoiInfoMapByPoiIds(Lists.newArrayList(storeId), tenantedId);
        if (poiResp.getStatus().isSuccess() && MapUtils.isNotEmpty(poiResp.getPoiInfoMap())) {
            poiType = poiResp.getPoiInfoMap().get(storeId) == null ? 0 : poiResp.getPoiInfoMap().get(storeId).getEntityType();
        }
        log.info("当前POI类型: " + poiType);

        if (CollectionUtils.isNotEmpty(request.getTabTypeList())) {
            Set<Integer> tabSet = request.getTabTypeList().stream().collect(Collectors.toSet());
            PickTabNumRequest tabNumRequest = new PickTabNumRequest();
            tabNumRequest.setTabTypeList(tabSet);
            tabNumRequest.setTenantId(tenantedId);
            tabNumRequest.setEmpowerStoreId(storeId);
            tabNumRequest.setAccountId(accountId);
            try {
                log.info("调用warehouseThriftService.queryTabNum request:{}", tabNumRequest);
                PickTabNumResponse tabNumResponse = warehouseThriftService.queryTabNum(tabNumRequest);
                log.info("调用warehouseThriftService.queryTabNum request:{} response:{}", tabNumRequest, tabNumResponse);
                if (tabNumResponse.getStatus().getCode() != com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum.SUCCESS.getValue() || tabNumResponse == null) {
                    log.warn("查询tab数据数量失败 tabNumResponse:{}", tabNumResponse);
                    return CommonResponse.fail(ResultCode.FAIL.getCode(), tabNumResponse.getStatus().getMessage());
                }
                if (MapUtils.isNotEmpty(tabNumResponse.getTabNum())) {
                    response.setTabNumMap(tabNumResponse.getTabNum());
                }

                // 如果查询页签数量范围包含待集单，那么需要聚合配销待集单数量、采退待集单数量
                if (tabSet.contains(WavehouseCountEnum.WAIT_COLLECTION_COUNT.getValue())){
                    SearchOutboundReq searchReq = buildSearchOutboundReq(tenantedId, storeId);
                    int saleCount = 0;
                    int allocCount = response.getTabNumMap().getOrDefault(
                            WavehouseCountEnum.WAIT_COLLECTION_COUNT.getValue(), 0L).intValue();
                    TResp<Integer> resp;

                    Map<String, Integer> subTabNumMap = new HashMap<>();
                    subTabNumMap.put(TAB_NUM_ALLOCCOUNT, allocCount);
                    response.setSubTabNumMap(subTabNumMap);

                    if (MccConfigUtil.isDistributionOrderEnable()){
                        searchReq.setOutboundOrderTypeList(Lists.newArrayList(OutboundTypeEnum.SALE_OUTBOUND.getCode()));
                        log.info("调用outboundOrderThriftService.countOrderBySearch request:{}", JacksonUtils.toJson(searchReq));
                        resp = outboundOrderThriftService.countOrderBySearch(searchReq);
                        log.info("调用outboundOrderThriftService.countOrderBySearch resp:{}", JacksonUtils.toJson(resp));
                        if (ResultCodeEnum.SUCCESS.getCode() != resp.getCode() || resp.getData() == null) {
                            log.error("查询销售待集单任务失败 resp:{}", JacksonUtils.toJson(resp));
                            return CommonResponse.fail(ResultCode.FAIL.getCode(), resp.getMessage());
                        }
                        saleCount = resp.getData();
                        response.getTabNumMap().put(
                                WavehouseCountEnum.WAIT_COLLECTION_COUNT.getValue(), (long) (allocCount + saleCount));
                        subTabNumMap.put(TAB_NUM_SALECOUNT, saleCount);
                    }


                }

                TransferPickConfigQueryReq req=new TransferPickConfigQueryReq();
                req.setAccountId(accountId);
                req.setTenantId(tenantedId);
                req.setStoreIdList(Arrays.asList(storeId));
                TransferPickConfigQueryResponse configQueryResponse = storePickConfigThriftService.batchQueryTransferPickConfig(req);
                if(configQueryResponse!=null && CollectionUtils.isNotEmpty(configQueryResponse.getTransferPickConfigList())){
                    response.setPickTaskReceiveMode(configQueryResponse.getTransferPickConfigList().get(0).getPickTaskReceiveMode());
                }

            } catch (Exception e) {
                log.error("查询tab数据数量失败", e);
                return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
            }
        }
        return CommonResponse.success(response);
    }

    private SearchOutboundReq buildSearchOutboundReq(long tenantedId, long storeId) {
        SearchOutboundReq searchReq = new SearchOutboundReq();
        searchReq.setTenantId(tenantedId);
        searchReq.setEntityidList(Lists.newArrayList(storeId));
        searchReq.setOutboundOrderStatusList(Lists.newArrayList(OutboundStatusEnum.INITIAL.getCode()));
        searchReq.setPageParam(new PageParam(1, 1));
        return searchReq;
    }

    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public CommonResponse<WarehousePickWaitReceiveResponse> queryWarehouseWaitReceive(WarehousePickWaitReceiveRequest request) {
        WarehousePickWaitReceiveResponse response = new WarehousePickWaitReceiveResponse();
        long storeId = ApiMethodParamThreadLocal.getIdentityInfo().getStoreId();
        long tenantedId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        PickWaitReceiveRequest waitReceiveRequest = new PickWaitReceiveRequest();
        waitReceiveRequest.setTenantId(tenantedId);
        waitReceiveRequest.setEmpowerStoreId(storeId);
        //暂时不分页查询
        try{
            log.info("调用warehouseThriftService.queryWaitToReceive request:{}", waitReceiveRequest);
            PickWaitReceiveResponse waitReceiveResponse = warehouseThriftService.queryWaitToReceive(waitReceiveRequest);
            log.info("调用warehouseThriftService.queryWaitToReceive request:{} response:{}", waitReceiveRequest, waitReceiveResponse);
            if (waitReceiveResponse.getStatus().getCode() != com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum.SUCCESS.getValue()) {
                log.warn("查询待领取任务失败 waitReceiveResponse:{}", waitReceiveResponse);
                return CommonResponse.fail(ResultCode.FAIL.getCode(), waitReceiveResponse.getStatus().getMessage());
            }
            if (CollectionUtils.isNotEmpty(waitReceiveResponse.getDataList())) {
                List<WarehousePickModuleVO> dtoList = waitReceiveResponse.getDataList().stream().map(item -> {
                    WarehousePickModuleVO warehousePickModuleVO = new WarehousePickModuleVO();
                    warehousePickModuleVO.setCreateTime(item.getCreateTime());
                    warehousePickModuleVO.setWaveOrderId(item.getWareOrderId());
                    warehousePickModuleVO.setTaskList(buildByWarehousePickTaskModuleVo(item.getTaskList()));
                    warehousePickModuleVO.setGoodsOwnerInfoList(buildGoodsOwnerInfoVOList(item.getGoodsOwnerInfoList()));
                    warehousePickModuleVO.setReceiveStoreInfoList(buildReceiveStoreInfoList(item.getReceiveStoreInfoList()));
                    return warehousePickModuleVO;
                }).collect(Collectors.toList());
                response.setDataList(dtoList);
            } else {
                log.warn("查询待拣货列表为空 storeId:{} ", storeId);
                response.setDataList(new ArrayList<>());
            }
        } catch (Exception e) {
            log.error("查询待领取任务失败", e);
            CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
        return CommonResponse.success(response);
    }

    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public CommonResponse<WarehouseWaitPickQueryResponse> queryWarehouseWaitPick(WarehouseWaitPickQueryRequest request) {
        WarehouseWaitPickQueryResponse response = new WarehouseWaitPickQueryResponse();
        long storeId = ApiMethodParamThreadLocal.getIdentityInfo().getStoreId();
        long tenantedId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        PickWaitReceiveRequest waitReceiveRequest = new PickWaitReceiveRequest();
        waitReceiveRequest.setTenantId(tenantedId);
        waitReceiveRequest.setEmpowerStoreId(storeId);
        long accountId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId();
        waitReceiveRequest.setAccountId(accountId);
        //暂时不分页查询
        try{
            log.info("调用warehouseThriftService.queryWaitToReceive request:{}", waitReceiveRequest);
            PickWaitReceiveResponse waitReceiveResponse = warehouseThriftService.queryWaitToPike(waitReceiveRequest);
            log.info("调用warehouseThriftService.queryWaitToReceive request:{} response:{}", waitReceiveRequest, waitReceiveResponse);
            if (waitReceiveResponse.getStatus().getCode() != com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum.SUCCESS.getValue()) {
                log.warn("查询待拣货任务失败 waitReceiveResponse:{}", waitReceiveResponse);
                CommonResponse.fail(ResultCode.FAIL.getCode(), waitReceiveResponse.getStatus().getMessage());
            }
            if (CollectionUtils.isNotEmpty(waitReceiveResponse.getDataList())) {
                List<WarehousePickModuleVO> dtoList = waitReceiveResponse.getDataList().stream().map(item -> {
                    WarehousePickModuleVO warehousePickModuleVO = new WarehousePickModuleVO();
                    warehousePickModuleVO.setCreateTime(item.getCreateTime());
                    warehousePickModuleVO.setWaveOrderId(item.getWareOrderId());
                    warehousePickModuleVO.setTaskList(buildByWarehousePickTaskModuleVo(item.getTaskList()));
                    warehousePickModuleVO.setGoodsOwnerInfoList(buildGoodsOwnerInfoVOList(item.goodsOwnerInfoList));
                    warehousePickModuleVO.setReceiveStoreInfoList(buildReceiveStoreInfoList(item.getReceiveStoreInfoList()));

                    return warehousePickModuleVO;
                }).collect(Collectors.toList());

                response.setDataList(dtoList);
            } else {
                log.warn("查询待拣货列表为空 storeId:{} accountId:{}", storeId, accountId);
                response.setDataList(new ArrayList<>());
            }
        } catch (Exception e) {
            log.error("查询待拣货任务失败", e);
            CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }
        return CommonResponse.success(response);
    }


    private List<WarehousePickTaskModuleVO> buildByWarehousePickTaskModuleVo(List<PickTaskModuleVO> vo){

        return vo.stream().map(item -> {
            WarehousePickTaskModuleVO warehousePickTaskModuleVO =new WarehousePickTaskModuleVO();
            warehousePickTaskModuleVO.setTaskNo(Integer.parseInt(item.getTaskNo()));
            warehousePickTaskModuleVO.setOrderId(item.getTaskOrderId());
            warehousePickTaskModuleVO.setSkuNum(item.getSkuNum());
            warehousePickTaskModuleVO.setItemNum(item.getItemNum());
            return warehousePickTaskModuleVO;
        }).collect(Collectors.toList());

    }

    private List<WarehouseGoodsOwnerInfoVO> buildGoodsOwnerInfoVOList(List<WarehouseGoodsOwnerInfoDTO> goodsOwnerInfoList) {
        if (CollectionUtils.isEmpty(goodsOwnerInfoList)) {
            log.warn("buildGoodsOwnerInfoVOList, goodsOwnerInfoList is empty");
            return Collections.emptyList();
        }

        return goodsOwnerInfoList.stream().map(goodsOwnerInfo -> WarehouseGoodsOwnerInfoVO.builder()
                .goodsOwnerName(goodsOwnerInfo.getGoodsOwnerName())
                .goodsOwnerId(goodsOwnerInfo.getGoodsOwnerId())
                .build()).collect(Collectors.toList());
    }

    private List<WarehouseReceiveStoreInfoVO> buildReceiveStoreInfoList(List<WarehouseReceiveStoreInfoDTO> receiveStoreInfoList) {
        if (CollectionUtils.isEmpty(receiveStoreInfoList)) {
            log.warn("receiveStoreInfoList, receiveStoreInfoList is empty");
            return Collections.emptyList();
        }

        return receiveStoreInfoList.stream().map(receiveStoreInfo -> WarehouseReceiveStoreInfoVO.builder()
                .storeId(receiveStoreInfo.getStoreId())
                .storeName(receiveStoreInfo.getStoreName())
                .build()).collect(Collectors.toList());
    }

    public CommonResponse<WarehouseWaveTaskDetailResponse> queryWarehouseWaveTaskDetail(WarehouseWaveTaskDetailRequest request) {
        WavePickWaveTaskResponse response;
        Long accountId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId();
        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        Long storeId = ApiMethodParamThreadLocal.getIdentityInfo().getStoreId();

        try {
            response =
                    warehouseThriftService.queryWarehouseWaveTaskDetail(warehouseWaveTaskDetailRequestConverter.convert2ThriftRequest(request, accountId, storeId, tenantId));
        } catch (TException e) {
            log.error("查询拣货/分拣任务信息异常, request:{}", request, e);
            throw new CommonRuntimeException(e);
        }

        if (response.getStatus().getCode() != ResultCodeEnum.SUCCESS.getCode()) {
            log.warn("查询拣货/分拣任务信息出错, response: {}", response);
            throw new CommonRuntimeException(response.getStatus().getMessage());
        }
        return CommonResponse.success(warehouseWaveTaskDetailResponseConverter.convert2Response(response.getWavePickWaveTaskDTO()));
    }

    public CommonResponse<WarehousePickSkuDetailResponse> queryWarehousePickSkuDetail(WarehousePickSkuDetailRequest request) {
        WarehouseWavePickSkuDetailResponse response;
        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        Long accountId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId();
        Long storeId = ApiMethodParamThreadLocal.getIdentityInfo().getStoreId();
        try {
            response =
                    warehouseThriftService.queryWarehousePickSkuDetail(warehousePickSkuDetailRequestConverter.convert2ThriftRequest(request, tenantId, accountId, storeId));
        } catch (TException e) {
            log.error("查询波次拣货详情中的商品列表异常, request:{}", request, e);
            throw new CommonRuntimeException(e);
        }

        if (response.getStatus().getCode() != ResultCodeEnum.SUCCESS.getCode()) {
            log.warn("查询波次拣货详情中的商品列表出错, response: {}", response);
            throw new CommonRuntimeException(response.getStatus().getMessage());
        }
        return CommonResponse.success(warehousePickSkuDetailResponseConverter.convert2Response(response.getWarehouseWavePickSkuDTO()));
    }

    public CommonResponse<String> updateWarehouseContainerBox(WarehouseContainerBoxUpdateRequest request) {
        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        Long storeId = ApiMethodParamThreadLocal.getIdentityInfo().getStoreId();
        Long operatorId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId();
        WarehouseContainerBoxOperateResponse response;

        try {
            response = warehouseThriftService.operateWarehouseContainerBox(warehouseContainerBoxUpdateRequestConverter.convert2ThriftRequest(request, tenantId, storeId, operatorId));
            if (response.getStatus().getCode() == ResultCode.WAREHOUSE_CONTAINER_ALREADY_OCCUPIED.getCode()) {
                return CommonResponse.fail(ResultCode.WAREHOUSE_CONTAINER_ALREADY_OCCUPIED.getCode(),
                        ResultCode.WAREHOUSE_CONTAINER_ALREADY_OCCUPIED.getErrorMessage());
            }
            if (response.getStatus().getCode() == ResultCode.WAREHOUSE_CONTAINER_UNBOUND_ONLY_ONE.getCode()) {
                return CommonResponse.fail(ResultCode.WAREHOUSE_CONTAINER_UNBOUND_ONLY_ONE.getCode(),
                        ResultCode.WAREHOUSE_CONTAINER_UNBOUND_ONLY_ONE.getErrorMessage());
            }
            if (response.getStatus().getCode() == ResultCode.WAREHOUSE_CONTAINER_UNBOUND_PICK_DONE.getCode()) {
                return CommonResponse.fail(ResultCode.WAREHOUSE_CONTAINER_UNBOUND_PICK_DONE.getCode(),
                        ResultCode.WAREHOUSE_CONTAINER_UNBOUND_PICK_DONE.getErrorMessage());
            }
        } catch (TException e) {
            log.error("波次拣货更新容器信息异常, request:{}", request, e);
            throw new CommonRuntimeException(e);
        }

        int operateType = request.getOperateType();
        if (operateType == 0) {
            return new CommonResponse<>(ResultCode.WAREHOUSE_CONTAINER_BOUND_SUCCESS.getCode(),
                    ResultCode.WAREHOUSE_CONTAINER_BOUND_SUCCESS.getDefaultMessage(), StringUtils.EMPTY);
        } else {
            return new CommonResponse<>(ResultCode.WAREHOUSE_CONTAINER_UNBOUND_SUCCESS.getCode(),
                    ResultCode.WAREHOUSE_CONTAINER_UNBOUND_SUCCESS.getDefaultMessage(), StringUtils.EMPTY);
        }
    }

    public CommonResponse<WarehousePickCompleteQueryResponse> queryWarehousePickComplete(WarehousePickCompleteQueryRequest request) {
        WarehouseWaveQueryPickCompleteResponse response;
        Long accountId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId();
        Long storeId = ApiMethodParamThreadLocal.getIdentityInfo().getStoreId();

        try {
            response = warehouseThriftService.queryWarehouseCompletePickList(warehousePickCompleteQueryRequestConverter.convert2ThriftRequest(request, accountId, storeId));
        } catch (TException e) {
            log.error("查询波次已拣货列表异常, request:{}", request, e);
            throw new CommonRuntimeException(e);
        }

        if (response.getStatus().getCode() != ResultCodeEnum.SUCCESS.getCode()) {
            log.warn("查询波次已拣货列表出错, response: {}", response);
            throw new CommonRuntimeException(response.getStatus().getMessage());
        }

        List<WarehousePickCompleteModuleDTO> pickCompleteModuleDTOList = response.getWavePickCompleteDTO().getDataList();
        if (CollectionUtils.isEmpty(pickCompleteModuleDTOList)) {
            log.warn("查询波次已拣货列表为空, accountId: {}, storeId: {}", accountId, storeId);
            return CommonResponse.success(WarehousePickCompleteQueryResponse.builder().dataList(Lists.newArrayList()).build());
        } else {
            List<WarehousePickCompleteModuleVO> dataList = pickCompleteModuleDTOList.stream().map(data -> warehousePickCompleteModuleConverter.convert2Vo(data)).collect(Collectors.toList());
            return CommonResponse.success(WarehousePickCompleteQueryResponse.builder().dataList(dataList).build());
        }
    }

    public CommonResponse<WarehousePickCompleteQueryResponseV2> queryWarehousePickCompleteV2(WarehousePickCompleteQueryRequestV2 request) {
        WarehouseWaveQueryPickCompleteResponseV2 response;
        Long accountId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId();
        Long storeId = ApiMethodParamThreadLocal.getIdentityInfo().getStoreId();

        try {
            response = warehouseThriftService.queryWarehouseCompletePickListV2(warehousePickCompleteQueryRequestV2Converter.convert2ThriftRequest(request, accountId, storeId));
        } catch (TException e) {
            log.error("查询波次已拣货列表v2异常, request:{}", request, e);
            throw new CommonRuntimeException(e);
        }
        if (Objects.isNull(response) || Objects.isNull(response.getStatus()) || response.getStatus().getCode() != ResultCodeEnum.SUCCESS.getCode() || Objects.isNull(response.getWavePickCompleteDTOV2())) {
            log.warn("查询波次已拣货列表v2出错, response: {}", response);
            throw new CommonRuntimeException(response.getStatus().getMessage());
        }

        List<WarehousePickCompleteModuleDTO> pickCompleteModuleDTOList = response.getWavePickCompleteDTOV2().getDataList();
        List<WarehousePickCompleteModuleVO> dataList;
        if (CollectionUtils.isEmpty(pickCompleteModuleDTOList)) {
            dataList = Collections.emptyList();
        } else {
            dataList = pickCompleteModuleDTOList.stream().map(data -> warehousePickCompleteModuleConverter.convert2Vo(data)).collect(Collectors.toList());
        }
        WarehousePickCompleteQueryResponseV2 queryResponseV2 = WarehousePickCompleteQueryResponseV2.builder()
                .dataList(dataList)
                .hasMore(response.getWavePickCompleteDTOV2().getHasMore())
                .isNewVersion(response.getWavePickCompleteDTOV2().getIsNewVersion())
                .build();
        return CommonResponse.success(queryResponseV2);
    }

    public CommonResponse<WarehouseSeedWaitQueryResponse> queryWarehouseWaitSeedList(WarehouseSeedWaitQueryRequest request) {
        WarehouseWaveQuerySeedWaitResponse response;
        Long storeId = ApiMethodParamThreadLocal.getIdentityInfo().getStoreId();

        try {
            response =
                    warehouseThriftService.queryWarehouseSeedWaitList(warehouseSeedWaitQueryRequestConverter.convert2ThriftRequest(request, storeId));
        } catch (TException e) {
            log.error("查询波次待分拣列表异常, request:{}", request, e);
            throw new CommonRuntimeException(e);
        }

        if (response.getStatus().getCode() != ResultCodeEnum.SUCCESS.getCode()) {
            log.warn("查询波次待分拣列表出错, response: {}", response);
            throw new CommonRuntimeException(response.getStatus().getMessage());
        }

        List<WarehouseSeedWaitModuleDTO> seedWaitModuleDTOList = response.getWaveSeedWaitDTO().getDataList();
        if (CollectionUtils.isEmpty(seedWaitModuleDTOList)) {
            log.warn("查询波次待分拣列表为空, storeId: {}", storeId);
            return CommonResponse.success(WarehouseSeedWaitQueryResponse.builder().dataList(Lists.newArrayList()).build());
        } else {
            List<WarehouseSeedWaitModuleVO> dataList = seedWaitModuleDTOList.stream().map(data -> warehouseSeedWaitModuleConverter.convert2Response(data)).collect(Collectors.toList());
            return CommonResponse.success(WarehouseSeedWaitQueryResponse.builder().dataList(dataList).build());
        }
    }

    public CommonResponse<WarehouseSeedSkuDetailResponse> queryWarehouseSeedSkuDetail(WarehouseSeedSkuDetailRequest request) {
        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        Long accountId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId();
        Long storeId = ApiMethodParamThreadLocal.getIdentityInfo().getStoreId();
        WarehouseWaveSeedSkuDetailResponse response;

        try {
            response =
                    warehouseThriftService.queryWarehouseSeedSkuDetail(warehouseSeedSkuDetailRequestConverter.convert2ThriftRequest(request, tenantId, accountId, storeId));
        } catch (TException e) {
            log.error("查询波次分拣详情中的商品列表异常, request:{}", request, e);
            throw new CommonRuntimeException(e);
        }

        if (response.getStatus().getCode() != ResultCodeEnum.SUCCESS.getCode()) {
            log.warn("查询波次分拣详情中的商品列表出错, response: {}", response);
            throw new CommonRuntimeException(response.getStatus().getMessage());
        }
        WarehouseSeedSkuDetailResponse seedSkuDetailResponse = warehouseSeedSkuDetailResponseConverter.convert2Response(response.getWarehouseWaveSeedSkuDTO());
        if(seedSkuDetailResponse != null && CollectionUtils.isNotEmpty(seedSkuDetailResponse.getDataList())){
            Set<Long> accountIdSet = seedSkuDetailResponse.getDataList().stream().filter(new Predicate<WarehouseSeedSkuItemVO>() {
                @Override
                public boolean test(WarehouseSeedSkuItemVO warehouseSeedSkuItemVO) {
                    return warehouseSeedSkuItemVO.getPickedAccountId()!=null && warehouseSeedSkuItemVO.getPickedAccountId()>0;
                }
            }).map(WarehouseSeedSkuItemVO::getPickedAccountId).collect(Collectors.toSet());
            if(CollectionUtils.isNotEmpty(accountIdSet)){
                Map<Long, AccountUserDto> userDtoMap = accountService.queryTenantUserByAccountIds(tenantId,new ArrayList<>(accountIdSet));
                if(MapUtils.isNotEmpty(userDtoMap)){
                    seedSkuDetailResponse.getDataList().forEach(d->{
                        if(d.getPickedAccountId()==null){
                            return;
                        }
                        if(d.getPickedAccountId() == 0){
                            d.setPickedOperateName(DEFAULT_NAME);
                        }
                        if(userDtoMap.containsKey(d.getPickedAccountId())){
                            d.setPickedOperateName(userDtoMap.get(d.getPickedAccountId()).getOperateName());
                        }
                    });
                }
            }
        }

        return CommonResponse.success(seedSkuDetailResponse);
    }

    public CommonResponse<WarehouseSeedFuzzySkuResponse> queryWarehouseSeedFuzzySku(WarehouseSeedFuzzySkuRequest request) {
        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        Long accountId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId();
        Long storeId = ApiMethodParamThreadLocal.getIdentityInfo().getStoreId();
        WarehouseFuzzySeedSkuResponse response;
        try {
            response =
                    warehouseThriftService.queryWarehouseSeedFuzzySku(warehouseSeedFuzzySkuRequestConverter.convert2ThriftRequest(request, tenantId, accountId, storeId));
        } catch (TException e) {
            log.error("模糊查询波次分拣详情中的商品列表异常, request:{}", request, e);
            throw new CommonRuntimeException(e);
        }

        if (response.getStatus().getCode() != ResultCodeEnum.SUCCESS.getCode()) {
            log.warn("模糊查询波次分拣详情中的商品列表出错, response: {}", response);
            throw new CommonRuntimeException(response.getStatus().getMessage());
        }

        if (Objects.isNull(response.getWarehouseSeedFuzzySkuDTO())) {
            log.warn("模糊查询分拣详情中的商品列表为空, waveId: {}, taskId: {}, condition: {}", request.getWaveId(),
                    request.getTaskId(), request.getCondition());
            return CommonResponse.success(WarehouseSeedFuzzySkuResponse.builder().hasMore(false).dataList(Collections.emptyList()).build());
        } else {

            WarehouseSeedFuzzySkuResponse fuzzySkuResponse = warehouseSeedFuzzySkuResponseConverter.convert2Response(response.getWarehouseSeedFuzzySkuDTO());
            if(fuzzySkuResponse != null && CollectionUtils.isNotEmpty(fuzzySkuResponse.getDataList())){
                Set<Long> accountIdSet = fuzzySkuResponse.getDataList().stream().filter(new Predicate<WarehouseSeedSkuItemVO>() {
                    @Override
                    public boolean test(WarehouseSeedSkuItemVO warehouseSeedSkuItemVO) {
                        return warehouseSeedSkuItemVO.getPickedAccountId()!=null && warehouseSeedSkuItemVO.getPickedAccountId()>0;
                    }
                }).map(WarehouseSeedSkuItemVO::getPickedAccountId).collect(Collectors.toSet());
                if(CollectionUtils.isNotEmpty(accountIdSet)){
                    Map<Long, AccountUserDto> userDtoMap = accountService.queryTenantUserByAccountIds(tenantId,new ArrayList<>(accountIdSet));
                    if(MapUtils.isNotEmpty(userDtoMap)){
                        fuzzySkuResponse.getDataList().forEach(d->{
                            if(d.getPickedAccountId()==null){
                                return;
                            }
                            if(d.getPickedAccountId() == 0){
                                d.setPickedOperateName(DEFAULT_NAME);
                            }
                            if(userDtoMap.containsKey(d.getPickedAccountId())){
                                d.setPickedOperateName(userDtoMap.get(d.getPickedAccountId()).getOperateName());
                            }
                        });
                    }
                }
            }
            return CommonResponse.success(fuzzySkuResponse);
        }
    }

    public CommonResponse<WarehousePickPackSeedCompleteDetailResponse> queryWarehouseSeedFuzzySkuForPackComplete(WarehouseSeedFuzzySkuRequest request) {
        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        Long accountId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId();
        Long storeId = ApiMethodParamThreadLocal.getIdentityInfo().getStoreId();
        WarehousePackSeedCompleteDetailResponse response;
        try {
            response =
                    warehouseThriftService.queryWarehouseCompleteSeedFuzzySku(warehouseSeedFuzzySkuRequestConverter.convert2ThriftRequest(request, tenantId, accountId, storeId));
        } catch (TException e) {
            log.error("模糊查询已分拣详情中的商品列表异常, request:{}", request, e);
            throw new CommonRuntimeException(e);
        }

        if (response.getStatus().getCode() != ResultCodeEnum.SUCCESS.getCode()) {
            log.warn("模糊查询已分拣详情中的商品列表出错, response: {}", response);
            throw new CommonRuntimeException(response.getStatus().getMessage());
        }

        if (Objects.isNull(response.getWarehouseWaveSeedCompletePackDTO())) {
            log.warn("模糊查询已分拣详情中的商品列表为空, waveId: {}, taskId: {}, condition: {}", request.getWaveId(),
                    request.getTaskId(), request.getCondition());
            return CommonResponse.success(new WarehousePickPackSeedCompleteDetailResponse());
        } else {
            WarehousePickPackSeedCompleteDetailResponse packSeedCompleteDetailResponse = convert2warehousePickPackSeedCompleteDetailResponse(response.getWarehouseWaveSeedCompletePackDTO());
            if (Objects.nonNull(packSeedCompleteDetailResponse.getDataList()) && CollectionUtils.isNotEmpty(packSeedCompleteDetailResponse.getDataList())) {
                List<WarehousePackItemVO> itemList = packSeedCompleteDetailResponse.getDataList().stream().map(PickPackSeedCompleteDetailVO::getItemList).flatMap(Collection::stream).collect(Collectors.toList());
                Set<Long> accountIdSet = itemList.stream().filter(warehousePackItemVO -> warehousePackItemVO.getPickedAccountId() != null && warehousePackItemVO.getPickedAccountId() > 0).map(WarehousePackItemVO::getPickedAccountId).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(accountIdSet)) {
                    Map<Long, AccountUserDto> userDtoMap = accountService.queryTenantUserByAccountIds(tenantId, new ArrayList<>(accountIdSet));
                    if (MapUtils.isNotEmpty(userDtoMap)) {
                        itemList.forEach(data -> {
                            if (data.getPickedAccountId() == null) {
                                return;
                            }
                            if (data.getPickedAccountId() == 0) {
                                data.setPickedOperateName(DEFAULT_NAME);
                            }
                            if (userDtoMap.containsKey(data.getPickedAccountId())) {
                                data.setPickedOperateName(userDtoMap.get(data.getPickedAccountId()).getOperateName());
                            }
                        });
                    }
                }
            }
            return CommonResponse.success(packSeedCompleteDetailResponse);
        }
    }

    public CommonResponse<WarehousePickDetailFuzzyResponse> queryWarehousePickDetailFuzzy(WarehousePickDetailFuzzyRequest request) {
        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        Long accountId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId();
        Long storeId = ApiMethodParamThreadLocal.getIdentityInfo().getStoreId();
        WarehouseFuzzyPickDetailResponse response;
        try {
            response = warehouseThriftService.queryWarehousePickDetailFuzzy(warehousePickDetailFuzzyRequestConverter.convert2ThriftRequest(request, tenantId, accountId, storeId));
        } catch (TException e) {
            log.error("模糊查询波次拣货详情中的商品列表异常, request:{}", request, e);
            throw new CommonRuntimeException(e);
        }

        if (response.getStatus().getCode() != ResultCodeEnum.SUCCESS.getCode()) {
            log.warn("模糊查询波次拣货详情中的商品列表出错, response: {}", response);
            throw new CommonRuntimeException(response.getStatus().getMessage());
        }

        if (Objects.isNull(response.getWarehousePickDetailFuzzyDTO())) {
            log.warn("模糊查询拣货详情中的商品列表为空, waveId: {}, taskId: {}, condition: {}", request.getWaveId(),
                    request.getTaskId(), request.getCondition());
            return CommonResponse.success(WarehousePickDetailFuzzyResponse.builder().hasMore(false).dataList(Collections.emptyList()).build());
        } else {
            return CommonResponse.success(warehousePickDetailFuzzyResponseConverter.convert2Response(response.getWarehousePickDetailFuzzyDTO()));
        }
    }

    public CommonResponse<WarehouseSeedCompleteQueryResponse> queryWarehouseSeedComplete(WarehouseSeedCompleteQueryRequest request) {
        WarehouseWaveQuerySeedCompleteResponse response;
        Long accountId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId();
        Long storeId = ApiMethodParamThreadLocal.getIdentityInfo().getStoreId();

        try {
            response =
                    warehouseThriftService.queryWarehouseSeedCompleteList(warehouseSeedCompleteQueryRequestConverter.convert2ThriftRequest(request, accountId, storeId));
        } catch (TException e) {
            log.error("查询波次已分拣列表异常, request:{}", request, e);
            throw new CommonRuntimeException(e);
        }

        if (response.getStatus().getCode() != ResultCodeEnum.SUCCESS.getCode()) {
            log.warn("查询波次已分拣列表出错, response: {}", response);
            throw new CommonRuntimeException(response.getStatus().getMessage());
        }

        List<WarehouseSeedCompleteModuleDTO> warehouseSeedCompleteModuleDTOList = response.getWaveSeedCompleteDTO().getDataList();
        if (CollectionUtils.isEmpty(warehouseSeedCompleteModuleDTOList)) {
            log.warn("查询波次已分拣列表为空, accountId: {}, storeId: {}", accountId, storeId);
            return CommonResponse.success(WarehouseSeedCompleteQueryResponse.builder().dataList(Lists.newArrayList()).build());
        } else {
            List<WarehouseSeedCompleteModuleVO> dataList = warehouseSeedCompleteModuleDTOList.stream().map(data -> warehouseSeedCompleteModuleConverter.convert2Response(data)).collect(Collectors.toList());
            return CommonResponse.success(WarehouseSeedCompleteQueryResponse.builder().dataList(dataList).build());
        }
    }

    public WarehousePickPoiInfoResponse getPoiInfo() throws TException {
        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        Long accountId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId();
        Long storeId = ApiMethodParamThreadLocal.getIdentityInfo().getStoreId();
        TransferPickConfigQueryReq req=new TransferPickConfigQueryReq();
        req.setAccountId(accountId);
        req.setTenantId(tenantId);
        req.setStoreIdList(Arrays.asList(storeId));
        WarehousePickPoiInfoResponse poiInfoResponse=new WarehousePickPoiInfoResponse();
        poiInfoResponse.setPoiInfoVOList(new ArrayList<>());
        TransferPickConfigQueryResponse response = storePickConfigThriftService.batchQueryTransferPickConfig(req);
        if(response==null || CollectionUtils.isEmpty(response.getTransferPickConfigList())){
            return poiInfoResponse;
        }
        List<StoreSortInfo> storeSortInfoList=new ArrayList<>();
        response.getTransferPickConfigList().forEach(d->{
            if(CollectionUtils.isNotEmpty(d.getPickStoreSortList())){
                storeSortInfoList.addAll(d.getPickStoreSortList());
            }
        });
        if(CollectionUtils.isEmpty(storeSortInfoList)){
            return poiInfoResponse;
        }
        List<WarehousePoiInfoVO> poiInfoVOList = storeSortInfoList.stream().map(new Function<StoreSortInfo, WarehousePoiInfoVO>() {
            @Override
            public WarehousePoiInfoVO apply(StoreSortInfo storeSortInfo) {
                return new WarehousePoiInfoVO(storeSortInfo.getStoreId(),storeSortInfo.getStoreName());
            }
        }).distinct().collect(Collectors.toList());
        poiInfoResponse.setPoiInfoVOList(poiInfoVOList);
        return poiInfoResponse;
    }

    /**
     * 支持跨租户收货方的查询
     * 已适配多货主的调拨单跨租户查询的场景
     * @return
     * @throws TException
     */
    public WarehousePickPoiInfoResponse getPoiInfoCrossTenant(String orderTag, Integer outboundOrderStatus) throws TException {
        WarehousePickPoiInfoResponse poiInfoResponse = new WarehousePickPoiInfoResponse();
        poiInfoResponse.setPoiInfoVOList(new ArrayList<>());

        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        Long storeId = ApiMethodParamThreadLocal.getIdentityInfo().getStoreId();

        StoreParam req = new StoreParam();
        req.setTenantId(tenantId);
        req.setEntityId(storeId);
        req.setEntityType(0);
        OutboundOrderQueryReceiverResp resp;
        List<Integer> outboundStatusList = Objects.isNull(outboundOrderStatus) ? new ArrayList<>() : Arrays.asList(outboundOrderStatus);

        switch (orderTag){
            case OrderTagConstant.TAG_DISTRIBUTION:
                resp = outboundDistributionOrderThriftService.queryOutboundOrderReceiverList(req,OutboundTypeEnum.SALE_OUTBOUND.getCode(),outboundStatusList);
                break;
            case OrderTagConstant.TAG_PURCHASE:
                resp = outboundDistributionOrderThriftService.queryOutboundOrderReceiverList(req,OutboundTypeEnum.PURCHASE_OUTBOUND.getCode(),outboundStatusList);
                break;
            default:
                resp = outboundDistributionOrderThriftService.queryOutboundOrderReceiverList(req,OutboundTypeEnum.ALLOT_OUTBOUND.getCode(),outboundStatusList);
        }

        if (resp == null || resp.getCode() != ResultCodeEnum.SUCCESS.getCode()) {
            log.error("queryOutboundOrderReceiverList error： " + JacksonUtils.toJson(resp));
            return poiInfoResponse;
        }
        if (CollectionUtils.isEmpty(resp.getPoiInfoVOList())) {
            return poiInfoResponse;
        }

        List<WarehousePoiInfoVO> poiList = new ArrayList<>();
        resp.getPoiInfoVOList().forEach(p -> {
            poiList.add(new WarehousePoiInfoVO(p.getStoreId(), p.getStoreName()));
        });
        poiInfoResponse.setPoiInfoVOList(poiList);
        return poiInfoResponse;
    }

    public CommonResponse<String> updateWarehousePickHuCode(WarehousePickHuCodeRequest request) {
        WarehouseHuOperateResponse response;
        try {
            response = warehouseOperateThriftService.operateWarehouseHu(convert2WarehouseHuOperateRequest(request));
            if (!response.getStatus().getCode().equals(Status.SUCCESS.getCode())) {
                return CommonResponse.fail(response.getStatus().getCode(), response.getStatus().getMessage());
            }
        } catch (TException e) {
            log.error("Hu解绑定异常, request:{}", request, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }

        return CommonResponse.success(response.getStatus().getMessage());
    }

    public CommonResponse<String> replaceWarehouseSeedHuCode(WarehouseSeedHuReplaceRequest request) {
        WarehouseHuOperateResponse response;
        try {
            response = warehouseOperateThriftService.replaceWarehouseHu(convert2WarehouseHuReplaceRequest(request));
            if (!response.getStatus().getCode().equals(Status.SUCCESS.getCode())) {
                return CommonResponse.fail(response.getStatus().getCode(), response.getStatus().getMessage());
            }
        } catch (TException e) {
            log.error("转箱异常, request:{}", request, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }

        return CommonResponse.success(response.getStatus().getMessage());
    }

    public CommonResponse<String> updateWarehousePickPackComplete(WarehousePickPackCompleteRequest request) {
        Long storeId = ApiMethodParamThreadLocal.getIdentityInfo().getStoreId();
        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        WarehousePackCompleteResponse response;
        WarehousePackCompleteRequest req = new WarehousePackCompleteRequest();
        req.setEmpowerStoreId(storeId);
        req.setTenantId(tenantId);
        req.setOperator(convert2WarehouseOperatorDTO(ApiMethodParamThreadLocal.getIdentityInfo().getUser()));
        req.setSkuId(request.getSkuId());
        req.setHuCode(request.getHuCode());
        req.setPackCompleteNum(request.getPackNum());
        req.setPickTaskId(request.getPickTaskId());
        req.setOrderId(request.getOrderId());
        try {
            response = warehouseOperateThriftService.packComplete(req);
            if (!response.getStatus().getCode().equals(Status.SUCCESS.getCode())) {
                return CommonResponse.fail(response.getStatus().getCode(), response.getStatus().getMessage());
            }
        } catch (TException e) {
            log.error("操作装箱完成异常, request:{}", request, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }

        return CommonResponse.success(response.getStatus().getMessage());
    }

    public CommonResponse<String> updateWarehousePickPackCancel(WarehousePickPackCancelRequest request) {
        Long storeId = ApiMethodParamThreadLocal.getIdentityInfo().getStoreId();
        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();

        WarehousePackCancelResponse response;
        WarehousePackCancelRequest req = new WarehousePackCancelRequest();
        req.setTenantId(tenantId);
        req.setEmpowerStoreId(storeId);
        req.setOperator(convert2WarehouseOperatorDTO(ApiMethodParamThreadLocal.getIdentityInfo().getUser()));
        req.setPickTaskId(request.getPickTaskId());
        req.setSkuId(request.getSkuId());
        req.setPackCancelNum(request.getPackNum());
        req.setHuCode(request.getHuCode());
        req.setOrderId(request.getOrderId());
        try {
            response = warehouseOperateThriftService.packCancel(req);
            if (!response.getStatus().getCode().equals(Status.SUCCESS.getCode())) {
                return CommonResponse.fail(response.getStatus().getCode(), response.getStatus().getMessage());
            }
        } catch (TException e) {
            log.error("操作取消装箱异常, request:{}", request, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }

        return CommonResponse.success(response.getStatus().getMessage());
    }

    public CommonResponse<String> updateWarehousePickPackFull(WarehousePickPackFullRequest request) {
        Long storeId = ApiMethodParamThreadLocal.getIdentityInfo().getStoreId();
        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        WarehousePackFullResponse response;
        WarehousePackFullRequest req = new WarehousePackFullRequest();
        req.setTenantId(tenantId);
        req.setEmpowerStoreId(storeId);
        req.setOperator(convert2WarehouseOperatorDTO(ApiMethodParamThreadLocal.getIdentityInfo().getUser()));
        req.setOrderId(request.getOrderId());
        req.setHuCode(request.getHuCode());
        try {
            response = warehouseOperateThriftService.packFull(req);
            if (!response.getStatus().getCode().equals(Status.SUCCESS.getCode())) {
                return CommonResponse.fail(response.getStatus().getCode(), response.getStatus().getMessage());
            }
        } catch (TException e) {
            log.error("操作装箱满箱异常, request:{}", request, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }

        return CommonResponse.success(response.getStatus().getMessage());
    }

    public CommonResponse<WarehousePickPackSeedWaitDetailResponse> queryWarehousePickPackSeedWaitDetail(WarehousePickPackSeedWaitDetailRequest request) {
        WarehousePackSeedWaitDetailResponse rpcResponse;
        Long accountId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId();
        Long storeId = ApiMethodParamThreadLocal.getIdentityInfo().getStoreId();
        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        WarehousePackSeedWaitDetailRequest req = new WarehousePackSeedWaitDetailRequest();
        req.setAccountId(accountId);
        req.setWaveId(request.getWaveId());
        req.setTaskId(ConverterUtils.StringToInteger(request.getTaskId()));
        req.setPageNo(request.getPageNo());
        req.setPageSize(request.getPageSize());
        req.setTenantId(tenantId);
        req.setStoreId(storeId);
        try {
            rpcResponse = warehouseThriftService.queryWarehousePackSeedWaitDetail(req);
            if (!rpcResponse.getStatus().getCode().equals(Status.SUCCESS.getCode())) {
                return CommonResponse.fail(rpcResponse.getStatus().getCode(), rpcResponse.getStatus().getMessage());
            }
        } catch (TException e) {
            log.error("查询待分拣详情异常, request:{}", request, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }

        WarehousePickPackSeedWaitDetailResponse response = convert2WarehousePickPackSeedWaitDetailResponse(rpcResponse.getWarehouseWaveSeedWaitPackDTO());
        if (Objects.nonNull(response.getDataList()) && CollectionUtils.isNotEmpty(response.getDataList())) {
            Set<Long> accountIdSet = response.getDataList().stream().filter(pickPackSeedWaitDetailVO -> pickPackSeedWaitDetailVO.getPickedAccountId() != null && pickPackSeedWaitDetailVO.getPickedAccountId() > 0).map(PickPackSeedWaitDetailVO::getPickedAccountId).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(accountIdSet)) {
                Map<Long, AccountUserDto> userDtoMap = accountService.queryTenantUserByAccountIds(tenantId, new ArrayList<>(accountIdSet));
                if (MapUtils.isNotEmpty(userDtoMap)) {
                    response.getDataList().forEach(data -> {
                        if (data.getPickedAccountId() == null) {
                            return;
                        }
                        if (data.getPickedAccountId() == 0) {
                            data.setPickedOperateName(DEFAULT_NAME);
                        }
                        if (userDtoMap.containsKey(data.getPickedAccountId())) {
                            data.setPickedOperateName(userDtoMap.get(data.getPickedAccountId()).getOperateName());
                        }
                    });
                }
            }
        }

        return CommonResponse.success(response);
    }

    public CommonResponse<WarehousePickPackSeedCompleteDetailResponse> queryWarehousePickPackSeedCompleteDetail(WarehousePickPackSeedCompleteDetailRequest request) {
        WarehousePackSeedCompleteDetailResponse rpcResponse;
        Long accountId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId();
        Long storeId = ApiMethodParamThreadLocal.getIdentityInfo().getStoreId();
        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        WarehousePackSeedCompleteDetailRequest req = new WarehousePackSeedCompleteDetailRequest();
        if (request == null){
            log.error("查询已分拣详情异常");
            throw new CommonRuntimeException("查询已分拣详情异常");
        }
        req.setAccountId(accountId);
        req.setTenantId(tenantId);
        req.setStoreId(storeId);
        req.setWaveId(request.getWaveId());
        req.setTaskId(ConverterUtils.StringToInteger(request.getTaskId()));
        req.setPageNo(request.getPageNo());
        req.setPageSize(request.getPageSize());
        try {
            rpcResponse = warehouseThriftService.queryWarehousePackSeedCompleteDetail(req);
            if (!rpcResponse.getStatus().getCode().equals(Status.SUCCESS.getCode())) {
                return CommonResponse.fail(rpcResponse.getStatus().getCode(), rpcResponse.getStatus().getMessage());
            }
        } catch (TException e) {
            log.error("查询已分拣详情异常, request:{}", request, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }

        WarehousePickPackSeedCompleteDetailResponse response = convert2warehousePickPackSeedCompleteDetailResponse(rpcResponse.getWarehouseWaveSeedCompletePackDTO());
        if (Objects.nonNull(response.getDataList()) && CollectionUtils.isNotEmpty(response.getDataList())) {
            List<WarehousePackItemVO> itemList = response.getDataList().stream().map(PickPackSeedCompleteDetailVO::getItemList).flatMap(Collection::stream).collect(Collectors.toList());
            Set<Long> accountIdSet = itemList.stream().filter(warehousePackItemVO -> warehousePackItemVO.getPickedAccountId() != null && warehousePackItemVO.getPickedAccountId() > 0).map(WarehousePackItemVO::getPickedAccountId).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(accountIdSet)) {
                Map<Long, AccountUserDto> userDtoMap = accountService.queryTenantUserByAccountIds(tenantId, new ArrayList<>(accountIdSet));
                if (MapUtils.isNotEmpty(userDtoMap)) {
                    itemList.forEach(data -> {
                        if (data.getPickedAccountId() == null) {
                            return;
                        }
                        if (data.getPickedAccountId() == 0) {
                            data.setPickedOperateName(DEFAULT_NAME);
                        }
                        if (userDtoMap.containsKey(data.getPickedAccountId())) {
                            data.setPickedOperateName(userDtoMap.get(data.getPickedAccountId()).getOperateName());
                        }
                    });
                }
            }
        }

        return CommonResponse.success(response);
    }

    private WarehouseOperatorDTO convert2WarehouseOperatorDTO(User user) {
        if (user == null) {
            return null;
        }
        WarehouseOperatorDTO warehouseOperatorDTO = new WarehouseOperatorDTO();
        warehouseOperatorDTO.setAccountId(user.getAccountId());
        warehouseOperatorDTO.setAccountName(user.getAccountName());
        warehouseOperatorDTO.setEmployeeName(user.getOperatorName());
        warehouseOperatorDTO.setEmployeeId(user.getEmployeeId());
        return warehouseOperatorDTO;
    }

    private WarehousePickPackSeedWaitDetailResponse convert2WarehousePickPackSeedWaitDetailResponse(WarehouseWaveSeedWaitPackDTO dto) {
        if (dto == null) {
            return null;
        }
        WarehousePickPackSeedWaitDetailResponse response = new WarehousePickPackSeedWaitDetailResponse();
        try {
            response.setHasMore(dto.getHasMore());
            if (CollectionUtils.isNotEmpty(dto.getDataList())) {
                response.setDataList(dto.getDataList().stream().map(item -> {
                    if (null != item){
                        PickPackSeedWaitDetailVO vo = new PickPackSeedWaitDetailVO();
                        vo.setItemName(item.getItemName());
                        vo.setSkuId(item.getSkuId());
                        vo.setSpec(item.getSpec());
                        vo.setCategoryName(item.getCategoryName());
                        vo.setPicList(item.getPicList());
                        vo.setUpcList(item.getUpcList());
                        vo.setPackNum(item.getPackNum());
                        vo.setPickTaskId(item.getPickTaskId());
                        vo.setSeedNum(item.getSeedNum());
                        vo.setPickedAccountId(item.getPickedAccountId());
                        PickPackingSpecVO specVO = convert2PickPackingSpecVO(item.getPickPackingSpec());
                        vo.setPickPackingSpec(specVO);
                        vo.setBasicUnit(item.getBasicUnit());
                        return vo;
                    }
                    return null;
                }).collect(Collectors.toList()));
            }else {
                response.setDataList(new ArrayList<>());
            }

            if (Objects.isNull(dto.getSeedProgress())){
                response.setSeedProgress(null);
            }else {
                SeedProgressVO vo = convert2SeedProgressVO(dto.getSeedProgress());
                response.setSeedProgress(vo);
            }
        } catch (Exception e){
            log.error("convert2WarehousePickPackSeedWaitDetailResponse error", e);
        }

        return response;
    }

    private WarehousePickPackSeedCompleteDetailResponse convert2warehousePickPackSeedCompleteDetailResponse(WarehouseWaveSeedCompletePackDTO dto) {
        if (dto == null) {
            return null;
        }
        WarehousePickPackSeedCompleteDetailResponse response = new WarehousePickPackSeedCompleteDetailResponse();
        try {
            response.setHasMore(dto.getHasMore());
            List<PickPackSeedCompleteDetailVO> dataList = convert2PackSeedCompleteDetailVoList(dto.getDataList());
            response.setDataList(dataList);

            if (Objects.isNull(dto.getSeedProgress())){
                response.setSeedProgress(null);
            }else {
                SeedProgressVO vo = convert2SeedProgressVO(dto.getSeedProgress());
                response.setSeedProgress(vo);
            }
        } catch (Exception e){
            log.error("convert2warehousePickPackSeedCompleteDetailResponse error", e);
        }
        return response;
    }

    private PickPackingSpecVO convert2PickPackingSpecVO(SkuPackingSpecDTO dto) {
        if (dto == null) {
            return null;
        }
        return PickPackingSpecVO.builder().packingSpecRatio(dto.getPackingSpecRatio()).packingSpecUnit(dto.packingSpecUnit).build();
    }
    private SeedProgressVO convert2SeedProgressVO(WarehouseWaveProgressDTO dto) {
        if (dto == null){
            return null;
        }
       return SeedProgressVO.builder().actualNum(dto.getActualNum()).actualSkuNum(dto.getActualSkuNum()).needSkuNum(dto.getNeedSkuNum()).needNum(dto.getNeedNum()).build();
    }

    private List<PickPackSeedCompleteDetailVO> convert2PackSeedCompleteDetailVoList(List<WarehouseWaveSeedCompletePackHuDTO> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return Collections.emptyList();
        }

        return dataList.stream().map(data -> PickPackSeedCompleteDetailVO.builder().huCode(data.getHuCode()).itemList(convert2WarehousePackItemVO(data.getSkuDTOList())).build()).collect(Collectors.toList());
    }

    private List<WarehousePackItemVO> convert2WarehousePackItemVO(List<WarehouseWaveSeedCompletePackSkuDTO> skuDTOList) {
        if (CollectionUtils.isEmpty(skuDTOList)) {
            return Collections.emptyList();
        }

        return skuDTOList.stream().map(skuDTO -> WarehousePackItemVO.builder()
                .itemName(skuDTO.getItemName())
                .skuId(skuDTO.getSkuId())
                .spec(skuDTO.getSpec())
                .categoryName(skuDTO.getCategoryName())
                .picList(skuDTO.getPicList())
                .upcList(skuDTO.getUpcList())
                .packNum(skuDTO.getPackNum())
                .pickTaskId(skuDTO.getPickTaskId())
                .pickedAccountId(skuDTO.getPickedAccountId())
                .pickPackingSpec(convert2PickPackingSpecVO(skuDTO.getPickPackingSpec()))
                .basicUnit(skuDTO.getBasicUnit())
                .build()).collect(Collectors.toList());
    }


    private WarehouseHuOperateRequest convert2WarehouseHuOperateRequest(WarehousePickHuCodeRequest request) {
        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        Long storeId = ApiMethodParamThreadLocal.getIdentityInfo().getStoreId();

        WarehouseOperatorDTO warehouseOperatorDTO = convert2WarehouseOperatorDTO(ApiMethodParamThreadLocal.getIdentityInfo().getUser());

        WarehouseHuOperateRequest req = new WarehouseHuOperateRequest();
        req.setOperator(warehouseOperatorDTO);
        req.setHuCode(request.getHuCode());
        req.setTaskId(request.getTaskId());
        req.setStoreId(storeId);
        req.setWaveId(request.getWaveId());
        req.setTenantId(tenantId);
        req.setOperateType(request.getOperateType());
        return req;
    }

    private WarehouseHuReplaceRequest convert2WarehouseHuReplaceRequest(WarehouseSeedHuReplaceRequest request) {
        Long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        Long storeId = ApiMethodParamThreadLocal.getIdentityInfo().getStoreId();

        WarehouseOperatorDTO warehouseOperatorDTO = convert2WarehouseOperatorDTO(ApiMethodParamThreadLocal.getIdentityInfo().getUser());

        WarehouseHuReplaceRequest req = new WarehouseHuReplaceRequest();
        req.setOperator(warehouseOperatorDTO);
        req.setTaskId(request.getTaskId());
        req.setStoreId(storeId);
        req.setWaveId(request.getWaveId());
        req.setTenantId(tenantId);
        req.setBeforeHuCode(request.getBeforeHuCode());
        req.setAfterHuCode(request.getAfterHuCode());
        return req;
    }

    private Map<Long, String> getTenantIdNameMap(Set<String> tenantIds){
        return tenantWrapper.getTenantIdNameMap(tenantIds.stream().map(Long::parseLong).collect(Collectors.toSet()));
    }
}
