/**
 * Copyright (C) 2021 Meituan
 * All rights reserved
 */
package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 多源商品查询结果，不同来源数据格式不同<br>
 * 1. 商品池<br>
 * 2. 标品库<br>
 *
 * <AUTHOR>
 * @since 2021-11-11 17:42
 */
@Data
public class MutliSourceQueryVo {
    @FieldDoc(
            description = "商品搜索结果", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品搜索结果")
    private List<BaseSearchVO> list;
}
