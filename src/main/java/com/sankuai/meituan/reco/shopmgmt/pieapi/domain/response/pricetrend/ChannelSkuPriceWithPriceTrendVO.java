package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pricetrend;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.PriceTrendConstants;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ProjectConstants;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.priceconfig.StoreSkuPriceFilter;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.StoreSkuBaseDetailVO;
import com.sankuai.meituan.shangou.saas.common.money.PriceUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@TypeDoc(
        description = "门店商品渠道价格及价格趋势",
        authors = "hejunliang"
)
@ApiModel("门店商品渠道价格及价格趋势")
@Data
@NoArgsConstructor
public class ChannelSkuPriceWithPriceTrendVO {

    @FieldDoc(
            description = "商品编码"
    )
    @ApiModelProperty(name = "商品编码")
    private String skuId;

    @FieldDoc(
            description = "规格"
    )
    @ApiModelProperty(name = "规格")
    private String spec;

    @FieldDoc(
            description = "当前价格信息"
    )
    @ApiModelProperty(name = "当前价格信息")
    private ChannelPriceInfoVO currentPriceInfo;

    @FieldDoc(
            description = "数据日期列表"
    )
    @ApiModelProperty(name = "数据日期列表")
    private ChannelSkuPriceTrendVO priceTrend;

    public static ChannelSkuPriceWithPriceTrendVO build(StoreSkuBaseDetailVO storeSkuBaseDetailVO,
                                                        ChannelSkuPriceTrendVO channelSkuPriceTrendVO,
                                                        StoreSkuPriceFilter filter, Long price, Integer weight) {

        ChannelSkuPriceWithPriceTrendVO channelSkuPriceWithPriceTrendVO = new ChannelSkuPriceWithPriceTrendVO();

        // 商品商品信息
        channelSkuPriceWithPriceTrendVO.setSkuId(storeSkuBaseDetailVO.getSkuId());
        channelSkuPriceWithPriceTrendVO.setSpec(storeSkuBaseDetailVO.getSpec());

        // 设置当前价格信息, 包括价格及市斤价
        ChannelPriceInfoVO currentPriceInfo = new ChannelPriceInfoVO();
        Long pricePer500g = null;
        if (filter.getCanCalculatePriceByWeight() && price != null
                && weight != null && weight > 0) {
            pricePer500g = PriceUtils.calculatePriceByWeight(price, weight, ProjectConstants.WEIGHT_500G);
        }
        if (price != null) {
            currentPriceInfo.setPrice(String.valueOf(PriceUtils.fen2Yuan(price)));
        }
        if (pricePer500g != null) {
            currentPriceInfo.setPricePer500g(String.valueOf(PriceUtils.fen2Yuan(pricePer500g)));
        }
        channelSkuPriceWithPriceTrendVO.setCurrentPriceInfo(currentPriceInfo);

        // 价格趋势信息
        channelSkuPriceWithPriceTrendVO.setPriceTrend(channelSkuPriceTrendVO);

        return channelSkuPriceWithPriceTrendVO;
    }

}


