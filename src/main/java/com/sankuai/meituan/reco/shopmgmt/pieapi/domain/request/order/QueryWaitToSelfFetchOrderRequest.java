package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @createTime 2023/02/10
 * @description
 */
@TypeDoc(
        description = "查询待自提列表"
)
@ApiModel("查询待自提列表")
@Data
public class QueryWaitToSelfFetchOrderRequest {

    @FieldDoc(
            description = "第几页", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "第几页", required = true)
    @NotNull
    private Integer page;

    @FieldDoc(
            description = "每页行数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "每页行数", required = true)
    @NotNull
    private Integer size;

    @FieldDoc(
            description = "门店类型 3-门店 5-中心仓 6-前置仓", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店类型")
    private Integer entityType;
}
