package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.delivery;

import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.store.wms.thrift.scmdelivery.ScmDeliveryThriftService;
import com.sankuai.meituan.reco.store.wms.thrift.scmdelivery.req.ScmDeliveryQueryReq;
import com.sankuai.meituan.reco.store.wms.thrift.scmdelivery.resp.ScmDeliveryQueryResp;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import com.sankuai.shangou.common.Result;
import com.sankuai.shangou.logistics.warehouse.order.enums.ReceivingOrderStatus;
import com.sankuai.shangou.logistics.wio.client.receiving.ReceivingQueryService;
import com.sankuai.shangou.logistics.wio.client.receiving.dto.StatisticOrderResultDTO;
import com.sankuai.shangou.logistics.wio.client.receiving.request.StatisticOrdersRequest;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.codehaus.jackson.type.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

/**
 * author xujunfeng02
 * dateTime 2023/2/23 4:39 PM
 * description 歪马收货wrapper
 */
@Component
@Slf4j
public class ScmDeliveryWrapper {

    // 1-未收货，2-部分收货
    private static final List<Integer> UNFINISHED_STATUS = Lists.newArrayList(1, 2);

    @Autowired
    private ReceivingQueryService receivingQueryService;

    @Resource(name = "wmsScmDeliveryThriftService")
    private ScmDeliveryThriftService wmsScmDeliveryThriftService;

    @MethodLog(logRequest = true, logResponse = true)
    public int count(PendingTaskParam param) {

        try {
            if (isWioReceivingOrderNumber(param.getEntityId())) {
                StatisticOrdersRequest request = new StatisticOrdersRequest();
                request.setMerchantId(param.getTenantId());
                request.setWarehouseIds(Collections.singletonList(param.getEntityId()));
                request.setStatusList(Arrays.asList(ReceivingOrderStatus.INITIAL.getValue(), ReceivingOrderStatus.RECEIVING.getValue()));
                Result<List<StatisticOrderResultDTO>> response = receivingQueryService.statisticOrders(request);

                if (Objects.isNull(response) || response.getCode() != 0 || CollectionUtils.isEmpty(response.getModule())) {
                    log.error("调用收货服务查询未完成收货单据数量失败, tenantId:{}, poiIds:{}, msg:{} ", param.getTenantId(), Collections.singletonList(param.getEntityId()), response.getMessage());
                    return 0;
                }
                return response.getModule().get(0).getOrderCount().intValue();
            } else {
                ScmDeliveryQueryReq req = new ScmDeliveryQueryReq();
                req.setTenantId(param.getTenantId());
                req.setStoreId(param.getEntityId());
                req.setDeliveryStatus(UNFINISHED_STATUS);
                req.setPageNo(1);
                req.setPageSize(1);
                ScmDeliveryQueryResp resp = wmsScmDeliveryThriftService.deliveryQuery(req);
                return resp.getPageInfo().getTotalCount();
            }
        } catch (Exception e) {
            log.error("查询收货单数量 error", e);
            return 0;
        }
    }

    private boolean isWioReceivingOrderNumber(Long warehouseId) {
        String warehouseIdsString = Lion.getConfigRepository("com.sankuai.drunkhorsemgmt.wms").get("wio_receiving_order_statistic_warehouseIds", "[]");
        List<Long> warehouseIds =  JacksonUtils.fromJson(warehouseIdsString, new TypeReference<List<Long>>() {});
        if (CollectionUtils.isEmpty(warehouseIds)) {
            return false;
        }
        if (warehouseIds.size() == 1 && warehouseIds.contains(-1L)) {
            return true;
        }
        return warehouseIds.contains(warehouseId);
    }
}