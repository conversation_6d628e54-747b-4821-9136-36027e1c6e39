package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.storecategory.spurelation;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelPriceVO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.PoiStoreCategorySpuDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

/**
 * <AUTHOR>
 */
@TypeDoc(
        description = "店内分类商品列表数据"
)
@Getter
@Setter
@ToString
public class StoreCategorySpuVo {

    @FieldDoc(
            description = "店内分类商品Id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "店内分类商品Id")
    private String spuId;

    @FieldDoc(
            description = "店内分类商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "店内分类商品名称")
    private String name;

    @FieldDoc(
            description = "店内分类商品排序", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "店内分类商品排序")
    private Integer sequence;

    @FieldDoc(
            description = "店内分类商品图片", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "店内分类商品图片")
    private List<String> images;

    @FieldDoc(
            description = "是否上线，1已上线 2未上线", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否上线，1已上线 2未上线")
    private Integer online;

    @FieldDoc(
            description = "erp编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "erp编码")
    private String erpCode;

    @FieldDoc(
            description = "upc条码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "upc条码")
    private String upc;

    @FieldDoc(
            description = "单位转换系数"
    )
    @ApiModelProperty(value = "单位转换系数")
    private Double unitConvertFactor;

    @FieldDoc(
            description = "销量"
    )
    @ApiModelProperty(value = "销量")
    private Integer saleAmount;

    @FieldDoc(
            description = "分渠道价格"
    )
    @ApiModelProperty(value = "分渠道价格")
    private List<ChannelPriceVO> channelOnlinePriceList;

    public static StoreCategorySpuVo convert2Vo(PoiStoreCategorySpuDTO dto) {
        StoreCategorySpuVo vo = new StoreCategorySpuVo();
        vo.setName(dto.getName());
        vo.setSpuId(dto.getSpuId());
        vo.setSequence(dto.getSequence());
        vo.setImages(dto.getImages());
        vo.setUpc(dto.getUpc());
        // 销量
        vo.setSaleAmount(dto.getSaleAmount());
        if(CollectionUtils.isNotEmpty(dto.getOnlinePriceList())){
            List<ChannelPriceVO> priceVOS = Fun.map(dto.getOnlinePriceList(),
                    each -> {
                        ChannelPriceVO channelPriceVO = new ChannelPriceVO();
                        channelPriceVO.setChannelId(each.getChannelId());
                        channelPriceVO.setOnlinePrice(String.valueOf(each.getOnlinePrice()));
                        return channelPriceVO;
                    });
            // 渠道最低零售价
            vo.setChannelOnlinePriceList(priceVOS);
        }
        return vo;
    }

}
