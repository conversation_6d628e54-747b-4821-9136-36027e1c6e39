package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.appmodel.workbench.WMTaskModelMenuHelper;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.commons.thrift.publisher.utils.JsonUtils;
import com.sankuai.shangou.waima.support.api.pl.dto.poipreparation.PoiAccountInfo;
import com.sankuai.shangou.waima.support.api.service.equipment.TEquipmentService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: hezhengyu
 * @create: 2023-11-17 14:30
 */
@Slf4j
@Service
public class EquipmentServiceWrapper {

    @Resource
    private TEquipmentService equipmentService;

    public CommonResponse<Integer> countTodoOrderForManager(Long tenantId, Long accountId, Long storeId) {
        CommonResponse<Integer> response = CommonResponse.success(0);

        try {
            TResult<Integer> result = equipmentService.countTodoOrderForManager(tenantId, storeId);
            if (result != null && result.isSuccess()) {
                response = CommonResponse.success(result.getData());
            }
        } catch (Exception e) {
            log.error("Failed to count equipment order", e);
            response = CommonResponse.fail(ResultCode.FAIL.getCode(), "查询待发放的骑手装备单据失败");
        }
        return response;
    }

    public CommonResponse<Integer> countTodoOrderForEmp(Long tenantId, Long accountId, Long storeId) {
        CommonResponse<Integer> response = CommonResponse.success(0);

        try {
            TResult<Integer> result = equipmentService.countTodoOrderForEmp(tenantId, accountId);
            if (result != null && result.isSuccess()) {
                response = CommonResponse.success(result.getData());
            }
        } catch (Exception e) {
            log.error("Failed to count equipment order", e);
            response = CommonResponse.fail(ResultCode.FAIL.getCode(), "查询待审批的骑手装备单据失败");
        }
        return response;
    }

}
