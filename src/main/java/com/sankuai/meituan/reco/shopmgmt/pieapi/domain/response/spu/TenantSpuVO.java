package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.linz.boot.util.Fun;
import com.meituan.linz.product.channel.EnhanceChannelType;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.StoreSkuPropertyVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.product.container.SpuContainer;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.AbnormalInfoUtils;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreCoverageBizDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.TenantSpuBizDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.TenantStoreCategoryBizDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.SaleAttrVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.tenant.RegionCoverageVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.tenant.StoreCoverageVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.tenant.StoreGroupCoverageVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.WeightTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.shangou.empower.ocms.client.product.enums.AllowSaleEnum;
import com.sankuai.meituan.shangou.empower.ocms.client.product.enums.ChannelAuditStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.client.product.enums.ChannelNormAuditStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelType;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.PoiGroupInfoDto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Title: TenantSpuVO
 * @Description: 商品池商品信息
 * @Author: wuyongjiang
 * @Date: 2022/8/23 11:13
 */
@EqualsAndHashCode(callSuper = true)
@TypeDoc(
        description = "商品池商品SPU信息"
)
@Data
@ApiModel("商品池商品SPU信息")
public class TenantSpuVO extends SpuContainer {
    @FieldDoc(
            description = "租户ID",requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户ID",required = true)
    private Long tenantId;

    @FieldDoc(
            description = "SPU_ID"
    )
    @ApiModelProperty("SPU_ID")
    private String spuId;

    @FieldDoc(
            description = "商品名称"
    )
    @ApiModelProperty("商品名称")
    private String name;

    @FieldDoc(
            description = "图片URI列表"
    )
    @ApiModelProperty("图片URI列表")
    private List<String> imageUris;
    private List<ImageInfoVO> imageUrlInfos;

    @FieldDoc(
            description = "主图"
    )
    @ApiModelProperty("主图")
    private String mainImage;

    @FieldDoc(
            description = "产地"
    )
    @ApiModelProperty("产地")
    private String producingPlace;

    @FieldDoc(
            description = "称重类型"
    )
    @ApiModelProperty("称重类型")
    private int weightType;

    @FieldDoc(
            description = "称重类型"
    )
    @ApiModelProperty("称重类型")
    private String weightTypeText;

    @FieldDoc(
            description = "是否标品，1：标品"
    )
    @ApiModelProperty("是否标品")
    private int standerType;

    @FieldDoc(
            description = "是否标品"
    )
    @ApiModelProperty("是否标品")
    private String standerTypeDesc;

    @FieldDoc(
            description = "是否允许自定义规格描述"
    )
    @ApiModelProperty("是否允许自定义规格描述")
    private Boolean canCustomizeSpec;

    @FieldDoc(
            description = "商品门店覆盖情况"
    )
    @ApiModelProperty("商品门店覆盖情况")
    private StoreCoverageVO storeCoverage;

    @FieldDoc(
            description = "商品门店分组覆盖情况"
    )
    private StoreGroupCoverageVO storeGroupCoverage;

    @FieldDoc(
            description = "SKU信息"
    )
    @ApiModelProperty("SKU信息")
    private List<TenantSkuVO> skus;

    @FieldDoc(
            description = "分类信息"
    )
    @ApiModelProperty("分类信息")
    private CategoryVO erpCategory;

    @FieldDoc(
            description = "覆盖区域数量"
    )
    @ApiModelProperty("覆盖区域数量")
    private RegionCoverageVO regionCoverage;

    @FieldDoc(
            description = "品牌ID"
    )
    @ApiModelProperty("品牌ID")
    private String brandId;

    @FieldDoc(
            description = "品牌名称"
    )
    @ApiModelProperty("品牌名称")
    private String brandName;

    @FieldDoc(
            description = "品牌信息"
    )
    @ApiModelProperty("品牌信息")
    private BrandVO erpBrand;

    @FieldDoc(
            description = "美团渠道类目"
    )
    @ApiModelProperty("美团渠道类目")
    private ChannelCategoryVO mtChannelCategory;

    @FieldDoc(
            description = "饿了么渠道信息"
    )
    @ApiModelProperty("饿了么渠道信息")
    private ChannelCategoryVO elmChannelCategory;


    @FieldDoc(
            description = "抖音渠道"
    )
    @ApiModelProperty("抖音渠道信息")
    private ChannelCategoryVO douyinChannelCategory;

    @FieldDoc(
            description = "抖音渠道信息是否可以修改"
    )
    @ApiModelProperty("抖音渠道类目是否可以修改")
    private Boolean douyinChannelCategoryCanModify;

    @FieldDoc(
            description = "饿了么渠道类目是否可以修改"
    )
    @ApiModelProperty("饿了么渠道类目是否可以修改")
    private Boolean elmChannelCategoryCanModify;

    @FieldDoc(
            description = "京东渠道信息"
    )
    @ApiModelProperty("京东渠道信息")
    private ChannelCategoryVO jdChannelCategory;

    @FieldDoc(
            description = "京东渠道类目是否可以修改"
    )
    @ApiModelProperty("京东渠道类目是否可以修改")
    private Boolean jdChannelCategoryCanModify;

    @FieldDoc(
            description = "美团渠道可售状态"
    )
    @ApiModelProperty("美团渠道可售状态")
    private int mtAllowSale;

    @FieldDoc(
            description = "美团渠道审核状态"
    )
    @ApiModelProperty("美团渠道审核状态")
    private List<Integer> auditStatusList;

    @FieldDoc(
            description = "美团渠道审核状态"
    )
    @ApiModelProperty("美团渠道审核状态")
    private List<Integer> normAuditStatusList;

    @FieldDoc(
            description = "商品视频信息"
    )
    @ApiModelProperty("商品视频信息")
    public VideoInfoVO videoInfo;

    @FieldDoc(
            description = "商品图片详情链接"
    )
    @ApiModelProperty("商品图片详情链接")
    public List<String> pictureContents;

    @FieldDoc(
            description = "规格类型"
    )
    @ApiModelProperty("规格类型")
    private Integer specType;

    @FieldDoc(
            description = "商品卖点"
    )
    @ApiModelProperty("商品卖点")
    private String sellingPoint;

    @FieldDoc(
            description = "商品描述"
    )
    @ApiModelProperty("商品描述")
    private String description;

    @FieldDoc(
            description = "店内分类列表"
    )
    @ApiModelProperty("店内分类列表")
    private List<MerchantStoreCategoryVO> tenantStoreCategoryList;

    @FieldDoc(
            description = "店内分类id"
    )
    @ApiModelProperty("店内分类id")
    private List<String> storeCategoryCodes;

    @FieldDoc(
            description = "是否控品，1是，0否"
    )
    @ApiModelProperty("是否控品")
    private Integer controlProduct;

    @FieldDoc(
            description = "加盟区域id列表"
    )
    @ApiModelProperty("加盟区域id列表")
    private List<Integer> joinAreaIds;

    @FieldDoc(
            description = "是否控货，1是，0否"
    )
    @ApiModelProperty("是否控货，1是，0否")
    @Deprecated // 主档维度该字段已弃用，是否控货有规格下的备货区域是否不为空确定
    private Integer controlGoods;

    @FieldDoc(
            description = "上下架状态1上架，2下架"
    )
    @ApiModelProperty("上下架状态1上架，2下架")
    private Integer saleStatus;

    @FieldDoc(
            description = "加盟主商品id"
    )
    @ApiModelProperty("加盟主商品id")
    private String franchisorControlProductSpuId;

    @FieldDoc(
            description = "抖音商品资质"
    )
    @ApiModelProperty("抖音商品资质")
    private List<QualificationVO> douyinQualification;


    @FieldDoc(
            description = "抖音售后服务"
    )
    @ApiModelProperty("抖音售后服务")
    private String douyinAfterSaleServiceType;


    @FieldDoc(
            description = "抖音图片审核状态"
    )
    @ApiModelProperty("抖音图片审核状态")
    private List<MaterialAuditVO> douyinMaterialAuditList;

    @FieldDoc(
            description = "商品属性 不超过十个属性", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "商品属性")
    private List<StoreSkuPropertyVO> properties;

    @FieldDoc(
            description = "渠道品牌"
    )
    @ApiModelProperty("渠道品牌")
    private List<ChannelBrandDomainVO> channelBrand;

    @FieldDoc(
            description = "京东渠道销售属性"
    )
    @ApiModelProperty("京东渠道销售属性")
    private List<SaleAttrVo> jdSaleAttrList;

    @FieldDoc(
            description = "京东渠道配送要求"
    )
    @ApiModelProperty("京东渠道配送要求")
    private Integer deliveryRequirement;

    @FieldDoc(
            description = "按门店分组的店内分类编码列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "按门店分组的店内分类编码列表")
    private List<StoreGroupCategoryCodeVO> storeGroupCategoryCodes;

    @FieldDoc(
            description = "按门店分组的店内分类列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "按门店分组的店内分类列表")
    private List<StoreGroupMerchantStoreCategoryVO> storeGroupCategoryList;

    @FieldDoc(
            description = "异常类型列表"
    )
    @ApiModelProperty(value = "异常类型列表")
    private List<String> abnormalCodes;

    @FieldDoc(
            description = "异常商品文案"
    )
    @ApiModelProperty(value = "异常商品文案")
    private String abnormalMarkMsg;

    @FieldDoc(
            description = "商品加盟门店覆盖情况"
    )
    @ApiModelProperty(value = "商品加盟门店覆盖情况")
    private FranchiseeStoreCoverageVO franchiseeStoreCoverage;

    @FieldDoc(
            description = "渠道类目信息，所有渠道类目统一管理，后续主键废弃掉散落的渠道类目信息"
    )
    @ApiModelProperty("渠道类目信息")
    private List<ChannelCategoryVO> channelCategoryVOList;

    @FieldDoc(
            description = "是否存在不可售", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否存在不可售")
    private Boolean hasNoSaleAbnormal;

    @FieldDoc(
            description = "不可售渠道信息列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "不可售渠道信息列表")
    private List<NoSaleChannelInfoVO> noSaleChannelInfoList;

    @FieldDoc(
            description = "医疗器械资质相关信息",
            rule = "渠道类目为部分医疗器械分类时，该字段必填",
            requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty("医疗器械资质相关信息")
    private MedicalDeviceQuaInfoVO medicalDeviceQuaInfo;

    @FieldDoc(
            description = "美团售后服务", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty("美团售后服务")
    private String mtAfterSaleServiceType;

    @FieldDoc(
            description = "货盘归属：1-总部货盘，2-门店货盘"
    )
    @ApiModelProperty(value = "货盘归属：1-总部货盘，2-门店货盘")
    private Integer palletSrc;
    @FieldDoc(description = "门店分组信息")
    private List<PoiGroupVo>poiGroupVoList;

    @FieldDoc(
            description = "特殊管控商品资质"
    )
    @ApiModelProperty("特殊管控商品资质")
    private List<String> controlQuaPicUrl;

    @FieldDoc(description = "AI推荐信息")
    private AiRecommendVO aiRecommendVO;

    @FieldDoc(description = "是否匹配医药标品库")
    private Integer matchMedicalStandard;

    @FieldDoc(
            description = "名称补充语信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty("特名称补充语信息")
    private NameSupplementInfoVO nameSupplementInfo;

    @FieldDoc(description = "美团特殊图片")
    private List<TenantSpecialPictureVO> mtSpecialPictureList;

    public static TenantSpuVO convertFromBizDto(TenantSpuBizDTO tenantSpuDTO) {
        TenantSpuVO vo = new TenantSpuVO();
        if (tenantSpuDTO == null) {
            return vo;
        }
        vo.setTenantId(tenantSpuDTO.getTenantId());
        vo.setSpuId(tenantSpuDTO.getSpuId());
        vo.setName(tenantSpuDTO.getName());
        vo.setProducingPlace(tenantSpuDTO.getProducingPlace());
        vo.setImageUris(tenantSpuDTO.getImageUrls());
        vo.setControlProduct(tenantSpuDTO.getControlProduct());
        vo.setJoinAreaIds(tenantSpuDTO.getJoinAreaIds());
        vo.setControlGoods(tenantSpuDTO.getControlGoods());
        vo.setSaleStatus(tenantSpuDTO.getSaleStatus());
        vo.setFranchisorControlProductSpuId(tenantSpuDTO.getFranchisorControlProductSpuId());
        List<String> images = vo.getImageUris();
        if (CollectionUtils.isNotEmpty(images) && images.get(0)!=null) {
            vo.setMainImage(images.get(0));
        }else{
            vo.setMainImage("");
        }
        vo.setWeightType(tenantSpuDTO.getWeightType());
        WeightTypeEnum weightTypeEnum = WeightTypeEnum.getByCode(tenantSpuDTO.getWeightType());
        if (weightTypeEnum != null) {
            vo.setWeightTypeText(weightTypeEnum.getDesc());
            vo.setStanderType(weightTypeEnum.toStanderType().getCode());
            vo.setStanderTypeDesc(weightTypeEnum.toStanderType().getDesc());
        }

        com.sankuai.meituan.shangou.empower.productbiz.client.dto.RegionCoverageDTO regionCoverageDTO = tenantSpuDTO.getRegionCoverageDTO();
        if (regionCoverageDTO != null) {
            RegionCoverageVO regionCoverageVO = new RegionCoverageVO();
            regionCoverageVO.setRegionTotal(regionCoverageDTO.getRegionTotal());
            regionCoverageVO.setCoverageRegionCount(regionCoverageDTO.getCoverageRegionCount());
            vo.setRegionCoverage(regionCoverageVO);
        }
        StoreCoverageBizDTO storeCoverageDTO = tenantSpuDTO.getStoreCoverageDTO();
        if (storeCoverageDTO != null) {
            StoreCoverageVO coverageVO = new StoreCoverageVO();
            coverageVO.setCoverageStoreCount(storeCoverageDTO.getCoverageStoreCount());
            coverageVO.setStoreTotal(storeCoverageDTO.getStoreTotal());
            vo.setStoreCoverage(coverageVO);
        }
        com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreGroupCoverageDTO storeGroupCoverageDTO = tenantSpuDTO.getStoreGroupCoverageDTO();
        if (storeGroupCoverageDTO != null){
            vo.setStoreGroupCoverage(StoreGroupCoverageVO.of(storeGroupCoverageDTO));
        }

        com.sankuai.meituan.shangou.empower.productbiz.client.dto.CategoryDTO categoryDTO = tenantSpuDTO.getCategory();
        if (categoryDTO != null) {
            CategoryVO erpCategoryVO = new CategoryVO();
            erpCategoryVO.setCategoryCode(categoryDTO.getCategoryCode());
            erpCategoryVO.setCategoryCodePath(categoryDTO.getCategoryCodePath());
            erpCategoryVO.setCategoryName(categoryDTO.getCategoryName());
            erpCategoryVO.setCategoryNamePath(StringUtils.replaceAll(categoryDTO.getCategoryNamePath(), "_", ">"));
            vo.setErpCategory(erpCategoryVO);
        }
        com.sankuai.meituan.shangou.empower.productbiz.client.dto.BrandDTO brandDTO = tenantSpuDTO.getBrand();
        if (brandDTO != null) {
            vo.setBrandId(brandDTO.getBrandCode());
            vo.setBrandName(brandDTO.getBrandName());
            vo.setErpBrand(BrandVO.ofDTO(brandDTO));
        }
        if (CollectionUtils.isNotEmpty(tenantSpuDTO.getTenantSkuDTOList())) {
            vo.setSkus(tenantSpuDTO.getTenantSkuDTOList().stream().map(TenantSkuVO::fromBizDTO).collect(Collectors.toList()));
        }

        // 所有渠道类目收拢到渠道类目列表中，对散落的渠道类目字段过渡完之后废弃掉，背景：https://km.sankuai.com/collabpage/2072737707
        List<ChannelCategoryVO> channelCategoryVOList= new ArrayList<>();
        //美团渠道类目
        com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelCategoryDTO mtChannelCategoryDTO = tenantSpuDTO.getMtChannelCategory();
        if (mtChannelCategoryDTO != null) {
            vo.setMtChannelCategory(ChannelCategoryVO.ofBizDTO(mtChannelCategoryDTO));
            channelCategoryVOList.add(ChannelCategoryVO.buildChannelCategoryVOForBiz(mtChannelCategoryDTO, true,
                    ChannelType.MEITUAN.getValue()));
        }

        //饿了么渠道类目
        vo.setElmChannelCategoryCanModify(tenantSpuDTO.getElmChannelCategoryCanModify());
        com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelCategoryDTO elmChannelCategoryDTO = tenantSpuDTO.getElmChannelCategory();
        if (elmChannelCategoryDTO != null) {
            vo.setElmChannelCategory(ChannelCategoryVO.ofBizDTO(elmChannelCategoryDTO));
            channelCategoryVOList.add(ChannelCategoryVO.buildChannelCategoryVOForBiz(elmChannelCategoryDTO,
                    tenantSpuDTO.getElmChannelCategoryCanModify(), ChannelType.ELEM.getValue()));
        }

        //抖音渠道类目
        com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelCategoryDTO dyChannelCategoryDTO = tenantSpuDTO.getDouyinChannelCategory();
        if (dyChannelCategoryDTO != null) {
            vo.setDouyinChannelCategory(ChannelCategoryVO.ofBizDTO(dyChannelCategoryDTO));
            vo.setDouyinChannelCategoryCanModify(true);//默认可编辑
            channelCategoryVOList.add(ChannelCategoryVO.buildChannelCategoryVOForBiz(dyChannelCategoryDTO, true,
                    ChannelType.DOU_YIN.getValue()));
        }
        vo.setDouyinAfterSaleServiceType(tenantSpuDTO.getDouyinAfterSaleServiceType());
        // 美团售后服务
        vo.setMtAfterSaleServiceType(tenantSpuDTO.getMtAfterSaleServiceType());
        if (CollectionUtils.isNotEmpty(tenantSpuDTO.getDouyinQualificationList())) {
            vo.setDouyinQualification(QualificationVO.ofBizDTOList(tenantSpuDTO.getDouyinQualificationList()));
        }
        if (CollectionUtils.isNotEmpty(tenantSpuDTO.getDouyinMaterialAuditList())) {
            vo.setDouyinMaterialAuditList(Fun.map(tenantSpuDTO.getDouyinMaterialAuditList(), MaterialAuditVO::ofBiz));
        }

        //京东渠道类目、品牌、配送要求、销售属性
        com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelCategoryDTO jdChannelCategoryDTO = tenantSpuDTO.getJdChannelCategory();
        if (jdChannelCategoryDTO != null) {
            vo.setJdChannelCategory(ChannelCategoryVO.ofBizDTO(jdChannelCategoryDTO));
            vo.setJdChannelCategoryCanModify(tenantSpuDTO.getJdChannelCategoryCanModify());
            channelCategoryVOList.add(ChannelCategoryVO.buildChannelCategoryVOForBiz(jdChannelCategoryDTO,
                    tenantSpuDTO.getJdChannelCategoryCanModify(), ChannelType.JD2HOME.getValue()));
        }
        vo.setChannelCategoryVOList(channelCategoryVOList);
        if (CollectionUtils.isNotEmpty(tenantSpuDTO.getChannelBrandRelationDTOS())) {
            vo.setChannelBrand(Fun.map(tenantSpuDTO.getChannelBrandRelationDTOS(), ChannelBrandDomainVO::ofBiz));
        } else {
            vo.setChannelBrand(Collections.emptyList());
        }
        vo.setDeliveryRequirement(tenantSpuDTO.getDeliveryRequirement());

        if (CollectionUtils.isNotEmpty(tenantSpuDTO.getJdSaleAttributeList())) {
            vo.setJdSaleAttrList(Fun.map(tenantSpuDTO.getJdSaleAttributeList(), SaleAttrVo::fromDTO));
        } else {
            vo.setJdSaleAttrList(Collections.emptyList());
        }

        //规格，卖点，描述，店内分类
        vo.setSpecType(tenantSpuDTO.getSpecType());
        vo.setSellingPoint(tenantSpuDTO.getSellingPoint());
        vo.setDescription(tenantSpuDTO.getDescription());
        vo.setProperties(ConverterUtils.convertList(tenantSpuDTO.getProperties(), StoreSkuPropertyVO::new));
        vo.setTenantStoreCategoryList(Optional.ofNullable(tenantSpuDTO.getStoreCategoryList()).map(List::stream).orElse(Stream.empty())
                .filter(Objects::nonNull).map(MerchantStoreCategoryVO::ofBiz).collect(Collectors.toList()));
        List<String> storeCategoryCodes = Optional.ofNullable(tenantSpuDTO.getStoreCategoryList()).map(List::stream)
                .orElse(Stream.empty())
                .filter(Objects::nonNull)
                .map(TenantStoreCategoryBizDTO::getCategoryId)
                .map(String::valueOf)
                .collect(Collectors.toList());
        vo.setStoreCategoryCodes(storeCategoryCodes);
        if (CollectionUtils.isNotEmpty(tenantSpuDTO.getStoreGroupCategoryList())) {
            vo.setStoreGroupCategoryCodes(tenantSpuDTO.getStoreGroupCategoryList().stream()
                    .map(StoreGroupCategoryCodeVO::ofBiz).collect(Collectors.toList()));
        } else {
            vo.setStoreGroupCategoryCodes(Collections.emptyList());
        }

        if (CollectionUtils.isNotEmpty(tenantSpuDTO.getStoreGroupCategoryInfoList())) {
            vo.setStoreGroupCategoryList(tenantSpuDTO.getStoreGroupCategoryInfoList().stream()
                    .map(StoreGroupMerchantStoreCategoryVO::ofBiz).collect(Collectors.toList()));
        } else {
            vo.setStoreGroupCategoryList(Collections.emptyList());
        }

        //美团渠道可售状态
        vo.setAuditStatusList(Optional.ofNullable(tenantSpuDTO.getAuditStatusList()).orElse(Lists.newArrayList()).stream()
                .map(auditStatus -> {
                    if (ChannelAuditStatusEnum.GRACE_PERIOD_REJECTED_EXPECT.getCode() == auditStatus) {
                        return ChannelAuditStatusEnum.GRACE_PERIOD_REJECTED.getCode();
                    } else {
                        return auditStatus;
                    }
                }).collect(Collectors.toList()));
        vo.setNormAuditStatusList(tenantSpuDTO.getNormAuditStatusList());
        vo.setMtAllowSale(AllowSaleEnum.toAllowSaleStatus(
                ConverterUtils.convertList(tenantSpuDTO.getAuditStatusList(), ChannelAuditStatusEnum::ofCode),
                ConverterUtils.convertList(tenantSpuDTO.getNormAuditStatusList(), ChannelNormAuditStatusEnum::ofCode)).getCode());

        vo.setCanCustomizeSpec(tenantSpuDTO.getAllowCustomizeSpec());
        if (tenantSpuDTO.getVideoInfo() != null) {
            vo.setVideoInfo(VideoInfoVO.of(tenantSpuDTO.getVideoInfo()));
        }
        vo.setPictureContents(tenantSpuDTO.getPictureContents());

        // 加盟门店覆盖率
        com.sankuai.meituan.shangou.empower.productbiz.client.dto.FranchiseeStoreCoverageDTO franchiseeStoreCoverageDTO = tenantSpuDTO.getFranchiseeStoreCoverageDTO();
        if (franchiseeStoreCoverageDTO != null){
            FranchiseeStoreCoverageVO storeCoverageVO = new FranchiseeStoreCoverageVO();
            storeCoverageVO.setCoverageFranchiseeStoreCount(franchiseeStoreCoverageDTO.getCoverageFranchiseeStoreCount());
            storeCoverageVO.setStoreTotal(franchiseeStoreCoverageDTO.getStoreTotal());
            vo.setFranchiseeStoreCoverage(storeCoverageVO);
        }
        // 添加加盟商门店异常提示信息
        AbnormalInfoUtils.fillFranchiseAbnormalInfoForBiz(tenantSpuDTO, vo);
        // 医疗器械资质信息
        vo.setMedicalDeviceQuaInfo(MedicalDeviceQuaInfoVO.fromMedicalDeviceQuaDTO(tenantSpuDTO.getMedicalDeviceQuaInfo()));
        List<String> noSaleAbnormalCodes = MccConfigUtil.noSaleAbnormalCodes();
        // 设置不可售标识
        if (CollectionUtils.isNotEmpty(tenantSpuDTO.getAbnormalCodes())
                && CollectionUtils.isNotEmpty(noSaleAbnormalCodes)
                && CollectionUtils.containsAny(noSaleAbnormalCodes, tenantSpuDTO.getAbnormalCodes())) {
            vo.setHasNoSaleAbnormal(true);
        }
        vo.setPalletSrc(tenantSpuDTO.getPalletSrc());
        // 设置租户门店分组信息
        vo.setPoiGroupVoList(buildPoiGroupVoList(tenantSpuDTO.getPoiGroupInfoDtoList()));
        vo.setChannelSaleAttrInfoList(getChannelSaleAttrInfoVOList(tenantSpuDTO));
        vo.setControlQuaPicUrl(tenantSpuDTO.getControlQuaPicUrl());

        if (tenantSpuDTO.getAiRecommendInfo() != null) {
            vo.setAiRecommendVO(AiRecommendVO.fetchAiRecommendBizDTO(tenantSpuDTO.getAiRecommendInfo()));
        }
        vo.setMatchMedicalStandard(tenantSpuDTO.getMatchMedicalStandard());

        // 名称补充语信息
        vo.setNameSupplementInfo(NameSupplementInfoVO.fromBizDTO(tenantSpuDTO.getNameSupplementInfo()));

        vo.setMtSpecialPictureList(Fun.map(tenantSpuDTO.getMtSpecialPictureList(), TenantSpecialPictureVO::toVO));
        return vo;
    }
    private static List<PoiGroupVo> buildPoiGroupVoList(List<PoiGroupInfoDto> poiGroupInfoDtoList) {
        if (CollectionUtils.isEmpty(poiGroupInfoDtoList)) {
            return Collections.emptyList();
        }
        List<PoiGroupVo> poiGroupVoList = new ArrayList<>();
        poiGroupInfoDtoList.forEach(dto -> {
            PoiGroupVo poiGroupVo = new PoiGroupVo();
            poiGroupVo.setPoiGroupId(dto.getPoiGroupId());
            poiGroupVo.setPoiGroupName(dto.getPoiGroupName());
            poiGroupVo.setPoiGroupViewId(dto.getPoiGroupViewId());
            poiGroupVo.setPoiGroupSize(dto.getPoiGroupSize());
            poiGroupVoList.add(poiGroupVo);
        });
        return poiGroupVoList;
    }

    public static List<ChannelSaleAttrInfoVO> getChannelSaleAttrInfoVOList(TenantSpuBizDTO tenantSpuDTO) {
        List<ChannelSaleAttrInfoVO> channelSaleAttrInfoVOList = new ArrayList<>();
        if (tenantSpuDTO.getMtChannelCategory() != null && CollectionUtils.isNotEmpty(tenantSpuDTO.getMtChannelCategory().getSaleAttrList())) {
            channelSaleAttrInfoVOList.add(ChannelSaleAttrInfoVO.build(EnhanceChannelType.MT.getChannelId(),
                    Fun.map(tenantSpuDTO.getMtChannelCategory().getSaleAttrList(), SaleAttrVo::fromDTO)));
        }
        if (tenantSpuDTO.getElmChannelCategory() != null && CollectionUtils.isNotEmpty(tenantSpuDTO.getElmChannelCategory().getSaleAttrList())) {
            channelSaleAttrInfoVOList.add(ChannelSaleAttrInfoVO.build(EnhanceChannelType.ELEM.getChannelId(),
                    Fun.map(tenantSpuDTO.getElmChannelCategory().getSaleAttrList(), SaleAttrVo::fromDTO)));
        }
        if (tenantSpuDTO.getJdChannelCategory() != null && CollectionUtils.isNotEmpty(tenantSpuDTO.getJdChannelCategory().getSaleAttrList())) {
            channelSaleAttrInfoVOList.add(ChannelSaleAttrInfoVO.build(EnhanceChannelType.JDDJ.getChannelId(),
                    Fun.map(tenantSpuDTO.getJdChannelCategory().getSaleAttrList(), SaleAttrVo::fromDTO), tenantSpuDTO.getJdChannelCategory().getSkuImageSetting()));
        }
        if (tenantSpuDTO.getDouyinChannelCategory() != null && CollectionUtils.isNotEmpty(tenantSpuDTO.getDouyinChannelCategory().getSaleAttrList())) {
            channelSaleAttrInfoVOList.add(ChannelSaleAttrInfoVO.build(EnhanceChannelType.DY.getChannelId(),
                    Fun.map(tenantSpuDTO.getDouyinChannelCategory().getSaleAttrList(), SaleAttrVo::fromDTO)));
        }
        return channelSaleAttrInfoVOList;
    }

}
