package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.WeightUnitConfigDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 重量单位配置VO
 *
 * <AUTHOR>
 * @since 2023/3/2
 */
@TypeDoc(
        description = "重量单位配置VO"
)
@Data
public class WeightUnitConfigVO {

    @FieldDoc(
            description = "重量单位名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "重量单位名称")
    private String unitName;

    @FieldDoc(
            description = "单位换算成克的比值", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "单位换算成克的比值")
    private Integer weightToGramRatio;

    @FieldDoc(
            description = "允许的最小值", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "允许的最小值")
    private String minValue;

    @FieldDoc(
            description = "允许的最大值", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "允许的最大值")
    private String maxValue;

    @FieldDoc(
            description = "允许的小数点个数", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "允许的小数点个数")
    private Integer allowFloatScale;

    public static WeightUnitConfigVO ofDTO(WeightUnitConfigDTO weightUnitConfigDTO) {
        WeightUnitConfigVO weightUnitConfigVO = new WeightUnitConfigVO();
        weightUnitConfigVO.setUnitName(weightUnitConfigDTO.getUnitName());
        weightUnitConfigVO.setWeightToGramRatio(weightUnitConfigDTO.getWeightToGramRatio());
        weightUnitConfigVO.setMinValue(weightUnitConfigDTO.getMinValue());
        weightUnitConfigVO.setMaxValue(weightUnitConfigDTO.getMaxValue());
        weightUnitConfigVO.setAllowFloatScale(weightUnitConfigDTO.getAllowFloatScale());
        return weightUnitConfigVO;
    }

}
