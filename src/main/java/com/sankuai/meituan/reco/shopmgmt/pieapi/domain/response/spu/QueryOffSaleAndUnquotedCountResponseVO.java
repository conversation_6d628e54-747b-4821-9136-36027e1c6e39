package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Map;
/**
 * @author: zhangbo
 * @date: 2020-05-15 17:03
 */
@TypeDoc(
        description = "按天查未报价/未上架商品总数响应"
)
@Data
@ApiModel("按天查未报价/未上架商品总数响应")
public class QueryOffSaleAndUnquotedCountResponseVO {
    @FieldDoc(
            description = "天数-商品总数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "天数-商品总数", required = true)
    private Map<Integer, Integer> dayCountMap;

}
