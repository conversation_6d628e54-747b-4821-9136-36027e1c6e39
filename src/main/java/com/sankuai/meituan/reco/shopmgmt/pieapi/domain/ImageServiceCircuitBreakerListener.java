package com.sankuai.meituan.reco.shopmgmt.pieapi.domain;

import com.dianping.lion.client.Lion;
import com.dianping.rhino.circuit.CircuitBreakerListener;
import com.dianping.rhino.circuit.listener.CircuitBreakerListenerContext;
import com.google.gson.JsonObject;
import com.meituan.linz.boot.util.TimeUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.squirrel.DegradeRecordSquirrelRepository;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.SpringAppContext;
import com.sankuai.xm.pubapi.thrift.GroupPushMessageWithUids;
import com.sankuai.xm.pubapi.thrift.PushMessageServiceI;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.dianping.rhino.circuit.CircuitBreakerEventType.CIRCUIT_BREAKER_AUTO_CLOSE;
import static com.dianping.rhino.circuit.CircuitBreakerEventType.CIRCUIT_BREAKER_AUTO_OPEN;

/**
 * <AUTHOR>
 * @since 2023/7/19 11:21
 **/
@Slf4j
public class ImageServiceCircuitBreakerListener implements CircuitBreakerListener {

    private final String PICK_SELECT_SERVICE_APP_KEY = "com.sankuai.waimai.sc.pickselectservice";

    private final String CHECK_PICK_IMAGE_LION_CONFIG_NAME = "no.need.picking.check.picture.store.list";

    //"-1"代表所有门店都不需要校验出库图片
    private final String NOT_CHECK_PICK_IMAGE = "-1";

    //""代表所有门店都需要校验出库图片
    private final String CHECK_PICK_IMAGE = "";

    private final String USER_NAME = "pieapi";

    @Override
    public void circuitBreakerOpened(CircuitBreakerListenerContext listenerContext) {
        log.info("ImageServiceCircuitBreakerListener.circuitBreakerOpened start");

        Long degradeMachineCount = SpringAppContext.AppContext.getBean(DegradeRecordSquirrelRepository.class).addDegradeMachineCount();

        //如果这是第一台降级的机器
        if (Objects.equals(degradeMachineCount, 1L)) {
            //设置拣货出库不校验出库图片
            setPickConfig(listenerContext);

            //发送大象通知
            pushXmMessage(listenerContext);
        }
    }

    @Override
    public void circuitBreakerClosed(CircuitBreakerListenerContext listenerContext) {
        log.info("ImageServiceCircuitBreakerListener.circuitBreakerClosed start");
        Long degradeMachineCount = SpringAppContext.AppContext.getBean(DegradeRecordSquirrelRepository.class).reduceDegradeMachineCount();

        //如果所有机器都已经恢复
        if (Objects.equals(degradeMachineCount, 0L)) {

            //设置拣货出库需要校验出库图片
            setPickConfig(listenerContext);

            //发送大象通知
            pushXmMessage(listenerContext);
        }
    }

    private void setPickConfig(CircuitBreakerListenerContext listenerContext) {
        String lionPassword = SpringAppContext.AppContext.getBean("lionPassword");
        String value;
        if (Objects.equals(listenerContext.getEventType(), CIRCUIT_BREAKER_AUTO_OPEN)) {
            value = NOT_CHECK_PICK_IMAGE;
        } else if (Objects.equals(listenerContext.getEventType(), CIRCUIT_BREAKER_AUTO_CLOSE)) {
            value = CHECK_PICK_IMAGE;
        } else {
            log.warn("其他事件类型,不处理。listenerContext: {}", listenerContext);
            return;
        }

        try {
            boolean success = Lion.getConfigRepository(PICK_SELECT_SERVICE_APP_KEY, USER_NAME, lionPassword)
                    .setValue(CHECK_PICK_IMAGE_LION_CONFIG_NAME, value);
            if (!success) {
                log.error("设置Lion开关失败, value: {}", value);
            }
        } catch (Exception e) {
            log.error("设置Lion开关失败, value: {}", value, e);
        }
    }

    private void pushXmMessage(CircuitBreakerListenerContext listenerContext) {
        List<Long> groupIds = MccConfigUtil.getFulfillWarningMessageNotifyGroupIds();

        String messageContent = buildMessageContent(listenerContext);
        if (StringUtils.isBlank(messageContent)) {
            log.warn("消息为空, 取消发送");
            return;
        }

        if (CollectionUtils.isNotEmpty(groupIds)) {
            groupIds.forEach(groupId -> {
                try {
                    PushMessageServiceI.Iface pushService = SpringAppContext.AppContext.getBean(PushMessageServiceI.Iface.class);
                    String result = pushService.pushToRoomWithUids(new GroupPushMessageWithUids(TimeUtils.currentMilli(), "text",
                            messageContent, groupId, SpringAppContext.AppContext.getBean("fulfillWarningMessagePusher"),
                            Collections.singletonList(-1L), ""));
                    log.info("push message result: {}", result);
                } catch (Exception e) {
                    log.warn("发送降级/恢复通知消息失败, groupId: {}, message:{}", groupId, messageContent, e);
                }
            });

        }
    }


    private String buildMessageContent(CircuitBreakerListenerContext listenerContext) {
        String template;
        if (Objects.equals(listenerContext.getEventType(), CIRCUIT_BREAKER_AUTO_OPEN)) {
            template = MccConfigUtil.getUploadImageDegradeNotifyMessageTemplate();
        } else if (Objects.equals(listenerContext.getEventType(), CIRCUIT_BREAKER_AUTO_CLOSE)) {
            template = MccConfigUtil.getUploadImageRecoverNotifyMessageTemplate();
        } else {
            log.warn("其他事件类型,不处理。listenerContext: {}", listenerContext);
            return "";
        }

        String notifyMessage = String.format(template, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        JsonObject messageBodyJson = new JsonObject();
        messageBodyJson.addProperty("text", notifyMessage);
        return messageBodyJson.toString();
    }

}
