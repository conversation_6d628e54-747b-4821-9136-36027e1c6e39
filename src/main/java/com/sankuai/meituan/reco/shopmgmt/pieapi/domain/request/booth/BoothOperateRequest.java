package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.booth;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.ParamException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/10/14 16:31
 * Description: 摊位操作请求，包括新建、编辑摊位操作
 */
@TypeDoc(
        description = "摊位操作请求",
        authors = {
                "liyang176"
        },
        version = "V1.0"
)
@Data
@ApiModel("摊位操作请求")
public class BoothOperateRequest {

    @FieldDoc(
            description = "摊位编码"
    )
    @ApiModelProperty(value = "摊位编码")
    private Long boothId;

    @FieldDoc(
            description = "摊位名称"
    )
    @ApiModelProperty(value = "摊位名称")
    private String name;

    @FieldDoc(
            description = "摊位所属门店编码"
    )
    @ApiModelProperty(value = "摊位所属门店编码")
    private Long poiId;

    @FieldDoc(
            description = "摊位备货方式，1-自采 2-外采"
    )
    @ApiModelProperty(value = "摊位备货方式，1-自采 2-外采")
    private Integer boothPickingType;

    @FieldDoc(
            description = "摊位的现结支付宝账户"
    )
    @ApiModelProperty(value = "摊位的现结支付宝账户")
    private String onSiteAlipayAccount;

    @FieldDoc(
            description = "摊位的现结支付宝的真实姓名"
    )
    @ApiModelProperty(value = "摊位的现结支付宝的真实姓名")
    private String onSiteAlipayRealname;

    @FieldDoc(
            description = "摊位快速检索码"
    )
    @ApiModelProperty(value = "摊位快速检索码")
    private String quickSearchCode;

    /**
     * 校验必填参数
     */
    public void baseValidate() {
        if (StringUtils.isBlank(name)) {
            throw new ParamException("摊位名称无效");
        }
        if (Objects.isNull(poiId) || poiId <= 0) {
            throw new ParamException("摊位所属门店编码无效");
        }
        if (StringUtils.isBlank(onSiteAlipayAccount)) {
            throw new ParamException("摊位的现结支付宝账户无效");
        }
        if (StringUtils.isBlank(onSiteAlipayRealname)) {
            throw new ParamException("摊位的现结支付宝的真实姓名无效");
        }
        if (StringUtils.isBlank(quickSearchCode)) {
            throw new ParamException("摊位的绑定二维码无效");
        }
    }
}
