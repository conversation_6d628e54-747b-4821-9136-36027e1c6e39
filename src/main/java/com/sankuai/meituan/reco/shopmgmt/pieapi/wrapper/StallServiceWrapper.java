package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.shangou.commons.thrift.publisher.request.UserContext;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.waima.support.api.service.punish.TPunishService;
import com.sankuai.shangou.waima.support.api.service.stall.TStallService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: hezhengyu
 * @create: 2023-11-17 14:30
 */
@Slf4j
@Service
public class StallServiceWrapper {

    @Resource
    private TStallService stallService;

    public CommonResponse<Integer> countExpireStall(Long tenantId, Long accountId, Long storeId) {
        CommonResponse<Integer> response = CommonResponse.success(0);

        try {
            TResult<Integer> result = stallService.queryStoreExpireStall(tenantId, accountId, storeId);
            if (result != null && result.isSuccess()) {
                response = CommonResponse.success(result.getData());
            }
        } catch (Exception e) {
            log.error("Failed to count waiting appeal ticket", e);
            response = CommonResponse.fail(ResultCode.FAIL.getCode(), "查询摆摊逾期未归还数量失败");
        }
        return response;
    }
}
