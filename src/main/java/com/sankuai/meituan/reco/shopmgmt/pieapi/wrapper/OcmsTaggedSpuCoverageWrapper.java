package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;
// Copyright (C) 2020 Meituan
// All rights reserved

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.meituan.shangou.saas.tenant.thrift.PoiThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiMapResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ChannelTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.PageTaggedSpuByQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.RegionSpuPageQueryResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.RegionSpuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.StoreSpuPageQueryResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.StoreSpuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.AccountTypeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.PermissionGroupTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.client.common.dto.PageInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.common.dto.PageQueryDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.RegionSpuDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.StoreSpuDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.RegionSpuExcludeStoreByTagPageQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.StoreSpuByTagPageQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.StoreSpuCoverageByTagRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.product.response.RegionSpuPageQueryResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.product.response.StoreCoreSkuCoverageResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.product.response.StoreSpuPageQueryResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.product.service.RegionSpuThriftService;
import com.sankuai.meituan.shangou.empower.ocms.client.product.service.StoreSpuThriftService;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.PlatformSoldOutInfoDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreSpuKeyDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.enums.ChannelAbnormalTypeEnum;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.QueryAbnormalSpuRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.QueryAbnormalSpuResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.service.ProblemSpuThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: <EMAIL>
 * @class: OcmsTaggedSpuCoverageWrapper
 * @date: 2020-06-17 16:56:26
 * @desc:
 */
@Component
@Slf4j
public class OcmsTaggedSpuCoverageWrapper {
    @Resource
    private PoiThriftService poiThriftService;

    @Resource
    private StoreSpuThriftService storeSpuThriftService;

    @Resource
    private RegionSpuThriftService regionSpuThriftService;

    @Resource
    private ThemeIndicDataWrapper themeIndicDataWrapper;

    @Resource
    private AuthThriftWrapper authThriftWrapper;
    @Resource
    private ProblemSpuThriftService problemSpuThriftService;

    @MethodLog(logResponse = true)
    public CommonResponse<String> getTaggedSpuCoverage(PageTaggedSpuByQueryRequest request, User user) {
        StoreSpuCoverageByTagRequest thriftRequest = new StoreSpuCoverageByTagRequest();
        Long tenantId = user.getTenantId();
        thriftRequest.setTenantId(tenantId);
        thriftRequest.setOperatorId(user.getAccountId());
        thriftRequest.setOperatorName(user.getOperatorName());
        thriftRequest.setStoreId(request.getStoreId());
        thriftRequest.setTagId(request.getTagId());
        thriftRequest.setRegionId((long)getRegionIdByStoreId(tenantId, request.getStoreId()));
        try {
            StoreCoreSkuCoverageResponse response = storeSpuThriftService.queryStoreSpuCoverageByTag(thriftRequest);
            if (response.getCode() != 0) {
                return CommonResponse.fail(-1, response.getMsg());
            }
            if (response.getRegionCoreSkuCount() == null || response.getRegionCoreSkuCount() == 0) {
                return CommonResponse.success("0%");
            }

            BigDecimal storeTaggedSpuCount = new BigDecimal(response.getStoreCoreSkuCount());
            BigDecimal regionTaggedSpuCount = new BigDecimal(response.getRegionCoreSkuCount());
            BigDecimal percentage = storeTaggedSpuCount.multiply(BigDecimal.valueOf(100)).divide(regionTaggedSpuCount, 2, RoundingMode.HALF_UP);
            return CommonResponse.success(percentage.toString() + "%");
        } catch (TException e) {
            log.error("调用OCMS获取门店某标签商品数量失败: request:{}", JSON.toJSONString(request), e);
            return CommonResponse.fail(-1, "获取门店商品覆盖率失败");
        }
    }

    @MethodLog(logResponse = true)
    public CommonResponse<RegionSpuPageQueryResponseVO> pageQueryRegionSpuExcludeStoreByTag(PageTaggedSpuByQueryRequest request, User user){
        RegionSpuExcludeStoreByTagPageQueryRequest thriftRequest = new RegionSpuExcludeStoreByTagPageQueryRequest();
        Long tenantId = user.getTenantId();
        thriftRequest.setTenantId(tenantId);
        thriftRequest.setOperatorId(user.getAccountId());
        thriftRequest.setOperatorName(user.getOperatorName());
        thriftRequest.setExcludeStoreId(request.getStoreId());
        thriftRequest.setTagId(request.getTagId());
        long cityId = getRegionIdByStoreId(tenantId, request.getStoreId());
        thriftRequest.setRegionId(cityId);
        PageQueryDTO pageQuery = new PageQueryDTO();
        pageQuery.setPageNum(request.getPage());
        pageQuery.setPageSize(request.getPageSize());
        thriftRequest.setPageQueryDTO(pageQuery);
        try {
            RegionSpuPageQueryResponse response = regionSpuThriftService.pageQueryRegionSpuExcludeStoreByTag(thriftRequest);
            if (response.getCode() != 0) {
                return CommonResponse.fail(-1, response.getMsg());
            }
            RegionSpuPageQueryResponseVO responseVO = new RegionSpuPageQueryResponseVO();
            Map<String, Integer> citySalesAmount = getCitySalesAmount(tenantId, cityId, response.getRegionSpus().stream().map(RegionSpuDTO::getSpuId).collect(Collectors.toList()));
            responseVO.fillInPageInfo(response.getPageInfoDTO())
                    .fillInData(response.getRegionSpus())
                    .fillInCitySaleAmount(citySalesAmount);
            return CommonResponse.success(responseVO);
        } catch (TException e) {
            log.error("获取城市某tag商品不在某门店的分页接口调用失败: request:{}", JSON.toJSONString(request), e);
            return CommonResponse.fail(-1, "获取城市某tag商品不在某门店的分页接口失败");
        }
    }

    @MethodLog(logResponse = true)
    public CommonResponse<StoreSpuPageQueryResponseVO> pageQueryStoreSpuByTag(PageTaggedSpuByQueryRequest request, User user){
        StoreSpuByTagPageQueryRequest thriftRequest = new StoreSpuByTagPageQueryRequest();
        Long tenantId = user.getTenantId();
        thriftRequest.setTenantId(tenantId);
        thriftRequest.setOperatorId(user.getAccountId());
        thriftRequest.setOperatorName(user.getOperatorName());
        thriftRequest.setStoreId(request.getStoreId());
        thriftRequest.setTagId(request.getTagId());
        PageQueryDTO pageQuery = new PageQueryDTO();
        pageQuery.setPageNum(request.getPage());
        pageQuery.setPageSize(request.getPageSize());
        thriftRequest.setPageQueryDTO(pageQuery);
        long cityId = getRegionIdByStoreId(tenantId, request.getStoreId());
        try {
            List<Long> boothIdList = authThriftWrapper.queryPermissionGroupId(user, PermissionGroupTypeEnum.BOOTH, Long::valueOf);
            Long boothId = null;
            if (user.getAccountType() == AccountTypeEnum.BOOTH.getValue() && !CollectionUtils.isEmpty(boothIdList)) {
                boothId = boothIdList.get(0);
            }
            thriftRequest.setBoothId(boothId);
            StoreSpuPageQueryResponse response = storeSpuThriftService.pageQueryStoreSpuByTag(thriftRequest);
            if (response.getCode() != 0) {
                return CommonResponse.fail(-1, response.getMsg());
            }
            Map<StoreSpuKeyDTO, PlatformSoldOutInfoDTO> storeSpuSoldOutInfoMap = getPlatformSoldOutInfo(user.getTenantId(), response.getStoreSpuList());
            StoreSpuPageQueryResponseVO storeSpuPageQueryResponseVO = StoreSpuPageQueryResponseVO.convertResponse(response, storeSpuSoldOutInfoMap);
            Map<String, Integer> citySalesAmount = getCitySalesAmount(tenantId, cityId, storeSpuPageQueryResponseVO.getStoreSpuList().stream().map(StoreSpuVO::getSpuId).collect(Collectors.toList()));
            storeSpuPageQueryResponseVO.fillInCitySaleAmount(citySalesAmount);
            return CommonResponse.success(storeSpuPageQueryResponseVO);
        } catch (TException e) {
            log.error("调用OCMS获取门店某标签商品数量失败: request:{}", JSON.toJSONString(request), e);
            return CommonResponse.fail(-1, "获取门店商品覆盖率失败");
        }
    }

    private Map<StoreSpuKeyDTO, PlatformSoldOutInfoDTO> getPlatformSoldOutInfo(Long tenantId, List<StoreSpuDTO> storeSpuDTOList) {
        if (CollectionUtils.isEmpty(storeSpuDTOList)) {
            return Maps.newHashMap();
        }

        List<StoreSpuKeyDTO> queryStoreSpuKeyList = storeSpuDTOList.stream()
                .filter(storeSpuDTO -> CollectionUtils.isNotEmpty(storeSpuDTO.getChannelSpuList()))
                .map(StoreSpuDTO::getChannelSpuList)
                .flatMap(List::stream)
                .filter(channelSpuDTO -> channelSpuDTO.getChannelId().equals(ChannelTypeEnum.MEITUAN.getChannelId()) && channelSpuDTO.isPlatformSoldOut())
                .map(channelSpuDTO -> StoreSpuKeyDTO.builder().storeId(channelSpuDTO.getStoreId()).spuId(channelSpuDTO.getSpuId()).build())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(queryStoreSpuKeyList)) {
            return Maps.newHashMap();
        }

        QueryAbnormalSpuRequest spuRequest = null;
        try {
            spuRequest = QueryAbnormalSpuRequest.builder()
                    .tenantId(tenantId)
                    .channelId(ChannelTypeEnum.MEITUAN.getChannelId())
                    .abnormalType(ChannelAbnormalTypeEnum.PLATFORM_SOLD_OUT.getCode())
                    .storeSpuKeyList(queryStoreSpuKeyList)
                    .page(1)
                    .pageSize(queryStoreSpuKeyList.size())
                    .build();
            QueryAbnormalSpuResponse spuResponse = problemSpuThriftService.queryAbnormalInfoByStoreSpuList(spuRequest);
            return Optional.ofNullable(spuResponse.getAbnormalSpuDetailDTOS()).orElse(Lists.newArrayList()).stream()
                    .collect(Collectors.toMap(detail -> StoreSpuKeyDTO.builder()
                            .storeId(detail.getStoreId()).spuId(detail.getSpuId()).build(), v -> v.getPlatformSoldOutInfo()));
        } catch (Exception e) {
            log.error("获取商品平台下架信息异常, request {}.", spuRequest, e);
        }

        return Maps.newHashMap();
    }

    private Integer getRegionIdByStoreId(Long tenantId, Long storeId) {
        PoiMapResponse poiMapResponse = poiThriftService.queryTenantPoiInfoMapByPoiIds(Lists.newArrayList(storeId), tenantId);
        if (poiMapResponse.getStatus().code != 0) {
            throw new CommonRuntimeException(poiMapResponse.getStatus().getMessage());
        }
        return poiMapResponse.getOne(storeId).getDistrict().getCityId();
    }

    private Map<String,Integer> getCitySalesAmount(Long tenantId, Long cityId, List<String> spuIdList){
        if (CollectionUtils.isEmpty(spuIdList)) {
            return Maps.newHashMap();
        }
        try {
            return themeIndicDataWrapper.getCitySpuRecent30DaySaleMap(tenantId, cityId, spuIdList);
        } catch (Exception e) {
            log.error("获取城市商品销量失败:", e);
        }
        return Maps.newHashMap();
    }
}
