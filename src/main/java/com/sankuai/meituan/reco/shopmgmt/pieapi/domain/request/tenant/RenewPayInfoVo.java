package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.tenant;

import com.dianping.lion.client.util.CollectionUtils;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.tenantbill.dto.model.RenewPayInfo;
import com.sankuai.meituan.shangou.empower.tenantbill.dto.model.ServiceRenewalDto;
import lombok.Data;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Data
@TypeDoc(
        description = "续签信息"
)
public class RenewPayInfoVo {
    @FieldDoc(description = "服务ID")
    private List<Long> serviceIdList;

    @FieldDoc(description = "支付金额")
    private Long calcAmount;

    @FieldDoc(description = "订货王续费详情")
    private List<ServiceRenewalVo> serviceRenewalList;

    public RenewPayInfo buildRenewPayInfoDto() {
        RenewPayInfo result = new RenewPayInfo();
        result.setServiceIdList(serviceIdList);
        result.setCalcAmount(calcAmount);
        return result;
    }

    public List<ServiceRenewalDto> buildServiceRenewalDto() {
        if(CollectionUtils.isEmpty(serviceRenewalList)) {
            return Collections.emptyList();
        }
        return serviceRenewalList.stream().map(renewal -> {
            ServiceRenewalDto renewalDto = new ServiceRenewalDto();
            renewalDto.setServiceId(renewal.getServiceId());
            renewalDto.setServiceType(renewal.getServiceType());
            renewalDto.setSubType(renewal.getSubType());
            renewalDto.setUnitNum(renewal.getUnitNum());
            return renewalDto;
        }).collect(Collectors.toList());
    }
}
