package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: zhangbo
 * @date: 2020-05-15 17:03
 */
@TypeDoc(
        description = "设置库存响应"
)
@Data
@ApiModel("错误信息")
public class ChannelSkuFailedRecordVO {
    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID", required = true)
    private Long skoreId;

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID", required = true)
    private Integer channelId;

    @FieldDoc(
            description = "SPU编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "SPU编码", required = true)
    private String spuId;

    @FieldDoc(
            description = "SKU编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "SKU编码", required = true)
    private String skuid;

    @FieldDoc(
            description = "已上架商品数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "已上架商品数", required = true)
    private Integer errorCode;

    @FieldDoc(
            description = "已下架商品数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "已下架商品数", required = true)
    private Integer errorMsg;
}
