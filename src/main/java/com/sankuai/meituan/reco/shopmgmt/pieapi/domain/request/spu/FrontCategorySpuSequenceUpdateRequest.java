package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.multidomain.request.StoreCategoryProductSequenceUpdateRequest;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.MapUtils;

import java.util.Map;

@TypeDoc(
        description = "店内分类下商品排序更新请求"
)
@Data
@NoArgsConstructor
public class FrontCategorySpuSequenceUpdateRequest {

    @FieldDoc(
            description = "门店id"
    )
    private Long storeId;

    @FieldDoc(
            description = "渠道id"
    )
    private Integer channelId;

    @FieldDoc(
            description = "店内分类id"
    )
    private Long frontCategoryId;

    @FieldDoc(
            description = "spu排序信息"
    )
    private Map<String, Integer> spuSequenceMap;

    public void validate() {
        if (this.storeId == null) {
            throw new CommonLogicException("门店id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (this.channelId == null) {
            throw new CommonLogicException("渠道id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (this.frontCategoryId == null) {
            throw new CommonLogicException("店内分类id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (MapUtils.isEmpty(this.spuSequenceMap)) {
            throw new CommonLogicException("spu排序信息不能为空", ResultCode.CHECK_PARAM_ERR);
        }
    }

    public StoreCategoryProductSequenceUpdateRequest convert2Request() {
        StoreCategoryProductSequenceUpdateRequest rpcUpdateRequest = new StoreCategoryProductSequenceUpdateRequest();
        rpcUpdateRequest.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        rpcUpdateRequest.setStoreId(this.storeId);
        rpcUpdateRequest.setChannelId(this.channelId);
        rpcUpdateRequest.setStoreCategoryId(this.frontCategoryId);
        rpcUpdateRequest.setSpuSequenceMap(this.spuSequenceMap);
        return rpcUpdateRequest;
    }
}
