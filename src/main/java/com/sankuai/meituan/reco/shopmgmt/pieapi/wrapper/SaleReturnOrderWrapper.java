package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.meituan.reco.pickselect.common.cat.CatTransaction;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.converters.SaleReturnConverter;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.CloseSaleReturnOrderRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.FallbackException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.store.management.enums.ResultCodeEnum;
import com.sankuai.meituan.reco.store.management.thrift.common.BaseResult;
import com.sankuai.meituan.reco.store.management.thrift.common.CommonParam;
import com.sankuai.meituan.reco.store.management.thrift.common.UserParam;
import com.sankuai.meituan.reco.store.management.thrift.scmdelivery.SaleReturnOrderThriftService;
import com.sankuai.meituan.reco.store.management.thrift.scmdelivery.req.SaleReturnOrderCloseReq;
import com.sankuai.meituan.reco.store.management.thrift.scmdelivery.req.SaleReturnOrderCreationPreCheckReq;
import com.sankuai.meituan.reco.store.management.thrift.scmdelivery.req.SaleReturnOrderQueryBySaleOrderReq;
import com.sankuai.meituan.reco.store.management.thrift.scmdelivery.resp.SaleReturnOrderCreationPreCheckResp;
import com.sankuai.meituan.reco.store.management.thrift.scmdelivery.resp.SaleReturnOrderQueryBySaleOrderResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2021/12/7
 */
@Rhino
@Slf4j
@Component
public class SaleReturnOrderWrapper {

	@Resource
	private SaleReturnOrderThriftService saleReturnOrderThriftService;

	@CatTransaction
	@MethodLog(logRequest = true, logResponse = true)
	@Degrade(rhinoKey = "SaleReturnOrderWrapper.canCreateSaleReturnOrder", fallBackMethod = "canCreateSaleReturnOrderFallback",
			timeoutInMilliseconds = 5000, errorThresholdCount = 10)
	public boolean canCreateSaleReturnOrder(Long tenantId, Long storeId, Integer orderBizType, String viewOrderId) {
		SaleReturnOrderCreationPreCheckReq request = new SaleReturnOrderCreationPreCheckReq(tenantId, storeId, orderBizType, viewOrderId);
		SaleReturnOrderCreationPreCheckResp response = saleReturnOrderThriftService.preCheckSaleReturnOrderCreation(request);
		checkResponse(response.getStatus());
		return response.isCanCreateSaleReturnOrder();
	}

	private boolean canCreateSaleReturnOrderFallback(Long tenantId, Long storeId, Integer orderBizType, String viewOrderId) {
		throw new FallbackException("SaleReturnOrderWrapper.canCreateSaleReturnOrder 熔断降级");
	}

	@CatTransaction
	@MethodLog(logRequest = true, logResponse = true)
	@Degrade(rhinoKey = "SaleReturnOrderWrapper.getSaleReturnOrderNo", fallBackMethod = "getSaleReturnOrderNoFallback",
			timeoutInMilliseconds = 5000, errorThresholdCount = 10)
	public SaleReturnOrderQueryBySaleOrderResp getSaleReturnOrderNo(Long tenantId, Long storeId, Integer orderBizType, String viewOrderId, String afterSaleId) {
		SaleReturnOrderQueryBySaleOrderReq request = SaleReturnOrderQueryBySaleOrderReq.builder()
				.tenantId(tenantId)
				.storeId(storeId)
				.saleOrderBizType(orderBizType)
				.saleOrderViewId(viewOrderId)
				.saleReturnId(afterSaleId)
				.build();
		SaleReturnOrderQueryBySaleOrderResp resp = saleReturnOrderThriftService.querySaleReturnOrderBySaleOrder(request);
		log.info("SaleReturnOrderWrapper.getSaleReturnOrderNo request:{}, response:{}", request, resp);
		return resp;
	}

	private boolean getSaleReturnOrderNoFallback(Long tenantId, Long storeId, Integer orderBizType, String viewOrderId, String afterSaleId) {
		throw new FallbackException("SaleReturnOrderWrapper.getSaleReturnOrderNo 熔断降级");
	}

	private void checkResponse(BaseResult result) {
		if (result.getCode() != ResultCodeEnum.SUCCESS.getCode()) {
			throw new CommonRuntimeException(result.getMsg());
		}
	}

	@CatTransaction
	@MethodLog(logRequest = true, logResponse = true)
	@Degrade(rhinoKey = "SaleReturnWrapper.closeSaleReturnOrder", fallBackMethod = "closeSaleReturnOrderFallback",
			timeoutInMilliseconds = 1000)
	public CommonResponse<Void> closeSaleReturnOrder(CloseSaleReturnOrderRequest request) throws TException {
		IdentityInfo user = ApiMethodParamThreadLocal.getIdentityInfo();

		BaseResult baseResult = saleReturnOrderThriftService.closeOrder(SaleReturnConverter.convertReq(request, user));
		log.info("销退单关单操作执行结果：baseResult=[{}].", baseResult);

		return new CommonResponse<>(baseResult.getCode(), baseResult.getMsg(), null);
	}

	private CommonResponse<Void> closeSaleReturnOrderFallback(CloseSaleReturnOrderRequest request) throws TException {
		throw new com.meituan.reco.pickselect.common.exception.FallbackException("SaleReturnWrapper.closeSaleReturnOrder 熔断降级");
	}

}
