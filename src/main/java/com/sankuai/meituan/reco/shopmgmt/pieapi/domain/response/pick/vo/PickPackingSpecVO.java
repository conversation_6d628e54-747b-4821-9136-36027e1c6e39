package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @Auther: nifei
 * @Date: 2023/8/30 11:04
 */
@TypeDoc(
        description = "拣货箱规单位"
)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PickPackingSpecVO {

    @FieldDoc(
            description = "箱规单位"
    )
    private String packingSpecUnit;

    @FieldDoc(
            description = "需求箱规单位与基本单位的转换系数"
    )
    private String packingSpecRatio;
}
