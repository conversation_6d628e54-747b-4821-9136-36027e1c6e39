package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dianping.cat.util.Pair;
import com.meituan.linz.boot.exception.ServiceRpcException;
import com.meituan.linz.boot.util.Fun;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.DataOrderUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.FormatUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.JacksonUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MoneyUtils;
import com.sankuai.meituan.shangou.saas.crm.data.client.dto.orderfinance.OrderProfitViewDto;
import com.sankuai.meituan.shangou.saas.crm.data.client.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.saas.crm.data.client.request.orderfinance.BatchQueryOrderProfitRequest;
import com.sankuai.meituan.shangou.saas.crm.data.client.request.orderfinance.OrderUniqueAttribute;
import com.sankuai.meituan.shangou.saas.crm.data.client.request.realtime.BoxedIndicatorRequest;
import com.sankuai.meituan.shangou.saas.crm.data.client.response.orderfinance.BatchQueryOrderProfitResponse;
import com.sankuai.meituan.shangou.saas.crm.data.client.response.realtime.BoxedIndicatorDataResponse;
import com.sankuai.meituan.shangou.saas.crm.data.client.service.OrderFinanceThriftService;
import com.sankuai.meituan.shangou.saas.crm.data.client.service.RealtimeThriftService;
import com.sankuai.sgdata.query.thrift.DataQueryThriftService;
import com.sankuai.sgdata.query.thrift.request.QueryRequest;
import com.sankuai.sgdata.query.thrift.response.QueryResponse;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

/**
 * 数据模块相关查询
 *
 * <AUTHOR>
 * @since 2021/7/7
 */
@Slf4j
@Service
public class SaasCrmDataWrapper {

    @Autowired
    private RealtimeThriftService realtimeThriftService;

    @Autowired
    private OrderFinanceThriftService orderFinanceThriftService;

    @Autowired
    private DataQueryThriftService dataQueryThriftService;

    /**
     * 获取方框图视图类型的主指标数据
     * key：指标id，value：指标值原始值
     * 注：使用时请务必确认原始值对应的单位/精度
     *
     * @param tenantId
     * @param poiId
     * @param modelCode
     * @return
     */
    public Map<Integer, Double> getRealTimeBoxedIndicatorData(long tenantId, long poiId, String modelCode) {
        BoxedIndicatorRequest request = new BoxedIndicatorRequest();
        request.setModelCode(modelCode);
        request.setPoiIds(Arrays.asList(poiId));
        request.setTenantId(tenantId);
        String today = LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
        request.setStartDate(today);
        request.setEndDate(today);
        BoxedIndicatorDataResponse response;
        try {
            log.info("获取方框图视图类型的指标数据 request:{}", JacksonUtils.toJson(request));
            response = realtimeThriftService.getBoxedIndicatorData(request);
            log.info("获取方框图视图类型的指标数据 response:{}", response);
        }
        catch (Exception e) {
            log.error("获取方框图视图类型的指标数据异常 request:{}", JacksonUtils.toJson(request), e);
            throw new ServiceRpcException("获取方框图视图类型的指标数据异常", e);
        }
        if (response == null || response.getCode() != ResultCodeEnum.SUCCESS) {
            throw new ServiceRpcException("获取方框图视图类型的指标数据失败");
        }

        return Optional.ofNullable(response.getData()).map(List::stream).orElse(Stream.empty())
                .filter(Objects::nonNull)
                .map(boxDto -> boxDto.indicInfo)
                .filter(infoDTO -> infoDTO != null && infoDTO.indicId != null && infoDTO.originalValue != null)
                .collect(Collectors.toMap(info -> info.indicId, info -> info.originalValue, (a, b) -> a));
    }

    /**
     * 获取订单毛利
     *
     * @param orderVOS
     * @return
     */
    public Map<String, OrderProfitView> queryNetProfit(List<OCMSOrderVO> orderVOS) {
        try {
            HashMap<String, OrderProfitView> resultMap = new HashMap<>();
            LocalDate begin = LocalDate.MAX;
            LocalDate end = LocalDate.MIN;
            ArrayList<OCMSOrderVO> realTimeOcmsOrderVOS = new ArrayList<>(orderVOS.size());
            ArrayList<OCMSOrderVO> offlineOcmsOrderVOS = new ArrayList<>(orderVOS.size());
            for (OCMSOrderVO ocmsOrderVO : orderVOS) {
                Long createTime = ocmsOrderVO.getCreateTime();
                LocalDate localDate = LocalDateTime.ofInstant(Instant.ofEpochMilli(createTime), ZoneId.systemDefault()).toLocalDate();
                boolean realTime = DataOrderUtils.isRealTime(localDate, localDate);
                if (realTime) {
                    realTimeOcmsOrderVOS.add(ocmsOrderVO);
                }
                else {
                    if (localDate.isBefore(begin)) {
                        begin = localDate;
                    }
                    if (localDate.isAfter(end)) {
                        end = localDate;
                    }
                    offlineOcmsOrderVOS.add(ocmsOrderVO);
                }
            }

            if (CollectionUtils.isNotEmpty(offlineOcmsOrderVOS)) {
                queryOffLineNetProfit(resultMap, begin, end, offlineOcmsOrderVOS);
            }

            if (CollectionUtils.isNotEmpty(realTimeOcmsOrderVOS)) {
                queryRealTimeNetProfit(resultMap, realTimeOcmsOrderVOS);
            }
            return resultMap;
        }
        catch (Exception e) {
            return Collections.emptyMap();
        }
    }

    private void queryRealTimeNetProfit(HashMap<String, OrderProfitView> resultMap, ArrayList<OCMSOrderVO> realTimeOcmsOrderVOS) throws TException {
        BatchQueryOrderProfitRequest batchQueryOrderProfitRequest = new BatchQueryOrderProfitRequest();
        List<OrderUniqueAttribute> orderUniqueAttributes = Fun.map(realTimeOcmsOrderVOS, ocmsOrderVO -> {
            OrderUniqueAttribute orderUniqueAttribute = new OrderUniqueAttribute();
            orderUniqueAttribute.setOrderBizType(ocmsOrderVO.getOrderBizType());
            orderUniqueAttribute.setViewOrderId(ocmsOrderVO.getViewOrderId());
            return orderUniqueAttribute;
        });
        batchQueryOrderProfitRequest.setOrderUniqueAttributes(orderUniqueAttributes);
        BatchQueryOrderProfitResponse batchQueryOrderProfitResponse = orderFinanceThriftService.batchQueryOrderProfit(batchQueryOrderProfitRequest);
        if (batchQueryOrderProfitResponse.getCode().getCode() == ResultCode.SUCCESS.getCode()) {
            for (OrderProfitViewDto viewDto : batchQueryOrderProfitResponse.getViewDtos()) {
                resultMap.put(viewDto.getViewOrderId(), new OrderProfitView(viewDto.getProfit(), viewDto.getWithDeliveryCost()));
            }
        }
    }

    private void queryOffLineNetProfit(HashMap<String, OrderProfitView> resultMap, LocalDate begin, LocalDate end, ArrayList<OCMSOrderVO> offlineOcmsOrderVOS) throws TException {
        List<String> offlineViewOrderIds = Fun.map(offlineOcmsOrderVOS, OCMSOrderVO::getViewOrderId);
        List<Long> poiIds = offlineOcmsOrderVOS.stream().map(OCMSOrderVO::getShopId).distinct().collect(Collectors.toList());
        QueryRequest queryRequest = new QueryRequest();
        queryRequest.setAppCode("shangou_reco_saas_data");
        queryRequest.setApiCode("b_service_baichuan_fin_poi_order_data");
        queryRequest.setNeedPage(false);
        Map<String, String> params = new HashMap<>();

        params.put("beginDate", begin.format(DateTimeFormatter.ISO_DATE).replace("-", ""));
        params.put("endDate", end.format(DateTimeFormatter.ISO_DATE).replace("-", ""));


        params.put("poiId", StringUtils.join(poiIds, ","));
        params.put("viewOrderId", FormatUtil.formatViewOrderIds(offlineViewOrderIds));
        //1 不限；2 亏损；3 盈利；
        params.put("profitType", "1");
        queryRequest.setParam(params);
        log.info("queryOffLineNetProfit request:{}", com.meituan.linz.boot.util.JacksonUtils.toJson(queryRequest));
        QueryResponse response = dataQueryThriftService.query(queryRequest);
        log.info("queryOffLineNetProfit response:{}", com.meituan.linz.boot.util.JacksonUtils.toJson(response));
        if (response.getCode() == ResultCode.SUCCESS.getCode()) {
            List<Map<String, String>> dataList = response.getData().getDataList();
            for (Map<String, String> data : dataList) {
                String viewOrderId = data.get("viewOrderId");
                long netProfitOnline = MoneyUtils.yuanToCent(data.get("netProfitOnline"));
                resultMap.put(viewOrderId, new OrderProfitView(netProfitOnline, Boolean.TRUE));
            }
        }
    }

    @Getter
    @Setter
    @ToString
    @AllArgsConstructor
    public static class OrderProfitView {

        private Long profit;

        private Boolean withDeliveryCost;
    }

}