package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pullnew;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2021/06/09 16:31
 * Description: 摊位操作请求，包括新建、编辑摊位操作
 */
@TypeDoc(
        description = "保存海报",
        authors = {
                "刘永高"
        },
        version = "V1.0"
)
@Data
@ApiModel("葵花码保存为海报")
public class SavePicRequest {

    @FieldDoc(
            description = "葵花码"
    )
    @ApiModelProperty(value = "葵花码")
    String miniProgramQrUrl;
    @FieldDoc(
            description = "横坐标"
    )
    @ApiModelProperty(value = "横坐标")
    private Integer width;

    @FieldDoc(
            description = "纵坐标"
    )
    @ApiModelProperty(value = "纵坐标")
    private Integer length;
}
