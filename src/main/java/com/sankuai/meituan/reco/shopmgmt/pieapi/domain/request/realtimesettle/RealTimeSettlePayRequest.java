package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.realtimesettle;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.order.platform.common.SelfCheckable;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: renhao
 * @Date: 2020/10/15
 * @Description:
 */
@TypeDoc(
        description = "外采现结支付请求"
)
@Data
public class RealTimeSettlePayRequest implements SelfCheckable {


    @FieldDoc(
            description = "门店ID"
    )
    @NotNull(message = "门店ID不能为空")
    private Long storeId;

    @FieldDoc(
            description = "交易单号"
    )
    @NotNull(message = "交易单号不能为空")
    private String tradeNo;

    @FieldDoc(
            description = "支付摊位ID"
    )
    @NotNull(message = "支付摊位ID不能为空")
    private String boothId;

    @FieldDoc(
            description = "支付摊位名称"
    )
    @NotNull(message = "支付摊位名称不能为空")
    private String boothName;

    @FieldDoc(
            description = "支付金额"
    )
    @NotNull(message = "支付金额不能为空")
    private Long payAmount;

    @FieldDoc(
            description = "支付商品列表"
    )
    @Valid
    @NotEmpty(message = "支付商品不能为空")
    private List<SkuPayModel> skuList;

}
