package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.realtimesettle;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.order.platform.common.SelfCheckable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@TypeDoc(
        description = "门店配置查询请求"
)
@Data
@ApiModel("门店信息")
public class StoreConfigQueryRequest implements SelfCheckable {
    @FieldDoc(
            description = "门店编号"
    )
    @ApiModelProperty(value = "门店编号", required = true)
    @NotNull(message = "门店编号不能为空")
    private Long storeId;

}
