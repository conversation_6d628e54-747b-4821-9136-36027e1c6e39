package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.management;
// Copyright (C) 2020 Meituan
// All rights reserved

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.resource.management.dto.appraise.AppraisePeriodPreviewDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

@TypeDoc(
        description = "考核阶段对象"
)
@Data
@NoArgsConstructor
public class AppraisePeriodVO {


    @FieldDoc(
            description = "阶段开始时间 yyyy-MM-dd",
            example = {}
    )
    private String periodStart;

    @FieldDoc(
            description = "阶段结束时间 yyyy-MM-dd",
            example = {}
    )
    private String periodEnd;

    @FieldDoc(
            description = "阶段名称, 正式期, 试用期, 鼓励期, 冲刺期, 平稳期",
            example = {}
    )
    private String periodName;

    @FieldDoc(
            description = "阶段描述",
            example = {}
    )
    private String periodDesc;

    private String statisticalStartDate;

    private String statisticalEndDate;


    public AppraisePeriodVO(AppraisePeriodPreviewDTO period) {
        this.periodStart = period.getStartDate();
        this.periodEnd = period.getEndDate();
        this.periodName = period.getPeriodName();
        this.periodDesc = period.getPeriodDesc();
        this.statisticalStartDate = period.getStatisticStartDate();
        this.statisticalEndDate = period.getStatisticEndDate();
    }
}
