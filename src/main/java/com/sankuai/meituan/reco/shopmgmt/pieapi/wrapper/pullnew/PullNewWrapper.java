package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.pullnew;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pullnew.PageQueryWarehouseRankingListRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pullnew.PullNewMultiModuleRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pullnew.PullNewQrCodeRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pullnew.PullNewRecordRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pullnew.PullNewStatRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pullnew.QueryPullNewByOrderRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pullnew.QueryWarehouseRankingDetailRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pullnew.SubordinateRecordRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew.PullNewAggModuleVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew.PullNewAggSubModuleVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew.PullNewDetailVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew.PullNewMyRecordVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew.PullNewQrCodeVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew.PullNewRecordVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew.PullNewStatAggrVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew.PullNewStatMultiModuleVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew.PullNewStatVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew.PullNewWarehouseRankDetailInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew.PullNewWarehouseRankingPageVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew.PullNewWarehouseRankingVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew.SubordinateStatAggrVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew.SubordinateStatVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.FlowValveException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.TimeUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.SacWrapper;
import com.sankuai.meituan.reco.store.management.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.auth.sdk.bean.SessionInfo;
import com.sankuai.meituan.shangou.empower.auth.sdk.context.SessionContext;
import com.sankuai.meituan.shangou.saas.crm.promotion.request.pullnew.CountSubordinateRequest;
import com.sankuai.meituan.shangou.saas.crm.promotion.request.pullnew.GetSuperiorRequest;
import com.sankuai.meituan.shangou.saas.crm.promotion.response.wechat.CountSubordinateResponse;
import com.sankuai.meituan.shangou.saas.crm.promotion.response.wechat.GetSuperiorResponse;
import com.sankuai.meituan.shangou.saas.crm.promotion.service.pullnew.PullNewQueryThriftService;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.enums.pullnew.QrCodeTypeEnum;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.request.pullnew.AppPullNewEsRequest;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.request.pullnew.AppPullNewStatEsRequest;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.request.pullnew.AppSubordinateStatEsRequest;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.request.pullnew.BindWeComPhoneRequest;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.request.pullnew.PageQueryPullNewRankingListRequest;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.request.pullnew.QueryPullNewRankingDetailRequest;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.request.pullnew.QueryPullNewRecordsByOrderIdRequest;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.request.pullnew.QueryWeComBindPhoneRequest;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.request.wechat.WeChatShowQRRequest;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.response.dto.PullNewAggSubModuleDto;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.response.dto.PullNewSimpleDetailDto;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.response.pullnew.AppPullNewEsDetailResponse;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.response.pullnew.AppPullNewRecordResponse;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.response.pullnew.AppPullNewStatEsResponse;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.response.pullnew.AppSubordinateStatEsResponse;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.response.pullnew.BindWeComPhoneResponse;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.response.pullnew.QueryPullNewRankingDetailResponse;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.response.pullnew.QueryPullNewRankingListResponse;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.response.pullnew.QueryPullNewRecordByOrderIdResponse;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.response.pullnew.QueryWeComBindPhoneResponse;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.response.wechat.WeChatShowQRResponse;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.thrift.PullNewThriftService;

import lombok.extern.slf4j.Slf4j;

/***
 * author : <EMAIL> 
 * data : 2021/6/9 
 * time : 下午2:48
 **/
@Slf4j
@Component
public class PullNewWrapper {

    @Resource
    PullNewQueryThriftService pullNewQueryThriftService;

    @Autowired
    PullNewThriftService pullNewThriftService;

    @Autowired
    private SacWrapper sacWrapper;

    //地推自提权限code，固定值
    private static final String PULL_NEW_SELF_PICK_AUTH_CODE = "PROMOTION-OFFLINE-QRCODE";

    // 美团APP推广
    private static final String WAIMA_MEITUAN_APP_QRCODE = "WAIMA_MEITUAN_APP_QRCODE";

    public Integer countSubordinate() {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        CountSubordinateRequest request = new CountSubordinateRequest();
        request.setAccountId(user.getAccountId());
        request.setTenantId(user.getTenantId());
        CountSubordinateResponse response = pullNewQueryThriftService.countSubordinate(request);
        if (!isSuccess(response.getCode())) {
            throw new FlowValveException(response.getCode(), response.getMsg());
        }
        return response.getSubordinateCount();
    }

    public PullNewStatAggrVo pullNewStat(PullNewStatRequest request) {

        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        AppPullNewStatEsRequest appPullNewStatRequest = new AppPullNewStatEsRequest();
        appPullNewStatRequest.setAccountId(user.getAccountId());
        appPullNewStatRequest.setTenantId(user.getTenantId());
        appPullNewStatRequest.setStoreId(request.getStoreId());
        appPullNewStatRequest.setStartTime(request.getStartTime());
        appPullNewStatRequest.setEndTime(request.getEndTime());
        AppPullNewStatEsResponse response = pullNewThriftService.appPullNewStat(appPullNewStatRequest);
        if (!isSuccess(response.getCode())) {
            throw new FlowValveException(response.getCode(), response.getMsg());
        }
        PullNewStatAggrVo pullNewStatAggrVo = new PullNewStatAggrVo();
        pullNewStatAggrVo.setSubordinateCount(response.getSubordinateCount());
        pullNewStatAggrVo.setSelfStat(
                Objects.isNull(response.getSelfStat()) ?
                        new PullNewStatVo() : PullNewStatVo.instanceOf(response.getSelfStat()));
        pullNewStatAggrVo.setSubordinateStat(
                Objects.isNull(response.getSubordinateStat()) ?
                        new PullNewStatVo() : PullNewStatVo.instanceOf(response.getSubordinateStat()));
        return pullNewStatAggrVo;
    }

    public SubordinateStatAggrVo subordinateStat(PullNewRecordRequest request) {

        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        AppSubordinateStatEsRequest appSubordinateStatRequest = new AppSubordinateStatEsRequest();
        appSubordinateStatRequest.setAccountId(user.getAccountId());
        appSubordinateStatRequest.setMarkId(request.getMarkId());
        appSubordinateStatRequest.setPageSize(request.getPageSize());
        appSubordinateStatRequest.setStoreId(request.getStoreId());
        appSubordinateStatRequest.setTenantId(user.getTenantId());
        appSubordinateStatRequest.setEndTime(request.getEndTime());
        appSubordinateStatRequest.setStartTime(request.getStartTime());
        AppSubordinateStatEsResponse response = pullNewThriftService.appSubordinateStat(appSubordinateStatRequest);
        if (!isSuccess(response.getCode())) {
            throw new FlowValveException(response.getCode(), response.getMsg());
        }
        SubordinateStatAggrVo subordinateStatAggrVo = new SubordinateStatAggrVo();
        subordinateStatAggrVo.setMarkId(response.getMarkId());
        subordinateStatAggrVo.setHasMore(response.isHasMore());
        subordinateStatAggrVo.setSubordinateStats(response.getList()
                .stream()
                .map(SubordinateStatVo::instanceOf)
                .collect(Collectors.toList()));
        return subordinateStatAggrVo;
    }

    public CommonResponse<PullNewQrCodeVo> qrCode(PullNewQrCodeRequest request) throws TException {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        if (request.getQrCodeType() == QrCodeTypeEnum.SELF_PICK.getCode()) {
            boolean authCheck = false;
            String authCode = PULL_NEW_SELF_PICK_AUTH_CODE;
            List<String> authCodes = Lists.newArrayList(authCode);
            Map<String, Boolean> authMap = getHasAuthMap(authCodes);
            //有权限才查
            authCheck = MapUtils.isNotEmpty(authMap) && authMap.containsKey(authCode) && Objects.equals(authMap.get(authCode), Boolean.TRUE);
            if (!authCheck) {
                log.warn("当前用户没有生成地推自提码权限,accountId:{}", user.getAccountId());
                return CommonResponse.fail(ResultCode.FORBIDDEN.getCode(), "没有地推自提码权限", null);
            }

        }
        if (request.getQrCodeType() == QrCodeTypeEnum.MEITUAN.getCode()) {
            boolean authCheck = false;
            String authCode = WAIMA_MEITUAN_APP_QRCODE;
            List<String> authCodes = Lists.newArrayList(authCode);
            Map<String, Boolean> authMap = getHasAuthMap(authCodes);
            //有权限才查
            authCheck = MapUtils.isNotEmpty(authMap) && authMap.containsKey(authCode) && Objects.equals(authMap.get(authCode), Boolean.TRUE);
            if (!authCheck) {
                log.warn("当前用户没有生成美团推广码权限,accountId:{}", user.getAccountId());
                return CommonResponse.fail(ResultCode.FORBIDDEN.getCode(), "没有地推美团推广码权限", null);
            }
        }
        WeChatShowQRRequest weChatShowQRRequest = new WeChatShowQRRequest();
        weChatShowQRRequest.setAccountId(user.getAccountId());
        weChatShowQRRequest.setAccountName(user.getAccountName());
        weChatShowQRRequest.setEmployeeName(user.getOperatorName());
        weChatShowQRRequest.setStoreId(request.getStoreId());
        weChatShowQRRequest.setTenantId(user.getTenantId());
        weChatShowQRRequest.setQrCodeType(request.getQrCodeType());
        weChatShowQRRequest.setPhone(user.getOperatorPhone());

        WeChatShowQRResponse response = pullNewThriftService.showPullNewQR(weChatShowQRRequest);
        if (response == null || response.getStatus() == null) {
            throw new FlowValveException(ResultCode.FAIL.code, "获取微信拉新二维码失败");
        } else if (!isSuccess(response.getStatus().getCode())) {
            throw new FlowValveException(response.getStatus().getCode(), response.getStatus().getMsg());
        }
        return CommonResponse.success(PullNewQrCodeVo.builder()
                .weixinQrTicket(response.getQrTicket())
                .weixinQrUrl(response.getQrUrl())
                .expireSeconds(response.getExpireSeconds())
                .miniProgramQrUrl(response.getMiniProgramQrUrl())
                .miniShortLink(response.getMiniShortLink())
                .wechatWorkUrl(response.getWechatWorkUrl())
                .manualBindWeCom(response.isManualBindPhone())
                .waimaiShortLink(response.getWaimaiShortLink())
                .memberQrUrl(response.getMemberQrUrl())
                .memberShortLink(response.getMemberShortLink())
                .mtDpLink(response.getMtDpLink())
                .build());
    }

    public PullNewRecordVo subordinatePullNewDetail(SubordinateRecordRequest request) throws TException {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        GetSuperiorRequest getSuperiorRequest = new GetSuperiorRequest();
        getSuperiorRequest.setTenantId(user.getTenantId());
        getSuperiorRequest.setAccountId(request.getSubordinateAccountId());
        GetSuperiorResponse getSuperiorResponse = pullNewQueryThriftService.getSuperior(getSuperiorRequest);
        if (!isSuccess(getSuperiorResponse.getCode())) {
            throw new FlowValveException(getSuperiorResponse.getCode(), getSuperiorResponse.getMsg());
        }
        if (!getSuperiorResponse.getSuperiorAccountId().equals(user.getAccountId())) {
            throw new FlowValveException(ResultCodeEnum.PARAM_ERR.getCode(), "该用户并非当前用户的下级");
        }

        AppPullNewEsRequest req = new AppPullNewEsRequest();
        req.setMarkId(request.getMarkId());
        req.setEndTime(request.getEndTime());
        req.setStoreId(request.getStoreId());
        req.setStartTime(request.getStartTime());
        req.setAccountId(request.getSubordinateAccountId());
        req.setAccountName(request.getSubordinateAccountName());
        req.setPageSize(request.getPageSize());
        return pullNewDetailES(req);
    }

    public PullNewRecordVo queryPullNewDetail(PullNewRecordRequest request) throws TException {

        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        AppPullNewEsRequest req = new AppPullNewEsRequest();
        req.setMarkId(request.getMarkId());
        req.setEndTime(request.getEndTime());
        req.setStoreId(request.getStoreId());
        req.setStartTime(request.getStartTime());
        req.setAccountId(user.getAccountId());
        req.setAccountName(user.getAccountName());
        req.setPageSize(request.getPageSize());

        return pullNewDetailES(req);
    }

    private PullNewRecordVo pullNewDetailES(AppPullNewEsRequest req) throws TException {
        AppPullNewEsDetailResponse response = pullNewThriftService.queryAppPullNew(req);
        if (!isSuccess(response.getCode())) {
            throw new FlowValveException(response.getCode(), response.getMsg());
        }
        PullNewRecordVo vo = new PullNewRecordVo();
        vo.setMarkId(response.getMarkId());
        vo.setCancelCount(response.getCancelFollowCount());
        vo.setHasMore(response.isHasMore());
        vo.setFirstOrderCount(response.getNewOrderCount());
        vo.setValidCount(response.getValidFollowCount());
        vo.setTwoOrderCount(response.getTwoOrderCount());
        vo.setCancelTwoOrderCount(response.getSecondOrderCancelCount());
        vo.setCancelFirstOrderCount(response.getFirstOrderCancelCount());
        vo.setAbnormalFirstOrderCnt(response.getAbnormalFirstOrderCount());
        vo.setAbnormalSecondOrderCnt(response.getAbnormalSecondOrderCount());
        vo.setUnSubscribeRate(response.getUnSubscribeRate());
        vo.setValidProductOrderCnt(response.getValidProductOrderCnt());
        vo.setCancelProductOrderCnt(response.getCancelProductOrderCnt());
        vo.setInvalidProductOrderCnt(response.getInvalidProductOrderCnt());
        vo.setValidFriendCnt(response.getValidFriendCnt());
        vo.setDelFriendCnt(response.getDelFriendCnt());
        vo.setDelPercent(response.getDelPercent());
        vo.setValidWaimaiOrderCnt(response.getValidWaimaiOrderCnt());
        vo.setInvalidWaimaiOrderCnt(response.getInvalidWaimaiOrderCnt());
        vo.setCancelWaimaiOrderCnt(response.getCancelWaimaiOrderCnt());
        vo.setValidAnnualMemberCnt(response.getValidAnnualMemberCnt());
        vo.setValidSeasonMemberCnt(response.getValidSeasonMemberCnt());
        vo.setCancelAnnualMemberCnt(response.getCancelAnnualMemberCnt());
        vo.setCancelSeasonMemberCnt(response.getCancelSeasonMemberCnt());
        vo.setInvalidMemberCnt(response.getInvalidMemberCnt());
        if (CollectionUtils.isEmpty(response.getPullNewDetails())) {
            vo.setPromotionRecords(Lists.newArrayList());
        } else {
            vo.setPromotionRecords(response.getPullNewDetails().stream()
                    .map(detail -> {
                        PullNewDetailVo pullNewDetailVo = new PullNewDetailVo();
                        pullNewDetailVo.setEventTime(detail.getEventTime());
                        pullNewDetailVo.setEventName(detail.getEventName());
                        pullNewDetailVo.setUserName(detail.getUserName());
                        pullNewDetailVo.setEventType(detail.getEventType());
                        pullNewDetailVo.setUserPic(detail.getHeadImage());
                        pullNewDetailVo.setOrderNo(detail.getOrderNo());
                        pullNewDetailVo.setOrderBizType(detail.getOrderBizType());
                        pullNewDetailVo.setNote(detail.getNote());
                        return pullNewDetailVo;
                    }).collect(Collectors.toList()));
        }

        return vo;
    }

    public void bindPhone(String phone) throws TException {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        BindWeComPhoneRequest bindWeComPhoneRequest = new BindWeComPhoneRequest();
        bindWeComPhoneRequest.setPhone(phone);
        bindWeComPhoneRequest.setAccountId(user.getAccountId());
        bindWeComPhoneRequest.setAccountName(user.getAccountName());
        BindWeComPhoneResponse response = pullNewThriftService.bindWeComPhone(bindWeComPhoneRequest);
        if (response == null || response.getStatus() == null) {
            throw new FlowValveException(ResultCode.FAIL.code, "绑定企微手机号失败");
        } else if (!isSuccess(response.getStatus().getCode())) {
            throw new FlowValveException(response.getStatus().getCode(), response.getStatus().getMsg());
        }
    }

    public String queryBindPhone() throws TException {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        QueryWeComBindPhoneRequest bindWeComPhoneRequest = new QueryWeComBindPhoneRequest();
        bindWeComPhoneRequest.setAccountId(user.getAccountId());
        bindWeComPhoneRequest.setAccountName(user.getAccountName());
        QueryWeComBindPhoneResponse response = pullNewThriftService.queryWeComPhone(bindWeComPhoneRequest);
        if (response == null || response.getStatus() == null) {
            throw new FlowValveException(ResultCode.FAIL.code, "绑定企微手机号失败");
        } else if (!isSuccess(response.getStatus().getCode())) {
            throw new FlowValveException(response.getStatus().getCode(), response.getStatus().getMsg());
        }
        if (StringUtils.isNotBlank(response.getDesensitizedPhone())) {
            return response.getDesensitizedPhone();
        }
        // 返回百川手机号
        return desensitizePhone(user.getOperatorPhone());
    }

    private String desensitizePhone(String phone) {
        if (StringUtils.isBlank(phone)) {
            return phone;
        }
        if (phone.length() <= 3) {
            return phone;
        }
        return phone.length() > 7 ? phone.substring(0, 3) + "****" + phone.substring(7) : phone.substring(0, 3) + "****";
    }

    private boolean isSuccess(int code) {
        return Objects.equals(ResultCodeEnum.SUCCESS.getCode(), code);
    }

    public PullNewStatMultiModuleVo queryPullNewWithModule(PullNewMultiModuleRequest req) {
        AppPullNewEsRequest request = new AppPullNewEsRequest();
        request.setEndTime(req.getEndTime());
        request.setStoreId(req.getStoreId());
        request.setStartTime(req.getStartTime());
        request.setQueryType(0);
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        request.setAccountId(user.getAccountId());
        AppPullNewRecordResponse response = pullNewThriftService.appQueryRecordAndAggModule(request);
        if (response == null) {
            throw new FlowValveException(ResultCode.FAIL.code, "查询多模块拉新数据失败");
        } else if (!isSuccess(response.getCode())) {
            throw new FlowValveException(response.getCode(), response.getMsg());
        }
        if (CollectionUtils.isEmpty(response.getPullNewAggModuleDtoList())) {
            return null;
        }
        PullNewStatMultiModuleVo vo = new PullNewStatMultiModuleVo();
        List<PullNewAggModuleVo> list = response.getPullNewAggModuleDtoList().stream().map(dto -> {
            PullNewAggModuleVo vo1 = new PullNewAggModuleVo();
            vo1.setName(dto.getName());
            vo1.setOneLineCount(dto.getOneLineCount());
            if (CollectionUtils.isNotEmpty(dto.getPullNewAggSubModuleVos())) {
                List<PullNewAggSubModuleVo> subModuleVos = new ArrayList<>();
                for (PullNewAggSubModuleDto subDto : dto.getPullNewAggSubModuleVos()) {
                    PullNewAggSubModuleVo subModuleVo = new PullNewAggSubModuleVo();
                    subModuleVo.setName(subDto.getName());
                    subModuleVo.setDesc(subDto.getDesc());
                    subModuleVo.setValue(subDto.getValue());
                    subModuleVos.add(subModuleVo);
                }
                vo1.setPullNewAggSubModuleVos(subModuleVos);
            }
            return vo1;
        }).collect(Collectors.toList());
        vo.setList(list);
        return vo;
    }

    public PullNewMyRecordVo pageQueryPullNewRecord(PullNewRecordRequest req) {

        AppPullNewEsRequest request = new AppPullNewEsRequest();
        request.setEndTime(req.getEndTime());
        request.setStoreId(req.getStoreId());
        request.setStartTime(req.getStartTime());
        request.setMarkId(req.getMarkId());
        request.setPageSize(req.getPageSize());
        request.setQueryType(1);
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        request.setAccountName(user.getAccountName());
        request.setAccountId(user.getAccountId());
        AppPullNewRecordResponse response = pullNewThriftService.appQueryRecordAndAggModule(request);
        if (response == null) {
            throw new FlowValveException(ResultCode.FAIL.code, "查询员工拉新记录失败");
        } else if (!isSuccess(response.getCode())) {
            throw new FlowValveException(response.getCode(), response.getMsg());
        }
        if (CollectionUtils.isEmpty(response.getPullNewSimpleDetailDtoList())) {
            return null;
        }
        PullNewMyRecordVo vo = new PullNewMyRecordVo();
        vo.setMarkId(response.getMarkId());
        vo.setHasMore(response.isHasMore());
        List<PullNewDetailVo> pullNewDetailVos = new ArrayList<>();
        for (PullNewSimpleDetailDto detailDto : response.getPullNewSimpleDetailDtoList()) {
            PullNewDetailVo detailVo = new PullNewDetailVo();
            detailVo.setUserName(detailDto.getUserName());
            detailVo.setEventName(detailDto.getEventName());
            detailVo.setEventType(detailDto.getEventType());
            detailVo.setOrderNo(detailDto.getOrderNo());
            detailVo.setEventTime(detailDto.getEventTime());
            detailVo.setNote(detailDto.getNote());
            pullNewDetailVos.add(detailVo);
        }
        vo.setPullNewDetailVos(pullNewDetailVos);

        return vo;
    }

    public List<PullNewDetailVo> queryByOrder(QueryPullNewByOrderRequest req) {
        QueryPullNewRecordsByOrderIdRequest request = new QueryPullNewRecordsByOrderIdRequest();
        request.setOrderId(req.getOrderId());
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        request.setAccountId(user.getAccountId());
        QueryPullNewRecordByOrderIdResponse response = pullNewThriftService.queryPulNewRecordsByOrderId(request);
        if (response == null) {
            throw new FlowValveException(ResultCode.FAIL.code, "绑定企微手机号失败");
        } else if (!isSuccess(response.getCode())) {
            throw new FlowValveException(response.getCode(), response.getMsg());
        }
        if (CollectionUtils.isEmpty(response.getPullNewSimpleDetailDtoList())) {
            return Collections.emptyList();
        }
        return response.getPullNewSimpleDetailDtoList().stream().map(dto -> {
            PullNewDetailVo vo = new PullNewDetailVo();
            vo.setEventName(dto.getEventName());
            vo.setEventTime(dto.getEventTime());
            vo.setOrderNo(dto.getOrderNo());
            vo.setNote(dto.getNote());
            vo.setUserName(dto.getUserName());
            return vo;
        }).collect(Collectors.toList());

    }

    public PullNewWarehouseRankingPageVo queryRankingList(PageQueryWarehouseRankingListRequest req) {
        PullNewWarehouseRankingPageVo pageVo = new PullNewWarehouseRankingPageVo();
        PageQueryPullNewRankingListRequest request = new PageQueryPullNewRankingListRequest();
        request.setPageNo(req.getPage());
        request.setPageSize(req.getPageSize());
        request.setRankType(req.getRankType());
        request.setDepartmentType(req.getDepartmentType());
        request.setDepartmentId(req.getDepartmentId());
        request.setStartTimeStr(req.getStartTimeStr());
        request.setEndTimeStr(req.getEndTimeStr());
        request.setDataType(req.getDataType());
        QueryPullNewRankingListResponse response = pullNewThriftService.queryWarehouseRankingList(request);
        if (response == null) {
            throw new FlowValveException(ResultCode.FAIL.code, "查询地推排行榜失败");
        } else if (!isSuccess(response.getCode())) {
            throw new FlowValveException(response.getCode(), response.getMsg());
        }
        if (Objects.nonNull(response.getDataUpdateTime())) {
            pageVo.setDataUpdateTime(TimeUtils.convertTimeStamp2YMDHMStr(response.getDataUpdateTime()));
        }
        pageVo.setHasMore(response.isHasMore());
        if (CollectionUtils.isEmpty(response.getPullNewWarehouseRankingDtoList())) {
            return pageVo;
        }

        List<PullNewWarehouseRankingVo> list = response.getPullNewWarehouseRankingDtoList().stream().map(dto -> {
            PullNewWarehouseRankingVo vo1 = new PullNewWarehouseRankingVo();
            vo1.setItemName(dto.getItemName());
            vo1.setItemRank(String.valueOf(dto.getItemRank()));
            vo1.setItemValue(String.valueOf(dto.getItemValue()));
            vo1.setItemId(dto.getItemId());
            vo1.setExtraInfo(dto.getExtraInfo());
            return vo1;
        }).collect(Collectors.toList());
        pageVo.setPullNewWarehouseRankingVoList(list);
        return pageVo;
    }

    public PullNewWarehouseRankDetailInfo queryRankingDetail(QueryWarehouseRankingDetailRequest req) {
        PullNewWarehouseRankDetailInfo detailInfo = new PullNewWarehouseRankDetailInfo();
        QueryPullNewRankingDetailRequest request = new QueryPullNewRankingDetailRequest();
        request.setTargetId(req.getTargetId());
        if (req.getRankType() == 2 && Objects.nonNull(req.getTargetId())) {
            User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
            if (Objects.nonNull(user)) {
                request.setTargetId((int) user.getAccountId());
            }
        }
        request.setRankType(req.getRankType());
        request.setDepartmentType(req.getDepartmentType());
        request.setDepartmentId(req.getDepartmentId());
        request.setStartTimeStr(req.getStartTimeStr());
        request.setEndTimeStr(req.getEndTimeStr());
        request.setDataType(req.getDataType());
        QueryPullNewRankingDetailResponse response = pullNewThriftService.queryWarehouseRankingDetail(request);
        if (response == null) {
            throw new FlowValveException(ResultCode.FAIL.code, "查询地推排行榜个人排名失败");
        } else if (!isSuccess(response.getCode())) {
            throw new FlowValveException(response.getCode(), response.getMsg());
        }
        detailInfo.setText(response.getText());
        detailInfo.setItemName(response.getItemName());
        detailInfo.setItemValue(String.valueOf(response.getItemValue()));
        detailInfo.setItemRank(String.valueOf(response.getItemRank()));
        return detailInfo;
    }

    private Map<String, Boolean> getHasAuthMap(List<String> authCodes) {
        int QIAN_NIU_HUA_APP_ID = 5;
        SessionInfo currentSession = SessionContext.getCurrentSession();
        Map<String, Boolean> authenticationMap = new HashMap<>();
        int appId = QIAN_NIU_HUA_APP_ID;
        Map<String, Boolean> stringBooleanMap = sacWrapper.accountAuthPermissions(currentSession.getAccountId(),
                appId, authCodes);
        if (MapUtils.isNotEmpty(stringBooleanMap)) {
            authenticationMap = stringBooleanMap;
        }
        return authenticationMap;
    }


}
