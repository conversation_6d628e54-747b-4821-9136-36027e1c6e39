package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-12-08
 * @email <EMAIL>
 */
@TypeDoc(
        description = "审批信息"
)
@Data
@ApiModel("审批信息")
@AllArgsConstructor
@NoArgsConstructor
public class ApprovalVO {

    @FieldDoc(
            description = "审批详情"
    )
    private String approvalText;

    @FieldDoc(
            description = "审批详情"
    )
    private List<String> approvalNameList;

    @FieldDoc(
            description = "状态"
    )
    private Integer status;

    @FieldDoc(
            description = "完成时间"
    )
    private Long doneTime;

    @FieldDoc(
            description = "实际审批人"
    )
    private String finishNodeName;

    @FieldDoc(
            description = "审批备注(驳回理由)"
    )
    private String remark;

}
