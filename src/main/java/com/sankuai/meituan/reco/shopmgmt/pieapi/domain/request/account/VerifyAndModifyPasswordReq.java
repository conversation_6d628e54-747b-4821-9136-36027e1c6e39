package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.account;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.request.SendForgetPassWordVerificationCodeRequest;
import com.sankuai.meituan.shangou.saas.common.aop.feature.Validatable;
import com.sankuai.meituan.shangou.saas.common.utils.AssertUtil;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/07/20
 */
@TypeDoc(
        description = "验证并修改密码请求"
)
@Data
public class VerifyAndModifyPasswordReq implements Validatable {

    @FieldDoc(
            description = "uuid"

    )
    private String uuid;

    @FieldDoc(
            description = "账号名"
    )
    private String accountName;

    @FieldDoc(
            description = "新密码"

    )
    private String password;

    @FieldDoc(
            description = "验证码"
    )
    private String smscode;

    //密码正则
    private static final String PASSWORD_REGULAR = "^(?=.*[A-Za-z])(?=.*\\d)[A-Za-z\\d]{6,16}$";


    @Override
    public void validate() {
        AssertUtil.isTrue(StringUtils.isNotBlank(uuid), "uuid 不能为空");
        AssertUtil.isTrue(StringUtils.isNotBlank(accountName), "accountName 不能为空");
        AssertUtil.isTrue(StringUtils.isNotBlank(password), "password 不能为空");
        AssertUtil.isTrue(StringUtils.isNotBlank(smscode), "smscode 不能为空");
           // 密码强度校验 统一交由 SacPasswordManagerThriftService#checkPasswordLegal 处理
      //  AssertUtil.isTrue(password.matches(PASSWORD_REGULAR), "密码格式不正确：6-16位，必须包含数字和字母");
    }

    public SendForgetPassWordVerificationCodeRequest toThriftRequest() {
        SendForgetPassWordVerificationCodeRequest request = new SendForgetPassWordVerificationCodeRequest();
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        String os = identityInfo.getOs();
        request.setAccountName(accountName);
        HttpServletRequest httpRequest =
                ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String ip = httpRequest.getHeader("x-real-ip");
        String ua = httpRequest.getHeader("user-agent");
        request.setUuid(uuid);
        request.setIp(ip);
        request.setUa(ua);
        request.setPlatform(os);
        request.setVersion(identityInfo.getAppVersion());
        request.setPassword(password);
        request.setSmscode(smscode);
        return request;
    }
}
