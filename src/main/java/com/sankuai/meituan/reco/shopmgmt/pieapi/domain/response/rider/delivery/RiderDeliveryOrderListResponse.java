package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.delivery;

import java.util.List;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分页查询骑手配送订单列表响应.
 *
 * <AUTHOR>
 * @since 2021/6/11 15:24
 */
@TypeDoc(
        description = "分页查询骑手配送订单列表响应"
)
@Data
@ApiModel("分页查询骑手配送订单列表响应")
@NoArgsConstructor
@AllArgsConstructor
public class RiderDeliveryOrderListResponse {

    @FieldDoc(
            description = "分页信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "分页信息", required = true)
    private PageInfoVO pageInfo;

    @FieldDoc(
            description = "订单列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单列表", required = true)
    private List<RiderDeliveryOrderVo> orderList;
}
