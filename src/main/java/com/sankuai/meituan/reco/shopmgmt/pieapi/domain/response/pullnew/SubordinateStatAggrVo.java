package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2021/11/1 5:47 下午
 **/
@TypeDoc(
        description = "下级拉新统计聚合"
)
@Data
@ApiModel("展示下级拉新统计聚合")
@NoArgsConstructor
@AllArgsConstructor
public class SubordinateStatAggrVo {
    @FieldDoc(
            description = "是否有下一页", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否有下一页", required = true)
    private boolean hasMore;

    @FieldDoc(
            description = "分页标识", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "分页标识")
    private Long markId;

    @FieldDoc(
            description = "下级的统计列表"
    )
    @ApiModelProperty(value = "下级的统计列表")
    private List<SubordinateStatVo> subordinateStats;
}
