package com.sankuai.meituan.reco.shopmgmt.pieapi.service.poi;

import com.sankuai.meituan.reco.shopmgmt.pieapi.service.area.bo.AreaBO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.poi.bo.PoiInfoBO;

import java.util.List;

/**
 * <AUTHOR>
 * Email <EMAIL>
 * Date 2022/9/7 8:04 下午
 * Description
 */
public interface PoiService {

    /**
     * 根据地区信息查询门店列表
     *
     * @param tenantId  租户 id
     * @param areaInfo  地区信息
     * @param searchKey 搜索条件
     * @return List<PoiInfoBO>
     */
    List<PoiInfoBO> queryPois(Long tenantId, AreaBO areaInfo, String searchKey);

    /**
     * 根据地区信息查询 poi 列表
     *
     * @param tenantId                              租户 id
     * @param areaInfo                              地区信息
     * @param searchKey                             搜索条件
     * @param entityTypes                           实体类型，为空时只查询门店
     * @param excludeShareableWarehouseBindingStore 是否排除绑定共享前置仓的门店
     * @return List<PoiInfoBO>
     */
    List<PoiInfoBO> queryPois(Long tenantId, AreaBO areaInfo, String searchKey, List<Integer> entityTypes, boolean excludeShareableWarehouseBindingStore);

    /**
     * 获取门店信息
     *
     * @param tenantId  租户id
     * @param poiId     门店id
     * @return
     */
    PoiInfoBO getPoiById(Long tenantId, Long poiId);

}
