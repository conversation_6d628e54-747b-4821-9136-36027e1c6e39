package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "加班申请详情"
)
@ApiModel("加班申请详情")
@Data
public class ExtraWorkApplyVO {

    @FieldDoc(
            description = "申诉记录baseInfoId"
    )
    @ApiModelProperty("申诉记录baseInfoId")
    private Long id;

    @FieldDoc(
            description = "申请加班出勤日期，格式：yyyy-MM-dd", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "申请加班出勤日期，格式：yyyy-MM-dd")
    private String applyAttendanceDay;


    @FieldDoc(
            description = "申请日期，格式：yyyy-MM-dd", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "申请时间")
    private Long createTime;

    @FieldDoc(
            description = "申述照片", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "申述照片")
    private List<String> photoList;

    @FieldDoc(
            description = "加班上线打卡时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "加班上线打卡时间")
    private Long checkinTime;

    @FieldDoc(
            description = "加班下线打卡时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "加班下线打卡时间")
    private Long checkoutTime;

    @FieldDoc(
            description = "申诉用户Id"
    )
    @ApiModelProperty("申诉用户Id")
    private Long applyUserId;

    @FieldDoc(
            description = "申诉用户名"
    )
    @ApiModelProperty("申诉用户名")
    private String applyUserName;

    @FieldDoc(
            description = "申诉完结时间"
    )
    @ApiModelProperty("申诉完结时间")
    private Long finishTime;

    @FieldDoc(
            description = "申诉原因说明"
    )
    @ApiModelProperty("申诉原因说明")
    private String applyText;

    @FieldDoc(
            description = "门店id"
    )
    @ApiModelProperty("门店id")
    private Long poiId;

    @FieldDoc(
            description = "门店名称"
    )
    @ApiModelProperty("门店名称")
    private String poiName;

    @FieldDoc(
            description = "审批结果"
    )
    @ApiModelProperty("审批结果")
    private Integer approvalStatus;

    @FieldDoc(
            description = "审批结果描述"
    )
    @ApiModelProperty("审批结果描述")
    private String approvalStatusText;

    @FieldDoc(
            description = "当前申述总次数"
    )
    @ApiModelProperty("当前申述总次数")
    private Integer approvalTimes;

    @FieldDoc(
            description = "最大允许申述总次数"
    )
    @ApiModelProperty("最大允许申述总次数")
    private Integer maxApprovalTimes;
}
