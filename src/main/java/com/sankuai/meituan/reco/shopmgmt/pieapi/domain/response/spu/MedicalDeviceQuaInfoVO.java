package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.MedicalDeviceQuaInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.MedicalDeviceQuaInfoIdl;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 医疗器械资质相关信息
 *
 * <AUTHOR>
 * @since 2024/8/2
 */
@TypeDoc(description = "医疗器械资质相关信息")
@Data
public class MedicalDeviceQuaInfoVO {

    @FieldDoc(
            description = "资质图图片链接",
            rule = "",
            example = "https://img.meituan.net/sgfn/0ddb624b8d6cd44918c7bb18aef3dc1f522891.jpg"
    )
    @ApiModelProperty("资质图图片链接")
    private List<String> quaPictures;

    @FieldDoc(
            description = "商品资质有效期，格式为 yyyy-MM-dd 或 -1，为-1时代表永久有效",
            rule = "必须大当前日期，值为-1时代表永久有效",
            example = "2023-11-11"
    )
    @ApiModelProperty("商品资质有效期")
    private String quaEffectiveDate;

    @FieldDoc(
            description = "商品资质审批通过日期，格式为 yyyy-MM-dd",
            rule = "必须小于当前日期",
            example = "2022-11-11"
    )
    @ApiModelProperty("商品资质审批通过日期")
    private String quaApprovalDate;

    public static MedicalDeviceQuaInfoVO fromMedicalDeviceQuaDTO(MedicalDeviceQuaInfoDTO medicalDeviceQuaInfoDTO) {
        if (medicalDeviceQuaInfoDTO == null) {
            return null;
        }

        MedicalDeviceQuaInfoVO medicalDeviceQuaInfoVO = new MedicalDeviceQuaInfoVO();
        medicalDeviceQuaInfoVO.setQuaPictures(medicalDeviceQuaInfoDTO.getQuaPictures());
        medicalDeviceQuaInfoVO.setQuaApprovalDate(medicalDeviceQuaInfoDTO.getQuaApprovalDate());
        medicalDeviceQuaInfoVO.setQuaEffectiveDate(medicalDeviceQuaInfoDTO.getQuaEffectiveDate());

        return medicalDeviceQuaInfoVO;
    }

    public static MedicalDeviceQuaInfoVO fromMedicalDeviceQuaDTO(com.sankuai.meituan.shangou.empower.productbiz.client.dto.MedicalDeviceQuaInfoDTO medicalDeviceQuaInfoDTO) {
        if (medicalDeviceQuaInfoDTO == null) {
            return null;
        }

        MedicalDeviceQuaInfoVO medicalDeviceQuaInfoVO = new MedicalDeviceQuaInfoVO();
        medicalDeviceQuaInfoVO.setQuaPictures(medicalDeviceQuaInfoDTO.getQuaPictures());
        medicalDeviceQuaInfoVO.setQuaApprovalDate(medicalDeviceQuaInfoDTO.getQuaApprovalDate());
        medicalDeviceQuaInfoVO.setQuaEffectiveDate(medicalDeviceQuaInfoDTO.getQuaEffectiveDate());

        return medicalDeviceQuaInfoVO;
    }

    public com.sankuai.meituan.shangou.empower.productbiz.client.dto.MedicalDeviceQuaInfoDTO toMedicalDeviceQuaInfoDTO() {
        com.sankuai.meituan.shangou.empower.productbiz.client.dto.MedicalDeviceQuaInfoDTO medicalDeviceQuaInfoDTO = new com.sankuai.meituan.shangou.empower.productbiz.client.dto.MedicalDeviceQuaInfoDTO();
        medicalDeviceQuaInfoDTO.setQuaPictures(this.getQuaPictures());
        medicalDeviceQuaInfoDTO.setQuaApprovalDate(this.getQuaApprovalDate());
        medicalDeviceQuaInfoDTO.setQuaEffectiveDate(this.getQuaEffectiveDate());

        return medicalDeviceQuaInfoDTO;
    }

    public MedicalDeviceQuaInfoDTO toOcmsMedicalDeviceQuaInfoDTO() {
        MedicalDeviceQuaInfoDTO medicalDeviceQuaInfoDTO = new MedicalDeviceQuaInfoDTO();
        medicalDeviceQuaInfoDTO.setQuaPictures(this.getQuaPictures());
        medicalDeviceQuaInfoDTO.setQuaApprovalDate(this.getQuaApprovalDate());
        medicalDeviceQuaInfoDTO.setQuaEffectiveDate(this.getQuaEffectiveDate());

        return medicalDeviceQuaInfoDTO;
    }

    public MedicalDeviceQuaInfoIdl toOcmsMedicalDeviceQuaInfoIdl() {
        MedicalDeviceQuaInfoIdl medicalDeviceQuaInfo = new MedicalDeviceQuaInfoIdl();
        medicalDeviceQuaInfo.setQuaPictures(this.getQuaPictures());
        medicalDeviceQuaInfo.setQuaApprovalDate(this.getQuaApprovalDate());
        medicalDeviceQuaInfo.setQuaEffectiveDate(this.getQuaEffectiveDate());
        return medicalDeviceQuaInfo;
    }
}
