package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ReviewReasonTemplateDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;

/**
 * @description: 审核拒绝原因模板
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-03-11
 **/
@TypeDoc(
        description = "审核拒绝原因模板"
)
@Data
@ApiModel("审核拒绝原因模板")
@NoArgsConstructor
public class ReviewReasonTemplateVO {


    @FieldDoc(
            description = "ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "ID")
    private Long id;

    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "租户ID")
    private Long tenantId;

    @FieldDoc(
            description = "拒绝原因", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "拒绝原因")
    private String reason;

    @FieldDoc(
            description = "创建时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "创建时间")
    private Long createTime;

    public ReviewReasonTemplateVO buildReviewReasonTemplateVO(ReviewReasonTemplateDTO reviewReasonTemplateDTO){

        this.id = reviewReasonTemplateDTO.getId();
        if (StringUtils.isNotEmpty(reviewReasonTemplateDTO.getReason())){
            this.reason = reviewReasonTemplateDTO.getReason().trim();
        }
        this.createTime = reviewReasonTemplateDTO.getCreateTime();
        this.tenantId = reviewReasonTemplateDTO.getTenantId();
        return this;

    }

}
