package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 创建编辑商品部分成功信息
 * @author: liyu44
 * @create: 2020-02-04
 **/
@TypeDoc(
        description = "创建编辑商品部分成功信息"
)
@Data
@ApiModel("创建编辑商品部分成功信息")
public class SaveCdqStoreSkuPartitionSuccessResponse {
    @FieldDoc(
            description = "错误文案", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "错误文案")
    private String errorMsg;

    @FieldDoc(
            description = "错误编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "错误编码")
    private Integer errorCode;
}
