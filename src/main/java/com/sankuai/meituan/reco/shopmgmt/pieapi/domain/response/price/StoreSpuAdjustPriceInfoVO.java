package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.ChannelStoreSkuKey;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.SkuPurchasePriceVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.bo.ChannelTypeEnumBo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelSpuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.StoreSpuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.shangou.empower.price.client.dto.strategy.SkuHitSyncStrategyDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

@TypeDoc(
        description = "SPU商品改价信息"
)
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
@Data
@ApiModel("SPU商品改价信息")
public class StoreSpuAdjustPriceInfoVO {

    @FieldDoc(
            description = "城市id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "城市id", required = true)
    private Long regionId;

    @FieldDoc(
            description = "区域名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "区域名称")
    private String regionName;

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店id", required = true)
    private Long storeId;

    @FieldDoc(
            description = "门店名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店名称", required = true)
    private String storeName;

    @FieldDoc(
            description = "spu编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "spu编码", required = true)
    private String spuId;

    @ApiModelProperty(name = "商品名称", required = true)
    private String name;

    @FieldDoc(
            description = "商品图片", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品图片", required = true)
    private List<String> images;

    @FieldDoc(
            description = "称重类型 1-称重计量 2-称重计件 3-非称重"
    )
    @ApiModelProperty(name = "称重类型")
    private Integer weightType;

    @FieldDoc(
            description = "sku改价信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "sku改价信息", required = true)
    private List<StoreSkuAdjustPriceDetailVO> skuAdjustPriceDetailVOList;

    @FieldDoc(
            description = "1 单规格 2 多规格"
    )
    @ApiModelProperty(name = "称重类型")
    private Integer specType;

    @FieldDoc(
            description = "多渠道门店商品状态，-1未上线，1上架，2下架", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "多渠道门店商品状态，-1未上线，1上架，2下架")
    public Integer storeSaleStatus;

    @FieldDoc(
            description = "渠道商品SPU列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道商品SPU列表")
    private List<ChannelSpuVO> channelSpuList;

    @FieldDoc(
            description = "价格分位模式，见PriceCentModeEnum", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "价格分位模式，见PriceCentModeEnum")
    private Integer priceCentMode;

    public static StoreSpuAdjustPriceInfoVO build(StoreSpuVO storeSpuVO) {

        if (storeSpuVO == null) {
            return null;
        }

        StoreSpuAdjustPriceInfoVO storeSpuAdjustPriceInfoVO = new StoreSpuAdjustPriceInfoVO();
        if (storeSpuVO.getRegion() != null) {
            storeSpuAdjustPriceInfoVO.setRegionId(storeSpuVO.getRegion().getRegionId());
            storeSpuAdjustPriceInfoVO.setRegionName(storeSpuVO.getRegion().getRegionName());
        }
        if (storeSpuVO.getStore() != null) {
            storeSpuAdjustPriceInfoVO.setStoreId(storeSpuVO.getStore().getStoreId());
            storeSpuAdjustPriceInfoVO.setStoreName(storeSpuVO.getStore().getStoreName());
        }

        storeSpuAdjustPriceInfoVO.setSpuId(storeSpuVO.getSpuId());
        storeSpuAdjustPriceInfoVO.setName(storeSpuVO.getName());
        storeSpuAdjustPriceInfoVO.setImages(storeSpuVO.getImages());
        storeSpuAdjustPriceInfoVO.setWeightType(storeSpuVO.getWeightType());
        storeSpuAdjustPriceInfoVO.setSpecType(storeSpuVO.getSpecType());
        storeSpuAdjustPriceInfoVO.setStoreSaleStatus(storeSpuVO.getStoreSaleStatus());
        storeSpuAdjustPriceInfoVO.setChannelSpuList(storeSpuVO.getChannelSpuList());

        return storeSpuAdjustPriceInfoVO;
    }

    public static StoreSpuAdjustPriceInfoVO build(StoreSpuVO storeSpuVO,
                                                  List<SkuHitSyncStrategyDTO> skuHitSyncStrategyDTOList,
                                                  List<SkuPurchasePriceVo> purchasePriceList,
                                                  List<ChannelTypeEnumBo> channelTypeEnums,
                                                  Integer priceCentMode) {

        if (storeSpuVO == null) {
            return null;
        }

        // 定价策略分组
        Map<ChannelStoreSkuKey, SkuHitSyncStrategyDTO> channelStoreSku2StrategyMap = ConverterUtils.listStreamMapToMap(skuHitSyncStrategyDTOList,
                strategyDTO -> ChannelStoreSkuKey.of(strategyDTO.getStoreId(), strategyDTO.getChannelId(), strategyDTO.getSkuId()), Function.identity());

        // 采购价分组
        Map<String, SkuPurchasePriceVo> skuPurchasePriceMap = ConverterUtils.listStreamMapToMap(purchasePriceList, SkuPurchasePriceVo::getSkuId, Function.identity());

        List<StoreSkuAdjustPriceDetailVO> storeSkuAdjustPriceDetailVOList = Lists.newArrayList();

        // 如果门店开通了渠道，则以门店+渠道+规格维度组装零售价和采购价信息
        if (CollectionUtils.isNotEmpty(channelTypeEnums)) {
            storeSpuVO.getStoreSkuList().forEach(storeSkuVO -> {
                SkuPurchasePriceVo priceDto = skuPurchasePriceMap.get(storeSkuVO.getSkuId());
                channelTypeEnums.forEach(channelTypeEnum -> {
                    SkuHitSyncStrategyDTO strategy = channelStoreSku2StrategyMap.get(ChannelStoreSkuKey.of(storeSkuVO.getStoreId(), channelTypeEnum.getChannelId(), storeSkuVO.getSkuId()));
                    if (Objects.nonNull(strategy)) {
                        storeSkuAdjustPriceDetailVOList.add(StoreSkuAdjustPriceDetailVO.buildStoreSkuAdjustPriceDetailVO(storeSpuVO, storeSkuVO, channelTypeEnum, strategy, priceDto, priceCentMode));
                    } else {
                        storeSkuAdjustPriceDetailVOList.add(StoreSkuAdjustPriceDetailVO.buildStoreSkuAdjustPriceDetailVO(storeSpuVO, storeSkuVO, channelTypeEnum, priceDto));
                    }
                });
            });
        } else {
            //如果门店未开通任何渠道，则仅以门店+规格组装采购价信息
            storeSpuVO.getStoreSkuList().forEach(storeSkuVO -> {
                SkuPurchasePriceVo priceDto = skuPurchasePriceMap.get(storeSkuVO.getSkuId());
                storeSkuAdjustPriceDetailVOList.add(StoreSkuAdjustPriceDetailVO.buildStoreSkuAdjustPriceDetailVO(storeSpuVO, storeSkuVO, null, priceDto));
            });
        }

        // 构建调价SPU商品改价信息
        StoreSpuAdjustPriceInfoVO storeSpuAdjustPriceInfoVO = StoreSpuAdjustPriceInfoVO.build(storeSpuVO);
        storeSpuAdjustPriceInfoVO.setSkuAdjustPriceDetailVOList(storeSkuAdjustPriceDetailVOList);
        storeSpuAdjustPriceInfoVO.setPriceCentMode(priceCentMode);

        return storeSpuAdjustPriceInfoVO;
    }

}
