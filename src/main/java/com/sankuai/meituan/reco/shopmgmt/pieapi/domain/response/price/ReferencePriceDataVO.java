package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MoneyUtils;
import com.sankuai.meituan.shangou.empower.price.client.dto.price_effect.ReferencePriceDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

@TypeDoc(
        description = "参考价",
        authors = "hejunliang"
)
@ApiModel("参考价")
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
@Data
public class ReferencePriceDataVO {
    @FieldDoc(
            description = "参考价类型code"
    )
    @ApiModelProperty("参考价类型code")
    private String code;

    @FieldDoc(
            description = "参考价类型名称"
    )
    @ApiModelProperty("参考价类型名称")
    private String name;

    @FieldDoc(
            description = "标准单位参考价, 单位:元"
    )
    @ApiModelProperty("标准单位参考价")
    private BigDecimal standardUnitReferencePrice;

    public static ReferencePriceDataVO build(ReferencePriceDTO dto) {

        if (dto == null) {
            return null;
        }

        ReferencePriceDataVO referencePriceDataVO = new ReferencePriceDataVO();
        referencePriceDataVO.setCode(dto.getReferencePriceType());
        referencePriceDataVO.setName(dto.getReferencePriceName());
        referencePriceDataVO.setStandardUnitReferencePrice(dto.getStandardUnitReferencePrice() != null
                ? MoneyUtils.centToYuanDecimalByDown(dto.getStandardUnitReferencePrice()) : null);

        return referencePriceDataVO;
    }
}
