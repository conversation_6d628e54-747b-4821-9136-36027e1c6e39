package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MoneyUtils;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.StoreSkuDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Title: StoreSkuDetailVO
 * @Description: 门店商品SKU详情信息
 * @Author: zhaolei12
 * @Date: 2020/5/16 5:41 下午
 */
@TypeDoc(
        description = "门店商品SKU详情信息"
)
@Data
@ApiModel("门店商品SKU详情信息")
public class StoreSkuDetailVO {

    @FieldDoc(
            description = "SKU编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "SKU编码", required = true)
    @NotNull
    private String skuId;
    @FieldDoc(
            description = "UPC编码列表，最多6个", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "UPC编码列表，最多6个")
    private List<String> upcList;
    @FieldDoc(
            description = "规格", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "规格")
    private String spec;
    @FieldDoc(
            description = "重量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "重量")
    private Integer weight;
    @FieldDoc(
            description = "门店商品价格（单位-元）,创建时必传", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店商品价格（单位-元）")
    @NotNull
    private Long storePrice;
    @FieldDoc(
            description = "售卖单位", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "售卖单位", required = true)
    @NotNull
    private String saleUnit;
    @FieldDoc(
            description = "库存", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "库存")
    private Integer stock;
    @FieldDoc(
            description = "(详情返回)自定义库存标记:0-非自定义库存 1-自定义库存", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "自定义库存标记")
    private Integer customizeStockFlag;
    @FieldDoc(
            description = "(详情返回)自定义库存数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "自定义库存数量")
    private Integer customizeStockQuantity;
    @FieldDoc(
            description = "(详情返回)第二天是否自动恢复无限库存：0-不自动恢复 1-自动恢复", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "第二天是否自动恢复无限库存")
    private Integer autoResumeInfiniteStock;

    public static List<StoreSkuDetailVO> ofDTOList(List<StoreSkuDTO> storeSkuDTOList) {
        if (CollectionUtils.isEmpty(storeSkuDTOList)) {
            return Lists.newArrayList();
        }

        return storeSkuDTOList.stream().filter(Objects::nonNull).map(StoreSkuDetailVO::ofDTO).collect(Collectors.toList())
                .stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static StoreSkuDetailVO ofDTO(StoreSkuDTO storeSkuDTO) {
        if (storeSkuDTO == null) {
            return null;
        }
        StoreSkuDetailVO storeSkuDetailVO = new StoreSkuDetailVO();
        storeSkuDetailVO.setSkuId(storeSkuDTO.getSkuId());
        storeSkuDetailVO.setUpcList(storeSkuDTO.getUpcList());
        storeSkuDetailVO.setSpec(storeSkuDTO.getSpec());
        storeSkuDetailVO.setWeight(storeSkuDTO.getWeight());
        if (storeSkuDTO.getStorePrice() != null) {
            storeSkuDetailVO.setStorePrice(MoneyUtils.yuanToCent(storeSkuDTO.getStorePrice()));
        }
        storeSkuDetailVO.setSaleUnit(storeSkuDTO.getUnit());
        storeSkuDetailVO.setStock(storeSkuDTO.getStock());
        storeSkuDetailVO.setCustomizeStockFlag(storeSkuDTO.getCustomizeStockFlag());
        storeSkuDetailVO.setCustomizeStockQuantity(storeSkuDTO.getCustomizeStockQuantity());
        storeSkuDetailVO.setAutoResumeInfiniteStock(storeSkuDTO.getAutoResumeInfiniteStock());

        return storeSkuDetailVO;
    }

}
