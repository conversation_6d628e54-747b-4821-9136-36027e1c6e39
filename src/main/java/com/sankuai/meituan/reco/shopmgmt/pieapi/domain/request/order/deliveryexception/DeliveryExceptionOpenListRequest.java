package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order.deliveryexception;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * @Author: <EMAIL>
 * @Date: 2020-02-18 16:13
 * @Description:
 */
@TypeDoc(
        description = "配送异常待处理列表请求"
)
@Data
public class DeliveryExceptionOpenListRequest {

    @FieldDoc(
            description = "租户ID",
            requiredness = Requiredness.REQUIRED
    )
    @NotNull(message = "租户不合法")
    @Min(value = 1, message = "租户不合法")
    private Long tenantId;

    @FieldDoc(
            description = "门店ID",
            requiredness = Requiredness.REQUIRED
    )
    @NotNull(message = "门店不合法")
    @Min(value = 1, message = "门店不合法")
    private Long storeId;

    @FieldDoc(
            description = "当前页;最小值为1，默认值为1",
            requiredness = Requiredness.REQUIRED
    )
    @NotNull(message = "当前页不合法")
    @Min(value = 1, message = "当前页不合法")
    private Integer pageNo = 1;

    @FieldDoc(
            description = "每页大小,默认20",
            requiredness = Requiredness.OPTIONAL
    )
    @Min(value = 1, message = "每页大小不合法")
    private Integer pageSize = 20;

}

