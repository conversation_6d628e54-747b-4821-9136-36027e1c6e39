package com.sankuai.meituan.reco.shopmgmt.pieapi.service.appmodel.workbench;

import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.shangou.munich.assistant.client.enums.TaskTypeEnum;
import com.meituan.shangou.munich.assistant.client.response.task.TaskDetailResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.sac.dto.model.SacMenuNodeDto;
import com.sankuai.meituan.reco.shopmgmt.pieapi.converters.assistant.AssistantTaskConverter;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.appmodel.QueryMenuInfoRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.appmodel.MenuInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.MenuCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.AssistantTaskWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.TenantWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 工作台歪马模块菜单service
 *
 * <AUTHOR>
 * @since 2021/7/5
 */
@Service
@Slf4j
public class WorkbenchWMTaskModelMenuService extends AbstractWorkBenchMenuService {
    @Autowired
    private AssistantTaskWrapper assistantTaskWrapper;

    @Autowired
    private TenantWrapper tenantWrapper;

    @Autowired
    private  WMTaskModelMenuHelper wmTaskModelMenuHelper;

    @Override
    protected MenuCodeEnum getMenuCode() {
        return MenuCodeEnum.WM_PROCESSING_TASK;
    }

    @Override
    protected Boolean hideParentMenu(Set<String> hasAuthSubMenuCodes) {
        return CollectionUtils.isEmpty(hasAuthSubMenuCodes);
    }

    @Override
    protected List<MenuInfo> getSubMenuInfos(Map<String, SacMenuNodeDto> menuCodeWithNodeMap,
                                             IdentityInfo identityInfo, QueryMenuInfoRequest request, Boolean possibleNewQueryGray) {
        try {
            CompletableFuture<List<MenuInfo>> munichSubMenuInfosFuture = CompletableFuture.supplyAsync(() -> getMunichSubMenuInfos(menuCodeWithNodeMap, identityInfo, request));
            CompletableFuture<List<MenuInfo>> extSubMenuInfosFuture = CompletableFuture.supplyAsync(() -> wmTaskModelMenuHelper.getExtSubMenuInfos(menuCodeWithNodeMap, identityInfo));
            CompletableFuture.allOf(munichSubMenuInfosFuture, extSubMenuInfosFuture).join();

            List<MenuInfo> menuInfos = munichSubMenuInfosFuture.get();
            List<MenuInfo> extMenuInfos = extSubMenuInfosFuture.get();
            if (Objects.nonNull(extMenuInfos)){
                menuInfos.addAll(extMenuInfos);
            }
            return menuInfos;
        } catch (Exception e) {
            log.error("getSubMenuInfos exception", e);
            return new ArrayList<>();
        }

    }

    private List<MenuInfo> getMunichSubMenuInfos(Map<String, SacMenuNodeDto> menuCodeWithNodeMap, IdentityInfo identityInfo, QueryMenuInfoRequest request) {
        // 针对任务菜单code，获取任务类型，并查询对应的数据
        Set<String> subMenuCodes = new HashSet<>(menuCodeWithNodeMap.keySet());
        List<TaskTypeEnum> taskTypes = getNeedTaskTypes(subMenuCodes);
        // 门店数 > 1，过滤掉不支持多门店的taskType
        if (identityInfo.getStoreIdList().size() > 1) {
            taskTypes = taskTypes.stream().filter(TaskTypeEnum::isSupportMultiPoi).collect(Collectors.toList());
        }

        Map<TaskTypeEnum, List<TaskDetailResponse.PoiTaskDetailData>> response = assistantTaskWrapper.getTaskDetail(
                identityInfo.getUser().getTenantId(),
                identityInfo.getStoreIdList(),
                identityInfo.getUser().getAccountId(),
                taskTypes);
        return buildMenuInfo(menuCodeWithNodeMap, response, identityInfo);
    }


    private List<MenuInfo> buildMenuInfo(Map<String, SacMenuNodeDto> menuCodeWithNodeMap, Map<TaskTypeEnum, List<TaskDetailResponse.PoiTaskDetailData>> input, IdentityInfo identityInfo) {
        Map<Long, PoiInfoDto> poiInfoDtoMap = tenantWrapper.queryPoiByIds(identityInfo.getUser().getTenantId(), identityInfo.getStoreIdList());

        return input.entrySet().stream().map(entry -> {
            if (CollectionUtils.isEmpty(entry.getValue())) {
                log.warn("任务查询没有返回数据 taskTypeEnum:{}, tenantId:{}, accountId:{}",
                        entry.getKey(), identityInfo.getUser().getTenantId(), identityInfo.getUser().getAccountId());
                return null;
            }
            MenuCodeEnum menuCodeEnum = MenuCodeEnum.ofTaskType(entry.getKey());
            SacMenuNodeDto sacMenuNodeDto = menuCodeWithNodeMap.get(menuCodeEnum.getCode());
            MenuInfo menuInfo = buildMenuInfoBySacMenuNodeDto(sacMenuNodeDto);

            int taskCount = entry.getValue().stream().mapToInt(TaskDetailResponse.PoiTaskDetailData::getTaskCount).sum();
            int delayTaskCount = entry.getValue().stream().mapToInt(TaskDetailResponse.PoiTaskDetailData::getDelayTaskCount).sum();
            menuInfo.setTaskCount(taskCount);
            menuInfo.setDelayTaskCount(delayTaskCount);
            List<MenuInfo> children = entry.getValue().stream().map(detail -> buildDetailMenuInfo(sacMenuNodeDto, poiInfoDtoMap, detail))
                    .filter(poiMenu -> poiMenu.getTaskCount() > 0)
                    .collect(Collectors.toList());
            menuInfo.setChildren(children);
            return menuInfo;
        }).filter(Objects::nonNull)
                .filter(menuInfo -> menuInfo.getTaskCount() > 0)
                .collect(Collectors.toList());
    }

    private MenuInfo buildDetailMenuInfo(SacMenuNodeDto sacMenuNodeDto, Map<Long, PoiInfoDto> poiInfoDtoMap, TaskDetailResponse.PoiTaskDetailData detailData) {
        MenuInfo menuInfo = new MenuInfo();
        menuInfo.setMenuCode(AssistantTaskConverter.POI_NAME_KEY);
        menuInfo.setMenuName(poiInfoDtoMap.get(detailData.getPoiId()).poiName);
        menuInfo.setTaskCount(detailData.getTaskCount());
        menuInfo.setDelayTaskCount(detailData.getDelayTaskCount());
        menuInfo.setUrl(sacMenuNodeDto.getRouteUrl());
        menuInfo.setExtraInfoMap(StringUtils.isBlank(sacMenuNodeDto.getMetaData()) ? new HashMap<>() :
                JacksonUtils.parseMap(sacMenuNodeDto.getMetaData(), String.class, Object.class));
        menuInfo.getExtraInfoMap().put(AssistantTaskConverter.POI_ID_KEY, detailData.getPoiId());
        menuInfo.setTaskQueryType(Integer.valueOf(menuInfo.getExtraInfoMap().getOrDefault(AssistantTaskConverter.TASK_QUERY_TYPE,"1").toString()));
        menuInfo.setHasAuth(true);
        return menuInfo;
    }

    private List<TaskTypeEnum> getNeedTaskTypes(Set<String> hasAuthMenuCode) {
        List<String> filterMunichSubMenu = MccConfigUtil.getFilterMunichSubMenu();
        if (CollectionUtils.isNotEmpty(filterMunichSubMenu)){
            // 过滤掉不需要执行的任务
            hasAuthMenuCode = hasAuthMenuCode.stream().filter(menuCode -> !filterMunichSubMenu.contains(menuCode)).collect(Collectors.toSet());
        }
        hasAuthMenuCode.retainAll(Arrays.stream(MenuCodeEnum.values()).map(MenuCodeEnum::getCode).collect(Collectors.toSet()));
        return hasAuthMenuCode.stream().map(MenuCodeEnum::ofAuthCode).filter(Objects::nonNull).map(MenuCodeEnum::getTaskTypeEnum).collect(Collectors.toList());
    }

}