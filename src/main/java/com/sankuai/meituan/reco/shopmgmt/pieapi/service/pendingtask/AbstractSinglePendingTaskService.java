/*
 * Copyright (c) 2019 Meituan.com. All Rights Reserved.
 */
package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask;

import com.google.common.collect.Maps;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import org.assertj.core.util.Sets;

import java.util.Map;
import java.util.Set;


/**
 * 抽象单个未处理任务service
 * <br><br>
 * Author: linjianyu <br>
 * Date: 2019-03-27 Time: 13:27
 */
public abstract class AbstractSinglePendingTaskService extends AbstractBatchPendingTaskService {

    @Override
    protected final Map<AuthCodeEnum, PendingTaskResult> getPendingTaskCountMap(PendingTaskParam param) throws Exception {
        Map<AuthCodeEnum, PendingTaskResult> map = Maps.newHashMap();
        map.put(module(), getPendingTaskCount(param));
        return map;
    }

    @Override
    protected final Set<AuthCodeEnum> authCodeEnumSet() {
        Set<AuthCodeEnum> moduleSet = Sets.newHashSet();
        moduleSet.add(module());
        return moduleSet;
    }

    protected abstract PendingTaskResult getPendingTaskCount(PendingTaskParam param) throws Exception;

    protected abstract AuthCodeEnum module();
}
