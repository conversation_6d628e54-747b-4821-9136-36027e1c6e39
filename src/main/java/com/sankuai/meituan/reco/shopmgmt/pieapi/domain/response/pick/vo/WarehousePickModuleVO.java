package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "待领取数据"
)
@Data
@ApiModel("待领取数据")
public class WarehousePickModuleVO {

    @FieldDoc(
            description = "波次号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "波次号")
    private String waveOrderId;

    @FieldDoc(
            description = "创建时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @FieldDoc(
            description = "任务列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "任务列表")
    private List<WarehousePickTaskModuleVO> taskList;

    @FieldDoc(
            description = "货主信息列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "货主信息列表")
    private List<WarehouseGoodsOwnerInfoVO> goodsOwnerInfoList;

    @FieldDoc(
            description = "收货门店信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "收货门店信息")
    private List<WarehouseReceiveStoreInfoVO> receiveStoreInfoList;

}
