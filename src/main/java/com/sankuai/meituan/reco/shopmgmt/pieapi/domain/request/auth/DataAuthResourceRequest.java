package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.auth;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;


@TypeDoc(
        description = "权限信息请求"
)
@Data
@ApiModel("权限信息请求")
public class DataAuthResourceRequest {

    @FieldDoc(
            description = "租户id"
    )
    @Min(1)
    @ApiModelProperty(value = "租户id", required = true)
    @NotNull
    private Long tenantId;

    @FieldDoc(
            description = "门店id"
    )
    @ApiModelProperty(value = "门店id", required = true)
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "资源code，即parentCode，据此查询其子类资源"
    )
    @ApiModelProperty(value = "资源code", required = true)
    @NotEmpty
    private String code;

    @FieldDoc(
            description = "资源类型"
    )
    @ApiModelProperty(value = "资源类型 0-全部(默认查询全部 )，1-菜单，2-页面，3-按钮，4-字段，5-子页面")
    private int type;
}
