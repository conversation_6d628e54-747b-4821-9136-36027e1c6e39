package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.impl;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.auth.AppModuleRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.AbstractSinglePendingTaskService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.ManagementWrapper;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.common.CommonParam;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.common.ThriftResult;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.ordermerchandise.OrderMerchandiseThriftService;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.ordermerchandise.request.OrderMerchandiseCountRequest;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.ordermerchandise.response.OrderMerchandiseCountResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 迁移不规范的要货小红点
 *
 * @see ManagementWrapper#revisionHomepage(Long, Long, List, AppModuleRequest)
 */
@Slf4j
@Service
public class SupplyChainOrderTaskService extends AbstractSinglePendingTaskService {

    private static final Integer WAIT_CONFIRM_ORDER_STATUS = 1;

    @Autowired
    @Qualifier("purchaseOrderMerchandiseThriftService")
    private OrderMerchandiseThriftService orderMerchandiseThriftService;

    @Override
    protected PendingTaskResult getPendingTaskCount(PendingTaskParam param) throws Exception {
        OrderMerchandiseCountResp orderMerchandiseCountResp = this.countOrderMerchandise(param.getTenantId(), param.getStoreIds());
        Long cnt = Optional.ofNullable(orderMerchandiseCountResp).map(OrderMerchandiseCountResp::getTotalCount).orElse(0L);
        return PendingTaskResult.createNumberMarker(Math.toIntExact(cnt));
    }


    private OrderMerchandiseCountResp countOrderMerchandise(Long tenantId, List<Long> storeIdList) {
        if (CollectionUtils.isEmpty(storeIdList)) {
            return OrderMerchandiseCountResp.builder().totalCount(0L).build();
        }
        OrderMerchandiseCountRequest countRequest = buildOrderMerchandiseCountReq(tenantId, storeIdList);
        try {
            ThriftResult<OrderMerchandiseCountResp> thriftResult = orderMerchandiseThriftService.countOrderMerchandise(countRequest);
            if (thriftResult.isFailure()) {
                log.error("查询待处理要货单数量失败，request:{}, response:{}", countRequest, thriftResult);
                return null;
            }
            return thriftResult.getData();
        } catch (Exception e) {
            log.error("查询待处理要货单数量失败，request:{}", countRequest, e);
        }
        return null;
    }

    private static OrderMerchandiseCountRequest buildOrderMerchandiseCountReq(Long tenantId, List<Long> storeIdList) {
        CommonParam commonParam = new CommonParam();
        commonParam.setTenantId(tenantId);
        commonParam.setStoreId(storeIdList.get(0));
        return OrderMerchandiseCountRequest.builder()
                .commonParam(commonParam)
                .orderStatus(WAIT_CONFIRM_ORDER_STATUS)
                .build();
    }


    @Override
    protected AuthCodeEnum module() {
        return AuthCodeEnum.SUPPLY_CHAIN_ORDER;
    }

}
