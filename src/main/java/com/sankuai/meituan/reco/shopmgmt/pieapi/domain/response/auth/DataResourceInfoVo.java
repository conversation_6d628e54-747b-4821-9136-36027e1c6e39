package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.auth;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created by ya<PERSON><PERSON> on 19/1/30.
 */
@Data
@ApiModel("权限资源信息对象VO")
public class DataResourceInfoVo {

    @ApiModelProperty(value = "资源id")
    private long id;

    @ApiModelProperty(value = "应用id")
    private int appId;

    @ApiModelProperty(value = "资源code")
    private String code;

    @ApiModelProperty(value = "父级资源code，根节点为空")
    private String parentCode;

    @ApiModelProperty(value = "资源名称")
    private String name;

    @ApiModelProperty(value = "字段权限列标识,如 name=手机号, field=mobile  业务系统自行定义规则，用于存储列明。")
    private String field;

    @ApiModelProperty(value = "资源url")
    private String uri;

    @ApiModelProperty(value = "资源方法 1-GET,2-PUT,3-POST,4-DELETE")
    private int method;

    @ApiModelProperty(value = "资源类型 1-菜单 2-页面 3-按钮  4-字段 5-子页面(数组菜单下子页面，不展示在左侧菜单栏)")
    private int type;

    @ApiModelProperty(value = "状态，是否有效0-停用，1有效")
    private int valid;
}
