package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.OrderHomePageModuleVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.OrderPendingTaskVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @createTime 2019/11/25
 * @description
 */
@TypeDoc(
        description = "拣货首页查询返回"
)
@Data
@ApiModel("拣货首页查询返回")
public class PickHomePageResponse {

    @FieldDoc(
            description = "拣货首页模块", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "拣货首页模块", required = true)
    @NotNull
    private PickHomePageModuleVO pickHomePageModuleVO;

    @FieldDoc(
            description = "拣货首页代办", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "拣货首页代办", required = true)
    @NotNull
    private PickHomePageDataVO pickHomePageDataVO;
}
