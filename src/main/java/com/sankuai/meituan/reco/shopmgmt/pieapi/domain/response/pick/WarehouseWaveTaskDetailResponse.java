package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.WarehouseGoodsOwnerInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.WarehouseContainerBoxModuleVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.WarehouseReceiveStoreInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@TypeDoc(
        description = "查询波次任务详情返回"
)
@Data
@ApiModel("查询波次任务详情返回")
public class WarehouseWaveTaskDetailResponse {

    @FieldDoc(
            description = "波次号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "波次号")
    private String waveId;

    @FieldDoc(
            description = "订单号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "订单号")
    private String orderId;

    @FieldDoc(
            description = "任务号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "任务号")
    private Integer taskId;

    @FieldDoc(
            description = "波次创建时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "波次创建时间")
    private String waveCreateTime;

    @FieldDoc(
            description = "任务领取时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "任务领取时间")
    private String receiveTime;

    @FieldDoc(
            description = "拣货完成时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "拣货完成时间")
    private String pickedTime;

    @FieldDoc(
            description = "分拣开始时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "分拣开始时间")
    private String seedStartTime;

    @FieldDoc(
            description = "容器列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "容器列表")
    private List<WarehouseContainerBoxModuleVO> containerBoxList;

    @FieldDoc(
            description = "是否拣货/分拣完成", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否拣货/分拣完成")
    private Boolean isCompleted;

    @FieldDoc(
            description = "收货门店信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "收货门店信息")
    private List<WarehouseReceiveStoreInfoVO> receiveStoreInfoList;

    @FieldDoc(
            description = "1正常、2临期预警、3临期禁售、4超期预警", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "1正常、2临期预警、3临期禁售、4超期预警")
    private Integer periodStatus;

    @FieldDoc(
            description = "装箱码，如果不为装箱模式返回null，待绑定箱子时为空字符串，绑定箱子时为对应装箱码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "装箱码，如果不为装箱模式返回null，待绑定箱子时为空字符串，绑定箱子时为对应装箱码")
    private String huCode;

    @FieldDoc(
            description = "装箱模式下收货方门店名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "装箱模式下收货方门店名称")
    private String storeNameForPack;

    @FieldDoc(
            description = "货主信息列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "货主信息列表")
    private List<WarehouseGoodsOwnerInfoVO> goodsOwnerInfoList;

    @FieldDoc(
            description = "波次单对应的出库单类型，参考OutboundOrderTypeEnum", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "波次单对应的出库单类型")
    private Integer outboundOrderType;
}
