package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.booth;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/10/15 15:34
 * Description: 摊位管理VO
 */
@TypeDoc(
        description = "摊位VO",
        authors = {
                "liyang176"
        },
        version = "V1.0"
)
@Data
public class BoothMngVO {

    @FieldDoc(
            description = "摊位id"
    )
    private Long boothId;

    @FieldDoc(
            description = "摊位名称"
    )
    private String boothName;

    @FieldDoc(
            description = "摊位对应部门id"
    )
    private Long depId;

    @FieldDoc(
            description = "摊位所属门店id"
    )
    private Long poiId;

    @FieldDoc(
            description = "摊位备货方式。1-自采 2-外采"
    )
    private Integer boothPickingType;

    @FieldDoc(
            description = "现结支付宝账户"
    )
    private String onSiteAlipayAccount;

    @FieldDoc(
            description = "现结支付宝账户的真实姓名"
    )
    private String onSiteAlipayRealname;

    @FieldDoc(
            description = "摊位检索码"
    )
    private String quickSearchCode;

    @FieldDoc(
            description = "摊位检索码绑定时间"
    )
    private String searchCodeBindTime;
}
