package com.sankuai.meituan.reco.shopmgmt.pieapi.service;

import com.meituan.linz.boot.exception.BusinessException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.regionselect.rsm.FileVO;
import com.sankuai.meituan.shangou.saas.common.storage.StorageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;

/**
 * S3存储封装类
 * <AUTHOR>
 * @since 2022/12/20
 */
@Service
@Slf4j
public class FileService {

    @Autowired
    private StorageService s3StorageService;

    /**
     * 上传文件
     *
     * @param file
     * @return
     */
    public CommonResponse<FileVO> uploadFileWithUrl(MultipartFile file) {
        try {
            InputStream inputStream = file.getInputStream();
            String objectKey = s3StorageService.uploadV2(file.getOriginalFilename(), inputStream);

            return CommonResponse.success(new FileVO(objectKey, getUrl(objectKey, 3 * 60, true)));
        } catch (IOException e) {
            log.warn("上传文件失败 文件名：={}", file.getOriginalFilename(), e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "上传文件失败");
        }
    }

    /**
     * 上传文件
     *
     * @param file
     * @return
     */
    public CommonResponse<FileVO> uploadFileWithUrl(MultipartFile file, int expireSeconds) {
        try {
            InputStream inputStream = file.getInputStream();
            String objectKey = s3StorageService.uploadV2(file.getOriginalFilename(), inputStream);

            return CommonResponse.success(new FileVO(objectKey, getUrl(objectKey, expireSeconds, true)));
        } catch (IOException e) {
            log.warn("上传文件失败 文件名：={}", file.getOriginalFilename(), e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "上传文件失败");
        }
    }


    /**
     * 获取下载链接<br>
     *
     * @param objectKey
     * @param expireSeconds 有效时长，单位秒
     * @param publicUrl     是否公网可用
     * @return
     */
    public String getUrl(String objectKey, int expireSeconds, boolean publicUrl) {
        String url = s3StorageService.getTempDownloadUrl(objectKey, expireSeconds, publicUrl);
        if (StringUtils.isBlank(url)) {
            throw new BusinessException( "获取下载链接失败");
        }
        return url;
    }

}
