package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery.orderplatform;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * RaiseTipReq
 *
 * <AUTHOR>
 * @since 2023/2/27
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class RaiseTipRequest extends OrderPlatformDeliveryReq {

    /**
     * 消费金额
     */
    @NotNull
    private Integer tip;

}
