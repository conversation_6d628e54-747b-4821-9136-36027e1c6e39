package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@TypeDoc(
        description = "查询已分拣列表请求"
)
@ApiModel("查询已分拣列表请求")
@Data
public class WarehouseSeedCompleteQueryRequest {

    @FieldDoc(
            description = "关键词", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "关键词")
    private String keyword;

    @FieldDoc(
            description = "页码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "页码")
    private Long pageNo;

    @FieldDoc(
            description = "个数", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "个数")
    private Integer pageSize;
}
