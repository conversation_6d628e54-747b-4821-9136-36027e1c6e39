package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import java.util.List;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ChannelSaleAttrValueInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.ReviewSkuSupplyRelationAndPurchaseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SkuImageInfo;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.review.ReviewTenantSkuDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 提报商品sku
 *
 * <AUTHOR>
 * @since 2024/08/19
 */
@TypeDoc(
        description = "提报商品sku"
)
@ApiModel("提报商品sku")
@Data
@NoArgsConstructor
public class ReviewTenantSkuVO {

    @FieldDoc(description = "商品sku")
    @ApiModelProperty(name = "商品sku")
    private String skuId;

    @FieldDoc(description = "规格名称")
    @ApiModelProperty(name = "规格名称")
    private String specName;

    @FieldDoc(description = "upc")
    @ApiModelProperty(name = "upc")
    private List<String> upcList;

    @FieldDoc(description = "基本单位")
    @ApiModelProperty(name = "基本单位")
    private String baseUnit;

    @FieldDoc(description = "重量")
    @ApiModelProperty(name = "重量")
    private Integer weight;

    @FieldDoc(description = "重量单位")
    @ApiModelProperty(name = "重量单位")
    private String weightUnit;

    @FieldDoc(description = "带单位的重量")
    @ApiModelProperty(name = "带单位的重量")
    private String weightForUnit;

    @FieldDoc(description = "总部零售价")
    @ApiModelProperty(name = "总部零售价")
    private Double suggestPrice;

    @FieldDoc(description = "采购信息")
    @ApiModelProperty(name = "采购信息")
    private ReviewSkuSupplyRelationAndPurchaseVO purchaseInfo;

    @FieldDoc(description = "销售属性值信息")
    @ApiModelProperty(name = "销售属性值信息")
    private List<ChannelSaleAttrValueInfoVO> channelSaleAttrValueInfoList;

    @FieldDoc(description = "规格图片信息")
    @ApiModelProperty(name = "规格图片信息")
    private SkuImageInfo imageInfo;

    @FieldDoc(description = "渠道sku属性值信息")
    @ApiModelProperty(value = "渠道sku属性值信息")
    private List<ChannelSkuAttrValueInfoVo> channelSkuAttrValueInfoList;

    public static ReviewTenantSkuVO fromReviewTenantSkuDTO(ReviewTenantSkuDTO skuDTO) {
        ReviewTenantSkuVO skuVO = new ReviewTenantSkuVO();
        skuVO.setSkuId(skuDTO.getSkuId());
        skuVO.setSpecName(skuDTO.getSpecName());
        skuVO.setUpcList(skuDTO.getUpcList());
        skuVO.setBaseUnit(skuDTO.getBaseUnit());
        skuVO.setWeight(skuDTO.getWeight());
        skuVO.setWeightUnit(skuDTO.getWeightUnit());
        skuVO.setWeightForUnit(skuDTO.getWeightForUnit());
        skuVO.setSuggestPrice(skuDTO.getSuggestPrice());
        skuVO.setPurchaseInfo(ReviewSkuSupplyRelationAndPurchaseVO.fromDTO(skuDTO.getSupplyPurchase()));
        skuVO.setChannelSaleAttrValueInfoList(Fun.map(skuDTO.getChannelSaleAttributeValues(), ChannelSaleAttrValueInfoVO::of));
        skuVO.setImageInfo(SkuImageInfo.of(skuDTO));
        skuVO.setChannelSkuAttrValueInfoList(Fun.map(skuDTO.getChannelSkuAttrValueInfoList(), ChannelSkuAttrValueInfoVo::of));
        return skuVO;
    }
}
