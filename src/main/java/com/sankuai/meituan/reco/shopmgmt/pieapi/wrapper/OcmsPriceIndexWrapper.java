package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Maps;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ProjectConstants;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.price.PriceIndexCategoryProductPageRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price.PriceIndexCategorySkuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price.PriceIndexStoreCategoryVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price.PriceIndexVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price.SkuPriceIndexVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ChannelPriceAndStockVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ChannelSkuForAppVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.store.saas.infrastructure.shield.common.monitor.CommonMonitorTransaction;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.AccountInfoVo;
import com.sankuai.meituan.shangou.empower.ocms.client.price.dto.index.PriceIndexDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.price.dto.index.SkuPriceIndexDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.price.dto.index.SkuPriceIndexListDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.price.dto.index.StoreCategoryPriceIndexDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.price.enums.ChannelPriceResultEnum;
import com.sankuai.meituan.shangou.empower.ocms.client.price.request.StoreBaseQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.price.request.index.CategorySkuPriceIndexQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.client.price.response.OcmsCommonResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.price.response.index.CategorySkuPriceIndexResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.price.response.index.StoreCategoryPriceIndexResponse;
import com.sankuai.meituan.shangou.empower.ocms.client.price.service.PriceIndexThriftService;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Author: wangyihao04
 * @Date: 2020-03-27 17:32
 * @Mail: <EMAIL>
 */
@Rhino
@Slf4j
public class OcmsPriceIndexWrapper {
    @Resource
    private PriceIndexThriftService priceIndexThriftService;
    @Resource
    private AuthThriftWrapper authThriftWrapper;
    @Resource
    private TenantWrapper tenantWrapper;
    @Resource
    private OCMSServiceWrapper ocmsServiceWrapper;

    private final static String PRICE_INDEX_DETAIL_AUTH_CODE = "57db10ed-e043-4952-98e3-f5719496317b";

    private final static String PRICE_INDEX = "价格指数";

    private final static String PRICE_TOO_HIGI = "价格过高";

    @Degrade(rhinoKey = "OcmsPriceIndexWrapper-queryStoreCategoryPriceIndex",
            fallBackMethod = "queryStoreCategoryPriceIndexFallBack", timeoutInMilliseconds = 1000l)
    @MethodLog(logResponse = true, logRequest = true)
    @CommonMonitorTransaction
    public CommonResponse<PriceIndexStoreCategoryVO> queryStoreCategoryPriceIndex(StoreBaseQueryRequest request) {
        try {
            StoreCategoryPriceIndexResponse response = priceIndexThriftService.queryStoreCategoryPriceIndex(request);
            checkResponseStatus(response.getStatus());
            PriceIndexStoreCategoryVO priceIndexStoreCategoryVO = Convert.storeCategoryPriceIndexToVO(response.getData());
            return CommonResponse.success(priceIndexStoreCategoryVO);
        } catch (CommonLogicException e) {
            log.error("查询门店价格指数服务端异常", e);
            return CommonResponse.fail2(ResultCode.FAIL);
        } catch (TException e) {
            log.error("调用门店价格指数接口异常", e);
            return CommonResponse.fail2(ResultCode.FAIL);
        } catch (Exception e) {
            log.error("查询门店价格指数未知异常", e);
            return CommonResponse.fail2(ResultCode.FAIL);
        }
    }

    public CommonResponse<PriceIndexStoreCategoryVO> queryStoreCategoryPriceIndexFallBack(StoreBaseQueryRequest request) {
        throw new CommonRuntimeException("查询门店价格指数降级异常");
    }

    @Degrade(rhinoKey = "OcmsPriceIndexWrapper-pageCategoryProductPriceIndex",
            fallBackMethod = "pageCategoryProductPriceIndexFallBack", timeoutInMilliseconds = 1000l)
    @MethodLog(logRequest = true, logResponse = true)
    @CommonMonitorTransaction
    public CommonResponse<PriceIndexCategorySkuVO> pageCategoryProductPriceIndex(Long tenantId, PriceIndexCategoryProductPageRequest request) {
        try {

            CategorySkuPriceIndexResponse response = priceIndexThriftService
                    .queryCategoryProductPriceIndex(buildQueryCategoryProductPriceIndexRequest(tenantId, request));
            checkResponseStatus(response.getStatus());

            // 是否spu灰度
            boolean isSpuGray = tenantWrapper.isSpuGray(tenantId);

            // 查询skuId对应的spuId
            Map<String, String> skuId2SpuIdMap = Collections.emptyMap();
            if (isSpuGray && response.getData() != null && CollectionUtils.isNotEmpty(response.getData().getSkuPriceIndexList())) {
                List<String> skuIds = response.getData().getSkuPriceIndexList().stream()
                        .map(SkuPriceIndexDTO::getSkuId).collect(Collectors.toList());
                skuId2SpuIdMap = ocmsServiceWrapper.querySkuId2SpuIdMap(tenantId, request.getStoreId(),
                        ProjectConstants.CHANNEL_SUPPORT, skuIds);
            }

            return CommonResponse.success(Convert.categorySkuPriceIndexToVO(response.getData(), hasPriceIndexDetailAuth(), skuId2SpuIdMap));
        } catch (CommonLogicException e) {
            log.error("查询品类商品价格指数服务端异常", e);
            return CommonResponse.fail2(ResultCode.FAIL);
        } catch (TException e) {
            log.error("调用品类商品价格指数接口异常", e);
            return CommonResponse.fail2(ResultCode.FAIL);
        } catch (Exception e) {
            log.error("查询品类商品价格指数未知异常", e);
            return CommonResponse.fail2(ResultCode.FAIL);
        }
    }

    public CommonResponse<PriceIndexCategorySkuVO> pageCategoryProductPriceIndexFallBack(Long tenantId, PriceIndexCategoryProductPageRequest request) {
        throw new CommonRuntimeException("查询品类商品价格指数降级异常");
    }


    public Boolean hasPriceIndexDetailAuth() {
        return authThriftWrapper.isCodeHasAuth(PRICE_INDEX_DETAIL_AUTH_CODE);
    }


    public void checkResponseStatus(OcmsCommonResponse status) {
        if (Objects.isNull(status) || status.getCode() != ChannelPriceResultEnum.SUCCESS.getCode()) {
            throw new CommonLogicException();
        }
    }

    public CategorySkuPriceIndexQueryRequest buildQueryCategoryProductPriceIndexRequest(Long tenantId, PriceIndexCategoryProductPageRequest request) {
        return CategorySkuPriceIndexQueryRequest.builder()
                .tenantId(tenantId)
                .storeId(request.getStoreId())
                .firstLevelCategoryId(request.getFirstLevelCategoryId())
                .needCategoryPriceIndex(request.isNeedCategoryPriceIndex())
                .pageNum(request.getPageNum())
                .pageSize(request.getPageSize())
                .build();
    }

    static class Convert {
        public static PriceIndexStoreCategoryVO storeCategoryPriceIndexToVO(StoreCategoryPriceIndexDTO dto) {
            PriceIndexVO storePriceIndex = priceIndexDTOToVO(dto.getStorePriceIndex());
            List<PriceIndexVO> categoryPriceIndexList = Optional
                    .ofNullable(dto.getCategoryPriceIndex())
                    .map(List::stream)
                    .orElse(Stream.empty())
                    .map(Convert::priceIndexDTOToVO)
                    .collect(Collectors.toList());
            return new PriceIndexStoreCategoryVO(storePriceIndex, categoryPriceIndexList);
        }

        public static PriceIndexCategorySkuVO categorySkuPriceIndexToVO(SkuPriceIndexListDTO data,
                                                                        Boolean hasAuth,
                                                                        Map<String, String> skuId2SpuIdMap) {

            PriceIndexVO categoryPriceIndex = priceIndexDTOToVO(data.getCategoryPriceIndex());
            List<SkuPriceIndexVO> skuPriceIndexVOS = Optional.ofNullable(data.getSkuPriceIndexList())
                    .map(List::stream)
                    .orElse(Stream.empty())
                    .map(skuPriceIndexDTO -> {
                        SkuPriceIndexVO skuPriceIndexVO = new SkuPriceIndexVO();
                        skuPriceIndexVO.setUniqueId(skuPriceIndexDTO.getSkuId());
                        skuPriceIndexVO.setDisplayName(skuPriceIndexDTO.getSkuName());
                        skuPriceIndexVO.setPriceIndex(skuPriceIndexDTO.getPriceIndex().getPriceIndex());
                        skuPriceIndexVO.setLevel(skuPriceIndexDTO.getPriceIndex().getLevel());
                        if (hasAuth) {
                            skuPriceIndexVO.setDisplayInfo(PRICE_INDEX + skuPriceIndexDTO.getPriceIndex().getDisplayIndex());
                        } else {
                            skuPriceIndexVO.setDisplayInfo(
                                    skuPriceIndexDTO.getPriceIndex().getLevel() >= 1 ? PRICE_TOO_HIGI : StringUtils.EMPTY);
                        }
                        ChannelSkuForAppVO channelSkuForAppVO = new ChannelSkuForAppVO();
                        channelSkuForAppVO.setSku(skuPriceIndexDTO.getSkuId());
                        channelSkuForAppVO.setSpuId(MapUtils.isNotEmpty(skuId2SpuIdMap) ? skuId2SpuIdMap.get(skuPriceIndexDTO.getSkuId()) : null);
                        channelSkuForAppVO.setName(skuPriceIndexDTO.getSkuName());
                        channelSkuForAppVO.setImages(skuPriceIndexDTO.getImageList());
                        channelSkuForAppVO.setWeight(skuPriceIndexDTO.getWeight());
                        channelSkuForAppVO.setSpec(skuPriceIndexDTO.getSpec());
                        channelSkuForAppVO.setMonthSaleAmount(skuPriceIndexDTO.getSales30Day());
                        channelSkuForAppVO.setQuotePrice(Optional
                                .ofNullable(skuPriceIndexDTO.getQuotePrice())
                                .map(Double::valueOf)
                                .orElse(null)
                        );

                        Map<Integer, String> channelAndCategoryId = Maps.newHashMap();
                        channelAndCategoryId.put(ChannelType.MEITUAN.getValue(), skuPriceIndexDTO.getFirstCategoryCode());
                        channelSkuForAppVO.setChannelId2FirstFrontCategoryNameMap(channelAndCategoryId);

                        List<ChannelPriceAndStockVO> priceList = Lists.newArrayList();
                        ChannelPriceAndStockVO offline = new ChannelPriceAndStockVO();
                        offline.setChannelId(-1);
                        offline.setPrice(Double.valueOf(skuPriceIndexDTO.getOfflinePrice()));
                        priceList.add(offline);
                        ChannelPriceAndStockVO mtChannel = new ChannelPriceAndStockVO();
                        mtChannel.setChannelId(ChannelType.MEITUAN.getValue());
                        mtChannel.setPrice(Double.valueOf(skuPriceIndexDTO.getChannelPrice()));
                        priceList.add(mtChannel);
                        channelSkuForAppVO.setChannels(priceList);
                        skuPriceIndexVO.setSkuInfo(channelSkuForAppVO);
                        return skuPriceIndexVO;
                    }).collect(Collectors.toList());

            PageInfoVO pageInfoVO = new PageInfoVO();
            pageInfoVO.setPage(data.getPageInfo().getPage());
            pageInfoVO.setSize(data.getPageInfo().getSize());
            pageInfoVO.setTotalPage(data.getPageInfo().getTotalPage());
            pageInfoVO.setTotalSize(Math.toIntExact(data.getPageInfo().getTotalSize()));
            return new PriceIndexCategorySkuVO(categoryPriceIndex, skuPriceIndexVOS, pageInfoVO);
        }

        public static PriceIndexVO priceIndexDTOToVO(PriceIndexDTO dto) {
            if (Objects.isNull(dto)) {
                return null;
            }
            PriceIndexVO vo = new PriceIndexVO();
            vo.setUniqueId(dto.getUniqueId());
            vo.setPriceIndex(dto.getPriceIndex());
            vo.setLevel(dto.getLevel());
            vo.setDisplayName(dto.getName());
            vo.setDisplayInfo(dto.getDisplayIndex());
            return vo;
        }

    }

}
