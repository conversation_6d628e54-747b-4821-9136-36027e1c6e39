package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.MtLabelDto;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.spu.MtLabelDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2024/8/27
 */
@TypeDoc(
        name = "美团商品标签VO（平台为商品打的标签）",
        description = "美团商品标签VO（平台为商品打的标签）"
)
@Getter
@Setter
@ToString
public class MtLabelVO {
    /**
     * 美团标签ID
     */
    private String mtLabelId;

    /**
     * 美团标签名称
     */
    private String mtLabelName;

    /**
     * 美团标签描述
     */
    private String mtLabelDescription;

    public static MtLabelVO of(MtLabelDTO labelDto) {
        MtLabelVO labelVO = new MtLabelVO();
        labelVO.setMtLabelId(labelDto.getMtLabelId());
        labelVO.setMtLabelName(labelDto.getMtLabelName());
        labelVO.setMtLabelDescription(labelDto.getMtLabelDescription());
        return labelVO;
    }

    public static MtLabelVO ofOcmsDto(MtLabelDto labelDto) {
        MtLabelVO labelVO = new MtLabelVO();
        labelVO.setMtLabelId(labelDto.getMtLabelId());
        labelVO.setMtLabelName(labelDto.getMtLabelName());
        labelVO.setMtLabelDescription(labelDto.getMtLabelDescription());
        return labelVO;
    }

}