package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order;

import com.facebook.swift.codec.ThriftField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 自提码校验响应
 * @author: caimengluan
 * @date: 2023/2/20
 * @time: 16:37
 * Copyright (C) 2019 Meituan
 * All rights reserved
 */
@TypeDoc(
        description = "自提码校验响应"
)
@Data
@ApiModel("自提码校验响应")
public class CheckSelfDeliveryCodeResponse {

    @FieldDoc(
            description = "租户id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "租户id", required = true)
    private Long tenantId;

    @FieldDoc(
            description = "渠道编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道编码", required = true)
    private Integer channelId;

    @FieldDoc(
            description = "渠道名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道名称", required = true)
    private String channelName;

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店id", required = true)
    private Long storeId;

    @FieldDoc(
            description = "门店名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店名称", required = true)
    private String storeName;

    @FieldDoc(
            description = "订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单号", required = true)
    private String channelOrderId;

    @FieldDoc(
            description = "自提码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "自提码", required = true)
    private String selfFetchCode;

    @FieldDoc(
            description = "订单流水号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "订单流水号", required = false)
    public Long orderSerialNumber;

}
