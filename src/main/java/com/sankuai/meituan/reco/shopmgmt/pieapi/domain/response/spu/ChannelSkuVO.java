package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.ChannelStoreSkuKey;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pricetrend.ChannelSkuPriceTrendVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MoneyUtils;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelSkuDTO;
import com.sankuai.meituan.shangou.empower.price.client.dto.ChannelStoreSkuDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Title: ChannelSkuVO
 * @Description: 渠道商品SKU信息
 * @Author: zhaolei12
 * @Date: 2020/5/16 1:08 下午
 */
@TypeDoc(
        description = "渠道商品SKU信息"
)
@Data
@ApiModel("渠道商品SKU信息")
public class ChannelSkuVO {

    @FieldDoc(
            description = "SKU编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "SKU编码", required = true)
    private String skuId;

    @FieldDoc(
            description = "店内码", requiredness = Requiredness.NONE
    )
    @ApiModelProperty(name = "店内码", required = false)
    private String customSkuId;

    @FieldDoc(
            description = "渠道价格 单位:元", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道价格 单位:元", required = true)
    private Long price;

    @FieldDoc(
            description = "渠道库存数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道库存数", required = true)
    private Long stock;

    @FieldDoc(
            description = "库存描述字段", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "库存描述字段")
    private String stockDesc;

    @FieldDoc(
            description = "价格来源(定价方式)，价格来源(定价方式), 0-全部 1-手动定价 2-按提价策略定价 3-等于提报价", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "价格来源(定价方式)，价格来源(定价方式), 0-全部 1-手动定价 2-按提价策略定价 3-等于提报价")
    private Integer priceSource;

    @FieldDoc(
            description = "市斤价, 单位:元", requiredness = Requiredness.NONE
    )
    @ApiModelProperty(name = "市斤价")
    private Double pricePer500g;

    @FieldDoc(
            description = "显示价格趋势图标 true-显示 false-不展示", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "显示价格趋势图标 true-显示 false-不展示", required = true)
    private Boolean showPriceTrendIcon;

    @FieldDoc(
            description = "价格趋势", requiredness = Requiredness.NONE
    )
    @ApiModelProperty(name = "价格趋势", required = true)
    private ChannelSkuPriceTrendVO priceTrend;

    @FieldDoc(
            description = "零售价报价审核状态 1-待审核", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "零售价报价审核状态")
    private Integer reviewStatus;

    @FieldDoc(
            description = "零售价报价价格，单位：分", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "零售价报价价格，单位：分")
    private Long quotePrice;

    @FieldDoc(
            description = "是否在促销中", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否在促销中")
    private Boolean atPromotion = false;

    @FieldDoc(
            description = "是否能改价", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否能改价")
    private Boolean canModifyPrice = true;

    @FieldDoc(
            description = "渠道呈现价和当前价是否一致", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道呈现价和当前价是否一致")
    private Boolean priceEqualSign = true;

    @FieldDoc(
            description = "渠道价", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道价")
    private String presentPrice = "";

    @FieldDoc(
            description = "价格策略1-手动定价 2-通用提价 4-单品提价", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "priceStrategy")
    private Integer priceStrategy;

    @FieldDoc(
            description = "最小起购数", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "minNum")
    private Integer minNum;

    @FieldDoc(
            description = "包装盒数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "boxNum")
    private Integer boxNum;

    @FieldDoc(
            description = "包装盒价格:元", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "boxPrice")
    private String boxPrice;

    public static List<ChannelSkuVO> ofDTOList(List<ChannelSkuDTO> channelSkuDTOList) {
        if (CollectionUtils.isEmpty(channelSkuDTOList)) {
            return Lists.newArrayList();
        }

        return channelSkuDTOList.stream().filter(Objects::nonNull).map(ChannelSkuVO::ofDTO).collect(Collectors.toList())
                .stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static ChannelSkuVO ofDTO(ChannelSkuDTO channelSkuDTO) {
        if (channelSkuDTO == null) {
            return null;
        }
        ChannelSkuVO channelSkuVO = new ChannelSkuVO();
        channelSkuVO.setSkuId(channelSkuDTO.getSkuId());
        channelSkuVO.setCustomSkuId(channelSkuDTO.getCustomSkuId());
        if (channelSkuDTO.getPrice() != null) {
            channelSkuVO.setPrice(MoneyUtils.yuanToCent(channelSkuDTO.getPrice()));
        }
        if (channelSkuDTO.getStock() != null) {
            channelSkuVO.setStock(Long.valueOf(channelSkuDTO.getStock()));
        }
        channelSkuVO.setPriceSource(channelSkuDTO.getPriceSource());
        channelSkuVO.setReviewStatus(channelSkuDTO.getReviewStatus());
        channelSkuVO.setQuotePrice(channelSkuDTO.getQuotePrice());
        channelSkuVO.setPriceStrategy(channelSkuDTO.getPriceStrategy());
        channelSkuVO.setMinNum(channelSkuDTO.getMinOrderCount());
        channelSkuVO.setBoxNum(channelSkuDTO.getBoxNum());
        channelSkuVO.setBoxPrice(String.valueOf(channelSkuDTO.getBoxPrice()));
        return channelSkuVO;
    }

    public static List<ChannelSkuVO> ofBizDTOList(List<com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelSkuDTO> channelSkuDTOList) {
        if (CollectionUtils.isEmpty(channelSkuDTOList)) {
            return Lists.newArrayList();
        }

        return channelSkuDTOList.stream().filter(Objects::nonNull).map(ChannelSkuVO::ofBizDTO).collect(Collectors.toList())
                .stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static ChannelSkuVO ofBizDTO(com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelSkuDTO channelSkuDTO) {
        if (channelSkuDTO == null) {
            return null;
        }
        ChannelSkuVO channelSkuVO = new ChannelSkuVO();
        channelSkuVO.setSkuId(channelSkuDTO.getSkuId());
        channelSkuVO.setCustomSkuId(channelSkuDTO.getCustomSkuId());
        if (channelSkuDTO.getOnlinePrice() != null) {
            channelSkuVO.setPrice(MoneyUtils.yuanToCent(channelSkuDTO.getOnlinePrice()));
        }
        if (channelSkuDTO.getStock() != null) {
            channelSkuVO.setStock(Long.valueOf(channelSkuDTO.getStock()));
        }
        channelSkuVO.setPriceSource(channelSkuDTO.getPriceSource());
        channelSkuVO.setReviewStatus(channelSkuDTO.getReviewStatus());
        channelSkuVO.setQuotePrice(channelSkuDTO.getQuotePrice());
        channelSkuVO.setPriceStrategy(channelSkuDTO.getPriceStrategy());
        channelSkuVO.setMinNum(channelSkuDTO.getMinOrderCount());
        channelSkuVO.setBoxNum(channelSkuDTO.getBoxNum());
        channelSkuVO.setBoxPrice(String.valueOf(channelSkuDTO.getBoxPrice()));
        return channelSkuVO;
    }

}
