package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.picking;

import lombok.Data;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023/8/1 20:55
 **/
@Data
public class QueryPromotePickOrderRequest {
    private Integer page;

    private Integer size;

    public Optional<String> validate() {
        if (Objects.isNull(page) || page <= 0) {
            return Optional.of("页码不合法");
        }

        if (Objects.isNull(size) || size <= 0) {
            return Optional.of("size不合法");
        }

        return Optional.empty();
    }
}
