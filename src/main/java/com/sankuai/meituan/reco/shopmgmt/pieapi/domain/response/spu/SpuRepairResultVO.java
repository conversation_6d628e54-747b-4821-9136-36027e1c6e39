package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.MultiChannelSpuKeyVO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelStoreCustomSpuKeyDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.SpuRepairResultDTO;
import com.meituan.linz.thrift.response.Status;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;

import java.util.Collections;
import java.util.List;

@TypeDoc(
        description = "商品修复结果",
        authors = "hejunliang"
)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SpuRepairResultVO {

    @ApiModelProperty(value = "渠道id", required = true)
    private Integer channelId;

    @FieldDoc(
            description = "商品编码"
    )
    @ApiModelProperty(value = "商品编码", required = true)
    private String customSpuId;

    @FieldDoc(
            description = "结果码 (code = 0, 成功)",
            requiredness = Requiredness.REQUIRED
    )
    private Integer code;

    @FieldDoc(
            description = "提示信息",
            requiredness = Requiredness.OPTIONAL
    )
    private String msg;

    public static List<SpuRepairResultVO> of(Integer channelId, List<String> customSpuIds,
                                             Integer code, String msg) {

        if (CollectionUtils.isEmpty(customSpuIds)) {
            return Collections.emptyList();
        }

        List<SpuRepairResultVO> resultVOList = Lists.newArrayList();
        customSpuIds.forEach(customSpuId -> {
            SpuRepairResultVO resultVO = SpuRepairResultVO.of(channelId, customSpuId, code, msg);
            resultVOList.add(resultVO);
        });

        return resultVOList;
    }

    public static List<SpuRepairResultVO> of(List<SpuRepairResultDTO> resultDTOList) {
        if (CollectionUtils.isEmpty(resultDTOList)) {
            return Collections.emptyList();
        }
        List<SpuRepairResultVO> resultVOList = Lists.newArrayList();
        resultDTOList.forEach(resultDTO -> {
            ChannelStoreCustomSpuKeyDTO customSpuKeyDTO = resultDTO.getCustomSpuKeyDTO();
            Status status = resultDTO.getStatus();
            SpuRepairResultVO resultVO = SpuRepairResultVO.of(customSpuKeyDTO.getChannelId(),
                    customSpuKeyDTO.getCustomSpuId(), status.getCode(), status.getMsg());
            resultVOList.add(resultVO);
        });

        return resultVOList;
    }

    public static SpuRepairResultVO of(Integer channelId, String customSpuId,
                                       Integer code, String msg) {
        SpuRepairResultVO resultVO = new SpuRepairResultVO();
        resultVO.setChannelId(channelId);
        resultVO.setCustomSpuId(customSpuId);
        resultVO.setCode(code);
        resultVO.setMsg(msg);
        return resultVO;
    }

    public static List<SpuRepairResultVO> of(List<MultiChannelSpuKeyVO> spuKeyVOS, Integer code, String msg) {

        if (CollectionUtils.isEmpty(spuKeyVOS)) {
            return Collections.emptyList();
        }

        List<SpuRepairResultVO> resultVOList = Lists.newArrayList();
        spuKeyVOS.forEach(spuKeyVO -> {
            spuKeyVO.getChannelIds().forEach(channelId -> {
                SpuRepairResultVO resultVO = SpuRepairResultVO.of(channelId, spuKeyVO.getCustomSpuId(), code, msg);
                resultVOList.add(resultVO);
            });
        });

        return resultVOList;
    }
}