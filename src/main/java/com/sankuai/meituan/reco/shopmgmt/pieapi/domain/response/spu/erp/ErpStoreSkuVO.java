package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp;

import java.util.List;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ERP门店商品SKU信息
 *
 * <AUTHOR>
 * @since 2023/05/12
 */
@TypeDoc(
        description = "ERP门店商品SKU信息"
)
@Data
@ApiModel("ERP门店商品SKU信息")
public class ErpStoreSkuVO {

    @FieldDoc(description = "租户ID", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "租户ID")
    private Long tenantId;

    @FieldDoc(description = "门店ID", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "门店ID")
    private Long storeId;

    @FieldDoc(description = "spu编码")
    @ApiModelProperty(name = "spu编码")
    private String spuId;

    @FieldDoc(description = "sku编码", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "sku编码")
    private String skuId;

    @FieldDoc(description = "erp编码", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "erp编码")
    private String erpCode;

    @FieldDoc(description = "upc列表", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "upc列表")
    private List<String> upcList;

    @FieldDoc(description = "规格", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "规格")
    private String spec;

    @FieldDoc(description = "重量", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "重量")
    private Integer weight;

    @FieldDoc(description = "售卖单位", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "售卖单位")
    private String saleUnit;

    @FieldDoc(description = "单位转换系数", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "单位转换系数")
    private String transferRatio;

    @FieldDoc(description = "原价", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "原价")
    private String originPrice;

    @FieldDoc(description = "售价", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "售价")
    private String salePrice;

    @FieldDoc(description = "线下售价", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "线下售价")
    private String offlineSalePrice;

    @FieldDoc(description = "线上可售库存", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "线上可售库存")
    private String onlineValidStock;

    @FieldDoc(description = "线下库存", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "线下库存")
    private String offlineValidStock;

}
