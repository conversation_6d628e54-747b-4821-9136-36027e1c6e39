package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.wavepick.converter;

import com.sankuai.meituan.reco.pickselect.thrift.wave.dto.WarehouseSeedWaitTaskModuleDTO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.WarehouseSeedWaitTaskModuleVO;
import org.mapstruct.Mapper;

/**
 * @Description:
 * @Author: zhangjian155
 * @Date: 2022/8/25 17:38
 */
@Mapper(componentModel = "spring", uses = {WarehouseContainerBoxModuleConverter.class})
public abstract class WarehouseSeedWaitTaskModuleConverter {
    public abstract WarehouseSeedWaitTaskModuleVO convert2Vo(WarehouseSeedWaitTaskModuleDTO warehouseSeedWaitTaskModuleDTO);
}
