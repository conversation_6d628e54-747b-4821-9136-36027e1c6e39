package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/8/19
 */
@TypeDoc(
        description = "ERP门店商品SPU开通渠道状态"
)
@Data
@ApiModel("ERP门店商品SPU开通渠道状态")
public class ErpChannelSpuStatusVO {

    @FieldDoc(description = "渠道ID", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "渠道ID")
    private Integer channelId;

    @FieldDoc(description = "售卖状态 -1-未上线 1-已上架 2-已下架", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "售卖状态 -1-未上线 1-已上架 2-已下架")
    private Integer spuStatus;
}
