package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.tenant;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.platform.empower.product.client.dto.StoreSelfPropertiesConfigDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2024/5/22
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(
        description = "门店配置详情"
)
public class StoreConfigInfoVo {

    @FieldDoc(
            description = "门店ID"
    )
    private Long storeId;

    @FieldDoc(
            description = "门店名称"
    )
    private String storeName;

    @FieldDoc(
            description = "开关状态 //0-未设置；1-开启；2-关闭"
    )
    private Integer switchStatus;

    public static StoreConfigInfoVo convert(StoreSelfPropertiesConfigDTO configDTO) {
        StoreConfigInfoVo configInfo = new StoreConfigInfoVo();
        configInfo.setStoreId(configDTO.getStoreId());
        configInfo.setStoreName(configDTO.getStoreName());
        configInfo.setSwitchStatus(configDTO.getSwitchStatus());

        return configInfo;
    }
}
