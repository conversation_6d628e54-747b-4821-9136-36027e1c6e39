package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.regionselect;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/12/7
 */
@TypeDoc(
        description = "仓源事件处理请求"
)
@Data
public class RentalSourceEventRequest {
    @FieldDoc(description = "仓源Id")
    private Long sourceId;
}
