package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.linz.product.channel.EnhanceChannelType;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelCategoryDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.MedicalStandardProductSearchDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StandardProductSearchDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.collections4.ListUtils;

/**
 * 标品库搜索结果
 *
 * <AUTHOR>
 * @date 2021-09-26 20:20
 */
@TypeDoc(
        description = "标品库搜索结果"
)
@Data
@ApiModel("标品库搜索结果")
public class StandardProductSearchVO extends BaseSearchVO {
    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称", required = true)
    private String name;

    @FieldDoc(
            description = "spu编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "spu编码", required = true)
    private String spuId;

    @FieldDoc(
            description = "sku", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "sku", required = true)
    private String skuId;

    @FieldDoc(
            description = "商品图片", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品图片", required = true)
    private List<String> imageList;

    @FieldDoc(
            description = "重量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "重量")
    private Integer weight;

    @FieldDoc(
            description = "带单位的重量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "带单位的重量", required = true)
    private String weightForUnit;

    @FieldDoc(
            description = "重量单位", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "重量单位", required = true)
    private String weightUnit;

    @FieldDoc(
            description = "售卖单位", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "售卖单位", required = true)
    private String saleUnit;

    @FieldDoc(
            description = "规格名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "规格名称", required = true)
    private String spec;

    @FieldDoc(
            description = "upc", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "upc", required = true)
    private String upc;

    @FieldDoc(
            description = "零售价，单位：分，仅租户为默认手动定价时展示", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "零售价，单位：分，仅租户为默认手动定价时展示")
    private Long storePrice;

    @FieldDoc(
            description = "渠道类目名称", requiredness = Requiredness.OPTIONAL
    )
    private String channelCategoryName;

    @FieldDoc(
            description = "渠道类目code", requiredness = Requiredness.OPTIONAL
    )
    private String channelCategoryCode;

    @FieldDoc(
            description = "该渠道类目的商品条形码（UPC）是否必填，0-必填，1-选填 (只有三级分类该属性才有效)", requiredness = Requiredness.OPTIONAL
    )
    private Integer upcRequired;

    @FieldDoc(
            description = "渠道类目路径名称", requiredness = Requiredness.OPTIONAL
    )
    private String channelCategoryNamePath;
    @FieldDoc(
            description = "渠道类目路径code", requiredness = Requiredness.OPTIONAL
    )
    private String channelCategoryCodePath;

    @FieldDoc(
            description = "渠道类目动态信息", requiredness = Requiredness.OPTIONAL
    )
    private List<ChannelDynamicInfoVO> dynamicInfoVOList;

    @FieldDoc(
            description = "标品品牌名称", requiredness = Requiredness.OPTIONAL
    )
    private String brandName;

    @FieldDoc(
            description = "标品品牌id", requiredness = Requiredness.OPTIONAL
    )
    private String brandId;

    @FieldDoc(
            description = "产地", requiredness = Requiredness.OPTIONAL
    )
    private String producingPlace;

    @FieldDoc(
            description = "图详"
    )
    private List<String> pictureContents;

    @FieldDoc(
            description = "视频"
    )
    private VideoVO videoVO;

    @FieldDoc(
            description = "是否标品 1-是、0-否"
    )
    @ApiModelProperty(value = "是否标品 1-是、0-否")
    private Integer standerType;

    @FieldDoc(
            description = "资质信息"
    )
    @ApiModelProperty(value = "资质信息")
    private MedicalDeviceQuaInfoVO medicalDeviceQuaInfo;

    @FieldDoc(
            description = "医疗器械资质信息填写要求，0-无需填写，1-非必填，2-必填",
            example = "1"
    )
    @ApiModelProperty(name = "医疗器械资质信息填写要求，0-无需填写，1-非必填，2-必填")
    private Integer medicalDeviceQuaRequirement;

    @FieldDoc(
            description = "美团类目，类目来源1-零售，2-医药",
            example = "1"
    )
    @ApiModelProperty(name = "美团类目，类目来源1-零售，2-医药")
    private Integer resourceType;

    public static StandardProductSearchVO convert(StandardProductSearchDTO dto) {
        StandardProductSearchVO vo = new StandardProductSearchVO();
        vo.setDatasourceType(dto.getDatasourceType());
        vo.setName(dto.getName());
        vo.setUpc(dto.getUpc());
        vo.setSpec(dto.getSpec());
        vo.setImageList(dto.getPicList());
        vo.setProducingPlace(dto.getProducingPlace());
        vo.setBrandName(dto.getBrandName());
        vo.setBrandId(dto.getBrandId());
        vo.setStorePrice(dto.getPrice());
        vo.setWeight(dto.getWeight());
        vo.setWeightForUnit(dto.getWeightForUnit());
        vo.setWeightUnit(dto.getWeightUnit());
        vo.setSaleUnit(dto.getUnit());
        dto.setProducingPlace(dto.getProducingPlace());
        vo.setChannelCategoryName(dto.getCategoryName());
        vo.setChannelCategoryCode(dto.getCategoryCode());
        vo.setChannelCategoryNamePath(dto.getCategoryNamePath());
        vo.setChannelCategoryCodePath(dto.getCategoryCodePath());
        vo.setDynamicInfoVOList(ChannelDynamicInfoVO.ofBizDTOList(dto.getDynamicInfoBizDTOList()));
        vo.setPictureContents(dto.getPictureContents());
        vo.setVideoVO(VideoVO.convert(dto.getVideo()));
        vo.setUpcRequired(dto.getUpcRequired());
        vo.setResourceType(dto.getResourceType());
        return vo;
    }

    public static StandardProductSearchVO convert(MedicalStandardProductSearchDTO dto) {
        StandardProductSearchVO standardProductSearchVO = new StandardProductSearchVO();
        standardProductSearchVO.setDatasourceType(dto.getDatasourceType());
        standardProductSearchVO.setUpc(dto.getUpc());
        standardProductSearchVO.setName(dto.getSpuName());
        standardProductSearchVO.setStanderType(dto.getStanderType());
        standardProductSearchVO.setSpec(dto.getSpec());
        standardProductSearchVO.setImageList(dto.getPictureList());
        standardProductSearchVO.setPictureContents(dto.getPictureContents());
        standardProductSearchVO.setMedicalDeviceQuaInfo(MedicalDeviceQuaInfoVO.fromMedicalDeviceQuaDTO(dto.getMedicalDeviceQuaInfo()));

        ChannelCategoryDTO mtCategoryDTO = ListUtils.emptyIfNull(dto.getChannelCategoryList())
                .stream()
                .filter(e -> Objects.equals(e.getChannelId(), EnhanceChannelType.MT.getChannelId()))
                .findFirst().orElse(null);
        standardProductSearchVO.setChannelCategoryCode(Optional.ofNullable(mtCategoryDTO).map(ChannelCategoryDTO::getChannelCategoryCode).orElse(null));
        standardProductSearchVO.setChannelCategoryName(Optional.ofNullable(mtCategoryDTO).map(ChannelCategoryDTO::getChannelCategoryName).orElse(null));
        standardProductSearchVO.setChannelCategoryCodePath(Optional.ofNullable(mtCategoryDTO).map(ChannelCategoryDTO::getChannelCategoryCodePath).orElse(null));
        standardProductSearchVO.setChannelCategoryNamePath(Optional.ofNullable(mtCategoryDTO).map(ChannelCategoryDTO::getChannelCategoryNamePath).orElse(null));
        standardProductSearchVO.setDynamicInfoVOList(ChannelDynamicInfoVO.ofBizDTOList(Optional.ofNullable(mtCategoryDTO)
                .map(ChannelCategoryDTO::getChannelDynamicInfoDTOList).orElse(Collections.emptyList())));
        standardProductSearchVO.setUpcRequired(Optional.ofNullable(mtCategoryDTO).map(ChannelCategoryDTO::getUpcRequired).orElse(null));
        standardProductSearchVO.setResourceType(Optional.ofNullable(mtCategoryDTO).map(ChannelCategoryDTO::getResourceType).orElse(null));
        standardProductSearchVO.setMedicalDeviceQuaRequirement(Optional.ofNullable(mtCategoryDTO).map(ChannelCategoryDTO::getMedicalDeviceQuaRequirement).orElse(null));

        return standardProductSearchVO;
    }

}
