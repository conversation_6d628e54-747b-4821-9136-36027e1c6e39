package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TypeDoc(
        description = "容器信息"
)
@Data
@ApiModel("容器信息")
public class WarehouseContainerBoxModuleVO {

    @FieldDoc(
            description = "容器Id", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "容器Id")
    private String containerBoxId;
}
