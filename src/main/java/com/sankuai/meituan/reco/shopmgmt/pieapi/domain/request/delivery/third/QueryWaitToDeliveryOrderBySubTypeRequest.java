package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery.third;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2023/8/31 17:30
 **/
@Data
public class QueryWaitToDeliveryOrderBySubTypeRequest extends PageQueryRequest {

    /**
     * @see com.sankuai.meituan.reco.shopmgmt.pieapi.enums.DrunkHorseWaitToDeliverySubTypeEnum
     */
    @FieldDoc(description = "子类型")
    @NotNull(message = "子类型不能为空")
    private Integer subType;
}
