package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.models.auth.In;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date: 2023/01/30
 */
@TypeDoc(
        description = "批次属性信息"
)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BatchInfoVO {

    @FieldDoc(
            description = "批次号"
    )
    private String batchNo;

    @FieldDoc(
            description = "生产时间"
    )
    private Long productTime;

    @FieldDoc(
            description = "批次拣货数量"
    )
    private Integer quantity;

    @FieldDoc(
            description = "是否批次"
    )
    private Integer enableBatch;

    @FieldDoc(
            description = "批次日期类型"
    )
    private Integer batchType;

    @FieldDoc(
            description = "保质期"
    )
    private Integer expirationDate;

    @FieldDoc(
            description = "效期状态，1:正常，2:预警，3:临期，4:超期"
    )
    private Integer periodStatus;





}
