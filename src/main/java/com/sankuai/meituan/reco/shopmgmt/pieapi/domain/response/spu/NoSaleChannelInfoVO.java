package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.NoSaleChannelInfoDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 *
 * @Author: luokai14
 * @Date: 2022/12/20 9:14 下午
 * @Mail: <EMAIL>
 */

@Slf4j
@TypeDoc(
        description = "不可售渠道信息",
        authors = "luokai14"
)
@Data
public class NoSaleChannelInfoVO {

    @FieldDoc(
            description = "渠道id"
    )
    private Integer channelId;

    @FieldDoc(
            description = "渠道名称"
    )
    private String channelName;

    @FieldDoc(
            description = "不可售异常信息列表"
    )
    private List<ProductAbnormalDetailVO> noSaleAbnormalInfoList;

    public static List<NoSaleChannelInfoVO> ofList(List<NoSaleChannelInfoDTO> noSaleChannelInfoDTOList){
        return Fun.map(noSaleChannelInfoDTOList, NoSaleChannelInfoVO::of);
    }

    public static NoSaleChannelInfoVO of(NoSaleChannelInfoDTO noSaleChannelInfoDTO){
        NoSaleChannelInfoVO noSaleChannelInfoVO = new NoSaleChannelInfoVO();
        noSaleChannelInfoVO.setChannelId(noSaleChannelInfoDTO.getChannelId());
        noSaleChannelInfoVO.setChannelName(noSaleChannelInfoDTO.getChannelName());
        noSaleChannelInfoVO.setNoSaleAbnormalInfoList(
                ProductAbnormalDetailVO.ofList(noSaleChannelInfoDTO.getNoSaleAbnormalInfoList(), null, null)
        );
        return noSaleChannelInfoVO;
    }

}
