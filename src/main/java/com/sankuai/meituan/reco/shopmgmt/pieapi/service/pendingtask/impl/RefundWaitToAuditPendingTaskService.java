/*
 * Copyright (c) 2019 Meituan.com. All Rights Reserved.
 */
package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.impl;

import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiEntityTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.RefundApplyListResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.AbstractSinglePendingTaskService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.OCMSOrderServiceWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class RefundWaitToAuditPendingTaskService extends AbstractSinglePendingTaskService {

    @Autowired
    private OCMSOrderServiceWrapper ocmsOrderServiceWrapper;

    @Override
    public PendingTaskResult getPendingTaskCount(PendingTaskParam param) throws Exception {
        int count=0;
        CommonResponse<RefundApplyListResponse> response = ocmsOrderServiceWrapper.queryWaitAuditRefund(param.getTenantId(), param.getStoreIds(), 1, 1, PoiEntityTypeEnum.STORE.code());
        if (ResultCode.SUCCESS.getCode()==response.getCode()){
            count=response.getData().getPageInfo().getTotalSize();
        }
        return PendingTaskResult.createNumberMarker(count);
    }

    @Override
    protected AuthCodeEnum module() {
        return AuthCodeEnum.ORDER_REFUND_AUDIT;
    }
}
