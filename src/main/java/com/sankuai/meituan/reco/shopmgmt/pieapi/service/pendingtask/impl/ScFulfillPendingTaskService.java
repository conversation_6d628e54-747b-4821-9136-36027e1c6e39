package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.impl;

import com.google.common.collect.Lists;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.AbstractSinglePendingTaskService;
import com.sankuai.shangou.commons.thrift.publisher.response.PaginationList;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.sc.fulfillment.client.asn.AsnScFulfillOrderQueryThriftService;
import com.sankuai.shangou.sc.fulfillment.client.asn.req.AsnScFulfillOrderQueryRequest;
import com.sankuai.shangou.sc.fulfillment.client.asn.vo.AsnScFulfillOrderVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * dateTime 2024/7/4 4:03 PM
 * description 到货预约待签到单据数量查询
 */
@Slf4j
@Service
public class ScFulfillPendingTaskService extends AbstractSinglePendingTaskService {

    @Resource
    private AsnScFulfillOrderQueryThriftService asnScFulfillOrderQueryThriftService;

    @Override
    protected PendingTaskResult getPendingTaskCount(PendingTaskParam param) throws Exception {
        AsnScFulfillOrderQueryRequest request = new AsnScFulfillOrderQueryRequest();
        request.setTenantId(param.getTenantId());
        request.setReceivingWarehouseIds(Lists.newArrayList(param.getEntityId()));
        request.setStatus(Lists.newArrayList(2)); // 已预约状态
        request.setPage(1);
        request.setPageSize(1);

        TResult<PaginationList<AsnScFulfillOrderVO>> resp = asnScFulfillOrderQueryThriftService.list(request);
        return PendingTaskResult.createNumberMarker(resp.getData().getTotal().intValue());
    }

    @Override
    protected AuthCodeEnum module() {
        return AuthCodeEnum.DH_DELIVERY_SIGN_IN;
    }
}