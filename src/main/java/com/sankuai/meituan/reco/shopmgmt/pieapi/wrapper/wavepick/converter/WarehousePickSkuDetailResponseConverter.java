package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.wavepick.converter;

import com.sankuai.meituan.reco.pickselect.thrift.wave.dto.WarehouseWavePickSkuDTO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.WarehousePickSkuDetailResponse;
import org.mapstruct.Mapper;

/**
 * @Description:
 * @Author: zhangjian155
 * @Date: 2022/8/25 15:55
 */
@Mapper(componentModel = "spring", uses = {WarehouseProgressConverter.class, WarehousePickItemSkuConverter.class})
public abstract class WarehousePickSkuDetailResponseConverter {
    public abstract WarehousePickSkuDetailResponse convert2Response(WarehouseWavePickSkuDTO warehouseWavePickSkuDTO);
}
