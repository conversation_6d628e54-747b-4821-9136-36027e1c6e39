package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2023/2/15 11:16
 * @Description:
 */
@TypeDoc(
        name = "查询推荐品牌结果对象",
        description = "查询推荐品牌结果对象"
)
@Data
public class RecommendBrandResponse {

    private List<BrandGroupByChannelVO> recommendBrandList;

}
