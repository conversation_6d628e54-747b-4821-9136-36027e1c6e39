package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery;

import javax.validation.constraints.NotNull;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TypeDoc(
		description = "配送管理平台信息"
)
@ApiModel("配送管理平台信息")
@Data
public class AggDeliveryPlatformInfoRequest {

	@FieldDoc(
			description = "平台code", requiredness = Requiredness.REQUIRED
	)
	@ApiModelProperty(value = "平台code", required = true)
	@NotNull(message = "平台code为空")
	private Integer platformCode;

	@FieldDoc(
			description = "是否开通、1开通、0关闭", requiredness = Requiredness.REQUIRED
	)
	@ApiModelProperty(value = "是否开通、1开通、0关闭", required = true)
	@NotNull(message = "状态信息为空")
	private Integer status;

	@FieldDoc(
			description = "渠道类型", requiredness = Requiredness.OPTIONAL
	)
	@ApiModelProperty(value = "渠道类型")
	private Integer channelType;

}
