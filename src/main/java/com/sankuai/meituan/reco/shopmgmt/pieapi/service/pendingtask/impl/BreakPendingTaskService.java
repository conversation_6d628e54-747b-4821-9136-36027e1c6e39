/*
 * Copyright (c) 2019 Meituan.com. All Rights Reserved.
 */
package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.impl;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.AbstractSinglePendingTaskService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.SkuSearchWrapper;
import com.sankuai.meituan.reco.store.management.enums.OrderTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 报损未完成量查询
 * <br><br>
 * Author: linjianyu <br>
 * Date: 2019-03-27 Time: 15:10
 * @since 2.1 权限迁移版本
 */
@Service
public class BreakPendingTaskService extends AbstractSinglePendingTaskService {

    @Autowired
    private SkuSearchWrapper skuSearchWrapper;

    @Override
    public PendingTaskResult getPendingTaskCount(PendingTaskParam param) throws Exception {
        int count = (int) skuSearchWrapper.queryRefundAndBreakOrderCount(
                param.getTenantId(), param.getEntityId(), param.getEntityType(), OrderTypeEnum.BREAK);
        return PendingTaskResult.createNumberMarker(count);
    }

    @Override
    protected AuthCodeEnum module() {
        return AuthCodeEnum.BREAKAGE_REPORT;
    }
}
