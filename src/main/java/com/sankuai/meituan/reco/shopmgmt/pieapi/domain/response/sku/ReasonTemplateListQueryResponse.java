package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.PageInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.QueryReasonTemplateListResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ReviewReasonTemplateDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 获取审核拒绝原因模板列表
 * @author: WangSukuan
 * @create: 2020-03-11
 **/
@TypeDoc(
        description = "获取审核拒绝原因模板列表响应"
)
@Data
@ApiModel("获取审核拒绝原因模板列表响应")
public class ReasonTemplateListQueryResponse {

    @FieldDoc(
            description = "审核拒绝原因模板(创建时间倒序)", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "审核拒绝原因模板(创建时间倒序)")
    private List<ReviewReasonTemplateVO> dataList = new ArrayList<>();

    @FieldDoc(
            description = "审核拒绝原因模板(创建时间倒序)", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "审核拒绝原因模板(创建时间倒序)")
    private PageInfoVO pageInfoVO;


   public  ReasonTemplateListQueryResponse buildReasonTemplateListQueryResponse(QueryReasonTemplateListResponse queryReasonTemplateListResponse){

       PageInfoDTO pageInfoDTO = queryReasonTemplateListResponse.getPageInfo();
       this.pageInfoVO = new PageInfoVO().buildPageInfoVO(pageInfoDTO);
       List<ReviewReasonTemplateDTO> reviewReasonTemplateDTOS = queryReasonTemplateListResponse.getDataList();
       if (CollectionUtils.isEmpty(reviewReasonTemplateDTOS)){
           return this;
       }
       for (ReviewReasonTemplateDTO reviewReasonTemplateDTO : reviewReasonTemplateDTOS){
           this.dataList.add(new ReviewReasonTemplateVO().buildReviewReasonTemplateVO(reviewReasonTemplateDTO));
       }
       return this;

   }




}
