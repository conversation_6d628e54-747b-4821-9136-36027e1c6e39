package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.wavepick.converter;

import com.sankuai.meituan.reco.pickselect.thrift.wave.dto.WarehouseWaveProgressDTO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.WarehouseWaveProgressVO;
import org.mapstruct.Mapper;

/**
 * @Description:
 * @Author: zhangjian155
 * @Date: 2022/8/25 18:03
 */
@Mapper(componentModel = "spring")
public abstract class WarehouseProgressConverter {
    public abstract WarehouseWaveProgressVO convert2Vo(WarehouseWaveProgressDTO warehouseWaveProgressDTO);
}
