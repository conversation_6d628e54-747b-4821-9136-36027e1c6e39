package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "渠道类目售后服务信息响应"
)
@Data
@ApiModel("渠道类目售后服务响应")
public class ChannelCategoryAfterSaleResponse {
    @FieldDoc(
            description = "默认选项ID"
    )
    private String defaultOption;

    @FieldDoc(
            description = "默认选项值"
    )
    private String defaultOptionName;

    @FieldDoc(
            description = "售后服务文案", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "售后服务文案")
    private List<ChannelCategoryAfterSaleVO> optionRules;
}
