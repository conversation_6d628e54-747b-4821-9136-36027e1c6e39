package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ERP渠道商品SKU信息
 *
 * <AUTHOR>
 * @since 2023/05/12
 */
@TypeDoc(
        description = "ERP渠道商品SKU信息"
)
@Data
@ApiModel("ERP渠道商品SKU信息")
public class ErpChannelSkuVO {

    @FieldDoc(description = "租户ID", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "租户ID")
    private Long tenantId;

    @FieldDoc(description = "门店ID", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "门店ID")
    public Long storeId;

    @FieldDoc(description = "spu编码", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "spu编码")
    public String spuId;

    @FieldDoc(description = "sku编码", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "sku编码")
    public String skuId;

    @FieldDoc(description = "渠道ID", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "渠道ID")
    public Integer channelId;
}
