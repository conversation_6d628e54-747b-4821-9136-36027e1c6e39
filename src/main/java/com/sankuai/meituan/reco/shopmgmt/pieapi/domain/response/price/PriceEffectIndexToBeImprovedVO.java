package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.List;

/**
 * @Author: wangyihao04
 * @Date: 2020-11-03 21:28
 * @Mail: <EMAIL>
 */
@TypeDoc(
        description = "待优化问题商品返回值"
)
@ApiModel("待优化问题商品返回值")
@Getter
@AllArgsConstructor
@ToString
public class PriceEffectIndexToBeImprovedVO {
    @FieldDoc(
            description = "门店聚合指标"
    )
    @ApiModelProperty("门店聚合指标")
    private PriceEffectIndexVO storeAggregation;
    @FieldDoc(
            description = "商品列表"
    )
    @ApiModelProperty("商品列表")
    private List<SkuWithPriceEffectIndexVO> skuList;
    @FieldDoc(
            description = "分页信息"
    )
    @ApiModelProperty("分页信息")
    private PageInfoVO pageInfoVO;
}
