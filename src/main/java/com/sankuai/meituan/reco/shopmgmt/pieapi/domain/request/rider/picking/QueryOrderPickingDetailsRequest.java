package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.picking;

import java.util.Objects;
import java.util.Optional;

import javax.swing.text.html.Option;
import javax.validation.constraints.NotNull;

import org.apache.commons.lang3.StringUtils;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 查询骑手已领取订单的待拣货内容的请求.
 *
 * <AUTHOR>
 * @since 2021/11/12 14:34
 */
@TypeDoc(
        description = "查询骑手已领取订单的待拣货内容的请求"
)
@ApiModel("查询骑手已领取订单的待拣货内容的请求")
@Data
public class QueryOrderPickingDetailsRequest {

    @FieldDoc(
            description = "订单渠道", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单渠道", required = true)
    private Integer channelId;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道订单号", required = true)
    @NotNull
    private String channelOrderId;

    @FieldDoc(
            description = "运单ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "运单ID", required = true)
    private Long deliveryOrderId;

    @FieldDoc(
            description = "是否是三方配送", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否是三方配送", required = false)
    private Boolean isThirdDelivery;

    public Optional<String> validate() {
        if (channelId == null || ChannelOrderConvertUtils.sourceMid2Biz(channelId) == null) {
            return Optional.of("订单渠道无效");
        }
        if (StringUtils.isBlank(channelOrderId)) {
            return Optional.of("渠道订单号无效");
        }

        //非三方配送时必传deliveryOrderId
        if (!Objects.equals(isThirdDelivery, true) && (deliveryOrderId == null || deliveryOrderId <= 0)) {
            return Optional.of("运单 ID 无效");
        }

        return Optional.empty();
    }
}
