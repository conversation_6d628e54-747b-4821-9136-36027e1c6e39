package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.eleccontract;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * @Author: <EMAIL>
 * @Date: 2020/3/16 18:48
 * @Description:
 */
@TypeDoc(description = "门店的电子协议签约人信息")
@Data
public class SignerInfoVO {

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店地址
     */
    private String address;

    /**
     * 法人姓名
     */
    private String legalPerson;

    /**
     * 签署人姓名
     */
    private String signerName;

    /**
     * 签署人电话
     */
    private String signerPhoneNum;

    /**
     * 资质主体外卖门店id
     */
    private Long subjectWmPoiId;

}
