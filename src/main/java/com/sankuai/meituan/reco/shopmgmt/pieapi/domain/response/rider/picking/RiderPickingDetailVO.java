package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.picking;

import java.util.List;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.dto.TConsumableMaterialInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 骑手的拣货任务详情.
 *
 * <AUTHOR>
 * @since 2021/11/15 16:31
 */
@TypeDoc(
        description = "骑手的拣货任务详情"
)
@ApiModel("骑手的拣货任务详情")
@Data
public class RiderPickingDetailVO {

    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "租户ID", required = true)
    private Long tenantId;

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道ID", required = true)
    private Integer channelId;

    @FieldDoc(
            description = "渠道名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道名称", required = true)
    private String channelName;

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店ID", required = true)
    private Long storeId;

    @FieldDoc(
            description = "门店名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店名称", required = true)
    private String storeName;

    @FieldDoc(
            description = "运单 ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "运单 ID", required = true)
    private Long deliveryOrderId;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道订单号", required = true)
    private String channelOrderId;

    @FieldDoc(
            description = "订单流水", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单流水", required = true)
    private Long serialNo;

    @FieldDoc(
            description = "配送订单类型  1-立即单 2-预约单", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "配送订单类型   1-立即单 2-预约单", required = true)
    private Integer deliveryOrderType;

    @FieldDoc(
            description = "配送订单类型名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "配送订单类型名称", required = true)
    private String deliveryOrderTypeName;

    @FieldDoc(
            description = "订单创建时间戳", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单创建时间戳", required = true)
    private Long createTime;

    @FieldDoc(
            description = "预计送达时间开始时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "预计送达时间开始时间", required = true)
    private Long estimateArriveTimeStart;

    @FieldDoc(
            description = "预计送达时间截止时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "预计送达时间截止时间", required = true)
    private Long estimateArriveTimeEnd;

    @FieldDoc(
            description = "配送方式,0-未知，1-配送到家，2-到店自提", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "配送方式,0-未知，1-配送到家，2-到店自提", required = true)
    private Integer deliveryMethod;

    @FieldDoc(
            description = "配送方式描述", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "配送方式描述", required = true)
    private String deliveryMethodDesc;

    // 收货人信息 start

    @FieldDoc(
            description = "收货人姓名", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "收货人姓名", required = true)
    private String receiverName;

    @FieldDoc(
            description = "收货人电话号码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "收货人电话号码", required = true)
    private String receiverPhone;

    @FieldDoc(
            description = "收货人地址", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "收货人地址", required = true)
    private String receiverAddress;

    // 收货人信息 end

    // 拣货条目信息 start

    @FieldDoc(
            description = "拣货工单 ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "拣货工单 ID", required = true)
    private Long pickWorkOrderId;

    @FieldDoc(
            description = "拣货工单状态", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "拣货工单状态", required = true)
    private Integer pickWorkOrderStatus;

    @FieldDoc(
            description = "拣货工单状态描述", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "拣货工单状态描述", required = true)
    private String pickWorkOrderStatusDesc;

    @FieldDoc(
            description = "拣货任务涉及的商品总数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "拣货任务涉及的商品总数量", required = true)
    private Integer itemCount;

    @FieldDoc(
            description = "备注", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "备注", required = false)
    private String comments;


    @FieldDoc(
            description = "是否弱校验sn", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否弱校验sn", required = false)
    private Boolean isWeakCheckSn;

    @FieldDoc(
            description = "待拣商品信息列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "待拣商品信息列表", required = true)
    private List<RiderPickTaskInfoVO> waitPickTasks;

    // 拣货条目信息 end
    @FieldDoc(
            description = "待拣商品信息列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "待拣商品信息列表", required = true)
    private List<TConsumableMaterialInfo> consumableMaterialInfoList;

    @FieldDoc(
            description = "异常单缺货项", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "异常单缺货项", required = true)
    private List<LackStockGoodsVO> lackStockGoodsList;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LackStockGoodsVO {
        @FieldDoc(description = "货品名称")
        private String goodsName;
        @FieldDoc(description = "货品SKU编号")
        private String skuId;
        @FieldDoc(description = "货品实拍图")
        private String picUrl;
        @FieldDoc(description = "货品规格")
        private String spec;
        @FieldDoc(description = "货品upc编号列表")
        private List<String> upcList;
        @FieldDoc(description = "要货量")
        private Integer needCount;
        @FieldDoc(description = "剩余可售库存")
        private Integer salableCount;
        @FieldDoc(description = "缺货量")
        private Integer lackCount;
    }



}
