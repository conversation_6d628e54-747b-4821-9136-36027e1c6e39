package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreCategoryBasicInfoDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.MerchantStoreCategorySortRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.MerchantStoreCategorySpuQueryRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.MerchantStoreCategorySpuSequenceUpdateRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.QueryPoiRelationStoreCategoryRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.QueryStoreCategoryByConditionRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.PoiStoreCategorySpuListResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.StoreCategoryBasicInfoResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.service.MerchantStoreCategoryBizThriftService;
import com.sankuai.meituan.shangou.empower.productbiz.client.service.MerchantStoreCategorySpuBizThriftService;
import com.sankuai.meituan.shangou.platform.empower.product.client.dto.MerchantStoreCategoryDTO;
import com.sankuai.meituan.shangou.platform.empower.product.client.request.merchant_store_category.CreateMerchantStoreCategoryRequest;
import com.sankuai.meituan.shangou.platform.empower.product.client.request.merchant_store_category.DeleteMerchantStoreCategoryRequest;
import com.sankuai.meituan.shangou.platform.empower.product.client.request.merchant_store_category.PoiStoreCategoryQueryRequest;
import com.sankuai.meituan.shangou.platform.empower.product.client.request.merchant_store_category.UpdateMerchantStoreCategoryRequest;
import com.sankuai.meituan.shangou.platform.empower.product.client.response.CommonResponse;
import com.sankuai.meituan.shangou.platform.empower.product.client.response.MerchantStoreCategoryListResponse;
import com.sankuai.meituan.shangou.platform.empower.product.client.service.MerchantStoreCategoryThriftService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/10/30
 */
@Component
@Slf4j
public class PoiStoreCategoryWrapper {
    @Resource
    private MerchantStoreCategoryBizThriftService merchantStoreCategoryBizThriftService;
    @Resource
    private MerchantStoreCategoryThriftService merchantStoreCategoryThriftService;
    @Resource
    private MerchantStoreCategorySpuBizThriftService merchantStoreCategorySpuBizThriftService;

    /**
     * 查询门店分类商品
     * @param request
     * @return
     */
    public PoiStoreCategorySpuListResponse pageQueryPoiCategorySpu(MerchantStoreCategorySpuQueryRequest request) {
        try {
            PoiStoreCategorySpuListResponse response = merchantStoreCategorySpuBizThriftService.pageQueryPoiCategorySpu(request);
            if (response !=null && response.getStatus() != null
                    && response.getStatus().getCode() == ResultCode.SUCCESS.getCode()) {
                return response;
            }
        } catch (Exception e) {
            log.error("pageQueryPoiCategorySpu error, request:{}.", request, e);
        }
        throw new CommonRuntimeException("查询分类下商品异常");
    }

    /**
     * 更新门店分类商品排序
     * @param request
     * @return
     */
    public com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse updateSpuSequence(MerchantStoreCategorySpuSequenceUpdateRequest request) {
        try {
            com.sankuai.meituan.shangou.empower.productbiz.client.response.CommonResponse response = merchantStoreCategorySpuBizThriftService.updateSequence(request);
            if (response == null || response.getStatus() == null
                    || response.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
                log.error("更新分类商品排序异常， request [{}], response [{}].", request, response);
                Integer code = (response == null || response.getStatus() == null) ? ResultCode.FAIL.getCode() : response.getStatus().getCode();
                String msg = (response == null || response.getStatus() == null) ? "更新分类商品排序异常" : response.getStatus().getMsg();
                return com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse.fail(code, msg);
            }
            return com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse.success(ResultCode.SUCCESS.getCode());
        } catch (Exception e) {
            log.error("updateSpuSequence error, request:{}.", request, e);
            return com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse.fail(ResultCode.FAIL.getCode(), "更新分类商品排序异常");
        }
    }

//    /**
//     * 查询店内分类（包含关联商品数量）
//     * @param request
//     * @return
//     */
//    public List<StoreCategoryBasicInfoDTO> queryStoreCategoryByCondition(QueryStoreCategoryByConditionRequest request) {
//        try {
//            StoreCategoryBasicInfoResponse response = merchantStoreCategoryBizThriftService.queryPoiStoreCategory(request);
//            if (response != null && response.getStatus() != null
//                    && response.getStatus().getCode() == ResultCode.SUCCESS.getCode()) {
//                return response.getStoreCategoryDTOList();
//            }
//        } catch (Exception e) {
//            log.error("queryStoreCategoryByCondition error, request:{}.", request, e);
//        }
//        throw new CommonRuntimeException("获取门店店内分类异常");
//    }

    public List<StoreCategoryBasicInfoDTO> queryPoiRelationStoreCategory(QueryPoiRelationStoreCategoryRequest request) {
        try {
            StoreCategoryBasicInfoResponse response = merchantStoreCategoryBizThriftService.queryPoiRelationStoreCategory(request);
            if (response != null && response.getStatus() != null
                    && response.getStatus().getCode() == ResultCode.SUCCESS.getCode()) {
                return response.getStoreCategoryDTOList();
            }
        } catch (Exception e) {
            log.error("queryPoiRelationStoreCategory error, request:{}.", request, e);
        }
        throw new CommonRuntimeException("获取门店店内分类异常");
    }

    /**
     * 查询店内分类简略信息
     * @param request
     * @return
     */
    public List<MerchantStoreCategoryDTO> querySimpleInfoByCategoryId(PoiStoreCategoryQueryRequest request) {
        try {
            MerchantStoreCategoryListResponse response = merchantStoreCategoryThriftService.queryPoiCategoryByCategoryIds(request);
            if (response != null && response.getCode() == ResultCode.SUCCESS.getCode()) {
                return response.getMerchantStoreCategoryList();
            }
        } catch (Exception e) {
            log.error("queryBasicByCategoryId error, request:{}.", request, e);
        }
        throw new CommonRuntimeException("获取门店店内分类异常");
    }

    /**
     * 更新门店店内分类排序
     * @param request
     * @return
     */
    public com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse sort(MerchantStoreCategorySortRequest request) {
        try {
            com.sankuai.meituan.shangou.empower.productbiz.client.response.CommonResponse response = merchantStoreCategoryBizThriftService.sort(request);
            if (response == null || response.getStatus() == null
                    || response.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
                log.error("更新店内分类排序异常， request [{}], response [{}].", request, response);
                Integer code = (response == null || response.getStatus() == null) ? ResultCode.FAIL.getCode() : response.getStatus().getCode();
                String msg = (response == null || response.getStatus() == null) ? "更新店内分类排序异常" : response.getStatus().getMsg();
                return com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse.fail(code, msg);
            }
            return com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse.success(ResultCode.SUCCESS.getCode());
        } catch (Exception e) {
            log.error("sort error, request:{}.", request, e);
            return com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse.fail(ResultCode.FAIL.getCode(), "更新店内分类排序异常");
        }
    }

    /**
     * 创建门店店内分类
     * @param request
     * @return
     */
    public com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse createStoreCategory(CreateMerchantStoreCategoryRequest request) {
        try {
            CommonResponse response = merchantStoreCategoryThriftService.createMerchantStoreCategory(request);
            if (response == null || response.getCode() != ResultCode.SUCCESS.getCode()) {
                log.error("创建店内分类异常， request [{}], response [{}].", request, response);
                Integer code = response == null ? ResultCode.FAIL.getCode() : response.getCode();
                String msg = response == null ? "创建店内分类异常" : response.getMessage();
                return com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse.fail(code, msg);
            }
            return com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse.success(ResultCode.SUCCESS.getCode());
        } catch (Exception e) {
            log.error("创建店内分类异常, request:{}", request, e);
            return com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse.fail(ResultCode.FAIL.getCode(), "创建店内分类异常");
        }
    }

    /**
     * 更新门店店内分类
     * @param request
     * @return
     */
    public com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse updateStoreCategory(UpdateMerchantStoreCategoryRequest request) {
        try {
            CommonResponse response = merchantStoreCategoryThriftService.updateMerchantStoreCategory(request);
            if (response == null || response.getCode() != ResultCode.SUCCESS.getCode()) {
                log.error("更新店内分类异常， request [{}], response [{}].", request, response);
                Integer code = response == null ? ResultCode.FAIL.getCode() : response.getCode();
                String msg = response == null ? "更新店内分类异常" : response.getMessage();
                return com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse.fail(code, msg);
            }
            return com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse.success(ResultCode.SUCCESS.getCode());
        } catch (Exception e) {
            log.error("更新店内分类异常, request:{}", request, e);
            return com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse.fail(ResultCode.FAIL.getCode(), "更新店内分类异常");
        }
    }

    /**
     * 删除门店店内分类
     * @param request
     * @return
     */
    public com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse deleteStoreCategory(DeleteMerchantStoreCategoryRequest request) {
        try {
            CommonResponse response = merchantStoreCategoryThriftService.deleteMerchantStoreCategory(request);
            if (response == null || response.getCode() != ResultCode.SUCCESS.getCode()) {
                log.error("删除店内分类异常， request [{}], response [{}].", request, response);
                Integer code = response == null ? ResultCode.FAIL.getCode() : response.getCode();
                String msg = response == null ? "更新店内分类异常" : response.getMessage();
                return com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse.fail(code, msg);
            }
            return com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse.success(ResultCode.SUCCESS.getCode());
        } catch (Exception e) {
            log.error("删除店内分类异常, request:{}", request, e);
            return com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse.fail(ResultCode.FAIL.getCode(), "删除店内分类异常");
        }
    }

}
