package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.messagetask;
// Copyright (C) 2020 Meituan
// All rights reserved

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * @author: <EMAIL>
 * @class: MessageTaskQueryRequest
 * @date: 2020-03-13 18:08:46
 * @desc:
 */
@ApiModel(
        "消息任务模块查询任务请求"
)
@TypeDoc(
        description = "消息任务模块查询任务请求"
)
public class MessageTaskQueryRequest {

    @FieldDoc(
            description = "门店id"
    )
    @ApiModelProperty("门店Id")
    private String storeId;

    public MessageTaskQueryRequest setStoreId(String storeId) {
        this.storeId = storeId;
        return this;
    }

    public Long getStoreId() {
        try {
            if (StringUtils.isNotEmpty(storeId)) {
                return Long.valueOf(storeId);
            }
        } catch (Exception e) {
            //类型转换错误, 全门店的时传递的storeId是一个逗号分隔的多个门店.
        }
        return null;

    }
}
