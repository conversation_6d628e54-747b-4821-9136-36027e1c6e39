package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.SecurityDepositConstants;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.tenant.TenantBillServicePayRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.securitydeposit.PayResultVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.tenant.TenantBillPrePayInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ResponseHandler;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.AccountInfoVo;
import com.sankuai.meituan.shangou.empower.pay.constant.GatheringPayStatus;
import com.sankuai.meituan.shangou.empower.pay.dto.model.GatheringTradeOrder;
import com.sankuai.meituan.shangou.empower.pay.dto.request.gathering.GatheringTradeQueryRequest;
import com.sankuai.meituan.shangou.empower.pay.dto.response.gathering.GatheringTradeOrderInfoResponse;
import com.sankuai.meituan.shangou.empower.pay.services.gathering.GatheringThriftService;
import com.sankuai.meituan.shangou.empower.settlement.dto.response.ResponseStatus;
import com.sankuai.meituan.shangou.empower.tenantbill.dto.model.ServiceRenewalDto;
import com.sankuai.meituan.shangou.empower.tenantbill.dto.model.TenantBillServiceBillDto;
import com.sankuai.meituan.shangou.empower.tenantbill.dto.request.*;
import com.sankuai.meituan.shangou.empower.tenantbill.dto.response.PageServicePurchaseDetailResponse;
import com.sankuai.meituan.shangou.empower.tenantbill.dto.response.QueryServiceBillDetailResponse;
import com.sankuai.meituan.shangou.empower.tenantbill.dto.response.TenantBillPageResponse;
import com.sankuai.meituan.shangou.empower.tenantbill.dto.response.TenantBillPrePayResponse;
import com.sankuai.meituan.shangou.empower.tenantbill.services.TenantBillManageThriftService;
import com.sankuai.meituan.shangou.empower.tenantbill.services.TenantBillQueryThriftService;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Slf4j
@Component
public class TenantBillWrapper {

    @Autowired
    private TenantBillManageThriftService tenantBillManageThriftService;

    @Autowired
    private AuthThriftWrapper authThriftWrapper;

    @Resource
    private TenantBillQueryThriftService tenantBillQueryThriftService;

    @Autowired
    private GatheringThriftService gatheringThriftService;

    public PayResultVO queryPayResult(Long tenantId, String tradeNo) {
        GatheringTradeQueryRequest request = new GatheringTradeQueryRequest();

        try {
            request.setTenantId(tenantId);
            request.setTradeNo(tradeNo);
            GatheringTradeOrderInfoResponse response = gatheringThriftService.queryGatheringTradeOrder(request);
            ResponseHandler.checkResponseAndStatus(response, resp -> resp.getResponseStatus().getCode(), resp -> resp.getResponseStatus().getMessage());

            GatheringTradeOrder tradeOrder = response.getTradeOrder();
            if (tradeOrder == null) {
                throw new BizException("查询不存在");
            }

            if (GatheringPayStatus.PAY_SUCCESS.getType() == tradeOrder.getPayStatus()) {
                return new PayResultVO(SecurityDepositConstants.PAY_SUCCESS_RET, null);
            } else if (GatheringPayStatus.PAYING.getType() == tradeOrder.getPayStatus()) {
                return new PayResultVO(SecurityDepositConstants.PAYING, null);
            } else {
                return new PayResultVO(SecurityDepositConstants.PAY_FAIL_RET, tradeOrder.getPayStatusComment());
            }

        } catch (Exception e) {
            log.warn("支付保证金异常: request [{}].", request, e);
            throw new CommonRuntimeException("查询支付状态异常.", ResultCode.SECURITY_DEPOSIT_ERROR);
        }
    }


    public TenantBillPageResponse queryTenantDailyBillPage(TenantBillPageRequest request) {
        TenantBillPageResponse response = tenantBillQueryThriftService.queryTenantBillPageV2(request);
        ResponseHandler.checkResponseAndStatus(response, resp -> resp.getStatus().getCode(), resp -> resp.getStatus().getMessage());
        return response;
    }

    public void closeServiceBillPay(CloseServiceBillPayRequest request) {
        ResponseStatus responseStatus = tenantBillManageThriftService.closeServiceBillPay(request);
        if (responseStatus.getCode() != 0) {
            throw new BizException(responseStatus.getCode(), responseStatus.getMessage());
        }
    }

    public TenantBillServiceBillDto queryServiceBillDetail(QueryServiceBillDetailRequest request) {
        QueryServiceBillDetailResponse response = tenantBillQueryThriftService.queryServiceBillDetail(request);
        ResponseHandler.checkResponseAndStatus(response, resp -> resp.getStatus().getCode(), resp -> resp.getStatus().getMessage());
        return response.getBillServiceBillDto();
    }

    public PageServicePurchaseDetailResponse pageServicePurchaseDetail(PageServicePurchaseDetailRequest request) {
        PageServicePurchaseDetailResponse response = tenantBillQueryThriftService.pageServicePurchaseDetail(request);
        ResponseHandler.checkResponseAndStatus(response, resp -> resp.getStatus().getCode(), resp -> resp.getStatus().getMessage());
        return response;
    }

    // 创建预支付单
    public TenantBillPrePayInfo createPrePayForService(Long billId, Long calcAmount, List<Long> serviceIdList, List<ServiceRenewalDto> orderKingRenewalList, Integer billSource, String tradeNo) {
        TenantBillCreatePrePayRequest prePayReq = new TenantBillCreatePrePayRequest();
        prePayReq.setBillId(billId);

        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        prePayReq.setTenantId(user.getTenantId());
        prePayReq.setOperatorId(user.getAccountId());

        AccountInfoVo accountInfoVo = getCurAccountInfoVo();
        prePayReq.setEpAccountId(accountInfoVo.getEpAccountId());
        prePayReq.setOperatorName(accountInfoVo.getAccountName());
        prePayReq.setOperatorUserName(user.getOperatorName());
        prePayReq.setOperatorType(accountInfoVo.getAccountType());

        prePayReq.setPayAmount(calcAmount);
        prePayReq.setServiceIdList(serviceIdList);
        prePayReq.setUserIp(getRemoteIp());
        prePayReq.setBillSource(billSource);

        prePayReq.setTradeNo(tradeNo);
        prePayReq.setServiceRenewalList(orderKingRenewalList);

        TenantBillPrePayResponse response = tenantBillManageThriftService.createPrePayForService(prePayReq);
        ResponseStatus status = response.getStatus();
        if (status.getCode() != ResultCode.SUCCESS.getCode()) {
            throw new CommonRuntimeException(status.getMessage(), ResultCode.FAIL);
        }

        TenantBillPrePayInfo tenantBillPrePayInfo = new TenantBillPrePayInfo();
        tenantBillPrePayInfo.setBillId(response.getBillId());
        tenantBillPrePayInfo.setPayToken(response.getPayToken());
        tenantBillPrePayInfo.setTradeNo(response.getTradeNo());
        return tenantBillPrePayInfo;
    }

    public TenantBillPrePayInfo createNewSignPrePay(TenantBillServicePayRequest request) {
        TenantBillCreatePrePayRequest prePayReq = buildPrePayReq(request);
        prePayReq.setNewSignPayInfoDto(request.buildNewSignPayInfoDto());
        return doCreatePrePay(prePayReq);
    }

    private TenantBillCreatePrePayRequest buildPrePayReq(TenantBillServicePayRequest request) {
        TenantBillCreatePrePayRequest prePayReq = new TenantBillCreatePrePayRequest();
        prePayReq.setBillId(request.getBillId());

        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        prePayReq.setTenantId(user.getTenantId());
        prePayReq.setOperatorId(user.getAccountId());

        AccountInfoVo accountInfoVo = getCurAccountInfoVo();
        prePayReq.setEpAccountId(accountInfoVo.getEpAccountId());
        prePayReq.setOperatorName(accountInfoVo.getAccountName());

        prePayReq.setPayAmount(request.getCalcAmount());

        prePayReq.setUserIp(getRemoteIp());

        prePayReq.setTradeNo(request.getTradeNo());
        return prePayReq;
    }

    private TenantBillPrePayInfo doCreatePrePay(TenantBillCreatePrePayRequest prePayReq) {
        TenantBillPrePayResponse response = tenantBillManageThriftService.createPrePayForService(prePayReq);
        ResponseHandler.checkResponseAndStatus(response, resp -> resp.getStatus().getCode(), resp -> resp.getStatus().getMessage());

        TenantBillPrePayInfo tenantBillPrePayInfo = new TenantBillPrePayInfo();
        tenantBillPrePayInfo.setBillId(response.getBillId());
        tenantBillPrePayInfo.setPayToken(response.getPayToken());
        tenantBillPrePayInfo.setTradeNo(response.getTradeNo());
        return tenantBillPrePayInfo;
    }

    public AccountInfoVo getCurAccountInfoVo() {
        AccountInfoVo accountVo;
        try {
            accountVo = authThriftWrapper.getCurrentAccountWithoutPermission();
        } catch (Exception exception) {
            throw new BizException("查询账户异常");
        }

        if (accountVo.getEpAccountId() == 0L) {
            throw new BizException(9999, "登录账户类型不支持支付");
        }

        return accountVo;
    }

    private String getRemoteIp() {
        HttpServletRequest request =
                ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        if (request == null) {
            return "";
        }

        String forwardedFor = request.getHeader("x-forwarded-for");
        if (request.getHeader("x-forwarded-for") == null) {
            return request.getRemoteAddr();
        }

        if (StringUtils.contains(forwardedFor, ",")) {
            return StringUtils.split(forwardedFor, ",")[0];
        }

        return forwardedFor;

    }


}
