package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: wanghongzhen
 * @date: 2020-05-11 17:15
 */
@TypeDoc(
        description = "错误信息"
)
@Data
@ApiModel("错误信息")
public class ChannelSpuFailRecordVO {

    @FieldDoc(
            description = "spu编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "spu编码", required = true)
    private String spuId;

    @FieldDoc(
            description = "门店编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店编码", required = true)
    private Long storeId;

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID", required = true)
    private Integer channelId;

    @FieldDoc(
            description = "存在零售价待审核", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "存在零售价待审核", required = true)
    private Boolean hasRetailPriceToReview;

    @FieldDoc(
            description = "存在进货价待审核", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "存在进货价待审核", required = true)
    private Boolean hasOfflinePriceToReview;

    @FieldDoc(
            description = "存在零售价为0", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "存在零售价为0", required = true)
    private Boolean hasZeroRetailPrice;

    @FieldDoc(
            description = "存在进货价为0", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "存在进货价为0", required = true)
    private Boolean hasZeroOfflinePrice;

    @FieldDoc(
            description = "错误类型", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "错误类型", required = true)
    private Integer errorCode;

    @FieldDoc(
            description = "错误信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "错误信息", required = true)
    private String errorMsg;
}
