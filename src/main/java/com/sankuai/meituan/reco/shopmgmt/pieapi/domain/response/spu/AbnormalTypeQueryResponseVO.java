package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/23 19:24
 */
@TypeDoc(
        description = "所有异常类型树信息"
)
@Setter
@Getter
@ToString
public class AbnormalTypeQueryResponseVO {

    @FieldDoc(
            description = "商品异常类型列表"
    )
    private List<AbnormalRuleNodeVO> abnormalRuleNodes;
}
