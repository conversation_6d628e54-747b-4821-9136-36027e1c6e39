package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery;

import javax.validation.constraints.NotNull;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TypeDoc(
		description = "配送管理配置查询"
)
@ApiModel("配送管理配置查询")
@Data
public class DeliveryManagementQueryRequest {

	@FieldDoc(
			description = "门店id", requiredness = Requiredness.REQUIRED
	)
	@ApiModelProperty(value = "门店id", required = true)
	@NotNull(message = "门店id不能为空")
	private Long storeId;
}
