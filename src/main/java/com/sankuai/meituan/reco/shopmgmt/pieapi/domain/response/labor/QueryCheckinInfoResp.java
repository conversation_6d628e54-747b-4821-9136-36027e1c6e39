package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor;

import com.facebook.swift.codec.ThriftField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.LocationStoreVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/10/19 15:00
 **/
@ApiModel("查询打卡信息响应")
@TypeDoc(description = "查询打卡信息响应")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryCheckinInfoResp {
    @FieldDoc(description = "员工姓名")
    @ApiModelProperty("员工姓名")
    private String employeeName;

    @FieldDoc(description = "打卡类型 0-当天未排班 1-不在打卡时间范围内 2-上班打卡 3-下班打卡")
    @ApiModelProperty("打卡类型 0-当天未排班 1-不在打卡时间范围内 2-上班打卡 3-下班打卡")
    private int checkinType;
    
    @FieldDoc(description = "打卡位置信息")
    @ApiModelProperty("打卡位置信息")
    private List<LocationStoreVO> locationInfos;

    @FieldDoc(description = "迟到时间点（上班打卡时有值）")
    @ApiModelProperty("迟到时间点（上班打卡时有值）")
    public Long beLateTimePoint;

    @FieldDoc(description = "早退时间点（下班打卡时有值）")
    @ApiModelProperty("早退时间点（下班打卡时有值）")
    public Long leaveEarlyTimePoint;

    @FieldDoc(description = "加班申请id，不为null表示申请了加班")
    @ApiModelProperty("加班申请id，不为null表示申请了加班")
    public Long extraWorkAttendanceApplyId;

    @FieldDoc(description = "加班申请状态：2-审批通过；4-审批驳回")
    @ApiModelProperty("加班申请状态：2-审批通过；4-审批驳回")
    public Integer extraWorkAttendanceApplyStatus;
}
