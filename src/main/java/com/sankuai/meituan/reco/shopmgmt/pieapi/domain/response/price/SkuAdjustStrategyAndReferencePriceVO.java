package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @Author: wangyihao04
 * @Date: 2020-12-30 19:32
 * @Mail: <EMAIL>
 */
@TypeDoc(
        description = "SKU改价明细和参考价"
)
@ApiModel("SKU改价明细和参考价")
@Data
public class SkuAdjustStrategyAndReferencePriceVO {
    @FieldDoc(
            description = "SKU改价明细"
    )
    @ApiModelProperty(name = "SKU改价明细")
    private StoreSkuAdjustPriceDetailVO storeSkuAdjustPriceDetail;
    @FieldDoc(
            description = "参考价"
    )
    @ApiModelProperty(name = "参考价")
    private Map<String, List<ReferencePriceDataVO>> referencePriceIndexMap;

}

