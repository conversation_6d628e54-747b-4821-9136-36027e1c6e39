package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.StoreSkuPropertyVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.TimeSlotVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.ChannelBrandRelationVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.SaleAttrVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.SupplyRelationAndPurchaseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.product.container.SpuContainer;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.WeightTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import org.apache.commons.collections.CollectionUtils;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: <EMAIL>
 * @Date: 2021-03-01 16:01
 * @Description:
 */
@EqualsAndHashCode(callSuper = true)
@TypeDoc(
        description = "保存门店商品SPU信息，并创建多个层级的商品"
)
@Data
@ApiModel("保存门店商品SPU信息，新")
public class StoreSpuSimpleCreateVO extends SpuContainer {

    @FieldDoc(
            description = "SPU编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "SPU编码", required = true)
    @NotNull
    private String spuId;

    @FieldDoc(
            description = "SPU名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "SPU名称", required = true)
    @NotNull
    @Length(max = 45)
    private String spuName;

    @FieldDoc(
            description = "可售时间，如果为无限，此字段为空。\n" +
                    "\n" +
                    "key:工作日 见@Enum WeekDayEnum\n" +
                    "* value:时间段 时间段不允许有交集，个数不超过5个\n" +
                    "* {\"09:00-09:30\"},{\"13:30-15:00\"},{\"20:00-21:00\"}", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "可售时间")
    private Map<Integer, List<TimeSlotVO>> availableTimes;
    @FieldDoc(
            description = "是否为“力荐”商品，字段取值范围：0-否， 1-是", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否为“力荐”商品")
    private Integer specialty;
    @FieldDoc(
            description = "商品描述 200字以内", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品描述")
    @Length(max = 200)
    private String description;
    @FieldDoc(
            description = "商品属性 不超过十个属性", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品属性")
    private List<StoreSkuPropertyVO> properties;

    @FieldDoc(
            description = "商品卖点 10字以内", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品卖点")
    @Length(max = 10)
    private String sellPoint;

    @FieldDoc(
            description = "摊位编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "摊位编码")
    private Long boothId;

    @FieldDoc(
            description = "门店商品SKU信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店商品SKU信息")
    private List<StoreSkuCreateVO> storeSkuList;

    @FieldDoc(
            description = "渠道商品SPU信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道商品SPU信息")
    private List<ChannelSpuCreateVO> channelSpuList;

    @FieldDoc(
            description = "自定义名称，新增编辑接口入参使用", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "自定义名称")
    private String customizedName;

    @FieldDoc(
            description = "图片链接列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "图片链接列表")
    @Size(max = 5)
    private List<String> imagesUrls;

    @FieldDoc(
            description = "图片链接列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "图片链接列表")
    private String producingArea;

    @FieldDoc(
            description = "品牌", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "[品牌")
    private String brandCode;

    @FieldDoc(
            description = "后台类目编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "后台类目编码")
    private String categoryCode;

    @FieldDoc(
            description = "商品视频", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品视频")
    private VideoInfoVO video;

    @FieldDoc(
            description = "商品图详", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品图详")
    private List<String> pictureContents;

    @FieldDoc(
            description = "饿了么渠道类目编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "饿了么渠道类目编码")
    private String elemChannelCategoryCode;

    @FieldDoc(
            description = "饿了么渠道类目属性", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "饿了么渠道类目属性")
    private List<ChannelDynamicInfoVO> elemChannelDynamicInfoVOList;

    @FieldDoc(
            description = "商品规格类型 1单规格 2多规格", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品规格类型 1单规格 2多规格")
    private Integer specType;

    @FieldDoc(
            description = "绑定采购供应商"
    )
    @ApiModelProperty(name = "绑定采购供应商")
    private List<SupplyRelationAndPurchaseVO> purchaseInfo;


    @FieldDoc(
            description = "京东末级类目", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "京东末级类目")
    private String jdCategoryId;

    @FieldDoc(
            description = "京东到家渠道类目属性列表，开通京东渠道需要传入", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "京东到家渠道类目属性列表，开通京东渠道需要传入")
    private List<SaleAttrVo> jdSaleAttrList;

    @FieldDoc(
            description = "店内分类分组列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "店内分类分组列表")
    private List<StoreGroupCategoryCodeVO> storeGroupCategoryCodes;

    @FieldDoc(
            description = "渠道品牌列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道品牌列表")
    @Valid
    private List<ChannelBrandRelationVO> channelBrand;

    @FieldDoc(
            description = "门店店内分类，非总部管品业态使用，单渠道不用", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店店内分类，非总部管品业态使用，单渠道不用")
    private List<Long> storeCategoryCodes;

    @FieldDoc(
            description = "总部店内分类", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "总部店内分类")
    private List<Long> merchantStoreCategoryCodes;

    @FieldDoc(
            description = "门店自定义店内分类", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店自定义店内分类")
    private List<Long> poiStoreCategoryCodes;

    @FieldDoc(
            description = "抖音渠道类目", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "抖音渠道类目")
    private String douyinCategoryId;

    @FieldDoc(
            description = "抖音渠道动态信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "抖音渠道动态信息")
    private List<ChannelDynamicInfoVO> douyinChannelDynamicInfoVOList;

    @FieldDoc(
            description = "抖音渠道售后服务类型", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "抖音渠道售后服务类型")
    private String douyinAfterSaleServiceType;

    @FieldDoc(
            description = "医药器械资质图信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "医药器械资质图信息")
    private MedicalDeviceQuaInfoVO medicalDeviceQuaInfo;

    @FieldDoc(
            description = "美团售后服务类型", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "美团售后服务类型")
    private String mtAfterSaleServiceType;

    @FieldDoc(
            description = "同步渠道分组", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "同步渠道分组")
    private List<Integer> poiGroupIdList;

    @FieldDoc(
            description = "特殊管控商品资质"
    )
    @ApiModelProperty("特殊管控商品资质")
    private List<String> controlQuaPicUrl;

    @FieldDoc(
            description = "AI推荐信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "AI推荐信息")
    private AiRecommendVO aiRecommendVO;

    @FieldDoc(
            description = "标品类型 1-非标品 3-标品", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "标品类型 1-非标品 3-标品")
    private Integer weightType;

    @FieldDoc(
            description = "商品名称补充语信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "商品名称补充语信息")
    private NameSupplementInfoVO nameSupplementInfo;

    @FieldDoc(description = "特殊图片")
    @ApiModelProperty(value = "特殊图片")
    private List<SpecialPictureVO> mtSpecialPictureList;

    /**
     * 根据规格UPC内容判断是否标品<br>
     * 只有规格中有UPC都认为是标品
     *
     * @return
     */
    public Integer getWeightType() {
        if(null != weightType){
            return weightType;
        }
        //如果入参为空，走下面的历史逻辑：只有规格中有UPC都认为是标品
        if (CollectionUtils.isEmpty(storeSkuList)) {
            return WeightTypeEnum.WEIGHT.getCode();
        }

        boolean upcExistent = storeSkuList.stream()
                .map(StoreSkuCreateVO::getUpcList)
                .anyMatch(CollectionUtils::isNotEmpty);

        if (upcExistent) {
            return WeightTypeEnum.NONE.getCode();
        } else {
            return WeightTypeEnum.WEIGHT.getCode();
        }
    }

}
