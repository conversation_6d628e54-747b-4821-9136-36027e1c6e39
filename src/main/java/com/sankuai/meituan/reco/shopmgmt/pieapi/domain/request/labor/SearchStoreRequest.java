package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022-08-01
 * @email <EMAIL>
 */
@TypeDoc(
        description = "提交员工信息请求"
)
@Data
@ApiModel("提交员工信息请求")
@AllArgsConstructor
@NoArgsConstructor
public class SearchStoreRequest {

    @FieldDoc(
            description = "租户id"
    )
    private Long cityId;

    @FieldDoc(
            description = "门店名关键字"
    )
    private String storeNameKeyword;
}
