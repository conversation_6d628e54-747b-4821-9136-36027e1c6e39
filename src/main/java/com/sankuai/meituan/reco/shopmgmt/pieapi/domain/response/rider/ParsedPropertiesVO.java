package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023-12-24
 * @email <EMAIL>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ParsedPropertiesVO {

    @FieldDoc(
            description = "属性文案", requiredness = Requiredness.OPTIONAL
    )
    private String text;

    @FieldDoc(
            description = "属性颜色", requiredness = Requiredness.OPTIONAL
    )
    private String color;
}
