package com.sankuai.meituan.reco.shopmgmt.pieapi.service.poi.bo;

import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiOperationModeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.sankuai.meituan.reco.shopmgmt.pieapi.controller.poi.response.PoiInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.area.bo.AreaBO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import lombok.*;

import java.util.Date;

/**
 * <AUTHOR>
 * Email <EMAIL>
 * Date 2022/9/7 8:14 下午
 * Description
 */

@Setter
@Getter
@NoArgsConstructor
@ToString
@EqualsAndHashCode(of = {"poiId"})
public class PoiInfoBO {

    private Long poiId;

    private String poiName;

    private Long tenantId;

    private Long wmPoiId;

    private String outPoiId;

    private String payMerchantId;

    private String poiAddress;

    private Integer poiStatus;

    private AreaBO areaInfo;

    private Long departmentId;

    private Date exchangeTime;

    private Long commercialAgentId;

    private Integer entityType;

    private String mobile;

    private Integer shippingMode;

    private Double longitude;

    private Double latitude;

    private Long shareableWarehouseId;

    private String shareableWarehouseName;

    // 运营模式（如加盟/自营） 1：自营 2：加盟
    public Integer operationMode;


    public PoiInfoBO(PoiInfoDto poiInfoDto) {
        poiId             = poiInfoDto.getPoiId();
        poiName           = poiInfoDto.getPoiName();
        tenantId          = poiInfoDto.getTenantId();
        wmPoiId           = poiInfoDto.getWmPoiId();
        outPoiId          = poiInfoDto.getOutPoiId();
        payMerchantId     = poiInfoDto.getPayMerchantId();
        poiAddress        = poiInfoDto.getPoiAddress();
        poiStatus         = poiInfoDto.getPoiStatus();
        areaInfo          = ConverterUtils.nonNullConvert(poiInfoDto.getDistrict(), AreaBO::new);
        departmentId      = poiInfoDto.getDepartmentId();
        commercialAgentId = poiInfoDto.getBindingCommercialAgentId();
        entityType = poiInfoDto.getEntityType();
        mobile = poiInfoDto.getMobile();
        shippingMode = poiInfoDto.getShippingMode();
        longitude = poiInfoDto.getLongitude();
        latitude = poiInfoDto.getLatitude();
        shareableWarehouseId = poiInfoDto.getShareableWarehouseId();
        shareableWarehouseName = poiInfoDto.getShareableWarehouseName();

        if (poiInfoDto.getPoiExtendContentDto() != null) {
            exchangeTime = ConverterUtils.nonNullConvert(poiInfoDto.getPoiExtendContentDto().getExchangeTime(), Date::new);
            operationMode = poiInfoDto.getPoiExtendContentDto().getOperationMode();
        } else {
            operationMode = PoiOperationModeEnum.SELF_EMPLOYED.getKey();
        }
    }

    public PoiInfoDto toPoiInfoDto(){
        return PoiInfoDto.builder()
                .poiId(poiId)
                .poiName(poiName)
                .tenantId(tenantId)
                .wmPoiId(wmPoiId)
                .outPoiId(outPoiId)
                .payMerchantId(payMerchantId)
                .poiAddress(poiAddress)
                .poiStatus(poiStatus)
                .entityType(entityType)
                .departmentId(departmentId)
                .mobile(mobile)
                .shippingMode(shippingMode)
                .longitude(longitude)
                .latitude(latitude)
                .shareableWarehouseId(shareableWarehouseId)
                .shareableWarehouseName(shareableWarehouseName)
                .build();

    }

    public PoiInfoVO toPoiResp() {
        PoiInfoVO resp = new PoiInfoVO();
        resp.setPoiId(poiId);
        resp.setPoiName(poiName);
        resp.setAddress(poiAddress);
        resp.setOutPoiId(outPoiId);
        resp.setStatus(Integer.valueOf(1).equals(poiStatus) ? "有效":"无效");
        resp.setStatusCode(String.valueOf(poiStatus));
        resp.setCityName(ConverterUtils.nonNullConvert(areaInfo, AreaBO::getCityName));
        resp.setAreaCode(ConverterUtils.nonNullConvert(areaInfo, a -> a.getProvinceCode()+"_"+a.getCityCode()));
        resp.setEntityType(entityType);
        resp.setShippingMode(shippingMode);
        resp.setOperationMode(operationMode);
        return resp;
    }
}