package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.shangou.saas.resource.management.dto.appraise.request.AppraisePreviewRequest;
import com.meituan.shangou.saas.resource.management.dto.appraise.request.AppraiseResultQueryRequest;
import com.meituan.shangou.saas.resource.management.dto.appraise.request.IndicatorDetailQueryRequest;
import com.meituan.shangou.saas.resource.management.dto.appraise.response.AppraisePreviewResponse;
import com.meituan.shangou.saas.resource.management.dto.appraise.response.AppraiseResultWithIndicatorResponse;
import com.meituan.shangou.saas.resource.management.dto.appraise.response.IndicatorDetailResponse;
import com.meituan.shangou.saas.resource.management.thrift.AppraiseThriftService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;

/**
 * @author: liwei101
 * @since: 2021/5/11 14:38
 */
@Component
public class AppraiseThriftAdapter {

    @Autowired
    private AppraiseThriftService appraiseThriftService;

    @Autowired
    private com.meituan.shangou.oslo.appraise.client.thrift.AppraiseThriftService newAppraiseThriftService;


    public AppraisePreviewResponse previewEntityAppraisePlan(AppraisePreviewRequest appraisePreviewRequest) {
        if (useNewAppraiseService()) {
            return convertPreviewResponse(newAppraiseThriftService.previewEntityAppraisePlan(convertPreviewRequest(appraisePreviewRequest)));
        }
        else {
            return appraiseThriftService.previewEntityAppraisePlan(appraisePreviewRequest);
        }
    }

    private AppraisePreviewResponse convertPreviewResponse(
            com.meituan.shangou.oslo.appraise.client.dto.appraise.response.AppraisePreviewResponse previewEntityAppraisePlan) {
        return JacksonUtils.convertValue(previewEntityAppraisePlan, AppraisePreviewResponse.class);
    }

    private com.meituan.shangou.oslo.appraise.client.dto.appraise.request.AppraisePreviewRequest convertPreviewRequest(AppraisePreviewRequest appraisePreviewRequest) {
        return JacksonUtils.convertValue(appraisePreviewRequest,
                com.meituan.shangou.oslo.appraise.client.dto.appraise.request.AppraisePreviewRequest.class);
    }

    private boolean useNewAppraiseService() {
        return MccConfigUtil.useNewAppraiseService();
    }


    public AppraiseResultWithIndicatorResponse queryAppraiseResult(AppraiseResultQueryRequest appraiseResultQueryRequest) {
        if (useNewAppraiseService()) {
            return convertQueryResultResponse(newAppraiseThriftService.queryAppraiseResult(convertQueryResultRequest(appraiseResultQueryRequest)));
        }
        else {
            return appraiseThriftService.queryAppraiseResult(appraiseResultQueryRequest);
        }
    }

    private AppraiseResultWithIndicatorResponse convertQueryResultResponse(
            com.meituan.shangou.oslo.appraise.client.dto.appraise.response.AppraiseResultWithIndicatorResponse queryAppraiseResult) {
        return JacksonUtils.convertValue(queryAppraiseResult, AppraiseResultWithIndicatorResponse.class);
    }

    private com.meituan.shangou.oslo.appraise.client.dto.appraise.request.AppraiseResultQueryRequest convertQueryResultRequest(AppraiseResultQueryRequest appraiseResultQueryRequest) {
        return JacksonUtils.convertValue(appraiseResultQueryRequest,
                com.meituan.shangou.oslo.appraise.client.dto.appraise.request.AppraiseResultQueryRequest.class);
    }


    public IndicatorDetailResponse queryIndicatorDetails(IndicatorDetailQueryRequest detailQueryRequest) {
        if (useNewAppraiseService()) {
            return convertQueryDetailResponse(newAppraiseThriftService.queryIndicatorDetails(convertQueryDetailRequest(detailQueryRequest)));
        }
        else {
            return appraiseThriftService.queryIndicatorDetails(detailQueryRequest);
        }
    }

    private IndicatorDetailResponse convertQueryDetailResponse(com.meituan.shangou.oslo.appraise.client.dto.appraise.response.IndicatorDetailResponse queryIndicatorDetails) {
        return JacksonUtils.convertValue(queryIndicatorDetails, IndicatorDetailResponse.class);
    }

    private com.meituan.shangou.oslo.appraise.client.dto.appraise.request.IndicatorDetailQueryRequest convertQueryDetailRequest(IndicatorDetailQueryRequest detailQueryRequest) {
        return JacksonUtils.convertValue(detailQueryRequest, com.meituan.shangou.oslo.appraise.client.dto.appraise.request.IndicatorDetailQueryRequest.class);
    }


}
