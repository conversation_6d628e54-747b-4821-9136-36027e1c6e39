package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-12-08
 * @email <EMAIL>
 */
@TypeDoc(
        description = "入职待审批/已审批详情请求返回"
)
@Data
@ApiModel("入职待审批/已审批详情请求返回")
@AllArgsConstructor
@NoArgsConstructor
public class ResignApprovalDetailVO {

    @FieldDoc(
            description = "员工姓名"
    )
    private String accountName;

    @FieldDoc(
            description = "员工id"
    )
    private Long employeeId;

    @FieldDoc(
            description = "员工姓名"
    )
    private String employeeName;

    @FieldDoc(
            description = "电话"
    )
    private String phoneNumber;

    @FieldDoc(
            description = "归属门店名称列表"
    )
    private List<String> belongStoreNameList;

    @FieldDoc(
            description = "工作类型名称"
    )
    private List<String> roleNameList;

    @FieldDoc(
            description = "申请时间，unix毫秒时间戳"
    )
    private Long resignDate;

    @FieldDoc(
            description = "申请时间，unix毫秒时间戳"
    )
    private Integer resignType;

    @FieldDoc(
            description = "申请时间，unix毫秒时间戳"
    )
    private String resignText;


    @FieldDoc(
            description = "门店类型"
    )
    private Integer poiOperationMode;

}
