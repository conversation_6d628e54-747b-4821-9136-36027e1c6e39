package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.SaleAttrVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ChannelCategoryDTO;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.dto.ChannelSaleAttrDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/23 10:56
 * @Description:
 */
@TypeDoc(
        name = "ChannelCategoryLevelVO",
        description = "渠道类目VO对象"
)
@Setter
@Getter
@ToString
@EqualsAndHashCode
public class ChannelCategoryLevelVO {

    @FieldDoc(
            description = "渠道类目编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道类目编码")
    private String categoryCode;

    @FieldDoc(
            description = "渠道类目父级编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道类目父级编码")
    private String parentCode;

    @FieldDoc(
            description = "渠道类目名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道类目名称")
    private String categoryName;


    @FieldDoc(
            description = "是否有子类目信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否有子类目信息")
    private Boolean hasChildren;

    @FieldDoc(
            description = "渠道类目层级", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道类目层级")
    private Integer level;

    @FieldDoc(
            description = "是否支持多规格 1单规格 2多规格", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否支持多规格")
    private Integer supportSpecType = 0;

    @FieldDoc(
            description = "京东到家渠道类目属性列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "京东到家渠道类目属性列表")
    private List<SaleAttrVo> jdSaleAttrList;

    @FieldDoc(
            description = "是否校验upc (0:不校验,1:校验，注意，只有三级分类该属性才有效)"
    )
    @ApiModelProperty(name = "是否校验upc (0:不校验,1:校验，注意，只有三级分类该属性才有效")
    private Integer checkUpcStatus;

    @FieldDoc(
            description = "该类目的商品条形码（UPC）是否必填，0-必填，1-选填 (只有三级分类该属性才有效)"
    )
    @ApiModelProperty(name = "该类目的商品条形码（UPC）是否必填，0-必填，1-选填 (只有三级分类该属性才有效)")
    private Integer upcRequired;

    @FieldDoc(
            description = "类目来源，1-零售，2-医药",
            example = {}
    )
    @ApiModelProperty(name = "类目来源，1-零售，2-医药")
    private Integer resourceType;

    @FieldDoc(
            description = "医药和零售是否为相同的类目，0-不相同，1-相同",
            example = {}
    )
    @ApiModelProperty(name = "医药和零售是否为相同的类目，0-不相同，1-相同")
    private Integer existedInRetailAndMedicine;

    @FieldDoc(
            description = "资质图信息填写要求 0-不需要填写 1-仅展示，不强制校验是否填写 2-必填",
            example = "1"
    )
    @ApiModelProperty(name = "资质图信息填写要求 0-不需要填写 1-仅展示，不强制校验是否填写 2-必填")
    private Integer medicalDeviceQuaRequirement;

    public ChannelCategoryLevelVO(ChannelCategoryDTO channelCategoryDTO) {
        categoryCode = channelCategoryDTO.getCategoryId();
        parentCode = channelCategoryDTO.getParentId();
        categoryName = channelCategoryDTO.getCategoryName();
        level = channelCategoryDTO.getDepth();
        hasChildren = channelCategoryDTO.subAmount > 0;
    }

    public ChannelCategoryLevelVO(
            com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.dto.ChannelCategoryDTO dto) {
        this.categoryCode = dto.getChannelCategoryId();
        this.parentCode = dto.getParentChannelCategoryId();
        this.categoryName = dto.getChannelCategoryName();
        this.level = dto.getLevel();
        this.hasChildren = dto.getSubChannelCategoryNum() != null && dto.getSubChannelCategoryNum() > 0;
        this.supportSpecType = dto.getSupportSpecType();
        this.checkUpcStatus = dto.getCheckUpcStatus();
        this.upcRequired = dto.getUpcRequired();
        if (CollectionUtils.isNotEmpty(dto.getChannelSaleAttrDTOS())) {
            this.jdSaleAttrList = ConverterUtils.convertList(dto.getChannelSaleAttrDTOS(),SaleAttrVo::ofDTO);
        }
        this.resourceType = dto.getResourceType();
        this.existedInRetailAndMedicine = dto.getExistedInRetailAndMedicine();
        if (dto.getMedicalDeviceQuaRequirement() != null) {
            this.medicalDeviceQuaRequirement = dto.getMedicalDeviceQuaRequirement().getRequirement();
        } else {
            // 默认为0-无需填写
            this.medicalDeviceQuaRequirement = 0;
        }
    }

}
