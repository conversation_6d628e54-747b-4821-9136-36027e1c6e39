package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/7/11
 * desc: 订单详情
 */
@TypeDoc(
        description = "订单详情"
)
@ApiModel("订单详情")
@Data
public class OrderDetailVO {

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道订单号", required = true)
    private String channelOrderId;

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID", required = true)
    private Integer channelId;

    @FieldDoc(
            description = "订单流水", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单流水", required = true)
    private Long serialNo;

    @FieldDoc(
            description = "商品数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品数量", required = true)
    private Integer itemCount;

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID", required = true)
    private Long storeId;

    @FieldDoc(
            description = "门店名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店名称", required = true)
    private String storeName;

    @FieldDoc(
            description = "渠道名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道名称", required = true)
    private String channelName;

    @FieldDoc(
            description = "可操作列表 10-接单 20-完成拣货 30-补打小票 40-全单退款 50-部分退款", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "可操作列表 10-接单 20-完成拣货 30-补打小票 40-全单退款 50-部分退款", required = true)
    private List<Integer> couldOperateItemList;

    @FieldDoc(
            description = "收货人姓名", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "收货人姓名", required = true)
    private String receiverName;

    @FieldDoc(
            description = "收货人电话", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "收货人电话", required = true)
    private String receiverPhone; // required

    @FieldDoc(
            description = "收货人地址", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "收货人地址", required = true)
    private String receiveAddress; // required

    @FieldDoc(
            description = "订单原金额 单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单原金额 单位:分", required = true)
    private Integer originalAmt;

    @FieldDoc(
            description = "订单实付金额  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单实付金额  单位:分", required = true)
    private Integer actualPayAmt;

    @FieldDoc(
            description = "商品金额小计  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品金额小计  单位:分", required = true)
    private Integer productTotalPayAmount;

    @FieldDoc(
            description = "商家实收金额  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商家实收金额  单位:分", required = true)
    private Integer bizReceiveAmt;

    @FieldDoc(
            description = "运费  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "运费  单位:分", required = true)
    private Integer freight;

    @FieldDoc(
            description = "打包费  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "打包费  单位:分", required = true)
    private Integer packageAmt;

    @FieldDoc(
            description = "平台服务费  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "平台服务费  单位:分", required = true)
    private Integer platformFee;

    @FieldDoc(
            description = "是否开发票  0-否 1-是", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "是否开发票  0-否 1-是", required = true)
    private Integer isNeedInvoice; // required

    @FieldDoc(
            description = "发票抬头", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "发票抬头", required = true)
    private String invoiceTitle; // required

    @FieldDoc(
            description = "发票税号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "发票税号", required = true)
    private String taxNo;

    @FieldDoc(
            description = "配送方式", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "配送方式", required = true)
    private int deliveryMethod;

    @FieldDoc(
            description = "配送方式描述", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "配送方式描述", required = true)
    private String deliveryMethodName;

    @FieldDoc(
            description = "配送人姓名", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "配送人姓名", required = true)
    private String deliveryUserName;

    @FieldDoc(
            description = "配送人电话", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "配送人电话", required = true)
    private String deliveryUserPhone;

    @FieldDoc(
            description = "配送订单类型   0-立即送达 1-预约送达", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "配送订单类型   0-立即送达 1-预约送达", required = true)
    private Integer deliveryOrderType;

    @FieldDoc(
            description = "配送订单类型名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "配送订单类型名称", required = true)
    private String deliveryOrderTypeName;

    @FieldDoc(
            description = "超时配送异常标志(0否1是)"
    )
    private Integer isDeliveryOvertime;

    @FieldDoc(
            description = "配送异常描述"
    )
    private String deliveryExceptionDescription;

    @FieldDoc(
            description = "配送类型"
    )
    private Integer distributeType;

    @FieldDoc(
            description = "配送类型名称"
    )
    private String distributeTypeName;

    @FieldDoc(
            description = "预计送达时间开始时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "预计送达时间开始时间", required = true)
    private Long estimatedSendArriveTimeStart;

    @FieldDoc(
            description = "预计送达时间截止时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "预计送达时间截止时间", required = true)
    private Long estimatedSendArriveTimeEnd;

    @FieldDoc(
            description = "支付方式", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "支付方式", required = true)
    private Integer payMethod;

    @FieldDoc(
            description = "支付方式描述", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "支付方式描述", required = true)
    private String payMethodDesc;

    @FieldDoc(
            description = "订单状态 10-新建订单 20-商家已确认 35-履约中 30-订单已完成 40-取消处理中 50-已取消", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单状态 10-新建订单 20-商家已确认 35-履约中 30-订单已完成 40-取消处理中 50-已取消", required = true)
    private Integer channelOrderStatus;

    @FieldDoc(
            description = "订单聚合状态 1-进行中订单 2-已完成订单 3-订单已取消", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单聚合状态 1-进行中订单 2-已完成订单 3-订单已取消", required = true)
    private Integer aggregationOrderStatus;

    @FieldDoc(
            description = "订单状态描述", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单状态描述", required = true)
    private String channelOrderStatusDesc;

    @FieldDoc(
            description = "创建时间戳", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "创建时间戳", required = true)
    private long createTime;

    @FieldDoc(
            description = "最新售后申请类型", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "最新售后申请类型", required = true)
    private Integer lastAfterSaleApplyRefundTagId;

    @FieldDoc(
            description = "最新售后申请原因", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "最新售后申请原因", required = true)
    private String lastAfterSaleApplyReason;

    @FieldDoc(
            description = "最新售后申请状态", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "最新售后申请状态", required = true)
    private Integer lastAfterSaleApplyStatus;

    @FieldDoc(
            description = "最新售后申请驳回原因", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "最新售后申请驳回原因", required = true)
    private String lastAfterSaleApplyRejectReason;

    @FieldDoc(
            description = "可退订单金额（实付金额 - 申请退款金额）单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "可退订单金额（实付金额 - 申请退款金额）单位:分", required = true)
    private Integer refundableOrderAmount;

    @FieldDoc(
            description = "更新时间戳", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "更新时间戳", required = true)
    private Long updateTime;

      @FieldDoc(
            description = "配送状态", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "配送状态", required = true)
    private Integer distributeStatus;

    @FieldDoc(
            description = "配送状态名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "配送状态名称", required = true)
    private String distributeStatusName;

    @FieldDoc(
            description = "拣货状态", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "拣货状态", required = true)
    private Integer pickupStatus;

    @FieldDoc(
            description = "渠道第二订单号 饿百渠道的饿了么订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道第二订单号 饿百渠道的饿了么订单号", required = true)
    private String channelExtraOrderId;

    @FieldDoc(
            description = "备注", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "备注", required = true)
    private String comments;

    @FieldDoc(
            description = "订单商品列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单商品列表", required = true)
    private List<ProductVOForRefund> productList;

    @FieldDoc(
            description = "促销列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "促销列表", required = true)
    private List<PromotionVO> promotionList;

    @FieldDoc(
            description = "订单状态日志列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单状态日志列表", required = true)
    private List<OrderStatusLogVO> orderStatuslogList;

    @FieldDoc(
            description = "售后记录列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "售后记录列表", required = true)
    private List<AfterSaleRecordVO> afterSaleRecordList;

    @FieldDoc(
            description = "支付时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "支付时间", required = true)
    private Long payTime;

    @FieldDoc(
            description = "订单来源", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单来源", required = true)
    private Integer orderSource;
}
