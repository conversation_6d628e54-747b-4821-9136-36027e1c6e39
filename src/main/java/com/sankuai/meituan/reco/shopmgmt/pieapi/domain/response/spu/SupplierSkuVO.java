package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.SupplierSkuDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/22
 */

@Data
public class SupplierSkuVO {
    @FieldDoc(
            description = "关联供货商规格ID"
    )
    @ApiModelProperty(value = "关联供货商规格ID")
    private String supplierSkuId;

    @FieldDoc(
            description = "箱规名称"
    )
    @ApiModelProperty("箱规名称")
    private String cartonMeasureName;

    @FieldDoc(
            description = "区域id列表"
    )
    @ApiModelProperty(value = "区域id列表")
    private List<Integer> areaIdList;

    public static SupplierSkuVO of(SupplierSkuDTO supplierSkuDTO) {
        SupplierSkuVO supplierSkuVO = new SupplierSkuVO();
        supplierSkuVO.setSupplierSkuId(supplierSkuDTO.getSupplierSkuId());
        supplierSkuVO.setCartonMeasureName(supplierSkuDTO.getCartonMeasureName());
        supplierSkuVO.setAreaIdList(supplierSkuDTO.getAreaIdList());
        return supplierSkuVO;
    }

    public static SupplierSkuDTO toSupplierSkuDTO(SupplierSkuVO supplierSkuVO) {
        SupplierSkuDTO supplierSkuDTO = new SupplierSkuDTO();
        supplierSkuDTO.setSupplierSkuId(supplierSkuVO.getSupplierSkuId());
        supplierSkuDTO.setCartonMeasureName(supplierSkuVO.getCartonMeasureName());
        supplierSkuDTO.setAreaIdList(supplierSkuVO.getAreaIdList());
        return supplierSkuDTO;
    }


    public static SupplierSkuVO of(com.sankuai.meituan.shangou.empower.productbiz.client.dto.SupplierSkuDTO supplierSkuDTO) {
        SupplierSkuVO supplierSkuVO = new SupplierSkuVO();
        supplierSkuVO.setSupplierSkuId(supplierSkuDTO.getSupplierSkuId());
        supplierSkuVO.setCartonMeasureName(supplierSkuDTO.getCartonMeasureName());
        supplierSkuVO.setAreaIdList(supplierSkuDTO.getAreaIdList());
        return supplierSkuVO;
    }

    public static com.sankuai.meituan.shangou.empower.productbiz.client.dto.SupplierSkuDTO toBizSupplierSkuDTO(SupplierSkuVO supplierSkuVO) {
        com.sankuai.meituan.shangou.empower.productbiz.client.dto.SupplierSkuDTO supplierSkuDTO = new com.sankuai.meituan.shangou.empower.productbiz.client.dto.SupplierSkuDTO();
        supplierSkuDTO.setSupplierSkuId(supplierSkuVO.getSupplierSkuId());
        supplierSkuDTO.setCartonMeasureName(supplierSkuVO.getCartonMeasureName());
        supplierSkuDTO.setAreaIdList(supplierSkuVO.getAreaIdList());
        return supplierSkuDTO;
    }
}
