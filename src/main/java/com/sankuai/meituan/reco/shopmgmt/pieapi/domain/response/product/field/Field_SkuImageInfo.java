package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.product.field;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SkuImageInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.product.IProductContainer;

/**
 * <AUTHOR>
 * @since 2024-12-26
 */
public interface Field_SkuImageInfo extends IProductContainer {
    default SkuImageInfo getImageInfo() {
        return (SkuImageInfo) get("imageInfo");
    }

    default void setImageInfo(SkuImageInfo imageInfo) {
        put("imageInfo", imageInfo);
    }
}
