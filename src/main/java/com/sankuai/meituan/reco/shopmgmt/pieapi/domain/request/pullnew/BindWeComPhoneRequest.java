package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pullnew;

import javax.validation.constraints.Pattern;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2022/8/16 17:49
 **/
@TypeDoc(
        description = "绑定企微电话号码请求",
        authors = {
                "youcong"
        },
        version = "V1.0"
)
@Data
@ApiModel("绑定企微电话号码服务")
public class BindWeComPhoneRequest {
    @FieldDoc(
            description = "手机号"
    )
    @ApiModelProperty(value = "手机号")
    @Pattern(regexp = "^1\\d{10}$", message = "手机号不合法")
    private String phone;
}
