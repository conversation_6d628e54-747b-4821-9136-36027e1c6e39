package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.IntegerBooleanConstants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @createTime 2019/11/19
 * @description
 */
@TypeDoc(
        description = "订单首页模块"
)
@Data
@ApiModel("订单首页模块")
public class OrderHomePageModuleVO {

    @FieldDoc(
            description = "是否展示待接单tab (0:否, 1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示待接单tab (0:否, 1:是)", required = true)
    @NotNull
    private Integer showWaitToTakeOrderTab;

    @FieldDoc(
            description = "是否展示待拣货tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示待拣货tab (0:否，1:是)", required = true)
    @NotNull
    private Integer showWaitToPickTab;

    @FieldDoc(
            description = "是否展示待配送tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示待配送tab (0:否，1:是)", required = true)
    @NotNull
    private Integer showWaitToDeliveryTab;

    @FieldDoc(
            description = "是否展示配送异常tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示配送异常tab (0:否，1:是)", required = true)
    @NotNull
    private Integer showDeliveryErrorTab;

    @FieldDoc(
            description = "是否展示退款tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示退款tab (0:否，1:是)", required = true)
    @NotNull
    private Integer showWaitToAuditRefundTab;

    @FieldDoc(
            description = "是否展示售后tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示售后tab (0:否，1:是)", required = true)
    @NotNull
    private Integer showWaitToAuditAfterSaleTab;

    @FieldDoc(
            description = "是否展示订单tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示订单tab (0:否，1:是)", required = true)
    @NotNull
    private Integer showOrderTab;

    @FieldDoc(
            description = "是否展示拣货tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示拣货tab (0:否，1:是)", required = true)
    @NotNull
    private Integer showPickTab;

    @FieldDoc(
            description = "是否展示订单查询 (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示订单查询 (0:否，1:是)", required = true)
    @NotNull
    private Integer showOrderSearch;

    /**
     * 骑手tab start
     */
    @FieldDoc(
            description = "是否展示骑手领取订单页面，即新配送任务页面 (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示骑手领取订单页面，即新配送任务页面 (0:否，1:是)", required = true)
    @NotNull
    private Integer showRiderGetOrderTab;

    @FieldDoc(
            description = "是否展示骑手待取货页面 (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示骑手待取货页面 (0:否，1:是)", required = true)
    @NotNull
    private Integer showRiderTakeGoodsTab;

    @FieldDoc(
            description = "是否展示骑手配送中页面 (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示骑手配送中页面 (0:否，1:是)", required = true)
    @NotNull
    private Integer showRiderInDeliveryTab;

    @FieldDoc(
            description = "是否展示骑手已完成页面 (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示骑手已完成页面 (0:否，1:是)", required = true)
    @NotNull
    private Integer showRiderCompletedTab;

    @FieldDoc(
            description = "是否展示歪马异常TAB (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示歪马异常TAB (0:否，1:是)", required = true)
    @NotNull
    private Integer showDhExceptionTab;
    /**
     * 骑手tab end
     */


    @FieldDoc(
            description = "是否将规格信息放到订单信息中(0:不展示,1:展示)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否将规格信息放到订单信息中(0:不展示,1:展示)", required = true)
    @NotNull
    private Integer appendSpecToOrderItem;

    @FieldDoc(
            description = "是否展示待自提tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示待自提tab (0:否，1:是)", required = true)
    @NotNull
    private Integer showWaitToSelfFetchTab;

    @FieldDoc(
            description = "是否展示评价tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示评价tab (0:否，1:是)")
    @NotNull
    private Integer showCommitManagementTab;

    @FieldDoc(
            description = "是否展示推广自提tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示推广自提tab (0:否，1:是)", required = true)
    @NotNull
    private Integer showSelfPickPromoteOrder;

    public static OrderHomePageModuleVO homePageInit(){
        OrderHomePageModuleVO orderHomePageModuleVO = new OrderHomePageModuleVO();
        orderHomePageModuleVO.setShowWaitToTakeOrderTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowWaitToPickTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowWaitToSelfFetchTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowWaitToDeliveryTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowDeliveryErrorTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowWaitToAuditRefundTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowWaitToAuditAfterSaleTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowOrderTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowPickTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowRiderGetOrderTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowRiderTakeGoodsTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowRiderInDeliveryTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowRiderCompletedTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        //全部订单、部分订单、评价默认无权限
        orderHomePageModuleVO.setShowOrderSearch(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowOrderTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowCommitManagementTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        return orderHomePageModuleVO;
    }

}
