package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.wavepick.converter;

import com.sankuai.meituan.reco.pickselect.thrift.wave.request.WarehousePackSeedWaitDetailRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick.WarehousePickPackSeedWaitDetailRequest;
import org.mapstruct.Mapper;

/**
 * @description:
 * @Auther: nifei
 * @Date: 2023/8/22 18:13
 */
@Mapper(componentModel = "spring")
public abstract class WarehousePickPackSeedWaitDetailRequestConverter {
    public abstract WarehousePackSeedWaitDetailRequest convert2ThriftRequest(WarehousePickPackSeedWaitDetailRequest request, Long tenantId, Long accountId, Long storeId);
}
