package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ReviewOperateRecordDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: 操作记录对象
 * @author: WangSukuan
 * @create: 2020-03-18
 **/
@TypeDoc(
        description = "操作记录对象"
)
@Data
@ApiModel("操作记录对象")
@NoArgsConstructor
public class ReviewOperateRecordVO {

    @FieldDoc(description = "操作人ID")
    @ApiModelProperty(name = "操作人ID")
    private Long operateId;

    @FieldDoc(description = "操作人名称")
    @ApiModelProperty(name = "操作人名称")
    private String operateName;

    @FieldDoc(description = "操作时间")
    @ApiModelProperty(name = "操作时间")
    private Long operateTime;

    @FieldDoc(description = "审核状态(SUBMIT = 1, 提报；RECALL = 6, 撤回；PASS = 10, 审核；REJECT = 11;驳回)")
    @ApiModelProperty(name = "审核状态(SUBMIT = 1, 提报；RECALL = 6, 撤回；PASS = 10, 审核；REJECT = 11;驳回)")
    private Integer operateType;

    @FieldDoc(description = "审核通过/驳回原因")
    @ApiModelProperty(name = "审核通过/驳回原因")
    private String rejectReason;
    @FieldDoc(description = "审核备注")
    @ApiModelProperty(name = "审核备注")
    private String reviewRemark;


    public ReviewOperateRecordVO buildReviewOperateRecordVO(ReviewOperateRecordDTO reviewOperateRecordDTO){

        this.operateId = reviewOperateRecordDTO.getOperateId();
        this.operateName = reviewOperateRecordDTO.getOperateName();
        this.operateTime = reviewOperateRecordDTO.getOperateTime();
        this.operateType = reviewOperateRecordDTO.getOperateType();
        return this;
    }

    public ReviewOperateRecordVO buildReviewOperateRecordVO(com.sankuai.meituan.shangou.empower.productbiz.client.dto.review.ReviewOperateRecordDTO reviewOperateRecordDTO){

        this.operateId = reviewOperateRecordDTO.getOperateId();
        this.operateName = reviewOperateRecordDTO.getOperateName();
        this.operateTime = reviewOperateRecordDTO.getOperateTime();
        this.operateType = reviewOperateRecordDTO.getOperateType();
        this.rejectReason = reviewOperateRecordDTO.getRejectReason();
        this.reviewRemark = reviewOperateRecordDTO.getReviewRemark();
        return this;
    }
}
