package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pullnew;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2021/11/1 5:38 下午
 **/
@TypeDoc(
        description = "用户下级拉新记录",
        authors = {
                "youcong"
        },
        version = "V1.0"
)
@Data
@ApiModel("用户下级拉新记录")
public class SubordinateRecordRequest {

    @FieldDoc(
            description = "门店ID"
    )
    @ApiModelProperty(value = "门店ID")
    private Long storeId;

    @FieldDoc(
            description = "下级账号id"
    )
    @ApiModelProperty(value = "下级账号id")
    private Long subordinateAccountId;

    @FieldDoc(
            description = "下级账号名"
    )
    @ApiModelProperty(value = "下级账号名")
    private String subordinateAccountName;

    @FieldDoc(
            description = "开始时间"
    )
    @ApiModelProperty(value = "开始时间")
    private Long startTime;

    @FieldDoc(
            description = "截止时间"
    )
    @ApiModelProperty(value = "截止时间")
    private Long endTime;

    @FieldDoc(
            description = "分页符"
    )
    @ApiModelProperty(value = "分页符")
    private Long markId;

    @FieldDoc(
            description = "每页大小"
    )
    @ApiModelProperty(value = "每页大小")
    private Integer pageSize = 20;
}
