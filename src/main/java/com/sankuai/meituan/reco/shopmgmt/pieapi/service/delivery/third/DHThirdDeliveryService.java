package com.sankuai.meituan.reco.shopmgmt.pieapi.service.delivery.third;

import com.dianping.cat.Cat;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;
import com.meituan.linz.boot.util.Fun;
import com.meituan.shangou.saas.order.management.client.dto.request.online.ViewIdCondition;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.*;
import com.meituan.shangou.saas.order.management.client.dto.response.revenue.OrderRevenueDetailResponse;
import com.meituan.shangou.saas.order.platform.enums.DistributeStatusEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.dto.RiderPickOrderDTO;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.response.QueryRiderPickOrderResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.DeliveryOperateItemEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.IntegerBooleanConstants;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.converters.channelorder.ChannelOrderConverter;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery.third.PageQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery.third.QueryWaitToDeliveryOrderBySubTypeRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.delivery.DeliveryRedirectModuleVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.delivery.third.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.OrderListResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.OrderVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.ProductVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.TagInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.ParsedPropertiesVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AggDeliveryPlatformEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.DrunkHorseWaitToDeliverySubTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.DeliveryChannelUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MemPageUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.PageUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.AbnOrderServiceWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.AuthThriftWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.OCMSOrderServiceWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.TenantWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.delivery.AppendDeliveryInfoService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.delivery.DeliveryChannelWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.delivery.TmsDeliveryStatusDesc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.delivery.TmsServiceWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.pick.DHThirdDeliveryPickWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.pick.FulfillmentOrderServiceWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.pick.TradeShippingOrderServiceWrapper;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.DeliveryOrderType;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.OrderCouldOperateItem;
import com.sankuai.meituan.shangou.empower.rider.client.enums.RiderDeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.dto.DeliveryChannelDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryDetail;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TOrderIdentifier;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.PageQueryDeliveryInfoResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.QueryThirdDeliveryOrderCountResponse;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.qnh.ofc.ofw.client.thrift.dto.order.FulfillmentOrderDetailDTO;
import com.sankuai.qnh.ofc.ofw.client.thrift.request.order.ChannelOrderIdKeyReq;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.delivery.poi.SelfDeliveryPoiConfigThriftService;
import com.sankuai.shangou.logistics.delivery.poi.dto.SelfDeliveryPoiConfigDTO;
import com.sankuai.shangou.logistics.warehouse.TradeShippingOrderService;
import com.sankuai.shangou.logistics.warehouse.dto.AbnOrderDTO;
import com.sankuai.shangou.logistics.warehouse.dto.TradeShippingOrderDTO;
import com.sankuai.shangou.logistics.warehouse.enums.TradeShippingOrderStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConsumableMaterialParseUtils.parseConsumableMaterialInfo;
import static com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.OCMSOrderServiceWrapper.transfer2GoodsItemVO;

/**
 * <AUTHOR>
 * @since 2023/8/25 15:09
 **/
@Service
@Slf4j
public class DHThirdDeliveryService {

    @Resource
    private DHThirdDeliveryPickWrapper dhThirdDeliveryPickWrapper;

    @Resource
    private OCMSOrderServiceWrapper ocmsOrderServiceWrapper;

    @Resource
    private AuthThriftWrapper authThriftWrapper;

    @Resource
    private TmsServiceWrapper tmsServiceWrapper;

    @Resource
    private DeliveryChannelWrapper deliveryChannelWrapper;

    @Resource
    private AppendDeliveryInfoService appendDeliveryInfoService;

    @Resource
    private TenantWrapper tenantWrapper;

    @Resource
    private SelfDeliveryPoiConfigThriftService selfDeliveryPoiConfigThriftService;

    @Resource
    private AbnOrderServiceWrapper abnOrderServiceWrapper;
    @Resource
    private TradeShippingOrderServiceWrapper tradeShippingOrderServiceWrapper;
    @Resource
    private FulfillmentOrderServiceWrapper fulfillmentOrderServiceWrapper;


    // TMS 系统，配送无异常的编码
    private static final Integer DELIVERY_NO_EXCEPTION = 0;

    private static final List<DeliveryStatusEnum> allWaitDeliveryStatus = Arrays.asList(DeliveryStatusEnum.DELIVERY_LAUNCHED,
            DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER,
            DeliveryStatusEnum.RIDER_ASSIGNED,
            DeliveryStatusEnum.RIDER_ARRIVED_SHOP,
            DeliveryStatusEnum.RIDER_TAKEN_GOODS);

    /**
     * 查询待领取的拣货单列表
     * @param page 页码
     * @param pageSize 页大小
     * @return ThirdDeliveryOrderListResponse
     */
    public ThirdDeliveryOrderListResponse queryWaitToTake(int page, int pageSize) {

        //查门店下所有未处理的
        List<TradeShippingOrderDTO> recentShippingOrderList= tradeShippingOrderServiceWrapper.getRecentListByOperatorIdAndStatusList(
                ApiMethodParamThreadLocal.getIdentityInfo().getStoreId(),
                null,
                Lists.newArrayList(TradeShippingOrderStatus.WAITED.getCode())
        );
        if (CollectionUtils.isEmpty(recentShippingOrderList)) {
            ThirdDeliveryOrderListResponse response = new ThirdDeliveryOrderListResponse();
            response.setHasMore(false);
            response.setTotalCount(0);
            response.setOrderList(Lists.newArrayList());
            return response;
        }
        return getThirdDeliveryOrderListResponse(page, pageSize, recentShippingOrderList);

    }

    /**
     * 查询待拣货的拣货单列表
     * @param page 页码
     * @param pageSize 页大小
     * @return ThirdDeliveryOrderListResponse
     */
    public ThirdDeliveryOrderListResponse queryWaitToPick(int page, int pageSize) {

        List<TradeShippingOrderDTO> recentShippingOrderList= tradeShippingOrderServiceWrapper.getRecentListByOperatorIdAndStatusList(
                ApiMethodParamThreadLocal.getIdentityInfo().getStoreId(),
                ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId(),
                Lists.newArrayList(TradeShippingOrderStatus.RECEIVE.getCode())
        );
        if (CollectionUtils.isEmpty(recentShippingOrderList)) {
            ThirdDeliveryOrderListResponse response = new ThirdDeliveryOrderListResponse();
            response.setHasMore(false);
            response.setTotalCount(0);
            response.setOrderList(Lists.newArrayList());
            return response;
        }
        return getThirdDeliveryOrderListResponse(page, pageSize, recentShippingOrderList);


    }

    private ThirdDeliveryOrderListResponse getThirdDeliveryOrderListResponse(int page, int pageSize, List<TradeShippingOrderDTO> recentShippingOrderList) {
        List<FulfillmentOrderDetailDTO> fulfillmentOrderDetailDTOS = getFulfillmentOrderDetailDTOS(recentShippingOrderList);
        List<ViewIdCondition> allViewIdConditions = fulfillmentOrderDetailDTOS
                .stream()
                //只要三方配送的
                .filter(fulfillmentOrderDetailDTO -> Objects.nonNull(fulfillmentOrderDetailDTO.getDeliveryInfo()) && Objects.equals(fulfillmentOrderDetailDTO.getDeliveryInfo().getDeliveryPlatform(), DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode()))
                //排序后内存分页
                .sorted(Comparator.comparingLong(FulfillmentOrderDetailDTO::getEstimatedEndTime))
                .map(fulfillmentOrderDetailDTO -> new ViewIdCondition(fulfillmentOrderDetailDTO.getOrderSource(), fulfillmentOrderDetailDTO.getChannelOrderId()))
                .collect(Collectors.toList());

        MemPageUtils.PageInfo<ViewIdCondition> viewIdConditionPageInfo = MemPageUtils.pagingList(allViewIdConditions, page, pageSize);

        List<ThirdDeliveryOrderVO> pickOrderVOS = buildNewThirdDeliveryOrderVOS(viewIdConditionPageInfo.getPagedList());
        pickOrderVOS.sort((o1, o2) -> {
            try {
                //都用期望的*送达时间*来比较
                long compareKey1 = Objects.nonNull(o1.getAssessDeliveryTime()) ? o1.getAssessDeliveryTime() : o1.getEstimateArriveTimeStart();
                long compareKey2 = Objects.nonNull(o2.getAssessDeliveryTime()) ? o2.getAssessDeliveryTime() : o2.getEstimateArriveTimeStart();
                return (int) (compareKey1 - compareKey2);
            } catch (Exception e) {
                log.error("delivery order compare error", e);
                Cat.logEvent("DELIVERY_ORDER", "COMPARE_ERROR");
                return (int) (o1.getEstimateArriveTimeStart() - o2.getEstimateArriveTimeStart());
            }
        });
        return new ThirdDeliveryOrderListResponse(allViewIdConditions.size(), viewIdConditionPageInfo.isHasMore(), pickOrderVOS);
    }

    private List<FulfillmentOrderDetailDTO> getFulfillmentOrderDetailDTOS(List<TradeShippingOrderDTO> recentListResult) {
        List<ChannelOrderIdKeyReq> channelOrderIdKeyReqs = recentListResult.stream().map(
                tradeShippingOrderDTO -> new ChannelOrderIdKeyReq(tradeShippingOrderDTO.getTradeChannelType(), tradeShippingOrderDTO.getTradeOrderNo())
        ).collect(Collectors.toList());

        List<FulfillmentOrderDetailDTO> fulfillmentOrderDetailDTOList = fulfillmentOrderServiceWrapper.searchFulfillmentOrderListByBatchFulfillmentOrderId(
                ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId(),
                ApiMethodParamThreadLocal.getIdentityInfo().getStoreId(),
                channelOrderIdKeyReqs
        );

        //两遍列表不相等就打个点
        if(Objects.equals(fulfillmentOrderDetailDTOList.size(), recentListResult.size())) {
            Cat.logEvent("RIDER_OFFLINE_PROMOTE_ERROR", "SHIPPING_FULFILLMENT_MISMATCH");
        }

        return fulfillmentOrderDetailDTOList;
    }

    private List<ThirdDeliveryOrderVO> buildNewThirdDeliveryOrderVOS(List<ViewIdCondition> viewIdConditionList) {
        if (CollectionUtils.isEmpty(viewIdConditionList)) {
            return Lists.newArrayList();
        }

        //2. 查订单信息
        List<OCMSOrderVO> ocmsOrderVOS = ocmsOrderServiceWrapper.batchQueryOcmsOrderList(viewIdConditionList);

        //3. 查营收数据
        List<OrderRevenueDetailResponse> revenueDetailResponses = queryOrderRevenueDetail(ocmsOrderVOS);

        //4. 查异常单
        Map<String, AbnOrderDTO> orderAbnMap = abnOrderServiceWrapper.getOrderAbnMap(getStoreId());

        //5. 查运单，填充倒计时
        PageQueryDeliveryInfoResponse deliveryInfoResponse = tmsServiceWrapper.pageQueryThirdDeliveryOrderList(
                getTenantId(), getStoreId(),
                Lists.newArrayList(DeliveryStatusEnum.DELIVERY_LAUNCHED.getCode(), DeliveryStatusEnum.WAITING_TO_ASSIGN_RIDER.getCode(),
                        DeliveryStatusEnum.RIDER_ASSIGNED.getCode(), DeliveryStatusEnum.RIDER_ARRIVED_SHOP.getCode(), DeliveryStatusEnum.RIDER_TAKEN_GOODS.getCode()), false,
                1, 10);
        List<TDeliveryDetail> tDeliveryDetails = deliveryInfoResponse.getTDeliveryDetails();
        List<ThirdDeliveryOrderVO> thirdDeliveryOrderVOS = this.buildNewOrderVOListFromPickOrder(viewIdConditionList, ocmsOrderVOS, revenueDetailResponses, orderAbnMap, tDeliveryDetails);
        thirdDeliveryOrderVOS.sort((o1, o2) -> {
            try {
                //都用期望的*送达时间*来比较
                long compareKey1 = Objects.nonNull(o1.getAssessDeliveryTime()) ? o1.getAssessDeliveryTime() : o1.getEstimateArriveTimeStart();
                long compareKey2 = Objects.nonNull(o2.getAssessDeliveryTime()) ? o2.getAssessDeliveryTime() : o2.getEstimateArriveTimeStart();
                return (int) (compareKey1 - compareKey2);
            } catch (Exception e) {
                log.error("delivery order compare error", e);
                Cat.logEvent("DELIVERY_ORDER", "COMPARE_ERROR");
                return (int) (o1.getEstimateArriveTimeStart() - o2.getEstimateArriveTimeStart());
            }
        });
        return thirdDeliveryOrderVOS;
    }


    /**
     * 查询待配送子tab的运单
     */
    public ThirdDeliveryOrderListResponse queryWaitDeliveryBySubType(QueryWaitToDeliveryOrderBySubTypeRequest request) {
        return queryDeliveryOrderList(request, false, DrunkHorseWaitToDeliverySubTypeEnum.enumOf(request.getSubType()).getDeliveryStatusEnumList());
    }


    /**
     * 查询异常运单
     */
    public ThirdDeliveryOrderListResponse queryThirdOnExceptionOrders(PageQueryRequest request) {
        List<TDeliveryDetail> tDeliveryDetails = tmsServiceWrapper.queryDeliveryExceptionOrCanceledOrders(getStoreId(),getTenantId());
        if(CollectionUtils.isEmpty(tDeliveryDetails)) {
            return new ThirdDeliveryOrderListResponse(0, false, Lists.newArrayList());
        }

        //2. 查订单信息
        List<OCMSOrderVO> ocmsOrderVOS = ocmsOrderServiceWrapper.batchQueryOcmsOrderList(Fun.map(tDeliveryDetails,
                deliveryOrder -> new ViewIdCondition(deliveryOrder.orderBizType, deliveryOrder.channelOrderId)));
        if (CollectionUtils.isEmpty(ocmsOrderVOS)) {
            return new ThirdDeliveryOrderListResponse(0, false, Lists.newArrayList());
        }

        Map<String, OCMSOrderVO> channelOrderIdVOMap = ocmsOrderVOS
                .stream()
                .collect(Collectors.toMap(
                        OCMSOrderVO::getViewOrderId,
                        Function.identity(),
                        (older, newer) -> newer
                ));

        List<TDeliveryDetail> filterDeliveryDetails = tDeliveryDetails
                .stream()
                .filter(tDeliveryDetail -> channelOrderIdVOMap.containsKey(tDeliveryDetail.channelOrderId))
                //取消即终态
                .filter(tOrderIdentifier -> !Objects.equals(channelOrderIdVOMap.get(tOrderIdentifier.channelOrderId).getOrderStatus(), OrderStatusEnum.CANCELED.getValue()))
                //运单是取消，但送达了也不展示了
                .filter(tOrderIdentifier -> !(Objects.equals(tOrderIdentifier.status, DeliveryStatusEnum.DELIVERY_CANCELLED.getCode()) && Objects.equals(channelOrderIdVOMap.get(tOrderIdentifier.channelOrderId).getOrderStatus(), OrderStatusEnum.COMPLETED.getValue())))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterDeliveryDetails)) {
            return new ThirdDeliveryOrderListResponse(0, false, Lists.newArrayList());
        }
        filterDeliveryDetails.sort((o1, o2) -> {
            long diff = o1.createTime - o2.createTime;
            return (int) diff;
        });

        //内存分页
        List<List<TDeliveryDetail>> partition = com.google.common.collect.Lists.partition(filterDeliveryDetails, request.getPageSize());
        if (request.getPage() > partition.size()) {
            return new ThirdDeliveryOrderListResponse(0, false, Lists.newArrayList());
        }
        List<TDeliveryDetail> pagedDeliveryDetails = partition.get(request.getPage() - 1);

        //3. 查营收数据
        List<OrderRevenueDetailResponse> revenueDetailResponses = queryOrderRevenueDetail(ocmsOrderVOS);

        List<ThirdDeliveryOrderVO> deliveryOrderVOS = buildOrderVOListFromDeliveryOrder(pagedDeliveryDetails, ocmsOrderVOS, revenueDetailResponses);
        for (ThirdDeliveryOrderVO deliveryOrderVO : deliveryOrderVOS) {
            if (Objects.isNull(deliveryOrderVO.getDeliveryExceptionType()) || Objects.equals(deliveryOrderVO.getDeliveryExceptionType(), DeliveryExceptionTypeEnum.NO_EXCEPTION.getCode())) {
                deliveryOrderVO.setDeliveryExceptionType(DeliveryExceptionTypeEnum.DELIVERY_EXCEPTION_BY_SYSTEM.getCode());
                deliveryOrderVO.setDeliveryExceptionDesc("其他原因取消");
            }
        }

        //append基础商品信息
        fillGoodsItemList(deliveryOrderVOS, ocmsOrderVOS);

        return new ThirdDeliveryOrderListResponse(filterDeliveryDetails.size(), request.getPage() >= partition.size(), deliveryOrderVOS);
    }

    /**
     * 查询待配送子tab数量
     * @return
     */
    public ThirdWaitToDeliverySubTypeCountResponse queryWaitDeliverySubTypeCount() {
        QueryThirdDeliveryOrderCountResponse statusCountResponse =
                tmsServiceWrapper.queryThirdDeliveryOrderCountByStatusList(getTenantId(), getStoreId());
        Integer exceptionCount = tmsServiceWrapper.countDeliveryExceptionOrCanceledOrder(getStoreId(),getTenantId());
        return buildResponse(statusCountResponse, exceptionCount);
    }

    /**
     * 查询待领取拣货单数量
     * @return
     */
    public Integer queryStoreUnAcceptedPickOrderCount() {
        return dhThirdDeliveryPickWrapper.queryStoreUnAcceptedPickOrderCount(getTenantId(), getStoreId());
    }

    /**
     * 查询待拣货数量
     * @return
     */
    public Integer queryWaitPickOrderCount() {
        return dhThirdDeliveryPickWrapper.queryRiderWaitToPickOrderCount(getTenantId(), getStoreId(), getAccountId());
    }

    /**
     * 三方转自送
     * @param
     * @return
     */
    public void turnToMerchantSelfDelivery(Long orderId, Long riderAccountId) throws TException {
        Optional<TenantWrapper.EmployeeBaseInfo> optEmployeeBaseInfo = tenantWrapper.queryEmployeeInfo(riderAccountId);
        if (!optEmployeeBaseInfo.isPresent()) {
            throw  new BizException(ResultCode.EBASE_EMPLOYEE_NOT_EXIST.defaultMessage);
        }

        TenantWrapper.EmployeeBaseInfo newRiderInfo = optEmployeeBaseInfo.get();
        tmsServiceWrapper.turnToMerchantSelfDeliveryForDrunkHorse(getStoreId(), orderId, riderAccountId, newRiderInfo.getEmployeeName(), newRiderInfo.getEmployeePhone());
    }

    /**
     * 领取三方配送的拣货单
     * @param
     * @return
     */
    public void acceptThirdDeliveryPickOrder(String viewOrderId, Integer channelId) {
        Integer bizType = ChannelOrderConverter.convertChannelId2OrderBizType(channelId);
        if (MccConfigUtil.isNewPickGrayStore(getStoreId())) {
            FulfillmentOrderDetailDTO fulfillmentOrderDetailDTO = fulfillmentOrderServiceWrapper.searchFulfillmentOrderFromMaster(getTenantId(), getStoreId(), new ChannelOrderIdKeyReq(bizType, viewOrderId));
            if (Objects.isNull(fulfillmentOrderDetailDTO.getDeliveryInfo()) || !Objects.equals(fulfillmentOrderDetailDTO.getDeliveryInfo().getDeliveryPlatform(), DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM.getCode())) {
                throw new BizException("该拣货单不是三方配送拣货单, 不可操作领取");
            }
            tradeShippingOrderServiceWrapper.startShip( getStoreId(), viewOrderId, bizType, getAccountId(), getOperatorName());
        } else {
            dhThirdDeliveryPickWrapper.acceptThirdDeliveryPickOrder(getTenantId(), getStoreId(), viewOrderId, bizType, getAccountId(), getOperatorName());
        }
    }

    public Map<DeliveryOperateOrderKey, List<Integer>> fillDeliveryOperateItemsForAllOrder(long tenantId, long storeId, Set<DeliveryOperateOrderKey> deliveryOperateOrderKeys) {
        if (CollectionUtils.isEmpty(deliveryOperateOrderKeys)) {
            return Maps.newHashMap();
        }
        Map<DeliveryOperateOrderKey, List<Integer>> resultMap = Maps.newHashMap();
        log.info("fillDeliveryOperateItemsForAllOrder request, tenantId = {}, storeId = {}, deliveryOperateOrderKeys = {}", tenantId, storeId, deliveryOperateOrderKeys);
        try {
            //非歪马租户直接返回
            if (!MccConfigUtil.getDHTenantIdList().contains(String.valueOf(tenantId))) {
                log.info("非歪马租户，不展示按钮。tenant ={}", tenantId);
                return Maps.newHashMap();
            }
            //1.操作人要有权限
            Map<String, Boolean> permissions = authThriftWrapper.isHasPermissionV2(Lists.newArrayList(AuthCodeEnum.TURN_AGG_DELIVERY.getAuthCode(), AuthCodeEnum.TURN_SELF_DELIVERY.getAuthCode()));
            boolean hasTurnAggAuth = permissions.getOrDefault(AuthCodeEnum.TURN_AGG_DELIVERY.getAuthCode(), Boolean.FALSE);
            boolean hasTurnSelfAuth = permissions.getOrDefault(AuthCodeEnum.TURN_SELF_DELIVERY.getAuthCode(), Boolean.FALSE);

            //校验转三方配置
            TResult<SelfDeliveryPoiConfigDTO> selfDeliveryPoiConfigDTOTResult = selfDeliveryPoiConfigThriftService.querySelfDeliveryConfig(tenantId, storeId);
            if (!selfDeliveryPoiConfigDTOTResult.isSuccess() || Objects.isNull(selfDeliveryPoiConfigDTOTResult.getData())) {
                throw new BizException("查询错误配送配置错误");
            }
            for (DeliveryOperateOrderKey deliveryOperateOrderKey : deliveryOperateOrderKeys) {
                if (Objects.equals(deliveryOperateOrderKey.getDeliveryPlatformEnum(), DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY)) {
                    boolean canTurnAgg = Objects.equals(selfDeliveryPoiConfigDTOTResult.getData().getEnableTurnDelivery(), IntegerBooleanConstants.BOOLEAN_TRUE);
                    boolean actualPayAmtLimit = deliveryOperateOrderKey.getActualPayAmt() < MccConfigUtil.getMaxActualPayAmtForThirdDelivery();
                    boolean orderStatusAfterConfirmed = deliveryOperateOrderKey.getOrderStatusEnum().getValue() >= OrderStatusEnum.MERCHANT_CONFIRMED.getValue();
                    boolean orderStatusNotCompleted = !Objects.equals(deliveryOperateOrderKey.getOrderStatusEnum(), OrderStatusEnum.COMPLETED) && !Objects.equals(deliveryOperateOrderKey.getOrderStatusEnum(), OrderStatusEnum.CANCELED);
                    //有权限 && 开启自配 && 实付小于150 && 接单<订单状态<终态
                    if (hasTurnAggAuth && canTurnAgg && actualPayAmtLimit && orderStatusAfterConfirmed && orderStatusNotCompleted) {
                        resultMap.put(deliveryOperateOrderKey, Lists.newArrayList(com.sankuai.meituan.reco.shopmgmt.pieapi.constants.OrderCouldOperateItem.TURN_AGG_DELIVERY.getValue() ));
                    }

                } else if (Objects.equals(deliveryOperateOrderKey.getDeliveryPlatformEnum(), DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM)) {
                    boolean orderStatusAfterConfirmed = deliveryOperateOrderKey.getOrderStatusEnum().getValue() >= OrderStatusEnum.MERCHANT_CONFIRMED.getValue();
                    boolean orderStatusNotCompleted = !Objects.equals(deliveryOperateOrderKey.getOrderStatusEnum(), OrderStatusEnum.COMPLETED) && !Objects.equals(deliveryOperateOrderKey.getOrderStatusEnum(), OrderStatusEnum.CANCELED);
                    //有权限 && 接单<订单状态<终态
                    if (hasTurnSelfAuth && orderStatusAfterConfirmed && orderStatusNotCompleted) {
                        resultMap.put(deliveryOperateOrderKey, Lists.newArrayList(com.sankuai.meituan.reco.shopmgmt.pieapi.constants.OrderCouldOperateItem.TURN_SELF_DELIVERY.getValue() ));
                    }
                }
            }
            log.info("fillDeliveryOperateItemsForAllOrder response, resultMap = {}", resultMap);
            return resultMap;
        } catch (Exception e) {
            log.error("fillDeliveryOperateItemsForAllOrder error");
            return resultMap;
        }
    }

    public List<Integer> getDeliveryChannelButtons(long tenantId, long storeId, OrderVO order) {
        DeliveryOperateOrderKey deliveryOperateOrderKey =  new DeliveryOperateOrderKey();
        deliveryOperateOrderKey.setOrderId(order.getEmpowerOrderId());
        deliveryOperateOrderKey.setActualPayAmt(order.getActualPayAmt());
        if(DeliveryChannelUtils.isDapDeliveryChannel(order.getDeliveryChannelId())) {
            deliveryOperateOrderKey.setDeliveryPlatformEnum(DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM);
        } else if(DeliveryChannelUtils.isMerchantDeliveryChannel(order.getDeliveryChannelId())) {
            deliveryOperateOrderKey.setDeliveryPlatformEnum(DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY);
        }
        deliveryOperateOrderKey.setOrderStatusEnum(OrderStatusEnum.enumOf(order.getOrderStatus()));
        Set<DeliveryOperateOrderKey> deliveryOperateSet = new HashSet<>();
        deliveryOperateSet.add(deliveryOperateOrderKey);
        Map<DeliveryOperateOrderKey, List<Integer>> deliveryOperateMap = fillDeliveryOperateItemsForAllOrder(tenantId, storeId, deliveryOperateSet);
        List<Integer> deliveryOperateList = deliveryOperateMap.get(deliveryOperateOrderKey);
        if(CollectionUtils.isNotEmpty(deliveryOperateList)) {
            return deliveryOperateList;
        }
        return new ArrayList<>();
    }

    private ThirdWaitToDeliverySubTypeCountResponse buildResponse(QueryThirdDeliveryOrderCountResponse statusCountResponse, Integer exceptionCount) {
        ThirdWaitToDeliverySubTypeCountResponse response = new ThirdWaitToDeliverySubTypeCountResponse();
        int allWaitToDeliveryCount = statusCountResponse.getWaitPlatformAcceptCount()
                + statusCountResponse.getWaitRiderAcceptCount()
                + statusCountResponse.getRiderAcceptedCount()
                + statusCountResponse.getWaitRiderArriveShopCount()
                + statusCountResponse.getRiderDeliveringCount();

        response.setAllSubTypeCount(allWaitToDeliveryCount);
        response.setWaitToLaunchDeliveryCount(statusCountResponse.getWaitPlatformAcceptCount());
        response.setWaitToRiderAcceptCount(statusCountResponse.getWaitRiderAcceptCount());
        response.setWaitToTakeGoodsCount(statusCountResponse.getRiderAcceptedCount() + statusCountResponse.getWaitRiderArriveShopCount());
        response.setDeliveringCount(statusCountResponse.getRiderDeliveringCount());
        response.setExceptionCount(exceptionCount);
        return response;
    }


    private ThirdDeliveryOrderListResponse queryDeliveryOrderList(PageQueryRequest request,
                                                                  Boolean filterException,
                                                                  List<DeliveryStatusEnum> deliveryStatusEnums) {
        //1. 分页查询运单
        PageQueryDeliveryInfoResponse deliveryInfoResponse = tmsServiceWrapper.pageQueryThirdDeliveryOrderList(
                getTenantId(), getStoreId(),
                Fun.map(deliveryStatusEnums, DeliveryStatusEnum::getCode), filterException,
                request.getPage(), request.getPageSize());
        List<TDeliveryDetail> tDeliveryDetails = deliveryInfoResponse.getTDeliveryDetails();

        List<ViewIdCondition> viewIdConditionList = Fun.map(tDeliveryDetails, deliveryOrder -> new ViewIdCondition(deliveryOrder.orderBizType, deliveryOrder.channelOrderId));

        //2. 查订单信息
        List<OCMSOrderVO> ocmsOrderVOS = ocmsOrderServiceWrapper.batchQueryOcmsOrderList(viewIdConditionList);

        //3. 查营收数据
        List<OrderRevenueDetailResponse> revenueDetailResponses = queryOrderRevenueDetail(ocmsOrderVOS);

        List<ThirdDeliveryOrderVO> deliveryOrderVOS = buildOrderVOListFromDeliveryOrder(tDeliveryDetails, ocmsOrderVOS, revenueDetailResponses);

        //append基础商品信息
        fillGoodsItemList(deliveryOrderVOS, ocmsOrderVOS);

        return new ThirdDeliveryOrderListResponse(deliveryInfoResponse.getPageInfo().getTotal(),
                deliveryInfoResponse.getPageInfo().getTotal() > deliveryInfoResponse.getPageInfo().getPageSize() * deliveryInfoResponse.getPageInfo().getPageNum(),
                deliveryOrderVOS);
    }

    private void fillGoodsItemList(List<ThirdDeliveryOrderVO> thirdDeliveryOrderVOS, List<OCMSOrderVO> ocmsOrderList) {
        try {
            if (CollectionUtils.isEmpty(thirdDeliveryOrderVOS)) {
                return;
            }

            //查出库单明细
            List<ViewIdCondition> viewIdConditionList = Fun.map(thirdDeliveryOrderVOS, orderVO -> new ViewIdCondition(ChannelOrderConverter.convertChannelId2OrderBizType(orderVO.getChannelId()), orderVO.getChannelOrderId()));
            List<TradeShippingOrderDTO> recentShippingOrderList = tradeShippingOrderServiceWrapper.queryByTradeOrderIds(getStoreId(), viewIdConditionList);

            Map<String, TradeShippingOrderDTO> shippingOrderDTOMap = recentShippingOrderList.stream()
                    .collect(Collectors.toMap(TradeShippingOrderDTO::getTradeOrderNo, Function.identity(), (ov, nv) -> nv));

            Map<String, OCMSOrderVO> orderVOMap = ocmsOrderList.stream()
                    .collect(Collectors.toMap(OnlineBaseOrderVO::getViewOrderId, Function.identity(), (ov, nv) -> ov));

            thirdDeliveryOrderVOS.forEach(orderVO -> {
                TradeShippingOrderDTO tradeShippingOrderDTO = shippingOrderDTOMap.get(orderVO.getChannelOrderId());

                if (Objects.nonNull(tradeShippingOrderDTO) && MccConfigUtil.isDrunkHorseTenant(orderVO.getTenantId()) && MccConfigUtil.isShowGoodsItemListGrayStore(orderVO.getStoreId())) {
                    orderVO.setGoodsItemList(transfer2GoodsItemVO(tradeShippingOrderDTO.getItems()));
                }

                //耗材信息
                OCMSOrderVO ocmsOrderVO = orderVOMap.get(orderVO.getChannelOrderId());

                if (Objects.nonNull(ocmsOrderVO) && MccConfigUtil.isDrunkHorseTenant(orderVO.getTenantId()) && MccConfigUtil.isShowGoodsItemListGrayStore(orderVO.getStoreId())) {
                    orderVO.setNeedWineBottleOpener(
                            parseConsumableMaterialInfo(ocmsOrderVO)
                                    .stream()
                                    .anyMatch(consumable -> {
                                        return MccConfigUtil.getWineBottleOpenerSkuIds().contains(consumable.getSkuId()) && Objects.nonNull(consumable.getCount()) && consumable.getCount() > 0;
                                    }));
                }
            });
        } catch (Exception e) {
            log.error("fillGoodsItemList error", e);
            Cat.logEvent("FILL_GOODS_ITEM", "ERROR");
        }
    }

    private void fillOrderMaltDeliveryPlatModule(List<ThirdDeliveryOrderVO> orderVOList, Map<Long, TDeliveryDetail> deliveryDetailMap, Map<Integer/*承运商code*/, DeliveryChannelDto> channelDtoMap) {
        //校验运单的配送渠道是否合法
        Map<Integer/*承运商code*/, Integer/*配送平台code*/> deliveryChannelMap = deliveryChannelWrapper.tratranslateToChannelIntgerMap(channelDtoMap);
        ArrayListMultimap<Long, Long> dapOrderIdMultiMap = ArrayListMultimap.create();
        for (ThirdDeliveryOrderVO vo : orderVOList) {
            TDeliveryDetail deliveryDetail = deliveryDetailMap.get(vo.getEmpowerOrderId());
            if (deliveryDetail == null) {
                continue;
            }
            if (!deliveryChannelWrapper.checkChannel(deliveryDetail.deliveryChannelCode, AggDeliveryPlatformEnum.DAP_DELIVERY.getCode(), vo.getTenantId(), vo.getStoreId(), deliveryChannelMap)) {
                continue;
            }

            dapOrderIdMultiMap.put(vo.getStoreId(), vo.getEmpowerOrderId());
        }


        //获取三方配送跳转url
        List<Long> orderIdList = Fun.map(orderVOList, ThirdDeliveryOrderVO::getEmpowerOrderId);
        Map<String, String> urlMap = tmsServiceWrapper.batchQueryOrderDeliveryUrl(getTenantId(), getStoreId(), orderIdList,null);


        //填充三方配送信息
        orderVOList.forEach(order -> {

            TDeliveryDetail tDeliveryDetail = deliveryDetailMap.get(order.getEmpowerOrderId());
            if(Objects.isNull(tDeliveryDetail)) {
                return;
            }

            if (deliveryChannelWrapper.checkChannel(tDeliveryDetail.deliveryChannelCode, AggDeliveryPlatformEnum.DAP_DELIVERY.getCode(), order.getTenantId(), order.getStoreId(), deliveryChannelMap)) {
                order.setDeliveryPlatformCode(AggDeliveryPlatformEnum.DAP_DELIVERY.getCode());
                //配送拒单，也不展示链接
                if (order.getRealDistributeStatus() == null || order.getRealDistributeStatus() <= TmsDeliveryStatusDesc.INIT.getCode() ||
                        order.getRealDistributeStatus() == TmsDeliveryStatusDesc.DELIVERY_REJECTED.getCode()) {
                    DeliveryRedirectModuleVo deliveryRedirectModuleVo = new DeliveryRedirectModuleVo();
                    deliveryRedirectModuleVo.setTitle("暂无配送状态");
                    deliveryRedirectModuleVo.setShowButton(false);
                    order.setDeliveryRedirectModule(deliveryRedirectModuleVo);
                    return;
                }
                String url = urlMap.get(order.getEmpowerOrderId().toString());
                DeliveryRedirectModuleVo deliveryRedirectModuleVo = AggDeliveryPlatformEnum.DAP_DELIVERY.fillDeliveryRedirectModule(url,
                        Objects.nonNull(order.getDeliveryExceptionType()) && !Objects.equals(order.getDeliveryExceptionType(), DELIVERY_NO_EXCEPTION));
                order.setDeliveryRedirectModule(deliveryRedirectModuleVo);
                order.setDeliveryPlatformCode(tDeliveryDetail.platformCode);
                order.setDeliveryPlatformDesc(tDeliveryDetail.platformDesc);
            }
        });
    }

    private void fillAssessDeliveryTime(List<ThirdDeliveryOrderVO> orderVOList, Map<Long, TDeliveryDetail> deliveryDetailMap) {
        //校验运单的配送渠道是否合法
        for (ThirdDeliveryOrderVO vo : orderVOList) {
            TDeliveryDetail deliveryDetail = deliveryDetailMap.get(vo.getEmpowerOrderId());
            if (deliveryDetail == null) {
                continue;
            }
            if (Objects.nonNull(deliveryDetail.assessDeliveryTime)) {
                vo.setAssessDeliveryTime(deliveryDetail.assessDeliveryTime);
            }
        }

    }

    private void fillThirdDeliveryOperateItem(List<ThirdDeliveryOrderVO> orderList, Map<Long/*orderId*/, TDeliveryDetail> deliveryDetailMap){
        if(CollectionUtils.isEmpty(orderList)) {
            return;
        }

        Map<String, Boolean> permissions = authThriftWrapper.isHasPermission(ImmutableList.of(AuthCodeEnum.TURN_SELF_DELIVERY.getAuthCode()));
        boolean couldTurnToSelfDelivery = permissions.getOrDefault(AuthCodeEnum.TURN_SELF_DELIVERY.getAuthCode(), Boolean.FALSE);
        if (!couldTurnToSelfDelivery) {
            return;
        }

        //查询门店配送配置 歪马默认都支持自配送 这里不校验

        //校验订单状态
        orderList.forEach(orderVo -> {
            List<Integer> deliveryOperateItems = new ArrayList<>();
            if (checkOrderStatusCouldTurnToMerchantDelivery(orderVo)) {
                deliveryOperateItems.add(DeliveryOperateItemEnum.DELIVERY_TO_SELF.type);
            }
            orderVo.setDeliveryOperateItems(deliveryOperateItems);
    });
    }

    //订单状态是在门店接单后、且订单非已完成/非已取消
    private Boolean checkOrderStatusCouldTurnToMerchantDelivery(ThirdDeliveryOrderVO thirdDeliveryOrderVO) {
        return thirdDeliveryOrderVO.getOrderStatus() > OrderStatusEnum.MERCHANT_CONFIRMED.getValue() &&
                !Objects.equals(thirdDeliveryOrderVO.getOrderStatus(), OrderStatusEnum.COMPLETED.getValue()) &&
                !Objects.equals(thirdDeliveryOrderVO.getOrderStatus(), OrderStatusEnum.CANCELED.getValue());
    }

    private List<ThirdDeliveryOrderVO> buildOrderVOListFromDeliveryOrder(List<TDeliveryDetail> tDeliveryDetails,
                                                                     List<OCMSOrderVO> ocmsOrderVOS,
                                                                     List<OrderRevenueDetailResponse> revenueDetails) {
        Map<String, OCMSOrderVO> orderVOMap = ocmsOrderVOS.stream()
                .collect(Collectors.toMap(OnlineBaseOrderVO::getViewOrderId, Function.identity()));

        Map<String, OrderRevenueDetailResponse> revenueDetailMap = revenueDetails.stream()
                .collect(Collectors.toMap(OrderRevenueDetailResponse::getOrderViewId, Function.identity()));

        Map<Long/*orderId*/, TDeliveryDetail> deliveryDetailMap = tDeliveryDetails.stream()
                .collect(Collectors.toMap(deliveryOrder -> deliveryOrder.bizOrderId, Function.identity()));

        List<OrderVO> orderVOS = tDeliveryDetails.stream().
                filter(deliveryOrder -> orderVOMap.containsKey(deliveryOrder.channelOrderId))
                .map(deliveryOrder -> ocmsOrderServiceWrapper.buildOrderVO(orderVOMap.get(deliveryOrder.channelOrderId), revenueDetailMap.get(deliveryOrder.channelOrderId), null))
                .collect(Collectors.toList());
        //NOTE: 有一种情况是，转单时青云发配送直接就失败了，不会同步给订单变更了。这个时候还是应该要有“转自配按钮”
        //配送方式以运单为准
        Map<String, TDeliveryDetail> channelOrderIdDeliveryMap = tDeliveryDetails.stream().collect(Collectors.toMap(
                it -> it.channelOrderId,
                Function.identity(),
                (older, newer) -> newer
        ));
        for (OrderVO orderVO : orderVOS) {
            if (channelOrderIdDeliveryMap.containsKey(orderVO.getChannelOrderId())) {
                orderVO.setDeliveryChannelId(channelOrderIdDeliveryMap.get(orderVO.getChannelOrderId()).deliveryChannelCode);
            }
        }

        //设置订单可操作项
        ocmsOrderServiceWrapper.setCouldOperateItems(getTenantId(), orderVOS, null, Lists.newArrayList(OrderCouldOperateItem.ACCEPT_ORDER), null);

        //OrderVO --> ThirdDeliveryOrderVO
        List<ThirdDeliveryOrderVO> thirdDeliveryOrderVOS = orderVOS.stream().map(ThirdDeliveryOrderVO::createFromOrderVO)
                .collect(Collectors.toList());

        //4.设置可操作项和三方配送信息
        if (CollectionUtils.isNotEmpty(thirdDeliveryOrderVOS)) {
            Set<Integer> channelCodes = tDeliveryDetails.stream().map(e-> e.deliveryChannelCode).collect(Collectors.toSet());
            Map<Integer/*承运商code*/, DeliveryChannelDto> deliveryChannelDtoMap = deliveryChannelWrapper.getDeliveryChannelDtoMap(channelCodes);
            appendDeliveryInfoService.mergeRealTimeDeliveryInfo4DH(thirdDeliveryOrderVOS, deliveryDetailMap, deliveryChannelDtoMap);
            fillOrderMaltDeliveryPlatModule(thirdDeliveryOrderVOS, deliveryDetailMap, deliveryChannelDtoMap);
            //fillThirdDeliveryOperateItem(thirdDeliveryOrderVOS, deliveryDetailMap);
            fillDeliveryTimeoutInfo(thirdDeliveryOrderVOS, orderVOMap, deliveryDetailMap);
            fillAssessDeliveryTime(thirdDeliveryOrderVOS, deliveryDetailMap);
        }

        return thirdDeliveryOrderVOS;
    }


    private List<ThirdDeliveryOrderVO> buildNewOrderVOListFromPickOrder(List<ViewIdCondition> viewIdConditionList,
                                                                     List<OCMSOrderVO> ocmsOrderVOS,
                                                                     List<OrderRevenueDetailResponse> revenueDetails,
                                                                     Map<String, AbnOrderDTO> orderAbnMap,
                                                                     List<TDeliveryDetail> tDeliveryDetails) {
        Map<String, OCMSOrderVO> orderVOMap = ocmsOrderVOS.stream()
                .collect(Collectors.toMap(OnlineBaseOrderVO::getViewOrderId, Function.identity()));

        Map<String, OrderRevenueDetailResponse> revenueDetailMap = revenueDetails.stream()
                .collect(Collectors.toMap(OrderRevenueDetailResponse::getOrderViewId, Function.identity()));

        Map<Long/*orderId*/, TDeliveryDetail> deliveryDetailMap = tDeliveryDetails.stream()
                .collect(Collectors.toMap(deliveryOrder -> deliveryOrder.bizOrderId, Function.identity()));

        List<ThirdDeliveryOrderVO> orderVOS = viewIdConditionList.stream().
                filter(viewIdCondition -> orderVOMap.containsKey(viewIdCondition.getViewOrderId()))
                .map(viewIdCondition -> ocmsOrderServiceWrapper.buildOrderVO(orderVOMap.get(viewIdCondition.getViewOrderId()),
                        revenueDetailMap.get(viewIdCondition.getViewOrderId()), null))
                .map(ThirdDeliveryOrderVO::createFromOrderVO)
                .collect(Collectors.toList());

        fillDeliveryTimeoutInfo(orderVOS, orderVOMap, deliveryDetailMap);

        fillHasLackInfo(orderVOS, orderAbnMap);
        return orderVOS;
    }

    private void fillHasLackInfo(List<ThirdDeliveryOrderVO> orderVOS, Map<String, AbnOrderDTO> orderAbnMap) {
        Optional.ofNullable(orderVOS).orElse(Lists.newArrayList())
                .forEach(
                        thirdDeliveryOrderVO -> {
                            thirdDeliveryOrderVO.setHasLackGoods(Objects.nonNull(orderAbnMap.get(thirdDeliveryOrderVO.getChannelOrderId())));
                        }
                );
    }


    private void fillDeliveryTimeoutInfo(List<ThirdDeliveryOrderVO> orderVOS, Map<String, OCMSOrderVO> orderVOMap, Map<Long/*orderId*/, TDeliveryDetail> deliveryDetailMap) {
        orderVOS.forEach(orderVO -> {
            OCMSOrderVO ocmsOrderVO = orderVOMap.get(orderVO.getChannelOrderId());
            if (ocmsOrderVO == null) {
                return;
            }
            OCMSDeliveryInfoVO ocmsDeliveryInfoVO = ocmsOrderVO.getOcmsDeliveryInfoVO();

            if (Objects.isNull(ocmsDeliveryInfoVO)) {
                log.info("配送信息为空");
                return;
            }

            TDeliveryDetail tDeliveryDetail = deliveryDetailMap.get(orderVO.getEmpowerOrderId());
            if (Objects.isNull(tDeliveryDetail)) {
                log.info("配送详为空");
                return;
            }

            DeliveryOrderType deliveryOrderType = ocmsOrderVO.getIsBooking() == 1 ?
                    DeliveryOrderType.DELIVERY_BY_BOOK_TIME : DeliveryOrderType.DELIVERY_RIGHT_NOW;
            Long arrivalEndTime = ocmsDeliveryInfoVO.getArrivalEndTime();

            // 配送超时考核信息 start
            long evaluateArriveDeadline;
            if (Objects.nonNull(tDeliveryDetail.assessDeliveryTime)) {
                //新逻辑，考核时间与eta脱钩
                evaluateArriveDeadline = tDeliveryDetail.assessDeliveryTime;
            } else {
                //原逻辑
                if (deliveryOrderType.equals(DeliveryOrderType.DELIVERY_BY_BOOK_TIME)) {
                    // 预订单：考核时间=预计送达时间+5分钟
                    if (MccConfigUtil.isNewPreOrderAssessGrayStore(orderVO.getStoreId())) {
                        evaluateArriveDeadline = arrivalEndTime + MccConfigUtil.preOrderAssessTimePlusMills();
                    } else {
                        evaluateArriveDeadline = arrivalEndTime + (5 * 60 * 1000);
                    }
                } else {
                    // 实时单：考核时间=支付时间+25分钟
                    evaluateArriveDeadline = ocmsOrderVO.getPayTime() + (25 * 60 * 1000);
                }
            }

            orderVO.setEvaluateArriveDeadline(evaluateArriveDeadline);

            if (ocmsDeliveryInfoVO.getDistributeStatus() == null) {
                return;
            }

            if (DistributeStatusEnum.RIDER_DELIVERED.getValue() == ocmsDeliveryInfoVO.getDistributeStatus()) {
                orderVO.setEvaluateArriveLeftTime(0L);
                Optional<Long> deliveryDoneTimeOpt = ocmsOrderVO.getDeliveryStatusLogList().stream()
                        .filter(log -> Objects.equals(log.getTargetStatus(), DeliveryStatusEnum.DELIVERY_DONE.getCode()))
                        .map(DeliveryStatusLogVo::getOperateTime)
                        .findFirst();
                if (deliveryDoneTimeOpt.isPresent() && deliveryDoneTimeOpt.get() > evaluateArriveDeadline) {
                    // 已送达，已超时
                    orderVO.setEvaluateArriveTimeout(deliveryDoneTimeOpt.get() - evaluateArriveDeadline);
                } else {
                    // 已送达，未超时
                    orderVO.setEvaluateArriveTimeout(0L);
                }
            } else {
                long tsNow = System.currentTimeMillis();
                if (tsNow > evaluateArriveDeadline) {
                    // 配送中，已经超时
                    orderVO.setEvaluateArriveLeftTime(0L);
                    orderVO.setEvaluateArriveTimeout(tsNow - evaluateArriveDeadline);
                } else {
                    // 配送中，还未超时
                    orderVO.setEvaluateArriveLeftTime(evaluateArriveDeadline - tsNow);
                    orderVO.setEvaluateArriveTimeout(0L);
                }
            }
        });


        // 配送超时考核信息 end
    }


    //次要信息不影响流程
    private List<OrderRevenueDetailResponse> queryOrderRevenueDetail(List<OCMSOrderVO> ocmsOrderVOS) {
        try{
            Map<String, Boolean> permissions = authThriftWrapper.isHasPermission(ImmutableList.of(AuthCodeEnum.SHOW_SALE_PRICE.getAuthCode()));
            boolean showSalePrice = permissions.getOrDefault(AuthCodeEnum.SHOW_SALE_PRICE.getAuthCode(), Boolean.FALSE);

            List<Pair<Integer, String>> bizType2ViewOrderIdPairs = ocmsOrderVOS.stream()
                    .map(orderVO -> Pair.of(orderVO.getOrderBizType(), orderVO.getViewOrderId()))
                    .collect(Collectors.toList());

            return showSalePrice ? ocmsOrderServiceWrapper.getOrderListRevenueDetailByViewOrderIds(
                    getTenantId(), bizType2ViewOrderIdPairs) : Collections.emptyList();
        } catch (Exception e) {
            log.warn("查营收数据失败");
            return Collections.emptyList();
        }

    }


    private static Long getTenantId() {
        if (ApiMethodParamThreadLocal.getIdentityInfo() == null || ApiMethodParamThreadLocal.getIdentityInfo().getUser() == null) {
            throw new BizException("未获取到用户信息，请先登录");
        }

        return ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
    }

    private static Long getStoreId() {
        if (ApiMethodParamThreadLocal.getIdentityInfo() == null) {
            throw new BizException("未获取到用户信息，请先登录");
        }

        return ApiMethodParamThreadLocal.getIdentityInfo().getStoreId();
    }

    private static Long getAccountId() {
        if (ApiMethodParamThreadLocal.getIdentityInfo() == null) {
            throw new BizException("未获取到用户信息，请先登录");
        }

        return ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId();
    }

    private static String getOperatorName() {
        if (ApiMethodParamThreadLocal.getIdentityInfo() == null) {
            throw new BizException("未获取到用户信息，请先登录");
        }

        return ApiMethodParamThreadLocal.getIdentityInfo().getUser().getOperatorName();
    }

}
