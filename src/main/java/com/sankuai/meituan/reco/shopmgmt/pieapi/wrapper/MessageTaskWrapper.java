package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Maps;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.messagetask.MessageTaskQueryVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.saas.task.dto.simple.SimpleTaskTemplateDTO;
import com.sankuai.meituan.shangou.saas.task.request.simple.SimpleTaskResultQueryRequest;
import com.sankuai.meituan.shangou.saas.task.request.simple.SimpleTaskTemplateGetRequest;
import com.sankuai.meituan.shangou.saas.task.response.simple.SimpleTaskResultResponse;
import com.sankuai.meituan.shangou.saas.task.response.simple.SimpleTaskTemplateResponse;
import com.sankuai.meituan.shangou.saas.task.service.SimpleTaskThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Rhino
@Slf4j
public class MessageTaskWrapper {

    @Autowired
    private SimpleTaskThriftService simpleTaskThriftService;


    @Autowired
    private AuthThriftWrapper authThriftWrapper;


    public CommonResponse<List<MessageTaskQueryVO>> getUserTaskTemplate(User user, Long storeId) {
        try {
            SimpleTaskTemplateGetRequest request = new SimpleTaskTemplateGetRequest();
            request.setTenantId(user.getTenantId());
            SimpleTaskTemplateResponse resp = simpleTaskThriftService.getTenantTaskTemplateList(request);
            if (resp.getCode() != 0) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), resp.getMsg());
            }
            if (CollectionUtils.isEmpty(resp.getTemplateList())) {
                return CommonResponse.success(null);
            }
            List<String> currentAccountAllPermissionCodes = authThriftWrapper.queryAuthorizedCodes(buildAuthCode(resp.getTemplateList()));
            Map<String, SimpleTaskTemplateDTO> needCheckTask = Maps.newLinkedHashMap();
            for (SimpleTaskTemplateDTO simpleTaskTemplateDTO : resp.getTemplateList()) {
                if (CollectionUtils.isEmpty(simpleTaskTemplateDTO.getAuthCodeList())) {
                    needCheckTask.put(simpleTaskTemplateDTO.getTaskBizCode(), simpleTaskTemplateDTO);
                    continue;
                }
                for (String authCode : simpleTaskTemplateDTO.getAuthCodeList()) {
                    if (currentAccountAllPermissionCodes.contains(authCode)) {
                        needCheckTask.put(simpleTaskTemplateDTO.getTaskBizCode(), simpleTaskTemplateDTO);
                        continue;
                    }
                }
            }
            if (MapUtils.isEmpty(needCheckTask)) {
                return CommonResponse.success(null);
            }
            SimpleTaskResultQueryRequest resultQueryRequest = new SimpleTaskResultQueryRequest();
            resultQueryRequest.setTenantId(user.getTenantId());
            resultQueryRequest.setPoiId(storeId);
            resultQueryRequest.setBizCodeList(Lists.newArrayList(needCheckTask.keySet()));
            SimpleTaskResultResponse simpleTaskResultResponse = simpleTaskThriftService.queryTenantPoiTaskInfo(resultQueryRequest);
            if (simpleTaskResultResponse.getCode() != 0) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), resp.getMsg());
            }
            List<MessageTaskQueryVO> respData = Lists.newArrayList();
            Map<String, Integer> bizCodeToNum = simpleTaskResultResponse.getBizCodeToNum();
            for (Map.Entry<String, SimpleTaskTemplateDTO> bizCodeTemplate : needCheckTask.entrySet()) {
                if (bizCodeToNum.get(bizCodeTemplate.getKey()) != null && bizCodeToNum.get(bizCodeTemplate.getKey()) > 0) {
                    respData.add(new MessageTaskQueryVO(bizCodeTemplate.getValue(), bizCodeToNum.get(bizCodeTemplate.getKey())));
                }
            }
            return CommonResponse.success(respData);
        } catch (TException e) {
            log.error("调用消息中心查询租户任务模板失败", e);
            return CommonResponse.fail2(ResultCode.FAIL);
        }
    }

    private List<String> buildAuthCode(List<SimpleTaskTemplateDTO> templateList) {
        if (CollectionUtils.isEmpty(templateList)) {
            return Collections.emptyList();
        }

        return templateList.stream()
                .map(SimpleTaskTemplateDTO::getAuthCodeList)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }
}
