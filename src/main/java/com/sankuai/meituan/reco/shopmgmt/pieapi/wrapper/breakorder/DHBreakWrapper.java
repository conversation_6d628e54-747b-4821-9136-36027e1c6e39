package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.breakorder;

import com.google.common.collect.Lists;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.store.wms.thrift.breakorder.BreakOrderThriftService;
import com.sankuai.meituan.reco.store.wms.thrift.breakorder.request.BreakOrderCountQueryRequest;
import com.sankuai.meituan.reco.store.wms.thrift.breakorder.response.BreakOrderCountQueryResponse;
import com.sankuai.shangou.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * author xujunfeng02
 * dateTime 2023/2/23 4:04 PM
 * description 歪马报损wrapper
 */
@Component
@Slf4j
public class DHBreakWrapper {

    // 2-待审核, 15-待提交, 20-驳回
    private static final List<Integer> UNFINISHED_STATUS = Lists.newArrayList(2, 15, 20);

    @Resource
    private BreakOrderThriftService thriftService;

    @MethodLog(logRequest = true, logResponse = true)
    public int count(PendingTaskParam param) {

        try {

            BreakOrderCountQueryRequest request = new BreakOrderCountQueryRequest();
            request.setMerchantId(param.getTenantId());
            request.setWarehouseId(param.getEntityId());
            request.setStatus(UNFINISHED_STATUS);
            Result<BreakOrderCountQueryResponse> result = thriftService.count(request);
            result.checkBizException();
            return result.getModule().getCount().intValue();
        } catch (Exception e) {

            log.error("DHBreakWrapper count error", e);
            return 0;
        }
    }
}