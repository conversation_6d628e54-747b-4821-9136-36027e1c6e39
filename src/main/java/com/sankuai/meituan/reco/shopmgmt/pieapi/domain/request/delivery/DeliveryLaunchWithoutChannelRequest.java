package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery;

import javax.validation.constraints.NotNull;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 发送黑盒渠道三方配送请求体.
 *
 * <AUTHOR>
 * @since 2021/4/19 19:44
 */
@TypeDoc(
        description = "发送黑盒渠道三方配送请求体"
)
@ApiModel("发送黑盒渠道三方配送请求体")
@Data
public class DeliveryLaunchWithoutChannelRequest {

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店id", required = true)
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "赋能订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "赋能订单号", required = true)
    @NotNull
    private Long orderId;
}
