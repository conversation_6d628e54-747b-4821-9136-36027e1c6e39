package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.regionselect;

import com.dianping.lion.common.util.JsonUtils;
import com.meituan.linz.boot.exception.BusinessException;
import com.meituan.linz.boot.util.Assert;
import com.meituan.linz.boot.util.Bssert;
import com.meituan.linz.boot.util.Fun;
import com.meituan.shangou.saas.tenant.thrift.DepartmentThriftService;
import com.meituan.shangou.saas.tenant.thrift.EmployeeThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.StatusCodeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.department.DepartmentDto;
import com.meituan.shangou.saas.tenant.thrift.dto.department.response.DepartmentListResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.employee.EmployeeDepDto;
import com.meituan.shangou.saas.tenant.thrift.dto.employee.response.EmployDepInfoResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.employee.response.EmployDepMapResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.employee.response.EmployListResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.regionselect.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.regionselect.rsm.FdcAddressingTaskQueryListRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.regionselect.rsm.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.FdcAddressingThriftService;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.LoiIndicatorThriftService;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.dto.RespStatus;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.dto.geobase.GeoCoordinateDto;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.dto.indicator.HeatmapPointDto;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.dto.indicator.PopulationDensityDto;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.dto.rsm.FdcAddressingTaskDto;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.dto.rsm.FdcAddressingTaskWithSourceDto;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.dto.rsm.FdcRentalSourceDto;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.dto.rsm.FormDefineDto;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.enums.FdcAddressingTaskStatusEnum;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.enums.FdcRentalSourceStatusEnum;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.enums.HeatmapTypeEnum;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.enums.event.FdcAddressingTaskEvent;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.enums.event.FdcRentalSourceEvent;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.request.*;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.response.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static org.apache.commons.lang.StringUtils.defaultIfBlank;

/**
 * <AUTHOR>
 * @since 2022/12/6 16:16
 **/
@Service
@Slf4j
public class FdcAddressingWrapper {
    @Resource
    private FdcAddressingThriftService fdcAddressingThriftService;
    @Resource
    private EmployeeThriftService employThriftService;
    @Resource
    private DepartmentThriftService departmentThriftService;
    @Resource
    private LoiIndicatorThriftService loiIndicatorThriftService;

    // 运营人员可操作事件
    public static final Set<Integer> OPERATE_OPTIONAL_ADDRESSING_TASK_EVENTS =
            new HashSet<>(Fun.map(Arrays.asList(FdcAddressingTaskEvent.ABANDON_TASK),
                    FdcAddressingTaskEvent::getCode));

    public static final Set<Integer> OPERATE_OPTIONAL_RENTAL_SOURCE_EVENTS =
            new HashSet<>(Fun.map(Arrays.asList(FdcRentalSourceEvent.EDIT_RENTAL_SOURCE,
                            FdcRentalSourceEvent.PRELIMINARY_REVIEW_PASSED,FdcRentalSourceEvent.OFFLINE_SURVEY_PASSED,
                            FdcRentalSourceEvent.SIGN_RENTAL_SOURCE, FdcRentalSourceEvent.ABANDON_SOURCE),
                    FdcRentalSourceEvent::getCode));

    // 寻仓人员可操作事件
    public static final Set<Integer> SEARCHER_OPTIONAL_ADDRESSING_TASK_EVENTS =
            new HashSet<>(Fun.map(Arrays.asList(FdcAddressingTaskEvent.ACCEPT_TASK,
                            FdcAddressingTaskEvent.TRANSFER_TASK, FdcAddressingTaskEvent.CREATE_RENTAL_SOURCE),
                    FdcAddressingTaskEvent::getCode));

    public static final Set<Integer> SEARCHER_OPTIONAL_RENTAL_SOURCE_EVENTS =
            new HashSet<>(Fun.map(Arrays.asList(FdcRentalSourceEvent.SAVE_DRAFT,
                            FdcRentalSourceEvent.SUBMIT_RENTAL_SOURCE, FdcRentalSourceEvent.EDIT_RENTAL_SOURCE),
                    FdcRentalSourceEvent::getCode));

    public void preliminaryReviewPass(Long sourceId) {
        FdcRentalSourcePreliminaryReviewPassRequest request =
                new FdcRentalSourcePreliminaryReviewPassRequest(sourceId, getOperatorFromThreadLocal());

        RespStatus resp = null;

        try {
            resp = fdcAddressingThriftService.preliminaryReviewPassRentalSource(request);
        } catch (Exception e) {
            log.error("invoke fdcAddressingThriftService.preliminaryReviewPassRentalSource error, req:{}", request, e);
            throw new BusinessException("仓源状态扭转失败, 请稍后重试");
        }

        checkResp(resp);
    }

    public void offlineSurveyPassRentalSource(RentalSourceOfflineSurveyRequest req) {

        FdcRentalSourceOfflineSurveyPassRequest request = new FdcRentalSourceOfflineSurveyPassRequest(req.getSourceId(), req.getConsensusStatus(),
                getOperatorFromThreadLocal());

        RespStatus resp = null;

        try {
            resp = fdcAddressingThriftService.offlineSurveyPassRentalSource(request);
        } catch (Exception e) {
            log.error("invoke fdcAddressingThriftService.offlineSurveyPassRentalSource error, req:{}", request, e);
            throw new BusinessException("仓源状态扭转失败, 请稍后重试");
        }

        checkResp(resp);
    }

    public void signRentalSource(Long sourceId) {

        FdcRentalSourceSignRequest request = new FdcRentalSourceSignRequest(sourceId, getOperatorFromThreadLocal());

        RespStatus resp = null;

        try {
            resp = fdcAddressingThriftService.signRentalSource(request);
        } catch (Exception e) {
            log.error("invoke fdcAddressingThriftService.signRentalSource error, req:{}", request, e);
            throw new BusinessException("仓源状态扭转失败, 请稍后重试");
        }

        checkResp(resp);
    }

    public void invalidRentalSource(Long sourceId) {
        FdcRentalSourceInvalidRequest request = new FdcRentalSourceInvalidRequest(sourceId, getOperatorFromThreadLocal());

        RespStatus resp = null;

        try {
            resp = fdcAddressingThriftService.invalidRentalSource(request);
        } catch (Exception e) {
            log.error("invoke fdcAddressingThriftService.invalidRentalSource error, req:{}", request, e);
            throw new BusinessException("仓源状态扭转失败, 请稍后重试");
        }

        checkResp(resp);
    }

    public void invalidAddressingTask(Long sourceId) {
        FdcAddressingInvalidRequest request = new FdcAddressingInvalidRequest(sourceId, getOperatorFromThreadLocal());

        RespStatus resp = null;

        try {
            resp = fdcAddressingThriftService.invalidFdcAddressingTask(request);
        } catch (Exception e) {
            log.error("invoke fdcAddressingThriftService.invalidFdcAddressingTask error, req:{}", request, e);
            throw new BusinessException("作废寻仓任务失败, 请稍后重试");
        }

        checkResp(resp);
    }

    public void acceptAddressingTask(Long taskId) {
        FdcAddressingTaskAcceptRequest request = new FdcAddressingTaskAcceptRequest();
        request.setTaskId(taskId);
        long employeeId = getCurrentEmployeeId();
        request.setOperator(getOperatorFromThreadLocal());
        request.setOwnerEmployeeId(employeeId);

        RespStatus resp = null;
        try {
            resp = fdcAddressingThriftService.acceptFdcAddressingTask(request);
        } catch (Exception e) {
            log.error("invoke fdcAddressingThriftService.acceptFdcAddressingTask error, req:{}", request, e);
            throw new BusinessException("认领寻仓任务失败, 请稍后重试");
        }

        checkResp(resp);
    }

    public void transferAddressingTask(AddressingTaskTransferRequest req) {
        FdcAddressingTaskTransferRequest request = new FdcAddressingTaskTransferRequest();
        request.setTaskId(req.getTaskId());
        request.setOwnerEmployeeId(req.getOwnerEmployeeId());
        request.setOperator(getOperatorFromThreadLocal());

        RespStatus resp = null;
        try {
            resp = fdcAddressingThriftService.transferFdcAddressingTask(request);
        } catch (Exception e) {
            log.error("invoke fdcAddressingThriftService.transferFdcAddressingTask error, req:{}", request, e);
            throw new BusinessException("转派寻仓任务失败, 请稍后重试");
        }

        checkResp(resp);
    }

    public FdcRentalSourceVO upsertRentalSourceDraft(RentalSourceDraftUpsertRequest req) {
        FdcRentalSourceSaveDraftRequest request = new FdcRentalSourceSaveDraftRequest();
        request.setOperator(getOperatorFromThreadLocal());
        request.setTenantId(MccConfigUtil.getRegionSelectTenantId());
        request.setTaskId(req.getFdcAddressingTaskId());
        request.setCoordinate(req.getCoordinate());
        request.setSourceName(req.getSourceName());
        request.setSourceType(req.getSourceType());
        request.setFormItems(req.getFormItems());
        request.setSourceId(req.getSourceId());
        request.setFormVersion(req.getFormVersion());
        try {
            FdcCreateRentalSourceResp resp = fdcAddressingThriftService.saveFdcRentalSourceDraft(request);
            checkResp(resp.getStatus());
            FdcRentalSourceVO vo = new FdcRentalSourceVO();
            vo.setId(resp.getSourceId());
            return vo;
        } catch (TException e) {
            log.error("invoke fdcAddressingThriftService.saveFdcRentalSourceDraft error, req:{}", request, e);
            throw new BusinessException("仓源保存草稿失败, 请稍后重试");
        }
    }

    public FdcRentalSourceVO submitRentalSource(RentalSourceSubmitRequest req) {
        // 提交仓源
        FdcRentalSourceSubmitRequest request = new FdcRentalSourceSubmitRequest();
        request.setOperator(getOperatorFromThreadLocal());
        request.setTenantId(MccConfigUtil.getRegionSelectTenantId());
        request.setTaskId(req.getFdcAddressingTaskId());
        request.setCoordinate(req.getCoordinate());
        request.setSourceName(req.getSourceName());
        request.setSourceType(req.getSourceType());
        request.setFormItems(req.getFormItems());
        request.setSourceId(req.getSourceId());
        request.setFormVersion(req.getFormVersion());
        try {
            FdcCreateRentalSourceResp resp = fdcAddressingThriftService.submitFdcRentalSource(request);
            checkResp(resp.getStatus());
            FdcRentalSourceVO vo = new FdcRentalSourceVO();
            vo.setId(resp.getSourceId());
            return vo;
        } catch (TException e) {
            log.error("invoke fdcAddressingThriftService.submitFdcRentalSource error, req:{}", request, e);
            throw new BusinessException("仓源提交失败, 请稍后重试");
        }
    }

    public void updateRentalSource(RentalSourceUpdateRequest req) {
        // 提交仓源
        UpdateFdcRentalSourceRequest request = new UpdateFdcRentalSourceRequest();
        request.setOperatorEmployeeId(getCurrentEmployeeId());
        request.setOperatorTenantId(getCurrentTenantId());
        request.setCoordinate(req.getCoordinate());
        request.setSourceName(req.getSourceName());
        request.setFormItems(req.getFormItems());
        request.setSourceId(req.getSourceId());
        request.setFormVersion(req.getFormVersion());
        try {
            RespStatus resp = fdcAddressingThriftService.updateFdcRentalSource(request);
            checkResp(resp);
        } catch (TException e) {
            log.error("invoke fdcAddressingThriftService.updateFdcRentalSource error, req:{}", request, e);
            throw new BusinessException("仓源编辑失败, 请稍后重试");
        }
    }

    public List<FormDefineDto> queryLatestFormDefine() {
        try {
            return fdcAddressingThriftService.queryLatestRentalSourceFormDefine(MccConfigUtil.getRegionSelectTenantId());
        } catch (TException e) {
            log.error("invoke fdcAddressingThriftService.queryLatestRentalSourceFormDefine error", e);
            throw new BusinessException("仓源最新版本表单定义获取失败, 请稍后重试");
        }
    }

    public List<PopulationDensityDto> queryPopulationDensity(PopulationDensityRequest req){
        PopulationDensityQueryRequest request = new PopulationDensityQueryRequest();
        request.setTenantCode(Long.toString(MccConfigUtil.getRegionSelectTenantId()));
        request.setCityId(req.getCityId());
        request.setScaling(req.getScaling());

        GeoCoordinateDto southWest = parseCoordinate(req.getSouthWestPoint());
        GeoCoordinateDto northEast = parseCoordinate(req.getNorthEastPoint());
        request.setMaxLng(northEast.getLongitude());
        request.setMinLng(southWest.getLongitude());
        request.setMaxLat(northEast.getLatitude());
        request.setMinLat(southWest.getLatitude());

        try {
            PopulationDensityListResp resp = loiIndicatorThriftService.queryPopulationDensityInCity(request);
            log.info("invoke PopulationDensityInCity, request: {}, resp: {}", JsonUtils.toJson(request),
                    JsonUtils.toJson(resp));
            checkResp(resp.getStatus());
            return resp.getPopulationDensityList();
        } catch (TException e) {
            log.error("invoke loiIndicatorThriftService.PopulationDensityInCity fail", e);
            throw new BusinessException("获取热力图失败，请稍后重试");
        }
    }

    private GeoCoordinateDto parseCoordinate(String coordinateStr) {
        Assert.throwIfBlank(coordinateStr, "坐标点不得为空");
        String[] coordinateArr = coordinateStr.split(",");
        Assert.throwIfTrue(coordinateArr.length != 2, "坐标点格式应该为逗号分隔的经度纬度");
        String lngStr = coordinateArr[0];
        String latStr = coordinateArr[1];
        Assert.throwIfTrue(!NumberUtils.isParsable(lngStr), "经度格式错误");
        Assert.throwIfTrue(!NumberUtils.isParsable(latStr), "纬度格式错误");
        GeoCoordinateDto geoCoordinate = new GeoCoordinateDto(Double.valueOf(lngStr), Double.valueOf(latStr));
        geoCoordinate.validate();
        return geoCoordinate;
    }

    private void checkResp(RespStatus status) {
        Bssert.throwIfNull(status, "操作失败，请稍后重试");
        Bssert.throwIfNull(status.getCode(), "操作失败，请稍后重试");
        Bssert.throwIfTrue(status.getCode() != 0, defaultIfBlank(status.getMsg(), "内部错误，请稍后重试"));
    }

    private long getCurrentEmployeeId() {
        if (Objects.isNull(ApiMethodParamThreadLocal.getIdentityInfo()) || Objects.isNull(ApiMethodParamThreadLocal.getIdentityInfo().getUser())) {
            throw new BusinessException("获取用户信息失败，请先登录");
        }
        return ApiMethodParamThreadLocal.getIdentityInfo().getUser().getEmployeeId();
    }

    private long getCurrentTenantId() {
        if (Objects.isNull(ApiMethodParamThreadLocal.getIdentityInfo()) || Objects.isNull(ApiMethodParamThreadLocal.getIdentityInfo().getUser())) {
            throw new BusinessException("获取用户信息失败，请先登录");
        }
        return ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
    }

    /**
     * 查询寻仓任务状态统计信息
     *
     * @return
     */
    public CommonResponse<FdcAddressingTaskStatVO> queryStatusStatisticsForOp() {
        FdcQueryAddressingTaskStatusStatRequest request = new FdcQueryAddressingTaskStatusStatRequest();
        request.setTenantId(MccConfigUtil.getRegionSelectTenantId());


        FdcQueryAddressingTaskExpiredRequest requestExpired = new FdcQueryAddressingTaskExpiredRequest();
        requestExpired.setTenantId(MccConfigUtil.getRegionSelectTenantId());

        CommonResponse<FdcAddressingTaskStatVO> response = null;
        try {
            FdcQueryAddressingTaskStatusStatResp resp =
                    fdcAddressingThriftService.queryAddressingTaskStatusStat(request);

            FdcQueryAddressingTaskStatusStatResp respExpired =
                    fdcAddressingThriftService.queryAddressingTaskExpiredStat(requestExpired);

            if (Objects.isNull(resp) || Objects.isNull(resp.getStatus())
                    || Objects.isNull(respExpired) || Objects.isNull(respExpired.getStatus())) {
                response = CommonResponse.fail(ResultCode.FAIL.getCode(), "查询状态统计数据失败");
            } else if (!RespStatus.SUCCESS_CODE.equals(resp.getStatus().getCode())
                    || !RespStatus.SUCCESS_CODE.equals(respExpired.getStatus().getCode())) {
                response = CommonResponse.fail(ResultCode.FAIL.getCode(), resp.getStatus().getMsg());
            } else {
                FdcAddressingTaskStatVO vo = new FdcAddressingTaskStatVO();
                vo.setUnassignedCount(resp.getStatCountMap().getOrDefault(FdcAddressingTaskStatusEnum.UNASSIGNED.getCode(), 0));
                vo.setInProgressCount(resp.getStatCountMap().getOrDefault(FdcAddressingTaskStatusEnum.IN_PROGRESS.getCode(), 0));
                vo.setPendingCount(resp.getStatCountMap().getOrDefault(FdcAddressingTaskStatusEnum.PENDING.getCode(),
                        0));
                vo.setFinishedCount(resp.getStatCountMap().getOrDefault(FdcAddressingTaskStatusEnum.DONE.getCode(), 0));
                vo.setRentalSourceWaitingForSignCount(resp.getStatCountMap().getOrDefault(FdcAddressingTaskStatusEnum.RENTAL_SOURCE_WAITING_FOR_SIGN.getCode(), 0));

                Map<Integer, Integer> statExpiredMap = respExpired.getStatCountMap();
                vo.setUnassignedExpiredCount(statExpiredMap.getOrDefault(FdcAddressingTaskStatusEnum.UNASSIGNED.getCode(), 0));
                vo.setInProgressExpiredCount(statExpiredMap.getOrDefault(FdcAddressingTaskStatusEnum.IN_PROGRESS.getCode(), 0));
                vo.setPendingExpiredCount(statExpiredMap.getOrDefault(FdcAddressingTaskStatusEnum.PENDING.getCode(), 0));
                vo.setRentalSourceWaitingForSignExpiredCount(statExpiredMap.getOrDefault(FdcAddressingTaskStatusEnum.RENTAL_SOURCE_WAITING_FOR_SIGN.getCode(), 0));
                response = CommonResponse.success(vo);
            }
        } catch (Exception exp) {
            log.error(exp.getMessage(), exp);
            response = CommonResponse.fail(ResultCode.FAIL.getCode(), "查询状态统计出错");
        }
        return response;
    }

    /**
     * 查询寻仓任务状态统计信息
     *
     * @return
     */
    public CommonResponse<FdcAddressingTaskStatVO> queryStatusStatisticsForFp() {
        long tenantId = MccConfigUtil.getRegionSelectTenantId();
        long employeeId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getEmployeeId();
        List<Long> empOrgIds = this.queryOrgIdsForAccount(employeeId);
        if (CollectionUtils.isEmpty(empOrgIds)) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "未找到员工组织信息");
        }

        CommonResponse<FdcAddressingTaskStatVO> response;
        try {
            // 逐个状态查询
            FdcAddressingTaskStatVO statVO = new FdcAddressingTaskStatVO();
            // 待分配
            FdcQueryAddressingTaskOneStatusCountRequest request = new FdcQueryAddressingTaskOneStatusCountRequest();
            request.setTenantId(tenantId);
            request.setOwnerOrgIds(empOrgIds);
            request.setTaskStatus(FdcAddressingTaskStatusEnum.UNASSIGNED.getCode());
            FdcQueryCountCommonResp resp = fdcAddressingThriftService.queryAddressingTaskOneStatusCount(request);
            log.info("queryUnsignedCount:{},{}", request, resp);
            if (resp != null && resp.getStatus().getCode().equals(RespStatus.SUCCESS_CODE)) {
                statVO.setUnassignedCount(resp.getTotalCount().intValue());
            }
            // 进行中
            request.setTenantId(tenantId);
            request.setOwnerOrgIds(null);
            request.setOwnerEmployeeId(employeeId);
            request.setTaskStatus(FdcAddressingTaskStatusEnum.IN_PROGRESS.getCode());
            resp = fdcAddressingThriftService.queryAddressingTaskOneStatusCount(request);
            log.info("queryInProgressCount:{},{}", request, resp);
            if (resp != null && resp.getStatus().getCode().equals(RespStatus.SUCCESS_CODE)) {
                statVO.setInProgressCount(resp.getTotalCount().intValue());
            }
            // 待执行
            request.setTenantId(tenantId);
            request.setOwnerOrgIds(null);
            request.setOwnerEmployeeId(employeeId);
            request.setTaskStatus(FdcAddressingTaskStatusEnum.PENDING.getCode());
            resp = fdcAddressingThriftService.queryAddressingTaskOneStatusCount(request);
            log.info("queryPendingCount:{},{}", request, resp);
            if (resp != null && resp.getStatus().getCode().equals(RespStatus.SUCCESS_CODE)) {
                statVO.setPendingCount(resp.getTotalCount().intValue());
            }
            // 已完成
            request.setTenantId(tenantId);
            request.setOwnerOrgIds(null);
            request.setOwnerEmployeeId(employeeId);
            request.setTaskStatus(FdcAddressingTaskStatusEnum.DONE.getCode());
            resp = fdcAddressingThriftService.queryAddressingTaskOneStatusCount(request);
            log.info("queryDoneCount:{},{}", request, resp);
            if (resp != null && resp.getStatus().getCode().equals(RespStatus.SUCCESS_CODE)) {
                statVO.setFinishedCount(resp.getTotalCount().intValue());
            }
            response = CommonResponse.success(statVO);
        } catch (Exception exp) {
            log.error(exp.getMessage(), exp);
            response = CommonResponse.fail(ResultCode.FAIL.getCode(), "查询状态统计出错");
        }
        return response;
    }

    /**
     * 查询任务详情
     *
     * @param taskId
     * @param forOpUse 是否运营需要
     * @return
     */
    public CommonResponse<FdcAddressingTaskVO> queryAddressingTask(Long taskId, Boolean forOpUse) {
        FdcQueryAddressingTaskRequest request = new FdcQueryAddressingTaskRequest();
        request.setTenantId(MccConfigUtil.getRegionSelectTenantId());
        request.setTaskId(taskId);
        request.setReturnRentalSource(true);
        if (forOpUse) {
            // 排除草稿状态的仓源信息
            request.setExcludeRentalSourceStatuses(Arrays.asList(FdcRentalSourceStatusEnum.DRAFT.getCode()));
        }

        CommonResponse<FdcAddressingTaskVO> result;
        try {
            FdcQueryAddressingTaskResp resp = fdcAddressingThriftService.queryAddressingTaskById(request);
            if (Objects.isNull(resp) || Objects.isNull(resp.getStatus())) {
                result = CommonResponse.fail(ResultCode.FAIL.getCode(), "查询数据失败");
            } else if (!RespStatus.SUCCESS_CODE.equals(resp.getStatus().getCode())) {
                result = CommonResponse.fail(ResultCode.FAIL.getCode(), resp.getStatus().getMsg());
            } else {
                FdcAddressingTaskVO vo = this.toFdcAddressingTaskVO(resp.getTaskDto(), resp.getSourceDtoList(), forOpUse);

                vo.setOwnerOrgName(this.queryOrgNameById(resp.getTaskDto().getOwnerOrgId()));
                vo.setOwnerEmployeeName(this.queryEmployeeNameById(resp.getTaskDto().getOwnerEmployeeId()));
                result = CommonResponse.success(vo);
            }
            return result;
        } catch (Exception exp) {
            log.error(exp.getMessage(), exp);
            result = CommonResponse.fail(ResultCode.FAIL.getCode(), "查询数据出错");
        }
        return result;
    }

    /**
     * 查询仓源详情
     *
     * @param sourceId 仓源id
     * @return
     */
    public CommonResponse<FdcRentalSourceVO> queryRentalSource(Long sourceId, Boolean forOpUse) {
        CommonResponse<FdcRentalSourceVO> result;
        try {
            long tenantId = MccConfigUtil.getRegionSelectTenantId();
            FdcQueryRentalSourceResp resp = fdcAddressingThriftService.queryRentalSourceById(tenantId, sourceId);
            if (Objects.isNull(resp) || Objects.isNull(resp.getStatus())) {
                result = CommonResponse.fail(ResultCode.FAIL.getCode(), "查询数据失败");
            } else if (!RespStatus.SUCCESS_CODE.equals(resp.getStatus().getCode())) {
                result = CommonResponse.fail(ResultCode.FAIL.getCode(), resp.getStatus().getMsg());
            } else {
                FdcRentalSourceVO vo = this.toFdcRentalSourceVO(resp.getSourceDto(), forOpUse);
                result = CommonResponse.success(vo);
            }
        } catch (Exception exp) {
            log.error(exp.getMessage(), exp);
            result = CommonResponse.fail(ResultCode.FAIL.getCode(), "查询数据出错");
        }
        return result;
    }

    /**
     * 查询任务列表（For寻仓人员）
     *
     * @param taskStatus
     * @return
     */
    public CommonResponse<FdcAddressingTaskQueryListResponse> queryTaskListForFp(Integer taskStatus, Integer pageNo, Integer pageSize) {
        long tenantId = MccConfigUtil.getRegionSelectTenantId();
        long employeeId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getEmployeeId();
        List<Long> empOrgIds = this.queryOrgIdsForAccount(employeeId);
        if (CollectionUtils.isEmpty(empOrgIds)) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "未找到员工组织信息");
        }

        FdcQueryAddressingTaskListRequest request = new FdcQueryAddressingTaskListRequest();
        request.setTenantId(tenantId);
        request.setOwnerOrgIds(empOrgIds);
        if (taskStatus != null
                && (FdcAddressingTaskStatusEnum.UNASSIGNED.getCode() != taskStatus.intValue()
                && FdcAddressingTaskStatusEnum.PENDING.getCode() != taskStatus.intValue())) {
            request.setOwnerEmployeeIds(Arrays.asList(employeeId));
        }
        if (!Objects.isNull(taskStatus)) {
            request.setTaskStatuses(Arrays.asList(taskStatus));
        }
        // 需要设置后端排序规则按寻仓人员排序
        request.setSortForFp(true);
        request.setPageNo(pageNo);
        request.setPageSize(pageSize);
        CommonResponse<FdcAddressingTaskQueryListResponse> result;
        try {
            FdcQueryAddressingTaskListWithRentalSourceResp resp = fdcAddressingThriftService.queryAddressingTaskListWithRentalSource(request);
            if (Objects.isNull(resp) || Objects.isNull(resp.getStatus())) {
                result = CommonResponse.fail(ResultCode.FAIL.getCode(), "查询数据失败");
            } else if (!RespStatus.SUCCESS_CODE.equals(resp.getStatus().getCode())) {
                result = CommonResponse.fail(ResultCode.FAIL.getCode(), resp.getStatus().getMsg());
            } else {
                List<FdcAddressingTaskVO> list = null;
                if (CollectionUtils.isNotEmpty(resp.getTaskWithSourceDtoList())) {
                    list = new ArrayList<>();
                    for (FdcAddressingTaskWithSourceDto dto : resp.getTaskWithSourceDtoList()) {
                        list.add(this.toFdcAddressingTaskVO(dto.getTaskDto(), dto.getSourceDtoList(), false));
                    }
                }
                FdcAddressingTaskQueryListResponse response = new FdcAddressingTaskQueryListResponse();
                response.setTotalCount(resp.getPageInfo().getTotalCount());
                response.setHasMore(resp.getPageInfo().getHasMore() ? 1 : 0);
                response.setList(list);
                result = CommonResponse.success(response);
            }
        } catch (Exception exp) {
            log.error(exp.getMessage(), exp);
            result = CommonResponse.fail(ResultCode.FAIL.getCode(), "查询数据出错");
        }
        return result;
    }

    /**
     * 查询任务列表（For运营人员）
     *
     * @param request
     * @return
     */
    public CommonResponse<FdcAddressingTaskQueryListResponse> queryTaskListForOp(FdcAddressingTaskQueryListRequest request) {
        long tenantId = MccConfigUtil.getRegionSelectTenantId();
        FdcQueryAddressingTaskListRequest req = new FdcQueryAddressingTaskListRequest();
        req.setTenantId(tenantId);
        req.setCityIdList(request.getCityIdList());
        req.setTaskName(request.getTaskName());
        req.setAoiName(request.getAoiName());
        req.setOwnerType(request.getOwnerType());
        if (!Objects.isNull(request.getTaskStatus())) {
            req.setTaskStatuses(Arrays.asList(request.getTaskStatus()));
        }
        req.setOwnerOrgIds(request.getOwnerOrgIdList());
        if (StringUtils.isNotBlank(request.getOwnerEmployeeName())) {
            List<Long> employeeIds = this.queryIdsByEmployeeName(request.getOwnerEmployeeName());
            if (CollectionUtils.isEmpty(employeeIds)) {
                // 没有满足条件的用户，直接返回空
                return CommonResponse.success(null);
            } else {
                req.setOwnerEmployeeIds(employeeIds);
            }
        }
        req.setRentalSourceName(request.getRentalSourceName());
        // 排除草稿状态的仓源
        req.setExcludeRentalSourceStatuses(Arrays.asList(FdcRentalSourceStatusEnum.DRAFT.getCode()));
        req.setPageNo(request.getPage());
        req.setPageSize(request.getPageSize());

        CommonResponse<FdcAddressingTaskQueryListResponse> result;
        try {
            FdcQueryAddressingTaskListWithRentalSourceResp resp =
                    fdcAddressingThriftService.queryAddressingTaskListWithRentalSource(req);
            if (Objects.isNull(resp) || Objects.isNull(resp.getStatus())) {
                result = CommonResponse.fail(ResultCode.FAIL.getCode(), "查询数据失败");
            } else if (!RespStatus.SUCCESS_CODE.equals(resp.getStatus().getCode())) {
                result = CommonResponse.fail(ResultCode.FAIL.getCode(), resp.getStatus().getMsg());
            } else {
                FdcAddressingTaskQueryListResponse response = new FdcAddressingTaskQueryListResponse();
                if (CollectionUtils.isNotEmpty(resp.getTaskWithSourceDtoList())) {
                    List<FdcAddressingTaskVO> list = new ArrayList<>();
                    for (FdcAddressingTaskWithSourceDto dto : resp.getTaskWithSourceDtoList()) {
                        list.add(this.toFdcAddressingTaskVO(dto.getTaskDto(), dto.getSourceDtoList(), true));
                    }
                    // 填充员工名称和组织名称
                    this.fillEmployeeNameAndOrgName(list);
                    response.setList(list);
                }
                response.setTotalCount(resp.getPageInfo().getTotalCount());
                response.setHasMore(resp.getPageInfo().getHasMore() ? 1 : 0);
                result = CommonResponse.success(response);
            }
        } catch (Exception exp) {
            log.error(exp.getMessage(), exp);
            result = CommonResponse.fail(ResultCode.FAIL.getCode(), "查询数据出错");
        }
        return result;
    }


    public List<HeatmapPointDto> querySgBeverageGmvCover(SgBeverageGmvCoverRequest req) throws TException {
        CityHeatmapQueryRequest request = new CityHeatmapQueryRequest();
        request.setDateType(req.getDateType());
        request.setDt(req.getDt());
        request.setHeatmapType(HeatmapTypeEnum.SHANGOU_BEVERAGE_GMV_COVER.name());
        request.setNorthEastPoint(req.getNorthEastPoint());
        request.setSouthWestPoint(req.getSouthWestPoint());
        CityHeatmapPointListResp resp = loiIndicatorThriftService.queryHeatmapInCity(request);
        checkResp(resp.getStatus());
        return resp.getHeatPoints();
    }

    /**
     * 通过员工名查询账号id
     *
     * @param employeeName
     * @return
     */
    private List<Long> queryIdsByEmployeeName(String employeeName) {
        long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        EmployListResponse resp = employThriftService.fuzzyQueryEmployeesByName(tenantId, employeeName);
        if (resp != null && resp.getStatus().getCode().equals(StatusCodeEnum.SUCCESS.getCode())) {
            return resp.getEmployeeInfos().stream().map(e -> e.getEmployeeId()).collect(Collectors.toList());
        }
        return null;
    }

    /**
     * 查询当前账号对应的员工姓名
     *
     * @param empId
     * @return
     */
    private String queryEmployeeNameById(Long empId) {
        if (!Objects.isNull(empId)) {
            long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
            EmployDepInfoResponse resp = employThriftService.queryEmployeeById(empId, tenantId);
            if (resp != null && resp.getStatus().getCode().equals(StatusCodeEnum.SUCCESS.getCode())) {
                return resp.getEmployeeInfo().getEmployeeName();
            }
        }
        return null;
    }

    /**
     * 查询组织（部门）名称
     *
     * @param orgId
     * @return
     */
    private String queryOrgNameById(Long orgId) {
        if (!Objects.isNull(orgId)) {
            long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
            DepartmentListResponse resp = departmentThriftService.queryDepartmentListByIds(tenantId,
                    Arrays.asList(orgId));
            if (resp != null && resp.getStatus().getCode().equals(StatusCodeEnum.SUCCESS.getCode())) {
                if (CollectionUtils.isNotEmpty(resp.getDepartmentList())) {
                    return resp.getDepartmentList().get(0).getDepartmentName();
                }
            }
        }
        return null;
    }

    /**
     * 查询账号对应的组织id
     *
     * @param empId 员工id
     * @return
     */
    private List<Long> queryOrgIdsForAccount(Long empId) {
        if (!Objects.isNull(empId)) {
            long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
            EmployDepInfoResponse resp = employThriftService.queryEmployeeById(empId, tenantId);
            if (resp != null && resp.getStatus().getCode().equals(StatusCodeEnum.SUCCESS.getCode())) {
                if (resp.getEmployeeInfo() != null && CollectionUtils.isNotEmpty(resp.getEmployeeInfo().getDepartmentList())) {
                    return resp.getEmployeeInfo().getDepartmentList().stream().map(d -> d.getDepartmentId()).collect(Collectors.toList());
                }
            }
        }
        return null;
    }

    /**
     * 批量查询部门名称信息
     * @param orgIds
     * @return
     */
    private Map<Long/*orgId*/, String/*orgName*/> queryOrgIdNameMap(List<Long> orgIds) {
        if (CollectionUtils.isNotEmpty(orgIds)) {
            long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
            DepartmentListResponse resp = departmentThriftService.queryDepartmentListByIds(tenantId, orgIds);
            if (resp != null && resp.getStatus().getCode().equals(StatusCodeEnum.SUCCESS.getCode())) {
                if (CollectionUtils.isNotEmpty(resp.getDepartmentList())) {
                    Map<Long, String> result = new HashMap<>();
                    for (DepartmentDto dto : resp.getDepartmentList()) {
                        result.put(dto.getDepartmentId(), dto.getDepartmentName());
                    }
                    return result;
                }
            }
        }
        return Collections.emptyMap();
    }

    /**
     * 批量查询账号对应查询员工的姓名
     * @param employeeIds
     * @return
     */
    private Map<Long/*employeeId*/, String/*employeeName*/> queryEmployeeIdNameMap(List<Long> employeeIds) {
        if (CollectionUtils.isNotEmpty(employeeIds)) {
            long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
            EmployDepMapResponse resp = employThriftService.queryEmployeeByIds(employeeIds, tenantId);
            if (resp != null && resp.getStatus().getCode().equals(StatusCodeEnum.SUCCESS.getCode())) {
                if (resp.getEmployeeInfoMap() != null && resp.getEmployeeInfoMap().size() > 0) {
                    Map<Long, String> result = new HashMap<>();
                    for (Map.Entry<Long, EmployeeDepDto> entry : resp.getEmployeeInfoMap().entrySet()) {
                        result.put(entry.getKey(), entry.getValue().getEmployeeName());
                    }
                    return result;
                }
            }
        }
        return Collections.emptyMap();
    }

    /**
     * 填充员工姓名和组织名称
     * @param list
     */
    private void fillEmployeeNameAndOrgName(List<FdcAddressingTaskVO> list) {
        Set<Long> employeeIds = new HashSet<>();
        Set<Long> orgIds = new HashSet<>();
        for (FdcAddressingTaskVO vo : list) {
            if (!Objects.isNull(vo.getOwnerEmployeeId())) {
                employeeIds.add(vo.getOwnerEmployeeId());
            }
            if (!Objects.isNull(vo.getOwnerOrgId())) {
                orgIds.add(vo.getOwnerOrgId());
            }
        }
        Map<Long, String> employeeMap = this.queryEmployeeIdNameMap(new ArrayList<>(employeeIds));
        Map<Long, String> orgMap = this.queryOrgIdNameMap(new ArrayList<>(orgIds));
        for (FdcAddressingTaskVO vo : list) {
            if (!Objects.isNull(vo.getOwnerEmployeeId()) && employeeMap.containsKey(vo.getOwnerEmployeeId())) {
                vo.setOwnerEmployeeName(employeeMap.get(vo.getOwnerEmployeeId()));
            }
            if (!Objects.isNull(vo.getOwnerOrgId()) && orgMap.containsKey(vo.getOwnerOrgId())) {
                vo.setOwnerOrgName(orgMap.get(vo.getOwnerOrgId()));
            }
        }
    }

    private FdcAddressingTaskVO toFdcAddressingTaskVO(FdcAddressingTaskDto taskDto,
                                                      List<FdcRentalSourceDto> sourceDtoList, Boolean forOpUse) {
        if (Objects.isNull(taskDto)) {
            return null;
        }
        FdcAddressingTaskVO vo = new FdcAddressingTaskVO();
        vo.setId(taskDto.getId());
        vo.setTaskName(taskDto.getTaskName());
        vo.setProvinceName(taskDto.getProvinceName());
        vo.setCityId(taskDto.getCityId());
        vo.setCityName(taskDto.getCityName());
        vo.setAoiName(taskDto.getAoiName());
        vo.setAoiId(taskDto.getAoiId());
        vo.setAoiRegion(taskDto.getAoiRegion());
        vo.setAoiCoordinate(taskDto.getAoiCoordinate());
        vo.setOwnerType(taskDto.getOwnerType());
        vo.setTaskStatus(taskDto.getTaskStatus());
        vo.setOwnerEmployeeId(taskDto.getOwnerEmployeeId());
        vo.setOwnerOrgId(taskDto.getOwnerOrgId());
        vo.setExpectFinishTime(taskDto.getExpectFinishTime());
        vo.setActualFinishTime(taskDto.getActualFinishTime());
        vo.setCreateTime(taskDto.getCreateTime());
        if(forOpUse){
            vo.setExecutableEventList(Fun.filter(taskDto.getExecutableEventList(),
                    t->OPERATE_OPTIONAL_ADDRESSING_TASK_EVENTS.contains(t)));
        }else {
            vo.setExecutableEventList(Fun.filter(taskDto.getExecutableEventList(),
                    t->SEARCHER_OPTIONAL_ADDRESSING_TASK_EVENTS.contains(t)));
        }
        try {
            if (StringUtils.isNotBlank(taskDto.getFocusCircles())) {
                Optional<List<FocusCircleVO>> optional = FocusCircleVO.build(taskDto.getFocusCircles());
                if (optional.isPresent()) {
                    vo.setFocusCircles(optional.get());
                }
            }
        } catch (Exception exp) {
            log.error(exp.getMessage(), exp);
        }

        if (CollectionUtils.isNotEmpty(sourceDtoList)) {
            vo.setRentalSourceList(this.toFdcRentalSourceVOList(sourceDtoList, forOpUse));
        }
        return vo;
    }

    private List<FdcRentalSourceVO> toFdcRentalSourceVOList(List<FdcRentalSourceDto> sourceDtoList, Boolean forOpUse) {
        if (CollectionUtils.isEmpty(sourceDtoList)) {
            return null;
        } else {
            return sourceDtoList.stream().map(s -> toFdcRentalSourceVO(s, forOpUse)).collect(Collectors.toList());
        }
    }

    private FdcRentalSourceVO toFdcRentalSourceVO(FdcRentalSourceDto sourceDto, Boolean forOpUse) {
        if (sourceDto == null) {
            return null;
        }
        FdcRentalSourceVO vo = new FdcRentalSourceVO();
        vo.setId(sourceDto.getId());
        vo.setFdcAddressingTaskTaskId(sourceDto.getFdcAddressingTaskId());
        vo.setSourceType(sourceDto.getSourceType());
        vo.setSourceName(sourceDto.getSourceName());
        vo.setSourceStatus(sourceDto.getSourceStatus());
        vo.setCoordinate(sourceDto.getCoordinate());
        if(forOpUse){
            vo.setExecutableEventList(Fun.filter(sourceDto.getExecutableEventList(),
                    t->OPERATE_OPTIONAL_RENTAL_SOURCE_EVENTS.contains(t)));
        }else {
            // 寻仓人员只能操作复核前的仓源
            if(sourceDto.getSourceStatus() <= FdcRentalSourceStatusEnum.WAITING_FOR_PRELIMINARY_REVIEW.getCode()){
                vo.setExecutableEventList(Fun.filter(sourceDto.getExecutableEventList(),
                        t->SEARCHER_OPTIONAL_RENTAL_SOURCE_EVENTS.contains(t)));
            }
        }
        vo.setFormItems(sourceDto.getFormItemMap());
        vo.setConsensusStatus(sourceDto.getConsensusStatus());
        vo.setFormVersion(sourceDto.getFormVersion());
        return vo;
    }

    private Operator getOperatorFromThreadLocal() {
        if (Objects.isNull(ApiMethodParamThreadLocal.getIdentityInfo()) || Objects.isNull(ApiMethodParamThreadLocal.getIdentityInfo().getUser())) {
            throw new BusinessException("获取用户信息失败，请先登录");
        }
        long employeeId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getEmployeeId();
        long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();

        return new Operator(employeeId, tenantId);
    }
}
