package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.purchase;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2024/7/29
 * 供应商
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@ToString
@Builder
public class SupplierVO {
    @FieldDoc(
            description = "供应商id",
            requiredness = Requiredness.REQUIRED
    )
    private Long supplierId;
    @FieldDoc(
            description = "供应商名称",
            requiredness = Requiredness.REQUIRED
    )
    private String supplierName;

    @FieldDoc(
            description = "供应商渠道ID",
            requiredness = Requiredness.OPTIONAL
    )
    private Integer supplierChannelId;
    @FieldDoc(
            description = "结算方式",
            requiredness = Requiredness.OPTIONAL
    )
    private Integer settleMethod;
    @FieldDoc(
            description = "付款方式",
            requiredness = Requiredness.OPTIONAL
    )
    private Integer payMethod;

    @FieldDoc(description = "供应商到货周期")
    private Integer arrivalDays;
}
