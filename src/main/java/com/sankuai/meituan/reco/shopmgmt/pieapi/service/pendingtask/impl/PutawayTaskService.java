package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.impl;

import com.google.common.collect.Lists;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.AbstractSinglePendingTaskService;
import com.sankuai.shangou.common.PageListDTO;
import com.sankuai.shangou.common.PageQueryDTO;
import com.sankuai.shangou.common.Result;
import com.sankuai.shangou.logistics.warehouse.task.TaskQueryServiceV2;
import com.sankuai.shangou.logistics.warehouse.task.enums.status.TaskStatusEnum;
import com.sankuai.shangou.logistics.warehouse.task.enums.type.TaskTypeEnum;
import com.sankuai.shangou.logistics.warehouse.task.model.TaskDTOV2;
import com.sankuai.shangou.logistics.warehouse.task.model.cmd.query.TaskSearchCmdDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PutawayTaskService extends AbstractSinglePendingTaskService {

	@Autowired
	private TaskQueryServiceV2 taskQueryServiceV2;

	@Override
	protected PendingTaskResult getPendingTaskCount(PendingTaskParam param) throws Exception {
		TaskSearchCmdDTO cmdDTO = new TaskSearchCmdDTO();
		cmdDTO.setMerchantId(param.getTenantId());
		cmdDTO.setWarehouseIds(Lists.newArrayList(param.getEntityId()));
		cmdDTO.setTaskTypeList(Lists.newArrayList(TaskTypeEnum.RECO_NO_HU_PUTAWAY_TAK.getCode(),
				TaskTypeEnum.RECO_HALF_AUTO_NO_HU_PUTAWAY_TASK.getCode()));
		List<Integer> statusList = com.sankuai.shangou.logistics.warehouse.task.enums.status.TaskStatusEnum.UNDONE_STATUSES.stream().
				map(TaskStatusEnum::getCode).collect(Collectors.toList());

		cmdDTO.setTaskStatusList(statusList);
		Result<PageListDTO<TaskDTOV2>> result = taskQueryServiceV2.pageSearchTasks(new PageQueryDTO<>(cmdDTO, 1, 10, StringUtils.EMPTY, null));
		if (Objects.isNull(result) || result.getCode() !=0 ){
			log.warn("查询待上架任务报错:{}", result);
			return PendingTaskResult.createNumberMarker(0);
		}
		return PendingTaskResult.createNumberMarker(Long.valueOf(result.getModule().getTotal()).intValue());
	}

	@Override
	protected AuthCodeEnum module() {
		return AuthCodeEnum.WMS_PENDING_PUTAWAY_TASK;
	}
}
