package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: <EMAIL>
 * @Date: 2020-02-17 16:33
 * @Description:
 */
@TypeDoc(
        description = "部分退款页面检查退款vo"
)
@ApiModel("部分退款页面检查退款请求vo")
@Data
public class OrderItemPartRefundCheckVO {
    @FieldDoc(
            description = "商品sku,customer skuId，渠道sku", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品sku", required = true)
    private String skuId;

    @FieldDoc(
            description = "退款价格", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "退款价格", required = true)
    private String refundPrice;

    @FieldDoc(
            description = "可退数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "可退数量", required = true)
    private Integer canRefundCount;


    @FieldDoc(
            description = "现价", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "现价,单位为分", required = true)
    private Integer currentPrice;
}
