package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.tenant;

import com.meituan.shangou.saas.tenant.thrift.dto.config.ConfigDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/12/30
 */
@Data
@ApiModel("租户配置")
public class TenantConfigVo {

    @ApiModelProperty(value = "租户ID", required = true)
    private Long tenantId;

    @ApiModelProperty(value = "配置主体ID", required = true)
    private Long subjectId;

    @ApiModelProperty(value = "配置项ID", required = true)
    private Integer configId;

    @ApiModelProperty(value = "配置内容", required = true)
    private String configContent;

    public static TenantConfigVo of(ConfigDto dto) {
        TenantConfigVo vo = new TenantConfigVo();
        vo.setTenantId(dto.getTenantId());
        vo.setSubjectId(dto.getSubjectId());
        vo.setConfigId(dto.getConfigId());
        vo.setConfigContent(dto.getConfigContent());
        return vo;
    }

}
