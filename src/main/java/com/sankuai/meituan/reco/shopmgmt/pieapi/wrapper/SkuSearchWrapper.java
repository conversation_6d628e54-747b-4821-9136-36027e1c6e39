package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.FallbackException;
import com.sankuai.meituan.reco.store.management.enums.OrderTypeEnum;
import com.sankuai.meituan.reco.store.management.thrift.EmpowerTaskLogicException;
import com.sankuai.meituan.reco.store.management.thrift.OrderCountRequest;
import com.sankuai.meituan.reco.store.management.thrift.refundandbreak.RefundAndBreakSkuThriftService;
import com.sankuai.meituan.reco.store.saas.infrastructure.shield.common.monitor.CommonMonitorTransaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by liujia on 2018/9/28.
 */
@Component
@Slf4j
@Rhino
public class SkuSearchWrapper {

    @Autowired
    private RefundAndBreakSkuThriftService.Iface refundAndBreakSkuThriftService;


    @Degrade(rhinoKey = "SkuSearchWrapper.queryRefundAndBreakOrderCount",
            fallBackMethod = "queryRefundAndBreakOrderCountFallback",
            timeoutInMilliseconds = 2000,
            ignoreExceptions = {CommonLogicException.class, EmpowerTaskLogicException.class})
    @MethodLog(logRequest = true, logResponse = true)
    @CommonMonitorTransaction
    public long queryRefundAndBreakOrderCount(long tenantId, long entityId, int entityType, OrderTypeEnum orderType) throws EmpowerTaskLogicException {
        OrderCountRequest orderCountRequest = new OrderCountRequest();
        orderCountRequest.setTenantId(tenantId);
        orderCountRequest.setEntityId(entityId);
        orderCountRequest.setEntityType(entityType);
        orderCountRequest.setOrderType(orderType.getCode());
        try {
            log.info("refundAndBreakSkuThriftService.queryOrderCount request:{}", orderCountRequest);
            return refundAndBreakSkuThriftService.queryOrderCount(orderCountRequest);
        } catch (Exception e) {
            log.error("refundAndBreakSkuThriftService.queryOrderCount exception", e);
            throw new CommonRuntimeException("库区查询异常", ResultCode.RETRY_INNER_FAIL);
        }
    }

    public long queryRefundAndBreakOrderCountFallback(long tenantId, long entityId, int entityType, OrderTypeEnum orderType) throws EmpowerTaskLogicException {
        throw new FallbackException("SkuSearchWrapper.queryRefundAndBreakOrderCount降级");
    }
}
