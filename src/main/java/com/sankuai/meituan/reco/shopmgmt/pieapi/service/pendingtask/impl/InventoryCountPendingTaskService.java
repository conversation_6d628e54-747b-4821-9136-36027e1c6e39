package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.impl;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.AbstractSinglePendingTaskService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.inventorycount.InventoryCountWrapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * author xujunfeng02
 * dateTime 2023/2/23 4:36 PM
 * description 歪马盘点待处理任务服务
 */
@Service
public class InventoryCountPendingTaskService extends AbstractSinglePendingTaskService {

    @Resource
    private InventoryCountWrapper wrapper;

    @Override
    protected PendingTaskResult getPendingTaskCount(PendingTaskParam param) throws Exception {

        return PendingTaskResult.createNumberMarker(wrapper.count(param));
    }

    @Override
    protected AuthCodeEnum module() {
        return AuthCodeEnum.DH_STOCK_CHECK;
    }
}