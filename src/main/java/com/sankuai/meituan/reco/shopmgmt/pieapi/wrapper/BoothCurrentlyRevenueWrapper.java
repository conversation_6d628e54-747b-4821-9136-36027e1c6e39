package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.meituan.shangou.saas.order.management.client.service.revenue.BoothRevenueQueryService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.management.RevenueOverviewVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.DateUtils;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2019-06-21
 * @description
 */
@Service
@Slf4j
public class BoothCurrentlyRevenueWrapper {


    @Resource
    private BoothRevenueQueryService boothRevenueQueryService;


    /**
     * 根据是否是摊位来查看营收金额
     * 非摊主账号 查看订单实收金额
     * 摊主账号 查看摊主营收金额
     *
     * @param tenantId
     * @param storeIdList
     * @return
     */
    public RevenueOverviewVO overview(Long tenantId, List<Long> storeIdList, Long boothId) {

        com.meituan.shangou.saas.order.management.client.dto.request.revenue.BoothTotalRevenueRequest requestParam = new com.meituan.shangou.saas.order.management.client.dto.request.revenue.BoothTotalRevenueRequest();
        requestParam.setTenantId(tenantId);

        if (CollectionUtils.isEmpty(storeIdList)) {
            throw new CommonRuntimeException("门店ID不能为空");
        }
        requestParam.setShopId(storeIdList.get(0));
        requestParam.setBoothId(boothId);
        String today = DateUtils.format(new Date(), DateUtils.YYYY_MM_DD);
        requestParam.setStartDate(today);
        requestParam.setEndDate(today);

        // 如果是非摊主，查看门店营收金额
        if (boothId == null) {
            requestParam.setNeedMerchantReceiveAmt(Boolean.TRUE);
        }

        try {
            log.info("revenueThriftService.boothTotalRevenue request:{}", requestParam);
            com.meituan.shangou.saas.order.management.client.dto.response.revenue.BoothTotalRevenueResponse boothTotalRevenueResponse = boothRevenueQueryService.totalRevenue(requestParam);
            log.info("revenueThriftService.boothTotalRevenue response:{}", boothTotalRevenueResponse);
            RevenueOverviewVO boothRevenueOverviewVO = new RevenueOverviewVO();
            boothRevenueOverviewVO.setCompleteOrderCount(boothTotalRevenueResponse.getCompleteOrderCount());
            boothRevenueOverviewVO.setTotalRefund(boothTotalRevenueResponse.getRefundAmount());
            // 如果是摊主，设置摊位营收金额
            if (boothId != null) {
                boothRevenueOverviewVO.setTotalRevenue(Long.valueOf(boothTotalRevenueResponse.getRevenueAmount()));
                boothRevenueOverviewVO.setRevenueTips(boothTotalRevenueResponse.getRevenueFormulaTips());
                boothRevenueOverviewVO.setTotalRevenueName(ConfigUtilAdapter.getString("boothRevenueName", "今日营收金额"));
            } else {
                // 如果是非摊主，设置门店营收金额
                boothRevenueOverviewVO.setTotalRevenue(boothTotalRevenueResponse.getMerchantReceiveAmt());
                boothRevenueOverviewVO.setRevenueTips(boothTotalRevenueResponse.getMerchantReceiveTips());
                boothRevenueOverviewVO.setTotalRevenueName(ConfigUtilAdapter.getString("merchantRevenueName", "今日预计收入"));
            }
            return boothRevenueOverviewVO;
        } catch (Exception ex) {
            log.error("BoothCurrentlyRevenueWrapper.overview exception", ex);
            throw new CommonRuntimeException(ex);
        }
    }

    /**
     * 根据摊位id查看营收金额
     * 非摊主账号：查看所有摊位的营收金额
     * 摊主账号: 查看本摊位的营收金额
     *
     * @param tenantId
     * @param storeIdList
     * @param boothId
     * @return
     */
    public RevenueOverviewVO overviewOfflinePriceRevenue(Long tenantId, List<Long> storeIdList, Long boothId) {

        com.meituan.shangou.saas.order.management.client.dto.request.revenue.BoothTotalRevenueRequest requestParam = new com.meituan.shangou.saas.order.management.client.dto.request.revenue.BoothTotalRevenueRequest();
        requestParam.setTenantId(tenantId);

        if (CollectionUtils.isEmpty(storeIdList)) {
            throw new CommonRuntimeException("门店ID不能为空");
        }
        requestParam.setShopId(storeIdList.get(0));
        requestParam.setBoothId(boothId);
        String today = DateUtils.format(new Date(), DateUtils.YYYY_MM_DD);
        requestParam.setStartDate(today);
        requestParam.setEndDate(today);

        try {
            log.info("revenueThriftService.boothTotalRevenue request:{}", requestParam);
            com.meituan.shangou.saas.order.management.client.dto.response.revenue.BoothTotalRevenueResponse boothTotalRevenueResponse = boothRevenueQueryService.totalRevenue(requestParam);
            log.info("revenueThriftService.boothTotalRevenue response:{}", boothTotalRevenueResponse);
            RevenueOverviewVO boothRevenueOverviewVO = new RevenueOverviewVO();
            boothRevenueOverviewVO.setTotalRevenue(Long.valueOf(boothTotalRevenueResponse.getRevenueAmount()));
            boothRevenueOverviewVO.setCompleteOrderCount(boothTotalRevenueResponse.getCompleteOrderCount());
            boothRevenueOverviewVO.setTotalRefund(boothTotalRevenueResponse.getRefundAmount());
            boothRevenueOverviewVO.setRevenueTips(boothTotalRevenueResponse.getRevenueFormulaTips());
            boothRevenueOverviewVO.setTotalRevenueName(ConfigUtilAdapter.getString("todayBoothRevenueName", "今日营收金额"));
            return boothRevenueOverviewVO;
        } catch (Exception ex) {
            log.error("BoothCurrentlyRevenueWrapper.overviewOfflinePriceRevenue exception", ex);
            throw new CommonRuntimeException(ex);
        }
    }


}

