package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.delivery;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@TypeDoc(
        description = "门店配送配置响应体"
)
@Data
@ApiModel("门店配送配置响应体")
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryStoreManagementQueryResponse {

    @FieldDoc(
            description = "门店聚合配送配置"
    )
    @ApiModelProperty(value = "门店聚合配送配置")
    private StoreAggDeliveryConfigVo storeAggDeliveryConfig;

}
