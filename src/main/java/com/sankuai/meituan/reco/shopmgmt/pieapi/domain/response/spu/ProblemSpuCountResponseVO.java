package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2022/3/4 11:59 上午
 **/

@TypeDoc(
        description = "商品信息异常，不可售商品，平台停售商品统计"
)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ProblemSpuCountResponseVO {

    @FieldDoc(
            description = "问题商品统计", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "问题商品统计", required = true)
    private List<ProblemSpuCountDetailVO> overviewGroupList;
}
