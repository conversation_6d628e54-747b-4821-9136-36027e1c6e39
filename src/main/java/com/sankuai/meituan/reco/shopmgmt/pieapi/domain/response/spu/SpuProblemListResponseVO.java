package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.store.StoreVO;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.enums.ChannelEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2022/3/31 2:32 下午
 **/
@TypeDoc(
        description = "商品信息和问题列表"
)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SpuProblemListResponseVO {

    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户ID", required = true)
    private Long tenantId;
    @FieldDoc(
            description = "门店信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店信息", required = true)
    private StoreVO store;
    @FieldDoc(
            description = "spu编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "spu编码", required = true)
    private String spuId;
    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称", required = true)
    private String name;

    @Deprecated
    @FieldDoc(
            description = "商品图片", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品图片", required = true)
    private List<String> images;

    @FieldDoc(
            description = "商品图片", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品图片", required = true)
    private List<ImageInfoVO> imageUrlInfos;

    @FieldDoc(
            description = "规格列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "规格列表")
    private List<String> specList;
    @FieldDoc(
            description = "月销量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "月销量 SPU维度=SKU销量总和")
    private Integer monthSaleAmount;

    @FieldDoc(
            description = "库存描述字段", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "库存描述字段")
    private String stockDesc;

    @FieldDoc(
            description = "审核状态"
    )
    @ApiModelProperty(name = "审核状态")
    private Integer auditStatus;

    @FieldDoc(
            description = "类型(1-渠道商品缺失、2-牵牛花商品缺失、3-规格缺失、4-基础信息不一致、5-销售信息不一致、6-价格不一致)", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品不一致类型")
    private Integer unEqualType;
    private List<Integer> unEqualTypeList;

    @FieldDoc(
            description = "停售类型(1 缺乏必填信息,2 缺乏资质,3 缺乏必填信息并缺乏资质)", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "停售类型")
    private Integer stopSellType;

    @FieldDoc(
            description = "审核信息"
    )
    @ApiModelProperty(name = "审核信息")
    private String auditStatusComment;

    @FieldDoc(
            description = "合规审核状态"
    )
    @ApiModelProperty(name = "合规审核状态")
    private Integer normAuditStatus;

    @FieldDoc(
            description = "合规审核信息"
    )
    @ApiModelProperty(name = "合规审核信息")
    private String normAuditComment;

    @FieldDoc(
            description = "平台下架信息"
    )
    @ApiModelProperty(name = "平台下架信息")
    private PlatformSoldOutInfoVO platformSoldOutInfo;

    public static SpuProblemListResponseVO of(StoreSpuVO storeSpuVO) {
        SpuProblemListResponseVO spuProblemListResponseVO = new SpuProblemListResponseVO();
        spuProblemListResponseVO.setTenantId(storeSpuVO.getTenantId());
        spuProblemListResponseVO.setStore(storeSpuVO.getStore());
        spuProblemListResponseVO.setName(storeSpuVO.getName());
        spuProblemListResponseVO.setSpuId(storeSpuVO.getSpuId());
        spuProblemListResponseVO.setImages(storeSpuVO.getImages());
        spuProblemListResponseVO.setImageUrlInfos(storeSpuVO.getImageUrlInfos());
        spuProblemListResponseVO.setMonthSaleAmount(storeSpuVO.getMonthSaleAmount());
        if(CollectionUtils.isNotEmpty(storeSpuVO.getChannelSpuList())){
            storeSpuVO.getChannelSpuList().forEach(channelSpuVO -> {
               if(channelSpuVO.getChannelId().equals(ChannelEnum.MEITUAN.getCode())){
                   spuProblemListResponseVO.setAuditStatus(channelSpuVO.getAuditStatus());
                   spuProblemListResponseVO.setStopSellType(channelSpuVO.getStopSellingStatus());
                   spuProblemListResponseVO.setAuditStatusComment(channelSpuVO.getAuditStatusComment());
                   spuProblemListResponseVO.setNormAuditComment(channelSpuVO.getNormAuditComment());
                   spuProblemListResponseVO.setNormAuditStatus(channelSpuVO.getNormAuditStatus());
                   spuProblemListResponseVO.setPlatformSoldOutInfo(channelSpuVO.getPlatformSoldOutInfo());
               }
            });
        }

        if (CollectionUtils.isNotEmpty(storeSpuVO.getStoreSkuList())) {
            spuProblemListResponseVO.setSpecList(storeSpuVO.getStoreSkuList().stream()
                    .map(StoreSkuVO::getSpec).collect(Collectors.toList()));
        }
        spuProblemListResponseVO.setStockDesc(storeSpuVO.getStockDesc());
        spuProblemListResponseVO.setUnEqualType(storeSpuVO.getUnEqualType());
        spuProblemListResponseVO.setUnEqualTypeList(storeSpuVO.getUnEqualTypeList());
        return spuProblemListResponseVO;
    }
}
