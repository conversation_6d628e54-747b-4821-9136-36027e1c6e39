package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @desc 搜索结果
 * @email <EMAIL>
 * @date 2020-12-09
 */
@TypeDoc(
        description = "门店搜索结果"
)
@ApiModel("门店搜索结果")
@Getter
@AllArgsConstructor
@ToString
public class ContrastStoreSearchVO {
    @FieldDoc(
            description = "门店指标"
    )
    @ApiModelProperty("门店指标")
    private List<ContrastStoreWithIndexVO> contrastStoreList;
}
