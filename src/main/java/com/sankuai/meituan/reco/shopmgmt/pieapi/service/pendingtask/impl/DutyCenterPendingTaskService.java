package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.impl;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.AbstractSinglePendingTaskService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.DutyCenterWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class DutyCenterPendingTaskService extends AbstractSinglePendingTaskService {

    @Resource
    private DutyCenterWrapper dutyCenterWrapper;

    @Override
    protected PendingTaskResult getPendingTaskCount(PendingTaskParam param) throws Exception {
        return PendingTaskResult.createNumberMarker(dutyCenterWrapper.queryPendingCount(param.getTenantId(), param.getEntityId(), param.getUser().getAccountId()));
    }

    @Override
    protected AuthCodeEnum module() {
        return AuthCodeEnum.DUTY_CENTER;
    }
}
