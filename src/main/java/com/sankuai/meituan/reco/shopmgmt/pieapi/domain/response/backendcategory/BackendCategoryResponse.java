package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.backendcategory;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2021/06/16
 */
@TypeDoc(
        description = "商家后台类目查询返回结构"
)
@Data
@ApiModel("商家后台类目查询返回")
@AllArgsConstructor
public class BackendCategoryResponse {

    private List<BackendCategoryTreeNodeVO> categories;
}
