package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.SpuTagDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.spu.SpuTagDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Title: SpuTagVO
 * @Description: 商品标签信息
 * @Author: zhaolei12
 * @Date: 2020/5/16 12:14 下午
 */
@TypeDoc(
        description = "Spu标签信息"
)
@Data
@ApiModel("商品标签信息")
public class SpuTagVO {

    @FieldDoc(
            description = "标签编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "标签编码", required = true)
    private Long tagId;

    @FieldDoc(
            description = "标签名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "标签名称", required = true)
    private String tagName;

    public static List<SpuTagVO> ofDTOList(List<SpuTagDTO> spuTagDTOList) {
        if (CollectionUtils.isEmpty(spuTagDTOList)) {
            return Lists.newArrayList();
        }

        return spuTagDTOList.stream().filter(Objects::nonNull).map(SpuTagVO::ofDTO).collect(Collectors.toList())
                .stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static SpuTagVO ofDTO(SpuTagDTO spuTagDTO) {
        if (spuTagDTO == null) {
            return null;
        }

        SpuTagVO spuTagVO = new SpuTagVO();
        spuTagVO.setTagId(spuTagDTO.getTagId());
        spuTagVO.setTagName(spuTagDTO.getTagName());

        return spuTagVO;
    }

    public static List<SpuTagVO> ofDTOListForBiz(List<SpuTagDto> spuTagDTOList) {
        if (CollectionUtils.isEmpty(spuTagDTOList)) {
            return Lists.newArrayList();
        }

        return spuTagDTOList.stream().filter(Objects::nonNull).map(SpuTagVO::ofDTOListForBiz).collect(Collectors.toList())
                .stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static SpuTagVO ofDTOListForBiz(SpuTagDto spuTagDTO) {
        if (spuTagDTO == null) {
            return null;
        }

        SpuTagVO spuTagVO = new SpuTagVO();
        spuTagVO.setTagId(spuTagDTO.getTagId());
        spuTagVO.setTagName(spuTagDTO.getTagName());

        return spuTagVO;
    }
}
