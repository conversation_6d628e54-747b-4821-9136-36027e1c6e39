package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.diffcompare.DiffCompareChannelInfoDTO;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "不一致商品"
)
@Data
@Builder
public class MultiChannelDiffCompareInfoVO {
    @FieldDoc(
            description = "对比字段内容"
    )
    private String compareField;

    @FieldDoc(
            description = "对比描述"
    )
    private String compareDesc;

    @FieldDoc(
            description = "比对内容值"
    )
    private String compareValueInfo;

    @FieldDoc(
            description = "商品店内码"
    )
    private String skuId;

    @FieldDoc(
            description = "对应的渠道信息集合"
    )
    private List<ChannelCompareInfoVO> channelCompareInfoVOS;

    public static MultiChannelDiffCompareInfoVO of(DiffCompareChannelInfoDTO channelInfoDTO) {
        return MultiChannelDiffCompareInfoVO.builder()
                .compareField(channelInfoDTO.getProductFieldType().name())
                .compareDesc(channelInfoDTO.getProductFieldType().getFieldDesc())
                .compareValueInfo(channelInfoDTO.getSourceCompareValueInfo())
                .skuId(channelInfoDTO.getSkuId())
                .channelCompareInfoVOS(ConverterUtils.convertList(channelInfoDTO.getChannelCompareInfoVOS(), ChannelCompareInfoVO::of))
                .build();
    }
}
