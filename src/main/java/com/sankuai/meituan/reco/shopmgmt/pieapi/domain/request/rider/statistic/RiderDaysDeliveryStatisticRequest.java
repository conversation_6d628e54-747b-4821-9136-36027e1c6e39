package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.statistic;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/8/23 17:26
 **/
@TypeDoc(description = "骑手配送统计数据-多日维度 请求")
@ApiModel("骑手配送统计数据-多日维度 请求")
@Data
public class RiderDaysDeliveryStatisticRequest {
    @FieldDoc(description = "统计时间范围-开始时间 格式:YYYYMMDD",requiredness = Requiredness.REQUIRED)
    @ApiModelProperty("统计时间范围-开始时间 格式:yyyy-MM-dd")
    private String beginTime;

    @FieldDoc(description = "统计时间范围-结束时间 格式:YYYYMMDD",requiredness = Requiredness.REQUIRED)
    @ApiModelProperty("统计时间范围-结束时间 格式:yyyy-MM-dd")
    private String endTime;

    @FieldDoc(description = "门店ID",requiredness = Requiredness.REQUIRED)
    @ApiModelProperty("门店ID")
    private Long storeId;
}
