package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.SpuCompareRecordDetailInfoDTO;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;

@TypeDoc(
        description = "不一致列表详情"
)
@Data
public class ProblemListInfoVO {

    @FieldDoc(description = "门店ID")
    private Long storeId;

    @FieldDoc(description = "渠道商品spuId")
    private String customSpuId;

    @FieldDoc(description = "门店商品spuId")
    private String spuId;

    @FieldDoc(description = "商品名称")
    private String spuName;

    @FieldDoc(description = "上架状态1-上架 2-下架")
    private Integer onSaleStatus;

    @FieldDoc(description = "商品图片链接")
    private String imageUrl;

    @FieldDoc(description = "是否力荐")
    private Integer isSpecialty;

    @FieldDoc(description = "不一致详情列表")
    private List<CompareRecordInfoVO> problemInfoList;

    @FieldDoc(
            description = "支持的操作列表(1从商家端删除;2从蔬果派删除;3向商家端新增;4向蔬果派新增;5跳转编辑;6信息与商家端一致;7信息与蔬果派一致;8改价/报价;9价格与商家端一致;10价格与蔬果派一致)"
    )
    private List<Integer> optList;

    public static ProblemListInfoVO of(SpuCompareRecordDetailInfoDTO dto) {
        ProblemListInfoVO problemListInfoVO = new ProblemListInfoVO();
        problemListInfoVO.setStoreId(dto.getStoreId());
        problemListInfoVO.setCustomSpuId(dto.getCustomSpuId());
        problemListInfoVO.setSpuId(dto.getSpuId());
        problemListInfoVO.setSpuName(dto.getSpuName());
        problemListInfoVO.setOnSaleStatus(dto.getStatus());
        problemListInfoVO.setImageUrl(dto.getImageUrl());
        problemListInfoVO.setIsSpecialty(dto.isSpecialty);
        problemListInfoVO.setProblemInfoList(ConverterUtils.convertList(dto.getSpuCompareRecordFieldDTOS(), CompareRecordInfoVO::of));
        problemListInfoVO.setOptList(CollectionUtils.isEmpty(dto.getOptList()) ? Collections.emptyList() : dto.getOptList());
        return problemListInfoVO;
    }

}