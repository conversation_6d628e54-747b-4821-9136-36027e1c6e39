package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.delivery;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.CharEncoding;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.util.HtmlUtils;

import javax.validation.constraints.NotNull;
import java.util.List;

@TypeDoc(
        description = "配送异常上报请求"
)
@ApiModel("配送异常上报请求")
@Data
public class DeliveryExceptionReportRequest {

    @FieldDoc(
            description = "运单id",
            requiredness = Requiredness.REQUIRED
    )
    @NotNull(message = "运单id不能为空")
    @ApiModelProperty(value = "运单id", required = true)
    private Long deliveryOrderId;

    @FieldDoc(
            description = "渠道订单id",
            requiredness = Requiredness.REQUIRED
    )
    @NotNull(message = "渠道订单id不能为空")
    @ApiModelProperty(value = "渠道订单id", required = true)
    private Long channelOrderId;

    @FieldDoc(
            description = "渠道id",
            requiredness = Requiredness.REQUIRED
    )
    @NotNull(message = "渠道id不能为空")
    @ApiModelProperty(value = "渠道id", required = true)
    private Integer channelId;

    @FieldDoc(
            description = "异常一级类型",
            requiredness = Requiredness.REQUIRED
    )
    @NotNull(message = "异常一级类型不能为空")
    @ApiModelProperty(value = "异常一级类型", required = true)
    private Integer exceptionType;

    @FieldDoc(
            description = "异常二级类型",
            requiredness = Requiredness.REQUIRED
    )
    @NotNull(message = "异常二级类型不能为空")
    @ApiModelProperty(value = "异常二级类型", required = true)
    private Integer exceptionSubType;

    @FieldDoc(
            description = "照片URL列表",
            requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "照片URL列表")
    private List<String> picUrls;

    @FieldDoc(
            description = "备注",
            requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "备注")
    private String comment;

    @FieldDoc(
            description = "用户真实地址",
            requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "用户真实地址")
    private String userRealAddress;

    @FieldDoc(
            description = "修改后的地址",
            requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "修改后的地址")
    private String modifiedAddress;

    public void preHandle() {
        if (StringUtils.isNotBlank(this.comment)) {
            this.comment = HtmlUtils.htmlEscape(this.comment, CharEncoding.UTF_8);
        }

    }

}
