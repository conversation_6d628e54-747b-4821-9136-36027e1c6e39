package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-12-09
 * @email <EMAIL>
 */
@ApiModel("查询待申请列表vo")
@TypeDoc(description = "查询待申请列表vo")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WaitToResignApplyVO {

    @FieldDoc(description = "员工id")
    @ApiModelProperty("员工id")
    private Long employeeId;

    @FieldDoc(description = "员工姓名")
    @ApiModelProperty("员工姓名")
    private String employeeName;

    @FieldDoc(description = "账号名")
    @ApiModelProperty("账号名")
    private String accountName;

    @FieldDoc(description = "门店名称列表")
    @ApiModelProperty("门店名称列表")
    private List<String> belongStoreNameList;

    @FieldDoc(description = "角色名称列表")
    @ApiModelProperty("角色名称列表")
    private List<String> roleNameList;

    @FieldDoc(description = "手机号")
    @ApiModelProperty("手机号")
    private String phoneNumber;

    @FieldDoc(description = "申请状态")
    @ApiModelProperty("申请状态")
    private Integer applyStatus;

    @FieldDoc(description = "岗位")
    @ApiModelProperty("岗位")
    private List<String> depPositionNameList;

}
