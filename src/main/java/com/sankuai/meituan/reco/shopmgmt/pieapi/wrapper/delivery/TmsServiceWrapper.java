package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.delivery;

import com.alibaba.fastjson.JSONObject;
import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.ImmutableList;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery.AggDeliveryPlatformInfoRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery.DeliveryManagementModifyRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery.DeliveryManagementUpdateRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.delivery.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AggDeliveryPlatformEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.FallbackException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.RpcInvokeUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelType;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.TPageInfo;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.ConfigModuleEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.ResponseCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.SiteTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.DeliveryConfigurationThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.dto.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.DeliveryOperationThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.orderplatform.DeliveryManualLaunchRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response.DeliveryChannelPreLaunchResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response.DeliveryPreLaunchResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.QueryDeliveryInfoThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.*;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/5
 * @Desc 对于tms的依赖，主要是需要实时配送数据的场景
 */
@Rhino
@Service
@Slf4j
public class TmsServiceWrapper {

    @Resource
    private QueryDeliveryInfoThriftService queryDeliveryRpcService;
    @Resource
    private DeliveryOperationThriftService deliveryOperationRpcService;
    @Resource
    private DeliveryConfigurationThriftService deliveryConfigurationThriftService;

    @Degrade(rhinoKey = "TmsServiceWrapper-queryDeliveryInfo",
            fallBackMethod = "queryDeliveryInfoFallback",
            timeoutInMilliseconds = 2000)
    @CatTransaction
    public List<TDeliveryDetail> queryDeliveryInfoByOrderIds(List<QueryOrderDeliveryInfoKey> empowerOrderIds) {

        QueryDeliveryInfoRequest req = new QueryDeliveryInfoRequest(empowerOrderIds);
        log.info("TmsServiceWrapper-queryDeliveryInfoByOrderIds, req={}", empowerOrderIds);
        QueryDeliveryInfoResponse response = queryDeliveryRpcService.queryDeliveryInfoByOrderKeys(req);
        log.info("TmsServiceWrapper-queryDeliveryInfoByOrderIds, response={}", response);
        return response.TDeliveryDetails;
    }

    @Degrade(rhinoKey = "TmsServiceWrapper-batchQueryStoreDeliveryConfig",
            fallBackMethod = "batchQueryStoreDeliveryConfigFallback",
            timeoutInMilliseconds = 2000)
    @CatTransaction
    public List<StoreSubDeliveryConfigDto> batchQueryStoreDeliveryConfig(long tenantId, long storeId) {

        BatchStoreDeliveryConfigQueryRequest req = new BatchStoreDeliveryConfigQueryRequest();
        req.setTenantId(tenantId);
        req.setStoreIdList(Lists.newArrayList(storeId));
        log.info("TmsServiceWrapper-batchQueryStoreDeliveryConfig, req={}", storeId);
        StoreDeliveryConfigBatchQueryResponse response = deliveryConfigurationThriftService.batchQueryStoreDeliveryConfig(req);
        log.info("TmsServiceWrapper-batchQueryStoreDeliveryConfig, response={}", response);
        if (CollectionUtils.isNotEmpty(response.getBatchStoreDeliveryConfigDtoList())) {
            return response.getBatchStoreDeliveryConfigDtoList().get(0).getStoreChannelDeliveryConfigList();
        }
        return Lists.newArrayList();
    }

    @Deprecated
    public List<StoreSubDeliveryConfigDto> batchQueryStoreDeliveryConfigFallback(long tenantId, long storeId) {
        log.warn("batchQueryStoreDeliveryConfig 已降级");
        return Lists.newArrayList();
    }

    @Degrade(rhinoKey = "TmsServiceWrapper-queryActiveDeliveryInfo",
            fallBackMethod = "queryActiveDeliveryInfoFallback",
            timeoutInMilliseconds = 2000)
    @CatTransaction
    public List<TDeliveryOrder> queryActiveDeliveryInfoByOrderIds(List<Long> orderList) {

        log.info("TmsServiceWrapper-queryDeliveryInfoByOrderIds, req={}", orderList);
        QueryDeliveryOrderResponse response = queryDeliveryRpcService.queryActiveDeliveryOrderByOrderIdList(orderList);
        log.info("TmsServiceWrapper-queryDeliveryInfoByOrderIds, response={}", response);
        return response.getTDeliveryOrders();
    }

    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    @Degrade(rhinoKey = "TmsServiceWrapper-preLaunchDelivery", fallBackMethod = "preLaunchDeliveryFallback", timeoutInMilliseconds = 2000)
    public List<DeliveryChannelPreLaunchResponse> preLaunchDelivery(Long storeId, Long orderId) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        DeliveryPreLaunchRequest request = new DeliveryPreLaunchRequest(
                identityInfo.getUser().getTenantId(), storeId, orderId, identityInfo.getUser().getAccountId()
        );
        DeliveryPreLaunchResponse response = deliveryOperationRpcService.preLaunchDelivery(request);
        if (response.getStatus().getCode() == ResponseCodeEnum.SUCCESS.getValue()) {
            return Optional.ofNullable(response.getDeliveryChannelList()).orElse(new ArrayList<>());
        }
        else {
            throw new CommonRuntimeException(response.getStatus().getMsg(), ResultCode.FAIL);
        }
    }

    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    @Degrade(rhinoKey = "TmsServiceWrapper-launchDelivery", fallBackMethod = "launchDeliveryFallback", timeoutInMilliseconds = 1000)
    public void launchDelivery(Long storeId, Long orderId, Integer deliveryChannelId, String servicePackage, Double estimatedDeliveryFee) {
        User currentUser = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        DeliveryLaunchRequest request = new DeliveryLaunchRequest(
                currentUser.getTenantId(), storeId, orderId, deliveryChannelId, servicePackage, estimatedDeliveryFee, currentUser.getAccountId()
        );
        DeliveryLaunchResponse response = deliveryOperationRpcService.launchDelivery(request);
        if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
            throw new CommonRuntimeException(response.getStatus().getMsg(), ResultCode.FAIL);
        }
    }

    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    @Degrade(rhinoKey = "TmsServiceWrapper-manualLaunchPlatformDelivery", fallBackMethod = "manualLaunchPlatformDeliveryFallback", timeoutInMilliseconds = 2000)
    public CommonResponse<Void> manualLaunchPlatformDelivery(Long storeId, Long orderId) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        Long tenantId = identityInfo.getUser().getTenantId();
        Long operatorId = identityInfo.getUser().getAccountId();
        DeliveryManualLaunchRequest request = new DeliveryManualLaunchRequest();
        request.setAppId(ApiMethodParamThreadLocal.getIdentityInfo().getAppId());
        request.setTenantId(tenantId);
        request.setStoreId(storeId);
        request.setOrderId(orderId);
        request.setOperatorId(operatorId);
        DeliveryLaunchResponse response = deliveryOperationRpcService.manualLaunchDelivery(request);
        if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
            return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), response.getStatus().getMsg());
        } else {
            return CommonResponse.success(null);
        }
    }

    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    @Degrade(rhinoKey = "TmsServiceWrapper-launchWithOutChannelDelivery", fallBackMethod = "launchWithOutChannelDeliveryFallback",
            timeoutInMilliseconds = 1000, isDegradeOnException = true)
    public void launchWithOutChannelDelivery(Long storeId, Long orderId) {
        User currentUser = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        DLaunchWithOutChannelReq request = new DLaunchWithOutChannelReq(
                currentUser.getTenantId(), storeId, orderId, currentUser.getAccountId());
        log.info("TmsServiceWrapper-launchWithOutChannelDelivery, req={}", request);
        DeliveryLaunchResponse response = deliveryOperationRpcService.launchWithOutChannelDelivery(request);
        log.info("TmsServiceWrapper-launchWithOutChannelDelivery, response={}", response);
        if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
            throw new CommonRuntimeException(response.getStatus().getMsg(), ResultCode.FAIL);
        }
    }

    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    @Degrade(rhinoKey = "TmsServiceWrapper-turnToThirdDelivery", fallBackMethod = "turnToThirdDeliveryFallback", timeoutInMilliseconds = 1000)
    public void turnToThirdDelivery(Long storeId, Long orderId, Integer deliveryChannelId, String servicePackage, Double estimatedDeliveryFee) {
        User currentUser = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        TurnToThirdDeliveryRequest request = new TurnToThirdDeliveryRequest(
                currentUser.getTenantId(), storeId, orderId, deliveryChannelId, servicePackage, estimatedDeliveryFee, currentUser.getAccountId()
        );
        TurnToThirdDeliveryResponse response = deliveryOperationRpcService.turnToThirdDelivery(request);
        if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
            throw new CommonRuntimeException(response.getStatus().getMsg(), ResultCode.FAIL);
        }
    }

    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    @Degrade(rhinoKey = "TmsServiceWrapper-turnToMerchantSelfDelivery", fallBackMethod = "turnToMerchantSelfDeliveryFallback", timeoutInMilliseconds = 1000)
    public void turnToMerchantSelfDelivery(Long storeId, Long orderId) {
        User currentUser = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        TurnToMerchantSelfDeliveryRequest request = new TurnToMerchantSelfDeliveryRequest(
                currentUser.getTenantId(), storeId, orderId, currentUser.getAccountId()
        );
        TurnToMerchantSelfDeliveryResponse response = deliveryOperationRpcService.turnToMerchantSelfDelivery(request);
        if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
            throw new CommonRuntimeException(response.getStatus().getMsg(), ResultCode.FAIL);
        }
    }

    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    @Degrade(rhinoKey = "TmsServiceWrapper-turnToMerchantSelfDeliveryForDrunkHorse", fallBackMethod = "turnToMerchantSelfDeliveryForDrunkHorseFallback", timeoutInMilliseconds = 3000)
    public void turnToMerchantSelfDeliveryForDrunkHorse(Long storeId, Long orderId, Long newRiderAccountId, String newRiderName, String newRiderPhone) {
        User currentUser = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        DrunkHorseTurnToMerchantSelfDeliveryRequest request = new DrunkHorseTurnToMerchantSelfDeliveryRequest(
                currentUser.getTenantId(), storeId, orderId, currentUser.getAccountId(), newRiderAccountId, newRiderName, newRiderPhone);
        TurnToMerchantSelfDeliveryResponse response = deliveryOperationRpcService.turnToMerchantSelfDeliveryForDrunkHorse(request);
        if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
            throw new CommonRuntimeException(response.getStatus().getMsg(), ResultCode.FAIL);
        }
    }

    public void turnToMerchantSelfDeliveryForDrunkHorseFallback(Long storeId, Long orderId, Long newRiderAccountId, String newRiderName, String newRiderPhone) {
        log.info("TmsServiceWrapper-turnToMerchantSelfDeliveryForDrunkHorse 发生降级, orderId:{}", orderId);
        return;
    }

    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    @Degrade(rhinoKey = "TmsServiceWrapper-cancelDelivery", fallBackMethod = "cancelDeliveryFallback", timeoutInMilliseconds = 1000)
    public void cancelDelivery(Long storeId, Long orderId) {
        User currentUser = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        DeliveryCancelRequest request = new DeliveryCancelRequest(
                currentUser.getTenantId(), storeId, orderId, currentUser.getAccountId()
        );
        DeliveryCancelResponse response = deliveryOperationRpcService.cancelDelivery(request);
        if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
            throw new CommonRuntimeException(response.getStatus().getMsg(), ResultCode.FAIL);
        }
    }

    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    @Degrade(rhinoKey = "TmsServiceWrapper-turnToAggregationDelivery", fallBackMethod = "turnToAggregationDeliveryFallback", timeoutInMilliseconds = 1000)
    public CommonResponse<Void> turnToAggregationDelivery(Long storeId, Long orderId,Integer platformId) {
        User currentUser = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        TurnToAggregationDeliveryRequest request = new TurnToAggregationDeliveryRequest(
                currentUser.getTenantId(), storeId, orderId, currentUser.getAccountId(),currentUser.getOperatorName(),platformId
        );
        TurnToAggregationDeliveryResponse response = deliveryOperationRpcService.turnToAggregationDelivery(request);
        if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
            return CommonResponse.fail(ResponseCodeEnum.FAILED.getValue(), response.getStatus().getMsg());
        } else {
            return CommonResponse.success(null);
        }
    }

    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    @Degrade(rhinoKey = "TmsServiceWrapper-deliveryStoreConfigSearch", fallBackMethod = "deliveryStoreConfigSearchFallback",
            timeoutInMilliseconds = 1000)
    public DeliveryManagementConfigResponse deliveryStoreConfigSearch(Long storeId) {
        User currentUser = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        List<Integer> platformList=ImmutableList.of(AggDeliveryPlatformEnum.MALT_FARM.getCode(),AggDeliveryPlatformEnum.DAP_DELIVERY.getCode());
        StoreConfigQueryRequest storeConfigQueryRequest = new StoreConfigQueryRequest(currentUser.getTenantId(), storeId, platformList);
        StoreConfigQueryResponse storeConfigQueryResponse = deliveryConfigurationThriftService.queryStoreConfiguration(storeConfigQueryRequest);
        if(storeConfigQueryResponse.getStatus() == null || storeConfigQueryResponse.getStatus().getCode() != 0 || storeConfigQueryResponse.getTStoreConfig()==null){
            return null;
        }

        List<TAggDeliveryPlatformConfig> configList= storeConfigQueryResponse.getTStoreConfig().getAggPlatformConfigs();
        List<AggDeliveryPlatformConfigVo> configVoList=new ArrayList<>();
        ArrayListMultimap<Integer,TAggDeliveryPlatformConfig> platformChannelMultiMap=ArrayListMultimap.create();
        Map<Integer,TAggDeliveryPlatformConfig> lastMap=new HashMap<>();
        if(CollectionUtils.isNotEmpty(storeConfigQueryResponse.getTStoreConfig().getLastPlatformConfigs())){
            for (TAggDeliveryPlatformConfig config : storeConfigQueryResponse.getTStoreConfig().getLastPlatformConfigs()){
                lastMap.put(config.getChannelType()==null ? ChannelType.MEITUAN.getValue():config.getChannelType(),config);
            }
        }
        for (TAggDeliveryPlatformConfig config : configList){
            if(!platformList.contains(config.getPlatformCode())){
                continue;
            }
            platformChannelMultiMap.put(config.getChannelType()==null ? ChannelType.MEITUAN.getValue():config.getChannelType(),config);
        }
        if(platformChannelMultiMap.isEmpty()){
            return null;
        }
        for (Integer channelType : platformChannelMultiMap.keySet()){
            List<TAggDeliveryPlatformConfig> configs=platformChannelMultiMap.get(channelType);
            TAggDeliveryPlatformConfig maltConfig=configs.stream().filter(config-> AggDeliveryPlatformEnum.MALT_FARM.getCode().equals(config.getPlatformCode())).findAny().orElse(null);
            TAggDeliveryPlatformConfig openConfig=configs.stream().filter(config-> 1==config.getOpenFlag()).findAny().orElse(maltConfig);
            if(openConfig==null || openConfig.getPlatformCode()==null){
                continue;
            }
            AggDeliveryPlatformEnum platformEnum=AggDeliveryPlatformEnum.codeValueOf(openConfig.getPlatformCode());
            if(platformEnum==null){
                continue;
            }
            configVoList.add(platformEnum.fillPlatformConfig(openConfig,storeId));
        }
        if(CollectionUtils.isEmpty(configVoList)){
            return null;
        }

        List<AggDeliveryPlatformConfigVo> finalConfigVoList=new ArrayList<>();
        if(MapUtils.isNotEmpty(lastMap)){
            for (AggDeliveryPlatformConfigVo vo : configVoList){
                if(!lastMap.containsKey(vo.getChannelType()) || vo.getStatus().equals(1)){
                    finalConfigVoList.add(vo);
                    continue;
                }
                TAggDeliveryPlatformConfig config=lastMap.get(vo.getChannelType());
                if(!AggDeliveryPlatformEnum.MALT_FARM.getCode().equals(config.getPlatformCode()) && !AggDeliveryPlatformEnum.DAP_DELIVERY.getCode().equals(config.getPlatformCode())){
                    finalConfigVoList.add(vo);
                    continue;
                }
                finalConfigVoList.add(AggDeliveryPlatformEnum.codeValueOf(config.getPlatformCode()).fillPlatformConfig(config,storeId));
            }
        }


        Optional<AggDeliveryPlatformConfigVo> configOptional = finalConfigVoList.stream().filter(config ->
                ChannelType.MEITUAN.getValue()==config.getChannelType()).findAny();
        DeliveryManagementConfigResponse configResponse = new DeliveryManagementConfigResponse();
        configResponse.setTenantId(currentUser.getTenantId());
        configResponse.setStoreId(storeId);
        configResponse.setAggDeliveryPlatformConfig(configOptional.isPresent() ? configOptional.get():null);
        configResponse.setAggDeliveryPlatformConfigList(finalConfigVoList);
        return configResponse;
    }

    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    @Degrade(rhinoKey = "TmsServiceWrapper-batchDeliveryStoreConfigSearch", fallBackMethod = "batchDeliveryStoreConfigSearchFallback",
            timeoutInMilliseconds = 1000)
    public BatchStoreConfigQueryResponse batchDeliveryStoreConfigSearch(Long tenantId, List<Long> storeIdList) {
        BatchStoreConfigQueryRequest storeConfigQueryRequest = new BatchStoreConfigQueryRequest();
        storeConfigQueryRequest.setStoreIdList(storeIdList);
        storeConfigQueryRequest.setTenantId(tenantId);
        storeConfigQueryRequest.setAggPlatformCodes(ImmutableList.of(AggDeliveryPlatformEnum.MALT_FARM.getCode(),AggDeliveryPlatformEnum.DAP_DELIVERY.getCode()));
        BatchStoreConfigQueryResponse batchStoreConfigQueryResponse = deliveryConfigurationThriftService.batchQueryStoreConfiguration(storeConfigQueryRequest);
        return batchStoreConfigQueryResponse;
    }

    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    @Degrade(rhinoKey = "TmsServiceWrapper-batchQueryOrderDeliveryUrl", fallBackMethod = "batchQueryOrderDeliveryUrlFallback",
            timeoutInMilliseconds = 1000)
    public Map<String,String> batchQueryOrderDeliveryUrl(Long tenantId, Long storeId, List<Long> orderIdList,List<String> marksIdList) {
        if (!MccConfigUtil.dapOrderLinkNoAuthSwitch()) {
            return Collections.emptyMap();
        }
        if(CollectionUtils.isEmpty(orderIdList) && CollectionUtils.isEmpty(marksIdList)){
            return Collections.emptyMap();
        }
        DeliveryOrderUrlRequest orderUrlRequest = new DeliveryOrderUrlRequest();
        orderUrlRequest.setTenantId(tenantId);
        orderUrlRequest.setStoreId(storeId);
        orderUrlRequest.setOrderIdList(orderIdList);
        orderUrlRequest.setFulfillMarkIdList(marksIdList);
        DeliveryOrderUrlResponse response = deliveryOperationRpcService.queryOrderDeliveryUrl(orderUrlRequest);
        if(response==null || MapUtils.isEmpty(response.getOrderUrlMap())){
            return Collections.emptyMap();
        }
        return response.getOrderUrlMap();
    }

    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    @Degrade(rhinoKey = "TmsServiceWrapper-deliveryStoreConfigUpdate", fallBackMethod = "deliveryStoreConfigUpdateFallback",
            timeoutInMilliseconds = 1000)
    public ConfigCommonResponse deliveryStoreConfigUpdate(DeliveryManagementUpdateRequest config) {
        User currentUser = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        StoreConfigSaveRequest storeConfigQueryRequest = new StoreConfigSaveRequest();
        storeConfigQueryRequest.setStoreId(config.getStoreId());
        storeConfigQueryRequest.setTenantId(currentUser.getTenantId());
        TAggDeliveryPlatformConfig platformConfig = new TAggDeliveryPlatformConfig();
        AggDeliveryPlatformInfoRequest aggDeliveryPlatformInfo = config.getAggDeliveryPlatformInfo();
        platformConfig.setPlatformCode(aggDeliveryPlatformInfo.getPlatformCode());
        platformConfig.setOpenFlag(aggDeliveryPlatformInfo.getStatus());
        platformConfig.setChannelType(aggDeliveryPlatformInfo.getChannelType()==null ? ChannelType.MEITUAN.getValue() : aggDeliveryPlatformInfo.getChannelType());
        storeConfigQueryRequest.setTAggDeliveryPlatformConfig(platformConfig);
        storeConfigQueryRequest.setModule(ConfigModuleEnum.PLATFORM.getValue());
        storeConfigQueryRequest.setOperatorId(currentUser.getAccountId());
        storeConfigQueryRequest.setOperatorName(currentUser.getOperatorName());
        ConfigCommonResponse configCommonResponse = deliveryConfigurationThriftService.saveStoreConfiguration(storeConfigQueryRequest);
        return configCommonResponse;
    }

    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    @Degrade(rhinoKey = "TmsServiceWrapper-deliveryStoreConfigQuery", fallBackMethod = "deliveryStoreConfigQueryFallback",
            timeoutInMilliseconds = 1000)
    public DeliveryStoreManagementQueryResponse deliveryStoreConfigQuery(Long storeId) {
        User currentUser = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        String token = ApiMethodParamThreadLocal.getIdentityInfo().getToken();
        StoreDeliveryConfigQueryRequest storeConfigQueryRequest = new StoreDeliveryConfigQueryRequest();
        storeConfigQueryRequest.setStoreId(storeId);
        storeConfigQueryRequest.setTenantId(currentUser.getTenantId());
        storeConfigQueryRequest.setToken(token);
        storeConfigQueryRequest.setDeviceType(SiteTypeEnum.APP.getCode());
        StoreDeliveryConfigQueryResponse storeDeliveryConfigQueryResponse = deliveryConfigurationThriftService.queryStoreDeliveryConfig(storeConfigQueryRequest);
        if(storeDeliveryConfigQueryResponse.getStatus() == null || storeDeliveryConfigQueryResponse.getStatus().getCode() != 0 || storeDeliveryConfigQueryResponse.getStoreDeliveryConfigDto()==null){
            return null;
        }
        DeliveryStoreManagementQueryResponse queryResponse = new DeliveryStoreManagementQueryResponse();
        StoreAggDeliveryConfigVo storeAggDeliveryConfigVo = new StoreAggDeliveryConfigVo();
        storeAggDeliveryConfigVo.setDeliveryChannelConfigs(transferConfigVo(storeDeliveryConfigQueryResponse.getStoreDeliveryConfigDto()));
        queryResponse.setStoreAggDeliveryConfig(storeAggDeliveryConfigVo);

        return queryResponse;

    }

    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    @Degrade(rhinoKey = "TmsServiceWrapper-deliveryStoreConfigModify", fallBackMethod = "deliveryStoreConfigModifyFallback",
            timeoutInMilliseconds = 1000)
    public CommonResponse deliveryStoreConfigModify(DeliveryManagementModifyRequest request) {
        User currentUser = ApiMethodParamThreadLocal.getIdentityInfo().getUser();

        StoreAggDeliveryConfigDto configDto = new StoreAggDeliveryConfigDto();
        List<DeliveryChannelLaunchPointDto> deliveryChannelLaunchPoints =
                request.getDeliveryLaunchPoints().stream().map(configVo -> DeliveryChannelLaunchPointDto.builder()
                        .channelType(configVo.getChannelType())
                        .deliveryLaunchPoint(configVo.getDeliveryLaunchPoint())
                        .deliveryLaunchDelayMinutes(configVo.getDeliveryLaunchDelayMinutes())
                        .bookingOrderDeliveryLaunchMinutes(configVo.getBookingOrderDeliveryLaunchMinutes())
                        .build()).collect(Collectors.toList());
        StoreAggDeliveryConfigModifyRequest modifyRequest = new StoreAggDeliveryConfigModifyRequest();
        modifyRequest.setTenantId(currentUser.getTenantId());
        modifyRequest.setStoreId(request.getStoreId());
        modifyRequest.setOperatorId(currentUser.getAccountId());
        modifyRequest.setOperatorName(currentUser.getOperatorName());
        configDto.setDeliveryChannelLaunchPoints(deliveryChannelLaunchPoints);
        modifyRequest.setStoreAggDeliveryConfigDto(configDto);

        ConfigCommonResponse configCommonResponse = deliveryConfigurationThriftService.modifyStoreAggDeliveryConfig(modifyRequest);
        int code = configCommonResponse.getStatus().getCode();
        return code == ResponseCodeEnum.SUCCESS.getValue() ? CommonResponse.success(null) : CommonResponse.fail(code, configCommonResponse.getStatus().getMsg());
    }


    @Degrade(rhinoKey = "TmsServiceWrapper-pageQueryThirdDeliveryOrderList",
            fallBackMethod = "pageQueryThirdDeliveryOrderListFallback",
            timeoutInMilliseconds = 2000,
            isDegradeOnException = true)
    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public PageQueryDeliveryInfoResponse pageQueryThirdDeliveryOrderList(Long tenantId, Long storeId,
                                                                         List<Integer> statusList, Boolean filterException,int page, int pageSize) {
        QueryDeliveryByStatusRequest request = new QueryDeliveryByStatusRequest(tenantId, storeId, statusList, filterException, page, pageSize);
        return RpcInvokeUtils.rpcInvokeTemplate(() -> queryDeliveryRpcService.pageQueryThirdDeliveryOrderList(request),
                "分页查询运单列表失败", resp -> resp.getStatus().getCode(),
                resp -> resp.getStatus().getMsg());
    }


    @Degrade(rhinoKey = "TmsServiceWrapper-queryThirdDeliveryOrderCountByStatusList",
            fallBackMethod = "queryThirdDeliveryOrderCountByStatusListFallBack",
            timeoutInMilliseconds = 2000,
            isDegradeOnException = true)
    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public QueryThirdDeliveryOrderCountResponse queryThirdDeliveryOrderCountByStatusList(Long tenantId, Long storeId) {
        return RpcInvokeUtils.rpcInvokeTemplate(() -> queryDeliveryRpcService.queryThirdDeliveryOrderCount(tenantId, storeId),
                "查询运单数量失败",
                resp -> resp.getStatus().getCode(),
                resp -> resp.getStatus().getMsg());
    }

    public  QueryThirdDeliveryOrderCountResponse queryThirdDeliveryOrderCountByStatusListFallBack(Long tenantId, Long storeId) {
        return new QueryThirdDeliveryOrderCountResponse(Status.SUCCESS, 0, 0, 0, 0, 0);
    }
    public PageQueryDeliveryInfoResponse pageQueryThirdDeliveryOrderListFallback(Long tenantId, Long storeId,
                                                                            List<Integer> statusList,   Boolean filterException, int page, int pageSize) {
        log.info("查询接口降级, tenantId: {}, storeId: {}", tenantId, storeId);
        return new PageQueryDeliveryInfoResponse(Status.SUCCESS, new TPageInfo(page, pageSize, 0), Collections.emptyList());
    }

    @Degrade(rhinoKey = "TmsServiceWrapper-queryDeliveryErrorOrdersBySubTypeAndStores",
            fallBackMethod = "queryDeliveryErrorOrdersBySubTypeAndStoresFallback",
            timeoutInMilliseconds = 2000,
            isDegradeOnException = true)
    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public DeliveryExceptionOrdersBySubTypeAndStoreIdsResponse queryDeliveryErrorOrdersBySubTypeAndStores(int pageNum, int pageSize, Integer subType, List<Long> storeIds) {
        QueryDeliveryExceptionOrderBySubTypeAndStoreIdsRequest req = new QueryDeliveryExceptionOrderBySubTypeAndStoreIdsRequest();
        req.setSubType(subType);
        req.setEmpowerStoreIds(storeIds);
        req.setPageNum(pageNum);
        req.setPageSize(pageSize);
        DeliveryExceptionOrdersBySubTypeAndStoreIdsResponse response = queryDeliveryRpcService.queryDeliveryExceptionOrdersBySubTypeAndStoreIds(req);
        log.info("queryDeliveryErrorOrdersBySubTypeAndStores  response: {}", JSONObject.toJSONString(response));
        if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
            throw new CommonRuntimeException(response.getStatus().getMsg(), ResultCode.FAIL);
        }
        return response;
    }

    private List<DeliveryChannelConfigVo> transferConfigVo (StoreDeliveryConfigDto storeDeliveryConfigDto) {
        List<StoreChannelDeliveryConfigDto> storeChannelDeliveryConfigList = storeDeliveryConfigDto.getStoreChannelDeliveryConfigList();
        if(CollectionUtils.isEmpty(storeChannelDeliveryConfigList)) {
            log.info("查询聚合配送配置为空");
            return Collections.emptyList();
        } else {
            List<DeliveryChannelConfigVo> configVo = storeChannelDeliveryConfigList.stream().map(config -> {
                        DeliveryChannelConfigVo deliveryChannelConfigVo = new DeliveryChannelConfigVo();
                        deliveryChannelConfigVo.setChannelType(config.getChannelType());
                        deliveryChannelConfigVo.setDeliveryLaunchPoint(config.getDeliveryLaunchPoint());
                        deliveryChannelConfigVo.setDeliveryPlatformConfigVo(buildDeliveryPlatformConfigVo(config.getDeliveryPlatformConfig()));
                        deliveryChannelConfigVo.setDeliveryLaunchDelayMinutes(Optional.ofNullable(config.getDeliveryDelayLaunchMinutes()).orElse(0));
                        deliveryChannelConfigVo.setBookingOrderDeliveryLaunchMinutes(Optional.ofNullable(config.getBookingOrderDeliveryLaunchMinutes()).orElse(60));
                        return deliveryChannelConfigVo;
                    }
            ).collect(Collectors.toList());
            return configVo;
        }

    }

    private DeliveryPlatformConfigVo buildDeliveryPlatformConfigVo (DeliveryPlatformConfigDto configDto) {
        if(Objects.isNull(configDto)) {
            log.error("buildDeliveryPlatformConfigVo, configDto is null");
            return DeliveryPlatformConfigVo.EMPTY;
        }
        return DeliveryPlatformConfigVo.builder()
                .platformCode(configDto.getPlatformCode())
                .status(configDto.getOpenFlag())
                .redirectUrl(configDto.getRedirectUrl())
                .build();
    }

    @Degrade(rhinoKey = "TmsServiceWrapper-queryDeliveryExceptionOrCanceledOrders",
            fallBackMethod = "queryDeliveryExceptionOrCanceledOrdersFallback",
            timeoutInMilliseconds = 2000,
            isDegradeOnException = true)
    @CatTransaction
    public List<TDeliveryDetail> queryDeliveryExceptionOrCanceledOrders(Long storeId,Long tenantId) {
        QueryDeliveryExceptionOrderRequest req = new QueryDeliveryExceptionOrderRequest(storeId,tenantId);
        log.info("TmsServiceWrapper-queryDeliveryExceptionOrCanceledOrders, req={}", req);
        QueryDeliveryInfoResponse response = queryDeliveryRpcService.queryDeliveryExceptionOrCanceledOrders(req);
        log.info("TmsServiceWrapper-queryDeliveryExceptionOrCanceledOrders, response={}", response);
        return response.getTDeliveryDetails();
    }

    private List<TDeliveryDetail> queryDeliveryExceptionOrCanceledOrdersFallback(Long storeId,Long tenantId) {
        log.error("queryDeliveryExceptionOrCanceledOrders {}", storeId);
        throw new FallbackException("TmsServiceWrapper-queryDeliveryExceptionOrCanceledOrders 熔断降级");
    }


    @Degrade(rhinoKey = "TmsServiceWrapper-countDeliveryExceptionOrCanceledOrder",
            fallBackMethod = "countDeliveryExceptionOrCanceledOrderFallback",
            timeoutInMilliseconds = 2000,
            isDegradeOnException = true)
    @CatTransaction
    public Integer countDeliveryExceptionOrCanceledOrder(Long storeId,Long tenantId) {
        QueryDeliveryExceptionOrderRequest req = new QueryDeliveryExceptionOrderRequest(storeId,tenantId);
        log.info("TmsServiceWrapper-countDeliveryExceptionOrCanceledOrder, req={}", req);
        DeliveryExceptionOrderNumResponse response = queryDeliveryRpcService.countDeliveryExceptionOrCanceledOrder(req);
        log.info("TmsServiceWrapper-countDeliveryExceptionOrCanceledOrder, response={}", response);
        if (response.getStatus().getCode() == ResponseCodeEnum.SUCCESS.getValue()) {
            return Optional.ofNullable(response.getOrderNum()).orElse(0);
        } else {
            throw new CommonRuntimeException(response.getStatus().getMsg(), ResultCode.FAIL);
        }
    }

    private Integer countDeliveryExceptionOrCanceledOrderFallback(Long storeId,Long tenantId) {
        log.error("countDeliveryExceptionOrCanceledOrderFallback {}", storeId);
        throw new FallbackException("TmsServiceWrapper-countDeliveryExceptionOrCanceledOrderFallback 熔断降级");
    }


    @Degrade(rhinoKey = "TmsServiceWrapper-queryDeliveryExceptionOrderSubTypeAndStoresCount",
            fallBackMethod = "queryDeliveryExceptionOrderSubTypeAndStoresCountFallback",
            timeoutInMilliseconds = 2000,
            isDegradeOnException = true)
    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public DeliveryExceptionOrderSubTypeCountResponse queryDeliveryExceptionOrderSubTypeAndStoresCount(List<Long> storeIds) {
        QueryDeliveryExceptionOrderByStoreIdsRequest req = new QueryDeliveryExceptionOrderByStoreIdsRequest(storeIds);
        DeliveryExceptionOrderSubTypeCountResponse response = queryDeliveryRpcService.queryDeliveryExceptionOrderSubTypeCountByStoreIds(req);
        if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
            throw new CommonRuntimeException(response.getStatus().getMsg(), ResultCode.FAIL);
        }
        return response;
    }

    @Degrade(rhinoKey = "TmsServiceWrapper-countDeliveryExceptionOrderByStoreIds",
            fallBackMethod = "countDeliveryExceptionOrderByStoreIdsFallback",
            timeoutInMilliseconds = 2000,
            isDegradeOnException = true)
    @CatTransaction
    public Integer countDeliveryExceptionOrderByStoreIds(List<Long> empowerStoreIds) {
        QueryDeliveryExceptionOrderByStoreIdsRequest req = new QueryDeliveryExceptionOrderByStoreIdsRequest(empowerStoreIds);
        log.info("TmsServiceWrapper-countDeliveryExceptionOrderByStoreIds, req={}", req);
        DeliveryExceptionOrderNumResponse response = queryDeliveryRpcService.countDeliveryExceptionOrderByStoreIds(req);
        log.info("TmsServiceWrapper-countDeliveryExceptionOrderByStoreIds, response={}", response);
        if (response.getStatus().getCode() == ResponseCodeEnum.SUCCESS.getValue()) {
            return Optional.ofNullable(response.getOrderNum()).orElse(0);
        } else {
            throw new CommonRuntimeException(response.getStatus().getMsg(), ResultCode.FAIL);
        }
    }

    private List<DeliveryChannelPreLaunchResponse> cancelDeliveryFallback(Long storeId, Long orderId, Throwable t) {
        log.error("cancelDeliveryFallback {} {} {}", storeId, orderId, t.getMessage(), t);
        throw new FallbackException("TmsServiceWrapper-cancelDeliveryFallback 熔断降级");
    }

    private List<DeliveryChannelPreLaunchResponse> turnToAggregationDeliveryFallback(Long storeId, Long orderId,Integer platformId, Throwable t) {
        log.error("turnToAggregationDeliveryFallback {} {} {} {}", storeId, orderId,platformId, t.getMessage(), t);
        throw new FallbackException("TmsServiceWrapper-turnToAggregationDeliveryFallback 熔断降级");
    }

    private List<DeliveryChannelPreLaunchResponse> preLaunchDeliveryFallback(Long storeId, Long orderId, Throwable t) {
        log.error("preLaunchDeliveryFallback {} {} {}", storeId, orderId, t.getMessage(), t);
        throw new FallbackException("TmsServiceWrapper-preLaunchDelivery 熔断降级");
    }

    private void launchDeliveryFallback(Long storeId, Long orderId, Integer deliveryChannelId, String servicePackage, Double estimatedDeliveryFee, Throwable t) {
        log.error("launchDeliveryFallback {} {} {} {} {} {}", storeId, orderId, deliveryChannelId, servicePackage, estimatedDeliveryFee, t.getMessage(), t);
        throw new FallbackException("TmsServiceWrapper-launchDelivery 熔断降级");
    }

    private void manualLaunchPlatformDeliveryFallback(Long storeId, Long orderId, Throwable t) {
        log.error("launchDeliveryFallback {} {} {}", storeId, orderId, t.getMessage(), t);
        throw new FallbackException("TmsServiceWrapper-manualLaunchPlatformDelivery 熔断降级");
    }

    private void launchWithOutChannelDeliveryFallback(Long storeId, Long orderId, Throwable t) {
        log.error("launchWithOutChannelDeliveryFallback {} {} {}", storeId, orderId, t.getMessage(), t);
        throw new FallbackException("TmsServiceWrapper-launchWithOutChannelDelivery 熔断降级");
    }

    private void turnToThirdDeliveryFallback(Long storeId, Long orderId, Integer deliveryChannelId, String servicePackage, Double estimatedDeliveryFee, Throwable t) {
        log.error("turnToThirdDeliveryFallback {} {} {} {} {} {}", storeId, orderId, deliveryChannelId, servicePackage, estimatedDeliveryFee, t.getMessage(), t);
        throw new FallbackException("TmsServiceWrapper-turnToThirdDelivery 熔断降级");
    }

    private void turnToMerchantSelfDeliveryFallback(Long storeId, Long orderId, Throwable t) {
        log.error("turnToMerchantSelfDeliveryFallback {} {} {}", storeId, orderId, t.getMessage(), t);
        throw new FallbackException("TmsServiceWrapper-turnToMerchantSelfDelivery 熔断降级");
    }

    private List<TDeliveryDetail> queryDeliveryInfoFallback(List<QueryOrderDeliveryInfoKey> empowerOrderIds, Throwable t) {
        log.error("queryDeliveryInfoFallback {} {}", empowerOrderIds, t.getMessage(), t);
        return Lists.newArrayList();
    }
    private List<TDeliveryOrder> queryActiveDeliveryInfoFallback(List<Long> orderIdList, Throwable t) {
        log.error("queryActiveDeliveryInfoFallback {} {}", orderIdList, t.getMessage(), t);
        return Lists.newArrayList();
    }


    private DeliveryManagementConfigResponse deliveryStoreConfigSearchFallback(Long storeId, Throwable t) {
        log.error("deliveryStoreConfigSearchFallback {} {}", storeId, t.getMessage(), t);
        throw new FallbackException("TmsServiceWrapper-deliveryStoreConfigSearch 熔断降级");
    }

    private CommonResponse deliveryStoreConfigUpdateFallback(DeliveryManagementUpdateRequest config, Throwable t) {
        log.error("deliveryStoreConfigUpdateFallback {} {}", config, t.getMessage(), t);
        throw new FallbackException("TmsServiceWrapper-deliveryStoreConfigUpdate 熔断降级");
    }

    private BatchStoreConfigQueryResponse batchDeliveryStoreConfigSearchFallback(Long tenantId, List<Long> storeIdList, Throwable t) {
        log.error("batchDeliveryStoreConfigSearchFallback {} {} {}", tenantId, storeIdList, t.getMessage(), t);
        throw new FallbackException("TmsServiceWrapper-batchDeliveryStoreConfigSearch 熔断降级");
    }

    private List<TOrderIdentifier> queryDeliveryErrorOrdersFallback(Long storeId, Throwable t) {
        log.error("queryDeliveryErrorOrdersFallback {} {}", storeId, t.getMessage(), t);
        throw new FallbackException("TmsServiceWrapper-queryDeliveryErrorOrders 熔断降级");
    }

    private Integer countDeliveryErrorOrderFallback(Long storeId, Throwable t) {
        log.error("countDeliveryErrorOrderFallback {} {}", storeId, t.getMessage(), t);
        throw new FallbackException("TmsServiceWrapper-countDeliveryErrorOrder 熔断降级");
    }

    private List<TOrderIdentifier> queryDeliveryErrorOrdersBySubTypeFallback(Integer subType, Long storeId, Throwable t) {
        log.error("queryDeliveryErrorOrdersBySubTypeFallback {} {} {}", subType, storeId, t.getMessage(), t);
        throw new FallbackException("TmsServiceWrapper-queryDeliveryErrorOrdersBySubType 熔断降级");
    }

    private List<TOrderIdentifier> queryDeliveryErrorOrdersBySubTypeAndStoresFallback(int pageNum, int pageSize, Integer subType, List<Long> storeIds, Throwable t) {
        log.error("queryDeliveryErrorOrdersBySubTypeAndStoresFallback pageNum:{}, pageSize:{}, subType:{}, storeIds:{}; {}", pageNum, pageSize, subType, storeIds, t.getMessage(), t);
        throw new FallbackException("TmsServiceWrapper-queryDeliveryErrorOrdersBySubTypeAndStores 熔断降级");
    }

    private DeliveryExceptionOrderSubTypeCountResponse queryDeliveryExceptionOrderSubTypeCountFallback(Long storeId, Throwable t) {
        log.error("queryDeliveryExceptionOrderSubTypeCountFallback {} {}", storeId, t.getMessage(), t);
        throw new FallbackException("TmsServiceWrapper-queryDeliveryExceptionOrderSubTypeCount 熔断降级");
    }

    private DeliveryExceptionOrderSubTypeCountResponse queryDeliveryExceptionOrderSubTypeAndStoresCountFallback(List<Long> storeIds, Throwable t) {
        log.error("queryDeliveryExceptionOrderSubTypeAndStoresCountFallback {} {}", storeIds, t.getMessage(), t);
        throw new FallbackException("TmsServiceWrapper-queryDeliveryExceptionOrderSubTypeAndStoresCount 熔断降级");
    }

    private Map<String,String> batchQueryOrderDeliveryUrlFallback(Long tenantId, Long storeId, List<Long> orderIdList,List<String> marksIdList, Throwable t) {
        log.error("batchQueryOrderDeliveryUrl {} {}", storeId, t.getMessage(), t);
        throw new FallbackException("TmsServiceWrapper-batchQueryOrderDeliveryUrl 熔断降级");
    }

    public DeliveryStoreManagementQueryResponse deliveryStoreConfigQueryFallback(Long storeId, Throwable t){
        log.error("deliveryStoreConfigQueryFallback {} {}", storeId, t.getMessage(), t);
        throw new FallbackException("TmsServiceWrapper-deliveryStoreConfigQuery 熔断降级");
    }

    public CommonResponse deliveryStoreConfigModifyFallback(DeliveryManagementModifyRequest request, Throwable t){
        log.error("deliveryStoreConfigModify {} {}", request, t.getMessage(), t);
        throw new FallbackException("TmsServiceWrapper-deliveryStoreConfigModify 熔断降级");
    }

    private Integer countDeliveryExceptionOrderByStoreIdsFallback(List<Long> empowerStoreIds,  Throwable t) {
        log.error("deliveryStoreConfigModify {} {}", empowerStoreIds, t.getMessage(), t);
        throw new FallbackException("TmsServiceWrapper-countDeliveryExceptionOrderByStoreIds 熔断降级");
    }

    @Degrade(rhinoKey = "TmsServiceWrapper-queryDeliveryOperateItem",
            fallBackMethod = "queryDeliveryOperateItemFallback",
            timeoutInMilliseconds = 2000,
            isDegradeOnException = true)
    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public Map<Long, DeliveryOperateItem> queryDeliveryOperateItem(Long tenantId,Long storeId,List<Long> orderIdList){
        QueryDeliveryOperateItemRequest request = new QueryDeliveryOperateItemRequest();
        request.setTenantId(tenantId);
        request.setStoreId(storeId);
        request.setOrderIdList(orderIdList);
        QueryDeliveryOperateItemResponse response = queryDeliveryRpcService.queryDeliveryOperateItem(request);
        log.info("queryDeliveryOperateItem response:{}",response);
        if(response==null || response.getStatus()==null || response.getStatus().getCode()!= Status.SUCCESS.getCode()){
            throw new CommonRuntimeException("列表获取失败", ResultCode.FAIL);
        }
        return response.getOperateItemMap();
    }

    public Map<Long, DeliveryOperateItem> queryDeliveryOperateItemFallback(Long tenantId,Long storeId,List<Long> orderIdList, Throwable t) {
        log.error("queryDeliveryOperateItemFallback {} {}", storeId, t.getMessage(), t);
        throw new FallbackException("TmsServiceWrapper-queryDeliveryOperateItem 熔断降级");
    }

}
