package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pullnew;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/06/09 16:31
 * Description: 摊位操作请求，包括新建、编辑摊位操作
 */
@TypeDoc(
        description = "展示二维码服务",
        authors = {
                "liyang176"
        },
        version = "V1.0"
)
@Data
@ApiModel("展示了拉新记录")
public class PullNewStatRequest {

    @FieldDoc(
            description = "门店ID"
    )
    @ApiModelProperty(value = "门店ID")
    private Long storeId;

    @FieldDoc(
            description = "开始时间"
    )
    @ApiModelProperty(value = "开始时间")
    private Long startTime;

    @FieldDoc(
            description = "截止时间"
    )
    @ApiModelProperty(value = "截止时间")
    private Long endTime;
}
