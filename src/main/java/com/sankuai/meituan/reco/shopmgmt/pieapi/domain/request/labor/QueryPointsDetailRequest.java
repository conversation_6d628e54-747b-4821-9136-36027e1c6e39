package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import scala.Int;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2023-03-15
 * @email <EMAIL>
 */
@TypeDoc(
        description = "查询指定时间的积分明细请求"
)
@Data
@ApiModel("查询指定时间的积分明细请求")
@AllArgsConstructor
@NoArgsConstructor
public class QueryPointsDetailRequest {

    @FieldDoc(
            description = "开始时间，unix毫秒时间戳，包括该时间"
    )
    @NotNull
    private Long beginTime;

    @FieldDoc(
            description = "结束时间，unix毫秒时间戳，不包括该时间"
    )
    @NotNull
    private Long endTime;

    @FieldDoc(
            description = "页码"
    )
    @NotNull
    private Integer pageNo;

    @FieldDoc(
            description = "一页大小"
    )
    @NotNull
    private Integer pageSize;

}
