package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2022/2/25
 **/
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2022/2/21 下午8:27
 **/
@TypeDoc(
        description = "门店问题商品总数统计"
)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class StoreSpuProblemCountResponseVO {

    @FieldDoc(
            description = "问题商品数量统计", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "问题商品数量统计", required = true)
    private Integer count;

}
