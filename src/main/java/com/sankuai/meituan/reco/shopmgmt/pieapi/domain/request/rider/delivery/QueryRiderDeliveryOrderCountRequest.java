package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.delivery;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 查询骑手订单数量请求.
 */
@TypeDoc(
        description = "查询骑手订单数量请求."
)
@ApiModel("查询骑手订单数量请求.")
@Data
public class QueryRiderDeliveryOrderCountRequest {

    @FieldDoc(
            description = "租户id"
    )
    @NotNull
    @ApiModelProperty(value = "租户id", required = true)
    private Long tenantId;

    @FieldDoc(
            description = "门店id"
    )
    @ApiModelProperty(value = "门店id", required = true)
    @NotNull
    private Long storeId;

}
