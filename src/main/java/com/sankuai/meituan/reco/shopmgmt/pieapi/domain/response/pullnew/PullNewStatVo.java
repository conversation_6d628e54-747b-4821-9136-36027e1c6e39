package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.saas.crm.promotion.dto.pullnew.AppPullNewStat;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.response.dto.AppPullNewStatDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2021/11/1 5:22 下午
 **/
@TypeDoc(
        description = "拉新统计"
)
@Data
@ApiModel("展示微信拉新统计")
@NoArgsConstructor
@AllArgsConstructor
public class PullNewStatVo {
    @FieldDoc(
            description = "有效关注数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "有效关注数", required = true)
    private int validCount;

    @FieldDoc(
            description = "取关", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "取关", required = true)
    private int cancelCount;

    @FieldDoc(
            description = "新客下单数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "新客下单数", required = true)
    private int firstOrderCount;

    @FieldDoc(
            description = "新客二单数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "新客二单数", required = true)
    private int twoOrderCount;

    @FieldDoc(
            description = "二单取消数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "二单取消数", required = true)
    private int cancelTwoOrderCount;

    @FieldDoc(
            description = "首单取消数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "首单取消数", required = true)
    private int cancelFirstOrderCount;

    @FieldDoc(
            description = "异常首单", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "异常首单数", required = true)
    private int abnormalFirstOrderCnt;

    @FieldDoc(
            description = "异常二单", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "异常二单数", required = true)
    private int abnormalSecondOrderCnt;

    @FieldDoc(
            description = "取关率", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "取关率", required = true)
    private String unSubscribeRate;

    @FieldDoc(
            description = "有效商品单"
    )
    @ApiModelProperty(value = "有效商品单", required = true)
    public int validProductOrderCnt;

    @FieldDoc(
            description = "取消商品单"
    )
    @ApiModelProperty(value = "取消商品单", required = true)
    public int cancelProductOrderCnt;

    @FieldDoc(
            description = "无效商品单"
    )
    @ApiModelProperty(value = "无效商品单", required = true)
    public int invalidProductOrderCnt;

    @FieldDoc(
            description = "有效好友量"
    )
    @ApiModelProperty(value = "有效好友量", required = true)
    public int validFriendCnt;

    @FieldDoc(
            description = "删除好友量"
    )
    @ApiModelProperty(value = "删除好友量", required = true)
    public int delFriendCnt;

    @FieldDoc(
            description = "删除率"
    )
    @ApiModelProperty(value = "删除率", required = true)
    public String delPercent;

    @FieldDoc(
            description = "有效一元单"
    )
    @ApiModelProperty(value = "有效一元单")
    public int validWaimaiOrderCnt;

    @FieldDoc(
            description = "无效一元单"
    )
    @ApiModelProperty(value = "无效一元单")
    public int invalidWaimaiOrderCnt;

    @FieldDoc(
            description = "取消一元单"
    )
    @ApiModelProperty(value = "取消一元单")
    public int cancelWaimaiOrderCnt;

    @FieldDoc(
            description = "有效会员单（年卡）数量"
    )
    @ApiModelProperty(value = "有效会员单（年卡）数量")
    public int validAnnualMemberCnt;

    @FieldDoc(
            description = "有效会员单（季卡）数量"
    )
    @ApiModelProperty(value = "有效会员单（季卡）数量")
    public int validSeasonMemberCnt;

    @FieldDoc(
            description = "取消会员单（年卡）数量"
    )
    @ApiModelProperty(value = "取消会员单（年卡）数量")
    public int cancelAnnualMemberCnt;

    @FieldDoc(
            description = "取消会员单（季卡）数量"
    )
    @ApiModelProperty(value = "取消会员单（季卡）数量")
    public int cancelSeasonMemberCnt;

    @FieldDoc(
            description = "无效会员单数量"
    )
    @ApiModelProperty(value = "无效会员单数量")
    public int invalidMemberCnt;

    public static PullNewStatVo instanceOf(AppPullNewStat stat) {
        PullNewStatVo pullNewStatVo = new PullNewStatVo();
        pullNewStatVo.setCancelCount(stat.getCancelFollowCount());
        pullNewStatVo.setFirstOrderCount(stat.getNewOrderCount());
        pullNewStatVo.setCancelFirstOrderCount(stat.getFirstOrderCancelCount());
        pullNewStatVo.setTwoOrderCount(stat.getTwoOrderCount());
        pullNewStatVo.setValidCount(stat.getValidFollowCount());
        pullNewStatVo.setCancelTwoOrderCount(stat.getSecondOrderCancelCount());
        pullNewStatVo.setAbnormalFirstOrderCnt(stat.getAbnormalFirstOrderCount());
        pullNewStatVo.setAbnormalSecondOrderCnt(stat.getAbnormalSecondOrderCount());
        pullNewStatVo.setUnSubscribeRate(stat.getUnSubscribeRate());
        return pullNewStatVo;
    }

    public static PullNewStatVo instanceOf(AppPullNewStatDto stat) {
        PullNewStatVo pullNewStatVo = new PullNewStatVo();
        pullNewStatVo.setCancelCount(stat.getCancelFollowCount());
        pullNewStatVo.setCancelFirstOrderCount(stat.getFirstOrderCancelCount());

        pullNewStatVo.setFirstOrderCount(stat.getNewOrderCount());
        pullNewStatVo.setTwoOrderCount(stat.getTwoOrderCount());

        pullNewStatVo.setValidCount(stat.getValidFollowCount());
        pullNewStatVo.setCancelTwoOrderCount(stat.getSecondOrderCancelCount());
        pullNewStatVo.setAbnormalFirstOrderCnt(stat.getAbnormalFirstOrderCount());
        pullNewStatVo.setAbnormalSecondOrderCnt(stat.getAbnormalSecondOrderCount());
        pullNewStatVo.setUnSubscribeRate(stat.getUnSubscribeRate());
        pullNewStatVo.setValidProductOrderCnt(stat.getValidProductOrderCnt());
        pullNewStatVo.setCancelProductOrderCnt(stat.getCancelProductOrderCnt());
        pullNewStatVo.setInvalidProductOrderCnt(stat.getInvalidProductOrderCnt());

        pullNewStatVo.setValidFriendCnt(stat.getValidFriendCnt());
        pullNewStatVo.setDelFriendCnt(stat.getDelFriendCnt());
        pullNewStatVo.setDelPercent(stat.getDelPercent());

        pullNewStatVo.setValidWaimaiOrderCnt(stat.getValidWaimaiOrderCnt());
        pullNewStatVo.setCancelWaimaiOrderCnt(stat.getCancelWaimaiOrderCnt());
        pullNewStatVo.setInvalidWaimaiOrderCnt(stat.getInvalidWaimaiOrderCnt());

        pullNewStatVo.setValidAnnualMemberCnt(stat.getValidAnnualMemberCnt());
        pullNewStatVo.setValidSeasonMemberCnt(stat.getValidSeasonMemberCnt());
        pullNewStatVo.setCancelAnnualMemberCnt(stat.getCancelAnnualMemberCnt());
        pullNewStatVo.setCancelSeasonMemberCnt(stat.getCancelSeasonMemberCnt());
        pullNewStatVo.setInvalidMemberCnt(stat.getInvalidMemberCnt());

        return pullNewStatVo;
    }
}
