package com.sankuai.meituan.reco.shopmgmt.pieapi.service.area.bo;

import com.meituan.shangou.saas.tenant.thrift.dto.DistrictDto;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import lombok.*;

/**
 * <AUTHOR>
 * Email <EMAIL>
 * Date 2022/9/7 8:19 下午
 * Description
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class AreaBO {

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 地区编码
     */
    private String areaCode;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 地区名称
     */
    private String areaName;



    public AreaBO(DistrictDto districtDto) {

        provinceCode = ConverterUtils.nonNullConvert(districtDto.getProvinceId(),String::valueOf);
        cityCode = ConverterUtils.nonNullConvert(districtDto.getCityId(),String::valueOf);
        areaCode = ConverterUtils.nonNullConvert(districtDto.getAreaId(),String::valueOf);

        provinceName = districtDto.getProvinceName();
        cityName = districtDto.getCityName();
        areaName = districtDto.getAreaName();
    }
}

