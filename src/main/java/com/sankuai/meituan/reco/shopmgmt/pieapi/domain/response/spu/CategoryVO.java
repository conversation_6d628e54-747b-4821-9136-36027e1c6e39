package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.CategoryDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ErpCategoryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Title: CategoryVO
 * @Description: ERP分类信息
 * @Author: zhaolei12
 * @Date: 2020/5/16 5:40 下午
 */
@TypeDoc(
        description = "ERP分类信息"
)
@Data
@ApiModel("ERP分类信息")
public class CategoryVO {

    @FieldDoc(
            description = "商品类目code", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品类目code")
    private String categoryCode;

    @FieldDoc(
            description = "商品类目名称路径（创建更新商品时无需传入，用于详情返回）", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品类目名称路径（创建更新商品时无需传入，用于详情返回）")
    private String categoryCodePath;

    @FieldDoc(
            description = "商品类目名称（创建更新商品时无需传入，用于详情返回）", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品类目名称（创建更新商品时无需传入，用于详情返回）")
    private String categoryName;

    @FieldDoc(
            description = "商品类目名称路径（创建更新商品时无需传入，用于详情返回）", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品类目名称路径（创建更新商品时无需传入，用于详情返回）")
    private String categoryNamePath;

    public static CategoryVO ofDTO(CategoryDTO categoryDTO) {
        if (categoryDTO == null) {
            return null;
        }
        CategoryVO categoryVO = new CategoryVO();
        categoryVO.setCategoryCode(categoryDTO.getCategoryCode());
        categoryVO.setCategoryCodePath(categoryDTO.getCategoryCodePath());
        categoryVO.setCategoryName(categoryDTO.getCategoryName());
        categoryVO.setCategoryNamePath(categoryDTO.getCategoryNamePath());
        return categoryVO;
    }

    public static CategoryVO ofDTO(com.sankuai.meituan.shangou.empower.productbiz.client.dto.CategoryDTO categoryDTO) {
        if (categoryDTO == null) {
            return null;
        }
        CategoryVO categoryVO = new CategoryVO();
        categoryVO.setCategoryCode(categoryDTO.getCategoryCode());
        categoryVO.setCategoryCodePath(categoryDTO.getCategoryCodePath());
        categoryVO.setCategoryName(categoryDTO.getCategoryName());
        categoryVO.setCategoryNamePath(categoryDTO.getCategoryNamePath());
        return categoryVO;
    }

    public static CategoryVO ofDTO(ErpCategoryDTO categoryDTO) {
        if (categoryDTO == null) {
            return null;
        }
        CategoryVO categoryVO = new CategoryVO();
        categoryVO.setCategoryCode(categoryDTO.getCategoryCode());
        categoryVO.setCategoryCodePath(categoryDTO.getCategoryCodePath());
        categoryVO.setCategoryName(categoryDTO.getCategoryName());
        categoryVO.setCategoryNamePath(categoryDTO.getCategoryNamePath());
        return categoryVO;
    }

    public static CategoryDTO toDTO(CategoryVO categoryVO) {
        if (categoryVO == null) {
            return null;
        }
        CategoryDTO categoryDTO = new CategoryDTO();
        categoryDTO.setCategoryCode(categoryVO.getCategoryCode());
        categoryDTO.setCategoryCodePath(categoryVO.getCategoryCodePath());
        categoryDTO.setCategoryName(categoryVO.getCategoryName());
        categoryDTO.setCategoryNamePath(categoryVO.getCategoryNamePath());
        return categoryDTO;
    }

}
