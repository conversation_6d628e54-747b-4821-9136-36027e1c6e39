package com.sankuai.meituan.reco.shopmgmt.pieapi.service.product;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.ImmutableSet;
import com.meituan.linz.boot.util.Fun;
import com.meituan.linz.product.channel.EnhanceChannelType;
import com.meituan.shangou.saas.tenant.config.TenantChannelConfigKey;
import com.meituan.shangou.saas.tenant.thrift.common.enums.ConfigItemEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.config.ChannelConfigDto;
import com.sankuai.meituan.reco.shopmgmt.pieapi.facade.tenantconfig.TenantConfigClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @since 2023-11-17
 */
@Slf4j
@Service
public class ChannelNameService {

    private final Cache<Long, Map<Integer, String>> tenantChannelAliasLocalCache = CacheBuilder.newBuilder()
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .build();

    private static final Set<Integer> NOT_RENAMEABLE_CHANNELS = ImmutableSet.of(EnhanceChannelType.MT.getChannelId(),
            EnhanceChannelType.ELEM.getChannelId(), EnhanceChannelType.JDDJ.getChannelId());

    @Autowired
    private TenantConfigClient tenantConfigFacade;

    /**
     * 查询渠道名称，包含渠道别名映射
     *
     * @param tenantId   租户ID
     * @param channelIds 渠道ID列表
     * @return 渠道ID和名称的映射关系
     */
    public Map<Integer, String> getChannelName(Long tenantId, List<Integer> channelIds) {
        Map<Integer, String> channelNameMap = Fun.toMapQuietly(channelIds, Function.identity(), EnhanceChannelType::getDescByCode);
        if (NOT_RENAMEABLE_CHANNELS.containsAll(channelIds)) {
            return channelNameMap;
        }

        Map<Integer, String> aliasChannelName = getAllAliasChannelName(tenantId);
        channelIds.stream().filter(aliasChannelName::containsKey).forEach(channelId -> channelNameMap.put(channelId, aliasChannelName.get(channelId)));
        return channelNameMap;
    }

    public Map<Integer, String> getAllAliasChannelName(Long tenantId) {
        if (tenantId == null) {
            return Collections.emptyMap();
        }
        try {
            return tenantChannelAliasLocalCache.get(tenantId, () -> getAllAliasChannelNameNoCache(tenantId));
        }
        catch (Exception e) {
            log.error("query tenant for channel alias name failed");
            return Collections.emptyMap();
        }
    }

    public Map<Integer, String> getAllAliasChannelNameNoCache(Long tenantId) {
        List<Integer> channelIds = new ArrayList<>(getAllChannelIds());
        List<TenantChannelConfigKey> tenantChannelConfigKeys = Fun.map(channelIds, channelId -> TenantChannelConfigKey.builder()
                .tenantId(tenantId).channelId(channelId).subjectId(tenantId).configId(ConfigItemEnum.CHANNEL_ALIAS.getConfigId()).build());

        List<ChannelConfigDto> channelConfigDtoList = tenantConfigFacade.batchQueryTenantChannelConfig(tenantChannelConfigKeys);

        Map<Integer, String> channelAliasNameMap = new HashMap<>();
        for (ChannelConfigDto channelConfigDto : channelConfigDtoList) {
            String aliasName = ConfigItemEnum.CHANNEL_ALIAS.getMainConfigValueAsStr(channelConfigDto.getConfigContent());
            if (StringUtils.isNotBlank(aliasName)) {
                channelAliasNameMap.put(channelConfigDto.getChannelId(), aliasName);
            }
        }
        return channelAliasNameMap;
    }

    public String getChannelName(Long tenantId, int channelId) {
        return getChannelName(tenantId, Collections.singletonList(channelId)).get(channelId);
    }


    private Set<Integer> getAllChannelIds() {
        return EnhanceChannelType.getAllChannelMap().keySet();
    }
}