package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 2022/8/22
 **/
@TypeDoc(
        description = "渠道用户请求",
        version = "2022/8/22",
        authors = {"xudatang"}
)
@Data
@ApiModel("渠道用户请求")
public class ChannelUserRequest {

    @FieldDoc(
            description = "渠道用户id"
    )
    @ApiModelProperty(value = "渠道用户id", required = true)
    @NotNull
    private Long userId;

    @FieldDoc(
            description = "渠道id",
            example = {}
    )
    @ApiModelProperty(value = "渠道id")
    private Integer channelId;

    @FieldDoc(
            description = "订单来源",
            example = {}
    )
    @ApiModelProperty(value = "订单来源")
    private Integer orderBizType;
}
