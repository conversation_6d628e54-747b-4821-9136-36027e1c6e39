package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.shangou.empower.ocms.thrift.category.dto.ChannelStoreFrontCategoryDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreCategoryDetailDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2022/6/23 8:13 下午
 **/
@TypeDoc(
        description = "前台分类VO"
)
@Data
@ApiModel("前台分类VO")
public class FrontCategoryVO {


    @FieldDoc(
            description = "分类名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "分类名称", required = true)
    private String title;


    @FieldDoc(
            description = "分类父类Id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "分类父类Id", required = true)
    private String parent;

    @FieldDoc(
            description = "分类层级", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "分类层级", required = true)
    private int level;

    @FieldDoc(
            description = "是否包含商品", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "是否包含商品", required = true)
    private Integer hasProduct;

    @FieldDoc(
            description = "包含商品数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "包含商品数量", required = true)
    private Integer productAmount;

    @FieldDoc(
            description = "下级分类数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "下级分类数量", required = true)
    private int subAmount;

    @FieldDoc(
            description = "分类下是否有子分类", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "分类下是否有子分类", required = true)
    private int hasChildren;

    @FieldDoc(
            description = "渠道Id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道Id", required = true)
    private String channelId;

    @FieldDoc(
            description = "分类ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "分类ID", required = true)
    private String categoryId;

    @FieldDoc(
            description = "子分类列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "子分类列表", required = true)
    private List<FrontCategoryVO> children;

    @FieldDoc(
            description = "置顶标识", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "置顶标识")
    private Integer topFlag;

    @FieldDoc(
            description = "置顶周期，1,2,3,4,5,6,7代表周一至周日", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "置顶周期，1,2,3,4,5,6,7代表周一至周日")
    private String weeksTime;

    @FieldDoc(
            description = "置顶时段，00:00-09:00", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "置顶时段，00:00-09:00")
    private List<String> period;

    /**
     * 单门店单渠道前台分类对象转换
     * @param categoryDTO
     * @return
     */
    public static FrontCategoryVO fCategoryConvert(ChannelStoreFrontCategoryDTO categoryDTO) {
        if (categoryDTO == null) {
            return new FrontCategoryVO();
        }
        FrontCategoryVO frontCategoryVO = new FrontCategoryVO();
        frontCategoryVO.setParent(String.valueOf(categoryDTO.getParentId()));
        frontCategoryVO.setCategoryId(String.valueOf(categoryDTO.getId()));
        frontCategoryVO.setChannelId(String.valueOf(categoryDTO.getChannelId()));
        frontCategoryVO.setTitle(categoryDTO.getName());
        frontCategoryVO.setProductAmount(categoryDTO.getSkuAmount());
        frontCategoryVO.setHasProduct(ConverterUtils.wrap(categoryDTO.isHasSku()));
        frontCategoryVO.setSubAmount(categoryDTO.getSubAmount());
        frontCategoryVO.setLevel(categoryDTO.getLevel());
        frontCategoryVO.setTopFlag(categoryDTO.getTopFlag());
        frontCategoryVO.setWeeksTime(categoryDTO.getWeeksTime());
        frontCategoryVO.setPeriod(categoryDTO.getPeriod());
        return frontCategoryVO;
    }

    public static FrontCategoryVO fCategoryConvert(StoreCategoryDetailDTO categoryDTO) {
        if (categoryDTO == null) {
            return new FrontCategoryVO();
        }
        FrontCategoryVO frontCategoryVO = new FrontCategoryVO();
        if (Objects.nonNull(categoryDTO.getParentStoreCategoryId())) {
            frontCategoryVO.setParent(String.valueOf(categoryDTO.getParentStoreCategoryId()));
        }
        frontCategoryVO.setCategoryId(String.valueOf(categoryDTO.getStoreCategoryId()));
        frontCategoryVO.setChannelId(String.valueOf(categoryDTO.getChannelId()));
        frontCategoryVO.setTitle(categoryDTO.getStoreCategoryName());
        frontCategoryVO.setProductAmount(categoryDTO.getProductNum());
        frontCategoryVO.setHasProduct(ConverterUtils.wrap(categoryDTO.getHasProduct()));
        frontCategoryVO.setSubAmount(categoryDTO.getSubStoreCategoryNum());
        frontCategoryVO.setLevel(categoryDTO.getLevel());
        frontCategoryVO.setTopFlag(categoryDTO.getTopFlag());
        frontCategoryVO.setWeeksTime(categoryDTO.getWeeksTime());
        frontCategoryVO.setPeriod(categoryDTO.getPeriod());
        return frontCategoryVO;
    }
}
