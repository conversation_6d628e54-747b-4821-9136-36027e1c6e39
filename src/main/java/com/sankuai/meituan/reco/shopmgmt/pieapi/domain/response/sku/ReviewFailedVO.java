package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ReviewFailedDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: 商品提报审核失败明细
 * @author: WangSukuan
 * @create: 2020-03-15
 **/
@TypeDoc(
        description = "商品提报审核失败明细"
)
@Data
@ApiModel("商品提报审核失败明细")
@NoArgsConstructor
public class ReviewFailedVO {

    @FieldDoc(
            description = "业务记录id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "业务记录id", required = true)
    private Long bizId;

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称", required = true)
    private String name;

    @FieldDoc(
            description = "失败原因", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "失败原因", required = true)
    private String reason;

    public ReviewFailedVO buildReviewFailedVO(ReviewFailedDTO reviewFailedDTO){
        this.bizId = reviewFailedDTO.getBizId();
        this.name = reviewFailedDTO.getName();
        this.reason = reviewFailedDTO.getReason();
        return this;
    }


}
