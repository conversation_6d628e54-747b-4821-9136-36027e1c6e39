package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@ApiModel(
        "SPU调整价格信息查询"
)
@TypeDoc(
        description = "SPU调整价格信息查询",
        authors = "hejunliang"
)
@Data
public class StoreSpuAdjustPriceInfoQueryRequest {

    @FieldDoc(
            description = "门店id"
    )
    @ApiModelProperty("门店id")
    private Long storeId;

    @FieldDoc(
            description = "spu编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "spu编码")
    private String spuId;

    public void validate() {

        if (this.storeId == null) {
            throw new CommonLogicException("门店id不能为空", ResultCode.CHECK_PARAM_ERR);
        }

        if (StringUtils.isEmpty(this.spuId)) {
            throw new CommonLogicException("spu编码不能为空", ResultCode.CHECK_PARAM_ERR);
        }
    }
}
