package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/19
 */
@TypeDoc(
        description = "ERP门店商品SKU信息裁剪后对象集，配合前端完成PDA拆分项目"
)
@Data
@ApiModel("ERP门店商品SKU信息裁剪后对象集")
public class ErpStoreSkuClippingVO {

    @FieldDoc(description = "erp编码", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "erp编码")
    private String erpCode;

    @FieldDoc(description = "upc列表", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "upc列表")
    private List<String> upcList;

    @FieldDoc(description = "规格", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "规格")
    private String spec;

    @FieldDoc(description = "单位转换系数", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "单位转换系数")
    private String transferRatio;

    @FieldDoc(description = "售价", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "售价")
    private String salePrice;

    @FieldDoc(description = "线上可售库存", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "线上可售库存")
    private String onlineValidStock;

    @FieldDoc(description = "线下库存", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "线下库存")
    private String offlineValidStock;
}