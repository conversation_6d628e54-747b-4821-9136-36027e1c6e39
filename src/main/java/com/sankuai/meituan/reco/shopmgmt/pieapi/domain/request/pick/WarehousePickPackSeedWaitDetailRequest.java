package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @Auther: nifei
 * @Date: 2023/8/21 14:51
 */
@TypeDoc(
        description = "查询待分拣详情请求"
)
@ApiModel("查询待分拣详情请求")
@Data
public class WarehousePickPackSeedWaitDetailRequest {
    @FieldDoc(
            description = "波次ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "波次ID")
    private String waveId;

    @FieldDoc(
            description = "波次任务ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "波次任务ID")
    private String taskId;

    @FieldDoc(
            description = "分页码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "分页码")
    private Integer pageNo;

    @FieldDoc(
            description = "分页大小", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "分页大小")
    private Integer pageSize;
}
