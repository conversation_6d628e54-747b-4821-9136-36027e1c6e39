package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Title: PicAuditResultVO
 * @Description:
 * @Author: zhaolei12
 * @Date: 2020/8/4 12:41 下午
 */
@TypeDoc(
        description = "图片白底检测结果"
)
@Data
@ApiModel("图片白底检测结果")
public class PicAuditResultVO {

    @FieldDoc(
            description = "检测结果 true-通过 false-不通过", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "检测结果")
    private Boolean audit = true;

    public static PicAuditResultVO of(Boolean audit) {
        PicAuditResultVO picAuditResultVO = new PicAuditResultVO();
        picAuditResultVO.setAudit(audit == null ? true : audit);
        return picAuditResultVO;
    }

}
