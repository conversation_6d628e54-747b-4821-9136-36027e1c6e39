package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MoneyUtils;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelPriceDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@ToString
public class ChannelPriceVO {

    @FieldDoc(
            description = "渠道id"
    )
    @ApiModelProperty(name = "渠道id")
    private Integer channelId;

    @FieldDoc(
            description = "渠道价格，单位元"
    )
    @ApiModelProperty(name = "渠道价格，单位元")
    private String onlinePrice;

    public static ChannelPriceDTO toDto(ChannelPriceVO priceVO) {
        ChannelPriceDTO priceDTO = new ChannelPriceDTO();
        priceDTO.setChannelId(priceVO.getChannelId());
        priceDTO.setOnlinePrice4Cent(MoneyUtils.yuanToCent(priceVO.getOnlinePrice()));
        return priceDTO;
    }

}
