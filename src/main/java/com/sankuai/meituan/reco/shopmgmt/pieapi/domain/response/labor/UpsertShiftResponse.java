package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022-10-18
 * @email <EMAIL>
 */
@TypeDoc(
        description = "新建/编辑班次返回"
)
@ApiModel("新建/编辑班次返回")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpsertShiftResponse {

    @FieldDoc(
            description = "班次id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "班次id")
    private Long shiftId;

}
