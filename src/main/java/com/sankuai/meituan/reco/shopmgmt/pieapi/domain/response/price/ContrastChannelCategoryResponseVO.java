package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @desc 渠道类目结果VO
 * @email <EMAIL>
 * @date 2020-12-10
 */
@TypeDoc(
        description = "渠道类目结果VO"
)
@ApiModel("渠道类目结果VO")
@Getter
@AllArgsConstructor
@ToString
public class ContrastChannelCategoryResponseVO {
    @FieldDoc(
            description = "类目列表"
    )
    @ApiModelProperty("类目列表")
    List<ContrastChannelCategoryVO> categories;
}
