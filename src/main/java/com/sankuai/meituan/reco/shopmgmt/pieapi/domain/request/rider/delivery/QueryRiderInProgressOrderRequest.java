package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.delivery;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotNull;

@TypeDoc(
        description = "查询骑手进行中的的订单的请求"
)
@ApiModel("查询骑手未完成的订单的请求")
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class QueryRiderInProgressOrderRequest {

    @FieldDoc(
            description = "第几页", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "第几页", required = true)
    @NotNull
    private Integer page;

    @FieldDoc(
            description = "每页行数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "每页行数", required = true)
    @NotNull
    private Integer size;

    //调用接口/pieapi/rider/delivery/queryinprogress时有可能还没有选择门店，导致取无法从header里面取到有效门店，所以前端将storeId放在body里面
    @FieldDoc(
            description = "门店id列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店id列表", required = true)
    private String storeId;

    @FieldDoc(
            description = "是否需要返回高价值标签", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否需要返回高价值标签", required = true)
    private Boolean needReturnHighPriceTag;
}
