package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.management;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @createTime 2019/11/19
 * @description
 */
@TypeDoc(
        description = "营收数据"
)
@Data
@ApiModel("营收数据")
public class ManagementRevenueVO {

    @FieldDoc(
            description = "是否展示营收数据(0: 不展示, 1: 展示)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示营收 (0: 不展示, 1: 展示)", required = true)
    @NotNull
    private Integer showRevenue;

    @FieldDoc(
            description = "收入金额", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "收入金额", required = true)
    @NotNull
    private String todayTotalRevenue;

    @FieldDoc(
            description = "收入金额名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "收入金额名称", required = true)
    @NotNull
    private String todayTotalRevenueName;


    @FieldDoc(
            description = "营收公式提示", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "营收公式提示", required = true)
    @NotNull
    private String revenueFormulaTips;


    @FieldDoc(
            description = "今日订单数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "今日订单数量", required = true)
    @NotNull
    private String todayOrderCount;


    @FieldDoc(
            description = "等待退款数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "等待退款数量", required = true)
    @NotNull
    private Integer waitToAuditRefundCount;


}
