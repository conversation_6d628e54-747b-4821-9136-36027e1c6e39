package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.quality;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.product.dto.QualityProblemInfoDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/9/20
 */
@TypeDoc(
        name = "质量问题描述",
        description = "质量问题描述"
)
@Data
public class FieldsProblemInfoVo {

    @FieldDoc(
            description = "问题描述"
    )
    @ApiModelProperty(name = "问题描述")
    private String descMsg;

    @FieldDoc(
            description = "问题释义"
    )
    @ApiModelProperty(name = "问题释义")
    private String paraphraseMsg;

    public static FieldsProblemInfoVo of(QualityProblemInfoDto infoDto) {
        FieldsProblemInfoVo vo = new FieldsProblemInfoVo();
        vo.setDescMsg(infoDto.getDescMsg());
        vo.setParaphraseMsg(infoDto.getParaphraseMsg());

        return vo;
    }
}
