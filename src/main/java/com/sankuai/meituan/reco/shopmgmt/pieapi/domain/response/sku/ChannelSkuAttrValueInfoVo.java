package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelDynamicInfoVO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelSkuAttrValueInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/3/24
 * @Description
 */
@TypeDoc(
        description = "渠道sku属性值信息"
)
@Data
@ApiModel("渠道sku属性值信息")
public class ChannelSkuAttrValueInfoVo {

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID", required = true)
    private Integer channelId;

    @FieldDoc(
            description = "SKU属性值信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "SKU属性值信息", required = true)
    private List<ChannelDynamicInfoVO> skuAttrValueInfoList;

    public static List<ChannelSkuAttrValueInfoDTO> toDtoList(List<ChannelSkuAttrValueInfoVo> channelSkuAttrValueInfoList) {
        return Fun.map(channelSkuAttrValueInfoList, vo -> {
            ChannelSkuAttrValueInfoDTO dto = new ChannelSkuAttrValueInfoDTO();
            dto.setChannelId(vo.getChannelId());
            dto.setSkuAttrValueInfoList(ChannelDynamicInfoVO.toBizDTOList(vo.getSkuAttrValueInfoList()));
            return dto;
        });
    }

    public static List<ChannelSkuAttrValueInfoVo> ofOcmsDtoList(List<com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelSkuAttrValueInfoDTO> channelSkuAttrValueInfoList) {
        return Fun.map(channelSkuAttrValueInfoList, dto -> {
            ChannelSkuAttrValueInfoVo vo = new ChannelSkuAttrValueInfoVo();
            vo.setChannelId(dto.getChannelId());
            vo.setSkuAttrValueInfoList(ChannelDynamicInfoVO.ofDTOList(dto.getSkuAttrValueInfoList()));
            return vo;
        });
    }

    public static ChannelSkuAttrValueInfoVo of(ChannelSkuAttrValueInfoDTO dto) {
        ChannelSkuAttrValueInfoVo vo = new ChannelSkuAttrValueInfoVo();
        vo.setChannelId(dto.getChannelId());
        vo.setSkuAttrValueInfoList(ChannelDynamicInfoVO.ofBizDTOList(dto.getSkuAttrValueInfoList()));
        return vo;
    }
}
