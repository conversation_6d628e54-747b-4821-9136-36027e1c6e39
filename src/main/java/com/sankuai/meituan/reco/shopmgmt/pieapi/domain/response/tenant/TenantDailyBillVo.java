package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.tenant;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * @Author: <EMAIL>
 * @Date: 2023-03-10 11:27
 * @Description:
 */
@TypeDoc(
        description = "日账单列表"
)
@Data
public class TenantDailyBillVo {
    /**
     * 账单ID
     */
    private Long billId;

    /**
     * 账单开始日期，格式：yyyy.MM.dd
     */
    private String billStartDate;

    /**
     * 账单结束日期，格式：yyyy.MM.dd
     */
    private String billEndDate;

    /**
     * 账单费用类型枚举，例如：10
     */
    private Integer billType;

    /**
     * 账单费用类型枚举描述，例如：交易提成费
     */
    private String billTypeDesc;

    /**
     * 账单金额分
     *
     * 总提成费用
     */
    private Long billAmount;

    /**
     * 账单状态
     */
    private Integer billStatus;

    /**
     * 是否封顶
     */
    private Boolean isCap;

    /**
     * 计费总销售额分
     */
    private Long totalSales;

    /**
     * 计费总单数
     */
    private Long totalOrderCnt;

    /**
     * 此账单对应结算规则版本
     */
    private String ruleVersion;

    // 账单计算金额
    private Long calcAmount;

    // 账单调整金额
    private Long adjustBillAmount;

    // 账单实际金额
    private Long actualBillAmount;

    // 账单支付截止时间
    private String createTime;

    // 账单创建时间
    private String payEndTime;

}
