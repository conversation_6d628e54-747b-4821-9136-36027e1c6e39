package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;
// Copyright (C) 2020 Meituan
// All rights reserved

import com.google.common.collect.Maps;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.shangou.saas.common.datetime.DateUtil;
import com.sankuai.meituan.shangou.saas.crm.data.client.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.saas.crm.data.client.response.GeneralThemeIndicatorDataResponse;
import com.sankuai.meituan.shangou.saas.crm.data.client.service.GeneralThemeIndicDataThriftService;
import com.sankuai.meituan.shangou.saas.crm.data.client.util.constant.SaasDataConstant;
import com.sankuai.meituan.shangou.saas.crm.data.client.util.indicdata.FilterCriteria;
import com.sankuai.meituan.shangou.saas.crm.data.client.util.indicdata.IndicQueryTemplate;
import com.sankuai.meituan.shangou.saas.crm.data.client.util.indicdata.IndicQueryTemplateBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * @author: <EMAIL>
 * @class: ThemeIndicDataWrapper
 * @date: 2020-06-18 15:52:37
 * @desc:
 */
@Slf4j
@Component
public class ThemeIndicDataWrapper {

    @Autowired
    private GeneralThemeIndicDataThriftService themeIndicDataService;

    private final static Integer SALES_AMOUNT_INDIC_ID = 751;

    private final static Long THEME_ID = 6L;

    private final static String SPU_FIELD_NAME = "spuId";

    @MethodLog(logResponse = true)
    public Map<String, Integer> getCitySpuRecent30DaySaleMap(Long tenantId, Long cityId, List<String> spuIdList) {
        IndicQueryTemplateBuilder queryBuilder = IndicQueryTemplateBuilder
                .getThemeTemplateBuilder(THEME_ID, Lists.newArrayList(SALES_AMOUNT_INDIC_ID));
        queryBuilder.setDimList(Lists.newArrayList(SPU_FIELD_NAME));
        FilterCriteria criteria = queryBuilder.createCriteria();
        String queryStartDateStr = LocalDate.now().minusDays(30).format(DateTimeFormatter.ofPattern(DateUtil.YYYY_MM_DD));
        String queryEndDateStr = LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.YYYY_MM_DD));
        criteria.andBetween("dtDate", queryStartDateStr, queryEndDateStr)
                .andIn(SPU_FIELD_NAME, spuIdList)
                .andEqualTo("tenantId", tenantId.toString())
                .andEqualTo("cityId", cityId.toString());
        IndicQueryTemplate queryTemplate = queryBuilder.build();
        try {
            GeneralThemeIndicatorDataResponse resp = themeIndicDataService.getThemeIndicatorData(queryTemplate);
            if (resp.getCode() != ResultCodeEnum.SUCCESS) {
                log.error("查询城市商品月销量异常 msg:" + resp.getMsg());
                throw new CommonRuntimeException("查询城市商品月销量异常 msg:" + resp.getMsg());
            }
            Map<String, Integer> resultMap = Maps.newHashMap();
            for (Map<String, String> dataMap : resp.getData()) {
                String spuId = dataMap.get(SPU_FIELD_NAME);
                String citySalesAmountStr = dataMap.get(String.valueOf(SALES_AMOUNT_INDIC_ID));
                if (SaasDataConstant.NAN.equals(citySalesAmountStr)) {
                    continue;
                }
                Integer sales = new BigDecimal(citySalesAmountStr).intValue();
                resultMap.put(spuId, sales);
            }
            return resultMap;
        } catch (TException e) {
            log.error("调用查询城市商品月销量异常" ,e);
            throw new CommonRuntimeException("查询城市商品月销量异常");
        }
    }
}
