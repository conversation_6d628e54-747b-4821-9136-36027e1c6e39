package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.account;

import org.apache.commons.lang3.StringUtils;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.saas.common.aop.feature.Validatable;
import com.sankuai.meituan.shangou.saas.common.utils.AssertUtil;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/07/20
 */
@TypeDoc(
        description = "验证并修改密码请求"
)
@Data
public class MarkMobileReq implements Validatable {

    @FieldDoc(
            description = "账号名"
    )
    private String accountName;


    @Override
    public void validate() {
        AssertUtil.isTrue(StringUtils.isNotBlank(accountName), "accountName 不能为空");
    }
}
