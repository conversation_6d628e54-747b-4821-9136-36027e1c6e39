package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.HireApprovalVO;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-08-01
 * @email <EMAIL>
 */
@TypeDoc(
        description = "入职待审批/已审批列表请求返回"
)
@Data
@ApiModel("入职待审批/已审批列表请求返回")
@AllArgsConstructor
@NoArgsConstructor
public class QueryApprovalListResponse {

    @FieldDoc(
            description = "是否有更多"
    )
    private List<HireApprovalVO> hireApprovalList;

    @FieldDoc(
            description = "是否有更多"
    )
    private Boolean hasMore;

    @FieldDoc(
            description = "总数"
    )
    private Long totalCount;

}
