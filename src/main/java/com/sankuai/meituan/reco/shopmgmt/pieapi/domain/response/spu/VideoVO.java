package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.VideoInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Objects;

/**
 * @Author: wangyihao04
 * @Date: 2022-04-06 11:10
 * @Mail: <EMAIL>
 */
@TypeDoc(
        description = "视频信息"
)
@Data
@ApiModel("视频信息")
public class VideoVO {
    @FieldDoc(
            description = "商品视频链接", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品视频链接", required = true)
    private String videoUrl;

    @FieldDoc(
            description = "商品视频封面链接", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品视频封面链接", required = true)
    private String videoCoverUrl;

    public static VideoVO convert(VideoInfoDTO dto){
        if (Objects.isNull(dto)){
            return null;
        }
        VideoVO videoVO = new VideoVO();
        videoVO.setVideoUrl(dto.getVideoUrl());
        videoVO.setVideoCoverUrl(dto.getCoverImageUrl());
        return videoVO;
    }
}
