package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.wavepick.converter;

import com.sankuai.meituan.reco.pickselect.thrift.wave.request.WarehouseQueryPickCompleteRequestV2;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick.WarehousePickCompleteQueryRequestV2;
import org.mapstruct.Mapper;

/**
 * @Description:
 * @Author: zhangjian155
 * @Date: 2022/8/25 16:40
 */
@Mapper(componentModel = "spring")
public abstract class WarehousePickCompleteQueryRequestV2Converter {
    public abstract WarehouseQueryPickCompleteRequestV2 convert2ThriftRequest(WarehousePickCompleteQueryRequestV2 request
            , Long accountId, Long storeId);
}
