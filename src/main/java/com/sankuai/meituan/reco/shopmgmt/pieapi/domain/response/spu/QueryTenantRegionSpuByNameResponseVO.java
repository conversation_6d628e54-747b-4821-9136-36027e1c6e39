package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: wanghongzhen
 * @date: 2020-05-11 15:51
 */
@TypeDoc(
        description = "商品SPU名称联想查询分页查询响应"
)
@Data
@ApiModel("商品SPU名称联想查询分页查询响应")
public class QueryTenantRegionSpuByNameResponseVO {
    @FieldDoc(
            description = "商品明细", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品明细")
    private List<SuggestSpuVO> suggestSpuList;
    @FieldDoc(
            description = "分页信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "分页信息")
    private PageInfoVO pageInfo;
}
