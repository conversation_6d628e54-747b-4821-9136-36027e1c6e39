package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-10-19
 * @email <EMAIL>
 */
@TypeDoc(
        description = "新建/编辑班次请求"
)
@ApiModel("新建/编辑班次请求")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ScheduleDetailVO {

    @FieldDoc(
            description = "员工id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "员工id")
    private Long employeeId;

    @FieldDoc(
            description = "员工名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "员工名称")
    private String employeeName;

    @FieldDoc(
            description = "员工名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "员工名称")
    private List<DetailVO> detailList;

    @TypeDoc(
            description = "明细"
    )
    @ApiModel("明细")
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class DetailVO {
        @FieldDoc(
                description = "排班时间", requiredness = Requiredness.REQUIRED
        )
        @ApiModelProperty(value = "班次时间")
        private Long date;

        @FieldDoc(
                description = "班次id", requiredness = Requiredness.REQUIRED
        )
        @ApiModelProperty(value = "班次id")
        private List<ScheduleShiftVO> shiftList;

    }



}
