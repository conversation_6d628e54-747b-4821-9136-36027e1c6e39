package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.WarehouseSeedSkuItemVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.WarehouseWaveProgressVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "查询分拣详情商品列表返回"
)
@Data
@ApiModel("查询分拣详情商品列表返回")
public class WarehouseSeedSkuDetailResponse {

    @FieldDoc(
            description = "分拣进度信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "分拣进度信息", required = true)
    private WarehouseWaveProgressVO seedProgress;

    @FieldDoc(
            description = "分拣商品列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "分拣商品列表")
    private List<WarehouseSeedSkuItemVO> dataList;

    @FieldDoc(
            description = "分页信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否还有数据")
    private Boolean hasMore;
}
