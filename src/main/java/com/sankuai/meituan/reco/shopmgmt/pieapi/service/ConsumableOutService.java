package com.sankuai.meituan.reco.shopmgmt.pieapi.service;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.meituan.mafka.client.bean.MafkaProducer;
import com.meituan.mafka.client.producer.AsyncProducerResult;
import com.meituan.mafka.client.producer.FutureCallback;
import com.sankuai.meituan.reco.shopmgmt.pieapi.controller.consumable.ConsumableItemDetail;
import com.sankuai.meituan.reco.shopmgmt.pieapi.controller.consumable.ConsumableItemResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.controller.consumable.ConsumableOutRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.ConsumableTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.facade.SealContainerOpLogFacade;
import com.sankuai.meituan.reco.shopmgmt.pieapi.mq.ConsumableOutMsg;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.RetryTemplateUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.StockWrapper;
import com.sankuai.shangou.logistics.delivery.gray.enums.GrayKeyEnum;
import com.sankuai.shangou.logistics.delivery.gray.utils.GrayConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.RetryContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ConsumableOutService {

    @Resource
    private MafkaProducer consumableOutProducer;

    @Resource
    private StockWrapper stockWrapper;

    @Resource
    private SealContainerOpLogFacade sealContainerOpLogFacade;

    private final static String wmsAppKey = "com.sankuai.drunkhorsemgmt.wms";

    public ConsumableItemResponse queryConsumableList(Long storeId) {
        log.info("storeId: {}, accountId: {}", storeId, ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId());
        long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();

        List<Long> greyStoreIds = Lion.getList(wmsAppKey, "waima_consumable_out_grey_store_ids", Long.class,
                Collections.emptyList());

        List<ConsumableItemDetail> consumableList;
        if (greyStoreIds.contains(storeId)) {
            consumableList = Lion.getList(wmsAppKey,
                    "waima_consumable_out_grey_skulist", ConsumableItemDetail.class, Collections.emptyList());
        } else {
            consumableList = Lion.getList(wmsAppKey, "waima_consumable_out_skulist",
                    ConsumableItemDetail.class, Collections.emptyList());
        }
        consumableList.forEach(
                consumableItemDetail -> consumableItemDetail.setType(ConsumableTypeEnum.NORMAL.getType())
        );

        if (GrayConfigUtils.judgeIsGrayStore(tenantId, storeId, GrayKeyEnum.SEAL_DELIVERY.getGrayKey())) {
            List<ConsumableItemDetail> needManageStockConsumableItem = MccConfigUtil.getNeedManageStockConsumableItem();
            if (!GrayConfigUtils.judgeIsGrayStore(tenantId, storeId, GrayKeyEnum.SEAL_DELIVERY_V2.getGrayKey())) {
                needManageStockConsumableItem = needManageStockConsumableItem.stream()
                        .filter(item -> !Objects.equals(item.getType(), ConsumableTypeEnum.SEAL_CONTAINER.getType()))
                        .collect(Collectors.toList());
            }

            Map<String, ConsumableItemDetail> needManageStockConsumableMap = needManageStockConsumableItem.stream().collect(Collectors.toMap(ConsumableItemDetail::getSkuId, Function.identity()));
            List<String> needCheckStockSkuIds = needManageStockConsumableItem.stream().map(ConsumableItemDetail::getSkuId).collect(Collectors.toList());
            try {
                Map<String, BigDecimal> stockAvailable = RetryTemplateUtil.simpleWithFixedRetry(3, 100)
                        .execute((RetryCallback<Map<String, BigDecimal>, Exception>) context -> {
                            if (MccConfigUtil.isSwitchBatchStockGrayStore(storeId)) {
                                return stockWrapper.querySaleZoneSkuStockAvailable(storeId, tenantId, needCheckStockSkuIds);
                            } else {
                                return stockWrapper.oldQuerySaleZoneSkuStockAvailable(storeId, tenantId, needCheckStockSkuIds);
                            }
                        });

                if (GrayConfigUtils.judgeIsGrayStore(tenantId, storeId, GrayKeyEnum.SEAL_DELIVERY_V2.getGrayKey())) {
                    Integer sealContainerUsingCount = RetryTemplateUtil.simpleWithFixedRetry(3, 100)
                            .execute(new RetryCallback<Integer, Exception>() {
                                @Override
                                public Integer doWithRetry(RetryContext context) throws Exception {
                                    return sealContainerOpLogFacade.queryWarehouseUsingSealContainerCount(tenantId, storeId);
                                }
                            });

                    stockAvailable.forEach((skuId, value) -> {
                        if(Objects.equals(needManageStockConsumableMap.get(skuId).getType(), ConsumableTypeEnum.SEAL_CONTAINER.getType())) {
                            int usableStock =  Math.max(value.subtract(new BigDecimal(sealContainerUsingCount)).intValue(),  BigDecimal.ZERO.intValue());
                            stockAvailable.put(skuId, new BigDecimal(usableStock));
                        }
                    });
                }

                Map<String, String> manageStockConsumableItemWarnQtyMap = MccConfigUtil.getManageStockConsumableItemWarnQtyMap();
                needManageStockConsumableItem.forEach(consumableItemDetail -> {
                    if (stockAvailable.containsKey(consumableItemDetail.getSkuId())) {
                        consumableItemDetail.setCurrentStockQuantity(stockAvailable.get(consumableItemDetail.getSkuId()).stripTrailingZeros().toPlainString());
                        consumableItemDetail.setNeedCheckStockQuantity(true);
                        consumableItemDetail.setWarnStockQuantity(manageStockConsumableItemWarnQtyMap.getOrDefault(consumableItemDetail.getSkuId(), String.valueOf(Integer.MAX_VALUE)));
                    }
                });
            } catch (Exception e) {
                log.error("查询耗材库存失败", e);
                Cat.logEvent("SEAL", "QUERY_STOCK_ERROR");
            }

            consumableList.addAll(needManageStockConsumableItem);
        }

        return new ConsumableItemResponse(consumableList);
    }

    public void consumableOut(ConsumableOutRequest request, IdentityInfo identityInfo) throws Exception {
        ConsumableOutMsg outMsg = new ConsumableOutMsg(identityInfo.getUser().getTenantId(),
                identityInfo.getStoreId(), request.getViewOrderId(), identityInfo.getUser().getEmployeeId(),
                String.valueOf(identityInfo.getUser().getAccountId()), identityInfo.getUser().getOperatorName(),
                System.currentTimeMillis(), request.getItems().stream()
                .filter(item -> new BigDecimal(item.getQuantity()).compareTo(BigDecimal.ZERO) > 0)
                .map(item -> new ConsumableOutMsg.ConsumableItem(item.getSkuId(),
                        new BigDecimal(item.getQuantity()))).collect(Collectors.toList()));

        if (CollectionUtils.isEmpty(outMsg.getItems())) {
            return;
        }
        consumableOutProducer.sendAsyncMessage(JSON.toJSONString(outMsg), new FutureCallback() {
            @Override
            public void onSuccess(AsyncProducerResult result) {
            }

            @Override
            public void onFailure(Throwable t) {
                throw new CommonRuntimeException("耗材随单出库失败");
            }
        });
    }


}
