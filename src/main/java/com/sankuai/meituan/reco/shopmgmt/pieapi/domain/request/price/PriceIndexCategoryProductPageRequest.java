package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.price;
// Copyright (C) 2020 Meituan
// All rights reserved

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: <EMAIL>
 * @class: MessageTaskQueryRequest
 * @date: 2020-03-13 18:08:46
 * @desc:
 */
@Data
@ApiModel(
        "品类商品价格指数查询"
)
@TypeDoc(
        description = "消息任务模块查询任务请求"
)
public class PriceIndexCategoryProductPageRequest {

    @FieldDoc(
            description = "门店id"
    )
    @ApiModelProperty("门店Id")
    private Long storeId;


    @FieldDoc(
            description = "一级类目品类Id"
    )
    @ApiModelProperty("一级类目品类Id")
    private String firstLevelCategoryId;


    @FieldDoc(
            description = "是否需要品类价格指数数据"
    )
    @ApiModelProperty("是否需要品类价格指数数据")
    private boolean needCategoryPriceIndex;


    @FieldDoc(
            description = "请求分页商品数量"
    )
    @ApiModelProperty("请求分页商品数量")
    private Integer pageSize;

    @FieldDoc(
            description = "当前页码"
    )
    @ApiModelProperty("当前页码")
    private Integer pageNum;
}
