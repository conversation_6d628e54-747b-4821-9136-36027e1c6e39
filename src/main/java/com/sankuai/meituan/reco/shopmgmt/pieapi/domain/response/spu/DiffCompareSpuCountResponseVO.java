package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.google.common.collect.Maps;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.CompareSpuTypeEnum;
import com.sankuai.meituan.shangou.empower.productbiz.client.enums.DiffCompareTypeEnum;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.QueryCompareTypeCountResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @version 1.0
 * @created 2022/2/28 3:45 下午
 **/
@TypeDoc(
        description = "二级商品类型统计"
)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class DiffCompareSpuCountResponseVO {


    @FieldDoc(
            description = "类型数量统计,一级分类:key:(100商品缺失，200规格缺失，300 信息不一致 400 价格不一致) value 二级分类和一级统计数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "类型数量统计", required = true)
    private Map<Integer, ProblemSpuTypeVo> topTypeCount;

    public static DiffCompareSpuCountResponseVO of(QueryCompareTypeCountResponse queryCompareTypeCountResponse){
        Map<Integer, ProblemSpuTypeVo> typeCount = Maps.newHashMap();
        Map<Integer, Integer> compareTypeCount = queryCompareTypeCountResponse.getCompareTypeCount();

        typeCount.put(CompareSpuTypeEnum.SPU_MISS.getCode(), ProblemSpuTypeVo.ofSpuMiss(
                compareTypeCount.getOrDefault(DiffCompareTypeEnum.CHANNEL_SPU_MISS.getType(),0),
                compareTypeCount.getOrDefault(DiffCompareTypeEnum.SGP_SPU_MISS.getType(),0)));

        typeCount.put(CompareSpuTypeEnum.SPEC_MISS.getCode(), ProblemSpuTypeVo.ofSpecMiss(
                compareTypeCount.getOrDefault(DiffCompareTypeEnum.SPEC_MISS.getType(),0),0));


        typeCount.put(CompareSpuTypeEnum.TOP_BASE_DIFF.getCode(), ProblemSpuTypeVo.ofBaseDiff(
                compareTypeCount.getOrDefault(DiffCompareTypeEnum.BASIC_INFO_DIFF.getType(), 0),
                compareTypeCount.getOrDefault(DiffCompareTypeEnum.SALES_INFO_DIFF.getType(), 0)));

        typeCount.put(CompareSpuTypeEnum.TOP_PRICE_DIFF.getCode(), ProblemSpuTypeVo.ofPriceDiff(
                compareTypeCount.getOrDefault(DiffCompareTypeEnum.PRICE_DIFF.getType(),0)));
        return new DiffCompareSpuCountResponseVO(typeCount);
    }
}
