package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.labor;

import com.google.common.collect.Lists;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.sankuai.drunkhorsemgmt.labor.thrift.EmployeePointThriftService;
import com.sankuai.drunkhorsemgmt.labor.thrift.LaborHireApprovalThriftService;
import com.sankuai.drunkhorsemgmt.labor.thrift.LaborHireThriftService;
import com.sankuai.drunkhorsemgmt.labor.thrift.ResignThriftService;
import com.sankuai.drunkhorsemgmt.labor.thrift.dto.DatePointDTO;
import com.sankuai.drunkhorsemgmt.labor.thrift.dto.HireApprovalDetailDTO;
import com.sankuai.drunkhorsemgmt.labor.thrift.dto.HireApprovalInfoDTO;
import com.sankuai.drunkhorsemgmt.labor.thrift.page.PageRequest;
import com.sankuai.drunkhorsemgmt.labor.thrift.request.*;
import com.sankuai.drunkhorsemgmt.labor.thrift.response.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.store.StoreVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.labor.convertor.ApprovalConverter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-12-08
 * @email <EMAIL>
 */
@Slf4j
@Service
public class LaborPointServiceWrapper {

    @Resource
    private EmployeePointThriftService employeePointThriftService;

    @MethodLog(logRequest = true, logResponse = true)
    public CommonResponse<QueryStarResponse> queryStar() {
        QueryStarReq request =  new QueryStarReq();
        request.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        request.setEmployeeId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getEmployeeId());
        if(CollectionUtils.isNotEmpty(ApiMethodParamThreadLocal.getIdentityInfo().getStoreIdList())){
            request.setPoiId(ApiMethodParamThreadLocal.getIdentityInfo().getStoreIdList().get(0));
        }
        QueryStarResp resp = employeePointThriftService.queryStar(request);
        if (!resp.getStatus().successful()) {
            return CommonResponse.fail(resp.getStatus().getCode(), resp.getStatus().getMsg());
        }
        return CommonResponse.success(new QueryStarResponse(resp.getStar(), resp.getTotalPoints(), resp.getNextStarPoints()));
    }

    @MethodLog(logRequest = true, logResponse = true)
    public CommonResponse<QueryPointsDetailResponse> queryPointsDetail(QueryPointsDetailRequest request) {
        QueryPointsDetailReq req = new QueryPointsDetailReq();
        req.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        req.setEmployeeId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getEmployeeId());
        if(CollectionUtils.isNotEmpty(ApiMethodParamThreadLocal.getIdentityInfo().getStoreIdList())){
            req.setPoiId(ApiMethodParamThreadLocal.getIdentityInfo().getStoreIdList().get(0));
        }
        req.setBeginTime(request.getBeginTime());
        req.setEndTime(request.getEndTime());
        req.setPageNo(request.getPageNo());
        req.setPageSize(request.getPageSize());
        QueryPointsDetailResp resp = employeePointThriftService.queryPointsDetail(req);
        if (!resp.getStatus().successful()) {
            return CommonResponse.fail(resp.getStatus().getCode(), resp.getStatus().getMsg());
        }
        return CommonResponse.success(convertToVO(resp));
    }

    private QueryPointsDetailResponse convertToVO(QueryPointsDetailResp resp) {
        QueryPointsDetailResponse response = new QueryPointsDetailResponse();
        response.setIsSettling(resp.getIsSettling());
        response.setHasMore(resp.getHasMore());
        response.setTotal(resp.getTotalCount());
        response.setMonthTotalPoints(resp.getTotalPoints());
        response.setDatePointList(
                Optional.ofNullable(resp.getDatePointDTOList())
                        .orElse(Lists.newArrayList())
                        .stream()
                        .map(this::convertTODatePointVO)
                        .collect(Collectors.toList())
        );
        return response;
    }

    private DatePointVO convertTODatePointVO(DatePointDTO datePointDTO) {
        return new DatePointVO(
                datePointDTO.getDate(),
                datePointDTO.getDateTotalPoints(),
                convertToPointDetailVOList(datePointDTO)
        );
    }

    private List<PointDetailVO> convertToPointDetailVOList(DatePointDTO datePointDTO) {
        return Optional.ofNullable(datePointDTO.getPointDetailDTOList())
                .orElse(Lists.newArrayList())
                .stream()
                .map(dto -> new PointDetailVO(dto.getPointText(), dto.getPointTime(), dto.getGainPoints(), dto.getNowTotalPoints()))
                .collect(Collectors.toList());
    }


}
