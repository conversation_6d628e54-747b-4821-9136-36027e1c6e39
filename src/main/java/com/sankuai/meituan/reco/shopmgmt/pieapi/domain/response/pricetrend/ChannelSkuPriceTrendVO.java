package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pricetrend;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.PriceTrendConstants;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

@TypeDoc(
        description = "门店商品渠道价格趋势",
        authors = "hejunliang"
)
@ApiModel("门店商品渠道价格趋势")
@Data
@NoArgsConstructor
public class ChannelSkuPriceTrendVO {

    @FieldDoc(
            description = "数据日期列表"
    )
    @ApiModelProperty(name = "数据日期列表")
    private List<String> dateList;

    @FieldDoc(
            description = "渠道价格 (线下渠道取门店价，线上渠道取渠道价)"
    )
    @ApiModelProperty(name = "渠道价格")
    private List<PriceOfDateVO> priceList;

    @FieldDoc(
            description = "基准价 (线下渠道取线下基准价，线上渠道取线上基准价)"
    )
    @ApiModelProperty(name = "基准价")
    private List<PriceOfDateVO> cityBasePriceList;

    @FieldDoc(
            description = "市调价"
    )
    @ApiModelProperty(name = "市调价")
    private List<PriceOfDateVO> mrPriceList;

    public boolean isHasPriceTrend() {

        boolean isHasPriceTrend = CollectionUtils.isNotEmpty(this.priceList);
        boolean isHasCityBasePrice = CollectionUtils.isNotEmpty(this.cityBasePriceList);
        boolean isHasMrPriceTrend = CollectionUtils.isNotEmpty(this.mrPriceList);

        return (isHasPriceTrend || isHasCityBasePrice || isHasMrPriceTrend);
    }

    public static ChannelSkuPriceTrendVO empty() {

        LocalDate currentDay = LocalDate.now();
        String beginDate = DateUtils.format(currentDay.minusDays(PriceTrendConstants.STORE_SKU_PRICE_TREND_DEFAULT_QUERY_DAYS),
                DateUtils.YYYY_MM_DD);
        String endDate = DateUtils.format(currentDay.minusDays(1), DateUtils.YYYY_MM_DD);

        List<String> dateList = DateUtils.getBetweenDateList(beginDate, endDate, DateUtils.YYYY_MM_DD);

        ChannelSkuPriceTrendVO priceTrendVO = new ChannelSkuPriceTrendVO();
        priceTrendVO.setDateList(dateList);
        priceTrendVO.setPriceList(Collections.emptyList());
        priceTrendVO.setCityBasePriceList(Collections.emptyList());
        priceTrendVO.setMrPriceList(Collections.emptyList());

        return priceTrendVO;
    }
}


