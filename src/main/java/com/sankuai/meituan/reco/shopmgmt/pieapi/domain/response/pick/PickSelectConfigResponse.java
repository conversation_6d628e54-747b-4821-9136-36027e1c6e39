package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.AutoPickDoneConfig;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.ResvAutoPickDoneConfig;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.ChannelAutoPickDoneConfig;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.ResvChannelAutoPickDoneConfig;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.config.StoreFulfillShowConfigVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.ActionAfterLackConfig;
import io.swagger.annotations.ApiModel;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/2/4
 */
@TypeDoc(
        description = "拣货首页请求"
)
@ApiModel("拣货首页请求")
@Data
public class PickSelectConfigResponse {

    private ActionAfterLackConfig actionAfterLackConfig;

    private AutoPickDoneConfig autoPickDoneConfig;

    private StoreFulfillShowConfigVO showConfig;

    private ResvAutoPickDoneConfig resvAutoPickDoneConfig;

    private List<ChannelAutoPickDoneConfig> channelAutoPickDoneConfigList;

    private List<ResvChannelAutoPickDoneConfig> resvChannelAutoPickDoneConfigList;

    private int autoPickDoneSwitch = 1;
    private int resvAutoPickDoneSwitch = 1;
    private int autoPickDoneType = 0;
    private int resvAutoPickDoneType = 0;
}
