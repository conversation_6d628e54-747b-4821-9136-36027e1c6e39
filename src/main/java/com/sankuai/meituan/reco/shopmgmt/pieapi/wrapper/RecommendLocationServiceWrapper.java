package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Lists;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.warehouse.TradeLocationRecommendService;
import com.sankuai.shangou.logistics.warehouse.dto.RecommendLocationBatch;
import com.sankuai.shangou.logistics.warehouse.dto.request.QueryLocationListRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.shaded.com.google.common.util.concurrent.ListenableScheduledFuture;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-01-22
 * @email <EMAIL>
 */
@Slf4j
@Rhino
public class RecommendLocationServiceWrapper {

    @Resource
    private TradeLocationRecommendService tradeLocationRecommendService;

    @Degrade(rhinoKey = "RecommendLocationServiceWrapper.recommendLocations",
            fallBackMethod = "recommendLocationsFallback",
            timeoutInMilliseconds = 5000)
    @MethodLog(logRequest = true, logResponse = true)
    public List<RecommendLocationBatch> recommendLocations(QueryLocationListRequest request) {
        try {
            TResult<List<RecommendLocationBatch>> result = tradeLocationRecommendService.recommendLocations(request);
            if (!result.isSuccess()) {
                throw new ThirdPartyException("RecommendLocationServiceWrapper.recommendLocations error");
            }
            return result.getData();
        } catch (Exception e) {
            log.error("invoke RecommendLocationServiceWrapper.recommendLocations error", e);
            return Lists.newArrayList();
        }
    }


    public List<RecommendLocationBatch> recommendLocationsFallback(QueryLocationListRequest request) {
        log.warn("recommendLocationsFallback , request = {}", request);
        return Lists.newArrayList();
    }


}
