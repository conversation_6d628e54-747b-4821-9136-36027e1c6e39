package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Optional;

/**
 * <AUTHOR>
 * Email <EMAIL>
 * Date 2022/10/14 5:56 下午
 * Description
 */

@TypeDoc(
        description = "考勤异常申报详情查询请求"
)
@ApiModel("考勤异常申报详情查询请求")
@Data
public class AttendanceApprovalDetailRequest {

    @FieldDoc(
            description = "审批详情Id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "审批详情Id")
    private Long baseInfoId;

    public Optional<String> validate() {
        if (this.baseInfoId == null || this.baseInfoId <= 0) {
            return Optional.of("审批详情Id为空");
        }
        return Optional.empty();
    }
}
