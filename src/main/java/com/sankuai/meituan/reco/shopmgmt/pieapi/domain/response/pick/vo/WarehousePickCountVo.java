package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@TypeDoc(
        description = "商品数量和种类"
)
@Data
@ApiModel("商品数量和种类")
public class WarehousePickCountVo {

    @FieldDoc(
            description = "商品总数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "商品总数", required = true)
    private Integer goodsCount;

    @FieldDoc(
            description = "商品种类", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "商品种类", required = true)
    private Integer skuCount;
}
