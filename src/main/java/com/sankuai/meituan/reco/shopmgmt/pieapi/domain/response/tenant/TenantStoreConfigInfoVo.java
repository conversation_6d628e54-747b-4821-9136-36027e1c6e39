package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.tenant;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/5/22
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(
        description = "租户门店配置详情"
)
public class TenantStoreConfigInfoVo {

    @FieldDoc(
            description = "商品库管品模式：1-门店管品 2-连锁管品"
    )
    private Integer storeManageType;

    @FieldDoc(
            description = "门店配置信息"
    )
    private List<StoreConfigInfoVo> storeConfigInfoList;
}
