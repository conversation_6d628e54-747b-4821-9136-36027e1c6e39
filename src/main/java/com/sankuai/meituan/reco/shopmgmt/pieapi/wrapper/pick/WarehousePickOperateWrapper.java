package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.pick;

import com.sankuai.meituan.reco.pickselect.common.Status;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.Store;
import com.sankuai.meituan.reco.pickselect.thrift.wave.WarehouseOperateThriftService;
import com.sankuai.meituan.reco.pickselect.thrift.wave.request.*;
import com.sankuai.meituan.reco.pickselect.thrift.wave.response.CollectionWarehouseOrderResponse;
import com.sankuai.meituan.reco.pickselect.thrift.wave.response.PickWarehouseTasksResponse;
import com.sankuai.meituan.reco.pickselect.thrift.wave.response.SeedWarehouseTasksResponse;
import com.sankuai.meituan.reco.pickselect.thrift.wave.response.TakeWarehouseDispatchResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.StoreFulfillConfigWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class WarehousePickOperateWrapper {

    @Autowired
    private WarehouseOperateThriftService warehouseOperateThriftService;
    @Resource
    private StoreFulfillConfigWrapper storeFulfillConfigWrapper;

    @MethodLog(logResponse = true, logRequest = true)
    public void operateWarehouseCollection(WarehousePickCollectionOperateRequest request){
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        Long offlineStoreId = getOfflineStoreIdFromContext(identityInfo);
        Store store = storeFulfillConfigWrapper.getStore(offlineStoreId);
        CollectionWarehouseOrderRequest orderRequest=new CollectionWarehouseOrderRequest();
        orderRequest.setTenantId(store.getTenantId());
        orderRequest.setEmpowerStoreId(store.getOfflineStoreId());
        WarehouseOperatorDTO operatorDTO=new WarehouseOperatorDTO();
        operatorDTO.setAccountId(identityInfo.getUser().getAccountId());
        operatorDTO.setAccountName(identityInfo.getUser().getAccountName());
        operatorDTO.setEmployeeName(identityInfo.getUser().getOperatorName());
        operatorDTO.setEmployeeId(identityInfo.getUser().getEmployeeId());
        orderRequest.setOperator(operatorDTO);
        orderRequest.setOutWarehouseOrderList(request.getOutWarehouseOrderList());
        orderRequest.setTaskNum(request.getTaskNum());
        orderRequest.setOrderType(Optional.ofNullable(request.getOrderType()).orElse(1));
        CollectionWarehouseOrderResponse response = warehouseOperateThriftService.collectionWarehouseOrder(orderRequest);
        if(response.getStatus().getCode().equals(Status.FAIL.getCode())){
            throw new CommonRuntimeException(response.getStatus().getMessage());
        }
    }

    @MethodLog(logResponse = true, logRequest = true)
    public void operateWarehousePickReceive(WarehousePickReceiveRequest request) throws TException {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        Long offlineStoreId = getOfflineStoreIdFromContext(identityInfo);
        TakeWarehouseDispatchRequest dispatchRequest=new TakeWarehouseDispatchRequest();
        dispatchRequest.setEmpowerStoreId(offlineStoreId);
        WarehouseOperatorDTO operatorDTO=new WarehouseOperatorDTO();
        operatorDTO.setAccountId(identityInfo.getUser().getAccountId());
        operatorDTO.setAccountName(identityInfo.getUser().getAccountName());
        operatorDTO.setEmployeeName(identityInfo.getUser().getOperatorName());
        dispatchRequest.setOperator(operatorDTO);
        dispatchRequest.setOrderId(request.getTaskOrderIdList().get(0));
        TakeWarehouseDispatchResponse response = warehouseOperateThriftService.takeDispatchWorkOrder(dispatchRequest);
        if(response.getStatus().getCode().equals(Status.FAIL.getCode())){
            throw new CommonRuntimeException(response.getStatus().getMessage());
        }
    }

    @MethodLog(logResponse = true, logRequest = true)
    public PickWarehouseTasksResponse operateWarehousePickComplete(WarehousePickCompleteRequest request) throws TException {
        PickWarehouseTasksRequest tasksRequest=new PickWarehouseTasksRequest();
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        Long offlineStoreId = getOfflineStoreIdFromContext(identityInfo);
        tasksRequest.setEmpowerStoreId(offlineStoreId);
        tasksRequest.setOrderId(request.getTaskOrderId());
        tasksRequest.setPickedNum(request.getPickNum());
        tasksRequest.setSkuId(request.getSkuId());
        tasksRequest.setPickTaskId(request.getPickTaskId());
        WarehouseOperatorDTO operatorDTO=new WarehouseOperatorDTO();
        operatorDTO.setAccountId(identityInfo.getUser().getAccountId());
        operatorDTO.setAccountName(identityInfo.getUser().getAccountName());
        operatorDTO.setEmployeeName(identityInfo.getUser().getOperatorName());
        tasksRequest.setOperator(operatorDTO);
        setPackingSpecUnit(request.getPackingSpec(), tasksRequest);
        PickWarehouseTasksResponse response = warehouseOperateThriftService.pickTasks(tasksRequest);
        if(response.getStatus().getCode().equals(Status.FAIL.getCode())){
            throw new CommonRuntimeException(response.getStatus().getMessage());
        }
        return response;
    }

    private void setPackingSpecUnit(SkuPackingSpecRequest skuPackingSpecRequest, PickWarehouseTasksRequest tasksRequest) {
        if(skuPackingSpecRequest != null){
            PackingSpecUnitRequest packingSpecUnitRequest = new PackingSpecUnitRequest();
            packingSpecUnitRequest.setPackingSpecUnit(skuPackingSpecRequest.getPackingSpecUnit());
            packingSpecUnitRequest.setPackingSpecRatio(skuPackingSpecRequest.getPackingSpecRatio());
            packingSpecUnitRequest.setBasicUnit(skuPackingSpecRequest.getBasicUnit());
            tasksRequest.setPackingSpec(packingSpecUnitRequest);
        }
    }

    @MethodLog(logResponse = true, logRequest = true)
    public void updateWarehousePickCompleteNum(WarehousePickCompleteRequest request) throws TException {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        Long offlineStoreId = getOfflineStoreIdFromContext(identityInfo);
        PickWarehouseTasksRequest tasksRequest=new PickWarehouseTasksRequest();
        tasksRequest.setPickedNum(request.getPickNum());
        tasksRequest.setOrderId(request.getTaskOrderId());
        tasksRequest.setSkuId(request.getSkuId());
        tasksRequest.setPickTaskId(request.getPickTaskId());
        tasksRequest.setEmpowerStoreId(offlineStoreId);
        WarehouseOperatorDTO operatorDTO=new WarehouseOperatorDTO();
        operatorDTO.setAccountId(identityInfo.getUser().getAccountId());
        operatorDTO.setAccountName(identityInfo.getUser().getAccountName());
        operatorDTO.setEmployeeName(identityInfo.getUser().getOperatorName());
        tasksRequest.setOperator(operatorDTO);
        setPackingSpecUnit(request.getPackingSpec(), tasksRequest);
        PickWarehouseTasksResponse response = warehouseOperateThriftService.modifyPickTasks(tasksRequest);
        if(response.getStatus().getCode().equals(Status.FAIL.getCode())){
            throw new CommonRuntimeException(response.getStatus().getMessage());
        }
    }

    @MethodLog(logResponse = true, logRequest = true)
    public CommonResponse<Void> operateWarehouseSortingPick(WarehouseSortingPickOperateRequest request){
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        Long offlineStoreId = getOfflineStoreIdFromContext(identityInfo);
        SeedWarehouseTasksRequest tasksRequest=new SeedWarehouseTasksRequest();
        tasksRequest.setEmpowerStoreId(offlineStoreId);
        tasksRequest.setOrderId(request.getTaskOrderId());
        tasksRequest.setSkuId(request.getSkuId());
        tasksRequest.setPickTaskId(request.getPickTaskId());
        WarehouseOperatorDTO operatorDTO=new WarehouseOperatorDTO();
        operatorDTO.setAccountId(identityInfo.getUser().getAccountId());
        operatorDTO.setAccountName(identityInfo.getUser().getAccountName());
        operatorDTO.setEmployeeName(identityInfo.getUser().getOperatorName());
        tasksRequest.setOperator(operatorDTO);
        tasksRequest.setNum(request.getNum());

        try {
            SeedWarehouseTasksResponse response = warehouseOperateThriftService.seedTasks(tasksRequest);
            if(!response.getStatus().getCode().equals(Status.SUCCESS.getCode())){
                return CommonResponse.fail(response.getStatus().getCode(),response.getStatus().getMessage());
            }
        }catch (CommonRuntimeException e){
            log.error("operateWarehouseSortingPick error request:{}",request,e);
            return CommonResponse.fail(1,e.getMessage());
        } catch (TException e) {
            log.error("operateWarehouseSortingPick error request:{}",request,e);
            return CommonResponse.fail(1,"分拣失败");
        }
        return CommonResponse.success(null);
    }

    public Long getOfflineStoreIdFromContext(IdentityInfo identityInfo) {
        List<Long> storeIdList = identityInfo.getStoreIdList();
        if (CollectionUtils.isNotEmpty(storeIdList) && storeIdList.size() == 1) {
            return storeIdList.get(0);
        } else {
            log.error("PickWrapper.homepage()不支持多门店，，storeIdList={}", storeIdList);
            throw new CommonRuntimeException("不支持多门店");
        }
    }

}
