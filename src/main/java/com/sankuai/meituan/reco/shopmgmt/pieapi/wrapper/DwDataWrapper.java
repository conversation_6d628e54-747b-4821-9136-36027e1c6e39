package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.dianping.lion.Environment;
import com.google.common.collect.ImmutableMap;
import com.meituan.linz.boot.exception.ServiceRpcException;
import com.meituan.linz.boot.util.TimeUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.PowerApiConstant;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PoiSgRealTimeDataDto;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.DateUtils;
import com.sankuai.waimai.dws.protocol.openapi.OpenapiQueryService;
import com.sankuai.waimai.dws.protocol.structs.DWSParam;
import com.sankuai.waimai.dws.protocol.structs.DWSResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 数仓接口
 *
 * <AUTHOR>
 * @since 2023/02/01
 */
@Service
@Slf4j
public class DwDataWrapper {

    /**
     * 总营业额
     */
    private static final String OPEN_AMT_GMV_KEY = "openAmtGmv";

    /**
     * 总营业额-环比
     */
    private static final String OPEN_AMT_GMV_CR_KEY = "openAmtGmvCr";

    /**
     * 商品销售额
     */
    private static final String PROD_AMT_KEY = "prodAmt";

    /**
     * 商品销售额-环比
     */
    private static final String PROD_AMT_CR_KEY = "prodAmtCr";

    /**
     * 销售收入
     */
    private static final String ORD_AMT_KEY = "ordAmt";

    /**
     * 销售收入-环比
     */
    private static final String ORD_AMT_CR_KEY = "ordAmtCr";

    /**
     * 预计收入
     */
    private static final String TO_PREDICT_INCOME_KEY = "toPredictIncome";

    /**
     * 预计收入-环比
     */
    private static final String TO_PREDICT_INCOME_CR_KEY = "toPredictIncomeCr";

    /**
     * 有效订单量
     */
    private static final String EFF_ORD_CNT_KEY = "effOrdCnt";

    /**
     * 有效订单量-环比
     */
    private static final String EFF_ORD_CNT_CR_KEY = "effOrdCntCr";

    /**
     * 履约超时率
     */
    private static final String ORD_TIMEOUT_RATE_KEY = "ordTimeoutRate";

    /**
     * 履约超时率-环比
     */
    private static final String ORD_TIMEOUT_RATE_CR_KEY = "ordTimeoutRateCr";

    @Autowired
    private OpenapiQueryService.Iface openapiQueryService;

    /**
     * 查询数仓门店实时数据
     * 当日无交易数据，返回默认数据，指标都为0
     *
     * @param poiIds 门店id
     * @return
     */
    public PoiSgRealTimeDataDto queryRealTimeData(List<Long> poiIds) {
        String today = DateUtils.format(new Date(), DateUtils.YYYYMMDD);
        LocalDate todayLd = LocalDate.parse(today, TimeUtils.YYYYMMDD_FORMATTER);
        String yesterday = todayLd.minusDays(1).format(TimeUtils.YYYYMMDD_FORMATTER);
        String lastDay = todayLd.minusWeeks(1).format(TimeUtils.YYYYMMDD_FORMATTER);

        DWSParam queryParam = new DWSParam();
        queryParam.setAppKey(Environment.getAppName());
        queryParam.setToken(PowerApiConstant.HomePageProject.REQ_ACCESS);
        queryParam.setAppCode(PowerApiConstant.HomePageProject.APP_CODE);
        queryParam.setApiCode("b_service_qnh_home_board_rt_data");
        queryParam.setSqlParam(new ImmutableMap.Builder<String,String>()
                .put("beginDate", today)
                .put("endDate", today)
                .put("beginDateCr", yesterday)
                .put("endDateCr", yesterday)
                .put("beginDateLast", lastDay)
                .put("endDateLast", lastDay)
                .put("poiId",  StringUtils.join(poiIds, ','))
                .put("dateType", "r")
                .build());
        try {
            DWSResponse response = openapiQueryService.query(queryParam);

            if (response == null || response.getCode() != 0) {
                throw new ServiceRpcException("查询数仓门店实时数据失败");
            }

            if (response.getData() != null && CollectionUtils.isNotEmpty(response.getData().getDataList())) {
                return buildPoiSgRealTimeDataDto(response.getData().getDataList().get(0));
            }
        } catch (TException e) {
            log.error("查询数仓门店实时数据异常 poiId:{}", poiIds);
            throw new ServiceRpcException("查询数仓门店实时数据异常", e);
        }
        return buildDefaultPoiSgRealTimeDataDto();
    }

    private PoiSgRealTimeDataDto buildPoiSgRealTimeDataDto(Map<String, String> dataMap) {
        PoiSgRealTimeDataDto timeDataDto = new PoiSgRealTimeDataDto();
        // 总营业额
        if (StringUtils.isNotBlank(dataMap.get(OPEN_AMT_GMV_KEY))) {
            timeDataDto.setOpenAmtGtv(Double.valueOf(dataMap.get(OPEN_AMT_GMV_KEY)));
        }
        if (StringUtils.isNotBlank(dataMap.get(OPEN_AMT_GMV_CR_KEY))) {
            timeDataDto.setOpenAmtGtvCr(Double.valueOf(dataMap.get(OPEN_AMT_GMV_CR_KEY)));
        }
        // 商品销售额
        if (StringUtils.isNotBlank(dataMap.get(PROD_AMT_KEY))) {
            timeDataDto.setProdAmt(Double.valueOf(dataMap.get(PROD_AMT_KEY)));
        }
        if (StringUtils.isNotBlank(dataMap.get(PROD_AMT_CR_KEY))) {
            timeDataDto.setProdAmtCr(Double.valueOf(dataMap.get(PROD_AMT_CR_KEY)));
        }
        // 销售收入
        if (StringUtils.isNotBlank(dataMap.get(ORD_AMT_KEY))) {
            timeDataDto.setOrdAmt(Double.valueOf(dataMap.get(ORD_AMT_KEY)));
        }
        if (StringUtils.isNotBlank(dataMap.get(ORD_AMT_CR_KEY))) {
            timeDataDto.setOrdAmtCr(Double.valueOf(dataMap.get(ORD_AMT_CR_KEY)));
        }
        // 预计收入
        if (StringUtils.isNotBlank(dataMap.get(TO_PREDICT_INCOME_KEY))) {
            timeDataDto.setToPredictIncome(Double.valueOf(dataMap.get(TO_PREDICT_INCOME_KEY)));
        }
        if (StringUtils.isNotBlank(dataMap.get(TO_PREDICT_INCOME_CR_KEY))) {
            timeDataDto.setToPredictIncomeCr(Double.valueOf(dataMap.get(TO_PREDICT_INCOME_CR_KEY)));
        }
        // 有效订单数
        if (StringUtils.isNotBlank(dataMap.get(EFF_ORD_CNT_KEY))) {
            timeDataDto.setFinOrdNum(Integer.valueOf(dataMap.get(EFF_ORD_CNT_KEY)));
        }
        if (StringUtils.isNotBlank(dataMap.get(EFF_ORD_CNT_CR_KEY))) {
            timeDataDto.setFinOrdNumCr(Double.valueOf(dataMap.get(EFF_ORD_CNT_CR_KEY)));
        }
        // 整单超时率
        if (StringUtils.isNotBlank(dataMap.get(ORD_TIMEOUT_RATE_KEY))) {
            timeDataDto.setPerformanceOvertimeRate(Double.valueOf(dataMap.get(ORD_TIMEOUT_RATE_KEY)));
        }
        if (StringUtils.isNotBlank(dataMap.get(ORD_TIMEOUT_RATE_CR_KEY))) {
            timeDataDto.setPerformanceOvertimeRateCr(Double.valueOf(dataMap.get(ORD_TIMEOUT_RATE_CR_KEY)));
        }
        return timeDataDto;
    }

    private PoiSgRealTimeDataDto buildDefaultPoiSgRealTimeDataDto() {
        PoiSgRealTimeDataDto timeDataDto = new PoiSgRealTimeDataDto();
        timeDataDto.setOpenAmtGtv(0d);
        timeDataDto.setProdAmt(0d);
        timeDataDto.setOrdAmt(0d);
        timeDataDto.setToPredictIncome(0d);
        timeDataDto.setFinOrdNum(0);
        timeDataDto.setPerformanceOvertimeRate(0d);
        return timeDataDto;
    }
}