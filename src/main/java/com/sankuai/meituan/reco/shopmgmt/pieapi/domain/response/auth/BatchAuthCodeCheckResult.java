package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.auth;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

@Data
@ApiModel("批量查询权限码是否有权限响应")
public class BatchAuthCodeCheckResult {

    @ApiModelProperty(value = "权限码检查结果,key: authCode,value: 0(没有权限)，1(有权限)")
    private Map<String, Integer> authCodeCheckResult;
}
