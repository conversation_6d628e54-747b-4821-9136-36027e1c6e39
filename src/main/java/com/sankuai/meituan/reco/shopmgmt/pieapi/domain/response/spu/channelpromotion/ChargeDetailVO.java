package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.channelpromotion;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelpromotion.dto.ChargeDetailDTO;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/9/12
 */
@TypeDoc(
        description = "活动折扣信息"
)
@Data
public class ChargeDetailVO {
    /**
     * 美团补贴
     */
    private String mtChargeAmount;
    /**
     * 商家补贴
     */
    private String poiChargeAmount;
    /**
     * 合作方补贴
     */
    private String partnerChargeAmount;

    public static ChargeDetailVO fromDTO(ChargeDetailDTO dto) {
        ChargeDetailVO vo = new ChargeDetailVO();
        vo.setMtChargeAmount(dto.getMtChargeAmount());
        vo.setPoiChargeAmount(dto.getPoiChargeAmount());
        vo.setPartnerChargeAmount(dto.getPartnerChargeAmount());
        return vo;
    }
}
