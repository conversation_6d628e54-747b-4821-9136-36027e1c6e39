package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ReviewRecallFaliedDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.review.BatchRecallProductReviewResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * @description: 失败数据
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-03-11
 **/
@TypeDoc(
        description = "失败数据"
)
@Data
@ApiModel("失败数据")
public class ReviewRecallFailedVO {


    @FieldDoc(
            description = "失败原因", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "失败原因")
    private String reason;

    @FieldDoc(
            description = "提报申请id", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "提报申请id")
    private Long id;


    public ReviewRecallFaliedDTO buildReviewRecallFaliedDTO(){

        ReviewRecallFaliedDTO reviewRecallFaliedDTO = new ReviewRecallFaliedDTO();
        reviewRecallFaliedDTO.setReason(this.getReason());
        reviewRecallFaliedDTO.setId(this.getId());
        return reviewRecallFaliedDTO;

    }

    public ReviewRecallFailedVO buildReviewRecallFailedVO(ReviewRecallFaliedDTO reviewRecallFaliedDTO){

        if (StringUtils.isNotEmpty(reviewRecallFaliedDTO.getReason())){
            this.setReason(reviewRecallFaliedDTO.getReason().trim());
        }
        this.setId(reviewRecallFaliedDTO.getId());
        return this;

    }

    public static ReviewRecallFailedVO buildReviewRecallFailedVO(BatchRecallProductReviewResponse.RecallFailItem recallFailItem) {
        ReviewRecallFailedVO reviewRecallFailedVO = new ReviewRecallFailedVO();
        reviewRecallFailedVO.setId(recallFailItem.getReviewId());
        if (StringUtils.isNotEmpty(recallFailItem.getReason())){
            reviewRecallFailedVO.setReason(recallFailItem.getReason().trim());
        }
        return reviewRecallFailedVO;
    }
}
