package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.management;

import java.util.Map;

import com.meituan.servicecatalog.api.annotations.FieldDoc;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/4/19
 */
@Data
public class ConfigContent {

    @FieldDoc(
            description = "配置id"
    )
    public Integer configId;

    @FieldDoc(
            description = "配置内容"
    )
    public String configContent;


    @FieldDoc(
            description = "配置内容Map"
    )
    public Map<String,Object> configContentMap;
}
