package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@TypeDoc(
		description = "根据账户批量获取门店信息请求"
)
@Data
@ApiModel("根据账户批量获取门店信息请求")
public class GetStoreByIdsRequest {
	@FieldDoc(
			description = "租户id"
	)
	@ApiModelProperty(value = "租户id", required = true)
	@NotNull
	private Long tenantId;
}
