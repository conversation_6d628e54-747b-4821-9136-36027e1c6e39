package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

@TypeDoc(
        description = "价格评估指标参考价查询请求"
)
@ApiModel("价格评估指标参考价查询请求")
@Data
public class PriceEffectIndexReferencePriceQueryRequest {

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店id", required = true)
    private Long storeId;

    @FieldDoc(
            description = "sku编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "sku编码", required = true)
    private List<String> skuIds;

    public void validate() {

        if (this.storeId == null) {
            throw new CommonLogicException("门店id不能为空", ResultCode.CHECK_PARAM_ERR);
        }

        if (CollectionUtils.isEmpty(this.skuIds)) {
            throw new CommonLogicException("sku编码不能为空", ResultCode.CHECK_PARAM_ERR);
        }
    }
}
