package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.tenant;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.order.platform.common.SelfCheckable;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;


@Data
@TypeDoc(
        description = "购买详情"
)
public class PurchaseDetailVo implements SelfCheckable {
    
    @FieldDoc(description = "服务类型")
    @NotNull(message = "服务类型不能为空")
    public Integer serviceType;
    
    @FieldDoc(description = "子服务类型")
    @NotNull(message = "子服务类型不能为空")
    public Integer subType;
    
    @FieldDoc(description = "购买数量")
    @NotNull(message = "购买数量不能为空")
    public Integer unitNum;
    
    @FieldDoc(description = "价格，单位：分")
    @NotNull(message = "价格不能为空")
    public Integer amount;

    @FieldDoc(description = "绑定门店信息")
    public List<BindStoreVo> bindStoreList;
}
