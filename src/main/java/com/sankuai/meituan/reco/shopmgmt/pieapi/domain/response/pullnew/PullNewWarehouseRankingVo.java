package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/9/22
 */

@TypeDoc(
        description = "地推排行榜信息"
)
@Data
@ApiModel("地推排行榜信息")
@NoArgsConstructor
@AllArgsConstructor
public class PullNewWarehouseRankingVo {

    public String itemRank;

    public String itemName;

    public String itemValue;

    public Long itemId;

    public String extraInfo;
}
