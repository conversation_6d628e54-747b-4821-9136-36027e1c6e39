package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.ApprovalVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.HireApprovalDetailVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.ResignApplyVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.ResignApprovalDetailVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/10/19 15:00
 **/
@ApiModel("查询待申请列表返回")
@TypeDoc(description = "查询待申请列表返回")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryResignApplyDetailResp {

    @FieldDoc(description = "员工姓名")
    @ApiModelProperty("员工姓名")
    private ResignApprovalDetailVO resignApplyInfo;

    @FieldDoc(
            description = "审批信息list"
    )
    private List<ApprovalVO> approvalList;

    @FieldDoc(description = "迟到时间点（上班打卡时有值）")
    @ApiModelProperty("迟到时间点（上班打卡时有值）")
    private Integer approvalStatus;

    @FieldDoc(description = "早退时间点（下班打卡时有值）")
    @ApiModelProperty("早退时间点（下班打卡时有值）")
    private boolean canApproval;

}
