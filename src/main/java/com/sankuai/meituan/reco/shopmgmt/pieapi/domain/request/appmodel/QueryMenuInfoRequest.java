package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.appmodel;

import javax.validation.constraints.NotNull;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 查询app模块菜单信息请求
 *
 * <AUTHOR>
 * @since 2021/7/5
 */
@TypeDoc(
        description = "查询app模块菜单信息请求"
)
@Data
@ApiModel("查询app模块菜单信息请求")
public class QueryMenuInfoRequest {
    @FieldDoc(
            description = "菜单code", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "菜单code")
    @NotNull(message = "菜单code不能为空")
    private String menuCode;

    @FieldDoc(
            description = "菜单tag"
    )
    @ApiModelProperty(value = "菜单tag")
    private String tag;
}