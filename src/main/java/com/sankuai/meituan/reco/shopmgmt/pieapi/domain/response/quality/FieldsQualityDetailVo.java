package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.quality;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.product.dto.FieldsQualityDetailDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/9/20
 */
@TypeDoc(
        name = "字段维度质量分详情",
        description = "字段维度质量分详情"
)
@Data
public class FieldsQualityDetailVo {

    @FieldDoc(
            description = "字段标识"
    )
    @ApiModelProperty(name = "字段标识")
    private String fields;

    @FieldDoc(
            description = "1-spu,2-sku"
    )
    @ApiModelProperty(name = "1-spu,2-sku")
    private int fieldsType;

    @FieldDoc(
            description = "属性名称列表"
    )
    @ApiModelProperty(name = "属性名称列表")
    private String fieldsName;

    @FieldDoc(
            description = "问题列表描述"
    )
    private List<FieldsDetailVo> fieldsQualityList;

    public static FieldsQualityDetailVo of(FieldsQualityDetailDto dto) {
        FieldsQualityDetailVo vo = new FieldsQualityDetailVo();
        if (Objects.nonNull(dto.getQualityFieldsType())) {
            vo.setFields(dto.getQualityFieldsType().getFields());
            vo.setFieldsName(dto.getQualityFieldsType().getFieldsName());
            vo.setFieldsType(dto.getQualityFieldsType().getDimensionType());
        }
        vo.setFieldsQualityList(Fun.map(dto.getQualityDetailList(), FieldsDetailVo::of));

        return vo;
    }
}
