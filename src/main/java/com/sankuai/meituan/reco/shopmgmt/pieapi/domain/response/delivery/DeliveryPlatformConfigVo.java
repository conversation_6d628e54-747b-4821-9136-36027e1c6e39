package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.delivery;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;

/**
 * @Description: 聚合配送门店平台配置
 * @Author: yuanyu09
 * @Date: 2022/12/12
 */

@Data
@ApiModel("聚合配送门店平台配置信息")
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DeliveryPlatformConfigVo {

    public static final DeliveryPlatformConfigVo EMPTY =
            DeliveryPlatformConfigVo.builder().platformCode(DeliveryPlatformEnum.MALT_FARM_DELIVERY_PLATFORM.getCode())
                    .status(NumberUtils.INTEGER_ZERO).redirectUrl(StringUtils.EMPTY).build();

    @FieldDoc(
            description = "平台code", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "平台code", required = true)
    private Integer platformCode;

    @FieldDoc(
            description = "平台是否开启", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "平台是否开启", required = true)
    private Integer status;

    @FieldDoc(
            description = "跳转url", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "跳转url", required = true)
    private String redirectUrl;
}
