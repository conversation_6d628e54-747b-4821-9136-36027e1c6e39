package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.wavepick.converter;

import com.sankuai.meituan.reco.pickselect.thrift.BatchInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.BatchInfoVO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public abstract class WarehousePickItemSkuBatchConverter {
    public abstract BatchInfoVO convert2Vo(BatchInfo batchInfo);
}
