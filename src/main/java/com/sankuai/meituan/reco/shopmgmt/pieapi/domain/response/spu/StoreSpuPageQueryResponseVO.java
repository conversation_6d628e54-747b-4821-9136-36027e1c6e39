package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import com.sankuai.meituan.shangou.empower.ocms.client.common.dto.PageInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.enums.ChannelAuditStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.client.product.enums.ChannelNormAuditStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.client.product.enums.ChannelSpuStopSellingStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.client.product.response.StoreSpuPageQueryResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.PlatformSoldOutInfoDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreSpuKeyDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.enums.DiffCompareTypeEnum;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.spu.NonErpPageQueryStoreSpuResponse;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.enums.ChannelEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.assertj.core.util.Lists;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Title: StoreSpuPageQueryResponseVO
 * @Description: 门店商品SPU分页查询响应结果
 * @Author: zhaolei12
 * @Date: 2020/4/17 6:21 下午
 */
@TypeDoc(
        description = "门店商品SPU分页查询响应结果",
        authors = {"zhaolei12"}
)
@Data
@ApiModel("门店商品SPU分页查询响应结果")
public class StoreSpuPageQueryResponseVO {

    @FieldDoc(
            description = "商品信息列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品信息列表", required = true)
    private List<StoreSpuVO> storeSpuList;

    @FieldDoc(
            description = "分页信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "分页信息", required = true)
    private PageInfoVO pageInfo;

    public static StoreSpuPageQueryResponseVO convertResponse(StoreSpuPageQueryResponse response,
                                                              Map<StoreSpuKeyDTO, PlatformSoldOutInfoDTO> storeSpuAbnormalInfoMap) {
        StoreSpuPageQueryResponseVO storeSpuPageQueryResponseVO = new StoreSpuPageQueryResponseVO();
        storeSpuPageQueryResponseVO.setStoreSpuList(StoreSpuVO.ofDTOList(response.getStoreSpuList(), storeSpuAbnormalInfoMap));
        storeSpuPageQueryResponseVO.setPageInfo(convert(response.getPageInfoDTO()));
        return storeSpuPageQueryResponseVO;
    }

    public static StoreSpuPageQueryResponseVO convertResponseForBiz(NonErpPageQueryStoreSpuResponse response,
                                                                    Map<StoreSpuKeyDTO, PlatformSoldOutInfoDTO> storeSpuAbnormalInfoMap) {
        StoreSpuPageQueryResponseVO storeSpuPageQueryResponseVO = new StoreSpuPageQueryResponseVO();
        storeSpuPageQueryResponseVO.setStoreSpuList(StoreSpuVO.ofBizDTOList(response.getStoreSpuList(), storeSpuAbnormalInfoMap));
        storeSpuPageQueryResponseVO.setPageInfo(convert(response.getPageInfoDTO()));
        return storeSpuPageQueryResponseVO;
    }

    private static PageInfoVO convert(PageInfoDTO pageInfoDTO) {
        if (Objects.isNull(pageInfoDTO)) {
            return null;
        }
        PageInfoVO pageInfoVO = new PageInfoVO();
        pageInfoVO.setPage(pageInfoDTO.getPage());
        pageInfoVO.setSize(pageInfoDTO.getSize());
        pageInfoVO.setTotalPage(pageInfoDTO.getTotalPage());
        pageInfoVO.setTotalSize(pageInfoDTO.getTotal());
        return pageInfoVO;
    }

    private static PageInfoVO convert(com.sankuai.meituan.shangou.empower.productbiz.client.dto.PageInfoDTO pageInfoDTO) {
        if (Objects.isNull(pageInfoDTO)) {
            return null;
        }
        PageInfoVO pageInfoVO = new PageInfoVO();
        pageInfoVO.setPage(pageInfoDTO.getPage());
        pageInfoVO.setSize(pageInfoDTO.getSize());
        pageInfoVO.setTotalPage(pageInfoDTO.getTotalPage());
        pageInfoVO.setTotalSize(Optional.ofNullable(pageInfoDTO.getTotal()).map(Long::intValue).orElse(0));
        return pageInfoVO;
    }

    public StoreSpuPageQueryResponseVO fillInCitySaleAmount(Map<String, Integer> salesAmount) {
        for (StoreSpuVO storeSpuVO : storeSpuList) {
            if (salesAmount.containsKey(storeSpuVO.getSpuId())) {
                storeSpuVO.setCityMonthSaleAmount(salesAmount.get(storeSpuVO.getSpuId()));
            }
        }
        return this;
    }

    public static void addProductPropertyConsistencyInfo(List<StoreSpuVO> storeSpuVOS,
                                                         Map<Long, Map<String,List<Integer>>> diffCompareStoreSpuTypeMap) {
        if (CollectionUtils.isEmpty(storeSpuVOS)) {
            return;
        }

        storeSpuVOS.forEach(storeSpuVO -> {
            int problemCount = 0;
            if(MapUtils.isNotEmpty(diffCompareStoreSpuTypeMap)
                    && MapUtils.isNotEmpty(diffCompareStoreSpuTypeMap.get(storeSpuVO.getStore().getStoreId()))){
                List<Integer> problemTypes = Optional.ofNullable(diffCompareStoreSpuTypeMap.get(storeSpuVO.getStore()
                                .getStoreId()).get(storeSpuVO.getSpuId())).orElse(Lists.newArrayList()).stream()
                        .filter(Objects::nonNull)
                        .filter(type -> !DiffCompareTypeEnum.SGP_SPU_MISS.getType().equals(type))
                        .collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(problemTypes)){
                    storeSpuVO.setIsSpuInfoUnEqual(true);
                    problemTypes.sort(Comparator.comparingInt(o -> o.intValue()));
                    storeSpuVO.setUnEqualTypeList(problemTypes);
                    problemCount++;
                }
            }


            if (CollectionUtils.isNotEmpty(storeSpuVO.getChannelSpuList())) {
                // 质量审核
                problemCount += storeSpuVO.getChannelSpuList().stream()
                        .filter(channelSpuVO -> channelSpuVO.getChannelId().equals(ChannelEnum.MEITUAN.getCode())
                                && Objects.nonNull(channelSpuVO.getAuditStatus()))
                        .anyMatch(channelSpuVO -> ChannelAuditStatusEnum.getAllRejectStatusCodes().contains(channelSpuVO.getAuditStatus())
                               || channelSpuVO.getAuditStatus().equals(ChannelAuditStatusEnum.AUDITING_FOR_BEFORE_RELEASE.getCode()))? 1 : 0;

                // 合规审核
                problemCount += storeSpuVO.getChannelSpuList().stream()
                        .filter(channelSpuVO -> channelSpuVO.getChannelId().equals(ChannelEnum.MEITUAN.getCode())
                                && Objects.nonNull(channelSpuVO.getNormAuditStatus()))
                        .anyMatch(channelSpuVO -> ChannelNormAuditStatusEnum.getAllRejectStatusCodes().contains(channelSpuVO.getNormAuditStatus())
                                || channelSpuVO.getNormAuditStatus().equals(ChannelNormAuditStatusEnum.AUDITING_FOR_BEFORE_RELEASE.getCode()))? 1 : 0;

                // 资质缺失 or 必填信息缺失
                problemCount += storeSpuVO.getChannelSpuList().stream()
                        .filter(channelSpuVO -> channelSpuVO.getChannelId().equals(ChannelEnum.MEITUAN.getCode())
                                && Objects.nonNull(channelSpuVO.getStopSellingStatus()))
                        .anyMatch(channelSpuVO -> channelSpuVO.getStopSellingStatus().equals(ChannelSpuStopSellingStatusEnum.INCOMPLETE.getCode())
                                || channelSpuVO.getStopSellingStatus().equals(ChannelSpuStopSellingStatusEnum.UNQUALIFIED.getCode())) ? 1 : 0;

                // 资质缺失 && 必填信息缺失  >>> 错误数 +2
                problemCount += storeSpuVO.getChannelSpuList().stream()
                        .filter(channelSpuVO -> channelSpuVO.getChannelId().equals(ChannelEnum.MEITUAN.getCode())
                                && Objects.nonNull(channelSpuVO.getStopSellingStatus()))
                        .anyMatch(channelSpuVO -> channelSpuVO.getStopSellingStatus().equals(ChannelSpuStopSellingStatusEnum.INCOMPLETEANDUNQUALIFIED.getCode())) ? 2 : 0;

                // 平台下架商品
                problemCount += storeSpuVO.getChannelSpuList().stream()
                        .filter(channelSpuVO -> channelSpuVO.getChannelId().equals(ChannelEnum.MEITUAN.getCode())
                                && Objects.nonNull(channelSpuVO.getPlatformSoldOutInfo()) )
                        .anyMatch(channelSpuVO -> channelSpuVO.getPlatformSoldOutInfo().isPlatformSoldOutFlag()) ? 1 : 0;
            }
            storeSpuVO.setProblemCount(problemCount);
        });
    }

}
