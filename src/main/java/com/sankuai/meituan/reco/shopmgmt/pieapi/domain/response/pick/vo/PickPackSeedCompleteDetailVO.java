package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description:
 * @Auther: nifei
 * @Date: 2023/8/21 16:21
 */
@TypeDoc(
        description = "已分拣商品列表信息"
)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PickPackSeedCompleteDetailVO {
    @FieldDoc(
            description = "箱码"
    )
    private String huCode;

    @FieldDoc(
            description = "箱中对应的已分拣item列表"
    )
    private List<WarehousePackItemVO> itemList;
}
