package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.tenant;

import com.sankuai.meituan.shangou.empower.productbiz.client.dto.HsPurchasePlatformInfoDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 品牌商采购平台信息
 * <AUTHOR>
 * @since 2023/9/5
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class HsPurchasePlatformInfoVO {

    private String shopName;

    private String shopId;

    private Integer areaId;

    private String areaName;

    public static HsPurchasePlatformInfoVO of(HsPurchasePlatformInfoDTO dto) {
        HsPurchasePlatformInfoVO vo = new HsPurchasePlatformInfoVO();
        vo.setShopId(dto.getShopId());
        vo.setShopName(dto.getShopName());
        vo.setAreaId(dto.getAreaId());
        vo.setAreaName(dto.getAreaName());

        return vo;
    }
}
