package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.assistant;

import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2021/1/28 17:17
 */
@Getter
@Setter
public class SpuTaskVo {

    /**
     * 任务ID.
     */
    private Long taskId;

    /**
     * 商品SPUID.
     */
    private String spuId;

    /**
     * 商品名称.
     */
    private String spuName;

    /**
     * 商品规格.
     */
    private String spec;

    /**
     * 销量数据.
     */
    private Integer sales;


    /**
     * 商品价格.
     */
    private String price;


    /**
     * 商品图片信息(主图).
     */
    private String picUrl;


    /**
     * 商品被差评的次数.
     */
    private Integer badCaseNums;

    /**
     * 任务创建时间.
     */
    private String createDate;

    /**
     * 商品下架时间.
     */
    private String spuOffSaleTime;


    /**
     * 商品属性列表.
     */
    private List<SpuPropertyVo> spuProperties;

}
