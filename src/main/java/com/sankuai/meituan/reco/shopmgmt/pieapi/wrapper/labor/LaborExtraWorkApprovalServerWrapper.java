package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.labor;

import com.sankuai.drunkhorsemgmt.labor.thrift.approval.AttendanceApprovalThriftService;
import com.sankuai.drunkhorsemgmt.labor.thrift.approval.request.ExtraWorkApprovalCreateReq;
import com.sankuai.drunkhorsemgmt.labor.thrift.approval.response.ExtraWorkApprovalCreateResp;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor.ExtraWorkAttendanceApprovalCreateRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.TimeUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

@Service
public class LaborExtraWorkApprovalServerWrapper {

    @Resource
    private AttendanceApprovalThriftService attendanceApprovalThriftService;

    public CommonResponse<Long> create(ExtraWorkAttendanceApprovalCreateRequest request) {
        long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        long employeeId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getEmployeeId();
        LocalDate attendanceDay = TimeUtils.stringFormatToLocalDate(request.getApplyAttendanceDay());
        LocalDateTime checkinTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(request.getCheckinTime()), ZoneId.systemDefault());
        LocalDateTime checkoutTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(request.getCheckoutTime()), ZoneId.systemDefault());
        ExtraWorkApprovalCreateReq req = new ExtraWorkApprovalCreateReq(tenantId, request.getPoiId(), employeeId,
                request.getApplyText(), request.getPhotoList(), attendanceDay, checkinTime, checkoutTime);
        ExtraWorkApprovalCreateResp resp = attendanceApprovalThriftService.createExtraWorkApproval(req);
        if (resp == null || resp.getStatus() == null) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), ResultCode.FAIL.getDefaultMessage(), null);
        }
        if (!resp.getStatus().successful()) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), resp.getStatus().getMsg(), null);
        }
        return CommonResponse.success(resp.getBaseInfoId());
    }
}
