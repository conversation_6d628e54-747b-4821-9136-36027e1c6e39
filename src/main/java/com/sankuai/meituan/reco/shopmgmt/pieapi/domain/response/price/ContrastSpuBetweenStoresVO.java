package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.shangou.empower.price.client.response.contrast.ContrastSpuBetweenStoresResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * @Author: wangyihao04
 * @Date: 2020-12-01 19:34
 * @Mail: <EMAIL>
 */
@TypeDoc(
        description = "门店间商品对比返回值"
)
@ApiModel("门店间商品对比返回值")
@Getter
@AllArgsConstructor
@ToString
public class ContrastSpuBetweenStoresVO {
    @FieldDoc(
            description = "商品指标列表"
    )
    @ApiModelProperty("商品指标列表")
    private List<ContrastSpuVO> spuIndexes;
    @FieldDoc(
            description = "是否有下一页"
    )
    @ApiModelProperty("是否有下一页")
    private Boolean hasNext;

    public static ContrastSpuBetweenStoresVO valueOf(ContrastSpuBetweenStoresResponse response){
        if (CollectionUtils.isEmpty(response.getSpuContrastIndexList())){
            return new ContrastSpuBetweenStoresVO(Collections.emptyList(), response.getHasNest());
        }
        return new ContrastSpuBetweenStoresVO(ConverterUtils.convertList(response.getSpuContrastIndexList(), ContrastSpuVO::valueOf), response.getHasNest());
    }
}
