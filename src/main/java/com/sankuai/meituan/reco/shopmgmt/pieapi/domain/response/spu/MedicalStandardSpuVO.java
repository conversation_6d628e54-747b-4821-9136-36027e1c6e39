package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import java.util.List;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.MedicalStandardProductDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.MedicalStandardProductSearchDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 医药标品库商品信息
 *
 * <AUTHOR>
 * @since 2025/02/11
 */
@TypeDoc(
        description = "医药标品库商品信息"
)
@ApiModel("医药标品库商品信息")
@Getter
@Setter
@ToString
public class MedicalStandardSpuVO {

    @FieldDoc(
            description = "商品条码"
    )
    @ApiModelProperty(value = "商品条码")
    private String upc;

    @FieldDoc(
            description = "商品名称"
    )
    @ApiModelProperty(value = "商品名称")
    private String spuName;

    @FieldDoc(
            description = "渠道类目"
    )
    @ApiModelProperty(value = "渠道类目")
    private List<ChannelCategoryVO> channelCategoryList;

    @FieldDoc(
            description = "是否标品 1-是、0-否"
    )
    @ApiModelProperty(value = "是否标品 1-是、0-否")
    private Integer standerType;

    @FieldDoc(
            description = "规格"
    )
    @ApiModelProperty(value = "规格")
    private String spec;

    @FieldDoc(
            description = "商品图片"
    )
    @ApiModelProperty(value = "商品图片")
    private List<String> pictureList;

    @FieldDoc(
            description = "商品图详"
    )
    @ApiModelProperty(value = "商品图详")
    private List<String> pictureContents;

    @FieldDoc(
            description = "资质信息"
    )
    @ApiModelProperty(value = "资质信息")
    private MedicalDeviceQuaInfoVO medicalDeviceQuaInfo;

    public static MedicalStandardSpuVO fromDTO(MedicalStandardProductDTO dto) {
        MedicalStandardSpuVO vo = new MedicalStandardSpuVO();
        vo.setUpc(dto.getUpc());
        vo.setSpuName(dto.getSpuName());
        vo.setStanderType(dto.getStanderType());
        vo.setSpec(dto.getSpec());
        vo.setPictureList(dto.getPictureList());
        vo.setPictureContents(dto.getPictureContents());
        vo.setChannelCategoryList(Fun.map(dto.getChannelCategoryList(), ChannelCategoryVO::ofBizDTO));
        vo.setMedicalDeviceQuaInfo(MedicalDeviceQuaInfoVO.fromMedicalDeviceQuaDTO(dto.getMedicalDeviceQuaInfo()));
        return vo;
    }

}