package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-10-19
 * @email <EMAIL>
 */
@TypeDoc(
        description = "新建/编辑班次请求"
)
@ApiModel("新建/编辑班次请求")
@Data
public class ScheduleRuleVO {

    @FieldDoc(
            description = "规则id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "规则id")
    @NotNull
    private Long ruleId;

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店id")
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店id")
    @NotNull
    private String storeName;

    @FieldDoc(
            description = "名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "名称")
    @NotNull
    private String name;

    @FieldDoc(
            description = "类型", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "类型")
    @NotNull
    private String type;

    @FieldDoc(
            description = "员工列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "员工列表")
    @NotNull
    private List<RuleEmployeeVO> employeeList;

    @FieldDoc(
            description = "班次id列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "班次id列表")
    @NotNull
    @Deprecated
    private List<ScheduleShiftVO> shiftList;

    @FieldDoc(
            description = "新建/编辑班次请求", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "新建/编辑班次请求")
    @NotNull
    @Deprecated
    private List<ScheduleDetailVO> scheduleDetailList;

    @FieldDoc(
            description = "绑定门店位置信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "绑定门店位置信息")
    @NotNull
    private List<LocationStoreVO> locationStoreList;

    @FieldDoc(
            description = "班次数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "班次数量")
    @NotNull
    private Integer shiftNum;

    @FieldDoc(
            description = "员工数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "员工数量")
    @NotNull
    private Integer employeeNum;
}
