package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.out.warehouse;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.FallbackException;
import com.sankuai.meituan.reco.store.management.thrift.EmpowerTaskLogicException;
import com.sankuai.meituan.reco.store.saas.infrastructure.shield.common.monitor.CommonMonitorTransaction;
import com.sankuai.meituan.reco.store.wms.enums.ResultCodeEnum;
import com.sankuai.meituan.reco.store.wms.thrift.outwarehouse.common.Status;
import com.sankuai.meituan.reco.store.wms.thrift.outwarehouse.workorder.OutWarehouseWorkOrderThriftService;
import com.sankuai.meituan.reco.store.wms.thrift.outwarehouse.workorder.request.OutWarehouseWorkOrderCountThriftRequest;
import com.sankuai.meituan.reco.store.wms.thrift.outwarehouse.workorder.response.OutWarehouseWorkOrderCountThriftResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * author xujunfeng02
 * dateTime 2022/5/11 4:28 PM
 * description 调拨出库工单wrapper
 */
@Rhino
@Component
@Slf4j
public class OutWarehouseWorkOrderWrapper {

    @Resource
    private OutWarehouseWorkOrderThriftService outWarehouseWorkOrderThriftService;

    @Degrade(rhinoKey = "OutWarehouseWorkOrderWrapper.count",
            fallBackMethod = "countFallback",
            timeoutInMilliseconds = 2000,
            ignoreExceptions = {CommonLogicException.class, EmpowerTaskLogicException.class})
    @CommonMonitorTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public OutWarehouseWorkOrderCountThriftResponse count(OutWarehouseWorkOrderCountThriftRequest request) {

        try {

            OutWarehouseWorkOrderCountThriftResponse response = outWarehouseWorkOrderThriftService.count(request);
            checkStatus(response.getStatus());
            return response;
        } catch (Exception e) {

            log.error("OutWarehouseWorkOrderWrapper count error", e);
            throw new CommonRuntimeException("调拨出库工单数量查询异常", ResultCode.RETRY_INNER_FAIL);
        }
    }

    private void checkStatus(Status status) {

        if (status == null || status.getCode() != ResultCodeEnum.SUCCESS.getCode()) {

            log.warn("AllotOutWarehouseWorkOrderClient count fail status:{}", status);
            throw new CommonRuntimeException("调拨出库工单数量查询异常", ResultCode.RETRY_INNER_FAIL);
        }
    }

    public OutWarehouseWorkOrderCountThriftResponse countFallback(OutWarehouseWorkOrderCountThriftRequest request) {

        throw new FallbackException("OutWarehouseWorkOrderWrapper.count降级");
    }
}