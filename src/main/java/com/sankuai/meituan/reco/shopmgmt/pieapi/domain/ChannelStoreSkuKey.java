package com.sankuai.meituan.reco.shopmgmt.pieapi.domain;

import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelSkuDTO;
import com.sankuai.meituan.shangou.empower.price.client.dto.ChannelStoreSkuDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 *
 * 渠道商品key
 */
@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class ChannelStoreSkuKey {

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 商品编码
     */
    private String skuId;

    public static ChannelStoreSkuKey of(Long storeId, Integer channelId, String skuId) {
        return new ChannelStoreSkuKey(storeId, channelId, skuId);
    }

    public static ChannelStoreSkuKey convertTo(ChannelStoreSkuDTO dto){
        return new ChannelStoreSkuKey(dto.getStoreId(), dto.getChannelId(), dto.getSkuId());
    }
}
