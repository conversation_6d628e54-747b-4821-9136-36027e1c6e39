package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.regionselect.rsm;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * Email <EMAIL>
 * Date 2022/12/7 12:54 下午
 * Description
 */
@Data
@TypeDoc(description = "仓源表单项")
@ApiModel("仓源表单项")
public class FdcRentalSourceFormItem {

    @ApiModelProperty("表单项key")
    @FieldDoc(description = "表单项key")
    private String key;

    @ApiModelProperty("表单项value")
    @FieldDoc(description = "表单项value")
    private String value;
}
