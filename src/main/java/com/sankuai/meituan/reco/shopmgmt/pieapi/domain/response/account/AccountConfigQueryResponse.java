package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.account;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @createTime 2019/11/19
 * @description
 */
@TypeDoc(
        description = "账号消息和铃声查询响应"
)
@Data
@ApiModel("账号消息和铃声查询响应")
public class AccountConfigQueryResponse {

    /**
     * 消息和铃声设置项
     */
    @FieldDoc(
            description = "账号消息和铃声配置模块权限", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "账号消息和铃声配置模块权限", required = true)
    @NotNull
    private AccountConfigModuleVO accountConfigModuleVO;


    @FieldDoc(
            description = "账号消息和铃声配置", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "账号消息和铃声配置", required = true)
    @NotNull
    private AccountConfigVO accountConfigVO;

}
