package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.statistic;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/09/19
 */
@TypeDoc(description = "骑手配送统计数据-多月维度 请求")
@ApiModel("骑手配送统计数据-多月维度 请求")
@Data
public class RiderMonthsDeliveryStatisticRequest {

    @FieldDoc(description = "门店ID",requiredness = Requiredness.REQUIRED)
    @ApiModelProperty("门店ID")
    private Long storeId;
}
