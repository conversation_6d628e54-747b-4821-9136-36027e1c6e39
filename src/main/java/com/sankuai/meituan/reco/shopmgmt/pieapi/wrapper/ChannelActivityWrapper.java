package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.google.common.collect.Lists;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.ChannelStoreCustomSkuKey;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.ChannelStoreCustomSpuKey;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price.SkuCanModifyPriceVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price.SpuCanModifyPriceVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelType;
import com.sankuai.meituan.shangou.empower.price.client.dto.ChannelStoreCustomSkuDTO;
import com.sankuai.meituan.shangou.empower.price.client.dto.ChannelStoreCustomSpuDTO;
import com.sankuai.meituan.shangou.empower.price.client.enums.PriceResultEnum;
import com.sankuai.meituan.shangou.empower.price.client.request.SpuCanChangePriceRequest;
import com.sankuai.meituan.shangou.empower.price.client.request.StoreSkuPromotionRequest;
import com.sankuai.meituan.shangou.empower.price.client.response.SpuCanChangePriceResponse;
import com.sankuai.meituan.shangou.empower.price.client.response.StoreSkuPromotionResponse;
import com.sankuai.meituan.shangou.empower.price.client.service.ChannelActivityThriftService;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/8/15
 */
@Service
@Slf4j
public class ChannelActivityWrapper {

    @Resource
    private ChannelActivityThriftService priceChannelActivityThriftService;

    public List<SpuCanModifyPriceVO> canModifyPriceByCustomSpuId(Long tenantId, List<ChannelStoreCustomSpuKey> keyList) {

        List<ChannelStoreCustomSpuDTO> channelStoreCustomSpuDTOS = Optional.ofNullable(keyList)
                .orElse(Lists.newArrayList()).stream()
                .filter(customSpuVO -> customSpuVO.getChannelId().equals(ChannelType.MEITUAN.getValue()))
                .map(customSpuVO -> ChannelStoreCustomSpuDTO.builder()
                        .storeId(customSpuVO.getStoreId())
                        .channelId(customSpuVO.getChannelId())
                        .customSpuId(customSpuVO.getCustomSpuId())
                        .build())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(channelStoreCustomSpuDTOS)) {
            return Collections.emptyList();
        }

        SpuCanChangePriceRequest request = SpuCanChangePriceRequest.builder()
                .tenantId(tenantId)
                .channelStoreCustomSpuDTOS(channelStoreCustomSpuDTOS)
                .build();
        try {
            SpuCanChangePriceResponse response = priceChannelActivityThriftService.queryCanChangePriceInfoByCustomSpuKeys(request);
            log.info("channelActivityThriftService.queryCanChangePriceInfoByCustomSpuKeys, command:{}, result:{}", request, response);
            if (response == null || Objects.isNull(response.getStatus())) {
                throw new BizException(-1, "查询商品活动信息异常, 请稍后重试！");
            }
            if (response.getStatus().getCode() != 0) {
                throw new BizException(-1, response.getStatus().getMsg());
            }

            return ConverterUtils.convertList(response.getCanChangePriceSpuDTOList(), SpuCanModifyPriceVO::valueOf);
        } catch (Exception e) {
            log.error("channelActivityThriftService.queryCanChangePriceInfoByCustomSpuKeys exception, command:{}", request, e);
            throw new BizException(-1, "查询商品活动信息异常, 请稍后重试！");
        }
    }

    public List<SkuCanModifyPriceVO> canModifyPriceByCustomSkuId(Long tenantId, List<ChannelStoreCustomSkuKey> skuKeys) {

        if (CollectionUtils.isEmpty(skuKeys)) {
            return Collections.emptyList();
        }
        List<ChannelStoreCustomSkuDTO> customSkuKeys = skuKeys.stream().filter(skuKey -> skuKey.getChannelId().equals(ChannelType.MEITUAN.getValue()))
                .map(ChannelStoreCustomSkuKey::toChannelStoreCustomSkuDTO)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(customSkuKeys)) {
            return Collections.emptyList();
        }
        StoreSkuPromotionRequest request = new StoreSkuPromotionRequest();
        request.setTenantId(tenantId);
        request.setChannelStoreSkuList(customSkuKeys);
        try {
            StoreSkuPromotionResponse response = priceChannelActivityThriftService.queryStoreSkuPromotionInfo(request);
            if (response.getStatus().getCode() != PriceResultEnum.SUCCESS.getCode()){
                throw new BizException(response.getStatus().getMsg());
            }
            return ConverterUtils
                    .convertList(response.getPromotionDTOList(), SkuCanModifyPriceVO::valueOf);
        } catch (Exception e) {
            log.warn("查询渠道商品SKU促销信息，customSkuKeys {}, msg {}", customSkuKeys, e.getMessage());
            throw new BizException(-1, "查询渠道商品SKU促销信息, 请稍后重试！");
        }
    }
}
