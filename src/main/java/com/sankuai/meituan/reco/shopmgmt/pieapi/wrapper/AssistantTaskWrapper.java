package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.meituan.linz.boot.util.Fun;
import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.shangou.munich.assistant.client.enums.TaskTypeEnum;
import com.meituan.shangou.munich.assistant.client.request.task.SpuTaskListRequest;
import com.meituan.shangou.munich.assistant.client.request.task.TaskDetailRequest;
import com.meituan.shangou.munich.assistant.client.request.task.TaskIgnoredRequest;
import com.meituan.shangou.munich.assistant.client.request.task.TaskSummaryByTypeRequest;
import com.meituan.shangou.munich.assistant.client.request.task.TaskSummaryRequest;
import com.meituan.shangou.munich.assistant.client.response.task.SpuTaskListResponse;
import com.meituan.shangou.munich.assistant.client.response.task.TaskDetailResponse;
import com.meituan.shangou.munich.assistant.client.response.task.TaskSummaryResponse;
import com.meituan.shangou.munich.assistant.client.service.AssistantTaskThriftService;
import com.meituan.shangou.munich.assistant.client.support.CommonResponse;
import com.meituan.shangou.munich.assistant.client.to.task.SpuTaskTo;
import com.meituan.shangou.munich.assistant.client.to.task.TaskSummaryTo;
import com.meituan.shangou.sac.dto.model.SacMenuNodeWithChild;
import com.sankuai.meituan.reco.shopmgmt.pieapi.converters.assistant.AssistantTaskConverter;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.appmodel.MenuInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.MenuCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.PermissionCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.shangou.empower.auth.sdk.bean.SessionInfo;
import com.sankuai.meituan.shangou.empower.auth.sdk.context.SessionContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2021/1/25 14:09
 */
@Service
@Slf4j
public class AssistantTaskWrapper {

    @Autowired
    private AssistantTaskThriftService assistantTaskThriftService;

    @Autowired
    private AuthThriftWrapper authThriftWrapper;

    @Autowired
    private SacWrapper sacWrapper;


    /**
     * 获取任务汇总信息.
     *
     * @param tenantId 租户ID
     * @param poiId 门店ID
     * @return 对应门店的任务汇总信息
     */
    public List<TaskSummaryTo> getTaskSummary(long tenantId, long poiId) {
        TaskSummaryRequest request = new TaskSummaryRequest();
        request.setTenantId(tenantId);
        request.setPoiId(poiId);

        try {
            TaskSummaryResponse taskSummaryResponse = assistantTaskThriftService.getTaskSummary(request);
            if (taskSummaryResponse.getStatus() == null
                    || taskSummaryResponse.getStatus().getCode() != 0) {
                throw new CommonLogicException("查询任务列表失败");
            }

            return filterAuth(taskSummaryResponse.getTaskSummaryDtoList());
        }
        catch (TException e) {
            log.error("查询任务失败, tenantId:{}, poiId:{}", tenantId, poiId, e);
            throw new RuntimeException("查询任务列表失败", e);
        }
    }

    /**
     * 根据任务类型查询任务汇总信息
     *
     * @param tenantId
     * @param poiId
     * @param taskTypeList
     * @return Pair<是否异常查询灰度, 任务统计列表>
     */
    public Pair<Boolean, List<TaskSummaryTo>> getTaskSummaryByType(long tenantId, long poiId, long accountId, List<TaskTypeEnum> taskTypeList, Boolean possibleNewQueryGray) {
        if (CollectionUtils.isEmpty(taskTypeList)) {
            return Pair.of(null, Collections.emptyList());
        }
        TaskSummaryByTypeRequest request = new TaskSummaryByTypeRequest();
        request.setTenantId(tenantId);
        request.setPoiId(poiId);
        request.setTaskTypeList(taskTypeList);
        request.setAccountId(accountId);
        request.setPossibleNewQueryGray(possibleNewQueryGray);
        TaskSummaryResponse taskSummaryResponse;
        try {
            log.info("根据任务类型查询任务汇总信息 request:{}", JacksonUtils.toJson(request));
            taskSummaryResponse = assistantTaskThriftService.getTaskSummaryByType(request);
            log.info("根据任务类型查询任务汇总信息 response:{}", JacksonUtils.toJson(taskSummaryResponse));
        }
        catch (Exception e) {
            log.error("根据任务类型查询任务汇总信息, request:{} ", JacksonUtils.toJson(request), e);
            return Pair.of(null, Collections.emptyList());
        }
        if (taskSummaryResponse.getStatus() == null
                || taskSummaryResponse.getStatus().getCode() != 0) {
            log.error("根据任务类型查询任务汇总信息, request:{} response:{}", JacksonUtils.toJson(request),
                    JacksonUtils.toJson(taskSummaryResponse));
            return Pair.of(null, Collections.emptyList());
        }

        return Pair.of(taskSummaryResponse.getNewQueryAbnormalGray(), taskSummaryResponse.getTaskSummaryDtoList());
    }

    public Map<TaskTypeEnum, List<TaskDetailResponse.PoiTaskDetailData>> getTaskDetail(long tenantId, List<Long> poiIds, long accountId, List<TaskTypeEnum> taskTypeList) {
        TaskDetailRequest request = new TaskDetailRequest();
        request.setTenantId(tenantId);
        request.setPoiIds(poiIds);
        request.setAccountId(accountId);
        request.setTaskTypes(taskTypeList);
        try {
            log.info("查询任务详情 request:{}", JacksonUtils.toJson(request));
            TaskDetailResponse response = assistantTaskThriftService.getTaskDetail(request);
            log.info("查询任务详情 response:{}", JacksonUtils.toJson(response));
            if (response.getStatus() == null || response.getStatus().getCode() != 0) {
                log.error("查询任务详情失败, request:{}, response:{}", JacksonUtils.toJson(request), JacksonUtils.toJson(response));
                return Collections.emptyMap();
            }
            return response.getData();
        } catch (Exception e) {
            log.error("查询任务详情失败, request:{} ", JacksonUtils.toJson(request), e);
            return Collections.emptyMap();
        }
    }


    /**
     *
     * @return waima supervisor menu info
     */
    public MenuInfo getWaiMaSupervisorMenuInfo() {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        String menuCode = MenuCodeEnum.WM_SUPERVISOR_TASK.getCode();
        Map<String, SacMenuNodeWithChild> menuNodeWithChildMap = sacWrapper.getAccountMenusByMenuCodes(
                identityInfo.getUser().getAccountId(),
                identityInfo.getAuthId(),
                Collections.singletonList(menuCode));
        if (menuNodeWithChildMap == null || !menuNodeWithChildMap.containsKey(menuCode) || menuNodeWithChildMap.get(menuCode) == null) {
            return null;
        }
        SacMenuNodeWithChild menuNodeWithChild = menuNodeWithChildMap.get(menuCode);
        return AssistantTaskConverter.convertSacMenu2AssistantMenu(menuNodeWithChild.getSacMenuNodeDto());
    }

    private List<TaskSummaryTo> filterAuth(List<TaskSummaryTo> taskSummaryDtoList) {
        if (CollectionUtils.isEmpty(taskSummaryDtoList)) {
            return Collections.emptyList();
        }
        SessionInfo currentSession = SessionContext.getCurrentSession();
        int appId = ApiMethodParamThreadLocal.getIdentityInfo().getAuthId();
        Boolean hasOffSaleInStockAuth = sacWrapper.accountAuthPermissions(currentSession.getAccountId(), appId,
                Arrays.asList(PermissionCodeEnum.ASSISTANT_OFF_SALE_IN_STOCK.getCode()))
                .get(PermissionCodeEnum.ASSISTANT_OFF_SALE_IN_STOCK.getCode());

        if (BooleanUtils.isFalse(hasOffSaleInStockAuth)) {
            return Fun.filter(taskSummaryDtoList, t -> !Objects.equals(t.getTaskType(), TaskTypeEnum.SKU_OFF_SALE_IN_STOCK.code()));
        }

        return taskSummaryDtoList;
    }


    /**
     * 忽略任务.
     *
     * @param tenantId 租户ID
     * @param taskIds 任务ID
     */
    public void ignoreTask(long tenantId, List<Long> taskIds) {
        TaskIgnoredRequest request = new TaskIgnoredRequest();
        request.setTenantId(tenantId);
        request.setTaskIds(taskIds);

        try {
            CommonResponse commonResponse = assistantTaskThriftService.taskIgnored(request);
            if (commonResponse.getStatus() == null
                    || commonResponse.getStatus().getCode() != 0) {
                throw new CommonLogicException("忽略任务失败");
            }
        }
        catch (TException e) {
            log.error("忽略任务失败, tenantId:{}, taskId:{}", tenantId, taskIds, e);
            throw new RuntimeException("忽略任务失败", e);
        }
    }


    /**
     * 查询商品任务列表.
     *
     * @param tenantId 租户ID
     * @param poiId 门店ID
     * @param taskType 任务类型
     * @return 商品任务列表
     */
    public List<SpuTaskTo> querySpuTaskList(long tenantId, long poiId, int taskType) {
        SpuTaskListRequest request = new SpuTaskListRequest();
        request.setTenantId(tenantId);
        request.setPoiId(poiId);
        request.setTaskType(taskType);
        try {
            SpuTaskListResponse response = assistantTaskThriftService.getSkuTaskListByType(request);
            if (response.getStatus() == null
                    || response.getStatus().getCode() != 0) {
                throw new CommonLogicException("查询任务列表失败");
            }

            return response.getSkuTaskToList();
        }
        catch (TException e) {
            log.error("查询任务列表失败, tenantId:{}, poiId:{}, taskType:{}",
                    tenantId, poiId, taskType, e);
            throw new RuntimeException("查询任务列表失败", e);
        }
    }

    /**
     * 查询商品任务列表.
     *
     * @param tenantId 租户ID
     * @param poiId 门店ID
     * @param taskType 任务类型
     * @return 商品任务列表
     */
    public SpuTaskListResponse querySpuTaskListWithPage(long tenantId, long poiId, int taskType, int page, int pageSize) {
        SpuTaskListRequest request = new SpuTaskListRequest();
        request.setTenantId(tenantId);
        request.setPoiId(poiId);
        request.setTaskType(taskType);
        request.setPage(page);
        request.setPageSize(pageSize);
        try {
            SpuTaskListResponse response = assistantTaskThriftService.getSkuTaskListByTypeWithPage(request);
            if (response.getStatus() == null
                    || response.getStatus().getCode() != 0) {
                throw new CommonLogicException("查询任务列表失败");
            }
            return response;
        }
        catch (TException e) {
            log.error("查询任务列表失败, tenantId:{}, poiId:{}, taskType:{}",
                    tenantId, poiId, taskType, e);
            throw new RuntimeException("查询任务列表失败", e);
        }
    }
}
