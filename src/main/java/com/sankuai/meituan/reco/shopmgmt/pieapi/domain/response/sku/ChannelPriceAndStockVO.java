package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TypeDoc(
        description = "渠道价格和库存"
)
@Data
@ApiModel("渠道价格和库存")
public class ChannelPriceAndStockVO {

    @FieldDoc(
            description = "渠道id  -1-线下 100-美团 200-饿了么 300-京东到家", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道id", required = true)
    private Integer channelId;

    @FieldDoc(
            description = "渠道价格 单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道价格 单位:分", required = true)
    private Double price;

    @FieldDoc(
            description = "渠道库存数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道库存数", required = true)
    private Long stock;

    @FieldDoc(
            description = "状态 1-上架  2-下架 3-未上传", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "状态 1-上架  2-下架 3-未上传", required = true)
    private Integer status;

    @FieldDoc(
            description = "显示价格趋势图标 true-显示 false-不展示", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "显示价格趋势图标 true-显示 false-不展示", required = true)
    private Boolean showPriceTrendIcon;
}
