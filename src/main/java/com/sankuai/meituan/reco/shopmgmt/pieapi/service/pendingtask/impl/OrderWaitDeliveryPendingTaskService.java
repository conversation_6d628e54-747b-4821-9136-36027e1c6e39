/*
 * Copyright (c) 2019 Meituan.com. All Rights Reserved.
 */
package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.impl;

import com.dianping.cat.util.MetricHelper;
import com.meituan.shangou.saas.order.management.client.dto.request.online.Wait2DeliveryCountRequest;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OcmsWaitToDeliveryOrderSubTypeCountResponse;
import com.meituan.shangou.saas.order.management.client.service.online.OCMSQueryThriftService;
import com.meituan.shangou.saas.order.platform.enums.*;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiEntityTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.AbstractSinglePendingTaskService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.TimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;



@Slf4j
@Service
public class OrderWaitDeliveryPendingTaskService extends AbstractSinglePendingTaskService {

    @Resource
    private OCMSQueryThriftService ocmsQueryThriftService;


    @Override
    public PendingTaskResult getPendingTaskCount(PendingTaskParam param) throws Exception {
        int count=0;
        try {
            Wait2DeliveryCountRequest request = buildWait2DeliveryCountRequest(param.getTenantId(), param.getStoreIds(), param.getEntityType(), true);
            log.info("OrderService.OrderWaitDeliveryPendingTaskService  调用ocmsQueryThriftService.queryWaitToDeliveryOrderSubTypeCountV2 request:{}", request);
            OcmsWaitToDeliveryOrderSubTypeCountResponse ocmsListOrderResponse = ocmsQueryThriftService.queryWaitToDeliveryOrderSubTypeCountV2(request);
            log.info("OrderService.OrderWaitDeliveryPendingTaskService  调用ocmsQueryThriftService.queryWaitToDeliveryOrderSubTypeCountV2 response:{}", ocmsListOrderResponse);
            if (ocmsListOrderResponse.getStatus().getCode() == com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum.SUCCESS.getValue()) {
                count = ocmsListOrderResponse.getAllSubTypeCount();
            }
            return PendingTaskResult.createNumberMarker(count);
        } catch (Exception e) {
            MetricHelper.build().name("order.buildOCMSListWaitAuditOrderRequest.err").tag("tenantId", String.valueOf(param.getTenantId())).tag("storeId", String.valueOf(firstShopId(param.getStoreIds()))).count();
            log.error("OrderService.queryWaitAuditRefundGoodsBySubtypeCount  调用ocmsQueryThriftService.queryWaitToRefundGoodsBySubtypeCount error", e);
            throw new CommonRuntimeException(e);
        }
    }

    private long firstShopId(List<Long> storeIdList) {
        if (CollectionUtils.isNotEmpty(storeIdList)){
            return storeIdList.get(0);
        }
        return 0;
    }

    private Wait2DeliveryCountRequest buildWait2DeliveryCountRequest(Long tenantId, List<Long> storeIdList, Integer entityType, boolean onlyTotalCount) {
        Wait2DeliveryCountRequest request = new Wait2DeliveryCountRequest();
        request.setTenantId(tenantId);
        request.setShopIdList(storeIdList);
        request.setDeliveryStatusList(Lists.newArrayList(
                DistributeStatusEnum.WAIT_FOR_ASSIGN_RIDER.getValue(),
                DistributeStatusEnum.DISTRIBUTE_REJECTED.getValue(),
                DistributeStatusEnum.RIDER_ASSIGNED.getValue(),
                DistributeStatusEnum.RIDER_REACH_SHOP.getValue(),
                DistributeStatusEnum.RIDER_TAKE_GOODS_FAILED.getValue(),
                DistributeStatusEnum.DISTRIBUTE_CANCELED.getValue(),
                DistributeStatusEnum.UN_KNOWN.getValue(),
                DistributeStatusEnum.DISTRIBUTE_UNKNOWN.getValue(),
                DistributeStatusEnum.RIDER_TAKE_GOODS.getValue()
        ));
        request.setOnlyTotalCount(onlyTotalCount);
        //查询当前时间之前7天的订单
        request.setBeginCreateTime(TimeUtils.getBeforeDayTimeStamp(MccConfigUtil.queryOrderCreateTimeBefore()));
        request.setEndCreateTime(System.currentTimeMillis());
        request.setViewFuseOrderStatusList(Lists.newArrayList(FuseOrderStatusEnum.PICKED.getValue(), FuseOrderStatusEnum.SHIPPING.getValue()));
        if(Objects.equals(entityType, PoiEntityTypeEnum.SHAREABLE_WAREHOUSE.code())){
            request.setShopIdList(new ArrayList<>());
            request.setWarehouseIdList(storeIdList);
        }
        return request;
    }


    @Override
    protected AuthCodeEnum module() {
        return AuthCodeEnum.ORDER_WAIT_TO_DELIVERY;
    }
}
