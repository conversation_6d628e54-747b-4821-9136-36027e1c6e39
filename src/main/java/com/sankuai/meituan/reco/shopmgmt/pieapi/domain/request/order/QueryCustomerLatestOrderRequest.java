package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: <EMAIL>
 * @Date: 2020-02-11 10:41
 * @Description:
 */
@TypeDoc(
        description = "查询用户最近订单请求"
)
@ApiModel("查询用户最近订单请求")
@Data
public class QueryCustomerLatestOrderRequest {


    @FieldDoc(
            description = "美团用户Id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "美团用户Id")
    @NotNull
    private String userId;

    @FieldDoc(
            description = "门店Id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店Id")
    @NotNull
    private String poiId;


}
