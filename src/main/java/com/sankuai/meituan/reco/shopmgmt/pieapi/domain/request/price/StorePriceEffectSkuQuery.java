package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: wangyihao04
 * @Date: 2020-11-03 21:20
 * @Mail: <EMAIL>
 */
@ApiModel(
        "门店问题商品列表页查询"
)
@TypeDoc(
        description = "门店问题商品列表页查询"
)
@Data
public class StorePriceEffectSkuQuery {
    @FieldDoc(
            description = "租户id"
    )
    @ApiModelProperty("租户Id")
    private Long tenantId;

    @FieldDoc(
            description = "门店id"
    )
    @NotNull
    @ApiModelProperty("门店Id")
    private Long storeId;


    @FieldDoc(
            description = "是否需要门店聚合数据"
    )
    @ApiModelProperty("是否需要门店聚合数据")
    private Boolean needStoreAggregation;


    @FieldDoc(
            description = "查询价格指标类型"
    )
    @NotNull
    @ApiModelProperty("查询价格指标类型")
    private String priceEffectIndexType;


    @FieldDoc(
            description = "只看核心品"
    )
    @ApiModelProperty("只看核心品")
    private Boolean filterCore;

    @FieldDoc(
            description = "当前页码"
    )
    @NotNull
    @ApiModelProperty("当前页码")
    private Integer pageNum;

    @FieldDoc(
            description = "页大小"
    )
    @NotNull
    @ApiModelProperty("页大小")
    private Integer pageSize;

    @FieldDoc(
            description = "排序类型"
    )
    @ApiModelProperty("排序类型")
    private Integer sortType;

}
