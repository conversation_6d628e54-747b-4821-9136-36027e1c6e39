package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TypeDoc(
        description = "查询拣货详情进度"
)
@Data
@ApiModel("查询拣货详情进度")
public class WarehouseWaveProgressVO {

    @FieldDoc(
            description = "已拣商品件数", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "已拣商品件数")
    private Integer actualNum;

    @FieldDoc(
            description = "应拣商品件数", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "应拣商品件数")
    private Integer needNum;

    @FieldDoc(
            description = "已拣商品种数", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "已拣商品种数")
    private Integer actualSkuNum;

    @FieldDoc(
            description = "应拣商品种数", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "应拣商品种数")
    private Integer needSkuNum;
}
