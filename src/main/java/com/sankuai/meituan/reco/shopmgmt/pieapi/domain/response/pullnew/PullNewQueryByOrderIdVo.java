package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 根据单号查询地推事件返回
 *
 * <AUTHOR>
 * @since 5/8/23
 */
@TypeDoc(
        description = "根据单号查询地推事件返回信息"
)
@Data
@ApiModel("根据单号查询地推事件返回信息")
@NoArgsConstructor
@AllArgsConstructor
public class PullNewQueryByOrderIdVo {

    @FieldDoc(
            description = "地推事件列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "地推事件列表", required = true)
    private List<PullNewDetailVo> list;
}
