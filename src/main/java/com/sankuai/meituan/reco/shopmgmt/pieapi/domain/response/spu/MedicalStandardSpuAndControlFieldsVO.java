package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import java.util.List;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.MedicalStandardProductAndControlFieldsDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 医药标品库商品及其强管控字段信息
 *
 * <AUTHOR>
 * @since 2025/02/11
 */
@TypeDoc(
        description = "医药标品库商品及其强管控字段信息"
)
@ApiModel("医药标品库商品及其强管控字段信息")
@Getter
@Setter
@ToString
public class MedicalStandardSpuAndControlFieldsVO {

    @FieldDoc(
            description = "医药标品库商品"
    )
    @ApiModelProperty(value = "医药标品库商品")
    private MedicalStandardSpuVO medicalStandardProductInfo;

    @FieldDoc(
            description = "医药标品强管控字段(无法自定义)"
    )
    @ApiModelProperty(value = "医药标品强管控字段(无法自定义)")
    private List<String> forceControlFields;

    public static MedicalStandardSpuAndControlFieldsVO fromDTO(MedicalStandardProductAndControlFieldsDTO dto) {
        MedicalStandardSpuAndControlFieldsVO vo = new MedicalStandardSpuAndControlFieldsVO();
        vo.setMedicalStandardProductInfo(MedicalStandardSpuVO.fromDTO(dto.getMedicalStandardProductInfo()));
        vo.setForceControlFields(dto.getForceControlFields());
        return vo;
    }
}