package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.labor;

import com.google.common.collect.Lists;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.sankuai.drunkhorsemgmt.labor.constants.ApprovalActivityStatusEnum;
import com.sankuai.drunkhorsemgmt.labor.thrift.ResignThriftService;
import com.sankuai.drunkhorsemgmt.labor.thrift.dto.HireAndResignEmployeeDTO;
import com.sankuai.drunkhorsemgmt.labor.thrift.dto.ResignApplyInfoDTO;
import com.sankuai.drunkhorsemgmt.labor.thrift.dto.ResignDetailDTO;
import com.sankuai.drunkhorsemgmt.labor.thrift.employee.dto.DepPositionInfoDTO;
import com.sankuai.drunkhorsemgmt.labor.thrift.page.PageRequest;
import com.sankuai.drunkhorsemgmt.labor.thrift.request.ApplyResignReq;
import com.sankuai.drunkhorsemgmt.labor.thrift.request.QueryResignApplyDetailReq;
import com.sankuai.drunkhorsemgmt.labor.thrift.request.QueryResignApplyReq;
import com.sankuai.drunkhorsemgmt.labor.thrift.response.ApplyResignResp;
import com.sankuai.drunkhorsemgmt.labor.thrift.response.QueryAlreadyResignApplyResp;
import com.sankuai.drunkhorsemgmt.labor.thrift.response.QueryResignApplyResp;
import com.sankuai.drunkhorsemgmt.labor.thrift.response.QueryResignDetailResp;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.QueryApprovalResignApplyListResp;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.QueryResignApplyDetailResp;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.QueryResignApplyListResp;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.QueryWaitToResignApplyResp;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.ResignApplyVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.ResignApprovalDetailVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.WaitToResignApplyVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.labor.convertor.ApprovalConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-12-09
 * @email <EMAIL>
 */
@Slf4j
@Service
public class ResignServiceWrapper {

    @Resource
    private ResignThriftService resignThriftService;
    @Resource
    private ApprovalConverter approvalConverter;

    private static final Integer EMPLOYEE_QUERY_TYPE = 1;
    private static final Integer MANAGER_QUERY_TYPE = 2;

    private static final Integer RESIGN_WAIT_APPROVE_QUERY_TYPE = 1;
    private static final Integer RESIGN_APPROVED_QUERY_TYPE = 2;


    @MethodLog(logRequest = true, logResponse = true)
    public CommonResponse<QueryWaitToResignApplyResp> queryWaitToResignApplyList(QueryWaitToResignApplyListRequest request) {
        QueryResignApplyReq req = new QueryResignApplyReq();
        req.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        req.setStoreId(ApiMethodParamThreadLocal.getIdentityInfo().getStoreId());
        req.setAccountId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId());
        req.setQueryType(EMPLOYEE_QUERY_TYPE);
        req.setPageRequest(new PageRequest(request.getPageNo(), request.getPageSize()));
        QueryResignApplyResp resp = resignThriftService.queryWaitToResignApplyList(req);
        if (!resp.getStatus().successful()) {
            return CommonResponse.fail(resp.getStatus().getCode(), resp.getStatus().getMsg());
        }
        return CommonResponse.success(new QueryWaitToResignApplyResp(
                convertToVO(resp.getHireAndResignEmployeeDTOList()), resp.isHasMore(), resp.getTotalCount()
        ));
    }

    @MethodLog(logRequest = true, logResponse = true)
    public CommonResponse<Void> submitApply(SubmitApplyRequest request) {
        ApplyResignReq req = new ApplyResignReq();
        req.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        req.setPoiId(ApiMethodParamThreadLocal.getIdentityInfo().getStoreIdList().get(0));
        req.setApplyAccountId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId());
        req.setResignEmployeeId(request.getEmployeeId());
        req.setResignDate(request.getResignDate());
        req.setResignType(request.getResignType());
        req.setResignText(request.getResignText());
        ApplyResignResp resp = resignThriftService.applyResign(req);
        if (!resp.getStatus().successful()) {
            return CommonResponse.fail(resp.getStatus().getCode(), resp.getStatus().getMsg());
        }
        return CommonResponse.success(null);
    }

    @MethodLog(logRequest = true, logResponse = true)
    public CommonResponse<QueryResignApplyListResp> queryApplyList(QueryResignApplyListRequest request) {
        QueryResignApplyReq req = new QueryResignApplyReq();
        req.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        req.setStoreId(ApiMethodParamThreadLocal.getIdentityInfo().getStoreId());
        req.setAccountId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId());
        req.setQueryType(EMPLOYEE_QUERY_TYPE);
        req.setPageRequest(new PageRequest(request.getPageNo(), request.getPageSize()));
        req.setNeedDetail(false);
        QueryAlreadyResignApplyResp resp = resignThriftService.queryAlreadyResignApplyList(req);
        if (!resp.getStatus().successful()) {
            return CommonResponse.fail(resp.getStatus().getCode(), resp.getStatus().getMsg());
        }
        return CommonResponse.success(new QueryResignApplyListResp(
                convertToResignApplyVO(resp.getResignApplyInfoDTOList()), resp.isHasMore(), resp.getTotalCount()
        ));
    }

    @MethodLog(logRequest = true, logResponse = true)
    public CommonResponse<QueryApprovalResignApplyListResp> queryApprovalList(QueryResignApprovalListRequest request) {
        QueryResignApplyReq req = new QueryResignApplyReq();
        req.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        req.setStoreIds(ApiMethodParamThreadLocal.getIdentityInfo().getStoreIdList());
        req.setAccountId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId());
        req.setQueryType(MANAGER_QUERY_TYPE);
        req.setApprovalType(request.getType());
        req.setNeedDetail(false);
        req.setKeyword(request.getKeyword());
        req.setPageRequest(new PageRequest(request.getPageNo(), request.getPageSize()));
        if (Objects.equals(RESIGN_WAIT_APPROVE_QUERY_TYPE, req.getQueryType())) {
            req.setApprovalStatusList(Lists.newArrayList(ApprovalActivityStatusEnum.WAITING.getStatus()));
        } else {
            req.setApprovalStatusList(Lists.newArrayList(
                            ApprovalActivityStatusEnum.APPROVED.getStatus(),
                            ApprovalActivityStatusEnum.REJECTED.getStatus(),
                            ApprovalActivityStatusEnum.CANCELLED.getStatus()
                    )
            );
        }
        QueryAlreadyResignApplyResp resp = resignThriftService.queryAlreadyResignApplyList(req);
        if (!resp.getStatus().successful()) {
            return CommonResponse.fail(resp.getStatus().getCode(), resp.getStatus().getMsg());
        }
        return CommonResponse.success(new QueryApprovalResignApplyListResp(
                convertToResignApplyVO(resp.getResignApplyInfoDTOList()), resp.isHasMore(), resp.getTotalCount()
        ));
    }

    @MethodLog(logRequest = true, logResponse = true)
    public CommonResponse<QueryResignApplyDetailResp> queryResignApprovalDetail(@RequestBody QueryResignApprovalDetailRequest request) {
        QueryResignApplyDetailReq req = new QueryResignApplyDetailReq();
        req.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        req.setAccountId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId());
        req.setEmployeeId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getEmployeeId());
        req.setApprovalId(request.getApprovalId());
        QueryResignDetailResp resp = resignThriftService.queryResignApplyDetail(req);
        if (!resp.getStatus().successful()) {
            return CommonResponse.fail(resp.getStatus().getCode(), resp.getStatus().getMsg());
        }
        return CommonResponse.success(
                new QueryResignApplyDetailResp(convertToVO(resp.getResignDetailDTO()), approvalConverter.convertToApprovalVO(resp.getResignDetailDTO().getApprovalNodeDTOList()), resp.getResignDetailDTO().getApprovalStatus(), resp.getResignDetailDTO().getCanApproval())
        );
    }

    private List<ResignApplyVO> convertToResignApplyVO(List<ResignApplyInfoDTO> dtoList) {
        return Optional
                .ofNullable(dtoList)
                .orElse(Lists.newArrayList())
                .stream()
                .map(
                        resignApplyInfoDTO -> {
                            HireAndResignEmployeeDTO dto = resignApplyInfoDTO.getHireAndResignEmployeeDTO();
                            ResignApplyVO vo = new ResignApplyVO();
                            vo.setEmployeeId(dto.getEmployeeId());
                            vo.setEmployeeName(dto.getEmployeeName());
                            vo.setAccountName(dto.getAccountName());
                            vo.setBelongStoreNameList(Lists.newArrayList(dto.getStoreName()));
                            vo.setRoleNameList(dto.getRoleNameList());
                            vo.setPhoneNumber(dto.getPhoneNumber());
                            vo.setApprovalStatus(dto.getApprovalStatus());
                            vo.setApprovalId(dto.getApprovalId());
                            vo.setDepPositionNameList(dto.getDepPositionList().stream().map(DepPositionInfoDTO::getPositionName).collect(Collectors.toList()));
                            return vo;
                        }
                ).collect(Collectors.toList());
    }

    private List<WaitToResignApplyVO> convertToVO(List<HireAndResignEmployeeDTO> dtoList) {
        return Optional
                .ofNullable(dtoList)
                .orElse(Lists.newArrayList())
                .stream()
                .map(
                        dto -> {
                            WaitToResignApplyVO vo = new WaitToResignApplyVO();
                            vo.setEmployeeId(dto.getEmployeeId());
                            vo.setEmployeeName(dto.getEmployeeName());
                            vo.setAccountName(dto.getAccountName());
                            vo.setBelongStoreNameList(Lists.newArrayList(dto.getStoreName()));
                            vo.setRoleNameList(dto.getRoleNameList());
                            vo.setPhoneNumber(dto.getPhoneNumber());
                            vo.setApplyStatus(dto.getApprovalStatus());
                            vo.setDepPositionNameList(dto.getDepPositionList().stream().map(DepPositionInfoDTO::getPositionName).collect(Collectors.toList()));
                            return vo;
                        }
                ).collect(Collectors.toList());
    }

    private ResignApprovalDetailVO convertToVO(ResignDetailDTO resignDetailDTO) {
        ResignApprovalDetailVO vo = new ResignApprovalDetailVO();
        vo.setAccountName(resignDetailDTO.getAccountName());
        vo.setEmployeeId(resignDetailDTO.getEmployeeId());
        vo.setEmployeeName(resignDetailDTO.getEmployeeName());
        vo.setBelongStoreNameList(Lists.newArrayList(resignDetailDTO.getStoreName()));
        vo.setPhoneNumber(resignDetailDTO.getPhoneNumber());
        vo.setRoleNameList(resignDetailDTO.getRoleNameList());
        vo.setResignDate(resignDetailDTO.getResignTime());
        vo.setResignType(resignDetailDTO.getResignType());
        vo.setResignText(resignDetailDTO.getResignTypeText());
        vo.setPoiOperationMode(resignDetailDTO.getPoiOperationMode());
        return vo;
    }
}
