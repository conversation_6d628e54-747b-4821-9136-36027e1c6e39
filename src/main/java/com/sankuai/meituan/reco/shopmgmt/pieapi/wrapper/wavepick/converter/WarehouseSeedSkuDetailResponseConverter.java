package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.wavepick.converter;

import com.sankuai.meituan.reco.pickselect.thrift.wave.dto.WarehouseWaveSeedSkuDTO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.WarehouseSeedSkuDetailResponse;
import org.mapstruct.Mapper;

/**
 * @Description:
 * @Author: zhangjian155
 * @Date: 2022/8/25 17:56
 */
@Mapper(componentModel = "spring", uses = {WarehouseProgressConverter.class, WarehouseSeedItemSkuConverter.class})
public abstract class WarehouseSeedSkuDetailResponseConverter {
    public abstract WarehouseSeedSkuDetailResponse convert2Response(WarehouseWaveSeedSkuDTO warehouseWaveSeedSkuDTO);
}
