package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.ApprovalVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.HireApprovalDetailVO;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-08-01
 * @email <EMAIL>
 */
@TypeDoc(
        description = "入职待审批/已审批列表请求返回"
)
@Data
@ApiModel("入职待审批/已审批列表请求返回")
@AllArgsConstructor
@NoArgsConstructor
public class QueryApprovalDetailResponse {

    @FieldDoc(
            description = "明细dto"
    )
    private HireApprovalDetailVO hireInfo;

    @FieldDoc(
            description = "审批信息list"
    )
    private List<ApprovalVO> approvalList;

    @FieldDoc(
            description = "明细dto"
    )
    private Integer approvalStatus;

    @FieldDoc(
            description = "明细dto"
    )
    private boolean canApproval;

}
