package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.FrontCategorySimpleVO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelSpuDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.FrontCategoryWithPathDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Title: ChannelSpuDetailVO
 * @Description:
 * @Author: zhaolei12
 * @Date: 2020/5/16 5:51 下午
 */
@TypeDoc(
        description = "渠道商品SPU详情信息"
)
@Data
@ApiModel("渠道商品SPU详情信息")
public class ChannelSpuDetailVO {

    @FieldDoc(
            description = "SPU编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "SPU编码", required = true)
    @NotNull
    private String spuId;

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID", required = true)
    @NotNull
    private Integer channelId;

    @FieldDoc(
            description = "商品状态", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品状态", required = true)
    @NotNull
    private Integer spuStatus;

    @FieldDoc(
            description = "门店商品多分类", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店商品多分类")
    private List<FrontCategorySimpleVO> frontCategories = new ArrayList<>();

    @FieldDoc(
            description = "门店商品多分类", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道类目信息")
    private ChannelCategoryVO channelCategory;

    @FieldDoc(
            description = "门店商品多分类", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道品牌信息")
    private ChannelBrandVO channelBrand;

    @FieldDoc(
            description = "渠道商品SKU信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道商品SKU信息")
    private List<ChannelSkuDetailVO> channelSkuList;

    public static List<ChannelSpuDetailVO> ofDTOList(List<ChannelSpuDTO> channelSpuDTOList) {
        if (CollectionUtils.isEmpty(channelSpuDTOList)) {
            return Lists.newArrayList();
        }

        return channelSpuDTOList.stream().filter(Objects::nonNull).map(ChannelSpuDetailVO::ofDTO).collect(Collectors.toList())
                .stream().filter(Objects::nonNull).collect(Collectors.toList());

    }

    public static ChannelSpuDetailVO ofDTO(ChannelSpuDTO channelSpuDTO) {
        if (channelSpuDTO == null) {
            return null;
        }

        ChannelSpuDetailVO channelSpuDetailVO = new ChannelSpuDetailVO();
        channelSpuDetailVO.setSpuId(channelSpuDTO.getSpuId());
        channelSpuDetailVO.setChannelId(channelSpuDTO.getChannelId());
        channelSpuDetailVO.setSpuStatus(channelSpuDTO.getSpuStatus());
        channelSpuDetailVO.setFrontCategories(convertFrontCategories(channelSpuDTO.getFrontCategories()));
        channelSpuDetailVO.setChannelCategory(ChannelCategoryVO.ofDTO(channelSpuDTO.getChannelCategory()));
        channelSpuDetailVO.setChannelBrand(ChannelBrandVO.ofDTO(channelSpuDTO.getChannelBrand()));
        channelSpuDetailVO.setChannelSkuList(ChannelSkuDetailVO.ofDTOList(channelSpuDTO.getChannelSkuList()));

        return channelSpuDetailVO;

    }

    private static List<FrontCategorySimpleVO> convertFrontCategories(List<FrontCategoryWithPathDTO> frontCategoryWithPathDTOList) {
        if (CollectionUtils.isEmpty(frontCategoryWithPathDTOList)) {
            return Lists.newArrayList();
        }
        List<FrontCategorySimpleVO> frontCategorySimpleVOList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(frontCategoryWithPathDTOList)){
            for (FrontCategoryWithPathDTO frontCategoryWithPathDTO : frontCategoryWithPathDTOList){
                FrontCategorySimpleVO frontCategorySimpleVO = new FrontCategorySimpleVO(frontCategoryWithPathDTO);
                frontCategorySimpleVOList.add(frontCategorySimpleVO);
            }
        }
        return frontCategorySimpleVOList;
    }

}
