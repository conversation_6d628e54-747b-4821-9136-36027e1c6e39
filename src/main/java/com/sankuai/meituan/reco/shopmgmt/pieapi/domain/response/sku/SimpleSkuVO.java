package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelDynamicInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.MedicalDeviceQuaInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.VideoVO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.SimpleSpuDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StandardProductDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.TenantSkuDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2021-03-01 14:51
 * @Description:
 */
@TypeDoc(
        description = "简要sku信息,来自于标品库或租户sku"
)
@Data
@ApiModel("简要sku信息,来自于标品库或租户sku")
public class SimpleSkuVO {

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称", required = true)
    private String name;

    @FieldDoc(
            description = "spu编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "spu编码", required = true)
    private String spuId;

    @FieldDoc(
            description = "sku", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "sku", required = true)
    private String skuId;

    @FieldDoc(
            description = "商品图片", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品图片", required = true)
    private List<String> imageList;

    @FieldDoc(
            description = "重量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "重量", required = false)
    private Integer weight;

    @FieldDoc(
            description = "带单位的重量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "带单位的重量", required = true)
    private String weightForUnit;

    @FieldDoc(
            description = "重量单位", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "重量单位", required = true)
    private String weightUnit;

    @FieldDoc(
            description = "售卖单位", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "售卖单位", required = true)
    private String saleUnit;

    @FieldDoc(
            description = "规格名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "规格名称", required = true)
    private String spec;

    @FieldDoc(
            description = "UPC信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "UPC信息", required = true)
    private String upc;

    @FieldDoc(
            description = "零售价，单位：分，仅租户为默认手动定价时展示", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "零售价，单位：分，仅租户为默认手动定价时展示")
    private Long storePrice;

    @FieldDoc(
            description = "渠道类目名称", requiredness = Requiredness.OPTIONAL
    )
    private String channelCategoryName;

    @FieldDoc(
            description = "渠道类目code", requiredness = Requiredness.OPTIONAL
    )
    private String channelCategoryCode;

    @FieldDoc(
            description = "渠道类目路径名称", requiredness = Requiredness.OPTIONAL
    )
    private String channelCategoryNamePath;
    @FieldDoc(
            description = "渠道类目路径code", requiredness = Requiredness.OPTIONAL
    )
    private String channelCategoryCodePath;

    @FieldDoc(
            description = "渠道类目动态信息", requiredness = Requiredness.OPTIONAL
    )
    private List<ChannelDynamicInfoVO> dynamicInfoVOList;


    @FieldDoc(
            description = "标品品牌名称", requiredness = Requiredness.OPTIONAL
    )
    private String brandName;

    @FieldDoc(
            description = "产地", requiredness = Requiredness.OPTIONAL
    )
    private String producingPlace;

    @FieldDoc(
            description = "数据源类型，商品池-1，标品库-2", requiredness = Requiredness.REQUIRED
    )
    private int datasourceType;

    @FieldDoc(
            description = "图详"
    )
    private List<String> pictureContents;

    @FieldDoc(
            description = "视频"
    )
    private VideoVO video;

    @FieldDoc(
            description = "是否标品 1-是、0-否"
    )
    @ApiModelProperty(value = "是否标品 1-是、0-否")
    private Integer standerType;

    @FieldDoc(
            description = "资质信息"
    )
    @ApiModelProperty(value = "资质信息")
    private MedicalDeviceQuaInfoVO medicalDeviceQuaInfo;

    @FieldDoc(
            description = "美团类目，类目来源1-零售，2-医药",
            example = {}
    )
    @ApiModelProperty(name = "美团类目，类目来源1-零售，2-医药")
    private Integer resourceType;

    public static SimpleSkuVO buildVO(SimpleSpuDTO dto) {
        SimpleSkuVO vo = new SimpleSkuVO();
        vo.setName(dto.getName());
        vo.setUpc(dto.getUpc());
        vo.setSpec(dto.getSpec());
        vo.setImageList(dto.getPicList());
        vo.setProducingPlace(dto.getProducingPlace());
        vo.setBrandName(dto.getBrandName());
        vo.setStorePrice(dto.getPrice());
        vo.setWeight(dto.getWeight());
        vo.setWeightForUnit(dto.getWeightForUnit());
        vo.setWeightUnit(dto.getWeightUnit());
        vo.setSaleUnit(dto.getUnit());
        dto.setProducingPlace(dto.getProducingPlace());
        if (Objects.nonNull(dto.getTenantSkuDTO())) {
            TenantSkuDTO tenantSkuDTO = dto.getTenantSkuDTO();
            vo.setSpuId(tenantSkuDTO.getSpuId());
            vo.setSkuId(tenantSkuDTO.getSkuId());
        }
        vo.setChannelCategoryName(dto.getCategoryName());
        vo.setChannelCategoryCode(dto.getCategoryCode());
        vo.setChannelCategoryNamePath(dto.getCategoryNamePath());
        vo.setChannelCategoryCodePath(dto.getCategoryCodePath());
        vo.setDynamicInfoVOList(ChannelDynamicInfoVO.ofBizDTOList(dto.getDynamicInfoBizDTOList()));
        vo.setDatasourceType(dto.getDatasourceType());

        vo.setPictureContents(dto.getPictureContents());
        vo.setVideo(VideoVO.convert(dto.getVideoInfo()));
        vo.setStanderType(dto.getStanderType());
        vo.setMedicalDeviceQuaInfo(MedicalDeviceQuaInfoVO.fromMedicalDeviceQuaDTO(dto.getMedicalDeviceQuaInfo()));
        vo.setResourceType(dto.getResourceType());
        return vo;
    }

    public static SimpleSkuVO buildVO(StandardProductDTO dto) {
        SimpleSkuVO vo = new SimpleSkuVO();
        vo.setName(dto.getName());
        vo.setUpc(dto.getUpc());
        vo.setSpec(dto.getSpec());
        vo.setImageList(dto.getPicList());
        vo.setProducingPlace(dto.getProducingPlace());
        vo.setBrandName(dto.getBrandName());
        vo.setStorePrice(dto.getPrice());
        vo.setWeight(dto.getWeight());
        vo.setSaleUnit(dto.getUnit());
        dto.setProducingPlace(dto.getProducingPlace());
        vo.setChannelCategoryName(dto.getCategoryName());
        vo.setChannelCategoryCode(dto.getCategoryCode());
        vo.setChannelCategoryNamePath(dto.getCategoryNamePath());
        vo.setChannelCategoryCodePath(dto.getCategoryCodePath());
        return vo;
    }

    public static List<SimpleSkuVO> buildVOList(List<StandardProductDTO> standardProductDTOS) {
        return standardProductDTOS.stream().filter(Objects::nonNull).map(SimpleSkuVO::buildVO).collect(Collectors.toList());
    }
}

