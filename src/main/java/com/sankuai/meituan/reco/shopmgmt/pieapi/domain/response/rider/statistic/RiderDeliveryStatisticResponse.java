package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.statistic;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/8/23 18:09
 **/
@ApiModel("骑手配送统计数据响应")
@TypeDoc(description = "骑手配送统计数据响应")
@Data
public class RiderDeliveryStatisticResponse {
    @ApiModelProperty("租户id")
    @FieldDoc(description = "租户id")
    private Long tenantId;

    @ApiModelProperty("骑手账号id")
    @FieldDoc(description = "骑手账号id")
    private Long riderAccountId;

    @ApiModelProperty("经营类型 1-直营前置仓 2-加盟前置仓 11-直营中心仓 12-3pl中心仓")
    @FieldDoc(description = "经营类型 1-直营前置仓 2-加盟前置仓 11-直营中心仓 12-3pl中心仓")
    private Integer operationMode;

    @ApiModelProperty("各个时间段的骑手配送统计数据")
    @FieldDoc(description = "各个时间段的骑手配送统计数据")
    List<RiderDeliveryStatisticVO> deliveryStatisticVOS;
}
