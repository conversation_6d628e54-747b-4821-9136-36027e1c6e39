package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.rider;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Nullable;
import javax.annotation.Resource;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.ImmutableList;
import com.meituan.shangou.goodscenter.dto.DepotGoodsDetailDto;
import com.meituan.shangou.goodscenter.dto.GoodsExpirationDto;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.shangou.goodscenter.dto.DepotGoodsDetailDto;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderItemModel;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderModel;
import com.meituan.shangou.saas.o2o.dto.model.ChildSkuModel;
import com.meituan.shangou.saas.o2o.dto.model.ComposeSkuModel;
import com.meituan.shangou.saas.order.management.client.dto.Status;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OCMSListViewIdConditionRequest;
import com.meituan.shangou.saas.order.management.client.dto.request.online.ViewIdCondition;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OCMSListViewIdConditionResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.*;
import com.meituan.shangou.saas.order.management.client.dto.response.revenue.OrderAmountInfo;
import com.meituan.shangou.saas.order.management.client.dto.response.revenue.OrderRevenueDetailResponse;
import com.meituan.shangou.saas.order.management.client.enums.SortByEnum;
import com.meituan.shangou.saas.order.management.client.enums.SortFieldEnum;
import com.meituan.shangou.saas.order.management.client.service.online.OCMSQueryThriftService;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.DeliveryStatusEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderBizTypeEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.sankuai.meituan.reco.fulfill.constants.PickingTaskStatusEnum;
import com.sankuai.meituan.reco.fulfill.constants.PickingWorkOrderStatusEnum;
import com.meituan.shangou.saas.order.platform.enums.*;
import com.sankuai.meituan.reco.pickselect.consts.TemperaturePropertyEnum;
import com.sankuai.meituan.reco.pickselect.dh.PickingProcessService;
import com.sankuai.meituan.reco.pickselect.dh.dto.TradeOutboundItemOperateDTO;
import com.sankuai.meituan.reco.pickselect.dh.dto.request.FinishPickRequest;
import com.sankuai.meituan.reco.pickselect.dh.dto.response.FinishPickResponse;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.RiderPickingThriftService;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.dto.*;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.request.*;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.response.ImmediatelyPushDownFulfillWorkOrderResponse;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.response.RiderFinishPickResponse;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.response.RiderOfflinePromotePickOrderResponse;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.response.RiderPickWorkOrderResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.delivery.third.ThirdDeliveryOrderVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.ParsedPropertiesVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConsumableMaterialParseUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ChannelTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.converters.channelorder.ChannelOrderConverter;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.delivery.ImmediatelyDeliveryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.picking.QueryPromotePickOrderRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.picking.RiderFinishPickingRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.picking.RiderPickTaskInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.picking.RiderPickingDetailVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.picking.PickConfigVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.facade.OrderBizFacade;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MemPageUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.delivery.TmsServiceWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.pick.FulfillmentOrderServiceWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.pick.TradeShippingOrderServiceWrapper;
import com.sankuai.meituan.reco.stock.operate.center.common.BizType;
import com.sankuai.meituan.reco.stock.operate.center.common.TemperatureType;
import com.sankuai.meituan.reco.stock.operate.center.thrift.LocationBatchRecommendThriftService;
import com.sankuai.meituan.reco.stock.operate.center.thrift.dto.RecommendLocationBatch;
import com.sankuai.meituan.reco.stock.operate.center.thrift.dto.RecommendSku;
import com.sankuai.meituan.reco.stock.operate.center.thrift.request.QueryLocationBatchListRequest;
import com.sankuai.meituan.reco.store.management.stock.biz.commons.constants.RepositoryType;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.DeliveryOrderType;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.RiderOperateThriftService;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request.RiderOperateTRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.response.RiderOperateTResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryOrder;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.qnh.ofc.ofw.client.thrift.common.base.OfcStatus;
import com.sankuai.qnh.ofc.ofw.client.thrift.dto.order.ChannelOrderKeyDTO;
import com.sankuai.qnh.ofc.ofw.client.thrift.dto.order.FulfillmentOrderDetailDTO;
import com.sankuai.qnh.ofc.ofw.client.thrift.request.operate.FulfillmentPickDispatchRequest;
import com.sankuai.qnh.ofc.ofw.client.thrift.request.order.BatchFulfillmentOrderIdKeyReq;
import com.sankuai.qnh.ofc.ofw.client.thrift.request.order.ChannelOrderIdKeyReq;
import com.sankuai.qnh.ofc.ofw.client.thrift.response.operate.FulfillmentPickDispatchResponse;
import com.sankuai.qnh.ofc.ofw.client.thrift.response.order.FulfillmentOrderDetailResponse;
import com.sankuai.qnh.ofc.ofw.client.thrift.service.operate.FulfillmentOperateThriftService;
import com.sankuai.qnh.ofc.ofw.client.thrift.service.order.FulfillmentOrderSearchThriftService;
import com.sankuai.qnh.ofc.transfer.common.consts.FulfillmentOrderStatusEnum;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.warehouse.TradeShippingOrderService;
import com.sankuai.shangou.logistics.warehouse.dto.AbnOrderDTO;
import com.sankuai.shangou.logistics.warehouse.dto.AbnOrderItemDTO;
import com.sankuai.shangou.logistics.warehouse.dto.TradeShippingOrderDTO;
import com.sankuai.shangou.logistics.warehouse.dto.TradeShippingOrderItemDTO;
import com.sankuai.shangou.logistics.warehouse.dto.request.QueryLocationListRequest;
import com.sankuai.shangou.logistics.warehouse.enums.TradeShippingOrderStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.meituan.reco.pickselect.consts.ResultCodeEnum.EXCEED_SHOULD_PICK_COUNT;
import static com.sankuai.meituan.reco.pickselect.consts.ResultCodeEnum.SUCCESS;
import static com.sankuai.meituan.reco.pickselect.consts.TemperaturePropertyEnum.*;
import static com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConsumableMaterialParseUtils.parseConsumableMaterialInfo;
import static com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.OCMSOrderServiceWrapper.transfer2GoodsItemVO;

/**
 * 拣配一体相关服务.
 *
 * <AUTHOR>
 * @since 2021/11/18 17:36
 */
@Service
@Slf4j
@Rhino
public class RiderPickingServiceWrapper {

    @Resource
    private OCMSQueryThriftService ocmsQueryThriftService;

    @Resource
    private RiderPickingThriftService riderPickingRpcService;

    @Resource
    private RiderOperateThriftService riderOperateRpcService;

    @Resource
    private LocationBatchWrapper locationBatchWrapper;

    @Resource
    private OrderBizFacade orderBizFacade;

    @Resource
    private AuthThriftWrapper authThriftWrapper;

    @Resource
    private OCMSOrderServiceWrapper ocmsOrderServiceWrapper;

    @Resource
    private TmsServiceWrapper tmsServiceWrapper;

    @Resource
    private GoodsCenterWrapper goodsCenterWrapper;

    @Resource
    private AbnOrderRemoteService abnOrderRemoteService;
    @Resource
    private DepotGoodsService depotGoodsService;
    @Resource
    private TradeShippingOrderServiceWrapper tradeShippingOrderServiceWrapper;

    @Resource
    private AbnOrderServiceWrapper abnOrderServiceWrapper;
    @Resource
    private RecommendLocationServiceWrapper recommendLocationServiceWrapper;
    @Resource
    private PickingProcessService pickingProcessService;
    @Resource
    private FulfillmentOrderServiceWrapper fulfillmentOrderServiceWrapper;

    @Resource
    private SupplyProductTagWrapper supplyProductTagWrapper;


    private final int OUT_STOCK_OPERATE = 2;

    private static int LACK_STOCK_CODE = 20000099;

    /**
     * 查询骑手待拣货的拣货工单信息.
     *
     * @param channelId       订单渠道
     * @param channelOrderId  渠道订单号
     * @param deliveryOrderId 运单 ID
     * @return 拣货工单信息
     */
    public CommonResponse<RiderPickingDetailVO> queryWaitPickDetail(int channelId, String channelOrderId, Long deliveryOrderId) {
        // 1. 基本参数校验
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        List<Long> storeIdList = identityInfo.getStoreIdList();
        long tenantId = identityInfo.getUser().getTenantId();
        if (CollectionUtils.isEmpty(storeIdList) || storeIdList.size() > 1) {
            log.error("骑手订单展示只支持单门店模式：storeIdList={}", storeIdList);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "只支持单门店模式");
        }
        long storeId = storeIdList.get(0);
        DynamicOrderBizType orderBizType = DynamicOrderBizType.findOf(ChannelOrderConvertUtils.sourceMid2Biz(channelId));
        if (orderBizType == null) {
            log.warn("订单渠道无效, channelId:{}", channelId);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "订单渠道无效");
        }
        try {
            // 2. 获取订单信息并校验
            OCMSOrderVO ocmsOrder = getOCMSOrder(orderBizType.getValue(), channelOrderId);
            DeliveryStatusEnum pickStatus = Optional.ofNullable(ocmsOrder.getOcmsDeliveryInfoVO()).map(OnlineBaseDeliveryInfoVO::getDeliveryStatus)
                    .map(DeliveryStatusEnum::enumOf).orElse(null);
            OrderStatusEnum orderStatus = OrderStatusEnum.enumOf(ocmsOrder.getOrderStatus());
            // 若订单状态为已拣货或已取消，则放弃查询
            if (pickStatus == DeliveryStatusEnum.PICKED) {
                log.warn("拣货已完成，不支持进行扫码拣货, pickStatus:{}, orderStatus:{}", pickStatus, orderStatus);
                Cat.logEvent("DH_ADAPT_SN", "ORDER_IS_COMPLETE");
                return CommonResponse.fail(ResultCode.RIDER_PICK_ALREADY_COMPLETED.getCode(), ResultCode.RIDER_PICK_ALREADY_COMPLETED.getErrorMessage());
            }

            if (pickStatus == DeliveryStatusEnum.CANCELED || orderStatus == OrderStatusEnum.CANCELED) {
                log.warn("拣货已取消，不支持进行扫码拣货, pickStatus:{}, orderStatus:{}", pickStatus, orderStatus);
                return CommonResponse.fail(ResultCode.RIDER_PICK_ALREADY_CANCELED.getCode(), ResultCode.RIDER_PICK_ALREADY_CANCELED.getErrorMessage());
            }

            // 3. 查询拣货工单信息
            long operatorAccountId = identityInfo.getUser().getAccountId();
            if (MccConfigUtil.isNewPickGrayStore(storeId)) {
                List<TradeShippingOrderDTO> tradeShippingOrderDTOS = tradeShippingOrderServiceWrapper.getByTradeOrderNos(storeId, orderBizType.getValue(), Lists.newArrayList(channelOrderId));
                if (CollectionUtils.isEmpty(tradeShippingOrderDTOS)) {
                    throw new CommonRuntimeException("未查询到出库单");
                }
                TradeShippingOrderDTO tradeShippingOrderDTO = tradeShippingOrderDTOS.get(0);

                QueryLocationListRequest request = new QueryLocationListRequest();
                request.setMerchantId(ocmsOrder.getTenantId());
                request.setRepositoryId(ocmsOrder.getShopId());
                request.setRecommendSkus(
                        tradeShippingOrderDTO.getItems().stream().map(
                                tradeShippingOrderItemDTO -> {
                                    com.sankuai.shangou.logistics.warehouse.dto.RecommendSku recommendSku = new com.sankuai.shangou.logistics.warehouse.dto.RecommendSku();
                                    recommendSku.setSkuId(tradeShippingOrderItemDTO.getSkuId());
                                    recommendSku.setRequestQuantity(tradeShippingOrderItemDTO.getActualQty());
                                    recommendSku.setTemperatureType(convertTemperatureAttribute2TemperatureType(tradeShippingOrderItemDTO.getTemperatureZoneCode()));
                                    return recommendSku;
                                }
                        ).collect(Collectors.toList())
                );

                List<com.sankuai.shangou.logistics.warehouse.dto.RecommendLocationBatch> recommendLocationBatches = recommendLocationServiceWrapper.recommendLocations(request);

                Map<com.sankuai.shangou.logistics.warehouse.dto.RecommendLocationBatch.SkuInfo, ArrayList<com.sankuai.shangou.logistics.warehouse.dto.RecommendLocationBatch>> skuRecommendLocationListMap =
                        Optional.ofNullable(recommendLocationBatches).orElse(Lists.newArrayList())
                        .stream()
                        .collect(Collectors.toMap(
                                com.sankuai.shangou.logistics.warehouse.dto.RecommendLocationBatch::getSku,
                                Lists::newArrayList,
                                (older, newer) -> {
                                    newer.addAll(older);
                                    return newer;
                                }
                        ));

                //6.查询保质期
                Map<String, GoodsExpirationDto> goodsExpirationMap = getGoodsExpirationMap(tenantId, storeId, tradeShippingOrderDTO);
                RiderPickingDetailVO pickingDetail = buildPickingDetail(ocmsOrder, tradeShippingOrderDTOS.get(0), deliveryOrderId, skuRecommendLocationListMap, goodsExpirationMap);
                fillLackStockGoodsInfo(pickingDetail);

                //填充高价值标签
                fillHighPriceTag(pickingDetail);
                return CommonResponse.success(pickingDetail);
            }
            RiderPickWorkOrderDTO pickWorkOrder = getPickWorkOrder(tenantId, storeIdList.get(0), operatorAccountId, orderBizType, channelOrderId);
            if (pickWorkOrder.getPickWoStatus() == PickingWorkOrderStatusEnum.EXPIRE.getCode() || pickWorkOrder.getPickWoStatus() == PickingWorkOrderStatusEnum.PICK_DONE.getCode()) {
                log.warn("拣货工单已结束，不支持进行扫码拣货, pickStatus:{}, orderStatus:{}", pickStatus, orderStatus);
                return CommonResponse.fail(ResultCode.RIDER_PICK_ALREADY_COMPLETED.getCode(), ResultCode.RIDER_PICK_ALREADY_COMPLETED.getErrorMessage());
            }else if (pickWorkOrder.getPickWoStatus() == PickingWorkOrderStatusEnum.CANCELED.getCode()) {
                log.warn("拣货工单已取消，不支持进行扫码拣货, pickStatus:{}, orderStatus:{}", pickStatus, orderStatus);
                return CommonResponse.fail(ResultCode.RIDER_PICK_ALREADY_CANCELED.getCode(), ResultCode.RIDER_PICK_ALREADY_CANCELED.getErrorMessage());
            }

            //4. 查询库位信息
            HashMap<RecommendLocationBatch.SkuInfo, List<RecommendLocationBatch>> skuRecommendLocationBatchMap = new HashMap<>();
            if(MccConfigUtil.isGrayStore4SplitByTemperature(storeId)) {
                skuRecommendLocationBatchMap = getRecommendLocationBatchesMap(tenantId, storeId, pickWorkOrder);
            }


            //6.查询保质期
            Map<String, GoodsExpirationDto> goodsExpirationMap = getGoodsExpirationMap(tenantId, storeId, pickWorkOrder);

            RiderPickingDetailVO pickingDetail = buildPickingDetail(ocmsOrder, pickWorkOrder, deliveryOrderId,
                    skuRecommendLocationBatchMap, goodsExpirationMap);

            //填充高价值标签
            fillHighPriceTag(pickingDetail);
            return CommonResponse.success(pickingDetail);
        } catch (CommonRuntimeException e) {
            log.warn("RiderPickingServiceWrapper.queryWaitPickDetail fail", e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), e.getMessage());
        }


    }



    //失败不阻塞流程
    private void fillHighPriceTag(RiderPickingDetailVO pickingDetail) {
        try {
            if (!MccConfigUtil.isDrunkHorseTenant(pickingDetail.getTenantId()) || !MccConfigUtil.isHighPriceTagGrayStore(pickingDetail.getStoreId())) {
                return;
            }

            List<String> skuIds = pickingDetail.getWaitPickTasks().stream()
                    .map(RiderPickTaskInfoVO::getSkuId)
                    .distinct()
                    .collect(Collectors.toList());
            Map<String, Boolean> highPriceSkuMap =
                    supplyProductTagWrapper.batchGetProductHighPriceTag(pickingDetail.getTenantId(), skuIds);

            for (RiderPickTaskInfoVO pickTaskInfoVO: pickingDetail.getWaitPickTasks()) {
                pickTaskInfoVO.setIsHighWacGoods(highPriceSkuMap.getOrDefault(pickTaskInfoVO.getSkuId(), false));
            }
        } catch (Exception e) {
            log.error("查询高价值标签失败", e);
            Cat.logEvent("HIGH_PRICE_TAG", "QUERY_FAIL");
        }

    }

    private HashMap<RecommendLocationBatch.SkuInfo, List<RecommendLocationBatch>> getRecommendLocationBatchesMap(Long tenantId, Long storeId, RiderPickWorkOrderDTO pickWorkOrder) {
        try {
            List<RecommendLocationBatch> recommendLocationBatches = locationBatchWrapper.queryLocationBatchRecommend(buildQueryLocationBatchListRequest(tenantId, storeId, pickWorkOrder));

            //库位推荐结果map化
            return getRecommendLocationBatchesMap(recommendLocationBatches);

        } catch (Exception e) {
            // 查询推荐库位失败不阻塞拣货流程
            log.warn("查询库位推荐失败, pickWorkOrder: {}", pickWorkOrder, e);
            return new HashMap<>();
        }

    }

    private Map<String, GoodsExpirationDto> getGoodsExpirationMap(Long tenantId, Long storeId, RiderPickWorkOrderDTO pickWorkOrder) {
        try {
            if(!MccConfigUtil.isPickPhotoConfigurableGrayStore(storeId)) {
                return Collections.emptyMap();
            }

            List<String> skuIds = pickWorkOrder.getPickTasks().stream()
                    .map(RiderPickTaskDTO::getStoreSkuId)
                    .distinct()
                    .collect(Collectors.toList());
            List<DepotGoodsDetailDto> depotGoodsDetailDtos = goodsCenterWrapper.queryGoodsInfo(tenantId, storeId, skuIds);
            return depotGoodsDetailDtos.stream()
                    .collect(Collectors.toMap(DepotGoodsDetailDto::getGoodsId, DepotGoodsDetailDto::getExpirationInfo, (k1,k2) -> k2));
        } catch (Exception e) {
            log.warn("查询商品保质期失败", e);
            Cat.logEvent("SWITCH_TO_GOODS_CENTER", "GET_GOODS_EXPIRATION_FAIL");
            return Collections.emptyMap();
        }
    }

    private Map<String, GoodsExpirationDto> getGoodsExpirationMap(Long tenantId, Long storeId, TradeShippingOrderDTO tradeShippingOrderDTO) {
        try {
            if(!MccConfigUtil.isPickPhotoConfigurableGrayStore(storeId)) {
                return Collections.emptyMap();
            }

            List<String> skuIds = tradeShippingOrderDTO.getItems().stream()
                    .map(TradeShippingOrderItemDTO::getSkuId)
                    .distinct()
                    .collect(Collectors.toList());
            List<DepotGoodsDetailDto> depotGoodsDetailDtos = goodsCenterWrapper.queryGoodsInfo(tenantId, storeId, skuIds);
            return depotGoodsDetailDtos.stream()
                    .collect(Collectors.toMap(DepotGoodsDetailDto::getGoodsId, DepotGoodsDetailDto::getExpirationInfo, (k1,k2) -> k2));
        } catch (Exception e) {
            log.warn("查询商品保质期失败", e);
            Cat.logEvent("SWITCH_TO_GOODS_CENTER", "GET_GOODS_EXPIRATION_FAIL");
            return Collections.emptyMap();
        }
    }

    /**
     * 构造（拣配一体）拣货详情返回结果
     *
     * @param ocmsOrder       订单信息
     * @param pickWorkOrder   拣货工单信息
     * @param deliveryOrderId
     * @return
     */
    @Deprecated
    private RiderPickingDetailVO buildPickingDetail(OCMSOrderVO ocmsOrder, RiderPickWorkOrderDTO pickWorkOrder,
                                                    Long deliveryOrderId,
                                                    HashMap<RecommendLocationBatch.SkuInfo, List<RecommendLocationBatch>> recommendLocationBatchesMap,
                                                    Map<String, GoodsExpirationDto> goodsExpirationMap) {
        OCMSDeliveryInfoVO ocmsDeliveryInfoVO = ocmsOrder.getOcmsDeliveryInfoVO();
        // 构造拣货详情返回结果
        RiderPickingDetailVO pickingDetail = new RiderPickingDetailVO();
        pickingDetail.setTenantId(ocmsOrder.getTenantId());
        Integer channelId = ChannelOrderConvertUtils.sourceBiz2Mid(ocmsOrder.getOrderBizType());
        pickingDetail.setChannelId(channelId);
        pickingDetail.setChannelName(ChannelTypeEnum.findChannelNameByChannelId(channelId));
        pickingDetail.setStoreId(ocmsOrder.getShopId());
        pickingDetail.setStoreName(ocmsOrder.getShopName());
        pickingDetail.setDeliveryOrderId(deliveryOrderId);
        pickingDetail.setChannelOrderId(ocmsOrder.getViewOrderId());
        pickingDetail.setSerialNo(ocmsOrder.getOrderSerialNumber());
        pickingDetail.setIsWeakCheckSn(pickWorkOrder.getIsWeakCheckSn());
        int deliveryOrderType = ocmsOrder.getIsBooking() == 1 ? DeliveryOrderType.DELIVERY_BY_BOOK_TIME.getValue() :
                DeliveryOrderType.DELIVERY_RIGHT_NOW.getValue();
        pickingDetail.setDeliveryOrderType(deliveryOrderType);
        pickingDetail.setDeliveryOrderTypeName(getDeliveryOrderTypeName(deliveryOrderType));
        pickingDetail.setCreateTime(ocmsOrder.getCreateTime());
        if (ocmsDeliveryInfoVO != null) {
            pickingDetail.setEstimateArriveTimeStart(ocmsDeliveryInfoVO.getArrivalTime());
            pickingDetail.setEstimateArriveTimeEnd(ocmsDeliveryInfoVO.getArrivalEndTime());

            // 自提配送 or 送货上门
            pickingDetail.setDeliveryMethod(ocmsDeliveryInfoVO.getDistributeMethod());
            pickingDetail.setDeliveryMethodDesc(ocmsDeliveryInfoVO.getDistributeMethodName());

            pickingDetail.setReceiverName(ocmsDeliveryInfoVO.getUserName());
            pickingDetail.setReceiverPhone(ocmsDeliveryInfoVO.getUserPhone());
            pickingDetail.setReceiverAddress(ocmsDeliveryInfoVO.getUserAddress());
        }

        pickingDetail.setPickWorkOrderId(pickWorkOrder.getPickWoId());
        pickingDetail.setPickWorkOrderStatus(pickWorkOrder.getPickWoStatus());
        pickingDetail.setPickWorkOrderStatusDesc(PickingWorkOrderStatusEnum.fromCode(pickWorkOrder.getPickWoStatus()).name());
        List<RiderPickTaskDTO> pickTaskDtos = pickWorkOrder.getPickTasks();
        pickingDetail.setItemCount(getTotalPickCount(pickTaskDtos));
        // 备注不为空且不为 0 才展示
        if (StringUtils.isNotEmpty(ocmsOrder.getComments()) && !ocmsOrder.getComments().equals("0")) {
            pickingDetail.setComments(ocmsOrder.getComments());
        }

        List<RiderPickTaskInfoVO> pickTasks = buildPickTaskVos(pickWorkOrder.getStoreId(), pickTaskDtos, recommendLocationBatchesMap, goodsExpirationMap);
        pickingDetail.setWaitPickTasks(pickTasks);
        pickingDetail.setConsumableMaterialInfoList(pickWorkOrder.getConsumableMaterialInfos());
        return pickingDetail;
    }

    private void fillLackStockGoodsInfo(RiderPickingDetailVO riderPickingDetailVO) {
        try {
            //过滤非歪马租户
            if (!MccConfigUtil.isDrunkHorseTenant(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId())) {
                return;
            }

            //查异常单
            Optional<AbnOrderDTO> abnOrderOpt = abnOrderRemoteService.getUnprocessedAbnOrderBySource(riderPickingDetailVO.getStoreId(),
                    riderPickingDetailVO.getChannelId(), riderPickingDetailVO.getChannelOrderId());

            if (!abnOrderOpt.isPresent() || CollectionUtils.isEmpty(abnOrderOpt.get().getItems())) {
                return;
            }

            AbnOrderDTO abnOrder = abnOrderOpt.get();

            List<String> lackStockGoodsIds = abnOrder.getItems().stream().map(AbnOrderItemDTO::getSkuId).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(lackStockGoodsIds)) {
                return;
            }

            Map<String, DepotGoodsDetailDto> goodsDetailDtoMap = depotGoodsService.queryByGoodsIds(riderPickingDetailVO.getTenantId(), riderPickingDetailVO.getStoreId(), lackStockGoodsIds);

            List<RiderPickingDetailVO.LackStockGoodsVO> lackStockGoodsList = abnOrder.getItems().stream()
                    .map(item -> this.buildLackStockGoodsVO(item, goodsDetailDtoMap.get(item.getSkuId())))
                    .collect(Collectors.toList());

            //填充缺货货品列表
            riderPickingDetailVO.setLackStockGoodsList(lackStockGoodsList);

            //如果有缺货的货品,需要在对应的销售商品上标识出来
            if (CollectionUtils.isNotEmpty(lackStockGoodsList)) {

                //调用订单拿到商品组合关系
                BizOrderModel bizOrderModel = orderBizFacade.queryOrderDetailWithComposeSku(riderPickingDetailVO.getTenantId(), riderPickingDetailVO.getStoreId(), riderPickingDetailVO.getChannelId(), riderPickingDetailVO.getChannelOrderId());
                if (bizOrderModel == null) {
                    return;
                }

                //标识出组合关系中包含缺货货品的销售商品
                List<Long> lackStockOrderItemIds = bizOrderModel.getComposeSkuModels().stream().filter(
                        composeSkuModel -> {
                            Optional<String> lackStockSkuIdOpt = composeSkuModel.getChildItemList().stream()
                                    .map(ChildSkuModel::getSkuId)
                                    .filter(lackStockGoodsIds::contains)
                                    .findAny();
                            return lackStockSkuIdOpt.isPresent();
                        }).map(ComposeSkuModel::getOrderItemId).collect(Collectors.toList());

                List<String> lackStockOrderItemSkuIds = bizOrderModel.getBizOrderItemModelList().stream()
                        .filter(orderItem -> lackStockOrderItemIds.contains(orderItem.getOrderItemId()))
                        .map(BizOrderItemModel::getInstoreSkuId2)
                        .collect(Collectors.toList());

                riderPickingDetailVO.getWaitPickTasks().forEach(
                        taskInfoVO -> taskInfoVO.setIsIncludeStockLackGoods(lackStockOrderItemSkuIds.contains(taskInfoVO.getSkuId()))
                );
            }

        } catch (Exception e) {
            log.error("填充缺货信息失败", e);
            Cat.logEvent("PART_REFUND", "FILL_ABN_ORDER_ITEM_FAIL");
        }
    }

    private RiderPickingDetailVO.LackStockGoodsVO buildLackStockGoodsVO(AbnOrderItemDTO abnOrderItemDTO, DepotGoodsDetailDto goods) {
        if (Objects.isNull(goods)) {
            log.error("未获取到货品信息, skuId: {}", abnOrderItemDTO.getSkuId());
            throw new CommonRuntimeException("未获取到货品信息");
        }

        return RiderPickingDetailVO.LackStockGoodsVO.builder()
                .goodsName(goods.getGoodsName())
                .skuId(abnOrderItemDTO.getSkuId())
                .picUrl(parseRealPicUrl(goods))
                .spec(goods.getSpecName())
                .upcList(goods.getUpcList())
                .needCount(abnOrderItemDTO.getDemandQuantity().intValue())
                .salableCount(abnOrderItemDTO.getAvailableQuantity().intValue())
                .lackCount(abnOrderItemDTO.getDemandQuantity().subtract(abnOrderItemDTO.getAvailableQuantity()).intValue())
                .build();
    }

    private String parseRealPicUrl(DepotGoodsDetailDto goods) {
        if (Objects.isNull(goods) || Objects.isNull(goods.getGoodsPic())
                || CollectionUtils.isEmpty(goods.getGoodsPic().getRealPicUrlList())) {
            return "";
        }
        return goods.getGoodsPic().getRealPicUrlList().get(0);
    }

    /**
     * 构造（拣配一体）拣货详情返回结果
     *
     * @param ocmsOrder       订单信息
     * @param deliveryOrderId
     * @return
     */
    private RiderPickingDetailVO buildPickingDetail(OCMSOrderVO ocmsOrder, TradeShippingOrderDTO tradeShippingOrderDTO,
                                                    Long deliveryOrderId,
                                                    Map<com.sankuai.shangou.logistics.warehouse.dto.RecommendLocationBatch.SkuInfo, ArrayList<com.sankuai.shangou.logistics.warehouse.dto.RecommendLocationBatch>> skuRecommendLocationListMap,
                                                    Map<String, GoodsExpirationDto> goodsExpirationMap) {
        OCMSDeliveryInfoVO ocmsDeliveryInfoVO = ocmsOrder.getOcmsDeliveryInfoVO();
        // 构造拣货详情返回结果
        RiderPickingDetailVO pickingDetail = new RiderPickingDetailVO();
        pickingDetail.setTenantId(ocmsOrder.getTenantId());
        Integer channelId = ChannelOrderConvertUtils.sourceBiz2Mid(ocmsOrder.getOrderBizType());
        pickingDetail.setChannelId(channelId);
        pickingDetail.setChannelName(ChannelTypeEnum.findChannelNameByChannelId(channelId));
        pickingDetail.setStoreId(ocmsOrder.getShopId());
        pickingDetail.setStoreName(ocmsOrder.getShopName());
        pickingDetail.setDeliveryOrderId(deliveryOrderId);
        pickingDetail.setChannelOrderId(ocmsOrder.getViewOrderId());
        pickingDetail.setSerialNo(ocmsOrder.getOrderSerialNumber());
        pickingDetail.setIsWeakCheckSn(MccConfigUtil.checkIsSnWeekCheckStore(ocmsOrder.getShopId()));
        int deliveryOrderType = ocmsOrder.getIsBooking() == 1 ? DeliveryOrderType.DELIVERY_BY_BOOK_TIME.getValue() :
                DeliveryOrderType.DELIVERY_RIGHT_NOW.getValue();
        pickingDetail.setDeliveryOrderType(deliveryOrderType);
        pickingDetail.setDeliveryOrderTypeName(getDeliveryOrderTypeName(deliveryOrderType));
        pickingDetail.setCreateTime(ocmsOrder.getCreateTime());
        if (ocmsDeliveryInfoVO != null) {
            pickingDetail.setEstimateArriveTimeStart(ocmsDeliveryInfoVO.getArrivalTime());
            pickingDetail.setEstimateArriveTimeEnd(ocmsDeliveryInfoVO.getArrivalEndTime());

            // 自提配送 or 送货上门
            pickingDetail.setDeliveryMethod(ocmsDeliveryInfoVO.getDistributeMethod());
            pickingDetail.setDeliveryMethodDesc(ocmsDeliveryInfoVO.getDistributeMethodName());

            pickingDetail.setReceiverName(ocmsDeliveryInfoVO.getUserName());
            pickingDetail.setReceiverPhone(ocmsDeliveryInfoVO.getUserPhone());
            pickingDetail.setReceiverAddress(ocmsDeliveryInfoVO.getUserAddress());
        }

        pickingDetail.setPickWorkOrderId(Long.parseLong(tradeShippingOrderDTO.getOrderNo()));
        pickingDetail.setPickWorkOrderStatus(tradeShippingOrderDTO.getStatus());
        //fixme: 和前端确认了，这行没用到。但这里历史逻辑包袱太重，暂时不删了。
        pickingDetail.setPickWorkOrderStatusDesc(TradeShippingOrderStatus.valueOfCode(tradeShippingOrderDTO.getStatus()).name());
        List<TradeShippingOrderItemDTO> itemList = tradeShippingOrderDTO.getItems();
        pickingDetail.setItemCount(getTotalCount(itemList));
        // 备注不为空且不为 0 才展示
        if (StringUtils.isNotEmpty(ocmsOrder.getComments()) && !ocmsOrder.getComments().equals("0")) {
            pickingDetail.setComments(ocmsOrder.getComments());
        }

        List<RiderPickTaskInfoVO> pickTasks = buildNewPickTaskVos(ocmsOrder.getShopId(), itemList, skuRecommendLocationListMap, goodsExpirationMap);
        pickingDetail.setWaitPickTasks(pickTasks);

        pickingDetail.setConsumableMaterialInfoList(parseConsumableMaterialInfo(ocmsOrder));
        return pickingDetail;
    }

    private List<TConsumableMaterialInfo> parseConsumableMaterialInfo(OCMSOrderVO ocmsOrderVO) {
        try {
            log.info("开始解析耗材信息, storeID:{}, viewOrderId: {}", ocmsOrderVO.getShopId(), ocmsOrderVO.getViewOrderId() );
            List<TConsumableMaterialInfo> consumableMaterialInfos = ConsumableMaterialParseUtils.parseConsumableInfoFromBizOrderItemModel(ocmsOrderVO.getOcmsOrderItemVOList());
            log.info("结束解析耗材信息, consumableMaterialInfos:{}", consumableMaterialInfos);
            return consumableMaterialInfos;
        } catch (Exception e) {
            Cat.logEvent("CONSUME_CHECK", "PARSE_CONSUMABLE_MATERIAL_INFO_FAIL");
            log.error("解析耗材数量失败, viewOrderId: {}", ocmsOrderVO.getViewOrderId(), e);
            return Collections.emptyList();
        }

    }

    /**
     * 推荐批次库位列表map化
     * @param recommendLocationBatches
     * @return
     */
    private HashMap<RecommendLocationBatch.SkuInfo, List<RecommendLocationBatch>> getRecommendLocationBatchesMap(
            List<RecommendLocationBatch> recommendLocationBatches) {
        HashMap<RecommendLocationBatch.SkuInfo, List<RecommendLocationBatch>> skuRecommendLocationBatchMap = new HashMap<>();
        for (RecommendLocationBatch recommendLocationBatch:recommendLocationBatches) {
            if (!skuRecommendLocationBatchMap.containsKey(recommendLocationBatch.getSku())) {
                skuRecommendLocationBatchMap.put(recommendLocationBatch.getSku(), new ArrayList<>());
            }
            skuRecommendLocationBatchMap.get(recommendLocationBatch.getSku()).add(recommendLocationBatch);
        }

        return skuRecommendLocationBatchMap;
    }

    @Deprecated
    private List<RiderPickTaskInfoVO> buildPickTaskVos(Long storeId, List<RiderPickTaskDTO> pickTaskDtos,
                                                       HashMap<RecommendLocationBatch.SkuInfo,
                                                       List<RecommendLocationBatch>> skuRecommendLocationBatchMap,
                                                       Map<String, GoodsExpirationDto> goodsExpirationMap) {
        if (CollectionUtils.isEmpty(pickTaskDtos)) {
            return Collections.emptyList();
        }

        return pickTaskDtos.stream().map(taskDto -> {
                    RiderPickTaskInfoVO taskVo = new RiderPickTaskInfoVO();
                    taskVo.setPickTaskId(taskDto.getPickTaskId());
                    taskVo.setPickTaskStatus(taskDto.getPickTaskStatus());
                    taskVo.setPickTaskStatusDesc(PickingTaskStatusEnum.fromCode(taskDto.getPickTaskStatus()).name());
                    taskVo.setSkuId(taskDto.getStoreSkuId());
                    taskVo.setSkuName(taskDto.getSkuName());
                    taskVo.setUpcCodes(buildUpcAndBoxCodeSet(taskDto));
                    taskVo.setPicUrl(taskDto.getPicUrl());
                    taskVo.setSpecification(taskDto.getSpecification());
                    taskVo.setCount(taskDto.getCount() - Optional.ofNullable(taskDto.getRefundCount()).orElse(0));
                    taskVo.setHavePartRefundFlag(Objects.nonNull(taskDto.getRefundCount()) && taskDto.getRefundCount() > 0);
                    taskVo.setTemperatureAttributeType(taskDto.getTemperatureAttribute());
                    taskVo.setIsSnProduct(taskDto.getIsManagementSnCode());
                    taskVo.setRealPicUrlList(taskDto.getRealPicUrls());
                    //由于前后端灰度节奏不同，前端比后端先全量，这个字段用来告诉前端展示实拍图还是展示原来的商品图片
                    taskVo.setShowRealPic(MccConfigUtil.isReadGoodsGrayStore(storeId));

                    fillLocationBatchInfo(taskDto, taskVo, skuRecommendLocationBatchMap);
                    fillExpirationInfo(taskVo, goodsExpirationMap);

                    return taskVo;
                }
        ).sorted(this::compareByTemperatureAttribute).collect(Collectors.toList());
    }

    private List<RiderPickTaskInfoVO> buildNewPickTaskVos(long warehouseId, List<TradeShippingOrderItemDTO> itemList,
                                                          Map<com.sankuai.shangou.logistics.warehouse.dto.RecommendLocationBatch.SkuInfo, ArrayList<com.sankuai.shangou.logistics.warehouse.dto.RecommendLocationBatch>> skuRecommendLocationListMap,
                                                          Map<String, GoodsExpirationDto> goodsExpirationMap) {
        if (CollectionUtils.isEmpty(itemList)) {
            return Collections.emptyList();
        }

        return itemList.stream().map(taskDto -> {
                    RiderPickTaskInfoVO taskVo = new RiderPickTaskInfoVO();
                    taskVo.setPickTaskId(taskDto.getId());
                    taskVo.setSkuId(taskDto.getSkuId());
                    taskVo.setSkuName(taskDto.getSkuName());
                    taskVo.setUpcCodes(buildUpcAndBoxCodeSet(taskDto));
                    if (CollectionUtils.isNotEmpty(taskDto.getImgUrls())) {
                        taskVo.setPicUrl(taskDto.getImgUrls().get(0));
                    }
                    taskVo.setSpecification(taskDto.getSpecification());
                    taskVo.setCount(taskDto.getActualQty().intValue());
                    taskVo.setHavePartRefundFlag(Objects.nonNull(taskDto.getRefundQty()) && taskDto.getRefundQty().compareTo(BigDecimal.ZERO) > 0);
                    taskVo.setTemperatureAttributeType(Optional.ofNullable(taskDto.getTemperatureZoneCode()).orElse(StringUtils.EMPTY));
                    taskVo.setIsSnProduct(taskDto.getIsManagementSnCode());
                    taskVo.setRealPicUrlList(taskDto.getImgUrls());
                    taskVo.setShowRealPic(MccConfigUtil.isReadGoodsGrayStore(warehouseId));
                    fillNewLocationBatchInfo(taskDto, taskVo, skuRecommendLocationListMap);
                    fillExpirationInfo(taskVo, goodsExpirationMap);
                    return taskVo;
                }
        ).sorted(this::compareByTemperatureAttribute).collect(Collectors.toList());
    }



    /**
     * 填充库位批次信息，如果出现异常返回空库位，不阻塞扫码出库主流程
     * @param taskDto
     * @param taskVo
     * @param skuRecommendLocationBatchMap
     */
    @Deprecated
    private void fillLocationBatchInfo(RiderPickTaskDTO taskDto, RiderPickTaskInfoVO taskVo,
                                       HashMap<RecommendLocationBatch.SkuInfo, List<RecommendLocationBatch>> skuRecommendLocationBatchMap) {
        try {
            //填充库位信息、批次信息和所需温区库存是否充足
            RecommendLocationBatch.SkuInfo skuInfo = new RecommendLocationBatch.SkuInfo(taskDto.getStoreSkuId(),
                    convert2TemperatureAttributeType(taskDto.getTemperatureAttribute()));
            List<RecommendLocationBatch> recommendLocationBatches = skuRecommendLocationBatchMap.getOrDefault(skuInfo, Collections.emptyList());

            List<RecommendLocationBatch.BatchInfo> batchInfoList = recommendLocationBatches
                    .stream()
                    .map(RecommendLocationBatch::getBatch)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            List<RecommendLocationBatch.LocationInfo> locationInfos = recommendLocationBatches
                    .stream()
                    .map(RecommendLocationBatch::getLocation)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            //所需温区为指定温区且查到的温区和所需温区不同 判定为所需温区库存不足
            boolean requiredTemperatureAreaStockIsNotEnough = false;

            boolean requiredSpecificTemperatureArea = !Objects.equals(skuInfo.getTemperatureType(), TemperatureType.UNDEFINED.getCode());

            if (requiredSpecificTemperatureArea) {
                requiredTemperatureAreaStockIsNotEnough = recommendLocationBatches.stream()
                        .anyMatch(this::recommendedNotMatchRequiredTemperature);
            }

            taskVo.setBatchInfos(batchInfoList);
            taskVo.setLocationInfos(locationInfos);
            taskVo.setRequiredTemperatureAreaStockIsEnough(!requiredTemperatureAreaStockIsNotEnough);
        } catch (Exception e) {
            taskVo.setBatchInfos(Collections.emptyList());
            taskVo.setLocationInfos(Collections.emptyList());
            taskVo.setRequiredTemperatureAreaStockIsEnough(true);
            Cat.logEvent("FILL_LOCATION_BATCH_INFO", "FAIL");
            log.error("fillLocationBatchInfo error, taskDto:{}, skuRecommendLocationBatchMap:{}",taskDto, skuRecommendLocationBatchMap, e);
        }

    }

    private void fillNewLocationBatchInfo(TradeShippingOrderItemDTO tradeShippingOrderItemDTO, RiderPickTaskInfoVO taskVo,
                                          Map<com.sankuai.shangou.logistics.warehouse.dto.RecommendLocationBatch.SkuInfo, ArrayList<com.sankuai.shangou.logistics.warehouse.dto.RecommendLocationBatch>> skuRecommendLocationListMap) {
        try {
            //填充库位信息、批次信息和所需温区库存是否充足
            com.sankuai.shangou.logistics.warehouse.dto.RecommendLocationBatch.SkuInfo skuInfo = new com.sankuai.shangou.logistics.warehouse.dto.RecommendLocationBatch.SkuInfo(tradeShippingOrderItemDTO.getSkuId(),
                    convert2TemperatureAttributeType(tradeShippingOrderItemDTO.getTemperatureZoneCode()));
            List<com.sankuai.shangou.logistics.warehouse.dto.RecommendLocationBatch> recommendLocationBatches = skuRecommendLocationListMap.getOrDefault(skuInfo, Lists.newArrayList());

            List<com.sankuai.shangou.logistics.warehouse.dto.RecommendLocationBatch.BatchInfo> batchInfoList = recommendLocationBatches
                    .stream()
                    .map(com.sankuai.shangou.logistics.warehouse.dto.RecommendLocationBatch::getBatch)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            List<com.sankuai.shangou.logistics.warehouse.dto.RecommendLocationBatch.LocationInfo> locationInfos = recommendLocationBatches
                    .stream()
                    .map(com.sankuai.shangou.logistics.warehouse.dto.RecommendLocationBatch::getLocation)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            //所需温区为指定温区且查到的温区和所需温区不同 判定为所需温区库存不足
            boolean requiredTemperatureAreaStockIsNotEnough = false;

            boolean requiredSpecificTemperatureArea = !Objects.equals(skuInfo.getTemperatureType(), TemperatureType.UNDEFINED.getCode());

            if (requiredSpecificTemperatureArea) {
                requiredTemperatureAreaStockIsNotEnough = recommendLocationBatches.stream()
                        .anyMatch(this::newRecommendedNotMatchRequiredTemperature);
            }

            //兼容代码
            taskVo.setBatchInfos(
                    batchInfoList
                            .stream()
                            .map(
                                    batchInfo -> {
                                        RecommendLocationBatch.BatchInfo originBatchInfo = new RecommendLocationBatch.BatchInfo();
                                        originBatchInfo.setBatchNo(batchInfo.getBatchNo());
                                        originBatchInfo.setProductionDate(batchInfo.getProductionDate());
                                        return originBatchInfo;
                                    }
                            ).collect(Collectors.toList())
            );
            taskVo.setLocationInfos(
                    locationInfos
                            .stream()
                            .map(
                                    locationInfo -> {
                                        RecommendLocationBatch.LocationInfo originLocationInfo = new RecommendLocationBatch.LocationInfo();
                                        originLocationInfo.setLocationId(locationInfo.getLocationId());
                                        originLocationInfo.setLocationCode(locationInfo.getLocationCode());
                                        originLocationInfo.setLocationType(locationInfo.getLocationType());
                                        originLocationInfo.setAreaType(locationInfo.getAreaType());
                                        originLocationInfo.setAreaTypeName(locationInfo.getAreaTypeName());
                                        originLocationInfo.setTemperatureType(locationInfo.getTemperatureType());
                                        originLocationInfo.setIsDefaultLocation(locationInfo.getIsDefaultLocation());
                                        return originLocationInfo;
                                    }
                            ).collect(Collectors.toList())
            );
            taskVo.setRequiredTemperatureAreaStockIsEnough(!requiredTemperatureAreaStockIsNotEnough);
        } catch (Exception e) {
            taskVo.setBatchInfos(Collections.emptyList());
            taskVo.setLocationInfos(Collections.emptyList());
            taskVo.setRequiredTemperatureAreaStockIsEnough(true);
            Cat.logEvent("FILL_LOCATION_BATCH_INFO", "FAIL");
            log.error("fillLocationBatchInfo error, tradeShippingOrderItemDTO:{}, skuRecommendLocationBatchMap:{}",tradeShippingOrderItemDTO, skuRecommendLocationListMap, e);
        }

    }

    /**
     * 填充保质期等信息, 如果出现异常不阻塞流程
     * @param taskVo
     * @param goodsExpirationMap
     */
    private void fillExpirationInfo(RiderPickTaskInfoVO taskVo, Map<String, GoodsExpirationDto> goodsExpirationMap) {
        try {
            String skuId = taskVo.getSkuId();
            GoodsExpirationDto expirationDto = goodsExpirationMap.get(skuId);
            if (expirationDto != null) {
                if (expirationDto.getExpire() != null && MccConfigUtil.getShortExpirationThreshold() > expirationDto.getExpire()) {
                    taskVo.setIsShortExpirationGoods(true);
                } else {
                    taskVo.setIsShortExpirationGoods(false);
                }
            }
        } catch (Exception e) {
            log.warn("fillExpirationInfo error", e);
            Cat.logEvent("SWITCH_TO_GOODS_CENTER", "FILL_EXPIRATION_INFO_FAIL");
        }

    }

    private boolean recommendedNotMatchRequiredTemperature(RecommendLocationBatch recommendLocationBatch) {
        Integer requiredTemperatureArea = Optional.ofNullable(recommendLocationBatch)
                .map(RecommendLocationBatch::getSku)
                .map(RecommendLocationBatch.SkuInfo::getTemperatureType)
                .orElse(null);

        Integer recommendTemperatureArea = Optional.ofNullable(recommendLocationBatch)
                .map(RecommendLocationBatch::getLocation)
                .map(RecommendLocationBatch.LocationInfo::getTemperatureType)
                .orElse(null);

        if (recommendTemperatureArea == null || requiredTemperatureArea == null) {
            return false;
        }

        return !Objects.equals(requiredTemperatureArea, recommendTemperatureArea);
    }

    private boolean newRecommendedNotMatchRequiredTemperature(com.sankuai.shangou.logistics.warehouse.dto.RecommendLocationBatch recommendLocationBatch) {
        Integer requiredTemperatureArea = Optional.ofNullable(recommendLocationBatch)
                .map(com.sankuai.shangou.logistics.warehouse.dto.RecommendLocationBatch::getSku)
                .map(com.sankuai.shangou.logistics.warehouse.dto.RecommendLocationBatch.SkuInfo::getTemperatureType)
                .orElse(null);

        Integer recommendTemperatureArea = Optional.ofNullable(recommendLocationBatch)
                .map(com.sankuai.shangou.logistics.warehouse.dto.RecommendLocationBatch::getLocation)
                .map(com.sankuai.shangou.logistics.warehouse.dto.RecommendLocationBatch.LocationInfo::getTemperatureType)
                .orElse(null);

        if (recommendTemperatureArea == null || requiredTemperatureArea == null) {
            return false;
        }

        return !Objects.equals(requiredTemperatureArea, recommendTemperatureArea);
    }

    @Deprecated
    private Set<String> buildUpcAndBoxCodeSet(RiderPickTaskDTO taskDto) {
        Set<String> upcAndBoxCodeList = new HashSet<>();
        if (CollectionUtils.isNotEmpty(taskDto.getUpcList())) {
            upcAndBoxCodeList.addAll(taskDto.getUpcList());
        }

        if (CollectionUtils.isNotEmpty(taskDto.getBoxCodes())) {
            upcAndBoxCodeList.addAll(taskDto.getBoxCodes());
        }

        return upcAndBoxCodeList;
    }

    private Set<String> buildUpcAndBoxCodeSet(TradeShippingOrderItemDTO taskDto) {
        Set<String> upcAndBoxCodeList = new HashSet<>();
        if (CollectionUtils.isNotEmpty(taskDto.getBarCodes())) {
            upcAndBoxCodeList.addAll(taskDto.getBarCodes());
        }

        if (CollectionUtils.isNotEmpty(taskDto.getBoxCodes())) {
            upcAndBoxCodeList.addAll(taskDto.getBoxCodes());
        }

        return upcAndBoxCodeList;
    }

    /**
     * 按照温度属性排序
     * 排序规则:冰的a-冰的b-常温的a-常温的b-自定义的a-自定义的b-无属性a-无属性b
     *
     * @param o1
     * @param o2
     * @return
     */
    private int compareByTemperatureAttribute(RiderPickTaskInfoVO o1, RiderPickTaskInfoVO o2) {
        return convert2CompareValue(o1) - convert2CompareValue(o2);
    }

    private int convert2CompareValue(RiderPickTaskInfoVO riderPickTaskInfoVO) {
        if (riderPickTaskInfoVO == null || StringUtils.isBlank(riderPickTaskInfoVO.getTemperatureAttributeType())) {
            return 40;
        }

        TemperaturePropertyEnum temperaturePropertyEnum = temperaturePropertyEnumOf(riderPickTaskInfoVO.getTemperatureAttributeType());

        switch (temperaturePropertyEnum) {
            case ICE_TEMPERATURE:
                return 10;
            case NORMAL_TEMPERATURE:
                return 20;
            case USER_DEFINED:
                return 30;
            default:
                return 40;
        }
    }

    private Integer convert2TemperatureAttributeType(String attributeStr) {
        if (StringUtils.isBlank(attributeStr)) {
            return TemperatureType.UNDEFINED.getCode();
        }

        switch (temperaturePropertyEnumOf(attributeStr)) {
            case NORMAL_TEMPERATURE:
                return TemperatureType.NORMAL.getCode();
            case ICE_TEMPERATURE:
                return TemperatureType.FREEZING.getCode();
            default:
                return TemperatureType.UNDEFINED.getCode();
        }
    }


    private String getDeliveryOrderTypeName(Integer deliveryOrderType) {
        DeliveryOrderType deliveryOrderTypeEnum = DeliveryOrderType.findByValue(deliveryOrderType);
        if (Objects.isNull(deliveryOrderTypeEnum)) {
            return "未知";
        }
        switch (deliveryOrderTypeEnum) {
            case DELIVERY_RIGHT_NOW:
                return "立即送达";
            case DELIVERY_BY_BOOK_TIME:
                return "预订";
            default:
                return "未知";
        }
    }

    @Deprecated
    private Integer getTotalPickCount(List<RiderPickTaskDTO> pickTaskDtos) {
        if (CollectionUtils.isEmpty(pickTaskDtos)) {
            return 0;
        }
        return pickTaskDtos.stream().mapToInt(task -> Optional.ofNullable(task.getCount()).orElse(0)).sum();
    }


    private Integer getTotalCount(List<TradeShippingOrderItemDTO> tradeOutboundOrderItemDTOS) {
        if (CollectionUtils.isEmpty(tradeOutboundOrderItemDTOS)) {
            return 0;
        }
        return tradeOutboundOrderItemDTOS.stream().mapToInt(task -> Optional.ofNullable(task.getActualQty()).orElse(BigDecimal.ZERO).intValue()).sum();
    }

    private QueryLocationBatchListRequest buildQueryLocationBatchListRequest(Long tenantId, Long repositoryId,
                                                                             RiderPickWorkOrderDTO riderPickWorkOrderDTO) {
        QueryLocationBatchListRequest request = new QueryLocationBatchListRequest();
        List<RecommendSku> recommendSkus = riderPickWorkOrderDTO.getPickTasks().stream()
                .map(task -> {
                    RecommendSku recommendSku = new RecommendSku();
                    int count = Optional.ofNullable(task.getCount()).orElse(0);
                    int refundCount = Optional.ofNullable(task.getRefundCount()).orElse(0);
                    recommendSku.setQuantity(String.valueOf(count - refundCount));
                    recommendSku.setSkuId(task.getStoreSkuId());
                    recommendSku.setTemperatureType(convertTemperatureAttribute2TemperatureType(task.getTemperatureAttribute()));
                    return recommendSku;
                })
                .collect(Collectors.toList());


        QueryLocationBatchListRequest.RecommendOption recommendOption = new QueryLocationBatchListRequest.RecommendOption();
        recommendOption.setBizType(BizType.SALE_OUT.getCode());
        recommendOption.setStockOperate(OUT_STOCK_OPERATE);

        request.setTenantId(tenantId);
        request.setRepositoryId(repositoryId);
        request.setRepositoryType(RepositoryType.STORE.val());
        request.setRecommendOption(recommendOption);
        request.setRecommendSkus(recommendSkus);

        return request;
    }

    private Integer convertTemperatureAttribute2TemperatureType(String temperatureAttributeStr) {
        if (NORMAL_TEMPERATURE.getDesc().equals(temperatureAttributeStr)) {
            return TemperatureType.NORMAL.getCode();
        }

        if (temperatureAttributeStr.equals(ICE_TEMPERATURE.getDesc())) {
            return TemperatureType.FREEZING.getCode();
        }

        return TemperatureType.UNDEFINED.getCode();
    }

    /**
     * 获取订单详情.
     *
     * @return List<OCMSOrderVO>
     */
    private OCMSOrderVO getOCMSOrder(int orderBizType, String channelOrderId) {
        OCMSListViewIdConditionRequest viewIdConditionRequest = new OCMSListViewIdConditionRequest();
        viewIdConditionRequest.setViewIdConditionList(Lists.newArrayList(
                new ViewIdCondition(orderBizType, channelOrderId)));
        viewIdConditionRequest.setSortField(SortFieldEnum.ORDER_CREATE_TIME);
        viewIdConditionRequest.setSort(SortByEnum.DESC);
        // 1. 查询订单信息.
        OCMSListViewIdConditionResponse viewIdConditionResponse = null;
        try {
            viewIdConditionResponse = ocmsQueryThriftService.queryOrderByViewIdCondition(viewIdConditionRequest);
            log.info("Call ocmsQueryThriftService#queryOrderByViewIdCondition. request:{}, response:{}", viewIdConditionRequest,
                    viewIdConditionResponse);
        } catch (Exception e) {
            log.error("Call ocmsQueryThriftService#queryOrderByViewIdCondition error. request:{}", viewIdConditionRequest, e);
            throw new CommonRuntimeException(ResultCode.INTERNAL_SERVER_ERROR.defaultMessage);
        }

        // 2. 处理返回结果
        if (viewIdConditionResponse == null || viewIdConditionResponse.getStatus() == null ||
                viewIdConditionResponse.getStatus().getCode() != ResultCodeEnum.SUCCESS.getValue()) {
            log.warn("获取订单详情失败, viewOrderId:{}",
                    viewIdConditionRequest.getViewIdConditionList().stream().map(e -> e.getViewOrderId()).collect(Collectors.toList()));
            throw new CommonRuntimeException(Optional.ofNullable(viewIdConditionResponse).map(OCMSListViewIdConditionResponse::getStatus)
                    .map(Status::getMessage).orElse(ResultCode.INTERNAL_SERVER_ERROR.defaultMessage));
        }
        List<OCMSOrderVO> ocmsOrderList = viewIdConditionResponse.getOcmsOrderList();
        if (CollectionUtils.isEmpty(ocmsOrderList)) {
            log.warn("未查询到订单, viewOrderId:{}",
                    viewIdConditionRequest.getViewIdConditionList().stream().map(e -> e.getViewOrderId()).collect(Collectors.toList()));
            throw new CommonRuntimeException("未查询到订单");
        }
        return ocmsOrderList.get(0);
    }

    private RiderOfflinePromotePickOrderResponse getRiderOfflinePromotePickOrderList(QueryPromotePickOrderRequest request) {

        if (Objects.isNull(ApiMethodParamThreadLocal.getIdentityInfo()) || Objects.isNull(ApiMethodParamThreadLocal.getIdentityInfo().getUser())) {
            throw new CommonRuntimeException("无法获取用户信息,请先登陆");
        }

        RiderOfflinePromotePickOrderQueryRequest pickOrderQueryRequest =
                new RiderOfflinePromotePickOrderQueryRequest(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId(),
                        ApiMethodParamThreadLocal.getIdentityInfo().getStoreId(),
                        ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId(),
                        request.getPage(), request.getSize());

        RiderOfflinePromotePickOrderResponse pickOrderResponse = null;
        try {
            pickOrderResponse = riderPickingRpcService.queryOfflinePromotePickOrderList(pickOrderQueryRequest);
            log.info("Call riderPickingRpcService#queryOfflinePromotePickOrderList. request:{}, response:{}", pickOrderQueryRequest,
                    pickOrderResponse);
        } catch (Exception e) {
            log.error("查询推广自提拣货单失败", e);
            throw new CommonRuntimeException("查询推广自提拣货单失败");
        }


        if (Objects.isNull(pickOrderResponse) || Objects.isNull(pickOrderResponse.getStatus())) {
            throw new CommonRuntimeException("查询推广自提拣货单失败: 内部错误");
        }

        if (pickOrderResponse.getStatus().getCode() != SUCCESS.getCode()) {
            throw new CommonRuntimeException("查询推广自提拣货单失败:" + pickOrderResponse.getStatus().getMessage());
        }

        return pickOrderResponse;
    }



    /**
     * 查询订单的拣货工单信息.
     *
     * @param tenantId     租户 ID
     * @param storeId      门店 ID
     * @param accountId    进行查询的账户 ID
     * @param orderBizType 订单来源
     * @param unifyOrderId 订单的外部唯一订单号
     * @return 拣货工单信息
     */
    private RiderPickWorkOrderDTO getPickWorkOrder(long tenantId, long storeId, long accountId, DynamicOrderBizType orderBizType, String unifyOrderId) {
        QueryRiderPickWorkOrderRequest tRequest = new QueryRiderPickWorkOrderRequest();
        tRequest.setTenantId(tenantId);
        tRequest.setEmpowerStoreId(storeId);
        tRequest.setAccountId(accountId);
        tRequest.setOrderSource(orderBizType.getValue());
        tRequest.setUnifyOrderId(unifyOrderId);

        // 1. thrift 调用
        RiderPickWorkOrderResponse response = null;
        try {
            response = riderPickingRpcService.queryPickWorkOrderDetail(tRequest);
            log.info("Call RiderPickingThriftService#queryPickWorkOrderDetail. request:{} response:{}", tRequest, response);
        } catch (Exception e) {
            log.error("Call RiderPickingThriftService#queryPickWorkOrderDetail error. request:{}", tRequest, e);
            throw new CommonRuntimeException(ResultCode.INTERNAL_SERVER_ERROR.defaultMessage);
        }
        // 2. 处理返回结果
        if (response == null || response.getStatus() == null ||
                response.getStatus().getCode() != com.sankuai.meituan.reco.pickselect.consts.ResultCodeEnum.SUCCESS.getCode()) {
            log.warn("获取拣货工单信息失败, request:{}, response:{}", tRequest, response);
            throw new CommonRuntimeException(response.getStatus().getMessage());
        }
        RiderPickWorkOrderDTO pickWorkOrderDto = response.getPickWorkOrderDto();
        if (pickWorkOrderDto == null) {
            log.warn("拣货工单不存在");
            throw new CommonRuntimeException("拣货工单不存在，请稍后再试");
        }
        return pickWorkOrderDto;

    }

    /**
     * 骑手操作拣货完成.
     *
     * @param request 骑手拣货完成请求
     */
    @Deprecated
    public void finishPicking(RiderFinishPickingRequest request) {
        // 1. 基本校验
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        List<Long> storeIdList = identityInfo.getStoreIdList();
        if (CollectionUtils.isEmpty(storeIdList) || storeIdList.size() > 1) {
            log.error("骑手订单展示只支持单门店模式：storeIdList={}", storeIdList);
            // 客户端对 ResultCode.FAIL 进行 toast 提醒
            throw new CommonRuntimeException("骑手订单展示只支持单门店模式", ResultCode.FAIL);
        }
        // 1.1 获取订单信息并校验
        OrderBizTypeEnum orderBizType = OrderBizTypeEnum.enumOf(ChannelOrderConvertUtils.sourceMid2Biz(request.getChannelId()));
        if (orderBizType == null) {
            log.warn("订单渠道无效, channelId:{}", request.getChannelId());
            throw new CommonRuntimeException("订单渠道无效", ResultCode.FAIL);
        }
        OCMSOrderVO ocmsOrder = getOCMSOrder(orderBizType.getValue(), request.getChannelOrderId());
        DeliveryStatusEnum pickStatus = Optional.ofNullable(ocmsOrder.getOcmsDeliveryInfoVO()).map(OnlineBaseDeliveryInfoVO::getDeliveryStatus)
                .map(DeliveryStatusEnum::enumOf).orElse(null);
        OrderStatusEnum orderStatus = OrderStatusEnum.enumOf(ocmsOrder.getOrderStatus());
        // 若订单状态为已取消，则放弃拣货
        if (pickStatus == DeliveryStatusEnum.CANCELED || orderStatus == OrderStatusEnum.CANCELED) {
            log.warn("订单已取消，不支持进行扫码拣货, pickStatus:{}, orderStatus:{}", pickStatus, orderStatus);
            // 客户端对 ResultCode.RIDER_PICK_FINISH_PICK_FAIL 进行弹窗提醒
            throw new CommonRuntimeException("订单已取消，请勿出库", ResultCode.RIDER_PICK_FINISH_PICK_FAIL);
        }
        if (pickStatus == DeliveryStatusEnum.PICKED || orderStatus == OrderStatusEnum.COMPLETED) {
            log.warn("拣货或订单已完成，不支持进行扫码拣货, pickStatus:{}, orderStatus:{}", pickStatus, orderStatus);
            throw new CommonRuntimeException("拣货或订单已完成，请退出重试", ResultCode.RIDER_PICK_FINISH_PICK_FAIL);
        }

        // 2. 进行拣货出库
        Long storeId = storeIdList.get(0);
        User user = identityInfo.getUser();
        boolean pickSuccess = riderFinishPick(request, storeId, user);
        if (!pickSuccess) {
            throw new CommonRuntimeException("执行出库失败，请稍后重试或联系管理员", ResultCode.FAIL);
        }
        // 3. 进行骑手配送状态流转
        boolean deliverySuccess = riderTakeAway(request, user);
        if (!deliverySuccess) {
            // 骑手完成拣货时，若配送状态流转失败，仍返回成功，等待 TMS 消费拣货完成的消息，兜底进行配送状态流转
            Cat.logEvent("RIDER_PICK", "PICK_SUCCESS_BUT_TAKE_AWAY_FAIL");
            log.warn("骑手完成拣货时，配送状态流转失败. request:{}", request);
        }
        return;
    }

    /**
     * 查询骑手推广自提数量.
     *
     * @return Map<Integer, Integer> deliveryStatus-quantity
     */
    public Integer queryWaitToFinishSelfPickPromoteOrderCount() {
        try {
            IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
            List<Long> storeIdList = identityInfo.getStoreIdList();
            long tenantId = identityInfo.getUser().getTenantId();
            if (CollectionUtils.isEmpty(storeIdList) || storeIdList.size() > 1) {
                log.error("骑手推广自提查询只支持单门店模式：storeIdList={}", storeIdList);
                return 0;
            }
            long operatorAccountId = identityInfo.getUser().getAccountId();

            RiderOfflinePromoteOrderCountRequest request = new RiderOfflinePromoteOrderCountRequest();
            request.setTenantId(tenantId);
            request.setOfflineStoreId(storeIdList.get(0));
            request.setAccountId(operatorAccountId);

            Integer integer = riderPickingRpcService.queryUnfinishedOfflinePromoteOrderCount(request);
            log.info("RiderDeliveryServiceWrapper call RiderQueryThriftService-queryWaitToFinishSelfPickPromoteOrderCount end. request:{}, resonse:{}", request, integer);
            return integer;

        } catch (Exception e) {
            log.error("invoke queryWaitToFinishSelfPickPromoteOrderCount error",e);
            return 0;
        }

    }

    /**
     * 骑手完成拣货操作.
     *
     * @param request 用户请求
     * @param storeId 门店 ID
     * @param user    用户信息
     * @return 是否成功拣货完成
     */
    @Deprecated
    private boolean riderFinishPick(RiderFinishPickingRequest request, long storeId, User user) {
        RiderFinishPickRequest tRequest = convert2PickRequest(request, storeId, user.getAccountId(), user.getTenantId());

        RiderFinishPickResponse response = null;
        try {
            response = riderPickingRpcService.finishPick(tRequest);
            log.info("Call RiderPickingThriftService#finishPick. request:{}, response:{}", tRequest, response);
        } catch (Exception e) {
            log.error("Call RiderPickingThriftService#finishPick error. request:{}", tRequest, e);
            return false;
        }
        if (response == null || response.getStatus() == null ||
                response.getStatus().getCode() != com.sankuai.meituan.reco.pickselect.consts.ResultCodeEnum.SUCCESS.getCode()) {
            log.warn("拣货工单操作拣货完成失败, request:{}, response:{}", tRequest, response);
            return false;
        }
        return true;
    }


    public List<String> finishPickingV2(RiderFinishPickingRequest request) {
        // 1. 基本校验
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        List<Long> storeIdList = identityInfo.getStoreIdList();
        if (CollectionUtils.isEmpty(storeIdList) || storeIdList.size() > 1) {
            log.error("骑手订单展示只支持单门店模式：storeIdList={}", storeIdList);
            // 客户端对 ResultCode.FAIL 进行 toast 提醒
            throw new CommonRuntimeException("骑手订单展示只支持单门店模式", ResultCode.FAIL);
        }
        // 1.1 获取订单信息并校验
        OrderBizTypeEnum orderBizType = OrderBizTypeEnum.enumOf(ChannelOrderConvertUtils.sourceMid2Biz(request.getChannelId()));
        if (orderBizType == null) {
            log.warn("订单渠道无效, channelId:{}", request.getChannelId());
            throw new CommonRuntimeException("订单渠道无效", ResultCode.FAIL);
        }
        OCMSOrderVO ocmsOrder = getOCMSOrder(orderBizType.getValue(), request.getChannelOrderId());
        DeliveryStatusEnum pickStatus = Optional.ofNullable(ocmsOrder.getOcmsDeliveryInfoVO()).map(OnlineBaseDeliveryInfoVO::getDeliveryStatus)
                .map(DeliveryStatusEnum::enumOf).orElse(null);
        OrderStatusEnum orderStatus = OrderStatusEnum.enumOf(ocmsOrder.getOrderStatus());
        // 若订单状态为已取消，则放弃拣货
        if (pickStatus == DeliveryStatusEnum.CANCELED || orderStatus == OrderStatusEnum.CANCELED) {
            log.warn("订单已取消，不支持进行扫码拣货, pickStatus:{}, orderStatus:{}", pickStatus, orderStatus);
            // 客户端对 ResultCode.RIDER_PICK_FINISH_PICK_FAIL 进行弹窗提醒
            throw new CommonRuntimeException("订单已取消，请勿出库", ResultCode.RIDER_PICK_FINISH_PICK_FAIL);
        }

        //若配送状态为暂停配送，则放弃拣货
        if (ocmsOrder.getOcmsDeliveryInfoVO() != null && ocmsOrder.getOcmsDeliveryInfoVO().getDeliveryPauseFlag() != null
        && ocmsOrder.getOcmsDeliveryInfoVO().getDeliveryPauseFlag() == 1) {
            log.warn("运单状态为暂停配送，不能扫码出库");
            throw new CommonRuntimeException("订单已暂停配送，无法出库", ResultCode.RIDER_PICK_FINISH_PICK_FAIL);
        }

        if (pickStatus == DeliveryStatusEnum.PICKED) {
            log.warn("拣货已完成，不支持进行扫码拣货, pickStatus:{}, orderStatus:{}", pickStatus, orderStatus);
            Cat.logEvent("DH_ADAPT_SN", "ORDER_COMPLETE");
            throw new CommonRuntimeException("订单已完成，请退出重试", ResultCode.RIDER_PICK_FINISH_PICK_FAIL);
        }

        // 2. 进行拣货出库
        Long storeId = storeIdList.get(0);
        User user = identityInfo.getUser();
        List<String> invalidSnCodes;
        if(MccConfigUtil.isNewPickGrayStore(storeId)) {
            invalidSnCodes = newRiderFinishPick(request, storeId, user);
        } else {
            invalidSnCodes = riderFinishPickV2(request, storeId, user);
        }
        if (CollectionUtils.isNotEmpty(invalidSnCodes)) {
            return invalidSnCodes;
        }

        //如果是三方配送,不扭转运单状态,直接return
        if (checkIsThirdDeliveryOrder(request, ocmsOrder.getOrderId())) {
            log.info("三方配送单, 不扭转运单状态, orderId:{}", ocmsOrder.getOrderId());
            return Collections.emptyList();
        }

        // 3. 进行骑手配送状态流转
        boolean deliverySuccess = riderTakeAway(request, user);
        if (!deliverySuccess) {
            // 骑手完成拣货时，若配送状态流转失败，仍返回成功，等待 TMS 消费拣货完成的消息，兜底进行配送状态流转
            Cat.logEvent("RIDER_PICK", "PICK_SUCCESS_BUT_TAKE_AWAY_FAIL");
            log.warn("骑手完成拣货时，配送状态流转失败. request:{}", request);
        }

        return Collections.emptyList();
    }


    /**
     * 骑手完成拣货操作.
     *
     * @param request 用户请求
     * @param storeId 门店 ID
     * @param user    用户信息
     * @return 是否成功拣货完成
     */
    private List<String> riderFinishPickV2(RiderFinishPickingRequest request, long storeId, User user) {
        RiderFinishPickRequest tRequest = convert2PickRequest(request, storeId, user.getAccountId(), user.getTenantId());

        RiderFinishPickResponse response = null;
        try {
            response = riderPickingRpcService.finishPickV2(tRequest);
            log.info("Call RiderPickingThriftService#finishPick. request:{}, response:{}", tRequest, response);
        } catch (Exception e) {
            log.error("Call RiderPickingThriftService#finishPick error. request:{}", tRequest, e);
            throw new CommonRuntimeException("执行出库失败，请稍后重试或联系管理员", ResultCode.RIDER_PICK_FINISH_PICK_FAIL);
        }

        // 把需要拍照留存的错误码直接返回给前端
        if (response != null && response.getStatus() != null &&
                response.getStatus().getCode() == com.sankuai.meituan.reco.pickselect.consts.ResultCodeEnum.NEED_TAKE_PHOTO_BEFORE_FINISH_PICKING.getCode()) {
            log.warn("拣货工单操作拣货完成失败, request:{}, response:{}", tRequest, response);
            throw new CommonRuntimeException(ResultCode.NEED_TAKE_PHOTO_BEFORE_FINISH_PICKING.getErrorMessage(), ResultCode.NEED_TAKE_PHOTO_BEFORE_FINISH_PICKING);
        }

        if (Objects.equals(
                EXCEED_SHOULD_PICK_COUNT.getCode(),
                Optional.ofNullable(response).map(RiderFinishPickResponse::getStatus).map(com.sankuai.meituan.reco.pickselect.common.Status::getCode).orElse(null)
        )) {
            log.warn("拣货工单操作拣货完成失败, request:{}, response:{}", tRequest, response);
            throw new CommonLogicException(ResultCode.EXCEED_SHOULD_PICK_COUNT.getErrorMessage(), ResultCode.EXCEED_SHOULD_PICK_COUNT);
        }

        if (response == null || response.getStatus() == null) {
            log.warn("拣货工单操作拣货完成失败, request:{}, response:{}", tRequest, response);
            throw new CommonRuntimeException("执行出库失败，请稍后重试或联系管理员", ResultCode.RIDER_PICK_FINISH_PICK_FAIL);
        }

        if(response.getStatus().getCode() != com.sankuai.meituan.reco.pickselect.consts.ResultCodeEnum.SUCCESS.getCode()) {
            if (CollectionUtils.isNotEmpty(response.getInvalidSnCodeList())) {
                return response.getInvalidSnCodeList();
            }
            throw new CommonRuntimeException(StringUtils.isBlank(response.getStatus().getMessage()) ?
                    "执行出库失败，请稍后重试或联系管理员" : response.getStatus().getMessage(),
                    ResultCode.RIDER_PICK_FINISH_PICK_FAIL);
        }

        return Collections.emptyList();
    }


    private List<String> newRiderFinishPick(RiderFinishPickingRequest request, long storeId, User user) {

        FinishPickRequest tRequest = new FinishPickRequest();
        tRequest.setWarehouseId(storeId);
        tRequest.setTenantId(user.getTenantId());
        tRequest.setTradeOutboundOrderNo(String.valueOf(request.getPickingWoId()));
        tRequest.setItemOperateDTOList(
                request.getPickTasks().stream()
                        .map(task -> {
                            TradeOutboundItemOperateDTO tradeOutboundItemOperateDTO = new TradeOutboundItemOperateDTO();
                            tradeOutboundItemOperateDTO.setItemId(task.getPickingTaskId());
                            tradeOutboundItemOperateDTO.setPickedCount(task.getPickedCount());
                            tradeOutboundItemOperateDTO.setEnteringType(task.getPickItemStockOutEnteringType());

                            //添加sn码
                            List<com.sankuai.meituan.reco.pickselect.dh.dto.SnInfoDto> snInfoDtos = Optional.ofNullable(task.getSnInfoList()).orElse(Collections.emptyList())
                                    .stream().map(snInfo -> new com.sankuai.meituan.reco.pickselect.dh.dto.SnInfoDto(snInfo.getSnCode(), snInfo.getSnCodeEnteringType()))
                                    .collect(Collectors.toList());

                            tradeOutboundItemOperateDTO.setSnInfoDtoList(snInfoDtos);
                            return tradeOutboundItemOperateDTO;
                        }).collect(Collectors.toList())
        );
        tRequest.setPickingCheckPictureUrlList(request.getPickingCheckPictureUrlList());
        tRequest.setIsThirdDeliveryPick(request.getIsThirdDelivery());
        tRequest.setOperateAccountId(user.getAccountId());
        tRequest.setOperateName(user.getOperatorName());


        TResult<FinishPickResponse> response = null;
        try {
            response = pickingProcessService.finishPick(tRequest);
            log.info("Call RiderPickingThriftService#finishPick. request:{}, response:{}", tRequest, response);
        } catch (Exception e) {
            log.error("Call RiderPickingThriftService#finishPick error. request:{}", tRequest, e);
            throw new CommonRuntimeException("执行出库失败，请稍后重试或联系管理员", ResultCode.RIDER_PICK_FINISH_PICK_FAIL);
        }

        // 把需要拍照留存的错误码直接返回给前端
        if (response != null && response.getCode() == com.sankuai.meituan.reco.pickselect.consts.ResultCodeEnum.NEED_TAKE_PHOTO_BEFORE_FINISH_PICKING.getCode()) {
            log.warn("拣货工单操作拣货完成失败, request:{}, response:{}", tRequest, response);
            throw new CommonRuntimeException(ResultCode.NEED_TAKE_PHOTO_BEFORE_FINISH_PICKING.getErrorMessage(), ResultCode.NEED_TAKE_PHOTO_BEFORE_FINISH_PICKING);
        }

        if (Objects.equals(
                EXCEED_SHOULD_PICK_COUNT.getCode(),
                Optional.ofNullable(response).map(TResult::getCode).orElse(null)
        )) {
            log.warn("拣货工单操作拣货完成失败, request:{}, response:{}", tRequest, response);
            throw new CommonLogicException(ResultCode.EXCEED_SHOULD_PICK_COUNT.getErrorMessage(), ResultCode.EXCEED_SHOULD_PICK_COUNT);
        }

        if (Objects.equals(LACK_STOCK_CODE, Optional.ofNullable(response).map(TResult::getCode).orElse(null))) {
            log.warn("拣货工单操作拣货完成失败, request:{}, response:{}", tRequest, response);
            throw new CommonLogicException(ResultCode.STOCK_OUT_STOCK_LACK.getErrorMessage(), ResultCode.STOCK_OUT_STOCK_LACK);
        }

        if (response == null) {
            log.warn("拣货工单操作拣货完成失败, request:{}, response:{}", tRequest, response);
            throw new CommonRuntimeException("执行出库失败，请稍后重试或联系管理员", ResultCode.RIDER_PICK_FINISH_PICK_FAIL);
        }

        if(!response.isSuccess()) {
            if (Objects.nonNull(response.getData()) && CollectionUtils.isNotEmpty(response.getData().getInvalidSnCodeList())) {
                return response.getData().getInvalidSnCodeList();
            }
            throw new CommonRuntimeException(StringUtils.isBlank(response.getMsg()) ?
                    "执行出库失败，请稍后重试或联系管理员" : response.getMsg(),
                    ResultCode.RIDER_PICK_FINISH_PICK_FAIL);
        }

        return Collections.emptyList();
    }

    /**
     * 立即下发履约工单
     *
     * @param request 骑手立即履约请求
     */
    @MethodLog(logRequest = true,logResponse = true)
    public void immediatelyPushDownFulfillWorkOrder4BookingOrder(ImmediatelyDeliveryRequest request) {
        // 1.获取订单信息并校验
        BizOrderModel bizOrderModel = null;
        try {
            bizOrderModel = orderBizFacade.queryOrderModelByOrderId(request.getOrderId());
        } catch (BizException e) {
            log.warn("查询订单信息失败, orderId:{}", request.getOrderId());
            throw new CommonRuntimeException("查询订单信息失败,请稍后重试或联系管理员", ResultCode.FAIL);
        }
        OrderStatusEnum orderStatus = OrderStatusEnum.enumOf(bizOrderModel.getOrderStatus());

        // 若订单状态是已完成或已取消
        if (orderStatus == OrderStatusEnum.COMPLETED || orderStatus == OrderStatusEnum.CANCELED) {
            log.warn("当前状态的订单无法进行此项操作,orderStatus:{}", orderStatus);
            throw new CommonRuntimeException("当前状态的订单无法进行此项操作", ResultCode.FAIL);
        }

        // 2. 立即下发履约工单
        boolean pushDownSuccess;
        if (MccConfigUtil.isNewPickGrayStore(bizOrderModel.getShopId())) {
            pushDownSuccess= immediatelyPushDownBookingOrderByOfc(request, bizOrderModel);
        } else  {
            pushDownSuccess = immediatelyPushDownBookingOrderFulfillWorkOrder(request,bizOrderModel);
        }
        if (!pushDownSuccess) {
            throw new CommonRuntimeException("立即下发履约工单失败，请稍后重试或联系管理员", ResultCode.FAIL);
        }
    }


    /**
     * 查询推广自提单列表
     * @param request
     * @return RiderDeliveryOrderListResponse
     * 因为前端希望response数据结构与订单列表页面保持一致 所以复用了OrderListResponse 没用到的字段未赋值
     */
    public OrderListResponse queryRiderOfflinePromoteOrder(QueryPromotePickOrderRequest request) {
        //查拣货单详情
        if (MccConfigUtil.isNewPickGrayStore(ApiMethodParamThreadLocal.getIdentityInfo().getStoreId())) {
            try {
                List<TradeShippingOrderDTO> recentShippingOrderList = tradeShippingOrderServiceWrapper.getRecentListByOperatorIdAndStatusList(
                        ApiMethodParamThreadLocal.getIdentityInfo().getStoreId(),
                        ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId(),
                        Lists.newArrayList(TradeShippingOrderStatus.WAITED.getCode(), TradeShippingOrderStatus.RECEIVE.getCode(),
                                TradeShippingOrderStatus.FINISH.getCode(), TradeShippingOrderStatus.INVALID.getCode()
                        )
                );

                if (CollectionUtils.isEmpty(recentShippingOrderList)) {
                    OrderListResponse orderListResponse = new OrderListResponse();
                    orderListResponse.setPageInfo(PageInfoVO.initPageInfoVO(request.getPage(), request.getSize()));
                    orderListResponse.setOrderList(Lists.newArrayList());
                    return orderListResponse;
                }
                List<FulfillmentOrderDetailDTO> allFulfillmentOrderDetailDTOs = getFulfillmentOrderDetailDTOS(recentShippingOrderList)
                        .stream()
                        //过滤地推单
                        .filter(fulfillmentOrderDetailDTO -> Objects.nonNull(fulfillmentOrderDetailDTO.getOfflinePromote()) && Objects.nonNull(fulfillmentOrderDetailDTO.getOfflinePromote().getPullNewAccountId()) && fulfillmentOrderDetailDTO.getOfflinePromote().isPullNewSelfPickGoodsOrder())
                        //过滤已取消的订单
                        .filter(fulfillmentOrderDetailDTO -> !Objects.equals(fulfillmentOrderDetailDTO.getStatus(), FulfillmentOrderStatusEnum.CANCEL.getCode()))
                        //先按照状态排 再按照创建时间倒排
                        .sorted((o1, o2) -> {
                            if (!Objects.equals(o1.getStatus(), o2.getStatus())) {
                                return o1.getStatus() - o2.getStatus();
                            }

                            return -o1.getCreateTime().compareTo(o2.getCreateTime());
                        })
                        .collect(Collectors.toList());

                //内存分页
                MemPageUtils.PageInfo<FulfillmentOrderDetailDTO> pageInfo = MemPageUtils.pagingList(allFulfillmentOrderDetailDTOs, request.getPage(), request.getSize());

                List<FulfillmentOrderDetailDTO> fulfillmentOrderList = pageInfo.getPagedList();

                List<ViewIdCondition> viewIdConditions = fulfillmentOrderList
                        .stream()
                        .map(fulfillmentOrderDetailDTO -> new ViewIdCondition(fulfillmentOrderDetailDTO.getOrderSource(), fulfillmentOrderDetailDTO.getChannelOrderId()))
                        .collect(Collectors.toList());
                //查订单详情
                List<OCMSOrderVO> ocmsOrderList = ocmsOrderServiceWrapper.batchQueryOcmsOrderList(viewIdConditions);

                //查异常单
                Map<String, AbnOrderDTO> orderAbnMap = abnOrderServiceWrapper.getOrderAbnMap(ApiMethodParamThreadLocal.getIdentityInfo().getStoreId());

                OrderListResponse orderListResponse = buildNewOrderListResponse(pageInfo.getPagedList(), ocmsOrderList, request, orderAbnMap);
                //补page信息
                PageInfoVO pageInfoVO = new PageInfoVO(request.getPage(), request.getSize(), allFulfillmentOrderDetailDTOs.size());
                orderListResponse.setPageInfo(pageInfoVO);

                // 查询订单营收
                fillOrderRevenueDetail(orderListResponse);

                //填充基础商品信息
                fillGoodsItemList(orderListResponse.getOrderList(), recentShippingOrderList, ocmsOrderList);

                return orderListResponse;

            } catch (Exception e) {
                log.error("查询履约单失败，请稍后再试", e);
                throw new CommonRuntimeException("查询履约单失败，请稍后再试");
            }
        } else {
            RiderOfflinePromotePickOrderResponse pickOrderResponse = getRiderOfflinePromotePickOrderList(request);
            List<ViewIdCondition> viewIdConditions = pickOrderResponse.getRiderOfflinePromotePickOrderDTOS().stream()
                    .map(dto -> new ViewIdCondition(dto.getOrderBizType(), dto.getUnifyOrderId()))
                    .collect(Collectors.toList());
            //查订单详情
            List<OCMSOrderVO> ocmsOrderList = ocmsOrderServiceWrapper.batchQueryOcmsOrderList(viewIdConditions);

            OrderListResponse orderListResponse = buildOrderListResponse(pickOrderResponse, ocmsOrderList, request);
            // 查询订单营收
            fillOrderRevenueDetail(orderListResponse);

            return orderListResponse;
        }

    }

    private List<FulfillmentOrderDetailDTO> getFulfillmentOrderDetailDTOS(List<TradeShippingOrderDTO> recentListResult) {
        List<ChannelOrderIdKeyReq> channelOrderIdKeyReqs = recentListResult.stream().map(
                tradeShippingOrderDTO -> new ChannelOrderIdKeyReq(tradeShippingOrderDTO.getTradeChannelType(), tradeShippingOrderDTO.getTradeOrderNo())
        ).collect(Collectors.toList());

        List<FulfillmentOrderDetailDTO> fulfillmentOrderDetailDTOList = fulfillmentOrderServiceWrapper.searchFulfillmentOrderListByBatchFulfillmentOrderId(
                ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId(),
                ApiMethodParamThreadLocal.getIdentityInfo().getStoreId(),
                channelOrderIdKeyReqs
        );

        //两遍列表不相等就打个点
        if(Objects.equals(fulfillmentOrderDetailDTOList.size(), recentListResult.size())) {
            Cat.logEvent("RIDER_OFFLINE_PROMOTE_ERROR", "SHIPPING_FULFILLMENT_MISMATCH");
        }

        return fulfillmentOrderDetailDTOList;
    }

    /**
     * 查询拣货配置
     */
    public PickConfigVO queryPickingConfig() {
        if (MccConfigUtil.isPickPhotoConfigurableGrayStore(ApiMethodParamThreadLocal.getIdentityInfo().getStoreId())) {
            return MccConfigUtil.getPickingConfig();
        }

        return null;
    }

    private void fillHasLackInfo(List<OrderVO> orderVOS, Map<String, AbnOrderDTO> orderAbnMap) {
        Optional.ofNullable(orderVOS).orElse(org.assertj.core.util.Lists.newArrayList())
                .forEach(
                        orderVO -> {
                            orderVO.setHasLackGoods(Objects.nonNull(orderAbnMap.get(orderVO.getChannelOrderId())));
                        }
                );
    }


    //次要信息不影响流程
    private void fillOrderRevenueDetail(OrderListResponse orderListResponse) {
        try {
            Map<String, Boolean> permissions = authThriftWrapper.isHasPermission(ImmutableList.of(AuthCodeEnum.SHOW_SALE_PRICE.getAuthCode()));
            boolean showSalePrice = permissions.getOrDefault(AuthCodeEnum.SHOW_SALE_PRICE.getAuthCode(), Boolean.FALSE);

            List<Pair<Integer, String>> bizType2ViewOrderIdPairs = orderListResponse.getOrderList().stream()
                    .map(orderVO -> Pair.of(ChannelOrderConverter.convertChannelId2OrderBizType(orderVO.getChannelId()), orderVO.getChannelOrderId()))
                    .collect(Collectors.toList());

            List<OrderRevenueDetailResponse> orderRevenueDetailList  = showSalePrice ? ocmsOrderServiceWrapper.getOrderListRevenueDetailByViewOrderIds(
                    ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId(), bizType2ViewOrderIdPairs) : Collections.emptyList();

            Map<String, OrderRevenueDetailResponse> orderRevenueMap = orderRevenueDetailList.stream()
                    .collect(Collectors.toMap(OrderRevenueDetailResponse::getOrderViewId, revenue -> revenue, (ov, nv) -> ov));

            orderListResponse.getOrderList().forEach(orderVO -> fillRevenueDetail(orderVO, orderRevenueMap.get(orderVO.getChannelOrderId())));

        } catch (Exception e) {
            log.warn("查询订单营收失败", e);
        }

    }

    private OrderListResponse buildOrderListResponse(RiderOfflinePromotePickOrderResponse pickOrderResponse,
                                                     List<OCMSOrderVO> ocmsOrderList,
                                                     QueryPromotePickOrderRequest request) {
        OrderListResponse response = new OrderListResponse();


        Map<String, OCMSOrderVO> orderVOMap = ocmsOrderList.stream()
                .collect(Collectors.toMap(OnlineBaseOrderVO::getViewOrderId, Function.identity(), (ov, nv) -> ov));

        List<OrderVO> orderVOList = pickOrderResponse.getRiderOfflinePromotePickOrderDTOS().stream()
                .map(pickOrderDto -> {
                    if (!orderVOMap.containsKey(pickOrderDto.getUnifyOrderId())) {
                        log.warn("地推自提单未查到对应订单详情, viewOrderId: {}", pickOrderDto.getUnifyOrderId());
                        return null;
                    }

                    OCMSOrderVO ocmsOrderVO = orderVOMap.get(pickOrderDto.getUnifyOrderId());

                    return buildOrderVO(ocmsOrderVO);
                }).filter(Objects::nonNull)
                .filter(orderVO -> !Objects.equals(orderVO.getOrderStatus(), OrderStatusEnum.CANCELED.getValue())) //过滤已取消的订单
                .sorted(this::compareOrderVo)   //按订单状态排序
                .collect(Collectors.toList());;

        if (CollectionUtils.isEmpty(orderVOList)) {
            response.setOrderList(Collections.emptyList());
        } else {
            //内存分页
            int offset = (request.getPage() - 1) * request.getSize();
            List<OrderVO> pageList = orderVOList.subList(offset, Math.min(orderVOList.size(), request.getPage() * request.getSize()));
            response.setOrderList(pageList);
        }
        PageInfoVO pageInfoVO = new PageInfoVO(request.getPage(), request.getSize(), orderVOList.size());
        response.setPageInfo(pageInfoVO);
        return response;
    }

    private OrderListResponse buildNewOrderListResponse(List<FulfillmentOrderDetailDTO> fulfillmentOrderDetailDTOList,
                                                     List<OCMSOrderVO> ocmsOrderList,
                                                     QueryPromotePickOrderRequest request,
                                                     Map<String, AbnOrderDTO> orderAbnMap) {
        OrderListResponse response = new OrderListResponse();


        Map<String, OCMSOrderVO> orderVOMap = ocmsOrderList.stream()
                .collect(Collectors.toMap(OnlineBaseOrderVO::getViewOrderId, Function.identity(), (ov, nv) -> ov));
        List<OrderVO> orderVOList = fulfillmentOrderDetailDTOList
                .stream()
                .map(fulfillmentOrderDetailDTO -> {
                    if (!orderVOMap.containsKey(fulfillmentOrderDetailDTO.getChannelOrderId())) {
                        log.warn("地推自提单未查到对应订单详情, viewOrderId: {}", fulfillmentOrderDetailDTO.getUnifyOrderId());
                        return null;
                    }
                    OCMSOrderVO ocmsOrderVO = orderVOMap.get(fulfillmentOrderDetailDTO.getChannelOrderId());

                    return buildOrderVO(ocmsOrderVO);

                })
                .filter(Objects::nonNull)
                .sorted(this::compareOrderVo)   //按订单状态排序
                .collect(Collectors.toList());


        if (CollectionUtils.isEmpty(orderVOList)) {
            response.setOrderList(Collections.emptyList());
        } else {
            response.setOrderList(orderVOList);
            fillHasLackInfo(orderVOList, orderAbnMap);
        }

        return response;
    }

    private void fillGoodsItemList(List<OrderVO> orderVOList, List<TradeShippingOrderDTO> recentShippingOrderList, List<OCMSOrderVO> ocmsOrderList) {
        try {
            if (CollectionUtils.isEmpty(orderVOList)) {
                return;
            }

            Map<String, TradeShippingOrderDTO> shippingOrderDTOMap = recentShippingOrderList.stream()
                    .collect(Collectors.toMap(TradeShippingOrderDTO::getTradeOrderNo, Function.identity(), (ov, nv) -> nv));

            Map<String, OCMSOrderVO> orderVOMap = ocmsOrderList.stream()
                    .collect(Collectors.toMap(OnlineBaseOrderVO::getViewOrderId, Function.identity(), (ov, nv) -> ov));

            orderVOList.forEach(orderVO -> {
                TradeShippingOrderDTO tradeShippingOrderDTO = shippingOrderDTOMap.get(orderVO.getChannelOrderId());

                if (Objects.nonNull(tradeShippingOrderDTO) && MccConfigUtil.isDrunkHorseTenant(orderVO.getTenantId()) && MccConfigUtil.isShowGoodsItemListGrayStore(orderVO.getStoreId())) {
                    orderVO.setGoodsItemList(transfer2GoodsItemVO(tradeShippingOrderDTO.getItems()));
                }

                //耗材信息
                OCMSOrderVO ocmsOrderVO = orderVOMap.get(orderVO.getChannelOrderId());

                if (Objects.nonNull(ocmsOrderVO) && MccConfigUtil.isDrunkHorseTenant(orderVO.getTenantId()) && MccConfigUtil.isShowGoodsItemListGrayStore(orderVO.getStoreId())) {
                    orderVO.setNeedWineBottleOpener(
                            parseConsumableMaterialInfo(ocmsOrderVO)
                                    .stream()
                                    .anyMatch(consumable -> {
                                        return MccConfigUtil.getWineBottleOpenerSkuIds().contains(consumable.getSkuId()) && Objects.nonNull(consumable.getCount()) && consumable.getCount() > 0;
                                    }));
                }
            });
        } catch (Exception e) {
            log.error("fillGoodsItemList error", e);
            Cat.logEvent("FILL_GOODS_ITEM", "ERROR");
        }
    }


    private OrderVO buildOrderVO(OCMSOrderVO ocmsOrderVO) {
        List<OCMSOrderItemVO> ocmsOrderItemVOList = ocmsOrderVO.getOcmsOrderItemVOList();
        OrderVO orderVO = new OrderVO();
        orderVO.setTenantId(ocmsOrderVO.getTenantId());
        Integer channelId = ChannelOrderConvertUtils.sourceBiz2Mid(ocmsOrderVO.getOrderBizType());
        orderVO.setChannelId(channelId);
        if (Objects.nonNull(DynamicChannelType.findOf(channelId))) {
            //订单页展示渠道简称
            orderVO.setChannelName(Objects.requireNonNull(DynamicChannelType.findOf(channelId)).getDesc());
        }

        if (Objects.nonNull(ocmsOrderVO.getOcmsDeliveryInfoVO())){
            orderVO.setReceiverName(ocmsOrderVO.getOcmsDeliveryInfoVO().getUserName());
            orderVO.setReceiverPhone(ocmsOrderVO.getOcmsDeliveryInfoVO().getUserPhone());
        }
        orderVO.setStoreId(ocmsOrderVO.getShopId());
        orderVO.setStoreName(ocmsOrderVO.getShopName());
        orderVO.setChannelOrderId(ocmsOrderVO.getViewOrderId());
        orderVO.setSerialNo(ocmsOrderVO.getOrderSerialNumber());

        // 支付时间
        orderVO.setPayTime(ocmsOrderVO.getPayTime() != null ? ocmsOrderVO.getPayTime() : ocmsOrderVO.getCreateTime());
        //商品数量为所有商品数量之和
        orderVO.setActualPayAmt(ocmsOrderVO.getActualPayAmt());
        orderVO.setBizReceiveAmt(ocmsOrderVO.getMerchantAmount());

        orderVO.setOrderStatus(ocmsOrderVO.getOrderStatus());
        OrderStatusEnum orderStatus = OrderStatusEnum.enumOf(ocmsOrderVO.getOrderStatus());
        orderVO.setOrderStatusDesc(orderStatus == null? "未知状态": orderStatus.getDesc());
        orderVO.setCreateTime(ocmsOrderVO.getCreateTime());

        orderVO.setUpdateTime(ocmsOrderVO.getUpdateTime());

        //备注不为空且不为0才展示
        if (StringUtils.isNotEmpty(ocmsOrderVO.getComments()) && !ocmsOrderVO.getComments().equals("0")) {
            orderVO.setComments(ocmsOrderVO.getComments());
        }
        orderVO.setTotalOfflinePrice(ocmsOrderVO.getTotalOfflinePrice());

        if (CollectionUtils.isNotEmpty(ocmsOrderItemVOList)) {
            orderVO.setProductList(ocmsOrderItemVOList.stream().filter(Objects::nonNull).map(ocmsOrderServiceWrapper::buildProductVO).filter(Objects::nonNull).collect(Collectors.toList()));
            Integer itemCount = orderVO.getProductList().stream().filter(Objects::nonNull).mapToInt(ProductVO::getCount).sum();
            orderVO.setItemCount(itemCount);
        }else{
            orderVO.setItemCount(0);
            orderVO.setProductList(Collections.emptyList());
        }
        if (CollectionUtils.isNotEmpty(ocmsOrderVO.getOnlineGiftVOS())) {
            orderVO.setGiftVOList(ocmsOrderVO.getOnlineGiftVOS().stream().filter(Objects::nonNull).map(ocmsOrderServiceWrapper::buildGiftVO).collect(Collectors.toList()));
            orderVO.setGiftCount(ocmsOrderVO.getOnlineGiftVOS().stream().filter(Objects::nonNull).mapToInt(OnlineGiftVO::getGiftQuantity).sum());
        }

        //歪马需要额外信息：解析后的属性 + 赠品的mainSkuId
        if (MccConfigUtil.isDrunkHorseTenant(ocmsOrderVO.getTenantId()) && MccConfigUtil.isNewPickGrayStore(ocmsOrderVO.getShopId())) {
            //解析后的属性
            for (ProductVO productVO : Optional.ofNullable(orderVO.getProductList()).orElse(org.assertj.core.util.Lists.newArrayList())) {
                //之前逻辑是前端过滤为商品属性的，这次只替换tagInfos
                if (CollectionUtils.isNotEmpty(productVO.getTagInfos())) {
                    List<ParsedPropertiesVO> propertiesViewVOList = productVO.getTagInfos().stream()
                            .map(TagInfoVO::getName)
                            .filter(property -> MccConfigUtil.getPropertyColorMap().containsKey(property))
                            .map(property -> new ParsedPropertiesVO(property, MccConfigUtil.getPropertyColorMap().get(property)))
                            .collect(Collectors.toList());
                    productVO.setParsedProperties(propertiesViewVOList);
                }
                productVO.setTagInfos(null);
            }

            //赠品的mainSkuId
            orderVO.setGiftVOList(Optional.ofNullable(ocmsOrderVO.getOnlineGiftVOS())
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(Objects::nonNull)
                    .map(ocmsOrderServiceWrapper::buildGiftVOWithMainSkuId)
                    .collect(Collectors.toList()));
        }

        orderVO.setOrderUserType(ocmsOrderVO.getUserType());
        return orderVO;
    }

    private void fillRevenueDetail(OrderVO orderVO, OrderRevenueDetailResponse orderRevenue) {
        // 订单营收信息
        if(orderVO != null && orderRevenue != null && orderRevenue.getOrderAmountInfo() != null){
            OrderAmountInfo orderAmountInfo = orderRevenue.getOrderAmountInfo();
            RevenueDetailVo revenueDetailVo = new RevenueDetailVo();
            revenueDetailVo.setPromotionInfos(orderRevenue.getPromotionInfos());
            revenueDetailVo.setActualPayAmount(orderAmountInfo.getActualPayAmt());
            revenueDetailVo.setBizActivityAmount(orderAmountInfo.getBizCharge());
            revenueDetailVo.setDeliveryAmount(orderAmountInfo.getDeliveryFee());
            revenueDetailVo.setPackageAmount(orderAmountInfo.getPackageAmount());
            revenueDetailVo.setRevenueAmount(orderAmountInfo.getBizReceiveAmount());
            orderVO.setRevenueDetail(revenueDetailVo);
        }
    }

    /**
     * 先按照订单状态排序 退款中状态跟履约中状态平级 再按照支付时间排序 正在退款的订单会排在后面
     * @param order1
     * @param order2
     * @return
     */
    private int compareOrderVo(OrderVO order1, OrderVO order2) {
        int status1 = order1.getOrderStatus();
        int status2 = order2.getOrderStatus();
        if (Objects.equals(order1.getOrderStatus(), OrderStatusEnum.REFUND_APPLIED.getValue())) {
            status1 = OrderStatusEnum.PICKING.getValue();
        }

        if (Objects.equals(order2.getOrderStatus(), OrderStatusEnum.REFUND_APPLIED.getValue())) {
            status2 = OrderStatusEnum.PICKING.getValue();
        }

        if (!Objects.equals(status1, status2)) {
            return status1 - status2;
        }

        return (int) - (order1.getPayTime() - order2.getPayTime());
    }

    /**
     * 立即履约-操作立即下发预订单的履约工单.
     *
     */
    @Deprecated
    private boolean immediatelyPushDownBookingOrderFulfillWorkOrder(ImmediatelyDeliveryRequest request,BizOrderModel bizOrderModel) {
        ImmediatelyPushDownFulfillWorkOrderRequest tRequest = convert2PushDownFulfillWorkOrder(request,bizOrderModel);

        ImmediatelyPushDownFulfillWorkOrderResponse response = null;

        try {
            response = riderPickingRpcService.immediatelyPushDownBookingOrderFulfillWorkOrder(tRequest);
            log.info("Call RiderPickingThriftService#immediatelyPushDownBookingOrderFulfillWorkOrder. request:{}, response:{}", tRequest, response);
        } catch (Exception e) {
            log.error("Call RiderPickingThriftService#immediatelyPushDownBookingOrderFulfillWorkOrder error. request:{}", tRequest, e);
            return false;
        }
        if (response == null || response.getStatus() == null ||
                response.getStatus().getCode() != com.sankuai.meituan.reco.pickselect.consts.ResultCodeEnum.SUCCESS.getCode()) {
            log.warn("立即下发履约工单失败, request:{}, response:{}", tRequest, response);
            return false;
        }
        return true;
    }

    private boolean immediatelyPushDownBookingOrderByOfc(ImmediatelyDeliveryRequest request, BizOrderModel bizOrderModel) {

        try {
            fulfillmentOrderServiceWrapper.pickDispatch(bizOrderModel.getTenantId(), bizOrderModel.getShopId(), new ChannelOrderKeyDTO(bizOrderModel.getViewOrderId(), bizOrderModel.getOrderBizType()));
        } catch (Exception e) {
            log.error("Call fulfillmentOperateThriftService.pickDispatch. request:{}", request, e);
            return false;
        }

        return true;
    }


    private RiderFinishPickRequest convert2PickRequest(RiderFinishPickingRequest request, long storeId, long accountId, long tenantId) {
        RiderFinishPickRequest tRequest = new RiderFinishPickRequest();
        tRequest.setEmpowerStoreId(storeId);
        tRequest.setAccountId(accountId);
        tRequest.setPickingWoOrderId(request.getPickingWoId());
        tRequest.setPickingCheckPictureUrl(request.getPickingCheckPictureUrl());
        tRequest.setPickingCheckPictureUrlList(request.getPickingCheckPictureUrlList());
        tRequest.setMrnVersion(request.getMrnVersion());
        tRequest.setTenantId(tenantId);
        tRequest.setIsThirdDeliveryPick(request.getIsThirdDelivery());
        tRequest.setPickTaskDoneDtos(request.getPickTasks().stream()
                .map(task -> {
                    RiderPickTaskOpeDTO riderPickTaskOpeDTO = new RiderPickTaskOpeDTO();
                    riderPickTaskOpeDTO.setPickTaskId(task.getPickingTaskId());
                    riderPickTaskOpeDTO.setPickedCount(task.getPickedCount());
                    riderPickTaskOpeDTO.setEnteringType(task.getPickItemStockOutEnteringType());

                    //添加sn码
                    List<SnInfoDto> snInfoDtos = Optional.ofNullable(task.getSnInfoList()).orElse(Collections.emptyList())
                            .stream().map(snInfo -> new SnInfoDto(snInfo.getSnCode(), snInfo.getSnCodeEnteringType()))
                            .collect(Collectors.toList());

                    riderPickTaskOpeDTO.setSnInfoDtoList(snInfoDtos);
                    return riderPickTaskOpeDTO;
                }).collect(Collectors.toList()));
        return tRequest;
    }

    private ImmediatelyPushDownFulfillWorkOrderRequest convert2PushDownFulfillWorkOrder(ImmediatelyDeliveryRequest request,BizOrderModel bizOrderModel) {
        ImmediatelyPushDownFulfillWorkOrderRequest tRequest = new ImmediatelyPushDownFulfillWorkOrderRequest();
        tRequest.setAccountId(request.getUserAccountId());
        tRequest.setEmpowerStoreId(request.getStoreId());
        tRequest.setOrderSource(bizOrderModel.getOrderBizType());
        tRequest.setUnifyOrderId(Long.valueOf(bizOrderModel.getViewOrderId()));
        tRequest.setTenantId(request.getTenantId());
        return tRequest;
    }

    private Boolean checkIsThirdDeliveryOrder(RiderFinishPickingRequest request, Long orderId) {
        if (Objects.isNull(request.getIsThirdDelivery()) || Objects.equals(request.getIsThirdDelivery(), false)) {
            return false;
        }

        List<TDeliveryOrder> tDeliveryOrders =
                tmsServiceWrapper.queryActiveDeliveryInfoByOrderIds(Collections.singletonList(orderId));

        if (CollectionUtils.isEmpty(tDeliveryOrders) || tDeliveryOrders.size() > 1) {
            Cat.logEvent("DH_ADAPT_DAP", "PICK_FINISH_ERROR");
            log.error("当前订单没有生效中的运单或有多个生效中的运单,请联系管理员, orderId: {}", orderId);
            throw new BizException("当前订单没有生效中的运单或有多个生效中的运单,请联系管理员");
        }

        //如果运单是自营配送运单(即不是三方配送运单)
        if (Objects.equals(tDeliveryOrders.get(0).getDeliveryChannel(), DeliveryChannelEnum.MERCHANT_DELIVERY.getCode())) {
            Cat.logEvent("DH_ADAPT_DAP", "PICK_FINISH_ERROR");
            log.error("当前运单不是三方配送运单,请到自营配送页面操作, orderId: {}, deliveryId:{}", orderId, tDeliveryOrders.get(0).getId());
            throw new BizException("当前运单不是三方配送运单,请到自营配送页面操作");
        }

        return true;

    }

    /**
     * 骑手完成取货操作，流转配送状态.
     *
     * @param request 用户请求
     * @param user    用户信息
     * @return 是否取货成功
     */
    private boolean riderTakeAway(RiderFinishPickingRequest request, User user) {
        RiderOperateTRequest tRequest = convert2DeliveryRequest(request, user);

        RiderOperateTResponse response = null;
        try {
            response = riderOperateRpcService.takeAway(tRequest);
            log.info("Call RiderOperateThriftService#takeAway. request:{}, response:{}", tRequest, response);
        } catch (Exception e) {
            log.error("Call RiderOperateThriftService#takeAway error. request:{}", tRequest, e);
            return false;
        }
        if (response == null || response.getStatus() == null ||
                response.getStatus().getCode() != com.sankuai.meituan.reco.pickselect.consts.ResultCodeEnum.SUCCESS.getCode()) {
            log.warn("骑手操作拣货完成时，骑手配送状态流转失败, request:{}, response:{}", tRequest, response);
            return false;
        }
        return true;
    }

    private RiderOperateTRequest convert2DeliveryRequest(RiderFinishPickingRequest request, User user) {
        RiderOperateTRequest tRequest = new RiderOperateTRequest();
        tRequest.setDeliveryOrderId(request.getDeliveryOrderId());
        if (user == null) {
            log.error("请求人信息不完整");
            throw new CommonRuntimeException(ResultCode.PARAM_ERR);
        }
        tRequest.setOperatorId(user.getAccountId());
        tRequest.setOperatorName(user.getOperatorName());
        tRequest.setOperatorPhone(user.getOperatorPhone());
        return tRequest;
    }
}
