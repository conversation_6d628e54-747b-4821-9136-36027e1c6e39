package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;
// Copyright (C) 2020 Meituan
// All rights reserved

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ocms.ResponseConvertUtils;
import com.sankuai.meituan.shangou.empower.ocms.client.common.dto.PageInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.RegionSpuDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

import java.util.List;
import java.util.Map;

/**
 * @author: <EMAIL>
 * @class: RegionSpuPageQueryResponse
 * @date: 2020-06-17 18:03:30
 * @desc:
 */
@TypeDoc(
        description = "区域商品商品SPU分页查询响应结果",
        authors = {"gonglei"}
)
@Getter
@ApiModel("区域商品商品SPU分页查询响应结果")
public class RegionSpuPageQueryResponseVO {
    @FieldDoc(
            description = "商品信息列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品信息列表", required = true)
    private List<RegionSpuVO> regionSpuList;

    @FieldDoc(
            description = "分页信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "分页信息", required = true)
    private PageInfoVO pageInfo;

    public RegionSpuPageQueryResponseVO fillInPageInfo(PageInfoDTO pageInfoDTO) {
        this.pageInfo = ResponseConvertUtils.convertpageInfoVO(pageInfoDTO);
        return this;
    }

    public RegionSpuPageQueryResponseVO fillInData(List<RegionSpuDTO> regionSpuVOList) {
        this.regionSpuList = RegionSpuVO.ofDTOList(regionSpuVOList);
        return this;
    }

    public RegionSpuPageQueryResponseVO fillInCitySaleAmount(Map<String, Integer> salesAmount) {
        for (RegionSpuVO regionSpuVO : regionSpuList) {
            if (salesAmount.containsKey(regionSpuVO.getSpuId())) {
                regionSpuVO.setCityMonthSaleAmount(salesAmount.get(regionSpuVO.getSpuId()));
            }
        }
        return this;
    }
}
