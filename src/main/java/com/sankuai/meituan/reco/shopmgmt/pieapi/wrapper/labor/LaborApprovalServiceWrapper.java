package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.labor;

import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.sankuai.drunkhorsemgmt.labor.thrift.LaborApprovalThriftService;
import com.sankuai.drunkhorsemgmt.labor.thrift.request.LaborApprovalReq;
import com.sankuai.drunkhorsemgmt.labor.thrift.response.BaseResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.LaborApprovalRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor.AttendanceApprovalCreateRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022-12-12
 * @email <EMAIL>
 */
@Slf4j
@Service
public class LaborApprovalServiceWrapper {

    @Resource
    private LaborApprovalThriftService laborApprovalThriftService;

    @MethodLog(logResponse = true, logRequest = true)
    public CommonResponse<Void> approval(LaborApprovalRequest request) {
        LaborApprovalReq laborApprovalReq = new LaborApprovalReq();
        laborApprovalReq.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        laborApprovalReq.setApprovalId(request.getApprovalId());
        laborApprovalReq.setAction(request.getAction());
        laborApprovalReq.setRemark(request.getRemark());
        laborApprovalReq.setEmployeeId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getEmployeeId());
        BaseResponse approval = laborApprovalThriftService.approval(laborApprovalReq);
        if (approval.getStatus().successful()) {
            return CommonResponse.success(null);
        }
        return CommonResponse.fail(approval.getStatus().getCode(), approval.getStatus().getMsg());
    }


}
