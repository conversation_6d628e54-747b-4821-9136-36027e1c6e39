package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.locating;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;


@TypeDoc(
        description = "上报骑手定位异常请求"
)
@Data
@ApiModel("上报骑手定位异常请求")

public class PostLocatingExceptionRequest {

    @FieldDoc(
            description = "骑手账号id",
            requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "骑手账号",required = true)
    @NotNull
    private Long riderAccountId;

    @FieldDoc(
            description = "异常类型",
            requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "异常类型 1-调用后端查询进行中运单接口失败 2-用户在百川-隐私中关闭了定位服务 3-隐私SDK鉴权失败/用户未开启系统定位权限 4-定位SDK获取定位失败 5-前端向后端上报位置接口失败 6-骑手关闭应用 7-骑手切换门店 8-骑手退出登陆 9-未知异常 10-骑手完成配送后45分钟停止定位",required = true)
    @NotNull
    private Integer exceptionType;

    @FieldDoc(
            description = "设备id",
            requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "设备id",required = true)
    @NotNull
    private String uuid;

    @FieldDoc(
            description = "手机操作系统",
            requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "手机操作系统",required = false)
    private String phoneOS;

    @FieldDoc(
            description = "手机厂商",
            requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "手机厂商",required = false)
    private String manufacturer;

    @FieldDoc(
            description = "时间戳",
            requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "时间戳",required = true)
    private Long utime;

}
