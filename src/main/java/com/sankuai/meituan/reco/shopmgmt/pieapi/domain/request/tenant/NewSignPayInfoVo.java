package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.tenant;

import com.dianping.lion.client.util.CollectionUtils;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.order.platform.common.SelfCheckable;
import com.sankuai.meituan.shangou.empower.tenantbill.dto.model.BindStoreDto;
import com.sankuai.meituan.shangou.empower.tenantbill.dto.model.NewSignPayInfoDto;
import com.sankuai.meituan.shangou.empower.tenantbill.dto.model.PurchaseDetailDto;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.stream.Collectors;

@Data
@TypeDoc(
        description = "新签信息"
)
public class NewSignPayInfoVo implements SelfCheckable {

    @FieldDoc(description = "新签信息购买详情")
    @NotEmpty(message = "购买详情不能为空")
    public List<PurchaseDetailVo> purchaseDetails;

    @FieldDoc(description = "计算规则")
    public Long calcRule;

    @FieldDoc(description = "总价格")
    @NotNull(message = "总价格不能为空")
    public Long calcAmount;

    @FieldDoc(description = "crm侧订单id")
    public String crmOrderId;

    @FieldDoc(description = "合同编号")
    public String contractNo;

    public NewSignPayInfoDto buildNewSignPayInfoDto() {
        NewSignPayInfoDto dto = new NewSignPayInfoDto();
        List<PurchaseDetailDto> detailDtos = purchaseDetails.stream().map(detail -> {
            PurchaseDetailDto detailDto = new PurchaseDetailDto();
            detailDto.setServiceType(detail.getServiceType());
            detailDto.setSubType(detail.getSubType());
            detailDto.setUnitNum(detail.getUnitNum());
            detailDto.setAmount(detail.getAmount());
            if(CollectionUtils.isNotEmpty(detail.getBindStoreList())) {
                List<BindStoreDto> bindStoreDtos = detail.bindStoreList.stream().map(store -> {
                    BindStoreDto storeDto = new BindStoreDto();
                    storeDto.setStoreId(store.getStoreId());
                    storeDto.setStoreName(store.getStoreName());
                    storeDto.setUnitNum(store.getUnitNum());
                    return storeDto;
                }).collect(Collectors.toList());
                detailDto.setBindStoreList(bindStoreDtos);
            }
            return detailDto;
        }).collect(Collectors.toList());

        dto.setPurchaseDetails(detailDtos);
        dto.setCalcRule(calcRule);
        dto.setCalcAmount(calcAmount);
        dto.setCrmOrderId(crmOrderId);
        dto.setContractNo(contractNo);
        return dto;
    }
}
