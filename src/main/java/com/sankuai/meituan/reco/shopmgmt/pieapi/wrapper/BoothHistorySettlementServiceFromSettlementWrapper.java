package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;

import com.meituan.shangou.saas.tenant.thrift.ConfigThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.enums.SettlePeriodEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.SettlePeriodDTO;
import com.meituan.shangou.saas.tenant.thrift.dto.booth.BoothSettleAccountsDto;
import com.meituan.shangou.saas.tenant.thrift.dto.config.ConfigDto;
import com.meituan.shangou.saas.tenant.thrift.dto.config.request.ConfigQueryRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.config.response.TenantConfigResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order.revenue.BoothHistorySettlementDailyPageReq;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order.revenue.BoothHistorySettlementDailySummaryReq;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order.revenue.BoothHistorySettlementMonthlyPageReq;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order.revenue.BoothHistorySettlementMonthlySummaryReq;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.revenue.BoothHistorySettlementDailyPageVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.revenue.BoothHistorySettlementDailySummaryVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.revenue.BoothHistorySettlementMonthlyPageVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.revenue.BoothHistorySettlementMonthlySummaryVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.DateUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.AccountTypeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.PermissionGroupTypeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.service.AuthThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.PermissionGroupVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryPermissionGroupRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryPermissionGroupResponse;
import com.sankuai.meituan.shangou.empower.settlement.dto.request.boothsettle.BoothSettlementDailyPageRequest;
import com.sankuai.meituan.shangou.empower.settlement.dto.request.boothsettle.BoothSettlementMonthlyPageRequest;
import com.sankuai.meituan.shangou.empower.settlement.dto.request.boothsettle.BoothSettlementSummaryRequest;
import com.sankuai.meituan.shangou.empower.settlement.dto.response.boothsettle.BoothDailySettlementDetailVO;
import com.sankuai.meituan.shangou.empower.settlement.dto.response.boothsettle.BoothDailySettlementSummaryVO;
import com.sankuai.meituan.shangou.empower.settlement.dto.response.boothsettle.BoothMonthlySettlementDetailVO;
import com.sankuai.meituan.shangou.empower.settlement.dto.response.boothsettle.BoothMonthlySettlementSummaryVO;
import com.sankuai.meituan.shangou.empower.settlement.dto.response.boothsettle.BoothSettlementDailyPageResponse;
import com.sankuai.meituan.shangou.empower.settlement.dto.response.boothsettle.BoothSettlementMonthlyPageResponse;
import com.sankuai.meituan.shangou.empower.settlement.dto.response.boothsettle.BoothSettlementSummaryResponse;
import com.sankuai.meituan.shangou.empower.settlement.dto.response.boothsettle.HistoryConfigVO;
import com.sankuai.meituan.shangou.empower.settlement.services.BoothHistorySettlementQueryService;

import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;

/**
 * 摊位历史账单服务
 *
 * <AUTHOR>
 * @since 2020/02/24
 */
@Service
@Slf4j
public class BoothHistorySettlementServiceFromSettlementWrapper {

    private static final int QUERY_AUTO_SETTLE_CONFIGID = 14;
    public static final String MONTH_TO_DATE = "-01";

    @Resource
    private AuthThriftService.Iface authThriftService;

    @Resource
    private ConfigThriftService configThriftService;

    @Resource(name="boothHistorySettlementQueryFromSettlementService")
    private BoothHistorySettlementQueryService boothHistorySettlementQueryService;

    public CommonResponse<BoothHistorySettlementDailySummaryVO> dailySummary(BoothHistorySettlementDailySummaryReq req) {
        BoothHistorySettlementDailySummaryVO dailySummaryVO = new BoothHistorySettlementDailySummaryVO();
        dailySummaryVO.setIsAutoSettle(false);
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        Long accountId = identityInfo.getUser().getAccountId();
        Integer accountType = identityInfo.getUser().getAccountType();
        long tenantId = identityInfo.getUser().getTenantId();
        try {
            // 自动打款相关
            ConfigDto configDto = getBoothSettleAccount(tenantId, req.getStoreId());
            String settlePeriod = null;
            Long createTime = null;
            if (Objects.nonNull(configDto) && Objects.nonNull(configDto.getConfigContent())) {
                BoothSettleAccountsDto boothSettleAccountsDto = JSONObject.parseObject(configDto.getConfigContent(), BoothSettleAccountsDto.class);
                // 没有配置，则为手动打款
                if (Objects.nonNull(boothSettleAccountsDto) && Objects.nonNull(boothSettleAccountsDto.getAutoSettle()) && Objects.nonNull(boothSettleAccountsDto.getNeedSettle())) {
                    dailySummaryVO.setIsAutoSettle(boothSettleAccountsDto.getNeedSettle() && boothSettleAccountsDto.getAutoSettle());
                    settlePeriod = boothSettleAccountsDto.getSettlePeriod();
                    createTime = configDto.getCreateTime();
                }
            }
            // 自动打款填充结算周期、下一个结算日
            if (dailySummaryVO.getIsAutoSettle() && Objects.nonNull(settlePeriod) && Objects.nonNull(createTime)) {
                SettlePeriodDTO settlePeriodDTO = JSONObject.parseObject(settlePeriod, SettlePeriodDTO.class);
                if (Objects.nonNull(settlePeriodDTO)) {
                    dailySummaryVO.setSettlePeriod(buildSettlePeriod(settlePeriodDTO));
                    String nextAutoSettleDay = calculateNextAutoSettleDay(settlePeriodDTO, createTime);
                    if (Objects.nonNull(nextAutoSettleDay)) {
                        dailySummaryVO.setNextAutoSettleDay(changeDateToDot(nextAutoSettleDay));
                    }
                }
            }
            // 是否是摊主：获取账户信息
            Boolean isBooth = accountTypeIsBooth(accountType);
            dailySummaryVO.setIsBooth(isBooth);
            // 是摊主则获取boothId
            Long boothId = null;
            if (isBooth) {
                boothId = getBoothId(tenantId, accountId);
                if (Objects.isNull(boothId)) {
                    return CommonResponse.fail(ResultCode.FAIL.getCode(), "获取摊位id失败");
                }
                dailySummaryVO.setBoothId(boothId);
            }
            // 其他信息请求mng
            BoothSettlementSummaryResponse boothSettlementSummaryResponse = queryBoothSettlementSummary(tenantId, req.getStoreId(), boothId, req.getStartDate(), req.getEndDate());
            if (boothSettlementSummaryResponse.getStatus().getCode() != ResultCodeEnum.SUCCESS.getValue()) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), boothSettlementSummaryResponse.getStatus().getMessage());
            }
            HistoryConfigVO historyConfig = boothSettlementSummaryResponse.getHistoryConfig();
            BoothSettlementSummaryResponse.SummaryData summaryData = boothSettlementSummaryResponse.getSummaryData();
            dailySummaryVO.setIsSettlementPay(Objects.isNull(historyConfig) ? null : historyConfig.getIsSettlementPay());
            dailySummaryVO.setIsBoothSettlementDiscount(Objects.isNull(historyConfig) ? null : historyConfig.getIsBoothSettlementDiscount());
            dailySummaryVO.setTotalRevenue(Objects.isNull(summaryData) ? null : summaryData.getRevenueAmount());
            dailySummaryVO.setTotalOrderCount(Objects.isNull(summaryData) ? null : summaryData.getCompleteOrderCount());
            // 营收金额文案
            if (Objects.nonNull(historyConfig)) {
                String comment = buildRevenueComment(historyConfig, isBooth, true);
                dailySummaryVO.setRevenueComment(comment);
            }
        }
        catch (Exception e) {
            log.error("BoothHistorySettlementServiceWrapper dailySummary 发生异常：req={}, accountId={}", req, accountId, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "发生业务异常");
        }
        return CommonResponse.success(dailySummaryVO);
    }

    public CommonResponse<BoothHistorySettlementMonthlySummaryVO> monthlySummary(BoothHistorySettlementMonthlySummaryReq req) {
        // 设置按月查询默认时间段
        if (Objects.isNull(req.getStartMonth()) && Objects.isNull(req.getEndMonth())) {
            Pair<String, String> defaultMonth = geMonthlyQueryDefaultMonth();
            req.setStartMonth(defaultMonth.getKey());
            req.setEndMonth(defaultMonth.getValue());
        }
        BoothHistorySettlementMonthlySummaryVO monthlySummaryVO = new BoothHistorySettlementMonthlySummaryVO();
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        Long accountId = identityInfo.getUser().getAccountId();
        Integer accountType = identityInfo.getUser().getAccountType();
        long tenantId = identityInfo.getUser().getTenantId();
        try {
            //是否是摊主：获取账户信息
            Boolean isBooth = accountTypeIsBooth(accountType);
            monthlySummaryVO.setIsBooth(isBooth);
            //其他信息请求mng
            Long boothId = null;
            if (isBooth) {
                boothId = getBoothId(tenantId, accountId);
                if (Objects.isNull(boothId)) {
                    return CommonResponse.fail(ResultCode.FAIL.getCode(), "获取摊位id失败");
                }
                monthlySummaryVO.setBoothId(boothId);
            }
            //计算聚合数据的起止时间
            String beginDate = buildBeginDateOfMonth(req.getStartMonth());
            String endDate = buildEndDateOfMonth(req.getEndMonth());
            if (Objects.nonNull(endDate)) {
                LocalDate yesterday = LocalDate.now().plusDays(-1);
                endDate = (LocalDate.parse(endDate).isBefore(yesterday) ? LocalDate.parse(endDate) : yesterday).toString();
            }
            BoothSettlementSummaryResponse boothSettlementSummaryResponse = queryBoothSettlementSummary(tenantId, req.getStoreId(), boothId, beginDate, endDate);
            if (boothSettlementSummaryResponse.getStatus().getCode() != ResultCodeEnum.SUCCESS.getValue()) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), boothSettlementSummaryResponse.getStatus().getMessage());
            }
            HistoryConfigVO historyConfig = boothSettlementSummaryResponse.getHistoryConfig();
            BoothSettlementSummaryResponse.SummaryData summaryData = boothSettlementSummaryResponse.getSummaryData();
            monthlySummaryVO.setIsSettlementPay(Objects.isNull(historyConfig) ? null : historyConfig.getIsSettlementPay());
            monthlySummaryVO.setIsBoothSettlementDiscount(Objects.isNull(historyConfig) ? null : historyConfig.getIsBoothSettlementDiscount());
            monthlySummaryVO.setTotalRevenue(Objects.isNull(summaryData) ? null : summaryData.getRevenueAmount());
            monthlySummaryVO.setTotalOrderCount(Objects.isNull(summaryData) ? null : summaryData.getCompleteOrderCount());
            // 营收金额文案
            if (Objects.nonNull(historyConfig)) {
                String comment = buildRevenueComment(historyConfig, isBooth, false);
                monthlySummaryVO.setRevenueComment(comment);
            }
            return CommonResponse.success(monthlySummaryVO);
        }
        catch (Exception e) {
            log.error("BoothHistorySettlementServiceWrapper monthlySummary 发生异常：req={}, accountId={}", req, accountId, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "发生业务异常");
        }
    }


    public CommonResponse<BoothHistorySettlementDailyPageVO> dailyList(BoothHistorySettlementDailyPageReq req) {
        BoothHistorySettlementDailyPageVO dailyPageVO = new BoothHistorySettlementDailyPageVO();
        try {
            IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
            long tenantId = identityInfo.getUser().getTenantId();
            BoothSettlementDailyPageRequest boothSettlementDailyPageRequest = new BoothSettlementDailyPageRequest();
            boothSettlementDailyPageRequest.setTenantId(tenantId);
            boothSettlementDailyPageRequest.setShopId(req.getStoreId());
            boothSettlementDailyPageRequest.setBoothId(req.getBoothId());
            boothSettlementDailyPageRequest.setStartDate(req.getStartDate());
            boothSettlementDailyPageRequest.setEndDate(req.getEndDate());
            boothSettlementDailyPageRequest.setPageNo(req.getPageNo());
            boothSettlementDailyPageRequest.setPageSize(req.getPageSize());
            log.info("dailyList 调用boothHistorySettlementQueryService.dailyList：request={}", boothSettlementDailyPageRequest);
            BoothSettlementDailyPageResponse response = boothHistorySettlementQueryService.dailyList(boothSettlementDailyPageRequest);
            log.info("dailyList 调用boothHistorySettlementQueryService.dailyList：response={}", response);
            if (response.getStatus().getCode() != ResultCodeEnum.SUCCESS.getValue()) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), response.getStatus().getMessage());
            }
            dailyPageVO.setHasMore(response.getHasMore());
            List<BoothHistorySettlementDailyPageVO.BoothDailySettlementSummary> summaryList = response.getBoothDailySummaryList().stream()
                    .filter(Objects::nonNull)
                    .map(this::buildBoothDailySettlementSummary)
                    .collect(Collectors.toList());
            dailyPageVO.setBoothDailySummaryList(summaryList);
            return CommonResponse.success(dailyPageVO);
        }
        catch (Exception e) {
            log.error("BoothHistorySettlementServiceWrapper dailyList 发生异常：req={}", req, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "发生业务异常");
        }
    }

    public CommonResponse<BoothHistorySettlementMonthlyPageVO> monthlyList(BoothHistorySettlementMonthlyPageReq req) {
        BoothHistorySettlementMonthlyPageVO monthlyPageVo = new BoothHistorySettlementMonthlyPageVO();
        try {
            //设置按月查询默认时间段
            if (Objects.isNull(req.getStartMonth()) && Objects.isNull(req.getEndMonth())) {
                Pair<String, String> defaultMonth = geMonthlyQueryDefaultMonth();
                req.setStartMonth(defaultMonth.getKey());
                req.setEndMonth(defaultMonth.getValue());
            }
            IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
            long tenantId = identityInfo.getUser().getTenantId();
            BoothSettlementMonthlyPageRequest boothSettlementMonthlyPageRequest = new BoothSettlementMonthlyPageRequest();
            boothSettlementMonthlyPageRequest.setTenantId(tenantId);
            boothSettlementMonthlyPageRequest.setShopId(req.getStoreId());
            boothSettlementMonthlyPageRequest.setBoothId(req.getBoothId());
            boothSettlementMonthlyPageRequest.setStartMonth(req.getStartMonth());
            boothSettlementMonthlyPageRequest.setEndMonth(req.getEndMonth());
            boothSettlementMonthlyPageRequest.setPageNo(req.getPageNo());
            boothSettlementMonthlyPageRequest.setPageSize(req.getPageSize());
            log.info("dailyList 调用boothHistorySettlementQueryService.monthlyList：request={}", boothSettlementMonthlyPageRequest);
            BoothSettlementMonthlyPageResponse response = boothHistorySettlementQueryService.monthlyList(boothSettlementMonthlyPageRequest);
            log.info("dailyList 调用boothHistorySettlementQueryService.monthlyList：response={}", response);
            if (response.getStatus().getCode() != ResultCodeEnum.SUCCESS.getValue()) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), response.getStatus().getMessage());
            }
            monthlyPageVo.setHasMore(response.getHasMore());
            List<BoothHistorySettlementMonthlyPageVO.BoothMonthlySettlementSummary> summaryList = response.getBoothMonthlySettlementSummaryList().stream()
                    .filter(Objects::nonNull)
                    .map(this::buildBoothMonthlySettlementSummary)
                    .collect(Collectors.toList());
            monthlyPageVo.setBoothMonthlySettlementSummaryList(summaryList);
            monthlyPageVo.setNextPageNo(response.getNextPageNo());
            return CommonResponse.success(monthlyPageVo);
        }
        catch (Exception e) {
            log.error("BoothHistorySettlementServiceWrapper monthlyList 发生异常：req={}", req, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "发生业务异常");
        }
    }

    private ConfigDto getBoothSettleAccount(Long tenantId, Long storeId) {
        ConfigQueryRequest configQueryRequest = new ConfigQueryRequest();
        configQueryRequest.setTenantId(tenantId);
        configQueryRequest.setConfigId(QUERY_AUTO_SETTLE_CONFIGID);
        configQueryRequest.setSubjectId(storeId);
        log.info("getBoothSettleAccount 调用queryTenantConfig：configQueryRequest={}", configQueryRequest);
        TenantConfigResponse tenantConfigResponse = configThriftService.queryTenantConfig(configQueryRequest);
        log.info("getBoothSettleAccount 调用queryTenantConfig：response={}", tenantConfigResponse);
        if (tenantConfigResponse.getStatus().getCode() != ResultCodeEnum.SUCCESS.getValue()) {
            return null;
        }
        return tenantConfigResponse.getConfig();
    }

    private Boolean accountTypeIsBooth(Integer accountType) {
        return accountType == AccountTypeEnum.BOOTH.getValue();
    }

    private Long getBoothId(Long tenantId, Long accountId) {
        try {
            QueryPermissionGroupRequest request = new QueryPermissionGroupRequest();
            request.setAccountId(accountId);
            request.setTenantId(tenantId);
            request.setType(PermissionGroupTypeEnum.BOOTH.getValue());
            log.info("getBoothId 调用queryPermissionGroupList：request={}", request);
            QueryPermissionGroupResponse response = authThriftService.queryPermissionGroupList(request);
            log.info("getBoothId 调用queryPermissionGroupList：response={}", response);
            if (response.getResult().getCode() != ResultCodeEnum.SUCCESS.getValue()) {
                return null;
            }
            List<PermissionGroupVo> permissionGroupVos = response.getPermissionGroupCodeList();
            if (CollectionUtils.isEmpty(permissionGroupVos) || permissionGroupVos.size() > 1) {
                return null;
            }
            return Long.valueOf(permissionGroupVos.get(0).getCode());
        }
        catch (Exception e) {
            log.error("查询摊位id发送异常：tenantId={}, accountId={}", tenantId, accountId, e);
            return null;
        }
    }

    private BoothSettlementSummaryResponse queryBoothSettlementSummary(Long tenantId, Long shopId, Long boothId, String beginDate, String endDate) {
        BoothSettlementSummaryRequest boothSettlementSummaryRequest = new BoothSettlementSummaryRequest();
        boothSettlementSummaryRequest.setTenantId(tenantId);
        boothSettlementSummaryRequest.setShopId(shopId);
        boothSettlementSummaryRequest.setStartDate(beginDate);
        boothSettlementSummaryRequest.setEndDate(endDate);
        boothSettlementSummaryRequest.setBoothId(boothId);
        log.info("queryBoothSettlementSummary 调用boothHistorySettlementQueryService.summary：request={}", boothSettlementSummaryRequest);
        BoothSettlementSummaryResponse boothSettlementSummaryResponse = boothHistorySettlementQueryService.summary(boothSettlementSummaryRequest);
        log.info("queryBoothSettlementSummary 调用boothHistorySettlementQueryService.summary：response={}", boothSettlementSummaryResponse);
        return boothSettlementSummaryResponse;
    }

    /**
     * 计算下一个打款日
     *
     * @param settlePeriodDTO 自动打款周期
     * @param createTime      最近一次设置打款周期的时间
     * @return
     */
    public String calculateNextAutoSettleDay(SettlePeriodDTO settlePeriodDTO, Long createTime) {
        Integer type = settlePeriodDTO.getType();
        List<Integer> values = settlePeriodDTO.getValues();
        if (Objects.isNull(type) || CollectionUtils.isEmpty(values) || Objects.isNull(createTime)) {
            return null;
        }
        LocalDate now = LocalDate.now();
        LocalDate createDate = Instant.ofEpochMilli(createTime).atZone(ZoneId.systemDefault()).toLocalDate();
        //计算当前时间距离设置打款周期时间的天数
        //Period.between(now, createDate).getDays()：只能计算同月两天相差的天数，跨月则不行
        Integer betweenDays = (int) (now.toEpochDay() - createDate.toEpochDay());
        SettlePeriodEnum settlePeriodEnum = SettlePeriodEnum.getByCode(type);
        if (Objects.isNull(settlePeriodEnum)) {
            log.warn("未知结算周期");
            return null;
        }
        switch (settlePeriodEnum) {
            case DAYS:
                return now.plusDays((long) (values.get(0) - betweenDays % values.get(0))).toString();
            case WEEK:
                return calculateNextAutoSettleDayOfWeek(now, values).toString();
            case MONTH://打款周期为每月1日：将时间退回到本月1日，再往后挪一个月，即为下月1日
                return calculateNextAutoSettleDayOfMonth(now, values).toString();
            default:
                return null;
        }
    }

    private LocalDate calculateNextAutoSettleDayOfWeek(LocalDate now, List<Integer> values) {
        //今天是周几
        Integer dayOfWeek = now.getDayOfWeek().getValue();
        //本周的第一天
        LocalDate firstOfThisWeek = now.plusDays((long) (1 - dayOfWeek));
        Collections.sort(values);
        //获取本周内，今天后是否有打款日，有打款日，则在最近的打款日打款，没有打款日，则下周第一个打款日打款
        List<Integer> bigThanDayOfWeek = values.stream().filter(value -> value > dayOfWeek).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(bigThanDayOfWeek)) {
            return firstOfThisWeek.plusWeeks(1).plusDays((long) (values.get(0) - 1));
        }
        else {
            return firstOfThisWeek.plusDays((long) (bigThanDayOfWeek.get(0) - 1));
        }
    }

    private LocalDate calculateNextAutoSettleDayOfMonth(LocalDate now, List<Integer> values) {
        //今天是几号
        Integer dayOfMonth = now.getDayOfMonth();
        //本月的第一天
        LocalDate firstOfThisMonth = now.plusDays((long) (1 - dayOfMonth));
        Collections.sort(values);
        //获取本月内，今天后是否有打款日，有打款日，则在最近的打款日打款，没有打款日，则下个月第一个打款日打款
        List<Integer> bigThanDayOfMonth = values.stream().filter(value -> value > dayOfMonth).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(bigThanDayOfMonth)) {
            return firstOfThisMonth.plusMonths(1).plusDays((long) (values.get(0) - 1));
        }
        else {
            return firstOfThisMonth.plusDays((long) (bigThanDayOfMonth.get(0) - 1));
        }
    }

    public String buildSettlePeriod(SettlePeriodDTO settlePeriodDTO) {
        Integer type = settlePeriodDTO.getType();
        List<Integer> values = settlePeriodDTO.getValues();
        if (Objects.isNull(type) || CollectionUtils.isEmpty(values)) {
            return null;
        }
        SettlePeriodEnum settlePeriodEnum = SettlePeriodEnum.getByCode(type);
        switch (settlePeriodEnum) {
            case DAYS:
                return values.get(0) + "天";
            case WEEK:
                List<String> weekText = values.stream().filter(Objects::nonNull).map(this::weekNumToText).collect(Collectors.toList());
                return "每周" + StringUtils.join(weekText, ",");
            case MONTH:
                return "每月" + StringUtils.join(values, ",") + "日";
            default:
                return "未知结算周期";
        }
    }

    //星期几，数字转大写
    private String weekNumToText(Integer weekNum) {
        if (Objects.isNull(weekNum)) {
            return null;
        }
        switch (weekNum) {
            case 1:
                return "一";
            case 2:
                return "二";
            case 3:
                return "三";
            case 4:
                return "四";
            case 5:
                return "五";
            case 6:
                return "六";
            case 7:
                return "天";
            default:
                return null;
        }
    }

    private String buildRevenueComment(HistoryConfigVO historyConfig, boolean isBooth, boolean isDailyQuery) {
        if (Objects.isNull(historyConfig.getIsBoothSettlementDiscount())
                || Objects.isNull(historyConfig.getIsSettlementPay())
                || Objects.isNull(historyConfig.getSettlementModeEnum())) {
            return null;
        }
        //打款结算为否，或者，打款结算为是且折价结算为否，根据结算模式定方案
        if (!historyConfig.getIsSettlementPay() || !historyConfig.getIsBoothSettlementDiscount()) {
            switch (historyConfig.getSettlementModeEnum()) {
                case OFFLINE_PRICE:
                    return "等于线下商品金额之和";
                case POINT_DEDUCTION:
                    return StringUtils.isBlank(historyConfig.getSettlementFormula()) ? "等于扣点公式" : historyConfig.getSettlementFormula();
                case OTHER:
                    return "等于每日营收之和";
                default:
                    return null;
            }
        }

        if (isDailyQuery) {
            if (isBooth) {
                return "等于商品金额*折扣比例";
            }
            else {
                //非摊主账号
                return "等于所有摊位营收金额之和";
            }
        }
        else {
            return "等于所有日期营收金额之和";
        }
    }

    private BoothHistorySettlementDailyPageVO.BoothDailySettlementSummary buildBoothDailySettlementSummary(BoothDailySettlementSummaryVO summaryVO) {
        BoothHistorySettlementDailyPageVO.BoothDailySettlementSummary dailySummary = new BoothHistorySettlementDailyPageVO.BoothDailySettlementSummary();
        dailySummary.setRevenueAmount(summaryVO.getRevenueAmount());
        dailySummary.setSettleDate(changeDateToDot(summaryVO.getSettleDate()));
        dailySummary.setTotalOrderCount(summaryVO.getTotalOrderCount());
//        dailySummary.setTotalRevenueComment(summaryVO.getTotalRevenueComment());
        if (CollectionUtils.isNotEmpty(summaryVO.getBoothDailySettlementDetailList())) {
            //将详情按照boothId升序排列
            List<BoothDailySettlementDetailVO> detailVOList = summaryVO.getBoothDailySettlementDetailList();
            Comparator<BoothDailySettlementDetailVO> detailVOComparator = new Comparator<BoothDailySettlementDetailVO>() {
                @Override
                public int compare(BoothDailySettlementDetailVO detail1, BoothDailySettlementDetailVO detail2) {
                    return (int) (detail1.getBoothId() - detail2.getBoothId());
                }
            };
            detailVOList.sort(detailVOComparator);
            List<BoothHistorySettlementDailyPageVO.BoothDailySettlementDetail> details = detailVOList.stream()
                    .filter(Objects::nonNull)
                    .map(this::buildBoothDailySettlementDetail)
                    .collect(Collectors.toList());
            dailySummary.setBoothDailySettlementDetailList(details);
        }
        return dailySummary;
    }

    private BoothHistorySettlementDailyPageVO.BoothDailySettlementDetail buildBoothDailySettlementDetail(BoothDailySettlementDetailVO detailVO) {
        BoothHistorySettlementDailyPageVO.BoothDailySettlementDetail dailyDetail = new BoothHistorySettlementDailyPageVO.BoothDailySettlementDetail();
        dailyDetail.setBoothId(detailVO.getBoothId());
        dailyDetail.setBoothName(detailVO.getBoothName());
        dailyDetail.setDiscountRatio(detailVO.getDiscountRatio());
        dailyDetail.setProductTotalAmount(detailVO.getProductTotalAmount());
        dailyDetail.setSettleStatus(detailVO.getSettleStatus());
        dailyDetail.setSettleStatusDesc(detailVO.getSettleStatusDesc());
        dailyDetail.setRevenueAmount(detailVO.getRevenueAmount());
        dailyDetail.setTotalOrderCount(detailVO.getTotalOrderCount());
        return dailyDetail;
    }

    private BoothHistorySettlementMonthlyPageVO.BoothMonthlySettlementSummary buildBoothMonthlySettlementSummary(BoothMonthlySettlementSummaryVO summaryVO) {
        BoothHistorySettlementMonthlyPageVO.BoothMonthlySettlementSummary monthlySummary = new BoothHistorySettlementMonthlyPageVO.BoothMonthlySettlementSummary();
        monthlySummary.setRevenueAmount(summaryVO.getRevenueAmount());
        monthlySummary.setSettleMonth(changeDateToDot(summaryVO.getSettleMonth()));
        monthlySummary.setTotalOrderCount(summaryVO.getTotalOrderCount());
//        monthlySummary.setTotalRevenueComment(summaryVO.getTotalRevenueComment());
        if (CollectionUtils.isNotEmpty(summaryVO.getBoothMonthlySettlementDetailList())) {
            //将详情按照boothId升序排列
            List<BoothMonthlySettlementDetailVO> detailVOList = summaryVO.getBoothMonthlySettlementDetailList();
            Comparator<BoothMonthlySettlementDetailVO> detailVOComparator = new Comparator<BoothMonthlySettlementDetailVO>() {
                @Override
                public int compare(BoothMonthlySettlementDetailVO detail1, BoothMonthlySettlementDetailVO detail2) {
                    return (int) (detail1.getBoothId() - detail2.getBoothId());
                }
            };
            detailVOList.sort(detailVOComparator);
            List<BoothHistorySettlementMonthlyPageVO.BoothMonthlySettlementDetail> details = detailVOList.stream()
                    .filter(Objects::nonNull)
                    .map(this::buildBoothMonthlySettlementDetail)
                    .collect(Collectors.toList());
            monthlySummary.setBoothMonthlySettlementDetailList(details);
        }
        return monthlySummary;
    }

    private BoothHistorySettlementMonthlyPageVO.BoothMonthlySettlementDetail buildBoothMonthlySettlementDetail(BoothMonthlySettlementDetailVO detailVO) {
        BoothHistorySettlementMonthlyPageVO.BoothMonthlySettlementDetail monthlyDetail = new BoothHistorySettlementMonthlyPageVO.BoothMonthlySettlementDetail();
        monthlyDetail.setBoothId(detailVO.getBoothId());
        monthlyDetail.setBoothName(detailVO.getBoothName());
        monthlyDetail.setProductTotalAmount(detailVO.getProductTotalAmount());
        monthlyDetail.setSettleStatus(detailVO.getSettleStatus());
        monthlyDetail.setSettleStatusDesc(detailVO.getSettleStatusDesc());
        monthlyDetail.setRevenueAmount(detailVO.getRevenueAmount());
        monthlyDetail.setTotalOrderCount(detailVO.getTotalOrderCount());
        return monthlyDetail;
    }

    //yyyy-MM-dd转为yyyy.MMM.dd，yyyy-MM转为yyyy.MMM
    private String changeDateToDot(String date) {
        if (StringUtils.isEmpty(date)) {
            return null;
        }
        return date.replace("-", ".");
    }

    private String buildBeginDateOfMonth(String beginMonth) {
        if (StringUtils.isEmpty(beginMonth)) {
            return null;
        }
        LocalDate startDate = LocalDate.parse(beginMonth + MONTH_TO_DATE);
        if (Objects.isNull(startDate)) {
            return null;
        }
        return DateUtils.getFirstDayOfMonth(startDate.getYear(), startDate.getMonthValue());
    }

    private String buildEndDateOfMonth(String endMonth) {
        if (StringUtils.isEmpty(endMonth)) {
            return null;
        }
        LocalDate endDate = LocalDate.parse(endMonth + MONTH_TO_DATE);
        if (Objects.isNull(endDate)) {
            return null;
        }
        return DateUtils.getLastDayOfMonth(endDate.getYear(), endDate.getMonthValue());
    }

    //获取按月查询默认起止月份，key为起始月份，value为截止月份，格式：yyyy-MM
    private Pair<String, String> geMonthlyQueryDefaultMonth() {
        LocalDate yesterday = LocalDate.now().plusDays(-1);
        LocalDate beginOfYesterdayMonth = yesterday.plusDays(1L - yesterday.getDayOfMonth());
        String defaultEndMonth = DateUtils.format(beginOfYesterdayMonth, DateUtils.YYYY_MM);
        LocalDate plusHalfYear = beginOfYesterdayMonth.plusMonths(-5);
        String defaultBeginMonth = DateUtils.format(plusHalfYear, DateUtils.YYYY_MM);
        return new Pair<>(defaultBeginMonth, defaultEndMonth);
    }

}
