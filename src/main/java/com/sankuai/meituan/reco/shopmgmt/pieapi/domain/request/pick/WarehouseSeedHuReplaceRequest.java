package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/15
 */
@TypeDoc(
        description = "Hu换绑请求体"
)
@ApiModel("Hu换绑请求体")
@Data
public class WarehouseSeedHuReplaceRequest {
    @FieldDoc(
            description = "波次号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "波次号")
    private String waveId;

    @FieldDoc(
            description = "波次任务号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "波次任务号")
    private Integer taskId;

    @FieldDoc(
            description = "转箱前箱码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "转箱前箱码")
    private String beforeHuCode;

    @FieldDoc(
            description = "转箱前箱码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "转箱前箱码")
    private String afterHuCode;
}
