package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.regionselect;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2022/12/8 14:52
 **/
@Data
public class RentalSourceOfflineSurveyRequest {
    @NotNull
    @FieldDoc(description = "仓源Id")
    private Long sourceId;

    @NotNull
    @FieldDoc(description = "共识状态Id")
    private Integer consensusStatus;
}
