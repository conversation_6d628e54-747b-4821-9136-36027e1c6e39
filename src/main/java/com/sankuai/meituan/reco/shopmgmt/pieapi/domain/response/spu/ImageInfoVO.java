package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "门店商品图片展示信息"
)
@Data
@Builder
@ApiModel("门店商品图片展示信息")
public class ImageInfoVO {
    @FieldDoc(
            description = "商品图片", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品图片", required = true)
    private String imgUrl;

    @FieldDoc(
            description = "当前图片是否审核删除", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "当前图片是否审核删除", required = true)
    private Boolean isDelete;
}
