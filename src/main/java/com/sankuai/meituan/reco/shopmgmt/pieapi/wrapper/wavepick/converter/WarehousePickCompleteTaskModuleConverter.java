package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.wavepick.converter;

import com.sankuai.meituan.reco.pickselect.thrift.wave.dto.WarehousePickCompleteTaskModuleDTO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.WarehousePickCompleteTaskModuleVO;
import org.mapstruct.Mapper;

/**
 * @Description:
 * @Author: zhangjian155
 * @Date: 2022/8/25 16:52
 */
@Mapper(componentModel = "spring")
public abstract class WarehousePickCompleteTaskModuleConverter {
    public abstract WarehousePickCompleteTaskModuleVO convert2Vo(WarehousePickCompleteTaskModuleDTO warehousePickCompleteTaskModuleDTO);
}
