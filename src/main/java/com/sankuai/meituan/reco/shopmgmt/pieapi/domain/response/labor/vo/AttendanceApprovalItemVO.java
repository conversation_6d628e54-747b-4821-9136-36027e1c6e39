package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * Email <EMAIL>
 * Date 2022/10/14 6:25 下午
 * Description
 */

@TypeDoc(
        description = "考勤审批"
)
@ApiModel("考勤审批")
@Data
public class AttendanceApprovalItemVO {

    @FieldDoc(
            description = "申诉记录baseInfoId"
    )
    @ApiModelProperty("申诉记录baseInfoId")
    private Long id;

    @FieldDoc(
            description = "考勤记录Id"
    )
    @ApiModelProperty("考勤记录Id")
    private Long resultStatisticsId;

    @FieldDoc(
            description = "申诉用户Id"
    )
    @ApiModelProperty("申诉用户Id")
    private Long applyUserId;

    @FieldDoc(
            description = "申诉用户名"
    )
    @ApiModelProperty("申诉用户名")
    private String applyUserName;

    @FieldDoc(
            description = "申诉场景"
    )
    @ApiModelProperty("申诉场景")
    private String eventScene;

    @FieldDoc(
            description = "申诉原因说明"
    )
    @ApiModelProperty("申诉原因说明")
    private String applyText;

    @FieldDoc(
            description = "审批结果"
    )
    @ApiModelProperty("审批结果")
    private Integer approvalStatus;

    @FieldDoc(
            description = "审批结果描述"
    )
    @ApiModelProperty("审批结果描述")
    private String approvalStatusText;

    @FieldDoc(
            description = "申诉提交时间"
    )
    @ApiModelProperty("申诉提交时间")
    private Long createTime;

    @FieldDoc(
            description = "申诉完结时间"
    )
    @ApiModelProperty("申诉完结时间")
    private Long finishTime;

    @FieldDoc(
            description = "考勤结果统计日期"
    )
    @ApiModelProperty("考勤结果统计日期")
    private String resultDate;

    @FieldDoc(
            description = "门店id"
    )
    @ApiModelProperty("门店id")
    private Long poiId;

    @FieldDoc(
            description = "门店名称"
    )
    @ApiModelProperty("门店名称")
    private String poiName;

    @FieldDoc(
            description = "门店名称"
    )
    @ApiModelProperty("门店名称")
    private List<Integer> attendanceExceptions;

    @FieldDoc(
            description = "上班打卡时间"
    )
    @ApiModelProperty("上班打卡时间")
    private Long startWorkCheckinTime;

    @FieldDoc(
            description = "下班打卡时间"
    )
    @ApiModelProperty("下班打卡时间")
    private Long endWorkCheckinTime;

    @FieldDoc(
            description = "实际打卡地点距离门店位置"
    )
    @ApiModelProperty("实际打卡地点距离门店位置")
    private Long checkinDistanceToPoi;

    @FieldDoc(
            description = "打卡拍照url"
    )
    @ApiModelProperty("打卡拍照url")
    private String checkinPhotoUrl;

    @FieldDoc(
            description = "异常持续时间,单位为秒"
    )
    @ApiModelProperty("异常持续时间,单位为秒")
    private Long exceptionDuration;

    @FieldDoc(
            description = "申述照片组"
    )
    @ApiModelProperty("申述照片组")
    private List<String> photoList;

    @FieldDoc(
            description = "当前申述总次数"
    )
    @ApiModelProperty("当前申述总次数")
    private Integer approvalTimes;

    @FieldDoc(
            description = "最大允许申述总次数"
    )
    @ApiModelProperty("最大允许申述总次数")
    private Integer maxApprovalTimes;
}
