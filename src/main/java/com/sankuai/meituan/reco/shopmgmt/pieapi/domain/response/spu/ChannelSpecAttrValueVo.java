package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.dto.ChannelSpecAttrValueDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/7/1 17:56
 **/
@TypeDoc(
        name = "渠道特殊属性值VO对象",
        description = "渠道特殊属性值VO对象"
)
@Data
@ToString
@EqualsAndHashCode
public class ChannelSpecAttrValueVo {

    @FieldDoc(
            description = "属性值Id"
    )
    private String valueId;

    @FieldDoc(
            description = "值文本"
    )
    private String value;

    @FieldDoc(
            description = "属性值Id路径"
    )
    private String valueIdPath;

    @FieldDoc(
            description = "值文本路径"
    )
    private String valuePath;

    public static List<ChannelSpecAttrValueVo> build(List<ChannelSpecAttrValueDTO> attrValueDTOList){
        if(CollectionUtils.isEmpty(attrValueDTOList)){
            return Lists.newArrayList();
        }
        List<ChannelSpecAttrValueVo> attrValueVoList = new ArrayList<>();
        for(ChannelSpecAttrValueDTO attrValueDTO : attrValueDTOList) {
            ChannelSpecAttrValueVo attrValueVo = new ChannelSpecAttrValueVo();
            attrValueVo.setValueId(attrValueDTO.getValueId());
            attrValueVo.setValue(attrValueDTO.getValue());
            attrValueVo.setValueIdPath(attrValueDTO.getValueIdPath());
            attrValueVo.setValuePath(attrValueDTO.getValuePath());
            attrValueVoList.add(attrValueVo);
        }
        return attrValueVoList;
    }
}
