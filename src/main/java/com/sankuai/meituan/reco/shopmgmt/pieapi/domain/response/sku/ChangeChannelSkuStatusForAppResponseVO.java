package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "商品批量上下架响应"
)
@Data
@ApiModel("商品批量上下架响应")
public class ChangeChannelSkuStatusForAppResponseVO {

    @FieldDoc(
            description = "错误码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "错误码", required = true)
    private Integer errorCode;

    @FieldDoc(
            description = "错误记录集合", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "错误记录集合", required = true)
    private List<ErrorRecordVO> errorRecordList;
}
