package com.sankuai.meituan.reco.shopmgmt.pieapi.domain;

import lombok.Data;

/**
 * 门店商品规格采购价
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2022/1/26
 **/
@Data
public class SkuPurchasePriceVo {

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 规格ID
     */
    private String skuId;
    /**
     * 是否为组合商品
     */
    private Boolean isComposeSku = false;
    /**
     * 最近采购价
     */
    private String latestPurchasePrice;
    /**
     * 默认主供采购价(基本单位)
     */
    private String masterPurchasePrice;

    /**
     * 加权库存成本价(基本单位)
     */
    private String weightedInventoryCostPrice;

    /**
     * 加权库存成本价单位(基本单位)
     */
    private String weightedInventoryCostPriceUnit;


    /**
     * 最近下单价(基本单位)
     */
    private String lastDeliverPrice;

    /**
     * 最近下单价单位(基本单位)
     */
    private String lastDeliverPriceUnit;

    /**
     * 链接最新价(基本单位)
     */
    private String baseChannelGoodsPrice;

    /**
     * 默认供应商渠道类型 0或null 线下，1 闪电仓，2 1688
     */
    private Integer defaultSupplierType;

    /**
     * 采购最近更新时间
     */
    private String latestTime;

    /**
     * 采购商名称
     */
    private String supplierName;


    public static SkuPurchasePriceVo build(Long storeId, String skuId) {
        SkuPurchasePriceVo vo = new SkuPurchasePriceVo();
        vo.setStoreId(storeId);
        vo.setSkuId(skuId);
        return vo;
    }
}
