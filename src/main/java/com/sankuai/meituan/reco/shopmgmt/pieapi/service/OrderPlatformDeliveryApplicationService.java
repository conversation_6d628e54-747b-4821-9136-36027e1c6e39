package com.sankuai.meituan.reco.shopmgmt.pieapi.service;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery.orderplatform.AuditExceptionRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery.orderplatform.CallAgainRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery.orderplatform.CancelRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery.orderplatform.RaiseTipRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery.orderplatform.ReportExceptionRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.ocms.channel.exception.DeliveryOperationFailedException;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.dto.response.TmsResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.OrderPlatformDeliveryOperateThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.orderplatform.AuditExceptionReq;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.orderplatform.CallDeliveryReq;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.orderplatform.CancelDeliveryReq;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.orderplatform.ReportExceptionReq;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.orderplatform.UpdateTipReq;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response.DeliveryOperationResultDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * OrderPlatformDeliveryApplicationService
 *
 * <AUTHOR>
 * @since 2023/3/1
 */
@Slf4j
@Service
public class OrderPlatformDeliveryApplicationService {

    @Autowired
    private OrderPlatformDeliveryOperateThriftService orderPlatformDeliveryOperateThriftService;

    public void cancel(CancelRequest req) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        User user = identityInfo.getUser();
        Long storeId = Objects.nonNull(req.getStoreId()) ? req.getStoreId() : identityInfo.getStoreId();
        CancelDeliveryReq rpcReq = new CancelDeliveryReq(user.getTenantId(), storeId,
                req.getEmpowerOrderId(), req.getOrderChannel(), user.getAccountId(),
                user.getOperatorName(), identityInfo.getAppId());
        try {
            TmsResponse<DeliveryOperationResultDto> response = orderPlatformDeliveryOperateThriftService.cancelDelivery(
                    rpcReq);
            checkResult(response);
        } catch (TException e) {
            log.error("#cancelDelivery error: {}", e.getMessage(), e);
            throw new CommonRuntimeException(e);
        }
    }

    public void raiseTip(RaiseTipRequest req) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        User user = identityInfo.getUser();
        UpdateTipReq rpcReq = new UpdateTipReq(user.getTenantId(), identityInfo.getStoreId(),
                req.getEmpowerOrderId(), req.getOrderChannel(), req.getTip(),
                user.getAccountId(), user.getOperatorName(), identityInfo.getAppId());
        try {
            TmsResponse<DeliveryOperationResultDto> response = orderPlatformDeliveryOperateThriftService.updateTip(
                    rpcReq);
            checkResult(response);
        } catch (TException e) {
            log.error("#updateTip error: {}", e.getMessage(), e);
            throw new CommonRuntimeException(e);
        }
    }

    public void reportException(ReportExceptionRequest req) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        User user = identityInfo.getUser();
        ReportExceptionReq rpcReq = new ReportExceptionReq(user.getTenantId(), identityInfo.getStoreId(),
                req.getEmpowerOrderId(), req.getOrderChannel(), req.getPictureUrls(),
                user.getAccountId(), user.getOperatorName(), identityInfo.getAppId());
        try {
            TmsResponse<DeliveryOperationResultDto> response = orderPlatformDeliveryOperateThriftService.reportException(rpcReq);
            checkResult(response);
        } catch (TException e) {
            log.error("#reportException error: {}", e.getMessage(), e);
            throw new CommonRuntimeException(e);
        }
    }

    public void auditException(AuditExceptionRequest req) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        User user = identityInfo.getUser();
        AuditExceptionReq rpcReq = new AuditExceptionReq(user.getTenantId(), identityInfo.getStoreId(),
                req.getEmpowerOrderId(), req.getOrderChannel(), req.getIsAgree(),
                user.getAccountId(), user.getOperatorName(), identityInfo.getAppId());
        try {
            TmsResponse<DeliveryOperationResultDto> response = orderPlatformDeliveryOperateThriftService.auditException(rpcReq);
            checkResult(response);
        } catch (TException e) {
            log.error("#auditException error: {}", e.getMessage(), e);
            throw new CommonRuntimeException(e);
        }
    }

    public void callAgain(CallAgainRequest req) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        User user = identityInfo.getUser();
        CallDeliveryReq rpcReq = new CallDeliveryReq(user.getTenantId(), identityInfo.getStoreId(),
                req.getEmpowerOrderId(), req.getOrderChannel(), user.getAccountId(),
                user.getOperatorName(), identityInfo.getAppId());
        try {
            TmsResponse<DeliveryOperationResultDto> response = orderPlatformDeliveryOperateThriftService.callDelivery(rpcReq);
            checkResult(response);
        } catch (TException e) {
            log.error("#callDelivery error: {}", e.getMessage(), e);
            throw new CommonRuntimeException(e);
        }
    }

    private void checkResult(TmsResponse<DeliveryOperationResultDto> response) {
        if (!response.isSuccess()) {
            throw new DeliveryOperationFailedException(response.getMsg());
        }
    }

}
