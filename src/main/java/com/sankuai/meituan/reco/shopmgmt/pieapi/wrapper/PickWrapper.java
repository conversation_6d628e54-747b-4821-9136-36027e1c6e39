package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.dianping.rhino.annotation.Degrade;
import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.mtrace.Tracer;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OCMSListViewIdConditionRequest;
import com.meituan.shangou.saas.order.management.client.dto.request.online.ViewIdCondition;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OCMSListViewIdConditionResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderItemVO;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OrderItemTagVO;
import com.meituan.shangou.saas.order.management.client.enums.StatusCodeEnum;
import com.meituan.shangou.saas.order.management.client.service.online.OCMSQueryThriftService;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.ItemTagEnum;
import com.meituan.shangou.saas.tenant.thrift.BoothThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.booth.BoothInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.booth.response.BoothListResponse;
import com.meituan.shangou.saas.utils.ChannelTypeConvertUtils;
import com.sankuai.meituan.reco.pickselect.common.Status;
import com.sankuai.meituan.reco.pickselect.consts.TabCountEnum;
import com.sankuai.meituan.reco.pickselect.consts.WarehouseType;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.AutoPickDoneConfig;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.AutoPickDoneConfigInfo;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.OperateConfig;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.ResvAutoPickDoneConfig;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.ShowConfig;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.Store;
import com.sankuai.meituan.reco.pickselect.logic.thrift.compass.struct.request.CommonCompassDataReq;
import com.sankuai.meituan.reco.pickselect.logic.thrift.compass.struct.response.QueryTimeOutDataResp;
import com.sankuai.meituan.reco.pickselect.thrift.OpenPickThriftService;
import com.sankuai.meituan.reco.pickselect.thrift.OpenTabCountResponse;
import com.sankuai.meituan.reco.pickselect.query.thrift.picking.PickingThriftService;
import com.sankuai.meituan.reco.pickselect.query.thrift.picking.dto.WaitPickOrderInfoDTO;
import com.sankuai.meituan.reco.pickselect.query.thrift.picking.dto.WaitPickTaskInfoDTO;
import com.sankuai.meituan.reco.pickselect.query.thrift.picking.request.WaitPickWORequest;
import com.sankuai.meituan.reco.pickselect.query.thrift.picking.response.WaitPickWOResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.IntegerBooleanConstants;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.OrderAcceptMode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.converters.sku.SkuForPickAndOutwardSourcingConverter;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.ChannelAutoPickDoneConfig;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.ResvChannelAutoPickDoneConfig;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.bo.ChannelTypeEnumBo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.config.AccountStoreFulfillConfigVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.config.StoreFulfillShowConfigVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.ActionAfterLackConfig;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.auth.BatchAuthCodeCheckRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick.PickSelectConfigUpdateReq;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.QuerySkusForPickAndOutwardSourcingRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.auth.BatchAuthCodeCheckResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.PickHomePageDataVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.PickHomePageModuleVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.PickHomePageResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.PickSelectConfigResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.OrderForPickAndOutwardSourcingVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.QuerySkusForPickAndOutwardSourcingResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.SkuForPickAndOutwardSourcingVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.DaySeqNumUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.JacksonUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.TimeUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.StoreBoothSkuKeyBatchQueryResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.StoreBoothSkuKeyQueryBySkuRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.StoreBoothSkuThriftService;
import com.sankuai.meituan.shangou.empower.trade.dto.model.TradeInfoWithSkuKeyDTO;
import com.sankuai.meituan.shangou.empower.trade.dto.model.TradeSkuKeyDTO;
import com.sankuai.meituan.shangou.empower.trade.dto.request.QueryValidTradeByProductRequest;
import com.sankuai.meituan.shangou.empower.trade.dto.response.QueryValidTradeByProductResponse;
import com.sankuai.meituan.shangou.empower.trade.services.SettleTradeThriftService;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.meituan.shangou.saas.crm.data.client.dto.realtime.BoxedDTO;
import com.sankuai.meituan.shangou.saas.crm.data.client.dto.realtime.IndicInfoDTO;
import com.sankuai.meituan.shangou.saas.crm.data.client.request.realtime.BoxedIndicatorRequest;
import com.sankuai.meituan.shangou.saas.crm.data.client.response.realtime.BoxedIndicatorDataResponse;
import com.sankuai.meituan.shangou.saas.crm.data.client.service.RealtimeThriftService;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2019/11/12
 * desc: 拣货服务
 */
@Service
@Slf4j
public class PickWrapper {

    private static final Integer PERCENT_CONVERT = 10000;
    private static final int NONEED_SET_VALUE = -1;
    private static final int PICK_USE_SECONDS_KEY = 6;
    private static final int PICK_OVERTIME_RATE = 3;
    private static final long ONE_MINUTES = TimeUnit.MINUTES.toSeconds(1);

    @Resource
    private OpenPickThriftService.Iface openPickThriftService;

    @Resource
    private StoreFulfillConfigWrapper storeFulfillConfigWrapper;

    @Resource
    private AuthThriftWrapper authThriftWrapper;

    @Resource
    private SettleTradeThriftService settleTradeThriftService;

    @Resource
    private OCMSQueryThriftService ocmsQueryThriftService;

    @Resource
    private BoothThriftService boothThriftService;

    @Resource
    private StoreBoothSkuThriftService.Iface storeBoothSkuThriftService;

    @Resource(name = "queryPickingThriftService")
    private PickingThriftService queryPickingThriftService;

    @Resource(name = "pickingThriftService")
    private com.sankuai.meituan.reco.pickselect.thrift.picking.PickingThriftService pickingThriftService;

    @Resource
    private SkuForPickAndOutwardSourcingConverter skuForPickAndOutwardSourcingConverter;

    @Autowired
    private RealtimeThriftService realtimeThriftService;

    @Autowired
    private TenantWrapper tenantWrapper;

    @Degrade(rhinoKey = "PickWrapper.homepage",
            fallBackMethod = "homepageFallback",
            timeoutInMilliseconds = 5000)
    public CommonResponse<PickHomePageResponse> homepage() {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        Long accountId = identityInfo.getUser().getAccountId();
        Long offlineStoreId = getOfflineStoreIdFromContext(identityInfo);
        //获取业务配置
        AccountStoreFulfillConfigVO accountStoreFulfillConfigVO = storeFulfillConfigWrapper.getAccountStoreFulfillConfig(identityInfo.getUser().getTenantId(), offlineStoreId, accountId);
        //获取用户权限
        List<String> moduleList = Lists.newArrayList();
        PickHomePageModuleVO pickHomePageModuleVO = getPickHomePageModule(moduleList, accountStoreFulfillConfigVO.getMergeTaskMode());
        if(Objects.isNull(pickHomePageModuleVO)){
            log.error("PickWrapper.homepage() 获取权限失败");
            throw new CommonRuntimeException("获取权限失败");
        }
        PickHomePageDataVO pickHomePageDataVO = new PickHomePageDataVO();
        if(CollectionUtils.isNotEmpty(moduleList)){
            try{
                //查询拣货tab聚合接口
                log.info("PickWrapper.homepage  调用openPickThriftService.queryTabCount offlineStoreId:{}, accountId:{}, moduleList:{}", offlineStoreId, accountId, moduleList);
                Tracer.putContext("trace.context.appId", identityInfo.getAppId());
                OpenTabCountResponse response = openPickThriftService.queryTabCount(offlineStoreId, accountId, moduleList);
                log.info("PickWrapper.homepage  调用openPickThriftService.queryTabCount response:{}", response);
                if (response.getCode() == ResultCodeEnum.SUCCESS.getValue()) {
                    //获取租户的业务配置，构建拣货首页聚合数据
                    buildPickHomePageDataVO(pickHomePageDataVO, response.getTabCountList(), accountStoreFulfillConfigVO);
                }
            }catch (TException e){
                log.error("PickWrapper.homepage  调用openPickThriftService.queryTabCount error", e);
            }
        }

        //查询拣货平均时长、超时率
        if (pickHomePageModuleVO.getShowPickStatisticsTab().equals(IntegerBooleanConstants.BOOLEAN_TRUE)) {

            try{
                BoxedIndicatorRequest indicatorRequest = new BoxedIndicatorRequest();
                indicatorRequest.setModelCode("real_pick");
                indicatorRequest.setPoiIds(ImmutableList.of(offlineStoreId));
                indicatorRequest.setTenantId(identityInfo.getUser().getTenantId());
                indicatorRequest.setStartDate(LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE));
                // 开始时间和结束时间相同
                indicatorRequest.setEndDate(indicatorRequest.getStartDate());
                log.info("PickWrapper.homepage  调用realtimeThriftService.getBoxedIndicatorData:{}", indicatorRequest);
                BoxedIndicatorDataResponse boxedIndicatorData = realtimeThriftService.getBoxedIndicatorData(indicatorRequest);
                log.info("PickWrapper.homepage  调用realtimeThriftService.getBoxedIndicatorData response:{}", boxedIndicatorData);
                if(boxedIndicatorData != null &&
                        boxedIndicatorData.getCode() == com.sankuai.meituan.shangou.saas.crm.data.client.enums.ResultCodeEnum.SUCCESS){
                    Map<Integer, Double> infoDtoMap =
                            Optional.ofNullable(boxedIndicatorData.getData()).map(List::stream).orElse(Stream.empty())
                            .filter(Objects::nonNull)
                            .map(boxDto -> boxDto.indicInfo)
                            .filter(infoDTO -> Objects.nonNull(infoDTO) &&
                                    Objects.nonNull(infoDTO.indicId) && Objects.nonNull(infoDTO.originalValue))
                            .collect(Collectors.toMap(indic -> indic.indicId, indic -> indic.originalValue, (a, b) -> a));
                    if(infoDtoMap.containsKey(PICK_USE_SECONDS_KEY)){
                        pickHomePageDataVO.setPickUseSeconds(BigDecimal.valueOf(infoDtoMap.get(PICK_USE_SECONDS_KEY))
                                .multiply(BigDecimal.valueOf(ONE_MINUTES))
                                .setScale(BigInteger.ZERO.intValue(), RoundingMode.HALF_UP).intValue());
                    }

                    if(infoDtoMap.containsKey(PICK_OVERTIME_RATE)){
                        pickHomePageDataVO.setPickOvertimeRate(BigDecimal.valueOf(infoDtoMap.get(PICK_OVERTIME_RATE))
                                .multiply(BigDecimal.valueOf(PERCENT_CONVERT))
                                .setScale(BigInteger.ZERO.intValue(), RoundingMode.HALF_UP).intValue());
                    }
                }
            }catch (TException e){
                log.error("PickWrapper.homepage 调用realtimeThriftService.getBoxedIndicatorData error", e);
            }
        }
        //构建返回值
        PickHomePageResponse pickHomePageResponse = new PickHomePageResponse();
        pickHomePageResponse.setPickHomePageDataVO(pickHomePageDataVO);
        pickHomePageResponse.setPickHomePageModuleVO(pickHomePageModuleVO);
        return CommonResponse.success(pickHomePageResponse);
    }

    private Long getOfflineStoreIdFromContext(IdentityInfo identityInfo) {
        List<Long> storeIdList = identityInfo.getStoreIdList();
        if (CollectionUtils.isNotEmpty(storeIdList) && storeIdList.size() == 1) {
            return storeIdList.get(0);
        } else {
            log.error("PickWrapper.homepage()不支持多门店，，storeIdList={}", storeIdList);
            throw new CommonRuntimeException("不支持多门店");
        }
    }

    public CommonResponse<PickHomePageResponse> homepageFallback() {
        log.info("PickWrapper.homepage  调用降级方法");
        throw new CommonLogicException(ResultCode.RETRY_INNER_FAIL);
    }

    //获取权限，更新PickHomePageModuleVO和moduleList，moduleList用于查询拣货tab聚合
    private PickHomePageModuleVO getPickHomePageModule(List<String> moduleList, Integer mergeTaskMode) {
        PickHomePageModuleVO pickHomePageModuleVO = new PickHomePageModuleVO();
        pickHomePageModuleVO.setShowPickStatisticsTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        pickHomePageModuleVO.setShowPickStatisticsCompassTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        pickHomePageModuleVO.setShowLackStockTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        pickHomePageModuleVO.setShowMergeTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        pickHomePageModuleVO.setShowPickTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        pickHomePageModuleVO.setShowStockUpTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        pickHomePageModuleVO.setShowPickAndOutwardSourcingTab(IntegerBooleanConstants.BOOLEAN_FALSE);

        List<String> authCodeList = Lists.newArrayList(AuthCodeEnum.FULFILL_STATISTICS.getAuthCode(), AuthCodeEnum.FULFILL_STATISTICS_COMPASS_NEW.getAuthCode(),
                AuthCodeEnum.FULFILL_LACK_STATISTICS.getAuthCode(), AuthCodeEnum.FULFILL_PREPARE_STATISTICS.getAuthCode(),
                AuthCodeEnum.FULFILL_PICK_STATISTICS.getAuthCode(), AuthCodeEnum.FULFILL_MERGE_STATISTICS.getAuthCode(), AuthCodeEnum.OUTSIDE_SETTLE.getAuthCode());
        BatchAuthCodeCheckRequest request = new BatchAuthCodeCheckRequest();
        request.setAuthCode(authCodeList);
        CommonResponse<BatchAuthCodeCheckResult> batchAuthCodeCheckResultCommonResponse = authThriftWrapper.batchAuthCodeCheck(request);

        if(batchAuthCodeCheckResultCommonResponse.getCode() != ResultCode.SUCCESS.getCode()){
            return null;
        }

        Map<String, Integer> authCodeCheckResult = batchAuthCodeCheckResultCommonResponse.getData().getAuthCodeCheckResult();
        if(IntegerBooleanConstants.BOOLEAN_TRUE.equals(getAuthWithCode(authCodeCheckResult, AuthCodeEnum.FULFILL_STATISTICS.getAuthCode()))){
            pickHomePageModuleVO.setShowPickStatisticsTab(IntegerBooleanConstants.BOOLEAN_TRUE);
        }
        if(IntegerBooleanConstants.BOOLEAN_TRUE.equals(getAuthWithCode(authCodeCheckResult, AuthCodeEnum.FULFILL_STATISTICS_COMPASS_NEW.getAuthCode()))){
            pickHomePageModuleVO.setShowPickStatisticsCompassTab(IntegerBooleanConstants.BOOLEAN_TRUE);
        }
        if(IntegerBooleanConstants.BOOLEAN_TRUE.equals(getAuthWithCode(authCodeCheckResult, AuthCodeEnum.FULFILL_LACK_STATISTICS.getAuthCode()))){
            pickHomePageModuleVO.setShowLackStockTab(IntegerBooleanConstants.BOOLEAN_TRUE);
            moduleList.add(TabCountEnum.LACK.getTabName());
        }
        if(IntegerBooleanConstants.BOOLEAN_TRUE.equals(getAuthWithCode(authCodeCheckResult, AuthCodeEnum.FULFILL_PREPARE_STATISTICS.getAuthCode()))){
            pickHomePageModuleVO.setShowStockUpTab(IntegerBooleanConstants.BOOLEAN_TRUE);
            moduleList.add(TabCountEnum.PREPARE.getTabName());
        }
        if(IntegerBooleanConstants.BOOLEAN_TRUE.equals(getAuthWithCode(authCodeCheckResult, AuthCodeEnum.FULFILL_PICK_STATISTICS.getAuthCode()))){
            pickHomePageModuleVO.setShowPickTab(IntegerBooleanConstants.BOOLEAN_TRUE);
            moduleList.add(TabCountEnum.PICK.getTabName());
        }
        //只有具有权限且业务配置为"需要合流"，才需要显示合流标签
        if(IntegerBooleanConstants.BOOLEAN_TRUE.equals(getAuthWithCode(authCodeCheckResult, AuthCodeEnum.FULFILL_MERGE_STATISTICS.getAuthCode()))
                && mergeTaskMode.equals(IntegerBooleanConstants.BOOLEAN_TRUE)){
            pickHomePageModuleVO.setShowMergeTab(IntegerBooleanConstants.BOOLEAN_TRUE);
            moduleList.add(TabCountEnum.MERGE.getTabName());
        }
        if (IntegerBooleanConstants.BOOLEAN_TRUE.equals(getAuthWithCode(authCodeCheckResult, AuthCodeEnum.OUTSIDE_SETTLE.getAuthCode()))) {
            pickHomePageModuleVO.setShowPickAndOutwardSourcingTab(IntegerBooleanConstants.BOOLEAN_TRUE);
        }

        return pickHomePageModuleVO;
    }

    private void buildPickHomePageDataVO(PickHomePageDataVO pickHomePageDataVO, Map<String, Map<String, Integer>> tabCountList, AccountStoreFulfillConfigVO configVO) {
        if (tabCountList.containsKey("lack")) {
            pickHomePageDataVO.setLackStockPendingCount(tabCountList.get("lack").getOrDefault("waitToHandle", null));
            pickHomePageDataVO.setLackStockHandledCount(tabCountList.get("lack").getOrDefault("handleDone", null));
        }
        if (tabCountList.containsKey("prepare")) {
            pickHomePageDataVO.setStockUpPendingCount(tabCountList.get("prepare").getOrDefault("waitToPrepare", null));
            pickHomePageDataVO.setStockUpToTakeCount(tabCountList.get("prepare").getOrDefault("waitToTakeAway", null));
            pickHomePageDataVO.setStockUpDoneCount(tabCountList.get("prepare").getOrDefault("prepareDone", null));
        }
        if (tabCountList.containsKey("pick")) {
            //手动接单时需要设置待接单数量
            if (OrderAcceptMode.MANUAL_ACCEPT.equals(configVO.getOrderAcceptMode())) {
                pickHomePageDataVO.setPickUnTakenCount(tabCountList.get("pick").getOrDefault("untakenOrders", null));
            }
            //需要领取时需要设置待领取数量
            if (IntegerBooleanConstants.BOOLEAN_TRUE.equals(configVO.getPickTaskReceiveMode())) {
                pickHomePageDataVO.setPickUnclaimedCount(tabCountList.get("pick").getOrDefault("waitToReceive", null));
            }
            pickHomePageDataVO.setToPickCount(tabCountList.get("pick").getOrDefault("waitToPick", null));
            pickHomePageDataVO.setPickedCount(tabCountList.get("pick").getOrDefault("pickDone", null));
        }
        //需要合流时需要设置待合流数量、已合流数量
        if (tabCountList.containsKey("merge") && IntegerBooleanConstants.BOOLEAN_TRUE.equals(configVO.getMergeTaskMode())) {
            pickHomePageDataVO.setToMergeCount(tabCountList.get("merge").getOrDefault("waitToMerge", null));
            pickHomePageDataVO.setMergedCount(tabCountList.get("merge").getOrDefault("mergeDone", null));
        }
    }

    private Integer getAuthWithCode(Map<String, Integer> authCodeCheckResult, String code){
        if(Objects.isNull(authCodeCheckResult) || StringUtils.isBlank(code)){
            return IntegerBooleanConstants.BOOLEAN_FALSE;
        }
        return authCodeCheckResult.getOrDefault(code, IntegerBooleanConstants.BOOLEAN_FALSE);
    }

    @MethodLog(logRequest = true, logResponse = true)
    public PickSelectConfigResponse queryActionAfterLackConfig() {

        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        Long offlineStoreId = getOfflineStoreIdFromContext(identityInfo);
        Store store = storeFulfillConfigWrapper.getStore(offlineStoreId);

        PickSelectConfigResponse response = new PickSelectConfigResponse();
        ActionAfterLackConfig actionAfterLackConfig = new ActionAfterLackConfig();
        OperateConfig operateConfig = store.getOperateConfig();
        if (operateConfig != null) {
            actionAfterLackConfig.setLackAutoRefund(operateConfig.getDefaultLackAutoRefund());
            actionAfterLackConfig.setLackAutoSoldOut(operateConfig.getDefaultLackAutoSoldOut());
        }
        AutoPickDoneConfig autoPickDoneConfig = store.getAutoPickDoneConfig();
        if (Objects.isNull(autoPickDoneConfig)) {
            autoPickDoneConfig = new AutoPickDoneConfig();
        }
        autoPickDoneConfig.setAutoPickDoneTime(TimeUtils.convertSecond2Minute(autoPickDoneConfig.getAutoPickDoneTime()));
        autoPickDoneConfig.setAutoPickDoneTime4Resv(TimeUtils.convertSecond2Minute(autoPickDoneConfig.getAutoPickDoneTime4Resv()));
        response.setAutoPickDoneConfig(autoPickDoneConfig);
        response.setActionAfterLackConfig(actionAfterLackConfig);
        // 门店履约展示配置信息
        ShowConfig showConfig = store.getShowConfig();
        StoreFulfillShowConfigVO showConfigVO = new StoreFulfillShowConfigVO();
        int warehouseType = store.getWarehouseType();
        if (Objects.equals(WarehouseType.SHAREABLE_WAREHOUSE.getType(), warehouseType)) {
            showConfigVO.setWaitPickShowOrderInfo(showConfig.isWaitPickShowOrderInfo() ? 1 : 0);
            showConfigVO.setPickedShowOrderInfo(showConfig.isPickedShowOrderInfo() ? 1 : 0);
        } else {
            showConfigVO.setWaitPickShowOrderInfo(0);
            showConfigVO.setPickedShowOrderInfo(0);
        }
        response.setShowConfig(showConfigVO);

        List<Integer> channelIds = tenantWrapper.queryChannelIds(store.getTenantId())
                .stream()
                .map(ChannelTypeEnumBo::getChannelId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        List<com.sankuai.meituan.reco.pickselect.ebase.thrift.ChannelAutoPickDoneConfig> autoPickDoneConfigList = store.getAutoPickDoneConfigList();
        buildAutoPickDoneConfig(autoPickDoneConfigList, channelIds, response);
        ResvAutoPickDoneConfig resvAutoPickDoneConfig = store.getResvAutoPickDoneConfig();
        if (Objects.isNull(resvAutoPickDoneConfig)) {
            resvAutoPickDoneConfig = new ResvAutoPickDoneConfig();
        }
        resvAutoPickDoneConfig.setResvAutoPickDoneTime(TimeUtils.convertSecond2Minute(resvAutoPickDoneConfig.getResvAutoPickDoneTime()));
        response.setResvAutoPickDoneConfig(resvAutoPickDoneConfig);
        List<com.sankuai.meituan.reco.pickselect.ebase.thrift.ResvChannelAutoPickDoneConfig> resvChannelAutoPickDoneConfigList = store.getResvChannelAutoPickDoneConfigList();
        buildResvAutoPickDoneConfig(resvChannelAutoPickDoneConfigList, channelIds, response);
        response.setAutoPickDoneType(store.getAutoPickDoneType());
        response.setResvAutoPickDoneType(store.getResvAutoPickDoneType());
        response.setAutoPickDoneSwitch(store.getAutoPickDoneSwitch());
        response.setResvAutoPickDoneSwitch(store.getResvAutoPickDoneSwitch());
        return response;
    }

    private void buildResvAutoPickDoneConfig(List<com.sankuai.meituan.reco.pickselect.ebase.thrift.ResvChannelAutoPickDoneConfig> autoPickDoneConfigList, List<Integer> channelIds, PickSelectConfigResponse response) {
        if (CollectionUtils.isNotEmpty(channelIds)) {
            final Map<Integer, com.sankuai.meituan.reco.pickselect.ebase.thrift.ResvChannelAutoPickDoneConfig> map = Optional.ofNullable(autoPickDoneConfigList).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(com.sankuai.meituan.reco.pickselect.ebase.thrift.ResvChannelAutoPickDoneConfig::getOrderBizType, Function.identity(), (a, b) -> a));
            //默认值
            com.sankuai.meituan.reco.pickselect.ebase.thrift.ResvChannelAutoPickDoneConfig channelAutoPickDoneConfig = new com.sankuai.meituan.reco.pickselect.ebase.thrift.ResvChannelAutoPickDoneConfig();
            channelAutoPickDoneConfig.setResvAutoPickDoneTime(channelAutoPickDoneConfig.getResvAutoPickDoneTime());

            //默认值
            com.sankuai.meituan.reco.pickselect.ebase.thrift.ResvChannelAutoPickDoneConfig defaultChanel = map.getOrDefault(DynamicOrderBizType.MEITUAN_WAIMAI.getValue(), channelAutoPickDoneConfig);
            List<ResvChannelAutoPickDoneConfig> collect = channelIds.stream().map(item -> {
                final DynamicOrderBizType dynamicOrderBizType = DynamicOrderBizType.channelId2OrderBizType(item);
                if (dynamicOrderBizType == null) {
                    return null;
                } else {
                    com.sankuai.meituan.reco.pickselect.ebase.thrift.ResvChannelAutoPickDoneConfig value = map.getOrDefault(dynamicOrderBizType.getValue(), defaultChanel);
                    ResvChannelAutoPickDoneConfig channelAutoPickDoneConfigNew = new ResvChannelAutoPickDoneConfig();
                    channelAutoPickDoneConfigNew.setChannelId(dynamicOrderBizType.getChannelId());
                    channelAutoPickDoneConfigNew.setResvAutoPickDone(value.getResvAutoPickDone());
                    channelAutoPickDoneConfigNew.setResvAutoPickDoneTime(value.getResvAutoPickDoneTime() / 60);
                    return channelAutoPickDoneConfigNew;
                }
            }).filter(Objects::nonNull).collect(Collectors.toList());
            collect.sort(Comparator.comparingInt(ResvChannelAutoPickDoneConfig::getChannelId));
            response.setResvChannelAutoPickDoneConfigList(collect);
        } else {
            response.setResvChannelAutoPickDoneConfigList(new ArrayList<>());
        }
    }

    private void buildAutoPickDoneConfig(List<com.sankuai.meituan.reco.pickselect.ebase.thrift.ChannelAutoPickDoneConfig> autoPickDoneConfigList, List<Integer> channelIds, PickSelectConfigResponse response) {
        if (CollectionUtils.isNotEmpty(channelIds)) {
            final Map<Integer, com.sankuai.meituan.reco.pickselect.ebase.thrift.ChannelAutoPickDoneConfig> map = Optional.ofNullable(autoPickDoneConfigList).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(com.sankuai.meituan.reco.pickselect.ebase.thrift.ChannelAutoPickDoneConfig::getOrderBizType, Function.identity(), (a, b) -> a));
            //默认值
            com.sankuai.meituan.reco.pickselect.ebase.thrift.ChannelAutoPickDoneConfig channelAutoPickDoneConfig = new com.sankuai.meituan.reco.pickselect.ebase.thrift.ChannelAutoPickDoneConfig();

            //默认值
            com.sankuai.meituan.reco.pickselect.ebase.thrift.ChannelAutoPickDoneConfig defaultChanel = map.getOrDefault(DynamicOrderBizType.MEITUAN_WAIMAI.getValue(), channelAutoPickDoneConfig);
            List<ChannelAutoPickDoneConfig> collect = channelIds.stream().map(item -> {
                final DynamicOrderBizType dynamicOrderBizType = DynamicOrderBizType.channelId2OrderBizType(item);
                if (dynamicOrderBizType == null) {
                    return null;
                } else {
                    com.sankuai.meituan.reco.pickselect.ebase.thrift.ChannelAutoPickDoneConfig value = map.getOrDefault(dynamicOrderBizType.getValue(), defaultChanel);
                    ChannelAutoPickDoneConfig channelAutoPickDoneConfigNew = new ChannelAutoPickDoneConfig();
                    channelAutoPickDoneConfigNew.setChannelId(dynamicOrderBizType.getChannelId());
                    channelAutoPickDoneConfigNew.setAutoPickDone(value.getAutoPickDone());
                    channelAutoPickDoneConfigNew.setAutoPickDoneTime(value.getAutoPickDoneTime() / 60);
                    channelAutoPickDoneConfigNew.setAutoPickDoneTime4Resv(value.getAutoPickDoneTime4Resv() / 60);
                    channelAutoPickDoneConfigNew.setAutoPickDone4Resv(value.getAutoPickDone4Resv());
                    channelAutoPickDoneConfigNew.setAutoPickDone4ResvSelfBooking(value.getAutoPickDone4ResvSelfBooking());

                    return channelAutoPickDoneConfigNew;
                }
            }).filter(Objects::nonNull).collect(Collectors.toList());
            collect.sort(Comparator.comparingInt(ChannelAutoPickDoneConfig::getChannelId));
            response.setChannelAutoPickDoneConfigList(collect);
        } else {
            response.setChannelAutoPickDoneConfigList(new ArrayList<>());
        }
    }

    @MethodLog(logRequest = true, logResponse = true)
    public void updateActionAfterLackConfig(ActionAfterLackConfig actionAfterLackConfig) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        long accountId = identityInfo.getUser().getAccountId();
        Long offlineStoreId = getOfflineStoreIdFromContext(identityInfo);
        if (storeFulfillConfigWrapper.checkUserBelongToStore(accountId, offlineStoreId)) {
            storeFulfillConfigWrapper.updateStoreLackOperateConfig(offlineStoreId, buildOperateConfig(actionAfterLackConfig));
        }

    }
    @MethodLog(logRequest = true, logResponse = true)
    public void updateAutoPickDoneConfig(AutoPickDoneConfig autoPickDoneConfig) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        long accountId = identityInfo.getUser().getAccountId();
        Long offlineStoreId = getOfflineStoreIdFromContext(identityInfo);
        if (storeFulfillConfigWrapper.checkUserBelongToStore(accountId, offlineStoreId)) {
            storeFulfillConfigWrapper.updateAutoPickDoneConfig(offlineStoreId,autoPickDoneConfig);
        }

    }

    private OperateConfig buildOperateConfig(ActionAfterLackConfig actionAfterLackConfig) {

        OperateConfig operateConfig = new OperateConfig();
        Integer lackAutoRefund = actionAfterLackConfig.getLackAutoRefund();
        Integer lackAutoSoldOut = actionAfterLackConfig.getLackAutoSoldOut();
        if (lackAutoRefund == null) {
            operateConfig.setDefaultLackAutoRefund(NONEED_SET_VALUE);
        } else {
            operateConfig.setDefaultLackAutoRefund(actionAfterLackConfig.getLackAutoRefund());
        }
        if (lackAutoSoldOut == null) {
            operateConfig.setDefaultLackAutoSoldOut(NONEED_SET_VALUE);
        } else {
            operateConfig.setDefaultLackAutoSoldOut(actionAfterLackConfig.getLackAutoSoldOut());
        }
        return operateConfig;
    }

    @com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog(logRequest = true, logResponse = true)
    public QuerySkusForPickAndOutwardSourcingResponseVO querySkusForPickAndOutwardSourcing(QuerySkusForPickAndOutwardSourcingRequest request) {
        QuerySkusForPickAndOutwardSourcingResponseVO querySkusForPickAndOutwardSourcingResponseVO = new QuerySkusForPickAndOutwardSourcingResponseVO();
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        try {
            List<SkuForPickAndOutwardSourcingVO> skuForPickAndOutwardSourcingVOList = new ArrayList<>();
            List<OrderForPickAndOutwardSourcingVO> orderForPickAndOutwardSourcingVOList = new ArrayList<>();
            if(MccConfigUtil.getPickQuerySwitch()){
                WaitPickWORequest waitPickWORequest = new WaitPickWORequest();
                waitPickWORequest.setEmpowerStoreId(request.getStoreId());
                waitPickWORequest.setTenantId(user.getTenantId());
                waitPickWORequest.setAccountId(user.getAccountId());
                log.info("调用pickingThriftService.openBaseQueryWaitToPickList request:{}", waitPickWORequest);
                WaitPickWOResponse waitPickWOResponse = queryPickingThriftService.openBaseQueryWaitToPickList(waitPickWORequest);
                log.info("调用pickingThriftService.openBaseQueryWaitToPickList request:{} response:{}", waitPickWORequest, waitPickWOResponse);
                if (!Status.SUCCESS.getCode().equals(waitPickWOResponse.getStatus().getCode())) {
                    log.warn("查询待拣货任务失败 waitPickWOResponse:{}", waitPickWOResponse);
                    return querySkusForPickAndOutwardSourcingResponseVO;
                }
                List<WaitPickOrderInfoDTO> pickOrderInfoDTOList = waitPickWOResponse.getDto().getOrderList();
                if (CollectionUtils.isEmpty(pickOrderInfoDTOList)) {
                    return querySkusForPickAndOutwardSourcingResponseVO;
                }
                orderForPickAndOutwardSourcingVOList = pickOrderInfoDTOList.stream().map(e -> {
                    OrderForPickAndOutwardSourcingVO vo = skuForPickAndOutwardSourcingConverter.convertOrderForPickAndOutwardSourcingVO(e);
                    vo.setDaySeqNum(DaySeqNumUtil.getDaySeqNum(e.getDaySeq(), e.getDaySeqNum()));
                    return vo;
                }).collect(Collectors.toList());
                querySkusForPickAndOutwardSourcingResponseVO.setOrders(orderForPickAndOutwardSourcingVOList);

                List<WaitPickTaskInfoDTO> waitPickTaskInfoDTOList = waitPickWOResponse.getDto().getTaskList();
                if (CollectionUtils.isEmpty(pickOrderInfoDTOList)) {
                    return querySkusForPickAndOutwardSourcingResponseVO;
                }
                skuForPickAndOutwardSourcingVOList = waitPickTaskInfoDTOList.stream().map(e -> skuForPickAndOutwardSourcingConverter.convertSkuForPickAndOutwardSourcingVO(e)).collect(Collectors.toList());
            }else {
                com.sankuai.meituan.reco.pickselect.thrift.picking.request.WaitPickWORequest waitPickWORequest = new com.sankuai.meituan.reco.pickselect.thrift.picking.request.WaitPickWORequest();
                waitPickWORequest.setEmpowerStoreId(request.getStoreId());
                waitPickWORequest.setTenantId(user.getTenantId());
                waitPickWORequest.setAccountId(user.getAccountId());
                log.info("调用pickingThriftService.openBaseQueryWaitToPickList request:{}", waitPickWORequest);
                com.sankuai.meituan.reco.pickselect.thrift.picking.response.WaitPickWOResponse waitPickWOResponse = pickingThriftService.openBaseQueryWaitToPickList(waitPickWORequest);
                log.info("调用pickingThriftService.openBaseQueryWaitToPickList request:{} response:{}", waitPickWORequest, waitPickWOResponse);
                if (!Status.SUCCESS.getCode().equals(waitPickWOResponse.getStatus().getCode())) {
                    log.warn("查询待拣货任务失败 waitPickWOResponse:{}", waitPickWOResponse);
                    return querySkusForPickAndOutwardSourcingResponseVO;
                }
                List<com.sankuai.meituan.reco.pickselect.thrift.picking.dto.WaitPickOrderInfoDTO> pickOrderInfoDTOList = waitPickWOResponse.getDto().getOrderList();
                if (CollectionUtils.isEmpty(pickOrderInfoDTOList)) {
                    return querySkusForPickAndOutwardSourcingResponseVO;
                }
                orderForPickAndOutwardSourcingVOList = pickOrderInfoDTOList.stream().map(e -> {
                    OrderForPickAndOutwardSourcingVO vo = skuForPickAndOutwardSourcingConverter.convertPickOrderForPickAndOutwardSourcingVO(e);
                    vo.setDaySeqNum(DaySeqNumUtil.getDaySeqNum(e.getDaySeq(), e.getDaySeqNum()));
                    return vo;
                }).collect(Collectors.toList());
                querySkusForPickAndOutwardSourcingResponseVO.setOrders(orderForPickAndOutwardSourcingVOList);

                List<com.sankuai.meituan.reco.pickselect.thrift.picking.dto.WaitPickTaskInfoDTO> waitPickTaskInfoDTOList = waitPickWOResponse.getDto().getTaskList();
                if (CollectionUtils.isEmpty(pickOrderInfoDTOList)) {
                    return querySkusForPickAndOutwardSourcingResponseVO;
                }
                skuForPickAndOutwardSourcingVOList = waitPickTaskInfoDTOList.stream().map(e -> skuForPickAndOutwardSourcingConverter.convertPickSkuForPickAndOutwardSourcingVO(e)).collect(Collectors.toList());
            }




            querySkusForPickAndOutwardSourcingResponseVO.setSkus(skuForPickAndOutwardSourcingVOList);

            Map<Long, List<SkuForPickAndOutwardSourcingVO>> skuMap = skuForPickAndOutwardSourcingVOList.stream().collect(Collectors.groupingBy(SkuForPickAndOutwardSourcingVO::getFulFillWorkOrderId));

            QueryValidTradeByProductRequest queryValidTradeByProductRequest = new QueryValidTradeByProductRequest();

            Set<String> skuIds = Sets.newHashSet();

            List<ViewIdCondition> viewIdConditionList = Lists.newArrayList();

            queryValidTradeByProductRequest.setTradeSkuKeyDTOList(Lists.newArrayList());
            for (OrderForPickAndOutwardSourcingVO orderForPickAndOutwardSourcingVO : orderForPickAndOutwardSourcingVOList) {
                List<SkuForPickAndOutwardSourcingVO> skuList = skuMap.get(orderForPickAndOutwardSourcingVO.getFulFillWorkOrderId());

                ViewIdCondition viewIdCondition = new ViewIdCondition();
                viewIdCondition.setOrderBizType(ChannelTypeConvertUtils.convert2OrderBizType(orderForPickAndOutwardSourcingVO.getChannelId()));
                viewIdCondition.setViewOrderId(orderForPickAndOutwardSourcingVO.getOrderId());
                viewIdConditionList.add(viewIdCondition);

                for (SkuForPickAndOutwardSourcingVO skuForPickAndOutwardSourcingVO : skuList) {
                    TradeSkuKeyDTO tradeSkuKeyDTO = new TradeSkuKeyDTO();
                    tradeSkuKeyDTO.setOrderBizType(ChannelTypeConvertUtils.convert2OrderBizType(orderForPickAndOutwardSourcingVO.getChannelId()));
                    tradeSkuKeyDTO.setViewOrderId(orderForPickAndOutwardSourcingVO.getOrderId());
                    tradeSkuKeyDTO.setSkuId(skuForPickAndOutwardSourcingVO.getSkuId());
                    tradeSkuKeyDTO.setSkuName(skuForPickAndOutwardSourcingVO.getItemName());
                    tradeSkuKeyDTO.setAttributes(skuForPickAndOutwardSourcingVO.getAttribute());
                    queryValidTradeByProductRequest.getTradeSkuKeyDTOList().add(tradeSkuKeyDTO);
                }
            }

            filterAlreadyOutwardSourcingSku(orderForPickAndOutwardSourcingVOList, skuMap, queryValidTradeByProductRequest);


            Collection<List<SkuForPickAndOutwardSourcingVO>> values = skuMap.values();
            querySkusForPickAndOutwardSourcingResponseVO.setSkus(Lists.newArrayList());
            for (List<SkuForPickAndOutwardSourcingVO> skus : values) {
                querySkusForPickAndOutwardSourcingResponseVO.getSkus().addAll(skus);
                for (SkuForPickAndOutwardSourcingVO sku : skus) {
                    if (StringUtils.isNotEmpty(sku.getSkuId())) {
                        skuIds.add(sku.getSkuId());
                    }

                }

            }

            if (skuIds.isEmpty()) {
                return querySkusForPickAndOutwardSourcingResponseVO;
            }

            // 构建查询条件
            StoreBoothSkuKeyQueryBySkuRequest storeBoothSkuKeyQueryBySkuRequest = new StoreBoothSkuKeyQueryBySkuRequest();
            storeBoothSkuKeyQueryBySkuRequest.setTenantId(user.getTenantId());
            storeBoothSkuKeyQueryBySkuRequest.setSkuIds(skuIds);
            storeBoothSkuKeyQueryBySkuRequest.setStoreId(request.getStoreId());
            StoreBoothSkuKeyBatchQueryResponse storeBoothSkuKeyBatchQueryResponse = null;

            try {
                log.info("调用storeBoothSkuThriftService.batchQueryBoothSkuKeyBySku request:{}", storeBoothSkuKeyQueryBySkuRequest);
                storeBoothSkuKeyBatchQueryResponse = storeBoothSkuThriftService.batchQueryBoothSkuKeyBySku(storeBoothSkuKeyQueryBySkuRequest);
                log.info("调用storeBoothSkuThriftService.batchQueryBoothSkuKeyBySku request:{} response:{}", storeBoothSkuKeyQueryBySkuRequest, storeBoothSkuKeyBatchQueryResponse);
            } catch (Exception e) {
                log.error("storeBoothSkuThriftService.batchQueryBoothSkuKeyBySku失败", e);
            }

            Map<String, Long> skuIdAndBoothId = Maps.newHashMap();
            storeBoothSkuKeyBatchQueryResponse.getBoothSkuList().forEach(skuBoothInfo -> {
                        skuIdAndBoothId.put(skuBoothInfo.getSkuId(), skuBoothInfo.getBoothId());
                    }
            );

            Set<Long> boothIds = Sets.newHashSet();
            boothIds.addAll(skuIdAndBoothId.values());


            log.info("调用boothThriftService.queryBoothListByBoothIds tenantId:{} boothIds:{}", user.getTenantId(), boothIds);
            BoothListResponse boothListResponse = boothThriftService.queryBoothListByBoothIds(user.getTenantId(), com.google.common.collect.Lists.newArrayList(boothIds));
            log.info("调用boothThriftService.queryBoothListByBoothIds tenantId:{} boothIds:{} response:{}", user.getTenantId(), boothIds, boothListResponse);
            Map<Long, BoothInfoDto> boothInfoDtoMap = Maps.newHashMap();
            boothListResponse.getBoothList().forEach(booth -> {
                        boothInfoDtoMap.put(booth.getBoothId(), booth);
                    }
            );


            //从订单系统中查询商品快照信息
            Map<String, OCMSOrderItemVO> ocmsSkuMap = Maps.newHashMap();
            OCMSListViewIdConditionRequest ocmsListViewIdConditionRequest = OCMSListViewIdConditionRequest.builder().viewIdConditionList(viewIdConditionList).build();
            log.info("调用ocmsQueryThriftService.queryOrderByViewIdCondition request:{}", ocmsListViewIdConditionRequest);
            OCMSListViewIdConditionResponse ocmsListViewIdConditionResponse = ocmsQueryThriftService.queryOrderByViewIdCondition(ocmsListViewIdConditionRequest);
            log.info("调用ocmsQueryThriftService.queryOrderByViewIdCondition request:{} response:{}", ocmsListViewIdConditionRequest, ocmsListViewIdConditionResponse);
            if (Integer.valueOf(StatusCodeEnum.SUCCESS.getCode()).equals(ocmsListViewIdConditionResponse.getStatus().getCode())) {
                ocmsListViewIdConditionResponse.getOcmsOrderList().sort(Comparator.comparing(OCMSOrderVO::getUpdateTime).reversed());
                ocmsListViewIdConditionResponse.getOcmsOrderList().forEach(ocmsOrderVO -> {
                    ocmsOrderVO.getOcmsOrderItemVOList().forEach(ocmsOrderItemVO -> {
                        String key = generateKey(ocmsOrderItemVO);
                        if (skuIds.contains(ocmsOrderItemVO.getInstoreSkuId2()) && !ocmsSkuMap.containsKey(key)) {
                            ocmsSkuMap.put(key, ocmsOrderItemVO);
                        }
                    });
                });
            }

            // 填充摊位信息和线下价格
            for (OrderForPickAndOutwardSourcingVO orderForPickAndOutwardSourcingVO : orderForPickAndOutwardSourcingVOList) {
                for (SkuForPickAndOutwardSourcingVO sku : skuMap.get(orderForPickAndOutwardSourcingVO.getFulFillWorkOrderId())) {
                    if (StringUtils.isNotEmpty(sku.getSkuId()) && skuIdAndBoothId.containsKey(sku.getSkuId()) && Objects.nonNull(skuIdAndBoothId.get(sku.getSkuId())) && boothInfoDtoMap.containsKey(skuIdAndBoothId.get(sku.getSkuId()))) {
                        sku.setBoothId(skuIdAndBoothId.get(sku.getSkuId()));
                        sku.setBoothName(boothInfoDtoMap.get(skuIdAndBoothId.get(sku.getSkuId())).getBoothName());
                        sku.setBoothType(boothInfoDtoMap.get(skuIdAndBoothId.get(sku.getSkuId())).getPickingType());
                        sku.setBoothSortValue(boothInfoDtoMap.get(skuIdAndBoothId.get(sku.getSkuId())).getSortValue());
                    }
                    String key = generateKey(sku);
                    if (ocmsSkuMap.containsKey(key)) {
                        sku.setOfflinePrice(ocmsSkuMap.get(key).getSingleOfflinePrice());
                    }
                }
            }

            return querySkusForPickAndOutwardSourcingResponseVO;

        } catch (Exception e) {
            log.warn("查询待拣货及现结商品列表失败", e);
        }

        return querySkusForPickAndOutwardSourcingResponseVO;
    }

    /**
     * 过滤已经外采的商品
     * @param orderForPickAndOutwardSourcingVOList
     * @param queryValidTradeByProductRequest
     */
    private void filterAlreadyOutwardSourcingSku(List<OrderForPickAndOutwardSourcingVO> orderForPickAndOutwardSourcingVOList, Map<Long, List<SkuForPickAndOutwardSourcingVO>> skuMap, QueryValidTradeByProductRequest queryValidTradeByProductRequest) {
        try {
            log.info("调用settleTradeThriftService.queryValidTradeByProduct request:{}", queryValidTradeByProductRequest);
            QueryValidTradeByProductResponse queryValidTradeByProductResponse = settleTradeThriftService.queryValidTradeByProduct(queryValidTradeByProductRequest);
            log.info("调用settleTradeThriftService.queryValidTradeByProduct request:{} response:{}", queryValidTradeByProductRequest, queryValidTradeByProductResponse);
            List<TradeInfoWithSkuKeyDTO> tradeInfoWithSkuKeyDTOList = queryValidTradeByProductResponse.getTradeInfoWithSkuKeyDTOList();

            Map<Long, OrderForPickAndOutwardSourcingVO> orderMap = orderForPickAndOutwardSourcingVOList.stream().collect(Collectors.toMap(e -> e.getFulFillWorkOrderId(), e -> e));
            Map<String, TradeInfoWithSkuKeyDTO> map = tradeInfoWithSkuKeyDTOList.stream().collect(Collectors.toMap(e -> generateKey(e), e -> e));

            for (Long fulFillWorkOrderId : skuMap.keySet()) {
                Iterator<SkuForPickAndOutwardSourcingVO> iterator = skuMap.get(fulFillWorkOrderId).iterator();
                while (iterator.hasNext()) {
                    SkuForPickAndOutwardSourcingVO skuForPickAndOutwardSourcingVO = iterator.next();
                    String key = generateKey(orderMap.get(fulFillWorkOrderId), skuForPickAndOutwardSourcingVO);
                    //该商品已经外采
                    if (map.containsKey(key) && Objects.nonNull(map.get(key).getTradeNo())) {
                        iterator.remove();
                    }
                }
            }
        } catch (Exception e) {
            log.warn("过滤已外采商品错误", e);
        }
    }


    private String generateKey(TradeInfoWithSkuKeyDTO tradeInfoWithSkuKeyDTO) {
        TradeSkuKeyDTO tradeSkuKeyDTO = tradeInfoWithSkuKeyDTO.getTradeSkuKeyDTO();
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(tradeSkuKeyDTO.getOrderBizType());
        stringBuilder.append("_");
        stringBuilder.append(tradeSkuKeyDTO.getViewOrderId());
        stringBuilder.append("_");
        stringBuilder.append(tradeSkuKeyDTO.getSkuId());
        stringBuilder.append("_");
        stringBuilder.append(tradeSkuKeyDTO.getSkuName());
        stringBuilder.append("_");
        stringBuilder.append(tradeSkuKeyDTO.getAttributes());
        return stringBuilder.toString();
    }

    private String generateKey(OrderForPickAndOutwardSourcingVO orderForPickAndOutwardSourcingVO, SkuForPickAndOutwardSourcingVO tradeInfoWithSkuKeyDTO) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(ChannelTypeConvertUtils.convert2OrderBizType(orderForPickAndOutwardSourcingVO.getChannelId()));
        stringBuilder.append("_");
        stringBuilder.append(orderForPickAndOutwardSourcingVO.getOrderId());
        stringBuilder.append("_");
        stringBuilder.append(tradeInfoWithSkuKeyDTO.getSkuId());
        stringBuilder.append("_");
        stringBuilder.append(tradeInfoWithSkuKeyDTO.getSkuName());
        stringBuilder.append("_");
        stringBuilder.append(tradeInfoWithSkuKeyDTO.getAttribute());
        return stringBuilder.toString();
    }

    private String generateKey(SkuForPickAndOutwardSourcingVO tradeInfoWithSkuKeyDTO) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(tradeInfoWithSkuKeyDTO.getSkuId());
        stringBuilder.append("_");
        stringBuilder.append(tradeInfoWithSkuKeyDTO.getSkuName());
        stringBuilder.append("_");
        stringBuilder.append(tradeInfoWithSkuKeyDTO.getAttribute());
        return stringBuilder.toString();
    }

    private String generateKey(OCMSOrderItemVO ocmsOrderItemVO) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(ocmsOrderItemVO.getInstoreSkuId2());
        stringBuilder.append("_");
        stringBuilder.append(ocmsOrderItemVO.getSkuName());
        stringBuilder.append("_");
        stringBuilder.append(joinAttributesToString(ocmsOrderItemVO.getTagInfos()));
        return stringBuilder.toString();
    }

    /**
     * 获取商品属性
     *
     * @param tagInfos
     * @return
     */
    public static String joinAttributesToString(List<OrderItemTagVO> tagInfos) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(tagInfos)) {
            return StringUtils.EMPTY;
        }
        List<String> propertyList = tagInfos.stream().filter(orderItemTagVO -> orderItemTagVO.getType() != null && ItemTagEnum.ATTR_TAG.getValue() == orderItemTagVO.getType()).map(orderItemTagVO -> orderItemTagVO.getName()).collect(Collectors.toList());
        Collections.sort(propertyList);
        return Joiner.on("+").join(propertyList);
    }


    @MethodLog(logRequest = true, logResponse = true)
    public void updateAutoPickDoneConfigInfos(PickSelectConfigUpdateReq req) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        long accountId = identityInfo.getUser().getAccountId();
        Long offlineStoreId = getOfflineStoreIdFromContext(identityInfo);
        AutoPickDoneConfigInfo autoPickDoneConfigInfo = new AutoPickDoneConfigInfo();
        autoPickDoneConfigInfo.setAutoPickDoneType(Objects.isNull(req.getAutoPickDoneType()) ? 0 : req.getAutoPickDoneType());
        autoPickDoneConfigInfo.setResvAutoPickDoneType(Objects.isNull(req.getResvAutoPickDoneType()) ? 0 : req.getResvAutoPickDoneType());
        autoPickDoneConfigInfo.setAutoPickDoneSwitch(Objects.isNull(req.getAutoPickDoneSwitch()) ? 1 : req.getAutoPickDoneSwitch());
        autoPickDoneConfigInfo.setResvAutoPickDoneSwitch(Objects.isNull(req.getResvAutoPickDoneSwitch()) ? 1 : req.getResvAutoPickDoneSwitch());

        if (CollectionUtils.isNotEmpty(req.getChannelAutoPickDoneConfigList()) ) {

            final List<com.sankuai.meituan.reco.pickselect.ebase.thrift.ChannelAutoPickDoneConfig> collect = req.getChannelAutoPickDoneConfigList().stream().map(item -> {
                final DynamicOrderBizType dynamicOrderBizType = DynamicOrderBizType.channelId2OrderBizType(item.getChannelId());
                if (dynamicOrderBizType == null) {
                    return null;
                } else {
                    com.sankuai.meituan.reco.pickselect.ebase.thrift.ChannelAutoPickDoneConfig autoPickDoneConfig = new com.sankuai.meituan.reco.pickselect.ebase.thrift.ChannelAutoPickDoneConfig();
                    autoPickDoneConfig.setOrderBizType(dynamicOrderBizType.getValue());
                    autoPickDoneConfig.setAutoPickDone(item.getAutoPickDone());
                    autoPickDoneConfig.setAutoPickDoneTime(item.getAutoPickDoneTime());
                    autoPickDoneConfig.setAutoPickDone4Resv(item.getAutoPickDone4Resv());
                    autoPickDoneConfig.setAutoPickDoneTime4Resv(item.getAutoPickDoneTime4Resv());
                    autoPickDoneConfig.setAutoPickDone4ResvSelfBooking(item.getAutoPickDone4ResvSelfBooking());
                    return autoPickDoneConfig;
                }
            }).filter(Objects::nonNull).collect(Collectors.toList());
            collect.sort(Comparator.comparingInt(com.sankuai.meituan.reco.pickselect.ebase.thrift.ChannelAutoPickDoneConfig::getOrderBizType));
            autoPickDoneConfigInfo.setChannelAutoPickDoneConfigList(collect);
        }

        if (Objects.nonNull(req.getResvAutoPickDoneConfig())){
            ResvAutoPickDoneConfig resvAutoPickDoneConfig = new ResvAutoPickDoneConfig();
            resvAutoPickDoneConfig.setResvAutoPickDone(req.getResvAutoPickDoneConfig().getResvAutoPickDone());
            resvAutoPickDoneConfig.setResvAutoPickDoneTime(req.getResvAutoPickDoneConfig().getResvAutoPickDoneTime());
            autoPickDoneConfigInfo.setResvAutoPickDoneConfig(resvAutoPickDoneConfig);
        }

        if (CollectionUtils.isNotEmpty(req.getResvChannelAutoPickDoneConfigList())) {

            final List<com.sankuai.meituan.reco.pickselect.ebase.thrift.ResvChannelAutoPickDoneConfig> collect = req.getResvChannelAutoPickDoneConfigList().stream().map(item -> {
                final DynamicOrderBizType dynamicOrderBizType = DynamicOrderBizType.channelId2OrderBizType(item.getChannelId());
                if (dynamicOrderBizType == null) {
                    return null;
                } else {
                    com.sankuai.meituan.reco.pickselect.ebase.thrift.ResvChannelAutoPickDoneConfig resvChannelAutoPickDoneConfig = new com.sankuai.meituan.reco.pickselect.ebase.thrift.ResvChannelAutoPickDoneConfig();
                    resvChannelAutoPickDoneConfig.setOrderBizType(dynamicOrderBizType.getValue());
                    resvChannelAutoPickDoneConfig.setResvAutoPickDone(item.getResvAutoPickDone());
                    resvChannelAutoPickDoneConfig.setResvAutoPickDoneTime(item.getResvAutoPickDoneTime());
                    return resvChannelAutoPickDoneConfig;
                }
            }).filter(Objects::nonNull).collect(Collectors.toList());
            collect.sort(Comparator.comparingInt(com.sankuai.meituan.reco.pickselect.ebase.thrift.ResvChannelAutoPickDoneConfig::getOrderBizType));
            autoPickDoneConfigInfo.setResvChannelAutoPickDoneConfigList(collect);
        }
        if (storeFulfillConfigWrapper.checkUserBelongToStore(accountId, offlineStoreId)) {
            storeFulfillConfigWrapper.updateAutoPickDoneConfigInfos(offlineStoreId, autoPickDoneConfigInfo);
        }
    }
}
