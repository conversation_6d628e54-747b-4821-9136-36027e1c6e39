package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

@TypeDoc(
        description = "门店信息数据"
)
@Data
@ApiModel("门店信息数据")
@AllArgsConstructor
@NoArgsConstructor
public class WarehousePoiInfoVO {

    @FieldDoc(description = "门店ID，对应中台中心仓门店ID，对应履约系统内部是offlineStoreId")
    @ApiModelProperty(value = "门店ID，对应中台中心仓门店ID，对应履约系统内部是offlineStoreId")
    public long storeId;

    @FieldDoc(description = "门店名称")
    @ApiModelProperty(value = "门店名称")
    public String storeName;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        WarehousePoiInfoVO that = (WarehousePoiInfoVO) o;
        return storeId == that.storeId && Objects.equals(storeName, that.storeName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(storeId, storeName);
    }
}
