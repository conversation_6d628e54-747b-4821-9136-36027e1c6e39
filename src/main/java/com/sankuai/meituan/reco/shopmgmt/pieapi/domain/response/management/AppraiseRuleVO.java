package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.management;
// Copyright (C) 2020 Meituan
// All rights reserved

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.resource.management.common.enums.ResultTypeEnum;
import com.meituan.shangou.saas.resource.management.dto.appraise.AppraiseRulePreviewDTO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@TypeDoc(
        description = "考核规则对象"
)
@Data
public class AppraiseRuleVO {

    @FieldDoc(
            description = "规则id",
            example = {}
    )
    private Long ruleId;

    @FieldDoc(
            description = "规则名称",
            example = {}
    )
    private String ruleName;

    @FieldDoc(
            description = "跳转地址, 如果无法跳转/无权限, 则无",
            example = {}
    )
    private String jumpUrl;

    @FieldDoc(
            description = "跳转文案",
            example = {}
    )
    private String jumpDisplayName;

    @FieldDoc(
            description = "规则达标状态",
            example = {}
    )
    private Integer rulePassed = -1;

    @FieldDoc(
            description = "值类型, 1.数值, 2.百分比, 3.boolean",
            example = {}
    )
    private Integer valueType;

    @FieldDoc(
            description = "单位, valueType为数值时 可能有, 元, 单",
            example = {}
    )
    private String valueUnit;

    @FieldDoc(
            description = "门店当前值",
            example = {}
    )
    private String poiCurrentValue;

    @FieldDoc(
            description = "指标ID"
    )
    private String indicId;

    @FieldDoc(
            description = "指标说明"
    )
    private String indicDesc;


    private String indicStatisticalDimDesc;


    @FieldDoc(
            description = "条件列表",
            example = {}
    )
    private List<AppraiseConditionVO> conditions;


    public AppraiseRuleVO(AppraiseRulePreviewDTO rulePreview, List<String> authCodes) {
        this.ruleId = rulePreview.getRuleId();
        this.ruleName = rulePreview.getRuleName();
        if(CollectionUtils.isNotEmpty(authCodes)){
            if (CollectionUtils.isNotEmpty(rulePreview.getAuthCodes())
                    && CollectionUtils.containsAny(authCodes, rulePreview.getAuthCodes())) {
                this.jumpDisplayName = rulePreview.getJumpDisplayName();
                this.jumpUrl = rulePreview.getJumpUrl();
            }
        }
        if (rulePreview.isRulePassed() != null) {
            this.rulePassed = rulePreview.isRulePassed() ? 1 : 0;
        }
        this.valueType = ConverterUtils.nonNullConvert(rulePreview.getValueType(), ResultTypeEnum::getType);
        this.valueUnit = rulePreview.getValueUnit();
        this.poiCurrentValue = rulePreview.getPoiCurrentValue();
        this.indicId = rulePreview.getIndicId();
        this.indicDesc = rulePreview.getIndicDesc();
        this.indicStatisticalDimDesc = rulePreview.getDimDesc();
        this.conditions = rulePreview.getConditionList().stream().map(AppraiseConditionVO::new).collect(Collectors.toList());
    }
}
