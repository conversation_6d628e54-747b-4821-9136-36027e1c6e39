package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.BoothSimpleDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.spu.BoothSimpleDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Title: BoothVO
 * @Description: 摊位信息
 * @Author: zhaolei12
 * @Date: 2020/5/16 5:40 下午
 */
@TypeDoc(
        description = "摊位信息"
)
@Data
@ApiModel("摊位信息")
public class BoothVO {

    @FieldDoc(
            description = "摊位编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "摊位编码")
    private Long boothId;

    @FieldDoc(
            description = "摊位名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "摊位名称")
    private String boothName;

    public static BoothVO ofDTO(BoothSimpleDTO boothSimpleDTO) {
        if (boothSimpleDTO == null) {
            return null;
        }
        BoothVO boothVO = new BoothVO();
        boothVO.setBoothId(boothSimpleDTO.getBoothId());
        boothVO.setBoothName(boothSimpleDTO.getBoothName());
        return boothVO;
    }

    public static BoothVO ofDTO(BoothSimpleDto boothSimpleDto) {
        if (boothSimpleDto == null) {
            return null;
        }
        BoothVO boothVO = new BoothVO();
        boothVO.setBoothId(boothSimpleDto.getBoothId());
        boothVO.setBoothName(boothSimpleDto.getBoothName());
        return boothVO;
    }

    public static BoothSimpleDTO toDTO(BoothVO boothVO) {
        if (boothVO == null) {
            return null;
        }
        BoothSimpleDTO boothSimpleDTO = new BoothSimpleDTO();
        boothSimpleDTO.setBoothId(boothVO.getBoothId());
        boothSimpleDTO.setBoothName(boothVO.getBoothName());
        return boothSimpleDTO;
    }

    public static BoothSimpleDTO toDTO(Long boothId) {
        if (boothId == null) {
            return null;
        }
        BoothSimpleDTO boothSimpleDTO = new BoothSimpleDTO();
        boothSimpleDTO.setBoothId(boothId);
        return boothSimpleDTO;
    }

    public static BoothSimpleDto toBizBoothDto(Long boothId) {
        if (boothId == null) {
            return null;
        }
        BoothSimpleDto boothSimpleDto = new BoothSimpleDto();
        boothSimpleDto.setBoothId(boothId);
        return boothSimpleDto;
    }


}
