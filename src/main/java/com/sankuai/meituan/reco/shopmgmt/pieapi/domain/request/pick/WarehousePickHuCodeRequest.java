package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * @description:
 * @Auther: wb_nifei
 * @Date: 2023/8/18 16:44
 */
@TypeDoc(
        description = "Hu解绑定请求"
)
@ApiModel("Hu解绑定请求")
@Data
public class WarehousePickHuCodeRequest {
    @FieldDoc(
            description = "波次号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "波次号")
    private String waveId;

    @FieldDoc(
            description = "波次任务号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "波次任务号")
    private Integer taskId;

    @FieldDoc(
            description = "箱码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "箱码")
    private String huCode;
    @FieldDoc(
            description = "操作类型:0 新增,  1删除", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "操作类型:0 新增,  1删除")
    private Integer operateType;
}
