package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.meituan.shangou.saas.tenant.thrift.DepartmentThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.StatusCodeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.department.response.DepartmentSpreadResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.auth.DepartmentSpreadVo;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2022/12/20
 */
@Service
@Slf4j
public class DepartmentWrapper {
    @Resource
    DepartmentThriftService departmentThriftService;

    /**
     * 查询部门展开信息
     *
     * @param departmentId    部门ID
     * @param tenantId        租户ID
     * @param hasEmployeeList 是否返回员工列表
     * @return 单个部门展开对象
     */
    public DepartmentSpreadVo queryDepartmentSpread(Long departmentId, Long tenantId, Integer hasEmployeeList) {
        DepartmentSpreadResponse response = departmentThriftService.queryDepartmentSpreadInfo(departmentId, tenantId, hasEmployeeList);
        if (response.status.getCode() != StatusCodeEnum.SUCCESS.getCode()) {
            throw new BizException(response.status.getCode(), response.status.getMessage());
        }

        return new DepartmentSpreadVo().build(response);
    }

}
