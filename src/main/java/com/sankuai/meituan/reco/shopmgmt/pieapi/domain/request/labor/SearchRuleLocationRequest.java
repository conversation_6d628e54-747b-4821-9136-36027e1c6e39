package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor;

import com.facebook.swift.codec.ThriftField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022-10-18
 * @email <EMAIL>
 */
@TypeDoc(
        description = "新建/编辑班次请求"
)
@ApiModel("新建/编辑班次请求")
@Data
public class SearchRuleLocationRequest {

    @FieldDoc(
            description = "门店名，模糊搜索",
            requiredness = Requiredness.REQUIRED
    )
    @ThriftField(1)
    public String storeName;

    @FieldDoc(
            description = "页码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "页码")
    private Integer pageNo;

    @FieldDoc(
            description = "页大小", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "页大小")
    private Integer pageSize;

}
