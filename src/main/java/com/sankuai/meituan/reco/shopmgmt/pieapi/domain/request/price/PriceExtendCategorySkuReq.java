package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.price;
// Copyright (C) 2020 Meituan
// All rights reserved

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: <EMAIL>
 * @class: MessageTaskQueryRequest
 * @date: 2020-03-13 18:08:46
 * @desc:
 */
@Data
@ApiModel(
        "品类商品价格扩展查询"
)
@TypeDoc(
        description = "品类商品价格扩展查询"
)
public class PriceExtendCategorySkuReq {

    @FieldDoc(
            description = "门店id"
    )
    @ApiModelProperty("门店Id")
    private Long storeId;


    @FieldDoc(
            description = "一级类目品类Id"
    )
    @ApiModelProperty("一级类目品类Id")
    private String firstLevelCategoryId;


    @FieldDoc(
            description = "是否需要品类数据"
    )
    @ApiModelProperty("是否需要品类数据")
    private boolean needCategoryPriceExtend;

    @FieldDoc(
            description = "1-价格指数，2-溢价率"
    )
    @ApiModelProperty("类型")
    private Integer type;


    @FieldDoc(
            description = "请求分页商品数量"
    )
    @ApiModelProperty("请求分页商品数量")
    private Integer pageSize;

    @FieldDoc(
            description = "当前页码"
    )
    @ApiModelProperty("当前页码")
    private Integer pageNum;

    @FieldDoc(
            description = "标签筛选"
    )
    @ApiModelProperty("当前页码")
    private Boolean filterCore;

    @FieldDoc(
            description = "筛选出常规品"
    )
    @ApiModelProperty("筛选出常规品")
    private Boolean filterCommon;

    public void validate() {

        if (this.type == null) {
            throw new CommonLogicException("类型不能为空", ResultCode.CHECK_PARAM_ERR);
        }

        if (this.storeId == null) {
            throw new CommonLogicException("门店id不能为空", ResultCode.CHECK_PARAM_ERR);
        }

        if (this.pageNum == null) {
            throw new CommonLogicException("页码不能为空", ResultCode.CHECK_PARAM_ERR);
        }

        if (this.pageSize == null) {
            throw new CommonLogicException("分页大小不能为空", ResultCode.CHECK_PARAM_ERR);
        }
    }
}
