package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@TypeDoc(
        description = "查询待分拣列表请求"
)
@ApiModel("查询待分拣列表请求")
@Data
public class WarehouseSeedWaitQueryRequest {

    @FieldDoc(
            description = "页码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "页码")
    private Integer pageNo;

    @FieldDoc(
            description = "个数", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "个数")
    private Integer pageSize;
}
