package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.management;
// Copyright (C) 2020 Meituan
// All rights reserved

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.resource.management.dto.appraise.AppraisePreviewDTO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

@TypeDoc(
        description = "商家体检返回值"
)
@Data
public class MerchantExaminationResponse {


    @FieldDoc(
            description = "门店当前开始天数",
            example = {}
    )
    private Integer storeOpenedDays;


    @FieldDoc(
            description = "考核列表",
            example = {}
    )
    private List<AppraisePlanVO> appraiseList;


    @FieldDoc(
            description = "考核政策说明URL",
            example = {}
    )
    private String planDescriptionUrl;


    public MerchantExaminationResponse(AppraisePreviewDTO previewDTO, List<String> authCodes) {
        this.storeOpenedDays = previewDTO.getOpenedDays();
        this.appraiseList = previewDTO.getPlanPreviewList().stream().map(planPreview -> new AppraisePlanVO(planPreview, authCodes)).collect(Collectors.toList());
        this.planDescriptionUrl = MccConfigUtil.getPlanIntroducePictureUrl();
    }
}
