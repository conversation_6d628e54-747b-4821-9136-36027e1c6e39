package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.product;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024-12-26
 */
public interface IProductContainer {
    Map<String, Object> dataMap();

    default Object get(String field) {
        return dataMap().get(field);
    }

    default void put(String field, Object value) {
        if (value == null) {
            dataMap().remove(field);
            return;
        }
        dataMap().put(field, value);
    }
}
