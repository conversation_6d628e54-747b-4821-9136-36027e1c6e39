package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/7/12
 * desc: 退款理由
 */
@TypeDoc(
        description = "退款理由"
)
@ApiModel("退款理由")
@Data
public class RefundReasonAndCodeVO {

    @FieldDoc(
            description = "退款理由Code", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "退款理由Code", required = true)
    private Integer code;

    @FieldDoc(
            description = "退款理由文案", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "退款理由文案", required = true)
    private String reason;
}
