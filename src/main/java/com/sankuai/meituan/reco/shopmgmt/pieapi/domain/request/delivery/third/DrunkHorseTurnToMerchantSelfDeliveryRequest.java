package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery.third;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2023/8/31 17:08
 **/
@Data
public class DrunkHorseTurnToMerchantSelfDeliveryRequest {
    @FieldDoc(
            description = "赋能订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "赋能订单号", required = true)
    @NotNull
    private Long orderId;

    @FieldDoc(
            description = "转单后的骑手id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "转单后的骑手id", required = true)
    @NotNull
    private Long riderAccountId;
}
