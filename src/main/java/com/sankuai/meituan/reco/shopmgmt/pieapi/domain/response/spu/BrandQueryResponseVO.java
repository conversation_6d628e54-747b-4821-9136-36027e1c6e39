package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.SpuCompareRecordDetailInfoDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2023/2/16 1:05 下午
 **/
@TypeDoc(
        description = "渠道品牌查询结果"
)
@Data
public class BrandQueryResponseVO {

    @FieldDoc(
            description = "渠道品牌列表"
    )
    private List<ChannelBrandDomainVO> brandList;


}
