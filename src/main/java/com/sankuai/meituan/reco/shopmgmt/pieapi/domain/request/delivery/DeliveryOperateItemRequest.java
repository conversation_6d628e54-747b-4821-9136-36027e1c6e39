package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@TypeDoc(
        description = "配送可操作按钮"
)
@ApiModel("配送可操作按钮")
@Data
public class DeliveryOperateItemRequest {

    @FieldDoc(
            description = "百川订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "百川订单号", required = true)
    private Long empowerOrderId;

}
