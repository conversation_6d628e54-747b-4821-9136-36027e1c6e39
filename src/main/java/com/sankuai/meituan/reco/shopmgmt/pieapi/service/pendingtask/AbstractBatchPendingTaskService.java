/*
 * Copyright (c) 2019 Meituan.com. All Rights Reserved.
 */
package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask;

import com.google.common.collect.Maps;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;

import java.util.Map;
import java.util.Set;


/**
 * 抽象批量未处理任务service
 * <br><br>
 * Author: linjianyu <br>
 * Date: 2019-06-03 Time: 10:35
 */
public abstract class AbstractBatchPendingTaskService implements InitializingBean {

    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());
    static final Map<AuthCodeEnum, AbstractBatchPendingTaskService> PENDING_TASK_SERVICE_MAP = Maps.newHashMap();

    final Map<AuthCodeEnum, PendingTaskResult> pendingTaskCount(PendingTaskParam param) {
        try {
            Map<AuthCodeEnum, PendingTaskResult> map = getPendingTaskCountMap(param);
            return map == null ? Maps.newHashMap() : map;
        } catch (Exception e) {
            LOGGER.error("get pending task count error", e);
            return Maps.newHashMap();
        }
    }

    protected abstract Map<AuthCodeEnum, PendingTaskResult> getPendingTaskCountMap(PendingTaskParam param) throws Exception;

    protected abstract Set<AuthCodeEnum> authCodeEnumSet();

    @Override
    public void afterPropertiesSet() {
        for (AuthCodeEnum authCodeEnum : authCodeEnumSet()) {
            if (PENDING_TASK_SERVICE_MAP.get(authCodeEnum) != null) {
                throw new CommonRuntimeException("包含重复的模块角标处理器, 位于: "
                        + PENDING_TASK_SERVICE_MAP.get(authCodeEnum).getClass().getSimpleName() + ", " + this.getClass().getSimpleName());
            }
            PENDING_TASK_SERVICE_MAP.put(authCodeEnum, this);
        }
    }
}
