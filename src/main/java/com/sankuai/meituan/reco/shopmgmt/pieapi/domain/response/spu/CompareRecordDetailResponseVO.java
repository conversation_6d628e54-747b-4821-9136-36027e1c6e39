package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.SpuCompareRecordDetailInfoDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2022/3/1 1:05 下午
 **/
@TypeDoc(
        description = "不一致商品"
)
@Data
public class CompareRecordDetailResponseVO {

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    private Long storeId;

    @FieldDoc(
            description = "spu编码", requiredness = Requiredness.REQUIRED
    )
    private String spuId;

    @FieldDoc(
            description = "渠道spu编码", requiredness = Requiredness.REQUIRED
    )
    private String customSpuId;

    @FieldDoc(description = "商品名称")
    private String spuName;

    @FieldDoc(
            description = "商品不一致信息详情", requiredness = Requiredness.REQUIRED
    )
    private List<CompareRecordInfoVO> compareRecordInfoVOS;

    @FieldDoc(
            description = "商品图片", requiredness = Requiredness.REQUIRED
    )
    private String imageUrl;

    @FieldDoc(
            description = "支持的操作列表(1从商家端删除;2从蔬果派删除;3向商家端新增;4向蔬果派新增;5跳转编辑;6信息与商家端一致;7信息与蔬果派一致;8改价/报价;9价格与商家端一致;10价格与蔬果派一致)"
    )
    private List<Integer> optList;

    public static CompareRecordDetailResponseVO of(SpuCompareRecordDetailInfoDTO compareSpuDetailDTO) {
        CompareRecordDetailResponseVO compareRecordDetailResponseVO = new CompareRecordDetailResponseVO();

        compareRecordDetailResponseVO.setStoreId(compareSpuDetailDTO.getStoreId());
        compareRecordDetailResponseVO.setSpuId(compareSpuDetailDTO.getSpuId());
        compareRecordDetailResponseVO.setSpuName(compareSpuDetailDTO.getSpuName());
        compareRecordDetailResponseVO.setCustomSpuId(compareSpuDetailDTO.getCustomSpuId());
        compareRecordDetailResponseVO.setCompareRecordInfoVOS(
                ConverterUtils.convertList(compareSpuDetailDTO.getSpuCompareRecordFieldDTOS(), CompareRecordInfoVO::of));
        compareRecordDetailResponseVO.setOptList(compareSpuDetailDTO.getOptList());
        compareRecordDetailResponseVO.setImageUrl(compareSpuDetailDTO.getImageUrl());
        return compareRecordDetailResponseVO;
    }
}
