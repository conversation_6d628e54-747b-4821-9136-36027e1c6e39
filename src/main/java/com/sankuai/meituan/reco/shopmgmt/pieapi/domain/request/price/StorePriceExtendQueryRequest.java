package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: wangyihao04
 * @Date: 2020-06-15 11:49
 * @Mail: <EMAIL>
 */

@Data
@ApiModel(
        "门店品类价格扩展值查询接口"
)
@TypeDoc(
        description = "门店品类价格扩展值查询接口"
)
public class StorePriceExtendQueryRequest {

    @FieldDoc(
            description = "门店id"
    )
    @ApiModelProperty("门店Id")
    private Long storeId;

    @FieldDoc(
            description = "1-价格指数，2-溢价率"
    )
    @ApiModelProperty("类型")
    private Integer type;

    @FieldDoc(
            description = "核心品过滤"
    )
    @ApiModelProperty("核心品过滤")
    private Boolean filterCore;

    @FieldDoc(
            description = "常规品过滤"
    )
    @ApiModelProperty("常规品过滤")
    private Boolean filterCommon;

    public void validate() {
        if (this.storeId == null) {
            throw new CommonLogicException("门店id不能为空", ResultCode.CHECK_PARAM_ERR);
        }

        if (this.type == null) {
            throw new CommonLogicException("类型不能为空", ResultCode.CHECK_PARAM_ERR);
        }
    }
}
