package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.quality;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.product.dto.QualityDetailDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/9/20
 */
@TypeDoc(
        name = "质量分详情",
        description = "质量分详情"
)
@Data
public class FieldsDetailVo {

    @FieldDoc(
            description = "商品唯一标识"
    )
    @ApiModelProperty(name = "规格添加的顺序")
    private String uniqueFlag;

    @FieldDoc(
            description = "字段当前的值"
    )
    @ApiModelProperty(name = "规格添加的顺序")
    private Object fieldsValue;

    @FieldDoc(
            description = "字段权益列表"
    )
    @ApiModelProperty(name = "规格添加的顺序")
    private List<RuleInterestInfoVo> ruleInterestInfoList;

    @FieldDoc(
            description = "字段存在的问题列表"
    )
    @ApiModelProperty(name = "规格添加的顺序")
    private List<FieldsProblemInfoVo> problemDescList;

    public static FieldsDetailVo of(QualityDetailDto dto) {
        FieldsDetailVo vo = new FieldsDetailVo();
        vo.setFieldsValue(dto.getFieldsValue());
        vo.setUniqueFlag(dto.getUniqueFlag());
        vo.setRuleInterestInfoList(Fun.map(dto.getRuleInterestInfoList(), RuleInterestInfoVo::of));
        vo.setProblemDescList(Fun.map(dto.getProblemInfoList(), FieldsProblemInfoVo::of));

        return vo;
    }
}
