package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.securitydeposit;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * @Author: <EMAIL>
 * @Date: 2020/3/2 21:28
 * @Description:
 */
@TypeDoc(
        description = "StoreId请求"
)
@Data
public class StoreIdRequest {


    @FieldDoc(
            description = "门店ID"
    )
    private Long storeId;

    @FieldDoc(
            description = "本次缴纳的金额, 单位（分）"
    )
    private Long amount;


}
