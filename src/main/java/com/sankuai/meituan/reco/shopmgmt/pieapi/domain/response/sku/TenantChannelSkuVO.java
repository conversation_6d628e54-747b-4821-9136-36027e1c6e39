package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SaleAttrValueInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.TenantChannelSkuDTO;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;


/**
 * @Author: luokai14
 * @Date: 2022/9/13 8:11 下午
 * @Mail: <EMAIL>
 */
@Data
public class TenantChannelSkuVO {
    private Long tenantId;

    private Integer channelId;

    private String spuId;

    private String skuId;

    private String spec;
    /**
     * 渠道SKU编码
     */
    private String channelSkuId;

    private String customSkuId;

    private Double suggestPrice;

    private Integer weight;

    private String upc;

    /**
     * 京东规格属性值列表
     */
    private List<SaleAttrValueInfoVO> jdSaleAttrValueList;

    private Integer valid;

    public static TenantChannelSkuVO convertFromDto(TenantChannelSkuDTO tenantChannelSkuDTO) {
        TenantChannelSkuVO tenantChannelSkuVO = new TenantChannelSkuVO();

        tenantChannelSkuVO.setTenantId(tenantChannelSkuDTO.getTenantId());
        tenantChannelSkuVO.setChannelId(tenantChannelSkuDTO.getChannelId());
        tenantChannelSkuVO.setSpuId(tenantChannelSkuDTO.getSpuId());
        tenantChannelSkuVO.setSkuId(tenantChannelSkuDTO.getSkuId());
        tenantChannelSkuVO.setChannelSkuId(tenantChannelSkuDTO.getChannelSkuId());
        tenantChannelSkuVO.setCustomSkuId(tenantChannelSkuDTO.getCustomSkuId());
        tenantChannelSkuVO.setSpec(tenantChannelSkuDTO.getSpec());
        tenantChannelSkuVO.setValid(tenantChannelSkuDTO.getValid());

        tenantChannelSkuVO.setSuggestPrice(tenantChannelSkuDTO.getSuggestPrice());
        tenantChannelSkuVO.setUpc(tenantChannelSkuDTO.getUpc());
        tenantChannelSkuVO.setWeight(tenantChannelSkuDTO.getWeight());
        if (CollectionUtils.isNotEmpty(tenantChannelSkuDTO.getJdSaleAttrValueList())) {
            tenantChannelSkuVO.setJdSaleAttrValueList(ConverterUtils.convertList(tenantChannelSkuDTO.getJdSaleAttrValueList(), SaleAttrValueInfoVO::ofDTO));
        }

        return tenantChannelSkuVO;
    }
}
