package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.quality;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.product.request.SpuRecommendWeightRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/9/20
 */
@TypeDoc(
        name = "推荐重量请求",
        description = "推荐重量请求"
)
@Data
public class RecommendWeightRequest {

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店id")
    private String storeId;

    @FieldDoc(
            description = "渠道id，当前仅支持美团渠道", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道id，当前仅支持美团渠道", required = true)
    private int channelId;

    @FieldDoc(
            description = "渠道类目id", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道类目id")
    private String channelCategoryId;

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称")
    private String name;

    @FieldDoc(
            description = "UPC信息，若存在多个，则传入第一个", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "UPC信息，若存在多个，则传入第一个")
    private String upc;

    @FieldDoc(
            description = "兼容前端逻辑，主档时无法做到storeId不传值", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "兼容前端逻辑，主档时无法做到storeId不传值")
    private Boolean isMerchantSpu;

    public SpuRecommendWeightRequest convertRpcRequest() {
        SpuRecommendWeightRequest req = new SpuRecommendWeightRequest();
        req.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        if (BooleanUtils.isFalse(isMerchantSpu) && StringUtils.isNotBlank(storeId)) {
            req.setStoreId(Long.valueOf(storeId));
        }
        req.setChannelId(channelId);
        req.setName(name);
        req.setUpcCode(upc);
        req.setChannelCategoryId(channelCategoryId);

        return req;
    }
}
