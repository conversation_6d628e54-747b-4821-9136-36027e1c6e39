package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: wangyihao04
 * @Date: 2020-06-11 17:07
 * @Mail: <EMAIL>
 */
@Data
@TypeDoc(
        description = "价格扩展值对象"
)
@ApiModel("价格扩展值对象")
public class PriceExtendVO {
    @FieldDoc(
            description = "表示门店的价格扩展值为门店ID,表示品类的价格扩展值为品类ID"
    )
    @ApiModelProperty("表示门店的价格扩展值为门店ID,表示品类的价格扩展值为品类ID")
    private String uniqueId;
    @FieldDoc(
            description = "表示门店时不存在，品类时为品类id"
    )
    @ApiModelProperty("表示门店时不存在，品类时为品类id")
    private String name;
    @FieldDoc(
            description = "价格扩展值信息"
    )
    @ApiModelProperty("价格扩展值信息")
    private Double priceExtend;
    @FieldDoc(
            description = "判断价格扩展值是否过高的参考值"
    )
    @ApiModelProperty("判断价格扩展值是否过高的参考值")
    private Double threshold;
    @FieldDoc(
            description = "价格扩展值的展示值"
    )
    @ApiModelProperty("价格扩展值的展示值")
    private String displayInfo;
}
