package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description 待配送订单子类型数量响应
 * @ClassName WaitToDeliverySubTypeCountResponse
 * <AUTHOR>
 * @Version 1.0
 * @Date 2022/1/10 8:07 下午
 */
@TypeDoc(
        description = "待配送订单子类型数量响应"
)
@Data
@ApiModel("待配送订单子类型数量响应")
public class WaitToDeliverySubTypeCountResponse {

    @FieldDoc(
            description = "全部数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "全部数量", required = true)
    private Integer allSubTypeCount;

    @FieldDoc(
            description = "待发单数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "待发单数量", required = true)
    private Integer waitToSendDeliveryCount;

    @FieldDoc(
            description = "待接单数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "待接单数量", required = true)
    private Integer waitToRiderAcceptCount;

    @FieldDoc(
            description = "待到店数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "待到店数量", required = true)
    private Integer waitToArriveShopCount;

    @FieldDoc(
            description = "待取货数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "待取货数量", required = true)
    private Integer waitToTakeGoodsCount;

    @FieldDoc(
            description = "配送中数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "配送中数量", required = true)
    private Integer deliveringCount;
}
