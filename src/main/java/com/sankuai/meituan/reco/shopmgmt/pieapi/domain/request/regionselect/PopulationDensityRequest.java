package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.regionselect;

import com.meituan.linz.boot.util.Assert;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Getter;
import lombok.Setter;

@TypeDoc(
    description = "人口密度查询请求参数",
    authors = {
        "daiyuan03"
    }
)
@Getter
@Setter
public class PopulationDensityRequest {

    @FieldDoc(
        description = "城市id"
    )
    private Integer cityId;

    @FieldDoc(
        description = "西南角点位坐标"
    )
    private String southWestPoint;

    @FieldDoc(
        description = "东北角点位坐标"
    )
    private String northEastPoint;

    @FieldDoc(
        description = "缩放比例"
    )
    private Integer scaling;


    public void validate() {
        Assert.throwIfTrue(this.cityId == null || this.cityId <= 0L, "城市id不正确");
        Assert.throwIfTrue(this.scaling == null || this.scaling <= 0L, "缩放比例不正确");
        Assert.throwIfBlank(this.southWestPoint, "西南角点位坐标必传");
        Assert.throwIfBlank(this.northEastPoint, "东北角点位坐标必传");
    }

}
