package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.ParamException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.StoreSpuSpecialtyUpdateRequest;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@TypeDoc(
        description = "更新门店商品力荐请求",
        authors = "hejunliang"
)
@Data
public class StoreSpuSpecialtyUpdateApiRequest {

    @FieldDoc(
            description = "门店id",
            requiredness = Requiredness.REQUIRED
    )
    private Long storeId;

    @FieldDoc(
            description = "spu编码",
            requiredness = Requiredness.REQUIRED
    )
    private String spuId;

    @FieldDoc(
            description = "渠道id列表",
            requiredness = Requiredness.REQUIRED
    )
    private List<Integer> channelIds;

    @FieldDoc(
            description = "力荐标识, 0: 取消力荐, 1:设置力荐",
            requiredness = Requiredness.REQUIRED,
            rule = "0: 取消力荐, 1:设置力荐"
    )
    private Integer specialty;

    public void validate() {
        if (this.storeId == null) {
            throw new ParamException("门店id不能为空");
        }
        if (StringUtils.isEmpty(this.spuId)) {
            throw new ParamException("spuId不能为空");
        }
        if (CollectionUtils.isEmpty(this.channelIds)) {
            throw new ParamException("渠道id列表不能为空");
        }
        if (this.specialty == null) {
            throw new ParamException("力荐标识不能空");
        }
    }

    public static StoreSpuSpecialtyUpdateRequest toRpcRequest(StoreSpuSpecialtyUpdateApiRequest request, User user) {

        StoreSpuSpecialtyUpdateRequest rpcRequest = new StoreSpuSpecialtyUpdateRequest();
        rpcRequest.setTenantId(user.getTenantId());
        rpcRequest.setStoreId(request.getStoreId());
        rpcRequest.setSpuId(request.getSpuId());
        rpcRequest.setChannelIds(request.getChannelIds());
        rpcRequest.setSpecialty(request.getSpecialty());
        rpcRequest.setOperatorId(user.getAccountId());
        rpcRequest.setOperatorName(user.getOperatorName());

        return rpcRequest;
    }
}
