package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: zhangbo
 * @date: 2020-05-15 17:03
 */
@TypeDoc(
        description = "查询已上架已下架商品数响应"
)
@Data
@ApiModel("查询已上架已下架商品数响应")
public class QueryOnSaleAndOffSaleCountResponseVO {
    @FieldDoc(
            description = "已上架商品数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "已上架商品数", required = true)
    private Integer onSaleCount;

    @FieldDoc(
            description = "已下架商品数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "已下架商品数", required = true)
    private Integer offSaleCount;

    @FieldDoc(
            description = "未上线商品数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "未上线商品数", required = true)
    private Integer notOnlineCount;

}
