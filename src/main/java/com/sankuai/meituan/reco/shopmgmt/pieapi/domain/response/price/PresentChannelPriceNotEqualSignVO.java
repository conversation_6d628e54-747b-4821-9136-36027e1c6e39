package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.StoreSkuKey;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
@ApiModel(
        "渠道价格不一致问题商品信息"
)
@TypeDoc(
        description = "渠道价格不一致问题商品信息"
)
public class PresentChannelPriceNotEqualSignVO {

    @FieldDoc(
            description = "渠道"
    )
    @ApiModelProperty("渠道")
    private Integer channelId;

    @FieldDoc(
            description = "门店标识"
    )
    @ApiModelProperty("门店标识")
    private Long storeId;

    @FieldDoc(
            description = "门店名称"
    )
    @ApiModelProperty("门店名称")
    private String storeName;

    @FieldDoc(
            description = "商品SPU标识"
    )
    @ApiModelProperty("商品SPU标识")
    private String spuId;

    @FieldDoc(
            description = "商品SKU标识"
    )
    @ApiModelProperty("商品SKU标识")
    private String skuId;

    @FieldDoc(
            description = "商品名称"
    )
    @ApiModelProperty("商品名称")
    private String skuName;

    @FieldDoc(
            description = "商品规格"
    )
    @ApiModelProperty("商品规格")
    private String spec;

    @FieldDoc(
            description = "商品图片列表"
    )
    @ApiModelProperty("")
    private List<String> images;

    @FieldDoc(
            description = "价格不一致首次发现时间"
    )
    @ApiModelProperty("价格不一致首次发现时间")
    private Long notEqualSignTime;

    @FieldDoc(
            description = "当前平台零售价"
    )
    @ApiModelProperty("当前平台零售价")
    private String presentChannelPrice;

    @FieldDoc(
            description = "中台零售价"
    )
    @ApiModelProperty("中台零售价")
    private String channelPrice;

    @FieldDoc(
            description = "待审核零售价"
    )
    @ApiModelProperty("待审核零售价")
    private String reviewingChannelPrice;

    @FieldDoc(
            description = "是否处于活动中"
    )
    @ApiModelProperty("是否处于活动中")
    private Boolean atPromotion;

    @FieldDoc(
            description = "商品上下架状态：-1-未创建，1-上架，2-下架"
    )
    @ApiModelProperty("商品上下架状态：-1-未创建，1-上架，2-下架")
    private Integer status;

    @FieldDoc(
            description = "商品标签列表"
    )
    @ApiModelProperty("商品标签列表")
    private List<String> tagList;

    public StoreSkuKey genStoreSkuKey() {
        return StoreSkuKey.builder().storeId(storeId).skuId(skuId).build();
    }
}
