package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TypeDoc(
        description = "分拣操作请求"
)
@ApiModel("分拣操作请求")
@Data
public class WarehouseSortingPickOperateRequest {

    @FieldDoc(
            description = "波次任务号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "波次任务号")
    private String taskOrderId;

    @FieldDoc(
            description = "skuId", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "skuId")
    private String skuId;

    @FieldDoc(
            description = "num", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "num")
    private Integer num;

    @FieldDoc(
            description = "分拣任务id", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "分拣任务id")
    private Long pickTaskId;

}
