package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.impl;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.AbstractSinglePendingTaskService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.delivery.ScmDeliveryWrapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * author xujunfeng02
 * dateTime 2023/2/23 4:45 PM
 * description
 */
@Service
public class ScmDeliveryPendingTaskService extends AbstractSinglePendingTaskService {

    @Resource
    private ScmDeliveryWrapper wrapper;

    @Override
    protected PendingTaskResult getPendingTaskCount(PendingTaskParam param) throws Exception {

        return PendingTaskResult.createNumberMarker(wrapper.count(param));
    }

    @Override
    protected AuthCodeEnum module() {
        return AuthCodeEnum.DH_TAKE_DELIVERY_BY_ORDER;
    }
}