package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew.PullNewAggModuleVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 拉新个人统计多模块数据
 *
 * <AUTHOR>
 * @since 5/8/23
 */
@TypeDoc(
        description = "拉新个人统计多模块数据"
)
@Data
@ApiModel("拉新个人统计多模块数据")
@NoArgsConstructor
@AllArgsConstructor
public class PullNewStatMultiModuleVo {

    @FieldDoc(
            description = "模块数据", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "模块数据", required = true)
    private List<PullNewAggModuleVo> list;
}
