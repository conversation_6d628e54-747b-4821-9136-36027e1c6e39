package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.GetReasonTemplateRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.GetReasonTemplateResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.Get;

/**
 * @description: 获取审核拒绝原因模板响应
 * @author: WangSukuan
 * @create: 2020-03-11
 **/
@TypeDoc(
        description = "获取审核拒绝原因模板响应"
)
@Data
@ApiModel("获取审核拒绝原因模板响应")
public class ReasonTemplateGetResponse {

    @FieldDoc(
            description = "审核拒绝原因模板", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "审核拒绝原因模板")
    private  ReviewReasonTemplateVO reviewReasonTemplateVO;


    public ReasonTemplateGetResponse buildReasonTemplateGetResponse(GetReasonTemplateResponse getReasonTemplateResponse){

       if (null == getReasonTemplateResponse.getData()){
           return this;
       }
       this.reviewReasonTemplateVO = new ReviewReasonTemplateVO().buildReviewReasonTemplateVO(getReasonTemplateResponse.getData());
       return this;

    }

}
