package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.drunkhorsemgmt.labor.constants.ApprovalEventSceneEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * Email <EMAIL>
 * Date 2022/10/14 5:56 下午
 * Description
 */

@TypeDoc(
        description = "考勤异常申报请求"
)
@ApiModel("考勤异常申报请求")
@Data
public class AttendanceApprovalCreateRequest {

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店ID")
    @NotNull
    private Long poiId;

    @FieldDoc(
            description = "考勤统计id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "考勤统计id")
    @NotNull
    private Long resultStatisticsId;

    @FieldDoc(
            description = "申诉场景", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "申诉场景")
    private String eventScene;

    @FieldDoc(
            description = "申诉说明", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "申诉说明")
    private String applyText;

    @FieldDoc(
            description = "申述照片", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "申述照片")
    private List<String> photoList;

    public Optional<String> validate() {
        if (this.poiId == null || this.poiId <= 0) {
            return Optional.of("门店Id异常");
        } else if (this.resultStatisticsId == null || this.resultStatisticsId <= 0) {
            return Optional.of("考勤结果统计Id异常");
        } else if (!ApprovalEventSceneEnum.attendanceApprovalEvents().contains(ApprovalEventSceneEnum.enumOf(eventScene))) {
            return Optional.of("申诉场景未选择");
        }
        return Optional.empty();
    }
}
