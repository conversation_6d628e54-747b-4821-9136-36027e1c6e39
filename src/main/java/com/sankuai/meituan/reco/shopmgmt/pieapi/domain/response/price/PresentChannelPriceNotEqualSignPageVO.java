package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
@ApiModel(
        "渠道价格不一致问题商品信息分页信息"
)
@TypeDoc(
        description = "渠道价格不一致问题商品信息分页信息"
)
public class PresentChannelPriceNotEqualSignPageVO {

    @FieldDoc(
            description = "渠道价格不一致问题商品列表"
    )
    @ApiModelProperty("渠道价格不一致问题商品列表")
    private List<PresentChannelPriceNotEqualSignVO> notEqualSignSkus;

    @FieldDoc(
            description = "分页信息"
    )
    @ApiModelProperty("分页信息")
    private PageInfoVO pageInfo;
}
