package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.BrandDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Title: BrandVO
 * @Description: 品牌信息
 * @Author: zhaolei12
 * @Date: 2020/5/16 5:41 下午
 */
@TypeDoc(
        description = "品牌信息"
)
@Data
@ApiModel("品牌信息")
public class BrandVO {

    @FieldDoc(
            description = "品牌code", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "品牌code")
    private String brandCode;

    @FieldDoc(
            description = "品牌名称（创建更新商品时无需传入，用于详情返回）", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "品牌名称（创建更新商品时无需传入，用于详情返回）")
    private String brandName;

    @FieldDoc(
            description = "渠道类目编码全路径", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "品牌编码全路径")
    private String brandCodePath;

    @FieldDoc(
            description = "渠道类目名称全路径", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "品牌名称全路径")
    private String brandNamePath;

    public static BrandVO ofDTO(BrandDTO brandDTO) {
        if (brandDTO == null) {
            return null;
        }

        BrandVO brandVO = new BrandVO();
        brandVO.setBrandCode(brandDTO.getBrandCode());
        brandVO.setBrandName(brandDTO.getBrandName());
        brandVO.setBrandCodePath(brandDTO.getBrandCodePath());
        brandVO.setBrandNamePath(brandDTO.getBrandNamePath());

        return brandVO;
    }

    public static BrandVO ofDTO(com.sankuai.meituan.shangou.empower.productbiz.client.dto.BrandDTO brandDTO) {
        if (brandDTO == null) {
            return null;
        }

        BrandVO brandVO = new BrandVO();
        brandVO.setBrandCode(brandDTO.getBrandCode());
        brandVO.setBrandName(brandDTO.getBrandName());
        brandVO.setBrandCodePath(brandDTO.getBrandCodePath());
        brandVO.setBrandNamePath(brandDTO.getBrandNamePath());

        return brandVO;
    }

    public static BrandDTO toDTO(BrandVO brandVO) {
        if (brandVO == null) {
            return null;
        }

        BrandDTO brandDTO = new BrandDTO();
        brandDTO.setBrandCode(brandVO.getBrandCode());
        brandDTO.setBrandName(brandVO.getBrandName());
        brandDTO.setBrandCodePath(brandVO.getBrandCodePath());
        brandDTO.setBrandNamePath(brandVO.getBrandNamePath());

        return brandDTO;
    }
}
