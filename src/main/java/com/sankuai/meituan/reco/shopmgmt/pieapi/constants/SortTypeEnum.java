package com.sankuai.meituan.reco.shopmgmt.pieapi.constants;

import org.apache.commons.lang3.StringUtils;

/**
 * 排序类型
 *
 * <AUTHOR>
 */
public enum SortTypeEnum {

    ASC("asc", "升序"),
    DESC("desc", "降序");

    /**
     * 排序类型
     */
    private String sortType;

    /**
     * 描述
     */
    private String desc;

    SortTypeEnum(String sortType, String desc) {
        this.sortType = sortType;
        this.desc = desc;
    }

    public String getSortType() {
        return sortType;
    }

    public String getDesc() {
        return desc;
    }

    public static SortTypeEnum findBySortType(String sortType) {

        String trimSortType = StringUtils.trimToEmpty(sortType);

        if (StringUtils.equalsIgnoreCase(ASC.getSortType(), trimSortType)) {
            return ASC;
        } else if (StringUtils.equalsIgnoreCase(DESC.getSortType(), trimSortType)) {
            return DESC;
        } else {
            return null;
        }
    }

    public static boolean isValidSortType(String sortType) {
        SortTypeEnum sortTypeEnum = findBySortType(sortType);
        return sortType != null;
    }
}
