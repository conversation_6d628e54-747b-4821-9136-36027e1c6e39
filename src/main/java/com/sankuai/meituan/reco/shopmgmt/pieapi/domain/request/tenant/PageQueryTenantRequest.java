package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.tenant;

import javax.validation.constraints.NotNull;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import lombok.Data;

@Data
@TypeDoc(
        description = "分页查询租户列表"
)
public class PageQueryTenantRequest {

    @FieldDoc(description = "租户id")
    private Long tenantId;

    @FieldDoc(description = "租户名称")
    private String tenantName;

    @FieldDoc(description = "第几页")
    @NotNull
    private Integer page;

    @FieldDoc(description = "页大小")
    @NotNull
    private Integer pageSize;
}
