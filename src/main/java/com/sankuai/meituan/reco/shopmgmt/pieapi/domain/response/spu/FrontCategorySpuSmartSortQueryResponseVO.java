package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * FileName: FrontCategorySpuSmartSortQueryResponse
 * Author:   wangjiawei31
 * Date:     2021/2/25 5:03 下午
 * Description:
 */
@TypeDoc(
        description = "商品分类智能排序开关信息"
)
@Data
@ApiModel("商品分类智能排序开关信息")
public class FrontCategorySpuSmartSortQueryResponseVO {

    @FieldDoc(
            description = "租户id", requiredness = Requiredness.REQUIRED
    )
    private Long tenantId;

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    private Long storeId;

    @FieldDoc(
            description = "渠道id", requiredness = Requiredness.REQUIRED
    )
    private Integer channelId;

    @FieldDoc(
            description = "店内分类id", requiredness = Requiredness.REQUIRED
    )
    private Long frontCategoryId;

    @FieldDoc(
            description = "智能排序", requiredness = Requiredness.REQUIRED
    )
    private Boolean smartSort;
}
