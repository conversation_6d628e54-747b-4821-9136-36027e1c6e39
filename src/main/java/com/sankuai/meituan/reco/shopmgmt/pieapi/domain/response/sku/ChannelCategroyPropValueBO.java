package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelqualification.dto.CategoryPropDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/1/24
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ChannelCategroyPropValueBO {

    private String categoryPropId;
    private List<String> propValues;

    public CategoryPropDTO toCategoryPropDTO() {
        CategoryPropDTO categoryPropDTO = new CategoryPropDTO();
        categoryPropDTO.setCategoryPropId(categoryPropId);
        categoryPropDTO.setPropValues(propValues);
        return  categoryPropDTO;
    }

}
