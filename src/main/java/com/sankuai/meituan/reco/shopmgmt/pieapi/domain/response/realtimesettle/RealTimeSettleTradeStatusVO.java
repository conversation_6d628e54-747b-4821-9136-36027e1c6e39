package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.realtimesettle;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@TypeDoc(
        description = "实时结算交易状态值对象"
)
@Data
@ApiModel("实时结算交易状态值对象")
public class RealTimeSettleTradeStatusVO {

    @FieldDoc(
            description = "交易单号", requiredness = Requiredness.REQUIRED
    )
    private Long tradeNo;

    @FieldDoc(
            description = "支付状态(10支付中15已确认收款20支付成功30支付失败)", requiredness = Requiredness.REQUIRED
    )
    private Integer tradeStatus;

    @FieldDoc(
            description = "状态描述", requiredness = Requiredness.REQUIRED
    )
    private String statusComment;

}
