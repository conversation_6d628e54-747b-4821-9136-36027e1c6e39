package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.account;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @createTime 2019/11/19
 * @description
 */
@TypeDoc(
        description = "账号消息和铃声配置"
)
@Data
@ApiModel("账号消息和铃声配置")
public class AccountConfigVO {

    /**
     * 消息和铃声设置项
     */
    @FieldDoc(
            description = "新订单提醒 (0:不提醒，n:n次)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "新订单提醒 (0:不提醒，n:n次)", required = true)
    private Integer newOrderAlertTimes;


    @FieldDoc(
            description = "新拣货任务提醒 (0:不提醒，n:n次)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "新拣货任务提醒 (0:不提醒，n:n次)", required = true)
    private Integer newPickTaskAlertTimes;

    @FieldDoc(
            description = "订单领取超时提醒 (0:不提醒，n:n次)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单领取超时提醒 (0:不提醒，n:n次)", required = true)
    private Integer pickTaskClaimOvertimeAlertTimes;


    @FieldDoc(
            description = "订单拣货超时提醒 (0:不提醒，n:n次)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单拣货超时提醒 (0:不提醒，n:n次)", required = true)
    private Integer pickTaskOvertimeAlertTimes;


    @FieldDoc(
            description = "订单合流超时提醒 (0:不提醒，n:n次)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单合流超时提醒 (0:不提醒，n:n次)", required = true)
    private Integer mergeOvertimeAlertTimes;


    @FieldDoc(
            description = "是否接收电话通知 (true 接收  false 不接收)", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否接收电话通知 (true 接收  false 不接收)", required = false)
    private Boolean receivePhoneCall;
}
