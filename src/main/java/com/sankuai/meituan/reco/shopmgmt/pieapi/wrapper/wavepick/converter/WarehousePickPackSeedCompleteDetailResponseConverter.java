package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.wavepick.converter;

import com.sankuai.meituan.reco.pickselect.thrift.wave.dto.WarehouseWaveSeedCompletePackDTO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.WarehousePickPackSeedCompleteDetailResponse;
import org.mapstruct.Mapper;

/**
 * @description:
 * @Auther: nifei
 * @Date: 2023/8/25 10:47
 */
@Mapper(componentModel = "spring")
public abstract class WarehousePickPackSeedCompleteDetailResponseConverter {
    public abstract WarehousePickPackSeedCompleteDetailResponse convert2Response(WarehouseWaveSeedCompletePackDTO warehouseWaveSeedCompletePackDTO);
}
