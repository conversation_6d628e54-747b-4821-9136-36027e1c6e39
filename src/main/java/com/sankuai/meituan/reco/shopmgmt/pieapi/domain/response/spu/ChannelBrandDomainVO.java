package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelBrandBindingDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelBrandRelationDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Title: ChannelBrandVO
 * @Description: 渠道品牌信息
 * @Author: zhaolei12
 * @Date: 2020/5/16 5:58 下午
 */
@TypeDoc(
        description = "渠道品牌信息"
)
@Data
@ApiModel("渠道品牌信息")
public class ChannelBrandDomainVO {

    @FieldDoc(
            description = "渠道id", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道id")
    private Integer channelId;

    @FieldDoc(
            description = "渠道名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道名称")
    private String channelName;

    @FieldDoc(
            description = "渠道品牌编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道品牌编码")
    private String brandCode;

    @FieldDoc(
            description = "渠道品牌名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道品牌名称")
    private String brandName;

    public static ChannelBrandDomainVO of(ChannelBrandBindingDTO channelBrandDTO) {
        if (channelBrandDTO == null) {
            return null;
        }
        ChannelBrandDomainVO channelBrandVO = new ChannelBrandDomainVO();
        channelBrandVO.setChannelId(channelBrandDTO.getChannelId());
        channelBrandVO.setBrandCode(channelBrandDTO.getBrandCode());
        channelBrandVO.setBrandName(channelBrandDTO.getBrandName());
        return channelBrandVO;
    }

    public static ChannelBrandDomainVO ofBiz(ChannelBrandRelationDTO channelBrandDTO) {
        if (channelBrandDTO == null) {
            return null;
        }
        ChannelBrandDomainVO channelBrandVO = new ChannelBrandDomainVO();
        channelBrandVO.setChannelId(channelBrandDTO.getChannelId());
        channelBrandVO.setBrandCode(channelBrandDTO.getBrandCode());
        channelBrandVO.setBrandName(channelBrandDTO.getBrandName());
        return channelBrandVO;
    }

}
