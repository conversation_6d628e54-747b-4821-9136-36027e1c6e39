package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.priceconfig;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.CdqStoreSkuVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.ChannelSkuForAppVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.StoreSkuBaseDetailVO;
import com.sankuai.meituan.shangou.empower.price.client.dto.config.StoreSkuPriceFilterConditionDTO;
import lombok.Data;

/**
 * 门店商品价格过滤器查询条件
 *
 * <AUTHOR>
 */
@Data
public class StoreSkuPriceFilterCondition {

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * skuId
     */
    private String skuId;

    /**
     * 称重类型
     */
    private Integer weightType;

    /**
     * 重量
     */
    private Integer weight;

    public static StoreSkuPriceFilterCondition build(ChannelSkuForAppVO skuInfo) {

        StoreSkuPriceFilterCondition filterConditionDomain = new StoreSkuPriceFilterCondition();
        filterConditionDomain.setStoreId(skuInfo.getStoreId());
        filterConditionDomain.setSkuId(skuInfo.getSku());
        filterConditionDomain.setWeightType(skuInfo.getWeightType());
        filterConditionDomain.setWeight(skuInfo.getWeight());

        return filterConditionDomain;
    }

    public static StoreSkuPriceFilterCondition build(CdqStoreSkuVo storeSku) {

        StoreSkuPriceFilterCondition filterConditionDomain = new StoreSkuPriceFilterCondition();
        filterConditionDomain.setStoreId(storeSku.getStoreId());
        filterConditionDomain.setSkuId(storeSku.getSkuId());
        filterConditionDomain.setWeightType(storeSku.getWeightType());
        filterConditionDomain.setWeight(storeSku.getWeight());

        return filterConditionDomain;
    }

    public static StoreSkuPriceFilterCondition build(StoreSkuBaseDetailVO storeSkuBaseDetail) {
        StoreSkuPriceFilterCondition filterConditionDomain = new StoreSkuPriceFilterCondition();
        filterConditionDomain.setStoreId(storeSkuBaseDetail.getStoreId());
        filterConditionDomain.setSkuId(storeSkuBaseDetail.getSkuId());
        filterConditionDomain.setWeightType(storeSkuBaseDetail.getWeightType());
        filterConditionDomain.setWeight(storeSkuBaseDetail.getWeight());

        return filterConditionDomain;
    }


    public StoreSkuPriceFilterConditionDTO convert2StoreSkuPriceFilterConditionDTO() {
        StoreSkuPriceFilterConditionDTO dto = new StoreSkuPriceFilterConditionDTO();
        dto.setStoreId(this.storeId);
        dto.setSkuId(this.skuId);
        dto.setWeightType(this.weightType);
        dto.setWeight(this.weight);
        return dto;
    }

}
