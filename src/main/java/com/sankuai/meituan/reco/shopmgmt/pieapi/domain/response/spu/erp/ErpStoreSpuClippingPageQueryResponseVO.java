package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/19
 */
@TypeDoc(
        description = "ERP门店商品SPU裁剪结构分页查询响应结果"
)
@Data
@ApiModel("ERP门店商品SPU裁剪结构分页查询响应结果")
public class ErpStoreSpuClippingPageQueryResponseVO {

    @FieldDoc(description = "商品信息裁剪对象列表", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "商品信息裁剪对象列表")
    private List<ErpStoreSpuClippingVO> storeSpuList;

    @FieldDoc(description = "分页信息", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "分页信息")
    private PageInfoVO pageInfo;
}
