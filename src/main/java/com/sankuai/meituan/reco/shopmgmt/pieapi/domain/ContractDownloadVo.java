package com.sankuai.meituan.reco.shopmgmt.pieapi.domain;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

@TypeDoc(
    description = "<p>附件详情信息</p>"
)
@Data
public class ContractDownloadVo {
    @FieldDoc(
            description = "附件名称"
    )
    public String name;

    @FieldDoc(
            description = "合同下载地址"
    )
    public String attachmentDownloadUrl;
}