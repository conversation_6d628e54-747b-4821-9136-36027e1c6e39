/*
 * Copyright (c) 2019 Meituan.com. All Rights Reserved.
 */
package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.impl;

import com.sankuai.meituan.reco.pickselect.query.thrift.fulfill.FulfillThriftService;
import com.sankuai.meituan.reco.pickselect.query.thrift.fulfill.request.PageQueryFulfillingOrderRequest;
import com.sankuai.meituan.reco.pickselect.query.thrift.fulfill.response.PagedFulfillingOrdersResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.AbstractSinglePendingTaskService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class OrderWaitToPickPendingTaskService extends AbstractSinglePendingTaskService {

    @Resource(name = "queryFulfillThriftService")
    private FulfillThriftService queryFulfillThriftService;

    @Resource(name = "fulfillThriftService")
    private com.sankuai.meituan.reco.pickselect.thrift.fulfill.FulfillThriftService fulfillThriftService;

    @Override
    public PendingTaskResult getPendingTaskCount(PendingTaskParam param) throws Exception {
        //待拣货切换数据源
        Integer totalSize = 0;
        try {
            //请求拣货获取待拣货数量
            if(MccConfigUtil.getPickQuerySwitch()){
                PageQueryFulfillingOrderRequest pageQueryFulfillingOrderRequest = new PageQueryFulfillingOrderRequest();
                pageQueryFulfillingOrderRequest.setPageSize(1);
                pageQueryFulfillingOrderRequest.setPageNo(1);
                pageQueryFulfillingOrderRequest.setEmpowerStoreId(param.getStoreIds().get(0));
                pageQueryFulfillingOrderRequest.setTenantId(param.getTenantId());
                log.info("OrderWaitToPickPendingTaskService.getPendingTaskCount   fulfillThriftService.pageQueryFulfillingOrders request:{}", pageQueryFulfillingOrderRequest);
                PagedFulfillingOrdersResponse pagedFulfillingOrdersResponse = queryFulfillThriftService.pageQueryFulfillingOrders(pageQueryFulfillingOrderRequest);
                log.info("OrderWaitToPickPendingTaskService.getPendingTaskCount  fulfillThriftService.pageQueryFulfillingOrders response:{}", pagedFulfillingOrdersResponse);
                if (pagedFulfillingOrdersResponse.getStatus().getCode() == ResultCodeEnum.SUCCESS.getValue()) {
                    totalSize = pagedFulfillingOrdersResponse.getTotalSize().intValue();
                }
            }else {
                com.sankuai.meituan.reco.pickselect.thrift.fulfill.request.PageQueryFulfillingOrderRequest pageQueryFulfillingOrderRequest = new com.sankuai.meituan.reco.pickselect.thrift.fulfill.request.PageQueryFulfillingOrderRequest();
                pageQueryFulfillingOrderRequest.setPageSize(1);
                pageQueryFulfillingOrderRequest.setPageNo(1);
                pageQueryFulfillingOrderRequest.setEmpowerStoreId(param.getStoreIds().get(0));
                pageQueryFulfillingOrderRequest.setTenantId(param.getTenantId());
                log.info("OrderWaitToPickPendingTaskService.getPendingTaskCount   fulfillThriftService.pageQueryFulfillingOrders request:{}", pageQueryFulfillingOrderRequest);
                com.sankuai.meituan.reco.pickselect.thrift.fulfill.response.PagedFulfillingOrdersResponse pagedFulfillingOrdersResponse = fulfillThriftService.pageQueryFulfillingOrders(pageQueryFulfillingOrderRequest);
                log.info("OrderWaitToPickPendingTaskService.getPendingTaskCount  fulfillThriftService.pageQueryFulfillingOrders response:{}", pagedFulfillingOrdersResponse);
                if (pagedFulfillingOrdersResponse.getStatus().getCode() == ResultCodeEnum.SUCCESS.getValue()) {
                    totalSize = pagedFulfillingOrdersResponse.getTotalSize().intValue();
                }
            }

        } catch (Exception e) {
            log.error("OrderWaitToPickPendingTaskService.getPendingTaskCount  调用fulfillThriftService.pageQueryFulfillingOrders error", e);
        }
        return PendingTaskResult.createNumberMarker(totalSize);
    }

    @Override
    protected AuthCodeEnum module() {
        return AuthCodeEnum.ORDER_WAIT_TO_PICK;
    }
}
