package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.FranchisorPurchasePlatformInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 海商加盟主采购平台编码信息
 *
 * <AUTHOR>
 * @since 2023/9/6
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FranchisorPurchasePlatformInfoVO {

    private String shopName;

    private String shopId;

    private String code;

    private Integer areaId;

    private String areaName;

    @FieldDoc(
            description = "城市id"
    )
    @ApiModelProperty("城市id")
    private Integer cityId;


    @FieldDoc(
            description = "城市名称"
    )
    @ApiModelProperty("城市名称")
    private String cityName;

    @FieldDoc(
            description = "箱规名称"
    )
    @ApiModelProperty("箱规名称")
    private String cartonMeasureName;

    @FieldDoc(
            description = "控货方式  0 中心仓配送   1 供应商直送"
    )
    @ApiModelProperty(value = "控货方式 0 中心仓配送   1 供应商直送")
    private Integer controlGoodsType;

    @FieldDoc(
            description = "关联供货商规格ID"
    )
    @ApiModelProperty(value = "关联供货商规格ID")
    private String supplierSkuId;


    public static FranchisorPurchasePlatformInfoVO of(FranchisorPurchasePlatformInfoDTO dto) {
        FranchisorPurchasePlatformInfoVO vo = new FranchisorPurchasePlatformInfoVO();
        vo.setShopName(dto.getShopName());
        vo.setShopId(dto.getShopId());
        vo.setCode(dto.getCode());
        vo.setAreaId(dto.getAreaId());
        vo.setAreaName(dto.getAreaName());
        vo.setCityId(dto.getCityId());
        vo.setCityName(dto.getCityName());
        vo.setCartonMeasureName(dto.getCartonMeasureName());
        vo.setControlGoodsType(dto.getControlGoodsType());
        vo.setSupplierSkuId(dto.getSupplierSkuId());
        return vo;
    }

    public static FranchisorPurchasePlatformInfoDTO toBizDTO(FranchisorPurchasePlatformInfoVO vo) {
        FranchisorPurchasePlatformInfoDTO dto = new FranchisorPurchasePlatformInfoDTO();
        dto.setShopName(vo.getShopName());
        dto.setShopId(vo.getShopId());
        dto.setCode(vo.getCode());
        dto.setAreaId(vo.getAreaId());
        dto.setAreaName(vo.getAreaName());
        dto.setCartonMeasureName(vo.getCartonMeasureName());
        dto.setCityId(vo.getCityId());
        dto.setCityName(vo.getCityName());
        dto.setControlGoodsType(vo.getControlGoodsType());
        dto.setSupplierSkuId(vo.getSupplierSkuId());
        return dto;
    }

    public static FranchisorPurchasePlatformInfoVO of(com.sankuai.meituan.shangou.empower.ocms.client.product.dto.FranchisorPurchasePlatformInfoDTO dto) {
        FranchisorPurchasePlatformInfoVO vo = new FranchisorPurchasePlatformInfoVO();
        vo.setShopName(dto.getShopName());
        vo.setShopId(dto.getShopId());
        vo.setCode(dto.getCode());
        vo.setAreaId(dto.getAreaId());
        vo.setAreaName(dto.getAreaName());
        vo.setCartonMeasureName(dto.getCartonMeasureName());
        vo.setCityId(dto.getCityId());
        vo.setCityName(dto.getCityName());
        vo.setControlGoodsType(dto.getControlGoodsType());
        vo.setSupplierSkuId(dto.getSupplierSkuId());
        return vo;
    }

    public static com.sankuai.meituan.shangou.empower.ocms.client.product.dto.FranchisorPurchasePlatformInfoDTO toFranchisorPurchasePlatformInfoDTO(FranchisorPurchasePlatformInfoVO vo) {
        com.sankuai.meituan.shangou.empower.ocms.client.product.dto.FranchisorPurchasePlatformInfoDTO dto = new com.sankuai.meituan.shangou.empower.ocms.client.product.dto.FranchisorPurchasePlatformInfoDTO();
        dto.setShopName(vo.getShopName());
        dto.setShopId(vo.getShopId());
        dto.setCode(vo.getCode());
        dto.setAreaId(vo.getAreaId());
        dto.setAreaName(vo.getAreaName());
        dto.setCartonMeasureName(vo.getCartonMeasureName());
        dto.setCityId(vo.getCityId());
        dto.setCityName(vo.getCityName());
        dto.setControlGoodsType(vo.getControlGoodsType());
        dto.setSupplierSkuId(vo.getSupplierSkuId());
        return dto;
    }



}
