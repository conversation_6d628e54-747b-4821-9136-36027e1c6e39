package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.impl;

import com.google.common.collect.Lists;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.AbstractSinglePendingTaskService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.out.warehouse.OutWarehouseWorkOrderWrapper;
import com.sankuai.meituan.reco.store.wms.thrift.outwarehouse.workorder.request.OutWarehouseWorkOrderCountThriftRequest;
import com.sankuai.meituan.reco.store.wms.thrift.outwarehouse.workorder.response.OutWarehouseWorkOrderCountThriftResponse;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * author xujunfeng02
 * dateTime 2022/5/11 4:18 PM
 * description 调拨出库复核工单服务
 */
@Service
public class OutWarehouseReviewWorkOrderService extends AbstractSinglePendingTaskService {

    /**
     * 出库复核类型工单状态，60-未领取待复核，64-已领取待复核
     */
    private static final List<Integer> REVIEW_INITIALIZED = Lists.newArrayList(60, 64);

    /**
     * 工单类型：62-出库复核类型工单
     */
    private static final int OUT_WAREHOUSE_REVIEW = 62;

    @Resource
    private OutWarehouseWorkOrderWrapper outWarehouseWorkOrderWrapper;

    @Override
    protected PendingTaskResult getPendingTaskCount(PendingTaskParam param) throws Exception {

        OutWarehouseWorkOrderCountThriftRequest thriftRequest = OutWarehouseWorkOrderCountThriftRequest.builder()
                .entityId(param.getEntityId())
                .status(REVIEW_INITIALIZED)
                .operatorId(param.getUser().getAccountId())
                .type(OUT_WAREHOUSE_REVIEW)
                .merchantId(param.getTenantId())
                .build();
        OutWarehouseWorkOrderCountThriftResponse thriftResponse = outWarehouseWorkOrderWrapper.count(thriftRequest);
        return PendingTaskResult.createNumberMarker(thriftResponse.getCount().intValue());
    }

    @Override
    protected AuthCodeEnum module() {
        return AuthCodeEnum.DH_TASK_RECHECK;
    }
}