package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.account;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @createTime 2019/11/19
 * @description
 */
@TypeDoc(
        description = "我的账号首页"
)
@Data
@ApiModel("我的账号首页")
public class AccountHomePageVO {

    @FieldDoc(
            description = "账号租户信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "账号租户信息", required = true)
    @NotNull
    private AccountTenantInfoVO accountTenantInfoVO;

    @FieldDoc(
            description = "是否展示打印设置(0:不展示,1:展示)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示打印设置(0:不展示,1:展示)", required = true)
    @NotNull
    private Integer showPrintSetting;

    @FieldDoc(
            description = "是否展示门店管理(0:不展示,1:展示)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示门店管理(0:不展示,1:展示)", required = true)
    @NotNull
    private Integer showPoiMgr;


    @FieldDoc(
            description = "是否展示门店保证金(0:不展示,1:展示)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示门店保证金(0:不展示,1:展示)", required = true)
    @NotNull
    private Integer showPoiSecurityDeposit;




    @FieldDoc(
            description = "是否展示微信拉新跳转地址", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示微信拉新跳转地址", required = true)
    @NotNull
    private String showWeChatPullNewUrl;

}

