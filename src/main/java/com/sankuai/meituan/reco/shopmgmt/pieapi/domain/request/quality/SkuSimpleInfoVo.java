package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.quality;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.product.dto.SkuDetailInfoDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

/**
 * <AUTHOR>
 * @since 2023/9/20
 */
@TypeDoc(
        name = "已选择的商品动态属性",
        description = "已选择的商品动态属性"
)
@Data
public class SkuSimpleInfoVo {

    @FieldDoc(
            description = "规格skuID"
    )
    @ApiModelProperty(name = "规格skuID")
    private String skuId;

    @FieldDoc(
            description = "规格前端唯一标识"
    )
    @ApiModelProperty(name = "规格前端唯一标识")
    private String uniqueFlag;

    @FieldDoc(
            description = "商品条码"
    )
    @ApiModelProperty(name = "商品条码")
    private String upc;

    @FieldDoc(
            description = "规格名称"
    )
    @ApiModelProperty(name = "规格名称")
    private String spec;

    @FieldDoc(
            description = "指定单位的重量信息"
    )
    @ApiModelProperty(name = "指定单位的重量信息")
    private String weightForUnit;

    @FieldDoc(
            description = "当值重量值对应的单位"
    )
    @ApiModelProperty(name = "当值重量值对应的单位")
    private String weightUnit;

    @FieldDoc(
            description = "规格添加的顺序"
    )
    @ApiModelProperty(name = "规格添加的顺序")
    private Integer seqNo;

    @FieldDoc(
            description = "当前美团渠道类目下，upc是否允许为空，false：必填，true：非必填"
    )
    @ApiModelProperty(name = "当前美团渠道类目下，upc是否允许为空，false：必填，true：非必填")
    private Boolean allowUpcEmpty;

    public SkuDetailInfoDto convert2SkuDetailInfoDto() {
        SkuDetailInfoDto infoDto = new SkuDetailInfoDto();
        infoDto.setSkuId(skuId);
        infoDto.setSkuFlag(uniqueFlag);
        infoDto.setSpec(spec);
        infoDto.setUpc(upc);
        infoDto.setWeightUnit(weightUnit);
        infoDto.setSeqNo(seqNo);
        infoDto.setAllowUpcEmpty(allowUpcEmpty);
        if (StringUtils.isNotBlank(weightForUnit) && NumberUtils.isCreatable(weightForUnit)) {
            infoDto.setWeightForUnit(Double.valueOf(weightForUnit));
        }

        return infoDto;
    }
}
