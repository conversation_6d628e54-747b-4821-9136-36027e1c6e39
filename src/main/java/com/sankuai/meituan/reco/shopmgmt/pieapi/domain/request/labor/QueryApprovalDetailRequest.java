package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022-12-08
 * @email <EMAIL>
 */
@TypeDoc(
        description = "校验手机请求"
)
@Data
@ApiModel("校验手机请求")
@AllArgsConstructor
@NoArgsConstructor
public class QueryApprovalDetailRequest {

    @FieldDoc(
            description = "审核id"
    )
    @NotNull
    private Long approvalId;


}
