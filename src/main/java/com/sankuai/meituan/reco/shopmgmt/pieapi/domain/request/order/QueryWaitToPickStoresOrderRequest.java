package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@TypeDoc(
    description = "查询待接单列表"
)
@ApiModel("查询待接单列表")
@Data
public class QueryWaitToPickStoresOrderRequest extends QueryWaitToPickOrderRequest {
    @FieldDoc(
        description = "租户id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户id")
    @NotNull
    private Long tenantId;

    @FieldDoc(
        description = "门店id集合", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店id集合")
    private List<Long> storeIds;
}
