package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @Auther: wb_nifei
 * @Date: 2023/8/21 14:49
 */
@TypeDoc(
        description = "装箱满箱请求"
)
@ApiModel("装箱满箱请求")
@Data
public class WarehousePickPackFullRequest {
    @FieldDoc(
            description = "拣货订单ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "拣货订单ID")
    private String orderId;

    @FieldDoc(
            description = "箱ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "箱ID")
    private String huCode;
}
