package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TypeDoc(
        description = "模糊查询拣货任务详情里商品信息的请求体"
)
@ApiModel("模糊查询拣货任务详情里商品信息的请求体")
@Data
public class WarehousePickDetailFuzzyRequest {

    @FieldDoc(
            description = "模糊查询条件", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "模糊查询条件")
    private String condition;

    @FieldDoc(
            description = "商品展示模式，1代表已拣货，2代表待拣货", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "商品展示模式，1代表已拣货，2代表待拣货")
    private Integer skuMode;

    @FieldDoc(
            description = "波次号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "波次号")
    private String waveId;

    @FieldDoc(
            description = "任务号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "任务号")
    private Integer taskId;

    @FieldDoc(
            description = "分页码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "分页码")
    private Integer pageNo;

    @FieldDoc(
            description = "分页大小", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "分页大小")
    private Integer pageSize;
}
