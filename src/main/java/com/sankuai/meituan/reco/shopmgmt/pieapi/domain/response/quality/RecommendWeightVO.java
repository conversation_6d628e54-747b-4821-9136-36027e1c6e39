package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.quality;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/9/20
 */
@TypeDoc(
        name = "推荐重量对象",
        description = "推荐重量对象"
)
@Data
public class RecommendWeightVO {

    @FieldDoc(
            description = "重量单位"
    )
    @ApiModelProperty(name = "重量单位")
    private String weightUnit;

    @FieldDoc(
            description = "指定重量单位下的重量值"
    )
    @ApiModelProperty(name = "指定重量单位下的重量值")
    private String weightForUnit;
}
