package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.revenue;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @createTime 2019-06-18
 * @description
 */
@TypeDoc(
        description = "摊位历史账单按月查询响应——汇总信息",
        version = "1.0"
)
@ApiModel("摊位历史账单按月查询响应——汇总信息")
@Data
public class BoothHistorySettlementMonthlySummaryVO {

    @FieldDoc(
            description = "是否摊主", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否摊主", required = true)
    @NotNull
    private Boolean isBooth;

    @FieldDoc(
            description = "是否资金打款结算", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否资金打款结算", required = true)
    @NotNull
    private Boolean isSettlementPay;

    @FieldDoc(
            description = "是否摊位折价结算", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否摊位折价结算", required = true)
    @NotNull
    private Boolean isBoothSettlementDiscount;

    @FieldDoc(description = "营收总金额(分)", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "营收总金额(分)", required = true)
    private Long totalRevenue;

    @FieldDoc(description = "有效订单数量", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "有效订单数量", required = true)
    private Long totalOrderCount;

    @FieldDoc(description = "营收金额文案", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "营收金额文案", required = true)
    private String revenueComment;

    @FieldDoc(
            description = "摊位id，是摊主时该字段有值"
    )
    @ApiModelProperty(value = "摊位id")
    private Long boothId;
}
