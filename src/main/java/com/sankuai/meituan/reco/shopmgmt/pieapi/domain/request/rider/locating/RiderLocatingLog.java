package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.locating;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@TypeDoc(
        description = "定位日志上报结构"
)
@Data
@ApiModel("定位日志上报结构")
public class RiderLocatingLog {
    @FieldDoc(
            description = "骑手账号id",
            requiredness = Requiredness.REQUIRED
    )
    private Long riderAccountId;

    @FieldDoc(
            description = "日志类型 0-正常定位日志 1-异常日志",
            requiredness = Requiredness.REQUIRED
    )
    private Integer logType;

    @FieldDoc(
            description = "异常类型",
            requiredness = Requiredness.OPTIONAL
    )
    private Integer exceptionType;

    @FieldDoc(
            description = "纬度",
            requiredness = Requiredness.OPTIONAL
    )
    private String latitude;

    @FieldDoc(
            description = "经度",
            requiredness = Requiredness.OPTIONAL
    )
    private String longitude;

    @FieldDoc(
            description = "定位精度",
            requiredness = Requiredness.OPTIONAL
    )
    private String accuracy;

    @FieldDoc(
            description = "速度",
            requiredness = Requiredness.OPTIONAL
    )
    private String speed;

    @FieldDoc(
            description = "角度",
            requiredness = Requiredness.OPTIONAL
    )
    private String bearing;

    @FieldDoc(
            description = "手机系统",
            requiredness = Requiredness.OPTIONAL
    )
    private String phoneOS;

    @FieldDoc(
            description = "如果是正常定位日志，是否用到该位置",
            requiredness = Requiredness.OPTIONAL
    )
    private Boolean locationIsUsed;

    @FieldDoc(
            description = "设备id",
            requiredness = Requiredness.OPTIONAL
    )
    private String uuid;

    @FieldDoc(
            description = "手机厂商",
            requiredness = Requiredness.OPTIONAL
    )
    private String manufacturer;


    @FieldDoc(
            description = "前端采集到此条日志的时间",
            requiredness = Requiredness.OPTIONAL
    )
    private Long utime;
}
