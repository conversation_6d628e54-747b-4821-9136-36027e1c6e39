package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * <AUTHOR>
 * @createTime 2019/11/19
 * @description 拣货首页聚合数据
 */
@TypeDoc(
        description = "拣货首页聚合数据"
)
@Data
@ApiModel("拣货首页聚合数据")
public class PickHomePageDataVO {

    // 今日拣货数据

    @FieldDoc(
            description = "整单用时(秒)，为nul表示无需展示", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "整单用时(秒)")
    private Integer pickUseSeconds;

    @FieldDoc(
            description = "整单超时率(万分比)，为nul表示无需展示", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "整单超时率(万分比)")
    private Integer pickOvertimeRate;

    // 缺货订单

    @FieldDoc(
            description = "缺货订单待处理数量，为nul表示无需展示", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "缺货订单待处理数量")
    private Integer lackStockPendingCount;

    @FieldDoc(
            description = "缺货订单已处理数量，为nul表示无需展示", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "缺货订单已处理数量")
    private Integer lackStockHandledCount;

    // 备货订单

    @FieldDoc(
            description = "备货订单待备货数量，为nul表示无需展示", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "备货订单待备货数量")
    private Integer stockUpPendingCount;

    @FieldDoc(
            description = "备货订单待取货数量，为nul表示无需展示", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "备货订单待取货数量")
    private Integer stockUpToTakeCount;

    @FieldDoc(
            description = "备货订单已备货数量，为nul表示无需展示", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "备货订单已备货数量")
    private Integer stockUpDoneCount;

    // 拣货订单
    @FieldDoc(
            description = "拣货订单待接单数量，为nul表示无需展示", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "拣货订单待接单数量")
    private Integer pickUnTakenCount;

    @FieldDoc(
            description = "拣货订单待领取数量，为nul表示无需展示", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "拣货订单待领取数量")
    private Integer pickUnclaimedCount;

    @FieldDoc(
            description = "拣货订单待拣货数量，为nul表示无需展示", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "拣货订单待拣货数量")
    private Integer toPickCount;

    @FieldDoc(
            description = "拣货订单已拣货数量，为nul表示无需展示", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "拣货订单已拣货数量")
    private Integer pickedCount;

    // 合流订单

    @FieldDoc(
            description = "合流订单待合流数量，为nul表示无需展示", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "合流订单待合流数量")
    private Integer toMergeCount;

    @FieldDoc(
            description = "合流订单已合流数量，为nul表示无需展示", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "合流订单已合流数量")
    private Integer mergedCount;

}
