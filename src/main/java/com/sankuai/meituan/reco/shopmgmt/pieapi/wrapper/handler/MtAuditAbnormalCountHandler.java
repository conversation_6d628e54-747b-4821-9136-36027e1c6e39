package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.handler;


import com.meituan.linz.product.channel.EnhanceChannelType;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

import static com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ProjectConstants.MT_AUDIT_ABNORMAL_COUNT;

/**
 * <AUTHOR>
 * @since 2024/7/24
 */
@Component
public class MtAuditAbnormalCountHandler extends AbstractCountStoreSpuHandler {

    @Override
    public Integer getChannelId() {
        return EnhanceChannelType.MT.getChannelId();
    }

    @Override
    public String getCountCode() {
        return MT_AUDIT_ABNORMAL_COUNT;
    }

    @Override
    public List<String> getAbnormalCodes() {
        // 审核异常（含审核驳回和审核中）
        return Collections.singletonList("1002");
    }
}
