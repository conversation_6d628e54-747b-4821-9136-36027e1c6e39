package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "已拣货列表数据"
)
@Data
@ApiModel("已拣货列表数据")
public class WarehousePickCompleteModuleVO {

    @FieldDoc(
            description = "波次号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "波次号")
    private String waveId;

    @FieldDoc(
            description = "波次创建时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "波次创建时间")
    private String waveCreateTime;

    @FieldDoc(
            description = "已拣货任务列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "已拣货任务列表")
    private List<WarehousePickCompleteTaskModuleVO> waveTaskList;

    @FieldDoc(
            description = "货主信息列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "货主信息列表")
    private List<WarehouseGoodsOwnerInfoVO> goodsOwnerInfoList;

    @FieldDoc(
            description = "收货门店信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "收货门店信息")
    private List<WarehouseReceiveStoreInfoVO> receiveStoreInfoList;
}
