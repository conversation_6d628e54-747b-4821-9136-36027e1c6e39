package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.tenant;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.pay.constant.BillSourceEnum;
import com.sankuai.meituan.shangou.empower.tenantbill.dto.model.NewSignPayInfoDto;
import com.sankuai.meituan.shangou.empower.tenantbill.dto.model.ServiceRenewalDto;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;


@Data
@TypeDoc(
        description = "支付账单"
)
public class TenantBillServicePayRequest {

    @FieldDoc(description = "账单ID")
    private Long billId;

    @FieldDoc(description = "服务ID+计算金额，与账单ID二选一")
    @Deprecated
    private List<Long> serviceIdList;

    @FieldDoc(description = "支付金额")
    @Deprecated
    private Long calcAmount;

    @FieldDoc(description = "新签信息")
    public NewSignPayInfoVo newSignPayInfo;

    @FieldDoc(description = "续签信息")
    public RenewPayInfoVo renewPayInfo;

    @FieldDoc(description = "账单来源")
    public Integer billSource = BillSourceEnum.renewSign.getCode();

    @FieldDoc(description = "购买链路no")
    public String tradeNo;

    public NewSignPayInfoDto buildNewSignPayInfoDto() {
        return newSignPayInfo.buildNewSignPayInfoDto();
    }

    public Long parseCalcAmount() {

        if (renewPayInfo != null && renewPayInfo.getCalcAmount() != null) {
            return renewPayInfo.getCalcAmount();
        }
        if (calcAmount != null) {
            return calcAmount;
        }
        if (newSignPayInfo != null && newSignPayInfo.getCalcAmount() != null) {
            return newSignPayInfo.getCalcAmount();
        }
        return null;
    }

    public List<Long> parseServiceIdList() {

        if (renewPayInfo != null && CollectionUtils.isNotEmpty(renewPayInfo.getServiceIdList())){
            return renewPayInfo.getServiceIdList();
        }
        if (CollectionUtils.isNotEmpty(serviceIdList)) {
            return serviceIdList;
        }
        return null;
    }

    public List<ServiceRenewalDto> parseServiceRenewalList() {
        if (renewPayInfo != null && CollectionUtils.isNotEmpty(renewPayInfo.getServiceRenewalList())){
            return renewPayInfo.buildServiceRenewalDto();
        }
        return null;
    }
}
