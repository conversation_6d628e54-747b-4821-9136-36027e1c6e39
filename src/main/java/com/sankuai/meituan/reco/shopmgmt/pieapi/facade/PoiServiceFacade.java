package com.sankuai.meituan.reco.shopmgmt.pieapi.facade;

import com.google.common.collect.Maps;
import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.shangou.saas.tenant.thrift.ConfigThriftService;
import com.meituan.shangou.saas.tenant.thrift.PoiThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.constants.PoiFields;
import com.meituan.shangou.saas.tenant.thrift.common.enums.ConfigItemEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.config.ConfigDto;
import com.meituan.shangou.saas.tenant.thrift.dto.config.request.ConfigQueryRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.config.response.TenantConfigResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.request.PoiListQueryRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.request.PoiSearchRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiExtendInfoResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiListResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiMapResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiSearchResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ResponseHandler;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 *         Email <EMAIL>
 *         Date 2022/9/7 8:43 下午
 *         Description
 */
@Service
@Slf4j
public class PoiServiceFacade {

    @Autowired
    private PoiThriftService poiThriftService;

    @Resource
    private ConfigThriftService configThriftService;

    /**
     * 综合条件查询 POI 信息
     * 可通过 entityTypes 参数过滤需要的 poi 类型， entityTypes 为空时，默认只查询门店（entityType = 3）
     * 可通过 excludeShareableWarehouseBindingStore 参数 排除绑定共享前置仓的门店
     *
     * @param request PoiListQueryRequest
     * @return List<PoiInfoDto>
     */
    public List<PoiInfoDto> queryPoiListWithCondition(PoiListQueryRequest request) {
        PoiListResponse poiListResponse = poiThriftService.queryPoiInfoListByCondition(request);

        ResponseHandler.checkResponseAndStatus(poiListResponse, r -> r.getStatus().getCode(),
                r -> r.getStatus().getMessage(), ResultCode.FAIL);

        return poiListResponse.getPoiList();

    }

    public PoiInfoDto queryPoiInfo(Long tenantId, Long poiId) {
        PoiExtendInfoResponse response = poiThriftService.queryTenantPoiExtendInfoById(tenantId, poiId);
        ResponseHandler.checkResponseAndStatus(response, r -> r.getStatus().getCode(),
                r -> r.getStatus().getMessage(), ResultCode.FAIL);

        return response.getPoiInfo();
    }
    public Map<Long, String> queryShopId2ErpShopCode(Long tenantId, List<Long> shopIdList) {
        if (CollectionUtils.isEmpty(shopIdList) || Objects.isNull(tenantId)) {
            return Maps.newHashMap();
        }
        boolean hasErp = isErpStore(tenantId);
        log.info("tenantId :{}, hasErp:{}", tenantId, hasErp);
        if (!hasErp) {
            return Maps.newHashMap();
        }
        PoiSearchRequest request = new PoiSearchRequest();
        request.setTenantId(tenantId);
        request.setFields(new HashSet<>(Arrays.asList(PoiFields.POI_ID, PoiFields.OUT_POI_ID)));
        request.setPoiIdList(shopIdList);
        PoiSearchResponse response = poiThriftService.search(request);
        if (Objects.isNull(response) || CollectionUtils.isEmpty(response.getPoiList())) {
            return Maps.newHashMap();
        }
        return response.getPoiList().stream()
                .collect(Collectors.toMap(PoiInfoDto::getPoiId, PoiInfoDto::getOutPoiId, (v1, v2) -> v2));
    }

    public boolean isErpStore(Long tenantId) {
        ConfigQueryRequest configQueryRequest = new ConfigQueryRequest();
        configQueryRequest.setTenantId(tenantId);
        configQueryRequest.setSubjectId(tenantId);
        configQueryRequest.setConfigId(ConfigItemEnum.HAS_ERP.getKey());
        TenantConfigResponse tenantConfigResponse = configThriftService.queryTenantConfig(configQueryRequest);
        log.info("queryTenantConfig tenantId:{},resp:{}", tenantId, tenantConfigResponse);
        if (Objects.nonNull(tenantConfigResponse) && Objects.nonNull(tenantConfigResponse.getStatus())
                && ResultCode.SUCCESS.getCode() == tenantConfigResponse.getStatus().getCode()) {
            ConfigDto config = tenantConfigResponse.getConfig();
            if (Objects.nonNull(config) && StringUtils.isNotBlank(config.getConfigContent())) {
                String configContent = config.getConfigContent();
                return ConfigItemEnum.HAS_ERP.isMainConfigYesStr(configContent);
            }
        }
        return Boolean.FALSE;
    }

    public Map<Long, Boolean> isOutRepository(Long tenantId, List<Long> shopIdList) {
        try {
            PoiMapResponse response = poiThriftService.queryTenantPoiInfoMapByPoiIds(shopIdList, tenantId);
            log.info("查询门店信息, tenantId:{}, poiIds:{}, response:{}", tenantId, shopIdList,
                    JacksonUtils.toJson(response));
            if (Objects.nonNull(response) && Objects.nonNull(response.getStatus()) && response.getStatus().isSuccess()
                    && Objects.nonNull(response.getPoiInfoMap())) {
                Map<Long, PoiInfoDto> poiInfoMap = response.getPoiInfoMap();
                return poiInfoMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey,
                        entry -> Optional.ofNullable(entry.getValue().getOutRepository()).orElse(false)));
            }
        } catch (Exception e) {
            log.error("查询门店信息失败， tenantId:{}, poiIds:{}", tenantId, shopIdList, e);
        }
        return Collections.emptyMap();
    }

    public List<PoiInfoDto> queryCurrentTenantPoiList() {
        Long tenantId = ApiMethodParamThreadLocal.getInstance().get().getUser().getTenantId();
        PoiListResponse poiListResponse = poiThriftService.queryTenantPoiList(tenantId);
        List<PoiInfoDto> poiList = poiListResponse.getPoiList();
        return ListUtils.emptyIfNull(poiList);
    }
}
