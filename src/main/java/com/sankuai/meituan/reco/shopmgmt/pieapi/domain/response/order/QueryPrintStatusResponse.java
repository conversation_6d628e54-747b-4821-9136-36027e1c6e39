package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.pickselect.thrift.print.PrintDeviceInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/7/11
 * desc: 查询打印状态响应
 */
@TypeDoc(
        description = "查询打印状态响应"
)
@ApiModel("查询打印状态响应")
@Data
public class QueryPrintStatusResponse {

    @FieldDoc(
            description = "打印状态 0-准备打印 10-打印中 20-打印成功 30-打印超时 99-第三方服务不可用 999-未知", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "打印状态 0-准备打印 10-打印中 20-打印成功 30-打印超时 99-第三方服务不可用 999-未知", required = true)
    public Integer printStatus;

    @FieldDoc(
            description = "打印消息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "打印消息", required = true)
    public String msg;

    @FieldDoc(
            description = "打印机设备信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "打印机设备信息", required = true)
    public PrintDeviceInfo deviceInfo;
}
