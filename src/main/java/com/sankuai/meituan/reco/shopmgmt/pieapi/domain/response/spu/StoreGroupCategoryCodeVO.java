package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.TenantStoreCategoryDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.category.StoreGroupCategoryDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreGroupCategoryCodeDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class StoreGroupCategoryCodeVO {

    @FieldDoc(
            description = "分组ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "分组ID", required = true)
    private Integer storeGroupId;

    @FieldDoc(
            description = "店内分类编码列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "店内分类编码列表", required = true)
    private List<Long> categoryCodes;

    public static StoreGroupCategoryCodeVO of(StoreGroupCategoryDTO storeGroupCategoryCodeDTO) {
        StoreGroupCategoryCodeVO storeGroupMerchantStoreCategoryVo = new StoreGroupCategoryCodeVO();
        storeGroupMerchantStoreCategoryVo.setStoreGroupId(storeGroupCategoryCodeDTO.getStoreGroupId());
        if (CollectionUtils.isNotEmpty(storeGroupCategoryCodeDTO.getStoreCategoryList())) {
            storeGroupMerchantStoreCategoryVo.setCategoryCodes(storeGroupCategoryCodeDTO.getStoreCategoryList().stream()
                    .map(TenantStoreCategoryDTO::getCategoryId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList()));
        }
        return storeGroupMerchantStoreCategoryVo;
    }

    public static StoreGroupCategoryCodeVO ofBiz(StoreGroupCategoryCodeDTO storeGroupCategoryCodeDTO) {
        StoreGroupCategoryCodeVO storeGroupMerchantStoreCategoryVo = new StoreGroupCategoryCodeVO();
        storeGroupMerchantStoreCategoryVo.setStoreGroupId(storeGroupCategoryCodeDTO.getStoreGroupId());
        storeGroupMerchantStoreCategoryVo.setCategoryCodes(storeGroupCategoryCodeDTO.getCategoryCodes());
        return storeGroupMerchantStoreCategoryVo;
    }

    public StoreGroupCategoryDTO toDTO() {
        StoreGroupCategoryDTO storeGroupCategoryDTO = new StoreGroupCategoryDTO();
        storeGroupCategoryDTO.setStoreGroupId(storeGroupId);
        if (CollectionUtils.isNotEmpty(categoryCodes)) {
            storeGroupCategoryDTO.setStoreCategoryList(Fun.map(categoryCodes, categoryCode -> {
                TenantStoreCategoryDTO storeCategoryDTO = new TenantStoreCategoryDTO();
                storeCategoryDTO.setCategoryId(categoryCode);
                return storeCategoryDTO;
            }));
        }
        return storeGroupCategoryDTO;
    }

    public StoreGroupCategoryCodeDTO toBizDTO() {
        StoreGroupCategoryCodeDTO storeGroupCategoryDTO = new StoreGroupCategoryCodeDTO();
        storeGroupCategoryDTO.setStoreGroupId(storeGroupId);
        storeGroupCategoryDTO.setCategoryCodes(categoryCodes);
        return storeGroupCategoryDTO;
    }

}
