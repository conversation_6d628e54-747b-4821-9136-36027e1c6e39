package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.delivery;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@TypeDoc(
        description = "预发配送响应体"
)
@Data
@ApiModel("预发配送响应体")
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryPreLaunchResponse {

    @FieldDoc(
            description = "配送渠道列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "配送渠道列表", required = true)
    private List<DeliveryChannelPreLaunchResponse> deliveryChannelList;
}
