package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.wavepick.converter;

import com.sankuai.meituan.reco.pickselect.thrift.wave.request.WarehouseQuerySeedCompleteRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick.WarehouseSeedCompleteQueryRequest;
import org.mapstruct.Mapper;

/**
 * @Description:
 * @Author: zhangjian155
 * @Date: 2022/8/25 18:45
 */
@Mapper(componentModel = "spring")
public abstract class WarehouseSeedCompleteQueryRequestConverter {
    public abstract WarehouseQuerySeedCompleteRequest convert2ThriftRequest(WarehouseSeedCompleteQueryRequest request
            , Long accountId, Long storeId);
}
