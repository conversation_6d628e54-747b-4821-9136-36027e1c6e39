package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.osw;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.infra.osw.api.poi.TPoiService;
import com.sankuai.shangou.infra.osw.api.poi.dto.request.QueryOperatePoiRequest;
import com.sankuai.shangou.infra.osw.api.poi.dto.response.BusinessPoiDTO;
import com.sankuai.shangou.infra.osw.api.poi.store.dto.request.WarehouseIdsRequest;
import com.sankuai.shangou.infra.osw.api.poi.warehouse.TWarehouseService;
import com.sankuai.shangou.infra.osw.api.poi.warehouse.dto.response.WarehouseDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/12/20 17:38
 **/
@Rhino
@Slf4j
public class OSWServiceWrapper {

    private static final Long ALL_WAREHOUSE_ID = -1L;

    @Resource
    private TPoiService oswPoiService;
    @Resource
    private TWarehouseService oswWarehouseService;

    @Degrade(rhinoKey = "OSWServiceWrapper.queryOperatePoiByPoiId", fallBackMethod = "queryOperatePoiByPoiIdFallBack", timeoutInMilliseconds = 2000)
    public BusinessPoiDTO queryOperatePoiByPoiId(Long tenantId, Long storeId, Long accountId) {
        QueryOperatePoiRequest request = new QueryOperatePoiRequest();
        request.setPoiId(storeId);
        request.setTenantId(tenantId);
        request.setAccountId(accountId);
        TResult<BusinessPoiDTO> result = null;
        try {
            log.info("start invoke OSWServiceWrapper.queryOperatePoiByPoiId, request: {}", request);
            result = oswPoiService.queryOperatePoiByPoiId(request);
            log.info("end invoke OSWServiceWrapper.queryOperatePoiByPoiId, response: {}", result);
        } catch (Exception e) {
            log.error("查询店仓基础信息失败", e);
            throw new ThirdPartyException("查询店仓基础信息失败");
        }

        if (!result.isSuccess()) {
            throw new ThirdPartyException("查询店仓基础信息失败");
        }

        return result.getData();
    }

    public BusinessPoiDTO queryOperatePoiByPoiIdFallBack(Long tenantId, Long storeId, Long accountId) {
        log.warn("OSWServiceWrapper.queryOperatePoiByPoiId 发生降级");
        return null;
    }

    @Degrade(rhinoKey = "OSWServiceWrapper.queryWarehouseOrgCodes",
            fallBackMethod = "queryWarehouseOrgCodesFallBack",
            timeoutInMilliseconds = 2000)
    @MethodLog(logResponse = true, logRequest = true)
    public Map<Long, String> queryWarehouseOrgCodes(Long tenantId, List<Long> warehouseIds) {
        if(CollectionUtils.isEmpty(warehouseIds) || (warehouseIds.remove(ALL_WAREHOUSE_ID) && CollectionUtils.isEmpty(warehouseIds))) {
            return new HashMap<>();
        }

        try {
            WarehouseIdsRequest request = new WarehouseIdsRequest();
            request.setTenantId(tenantId);

            request.setWarehouseIds(warehouseIds);
            TResult<List<WarehouseDTO>> response = oswWarehouseService.batchQueryWarehouseById(request);
            if (Objects.isNull(response) || !response.isSuccess()) {
                throw new ThirdPartyException("oswWarehouseService.batchQueryBizWarehouseByIds response fail: " + response);
            }
            return response.getData().stream()
                    .collect(Collectors.toMap(WarehouseDTO::getId, it -> String.valueOf(it.getOrgId()), (o, n) -> n));

        } catch (Exception e) {
            throw new ThirdPartyException(String.format("牵牛花仓-组织ID转换，通过仓ID查询组织ID失败, 仓id:%s", warehouseIds));
        }
    }

    public Map<Long, String> queryWarehouseOrgCodesFallBack(Long tenantId, List<Long> warehouseIds) {
        log.warn("OSWServiceWrapper.queryWarehouseOrgCodes 发生降级");
        return null;
    }


    @Degrade(rhinoKey = "OSWServiceWrapper.queryWarehouseInfoById",
            fallBackMethod = "queryWarehouseInfoByIdFallBack",
            timeoutInMilliseconds = 2000)
    @MethodLog(logResponse = true, logRequest = true)
    public WarehouseDTO queryWarehouseInfoById(Long tenantId, Long warehouseId) {
        WarehouseIdsRequest request = new WarehouseIdsRequest();
        request.setTenantId(tenantId);
        request.setWarehouseIds(Collections.singletonList(warehouseId));
        TResult<List<WarehouseDTO>> response = null;
        try {
            response = oswWarehouseService.batchQueryWarehouseById(request);
            log.info("end invoke oswWarehouseService.batchQueryWarehouseById, request: {}, response: {}", request, response);
        } catch (Exception e) {
            throw new ThirdPartyException("查询店仓信息失败");
        }
        if (!response.isSuccess() || CollectionUtils.isEmpty(response.getData())) {
            throw new BizException("查询店仓信息失败");
        }
        return response.getData().get(0);
    }

    public WarehouseDTO queryWarehouseInfoByIdFallBack(Long tenantId, Long warehouseId) {
        log.warn("OSWServiceWrapper.queryWarehouseInfoById 发生降级");
        throw new ThirdPartyException("查询店仓信息失败");
    }
}
