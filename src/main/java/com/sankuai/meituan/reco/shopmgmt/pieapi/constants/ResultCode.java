package com.sankuai.meituan.reco.shopmgmt.pieapi.constants;

import com.google.common.collect.Maps;
import com.sankuai.meituan.util.ConfigUtilAdapter;

import java.util.Map;

public enum ResultCode {
    SUCCESS(0, ""),
    FAIL(1, ""),
    PARAM_ERR(2, "参数错误"),
    LOCK_FAIL(3, "有人正在修改数据,请稍后重试"),
    VALVE_EFFECT(4, "操作过于频繁"),
    TASK_NOT_WEIGHT(300, "订单包含生鲜商品必须传重量"),
    TASK_NOT_FOUND_ERR(100, "状态未找到"),
    UNAUTHORIZED(401, "未登录，请登录后再试"),
    FORBIDDEN(402, "认证不通过"),
    NOT_FOUND(404, "接口不存在"),
    PHONE_UNBOUND(405, "您美团账号绑定的手机号已发生变更，请用新绑定的手机号登录"),
    CONCURRENT_LOGIN(406, "账号正在登录，请稍后重试"),
    LOGIN_CERTIFICATION_ERROR(407, "用户名或者密码错误"),
    LOGIN_TENANT_INFO_ERROR(408, "商家数据异常，请重新登录"),
    AUTHORIZE_ERROR(409, "没有权限"),
    INTERNAL_SERVER_ERROR(500, "不好意思出错了，请稍候再试"),
    CONCURRENT_OPERATE(444, "合流失败，请重试"),
    OPERATING_FAIL(445, "操作失败，请重刷"),
    VERSION_TOO_LOW(450, "当前版本过低，请联系管理人员升级至最新版本"),
    EMPLOYEE_UNBIND(555, "没有该员工信息,请确认手机号是否同步系统"),
    CHECK_PARAM_ERR(100, "参数错误"),
    RETRY_INNER_FAIL(501, "网络异常，请重试"),
    RETRY_LOGIN_IN(502, "拣货作业模式已切换，请按照当前作业模式流程领取任务"),
    EBASE_EMPLOYEE_NOT_BIND(600, "没有该员工信息,请确认手机号是否同步系统"),
    EBASE_EMPLOYEE_DISABLED(601, "员工被禁用"),
    EBASE_EMPLOYEE_NO_AUTH(602, "员工没有权限"),
    EBASE_EMPLOYEE_NOT_EXIST(603, "员工不存在"),
    EBASE_EMPLOYEE_MOBILE_EXIST(604, "手机号：{0}已被使用，请重新输入"),
    EBASE_EMPLOYEE_NO_EXIST(605, "员工工号：{0}，已被使用，请重新输入"),
    EBASE_EMPLOYEE_UPDATED(606, "员工信息已经被修改，请重新编辑"),
    EBASE_EMPLOYEE_UNCOMPLETED_DEPT_TASK(607, "员工存在部门：{0}未完成的任务，请选完成任务再转移部门"),
    EBASE_EMPLOYEE_UNCOMPLETED_TASK(608, "存在未完成的任务，请让员工完成任务再停用员工"),
    EBASE_EMPLOYEE_DISABLED_DEPARTMENT(609, "部门已经被禁用：{0}"),
    EBASE_EMPLOYEE_MOBILE_BOUND_ANOTHER_USER(610, "手机号：{0}已被使用，请重新输入或禁用该用户"),
    STORAGE_BIN_WAIT_TO_SELECT_EXIST(700, "该门店下有未完成的待拣货任务，请完成所有待拣货的任务后再试！"),
    STORE_MANAGEMENT_EMPOWER_TASK_NO_SUCH_GOODS(800, "没有该商品"),
    STOCK_OPERATE_LOGIC_FAIL(900, "库存操作失败"),
    STOCK_OPERATE_LACK(901, "库存缺货"),
    NO_ACCOUNT_RELATED_STORE(1001, "该账号未绑定门店"),
    NOT_SUPPORT_FULL_STORE_MODE(2000, "当前功能模式不支持全门店模式"),
    SECURITY_DEPOSIT_ERROR(3000,"保证金支付出错"),
    ELEC_CONTRACT_ERROR(4000, "电子协议相关功能出错"),
    CONFIRM_LETTER_ERROR(4100, "电子协议确认函相关功能出错"),
    CONFIRM_LETTER_BUSINESS_EXCEPTION(4101, "电子协议确认确认函业务异常"),
    BOOTH_ERROR(5001, "摊位相关功能出错"),
    BOOTH_SEARCH_CODE_ERROR(6006, "摊位检索码错误"),
    SEARCH_CODE_WITH_NON_BOOTH(6008, "摊位检索码未检索到相应摊位"),
    BOOTH_WITH_NON_ONSITE_ALIPAY_ACCOUNT(6009, "摊位未维护现结支付宝账户"),
    BOOTH_PAY_CREATE_TRADE_EXCEPTION(6010, "外采现结生单异常"),
    CONTRACT_SINGER_ERROR(6011, "获取协议签署人信息失败"),

    MENU_CODE_NOT_EXIST(7000, "菜单code不存在"),

    RIDER_PICK_FINISH_PICK_FAIL(8000, "骑手操作拣货完成失败"),
    BOOKING_ORDER_IMMEDIATE_DELIVERY_FAIL(8002,"预订单发起立即履约失败"),
    NOT_APPROVE_IMMEDIATELY_DELIVERY(8003, "目前暂不支持提前履约功能"),
    NEED_TAKE_PHOTO_BEFORE_FINISH_PICKING(8004,"按照门店作业规范，请先对出库商品进行拍照留存"),
    RIDER_PICK_ALREADY_COMPLETED(8005, "当前拣货任务已经通过美团外卖商家端或百川其他入口完成，请确认商品无误后，点击下方按钮继续进行配送"),
    RIDER_PICK_ALREADY_CANCELED(8006, "拣货任务已取消，不支持进行扫码拣货，请退出页面刷新或联系管理员"),


    EXCEED_SHOULD_PICK_COUNT(30006,"该订单已部分退款，请按最新商品数量出库"),

    SN_CODE_NOT_VALID(30010, "sn码无效"),
    TENANT_BILL_SERVICE_PAY_AMOUNT_IS_NULL(30011, "支付金额不能为空"),
    TENANT_BILL_SERVICE_PAY_PARAM_INVALID(30012, "支付参数错误"),

    // 40000+移动端波次拣货相关
    WAREHOUSE_CONTAINER_ALREADY_OCCUPIED(40001, "移动端波次拣货，波次任务对应的容器已被占用"),
    WAREHOUSE_CONTAINER_UNBOUND_ONLY_ONE(40002, "移动端波次拣货，波次任务当前仅关联了一个容器，且就是要解绑的容器，不允许解绑"),
    WAREHOUSE_CONTAINER_UNBOUND_PICK_DONE(40003, "移动端波次拣货，波次任务为已完成拣货状态，不允许解绑"),
    WAREHOUSE_CONTAINER_BOUND_SUCCESS(0, "容器绑定成功"),
    WAREHOUSE_CONTAINER_UNBOUND_SUCCESS(0, "容器解绑成功"),

    //异常任务相关
    STOCK_OUT_STOCK_LACK(20000099, "商品库存数量不足,请处理异常任务后再出库"),

    //配送相关
    PICK_TASK_ALREADY_BE_ACCEPTED(20000100, "拣货任务已被领取，领配送任务成功"),

    ;

    public final int code;
    public final String defaultMessage;
    static Map<Integer, ResultCode> maps = Maps.newHashMap();

    private ResultCode(int code, String defaultMessage) {
        this.code = code;
        this.defaultMessage = defaultMessage;
    }

    public String getErrorMessage() {
        return ConfigUtilAdapter.getString("resultCode." + this.code, this.defaultMessage);
    }

    public static ResultCode fromCode(int code) {
        return (ResultCode) maps.get(code);
    }

    public String getDefaultMessage() {
        return this.defaultMessage;
    }

    public int getCode() {
        return this.code;
    }

    static {
        ResultCode[] var0 = values();
        int var1 = var0.length;

        for (int var2 = 0; var2 < var1; ++var2) {
            ResultCode resultCode = var0[var2];
            maps.put(resultCode.code, resultCode);
        }

    }
}
