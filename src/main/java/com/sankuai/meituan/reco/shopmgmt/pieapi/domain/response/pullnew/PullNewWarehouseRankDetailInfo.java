package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/9/22
 */
@TypeDoc(
        description = "地推排行榜详情"
)
@Data
@ApiModel("地推排行榜详情")
@NoArgsConstructor
@AllArgsConstructor
public class PullNewWarehouseRankDetailInfo {
    private String text;

    public String itemRank;

    public String itemName;
    public String itemValue;

}
