package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.ChannelSpecAttrRequest;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.dto.ChannelSpecialAttrDTO;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/7/1 17:54
 **/
@TypeDoc(
        name = "渠道特殊属性VO对象",
        description = "渠道特殊属性VO对象"
)
@Data
@ToString
@EqualsAndHashCode
public class ChannelSpecialAttrVo {

    @FieldDoc(
            description = "渠道ID"
    )
    private Integer channelId;

    @FieldDoc(
            description = "属性ID"
    )
    private Long attrId;

    @FieldDoc(
            description = "属性名称"
    )
    private String attrName;

    @FieldDoc(
            description = "属性值列表"
    )
    private List<ChannelSpecAttrValueVo> attrValueList;

    public static ChannelSpecialAttrVo build(ChannelSpecialAttrDTO attrDTO){
        ChannelSpecialAttrVo attrVo = new ChannelSpecialAttrVo();
        attrVo.setChannelId(attrDTO.getChannelId());
        attrVo.setAttrId(attrDTO.getAttrId());
        attrVo.setAttrName(attrDTO.getAttrName());
        attrVo.setAttrValueList(ChannelSpecAttrValueVo.build(attrDTO.getAttrValueList()));
        return attrVo;
    }

    public static ChannelSpecialAttrVo buildDefault(ChannelSpecAttrRequest request){
        ChannelSpecialAttrVo attrVo = new ChannelSpecialAttrVo();
        attrVo.setChannelId(request.getChannelId());
        attrVo.setAttrId(request.getAttrId());
        return attrVo;
    }

}
