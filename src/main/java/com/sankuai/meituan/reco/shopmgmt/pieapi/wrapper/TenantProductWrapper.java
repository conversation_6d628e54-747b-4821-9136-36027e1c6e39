package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ChannelTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.StandardSkuQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.ProductQuery4FastCreateSpuRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.QueryProductByUpcRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.TaxRateOptionQueryVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.PageQueryStandardSkuResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.QueryProductByUpcResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.SimpleSkuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.BaseSearchVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.WeightUnitConfigVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.TaxRateOptionQueryTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.JacksonUtilsV2;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.BaseSearchDTO;
import com.meituan.linz.thrift.response.Status;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.PageQueryStandardProductRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.Query4FastCreateSpuRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.request.QueryProductFromSpOrTenantRequest;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.PageQueryStandardProductResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.Query4FastCreateSpuResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.QueryProductFromSpOrTenantResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.TaxRateOptionQueryResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.WeightUnitOptionResponse;
import com.sankuai.meituan.shangou.empower.productbiz.client.service.TaxRateThriftService;
import com.sankuai.meituan.shangou.empower.productbiz.client.service.TenantSpuBizThriftService;
import com.sankuai.meituan.shangou.platform.empower.product.client.response.QueryPurchaseUnitResponse;
import com.sankuai.meituan.shangou.platform.empower.product.client.service.MerchantSpuThriftService;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2021-03-15 16:11
 * @Description: product biz新wrapper
 */
@Service
@Slf4j
public class TenantProductWrapper {

    @Resource
    private TenantSpuBizThriftService tenantSpuBizThriftService;

    @Autowired
    private MerchantSpuThriftService merchantSpuThriftService;

    @Autowired
    private TaxRateThriftService taxRateThriftService;



    public CommonResponse<QueryProductByUpcResponse> queryByUpc(QueryProductByUpcRequest queryProductByUpcRequest) {
        CommonResponse<QueryProductByUpcResponse> commonResponse = new CommonResponse<>();
        QueryProductByUpcResponse queryProductByUpcResponse = new QueryProductByUpcResponse();
        commonResponse.setData(queryProductByUpcResponse);
        commonResponse.setCode(ResultCode.SUCCESS.getCode());
        QueryProductFromSpOrTenantRequest request = new QueryProductFromSpOrTenantRequest();
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        request.setTenantId(user.getTenantId());
        request.setChannelId(ChannelTypeEnum.MEITUAN.getChannelId());
        request.setIsScanQuery(queryProductByUpcRequest.getNeedQuerySP());
        request.setNameLike(queryProductByUpcRequest.getName());
        request.setNeedTenantAudit(queryProductByUpcRequest.getNeedTenantAudit());
        request.setUpc(queryProductByUpcRequest.getUpc());
        request.setStoreId(queryProductByUpcRequest.getStoreId());
        try {
            log.info("tenantSpuBizThriftService.queryProductFromSpOrTenant,request:{}", request);
            QueryProductFromSpOrTenantResponse response = tenantSpuBizThriftService.queryProductFromSpOrTenant(request);
            log.info("tenantSpuBizThriftService.queryProductFromSpOrTenant,response:{}", response);

            if (Objects.isNull(response) || Objects.isNull(response.getStatus())) {
                commonResponse.setCode(ResultCode.FAIL.getCode());
                return commonResponse;
            }
            if (response.getStatus().getCode() == ResultCode.FAIL.getCode()) {
                commonResponse.setCode(ResultCode.FAIL.getCode());
                commonResponse.setMessage(response.getStatus().getMsg());
                return commonResponse;
            }

            if (Objects.nonNull(response.getSimpleSpuDTO())) {
                SimpleSkuVO simpleSkuVO = SimpleSkuVO.buildVO(response.getSimpleSpuDTO());
                queryProductByUpcResponse.setSimpleSkuVO(simpleSkuVO);
            }
            queryProductByUpcResponse.setBizCode(response.getStatus().getCode());
        } catch (Exception e) {
            log.error("queryByUpc异常,queryProductByUpcRequest:{}", queryProductByUpcRequest, e);
            commonResponse.setCode(ResultCode.FAIL.getCode());
        }
        return commonResponse;

    }


    public CommonResponse<PageQueryStandardSkuResponse> pageQuerySp(StandardSkuQueryRequest standardSkuQueryRequest) {
        CommonResponse<PageQueryStandardSkuResponse> commonResponse = new CommonResponse<>();
        commonResponse.setCode(ResultCode.SUCCESS.getCode());
        PageQueryStandardSkuResponse pageQueryStandardSkuResponse = new PageQueryStandardSkuResponse();
        commonResponse.setData(pageQueryStandardSkuResponse);
        if (StringUtils.isBlank(standardSkuQueryRequest.getName())) {
            pageQueryStandardSkuResponse.setPageInfo(PageInfoVO.initPageInfoVO(standardSkuQueryRequest.getPage(), standardSkuQueryRequest.getPageSize()));
            return commonResponse;
        }
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        PageQueryStandardProductRequest request = new PageQueryStandardProductRequest();
        request.setNameLike(standardSkuQueryRequest.getName());
        request.setPage(standardSkuQueryRequest.getPage());
        request.setPageSize(standardSkuQueryRequest.getPageSize());
        request.setTenantId(user.getTenantId());
        request.setChannelId(ChannelTypeEnum.MEITUAN.getChannelId());
        try {
            log.info("tenantSpuBizThriftService.pageQueryStandardProduct,request:{}", request);
            PageQueryStandardProductResponse response = tenantSpuBizThriftService.pageQueryStandardProduct(request);
            log.info("tenantSpuBizThriftService.pageQueryStandardProduct,response:{}", response);
            if (Objects.isNull(response) || Objects.isNull(response.getStatus())) {
                commonResponse.setCode(ResultCode.FAIL.getCode());
                return commonResponse;
            }
            if (response.getStatus().getCode() == ResultCode.FAIL.getCode()) {
                commonResponse.setCode(ResultCode.FAIL.getCode());
                commonResponse.setMessage(response.getStatus().getMsg());
                return commonResponse;
            }
            pageQueryStandardSkuResponse.setPageInfo(PageInfoVO.convert(response.getPageInfoDTO()));
            pageQueryStandardSkuResponse.setSimpleSkuVOList(SimpleSkuVO.buildVOList(response.getStandardProductDTOList()));

        } catch (Exception e) {
            log.error("pageQuerySp异常,PageQueryStandardProductRequest:{}", request, e);
            commonResponse.setCode(ResultCode.FAIL.getCode());
        }
        return commonResponse;
    }

    /**
     * 快捷建品商品搜索<br>
     * 支持商品池和标品库搜索
     *
     * @param request
     * @return
     */
    public List<BaseSearchVO> queryForFastCreate(ProductQuery4FastCreateSpuRequest request) {
        Query4FastCreateSpuRequest command = new Query4FastCreateSpuRequest();
        command.setKeyword(request.getKeyword());
        command.setStoreId(request.getStoreId());
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        command.setTenantId(user.getTenantId());
        command.setChannelId(ChannelTypeEnum.MEITUAN.getChannelId());
        command.setMerchandiseCategory(request.getMerchandiseCategory());

        Query4FastCreateSpuResponse response = tenantSpuBizThriftService.queryProduct4FastCreateSpu(command);
        Assert.state(response != null, "商品搜索为空");
        Status status = response.getStatus();
        Assert.state(status != null, "商品搜索为空");
        Assert.state(status.getCode() == ResultCode.SUCCESS.getCode(), status.getMsg());

        String productListContent = response.getProductList();
        if (StringUtils.isBlank(productListContent)) {
            return Collections.emptyList();
        }

        List<BaseSearchDTO> productList = JacksonUtilsV2.fromJson(productListContent,
                new TypeReference<List<BaseSearchDTO>>() {});
        if (CollectionUtils.isEmpty(productList)) {
            return Collections.emptyList();
        }

        return productList.stream()
                .map(BaseSearchVO::convert)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public CommonResponse<List<WeightUnitConfigVO>> queryWeightUnitOptionList() {
        WeightUnitOptionResponse weightUnitOptionResponse = tenantSpuBizThriftService.queryWeightUnitOptionList();
        if (Objects.isNull(weightUnitOptionResponse) || Objects.isNull(weightUnitOptionResponse.getStatus())) {
            return CommonResponse.fail2(ResultCode.FAIL);
        }
        if (!Status.SUCCESS_CODE.equals(weightUnitOptionResponse.getStatus().getCode())) {
            return CommonResponse.fail2(ResultCode.FAIL);
        }
        if (CollectionUtils.isEmpty(weightUnitOptionResponse.getWeightUnitOptionList())) {
            return CommonResponse.success(Collections.emptyList());
        }

        List<WeightUnitConfigVO> weightUnitConfigList = weightUnitOptionResponse.getWeightUnitOptionList().stream()
                .map(WeightUnitConfigVO::ofDTO)
                .collect(Collectors.toList());
        return CommonResponse.success(weightUnitConfigList);
    }

    public CommonResponse<List<String>> queryPurchaseUnitList() {
        try {
            QueryPurchaseUnitResponse response = merchantSpuThriftService.queryPurchaseUnitList();
            if (Objects.isNull(response) || Objects.isNull(response.getStatus())) {
                return CommonResponse.fail2(ResultCode.FAIL);
            }
            if (!Status.SUCCESS_CODE.equals(response.getStatus().getCode())) {
                return CommonResponse.fail2(ResultCode.FAIL);
            }

            return new CommonResponse<>(response.getStatus().getCode(), response.getStatus().getMsg(), response.getUnitNameList());
        } catch (Exception e) {
            log.error("查询商品箱规信息异常,", e);
            return CommonResponse.fail2(ResultCode.INTERNAL_SERVER_ERROR);
        }
    }

    public CommonResponse<TaxRateOptionQueryVO> queryTaxRateOptionList(Integer queryType) {
        TaxRateOptionQueryResponse response = taxRateThriftService.queryOptionList();
        if (response == null
                || response.getStatus() == null
                || !Status.SUCCESS_CODE.equals(response.getStatus().getCode())) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "查询税率失败");
        }
        TaxRateOptionQueryVO queryVO = new TaxRateOptionQueryVO();
        if (Objects.equals(queryType, TaxRateOptionQueryTypeEnum.IMPORT_RATE.getCode())) {
            queryVO.setTaxRateOptionList(response.getImportRateOptionList());
        } else if (Objects.equals(queryType, TaxRateOptionQueryTypeEnum.EXPORT_RATE.getCode())) {
            queryVO.setTaxRateOptionList(response.getExportRateOptionList());
        } else {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "不能识别的查询类型");
        }
        return CommonResponse.success(queryVO);
    }
}
