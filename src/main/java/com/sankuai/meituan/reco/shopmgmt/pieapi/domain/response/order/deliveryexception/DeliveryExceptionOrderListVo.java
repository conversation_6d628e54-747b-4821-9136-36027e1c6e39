package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.deliveryexception;

import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import com.meituan.shangou.saas.order.platform.enums.DistributeStatusEnum;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2020-02-18 16:11
 * @Description:
 */
@TypeDoc(
        description = "配送异常订单列表返回值"
)
@Data
public class DeliveryExceptionOrderListVo {

    @FieldDoc(
            description = "是否有更多数据"
    )
    private Boolean hasMore = true;

    @FieldDoc(
            description = "总记录数"
    )
    private Integer totalRrd;

    @FieldDoc(
            description = "订单列表"
    )
    private List<DeliveryExceptionOrderVo> orderList;


    @TypeDoc(
            description = "配送异常订单返回值"
    )
    @Data
    public static class DeliveryExceptionOrderVo {
        @FieldDoc(
                description = "内部订单ID"
        )
        private Long orderId;

        @FieldDoc(
                description = "渠道ID"
        )
        private Integer channelId;

        @FieldDoc(
                description = "渠道订单号"
        )
        private String channelOrderId;

        @FieldDoc(
                description = "租户ID"
        )
        private Long tenantId;

        /**
         * @see DistributeStatusEnum
         */
        @FieldDoc(
                description = "配送状态"
        )
        private Integer distributeStatus;

        @FieldDoc(
                description = "配送状态名称"
        )
        private String distributeStatusName;

        @FieldDoc(
                description = "配送状态的操作时间"
        )
        private Long distributeStatusTime;

        @FieldDoc(
                description = "配送异常描述"
        )
        private String exceptionDescription;

        @FieldDoc(
                description = "预计送达时间"
        )
        private Long expectedDeliveryTime;

        @FieldDoc(
                description = "下单时间"
        )
        private Long orderTime;

        @FieldDoc(
                description = "用户名称"
        )
        private String userName;

        @FieldDoc(
                description = "用户电话"
        )
        private String userPhone;

        @FieldDoc(
                description = "骑手名称"
        )
        private String riderName;

        @FieldDoc(
                description = "骑手电话"
        )
        private String riderPhone;

        @FieldDoc(
                description = "订单序号"
        )
        private String serialNum;

        @FieldDoc(
                description = "操作列表(1取消2转自配送3已送出4联系骑手5知道了6重发配送)"
        )
        private List<Integer> operationList = Lists.newArrayList();
    }
}

