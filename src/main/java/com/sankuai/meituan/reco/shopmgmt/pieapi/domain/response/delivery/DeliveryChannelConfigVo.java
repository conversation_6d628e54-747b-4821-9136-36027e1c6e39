package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.delivery;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 聚合配送门店配置
 * @Author: yuanyu09
 * @Date: 2022/12/12
 */
@Data
@ApiModel("聚合配送门店响应体")
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryChannelConfigVo {
    @FieldDoc(
            description = "渠道名称"
    )
    @ApiModelProperty(value = "渠道名称")
    private Integer channelType;

    @FieldDoc(
            description = "配送发单节点"
    )
    @ApiModelProperty(value = "配送发单节点")
    private Integer deliveryLaunchPoint;

    @FieldDoc(
            description = "配送平台设置"
    )
    @ApiModelProperty(value = "配送平台设置")
    private DeliveryPlatformConfigVo deliveryPlatformConfigVo;

    @FieldDoc(
            description = "立即单自动呼叫骑手延迟时间"
    )
    @ApiModelProperty(value = "预约单自动呼叫骑手延迟时间")
    private Integer deliveryLaunchDelayMinutes;

    @FieldDoc(
            description = "预约单自动呼叫骑手延迟时间"
    )
    @ApiModelProperty(value = "预约单自动呼叫骑手延迟时间")
    private Integer bookingOrderDeliveryLaunchMinutes;
}
