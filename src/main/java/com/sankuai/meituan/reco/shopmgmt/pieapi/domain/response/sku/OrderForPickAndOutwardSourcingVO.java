package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@TypeDoc(
        description = "待外拣货及采现结订单信息"
)
@Data
@ApiModel("待外拣货及采现结订单信息")
@NoArgsConstructor
public class OrderForPickAndOutwardSourcingVO {

    @FieldDoc(
            description = "履约工单id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "履约工单id", required = true)
    public Long fulFillWorkOrderId;

    @FieldDoc(
            description = "每天的第几单", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "每天的第几单", required = true)
    public Integer daySeq;

    @FieldDoc(
            description = "渠道名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道名称", required = true)
    public String channelName;

    @FieldDoc(
            description = "备注", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "备注", required = true)
    public String caution;

    @FieldDoc(
            description = "商品件数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品件数", required = true)
    public Integer skuCount;

    @FieldDoc(
            description = "下发时间-时间戳-秒", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "下发时间-时间戳-秒", required = true)
    public Long pushTime;

    @FieldDoc(
            description = "告警的值", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "告警的值", required = true)
    public Integer warningDuration;

    @FieldDoc(
            description = "是否为预订单", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "是否为预订单", required = true)
    private Boolean isReserved = false;

    @FieldDoc(
            description = "预计送达时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "预计送达时间", required = true)
    public Long deliverTime = 0L;

    @FieldDoc(
            description = "订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单号", required = true)
    public String orderId;

    @FieldDoc(
            description = "外部订单id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "外部订单id", required = true)
    public Integer channelId;

    @FieldDoc(
            description = "每天的第几单（新）", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "每天的第几单（新）", required = true)
    public String daySeqNum;
}
