package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.appraise;

import com.meituan.shangou.saas.resource.management.dto.appraise.IndicatorDetailDTO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2020/9/14 17:07
 * @Description:
 */
@ToString
@Data
public class IndicatorDetailVO {

    private String target;


    private String indicId;

    private String indicatorName;

    private String indicatorDesc;




    private List<IndicatorDayDetailVO> details;


    public IndicatorDetailVO(IndicatorDetailDTO detailDTO) {
        this.target = detailDTO.getTarget();
        this.indicatorName = detailDTO.getIndicatorName();
        this.indicatorDesc = detailDTO.getIndicatorDesc();
        this.indicId = ConverterUtils.nonNullConvert(detailDTO.getIndicatorId(), String::valueOf);

        this.details = ConverterUtils.convertList(detailDTO.getDailyDetail(), IndicatorDayDetailVO::new);

    }


}
