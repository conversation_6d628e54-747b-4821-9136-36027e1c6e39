package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.AttendanceApprovalItemVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * Email <EMAIL>
 * Date 2022/10/14 6:27 下午
 * Description
 */

@TypeDoc(
        description = "考勤审批列表响应"
)
@Data
@ApiModel("查询考勤审批列表响应")
public class AttendanceApprovalListResponse {


    @FieldDoc(
            description = "审批详情", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty("审批详情")
    private List<AttendanceApprovalItemVO> list;

    @FieldDoc(
            description = "总数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty("总数量")
    private Integer totalCount;

    @FieldDoc(
            description = "是否还有更多数据", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty("是否还有更多数据")
    private Integer hasMore;
}
