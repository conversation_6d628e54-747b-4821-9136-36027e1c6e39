package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.google.common.base.Preconditions;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.DxSSOConstants;
import com.sankuai.meituan.reco.shopmgmt.pieapi.facade.AccountService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.DxUtils;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.AccountSessionVO;
import com.sankuai.security.sdk.SecSdk;
import java.math.BigInteger;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.jetty.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class DxSSOWrapper {

    private static Long SECOND = TimeUnit.SECONDS.toMillis(1);

    @Resource
    private AccountService accountService;

    /**
     * 封装SSO登录地址
     *
     * @return SSO登录地址/异常信息
     */
    public String ssoAuth(HttpServletRequest request, HttpServletResponse response) {
        try {
            String redirectUri = request.getParameter(DxSSOConstants.REDIRECT_KEY);
            //安全扫描漏洞修复
            if (!SecSdk.securityUrlRedirect(redirectUri, DxSSOConstants.WHITE_URL)) {
                DxUtils.writeData2Response(response);
                return null;
            }
            Preconditions.checkArgument(StringUtils.isNotEmpty(redirectUri), "param redirect_uri is empty");
            response.sendRedirect(DxUtils.buildSSORedirectUrl(redirectUri));
            return DxSSOConstants.REDIRECT.concat(DxUtils.buildSSORedirectUrl(redirectUri));
        } catch (Exception e) {
            log.error("DxSSOWrapper.ssoAuth error, exception->", e);
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR_500);
            DxUtils.writeData2Response(response);
            return null;
        }
    }

    /**
     * SSO回调处理跳转大象
     *
     * @return 大象回调地址
     */
    public String ssoCallback(HttpServletRequest request, HttpServletResponse response,
            RedirectAttributes attributes) {
        try {
            //大象回调地址
            String callbackUrl = request.getParameter(DxSSOConstants.CALLBACK_KEY);
            Preconditions.checkArgument(StringUtils.isNotEmpty(callbackUrl), "param callbackUrl is empty");
            String bSid = request.getParameter(DxSSOConstants.BSID_KEY);
            if (StringUtils.isBlank(callbackUrl)) {
                DxUtils.writeData2Response(response,"回调url 未配置 请联系管理员");
                return null;
            }
            //安全扫描漏洞修复
            if (!SecSdk.securityUrlRedirect(callbackUrl, DxSSOConstants.WHITE_URL)) {
                DxUtils.writeData2Response(response);
                return null;
            }
            //如果回调
            if (StringUtils.isBlank(bSid)) {
                log.warn("BSID is empty");
                DxUtils.writeData2Response(response,"登录token 无效");
                return null;
            }

            AccountSessionVO accountSession = accountService.queryAccountSession(bSid);
            if (Objects.isNull(accountSession)) {
                log.warn("epassport login user is null BSID:{}", bSid);
                DxUtils.writeData2Response(response,"登录失败，请重新登录");
                //clearSession(logout epassport)
                accountService.clearSession(bSid);
                return null;
            }
            //如果用户在ep存在 在auth 不存在 需要告知客户端
            if (accountSession.getAccountId() <= BigInteger.ZERO.intValue()) {
                log.warn("epassport auth user is null BSID:{}", bSid);
                DxUtils.writeData2Response(response,"当前账号信息有误，请联系管理员处理");
                accountService.clearSession(bSid);
                return null;
            }
            if (StringUtils.isBlank(accountSession.getDxDomain()) || StringUtils.isBlank(accountSession.getDxMis())){
                log.warn("dxMis is null  accountSession:{}", accountSession);
                DxUtils.writeData2Response(response,"当前账号无法登录大象app 请联系管理员，请联系管理员处理");
                accountService.clearSession(bSid);
                return null;
            }
            //设置登录cookie(epassport token信息)
            DxUtils.setToken2Cookie(request, response, bSid);
            String email = accountSession.getDxMis().concat(DxSSOConstants.DOMAIN_CONCAT).concat(accountSession.dxDomain);
            String name = accountSession.getDxMis();
            long time = System.currentTimeMillis() / SECOND;
            String sign = DxUtils.md5(email + name + time + DxSSOConstants.SSO_FROM_SECRET);
            attributes.addAttribute("email", email);
            attributes.addAttribute("name", name);
            attributes.addAttribute("time", time);
            attributes.addAttribute("sso_from_key", DxSSOConstants.SSO_FROM_KEY);
            attributes.addAttribute("sign", sign);
            return DxSSOConstants.REDIRECT.concat(callbackUrl);
        } catch (Exception e) {
            log.error("DxSSOWrapper.ssoCallback error, exception->", e);
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR_500);
            DxUtils.writeData2Response(response);
            return null;
        }
    }

    /**
     * 封装SSO登出地址 清除sso逻辑
     */
    public String ssoAuthClear(HttpServletRequest request, HttpServletResponse response) {
        String redirectUri = request.getParameter(DxSSOConstants.REDIRECT_KEY);
        try {
            Preconditions.checkArgument(StringUtils.isNotEmpty(redirectUri), "param redirect_uri is empty");
            //安全扫描漏洞修复
            if (!SecSdk.securityUrlRedirect(redirectUri, DxSSOConstants.WHITE_URL)) {
                DxUtils.writeData2Response(response);
                return null;
            }
            //get cookie
            Cookie tokenCookie = DxUtils.getTokenCookie(request);
            if (Objects.isNull(tokenCookie) || StringUtils.isEmpty(tokenCookie.getValue())) {
                return DxSSOConstants.REDIRECT.concat(redirectUri);
            }
            //clearSession(logout epassport)
            accountService.clearSession(tokenCookie.getValue());
            //remove cookie(epassport token信息)
            DxUtils.removeCookie(request, response, tokenCookie);
            log.info("clear sso info redirect->{}", redirectUri);
            return DxSSOConstants.REDIRECT.concat(redirectUri);
        } catch (Exception e) {
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR_500);
            log.error("DxSSOWrapper.ssoAuthClear error, exception->", e);
            return DxSSOConstants.REDIRECT.concat(redirectUri);
        }
    }
}
