package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.CartonMeasureDto;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.CartonMeasureForIDLDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/6/13
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CartonMeasureConvertFactorVO {

    @FieldDoc(
            description = "箱规编码"
    )
    @ApiModelProperty(value = "箱规编码")
    private String cartonMeasureCode;

    @FieldDoc(
            description = "箱规名称"
    )
    @ApiModelProperty(value = "箱规名称")
    private String cartonMeasureName;

    @FieldDoc(
            description = "相对于基础单位转换比例"
    )
    @ApiModelProperty(value = "相对于基础单位转换比例")
    private Long basicUnitConvertFactor;

    public CartonMeasureDto toCartonMeasureDto() {
        CartonMeasureDto cartonMeasureDto = new CartonMeasureDto();
        cartonMeasureDto.setCartonMeasureCode(cartonMeasureCode);
        cartonMeasureDto.setCartonMeasureName(cartonMeasureName);
        cartonMeasureDto.setBasicUnitConvertFactor(basicUnitConvertFactor);
        return cartonMeasureDto;
    }

    public CartonMeasureForIDLDTO toCartonMeasureForIDLDTO() {
        CartonMeasureForIDLDTO cartonMeasureForIDLDTO = new CartonMeasureForIDLDTO();
        cartonMeasureForIDLDTO.setCartonMeasureCode(cartonMeasureCode);
        cartonMeasureForIDLDTO.setCartonMeasureName(cartonMeasureName);
        cartonMeasureForIDLDTO.setBasicUnitConvertFactor(basicUnitConvertFactor);
        return cartonMeasureForIDLDTO;
    }

    public com.sankuai.meituan.shangou.empower.productbiz.client.dto.CartonMeasureDto toBizDTO() {
        com.sankuai.meituan.shangou.empower.productbiz.client.dto.CartonMeasureDto dto =
                new com.sankuai.meituan.shangou.empower.productbiz.client.dto.CartonMeasureDto();
        dto.setCartonMeasureCode(cartonMeasureCode);
        dto.setCartonMeasureName(cartonMeasureName);
        dto.setBasicUnitConvertFactor(basicUnitConvertFactor);
        return dto;
    }
}
