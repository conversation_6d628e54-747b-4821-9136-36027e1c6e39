package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.FailedRecordDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 线上商品前台分类错误记录
 * @author: liyu44
 * @create: 2020-02-04
 **/
@TypeDoc(
        description = "线上商品前台分类错误记录"
)
@Data
@ApiModel("线上商品前台分类错误记录")
public class FailedRecordVO {

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道ID")
    private Integer channelId;

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店id")
    private Long storeId;

    @FieldDoc(
            description = "商品ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品ID")
    private String skuId;

    @FieldDoc(
            description = "错误文案", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "错误文案")
    private String errorMsg;

    @FieldDoc(
            description = "错误编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "错误编码")
    private Integer errorCode;

    public FailedRecordVO(FailedRecordDTO failedRecordDTO){
        this.setChannelId(failedRecordDTO.getChannelId());
        this.setStoreId(failedRecordDTO.getStoreId());
        this.setSkuId(failedRecordDTO.getSkuId());
        this.setErrorMsg(failedRecordDTO.getErrorMsg());
        this.setErrorCode(failedRecordDTO.getErrorCode());
    }

}
