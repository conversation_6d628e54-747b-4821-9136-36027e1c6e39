package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2021-03-04 19:44
 * @Description:
 */
@TypeDoc(
        description = "分页查询标品库响应"
)
@Data
@ApiModel("分页查询标品库响应")
public class PageQueryStandardSkuResponse {

    @FieldDoc(
            description = "标品库商品信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "标品库商品信息", required = true)
    List<SimpleSkuVO> simpleSkuVOList;

    @FieldDoc(
            description = "分页信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "分页信息", required = true)
    private PageInfoVO pageInfo;

}
