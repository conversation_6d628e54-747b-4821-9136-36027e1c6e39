package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * @Author: wangyihao04
 * @Date: 2020-11-03 21:28
 * @Mail: <EMAIL>
 */
@TypeDoc(
        description = "待优化问题商品返回列表"
)
@ApiModel("待优化问题商品返回列表")
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
public class SkuWithPriceEffectIndexVO {
    @FieldDoc(
            description = "skuid"
    )
    @ApiModelProperty("skuid")
    private String skuId;
    @FieldDoc(
            description = "spuid"
    )
    @ApiModelProperty("spuid")
    private String spuId;
    @FieldDoc(
            description = "商品图片"
    )
    @ApiModelProperty("商品图片")
    private List<String> imageList;
    @FieldDoc(
            description = "商品名称"
    )
    @ApiModelProperty("商品名称")
    private String skuName;
    @FieldDoc(
            description = "价格指标模型"
    )
    @ApiModelProperty("价格指标模型")
    private PriceEffectIndexVO priceEffectIndex;
    @FieldDoc(
            description = "中台零售价(当前规格下"
    )
    @ApiModelProperty("中台零售价(当前规格下")
    private Long channelPrice;
    @FieldDoc(
            description = "中台零售价(标准单位下)"
    )
    @ApiModelProperty("中台零售价(标准单位下)")
    private Long standardUnitChannelPrice;
    @FieldDoc(
            description = "进货价(当前规格下)"
    )
    @ApiModelProperty("进货价(当前规格下)")
    private Long offlinePrice;
    @FieldDoc(
            description = "进货价(标准单位下)"
    )
    @ApiModelProperty("进货价(标准单位下)")
    private Long standardUnitOfflinePrice;
    @FieldDoc(
            description = "待审核价(当前规格下)"
    )
    @ApiModelProperty("待审核价(当前规格下)")
    private Long reviewPrice;
    @FieldDoc(
            description = "待审核价(标准单位下)"
    )
    @ApiModelProperty("待审核价(标准单位下)")
    private Long standardUnitReviewPrice;
    @FieldDoc(
            description = "参考价模型"
    )
    @ApiModelProperty("参考价模型")
    private ReferencePriceVO referencePrice;
    @FieldDoc(
            description = "重量类型"
    )
    @ApiModelProperty("重量类型")
    private Integer weightType;
    @FieldDoc(
            description = "命中的定价方式"
    )
    @ApiModelProperty("命中的定价方式")
    private AdjustPriceStrategyVO hitStrategyDTO;
    @FieldDoc(
            description = "规格"
    )
    @ApiModelProperty("规格")
    private String spec;
    @FieldDoc(
            description = "月售"
    )
    @ApiModelProperty("月售")
    private Integer sales30Day;
    @FieldDoc(
            description = "重量"
    )
    @ApiModelProperty("重量")
    private Integer weight;
    @FieldDoc(
            description = "一级类目编码"
    )
    @ApiModelProperty("一级类目编码")
    private String firstCategoryCode;
    @FieldDoc(
            description = "一级类目名称"
    )
    @ApiModelProperty("一级类目名称")
    private String firstCategoryName;
    @FieldDoc(
            description = "售卖单位"
    )
    @ApiModelProperty("售卖单位")
    private String saleUnit;
}
