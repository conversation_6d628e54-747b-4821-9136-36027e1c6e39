package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.wavepick.converter;

import com.sankuai.meituan.reco.pickselect.thrift.wave.request.WaveSeedSkuDetailRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick.WarehouseSeedSkuDetailRequest;
import org.mapstruct.Mapper;

/**
 * @Description:
 * @Author: zhangjian155
 * @Date: 2022/8/25 17:47
 */
@Mapper(componentModel = "spring")
public abstract class WarehouseSeedSkuDetailRequestConverter {
    public abstract WaveSeedSkuDetailRequest convert2ThriftRequest(WarehouseSeedSkuDetailRequest request,
                                                                   Long tenantId, Long accountId, Long storeId);
}
