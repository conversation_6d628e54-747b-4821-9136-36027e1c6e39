package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.impl;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor.QueryApprovalListRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.AbstractSinglePendingTaskService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.labor.LaborHireApprovalServiceWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.labor.ResignServiceWrapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022-12-20
 * @email <EMAIL>
 */
@Service
public class HireAndResignTaskService extends AbstractSinglePendingTaskService {

    @Resource
    private LaborHireApprovalServiceWrapper laborHireApprovalServiceWrapper;

    @Override
    protected PendingTaskResult getPendingTaskCount(PendingTaskParam param) throws Exception {

        CommonResponse<Long> countResponse = laborHireApprovalServiceWrapper.count(param.getTenantId(), param.getEntityId(), param.getUser().getEmployeeId(), param.getUser().getAccountId());
        if (!countResponse.isSuccess()) {
            return PendingTaskResult.createNumberMarker(0);
        }

        return PendingTaskResult.createNumberMarker(countResponse.getData().intValue());
    }

    @Override
    protected AuthCodeEnum module() {
        return AuthCodeEnum.DH_ONBOARD_AND_RESIGN;
    }

}
