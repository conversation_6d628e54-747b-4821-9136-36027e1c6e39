package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.shangou.empower.price.client.dto.CanChangePriceSpuDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Optional;

/**
 * 作者：guohuqi
 * 时间：2022/11/3 3:22 PM
 * 功能：
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpuCanModifyPriceVO {

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID", required = true)
    private Long storeId;

    @FieldDoc(
            description = "渠道id  -1-线下 100-美团 200-饿了么 300-京东到家", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道id", required = true)
    private Integer channelId;
    @FieldDoc(
            description = "商品customSpuId编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品customSpuId编码", required = true)
    private String customSpuId;

    @FieldDoc(
            description = "是否活动中，活动中，不能改价"
    )
    @ApiModelProperty(value = "是否活动中，活动中，不能改价", required = true)
    private Boolean atPromotion;

    public static SpuCanModifyPriceVO valueOf(CanChangePriceSpuDTO spuDTO) {
        return SpuCanModifyPriceVO.builder()
                .channelId(spuDTO.getCustomSpuKey().getChannelId())
                .storeId(spuDTO.getCustomSpuKey().getStoreId())
                .customSpuId(spuDTO.getCustomSpuKey().getCustomSpuId())
                .atPromotion(!Optional.ofNullable(spuDTO.getCanChangePrice()).orElse(true))
                .build();
    }
}
