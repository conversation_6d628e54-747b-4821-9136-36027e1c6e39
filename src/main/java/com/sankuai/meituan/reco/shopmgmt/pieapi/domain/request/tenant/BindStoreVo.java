package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.tenant;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.order.platform.common.SelfCheckable;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: shen<PERSON><PERSON><PERSON><PERSON>
 * @create: 2024-09-12 14:45
 **/
@Data
@TypeDoc(
        description = "绑定详情"
)
public class BindStoreVo implements SelfCheckable {

    @FieldDoc(description = "门店id")
    @NotNull(message = "门店id不能为空")
    public String storeId;

    @FieldDoc(description = "门店名称")
    @NotNull(message = "门店名称不能为空")
    public String storeName;

    @FieldDoc(description = "绑定数量")
    @NotNull(message = "绑定数量不能为空")
    public Integer unitNum;
}
