package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.auth;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Created by yang<PERSON> on 18/9/28.
 */
@Data
@ApiModel("查询权限信息响应")
public class DataAuthResourceResult {

    @ApiModelProperty(value = "权限列表，层级展示")
    private List<DataAuthPermissionVo> dataPermissions;
}
