package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 非白底图转白底图响应
 *
 * <AUTHOR>
 * @date 2021-10-18 19:12
 */
@AllArgsConstructor
@Data
@TypeDoc(
        description = "非白底图转白底图响应"
)
@ApiModel("非白底图转白底图响应")
public class WhiteBackgroundImageVO {
    @FieldDoc(
            description = "白底图片链接", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "白底图片链接")
    private String imgUrl;
}
