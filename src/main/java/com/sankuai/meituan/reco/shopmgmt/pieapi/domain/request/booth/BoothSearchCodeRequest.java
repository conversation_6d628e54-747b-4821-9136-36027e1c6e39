package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.booth;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.ParamException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/10/15 17:26
 * Description: 摊位检索码校验请求
 */
@TypeDoc(
        description = "摊位检索码校验请求",
        authors = {
                "liyang176"
        },
        version = "V1.0"
)
@Data
@ApiModel("摊位检索码校验请求")
public class BoothSearchCodeRequest {

    @FieldDoc(
            description = "门店编码"
    )
    @ApiModelProperty(value = "门店编码")
    private Long poiId;

    @FieldDoc(
            description = "摊位检索码"
    )
    @ApiModelProperty(value = "摊位检索码")
    private String boothSearchCode;

    /**
     * 校验必填参数
     */
    public void baseValidate() {
        if (Objects.isNull(poiId) || poiId <= 0) {
            throw new ParamException("摊位所属门店编码无效");
        }

        if (StringUtils.isBlank(boothSearchCode)) {
            throw new ParamException("摊位的绑定二维码无效");
        }
    }
}
