package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.SpuCompareRecordFieldDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2022/3/1 2:14 下午
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CompareRecordInfoVO {

    @FieldDoc(
            description = "商品中心的不一致信息key"
    )
    private String compareRecordInfoKey;

    @FieldDoc(
            description = "商品中心不一致信息值"
    )
    private List<String> compareRecordInfoValues;

    @FieldDoc(
            description = "渠道的不一致信息值"
    )
    private List<String> channelCompareRecordInfoValues;

    public static CompareRecordInfoVO of(SpuCompareRecordFieldDTO compareInfoDTO) {
        CompareRecordInfoVO infoVO = new CompareRecordInfoVO();
        infoVO.setCompareRecordInfoKey(compareInfoDTO.getProductProblemKey());
        infoVO.setCompareRecordInfoValues(compareInfoDTO.getOcmsProductProblemValues());
        infoVO.setChannelCompareRecordInfoValues(compareInfoDTO.getChannelProductProblemValues());
        return infoVO;
    }

}
