package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@TypeDoc(
        name = "推荐渠道动态信息和重量VO对象",
        description = "推荐渠道动态信息和重量VO对象"
)
@Getter
@Setter
@ToString
@Builder
public class RecommendDynamicInfoAndWeightVo {

    @FieldDoc(
            description = "商品名称"
    )
    private String spuName;

    @FieldDoc(
            description = "渠道id"
    )
    private Integer channelId;

    @FieldDoc(
            description = "渠道类目id"
    )
    private String channelCategoryId;

    @FieldDoc(
            description = "动态信息列表"
    )
    private List<ChannelDynamicInfoVO> channelDynamicInfoVOList;

    @FieldDoc(
            description = "重量单位"
    )
    @ApiModelProperty(name = "重量单位")
    private String weightUnit;

    @FieldDoc(
            description = "指定重量单位下的重量值"
    )
    @ApiModelProperty(name = "指定重量单位下的重量值")
    private String weightForUnit;
}
