package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.manage;

import javax.validation.constraints.NotNull;

import org.apache.commons.lang3.StringUtils;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 根据骑手姓名或电话查询骑手信息的请求.
 *
 * <AUTHOR>
 * @since 2021/7/28 23:47
 */
@TypeDoc(
        description = "根据骑手检索信息（姓名或电话）查询可转单骑手信息的请求."
)
@ApiModel("根据骑手检索信息（姓名或电话）查询可转单骑手信息的请求.")
@Data
public class QueryTransferableRiderRequest {

    @FieldDoc(
            description = "门店 ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店 ID", required = true)
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "骑手检索信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "骑手检索信息", required = true)
    private String riderSearchKey;

    @FieldDoc(
            description = "当前骑手电话", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "当前骑手电话", required = true)
    private String currentRiderPhone;

    public String validate() {
        if (storeId == null || storeId <= 0) {
            return "门店 ID 无效";
        }
        if (StringUtils.isBlank(riderSearchKey)) {
            return "检索信息无效";
        }
        return null;
    }
}
