package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.price;

import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 关注竞对门店更新请求
 * @email <EMAIL>
 * @date 2020-11-30
 */
@ApiModel(
        "竞对门店更新"
)
@TypeDoc(
        description = "竞对门店更新"
)
@Data
public class ContrastStoreUpdateRequestVO {

    @FieldDoc(
            description = "门店ID"
    )
    @ApiModelProperty("门店ID")
    @NotNull
    public Long storeId;

    @FieldDoc(
            description = "竞对门店列表"
    )
    @ApiModelProperty("竞对门店列表")
    @NotNull
    public List<ContrastStoreKeyVO> contrastStoreList;

    @FieldDoc(
            description = "操作类型"
    )
    @ApiModelProperty("操作类型")
    @NotNull
    public String actionType;

}
