package com.sankuai.meituan.reco.shopmgmt.pieapi.squirrel;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023/7/27 23:47
 **/
@Repository
@Slf4j
public class DegradeRecordSquirrelRepository {

    @Resource(name = "wmRedisStoreClient")
    private RedisStoreClient wmRedisStoreClient;


    private final static String indicatorCategory = "pickimage_upload_degrade_times";

    private final static StoreKey storeKey = new StoreKey(indicatorCategory, "degrade_times");

    private final static Integer EXPIRE_IN_SECONDS = 24 * 60 * 60;

    public Long addDegradeMachineCount() {

        Long degradeMachineCount = wmRedisStoreClient.incrBy(storeKey,1, EXPIRE_IN_SECONDS,  0);
        log.info("addDegradeMachineCount : {}", degradeMachineCount);

        return degradeMachineCount;
    }


    public Long reduceDegradeMachineCount() {

        Long degradeMachineCount = wmRedisStoreClient.decrBy(storeKey, 1, EXPIRE_IN_SECONDS,  0);
        log.info("reduceDegradeMachineCount : {}", degradeMachineCount);

        return degradeMachineCount;
    }
}
