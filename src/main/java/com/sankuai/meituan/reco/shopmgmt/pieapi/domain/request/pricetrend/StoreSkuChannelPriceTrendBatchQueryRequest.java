package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pricetrend;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.PriceTrendConstants;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.ParamException;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;


@TypeDoc(
        description = "门店商品渠道价格趋势请求",
        authors = "hejunliang"
)
@Setter
@Getter
@ToString
@NoArgsConstructor
public class StoreSkuChannelPriceTrendBatchQueryRequest {

    @FieldDoc(
            description = "门店id"
    )
    private Long storeId;

    @FieldDoc(
            description = "商品编码"
    )
    private List<String> skuIds;

    @FieldDoc(
            description = "渠道id"
    )
    private Integer channelId;

    public void validate() {

        if (this.storeId == null) {
            throw new ParamException("门店id不能为空");
        }
        if (CollectionUtils.isEmpty(this.skuIds)) {
            throw new ParamException("商品编码不能为空");
        }
        if(skuIds.size() > PriceTrendConstants.SKU_IDS_MAX_SIZE) {
            throw new ParamException(String.format("一次最多查询%s个商品的价格趋势", PriceTrendConstants.SKU_IDS_MAX_SIZE));
        }
        if (this.channelId == null) {
            throw new ParamException("渠道id不能为空");
        }
    }

}
