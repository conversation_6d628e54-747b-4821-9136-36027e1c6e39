package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.google.common.collect.Maps;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.FieldSort;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.FrontCategorySpuSequenceUpdateByConditionRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.FrontCategorySpuSequenceUpdateRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.FrontCategorySpuSmartSortQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.FrontCategorySpuSmartSortUpdateRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.ChannelSpuBaseInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.FrontCategorySpuSmartSortQueryResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.common.response.Response;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.multidomain.request.StoreCategoryProductSequenceUpdateRequest;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.multidomain.response.StoreCategoryProductSequenceUpdateResponse;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.multidomain.service.StoreCategoryProductThriftService;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.storecategory.dto.StoreCategorySmartSortDTO;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.storecategory.request.StoreCategorySmartSortRequest;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.storecategory.response.StoreCategorySmartSortQueryResponse;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.storecategory.service.StoreCategoryThriftService;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 店内分类与SPU关系管理服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class FrontCategorySpuWrapper {

    @Autowired
    private ChannelSpuWrapper channelSpuWrapper;

    @Autowired
    private StoreCategoryProductThriftService storeCategoryProductThriftService;

    @Autowired
    private StoreCategoryThriftService storeCategoryThriftService;

    public CommonResponse updateSequence(User user, FrontCategorySpuSequenceUpdateRequest request) {

        try {
            // 更新门店分类下spu排序顺序
            updateFrontCategorySpuSequence(user, request.getStoreId(), request.getChannelId(),
                    request.getFrontCategoryId(), request.getSpuSequenceMap());

            return CommonResponse.success(Boolean.TRUE);

        } catch (TException e) {
            log.error("更新门店分类下商品排序异常, request:{}", request, e);
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse updateSequenceByCondition(User user, FrontCategorySpuSequenceUpdateByConditionRequest request) {

        Long storeId = request.getStoreId();
        Integer channelId = request.getChannelId();
        Long frontCategoryId = request.getFrontCategoryId();
        FieldSort fieldSort = request.getFieldSort();

        // 查询店分下下所有spu, 并按照指定规则排序
        List<ChannelSpuBaseInfoVO> channelSpuBaseInfoVOList = channelSpuWrapper.queryChannelSpuList(storeId, channelId, frontCategoryId, fieldSort);
        if (CollectionUtils.isEmpty(channelSpuBaseInfoVOList)) {
            return CommonResponse.success(Boolean.TRUE);
        }

        // 设置店内分类下spu顺序
        Map<String, Integer> spuSequenceMap = Maps.newHashMap();
        for (int i = 0; i < channelSpuBaseInfoVOList.size(); i++) {
            spuSequenceMap.put(channelSpuBaseInfoVOList.get(i).getSpuId(), i + 1);
        }

        try {
            // 更新门店分类下spu排序顺序
            updateFrontCategorySpuSequence(user, storeId, channelId, frontCategoryId, spuSequenceMap);
            return CommonResponse.success(Boolean.TRUE);
        } catch (TException e) {
            log.error("更新门店分类下商品排序异常, request:{}", request, e);
            throw new CommonRuntimeException(e);
        }
    }


    private void updateFrontCategorySpuSequence(User user, Long storeId, Integer channelId,
                                                Long frontCategoryId, Map<String, Integer> spuSequenceMap) throws TException {

        StoreCategoryProductSequenceUpdateRequest rpcUpdateRequest = new StoreCategoryProductSequenceUpdateRequest();
        rpcUpdateRequest.setTenantId(user.getTenantId());
        rpcUpdateRequest.setStoreId(storeId);
        rpcUpdateRequest.setChannelId(channelId);
        rpcUpdateRequest.setStoreCategoryId(frontCategoryId);
        rpcUpdateRequest.setSpuSequenceMap(spuSequenceMap);
        rpcUpdateRequest.setOperatorId(user.getAccountId());
        rpcUpdateRequest.setOperatorName(user.getAccountName());

        StoreCategoryProductSequenceUpdateResponse response = storeCategoryProductThriftService.updateStoreCategoryProductSequence(rpcUpdateRequest);

        if (Objects.isNull(response) || Objects.isNull(response.getCode())) {
            throw new CommonLogicException("更新门店分类下商品排序未返回结果", ResultCode.FAIL);
        }

        if (response.getCode() != ResultCode.SUCCESS.getCode()) {
            throw new CommonLogicException(response.getMessage(), ResultCode.FAIL);
        }
    }

    public CommonResponse updateSmartSort(User user, FrontCategorySpuSmartSortUpdateRequest request) {

        StoreCategorySmartSortRequest rpcUpdateRequest = new StoreCategorySmartSortRequest();
        rpcUpdateRequest.setTenantId(request.getTenantId());
        rpcUpdateRequest.setChannelId(request.getChannelId());
        rpcUpdateRequest.setStoreCategoryId(request.getFrontCategoryId());
        rpcUpdateRequest.setStoreId(request.getStoreId());
        rpcUpdateRequest.setSmartSort(request.getSmartSort());
        rpcUpdateRequest.setOperatorId(user.getAccountId());
        rpcUpdateRequest.setOperatorName(user.getOperatorName());

        Response response = storeCategoryThriftService.updateSmartSortSwitch(rpcUpdateRequest);

        if (response == null){
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "调用远程服务异常");
        }
        if (response.getCode() != ResultCode.SUCCESS.getCode()){
            return CommonResponse.fail(response.getCode(), response.getMessage());
        }

        return CommonResponse.success(ResultCode.SUCCESS);
    }

    public CommonResponse<FrontCategorySpuSmartSortQueryResponseVO> querySmartSort(User user, FrontCategorySpuSmartSortQueryRequest request) {

        StoreCategorySmartSortRequest rpcUpdateRequest = new StoreCategorySmartSortRequest();
        rpcUpdateRequest.setTenantId(request.getTenantId());
        rpcUpdateRequest.setChannelId(request.getChannelId());
        rpcUpdateRequest.setStoreCategoryId(request.getFrontCategoryId());
        rpcUpdateRequest.setStoreId(request.getStoreId());
        rpcUpdateRequest.setOperatorId(user.getAccountId());
        rpcUpdateRequest.setOperatorName(user.getOperatorName());

        StoreCategorySmartSortQueryResponse response = storeCategoryThriftService.querySmartSortSwitch(rpcUpdateRequest);

        if (response == null){
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "调用远程服务异常");
        }
        if (response.getCode() != ResultCode.SUCCESS.getCode() || response.getSmartSortDTO() == null){
            return CommonResponse.fail(response.getCode(), response.getMessage());
        }

        StoreCategorySmartSortDTO dto = response.getSmartSortDTO();
        FrontCategorySpuSmartSortQueryResponseVO vo = new FrontCategorySpuSmartSortQueryResponseVO();
        vo.setSmartSort(dto.getSmartSort());
        vo.setFrontCategoryId(dto.getStoreCategoryId());
        vo.setChannelId(request.getChannelId());
        vo.setStoreId(request.getStoreId());
        vo.setTenantId(request.getTenantId());

        return CommonResponse.success(vo);
    }

}
