package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.management;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.auth.AppModuleResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @createTime 2019/11/28
 * @description
 */
@TypeDoc(
        description = "运营首页查询返回"
)
@Data
@ApiModel("运营首页查询返回")
public class ManagementHomepageResponse {


    @FieldDoc(
            description = "运营数据模块", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty("运营数据模块")
    private ManagementRevenueVO managementRevenueVO;


    @FieldDoc(
            description = "运营数据模块", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty("运营数据模块")
    private AppModuleResult appModuleResult;
}
