package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.delivery.third;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/8/25 15:07
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ThirdDeliveryOrderListResponse {
    @FieldDoc(
            description = "总条数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "总条数", required = true)
    private Integer totalCount;

    @FieldDoc(
            description = "是否还有下一页", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否还有下一页", required = true)
    private Boolean hasMore;

    @FieldDoc(
            description = "订单列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单列表", required = true)
    private List<ThirdDeliveryOrderVO> orderList;
}
