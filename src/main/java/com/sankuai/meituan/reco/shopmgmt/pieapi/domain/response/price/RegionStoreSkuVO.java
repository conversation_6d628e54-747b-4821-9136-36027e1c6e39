package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MoneyUtils;
import com.sankuai.meituan.shangou.empower.price.client.dto.price_effect.RegionStoreSkuDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;

@TypeDoc(
        description = "城市门店商品信息"
)
@Data
@ApiModel("城市门店商品信息")
public class RegionStoreSkuVO {

    @FieldDoc(
            description = "城市id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "城市id", required = true)
    private Long regionId;

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店id", required = true)
    private Long storeId;

    @FieldDoc(
            description = "门店名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店名称", required = true)
    private String storeName;

    @FieldDoc(
            description = "spu编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "spu编码", required = true)
    private String spuId;

    @FieldDoc(
            description = "sku编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "sku编码", required = true)
    private String skuId;

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称", required = true)
    private String skuName;

    @FieldDoc(
            description = "规格", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "规格", required = true)
    private String spec;

    @FieldDoc(
            description = "称重类型", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "称重类型", required = true)
    private Integer weightType;

    @FieldDoc(
            description = "重量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "重量", required = true)
    private Integer weight;

    @FieldDoc(
            description = "零售价", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "零售价", required = true)
    private BigDecimal onlinePrice;

    @FieldDoc(
            description = "零售市斤价", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "零售价标准价", required = true)
    private BigDecimal standardUnitOnlinePrice;

    @FieldDoc(
            description = "月销量", requiredness = Requiredness.OPTIONAL
    )
    private Integer sales30day;

    @FieldDoc(
            description = "是否本店", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否本店", required = true)
    private Boolean oneself;

    @FieldDoc(
            description = "是否有门店权限", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否有门店权限", required = true)
    private Boolean hasStorePermission;

    public static RegionStoreSkuVO build(RegionStoreSkuDTO dto, Long currentStoreId, List<Long> hasPermissionStoreIds) {
        RegionStoreSkuVO vo = new RegionStoreSkuVO();
        vo.setRegionId(dto.getRegionId());
        vo.setStoreId(dto.getStoreId());
        vo.setStoreName(dto.getStoreName());
        vo.setSpuId(dto.getSpuId());
        vo.setSkuId(dto.getSkuId());
        vo.setSkuName(dto.getSkuName());
        vo.setSpec(dto.getSpec());
        vo.setWeightType(dto.getWeightType());
        vo.setWeight(dto.getWeight());
        vo.setOnlinePrice(dto.getOnlinePrice() != null ? MoneyUtils.centToYuanDecimalByDown(dto.getOnlinePrice()) : null);
        vo.setStandardUnitOnlinePrice(dto.getStandardUnitOnlinePrice() != null ? MoneyUtils.centToYuanDecimalByDown(dto.getStandardUnitOnlinePrice()) : null);
        vo.setSales30day(dto.getSales30day() != null ? dto.getSales30day() : null);
        vo.setOneself(currentStoreId.equals(dto.getStoreId()));
        vo.setHasStorePermission(CollectionUtils.isNotEmpty(hasPermissionStoreIds) && hasPermissionStoreIds.contains(dto.getStoreId()));
        return vo;
    }
}
