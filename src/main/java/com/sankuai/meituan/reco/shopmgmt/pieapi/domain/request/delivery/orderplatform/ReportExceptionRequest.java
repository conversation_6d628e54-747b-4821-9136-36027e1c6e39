package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery.orderplatform;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * ReportExceptionReq
 *
 * <AUTHOR>
 * @since 2023/2/27
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ReportExceptionRequest extends OrderPlatformDeliveryReq {

    /**
     * 图片url列表
     */
    @NotEmpty
    private List<String> pictureUrls;

}
