package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.manage;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 骑手信息.
 *
 * <AUTHOR>
 * @since 2021/7/28 23:53
 */
@TypeDoc(
        description = "骑手信息"
)
@ApiModel("骑手信息")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SelfRiderInfo {

    @FieldDoc(
            description = "骑手账号 ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "骑手账号 ID", required = true)
    private Long accountId;

    @FieldDoc(
            description = "骑手姓名", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "骑手姓名", required = true)
    private String name;

    @FieldDoc(
            description = "骑手电话", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "骑手电话", required = true)
    private String phone;

    @FieldDoc(
            description = "是否为临时骑手", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否为临时骑手", required = true)
    private Boolean tempRiderFlag;

    @FieldDoc(
            description = "是否培训通过", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否培训通过", required = false)
    private Boolean isCompleteTrain;

}
