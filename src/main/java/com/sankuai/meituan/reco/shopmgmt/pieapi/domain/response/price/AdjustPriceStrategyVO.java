package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MoneyUtils;
import com.sankuai.meituan.shangou.empower.price.client.dto.strategy.SkuHitSyncStrategyDTO;
import com.sankuai.meituan.shangou.empower.price.client.enums.strategy.SyncStrategyTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;
import java.util.Optional;

/**
 * @Author: wangyihao04
 * @Date: 2020-06-11 17:19
 * @Mail: <EMAIL>
 */
@Data
@TypeDoc(
        description = "调价策略对象"
)
@ApiModel("调价策略对象")
public class AdjustPriceStrategyVO {
    @FieldDoc(
            description = "调价类型 1-单品提价 2-通用提价 3-手动定价"
    )
    @ApiModelProperty("调价类型 1-单品提价 2-通用提价 3-手动定价")
    private Integer strategyType;
    @FieldDoc(
            description = "提价百分比，用于单品提价和通用提价"
    )
    @ApiModelProperty("提价百分比，用于单品提价和通用提价")
    private Double raisePercent;
    @FieldDoc(
            description = "增加价格，用于单品提价和通用提价"
    )
    @ApiModelProperty("增加价格，用于单品提价和通用提价")
    private Double raisePrice;
    @FieldDoc(
            description = "手动定价价格，用于手动定价"
    )
    @ApiModelProperty("手动定价价格，用于手动定价")
    private Double fixPrice;

    public static AdjustPriceStrategyVO of(SkuHitSyncStrategyDTO dto) {
        if (Objects.isNull(dto)){
            return null;
        }
        if (dto.getSyncStrategyType() == SyncStrategyTypeEnum.RAISE_STORE_PRICE
                || dto.getSyncStrategyType() == SyncStrategyTypeEnum.SINGLE_SKU_FIX_STRATEGY_PRICE) {
            AdjustPriceStrategyVO adjustPriceVo = new AdjustPriceStrategyVO();
            adjustPriceVo.setStrategyType(dto.getSyncStrategyType().getValue());
            adjustPriceVo.setRaisePercent(Optional.ofNullable(dto.getPriceStrategyDTO().getAdjustPercent())
                    .map(x -> BigDecimal.valueOf(x).divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN).doubleValue())
                    .orElse(null));
            adjustPriceVo.setRaisePrice(MoneyUtils.centToYuanByDown(dto.getPriceStrategyDTO().getAdjustMoney()));
            return adjustPriceVo;
        } else if (dto.getSyncStrategyType() == SyncStrategyTypeEnum.FIX_PRICE) {
            AdjustPriceStrategyVO adjustPriceVo = new AdjustPriceStrategyVO();
            adjustPriceVo.setStrategyType(dto.getSyncStrategyType().getValue());
            adjustPriceVo.setFixPrice(MoneyUtils.centToYuanByDown(dto.getFixedPrice()));
            return adjustPriceVo;
        }
        return null;

    }
}
