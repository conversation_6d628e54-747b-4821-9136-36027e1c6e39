package com.sankuai.meituan.reco.shopmgmt.pieapi.service.appmodel.workbench;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.Environment;
import com.dianping.lion.client.ConfigRepository;
import com.dianping.lion.client.Lion;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.service.mobile.mtthrift.generic.GenericService;
import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.sac.dto.model.SacMenuNodeDto;
import com.sankuai.meituan.reco.shopmgmt.pieapi.config.WmExtMenuInfoConfig;
import com.sankuai.meituan.reco.shopmgmt.pieapi.converters.assistant.AssistantTaskConverter;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.PoiAccountInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.appmodel.MenuInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.TenantWrapper;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @author: hezhengyu
 * @create: 2024-03-25 15:20
 */
@Component
@Slf4j
public class WMTaskModelMenuHelper implements InitializingBean {

    @Value("${app.name}")
    private String appKey;

    @Autowired
    private TenantWrapper tenantWrapper;

    private static Map<String, ThriftClientProxy> thriftClientMap = new HashMap<>();

    public static final String NEAR_EXPIRATION_TASK_COUNT = "nearExpirationTaskCount";

    @Override
    public void afterPropertiesSet() {
        updateThriftClientMap();
        ConfigRepository configRepository = Lion.getConfigRepository(Environment.getAppName());
        configRepository.addConfigListener(configEvent -> {
            if (Objects.equals(WmExtMenuInfoConfig.CONFIG_KEY, configEvent.getKey())){
                updateThriftClientMap();
            }
        });
    }

    public void updateThriftClientMap() {
        Map<String, WmExtMenuInfoConfig> configMap = WmExtMenuInfoConfig.getConfigMap();
        for (WmExtMenuInfoConfig config : configMap.values()) {
            String key = buildMapKey(config);
            if (thriftClientMap.containsKey(key)) {
                continue;
            }
            try {
                ThriftClientProxy clientProxy = new ThriftClientProxy();
                //声明
                clientProxy.setAppKey(appKey);
                clientProxy.setRemoteAppkey(config.getRemoteAppkey());
                clientProxy.setGenericServiceName(config.getGenericServiceName());
                clientProxy.setFilterByServiceName(true);
                clientProxy.setGeneric("json-simple");
                clientProxy.afterPropertiesSet();
                thriftClientMap.put(key, clientProxy);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        log.info("updateThriftClientMap configMap size:{}, thriftClientMap size:{}", configMap.size(), thriftClientMap.size());
    }

    private String buildMapKey(WmExtMenuInfoConfig config) {
        return config.getRemoteAppkey() + "-" + config.getGenericServiceName();
    }

    private final static ExecutorService executorService = Rhino.newThreadPool("menu-query-pool",
            DefaultThreadPoolProperties.Setter().withCoreSize(20).withMaxSize(20)).getExecutor();

    public List<MenuInfo> getExtSubMenuInfos(Map<String, SacMenuNodeDto> menuCodeWithNodeMap, IdentityInfo identityInfo) {
        try {
            List<WmExtMenuInfoConfig> configList = filterSubMenuCodes(menuCodeWithNodeMap, identityInfo);
            log.info("getExtSubMenuInfos filterSubMenuCodes:{}", JSON.toJSONString(configList));
            if (CollectionUtils.isEmpty(configList)) {
                return new ArrayList<>();
            }
            List<TaskInfo> taskInfoList = queryTaskInfo(menuCodeWithNodeMap, configList, identityInfo);
            return buildMenuInfo(menuCodeWithNodeMap, taskInfoList, identityInfo);
        } catch (Exception e) {
            log.error("查询歪马扩展待办失败", e);
            return new ArrayList<>();
        }
    }

    private List<TaskInfo> queryTaskInfo(Map<String, SacMenuNodeDto> menuCodeWithNodeMap, List<WmExtMenuInfoConfig> configList, IdentityInfo identityInfo) throws InterruptedException, ExecutionException {
        PoiAccountInfo accountInfo = buildAccountInfo(identityInfo);
        List<TaskInfo> taskInfoList = new ArrayList<>();
        List<Callable<TaskInfo>> tasks = new ArrayList<>();
        for (WmExtMenuInfoConfig config : configList) {
            tasks.add(() -> queryTaskDetail(config, accountInfo));
        }

        // 设置超时时间
        List<Future<TaskInfo>> futures = executorService.invokeAll(tasks, WmExtMenuInfoConfig.getExpiredTime(), TimeUnit.SECONDS);
        for (Future<TaskInfo> future : futures) {
            // 检查任务是否完成
            if (future.isDone() && !future.isCancelled()) {
                TaskInfo taskInfo = future.get();
                if (taskInfo != null) {
                    taskInfoList.add(taskInfo);
                }
            }
        }

        //排序
        return taskInfoList.stream().sorted(Comparator.comparing(en -> {
            SacMenuNodeDto sacMenuNodeDto = menuCodeWithNodeMap.get(en.getTaskType());
            return Objects.isNull(sacMenuNodeDto) ? Integer.MAX_VALUE : sacMenuNodeDto.getRank();
        })).collect(Collectors.toList());
    }

    private PoiAccountInfo buildAccountInfo(IdentityInfo identityInfo) {
        PoiAccountInfo accountInfo = new PoiAccountInfo();
        accountInfo.setTenantId(identityInfo.getUser().getTenantId());
        accountInfo.setAccountId(identityInfo.getUser().getAccountId());
        accountInfo.setPoiIds(identityInfo.getStoreIdList());
        accountInfo.setEmpId(identityInfo.getUser().getEmployeeId());
        return accountInfo;
    }

    private List<WmExtMenuInfoConfig> filterSubMenuCodes(Map<String, SacMenuNodeDto> menuCodeWithNodeMap, IdentityInfo identityInfo) {
        Set<String> subMenuCodes = new HashSet<>(menuCodeWithNodeMap.keySet());
        log.info("WMTaskModelMenuHelper subMenuCodes:{}", JSON.toJSONString(subMenuCodes));
        List<WmExtMenuInfoConfig> configList = WmExtMenuInfoConfig.getConfigList()
                .stream()
                .filter(config -> subMenuCodes.contains(config.getMenuCode()))
                .collect(Collectors.toList());
        // 门店数 > 1，过滤掉不支持多门店的taskType
        if (identityInfo.getStoreIdList().size() > 1) {
            configList = configList.stream().filter(WmExtMenuInfoConfig::isSupportMultiPoi).collect(Collectors.toList());
        }
        return configList;
    }

    private List<MenuInfo> buildMenuInfo(Map<String, SacMenuNodeDto> menuCodeWithNodeMap, List<TaskInfo> input, IdentityInfo identityInfo) {
        Map<Long, PoiInfoDto> poiInfoDtoMap = tenantWrapper.queryPoiByIds(identityInfo.getUser().getTenantId(), identityInfo.getStoreIdList());

        return input.stream().map(entry -> {
                    if (CollectionUtils.isEmpty(entry.getDetailList())) {
                        log.warn("任务查询没有返回数据 taskTypeEnum:{}, tenantId:{}, accountId:{}",
                                entry.getTaskType(), identityInfo.getUser().getTenantId(), identityInfo.getUser().getAccountId());
                        return null;
                    }
                    SacMenuNodeDto sacMenuNodeDto = menuCodeWithNodeMap.get(entry.getTaskType());
                    if (sacMenuNodeDto == null) {
                        return null;
                    }
                    MenuInfo menuInfo = AssistantTaskConverter.convertSacMenu2AssistantMenu(sacMenuNodeDto);

                    int taskCount = entry.getDetailList().stream().mapToInt(TaskDetailInfo::getTaskCount).filter(Objects::nonNull).sum();
                    int delayTaskCount = entry.getDetailList().stream().mapToInt(TaskDetailInfo::getDelayTaskCount).filter(Objects::nonNull).sum();
                    menuInfo.setTaskCount(taskCount);
                    menuInfo.setDelayTaskCount(delayTaskCount);
                    setNearExpirationTaskCount(entry, menuInfo);
                    List<MenuInfo> children = entry.getDetailList().stream().map(detail -> buildDetailMenuInfo(sacMenuNodeDto, poiInfoDtoMap, detail))
                            .filter(poiMenu -> poiMenu.getTaskCount() > 0)
                            .collect(Collectors.toList());
                    menuInfo.setChildren(children);
                    return menuInfo;
                }).filter(Objects::nonNull)
                .filter(menuInfo -> menuInfo.getTaskCount() > 0)
                .collect(Collectors.toList());
    }

    private MenuInfo buildDetailMenuInfo(SacMenuNodeDto sacMenuNodeDto, Map<Long, PoiInfoDto> poiInfoDtoMap, TaskDetailInfo detailData) {
        MenuInfo menuInfo = new MenuInfo();
        menuInfo.setMenuCode(AssistantTaskConverter.POI_NAME_KEY);
        menuInfo.setMenuName(poiInfoDtoMap.get(detailData.getPoiId()) == null ? null : poiInfoDtoMap.get(detailData.getPoiId()).poiName);
        menuInfo.setTaskCount(detailData.getTaskCount());
        menuInfo.setDelayTaskCount(detailData.getDelayTaskCount());
        menuInfo.setUrl(sacMenuNodeDto.getRouteUrl());
        menuInfo.setExtraInfoMap(StringUtils.isBlank(sacMenuNodeDto.getMetaData()) ? new HashMap<>() :
                JacksonUtils.parseMap(sacMenuNodeDto.getMetaData(), String.class, Object.class));
        menuInfo.getExtraInfoMap().put(AssistantTaskConverter.POI_ID_KEY, detailData.getPoiId());
        menuInfo.setTaskQueryType(Integer.valueOf(menuInfo.getExtraInfoMap().getOrDefault(AssistantTaskConverter.TASK_QUERY_TYPE, "1").toString()));
        menuInfo.setHasAuth(true);
        return menuInfo;
    }

    private static void setNearExpirationTaskCount(TaskInfo entry, MenuInfo menuInfo) {
        entry.getDetailList().stream()
                .map(TaskDetailInfo::getNearExpirationTaskCount)
                .filter(Objects::nonNull)
                .reduce(Integer::sum)
                .ifPresent(count -> {
                    Map<String, Object> extraInfoMap = menuInfo.getExtraInfoMap();
                    if (extraInfoMap == null) {
                        extraInfoMap = new HashMap<>();
                    }
                    extraInfoMap.put(NEAR_EXPIRATION_TASK_COUNT, count);
                });
    }

    public TaskInfo queryTaskDetail(WmExtMenuInfoConfig config, PoiAccountInfo poiAccountInfo) throws Exception {
        ThriftClientProxy clientProxy = thriftClientMap.get(buildMapKey(config));
        if (clientProxy == null) {
            log.error("config:{}, clientProxy为空", config);
            return null;
        }

        try {
            //注意GenericService是com.meituan.service.mobile.mtthrift.generic.GenericService
            GenericService genericService = (GenericService) clientProxy.getObject();

            //使用
            List<String> paramTypes = new ArrayList<>();
            paramTypes.add("com.sankuai.meituan.reco.shopmgmt.pieapi.domain.PoiAccountInfo");

            List<String> paramValues = new ArrayList<>();
            String param = JSON.toJSONString(poiAccountInfo);
            paramValues.add(param);

            //调用服务端方法
            String result = genericService.$invoke(config.getMethodName(), paramTypes, paramValues);
            log.info("genericService invoke config:{}, poiAccountInfo:{}, result:{}", config, poiAccountInfo, result);
            if (StringUtils.isBlank(result)) {
                return null;
            }

            //反序列化
            TResult<String> resp = JSON.parseObject(result, TResult.class);
            if (resp == null || !resp.isSuccess() || Objects.isNull(resp.getData())) {
                log.error("genericService invoke exception, config:{}", config);
                return null;
            }
            return JSON.parseObject(resp.getData(), TaskInfo.class);
        } catch (Exception e) {
            log.error("genericService invoke error, config:{}", config, e);
            return null;
        }
    }

    @Data
    @ToString
    public static class TaskInfo {
        @FieldDoc(
                description = "任务类型"
        )
        private String taskType;
        @FieldDoc(
                description = "任务详情列表"
        )
        private List<TaskDetailInfo> detailList;
    }

    @Data
    @ToString
    public static class TaskDetailInfo {
        @FieldDoc(
                description = "门店id"
        )
        private Long poiId;
        @FieldDoc(
                description = "任务数"
        )
        private Integer taskCount;
        @FieldDoc(
                description = "延时任务数"
        )
        private Integer delayTaskCount;

        @FieldDoc(
                description = "临期任务数"
        )
        private Integer nearExpirationTaskCount;
    }
}
