package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2022/3/22 4:20 下午
 **/
@TypeDoc(
        description = "商品修复结果",
        authors = "zhouyan32"
)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProblemSpuOptResponseVO {

    @FieldDoc(
            description = "操作失败列表"
    )
    @ApiModelProperty(value = "操作失败列表", required = true)
    private List<SpuRepairResultVO> failureList;
}
