package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.AbnormalProductInfoDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/3/7
 * @Description
 */
@TypeDoc(
        description = "商品异常信息"
)
@Setter
@Getter
@ToString
public class ProductAbnormalVO {


    @FieldDoc(
            description = "商品异常详情列表"
    )
    private List<ProductAbnormalDetailVO> abnormalDetails;

    public static ProductAbnormalVO of(List<AbnormalProductInfoDTO> abnormalProductInfoDTOList, List<Integer> diffComapreTypeList) {
        ProductAbnormalVO productAbnormalVO = new ProductAbnormalVO();
        productAbnormalVO.setAbnormalDetails(ProductAbnormalDetailVO.ofList(abnormalProductInfoDTOList, null, diffComapreTypeList));
        return productAbnormalVO;
    }
}
