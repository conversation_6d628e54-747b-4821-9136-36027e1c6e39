package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.wavepick.converter;

import com.sankuai.meituan.reco.pickselect.thrift.wave.request.WarehouseQueryPickCompleteRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick.WarehousePickCompleteQueryRequest;
import org.mapstruct.Mapper;

/**
 * @Description:
 * @Author: zhangjian155
 * @Date: 2022/8/25 16:40
 */
@Mapper(componentModel = "spring")
public abstract class WarehousePickCompleteQueryRequestConverter {
    public abstract WarehouseQueryPickCompleteRequest convert2ThriftRequest(WarehousePickCompleteQueryRequest request
            , Long accountId, Long storeId);
}
