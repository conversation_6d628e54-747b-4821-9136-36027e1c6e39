package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.labor;

import com.sankuai.drunkhorsemgmt.labor.common.Status;
import com.sankuai.drunkhorsemgmt.labor.thrift.AttendanceThriftService;
import com.sankuai.drunkhorsemgmt.labor.thrift.approval.AttendanceApprovalThriftService;
import com.sankuai.drunkhorsemgmt.labor.thrift.approval.dto.AttendanceApprovalBaseInfoDTO;
import com.sankuai.drunkhorsemgmt.labor.thrift.dto.ScheduledEmployeeDTO;
import com.sankuai.drunkhorsemgmt.labor.thrift.dto.ShiftDTO;
import com.sankuai.drunkhorsemgmt.labor.thrift.request.LaborAttendanceCheckinReq;
import com.sankuai.drunkhorsemgmt.labor.thrift.request.QueryAttendanceCalendarReq;
import com.sankuai.drunkhorsemgmt.labor.thrift.request.QueryPoiOnDutyEmployeeListRequest;
import com.sankuai.drunkhorsemgmt.labor.thrift.response.BaseResponse;
import com.sankuai.drunkhorsemgmt.labor.thrift.response.QueryAttendanceCalendarResp;
import com.sankuai.drunkhorsemgmt.labor.thrift.response.QueryAttendanceCheckinInfoResp;
import com.sankuai.drunkhorsemgmt.labor.thrift.response.QueryPoiOnDutyEmployeeListResp;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.ScheduledEmployeeVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor.CheckinRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor.CheckinWithPhotoRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor.QueryAttendanceStatisticsRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.QueryCheckinInfoResp;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.AttendanceStatisticsVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.TimeUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.shangou.empower.auth.thrift.service.AuthThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.AccountInfoVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryAccountDetailByTenantIdAndStaffIdsRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryAccountInfoListResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.meituan.reco.shopmgmt.pieapi.converters.labor.AttendanceConverter.*;


/**
 * <AUTHOR>
 * @since 2022/10/19 15:50
 **/
@Service
@Slf4j
public class AttendanceServiceWrapper {
    @Resource
    private AttendanceThriftService attendanceThriftService;

    @Resource
    private AttendanceApprovalThriftService attendanceApprovalThriftService;

    @Resource
    private AuthThriftService.Iface authThriftService;

    @MethodLog(logResponse = true, logRequest = true)
    public QueryCheckinInfoResp queryCheckinInfo() {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        if (Objects.isNull(identityInfo) || Objects.isNull(identityInfo.getUser())) {
            throw new CommonRuntimeException("获取用户信息失败");
        }
        long employeeId = identityInfo.getUser().getEmployeeId();
        String employeeName = identityInfo.getUser().getOperatorName();
        QueryAttendanceCheckinInfoResp resp;
        Map<LocalDate, AttendanceApprovalBaseInfoDTO> infoDTOMap;
        try {
            resp = attendanceThriftService.queryCheckInInfo(employeeId);
            infoDTOMap = attendanceApprovalThriftService.queryExtraWorkApprovalListByDuration(identityInfo.getUser().getTenantId(), employeeId, LocalDate.now(), LocalDate.now());
        } catch (Exception e) {
            log.error("remote invoke AttendanceThriftService.queryCheckInInfo error, employeeId:{}", employeeId, e);
            throw new CommonRuntimeException("查询打卡信息失败,请稍后再试");
        }

        if (Objects.isNull(resp) || Objects.isNull(resp.getStatus()) || resp.getStatus().getCode() != Status.SUCCESS.getCode()) {
            log.error("remote invoke AttendanceThriftService.queryCheckInInfo fail, employeeId:{}, resp:{}", employeeId, resp);
            throw new CommonRuntimeException("查询打卡信息失败,请稍后再试");
        }

        return buildQueryCheckinInfoResp(resp.getAttendanceCheckinInfoDTO(),
                MapUtils.isEmpty(infoDTOMap) ? null : infoDTOMap.get(LocalDate.now()), employeeName);
    }


    public void checkin(CheckinRequest request) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        if (Objects.isNull(identityInfo) || Objects.isNull(identityInfo.getUser())) {
            throw new CommonRuntimeException("获取用户信息失败");
        }

        BaseResponse resp;
        LaborAttendanceCheckinReq checkinReq = buildLaborAttendanceCheckinReq(request, identityInfo);
        try {
            resp = attendanceThriftService.checkin(checkinReq);
        } catch (Exception e) {
            log.error("remote invoke AttendanceThriftService.checkin error, checkinReq: {}", checkinReq, e);
            throw new CommonRuntimeException("考勤打卡失败,请稍后再试");
        }

        if (Objects.isNull(resp) || Objects.isNull(resp.getStatus()) || resp.getStatus().getCode() != Status.SUCCESS.getCode()) {
            log.error("remote invoke AttendanceThriftService.checkin fail, checkinReq: {}, resp:{}", checkinReq, resp);
            throw new CommonRuntimeException(Objects.nonNull(resp) && Objects.nonNull(resp.getStatus()) && StringUtils.isNotBlank(resp.getStatus().getMsg()) ? resp.getStatus().getMsg() : "考勤打卡失败,请稍后再试");
        }
    }


    public CommonResponse<String> checkinWithPhoto(CheckinWithPhotoRequest request) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        if (Objects.isNull(identityInfo) || Objects.isNull(identityInfo.getUser())) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "获取用户信息失败");
        }

        BaseResponse resp;
        LaborAttendanceCheckinReq checkinReq = buildLaborAttendanceCheckinReq(request, identityInfo);
        checkinReq.setPhotoUrl(request.getPhotoUrl());
        try {
            resp = attendanceThriftService.checkin(checkinReq);
        } catch (Exception e) {
            log.error("remote invoke AttendanceThriftService.checkin error, checkinReq: {}", checkinReq, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "考勤打卡失败，请稍后再试");
        }

        if (Objects.isNull(resp) || Objects.isNull(resp.getStatus()) || resp.getStatus().getCode() != Status.SUCCESS.getCode()) {
            log.error("remote invoke AttendanceThriftService.checkin fail, checkinReq: {}, resp:{}", checkinReq, resp);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), Objects.nonNull(resp) && Objects.nonNull(resp.getStatus()) && StringUtils.isNotBlank(resp.getStatus().getMsg()) ? resp.getStatus().getMsg() : "考勤打卡失败，请稍后再试");
        }
        return CommonResponse.success(request.getPhotoUrl());
    }


    public List<AttendanceStatisticsVO> queryAttendanceStatistics(QueryAttendanceStatisticsRequest request) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        if (Objects.isNull(identityInfo) || Objects.isNull(identityInfo.getUser())) {
            throw new CommonRuntimeException("获取用户信息失败");
        }

        Map<LocalDate, AttendanceApprovalBaseInfoDTO> infoDTOMap;
        QueryAttendanceCalendarResp resp;
        QueryAttendanceCalendarReq queryReq = buildQueryAttendanceCalendarReq(request, identityInfo);
        try {
            resp = attendanceThriftService.queryAttendanceCalendar(queryReq);
            infoDTOMap = attendanceApprovalThriftService.queryExtraWorkApprovalListByDuration(identityInfo.getUser().getTenantId(),
                    queryReq.getEmployeeId(), TimeUtils.stringFormatToLocalDate(request.getBeginDate()),
                    TimeUtils.stringFormatToLocalDate(request.getEndDate()));
        } catch (Exception e) {
            log.error("remote invoke AttendanceThriftService.queryAttendanceCalendar error, request: {}", queryReq, e);
            throw new CommonRuntimeException("查看考勤统计失败,请稍后再试");
        }

        if (Objects.isNull(resp) || Objects.isNull(resp.getStatus()) || resp.getStatus().getCode() != Status.SUCCESS.getCode()) {
            log.error("remote invoke AttendanceThriftService.queryAttendanceCalendar fail, request: {}, resp:{}", queryReq, resp);
            throw new CommonRuntimeException("查看考勤统计失败,请稍后再试");
        }

        return buildAttendanceStatisticsVOList(resp.getAttendanceCalendarDTOS(), infoDTOMap);
    }


    public List<ScheduledEmployeeVO> queryStoreScheduledEmployeeList(Long storeId) throws TException {
        QueryPoiOnDutyEmployeeListRequest request = new QueryPoiOnDutyEmployeeListRequest();
        long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        request.setStoreIds(Collections.singletonList(storeId));
        request.setTenantId(tenantId);
        log.info("invoke attendanceThriftService.queryPoiOnDutyEmployeeList start, req:{}", request);
        QueryPoiOnDutyEmployeeListResp onDutyEmployeeListResp = attendanceThriftService.queryPoiOnDutyEmployeeList(request);
        log.info("invoke attendanceThriftService.queryPoiOnDutyEmployeeList end, resp:{}", onDutyEmployeeListResp);
        if (onDutyEmployeeListResp == null || onDutyEmployeeListResp.getStatus() == null) {
            throw new CommonLogicException("查询考勤数据失败");
        }

        if (onDutyEmployeeListResp.getStatus().getCode() != Status.SUCCESS.getCode()) {
            throw new CommonLogicException(StringUtils.defaultIfBlank(onDutyEmployeeListResp.getStatus().getMsg(), "查询考勤数据失败"));
        }

        List<ScheduledEmployeeVO> scheduledEmployeeVOS = toScheduledEmployeeVOList(onDutyEmployeeListResp.getPoiOnDutyEmployeeList().get(storeId));
        fillAccountId(tenantId, scheduledEmployeeVOS);

        return scheduledEmployeeVOS;
    }

    private List<ScheduledEmployeeVO> toScheduledEmployeeVOList(List<ScheduledEmployeeDTO> poiOnDutyEmployeeList) {
        if (CollectionUtils.isEmpty(poiOnDutyEmployeeList)) {
            return Collections.emptyList();
        }

        return poiOnDutyEmployeeList.stream().map(
                        scheduledEmployeeDTO ->
                                new ScheduledEmployeeVO(null,
                                        scheduledEmployeeDTO.getEmployeeId(),
                                        scheduledEmployeeDTO.getEmployeeName(),
                                        Optional.ofNullable(scheduledEmployeeDTO.getShiftDto()).map(ShiftDTO::getId).orElse(null)))
                .collect(Collectors.toList());
    }

    private void fillAccountId(Long tenantId, List<ScheduledEmployeeVO> scheduledEmployeeVOS) throws TException {
        if (CollectionUtils.isEmpty(scheduledEmployeeVOS)) {
            return;
        }
        List<Long> employeeIds = scheduledEmployeeVOS.stream().map(ScheduledEmployeeVO::getEmployeeId).collect(Collectors.toList());
        QueryAccountDetailByTenantIdAndStaffIdsRequest request = new QueryAccountDetailByTenantIdAndStaffIdsRequest(tenantId, employeeIds);
        log.info("invoke authThriftService.queryAccountDetailByTenantIdAndStaffIds start, request:{}", request);
        QueryAccountInfoListResponse response = authThriftService.queryAccountDetailByTenantIdAndStaffIds(request);
        log.info("invoke authThriftService.queryAccountDetailByTenantIdAndStaffIds end, response:{}", response);
        if (response == null || response.getResult() == null) {
            throw new CommonLogicException("查账号信息失败");
        }

        if (response.getResult().getCode() != Status.SUCCESS.getCode()) {
            throw new CommonLogicException(StringUtils.defaultIfBlank(response.getResult().getMsg(), "查账号信息失败"));
        }

        Map<Long, Long> employeeId2AccountIdMap = response.getAccountInfoList().stream().collect(Collectors.toMap(AccountInfoVo::getStaffId, AccountInfoVo::getAccountId));
        for (ScheduledEmployeeVO vo : scheduledEmployeeVOS) {
            if (employeeId2AccountIdMap.containsKey(vo.getEmployeeId())) {
                vo.setAccountId(employeeId2AccountIdMap.get(vo.getEmployeeId()));
            } else {
                scheduledEmployeeVOS.remove(vo);
            }
        }
    }

}
