package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: <EMAIL>
 * @Date: 2020-02-11 19:24
 * @Description:
 */
@TypeDoc(
        description = "简易订单信息"
)
@ApiModel("简易订单信息")
@Data
public class SimpleOrderVO {
    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道ID", required = true)
    private Integer channelId;

    @FieldDoc(
            description = "订单实付金额  单位:元", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单实付金额  单位:元", required = true)
    private String actualPayAmt;

    @FieldDoc(
            description = "订单创建时间戳 单位：秒", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单创建时间戳 单位：秒", required = true)
    private Long createTime;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道订单号", required = true)
    private String channelOrderId;

    @FieldDoc(
            description = "订单状态描述", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单状态描述", required = true)
    private String orderStatusDesc;
}
