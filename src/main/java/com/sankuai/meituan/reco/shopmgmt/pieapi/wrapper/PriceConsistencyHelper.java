package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.google.common.collect.Maps;
import com.meituan.linz.boot.util.Fun;
import com.meituan.linz.product.channel.EnhanceChannelType;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.ChannelStoreCustomSkuKey;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.ChannelStoreSkuKey;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.PriceConsistencyVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price.SkuCanModifyPriceVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.StoreSpuPageQueryResponseVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.StoreSpuVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.price.PrecentChannelPriceWrapper;
import com.sankuai.meituan.shangou.empower.price.client.dto.ChannelStoreSkuDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: wangyihao04
 * @Date: 2021-04-16 11:43
 * @Mail: <EMAIL>
 */
@Service
@Slf4j
public class PriceConsistencyHelper {
    @Resource
    private PrecentChannelPriceWrapper precentChannelPriceWrapper;
    @Resource
    private ChannelActivityWrapper channelActivityWrapper;

    public void addPriceConsistencyAndPromotionInfo(Long tenantId, StoreSpuPageQueryResponseVO responseVO, Boolean asyncQueryPromotion) {
        if (CollectionUtils.isEmpty(responseVO.getStoreSpuList())) {
            return;
        }
        List<ChannelStoreSkuDTO> channelStoreSkuDTOS = responseVO.getStoreSpuList().stream()
                .map(this::extractChannelStoreSkuDTOS)
                .flatMap(List::stream)
                .collect(Collectors.toList());

        Map<ChannelStoreSkuKey, PriceConsistencyVO> priceConsistencyVOMap = getChannelStoreSkuKeyPriceConsistencyVOMap(tenantId, channelStoreSkuDTOS);
        Map<ChannelStoreCustomSkuKey, SkuCanModifyPriceVO> atPromotionMap = isStoreSpuPromotion(tenantId, responseVO.getStoreSpuList());

        fillPriceConsistencyAndPromotionFields(responseVO.getStoreSpuList(), priceConsistencyVOMap, atPromotionMap);
    }

    private Map<ChannelStoreSkuKey, PriceConsistencyVO> getChannelStoreSkuKeyPriceConsistencyVOMap(Long tenantId, List<ChannelStoreSkuDTO> channelStoreSkuDTOS) {
        try {
            return precentChannelPriceWrapper
                    .queryPriceConsistencyBySkuKeys(tenantId, channelStoreSkuDTOS, false, true)
                    .stream()
                    .collect(Collectors.toMap(PriceConsistencyVO::getSkuKey, Function.identity(), (o1, o2) -> o2));
        } catch (Exception e) {
            log.error("查询价格不一致信息失败", e);
            return Maps.newHashMap();
        }
    }


    public void addPriceConsistencyAndPromotionInfo(Long tenantId, StoreSpuVO storeSpuVO) {
        if (CollectionUtils.isEmpty(storeSpuVO.getChannelSpuList())) {
            return;
        }
        List<ChannelStoreSkuDTO> channelStoreSkuDTOS = extractChannelStoreSkuDTOS(storeSpuVO);
        Map<ChannelStoreSkuKey, PriceConsistencyVO> priceConsistencyVOMap = getChannelStoreSkuKeyPriceConsistencyVOMap(tenantId, channelStoreSkuDTOS);

        Map<ChannelStoreCustomSkuKey, SkuCanModifyPriceVO> atPromotionMap = isStoreSpuPromotion(tenantId, Lists.newArrayList(storeSpuVO));

        fillPriceConsistencyAndPromotionFields(Lists.newArrayList(storeSpuVO), priceConsistencyVOMap, atPromotionMap);
    }

    public void addPriceConsistencyAndPromotionInfo(Long tenantId, StoreSpuVO storeSpuVO, Boolean querySkuPromotion) {
        if (CollectionUtils.isEmpty(storeSpuVO.getChannelSpuList())) {
            return;
        }
        List<ChannelStoreSkuDTO> channelStoreSkuDTOS = extractChannelStoreSkuDTOS(storeSpuVO);
        Map<ChannelStoreSkuKey, PriceConsistencyVO> priceConsistencyVOMap = getChannelStoreSkuKeyPriceConsistencyVOMap(tenantId, channelStoreSkuDTOS);
        Map<ChannelStoreCustomSkuKey, SkuCanModifyPriceVO> atPromotionMap = isStoreSpuPromotion(tenantId, storeSpuVO, querySkuPromotion);
        fillPriceConsistencyAndPromotionFields(Lists.newArrayList(storeSpuVO), priceConsistencyVOMap, atPromotionMap);
    }

    private Map<ChannelStoreCustomSkuKey, SkuCanModifyPriceVO> isStoreSpuPromotion(Long tenantId, StoreSpuVO storeSpuVOList, Boolean querySkuPromotion) {
        if (BooleanUtils.isFalse(querySkuPromotion)) {
            return new HashMap<>();
        }
        return isStoreSpuPromotion(tenantId, Lists.newArrayList(storeSpuVOList));
    }

    private Map<ChannelStoreCustomSkuKey, SkuCanModifyPriceVO> isStoreSpuPromotion(Long tenantId, List<StoreSpuVO> storeSpuVOList) {
        if (CollectionUtils.isEmpty(storeSpuVOList)) {
            return new HashMap<>();
        }
        List<ChannelStoreCustomSkuKey> customSkuKeys = storeSpuVOList.stream()
                .filter(storeSpuVO -> CollectionUtils.isNotEmpty(storeSpuVO.getChannelSpuList()))
                .flatMap(storeSpuVO -> storeSpuVO.getChannelSpuList().stream()
                        .filter(channelSpuVO -> EnhanceChannelType.MT.getChannelId().equals(channelSpuVO.getChannelId())
                                && StringUtils.isNotBlank(channelSpuVO.getCustomSpuId())
                                && CollectionUtils.isNotEmpty(channelSpuVO.getChannelSkuList()))
                        .flatMap(channelSpuVO -> channelSpuVO.getChannelSkuList().stream()
                                .filter(channelSkuVO -> StringUtils.isNotBlank(channelSkuVO.getCustomSkuId()))
                                .map(channelSkuVO -> {
                                    ChannelStoreCustomSkuKey customSkuKey = new ChannelStoreCustomSkuKey();
                                    customSkuKey.setStoreId(storeSpuVO.getStore().getStoreId());
                                    customSkuKey.setChannelId(EnhanceChannelType.MT.getChannelId());
                                    customSkuKey.setCustomSpuId(channelSpuVO.getCustomSpuId());
                                    customSkuKey.setCustomSkuId(channelSkuVO.getCustomSkuId());
                                    return customSkuKey;
                                })
                        )
                )
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(customSkuKeys)) {
            return new HashMap<>();
        }
        try {
            List<SkuCanModifyPriceVO> canModifyPriceList= channelActivityWrapper.canModifyPriceByCustomSkuId(tenantId, customSkuKeys);
            return Fun.toMapQuietly(canModifyPriceList, ChannelStoreCustomSkuKey::of);
        } catch (Exception e) {
            log.error("查询商品是否活动信息异常, tenantId:{}, customSkuKeys:{}", tenantId, customSkuKeys, e);
            return Collections.emptyMap();
        }
    }

    private void fillPriceConsistencyAndPromotionFields(List<StoreSpuVO> storeSpuVOList,
                                                        Map<ChannelStoreSkuKey, PriceConsistencyVO> priceConsistencyVOMap,
                                                        Map<ChannelStoreCustomSkuKey, SkuCanModifyPriceVO> atPromotionMap) {
        if (CollectionUtils.isEmpty(storeSpuVOList)) {
            return;
        }

        storeSpuVOList.stream().filter(spuVo -> CollectionUtils.isNotEmpty(spuVo.getChannelSpuList()))
                .forEach(storeSpuVO -> {
                    storeSpuVO.getChannelSpuList().forEach(channelSpuVO -> {
                        if (CollectionUtils.isNotEmpty(channelSpuVO.getChannelSkuList())) {
                            channelSpuVO.getChannelSkuList().forEach(channelSkuVO -> {
                                ChannelStoreCustomSkuKey skuKey = new ChannelStoreCustomSkuKey(storeSpuVO.getStore().getStoreId(),
                                        channelSpuVO.getChannelId(), channelSpuVO.getCustomSpuId(), channelSkuVO.getCustomSkuId());
                                ChannelStoreSkuKey key = new ChannelStoreSkuKey(storeSpuVO.getStore().getStoreId(),
                                        channelSpuVO.getChannelId(), channelSkuVO.getSkuId());
                                PriceConsistencyVO priceConsistencyVO = priceConsistencyVOMap.get(key);
                                channelSkuVO.setPriceEqualSign(Optional.ofNullable(priceConsistencyVO).map(PriceConsistencyVO::getPriceConsistency).orElse(true));
                                channelSkuVO.setPresentPrice(Optional.ofNullable(priceConsistencyVO).map(PriceConsistencyVO::getPresentPrice).orElse(StringUtils.EMPTY));
                                SkuCanModifyPriceVO skuCanModifyPriceVO = atPromotionMap.get(skuKey);
                                channelSkuVO.setAtPromotion(skuCanModifyPriceVO != null && skuCanModifyPriceVO.getAtPromotion());
                                channelSkuVO.setCanModifyPrice(skuCanModifyPriceVO == null || skuCanModifyPriceVO.getCanModifyPrice());
                            });
                        }
                    });
                });

    }

    private List<ChannelStoreSkuDTO> extractChannelStoreSkuDTOS(StoreSpuVO storeSpuVO) {
        List<ChannelStoreSkuDTO> channelStoreSkuDTOS = Lists.newArrayList();
        if (CollectionUtils.isEmpty(storeSpuVO.getChannelSpuList())) {
            return channelStoreSkuDTOS;
        }
        storeSpuVO.getChannelSpuList().forEach(channelSpuVO -> {
            if (CollectionUtils.isNotEmpty(channelSpuVO.getChannelSkuList())) {
                channelSpuVO.getChannelSkuList().forEach(channelSkuVO -> {
                    ChannelStoreSkuDTO storeSkuDTO = new ChannelStoreSkuDTO();
                    storeSkuDTO.setStoreId(storeSpuVO.getStore().getStoreId());
                    storeSkuDTO.setChannelId(channelSpuVO.getChannelId());
                    storeSkuDTO.setSkuId(channelSkuVO.getSkuId());
                    channelStoreSkuDTOS.add(storeSkuDTO);
                });
            }
        });
        return channelStoreSkuDTOS;
    }
}
