package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.price;

import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.converters.PriceConverter;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.PriceConsistencyVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.StoreSkuKey;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price.PresentChannelPriceNotEqualSignPageVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku.StoreSkuBaseDetailVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.AuthThriftWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.OCMSServiceWrapper;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.PermissionGroupTypeEnum;
import com.sankuai.meituan.shangou.empower.price.client.dto.ChannelStoreSkuDTO;
import com.sankuai.meituan.shangou.empower.price.client.dto.present_price.PresentChannelPriceEqualSignDTO;
import com.sankuai.meituan.shangou.empower.price.client.enums.PriceResultEnum;
import com.sankuai.meituan.shangou.empower.price.client.request.present_price.QueryNotEqualSignRequest;
import com.sankuai.meituan.shangou.empower.price.client.request.present_price.QueryPresentChannelPriceEqualSignRequest;
import com.sankuai.meituan.shangou.empower.price.client.response.present_price.QueryNotEqualSignResponse;
import com.sankuai.meituan.shangou.empower.price.client.response.present_price.QueryPresentChannelPriceEqualSignResponse;
import com.sankuai.meituan.shangou.empower.price.client.service.ChannelActivityThriftService;
import com.sankuai.meituan.shangou.empower.price.client.service.PresentChannelPriceThriftService;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PrecentChannelPriceWrapper {

    @Resource
    private AuthThriftWrapper authThriftWrapper;
    @Lazy
    @Resource
    private OCMSServiceWrapper ocmsServiceWrapper;

    @Resource
    private PresentChannelPriceThriftService presentChannelPriceThriftService;

    @Resource
    private ChannelActivityThriftService priceChannelActivityThriftService;

    public static final Integer PAGE_SIZE = 50;

    @MethodLog(logRequest = true, logResponse = true)
    public CommonResponse<PresentChannelPriceNotEqualSignPageVO> queryPresentChannelPriceNotEqualSignSkuList(Long storeId, Integer page, Integer size) throws TException {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();

        // 1.获取当前用户有权限的门店列表
        List<Long> authStoreIds = authThriftWrapper.queryPermissionGroupId(user, PermissionGroupTypeEnum.POI,
                Long::valueOf);
        if (!authStoreIds.contains(storeId)) {
            log.warn("当前用户无门店权限，无请求数据");
            PageInfoVO pageInfo = PageInfoVO.builder().page(page).size(size).totalPage(0).totalSize(0).build();
            PresentChannelPriceNotEqualSignPageVO pageVO = PresentChannelPriceNotEqualSignPageVO.builder()
                    .notEqualSignSkus(Lists.newArrayList()).pageInfo(pageInfo).build();
            return CommonResponse.success(pageVO);
        }

        // 2.获取不一致问题商品列表
        QueryNotEqualSignRequest request = QueryNotEqualSignRequest.builder()
                .tenantId(user.getTenantId())
                .storeIds(Lists.newArrayList(storeId))
                .asceSeq(false)
                .pageSize(size)
                .pageNum(page)
                .build();
        QueryNotEqualSignResponse response = presentChannelPriceThriftService.queryNotEqualSignSkuList(request);
        if (Objects.isNull(response) || Objects.isNull(response.getStatus())) {
            return CommonResponse.fail2(ResultCode.FAIL);
        }
        if (response.getStatus().getCode().compareTo(PriceResultEnum.SUCCESS.getCode()) != 0) {
            return CommonResponse.fail(response.getStatus().getCode(), response.getStatus().getMsg(), null);
        }

        // 3.获取门店商品详情数据
        Map<StoreSkuKey, StoreSkuBaseDetailVO> skuDetailMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(response.getPriceNotEqualSignSkuList())) {
            skuDetailMap = querySkuImgList(user.getTenantId(), response.getPriceNotEqualSignSkuList());
        }

        return PriceConverter.convert2NotEqualSignPageVO(response, skuDetailMap);
    }


    private Map<StoreSkuKey, StoreSkuBaseDetailVO> querySkuImgList(Long tenantId, List<PresentChannelPriceEqualSignDTO> skuSignDTOS) {

        if (CollectionUtils.isEmpty(skuSignDTOS)) {
            return Maps.newHashMap();
        }

        Map<Long, Set<String>> storeSkus = skuSignDTOS.stream().map(PresentChannelPriceEqualSignDTO::getSkuKey)
                .collect(Collectors.groupingBy(ChannelStoreSkuDTO::getStoreId,
                        Collectors.mapping(ChannelStoreSkuDTO::getSkuId, Collectors.toSet())));
        // 获取渠道商品信息
        Map<StoreSkuKey, StoreSkuBaseDetailVO> skuDetailMap = Maps.newHashMap();
        storeSkus.entrySet().stream().forEach(entry -> {
            try {
                List<StoreSkuBaseDetailVO> baseDetailVOS = ocmsServiceWrapper.queryStoreSkuInfoBySkuIds(tenantId,
                        entry.getKey(), Lists.newArrayList(entry.getValue()), false);
                Map<StoreSkuKey, StoreSkuBaseDetailVO> tmpDetailMap = baseDetailVOS.stream()
                        .collect(Collectors.toMap(StoreSkuBaseDetailVO::genStoreSkuKey, Function.identity()));
                skuDetailMap.putAll(tmpDetailMap);
            } catch (Exception e) {
                log.info("查询门店商品信息异常. storeId [{}], skuIds [{}].", entry.getKey(), entry.getValue(), e);
            }
        });

        return skuDetailMap;
    }

    public List<PriceConsistencyVO> queryPriceConsistencyBySkuKeys(Long tenantId, List<ChannelStoreSkuDTO> skuDTOS,
                                                                   Boolean withPromotion, Boolean withPresentPrice) {
        Preconditions.checkArgument(tenantId != null && tenantId > 0);
        if (CollectionUtils.isEmpty(skuDTOS)){
            return Lists.newArrayList();
        }

        return Lists.partition(skuDTOS, PAGE_SIZE).stream()
                .map(part -> doQueryPriceConsistencyBySkuKeys(tenantId, part, withPromotion, withPresentPrice))
                .flatMap(List::stream)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private List<PriceConsistencyVO> doQueryPriceConsistencyBySkuKeys(Long tenantId, List<ChannelStoreSkuDTO> skuDTOS,
                                                                      Boolean withPromotion, Boolean withPresentPrice){
        if (CollectionUtils.isEmpty(skuDTOS)){
            return Lists.newArrayList();
        }
        QueryPresentChannelPriceEqualSignRequest request = new QueryPresentChannelPriceEqualSignRequest();
        request.setTenantId(tenantId);
        request.setChannelStoreSkuList(skuDTOS);
        request.setWithPromotionInfo(withPromotion);
        request.setWithPresentPrice(withPresentPrice);
        try {
            QueryPresentChannelPriceEqualSignResponse response = presentChannelPriceThriftService
                    .queryPresentChannelPriceEqualSign(request);
            if (response.getStatus().getCode() != PriceResultEnum.SUCCESS.getCode()){
                throw new IllegalStateException(response.getStatus().getMsg());
            }
            return ConverterUtils
                    .convertList(response.getPresentChannelPriceEqualSignDTOList(), PriceConsistencyVO::valueOf);

        } catch (Exception e) {
            log.warn("查询价格不一致信息异常，msg {}", e.getMessage());
            return Lists.newArrayList();
        }
    }
}
