package com.sankuai.meituan.reco.shopmgmt.pieapi.domain;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TypeDoc(
        description = "设备pushToken/unionId"
)
@Data
@ApiModel("设备pushToken/unionId")
public class PushConfigRequest {
    @FieldDoc(
            description = "pushToken"
    )
    @ApiModelProperty(value = "pushToken")
    private String pushToken;
    @FieldDoc(
            description = "unionId"
    )
    @ApiModelProperty(value = "unionId")
    private String unionId;
}
