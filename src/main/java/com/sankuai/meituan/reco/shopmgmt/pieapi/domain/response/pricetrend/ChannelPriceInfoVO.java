package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pricetrend;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TypeDoc(
        description = "渠道价格信息",
        authors = "hejunliang"
)
@ApiModel("渠道价格信息")
@Data
public class ChannelPriceInfoVO {
    @FieldDoc(
            description = "当前渠道价格, 单位：元（线上渠道表示线上价格，线下渠道表示线下价格）"
    )
    @ApiModelProperty(name = "当前渠道价格, 单位：元（线上渠道表示线上价格，线下渠道表示线下价格）")
    private String price;

    @FieldDoc(
            description = "当前渠道市斤价格, 单位：元（线上渠道表示线上价格，线下渠道表示线下价格）"
    )
    @ApiModelProperty(name = "当前渠道市斤价格, 单位：元（线上渠道表示线上价格，线下渠道表示线下价格）")
    private String pricePer500g;
}