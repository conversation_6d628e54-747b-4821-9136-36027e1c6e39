/*
 * Copyright (c) 2019 Meituan.com. All Rights Reserved.
 */
package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.impl;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.AbstractSinglePendingTaskService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.DeliveryWrapper;
import com.sankuai.meituan.reco.store.management.thrift.delivery.ObtainWaitCountRequest;
import com.sankuai.meituan.reco.store.management.thrift.delivery.ObtainWaitCountResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 有单收货未完成量查询
 * <br><br>
 * Author: linji<PERSON>u <br>
 * Date: 2019-03-27 Time: 15:10
 * @since 2.1 权限迁移版本
 */
@Service
public class DeliveryPendingTaskService extends AbstractSinglePendingTaskService {

    @Autowired
    private DeliveryWrapper deliveryWrapper;

    @Override
    public PendingTaskResult getPendingTaskCount(PendingTaskParam param) throws Exception {
        ObtainWaitCountRequest request = new ObtainWaitCountRequest();
        request.setTenantId(param.getTenantId()).setEntityId(param.getEntityId()).setEntityType(param.getEntityType());
        ObtainWaitCountResult result = deliveryWrapper.obtainWaitCount(request);
        int count = result == null ? 0 : result.getCount();
        return PendingTaskResult.createNumberMarker(count);
    }

    @Override
    protected AuthCodeEnum module() {
        return AuthCodeEnum.WITH_BILL_RECEIVE;
    }
}
