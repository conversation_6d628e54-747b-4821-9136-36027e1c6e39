package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.handler;

import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.ProblemSpuCountQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.AbnormalRuleNodeVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.facade.EmpowerSpuFacade;
import com.sankuai.meituan.reco.shopmgmt.pieapi.facade.item.AbnormalProductClient;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.command.PageQueryPoiSpuCommand;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.result.PageQueryPoiSpuResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024/7/24
 */
@Slf4j
public abstract class AbstractCountStoreSpuHandler {

    @Resource
    public EmpowerSpuFacade empowerSpuFacade;


    public abstract Integer getChannelId();

    public abstract String getCountCode();

    public abstract List<String> getAbnormalCodes();

    public boolean isMatch(Set<Integer> poiChannels){
        if (CollectionUtils.isEmpty(poiChannels)){
            return false;
        }
        return poiChannels.contains(getChannelId());
    }

    public Integer process(Long tenantId, ProblemSpuCountQueryRequest request, Map<String, AbnormalRuleNodeVO> abnormalRuleMap){
        Integer spuCount = 0;
        try {
            List<String> lastAbnormalCodes = AbnormalProductClient.matchAbnormalCodes(getAbnormalCodes(), abnormalRuleMap);
            if (CollectionUtils.isEmpty(lastAbnormalCodes)){
                log.warn("父异常类型匹配子异常码为空，tenantId: {} request:{} abnormalRuleMap: {}", tenantId, request, abnormalRuleMap);
                throw new CommonRuntimeException("父异常类型匹配子异常码为空");
            }
            PageQueryPoiSpuCommand command = request.toRequest(tenantId, lastAbnormalCodes);
            PageQueryPoiSpuResult result = empowerSpuFacade.pageQueryStoreSpu(command);

            if(Objects.nonNull(result) && Objects.nonNull(result.getStatus())
                    && result.getStatus().getCode() == ResultCode.SUCCESS.getCode()){
                spuCount = result.getPage().getTotalCount();
            }
        }catch (Exception e){
            log.error("门店商品统计失败 tenantId: {} request:{} abnormalRuleMap: {}", tenantId, request, abnormalRuleMap, e);
            throw new CommonRuntimeException(e);
        }

        return spuCount;
    }
}
