package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.VideoInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ReviewVideoInfoDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2022/3/18 4:27 下午
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VideoInfoVO {

    /**
     * 视频链接
     */
    private String videoUrl;

    /**
     * 视频封面Url
     */
    private String coverImageUrl;

    public static VideoInfoVO of(VideoInfoDTO video) {
        return Objects.nonNull(video) ? new VideoInfoVO(video.getVideoUrl(), video.getCoverImageUrl()) : null;
    }

    public static VideoInfoVO of(com.sankuai.meituan.shangou.empower.productbiz.client.dto.VideoInfoDTO video) {
        return Objects.nonNull(video) ? new VideoInfoVO(video.getVideoUrl(), video.getCoverImageUrl()) : null;
    }

    public ReviewVideoInfoDTO toDTO(){
        return new ReviewVideoInfoDTO(this.videoUrl,this.coverImageUrl);
    }

    public VideoInfoDTO toVideoInfoDTO(){
        return new VideoInfoDTO(this.videoUrl,this.coverImageUrl);
    }

    public com.sankuai.meituan.shangou.empower.productbiz.client.dto.VideoInfoDTO toBizVideoInfoDTO() {
        return new com.sankuai.meituan.shangou.empower.productbiz.client.dto.VideoInfoDTO(this.videoUrl, this.coverImageUrl);
    }

}
