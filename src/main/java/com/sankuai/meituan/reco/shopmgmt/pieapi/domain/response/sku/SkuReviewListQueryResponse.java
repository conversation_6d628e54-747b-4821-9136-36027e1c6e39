package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SkuReviewListQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.SkuReviewVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.QuerySkuReviewListResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.SkuReviewDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.review.ReviewTenantSpuDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.review.PageQueryReviewProductResponse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @description: 查询审核信息列表响应
 * @author: WangSukuan
 * @create: 2020-03-10
 **/
@TypeDoc(
        description = "查询审核信息列表响应"
)
@Data
@ApiModel("查询审核信息列表响应")
public class SkuReviewListQueryResponse {

    @FieldDoc(description = "返回数据")
    @ApiModelProperty(name = "返回数据")
    private List<SkuReviewVO> dataList = new ArrayList<>();

    @FieldDoc(description = "分页信息")
    @ApiModelProperty(name = "分页信息")
    private PageInfoVO pageInfoVO;


    public SkuReviewListQueryResponse buildSkuReviewListQueryResponse(QuerySkuReviewListResponse querySkuReviewListResponse){

        this.setPageInfoVO(new PageInfoVO().buildPageInfoVO(querySkuReviewListResponse.getPageInfo()));
        List<SkuReviewDTO> responseDataList = querySkuReviewListResponse.getDataList();
        if (CollectionUtils.isEmpty(responseDataList)){
            return this;
        }
        for (SkuReviewDTO skuReviewDTO : responseDataList){
            this.getDataList().add(new SkuReviewVO().buildSkuReviewVO(skuReviewDTO));
        }
        return this;

    }

    public static SkuReviewListQueryResponse fromPageQueryReviewProductResponse(PageQueryReviewProductResponse response) {
        SkuReviewListQueryResponse skuReviewListQueryResponse = new SkuReviewListQueryResponse();
        skuReviewListQueryResponse.setPageInfoVO(PageInfoVO.buildPageInfoVO(response.getPageInfo()));
        skuReviewListQueryResponse.getDataList().addAll(Fun.map(response.getList(), reviewTenantSpuDTO -> new SkuReviewVO().buildSkuReviewVO(reviewTenantSpuDTO)));
        return skuReviewListQueryResponse;
    }

    public static SkuReviewListQueryResponse buildEmptyResponse(SkuReviewListQueryRequest request) {
        SkuReviewListQueryResponse response = new SkuReviewListQueryResponse();
        response.setDataList(Collections.emptyList());
        PageInfoVO pageInfo = new PageInfoVO();
        pageInfo.setPage(request.getPageNum());
        pageInfo.setSize(request.getPageSize());
        pageInfo.setTotalPage(0);
        pageInfo.setTotalSize(0);
        response.setPageInfoVO(pageInfo);
        return response;
    }
}
