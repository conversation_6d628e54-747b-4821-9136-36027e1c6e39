package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask;

import com.meituan.mtrace.thread.TraceRunnable;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import com.sankuai.meituan.shangou.empower.auth.sdk.bean.SessionInfo;
import com.sankuai.meituan.shangou.empower.auth.sdk.context.SessionContext;

/***
 * author : <EMAIL> 
 * data : 2020/4/9 
 * time : 上午10:23
 **/
public class PendingTaskRunnable extends TraceRunnable {

    /**
     * 获取到调用线程的 ThreadLocal 信息
     */
    private final IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
    private final SessionInfo sessionInfo = SessionContext.getCurrentSession();

    public PendingTaskRunnable(Runnable runnable) {
        super(runnable);
    }

    @Override
    public void run() {
        IdentityInfo elderInstance = ApiMethodParamThreadLocal.getIdentityInfo();
        SessionInfo elderSessionInfo = SessionContext.getCurrentSession();
        // 设置调用线程的 ThreadLocal 信息到当前线程
        ApiMethodParamThreadLocal.getInstance().set(identityInfo);
        SessionContext.init(sessionInfo);
        try {
            super.run();
        }finally {
            if (elderInstance != null){
                ApiMethodParamThreadLocal.getInstance().set(elderInstance);
            }
            if (elderSessionInfo != null) {
                SessionContext.init(elderSessionInfo);
            }
        }
    }
}
