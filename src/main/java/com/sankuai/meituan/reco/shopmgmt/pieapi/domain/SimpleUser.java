package com.sankuai.meituan.reco.shopmgmt.pieapi.domain;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.AccountUserDto;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * SimpleUser
 *
 * <AUTHOR>
 * @since 2024/08/27
 */
@Getter
@Setter
@ToString
public class SimpleUser {

    private long tenantId;

    private long employeeId;

    private long accountId;

    private String accountName;

    public static SimpleUser build(User user) {
        if (user == null) {
            return null;
        }
        SimpleUser simpleUser = new SimpleUser();
        simpleUser.setTenantId(user.getTenantId());
        simpleUser.setEmployeeId(user.getEmployeeId());
        simpleUser.setAccountId(user.getAccountId());
        simpleUser.setAccountName(user.getAccountName());
        return simpleUser;
    }

    public static SimpleUser buildByAccountUserDto(long tenantId, AccountUserDto accountUserDto) {
        if (accountUserDto == null) {
            return null;
        }
        SimpleUser simpleUser = new SimpleUser();
        simpleUser.setTenantId(tenantId);
        if (accountUserDto.getEmployeeId() != null) {
            simpleUser.setEmployeeId(accountUserDto.getEmployeeId());
        }
        if (accountUserDto.getAccountId() != null) {
            simpleUser.setAccountId(accountUserDto.getAccountId());
        }
        simpleUser.setAccountName(accountUserDto.getAccountName());
        return simpleUser;
    }
}
