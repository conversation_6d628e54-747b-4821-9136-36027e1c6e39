package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022-12-08
 * @email <EMAIL>
 */
@TypeDoc(
        description = "查询员工下车列表"
)
@Data
@ApiModel("查询员工下车列表")
@AllArgsConstructor
@NoArgsConstructor
public class QueryWaitToResignApplyListRequest {

    @FieldDoc(
            description = "页码"
    )
    private Integer pageNo;

    @FieldDoc(
            description = "一页大小"
    )
    private Integer pageSize;

}
