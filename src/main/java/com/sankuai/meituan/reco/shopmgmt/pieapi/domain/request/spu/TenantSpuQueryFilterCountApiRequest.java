package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import java.util.stream.Collectors;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.shangou.empower.ocms.client.product.enums.ChannelAuditStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.client.product.enums.ChannelNormAuditStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.client.product.request.TenantSpuQueryFilterCountRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Title: TenantSpuQueryFilterCountApiRequest
 * @Description: 查询筛选项商品数量请求参数
 * @Author: wuyongjiang
 * @Date: 2022/8/30 18:55
 */
@TypeDoc(
        description = "查询筛选项商品数量请求参数"
)
@Data
@ApiModel("查询筛选项商品数量请求参数")
public class TenantSpuQueryFilterCountApiRequest {
    @FieldDoc(
            description = "是否无图，1：查无图商品", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否无图，1：查无图商品")
    private int noPic = 1;

    @FieldDoc(
            description = "是否可售; 0 全部 1 可售 2 不可售", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "spu")
    private int mtAllowSale = 2;


    @FieldDoc(
        description = "是否无动态信息; 0 全部 1 无", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否无动态信息")
    private int noDynamicInfo = 1;

    @FieldDoc(
            description = "查无饿了么类目属性；默认0；需要查1",requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "查无饿了么类目属性")
    private Integer noEleCategoryProperties;

    @FieldDoc(
            description = "统计不可售异常",requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "统计不可售异常")
    private Boolean hasNoSaleAbnormal;

    public static TenantSpuQueryFilterCountRequest toRpcRequest(TenantSpuQueryFilterCountApiRequest request, User user){
        TenantSpuQueryFilterCountRequest rpcRequest = new TenantSpuQueryFilterCountRequest();
        rpcRequest.setTenantId(user.getTenantId());
        rpcRequest.setNoPic(request.getNoPic());
        rpcRequest.setAuditStatusList(ChannelAuditStatusEnum.ofAllowSale(request.getMtAllowSale()).stream().map(ChannelAuditStatusEnum::getCode).collect(Collectors.toList()));
        rpcRequest.setNormAuditStatusList(ChannelNormAuditStatusEnum.ofAllowSale(request.getMtAllowSale()).stream().map(ChannelNormAuditStatusEnum::getCode).collect(Collectors.toList()));
        rpcRequest.setNoDynamicInfo(request.getNoDynamicInfo());
        rpcRequest.setNoEleCategoryProperties(request.getNoEleCategoryProperties());

        return rpcRequest;
    }
}
