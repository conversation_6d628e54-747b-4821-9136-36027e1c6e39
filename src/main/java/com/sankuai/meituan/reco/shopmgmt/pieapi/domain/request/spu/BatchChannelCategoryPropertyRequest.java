package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.MapUtils;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-02-12
 */
@TypeDoc(
        name = "渠道类目属性批量请求",
        description = "渠道类目属性批量请求"
)
@Data
public class BatchChannelCategoryPropertyRequest {
    @FieldDoc(
            description = "渠道编码和对应的类目编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道编码和对应的类目编码")
    private Map<Integer, List<String>> channelCategoryCodeMap;

    @FieldDoc(
            description = "商品SPU ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品SPU ID")
    private String spuId;

    public boolean validate() {
        return MapUtils.isNotEmpty(channelCategoryCodeMap);
    }
}
