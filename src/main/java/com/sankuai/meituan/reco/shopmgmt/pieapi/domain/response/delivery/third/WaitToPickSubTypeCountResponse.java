package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.delivery.third;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @since 2023/8/30 15:37
 **/
public class WaitToPickSubTypeCountResponse {
    @FieldDoc(
            description = "待拣货数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "待拣货数量", required = true)
    private Integer waitPickCount;
}
