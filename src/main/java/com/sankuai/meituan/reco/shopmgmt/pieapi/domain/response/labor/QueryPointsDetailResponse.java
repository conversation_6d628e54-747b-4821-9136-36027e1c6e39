package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.DatePointVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-03-15
 * @email <EMAIL>
 */
@ApiModel("查询待申请列表返回")
@TypeDoc(description = "查询待申请列表返回")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryPointsDetailResponse {

    @FieldDoc(description = "是否有更多")
    @ApiModelProperty("是否有更多")
    public Boolean isSettling;

    @FieldDoc(description = "是否有更多")
    @ApiModelProperty("是否有更多")
    public Boolean hasMore;

    @FieldDoc(description = "总数")
    @ApiModelProperty("总数")
    public Long total;

    @FieldDoc(description = "月度总积分")
    @ApiModelProperty("月度总积分")
    public String monthTotalPoints;

    @FieldDoc(description = "日积分明细list")
    @ApiModelProperty("日积分明细list")
    public List<DatePointVO> datePointList;

}
