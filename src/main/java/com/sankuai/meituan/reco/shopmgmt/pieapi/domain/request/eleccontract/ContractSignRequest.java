package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.eleccontract;

import com.facebook.swift.codec.ThriftField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * @Author: <EMAIL>
 * @Date: 2020/3/16 19:55
 * @Description:
 */
@TypeDoc(
        description = "电子协议签署请求"
)
@Data
public class ContractSignRequest {

    @FieldDoc(
            description = "门店ID"
    )
    private Long storeId;

    @FieldDoc(
            description = "商家名称"
    )
    private String merchantName;

    @FieldDoc(
            description = "门店地址"
    )
    private String address;
    @FieldDoc(
            description = "法人姓名"
    )
    private String legalPerson;

    @FieldDoc(
            description = "签署人姓名"
    )
    private String signerName;

    @FieldDoc(
            description = "签署人电话"
    )
    private String signerPhoneNum;

    @FieldDoc(
            description = "邮箱"
    )
    private String email;


    @FieldDoc(
            description = "短信验证请求编码"
    )
    private String requestCode;


    @FieldDoc(
            description = "短信验证码"
    )
    private String responseCode;


    @FieldDoc(
            description = "协议ID"
    )
    private Long contractId;

    @FieldDoc(
            description = "确认函ID"
    )
    private Long confirmLetterId;

    @FieldDoc(
            description = "资质主体外卖门店id"
    )
    private Long subjectWmPoiId;

}
