/*
 * Copyright (c) 2019 Meituan.com. All Rights Reserved.
 */
package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.impl;

import com.dianping.cat.util.MetricHelper;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OCMSListWaitAuditOrderRequest;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OcmsWaitToRefundGoodsOrderSubTypeCountResponse;
import com.meituan.shangou.saas.order.management.client.enums.SortByEnum;
import com.meituan.shangou.saas.order.management.client.service.online.OCMSQueryThriftService;
import com.meituan.shangou.saas.order.platform.enums.AfterSaleApplyStatusEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiEntityTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.AbstractSinglePendingTaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;


@Slf4j
@Service
public class OrderTabAfterSalePendingTaskService extends AbstractSinglePendingTaskService {

    @Resource
    private OCMSQueryThriftService ocmsQueryThriftService;

    @Override
    public PendingTaskResult getPendingTaskCount(PendingTaskParam param) throws Exception {
        int count=0;
        OCMSListWaitAuditOrderRequest ocmsListWaitAuditOrderRequest = buildOCMSListWaitAuditOrderRequest(param.getTenantId(), param.getStoreIds(), 1, 0, param.getEntityType());
        try {
            log.info("OrderService.queryWaitAuditRefundGoodsBySubtypeCount  调用ocmsQueryThriftService.queryWaitToRefundGoodsBySubtypeCount request:{}", ocmsListWaitAuditOrderRequest);
            OcmsWaitToRefundGoodsOrderSubTypeCountResponse response = ocmsQueryThriftService.queryWaitToRefundGoodsBySubtypeCount(ocmsListWaitAuditOrderRequest);
            log.info("OrderService.queryWaitAuditRefundGoodsBySubtypeCount  调用ocmsQueryThriftService.queryWaitToRefundGoodsBySubtypeCount response:{}", response);
            if (ResultCode.SUCCESS.getCode()==response.getStatus().getCode()){
                count=response.getAllSubTypeCount();
            }
            return PendingTaskResult.createNumberMarker(count);
        } catch (Exception e) {
            MetricHelper.build().name("order.buildOCMSListWaitAuditOrderRequest.err").tag("tenantId", String.valueOf(param.getTenantId())).tag("storeId", String.valueOf(firstShopId(param.getStoreIds()))).count();
            log.error("OrderService.queryWaitAuditRefundGoodsBySubtypeCount  调用ocmsQueryThriftService.queryWaitToRefundGoodsBySubtypeCount error", e);
            throw new CommonRuntimeException(e);
        }
    }

    private long firstShopId(List<Long> storeIdList) {
        if (CollectionUtils.isNotEmpty(storeIdList)){
            return storeIdList.get(0);
        }
        return 0;
    }

    private OCMSListWaitAuditOrderRequest buildOCMSListWaitAuditOrderRequest(Long tenantId, List<Long> storeIdList, Integer page, Integer size, Integer entityType) {
        OCMSListWaitAuditOrderRequest ocmsListWaitAuditOrderRequest = new OCMSListWaitAuditOrderRequest();
        ocmsListWaitAuditOrderRequest.setTenantId(tenantId);
        ocmsListWaitAuditOrderRequest.setShopIdList(storeIdList);
        if (Objects.equals(PoiEntityTypeEnum.SHAREABLE_WAREHOUSE.code(), entityType)) {
            ocmsListWaitAuditOrderRequest.setShopIdList(Lists.newArrayList());
            ocmsListWaitAuditOrderRequest.setWarehouseIdList(storeIdList);
        }
        ocmsListWaitAuditOrderRequest.setPage(page);
        ocmsListWaitAuditOrderRequest.setSize(size);
        ocmsListWaitAuditOrderRequest.setBeginCreateTime(0L);
        ocmsListWaitAuditOrderRequest.setEndCreateTime(System.currentTimeMillis());
        ocmsListWaitAuditOrderRequest.setAfterSaleApplyStatusList(Lists.newArrayList(AfterSaleApplyStatusEnum.COMMIT.getValue()));
        ocmsListWaitAuditOrderRequest.setRealTime(true);
        ocmsListWaitAuditOrderRequest.setSort(SortByEnum.ASC);
        return ocmsListWaitAuditOrderRequest;
    }

    @Override
    protected AuthCodeEnum module() {
        return AuthCodeEnum.ORDER_TAB_AFTER_SALES;
    }
}
