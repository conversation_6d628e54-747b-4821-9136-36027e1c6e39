package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


/**
 * <AUTHOR> wangheng34
 * @since : 2023/10/20 14:45 下午
 */

@TypeDoc(
        description = "门店商品字段属性配置")
@Getter
@Setter
public class StoreProductFieldsVO {

    @FieldDoc(
            description = "松鼠加盟商受控的门店商品，受加盟主控制的字段，门店不可编辑")
    @ApiModelProperty(name = "松鼠加盟商受控的门店商品，受加盟主控制的字段，门店不可编辑")
    private List<String> controlFieldsList;
}