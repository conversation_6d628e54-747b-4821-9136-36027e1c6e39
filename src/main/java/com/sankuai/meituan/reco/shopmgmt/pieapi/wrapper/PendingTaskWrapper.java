package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.google.common.collect.Lists;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pendingtask.PendingTaskQueryRequest;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pendingtask.PendingTaskCountVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pendingtask.PendingTaskQueryResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.PendingTaskAsyncService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.IdentityInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @createTime 2019/11/25
 * @description 代办数量查询
 */
@Slf4j
@Component
public class PendingTaskWrapper {


    @Resource
    PendingTaskAsyncService pendingTaskAsyncService;

    /**
     * 查询代办数量
     * @return
     */
    public CommonResponse<PendingTaskQueryResponse> queryPendingTask(PendingTaskQueryRequest request) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        List<PendingTaskCountVO> pendingTaskCountVOList = Lists.newArrayList();
        Map<String, PendingTaskResult> pendingTaskResultMap = queryPendingTask(identityInfo.getUser().getTenantId(), identityInfo.getStoreIdList(), request.getEntityType(), request.getEntityId(), request.getAuthCodes());
        if (MapUtils.isNotEmpty(pendingTaskResultMap)) {
            for (Map.Entry<String, PendingTaskResult> pendingTaskResultEntry : pendingTaskResultMap.entrySet()) {
                PendingTaskCountVO pendingTaskCountVO = new PendingTaskCountVO();
                pendingTaskCountVO.setAuthCode(pendingTaskResultEntry.getKey());
                pendingTaskCountVO.setCount(pendingTaskResultEntry.getValue().getCount());
                pendingTaskCountVOList.add(pendingTaskCountVO);
            }
        }
        PendingTaskQueryResponse taskQueryResponse = new PendingTaskQueryResponse();
        taskQueryResponse.setPendingTaskCountVOList(pendingTaskCountVOList);
        return CommonResponse.success(taskQueryResponse);
    }

    public  Map<String, PendingTaskResult> queryPendingTask(Long tenantId, List<Long> shopId, Integer entityType, Long entityId, Set<String> authCodes) {
        Map<String, PendingTaskResult> pendingTaskResultMap = pendingTaskAsyncService.queryPendingTaskCount(tenantId, entityId, entityType, shopId, authCodes);
        log.info("红点数字结果:{}", pendingTaskResultMap);
        return pendingTaskResultMap;
    }
}
