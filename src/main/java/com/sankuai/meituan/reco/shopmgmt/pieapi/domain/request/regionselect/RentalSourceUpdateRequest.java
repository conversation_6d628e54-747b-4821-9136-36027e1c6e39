package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.regionselect;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/12/7
 */
@TypeDoc(description = "仓源编辑请求")
@Data
public class RentalSourceUpdateRequest {

    @FieldDoc(description = "仓源id, 不传时视为新建")
    private Long sourceId;

    @FieldDoc(description = "仓源经纬度")
    private String coordinate;

    @FieldDoc(description = "仓源名")
    private String sourceName;

    @FieldDoc(description = "仓源表单")
    private Map<String, String> formItems = new HashMap<>();

    @FieldDoc(description = "表单版本")
    private Long formVersion;
}
