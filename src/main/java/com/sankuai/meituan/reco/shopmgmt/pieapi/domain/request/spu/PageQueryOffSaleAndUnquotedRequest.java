package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * @author: zhangbo
 * @date: 2020-05-15 17:02
 */
@TypeDoc(
        description = "查询已上架已下架商品数请求"
)
@Data
@ApiModel("按天查未报价/未上架商品")
public class PageQueryOffSaleAndUnquotedRequest {

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID", required = true)
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "查询天数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "查询天数", required = true)
    private Integer days;

    @FieldDoc(
            description = "分渠道门店分类信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "分渠道门店分类信息", required = true)
    private Map<Integer,String> channelStoreCategoryMap;

    @FieldDoc(
            description = "分类状态 1-未分类 2-有分类", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "分类状态 1-未分类 2-有分类", required = true)
    private Integer hasStoreCategory;

    @FieldDoc(
            description = "查询类型 1-未上架 2-未报价", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "查询类型 1-未上架 2-未报价", required = true)
    private Integer queryType;

    @FieldDoc(
            description = "第几页", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "第几页", required = true)
    @NotNull
    private Integer page;

    @FieldDoc(
            description = "每页行数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "每页行数", required = true)
    @NotNull
    private Integer size;
}
