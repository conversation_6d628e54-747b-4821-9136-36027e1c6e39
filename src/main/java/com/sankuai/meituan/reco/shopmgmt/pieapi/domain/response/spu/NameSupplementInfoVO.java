package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import java.util.Objects;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.NameSupplementDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.NameSupplementBizDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 商品名称补充语信息
 * 美团医药类目下适用
 *
 * <AUTHOR>
 * @since 2025/04/22
 */
@TypeDoc(
        name = "商品名称补充语VO对象",
        description = "商品名称补充语信息"
)
@ApiModel("商品名称补充语信息")
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class NameSupplementInfoVO {

    @FieldDoc(
            description = "名称补充语"
    )
    @ApiModelProperty(name = "名称补充语")
    private String nameSupplement;

    @FieldDoc(
            description = "名称补充语顺序 0-后置, 1-前置"
    )
    @ApiModelProperty(name = "名称补充语顺序 0-后置, 1-前置")
    private Integer nameSupplementSeq;

    public static NameSupplementBizDTO convertToBizDTO(NameSupplementInfoVO nameSupplementInfo) {
        if (Objects.isNull(nameSupplementInfo)) {
            return null;
        }

        NameSupplementBizDTO nameSupplementBizDTO = new NameSupplementBizDTO();
        nameSupplementBizDTO.setNameSupplement(nameSupplementInfo.getNameSupplement());
        nameSupplementBizDTO.setNameSupplementSeq(nameSupplementInfo.getNameSupplementSeq());
        return nameSupplementBizDTO;
    }

    public static NameSupplementInfoVO fromDTO(NameSupplementDTO nameSupplementDTO) {
        if (Objects.isNull(nameSupplementDTO)) {
            return null;
        }

        NameSupplementInfoVO nameSupplementInfoVO = new NameSupplementInfoVO();
        nameSupplementInfoVO.setNameSupplement(nameSupplementDTO.getNameSupplement());
        nameSupplementInfoVO.setNameSupplementSeq(nameSupplementDTO.getNameSupplementSeq());
        return nameSupplementInfoVO;
    }

    public static NameSupplementInfoVO fromBizDTO(NameSupplementBizDTO nameSupplementBizDTO) {
        if (Objects.isNull(nameSupplementBizDTO)) {
            return null;
        }

        NameSupplementInfoVO nameSupplementInfoVO = new NameSupplementInfoVO();
        nameSupplementInfoVO.setNameSupplement(nameSupplementBizDTO.getNameSupplement());
        nameSupplementInfoVO.setNameSupplementSeq(nameSupplementBizDTO.getNameSupplementSeq());
        return nameSupplementInfoVO;
    }

    public static NameSupplementDTO convertToDTO(NameSupplementInfoVO nameSupplementInfo) {
        if (Objects.isNull(nameSupplementInfo)) {
            return null;
        }

        NameSupplementDTO nameSupplementDTO = new NameSupplementDTO();
        nameSupplementDTO.setNameSupplement(nameSupplementInfo.getNameSupplement());
        nameSupplementDTO.setNameSupplementSeq(nameSupplementInfo.getNameSupplementSeq());
        return nameSupplementDTO;
    }
}
