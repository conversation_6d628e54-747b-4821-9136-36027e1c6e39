package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order;

import java.util.ArrayList;
import java.util.List;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/7/11
 * desc: 查询虚拟电话响应
 */
@TypeDoc(
        description = "查询虚拟号响应"
)
@ApiModel("查询虚拟号响应")
@Data
public class QueryVirtualPhoneResponse {

    @FieldDoc(
            description = "收货号码-隐私号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "收货号码-隐私号", required = true)
    private String phoneNo;

    @FieldDoc(
            description = "收货号码-备用隐私号码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "收货号码-备用隐私号码", required = true)
    private List<String> backUpPhoneNo = new ArrayList<>();


    @FieldDoc(
            description = "微信绑定手机号-隐私号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "微信绑定手机号-隐私号", required = true)
    private String bindPhoneNo;

    @FieldDoc(
            description = "微信绑定手机号-备用隐私号码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "微信绑定手机号-备用隐私号码", required = true)
    private List<String> bindBackupPhoneNo = new ArrayList<>();
}
