package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.impl;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.AbstractSinglePendingTaskService;
import com.sankuai.meituan.reco.store.wms.enums.ResultCodeEnum;
import com.sankuai.meituan.reco.store.wms.thrift.common.UserParam;
import com.sankuai.meituan.reco.store.wms.thrift.health.HealthForecastThriftService;
import com.sankuai.meituan.reco.store.wms.thrift.health.model.HealthForecastMsgCntRequest;
import com.sankuai.meituan.reco.store.wms.thrift.health.model.HealthForecastMsgCntResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class HealthForecastTaskService extends AbstractSinglePendingTaskService {

    @Resource
    private HealthForecastThriftService healthForecastThriftService;

    @Override
    protected PendingTaskResult getPendingTaskCount(PendingTaskParam param) throws Exception {
        HealthForecastMsgCntRequest request = new HealthForecastMsgCntRequest();
        request.setUserParam(new UserParam(String.valueOf(param.getUser().getAccountId()),
                param.getUser().getAccountName(), param.getUser().getAccountId()));
        request.setTenantId(param.getTenantId());
        request.setMarked(false);
        if (param.getStoreIds().size() == 0) {
            return PendingTaskResult.createNumberMarker(0);
        }
        request.setRepositoryId(param.getEntityId());

        HealthForecastMsgCntResult result = healthForecastThriftService.countForecastMsg(request);
        if (result.getCode() == ResultCodeEnum.SUCCESS.getCode()) {
            return PendingTaskResult.createNumberMarker(result.getCnt().intValue());
        }

        return PendingTaskResult.createNumberMarker(0);
    }

    @Override
    protected AuthCodeEnum module() {
        return AuthCodeEnum.DH_INVENTORY_WARNING;
    }
}
