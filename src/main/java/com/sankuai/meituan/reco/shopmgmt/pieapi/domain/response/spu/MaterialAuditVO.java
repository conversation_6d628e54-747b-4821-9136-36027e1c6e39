package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.MaterialAuditDTO;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/1/24
 */

@Data
public class MaterialAuditVO {
    @FieldDoc(
            description = "素材url"
    )
    private String url;

    @FieldDoc(
            description = "审核状态 1 待审核 2 审核中 3 审核通过 4 审核拒绝"
    )
    private Integer auditStatus;

    public static MaterialAuditVO of(MaterialAuditDTO dto) {
        MaterialAuditVO vo = new MaterialAuditVO();
        vo.setUrl(dto.getUrl());
        vo.setAuditStatus(dto.getAuditStatus());
        return vo;
    }

    public static MaterialAuditVO ofBiz(com.sankuai.meituan.shangou.empower.productbiz.client.dto.MaterialAuditDTO dto) {
        MaterialAuditVO vo = new MaterialAuditVO();
        vo.setUrl(dto.getUrl());
        vo.setAuditStatus(dto.getAuditStatus());
        return vo;
    }
}
