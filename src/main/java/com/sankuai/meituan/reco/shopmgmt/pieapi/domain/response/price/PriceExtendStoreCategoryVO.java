package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * @Author: wangyihao04
 * @Date: 2020-06-11 17:32
 * @Mail: <EMAIL>
 */
@TypeDoc(
        description = "门店品类价格扩展对象结果"
)
@ApiModel("门店品类价格扩展对象结果")
@Data
@AllArgsConstructor
public class PriceExtendStoreCategoryVO {

    @FieldDoc(
            description = "门店价格扩展值信息"
    )
    @ApiModelProperty("门店价格扩展值信息")
    private PriceExtendVO storePriceExtend;



    @FieldDoc(
            description = "品类价格扩展值信息"
    )
    @ApiModelProperty("品类价格扩展值信息")
    private List<PriceExtendVO> categoryPriceExtendList;
}
