package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.wavepick.converter;

import com.sankuai.meituan.reco.pickselect.thrift.BatchInfo;
import com.sankuai.meituan.reco.pickselect.thrift.SkuPackingSpecDTO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.BatchInfoVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.SkuPackingSpecVO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public abstract class WarehousePickItemSkuSpecConverter {
    public abstract SkuPackingSpecVO convert2Vo(SkuPackingSpecDTO skuPackingSpec);
}
