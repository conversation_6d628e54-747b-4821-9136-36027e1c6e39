package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.meituan.shangou.saas.dto.request.AxBPrivacyPhoneRequest;
import com.meituan.shangou.saas.dto.response.PrivacyPhoneResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OrderBaseVo;
import com.meituan.shangou.saas.service.PrivacyPhoneThriftService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.PrivateNumberBizScenarioType;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.tsp.privacy.thrift.iface.privacyphone.constant.PrivateNumberQuerySource;
import com.sankuai.meituan.tsp.privacy.thrift.iface.privacyphone.model.request.PrivateNumberCallBillAndRecordingQueryRequest;
import com.sankuai.meituan.tsp.privacy.thrift.iface.privacyphone.model.request.SecurityAuditRequest;
import com.sankuai.meituan.tsp.privacy.thrift.iface.privacyphone.model.response.PrivateNumberCallBill;
import com.sankuai.meituan.tsp.privacy.thrift.iface.privacyphone.model.response.PrivateNumberCallBillQueryResponse;
import com.sankuai.meituan.tsp.privacy.thrift.iface.privacyphone.service.PrivateNumberBillQueryThriftService;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.enums.recording.BillQueryResultEnum;
import com.sankuai.shangou.common.ResultCodeEnum;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/7/18 11:14 AM
 */
@Slf4j
@Component
@Rhino
public class PrivacyNumberWrapper {

    @Autowired
    private PrivacyPhoneThriftService privacyPhoneThriftService;

    @Resource
    private PrivateNumberBillQueryThriftService.Iface privateNumberBillQueryThriftService;

    @Value("${app.name}")
    private String APP_NAME;

    /**
     * 获取订单对应的axb隐私号
     *
     * @param orderBaseVo 订单基本信息
     * @param callPhone   拨打手机号
     * @param calledPhone 被拨打手机号
     * @return
     */
    public PrivacyPhoneResponse applyAxB(OrderBaseVo orderBaseVo, String callPhone, String calledPhone) {
        AxBPrivacyPhoneRequest request = new AxBPrivacyPhoneRequest();
        request.setUserId(orderBaseVo.getUserId());
        request.setUuid(orderBaseVo.getChannelOrderId());
        request.setChannelId(orderBaseVo.getChannelId());
        request.setUserPhone(callPhone);
        request.setCalledPhone(calledPhone);
        request.setCityId(orderBaseVo.getCityId());
        request.setDuration(MccConfigUtil.axbPrivacyPhoneValidTime());
        // 骑手呼叫用户
        request.setScenarioType(PrivateNumberBizScenarioType.WAIMAI_RIDER_CALL_CUSTOMER.code);
        return privacyPhoneThriftService.applyAxbPhone(request);
    }

    @Degrade(rhinoKey = "PrivacyNumberWrapper.queryCallBillAndRecording", fallBackMethod = "queryCallBillAndRecordingFallBack", timeoutInMilliseconds = 2000)
    public List<PrivateNumberCallBill> queryCallBillAndRecording(String viewOrderId, Integer orderTime) throws TException {
        PrivateNumberCallBillAndRecordingQueryRequest request = new PrivateNumberCallBillAndRecordingQueryRequest();
        request.setOrderTime(orderTime);
        request.setOuterUniqueId(viewOrderId);
        request.setQuerySource(PrivateNumberQuerySource.DRUNK_HORSE_PIE_API);

        SecurityAuditRequest securityAuditRequest = new SecurityAuditRequest();
        securityAuditRequest.setPId(APP_NAME);
        securityAuditRequest.setPName(APP_NAME);
        request.setSecurityAudit(securityAuditRequest);
        log.info("start invoke privateNumberBillQueryThriftService.queryCallBillAndRecording, request: {}", request);
        PrivateNumberCallBillQueryResponse response = privateNumberBillQueryThriftService.queryCallBillAndRecording(request);
        log.info("end invoke privateNumberBillQueryThriftService.queryCallBillAndRecording, response: {}", response);

        if (Objects.equals(response.getResultCode(), BillQueryResultEnum.EMPTY_CALL_BILL_RESULT.getCode())) {
            return Collections.emptyList();
        }

        if (!Objects.equals(response.getResultCode(), ResultCodeEnum.SUCCESS.getCode())) {
            throw new ThirdPartyException("查询隐私号通话记录失败");
        }

        return response.getCallBillList();
    }

    public List<PrivateNumberCallBill> queryCallBillAndRecordingFallBack(String viewOrderId, Integer orderTime) {
        log.error("PrivacyNumberWrapper.queryCallBillAndRecording 发生降级");
        return Collections.emptyList();
    }


}
