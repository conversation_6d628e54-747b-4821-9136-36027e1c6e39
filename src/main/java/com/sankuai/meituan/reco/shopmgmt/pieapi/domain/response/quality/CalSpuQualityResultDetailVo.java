package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.quality;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.product.dto.CalSpuQualityResultDetailDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/9/20
 */
@TypeDoc(
        name = "商品质量分详情",
        description = "商品质量分详情"
)
@Data
public class CalSpuQualityResultDetailVo {

    @FieldDoc(
            description = "商品质量总分"
    )
    @ApiModelProperty(name = "商品质量总分")
    private Integer totalScore;

    @FieldDoc(
            description = "商品权益问题数"
    )
    @ApiModelProperty(name = "商品权益问题数")
    private List<SpuQualityProblemCountVo> problemCountList;

    @FieldDoc(
            description = "商品各字段质量问题列表"
    )
    @ApiModelProperty(name = "商品各字段质量问题列表")
    private List<FieldsQualityDetailVo> qualityList;

    public static CalSpuQualityResultDetailVo of(CalSpuQualityResultDetailDto dto) {
        if (Objects.isNull(dto)) {
            return null;
        }

        CalSpuQualityResultDetailVo vo = new CalSpuQualityResultDetailVo();
        vo.setTotalScore(dto.getTotalScore());
        vo.setProblemCountList(Fun.map(dto.getProblemCountDtoList(), SpuQualityProblemCountVo::of));
        vo.setQualityList(Fun.map(dto.getFieldsQualityDetailDtoList(), FieldsQualityDetailVo::of));

        return vo;
    }
}
