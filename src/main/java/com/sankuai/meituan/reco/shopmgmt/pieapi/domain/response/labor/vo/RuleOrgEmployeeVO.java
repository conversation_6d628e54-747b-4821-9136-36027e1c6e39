package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022-10-27
 * @email <EMAIL>
 */
@TypeDoc(
        description = "规则组织架构员工vo"
)
@ApiModel("规则组织架构员工vo")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RuleOrgEmployeeVO {

    private Long employeeId;

    private String employeeName;

    private Long accountId;

    private String accountName;

}
