package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.securitydeposit;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: <EMAIL>
 * @Date: 2020/3/2 21:33
 * @Description:
 */
@TypeDoc(description = "保证金变更记录对象")
@Data
public class SecurityDepositChangeRecordVO {

    @FieldDoc(
            description = "变更时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "变更时间", required = true)
    @NotNull
    private Long opTime;


    @FieldDoc(
            description = "变更金额", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "变更金额", required = true)
    @NotNull
    private String amount;


    @FieldDoc(
            description = "变更原因", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "变更原因", required = true)
    @NotNull
    private String reason;


}
