package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price;
// Copyright (C) 2020 Meituan
// All rights reserved

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "品类商品价格指数对象结果"
)
@ApiModel("品类商品价格指数对象结果")
@Data
@AllArgsConstructor
public class PriceIndexCategorySkuVO {


    /**
     * 门店价格指数信息
     */
    @FieldDoc(
            description = "品类价格指数信息"
    )
    @ApiModelProperty("品类价格指数信息")
    private PriceIndexVO categoryPriceIndexInfo;


    /**
     * 品类价格指数列表
     */
    @FieldDoc(
            description = "商品价格指数信息"
    )
    @ApiModelProperty("商品价格指数信息")
    private List<SkuPriceIndexVO> categoryPriceIndexList;


    @FieldDoc(
            description = "分页信息"
    )
    @ApiModelProperty("分页信息")
    private PageInfoVO pageInfo;

}
