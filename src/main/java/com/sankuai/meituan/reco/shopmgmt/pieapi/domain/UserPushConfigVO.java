package com.sankuai.meituan.reco.shopmgmt.pieapi.domain;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TypeDoc(
        description = "用户推送配置"
)
@Data
@ApiModel("用户推送配置")
public class UserPushConfigVO {

    @FieldDoc(
            description = "设备uuid"
    )
    @ApiModelProperty(value = "设备uuid")
    private String uuid;
    @FieldDoc(
            description = "pushToken"
    )
    @ApiModelProperty(value = "pushToken")
    private String pushToken;
    @FieldDoc(
            description = "unionId"
    )
    @ApiModelProperty(value = "unionId")
    private String unionId;
    @FieldDoc(
            description = "platformType:0-ios;1-android"
    )
    @ApiModelProperty(value = "platformType:0-ios;1-android")
    private Integer platformType;
    @FieldDoc(
            description = "app版本号"
    )
    @ApiModelProperty(value = "app版本号")
    private String appVersion;
    @FieldDoc(
            description = "渠道标识, APPSTORE,或者 ADHOC"
    )
    @ApiModelProperty(value = "app渠道标识, APPSTORE与ADHOC")
    private String appChannel;


}
