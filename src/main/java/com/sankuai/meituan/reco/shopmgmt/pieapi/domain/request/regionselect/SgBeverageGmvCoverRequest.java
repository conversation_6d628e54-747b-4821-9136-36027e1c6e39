package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.regionselect;

import com.meituan.linz.boot.util.Assert;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sgshopmgmt.shangou.empower.regionselection.annotation.thrift.enums.DateTypeEnum;
import lombok.Data;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @since 2023/2/27 14:35
 **/
@TypeDoc(
        description = "闪购酒水覆盖率热力图查询请求"
)
@Data
public class SgBeverageGmvCoverRequest {
    @FieldDoc(
            description = "城市id"
    )
    private Integer cityId;

    @FieldDoc(
            description = "西南角点位坐标"
    )
    private String southWestPoint;

    @FieldDoc(
            description = "东北角点位坐标"
    )
    private String northEastPoint;

    @FieldDoc(
            description = "查询日期, 格式 yyyy-MM-dd"
    )
    private String dt = LocalDate.now().minusMonths(1).withDayOfMonth(1).format(DateTimeFormatter.ISO_LOCAL_DATE);

    @FieldDoc(
            description = "日期类型(WEEK/MONTH/QUARTER)"
    )
    private String dateType = DateTypeEnum.MONTH.name();

    public void validate() {
        Assert.throwIfTrue(this.cityId == null || this.cityId <= 0L, "城市id不正确");
        Assert.throwIfBlank(this.southWestPoint, "西南角点位坐标必传");
        Assert.throwIfBlank(this.northEastPoint, "东北角点位坐标必传");
        Assert.throwIfBlank(this.dt, "查询日期必传");
        Assert.throwIfBlank(this.dateType, "日期类型必传");
    }

}
