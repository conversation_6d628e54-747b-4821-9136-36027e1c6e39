package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Maps;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.QueryOrderDeliveryInfoKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryDetail;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.warehouse.AbnOrderService;
import com.sankuai.shangou.logistics.warehouse.dto.AbnOrderDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-12-19
 * @email <EMAIL>
 */
@Slf4j
@Component
@Rhino
public class AbnOrderServiceWrapper {

    @Resource
    private AbnOrderService abnOrderService;

    @Degrade(rhinoKey = "AbnOrderServiceWrapper-getUnprocessedAbnOrderCount",
            fallBackMethod = "getUnprocessedAbnOrderCountFallback",
            timeoutInMilliseconds = 1000)
    @MethodLog(logRequest = true, logResponse = true)
    public int getUnprocessedAbnOrderCount(long warehouseId) {
        try {
            TResult<List<AbnOrderDTO>> unprocessedOrderListResult = abnOrderService.getUnprocessed(warehouseId);
            if (!unprocessedOrderListResult.isSuccess() || CollectionUtils.isEmpty(unprocessedOrderListResult.getData())) {
                return 0;
            }
            return unprocessedOrderListResult.getData().size();
        } catch (Exception e) {
            log.error("invoke abnOrderService.getUnprocessed error");
            return 0;
        }
    }

    @Degrade(rhinoKey = "AbnOrderServiceWrapper-getUnprocessedAbnOrder",
            fallBackMethod = "getUnprocessedAbnOrderFallback",
            timeoutInMilliseconds = 1000)
    public List<AbnOrderDTO> getUnprocessedAbnOrder(long warehouseId) {
        try {
            TResult<List<AbnOrderDTO>> unprocessedOrderListResult = abnOrderService.getUnprocessed(warehouseId);
            log.info("invoke abnOrderService.getUnprocessed, warehouseId = {}, response = {}", warehouseId, unprocessedOrderListResult);
            if (!unprocessedOrderListResult.isSuccess() || CollectionUtils.isEmpty(unprocessedOrderListResult.getData())) {
                return Lists.newArrayList();
            }

            return unprocessedOrderListResult.getData().stream().filter(abnOrderDTO -> CollectionUtils.isNotEmpty(abnOrderDTO.getItems())).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("invoke abnOrderService.getUnprocessed error");
            return Lists.newArrayList();
        }
    }

    @Degrade(rhinoKey = "AbnOrderServiceWrapper-getOrderAbnMap",
            fallBackMethod = "getOrderAbnMapFallback",
            timeoutInMilliseconds = 1000)
    public Map<String, AbnOrderDTO> getOrderAbnMap(long warehouseId) {
        try {
            if (!MccConfigUtil.isNewPickGrayStore(warehouseId)) {
                return Maps.newHashMap();
            }

            List<AbnOrderDTO> unprocessedAbnOrder = getUnprocessedAbnOrder(warehouseId);
            if (CollectionUtils.isEmpty(unprocessedAbnOrder)) {
                return Maps.newHashMap();
            }

            return unprocessedAbnOrder.stream().collect(Collectors.toMap(
                    AbnOrderDTO::getSourceOrderNo,
                    Function.identity(),
                    (older, newer) -> newer
            ));
        } catch (Exception e) {
            log.error("getOrderAbnMap error", e);
            return Maps.newHashMap();
        }
    }


    public List<AbnOrderDTO> getUnprocessedAbnOrderFallback(long warehouseId) {
        log.error("getUnprocessedAbnOrderFallback {}", warehouseId);
        return Lists.newArrayList();
    }

    public int getUnprocessedAbnOrderCountFallback(long warehouseId){
        log.error("getUnprocessedAbnOrderCountFallback {}", warehouseId);
        return 0;
    }

    public Map<String, AbnOrderDTO> getOrderAbnMapFallback(long warehouseId) {
        log.error("getOrderAbnMapFallback {}", warehouseId);
        return Maps.newHashMap();
    }
}
