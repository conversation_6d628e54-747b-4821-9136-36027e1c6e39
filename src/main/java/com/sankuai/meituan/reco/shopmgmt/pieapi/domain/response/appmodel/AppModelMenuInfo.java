package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.appmodel;

import java.util.List;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * app模块菜单信息
 *
 * <AUTHOR>
 * @since 2021/7/2
 */
@TypeDoc(
        description = "app模块菜单信息"
)
@Data
@ApiModel("app模块菜单信息")
public class AppModelMenuInfo {

    @FieldDoc(
            description = "当前菜单信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "当前菜单信息")
    private MenuInfo parentMenu;

    @FieldDoc(
            description = "是否隐藏当前菜单，若不由后端控制则不返回", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否隐藏当前菜单，若不由后端控制则不返回")
    private Boolean hideParentMenu;

    @FieldDoc(
            description = "子菜单项信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "子菜单项信息")
    private List<MenuInfo> subMenus;
}