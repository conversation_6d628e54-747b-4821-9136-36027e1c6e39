package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/7/16
 * desc: 售后申请记录详情
 */
@TypeDoc(
        description = "售后商品信息"
)
@ApiModel("售后商品信息")
@Data
public class AfterSaleRecordDetailVO {

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID", required = true)
    private Integer channelId;

    @FieldDoc(
            description = "售后服务唯一ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "售后服务唯一ID", required = true)
    private Long serviceId;

    @FieldDoc(
            description = "skuId", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "skuId", required = true)
    private String skuId;

    @FieldDoc(
            description = "upc码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "upc码", required = true)
    private String upcCode;

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称", required = true)
    private String skuName;

    @FieldDoc(
            description = "规格", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "规格", required = true)
    private String specification;

    @FieldDoc(
            description = "售卖单位", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "售卖单位", required = true)
    private String sellUnit;

    @FieldDoc(
            description = "单价  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "单价  单位:分", required = true)
    private Integer unitPrice;

    @FieldDoc(
            description = "退货数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "退货数量", required = true)
    private Integer count;

    @FieldDoc(
            description = "退款金额  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "退款金额  单位:分", required = true)
    private Integer refundAmt;


}
