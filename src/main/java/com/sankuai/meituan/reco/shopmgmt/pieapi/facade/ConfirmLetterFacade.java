package com.sankuai.meituan.reco.shopmgmt.pieapi.facade;

import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.meituan.linz.boot.util.Assert;
import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.shangou.saas.tenant.thrift.ConfirmLetterThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.Status;
import com.meituan.shangou.saas.tenant.thrift.common.StatusCodeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.config.response.CommonResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.contract.GenerateConfirmLetterDto;
import com.meituan.shangou.saas.tenant.thrift.dto.contract.request.GenerateConfirmLetterRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.contract.request.SignConfirmLetterRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.contract.response.GenerateConfirmLetterResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;

import lombok.extern.slf4j.Slf4j;

/**
 * 确认函facade
 *
 * <AUTHOR>
 * @since 2021/12/4
 */
@Service
@Slf4j
public class ConfirmLetterFacade {

    @Autowired
    private ConfirmLetterThriftService confirmLetterThriftService;

    /**
     * 生成电子协议确认函
     *
     * @param request
     * @return
     */
    public GenerateConfirmLetterDto generateConfirmLetter(GenerateConfirmLetterRequest request) {
        try {
            GenerateConfirmLetterResponse response = confirmLetterThriftService.generateConfirmLetter(request);
            Assert.throwIfNull(response, "生成电子协议确认函服务响应为空");
            checkResultStatus(response.getStatus());
            return response.getGenerateConfirmLetterDto();
        }
        catch (CommonRuntimeException e) {
            throw e;
        }
        catch (Exception e) {
            log.error("生成电子协议确认函异常, req:{}", JacksonUtils.toJson(request), e);
            throw new CommonRuntimeException("生成电子协议确认函异常", ResultCode.CONFIRM_LETTER_ERROR);
        }
    }

    /**
     * 签署电子协议确认函
     *
     * @param request
     */
    public void sign(SignConfirmLetterRequest request) {
        try {
            CommonResponse response = confirmLetterThriftService.sign(request);
            Assert.throwIfNull(response, "签署确认函接口异常");
            checkResultStatus(response.getStatus());
        }
        catch (CommonRuntimeException e) {
            throw e;
        }
        catch (Exception e) {
            log.error("签署确认函接口异常, req:{}", JacksonUtils.toJson(request), e);
            throw new CommonRuntimeException("签署确认函接口异常，请稍后重试.", ResultCode.CONFIRM_LETTER_ERROR);
        }

    }

    private void checkResultStatus(Status status) {
        Assert.throwIfNull(status, "调用ConfirmLetterThriftService服务响应状态为空");
        if (!Objects.equals(status.getCode(), StatusCodeEnum.SUCCESS.getCode())) {
            log.error("调用ConfirmLetterThriftService业务异常, code: {}, message: {}", status.getCode(), status.getMessage());
            throw new CommonRuntimeException(status.getMessage(), ResultCode.CONFIRM_LETTER_BUSINESS_EXCEPTION);
        }
    }

}
