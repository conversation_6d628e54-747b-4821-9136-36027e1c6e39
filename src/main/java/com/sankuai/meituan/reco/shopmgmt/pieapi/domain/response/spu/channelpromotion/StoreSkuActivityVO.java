package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.channelpromotion;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.channelpromotion.dto.StoreSkuActivityDTO;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/9/12
 */
@TypeDoc(
        description = "门店sku活动信息"
)
@Data
public class StoreSkuActivityVO {
    @FieldDoc(description = "spuId")
    private String spuId;
    @FieldDoc(description = "spuName")
    private String spuName;
    @FieldDoc(description = "美团渠道店内码/货号")
    private String customSkuId;
    @FieldDoc(description = "skuId")
    private String skuId;
    @FieldDoc(description = "规格名称")
    private String specName;
    @FieldDoc(description = "upc")
    private String upc;
    @FieldDoc(description = "牵牛花门店名称")
    private String qnhStoreName;
    @FieldDoc(description = "牵牛花门店Id")
    private Long qnhStoreId;
    @FieldDoc(description = "美团渠道门店名称")
    private String mtStoreName;
    @FieldDoc(description = "美团渠道门店id")
    private String mtStoreId;
    @FieldDoc(description = "活动类型")
    private Integer activityType;
    @FieldDoc(description = "活动类型名称")
    private String activityTypeName;
    @FieldDoc(description = "活动Id")
    private Long activityId;
    @FieldDoc(description = "活动名称")
    private String activityName;
    @FieldDoc(description = "活动状态：0-活动未开始；1-活动已生效")
    private Integer activityStatus;
    private Long startTimeStamp;
    @FieldDoc(description = "活动结束时间，秒级时间戳")
    private Long endTimeStamp;
    @FieldDoc(description = "活动开始时间，秒级时间")
    private String startTimeDesc;
    @FieldDoc(description = "活动结束时间，秒级时间")
    private String endTimeDesc;
    @FieldDoc(description = "是否为报名活动（提报活动） 0-自营销活动 1-报名活动")
    private Integer isRegister;
    @FieldDoc(description = "活动内容：折扣（系数） 1表示1折；  (活动类型17 56 75 40内才有意义)")
    private String discount;
    @FieldDoc(description = "活动价")
    private String activityPrice;
    @FieldDoc(description = "补贴信息")
    private List<ChargeDetailVO> chargeDetailVOList;
    @FieldDoc(description = "补贴信息展示")
    private String chargeDetailDisplayVO;
    @FieldDoc(description = "重量单位")
    private String weightUnit;
    @FieldDoc(description = "重量")
    private String weightForUnit;


    public static StoreSkuActivityVO fromDto(StoreSkuActivityDTO dto) {
        if(dto==null){
            return null;
        }
        StoreSkuActivityVO vo = new StoreSkuActivityVO();
        vo.setSpuId(dto.getSpuId());
        vo.setSpuName(dto.getSpuName());
        vo.setCustomSkuId(dto.getCustomSkuId());
        vo.setSkuId(dto.getSkuId());
        vo.setSpecName(dto.getSpecName());
        vo.setUpc(dto.getUpc());
        vo.setWeightForUnit(dto.getWeightForUnit());
        vo.setWeightUnit(dto.getWeightUnit());
        vo.setQnhStoreName(dto.getQnhStoreName());
        vo.setQnhStoreId(dto.getQnhStoreId());
        vo.setMtStoreName(dto.getMtStoreName());
        vo.setMtStoreId(dto.getMtStoreId());
        vo.setActivityType(dto.getActivityType());
        vo.setActivityTypeName(dto.getActivityTypeName());
        vo.setActivityId(dto.getActivityId());
        vo.setActivityName(dto.getActivityName());
        vo.setActivityStatus(dto.getActivityStatus());
        vo.setStartTimeStamp(dto.getStartTime());
        vo.setEndTimeStamp(dto.getEndTime());
        vo.setStartTimeDesc(formatDate(dto.getStartTime()));
        vo.setEndTimeDesc(formatDate(dto.getEndTime()));
        vo.setIsRegister(dto.getRegister());
        vo.setDiscount(dto.getDiscount());
        vo.setActivityPrice(dto.getActivityPrice());
        List<ChargeDetailVO> chargeDetailVOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dto.getChargeDetailDTOList())) {
            dto.getChargeDetailDTOList().forEach(e -> chargeDetailVOList.add(ChargeDetailVO.fromDTO(e)));
        }
        vo.setChargeDetailVOList(chargeDetailVOList);
        vo.setChargeDetailDisplayVO(vo.buildChargeDetailDisplayVO());
        return vo;
    }

    private static String formatDate(Long timestamp) {
        if (timestamp == null) {
            return StringUtils.EMPTY;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(timestamp * 1000);
    }

    /**
     * 构建补贴信息的展示
     * @return
     */
    private String buildChargeDetailDisplayVO() {
        // 构建补贴信息
        StringBuilder chargeDetailBuilder = new StringBuilder();
        if (CollectionUtils.isNotEmpty(chargeDetailVOList)) {
            int size = chargeDetailVOList.size();
            chargeDetailVOList.forEach(chargeDetail -> {
                if (StringUtils.isNotBlank(chargeDetail.getMtChargeAmount()) && new BigDecimal(chargeDetail.getMtChargeAmount()).compareTo(BigDecimal.ZERO) > 0) {
                    chargeDetailBuilder.append(String.format("美团平台补贴%s元，", chargeDetail.getMtChargeAmount()));
                }
                if (StringUtils.isNotBlank(chargeDetail.getPartnerChargeAmount()) && new BigDecimal(chargeDetail.getPartnerChargeAmount()).compareTo(BigDecimal.ZERO) > 0) {
                    chargeDetailBuilder.append(String.format("合作商补贴%s元，", chargeDetail.getPartnerChargeAmount()));
                }
                if (StringUtils.isNotBlank(chargeDetail.getPoiChargeAmount()) && new BigDecimal(chargeDetail.getPoiChargeAmount()).compareTo(BigDecimal.ZERO) > 0) {
                    chargeDetailBuilder.append(String.format("商家补贴%s元，", chargeDetail.getPoiChargeAmount()));
                }
                if (size > 1 && chargeDetailBuilder.length() > 0) {
                    chargeDetailBuilder.deleteCharAt(chargeDetailBuilder.length() - 1);
                    chargeDetailBuilder.append("；");
                }
            });
            if (chargeDetailBuilder.length() > 0) {
                chargeDetailBuilder.deleteCharAt(chargeDetailBuilder.length() - 1);
            }
        }
        return chargeDetailBuilder.toString();
    }
}
