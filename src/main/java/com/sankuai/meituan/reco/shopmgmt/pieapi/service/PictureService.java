package com.sankuai.meituan.reco.shopmgmt.pieapi.service;

import com.dianping.lion.client.Lion;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.SsrfCheckUtils;
import com.sankuai.meituan.shangou.saas.common.hash.MD5Util;
import com.sankuai.meituan.shangou.saas.common.storage.StorageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.curator.utils.CloseableUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URL;

/**
 * 图片处理服务
 *
 * <AUTHOR>
 * @since 2022/6/9
 */
@Service
@Slf4j
public class PictureService {
    private static final int DEFAULT_SIZE_PROMOTION_PICTURE = 233;
    private static final String DIRECTORY = "picture/";
    private static final String DEFAULT_PIC = "jpg";
    private static final String SEPARATOR_CHAR = "/";
    public static final String FILE_NAME_SUFFIX_CHAR = ".";
    public static final String PROMOTION_PRODUCT_PICTURE = "promotion_product_picture";
    public static final String PROMOTION_PRODUCT_DEFAULT_PICTURE = "https://p0.meituan.net/travelcube/e0ed042d248a7ea9dd4cc77b44797949786011.jpg";



    @Value("${mts3.bucketName}")
    private String bucketName;

    @Value("${mts3.onlineUrl}")
    private String cdnOnlineUrl;

    @Autowired
    private StorageService s3StorageService;


    public  String combinePicture(Integer x, Integer y, String qrCodeUrl) {
        try {
            SsrfCheckUtils.checkInnerUrl(qrCodeUrl);
            String url = Lion.getConfigRepository().get(PROMOTION_PRODUCT_PICTURE, PROMOTION_PRODUCT_DEFAULT_PICTURE);
            File templateFile = getRemoteFile(url);
            File miniQrcode = getRemoteFile(qrCodeUrl);
            BufferedImage template = ImageIO.read(templateFile);
            BufferedImage qrCode = ImageIO.read(miniQrcode);
            BufferedImage resizeQrCode = resizeImage(qrCode, DEFAULT_SIZE_PROMOTION_PICTURE, DEFAULT_SIZE_PROMOTION_PICTURE);
            Graphics2D g = template.createGraphics();
            g.drawImage(resizeQrCode, x, y, resizeQrCode.getWidth(), resizeQrCode.getHeight(), null);
            g.dispose();
            ImageIO.write(template, DEFAULT_PIC, templateFile);
            return imgUpload(templateFile);
        } catch (Exception e) {
            log.error("生成模板加二维码图失败", e);
            throw new RuntimeException(e);
        }
    }


    /**
     * 远程图片获取，暂时不用了，用本地图片
     * @param url
     * @return
     */
    public File getRemoteFile(String url) {
        // 这对
        //对本地文件命名
        String fileName = url.substring(url.lastIndexOf("."));

        File file = null;
        try {
            file = File.createTempFile("net_url", fileName);
        } catch (Exception e) {
            log.error("createTempFile error", e);
            return file;
        }
        try (InputStream inStream = new URL(url).openStream();
             OutputStream os = new FileOutputStream(file)){

            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = inStream.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
        } catch (Exception e) {
            log.error("读取图片失败", e);
        }
        return file;
    }

    public String imgUpload(File file) {
        ByteArrayInputStream byteArrayInputStream = null;
        try {
            if (file == null) {
                return null;
            }
//            byteArrayIImageUtilsnputStream = new ByteArrayInputStream(file);
            String fileName = s3StorageService.upload(rename(DEFAULT_PIC), file);
            return String.join(StringUtils.EMPTY, cdnOnlineUrl, SEPARATOR_CHAR,
                    bucketName, SEPARATOR_CHAR, fileName);

        } catch (Exception e) {
            log.error("s3 upload error, exception:", e);
            throw new RuntimeException(e);
        } finally {
            CloseableUtils.closeQuietly(byteArrayInputStream);
        }
    }

    private String rename(String fileName) {
        int radix = fileName.lastIndexOf(FILE_NAME_SUFFIX_CHAR);
        String suffix = fileName.substring(radix + NumberUtils.INTEGER_ONE);
        return String.join(StringUtils.EMPTY, DIRECTORY, MD5Util.toMD5String(fileName), FILE_NAME_SUFFIX_CHAR, suffix);
    }

    private BufferedImage resizeImage(BufferedImage originalImage, int targetWidth, int targetHeight) {
        Image resultingImage = originalImage.getScaledInstance(targetWidth, targetHeight, Image.SCALE_AREA_AVERAGING);
        BufferedImage outputImage = new BufferedImage(targetWidth, targetHeight, BufferedImage.TYPE_INT_RGB);
        outputImage.getGraphics().drawImage(resultingImage, 0, 0, null);
        return outputImage;
    }
}
