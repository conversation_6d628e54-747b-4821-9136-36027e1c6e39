package com.sankuai.meituan.reco.shopmgmt.pieapi.service.appmodel;

import java.util.HashMap;
import java.util.Map;

import com.sankuai.meituan.reco.shopmgmt.pieapi.converters.assistant.AssistantTaskConverter;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.appmodel.QueryMenuInfoRequest;

import com.meituan.linz.boot.util.Bssert;
import com.meituan.shangou.sac.dto.model.SacMenuNodeDto;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.appmodel.AppModelMenuInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.appmodel.MenuInfo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.MenuCodeEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * 抽象app模块菜单service
 *
 * <AUTHOR>
 * @since 2021/7/5
 */
@Slf4j
public abstract class AbstractAppModelMenuService {

    protected static Map<MenuCodeEnum, AbstractAppModelMenuService> menuCodeServiceMap = new HashMap<>();


    protected AbstractAppModelMenuService() {
        menuCodeServiceMap.putIfAbsent(getMenuCode(), this);
    }

    /**
     * 返回支持的菜单code.
     *
     * @return 菜单code
     */
    protected abstract MenuCodeEnum getMenuCode();

    /**
     * 根据菜单code查询模块信息.
     *
     * @param menuCode 菜单code
     * @return 任务汇总信息
     */
    public static AppModelMenuInfo queryMenuInfoByCode(MenuCodeEnum menuCode, QueryMenuInfoRequest request, Boolean possibleNewQueryGray) {
        AbstractAppModelMenuService menuService = menuCodeServiceMap.get(menuCode);
        Bssert.throwIfNull(menuService, "菜单编码无对应服务");
        return menuService.queryMenuInfo(request, possibleNewQueryGray);
    }

    /**
     * 查询模块信息.
     *
     * @return 任务汇总列表
     */
    protected abstract AppModelMenuInfo queryMenuInfo(QueryMenuInfoRequest request, Boolean possibleNewQueryGray);


    protected MenuInfo buildMenuInfoBySacMenuNodeDto(SacMenuNodeDto sacMenuNodeDto) {
        return AssistantTaskConverter.convertSacMenu2AssistantMenu(sacMenuNodeDto);
    }

}