package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TypeDoc(
        description = "拣货完成请求"
)
@ApiModel("拣货完成请求")
@Data
public class WarehousePickCompleteRequest {

    @FieldDoc(
            description = "波次任务号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "波次任务号")
    private String taskOrderId;

    @FieldDoc(
            description = "skuId", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "skuId")
    private String skuId;

    @FieldDoc(
            description = "实拣数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "实拣数量")
    private Integer pickNum;

    @FieldDoc(
            description = "拣货任务id", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "拣货任务id")
    private Long pickTaskId;

    @FieldDoc(
            description = "拣货箱规单位", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "拣货箱规单位")
    private SkuPackingSpecRequest packingSpec;

}
