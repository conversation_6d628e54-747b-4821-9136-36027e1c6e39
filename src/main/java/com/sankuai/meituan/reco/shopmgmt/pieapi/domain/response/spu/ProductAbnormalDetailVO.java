package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.AbnormalProductInfoDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.enums.AbnormalJumpPageType;
import com.sankuai.meituan.shangou.empower.productbiz.client.enums.TenantSpuAbnormalSourceTypeEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/3/22
 * @Description
 */
@TypeDoc(
        description = "商品异常详情"
)
@Setter
@Getter
@ToString
public class ProductAbnormalDetailVO {
    @FieldDoc(
            description = "渠道"
    )
    private Integer channelId;
    @FieldDoc(
            description = "异常类型code"
    )
    private String abnormalCode;
    @FieldDoc(
            description = "异常类型描述"
    )
    private String abnormalDesc;
    @FieldDoc(
            description = "异常处理文案指引"
    )
    private String abnormalHandleMsg;
    @FieldDoc(
            description = "具体异常信息"
    )
    private String abnormalInfo;

    @FieldDoc(
            description = "此类型异常是否还有更多异常信息"
    )
    private Boolean hasMoreAbnormalInfo;

    @FieldDoc(
            description = "商品来源类型，目前只有牵牛花C用到，其他业务无需关注，默认是自有门店"
    )
    private Integer productSourceType = TenantSpuAbnormalSourceTypeEnum.SELF_STORE.getCode();

    @FieldDoc(
            description = "跳转页面类型： @See com.sankuai.meituan.shangou.empower.productbiz.client.enums.AbnormalJumpPageType"
    )
    private Integer jumpPageType;

    @FieldDoc(
            description = "不一致类型列表"
    )
    private List<Integer> diffCompareTypeList;

    public static List<ProductAbnormalDetailVO> ofList(List<AbnormalProductInfoDTO> abnormalProductInfoDTOS, Integer productSourceType, List<Integer> diffCompareTypeList) {
        if (CollectionUtils.isEmpty(abnormalProductInfoDTOS)){
            return new ArrayList<>();
        }
        return abnormalProductInfoDTOS.stream().map(abnormalProductInfoDTO -> {
            ProductAbnormalDetailVO productAbnormalDetailVO = new ProductAbnormalDetailVO();
            productAbnormalDetailVO.setAbnormalCode(abnormalProductInfoDTO.getAbnormalCode());
            productAbnormalDetailVO.setAbnormalDesc(abnormalProductInfoDTO.getAbnormalDesc());
            productAbnormalDetailVO.setAbnormalHandleMsg(abnormalProductInfoDTO.getAbnormalHandleMsg());
            productAbnormalDetailVO.setAbnormalInfo(abnormalProductInfoDTO.getAbnormalInfo());
            productAbnormalDetailVO.setChannelId(abnormalProductInfoDTO.getChannelId());
            productAbnormalDetailVO.setHasMoreAbnormalInfo(abnormalProductInfoDTO.getHasMoreAbnormalInfo());
            if (Objects.isNull(productAbnormalDetailVO.getHasMoreAbnormalInfo())){
                productAbnormalDetailVO.setHasMoreAbnormalInfo(Boolean.FALSE);
            }
            productAbnormalDetailVO.setProductSourceType(productSourceType);
            Integer jumpPageType = abnormalProductInfoDTO.getJumpPageType();
            if (AbnormalJumpPageType.SPU_DETAIL.getType().equals(jumpPageType)){
                jumpPageType = AbnormalJumpPageType.STORE_SPU_DETAIL.getType();
            }
            productAbnormalDetailVO.setJumpPageType(jumpPageType);
            if (AbnormalJumpPageType.STORE_SPU_DIFF_COMPARE_LIST.getType().equals(abnormalProductInfoDTO.getJumpPageType())){
                productAbnormalDetailVO.setDiffCompareTypeList(diffCompareTypeList);
            }
            return productAbnormalDetailVO;
        }).collect(Collectors.toList());
    }
}
