package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * Email <EMAIL>
 * Date 2022/10/14 5:56 下午
 * Description
 */

@TypeDoc(
        description = "考勤异常申报列表查询请求"
)
@ApiModel("考勤异常申报列表查询请求")
@Data
public class AttendanceApprovalListRequest {

    @FieldDoc(
            description = "是否查询已完结", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否查询已完结")
    private Integer isFinished;

    @FieldDoc(
            description = "是否是审批视角查询（1：是）", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否是审批视角查询")
    private Integer isApprovalViewed;

    @FieldDoc(
            description = "每页条数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "每页条数")
    private Integer pageSize;

    @FieldDoc(
            description = "页码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "页码")
    private Integer pageNo;
}
