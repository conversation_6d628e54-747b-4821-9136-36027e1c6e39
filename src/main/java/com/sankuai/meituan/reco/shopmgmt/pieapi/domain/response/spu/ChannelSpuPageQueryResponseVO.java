// Copyright (C) 2020 Meituan
// All rights reserved
package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.PageInfoVO;
import com.sankuai.meituan.shangou.empower.productplatform.thrift.product.response.ChannelProductPageQueryResponse;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2020/11/26 上午11:57
 **/
@Data
public class ChannelSpuPageQueryResponseVO {
    private PageInfoVO pageInfo;
    private List<ChannelSpuBaseInfoVO> list;
    public ChannelSpuPageQueryResponseVO(ChannelProductPageQueryResponse response) {
        this.pageInfo = new PageInfoVO(response.getPageInfo());
        if (CollectionUtils.isEmpty(response.getProductList())) {
            this.list = new ArrayList<>();
        } else {
            this.list = response.getProductList().stream().map(channelProductDTO -> {
                ChannelSpuBaseInfoVO channelSpuBaseInfoVO = new ChannelSpuBaseInfoVO();
                channelSpuBaseInfoVO.setTenantId(channelProductDTO.getTenantId());
                channelSpuBaseInfoVO.setStoreId(channelProductDTO.getStoreId());
                channelSpuBaseInfoVO.setChannelId(channelProductDTO.getChannelId());
                channelSpuBaseInfoVO.setSpuId(channelProductDTO.getSpuId());
                return channelSpuBaseInfoVO;
            }).collect(Collectors.toList());
        }
    }
}
