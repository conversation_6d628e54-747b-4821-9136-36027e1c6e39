package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp;

import java.util.List;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ERP渠道商品SPU信息
 *
 * <AUTHOR>
 * @since 2023/05/12
 */
@TypeDoc(
        description = "ERP渠道商品SPU信息"
)
@Data
@ApiModel("ERP渠道商品SPU信息")
public class ErpChannelSpuVO {

    @FieldDoc(description = "租户ID", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "租户ID")
    private Long tenantId;

    @FieldDoc(description = "门店ID", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "门店ID")
    public Long storeId;

    @FieldDoc(description = "spu编码", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "spu编码")
    public String spuId;

    @FieldDoc(description = "渠道ID", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "渠道ID")
    private Integer channelId;

    @FieldDoc(description = "售卖状态 -1-未上线 1-已上架 2-已下架", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "售卖状态 -1-未上线 1-已上架 2-已下架")
    private Integer spuStatus;

    @FieldDoc(description = "渠道商品sku列表", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "渠道商品sku列表")
    private List<ErpChannelSkuVO> channelSkuList;

    @FieldDoc(description = "美团渠道spu编码", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "美团渠道spu编码")
    private String mtChannelSpuId;

    @FieldDoc(description = "渠道sku价格列表(目前只支持美团渠道)", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "渠道sku价格列表(目前只支持美团渠道)")
    private List<ChannelPriceVO> channelPriceList;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ChannelPriceVO {

        @FieldDoc(description = "渠道ID", requiredness = Requiredness.OPTIONAL)
        @ApiModelProperty(name = "渠道ID")
        private Integer channelId;

        @FieldDoc(description = "sku编码", requiredness = Requiredness.OPTIONAL)
        @ApiModelProperty(name = "sku编码")
        private String skuId;

        @FieldDoc(description = "渠道线上价格，单位是分", requiredness = Requiredness.OPTIONAL)
        @ApiModelProperty(name = "渠道线上价格，单位是分")
        private Long channelPrice;
    }

}
