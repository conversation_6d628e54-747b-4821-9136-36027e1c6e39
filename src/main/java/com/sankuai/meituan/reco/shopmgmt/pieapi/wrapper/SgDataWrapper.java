package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.dianping.lion.Environment;
import com.google.common.collect.ImmutableMap;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.PowerApiConstant;
import com.sankuai.waimai.dws.protocol.openapi.OpenapiQueryService;
import com.sankuai.waimai.dws.protocol.structs.DWSParam;
import com.sankuai.waimai.dws.protocol.structs.DWSResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.meituan.linz.boot.exception.ServiceRpcException;
import com.meituan.linz.boot.util.JacksonUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PoiSgRealTimeDataDto;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.DateUtils;
import com.sankuai.sgdata.query.thrift.DataQueryThriftService;
import com.sankuai.sgdata.query.thrift.request.QueryRequest;
import com.sankuai.sgdata.query.thrift.response.QueryResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * 数仓接口
 *
 * <AUTHOR>
 * @since 2021/9/3
 */
@Service
@Slf4j
public class SgDataWrapper {

    private static final String API_CODE = "b_service_baichuan_board_label_rt_data";
    private static final String APP_CODE = "shangou_reco_saas_data";

    /**
     * 有效订单量
     */
    private static final String FIN_ORDER_NUM_KEY = "finOrdNum";
    /**
     * 总营业额
     */
    private static final String OPEN_AMOUNT_GEV_KEY = "openAmtGtv";
    /**
     * 预计收入
     */
    private static final String TO_PREDICT_INCOM_KEY = "toPredictIncome";
    /**
     * 履约超时率
     */
    private static final String PERFORMCE_OVERTIME_RATE_KEY = "performanceOvertimeRate";

    @Autowired
    private DataQueryThriftService dataQueryThriftService;

    @Autowired
    private OpenapiQueryService.Iface openapiQueryService;

    /**
     * 查询数仓门店实时数据
     * 当日无交易数据，返回默认数据，指标都为0
     *
     * @param poiId 门店id
     * @return
     */
    public PoiSgRealTimeDataDto queryRealTimeData(List<Long> poiIds) {
        QueryResponse response;
        try {
            String today = DateUtils.format(new Date(), DateUtils.YYYYMMDD);
            QueryRequest request = new QueryRequest();
            request.setAppCode(APP_CODE);
            request.setApiCode(API_CODE);
            request.setNeedPage(false);
            Map<String, String> param = new HashMap<>();
            param.put("beginDate", today);
            param.put("referenceDate", today);
            param.put("poiId", StringUtils.join(poiIds, ','));
            request.setParam(param);
            response = dataQueryThriftService.query(request);
            log.info("查询数仓门店实时数据 poiId:{},response:{}", poiIds, JacksonUtils.toJson(response));
        }
        catch (TException e) {
            log.error("查询数仓门店实时数据异常 poiId:{}", poiIds);
            throw new ServiceRpcException("查询数仓门店实时数据异常", e);
        }

        if (response == null || response.getCode() != 0) {
            throw new ServiceRpcException("查询数仓门店实时数据失败");
        }

        if (response.getData() != null && CollectionUtils.isNotEmpty(response.getData().getDataList())) {
            return buildPoiSgRealTimeDataDto(response.getData().getDataList().get(0));
        }
        return buildDefaultPoiSgRealTimeDataDto();
    }


    public PoiSgRealTimeDataDto queryRealTimeDataByPowerAPi(List<Long> poiIds) {
        String today = DateUtils.format(new Date(), DateUtils.YYYYMMDD);
        DWSParam queryParam = new DWSParam();
        queryParam.setAppCode(PowerApiConstant.RealtimeFinanceProject.APP_CODE);
        queryParam.setAppKey(Environment.getAppName());
        queryParam.setToken(PowerApiConstant.RealtimeFinanceProject.REQ_ACCESS);
        queryParam.setApiCode(PowerApiConstant.RealtimeFinanceProject.API_CODE);
        queryParam.setSqlParam(new ImmutableMap.Builder<String,String>()
                .put("beginDate", today)
                .put("referenceDate", today)
                .put("poiId", StringUtils.join(poiIds, ','))
                .build());
        try {
            DWSResponse response = openapiQueryService.query(queryParam);

            if (response == null || response.getCode() != 0) {
                throw new ServiceRpcException("查询数仓powerApi门店实时数据失败");
            }

            if (response.getData() != null && CollectionUtils.isNotEmpty(response.getData().getDataList())) {
                return buildPoiSgRealTimeDataDto(response.getData().getDataList().get(0));
            }
        } catch (TException e) {
            log.error("查询数仓powerApi门店实时数据异常 poiId:{}", poiIds);
            throw new ServiceRpcException("查询数仓powerApi门店实时数据异常", e);
        }
        return buildDefaultPoiSgRealTimeDataDto();
    }

    private PoiSgRealTimeDataDto buildPoiSgRealTimeDataDto(Map<String, String> dataMap) {
        PoiSgRealTimeDataDto timeDataDto = new PoiSgRealTimeDataDto();
        if (StringUtils.isNotBlank(dataMap.get(FIN_ORDER_NUM_KEY))) {
            timeDataDto.setFinOrdNum(Integer.valueOf(dataMap.get(FIN_ORDER_NUM_KEY)));
        }
        if (StringUtils.isNotBlank(dataMap.get(OPEN_AMOUNT_GEV_KEY))) {
            timeDataDto.setOpenAmtGtv(Double.valueOf(dataMap.get(OPEN_AMOUNT_GEV_KEY)));
        }
        if (StringUtils.isNotBlank(dataMap.get(TO_PREDICT_INCOM_KEY))) {
            timeDataDto.setToPredictIncome(Double.valueOf(dataMap.get(TO_PREDICT_INCOM_KEY)));
        }
        if (StringUtils.isNotBlank(dataMap.get(PERFORMCE_OVERTIME_RATE_KEY))) {
            timeDataDto.setPerformanceOvertimeRate(Double.valueOf(dataMap.get(PERFORMCE_OVERTIME_RATE_KEY)));
        }
        return timeDataDto;
    }

    private PoiSgRealTimeDataDto buildDefaultPoiSgRealTimeDataDto() {
        PoiSgRealTimeDataDto timeDataDto = new PoiSgRealTimeDataDto();
        timeDataDto.setFinOrdNum(0);
        timeDataDto.setOpenAmtGtv(0d);
        timeDataDto.setToPredictIncome(0d);
        timeDataDto.setPerformanceOvertimeRate(0d);
        return timeDataDto;
    }
}