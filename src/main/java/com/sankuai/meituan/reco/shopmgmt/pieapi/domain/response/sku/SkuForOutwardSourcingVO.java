package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@TypeDoc(
        description = "待外采商品信息信息"
)
@Data
@ApiModel("待外采商品信息信息")
@NoArgsConstructor
public class SkuForOutwardSourcingVO {

    @FieldDoc(
            description = "商品SKU编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品SKU编码", required = true)
    private String skuId;

    @FieldDoc(
            description = "离线价格 单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "离线价格 单位:分", required = true)
    private Integer offlinePrice;

    @FieldDoc(
            description = "摊位ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "摊位ID", required = true)
    private Long boothId;

    @FieldDoc(
            description = "摊位名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "摊位名称", required = true)
    private String boothName;


    @FieldDoc(
            description = "商品对应订单信息列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品对应订单信息列表", required = true)
    private List<OrderInfoForOutwardSourcingVO> orderInfos;


}
