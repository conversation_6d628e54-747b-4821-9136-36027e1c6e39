package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 4/21/23
 */
@TypeDoc(
        description = "我的推广记录页面数据"
)
@Data
@ApiModel("我的推广记录页面数据")
public class PullNewMyRecordVo {


    @FieldDoc(
            description = "列表查询推广记录事件", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "列表查询推广记录事件", required = true)
    private List<PullNewDetailVo> pullNewDetailVos;



    @FieldDoc(
            description = "是否有下一页", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否有下一页", required = true)
    private boolean hasMore;


    @FieldDoc(
            description = "分页标识", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "分页标识", required = true)
    private Long markId;

}
