package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.tenant;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 5/6/22
 **/
@TypeDoc(
        description = "根据仓库id查询门店列表"
)
@ApiModel("根据仓库id查询门店列表")
@Data
public class QueryPoiByWarehouseIdRequest {
    @FieldDoc(
            description = "仓库id", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "仓库id")
    @NotNull
    private Long warehouseId;

    @FieldDoc(description = "租户id")
    @ApiModelProperty(name = "租户id")
    @NotNull
    private Long tenantId;
}
