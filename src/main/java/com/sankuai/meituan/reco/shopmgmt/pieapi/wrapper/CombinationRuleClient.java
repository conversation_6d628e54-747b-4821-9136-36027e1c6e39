package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.meituan.shangou.sac.dto.model.CombinationRuleDTO;
import com.meituan.shangou.sac.dto.request.rule.QueryCombinationRuleRequest;
import com.meituan.shangou.sac.dto.response.SacStatus;
import com.meituan.shangou.sac.dto.response.rule.QueryCombinationRuleResponse;
import com.meituan.shangou.sac.thrift.rule.CombinationRuleThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class CombinationRuleClient {

    @Autowired
    private CombinationRuleThriftService combinationRuleThriftService;


    public List<CombinationRuleDTO> queryCombinationRule(QueryCombinationRuleRequest request) {
        QueryCombinationRuleResponse response = combinationRuleThriftService.queryCombinationRule(request);
        handleSacStatus(response.getSacStatus());
        return response.getCombinationRuleDTOs();
    }


    private void handleSacStatus(SacStatus sacStatus) {
        if (sacStatus.getCode() != ResultCodeEnum.SUCCESS.getValue()) {
            throw new BizException(sacStatus.getCode(), sacStatus.getMessage());
        }
    }


}
