package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.order;

import javax.validation.constraints.NotNull;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 分页查询配送异常的订单列表的请求.
 *
 * <AUTHOR>
 * @since 2021/4/14 14:24
 */
@TypeDoc(
        description = "分页查询配送异常的订单列表的请求"
)
@ApiModel("分页查询配送异常的订单列表的请求")
@Data
public class QueryDeliveryErrorOrderRequest {

    @FieldDoc(
            description = "第几页", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "第几页", required = true)
    @NotNull
    private Integer page;

    @FieldDoc(
            description = "每页行数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "每页行数", required = true)
    @NotNull
    private Integer size;
}
