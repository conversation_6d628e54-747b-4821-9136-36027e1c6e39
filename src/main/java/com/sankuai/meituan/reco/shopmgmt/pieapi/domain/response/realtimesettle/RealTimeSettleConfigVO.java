package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.realtimesettle;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TypeDoc(
        description = "实时结算配置对象"
)
@Data
@ApiModel("渠道SKU信息")
public class RealTimeSettleConfigVO {

    @FieldDoc(
            description = "是否启用外采现结(0否1是)"
    )
    private Integer useRealTimeSettle;

    @FieldDoc(
            description = "结算限价率"
    )
    private Double settleLimitRatio;

    @FieldDoc(
            description = "结算单个商品最大限价额(单位:分)"
    )
    private Integer maxSkuPriceLimit;

    @FieldDoc(
            description = "支付超时时间"
    )
    private Integer payTimeOutSeconds;
}
