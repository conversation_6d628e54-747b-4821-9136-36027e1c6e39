package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.impl;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.AbstractSinglePendingTaskService;
import com.sankuai.meituan.reco.store.management.thrift.stocktake.CommonParam;
import com.sankuai.meituan.reco.store.management.thrift.stocktake.StockTakeThriftService;
import com.sankuai.meituan.reco.store.management.thrift.stocktake.TaskCountResult;
import com.sankuai.meituan.reco.store.management.thrift.stocktake.UserParam;
import com.sankuai.meituan.reco.store.management.thrift.stocktake.WaitStockTakeTaskCountRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Slf4j
@Service
public class StockTakePendingTaskService extends AbstractSinglePendingTaskService {

    @Resource
    private StockTakeThriftService stockTakeThriftService;

    @Override
    protected PendingTaskResult getPendingTaskCount(PendingTaskParam param) {
        WaitStockTakeTaskCountRequest request = new WaitStockTakeTaskCountRequest();
        CommonParam commonParam = new CommonParam();
        commonParam.setTenantId(param.getTenantId());
        commonParam.setStoreId(param.getStoreIds().get(0));
        commonParam.setEntityId(param.getEntityId());
        commonParam.setEntityType(param.getEntityType());

        UserParam userParam = new UserParam("","",param.getUser().getEmployeeId());
        request.setCommonParam(commonParam);
        request.setUserParam(userParam);

        TaskCountResult result;
        try {
            log.info("stockTakeThriftService.queryStockTakeTaskCount request:{}", request);
            result = stockTakeThriftService.queryStockTakeTaskCount(request);
            log.info("stockTakeThriftService.queryStockTakeTaskCount response:{}", result);
            return PendingTaskResult.createNumberMarker(result.getCount());
        } catch (Exception e) {
            log.error("stockTakePendingTaskService exception, request:{}", param,e);
        }
        return PendingTaskResult.createNumberMarker(0);
    }

    @Override
    protected AuthCodeEnum module() {
        return AuthCodeEnum.STOCK_CHECK;
    }
}
