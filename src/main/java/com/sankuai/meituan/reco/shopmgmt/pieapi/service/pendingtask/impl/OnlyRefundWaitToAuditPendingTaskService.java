package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.impl;

import com.meituan.shangou.saas.dto.StatusCodeEnum;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OCMSQueryOrderQuantityRequest;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OCMSQueryOrderQuantityResponse;
import com.meituan.shangou.saas.order.management.client.enums.QueryOrderTypeQuantityEnum;
import com.meituan.shangou.saas.order.management.client.service.online.OCMSQueryThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiEntityTypeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.AbstractSinglePendingTaskService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.TimeUtils;
import com.sankuai.meituan.util.ConfigUtilAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;


@Slf4j
@Service
public class OnlyRefundWaitToAuditPendingTaskService extends AbstractSinglePendingTaskService {

    private static final String CONFIG_QUERY_ORDER_CREATE_TIME_BEFORE = "query.order.create.time.before";

    @Autowired
    private OCMSQueryThriftService ocmsQueryThriftService;

    @Override
    public PendingTaskResult getPendingTaskCount(PendingTaskParam param) throws Exception {
        int count=0;
        // 请求订单数据源
        OCMSQueryOrderQuantityResponse orderQuantityResponse = queryOrderQuantity(param.getTenantId(), param.getStoreIds(), param.getEntityType());
        if (orderQuantityResponse.getStatus() != null
                && StatusCodeEnum.SUCCESS.getCode() == orderQuantityResponse.getStatus().getCode()
                && MapUtils.isNotEmpty(orderQuantityResponse.getOrderTypeQuantityMap())) {
            // 设置待审批退款数量
            count = MapUtils.getInteger(orderQuantityResponse.getOrderTypeQuantityMap(), QueryOrderTypeQuantityEnum.WAIT_REFUND_AUDIT.getValue(), 0);
        }
        return PendingTaskResult.createNumberMarker(count);
    }

    @Override
    protected AuthCodeEnum module() {
        return AuthCodeEnum.REFUND_AUDIT;
    }

    private OCMSQueryOrderQuantityResponse queryOrderQuantity(Long tenantId, List<Long> storeIdList, Integer entityType) {
        OCMSQueryOrderQuantityRequest request = new OCMSQueryOrderQuantityRequest();
        List<Integer> orderQuantityType = Lists.newArrayList();
        orderQuantityType.add(QueryOrderTypeQuantityEnum.WAIT_REFUND_AUDIT.getValue());
        request.setTenantId(tenantId);

        if (Objects.equals(PoiEntityTypeEnum.SHAREABLE_WAREHOUSE.code(), entityType)) {
            request.setShopIdList(Lists.newArrayList());
            request.setWarehouseIdList(storeIdList);
        }else {
            request.setShopIdList(storeIdList);
        }
        request.setOrderBizTypeList(MccConfigUtil.getRefundOrderbizTypeList());
        request.setOrderQuantityTypeList(orderQuantityType);
        //查询当前时间之前7天的订单
        request.setBeginCreateTime(TimeUtils.setTimeAtDayStart(TimeUtils.getBeforeDayTimeStamp(ConfigUtilAdapter.getInt(CONFIG_QUERY_ORDER_CREATE_TIME_BEFORE, 7))));
        request.setEndCreateTime(System.currentTimeMillis());
        OCMSQueryOrderQuantityResponse response = null;
        try {
            response = ocmsQueryThriftService.queryOrderQuantity(request);
            log.info("请求订单待审批数量,request:{}, response:{}", request, response);
        }catch (Exception e){
            log.error("查询订单待审批数量失败，request:{}", request, e);
        }
        return response;
    }
}
