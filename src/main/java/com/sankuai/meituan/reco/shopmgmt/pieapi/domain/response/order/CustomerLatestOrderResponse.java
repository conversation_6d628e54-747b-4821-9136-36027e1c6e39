package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: <EMAIL>
 * @Date: 2020-02-11 10:49
 * @Description:
 */
@TypeDoc(
        description = "查询最新订单响应"
)
@ApiModel("查询用户最新响应")
@Data
public class CustomerLatestOrderResponse {
    @FieldDoc(
            description = "最新订单", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "最新订单", required = false)
   private SimpleOrderVO latestOrder;
}
