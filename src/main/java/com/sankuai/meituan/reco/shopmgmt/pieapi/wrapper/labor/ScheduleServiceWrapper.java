package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.labor;

import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.sankuai.drunkhorsemgmt.labor.constants.DeleteEnum;
import com.sankuai.drunkhorsemgmt.labor.thrift.ScheduleThriftService;
import com.sankuai.drunkhorsemgmt.labor.thrift.dto.*;
import com.sankuai.drunkhorsemgmt.labor.thrift.request.*;
import com.sankuai.drunkhorsemgmt.labor.thrift.response.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.QueryDetailListResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.QueryShiftListResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.QueryRuleListResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.SearchRuleLocationResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.SearchRuleStoreOrgEmployeeResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.UpsertShiftResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo.*;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.ApiMethodParamThreadLocal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-10-19
 * @email <EMAIL>
 */
@Slf4j
@Service
public class ScheduleServiceWrapper {

    @Resource
    private ScheduleThriftService scheduleThriftService;

    @MethodLog(logRequest = true, logResponse = true)
    public CommonResponse<UpsertShiftResponse> upsertScheduleShift(UpsertShiftRequest request) {
        UpsertScheduleShiftReq req = new UpsertScheduleShiftReq();
        req.setOperator(buildOperator());
        req.setShiftDTO(new ShiftDTO(
                request.getShiftId(), request.getRuleId(), request.getColor(), request.getName(), request.getStartWorkTime(),
                request.getStartWorkCheckInDurationBegin(), request.getStartWorkCheckInDurationEnd(),
                request.getEndWorkTime(), request.getEndWorkCheckInDurationBegin(), request.getEndWorkCheckInDurationEnd(), null ,null
        ));
        UpsertScheduleShiftResponse response = scheduleThriftService.upsertScheduleShift(req);
        if (!response.getStatus().successful()) {
            return CommonResponse.fail(response.getStatus().getCode(), response.getStatus().getMsg(), null);
        }
        return CommonResponse.success(new UpsertShiftResponse(response.getShiftId()));
    }

    @MethodLog(logRequest = true, logResponse = true)
    public CommonResponse<Void> upsertRule(UpsertRuleRequest request) {
        UpsertScheduleRuleReq req = new UpsertScheduleRuleReq();
        req.setOperator(buildOperator());

        List<EmployeeIdAndNameDTO> employeeIdAndNameDTOList = employeeVoToDto(request.getEmployeeList());

        List<ScheduleRuleLocationDTO> ruleLocationDTOList = locationVoToDto(request.getLocationStoreList());

        List<ScheduleDetailDTO> detailDTOList = detailVoToDto(request.getScheduleDetailList());

        req.setScheduleRuleDTO(
                new ScheduleRuleDTO(
                        request.getRuleId(), ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId(), request.getBelongStoreId(), request.getStoreName(),
                        request.getName(), request.getType(), employeeIdAndNameDTOList, ruleLocationDTOList, detailDTOList, request.getShiftIdList()
                )
        );
        UpsertScheduleRuleResponse response = scheduleThriftService.upsertScheduleRule(req);
        if (!response.getStatus().successful()) {
            return CommonResponse.fail(response.getStatus().getCode(), response.getStatus().getMsg(), null);
        }
        return CommonResponse.success(null);
    }


    @MethodLog(logRequest = true, logResponse = true)
    public CommonResponse<Void> deleteShift(DeleteShiftRequest request) {
        DeleteScheduleResponse response = scheduleThriftService.deleteScheduleShift(new DeleteScheduleShiftReq(buildOperator(), request.getShiftId()));
        if (!response.getStatus().successful()) {
            return CommonResponse.fail(response.getStatus().getCode(), response.getStatus().getMsg(), null);
        }
        return CommonResponse.success(null);
    }

    @MethodLog(logRequest = true, logResponse = true)
    public CommonResponse<Void> deleteRule(DeleteRuleRequest request) {
        DeleteScheduleResponse response = scheduleThriftService.deleteScheduleRule(new DeleteScheduleRuleReq(buildOperator(), request.getRuleId()));
        if (!response.getStatus().successful()) {
            return CommonResponse.fail(response.getStatus().getCode(), response.getStatus().getMsg(), null);
        }
        return CommonResponse.success(null);
    }

    @MethodLog(logRequest = true, logResponse = true)
    public CommonResponse<QueryRuleListResponse> ruleList(@RequestBody @Valid QueryRuleListRequest request) {
        QueryScheduleRuleListResponse response = scheduleThriftService.queryScheduleRuleList(
                new QueryScheduleRuleListReq(
                        ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId(), buildOperator(),
                        request.getPageNo(), request.getPageSize(), request.getPoiNameKeyword()
                )
        );
        if (!response.getStatus().successful()) {
            return CommonResponse.fail(response.getStatus().getCode(), response.getStatus().getMsg(), null);
        }
        List<ScheduleRuleVO> scheduleRuleVOList = Optional.ofNullable(response.getScheduleRuleListInfoDTO())
                .orElse(Lists.newArrayList())
                .stream()
                .map(
                        dto -> {
                            ScheduleRuleVO vo = new ScheduleRuleVO();
                            vo.setRuleId(dto.getId());
                            vo.setStoreId(dto.getStoreId());
                            vo.setStoreName(dto.getStoreName());
                            vo.setName(dto.getName());
                            vo.setType(dto.getType());
                            vo.setEmployeeList(employeeDtoToVo(dto.getEmployeeDTOList()));
                            vo.setShiftList(shiftDtoToVo(dto.getShiftDTOList()));
                            vo.setScheduleDetailList(detailDtoToVo(dto.getDetailDTOList()));
                            vo.setLocationStoreList(locationDtoToVo(dto.getLocationDTOList()));
                            vo.setEmployeeNum(dto.getEmployeeNum());
                            vo.setShiftNum(dto.getShiftNum());
                            return vo;
                        }
                ).collect(Collectors.toList());
        return CommonResponse.success(new QueryRuleListResponse(scheduleRuleVOList, response.isHasMore(), response.getTotalCount()));
    }

    @MethodLog(logRequest = true, logResponse = true)
    public CommonResponse<QueryShiftListResponse> shiftList(@RequestBody @Valid QueryShiftListRequest request) {
        QueryScheduleShiftListResponse response = scheduleThriftService.queryScheduleShiftList(
                new QueryScheduleShiftListReq(
                        ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId(), request.getRuleId(), buildOperator()
                )
        );
        if (!response.getStatus().successful()) {
            return CommonResponse.fail(response.getStatus().getCode(), response.getStatus().getMsg(), null);
        }

        return CommonResponse.success(new QueryShiftListResponse(shiftDtoToVo(response.getShiftDTOList())));
    }

    @MethodLog(logRequest = true, logResponse = true)
    public CommonResponse<QueryDetailListResponse> detailList(@RequestBody @Valid QueryDetailListRequest request) {
        QueryScheduleDetailListResponse response = scheduleThriftService.queryScheduleDetailList(
                new QueryScheduleDetailListReq(
                        ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId(), request.getRuleId(), request.getStartTime(), request.getEndTime()
                )
        );
        if (!response.getStatus().successful()) {
            return CommonResponse.fail(response.getStatus().getCode(), response.getStatus().getMsg(), null);
        }

        return CommonResponse.success(new QueryDetailListResponse(detailDtoToVo(response.getDetailDTOList())));
    }

    public CommonResponse<SearchRuleLocationResponse> locationStoreList(@RequestBody @Valid SearchRuleLocationRequest request) {
        SearchScheduleLocationStoreResponse response = scheduleThriftService.searchScheduleStoreLocation(
                new SearchScheduleLocationStoreReq(
                        ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId(),
                        request.getStoreName(),
                        ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId(),
                        request.getPageNo(),
                        request.getPageSize())
        );
        if (!response.getStatus().successful()) {
            return CommonResponse.fail(response.getStatus().getCode(), response.getStatus().getMsg(), null);
        }
        return CommonResponse.success(new SearchRuleLocationResponse(
                locationDtoToVo(response.getScheduleRuleLocationDTOList()),
                response.isHasMore(),
                response.getTotalCount())
        );
    }

    public CommonResponse<SearchRuleStoreOrgEmployeeResponse> storeOrgEmployeeList(@RequestBody @Valid SearchRuleStoreOrgEmployeeRequest request) {
        SearchScheduleRuleOrgEmployeeReq req = new SearchScheduleRuleOrgEmployeeReq();
        req.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        req.setPoiId(request.getBelongStoreId());
        req.setEmployeeNameKeyword(request.getEmployeeNameKeyword());
        req.setPage(request.getPageNo());
        req.setPageSize(request.getPageSize());
        SearchScheduleOrgEmployeeResponse response = scheduleThriftService.searchScheduleStoreOrgEmployee(req);

        if (!response.getStatus().successful()) {
            return CommonResponse.fail(response.getStatus().getCode(), response.getStatus().getMsg(), null);
        }
        return CommonResponse.success(new SearchRuleStoreOrgEmployeeResponse(
                orgEmployeeToVO(response.getEmployeeBaseInfoDTOList()),
                response.isHasMore(),
                response.getTotalCount())
        );
    }


    private List<ScheduleDetailVO> detailDtoToVo(List<ScheduleDetailDTO> scheduleDetailDTOList) {
        return Optional
                .ofNullable(scheduleDetailDTOList)
                .orElse(Lists.newArrayList())
                .stream()
                .map(
                        scheduleDetailDTO -> {
                            ScheduleDetailVO vo = new ScheduleDetailVO();
                            vo.setEmployeeId(scheduleDetailDTO.getEmployeeId());
                            vo.setEmployeeName(scheduleDetailDTO.getEmployeeName());
                            vo.setDetailList(
                                    Optional
                                            .ofNullable(scheduleDetailDTO.getDetailDTOList())
                                            .orElse(Lists.newArrayList())
                                            .stream()
                                            .map(detailDTO -> new ScheduleDetailVO.DetailVO(
                                                            detailDTO.getDate(),
                                                            shiftDtoToVo(detailDTO.getShiftDTOList())
                                                    )
                                            ).collect(Collectors.toList()));
                            return vo;
                        }
                ).collect(Collectors.toList());
    }

    private List<LocationStoreVO> locationDtoToVo(List<ScheduleRuleLocationDTO> dtoList) {
        return Optional
                .ofNullable(dtoList)
                .orElse(Lists.newArrayList())
                .stream()
                .map(
                        dto -> {
                            LocationStoreVO vo = new LocationStoreVO();
                            vo.setStoreId(dto.getStoreId());
                            vo.setStoreName(dto.getStoreName());
                            vo.setLongitude(dto.getLongitude());
                            vo.setLatitude(dto.getLatitude());
                            vo.setDescription(dto.getDescription());
                            return vo;
                        }
                ).collect(Collectors.toList());
    }

    private List<RuleEmployeeVO> employeeDtoToVo(List<EmployeeIdAndNameDTO> dtoList) {
        return Optional
                .ofNullable(dtoList)
                .orElse(Lists.newArrayList())
                .stream()
                .map(
                        dto -> {
                            RuleEmployeeVO vo = new RuleEmployeeVO();
                            vo.setEmployeeId(dto.getEmployeeId());
                            vo.setEmployeeName(dto.getName());
                            return vo;
                        }
                ).collect(Collectors.toList());
    }

    private List<ScheduleShiftVO> shiftDtoToVo(List<ShiftDTO> shiftDTOList) {
        return Optional
                .ofNullable(shiftDTOList)
                .orElse(Lists.newArrayList())
                .stream()
                .map(
                        dto -> {
                            ScheduleShiftVO vo = new ScheduleShiftVO();
                            vo.setRuleId(dto.getRuleId());
                            vo.setShiftId(dto.getId());
                            vo.setColor(dto.getColor());
                            vo.setName(dto.getName());
                            vo.setStartWorkTime(dto.getStartWorkTime());
                            vo.setStartWorkCheckInDurationBegin(dto.getStartWorkCheckInDurationBegin());
                            vo.setStartWorkCheckInDurationEnd(dto.getStartWorkCheckInDurationEnd());
                            vo.setEndWorkTime(dto.getEndWorkTime());
                            vo.setEndWorkCheckInDurationBegin(dto.getEndWorkCheckInDurationBegin());
                            vo.setEndWorkCheckInDurationEnd(dto.getEndWorkCheckInDurationEnd());
                            return vo;
                        }
                ).collect(Collectors.toList());
    }

    private List<ScheduleDetailDTO> detailVoToDto(List<ScheduleDetailVO> scheduleDetailList) {
        return scheduleDetailList
                .stream()
                .map(scheduleDetailVO -> new ScheduleDetailDTO(scheduleDetailVO.getEmployeeId(), scheduleDetailVO.getEmployeeName(),
                                Optional.ofNullable(scheduleDetailVO.getDetailList())
                                        .orElse(Lists.newArrayList())
                                        .stream()
                                        .map(detailVO -> new ScheduleDetailDTO.DetailDTO(detailVO.getDate(), shiftVOToDTO(detailVO.getShiftList()), DeleteEnum.NOT_DELETE.getCode()))
                                        .collect(Collectors.toList())
                        )
                )
                .collect(Collectors.toList());
    }

    private List<ScheduleRuleLocationDTO> locationVoToDto(List<LocationStoreVO> locationStoreList) {
        return locationStoreList
                .stream()
                .map(locationStoreVO -> new ScheduleRuleLocationDTO(locationStoreVO.getStoreId(), locationStoreVO.getStoreName(),
                                locationStoreVO.getLongitude(), locationStoreVO.getLatitude(), locationStoreVO.getAllowCheckinRange(), locationStoreVO.getDescription()
                        )
                ).collect(Collectors.toList());
    }

    private List<EmployeeIdAndNameDTO> employeeVoToDto(List<RuleEmployeeVO> employeeVOS) {
        return employeeVOS
                .stream()
                .map(employeeVO -> new EmployeeIdAndNameDTO(employeeVO.getEmployeeName(), employeeVO.getEmployeeId()))
                .collect(Collectors.toList());
    }

    private List<RuleOrgEmployeeVO> orgEmployeeToVO(List<EmployeeDTO> employeeBaseInfoDTOS) {
        return Optional
                .ofNullable(employeeBaseInfoDTOS)
                .orElse(Lists.newArrayList())
                .stream()
                .map(
                        dto -> new RuleOrgEmployeeVO(dto.getEmployeeId(), dto.getName(), dto.getAccountId(), dto.getAccountName())
                ).collect(Collectors.toList());
    }

    private List<ShiftDTO> shiftVOToDTO(List<ScheduleShiftVO> voList) {
        return Optional
                .ofNullable(voList)
                .orElse(Lists.newArrayList())
                .stream()
                .map(
                        vo -> new ShiftDTO(
                                vo.getShiftId(), vo.getRuleId(), vo.getColor(), vo.getName(), vo.getStartWorkTime(),
                                vo.getStartWorkCheckInDurationBegin(), vo.getStartWorkCheckInDurationEnd(),
                                vo.getEndWorkTime(), vo.getEndWorkCheckInDurationBegin(), vo.getEndWorkCheckInDurationEnd(),
                                null , null
                        )
                ).collect(Collectors.toList());
    }


    private EmployeeDTO buildOperator() {
        return new EmployeeDTO(
                ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId(),
                ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountName(),
                ApiMethodParamThreadLocal.getIdentityInfo().getUser().getOperatorName(),
                ApiMethodParamThreadLocal.getIdentityInfo().getUser().getEmployeeId()
        );
    }

}
