package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp;

import java.util.List;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * ERP门店商品渠道价格查询请求结果
 *
 * <AUTHOR>
 * @since 2023/05/12
 */
@TypeDoc(
        description = "ERP门店商品渠道价格查询请求结果"
)
@ApiModel("ERP门店商品渠道价格查询请求结果")
@Getter
@Setter
@ToString
public class ErpStoreSpuQueryChannelPriceResponseVO {

    @FieldDoc(description = "渠道价格信息列表", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "渠道价格信息列表")
    private List<ErpStoreSpuChannelPriceVO> channelPriceList;
}
