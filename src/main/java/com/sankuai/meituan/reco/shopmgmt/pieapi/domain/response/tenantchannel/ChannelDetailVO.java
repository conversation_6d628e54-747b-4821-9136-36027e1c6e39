package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.tenantchannel;

import lombok.*;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/8 16:43
 * @Description:
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class ChannelDetailVO {

    /**
     * 渠道编码
     */
    private Integer channelId;

    /**
     * 渠道名称或别名; 如果有别名，则取别名
     */
    private String channelName;

    /**
     * 渠道类型 1-公有渠道 0-私有渠道 -1 - pos渠道
     */
    private Integer standard = 1;

    /**
     * 渠道编码
     */
    public String channelCode;

    public String logo;

    /**
     * 颜色
     */
    public String color;

    /**
     * 渠道状态 0-停用 1-启用
     */
    public Integer status;

    public Boolean isOpenApp;


    /**
     * 订单域类型
     */
    public Integer orderBizType;

    /**
     * 渠道缩写
     */
    public String channelAbbr;

    /**
     * 渠道打印logo
     */
    public String channelPrintLogo;

    /**
     * 渠道名称
     */
    public String channelFullName;


    /**
     * 渠道别名
     */
    public String channelAlias;

    /**
     * 灰色logo
     */
    public String greyLogo;
}
