package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import java.util.List;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;


@TypeDoc(
        description = "批量保存审核模板响应"
)
@ApiModel("批量保存审核模板响应")
@Getter
@Setter
@ToString
public class ReasonTemplateBatchSaveResponse {

    @FieldDoc(
            description = "失败明细列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "失败明细列表")
    private List<FailItem> failList;

    @TypeDoc(
            description = "失败明细"
    )
    @ApiModel("失败明细")
    @Getter
    @Setter
    @ToString
    public static class FailItem {

        private String reason;

        private String msg;
    }

}
