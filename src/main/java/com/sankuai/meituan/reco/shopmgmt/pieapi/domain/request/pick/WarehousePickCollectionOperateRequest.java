package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "波次拣货集单请求"
)
@ApiModel("波次拣货集单请求")
@Data
public class WarehousePickCollectionOperateRequest {

    @FieldDoc(
            description = "单号列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "单号列表")
    private List<String> outWarehouseOrderList;

    @FieldDoc(
            description = "任务数", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "任务数")
    private Integer taskNum;

    @FieldDoc(
            description = "单据类型：参考OrderTagConstant", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "出库单类型")
    private Integer orderType = 1;

}
