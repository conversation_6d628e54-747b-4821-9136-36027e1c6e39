package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.price;

import com.google.common.base.Preconditions;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.ChannelStoreCustomSkuKey;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/7/3
 */
@ApiModel(
        "商品是否可以改价"
)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SkuCanModifyPriceRequest {
    @FieldDoc(
            description = "渠道商品列表"
    )
    @ApiModelProperty(value = "渠道商品列表", required = true)
    private List<ChannelStoreCustomSkuKey> storeSkuInfos;


    public void validate() {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(storeSkuInfos), "商品列表不能为空");
        storeSkuInfos.forEach(storeSpuInfo -> {
            Preconditions.checkArgument(StringUtils.isNotBlank(storeSpuInfo.getCustomSpuId()), "商品信息不能为空");
            Preconditions.checkArgument(StringUtils.isNotBlank(storeSpuInfo.getCustomSkuId()), "商品信息不能为空");
            Preconditions.checkArgument(Objects.nonNull(storeSpuInfo.getStoreId()), "门店信息不能为空");
            Preconditions.checkArgument(Objects.nonNull(storeSpuInfo.getChannelId()), "渠道信息不能为空");
        });
    }
}
