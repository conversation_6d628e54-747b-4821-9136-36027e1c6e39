package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.wavepick.converter;

import com.sankuai.meituan.reco.pickselect.thrift.wave.dto.WavePickWaveTaskDTO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.WarehouseWaveTaskDetailResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * @Description:
 * @Author: zhangjian155
 * @Date: 2022/8/25 16:13
 */
@Mapper(componentModel = "spring", uses = {WarehouseContainerBoxModuleConverter.class, WarehouseReceiveStoreInfoConverter.class, WarehouseGoodsOwnerModuleConverter.class})
public abstract class WarehouseWaveTaskDetailResponseConverter {

    @Mapping(source = "stockOutOrderType", target = "periodStatus")
    public abstract WarehouseWaveTaskDetailResponse convert2Response(WavePickWaveTaskDTO wavePickWaveTaskDTO);
}
