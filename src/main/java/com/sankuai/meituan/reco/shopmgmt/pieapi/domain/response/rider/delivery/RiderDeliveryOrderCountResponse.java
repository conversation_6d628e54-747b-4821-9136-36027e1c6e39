package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.rider.delivery;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@TypeDoc(
        description = "查询骑手订单数量响应"
)
@Data
@ApiModel("查询骑手订单数量响应")
@NoArgsConstructor
@AllArgsConstructor
public class RiderDeliveryOrderCountResponse {

    @FieldDoc(
            description = "待接单数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "待接单数量", required = true)
    private Integer riderWaitToGetOrderCount;

    @FieldDoc(
            description = "待取货数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "待取货数量", required = true)
    private Integer riderWaitToTakeGoodsCount;

    @FieldDoc(
            description = "配送中数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "配送中数量", required = true)
    private Integer riderInDeliveryCount;
}
