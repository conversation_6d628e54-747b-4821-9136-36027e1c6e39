package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.labor;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2022-10-18
 * @email <EMAIL>
 */
@TypeDoc(
        description = "新建/编辑班次请求"
)
@ApiModel("新建/编辑班次请求")
@Data
public class QueryShiftListRequest {

    @FieldDoc(
            description = "规则id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "页码")
    private Long ruleId;

}
