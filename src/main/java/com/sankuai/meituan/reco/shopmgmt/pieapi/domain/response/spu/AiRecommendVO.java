package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import java.util.Objects;

import lombok.Data;

/**
 * AI推荐信息
 *
 * <AUTHOR>
 * @since 2025/3/24
 */
@Data
public class AiRecommendVO {
    /**
     * AI推荐的卖点
     */
    private Boolean aiRecommendSellingPoint;
    public static com.sankuai.meituan.shangou.empower.productbiz.client.dto.AiRecommendDTO convertAiRecommendBizDTO(AiRecommendVO aiRecommendVO) {
        if (Objects.isNull(aiRecommendVO)) {
            return null;
        }
        com.sankuai.meituan.shangou.empower.productbiz.client.dto.AiRecommendDTO aiRecommendDTO = new com.sankuai.meituan.shangou.empower.productbiz.client.dto.AiRecommendDTO();
        aiRecommendDTO.setAiRecommendSellingPoint(aiRecommendVO.getAiRecommendSellingPoint());
        return aiRecommendDTO;
    }

    public static AiRecommendVO  fetchAiRecommendBizDTO(com.sankuai.meituan.shangou.empower.productbiz.client.dto.AiRecommendDTO aiRecommendDTO) {
        if (Objects.isNull(aiRecommendDTO)) {
            return null;
        }
        AiRecommendVO aiRecommendVO = new AiRecommendVO();
        aiRecommendVO.setAiRecommendSellingPoint(aiRecommendDTO.getAiRecommendSellingPoint());
        return aiRecommendVO;
    }

    public static com.sankuai.meituan.shangou.empower.ocms.client.product.dto.AiRecommendDTO convertAiRecommendOCMSDTO(AiRecommendVO aiRecommendVO) {
        if (Objects.isNull(aiRecommendVO)) {
            return null;
        }
        com.sankuai.meituan.shangou.empower.ocms.client.product.dto.AiRecommendDTO aiRecommendDTO = new com.sankuai.meituan.shangou.empower.ocms.client.product.dto.AiRecommendDTO();
        aiRecommendDTO.setAiRecommendSellingPoint(aiRecommendVO.getAiRecommendSellingPoint());
        return aiRecommendDTO;
    }

    public static AiRecommendVO  fetchAiRecommendOCMSDTO(com.sankuai.meituan.shangou.empower.ocms.client.product.dto.AiRecommendDTO aiRecommendDTO) {
        if (Objects.isNull(aiRecommendDTO)) {
            return null;
        }
        AiRecommendVO aiRecommendVO = new AiRecommendVO();
        aiRecommendVO.setAiRecommendSellingPoint(aiRecommendDTO.getAiRecommendSellingPoint());
        return aiRecommendVO;
    }
}
