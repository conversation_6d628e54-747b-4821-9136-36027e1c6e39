package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pullnew;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2021/11/1 5:24 下午
 **/
@TypeDoc(
        description = "拉新统计聚合"
)
@Data
@ApiModel("展示微信拉新统计")
@NoArgsConstructor
@AllArgsConstructor
public class PullNewStatAggrVo {
    @FieldDoc(
            description = "我的推广统计"
    )
    private PullNewStatVo selfStat;

    @FieldDoc(
            description = "下级人数"
    )
    private int subordinateCount;

    @FieldDoc(
            description = "下级推广统计"
    )
    private PullNewStatVo subordinateStat;
}
