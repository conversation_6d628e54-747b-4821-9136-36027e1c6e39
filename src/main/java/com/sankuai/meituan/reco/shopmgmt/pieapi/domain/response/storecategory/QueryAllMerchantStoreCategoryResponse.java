package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.storecategory;

import com.alibaba.fastjson.annotation.JSONField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 */
@TypeDoc(
        description = "商品池店内分类响应"
)
@ApiModel("商品池店内分类响应")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class QueryAllMerchantStoreCategoryResponse {

    @FieldDoc(
            description = "商品池店内分类列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品池店内分类列表", required = true)
    @JSONField(name = "assemblyChannelStoreCategoryList")
    private List<MerchantStoreCategoryVO> categoryList;

}
