package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.impl;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.AbstractSinglePendingTaskService;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.sdms.sdk.verify.VerifyTaskThriftService;
import com.sankuai.shangou.logistics.sdms.sdk.verify.request.QueryVerifyTaskRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 查询骑手待采集任务数量
 * @date 2025-03-28
 */
@Service
@Slf4j
public class DHVerifyTaskPendingTaskService extends AbstractSinglePendingTaskService {

    @Resource
    private VerifyTaskThriftService verifyTaskThriftService;

    @Override
    protected PendingTaskResult getPendingTaskCount(PendingTaskParam param) throws Exception {
        try {
            QueryVerifyTaskRequest request = new QueryVerifyTaskRequest();
            request.setRiderAccountId(param.getUser().getAccountId());
            TResult<Integer> countResult = verifyTaskThriftService.queryRiderDoingTaskCount(request);
            return PendingTaskResult.createNumberMarker(countResult.getData());
        } catch (Exception e) {
            log.error("查询骑手待采集任务数量异常", e);
            return PendingTaskResult.createNumberMarker(0);
        }
    }

    @Override
    protected AuthCodeEnum module() {
        return AuthCodeEnum.SMILE_ACT;
    }
}
