package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.auth;

import com.meituan.shangou.saas.tenant.thrift.dto.employee.EmployeeDto;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2018/10/23
 * 员工返回对象
 **/
@Data
@Builder
public class EmployeeVo {

    /**
     * 员工ID
     */
    private Long employeeId;

    /**
     * 员工名称
     */
    private String employeeName;

    public static EmployeeVo build(EmployeeDto bo) {
        return builder()
                .employeeId(bo.getEmployeeId())
                .employeeName(bo.getEmployeeName())
                .build();
    }


}
