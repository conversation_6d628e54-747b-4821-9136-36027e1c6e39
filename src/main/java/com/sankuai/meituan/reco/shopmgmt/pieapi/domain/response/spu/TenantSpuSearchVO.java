package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.controller.poi.response.PoiGroupVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.WeightTypeEnum;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.BrandDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.CategoryDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelCategoryDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.PoiGroupInfoDto;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.TenantSpuSearchDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Objects;

/**
 * 快捷建品-商品池搜索结果
 *
 * <AUTHOR>
 * @date 2021-09-26 20:14
 */
@Data
@TypeDoc(
        description = "商品池搜索结果"
)
@ApiModel("商品池搜索结果")
@EqualsAndHashCode(callSuper = true)
public class TenantSpuSearchVO extends BaseSearchVO {
    @FieldDoc(
            description = "租户商品名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "租户商品名称")
    private String tenantName;
    @FieldDoc(
            description = "区域商品名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "区域商品名称")
    private String regionName;
    @FieldDoc(
            description = "商品spuId", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品spu")
    private String spuId;
    @FieldDoc(
            description = "带入总部商品图片地址", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "租户SPU图片列表")
    private List<String> tenantImageUrls;
    @FieldDoc(
            description = "带入城市商品图片地址", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "城市SPU图片列表")
    private List<String> regionImageUrls;
    @FieldDoc(
            description = "产地", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "产地")
    private String producingPlace;
    @FieldDoc(
            description = "商品分类", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "分类")
    private CategoryDTO category;
    @FieldDoc(
            description = "带入商品品牌", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "品牌")
    private BrandDTO brand;
    @FieldDoc(
            description = "门店商品是否已经存在", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店商品是否已经存在")
    private Boolean isExistInStore;
    @FieldDoc(
            description = "称重属性"
    )
    @ApiModelProperty(name = "称重属性")
    private Integer standerType;
    @FieldDoc(
            description = "美团渠道类目信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "分类")
    private ChannelCategoryDTO mtChannelCategory;

    @FieldDoc(
            description = "多渠道-饿了么渠道类目信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "多渠道-饿了么渠道类目信息")
    private ChannelCategoryDTO elmChannelCategory;


    @FieldDoc(
            description = "图详"
    )
    private List<String> pictureContents;

    @FieldDoc(
            description = "视频"
    )
    private VideoVO videoVO;

    @FieldDoc(
            description = "货盘归属"
    )
    private Integer palletSrc;

    @FieldDoc(
            description = "门店分组信息"
    )
    private List<PoiGroupVo> poiGroupList;


    public static TenantSpuSearchVO convert(TenantSpuSearchDTO dto) {
        TenantSpuSearchVO vo = new TenantSpuSearchVO();
        vo.setDatasourceType(dto.getDatasourceType());
        vo.setTenantName(dto.getTenantSpuName());
        vo.setRegionName(dto.getRegionSpuName());
        vo.setSpuId(dto.getSpuId());
        vo.setProducingPlace(dto.getProducingPlace());
        vo.setTenantImageUrls(dto.getTenantSpuImageUrls());
        vo.setRegionImageUrls(dto.getRegionSpuImageUrls());
        vo.setCategory(dto.getCategory());
        vo.setBrand(dto.getBrand());
        WeightTypeEnum weightTypeEnum = WeightTypeEnum.getByCode(dto.getWeightType());
        if (weightTypeEnum != null) {
            vo.setStanderType(weightTypeEnum.toStanderType().getCode());
        }
        vo.setIsExistInStore(dto.getIsExistInStore());
        vo.setMtChannelCategory(dto.getMtChannelCategory());

        // 若有饿了么渠道，添加
        if (Objects.nonNull(dto.getElmChannelCategory())) {
            vo.setElmChannelCategory(dto.getElmChannelCategory());
        }

        vo.setPictureContents(dto.getPictureContents());
        vo.setVideoVO(VideoVO.convert(dto.getVideo()));
        vo.setPalletSrc(dto.getPalletSrc());
        vo.setPoiGroupList(Fun.map(dto.getPoiGroupInfoDtoList(), TenantSpuSearchVO::convert));

        return vo;
    }

    private static PoiGroupVo convert(PoiGroupInfoDto poiGroupInfoDto) {
        if (Objects.isNull(poiGroupInfoDto)) {
            return null;
        }

        PoiGroupVo poiGroupVo = new PoiGroupVo();
        poiGroupVo.setPoiGroupId(poiGroupInfoDto.getPoiGroupId());
        poiGroupVo.setPoiGroupViewId(poiGroupInfoDto.getPoiGroupViewId());
        poiGroupVo.setPoiGroupName(poiGroupInfoDto.getPoiGroupName());
        poiGroupVo.setPoiGroupSize(poiGroupInfoDto.getPoiGroupSize());

        return poiGroupVo;

    }
}
