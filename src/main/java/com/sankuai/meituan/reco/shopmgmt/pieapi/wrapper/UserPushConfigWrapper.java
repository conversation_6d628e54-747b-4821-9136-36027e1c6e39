package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.PlatformType;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.PushConfigByUuidRequest;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.ServiceType;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.UserPushConfig;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.UserPushConfigThriftService;
import com.sankuai.meituan.reco.pickselect.ebase.thrift.exception.EbaseLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.AppAuthIdConstants;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.UserPushConfigVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User;
import com.sankuai.meituan.reco.store.saas.infrastructure.shield.common.monitor.CommonMonitorTransaction;
import com.sankuai.meituan.shangou.saas.message.enums.push.PushOsTypeEnum;
import com.sankuai.meituan.shangou.saas.message.enums.push.PushTypeEnum;
import com.sankuai.meituan.shangou.saas.message.request.push.BindDeviceTokenRequest;
import com.sankuai.meituan.shangou.saas.message.request.push.BindDeviceUserRequest;
import com.sankuai.meituan.shangou.saas.message.request.push.PushReceivedRequest;
import com.sankuai.meituan.shangou.saas.message.request.push.UnbindDeviceUserRequest;
import com.sankuai.meituan.shangou.saas.message.response.MessageCommonResponse;
import com.sankuai.meituan.shangou.saas.message.service.PushThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Create by yujing10 on 2018/10/17.
 */
@Component
@Rhino
@Slf4j
public class UserPushConfigWrapper {

    @Resource
    private UserPushConfigThriftService.Iface userPushConfigThriftService;

    @Resource
    private PushThriftService pushThriftService;

    @Degrade(rhinoKey = "UserPushConfigWrapper.saveOrUpdatePushParam",
            fallBackMethod = "saveOrUpdatePushParamFallback",
            timeoutInMilliseconds = 1400,
            ignoreExceptions = {CommonLogicException.class})
    @MethodLog(logRequest = true, logResponse = true)
    @CommonMonitorTransaction
    public CommonResponse<Void> saveOrUpdatePushParam(UserPushConfigVO configVO, ServiceType serviceType) {
        UserPushConfig pushConfig = new UserPushConfig();
        pushConfig.setUuid(configVO.getUuid());
        pushConfig.setPushToken(configVO.getPushToken());
        pushConfig.setUnionId(configVO.getUnionId());
        pushConfig.setPlatformType(configVO.getPlatformType());
        pushConfig.setAppVersion(configVO.getAppVersion());
        pushConfig.setServiceType(serviceType.getValue());

        try {
            return userPushConfigThriftService.saveOrUpdatePushParam(pushConfig) ? CommonResponse.success(null) : CommonResponse.fail(ResultCode.RETRY_INNER_FAIL);
        } catch (TException e) {
            log.error("保存/更新设备推送pushToken/unionId失败, error:{}", e.getMessage(), e);
            throw new CommonRuntimeException("保存/更新设备推送pushToken/unionId失败");
        }
    }

    private CommonResponse<Void> saveOrUpdatePushParamFallback(UserPushConfigVO configVO, ServiceType serviceType) {
        throw new CommonRuntimeException("保存/更新设备推送pushToken/unionId失败");
    }

    @Degrade(rhinoKey = "UserPushConfigWrapper.queryPushConfigByUuid",
            fallBackMethod = "queryPushConfigByUuidFallback",
            timeoutInMilliseconds = 1400,
            ignoreExceptions = {CommonLogicException.class})
    @MethodLog(logRequest = true, logResponse = true)
    @CommonMonitorTransaction
    public CommonResponse<UserPushConfigVO> queryPushConfigByUuid(String uuid, ServiceType serviceType) {
        try {
            UserPushConfig pushConfig = userPushConfigThriftService.queryPushConfigByUuid(
                    new PushConfigByUuidRequest()
                            .setUuid(uuid)
                            .setServiceType(serviceType.getValue()));
            if (pushConfig == null) {
                return CommonResponse.success(null);
            }
            UserPushConfigVO vo = new UserPushConfigVO();
            vo.setUuid(pushConfig.getUuid());
            vo.setPushToken(pushConfig.getPushToken());
            vo.setUnionId(pushConfig.getUnionId());
            vo.setPlatformType(pushConfig.getPlatformType());
            vo.setAppVersion(pushConfig.getAppVersion());
            return CommonResponse.success(vo);
        } catch (EbaseLogicException e) {
            log.error("查询设备推送pushToken/unionId失败, error:{}", e.getMessage(), e);
            return CommonResponse.fail(ResultCode.RETRY_INNER_FAIL.getCode(), ResultCode.RETRY_INNER_FAIL.getErrorMessage());
        } catch (TException e) {
            log.error("查询设备推送pushToken/unionId失败, error:{}", e.getMessage(), e);
            throw new CommonRuntimeException("查询设备推送pushToken/unionId失败");
        }
    }

    public CommonResponse<UserPushConfigVO> queryPushConfigByUuidFallback(String uuid, ServiceType serviceType) {
        throw new CommonRuntimeException("查询设备推送pushToken/unionId失败");
    }


    @Degrade(rhinoKey = "UserPushConfigWrapper.saveMessageCenterPushToken",
            fallBackMethod = "saveMessageCenterPushTokenFallBack",
            timeoutInMilliseconds = 1400,
            ignoreExceptions = {CommonLogicException.class})
    @MethodLog(logRequest = true, logResponse = true)
    @CommonMonitorTransaction
    public CommonResponse<Void> saveMessageCenterPushToken(UserPushConfigVO pushConfig) {
        try {

            BindDeviceTokenRequest bindDeviceTokenRequest = new BindDeviceTokenRequest();
            bindDeviceTokenRequest.setAppCode(String.valueOf(AppAuthIdConstants.SHU_GUO_PAI.val()));
            bindDeviceTokenRequest.setAppVersion(pushConfig.getAppVersion());
            bindDeviceTokenRequest.setDeviceId(pushConfig.getUuid());
            bindDeviceTokenRequest.setOsType(platformType2OsType(pushConfig.getPlatformType()).name());
            bindDeviceTokenRequest.setNotificationToken(pushConfig.getPushToken());
            bindDeviceTokenRequest.setAppChannel(pushConfig.getAppChannel());
            MessageCommonResponse thriftResponse = pushThriftService.bindDeviceToken(bindDeviceTokenRequest);
            if (thriftResponse.getCode() == 0) {
                return CommonResponse.success(null);
            } else {
                log.info("消息中心绑定token绑定失败: {}", thriftResponse.getMsg());
                return CommonResponse.fail(ResultCode.FAIL);
            }
        } catch (TException e) {
            log.error("消息中心绑定设备token失败, error:{}", e.getMessage(), e);
            throw new CommonRuntimeException("消息中心绑定设备token失败");
        }
    }

    private CommonResponse<Void> saveMessageCenterPushTokenFallBack(UserPushConfigVO pushConfig) {
        throw new CommonRuntimeException("消息中心绑定token绑定失败");
    }

    @Degrade(rhinoKey = "UserPushConfigWrapper.unbindUserDevice",
            fallBackMethod = "unbindUserDeviceFallBack",
            timeoutInMilliseconds = 1400,
            ignoreExceptions = {CommonLogicException.class})
    @MethodLog(logRequest = true, logResponse = true)
    @CommonMonitorTransaction
    public CommonResponse<Void> unbindUserDevice(String authId, String deviceId) {
        try {


            UnbindDeviceUserRequest unbindDeviceUserRequest = new UnbindDeviceUserRequest();
            unbindDeviceUserRequest.setAppCode(authId);
            unbindDeviceUserRequest.setDeviceId(deviceId);
            MessageCommonResponse thriftResponse = pushThriftService.unbindDeviceUser(unbindDeviceUserRequest);
            if (thriftResponse.getCode() == 0) {
                return CommonResponse.success(null);
            } else {
                log.info("消息中心解绑用户device失败: {}", thriftResponse.getMsg());
                return CommonResponse.fail(ResultCode.FAIL);
            }
        } catch (TException e) {
            log.error("消息中心解绑用户device失败, error:{}", e.getMessage(), e);
            throw new CommonRuntimeException("消息中心解绑用户device失败");
        }
    }

    private CommonResponse<Void> unbindUserDeviceFallBack(String authId, String deviceId) {
        throw new CommonRuntimeException("消息中心解绑用户device失败");
    }

    @Degrade(rhinoKey = "UserPushConfigWrapper.bindUserDevice",
            fallBackMethod = "bindUserDeviceFallBack",
            timeoutInMilliseconds = 1400,
            ignoreExceptions = {CommonLogicException.class})
    @MethodLog(logRequest = true, logResponse = true)
    @CommonMonitorTransaction
    public CommonResponse<Void> bindUserDevice(String authId, String deviceId, com.sankuai.meituan.reco.shopmgmt.pieapi.utils.login.User user) {
        try {
            BindDeviceUserRequest request = new BindDeviceUserRequest();
            request.setAppCode(authId);
            request.setDeviceId(deviceId);
            request.setTenantId(user.getTenantId());
            request.setAccountId(user.getAccountId());
            MessageCommonResponse thriftResponse = pushThriftService.bindDeviceUser(request);
            if (thriftResponse.getCode() == 0) {
                return CommonResponse.success(null);
            } else {
                log.info("消息中心绑定用户device失败: {}", thriftResponse.getMsg());
                return CommonResponse.fail(ResultCode.FAIL);
            }
        } catch (TException e) {
            log.error("消息中心绑定用户device失败, error:{}", e.getMessage(), e);
            throw new CommonRuntimeException("消息中心绑定用户device失败");
        }
    }

    private CommonResponse<Void> bindUserDeviceFallBack(String authId, String deviceId, User user) {
        throw new CommonRuntimeException("消息中心绑定用户device失败");
    }

    @Degrade(rhinoKey = "UserPushConfigWrapper.pushReceived",
            fallBackMethod = "pushReceivedFallBack",
            timeoutInMilliseconds = 1400,
            ignoreExceptions = {CommonLogicException.class})
    @MethodLog(logRequest = true, logResponse = true)
    @CommonMonitorTransaction
    public CommonResponse<Void> pushReceived(String deviceId, String platMsgId, String pushType, Long eventId, Integer subTaskId, Integer resultCode){
        try {
            PushReceivedRequest request = new PushReceivedRequest();
            request.setAppCode(String.valueOf(AppAuthIdConstants.SHU_GUO_PAI.val()));
            request.setDeviceId(deviceId);
            request.setPlatMsgId(platMsgId);
            request.setPushType(EnumUtils.isValidEnum(PushTypeEnum.class, pushType) ?  pushType : PushTypeEnum.NOTIFICATION.name());
            request.setEventId(eventId);
            request.setSubTaskId(subTaskId);
            request.setResultCode(resultCode);
            MessageCommonResponse response = pushThriftService.pushReceived(request);
            if (response.getCode() == 0) {
                return CommonResponse.success(null);
            } else {
                log.info("消息中心处理接收消息失败: {}", response.getMsg());
                return CommonResponse.fail(ResultCode.FAIL);
            }
        } catch (TException e) {
            log.error("消息中心处理接收消息失败, error:{}", e.getMessage(), e);
            throw new CommonRuntimeException("消息中心处理接收消息失败");
        }
    }

    private CommonResponse<Void> pushReceivedFallBack(String deviceId, String platMsgId, String pushType, Long eventId, Integer subTaskId, Integer resultCode){
        throw new CommonRuntimeException("消息中心处理接收消息失败");
    }



    private PushOsTypeEnum platformType2OsType(Integer platformType) {
        PlatformType platType = PlatformType.findByValue(platformType);
        switch (platType) {
            case IOS:
                return PushOsTypeEnum.IOS;
            case ANDROID:
                return PushOsTypeEnum.ANDROID;
            default:
                throw new CommonRuntimeException("push不支持的系统");
        }
    }
}
