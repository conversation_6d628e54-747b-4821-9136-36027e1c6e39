package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.store;

import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.StoreDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Title: StoreVO
 * @Description: 门店信息
 * @Author: zhaolei12
 * @Date: 2020/5/16 12:07 下午
 */
@TypeDoc(
        description = "门店信息"
)
@Data
@ApiModel("门店信息")
public class StoreVO {

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID", required = true)
    private Long storeId;

    @FieldDoc(
            description = "门店名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店名称", required = true)
    private String storeName;

    public static StoreVO ofDTO(StoreDTO storeDTO) {
        if (storeDTO == null) {
            return null;
        }
        StoreVO storeVO = new StoreVO();
        storeVO.setStoreId(storeDTO.getStoreId());
        storeVO.setStoreName(storeDTO.getName());
        return storeVO;
    }

    public static StoreVO ofDTO(com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreDTO storeDTO) {
        if (storeDTO == null) {
            return null;
        }
        StoreVO storeVO = new StoreVO();
        storeVO.setStoreId(storeDTO.getStoreId());
        storeVO.setStoreName(storeDTO.getName());
        return storeVO;
    }

    public static StoreDTO toDTO(StoreVO storeVO) {
        if (storeVO == null) {
            return null;
        }
        StoreDTO storeDTO = new StoreDTO();
        storeDTO.setStoreId(storeVO.getStoreId());
        storeDTO.setName(storeVO.getStoreName());
        return storeDTO;
    }

    public static StoreDTO toDTO(Long storeId) {
        if (storeId == null) {
            return null;
        }
        StoreDTO storeDTO = new StoreDTO();
        storeDTO.setStoreId(storeId);
        return storeDTO;
    }

    public static StoreVO ofDTO(PoiInfoDto poiInfoDto) {
        if (poiInfoDto == null) {
            return null;
        }
        StoreVO storeVO = new StoreVO();
        storeVO.setStoreId(poiInfoDto.getPoiId());
        storeVO.setStoreName(poiInfoDto.getPoiName());
        return storeVO;
    }

    public static List<StoreVO> ofDTO(List<PoiInfoDto> poiInfoDtos) {
        if (CollectionUtils.isEmpty(poiInfoDtos)) {
            return Lists.newArrayList();
        }
        return poiInfoDtos.stream().map(StoreVO::ofDTO).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreDTO toBizDTO(Long storeId) {
        if (storeId == null) {
            return null;
        }
        com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreDTO storeDTO =
                new com.sankuai.meituan.shangou.empower.productbiz.client.dto.StoreDTO();
        storeDTO.setStoreId(storeId);
        return storeDTO;
    }
}
