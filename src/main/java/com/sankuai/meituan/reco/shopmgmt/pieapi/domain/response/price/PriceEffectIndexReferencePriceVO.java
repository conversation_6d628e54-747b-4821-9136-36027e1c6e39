package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.shangou.empower.price.client.dto.price_effect.PriceEffectIndexReferencePriceDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

@TypeDoc(
        description = "价格效果指标参考价",
        authors = "hejunliang"
)
@ApiModel("价格效果指标参考价")
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@Data
public class PriceEffectIndexReferencePriceVO {

    @FieldDoc(
            description = "价格评估指标类型"
    )
    @ApiModelProperty("价格评估指标类型")
    private String priceEffectIndexType;

    @FieldDoc(
            description = "参考价列表"
    )
    @ApiModelProperty("参考价列表")
    private List<ReferencePriceDataVO> referencePriceVOList;

    public static PriceEffectIndexReferencePriceVO build(PriceEffectIndexReferencePriceDTO dto) {

        if (dto == null) {
            return null;
        }

        PriceEffectIndexReferencePriceVO priceEffectIndexReferencePriceVO = new PriceEffectIndexReferencePriceVO();
        priceEffectIndexReferencePriceVO.setPriceEffectIndexType(dto.getPriceEffectIndex().getIndexKey());
        priceEffectIndexReferencePriceVO.setReferencePriceVOList(ConverterUtils.convertList(dto.getReferencePriceDTOList(), ReferencePriceDataVO::build));

        return priceEffectIndexReferencePriceVO;
    }
}
