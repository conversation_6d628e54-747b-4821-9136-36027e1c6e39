package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/6/12
 * desc:
 */
@TypeDoc(
        description = "商品上下架结果"
)
@Data
@ApiModel("商品上下架结果")
public class SkuChannelStatusChangeResultVO {

    @FieldDoc(
            description = "渠道ID   -1-线下 100-美团 200-饿了么 300-京东", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID   -1-线下 100-美团 200-饿了么 300-京东", required = true)
    private Integer channelId;

    @FieldDoc(
            description = "商品当前状态 -1-未上线，1-已上架，2-已下架", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品当前状态", required = true)
    private Integer skuChannelStatus;

    @FieldDoc(
            description = "是否操作成功 0-否，1-是", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "是否操作成功", required = true)
    private Integer success;

    @FieldDoc(
            description = "操作失败原因 success为0时有值", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "操作失败原因", required = true)
    private String failReason;

    @FieldDoc(
            description = "操作结果码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "操作结果码", required = true)
    private Integer code;
}

