package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.delivery;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/04/09 09:52
 */
@TypeDoc(
        description = "聚合运力配送配置信息"
)
@Data
@ApiModel("聚合运力配送配置信息")
@NoArgsConstructor
@AllArgsConstructor
public class AggDeliveryPlatformConfigVo {


    @FieldDoc(
            description = "平台code", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "平台code", required = true)
    private Integer platformCode;

    @FieldDoc(
            description = "提示文案", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "提示文案", required = true)
    private String title;

    @FieldDoc(
            description = "平台是否开启", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "平台是否开启", required = true)
    private Integer status;

    @FieldDoc(
            description = "跳转文案", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "跳转文案", required = true)
    private String redirectTitle;

    @FieldDoc(
            description = "跳转url", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "跳转url", required = true)
    private String redirectUrl;

    @FieldDoc(
            description = "渠道类型", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "渠道类型")
    private Integer channelType;
}
