package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.CartonMeasureConvertFactorVO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.SkuCartonMeasureDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/7/11
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CartonMeasureVO {
    /**
     * skuId
     */
    private String skuId;
    /**
     * 箱规列表
     */
    private List<CartonMeasureConvertFactorVO> cartonMeasureConvertFactorList;

    public static List<CartonMeasureVO> toCartonMeasureVOList(List<SkuCartonMeasureDTO> skuCartonMeasureDTOList){
        if (CollectionUtils.isEmpty(skuCartonMeasureDTOList)) {
            return Collections.emptyList();
        }

        return skuCartonMeasureDTOList.stream().map(sku ->
                new CartonMeasureVO(
                        sku.getSkuId(),
                        sku.getCartonMeasureList().stream().map(cm ->
                                        new CartonMeasureConvertFactorVO(cm.getCartonMeasureCode(), cm.getCartonMeasureName(), cm.getBasicUnitConvertFactor())
                                ).collect(Collectors.toList())
        )).collect(Collectors.toList());
    }
}
