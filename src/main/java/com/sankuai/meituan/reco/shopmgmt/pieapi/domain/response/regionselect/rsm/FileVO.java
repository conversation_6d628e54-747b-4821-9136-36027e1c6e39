package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.regionselect.rsm;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@TypeDoc(
        description = "文件信息"
)
@NoArgsConstructor
@AllArgsConstructor
public class FileVO {

    @FieldDoc(
            description = "资源"
    )
    private String src;

    @FieldDoc(
            description = "链接"
    )
    private String url;
}