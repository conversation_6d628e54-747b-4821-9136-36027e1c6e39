package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

@TypeDoc(
        description = "待领取任务数据"
)
@Data
@ApiModel("待领取任务数据")
public class WarehousePickTaskModuleVO {

    @FieldDoc(
            description = "订单号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "订单号")
    private String orderId;

    @FieldDoc(
            description = "任务号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "任务号")
    private Integer taskNo;

    @FieldDoc(
            description = "应拣sku数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "sku数量")
    private Integer skuNum;

    @FieldDoc(
            description = "实拣sku数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "实拣sku数量")
    private Integer actSkuNum;

    @FieldDoc(
            description = "应拣商品数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "商品数量")
    private Integer itemNum;

    @FieldDoc(
            description = "实拣商品数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "实拣商品数量")
    private Integer actItemNum;

    @FieldDoc(
            description = "关联容器", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "关联容器")
    private List<WarehouseContainerBoxModuleVO> containerBoxList;
}
