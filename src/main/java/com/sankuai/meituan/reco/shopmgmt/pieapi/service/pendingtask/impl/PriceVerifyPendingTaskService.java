/*
 * Copyright (c) 2019 Meituan.com. All Rights Reserved.
 */
package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.impl;

import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ProjectConstants;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.CommonResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.quote.QueryQuoteReviewingCountResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.AbstractSinglePendingTaskService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.OCMSServiceWrapper;
import com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.SaasPriceServiceWrapper;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 报价任务量
 * <br><br>
 * Author: linjianyu <br>
 * Date: 2019-07-10 Time: 15:21
 */
@Slf4j
@Service
public class PriceVerifyPendingTaskService extends AbstractSinglePendingTaskService {

    @Resource
    private OCMSServiceWrapper ocmsServiceWrapper;
    @Resource
    private SaasPriceServiceWrapper saasPriceServiceWrapper;

    @Override
    protected PendingTaskResult getPendingTaskCount(PendingTaskParam param) {
        CommonResponse<QueryQuoteReviewingCountResponse> resp = null;
        if (MccConfigUtil.isPriceMigrateGraySwitch(ProjectConstants.OFFLINE_PRICE_MIGRATE_SWITCH, param.getTenantId(), null)) {
            log.info("走价格服务获取待审核数");
            resp = saasPriceServiceWrapper.queryQuoteReviewingCountList(param.getStoreIds(), param.getUser());
        } else {
            log.info("走中台服务获取待审核数");
            resp = ocmsServiceWrapper.queryQuoteReviewingCountList(param.getStoreIds(), param.getUser());
        }

        if (resp == null || resp.getData() == null) {
            return null;
        }
        return PendingTaskResult.createNumberMarker(resp.getData().getCount());
    }

    @Override
    protected AuthCodeEnum module() {
        return AuthCodeEnum.PRICE_CHECK;
    }
}
