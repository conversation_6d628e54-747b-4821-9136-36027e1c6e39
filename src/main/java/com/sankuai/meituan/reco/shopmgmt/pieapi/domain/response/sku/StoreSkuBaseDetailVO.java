package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.StoreSkuKey;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.StoreSkuBaseDetailDTO;
import lombok.Data;

import java.util.List;

/**
 * 门店商品基本信息
 *
 * <AUTHOR>
 */
@Data
public class StoreSkuBaseDetailVO {

    /**
     * 门店编码
     */
    private Long storeId;

    /**
     * 门店编码
     */
    private String storeName;

    /**
     * 商品编码
     */
    private String spuId;

    /**
     * 商品编码
     */
    private String skuId; 
    
    /**
     * 商品名称 线上商品名称>线下商品名称
     */
    private String skuName;

    /**
     * 总部商品编码
     */
    private String tenantSkuId;
    
    /**
     * 规格 线上规格>线下规格
     */
    private String spec; 
    
    /**
     * 门店售价 单位：元
     */
    private Double storePrice; 
    
    /**
     * 单位 线上售卖单位>基本单位
     */
    private String unit;

    /**
     * 商品类型 1-商品 2-自动加工商品 3-组合商品 4-原料
     */
    private Integer skuType;

    /**
     * 称重类型 1-称重计量 2-称重计件 3-非称重
     */
    private Integer weightType;

    /**
     * 重量 线上重量>净重>毛重
     */
    private Integer weight;

    /**
     * UPC编码
     */
    private List<String> upcList;

    /**
     * 商品图片
     */
    private List<String> imageList;

    /**
     * 商家品牌编码
     */
    private String brandCode;

    /**
     * 商家品牌名称
     */
    private String brandName;

    /**
     * 品牌编码全路径
     */
    private String brandCodePath;

    /**
     * 品牌名称全路径
     */
    private String brandNamePath;

    /**
     * 后台类目编码
     */
    private String categoryCode;

    /**
     * 后台类目名称
     */
    private String categoryName;

    /**
     * 后台类目编码全路径
     */
    private String categoryCodePath;

    /**
     * 后台类目名称全路径
     */
    private String categoryNamePath;

    /**
     * 一级后台分类编码
     */
    private String firstCategoryCode;

    /**
     * 一级后台分类名称
     */
    private String firstCategoryName;

    /**
     * 是否可用 1-是 2-否
     */
    private Integer available;

    /**
     * 是否可售 1-是 2-否
     */
    private Integer allowSale;

    public static StoreSkuBaseDetailVO build(StoreSkuBaseDetailDTO dto) {
        StoreSkuBaseDetailVO storeSkuBaseDetailVO = new StoreSkuBaseDetailVO();
        storeSkuBaseDetailVO.setStoreId(dto.getStoreId());
        storeSkuBaseDetailVO.setStoreName(dto.getStoreName());
        storeSkuBaseDetailVO.setSkuId(dto.getSkuId());
        storeSkuBaseDetailVO.setSpuId(dto.getSpuId());
        storeSkuBaseDetailVO.setSkuName(dto.getSkuName());
        storeSkuBaseDetailVO.setTenantSkuId(dto.getSpuId());
        storeSkuBaseDetailVO.setSpec(dto.getSpec());
        storeSkuBaseDetailVO.setStorePrice(dto.getStorePrice());
        storeSkuBaseDetailVO.setUnit(dto.getUnit());
        storeSkuBaseDetailVO.setSkuType(dto.getSkuType());
        storeSkuBaseDetailVO.setWeightType(dto.getWeightType());
        storeSkuBaseDetailVO.setWeight(dto.getWeight());
        storeSkuBaseDetailVO.setUpcList(dto.getUpcList());
        storeSkuBaseDetailVO.setImageList(dto.getImageList());
        storeSkuBaseDetailVO.setBrandCode(dto.getBrandCode());
        storeSkuBaseDetailVO.setBrandName(dto.getBrandName());
        storeSkuBaseDetailVO.setBrandCodePath(dto.getBrandCodePath());
        storeSkuBaseDetailVO.setBrandNamePath(dto.getBrandNamePath());
        storeSkuBaseDetailVO.setCategoryCode(dto.getCategoryCode());
        storeSkuBaseDetailVO.setCategoryName(dto.getCategoryName());
        storeSkuBaseDetailVO.setCategoryCodePath(dto.getCategoryCodePath());
        storeSkuBaseDetailVO.setCategoryNamePath(dto.getCategoryNamePath());
        storeSkuBaseDetailVO.setFirstCategoryCode(dto.getFirstCategoryCode());
        storeSkuBaseDetailVO.setFirstCategoryName(dto.getFirstCategoryName());
        storeSkuBaseDetailVO.setAvailable(dto.getAvailable());
        storeSkuBaseDetailVO.setAllowSale(dto.getAllowSale());

        return storeSkuBaseDetailVO;
    }

    public  StoreSkuKey genStoreSkuKey() {
        return StoreSkuKey.builder().storeId(storeId).skuId(skuId).build();
    }
}
