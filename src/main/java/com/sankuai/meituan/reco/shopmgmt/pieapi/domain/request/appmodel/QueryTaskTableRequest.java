package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.appmodel;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * QueryTaskTableRequest
 *
 * <AUTHOR>
 * @since 5/23/23
 */
@TypeDoc(
        description = "查询app模块菜单信息请求"
)
@Data
@ApiModel("查询app模块菜单信息请求")
public class QueryTaskTableRequest {
    @FieldDoc(
            description = "菜单code", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "菜单code")
    @NotNull(message = "菜单code不能为空")
    private String menuCode;

    @FieldDoc(
            description = "分页信息，从1开始", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "分页信息，从1开始")
    @NotNull(message = "分页信息不能为空")
    private Integer page;


    @FieldDoc(
            description = "分页大小信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "分页大小信息")
    @NotNull(message = "分页大小信息不能为空")
    private Integer pageSize;
}
