package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.RoleInfoVo;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-12-15
 * @email <EMAIL>
 */
@TypeDoc(
        description = "角色vo"
)
@Data
@ApiModel("角色vo")
@AllArgsConstructor
@NoArgsConstructor
public class RoleVO {

    @FieldDoc(
            description = "id"
    )
    private Long roleId;

    @FieldDoc(
            description = "名称"
    )
    private String roleName;
}
