package com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.impl;

import com.google.common.collect.Lists;
import com.meituan.shangou.saas.tenant.thrift.PoiThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiMapResponse;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskResult;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AuthCodeEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.service.pendingtask.AbstractSinglePendingTaskService;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ResponseHandler;
import com.sankuai.meituan.reco.store.management.enums.EntityTypeEnum;
import com.sankuai.meituan.reco.store.management.thrift.outbound.OutboundOrderThriftService;
import com.sankuai.meituan.reco.store.management.thrift.outbound.req.WaitOutboundCountRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/12/14
 */
@Service
public class OutboundPendingTaskService extends AbstractSinglePendingTaskService {
    @Resource
    private OutboundOrderThriftService outboundOrderThriftService;
    @Autowired
    private PoiThriftService poiThriftService;

    @Override
    protected PendingTaskResult getPendingTaskCount(PendingTaskParam param) throws Exception {
        WaitOutboundCountRequest waitOutboundCountRequest = new WaitOutboundCountRequest(param.getTenantId(), param.getEntityId(), param.getEntityType());
        int orderCount = (int) outboundOrderThriftService.queryOutboundOrderCount(waitOutboundCountRequest);

        // 针对orderCount做特殊处理：如果是托管仓，展示数据为0
        PoiMapResponse poiMapResponse = poiThriftService.queryPoiInfoMapByPoiIds(Lists.newArrayList(param.getEntityId()));
        ResponseHandler.checkResponseAndStatus(poiMapResponse, res->res.getStatus().getCode(), res->res.getStatus().getMessage());
        Map<Long, PoiInfoDto> poiInfoMap = poiMapResponse.getPoiInfoMap();
        PoiInfoDto poiInfoDto = poiInfoMap.get(param.getEntityId());
        if (EntityTypeEnum.AREA_CENTRAL_WAREHOUSE.equals(EntityTypeEnum.getEnumByValue(poiInfoDto.getEntityType())) && Boolean.TRUE.equals(poiInfoDto.getOutRepository())){
            orderCount = 0;
        }

        return PendingTaskResult.createNumberMarker(orderCount);
    }

    @Override
    protected AuthCodeEnum module() {return AuthCodeEnum.OUTBOUND_ORDER_APP;}
}
