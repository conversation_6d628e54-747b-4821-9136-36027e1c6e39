package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @Author: wangyihao04
 * @Date: 2020-06-11 17:12
 * @Mail: <EMAIL>
 */
@Data
@TypeDoc(
        description = "商品价格扩展值对象"
)
@ApiModel("商品价格扩展值对象")
public class SkuPriceExtendVO extends PriceExtendVO{
    @FieldDoc(
            description = "商品图片"
    )
    @ApiModelProperty(name = "商品图片", required = true)
    private List<String> images;
    @FieldDoc(
            description = "规格"
    )
    @ApiModelProperty(name = "规格", required = true)
    private String spec;
    @FieldDoc(
            description = "重量"
    )
    @ApiModelProperty(name = "重量", required = true)
    private Integer weight;
    @FieldDoc(
            description = "月销量"
    )
    @ApiModelProperty(name = "月销量")
    private Integer monthSaleAmount;
    @FieldDoc(
            description = "进货价"
    )
    @ApiModelProperty(name = "进货价")
    private Double offlinePrice;
    @FieldDoc(
            description = "进货价/500g"
    )
    @ApiModelProperty(name = "进货价/500g")
    private Double offlinePriceOf500g;
    @FieldDoc(
            description = "零售价"
    )
    @ApiModelProperty(name = "零售价")
    private Double onlinePrice;
    @FieldDoc(
            description = "零售价/500g"
    )
    @ApiModelProperty(name = "零售价/500g")
    private Double onlinePriceOf500g;
    @FieldDoc(
            description = "市调价"
    )
    @ApiModelProperty(name = "市调价")
    private Double surveyPrice;
    @FieldDoc(
            description = "市调价"
    )
    @ApiModelProperty(name = "市调价/500g")
    private Double surveyPriceOf500g;
    @FieldDoc(
            description = "城市基准价"
    )
    @ApiModelProperty(name = "城市基准价/500g")
    private Double cityBasePrice;
    @FieldDoc(
            description = "城市基准价"
    )
    @ApiModelProperty(name = "城市基准价/500g")
    private Double cityBasePriceOf500g;
    @FieldDoc(
            description = "提价规则"
    )
    @ApiModelProperty(name = "提价规则")
    private AdjustPriceStrategyVO adjustPriceStrategy;
    @FieldDoc(
            description = "spu"
    )
    @ApiModelProperty(name = "spu")
    private String spuId;

    @FieldDoc(
            description = "售卖单位"
    )
    @ApiModelProperty(name = "售卖单位")
    private String saleUnit;

    @FieldDoc(
            description = "称重类型"
    )
    @ApiModelProperty(name = "称重类型")
    private Integer weightType;

    @FieldDoc(
            description = "待审核价"
    )
    @ApiModelProperty(name = "待审核价")
    private Double reviewPrice;
    @FieldDoc(
            description = "待审核价/500g"
    )
    @ApiModelProperty(name = "待审核价/500g")
    private Double reviewPriceOf500g;


}
