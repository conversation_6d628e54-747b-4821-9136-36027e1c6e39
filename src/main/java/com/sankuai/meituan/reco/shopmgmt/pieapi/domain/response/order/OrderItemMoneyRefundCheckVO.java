package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderItemMoneyRefundCheckModel;
import lombok.Data;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/3/7 14:28
 */
@TypeDoc(
        description = "金额退页面检查的商品信息"
)
@Data
public class OrderItemMoneyRefundCheckVO {
    @FieldDoc(
            description = "商品内部skuId"
    )
    private String innerSkuId;

    @FieldDoc(
            description = "商家在渠道的skuId"
    )
    private String customerSkuId;

    @FieldDoc(
            description = "商品名称"
    )
    private String skuName;

    @FieldDoc(
            description = "商品图片URL"
    )
    private String picUrl;

    @FieldDoc(
            description = "规格"
    )
    private String specification;

    @FieldDoc(
            description = "商品数量"
    )
    private Integer skuCount;

    @FieldDoc(
            description = "可退金额，分"
    )
    private Integer canRefundMoney;

    @FieldDoc(
            description = "实付价格"
    )
    private Integer currentPrice;

    @FieldDoc(
            description = "订单商品行id"
    )
    private String orderItemId;

    @FieldDoc(
            description = "商品spu"
    )
    private String spu;

    @FieldDoc(
            description = "erp商品编码"
    )
    private String erpItemCode;

    @FieldDoc(
            description = "商品条码"
    )
    private String upc;

    public static OrderItemMoneyRefundCheckVO buildOrderItemMoneyRefundCheckVO(BizOrderItemMoneyRefundCheckModel moneyRefundCheckModel) {
        OrderItemMoneyRefundCheckVO moneyRefundCheckVO = new OrderItemMoneyRefundCheckVO();
        moneyRefundCheckVO.setCanRefundMoney(moneyRefundCheckModel.getCanRefundMoney());
        moneyRefundCheckVO.setCurrentPrice(moneyRefundCheckModel.getCurrentPrice());
        moneyRefundCheckVO.setInnerSkuId(moneyRefundCheckModel.getInnerSkuId());
        moneyRefundCheckVO.setPicUrl(moneyRefundCheckModel.getPicUrl());
        moneyRefundCheckVO.setSkuCount(moneyRefundCheckModel.getSkuCount());
        moneyRefundCheckVO.setSkuName(moneyRefundCheckModel.getSkuName());
        moneyRefundCheckVO.setCustomerSkuId(moneyRefundCheckModel.getCustomerSkuId());
        moneyRefundCheckVO.setSpecification(moneyRefundCheckModel.getSpecification());
        moneyRefundCheckVO.setOrderItemId(moneyRefundCheckModel.getOrderItemId() != null ? moneyRefundCheckModel.getOrderItemId().toString() : null);
        moneyRefundCheckVO.setSpu(moneyRefundCheckModel.getSpu());
        moneyRefundCheckVO.setErpItemCode(moneyRefundCheckModel.getErpItemCode());
        moneyRefundCheckVO.setUpc(moneyRefundCheckModel.getBarCode());
        return moneyRefundCheckVO;
    }
}
