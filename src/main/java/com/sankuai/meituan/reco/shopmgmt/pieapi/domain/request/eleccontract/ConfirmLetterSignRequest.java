package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.eleccontract;

import com.meituan.servicecatalog.api.annotations.FieldDoc;

import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 确认函签署请求
 *
 * <AUTHOR>
 * @since 2021/12/7
 */
@ToString
@EqualsAndHashCode
public class ConfirmLetterSignRequest {

    @FieldDoc(
            description = "短信验证请求编码"
    )
    private String requestCode;


    @FieldDoc(
            description = "短信响应码"
    )
    private String responseCode;

    @FieldDoc(
            description = "确认函ID"
    )
    private Long confirmLetterId;

    public String getRequestCode() {
        return requestCode;
    }

    public void setRequestCode(String requestCode) {
        this.requestCode = requestCode;
    }

    public String getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(String responseCode) {
        this.responseCode = responseCode;
    }

    public Long getConfirmLetterId() {
        return confirmLetterId;
    }

    public void setConfirmLetterId(Long confirmLetterId) {
        this.confirmLetterId = confirmLetterId;
    }
}
