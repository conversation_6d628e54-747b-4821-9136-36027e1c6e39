package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.contract;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@TypeDoc(description = "APP端合同提醒确认")
@ApiModel("APP端合同提醒确认")
@Data
public class RemindConfirmRequest {

    @FieldDoc(
            description = "确认类型", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "确认类型", required = true)
    @NotNull(message = "确认类型不能为空")
    private Integer confirmType;
}
