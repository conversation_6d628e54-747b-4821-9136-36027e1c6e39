package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.regionselect.rsm;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * Email <EMAIL>
 * Date 2022/12/7 12:10 下午
 * Description
 */
@TypeDoc(
        description = "重点圆形区域",
        authors = {
                "wangqiang103"
        }
)
@ApiModel("重点圆形区域")
@Data
public class FocusCircleVO {
    @FieldDoc(
            description = "中心经纬度"
    )
    private String coordinate;

    @FieldDoc(
            description = "半径"
    )
    private String radius;

    @FieldDoc(
            description = "详细地址"
    )
    private String address;

    public static final String DELIMITER = ";";

    public static Optional<List<FocusCircleVO>> build(String focusCircleStr) {
        if (StringUtils.isBlank(focusCircleStr)) {
            return Optional.empty();
        }

        List<FocusCircleVO> voList = new ArrayList<>();

        for(String focusCircle: focusCircleStr.split(DELIMITER)){
            String[] elements = focusCircle.split(",");
            if (Objects.isNull(elements) || elements.length != 4) {
                return Optional.empty();
            }
            FocusCircleVO vo = new FocusCircleVO();
            vo.setCoordinate(String.format("%s,%s", elements[0], elements[1]));
            vo.setRadius(elements[2]);
            vo.setAddress(elements[3]);
            voList.add(vo);
        }


        return Optional.of(voList);
    }

    public String flatToString() {
        StringBuilder stringBuilder = new StringBuilder();
        return stringBuilder.append(coordinate).append(",")
                .append(radius).append(",")
                .append(address).toString();
    }

}

