package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pullnew;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 4/21/23
 */
@TypeDoc(
        description = "根据单号查询地推关联事件请求"
)
@Data
@ApiModel("根据单号查询地推关联事件请求")
public class QueryPullNewByOrderRequest {

    @FieldDoc(
            description = "单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "单号", required = true)
    @NotBlank(message = "搜索单号不能为空")
    private String orderId;


}
