package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.delivery.third;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.GiftVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.ProductVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.RevenueDetailVo;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.order.TagInfoVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/8/28 15:30
 **/
@Data
public class ThirdDeliveryPickOrderVO {
    @FieldDoc(
            description = "拣货单ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "拣货单ID", required = true)
    private Long pickOrderId;

    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "租户ID", required = true)
    private Long tenantId;

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道ID", required = true)
    private Integer channelId;

    @FieldDoc(
            description = "渠道名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道名称", required = true)
    private String channelName;

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店ID", required = true)
    private Long storeId;

    @FieldDoc(
            description = "门店名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "门店名称", required = true)
    private String storeName;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道订单号", required = true)
    private String channelOrderId;

    @FieldDoc(
            description = "订单流水", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单流水", required = true)
    private Long serialNo;

    @FieldDoc(
            description = "收货人姓名", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "收货人姓名", required = true)
    private String receiverName;

    @FieldDoc(
            description = "收货人电话号码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "收货人电话号码", required = true)
    private String receiverPhone;

    @FieldDoc(
            description = "收货人地址", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "收货人地址", required = true)
    private String receiverAddress;

    @FieldDoc(
            description = "收货人定位经度，精确到小数点后六位", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "收货人定位经度", required = true)
    private String receiverLongitude;

    @FieldDoc(
            description = "收货人定位纬度，精确到小数点后六位", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "收货人定位纬度", required = true)
    private String receiverLatitude;

    // 收货人信息 end

    // 商品信息 start

    @FieldDoc(
            description = "商品总数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "商品总数量", required = true)
    private Integer itemCount;

    @FieldDoc(
            description = "备注", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "备注", required = false)
    private String comments;

    @FieldDoc(
            description = "商品信息列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "商品信息列表", required = true)
    private List<ProductVO> productList;

    @FieldDoc(
            description = "订单营收数据", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "订单营收数据")
    private RevenueDetailVo revenueDetail;

    @FieldDoc(
            description = "提报价模式,订单线下价格总和", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "提报价模式,订单线下价格总和", required = false)
    private Integer totalOfflinePrice;

    @FieldDoc(
            description = "赠品信息列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "赠品信息列表", required = false)
    private List<GiftVO> giftVOList;

    @FieldDoc(
            description = "赠品总数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "赠品总数量", required = false)
    private Integer giftCount;

    // 商品信息 end

    @FieldDoc(
            description = "支付时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "支付时间", required = false)
    private Long payTime;


    @FieldDoc(
            description = "用户标签信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "用户标签信息")
    private List<TagInfoVO> userTags;

    @FieldDoc(
            description = "用户类型信息 10-普通用户, 15-会员用户", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "用户类型信息 10-普通用户, 15-会员用户")
    private Integer orderUserType;

    @FieldDoc(
            description = "订单创建时间戳", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单创建时间戳", required = true)
    private Long createTime;

    @FieldDoc(
            description = "预计送达时间开始时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "预计送达时间开始时间", required = true)
    private Long estimateArriveTimeStart;

    @FieldDoc(
            description = "预计送达时间截止时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "预计送达时间截止时间", required = true)
    private Long estimateArriveTimeEnd;

    @FieldDoc(
            description = "考核送达截止时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "考核送达截止时间")
    private Long evaluateArriveDeadline;

    @FieldDoc(
            description = "考核送达剩余时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "考核送达剩余时间")
    private Long evaluateArriveLeftTime;

    @FieldDoc(
            description = "考核送达超时时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "考核送达超时时间")
    private Long evaluateArriveTimeout;

    @FieldDoc(
            description = "配送距离，单位米", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "配送距离", required = false)
    private Long deliveryDistance;

}
