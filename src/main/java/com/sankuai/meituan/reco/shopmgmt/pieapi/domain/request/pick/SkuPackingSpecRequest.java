package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pick;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@TypeDoc(
        description = "拣货箱规单位"
)
@ApiModel("拣货箱规单位")
@Data
public class SkuPackingSpecRequest {

    @FieldDoc(
            description = "箱规单位名称"
    )
    private String packingSpecUnit;

    @FieldDoc(
            description = "箱规单位与基本单位转换比例"
    )
    private String packingSpecRatio;

    @FieldDoc(
            description = "基本单位"
    )
    private String basicUnit = StringUtils.EMPTY;

}
