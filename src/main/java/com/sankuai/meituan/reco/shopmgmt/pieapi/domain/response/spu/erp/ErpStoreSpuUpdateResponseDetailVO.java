package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu.erp;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ERP门店商品更新结果明细
 *
 * <AUTHOR>
 * @since 2023/05/24
 */
@TypeDoc(
        description = "ERP门店商品更新结果明细"
)
@Data
@ApiModel("ERP门店商品更新结果明细")
public class ErpStoreSpuUpdateResponseDetailVO {

    @FieldDoc(description = "租户ID", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "租户ID")
    private Long tenantId;

    @FieldDoc(description = "门店ID", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "门店ID")
    private Long storeId;

    @FieldDoc(description = "SPU编码", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "SPU编码")
    public String spuId;

    @FieldDoc(description = "erp编码", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "erp编码")
    public String erpCode;

    @FieldDoc(description = "条码", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "条码")
    public String upc;

    @FieldDoc(description = "skuId", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "skuId")
    public String skuId;

    @FieldDoc(description = "商品名称", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "商品名称")
    public String spuName;

    @FieldDoc(description = "code", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "code")
    private Integer code;

    @FieldDoc(description = "错误信息", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "错误信息")
    public String errorMsg;

}
