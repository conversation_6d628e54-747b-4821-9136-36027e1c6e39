package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.price;

import com.google.common.base.Preconditions;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.ChannelStoreCustomSpuKey;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * 作者：guohuqi
 * 时间：2022/11/3 4:49 PM
 * 功能：
 **/
@ApiModel(
        "商品是否可以改价"
)
@TypeDoc(
        description = "商品是否可以改价",
        authors = "guohuqi"
)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SpuCanModifyPriceRequest {
    @FieldDoc(
            description = "渠道商品列表"
    )
    @ApiModelProperty(value = "渠道商品列表", required = true)
    private List<ChannelStoreCustomSpuKey> storeSpuInfos;


    public void validate() {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(storeSpuInfos), "商品列表不能为空");
        storeSpuInfos.forEach(storeSpuInfo -> {
            Preconditions.checkArgument(StringUtils.isNotBlank(storeSpuInfo.getCustomSpuId()), "商品信息不能为空");
            Preconditions.checkArgument(Objects.nonNull(storeSpuInfo.getStoreId()), "门店信息不能为空");
            Preconditions.checkArgument(Objects.nonNull(storeSpuInfo.getChannelId()), "渠道信息不能为空");
        });
    }
}
