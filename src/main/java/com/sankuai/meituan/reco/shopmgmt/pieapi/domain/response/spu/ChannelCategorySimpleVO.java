package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import java.util.List;
import java.util.Objects;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.spu.vo.SaleAttrVo;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelCategoryDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 渠道类目简要信息
 *
 * <AUTHOR>
 * @since 2024-08-26
 */
@TypeDoc(
        description = "渠道类目简要信息"
)
@Data
@ApiModel("渠道类目简要信息")
public class ChannelCategorySimpleVO {
    @FieldDoc(
            description = "渠道编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道编码")
    private Integer channelId;

    @FieldDoc(
            description = "渠道类目编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道类目编码")
    private String channelCategoryCode;

    @FieldDoc(
            description = "渠道类目名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道类目名称")
    private String channelCategoryName;

    @FieldDoc(
            description = "渠道类目编码全路径", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道类目编码全路径")
    private String channelCategoryCodePath;

    @FieldDoc(
            description = "渠道类目名称全路径", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道类目名称全路径")
    private String channelCategoryNamePath;

    @FieldDoc(
            description = "支持的规格类型 1仅单规格 2单规格多规格", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "支持的规格类型 1仅单规格 2单规格多规格")
    private Integer supportSpecType;

    @FieldDoc(
            description = "京东到家渠道类目属性列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "京东到家渠道类目属性列表")
    private List<SaleAttrVo> jdSaleAttrList;

    @FieldDoc(
            description = "是否校验upc (0:不校验,1:校验，注意，只有三级分类该属性才有效)", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否校验upc (0:不校验,1:校验，注意，只有三级分类该属性才有效")
    private Integer checkUpcStatus;

    @FieldDoc(
            description = "该类目的商品条形码（UPC）是否必填，0-必填，1-选填 (只有三级分类该属性才有效)", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "该类目的商品条形码（UPC）是否必填，0-必填，1-选填 (只有三级分类该属性才有效)")
    private Integer upcRequired;

    @FieldDoc(
            description = "类目来源，1-零售，2-医药", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "类目来源，1-零售，2-医药")
    private Integer resourceType;

    @FieldDoc(
            description = "医药和零售是否为相同的类目，0-不相同，1-相同", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "医药和零售是否为相同的类目，0-不相同，1-相同")
    private Integer existedInRetailAndMedicine;

    @FieldDoc(
            description = "资质图信息填写要求 0-不需要填写 1-仅展示，不强制校验是否填写 2-必填", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "资质图信息填写要求 0-不需要填写 1-仅展示，不强制校验是否填写 2-必填")
    private Integer medicalDeviceQuaRequirement;

    public static ChannelCategorySimpleVO fromChannelCategoryDTO(ChannelCategoryDTO channelCategoryDTO) {
        if (Objects.isNull(channelCategoryDTO)) {
            return null;
        }
        ChannelCategorySimpleVO vo = new ChannelCategorySimpleVO();
        vo.setChannelId(channelCategoryDTO.getChannelId());
        vo.setChannelCategoryCode(channelCategoryDTO.getChannelCategoryCode());
        vo.setChannelCategoryName(channelCategoryDTO.getChannelCategoryName());
        vo.setChannelCategoryCodePath(channelCategoryDTO.getChannelCategoryCodePath());
        vo.setChannelCategoryNamePath(channelCategoryDTO.getChannelCategoryNamePath());
        vo.setSupportSpecType(channelCategoryDTO.getSupportSpecType());
        vo.setJdSaleAttrList(Fun.map(channelCategoryDTO.getSaleAttrList(), SaleAttrVo::fromDTO));
        vo.setCheckUpcStatus(channelCategoryDTO.getCheckUpcStatus());
        vo.setUpcRequired(channelCategoryDTO.getUpcRequired());
        vo.setResourceType(channelCategoryDTO.getResourceType());
        vo.setExistedInRetailAndMedicine(channelCategoryDTO.getExistedInRetailAndMedicine());
        vo.setMedicalDeviceQuaRequirement(channelCategoryDTO.getMedicalDeviceQuaRequirement());
        return vo;
    }
}
