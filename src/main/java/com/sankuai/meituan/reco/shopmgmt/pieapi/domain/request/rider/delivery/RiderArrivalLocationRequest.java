package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.delivery;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@TypeDoc(
        description = "上报骑手送达时刻位置请求"
)
@Data
public class RiderArrivalLocationRequest {
    @FieldDoc(
            description = "骑手账号id",requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "骑手账号id", required = true)
    @NotNull
    private Long riderAccountId;

    @FieldDoc(
            description = "运单id",requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "运单id", required = true)
    @NotNull
    private Long deliveryOrderId;

    @FieldDoc(
            description = "经度", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "经度")
    private String longitude;

    @FieldDoc(
            description = "纬度", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "纬度")
    private String latitude;
}
