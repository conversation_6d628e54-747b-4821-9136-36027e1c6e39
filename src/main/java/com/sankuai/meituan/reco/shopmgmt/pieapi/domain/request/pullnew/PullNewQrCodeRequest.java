package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.pullnew;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.ParamException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/10/14 16:31
 * Description: 摊位操作请求，包括新建、编辑摊位操作
 */
@TypeDoc(
        description = "展示二维码服务",
        authors = {
                "liyang176"
        },
        version = "V1.0"
)
@Data
@ApiModel("展示二维码服务")
public class PullNewQrCodeRequest {

    @FieldDoc(
            description = "门店ID"
    )
    @ApiModelProperty(value = "门店ID")
    private Long storeId;

    @FieldDoc(
            description = "二维码类型"
    )
    @ApiModelProperty(value = "0-默认的拉新二维码， 1-地推商品的葵花码，2-企微推广码，3-外卖1元单，4-会员推广 5-地推自提")
    private int qrCodeType;


}
