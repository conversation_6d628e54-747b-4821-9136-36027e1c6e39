package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/6/12
 * desc:
 */
@TypeDoc(
        description = "商品上下架响应"
)
@Data
@ApiModel("商品上下架响应")
public class ChangeMultiChannelSkuStatusResponse {

    @FieldDoc(
            description = "操作结果列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "操作结果列表", required = true)
    private List<SkuChannelStatusChangeResultVO> channelStatusChangeResults;
}
