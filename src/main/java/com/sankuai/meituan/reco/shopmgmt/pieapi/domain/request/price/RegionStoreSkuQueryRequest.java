package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.price;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonLogicException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@TypeDoc(
        description = "查询同城门店商品请求"
)
@ApiModel("查询同城门店商品请求")
@Data
public class RegionStoreSkuQueryRequest {

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店id", required = true)
    private Long storeId;

    @FieldDoc(
            description = "spu编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "spu编码", required = true)
    private String spuId;

    @FieldDoc(
            description = "sku编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "sku编码", required = true)
    private String skuId;

    public void validate(){

        if (this.storeId == null) {
            throw new CommonLogicException("门店id不能为空", ResultCode.CHECK_PARAM_ERR);
        }

        if (StringUtils.isEmpty(this.spuId)) {
            throw new CommonLogicException("spu编码不能为空", ResultCode.CHECK_PARAM_ERR);
        }

        if (StringUtils.isEmpty(this.skuId)) {
            throw new CommonLogicException("sku编码不能为空", ResultCode.CHECK_PARAM_ERR);
        }
    }
}
