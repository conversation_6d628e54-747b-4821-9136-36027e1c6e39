package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.labor.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/10/19 17:41
 **/
@ApiModel
@TypeDoc
@Data
public class AttendanceResultVO {
    @FieldDoc(description = "门店id")
    @ApiModelProperty("门店id")
    private Long storeId;

    @FieldDoc(description = "考勤统计id")
    @ApiModelProperty("考勤统计id")
    private Long statisticsId;

    @FieldDoc(description = "班次名称")
    @ApiModelProperty("班次名称")
    private String scheduleName;

    @FieldDoc(description = "上班时间 格式 HH:ss")
    @ApiModelProperty("上班时间 格式 HH:ss")
    private String startWorkTime;

    @FieldDoc(description = "下班时间 格式 HH:ss")
    @ApiModelProperty("下班时间 格式 HH:ss")
    private String endWorkTime;

    @FieldDoc(description = "上班打卡时段-开始时间 格式 HH:ss")
    @ApiModelProperty("上班打卡时段-开始时间 格式 HH:ss")
    private String startWorkDurationBeginTime;

    @FieldDoc(description = "上班打卡时段-结束时间 格式 HH:ss")
    @ApiModelProperty("上班打卡时段-结束时间 格式 HH:ss")
    private String startWorkDurationEndTime;

    @FieldDoc(description = "下班打卡时段-开始时间 格式 HH:ss")
    @ApiModelProperty("下班打卡时段-开始时间 格式 HH:ss")
    private String endWorkDurationBeginTime;

    @FieldDoc(description = "下班打卡时段-结束时间 格式 HH:ss")
    @ApiModelProperty("下班打卡时段-结束时间 格式 HH:ss")
    private String endWorkDurationEndTime;

    @FieldDoc(description = "上班打卡时间 格式 HH:ss")
    @ApiModelProperty("上班打卡时间 格式 HH:ss")
    private String startWorkCheckInTime;

    @FieldDoc(description = "下班打卡时间 格式 HH:ss")
    @ApiModelProperty("下班打卡时间 格式 HH:ss")
    private String endWorkCheckInTime;

    @FieldDoc(description = "上班打卡异常 1-未打卡 2-迟到 3-早退 4-地点异常")
    @ApiModelProperty("上班打卡异常 1-未打卡 2-迟到 3-早退 4-地点异常")
    private List<Integer> startWorkCheckInExceptions;

    @FieldDoc(description = "下班打卡异常 1-未打卡 2-迟到 3-早退 4-地点异常")
    @ApiModelProperty("下班打卡异常 1-未打卡 2-迟到 3-早退 4-地点异常")
    private List<Integer> endWorkCheckInExceptions;

    @FieldDoc(description = "上班打卡异常申诉id")
    @ApiModelProperty("上班打卡异常申诉id")
    private Long startWorkCheckInAppealId;

    @FieldDoc(description = "上班考勤状态 1-正常 2-异常 4-审批中 8-审批通过 16-审批驳回")
    @ApiModelProperty("上班考勤状态 1-正常 2-异常 4-审批中 8-审批通过 16-审批驳回")
    private Integer startWorkAttendanceStatus;

    @FieldDoc(description = "下班打卡异常申诉id")
    @ApiModelProperty("下班打卡异常申诉id")
    private Long endWorkCheckInAppealId;

    @FieldDoc(description = "下班考勤状态 1-正常 2-异常 4-审批中 8-审批通过 16-审批驳回")
    @ApiModelProperty("下班考勤状态 1-正常 2-异常 4-审批中 8-审批通过 16-审批驳回")
    private Integer endWorkAttendanceStatus;

    @FieldDoc(description = "上班打卡拍照url")
    @ApiModelProperty("上班打卡拍照url")
    private String startWorkCheckInPhotoUrl;

    @FieldDoc(description = "下班打卡拍照url")
    @ApiModelProperty("下班打卡拍照url")
    private String endWorkCheckInPhotoUrl;

    @FieldDoc(description = "迟到异常持续时间（单位是秒）")
    @ApiModelProperty("迟到异常持续时间（单位是秒）")
    private Long beLateExceptionDuration;

    @FieldDoc(description = "早退异常持续时间（单位是秒）")
    @ApiModelProperty("早退异常持续时间（单位是秒）")
    private Long leaveEarlyExceptionDuration;
}
