package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.sku.FrontCategorySimpleVO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ConverterUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MoneyUtils;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.ocms.OCMSUtils;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelSkuDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelSpuDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Title: ChannelSpuCreateVO
 * @Description: 创建渠道商品SPU信息
 * @Author: zhaolei12
 * @Date: 2020/5/16 5:51 下午
 */
@TypeDoc(
        description = "创建渠道商品SPU信息"
)
@Data
@ApiModel("创建渠道商品SPU信息")
public class ChannelSpuCreateVO {

    @FieldDoc(
            description = "SPU编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "SPU编码", required = true)
    @NotNull
    private String spuId;

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID", required = true)
    @NotNull
    private Integer channelId;

    @FieldDoc(
            description = "商品状态", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品状态", required = true)
    private Integer spuStatus;

    @FieldDoc(
            description = "门店商品多分类", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店商品多分类")
    private List<FrontCategorySimpleVO> frontCategories = new ArrayList<>();

    @FieldDoc(
            description = "渠道类目信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道类目信息")
    private ChannelCategoryVO channelCategory;

    public static List<ChannelSpuDTO> toDTOList(List<ChannelSpuCreateVO> channelSpuDetailVOList,
                                                List<StoreSkuCreateVO> skuCreateVOS) {
        if (CollectionUtils.isEmpty(channelSpuDetailVOList)) {
            return Lists.newArrayList();
        }

        return channelSpuDetailVOList.stream().filter(Objects::nonNull)
                .map(channelSpuVO -> toDTO(channelSpuVO, skuCreateVOS))
                .collect(Collectors.toList())
                .stream().filter(Objects::nonNull).collect(Collectors.toList());

    }

    public static ChannelSpuDTO toDTO(ChannelSpuCreateVO channelSpuDetailVO, List<StoreSkuCreateVO> skuCreateVOS) {
        if (channelSpuDetailVO == null) {
            return null;
        }

        ChannelSpuDTO channelSpuDTO = new ChannelSpuDTO();
        channelSpuDTO.setSpuId(channelSpuDetailVO.getSpuId());
        channelSpuDTO.setChannelId(channelSpuDetailVO.getChannelId());
        channelSpuDTO.setSpuStatus(channelSpuDetailVO.getSpuStatus());

        // 店内分类
        channelSpuDTO.setFrontCategories(OCMSUtils.convert2FrontCategoryWithPathDTO(channelSpuDetailVO.getFrontCategories()));

        // 渠道类目
        channelSpuDTO.setChannelCategory(OCMSUtils.convert2ChannelCategoryDTO(channelSpuDetailVO.getChannelCategory()));

        List<ChannelSkuDTO> channelSkuDTOList = ConverterUtils.convertList(skuCreateVOS, skuCreateVO -> {
            ChannelSkuDTO skuDTO = new ChannelSkuDTO();
            skuDTO.setChannelId(channelSpuDetailVO.getChannelId());
            skuDTO.setSpuId(channelSpuDetailVO.getSpuId());
            skuDTO.setSkuId(skuCreateVO.getSkuId());
            skuDTO.setPrice(centToYuan(skuCreateVO.getOnlinePrice()));
            skuDTO.setPriceStrategy(skuCreateVO.getPriceStrategy());
            skuDTO.setMinOrderCount(skuCreateVO.getMinNum());
            skuDTO.setBoxNum(skuCreateVO.getBoxNum());
            skuDTO.setBoxPrice(skuCreateVO.getBoxPrice());
            return skuDTO;
        });

        channelSpuDTO.setChannelSkuList(channelSkuDTOList);

        return channelSpuDTO;
    }

    public static Double centToYuan(Integer cent) {
        if (cent == null) {
            return 0D;
        }
        return BigDecimal.valueOf(cent).divide(BigDecimal.valueOf(100),2, RoundingMode.CEILING).doubleValue();
    }

}
