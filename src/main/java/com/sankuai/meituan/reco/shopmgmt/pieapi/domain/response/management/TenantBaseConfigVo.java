package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.management;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;

import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.shangou.saas.tenant.thrift.dto.biz.TenantBizModuleDto;
import com.meituan.shangou.saas.tenant.thrift.dto.config.ConfigContentDto;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/5/24
 */
@Data
public class TenantBaseConfigVo {


    @FieldDoc(
            description = "租户id"
    )
    private Long tenantId;


    @FieldDoc(
            description = "租户开通的模块列表"
    )
    private List<OpenBizModuleVo> openBizModuleList;


    @FieldDoc(
            description = "租户配置信息"
    )
    private List<ConfigContent> tenantConfigs;

    public static TenantBaseConfigVo of(List<TenantBizModuleDto> tenantModules, long tenantId) {
        TenantBaseConfigVo tenantBaseConfigVo = new TenantBaseConfigVo();
        tenantBaseConfigVo.setTenantId(tenantId);
        tenantBaseConfigVo.setOpenBizModuleList(buildModules(tenantModules));
        return tenantBaseConfigVo;
    }

    private static List<OpenBizModuleVo> buildModules(List<TenantBizModuleDto> tenantModules) {
        if (CollectionUtils.isEmpty(tenantModules)) {
            return Collections.emptyList();
        }
        List<OpenBizModuleVo> openBizModuleVoList = new ArrayList<>();
        for (TenantBizModuleDto tenantBizModuleDto : tenantModules) {
            openBizModuleVoList.add(convertFromTenantBizModuleDto(tenantBizModuleDto));
        }
        return openBizModuleVoList;
    }

    private static OpenBizModuleVo convertFromTenantBizModuleDto(TenantBizModuleDto tenantBizModuleDto) {
        OpenBizModuleVo openBizModuleVo = new OpenBizModuleVo();
        openBizModuleVo.setBizModuleId(tenantBizModuleDto.getBizModuleId());
        openBizModuleVo.setBizModuleCode(tenantBizModuleDto.getBizModuleCode());
        openBizModuleVo.setBizModuleName(tenantBizModuleDto.getBizModuleName());
        openBizModuleVo.setConfigs(buildConfigs(tenantBizModuleDto.getConfigContentList()));
        return openBizModuleVo;

    }

    private static List<ConfigContent> buildConfigs(List<ConfigContentDto> configContentList) {
        if (CollectionUtils.isEmpty(configContentList)) {
            return Collections.emptyList();
        }
        List<ConfigContent> configContents = new ArrayList<>();
        for (ConfigContentDto configContentDto : configContentList) {
            configContents.add(convertFromConfigContentDto(configContentDto));
        }
        return configContents;
    }

    private static ConfigContent convertFromConfigContentDto(ConfigContentDto configContentDto) {
        ConfigContent configContent = new ConfigContent();
        configContent.setConfigId(configContentDto.getConfigId());
        configContent.setConfigContent(configContentDto.getConfigContent());
        try {
            configContent.setConfigContentMap(JacksonUtils.parseMap(configContentDto.getConfigContent(), String.class, Object.class));
        }
        catch (Exception e) {
            // 忽略异常
        }
        return configContent;
    }
}
