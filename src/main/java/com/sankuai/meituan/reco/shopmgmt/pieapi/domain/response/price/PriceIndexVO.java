package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.price;
// Copyright (C) 2020 Meituan
// All rights reserved

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: <EMAIL>
 * @class: PriceIndexVO
 * @date: 2020-03-23 17:51:25
 * @desc:
 */
@Data
@TypeDoc(
        description = "价格指数对象"
)
@ApiModel("价格指数对象")
public class PriceIndexVO {


    @FieldDoc(
            description = "门店价格指数时为门店ID,品类价格指数时为品类ID"
    )
    @ApiModelProperty("门店价格指数时为门店ID,品类价格指数时为品类ID")
    private String uniqueId;

    @FieldDoc(
            description = "展示名称"
    )
    @ApiModelProperty("展示名称")
    private String displayName;

    @FieldDoc(
            description = "价格指数信息"
    )
    @ApiModelProperty("价格指数信息")
    private Double priceIndex;


    @FieldDoc(
            description = "价格指数等级"
    )
    @ApiModelProperty("价格指数等级")
    private Integer level;

    @FieldDoc(
            description = "格式化后的价格指数 具体数量或等级"
    )
    @ApiModelProperty("格式化后的价格指数 具体数量或等级")
    private String displayInfo;


}
