package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.wavepick.converter;

import com.sankuai.meituan.reco.pickselect.thrift.wave.dto.WarehousePickCompleteModuleDTO;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.pick.vo.WarehousePickCompleteModuleVO;
import org.mapstruct.Mapper;

/**
 * @Description:
 * @Author: zhangjian155
 * @Date: 2022/8/25 16:49
 */
@Mapper(componentModel = "spring", uses = {WarehousePickCompleteTaskModuleConverter.class, WarehouseGoodsOwnerModuleConverter.class, WarehouseReceiveStoreInfoConverter.class})
public abstract class WarehousePickCompleteModuleConverter {
    public abstract WarehousePickCompleteModuleVO convert2Vo(WarehousePickCompleteModuleDTO warehousePickCompleteModuleDTO);
}
