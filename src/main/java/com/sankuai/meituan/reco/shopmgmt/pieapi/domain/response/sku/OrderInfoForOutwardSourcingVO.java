package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.sku;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@TypeDoc(
        description = "待外采现结订单信息"
)
@Data
@ApiModel("待外采现结订单信息")
@NoArgsConstructor
public class OrderInfoForOutwardSourcingVO {

    @FieldDoc(
            description = "渠道ID  100:美团  200:饿了么  300:京东到家", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID  100:美团  200:饿了么  300:京东到家", required = true)
    private Integer channelId;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道订单号", required = true)
    private String orderId;
}
