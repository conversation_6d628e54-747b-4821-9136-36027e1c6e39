package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper;

import com.sankuai.meituan.common.time.TimeUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.shopmgmt.pieapi.constants.ResultCode;
import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.dto.PendingTaskParam;
import com.sankuai.meituan.reco.shopmgmt.pieapi.exception.CommonRuntimeException;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.TimeUtils;
import com.sankuai.meituan.shangou.empower.price.client.request.quote.QuoteReviewCountRequest;
import com.sankuai.meituan.shangou.empower.price.client.response.quote.QuoteReviewCountResponse;
import com.sankuai.meituan.shangou.empower.price.client.service.ChannelPriceQuoteThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

@Service
@Slf4j
public class ChannelPriceServiceWrapper {

    @Autowired
    private ChannelPriceQuoteThriftService retailPriceQuoteThriftService;


    @MethodLog(logRequest = true, logResponse = true)
    public Integer queryRetailPriceQuoteReviewingCountList(PendingTaskParam param) {
        try {
            QuoteReviewCountRequest request = new QuoteReviewCountRequest();
            request.setTenantId(param.getTenantId());
            request.setStoreIds(param.getStoreIds());
            LocalDateTime now = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);
            LocalDate threeMonthBefore = now.toLocalDate().minusMonths(3);

            request.setQuoteEndTime(TimeUtils.localDateTimeToMills(now));
            request.setQuoteStartTime(TimeUtils.localDateToMills(threeMonthBefore));
            log.info("channelPriceQuoteThriftService.queryChannelPriceQuoteReviewingCount() request:{}", request);
            QuoteReviewCountResponse queryResponse = retailPriceQuoteThriftService.queryChannelPriceQuoteReviewingCount(request);
            log.info("channelPriceQuoteThriftService.queryChannelPriceQuoteReviewingCount() response:{}", queryResponse);

            if (queryResponse.getCode() == ResultCode.SUCCESS.getCode()) {
                return queryResponse.getQuoteReviewingCount();
            } else {
                return null;
            }
        } catch (TException e) {
            log.error("channelPriceThriftService.queryQuoteReviewingNumList() TException ", e);
            throw new CommonRuntimeException(e);
        }
    }
}
