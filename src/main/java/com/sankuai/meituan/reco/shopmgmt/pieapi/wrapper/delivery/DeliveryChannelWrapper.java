package com.sankuai.meituan.reco.shopmgmt.pieapi.wrapper.delivery;

import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.AggDeliveryPlatformEnum;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.MccConfigUtil;
import com.sankuai.meituan.reco.shopmgmt.pieapi.utils.RetryTemplateUtil;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.DeliveryChannelThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.dto.DeliveryChannelDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.request.DeliveryChannelBatchQueryByCarrierCodeRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.response.DeliveryChannelBatchQueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Service;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * @description:
 * @Auther: nifei
 * @Date: 2023/4/7 18:11
 */
@Slf4j
@Service
public class DeliveryChannelWrapper {

    private static final int MAX_RETRY_COUNT = 3;
    private static final int BACK_OFF_PERIOD = 100;
    private static final Integer UNKNOWN_DELIVERY_PLATFORM_CODE = -1;
    private static final String UNKNOWN_CARRIER_NAME = "未知承运商";

    @Autowired
    private DeliveryChannelThriftService deliveryChannelThriftService;

    /**
     * 检查某平台是否已配置该渠道
     * @return 包含该配置返回true，否则返回false
     */
    public boolean checkChannel(Integer deliveryChannelCode, Integer deliveryPlatFormCode, Long tenantId, Long storeId, Map<Integer,Integer> channelMap){
        if(deliveryChannelCode == null || deliveryPlatFormCode == null){
            return false;
        }
        try {
            if (MapUtils.isEmpty(channelMap) || this.checkTenantAndStore(tenantId,storeId)){
                AggDeliveryPlatformEnum aggDeliveryPlatformEnum = AggDeliveryPlatformEnum.codeValueOf(deliveryPlatFormCode);
                log.info("checkChannel from enum");
                return Objects.nonNull(aggDeliveryPlatformEnum) && aggDeliveryPlatformEnum.checkChanel(deliveryChannelCode);
            }else {
                log.info("checkChannel from channelMap");
                return channelMap.containsKey(deliveryChannelCode) && deliveryPlatFormCode.equals(channelMap.get(deliveryChannelCode));
            }
        }catch (Exception e){
            log.error("DeliveryChannelWrapper checkChannel error",e);
            AggDeliveryPlatformEnum aggDeliveryPlatformEnum = AggDeliveryPlatformEnum.codeValueOf(deliveryPlatFormCode);
            return Objects.nonNull(aggDeliveryPlatformEnum) && aggDeliveryPlatformEnum.checkChanel(deliveryChannelCode);
        }
    }
    public Map<Integer,Integer> tratranslateToChannelIntgerMap(Map<Integer,DeliveryChannelDto> channelDtoMap){
        return channelDtoMap.entrySet().stream().filter(e -> Objects.nonNull(e.getValue())).collect(Collectors.toMap(Map.Entry::getKey,entry -> entry.getValue().deliveryPlatFormCode));
    }



    public Map<Integer,DeliveryChannelDto> getDeliveryChannelDtoMap(Set<Integer> channelCodes){
        HashMap<Integer, DeliveryChannelDto> channelMap = new HashMap<>();
        if (CollectionUtils.isEmpty(channelCodes)){
            return channelMap;
        }
        RetryTemplate retryTemplate = RetryTemplateUtil.simpleWithFixedRetry(MAX_RETRY_COUNT, BACK_OFF_PERIOD);
        DeliveryChannelBatchQueryByCarrierCodeRequest request = new DeliveryChannelBatchQueryByCarrierCodeRequest();
        request.setCarrierCodeSet(channelCodes);
        try {
            DeliveryChannelBatchQueryResponse response = retryTemplate.execute((RetryCallback<DeliveryChannelBatchQueryResponse, Exception>) retryContext -> deliveryChannelThriftService.batchQueryDeliveryChannelByCarrierCodeList(request));

            if (Objects.isNull(response)|| response.getStatus().getCode() != FailureCodeEnum.SUCCESS.getCode() || CollectionUtils.isEmpty((response.getDeliveryChannelDtoList()))) {

                Arrays.stream(DeliveryChannelEnum.values())
                        .filter(item -> Objects.nonNull(item.getDeliveryPlatform()))
                        .forEach(e -> channelMap.put(e.getCode(), translateFromDeliveryChannelEnum(e.getCode())));
                log.info("getDeliveryChannelDtoMap from enum");
                return channelMap;
            }
            log.info("getDeliveryChannelDtoMap from rpc");
            return response.getDeliveryChannelDtoList()
                    .stream()
                    .filter(channel -> Objects.nonNull(channel) && Objects.nonNull(channel.getCarrierCode()))
                    .collect(Collectors.toMap(DeliveryChannelDto::getCarrierCode, Function.identity()));
        }catch (Exception e){
            log.error("DeliveryChannelService getDeliveryChannelDtoMap error",e);
            Arrays.stream(DeliveryChannelEnum.values())
                    .filter(item -> Objects.nonNull(item.getDeliveryPlatform()))
                    .forEach(a -> channelMap.put(a.getCode(), translateFromDeliveryChannelEnum(a.getCode())));
            return channelMap;
        }
    }

    private DeliveryChannelDto getUnknownDeliveryChannel(Integer carrierCode) {
        return DeliveryChannelDto.builder()
                .logisticMark(StringUtils.EMPTY)
                .deliveryPlatFormCode(UNKNOWN_DELIVERY_PLATFORM_CODE)
                .carrierCode(carrierCode)
                .carrierName(UNKNOWN_CARRIER_NAME)
                .orderChannelCode(NumberUtils.INTEGER_ZERO).build();
    }

    private DeliveryChannelDto translateFromDeliveryChannelEnum(Integer carrierCode) {
        if (null == carrierCode){
            return null;
        }
        DeliveryChannelEnum deliveryChannelEnum = DeliveryChannelEnum.valueOf(carrierCode);
        if (Objects.isNull(deliveryChannelEnum)) {
            return getUnknownDeliveryChannel(carrierCode);
        }

        DeliveryChannelDto deliveryChannelDto = new DeliveryChannelDto();
        deliveryChannelDto.setLogisticMark(StringUtils.EMPTY);
        if (Objects.isNull(deliveryChannelEnum.getDeliveryPlatform())) {
            // 平台配送的deliveryPlatform为null
            deliveryChannelDto.setDeliveryPlatFormCode(UNKNOWN_DELIVERY_PLATFORM_CODE);
        } else {
            deliveryChannelDto.setDeliveryPlatFormCode(deliveryChannelEnum.getDeliveryPlatform().getCode());
        }
        deliveryChannelDto.setCarrierCode(deliveryChannelEnum.getCode());
        deliveryChannelDto.setCarrierName(deliveryChannelEnum.getName());
        deliveryChannelDto.setOrderChannelCode(NumberUtils.INTEGER_ZERO);

        return deliveryChannelDto;
    }

    /**
     * 全局开关打开 或 歪马租户 或 非灰度门店 走枚举类.返回true
     * @param tenantId
     * @param storeId
     * @return true or false
     */
    public boolean checkTenantAndStore(Long tenantId, Long storeId){
        if (Objects.isNull(tenantId) && Objects.isNull(storeId)){
            return true;
        }
        // 如果是全局开关打开 或 歪马租户 或 非灰度门店，走之前枚举类的逻辑
        return MccConfigUtil.isDeliveryChannelQuery4Enum() || MccConfigUtil.getDHTenantIdList().contains(String.valueOf(tenantId)) || !MccConfigUtil.isDeliveryChannelConfigStore(storeId);
    }
}
