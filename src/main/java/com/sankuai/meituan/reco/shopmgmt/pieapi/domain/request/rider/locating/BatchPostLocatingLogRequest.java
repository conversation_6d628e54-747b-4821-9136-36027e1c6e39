package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.rider.locating;

import com.facebook.swift.codec.ThriftField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.operation.request.RiderLocatingLogTRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@TypeDoc(
        description = "批量上报定位日志请求"
)
@Data
@ApiModel("批量上报定位日志请求")
public class BatchPostLocatingLogRequest {
    @FieldDoc(
            description = "定位日志List"
    )
    private List<RiderLocatingLog>  locatingLogList;
}
