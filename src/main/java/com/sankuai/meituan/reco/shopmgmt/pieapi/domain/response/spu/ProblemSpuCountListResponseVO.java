package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.spu;

import com.google.common.collect.Maps;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.shopmgmt.pieapi.enums.CompareSpuTypeEnum;
import com.sankuai.meituan.shangou.empower.productbiz.client.response.QueryCompareTypeCountResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2022/2/21 下午8:27
 **/
@TypeDoc(
        description = "不一致商品统计"
)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ProblemSpuCountListResponseVO {
    @FieldDoc(
            description = "类型数量统计,一级分类:key:(100商品缺失，200规格缺失，300 信息不一致 400 价格不一致) value 二级分类和一级统计数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "类型数量统计", required = true)
    private Map<Integer, ProblemSpuTypeVo> topTypeCount;

    public static ProblemSpuCountListResponseVO of(QueryCompareTypeCountResponse queryCompareTypeCountResponse){
        Map<Integer, ProblemSpuTypeVo> typeCount = Maps.newHashMap();
        Map<Integer, Integer> compareTypeCount = queryCompareTypeCountResponse.getCompareTypeCount();

        typeCount.put(CompareSpuTypeEnum.SPU_MISS.getCode(), ProblemSpuTypeVo.ofSpuMiss(
                compareTypeCount.getOrDefault(CompareSpuTypeEnum.WAIMAI_SPU_MISS.getWebCode(),0),
                compareTypeCount.getOrDefault(CompareSpuTypeEnum.SGP_SPU_MISS.getWebCode(),0)));

        typeCount.put(CompareSpuTypeEnum.SPEC_MISS.getCode(), ProblemSpuTypeVo.ofSpecMiss(
                compareTypeCount.getOrDefault(CompareSpuTypeEnum.WAIMAI_SPEC_MISS.getWebCode(),0),
                compareTypeCount.getOrDefault(CompareSpuTypeEnum.SGP_SPEC_MISS.getWebCode(),0)));


        typeCount.put(CompareSpuTypeEnum.TOP_BASE_DIFF.getCode(), ProblemSpuTypeVo.ofBaseDiff(
                compareTypeCount.getOrDefault(CompareSpuTypeEnum.BASE_INFO_DIFF.getWebCode(), 0),
                compareTypeCount.getOrDefault(CompareSpuTypeEnum.SALSE_INFO_DIFF.getWebCode(), 0)));

        typeCount.put(CompareSpuTypeEnum.TOP_PRICE_DIFF.getCode(), ProblemSpuTypeVo.ofPriceDiff(
                compareTypeCount.getOrDefault(CompareSpuTypeEnum.PRICE_DIFF.getWebCode(),0)));
        return new ProblemSpuCountListResponseVO(typeCount);
    }
}
