package com.sankuai.meituan.reco.shopmgmt.pieapi.domain.request.delivery;

import javax.validation.constraints.NotNull;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import com.sankuai.meituan.reco.shopmgmt.pieapi.domain.response.delivery.StoreDeliveryChannelLaunchPointVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@TypeDoc(
		description = "配送管理配置更新"
)
@ApiModel("配送管理配置更新")
@Data
public class DeliveryManagementUpdateRequest {

	@FieldDoc(
			description = "门店id", requiredness = Requiredness.REQUIRED
	)
	@ApiModelProperty(value = "门店id", required = true)
	@NotNull(message = "门店id不能为空")
	private Long storeId;

	@FieldDoc(
			description = "平台配置信息", requiredness = Requiredness.REQUIRED
	)
	@ApiModelProperty(value = "平台配置信息", required = true)
	@NotNull(message = "配置信息为空")
	private AggDeliveryPlatformInfoRequest aggDeliveryPlatformInfo;

}
