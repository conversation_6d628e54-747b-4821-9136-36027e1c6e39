package com.sankuai.shangou.qnh.orderapi.configuration.app;

import com.sankuai.meituan.shangou.empower.auth.sdk.filter.AuthFilterFactoryBean;
import com.sankuai.meituan.shangou.empower.auth.sdk.filter.LoginFilterFactoryBean;
import com.sankuai.shangou.qnh.orderapi.filter.app.CorsFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.web.filter.DelegatingFilterProxy;

import javax.servlet.DispatcherType;

/**
 * Created by l<PERSON><PERSON><PERSON> on 2018/8/15.
 */
@Configuration("appFilterConfiguration")
public class FilterConfiguration {
    private static final int CHARACTER_ENCODING_FILTER_ORDER = 2;

    /**
     * 确保加载顺序
     */
    private static final int JMONITOR_LISTENER_ORDER = Ordered.HIGHEST_PRECEDENCE;
    private static final int MT_CONTEXT_LISTENER_ORDER = JMONITOR_LISTENER_ORDER + 1;
    private static final int HTTP_MONITOR_FILTER_ORDER = MT_CONTEXT_LISTENER_ORDER + 1;
    private static final int CROS_MONITOR_FILTER_ORDER = HTTP_MONITOR_FILTER_ORDER + 1;
    private static final int LOGIN_FILTER_ORDER = CROS_MONITOR_FILTER_ORDER + 1;
    private static final int CAT_MONITOR_FILTER_ORDER = LOGIN_FILTER_ORDER + 1;
    private static final int ENCODING_MONITOR_FILTER_ORDER = CAT_MONITOR_FILTER_ORDER + 1;
    private static final int AUTH_FILTER_ORDER = ENCODING_MONITOR_FILTER_ORDER + 1;
    private static final int CORS_FILTER_ORDER = AUTH_FILTER_ORDER + 1;

    // 数据鉴权准备 filter，这里放最后面
    private static final int DATA_SECURITY_PREPARE_FILTER_ORDER = 100;

    //登录校验filter
    @Bean
    public FilterRegistrationBean appLoginFilter(){
        DelegatingFilterProxy filter = new DelegatingFilterProxy();
        FilterRegistrationBean registration = new FilterRegistrationBean();
        filter.setTargetBeanName("appLoginFilterBean");
        filter.setTargetFilterLifecycle(true);

        registration.setFilter(filter);
        registration.addUrlPatterns("/pieapi/account/homepage");
        registration.addUrlPatterns("/pieapi/account/queryconfig");
        registration.addUrlPatterns("/pieapi/account/setconfig");
        registration.addUrlPatterns("/pieapi/account/mobile/sendBindVerificationCode");
        registration.addUrlPatterns("/pieapi/account/mobile/verifyAndBind");
        registration.addUrlPatterns("/pieapi/account/mobile/interface/sendBindVerificationCode");
        registration.addUrlPatterns("/pieapi/account/mobile/interface/verifyAndBind");
        registration.addUrlPatterns("/pieapi/account/deactivationAccount");
        registration.addUrlPatterns("/pieapi/auth/*");
        registration.addUrlPatterns("/pieapi/management/*");
        registration.addUrlPatterns("/pieapi/order/*");
        registration.addUrlPatterns("/pieapi/pda/order/*");

        registration.addUrlPatterns("/pieapi/miniapp/order/*");
        registration.addUrlPatterns("/pieapi/pendingtask/*");
        registration.addUrlPatterns("/pieapi/pick/*");
        registration.addUrlPatterns("/pieapi/securitydeposit/*");
        registration.addUrlPatterns("/pieapi/employee/*");
        registration.addUrlPatterns("/pieapi/ocms/skuAndStock/*");
        registration.addUrlPatterns("/pieapi/push/config/uuid");
        registration.addUrlPatterns("/pieapi/messagecenter/*");
        registration.addUrlPatterns("/pieapi/contract/*");
        registration.addUrlPatterns("/order/settlement/history/booth/*");
        registration.addUrlPatterns("/pieapi/priceservice/*");
        registration.addUrlPatterns("/pieapi/priceTrend/*");
        registration.addUrlPatterns("/pieapi/ocms/store/spu/*");
        registration.addUrlPatterns("/pieapi/ocms/problem/spu/*");
        registration.addUrlPatterns("/pieapi/ocms/spu/region/*");
        registration.addUrlPatterns("/pieapi/ocms/channel/spu/*");
        registration.addUrlPatterns("/pieapi/ocms/tenant/spu/*");
        registration.addUrlPatterns("/pieapi/store/spu/erp/*");
        registration.addUrlPatterns("/pieapi/booth/*");
        registration.addUrlPatterns("/pieapi/productintelligent/*");
        registration.addUrlPatterns("/pieapi/ocms/channel/frontCategorySpu/*");
        registration.addUrlPatterns("/pieapi/price/*");
        registration.addUrlPatterns("/pieapi/realtime/*");
        registration.addUrlPatterns("/pieapi/assistant/task/*");
        registration.addUrlPatterns("/pieapi/delivery/*");
        registration.addUrlPatterns("/pieapi/my/deliverymanagement/*");
        registration.addUrlPatterns("/pieapi/backend-category/*");
        registration.addUrlPatterns("/pieapi/pullnew/*");
        registration.addUrlPatterns("/pieapi/rider/delivery/*");
        registration.addUrlPatterns("/pieapi/rider/manage/*");
        registration.addUrlPatterns("/pieapi/rider/picking/*");
        registration.addUrlPatterns("/pieapi/appmodel/*");
        registration.addUrlPatterns("/pieapi/ocms/brand/*");
        // /pieapi/tenant/* 下有非登录能使用的接口，这里单独进行配置
        registration.addUrlPatterns("/pieapi/tenant/bizMode");
        registration.addUrlPatterns("/pieapi/tenant/chainRelation");
        registration.addUrlPatterns("/pieapi/tenant/hsPurchaseShopInfo");
        registration.addUrlPatterns("/pieapi/tenant/queryPoiByWarehouseId");
        registration.addUrlPatterns("/pieapi/tenant/batchQueryTenantConfig");
        registration.addUrlPatterns("/pieapi/tenant/batchQueryPoiConfig");
        registration.addUrlPatterns("/pieapi/tenant/batchQueryTenantChannelConfig");
        registration.addUrlPatterns("/pieapi/tenant/batchQueryPoiChannelConfig");
        registration.addUrlPatterns("/pieapi/tenant/aggTenantLevelConfig");
        registration.addUrlPatterns("/pieapi/tenant/getTenantType");
        registration.addUrlPatterns("/pieapi/tenant/channels/*");
        registration.addUrlPatterns("/pieapi/rider/locatingLog/postLocatingException");
        registration.addUrlPatterns("/pieapi/merchant/storeCategory/*");
        registration.addUrlPatterns("/pieapi/image/*");
        registration.addUrlPatterns("/pieapi/poi/*");
        registration.addUrlPatterns("/pieapi/store/frontCategory/*");
        registration.addUrlPatterns("/pieapi/common/area/*");
        registration.addUrlPatterns("/pieapi/warehouse/pick/*");
        registration.addUrlPatterns("/pieapi/rider/delivery-statistic/*");
        registration.addUrlPatterns("/pieapi/labor/*");
        registration.addUrlPatterns("/pieapi/consumable/*");
        registration.addUrlPatterns("/pieapi/regionselect/*");
        registration.addUrlPatterns("/pieapi/department/*");
        registration.addUrlPatterns("/pieapi/notice/*");
        registration.addUrlPatterns("/pieapi/customer/*");
        registration.addUrlPatterns("/pieapi/common/sn/*");

        registration.setDispatcherTypes(DispatcherType.REQUEST);
        registration.setName("appLoginFilter");

        registration.setOrder(LOGIN_FILTER_ORDER);
        return registration;
    }

    @Bean
    public FilterRegistrationBean corsFilter() {
        CorsFilter filter = new CorsFilter();
        FilterRegistrationBean registration = new FilterRegistrationBean(filter);
        registration.addUrlPatterns("/pieapi/order/*");
        registration.addUrlPatterns("/pieapi/pda/order/*");

        registration.addUrlPatterns("/pieapi/miniapp/order/*");
        registration.setName("cors-filter");
        registration.setOrder(CORS_FILTER_ORDER);
        return registration;
    }

    //登录factoryBean 和相关配置
    @Bean
    public LoginFilterFactoryBean appLoginFilterBean(){
        LoginFilterFactoryBean loginFilterFactoryBean = new LoginFilterFactoryBean();
        loginFilterFactoryBean.setSecret("");//账号系统分配
        loginFilterFactoryBean.setLogEnable(true);//info日志打印
        loginFilterFactoryBean.setExcludedUriList("/api/account/**");//不校验配置urlList（ANT风格） 逗号分隔
        loginFilterFactoryBean.setIncludedUriList("");//校验配置urlList（ANT风格） 逗号分隔,优先级高于exludedUriList，同时配置则只有include生效
        // loginFilterFactoryBean.setUnAuthorizedResponse("{\"code\": 401,\"message\": \"未登录，请登录后再试\"}");//校验不通过给前端返回信息
        return loginFilterFactoryBean;
    }

    //鉴权filter
    @Bean
    public FilterRegistrationBean authFilter(){
        DelegatingFilterProxy filter = new DelegatingFilterProxy();
        FilterRegistrationBean registration = new FilterRegistrationBean();
        filter.setTargetBeanName("authFilterBean");
        filter.setTargetFilterLifecycle(true);

        registration.setFilter(filter);
        //接口鉴权url
        /**
         * account首页以及首页默认功能不再进行权限鉴权，需要进行鉴权的功能点再单独加功能点的url
         */
        registration.addUrlPatterns("/pieapi/account/deactivationAccount");

        /**
         * 经营首页因为特殊的模块加载机制，不能进行鉴权，而是等返回模块列表为空以后，由前端展示无权限给用户
         */
        /*registration.addUrlPatterns("/pieapi/management/*");*/
        registration.addUrlPatterns("/pieapi/order/*");
        /*registration.addUrlPatterns("/pieapi/miniapp/order/*");*/
        registration.addUrlPatterns("/pieapi/pendingtask/*");
        registration.addUrlPatterns("/pieapi/pick/*");
        registration.addUrlPatterns("/pieapi/employee/*");
        registration.addUrlPatterns("/pieapi/securitydeposit/list");
        registration.addUrlPatterns("/pieapi/push/config/uuid");
        registration.addUrlPatterns("/pieapi/priceservice/*");
        registration.addUrlPatterns("/pieapi/price/*");
        registration.addUrlPatterns("/pieapi/realtime/*");
        registration.setDispatcherTypes(DispatcherType.REQUEST);
        registration.setName("authFilter");

        registration.setOrder(AUTH_FILTER_ORDER);
        return registration;
    }

    //鉴权factorybean 和相关配置
    @Bean
    public AuthFilterFactoryBean authFilterBean(){
        AuthFilterFactoryBean authFilterFactoryBean = new AuthFilterFactoryBean();
        authFilterFactoryBean.setSecret("");//账号系统分配
        // 账号相关的接口不需要做功能鉴权,goods/list,goods/detail目前已经是公共接口,不需要做接口鉴权
        String excludedUriList = "/api/account/**,";
        authFilterFactoryBean.setExcludedUriList(excludedUriList);//不校验配置urlList（ANT风格） 逗号分隔
        authFilterFactoryBean.setLogEnable(true);//info日志打印
        authFilterFactoryBean.setIncludedUriList("");//校验配置urlList（ANT风格） 逗号分隔, 优先级高于exludedUriList，同时配置则只有include生效
        authFilterFactoryBean.setAuthFailedResponse("{\"code\": 402,\"message\": \"认证不通过\"}");//校验不通过给前端返回信息,

        return authFilterFactoryBean;
    }

}
