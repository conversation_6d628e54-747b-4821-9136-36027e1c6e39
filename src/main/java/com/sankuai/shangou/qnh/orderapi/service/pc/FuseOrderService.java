package com.sankuai.shangou.qnh.orderapi.service.pc;

import com.meituan.shangou.saas.order.platform.client.dto.model.OrderLabelModel;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.dto.DeliveryChannelDto;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.OrderFuseDetailBO;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.OrderFuseFinanceDetailBO;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.OrderFuseQueryBO;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.OrderFuseQueryStatisticsDataBO;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.OrderItemFuseQueryBO;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.OrderItemFuseQueryStatisticsDataBO;
import com.sankuai.shangou.qnh.orderapi.domain.request.CreateQnhInvoiceRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.QueryVirtualPhoneRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.*;
import com.sankuai.shangou.qnh.orderapi.domain.response.CreateQnhInvoiceResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.*;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.QueryVirtualPhoneResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.CheckSelfDeliveryCodeResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.CommonFuseResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.CommonResponse;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.MultiStageOptionable;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.Result;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.UiOptionMultiStage;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.AfsOrderFuseDetailVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.DeliveryStatusVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.ExportFieldsVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.OrderExportMaxCountVO;
import javafx.util.Pair;

import java.util.List;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @Date: 2022/12/8 14:25
 * @Description:
 */
public interface FuseOrderService {

    /**
     * 查询订单列表
     * @param orderFuseQueryBO
     * @return
     */
    CommonFuseResponse<OrderFuseListResponse> queryFuseOrders(OrderFuseQueryBO orderFuseQueryBO,Long currentStoreId);

    /**
     * 查询拣货员列表
     * @param request
     * @return
     */
    CommonFuseResponse<QueryShopPickerResponse> queryShopPicker(QueryShopPickerRequest request);

    /**
     * 任务分派
     * @param request
     * @return
     */
    CommonFuseResponse<String> transferPicker(TransferOrderRequest request);

    /**
     * 查询订单详情
     *
     * @param orderId
     * @param tenantId
     * @param channelId
     * @param uid
     * @return
     */
    Pair<OrderFuseDetailBO, Map<Integer, DeliveryChannelDto>> queryDetail(String orderId, Long tenantId, int channelId, Long uid);

    /**
     * 查询订单或退单财务明细
     * @param orderId 订单id或售后id
     * @param tenantId 租户id
     * @param type 1订单，2退单
     * @return
     */
    OrderFuseFinanceDetailBO queryFinanceDetail(Long orderId, Long shopId, Long tenantId, int type);

    /**
     * 查询订单或退单财务明细
     * @param orderIdList 订单id或售后id
     * @param tenantId 租户id
     * @param type 1订单，2退单
     * @return
     */
    List<OrderFuseFinanceDetailBO> queryMultiShopFinanceDetail(List<Long> orderIdList, List<Long> shopId, Long tenantId, int type);

    /**
     * 查询配送异常订单列表
     * @param request
     * @return
     */
    CommonFuseResponse<OrderFuseListResponse> queryDeliveryErrorOrderList(QueryDeliveryErrorOrderBySubTypeRequest request,Long curStoreId);
    /**
     * 查询订单子状态数量
     * @param orderFuseQueryBO
     * @return
     */
    CommonFuseResponse<OrderSubStatusCountResponse> queryWaitDeliverySubTypeCount(OrderFuseQueryBO orderFuseQueryBO);

    /**
     * 查询退单列表
     * @param refundOrderQueryRequest
     * @return
     */
    CommonFuseResponse<RefundOrderListResponse> queryRefundOrderList(RefundOrderQueryRequest refundOrderQueryRequest,Long currentStoreId);
    /**
     * 查询售后单明细
     * @param request
     * @return
     */
    Result<AfsOrderFuseDetailVO> queryAfsDetail(QueryAfsDetailRequest request);

    /**
     * 查询退单列表
     * @param refundOrderQueryRequest
     * @return
     */
    CommonFuseResponse<RefundOrderCountResponse> queryRefundOrderCount(RefundOrderQueryRequest refundOrderQueryRequest);

    /**
     * 查询订单商品列表
     * @param orderItemFuseQueryBO
     * @return
     */
    CommonFuseResponse<OrderItemFuseListResponse> queryFuseOrderItems(OrderItemFuseQueryBO orderItemFuseQueryBO);

    /**
     * 查询导出字段列表
     * @return
     */
    CommonFuseResponse<List<ExportFieldsVO>> queryExportProfitLoss(ExportTypeRequest request);

    /**
     * 生成导出任务
     * @return
     */
    Result<String> exportOrderListTask(OrderFuseQueryBO orderFuseQueryBO);

    /**
     * 生成退单列表导出
     *
     * @return
     */
    Result<String> exportRefundOrderList(RefundOrderQueryRequest refundOrderQueryRequest);

    /**
     * 生成退单明细导出
     *
     * @return
     */
    Result<String> exportRefundOrderDetailList(RefundOrderQueryRequest refundOrderQueryRequest);

    CommonResponse<DeliveryStatusVO> queryDeliveryDetail(OrderDeliveryDetailRequest request);

    /**
     * 自提码核验
     *
     * @return
     */
    Result verifySelfFetchCode(VerifySelfFetchCodeRequest verifySelfFetchCodeRequest);

    /**
     * 自提码校验
     *
     * @return
     */
    Result<CheckSelfDeliveryCodeResponse> queryOrderBySelfFetchCode(CheckSelfFetchCodeRequest checkSelfFetchCodeRequest);

    OrderExportMaxCountVO queryOrderCount(OrderFuseQueryBO orderFuseQueryBO);

    Result<String> exportOrderListAndDetail(OrderFuseQueryBO orderFuseQueryBO);

    /**
     * 查询订单列表统计数据
     * @param orderFuseQueryStatisticsDataBO
     * @return
     */
    CommonFuseResponse<OrderFuseStatisticsResponse> queryFuseOrderStatisticsData(OrderFuseQueryStatisticsDataBO orderFuseQueryStatisticsDataBO);

    /**
     * 查询订单明细列表统计数据
     *
     * @param orderItemFuseQueryStatisticsDataBO
     * @return
     */
    CommonFuseResponse<OrderItemFuseStatisticsResponse> queryItemFuseOrderStatisticsData(OrderItemFuseQueryStatisticsDataBO orderItemFuseQueryStatisticsDataBO);

    void checkFuseOrderItemsRequest(OrderItemFuseQueryBO request);

    void checkOrderQueryRequest(OrderFuseQueryBO request);

    /**
     *  创建发票
     * @param request
     * @return
     */
    CommonFuseResponse<CreateQnhInvoiceResponse> createInvoice(CreateQnhInvoiceRequest request);

    /**
     * 根据serviceIds查询对应的所有财务退单数据
     * @param serviceIds
     * @param shopId
     * @param tenantId
     * @param type 1正单 2退单
     * @return
     */
    List<OrderFuseFinanceDetailBO> queryFinanceDetails(List<Long> serviceIds, Long shopId, Long tenantId, int type);

    CommonResponse<Void> returnGoodsAudit(ReturnGoodsAuditRequest request);

    /**
     * 查询虚拟手机号
     * @param request
     * @return
     */
    CommonFuseResponse<QueryVirtualPhoneResponse> queryVirtualPhone(QueryVirtualPhoneRequest request);

    CommonResponse<Void> importRefundExchangeInfo(ImportRefundExchangeInfoRequest request);


    List<UiOptionMultiStage> queryOrderSearchLabel(Long tenantId);


    List<UiOptionMultiStage> queryRefundSearchLabel(Long tenantId);


    List<OrderLabelModel> queryOrderShowLabel(Long tenantId);
}
