package com.sankuai.shangou.qnh.orderapi.interceptor.store;

import com.sankuai.shangou.qnh.orderapi.configuration.store.OrderGrayConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/***
 * author : <EMAIL> 
 * data : 2021/3/16 
 * time : 下午2:51
 **/
@Slf4j
@Component
public class OrderGrayInterceptor extends HandlerInterceptorAdapter {

    public static final String HEAD_OCMS_MIGRATE_TEST = "OcmsMigrateTest";

    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {

        if (handler instanceof HandlerMethod) {
            writeContext(request);
        }

        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        OrderGrayConfiguration.release();
        super.postHandle(request, response, handler, modelAndView);
    }

    private void writeContext(HttpServletRequest request) {
        try {
            String ocmsOrderMigrate = request.getHeader(HEAD_OCMS_MIGRATE_TEST);
            OrderGrayConfiguration.setOcmsOrderMigrateGray(ocmsOrderMigrate);
        }catch (Exception e){
            log.error("解析header失败", e);
        }
    }

}
