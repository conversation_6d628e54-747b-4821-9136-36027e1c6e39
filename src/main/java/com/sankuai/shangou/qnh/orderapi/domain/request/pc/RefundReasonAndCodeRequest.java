package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RefundReasonAndCodeRequest {

    /**
     * 订单业务类型
     */
    private Integer orderBizType;

    private String viewOrderId;

    /**
     * 退款类型
     */
    private Integer refundType;

    /**
     * 退款操作类型
     */
    private Integer refundOperationType;
}
