package com.sankuai.shangou.qnh.orderapi.domain.dto.pc;
import com.sankuai.sgfulfillment.comment.thrift.dto.model.ChannelCommentReplyTemplateDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 评价回复模板查询DTO
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Getter
@Setter
public class ChannelCommentReplyTemplateListDTO {

    /**
     * 评价回复模板列表
     */
    private List<ChannelCommentReplyTemplateDTO> commentReplyTemplateDTOList;

    /**
     * 评价回复模板数量最大值
     */
    private Integer commentReplyTemplateCountMax;
}
