package com.sankuai.shangou.qnh.orderapi.domain.vo.app;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/11/18
 * desc: 退款申请记录
 */
@TypeDoc(
        description = "退款申请记录"
)
@ApiModel("退款申请记录")
@Data
public class RefundApplyRecordVO {

    @FieldDoc(
            description = "订单信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单信息", required = true)
    OrderVO orderVO;

    @FieldDoc(
            description = "退款申请唯一ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "退款申请唯一ID", required = true)
    private Long serviceId;

    @FieldDoc(
            description = "渠道售后id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道售后id", required = true)
    private String afterSaleId;

    @FieldDoc(
            description = "是否需要审核", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否需要审核", required = true)
    private Integer isAudit;

    @FieldDoc(
            description = "售后状态 1:提交，3：审核中，4：已审核， 5：已申请驳回， 6：自动审核通过， 7：处理中， 9：已完成， 20：已取消", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "1:提交，3：审核中，4：已审核， 5：已申请驳回， 6：自动审核通过， 7：处理中， 9：已完成， 20：已取消", required = true)
    private Integer status;

    @FieldDoc(
            description = "申请原因", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "申请原因", required = true)
    private String applyReason;

    @FieldDoc(
            description = "退款类型, 1-整单退款，2-部分退款", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "退款类型, 1:整单退款，2：部分退款", required = true)
    private Integer afsPattern;

    @FieldDoc(
            description = "退款金额  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "退款金额  单位:分", required = true)
    private Integer refundAmt;

    @FieldDoc(
            description = "创建时间戳", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "创建时间戳", required = true)
    private Long createTime;

    @FieldDoc(
            description = "更新时间戳", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "更新时间戳", required = true)
    private Long updateTime;

    @FieldDoc(
            description = "售后申请类型   0-未知 1-售中申请  2-售后申请", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "售后申请类型   0-未知 1-售中申请  2-售后申请", required = true)
    private Integer afsApplyType;

    @FieldDoc(
            description = "售后申请人   0-未知 1-用户 2-商户 3-渠道", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "售后申请人   0-未知 1-用户 2-商户 3-渠道", required = true)
    private Integer whoApplyType;

    @FieldDoc(
            description = "售后申请详情列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "售后申请详情列表", required = true)
    private List<RefundApplyRecordDetailVO> refundApplyRecordDetailVOList;

    @FieldDoc(
            description = "售后商品总数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "售后商品总数量", required = false)
    private Integer refundProductCount;

    @FieldDoc(
            description = "订单打印状态，只有待拣货页面才不为空", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "订单打印状态", required = false)
    private OrderPrintStatusVo printStatus;

    @FieldDoc(
            description = "订单可操作列表：10-接单操作，20-完成拣货，30-补打小票，40-全单退款，50-部分退款，60-收到退货", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "订单可操作列表：10-接单操作，20-完成拣货，30-补打小票，40-全单退款，50-部分退款，60-收到退货", required = false)
    private List<Integer> orderCouldOperateItems;


    @FieldDoc(
            description = "待处理退款信息及退款日志", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "待处理退款信息及退款日志", required = false)
    private OrderRefundInfo orderRefundInfo;

    @FieldDoc(
            description = "售后相关图片"
    )
    @ApiModelProperty(value = "售后相关图片")
    public List<String> refundPicList;

    @FieldDoc(
            description = "退款申请类型, 10-仅退款，20-退货退款"
    )
    @ApiModelProperty(value = "退款申请类型")
    public Integer refundApplyType;


    @FieldDoc(
            description = "预计退货产生运费",requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "预计退货产生运费",required = false)
    public Integer preReturnFreight;



    @FieldDoc(
            description = "是否可以直接退款",requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否可以直接退款",required = false)
    public Integer directRefundFlag;

    @FieldDoc(
            description = "分配处理的转单门店ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "分配处理的转单门店ID", required = true)
    private Long dispatchShopId;
    @FieldDoc(
            description = "分配处理的时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "分配处理的时间", required = true)
    private Long dealTime;

    @FieldDoc(
            description = "拣货人姓名List", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "拣货人姓名List", required = false)
    private List<String> pickerNameList = new ArrayList<>();

    @FieldDoc(
            description = "是否支持返货", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否支持返货", required = false)
    private Boolean isCanReturnGoods;

    @FieldDoc(
            description = "是否闪电送订单，1：是，0：否"
    )
    @ApiModelProperty(value = "是否闪电送订单，1：是，0：否")
    private Integer isFastOrder;

    @FieldDoc(
            description = "是否为闪电送转自送订单"
    )
    @ApiModelProperty(value = "是否为闪电送转自送订单")
    private Boolean isFastToSelfDelivery;

    @FieldDoc(
            description = "订单赔付信息"
    )
    @ApiModelProperty(value = "订单赔付信息")
    private CompensationVO compensationModel;

    @FieldDoc(
            description = "闪电送费用"
    )
    @ApiModelProperty(value = "闪电送费用")
    private Integer fastDeliveryAmt;

    @FieldDoc(
            description = "订单赔付信息列表"
    )
    @ApiModelProperty(value = "订单赔付信息列表")
    private List<CompensationVO> compensationModelList;

    @FieldDoc(
            description = "售后换货信息录入是否未录入，ture：未录入，false：已录入"
    )
    @ApiModelProperty(value = "售后换货信息录入是否未录入，ture：未录入，false：已录入")
    private Boolean isNotImport;
}