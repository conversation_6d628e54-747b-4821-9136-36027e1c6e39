package com.sankuai.shangou.qnh.orderapi.remote;

import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.sankuai.meituan.shangou.empower.settlement.dto.model.voucher.OrderChangeVoucherModel;
import com.sankuai.meituan.shangou.empower.settlement.dto.request.voucher.OrderChangeVoucherQueryRequest;
import com.sankuai.meituan.shangou.empower.settlement.dto.request.voucher.VoucherProductAdjustRequest;
import com.sankuai.meituan.shangou.empower.settlement.dto.request.voucher.VoucherProductAdjustment;
import com.sankuai.meituan.shangou.empower.settlement.dto.response.ResponseStatus;
import com.sankuai.meituan.shangou.empower.settlement.dto.response.voucher.OrderChangeVoucherQueryResponse;
import com.sankuai.meituan.shangou.empower.settlement.services.SettlementVoucherThriftService;
import com.sankuai.shangou.qnh.orderapi.constant.pc.MessageConstant;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.ChannelOrderAdjustBO;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.CommonDataBO;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.CommonResultBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/7/28
 */
@Slf4j
@Service
public class SettlementVoucherRemoteClient {

    @Autowired
    private SettlementVoucherThriftService settlementVoucherThriftService;

    /**
     * 查询订单调整凭证
     *
     * @param tenantId
     * @param channelId
     * @param viewOrderId
     * @return
     */
    public CommonDataBO<List<OrderChangeVoucherModel>> queryOrderChangeVouchers(Long tenantId, Integer channelId, String viewOrderId) {

        OrderChangeVoucherQueryRequest request = new OrderChangeVoucherQueryRequest();
        request.setTenantId(tenantId);
        request.setOrderBizType(ChannelOrderConvertUtils.sourceMid2Biz(channelId));
        request.setViewOrderId(viewOrderId);

        CommonDataBO<List<OrderChangeVoucherModel>> commonDataBO = new CommonDataBO<>();
        try {
            OrderChangeVoucherQueryResponse response = settlementVoucherThriftService.queryOrderChangeVoucher(request);
            log.info("查询订单调整凭证 request:{},response:{}", request, response);
            if (response != null && response.getResponseStatus().getCode() == 0) {
                commonDataBO.setSuccess(Boolean.TRUE);
                commonDataBO.setData(response.getOrderChangeVoucherModels());
                return commonDataBO;
            }
            commonDataBO.setSuccess(Boolean.FALSE);
            commonDataBO.setMessage(response == null ? MessageConstant.SYSTEM_ERROR : response.getResponseStatus().getMessage());
            return commonDataBO;
        }
        catch (TException e) {
            log.error("查询订单调整凭证异常 request:{}", request, e);
            commonDataBO.setSuccess(Boolean.FALSE);
            commonDataBO.setMessage(MessageConstant.SYSTEM_ERROR);
            return commonDataBO;
        }
    }

    public CommonResultBO adjustVoucherProduct(ChannelOrderAdjustBO adjustBO) {
        VoucherProductAdjustRequest request = new VoucherProductAdjustRequest();
        request.setTenantId(adjustBO.getTenantId());
        request.setOrderBizType(ChannelOrderConvertUtils.sourceMid2Biz(adjustBO.getChannelId()));
        request.setOperatorAccount(adjustBO.getOperatorAccount());
        request.setOperatorName(adjustBO.getOperatorUserName());
        request.setViewOrderId(adjustBO.getOrderId());
        request.setReason(adjustBO.getComments());
        List<VoucherProductAdjustment> voucherProductAdjustProductModelList = adjustBO.getOrderItemAdjustBOList().stream()
                .map(orderItem -> {
                    VoucherProductAdjustment productAdjustment = new VoucherProductAdjustment();
                    productAdjustment.setOrderItemId(orderItem.getOrderItemId());
                    productAdjustment.setBoothId(orderItem.getBoothId());
                    productAdjustment.setOfflinePrice(orderItem.getOfflinePrice());
                    productAdjustment.setLastUpdateTime(orderItem.getLastUpdateTime());
                    productAdjustment.setOriginalBoothId(orderItem.getOriginalBoothId());
                    productAdjustment.setOriginalOfflinePrice(orderItem.getOriginalOfflinePrice());
                    return productAdjustment;
                }).collect(Collectors.toList());
        request.setVoucherProductAdjustProductModelList(voucherProductAdjustProductModelList);
        try {
            ResponseStatus responseStatus = settlementVoucherThriftService.adjustVoucherProduct(request);
            log.info("调整结算凭证 request:{},response:{}", request, responseStatus);
            if (responseStatus != null && responseStatus.getCode() == 0) {
                return CommonResultBO.builder()
                        .success(Boolean.TRUE)
                        .message(responseStatus.getMessage())
                        .build();
            }
            else {
                return CommonResultBO.builder()
                        .success(Boolean.FALSE)
                        .message(responseStatus == null ? MessageConstant.SYSTEM_ERROR : responseStatus.getMessage())
                        .build();
            }
        }
        catch (TException e) {
            log.error("调整结算凭证发生异常 request:{}", request, e);
            return CommonResultBO.builder()
                    .success(Boolean.FALSE)
                    .message(MessageConstant.SYSTEM_ERROR)
                    .build();
        }
    }
}