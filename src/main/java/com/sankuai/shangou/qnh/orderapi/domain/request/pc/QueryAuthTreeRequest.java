package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.request.QueryTenantAuthTreeByTypeRequest;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import lombok.Data;

/**
 * @description:
 * @author: WangSukuan
 * @create: 2020-01-03
 **/
@Data
public class QueryAuthTreeRequest {

    @FieldDoc(
            description = "权限类型"
    )
    private Integer type = 1;

    @FieldDoc(
            description = "应用ID"
    )
    private Integer appId = 0;

    @FieldDoc(
        description = "业务ID"
    )
    private Integer bizAppId;

    public QueryTenantAuthTreeByTypeRequest convertQueryTenantAuthTreeByTypeRequest(){

        QueryTenantAuthTreeByTypeRequest request = new QueryTenantAuthTreeByTypeRequest();
        request.setTenantId(ContextHolder.currentUserTenantId());
        request.setType(this.getType());
        request.setAppId(this.getAppId());
        request.setBizAppId(this.getBizAppId() == null ? ContextHolder.currentUserBizAppId() : this.getBizAppId());
        return request;

    }

}
