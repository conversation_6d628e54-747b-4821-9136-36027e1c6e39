package com.sankuai.shangou.qnh.orderapi.converter.pc;

import com.meituan.shangou.empower.uwmsplatform.enums.OrderBizType;
import com.meituan.shangou.empower.uwmsplatform.enums.OrderChannelEnum;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * 无人仓自己的渠道enum相关转换
 */
@Mapper
public interface UwmsOrderChannelConvert {
    UwmsOrderChannelConvert INSTANCE = Mappers.getMapper(UwmsOrderChannelConvert.class);

    /**
     * 将无人仓的{@link OrderChannelEnum} 转为{@link OrderBizType}
     */
    default OrderBizType convertUwmsOrderChannelEnum2OrderBizType(OrderChannelEnum uwmsOrderChannel) {
        Optional<OrderBizType> bizTypeOptional = Arrays.stream(OrderBizType.values())
                .filter(item -> Objects.equals(item.getOrderChannel(), uwmsOrderChannel))
                .findFirst();

        return bizTypeOptional.orElse(OrderBizType.UN_KNOW);
    }

    default OrderBizType convertUwmsOrderChannelCode2OrderBizType(int uwmsOrderChannelCode) {
        OrderChannelEnum uwmsOrderChannel = OrderChannelEnum.codeOf(uwmsOrderChannelCode);

        return this.convertUwmsOrderChannelEnum2OrderBizType(uwmsOrderChannel);
    }
}
