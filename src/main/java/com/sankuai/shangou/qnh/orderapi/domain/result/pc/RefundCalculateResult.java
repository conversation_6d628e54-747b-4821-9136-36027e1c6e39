package com.sankuai.shangou.qnh.orderapi.domain.result.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/5/29
 * desc: 克重退差试算结果
 */
@TypeDoc(
        description = "克重退差试算结果"
)
@ApiModel("克重退差试算结果")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RefundCalculateResult {

    @FieldDoc(
            description = "sku", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "sku", required = true)
    private String skuId;

    @FieldDoc(
            description = "预计退款金额", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "预计退款金额", required = true)
    private Integer refundMoney;
}
