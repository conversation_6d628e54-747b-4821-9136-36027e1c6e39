package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@TypeDoc(
        description = "修改门店商品自动上架时间请求"
)
@Data
@ApiModel("修改门店商品自动上架时间请求")
public class ChangeStoreSkuAutoUploadTimeRequest {

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID", required = true)
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "相对时间（从0点开始的毫秒数，比如1点对应3600000）", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "相对时间（从0点开始的毫秒数，比如1点对应3600000）", required = true)
    @NotNull
    private Long autoUploadTime;

}
