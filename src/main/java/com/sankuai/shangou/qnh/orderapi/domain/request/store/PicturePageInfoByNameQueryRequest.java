package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description:
 * @author: WangSuku<PERSON>
 * @create: 2019-11-05
 **/
@TypeDoc(
        description = "根据商品名称分页查询图片请求"
)
@Data
@ApiModel("根据商品名称分页查询图片请求")
public class PicturePageInfoByNameQueryRequest {

    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户ID")
    @NotNull
    private int tenantId;

    @FieldDoc(
            description = "分页页码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "分页页码")
    @NotNull
    private int pageNum;

    @FieldDoc(
            description = "每页数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "每页数量")
    @NotNull
    private int pageSize;

    @FieldDoc(
            description = "查询关键词", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "查询关键词")
    @NotNull
    private String skuName;

}
