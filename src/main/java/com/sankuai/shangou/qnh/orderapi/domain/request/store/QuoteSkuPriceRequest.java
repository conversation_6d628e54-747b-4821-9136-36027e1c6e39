package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@TypeDoc(
        description = "商品提报价请求"
)
@Data
@ApiModel("商品提报价请求")
public class QuoteSkuPriceRequest {

    @FieldDoc(
            description = "商品编号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品编号", required = true)
    @NotNull
    private String skuId;

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店id", required = true)
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "商品提报价", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品提报价", required = true)
    private String price;
}

