package com.sankuai.shangou.qnh.orderapi.domain.response.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@TypeDoc(
        description = "查询履约详情Id看板响应",
        authors = "xihaiyu"
)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FulfillReportDetailResponse {
    @FieldDoc(
            description = "履约报表"
    )
    public List<String> viewOrderIdList;
}

