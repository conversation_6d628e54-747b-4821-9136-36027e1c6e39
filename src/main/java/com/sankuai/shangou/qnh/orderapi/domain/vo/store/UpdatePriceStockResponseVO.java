package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "商品价格库存更新响应"
)
@Data
@ApiModel("商品价格库存更新响应")
public class UpdatePriceStockResponseVO {

    @FieldDoc(
            description = "错误码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "错误码", required = true)
    private Integer errorCode;

    @FieldDoc(
            description = "文案  在报价审核场景中返回给前端展示文案", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "文案", required = true)
    private String comment;

    @FieldDoc(
            description = "是否需要审核  0-不需要 1-需要", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "是否需要审核  0-不需要 1-需要", required = true)
    private Integer needReview;

    @FieldDoc(
            description = "错误记录集合", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "错误记录集合", required = true)
    private List<UpdateFailedRecordVO> errorRecordList;
}
