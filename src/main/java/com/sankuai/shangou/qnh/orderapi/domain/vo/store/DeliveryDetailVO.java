package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

@TypeDoc(
        description = "配送相关信息"
)
@Data
public class DeliveryDetailVO {
    @FieldDoc(
            description = "配送小费", requiredness = Requiredness.REQUIRED
    )
    private Double deliveryTipAmount = 0D;

    @FieldDoc(
            description = "异常viewCode", requiredness = Requiredness.REQUIRED
    )
    private Integer exceptionViewCode = 0;

    @FieldDoc(
            description = "异常Code", requiredness = Requiredness.REQUIRED
    )
    private Integer exceptionCode = 0;

    @FieldDoc(
            description = "异常最后操作时间", requiredness = Requiredness.REQUIRED
    )
    private Long allowLatestAuditTime;

    @FieldDoc(
            description = "配送费", requiredness = Requiredness.REQUIRED
    )
    private Double deliveryFeeAmount = 0D;

}
