package com.sankuai.shangou.qnh.orderapi.remote;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.ImmutableList;
import com.meituan.reco.pickselect.common.cat.CatTransaction;
import com.meituan.reco.pickselect.common.exception.FallbackException;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.ResponseCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.DeliveryConfigurationThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request.BatchStoreConfigQueryRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.BatchStoreConfigQueryResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.DeliveryOperationThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.DeliveryOrderUrlRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response.DeliveryOrderUrlResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.QueryDeliveryInfoThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.DeliveryOperateItem;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.QueryOrderDeliveryInfoKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryDetail;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TOrderIdentifier;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request.QueryDeliveryExceptionCountRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request.QueryDeliveryExceptionOrderByStoreIdsRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request.QueryDeliveryExceptionOrderBySubTypeAndStoreIdsRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request.QueryDeliveryExceptionOrderBySubTypeRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request.QueryDeliveryExceptionOrderRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request.QueryDeliveryExceptionRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request.QueryDeliveryInfoRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request.QueryDeliveryOperateItemRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.DeliveryExceptionOrderNumResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.DeliveryExceptionOrderSubTypeCountResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.DeliveryExceptionOrdersBySubTypeAndStoreIdsResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.DeliveryExceptionOrdersBySubTypeResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.DeliveryExceptionOrdersCountResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.DeliveryExceptionOrdersResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.QueryDeliveryInfoResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.QueryDeliveryOperateItemResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.QueryDeliveryOrderResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.QueryThirdDeliveryOrderCountResponse;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.OrderFuseQueryBO;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.QueryDeliveryErrorOrderBySubTypeRequest;
import com.sankuai.shangou.qnh.orderapi.enums.app.ResultCodeEnum;
import com.sankuai.shangou.qnh.orderapi.enums.pc.AggDeliveryPlatformEnum;
import com.sankuai.shangou.qnh.orderapi.exception.pc.CommonRuntimeException;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import com.sankuai.shangou.qnh.orderapi.utils.app.RpcInvokeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Rhino
@Service
@Slf4j
public class TmsRemoteService {

    @Autowired
    private QueryDeliveryInfoThriftService queryDeliveryInfoThriftService;

    @Resource
    private DeliveryOperationThriftService deliveryOperationThriftService;

    @Resource
    private DeliveryConfigurationThriftService deliveryConfigurationThriftService;

    @Degrade(rhinoKey = "TmsRemoteService.queryDeliveryErrorOrders",
            fallBackMethod = "queryDeliveryErrorOrdersFallback",
            timeoutInMilliseconds = 2000,
            isDegradeOnException = true)
    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public Pair<Integer, List<TOrderIdentifier>> queryDeliveryErrorOrders(QueryDeliveryErrorOrderBySubTypeRequest request) {
        QueryDeliveryExceptionRequest queryDeliveryExceptionRequest = new QueryDeliveryExceptionRequest();
        queryDeliveryExceptionRequest.setPage(request.getPage());
        queryDeliveryExceptionRequest.setSize(request.getSize());
        List<Long> poiIdList = request.getPoiIdList();
        List<Long> warehouseIdList = request.getWarehouseIdList();
        List<Long> poiIdsAndWarehouseIds;
        if (CollectionUtils.isNotEmpty(poiIdList) && CollectionUtils.isNotEmpty(warehouseIdList)) {
            poiIdsAndWarehouseIds = Stream.of(poiIdList, warehouseIdList).flatMap(Collection::stream).distinct().collect(Collectors.toList());
        } else {
            poiIdsAndWarehouseIds = CollectionUtils.isNotEmpty(poiIdList) ? poiIdList : warehouseIdList;
        }
        queryDeliveryExceptionRequest.setStoreIdList(poiIdsAndWarehouseIds);
        queryDeliveryExceptionRequest.setViewOrderId(request.getViewOrderId());
        queryDeliveryExceptionRequest.setTenantId(request.getTenantId());

        log.info("TmsRemoteService-queryDeliveryErrorOrders, req={}", queryDeliveryExceptionRequest);

        final DeliveryExceptionOrdersResponse response = queryDeliveryInfoThriftService.queryDeliveryExceptionOrdersByStoreIdList(queryDeliveryExceptionRequest);

        if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
            throw new CommonRuntimeException("请求TMS报错");

        }
        return Pair.of(response.getTotalCount(), response.getOrders());
    }

    @Degrade(rhinoKey = "TmsRemoteService.queryDeliveryErrorOrdersCount",
            fallBackMethod = "queryDeliveryErrorOrdersCountFallback",
            timeoutInMilliseconds = 2000,
            isDegradeOnException = true)
    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public Integer queryDeliveryErrorOrdersCount(OrderFuseQueryBO orderFuseQueryBO) {

        QueryDeliveryExceptionCountRequest queryDeliveryExceptionRequest = new QueryDeliveryExceptionCountRequest();

        List<Long> poiIdList = orderFuseQueryBO.getPoiIds();
        List<Long> warehouseIdList = orderFuseQueryBO.getWarehouseIds();

        List<Long> poiIdsAndWarehouseIds;
        if (CollectionUtils.isNotEmpty(poiIdList) && CollectionUtils.isNotEmpty(warehouseIdList)) {
            poiIdsAndWarehouseIds = Stream.of(poiIdList, warehouseIdList).flatMap(Collection::stream).distinct().collect(Collectors.toList());
        } else {
            poiIdsAndWarehouseIds = CollectionUtils.isNotEmpty(poiIdList) ? poiIdList : warehouseIdList;
        }

        queryDeliveryExceptionRequest.setStoreIdList(poiIdsAndWarehouseIds);
        queryDeliveryExceptionRequest.setViewOrderId(orderFuseQueryBO.getOrderId());
        queryDeliveryExceptionRequest.setTenantId(ContextHolder.currentUserTenantId());

        final DeliveryExceptionOrdersCountResponse response = queryDeliveryInfoThriftService.queryDeliveryExceptionCounts(queryDeliveryExceptionRequest);

        if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
            throw new CommonRuntimeException("请求TMS报错");

        }
        return response.getTotalCount();
    }

    private Pair<Integer, List<TOrderIdentifier>> queryDeliveryErrorOrdersFallback(QueryDeliveryErrorOrderBySubTypeRequest request) {
        throw new CommonRuntimeException("queryDeliveryErrorOrdersCount 请求TMS超时");
    }


    private Integer queryDeliveryErrorOrdersCountFallback(OrderFuseQueryBO orderFuseQueryBO){
        throw new CommonRuntimeException("queryDeliveryErrorOrdersCount 请求TMS超时");
    }


    @Degrade(rhinoKey = "TmsRemoteService.queryDeliveryInfo",
            fallBackMethod = "queryDeliveryInfoFallback",
            timeoutInMilliseconds = 2000)
    @CatTransaction
    public List<TDeliveryDetail> queryDeliveryInfoByOrderIds(List<QueryOrderDeliveryInfoKey> empowerOrderIds) {

        QueryDeliveryInfoRequest req = new QueryDeliveryInfoRequest(empowerOrderIds);
        log.info("TmsRemoteService-queryDeliveryInfoByOrderIds, req={}", empowerOrderIds);
        QueryDeliveryInfoResponse response = queryDeliveryInfoThriftService.queryDeliveryInfoByOrderKeys(req);
        log.info("TmsRemoteService-queryDeliveryInfoByOrderIds, response={}", response);
        return response.TDeliveryDetails;
    }


    @Degrade(rhinoKey = "TmsRemoteService.queryActiveDeliveryInfo",
            fallBackMethod = "queryActiveDeliveryInfoFallback",
            timeoutInMilliseconds = 2000)
    @CatTransaction
    public List<TDeliveryOrder> queryActiveDeliveryInfoByOrderIds(List<Long> orderList) {

        log.info("TmsRemoteService-queryDeliveryInfoByOrderIds, req={}", orderList);
        QueryDeliveryOrderResponse response = queryDeliveryInfoThriftService.queryActiveDeliveryOrderByOrderIdList(orderList);
        log.info("TmsRemoteService-queryDeliveryInfoByOrderIds, response={}", response);
        return response.getTDeliveryOrders();
    }

    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    @Degrade(rhinoKey = "TmsRemoteService.batchDeliveryStoreConfigSearch", fallBackMethod = "batchDeliveryStoreConfigSearchFallback",
            timeoutInMilliseconds = 1000)
    public BatchStoreConfigQueryResponse batchDeliveryStoreConfigSearch(Long tenantId, List<Long> storeIdList) {
        BatchStoreConfigQueryRequest storeConfigQueryRequest = new BatchStoreConfigQueryRequest();
        storeConfigQueryRequest.setStoreIdList(storeIdList);
        storeConfigQueryRequest.setTenantId(tenantId);
        storeConfigQueryRequest.setAggPlatformCodes(ImmutableList.of(AggDeliveryPlatformEnum.MALT_FARM.getCode(),AggDeliveryPlatformEnum.DAP_DELIVERY.getCode()));
        BatchStoreConfigQueryResponse batchStoreConfigQueryResponse = deliveryConfigurationThriftService.batchQueryStoreConfiguration(storeConfigQueryRequest);
        return batchStoreConfigQueryResponse;
    }

    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    @Degrade(rhinoKey = "TmsRemoteService.batchQueryOrderDeliveryUrl", fallBackMethod = "batchQueryOrderDeliveryUrlFallback",
            timeoutInMilliseconds = 1000)
    public Map<String,String> batchQueryOrderDeliveryUrl(Long tenantId, Long storeId, List<Long> orderIdList,List<String> marksIdList) {
        if (!MccConfigUtil.dapLinkNoAuthSwitch()) {
            return Collections.emptyMap();
        }
        if(CollectionUtils.isEmpty(orderIdList) && CollectionUtils.isEmpty(marksIdList)){
            return Collections.emptyMap();
        }
        DeliveryOrderUrlRequest orderUrlRequest = new DeliveryOrderUrlRequest();
        orderUrlRequest.setTenantId(tenantId);
        orderUrlRequest.setStoreId(storeId);
        orderUrlRequest.setOrderIdList(orderIdList);
        orderUrlRequest.setFulfillMarkIdList(marksIdList);
        DeliveryOrderUrlResponse response = deliveryOperationThriftService.queryOrderDeliveryUrl(orderUrlRequest);
        if(response==null || MapUtils.isEmpty(response.getOrderUrlMap())){
            return Collections.emptyMap();
        }
        return response.getOrderUrlMap();
    }


    private List<TDeliveryDetail> queryDeliveryInfoFallback(List<QueryOrderDeliveryInfoKey> empowerOrderIds, Throwable t) {
        log.error("queryDeliveryInfoFallback {} {}", empowerOrderIds, t.getMessage(), t);
        return Lists.newArrayList();
    }
    private List<TDeliveryOrder> queryActiveDeliveryInfoFallback(List<Long> orderIdList, Throwable t) {
        log.error("queryActiveDeliveryInfoFallback {} {}", orderIdList, t.getMessage(), t);
        return Lists.newArrayList();
    }

    private BatchStoreConfigQueryResponse batchDeliveryStoreConfigSearchFallback(Long tenantId, List<Long> storeIdList, Throwable t) {
        log.error("batchDeliveryStoreConfigSearchFallback {} {} {}", tenantId, storeIdList, t.getMessage(), t);
        throw new FallbackException("TmsRemoteService-batchDeliveryStoreConfigSearch 熔断降级");
    }

    private Map<String,String> batchQueryOrderDeliveryUrlFallback(Long tenantId, Long storeId, List<Long> orderIdList,List<String> marksIdList, Throwable t) {
        log.error("batchQueryOrderDeliveryUrl {} {}", storeId, t.getMessage(), t);
        throw new FallbackException("TmsRemoteService-batchQueryOrderDeliveryUrl 熔断降级");
    }

    @Degrade(rhinoKey = "TmsRemoteService.queryDeliveryErrorOrders",
            fallBackMethod = "queryDeliveryErrorOrdersFallback",
            timeoutInMilliseconds = 2000,
            isDegradeOnException = true)
    @CatTransaction
    public List<TOrderIdentifier> queryDeliveryErrorOrders(Long storeId,Long tenantId) {
        QueryDeliveryExceptionOrderRequest req = new QueryDeliveryExceptionOrderRequest(storeId,tenantId);
        log.info("TmsRemoteService-queryDeliveryExceptionOrders, req={}", req);
        DeliveryExceptionOrdersResponse response = queryDeliveryInfoThriftService.queryDeliveryExceptionOrders(req);
        log.info("TmsRemoteService-queryDeliveryExceptionOrders, response={}", response);
        if (response.getStatus().getCode() == ResponseCodeEnum.SUCCESS.getValue()) {
            return Optional.ofNullable(response.getOrders()).orElse(Lists.emptyList());
        } else {
            throw new com.sankuai.shangou.qnh.orderapi.exception.app.CommonRuntimeException(response.getStatus().getMsg(), ResultCodeEnum.FAIL);
        }
    }

    @Degrade(rhinoKey = "TmsRemoteService.countDeliveryErrorOrder",
            fallBackMethod = "countDeliveryErrorOrderFallback",
            timeoutInMilliseconds = 2000,
            isDegradeOnException = true)
    @CatTransaction
    public Integer countDeliveryErrorOrder(Long storeId,Long tenantId) {
        QueryDeliveryExceptionOrderRequest req = new QueryDeliveryExceptionOrderRequest(storeId,tenantId);
        log.info("TmsRemoteService-countDeliveryExceptionOrder, req={}", req);
        DeliveryExceptionOrderNumResponse response = queryDeliveryInfoThriftService.countDeliveryExceptionOrder(req);
        log.info("TmsRemoteService-countDeliveryExceptionOrder, response={}", response);
        if (response.getStatus().getCode() == ResponseCodeEnum.SUCCESS.getValue()) {
            return Optional.ofNullable(response.getOrderNum()).orElse(0);
        } else {
            throw new com.sankuai.shangou.qnh.orderapi.exception.app.CommonRuntimeException(response.getStatus().getMsg(), ResultCodeEnum.FAIL);
        }
    }

    @Degrade(rhinoKey = "TmsRemoteService.queryDeliveryErrorOrdersBySubType",
            fallBackMethod = "queryDeliveryErrorOrdersBySubTypeFallback",
            timeoutInMilliseconds = 2000,
            isDegradeOnException = true)
    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public List<TOrderIdentifier> queryDeliveryErrorOrdersBySubType(Integer subType, Long storeId,Long tenantId) {
        QueryDeliveryExceptionOrderBySubTypeRequest req = new QueryDeliveryExceptionOrderBySubTypeRequest(subType, storeId,tenantId);
        DeliveryExceptionOrdersBySubTypeResponse response = queryDeliveryInfoThriftService.queryDeliveryExceptionOrdersBySubType(req);
        if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
            throw new com.sankuai.shangou.qnh.orderapi.exception.app.CommonRuntimeException(response.getStatus().getMsg(), ResultCodeEnum.FAIL);

        }
        return Optional.ofNullable(response.getOrders()).orElse(Lists.emptyList());
    }

    @Degrade(rhinoKey = "TmsRemoteService.queryThirdDeliveryOrderCountByStatusList",
            fallBackMethod = "queryThirdDeliveryOrderCountByStatusListFallBack",
            timeoutInMilliseconds = 2000,
            isDegradeOnException = true)
    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public QueryThirdDeliveryOrderCountResponse queryThirdDeliveryOrderCountByStatusList(Long tenantId, Long storeId) {
        return RpcInvokeUtils.rpcInvokeTemplate(() -> queryDeliveryInfoThriftService.queryThirdDeliveryOrderCount(tenantId, storeId),
                "查询运单数量失败",
                resp -> resp.getStatus().getCode(),
                resp -> resp.getStatus().getMsg());
    }

    public  QueryThirdDeliveryOrderCountResponse queryThirdDeliveryOrderCountByStatusListFallBack(Long tenantId, Long storeId) {
        return new QueryThirdDeliveryOrderCountResponse(Status.SUCCESS, 0, 0, 0, 0, 0);
    }

    @Degrade(rhinoKey = "TmsRemoteService.queryDeliveryErrorOrdersBySubTypeAndStores",
            fallBackMethod = "queryDeliveryErrorOrdersBySubTypeAndStoresFallback",
            timeoutInMilliseconds = 2000,
            isDegradeOnException = true)
    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public DeliveryExceptionOrdersBySubTypeAndStoreIdsResponse queryDeliveryErrorOrdersBySubTypeAndStores(int pageNum, int pageSize, Integer subType, List<Long> storeIds) {
        QueryDeliveryExceptionOrderBySubTypeAndStoreIdsRequest req = new QueryDeliveryExceptionOrderBySubTypeAndStoreIdsRequest();
        req.setSubType(subType);
        req.setEmpowerStoreIds(storeIds);
        req.setPageNum(pageNum);
        req.setPageSize(pageSize);
        DeliveryExceptionOrdersBySubTypeAndStoreIdsResponse response = queryDeliveryInfoThriftService.queryDeliveryExceptionOrdersBySubTypeAndStoreIds(req);
        log.info("queryDeliveryErrorOrdersBySubTypeAndStores  response: {}", JSONObject.toJSONString(response));
        if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
            throw new com.sankuai.shangou.qnh.orderapi.exception.app.CommonRuntimeException(response.getStatus().getMsg(), ResultCodeEnum.FAIL);
        }
        return response;
    }

    // 查询多门店的配送异常订单标识
    public List<TOrderIdentifier> queryDeliveryErrorOrdersBySubTypeAndStoresWrap(Integer subType, List<Long> storeIds) {
        // tms限制pageSize<=100；
        final int pageSize = 100;
        List<TOrderIdentifier> orderIdentifiers = Lists.newArrayList();
        for (int pageNum = 1; pageNum < MccConfigUtil.getOrderDeliveryErrorBySubTypeQueryTmsRoundLimit(); pageNum++) {
            List<TOrderIdentifier> pageOrderIdentifiers = queryDeliveryErrorOrdersBySubTypeAndStores(pageNum, pageSize, subType, storeIds).getOrders();
            if (CollectionUtils.isNotEmpty(pageOrderIdentifiers)) {
                orderIdentifiers.addAll(pageOrderIdentifiers);
            }
            if (pageOrderIdentifiers.size() < pageSize) {
                break;
            }
        }
        return orderIdentifiers;
    }

//    @Degrade(rhinoKey = "TmsRemoteService.queryDeliveryExceptionOrderSubTypeCount",
//            fallBackMethod = "queryDeliveryExceptionOrderSubTypeCountFallback",
//            timeoutInMilliseconds = 2000,
//            isDegradeOnException = true)
//    @CatTransaction
//    @MethodLog(logRequest = true, logResponse = true)
//    public DeliveryExceptionOrderSubTypeCountResponse queryDeliveryExceptionOrderSubTypeCount(Long storeId) {
//        QueryDeliveryExceptionOrderRequest req = new QueryDeliveryExceptionOrderRequest(storeId);
//        DeliveryExceptionOrderSubTypeCountResponse response = queryDeliveryInfoThriftService.queryDeliveryExceptionOrderSubTypeCount(req);
//        if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
//            throw new com.sankuai.shangou.qnh.orderapi.exception.app.CommonRuntimeException(response.getStatus().getMsg(), ResultCodeEnum.FAIL);
//        }
//        return response;
//    }

    @Degrade(rhinoKey = "TmsRemoteService.countDeliveryExceptionOrCanceledOrder",
            fallBackMethod = "countDeliveryExceptionOrCanceledOrderFallback",
            timeoutInMilliseconds = 2000,
            isDegradeOnException = true)
    @CatTransaction
    public Integer countDeliveryExceptionOrCanceledOrder(Long storeId,Long tenantId) {
        QueryDeliveryExceptionOrderRequest req = new QueryDeliveryExceptionOrderRequest(storeId,tenantId);
        log.info("TmsRemoteService-countDeliveryExceptionOrCanceledOrder, req={}", req);
        DeliveryExceptionOrderNumResponse response = queryDeliveryInfoThriftService.countDeliveryExceptionOrCanceledOrder(req);
        log.info("TmsRemoteService-countDeliveryExceptionOrCanceledOrder, response={}", response);
        if (response.getStatus().getCode() == ResponseCodeEnum.SUCCESS.getValue()) {
            return Optional.ofNullable(response.getOrderNum()).orElse(0);
        } else {
            throw new com.sankuai.shangou.qnh.orderapi.exception.app.CommonRuntimeException(response.getStatus().getMsg(), ResultCodeEnum.FAIL);
        }
    }

    private Integer countDeliveryExceptionOrCanceledOrderFallback(Long storeId,Long tenantId) {
        log.error("countDeliveryExceptionOrCanceledOrderFallback {}", storeId);
        throw new com.sankuai.shangou.qnh.orderapi.exception.app.FallbackException("TmsServiceWrapper-countDeliveryExceptionOrCanceledOrderFallback 熔断降级");
    }


    @Degrade(rhinoKey = "TmsRemoteService.queryDeliveryExceptionOrderSubTypeAndStoresCount",
            fallBackMethod = "queryDeliveryExceptionOrderSubTypeAndStoresCountFallback",
            timeoutInMilliseconds = 2000,
            isDegradeOnException = true)
    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public DeliveryExceptionOrderSubTypeCountResponse queryDeliveryExceptionOrderSubTypeAndStoresCount(List<Long> storeIds) {
        QueryDeliveryExceptionOrderByStoreIdsRequest req = new QueryDeliveryExceptionOrderByStoreIdsRequest(storeIds);
        DeliveryExceptionOrderSubTypeCountResponse response = queryDeliveryInfoThriftService.queryDeliveryExceptionOrderSubTypeCountByStoreIds(req);
        if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
            throw new com.sankuai.shangou.qnh.orderapi.exception.app.CommonRuntimeException(response.getStatus().getMsg(), ResultCodeEnum.FAIL);
        }
        return response;
    }

    @Degrade(rhinoKey = "TmsRemoteService.countDeliveryExceptionOrderByStoreIds",
            fallBackMethod = "countDeliveryExceptionOrderByStoreIdsFallback",
            timeoutInMilliseconds = 2000,
            isDegradeOnException = true)
    @CatTransaction
    public Integer countDeliveryExceptionOrderByStoreIds(List<Long> empowerStoreIds) {
        QueryDeliveryExceptionOrderByStoreIdsRequest req = new QueryDeliveryExceptionOrderByStoreIdsRequest(empowerStoreIds);
        log.info("TmsRemoteService-countDeliveryExceptionOrderByStoreIds, req={}", req);
        DeliveryExceptionOrderNumResponse response = queryDeliveryInfoThriftService.countDeliveryExceptionOrderByStoreIds(req);
        log.info("TmsRemoteService-countDeliveryExceptionOrderByStoreIds, response={}", response);
        if (response.getStatus().getCode() == ResponseCodeEnum.SUCCESS.getValue()) {
            return Optional.ofNullable(response.getOrderNum()).orElse(0);
        } else {
            throw new com.sankuai.shangou.qnh.orderapi.exception.app.CommonRuntimeException(response.getStatus().getMsg(), ResultCodeEnum.FAIL);
        }
    }

    private List<TOrderIdentifier> queryDeliveryErrorOrdersFallback(Long storeId,Long tenantId, Throwable t) {
        log.error("queryDeliveryErrorOrdersFallback {} {}", storeId, t.getMessage(), t);
        throw new com.sankuai.shangou.qnh.orderapi.exception.app.FallbackException("TmsServiceWrapper-queryDeliveryErrorOrders 熔断降级");
    }

    private Integer countDeliveryErrorOrderFallback(Long storeId,Long tenantId, Throwable t) {
        log.error("countDeliveryErrorOrderFallback {} {}", storeId, t.getMessage(), t);
        throw new com.sankuai.shangou.qnh.orderapi.exception.app.FallbackException("TmsServiceWrapper-countDeliveryErrorOrder 熔断降级");
    }

    private List<TOrderIdentifier> queryDeliveryErrorOrdersBySubTypeFallback(Integer subType, Long storeId,Long tenantId, Throwable t) {
        log.error("queryDeliveryErrorOrdersBySubTypeFallback {} {} {}", subType, storeId, t.getMessage(), t);
        throw new com.sankuai.shangou.qnh.orderapi.exception.app.FallbackException("TmsServiceWrapper-queryDeliveryErrorOrdersBySubType 熔断降级");
    }

    private List<TOrderIdentifier> queryDeliveryErrorOrdersBySubTypeAndStoresFallback(int pageNum, int pageSize, Integer subType, List<Long> storeIds, Throwable t) {
        log.error("queryDeliveryErrorOrdersBySubTypeAndStoresFallback pageNum:{}, pageSize:{}, subType:{}, storeIds:{}; {}", pageNum, pageSize, subType, storeIds, t.getMessage(), t);
        throw new com.sankuai.shangou.qnh.orderapi.exception.app.FallbackException("TmsServiceWrapper-queryDeliveryErrorOrdersBySubTypeAndStores 熔断降级");
    }

    private DeliveryExceptionOrderSubTypeCountResponse queryDeliveryExceptionOrderSubTypeCountFallback(Long storeId, Throwable t) {
        log.error("queryDeliveryExceptionOrderSubTypeCountFallback {} {}", storeId, t.getMessage(), t);
        throw new com.sankuai.shangou.qnh.orderapi.exception.app.FallbackException("TmsServiceWrapper-queryDeliveryExceptionOrderSubTypeCount 熔断降级");
    }

    private DeliveryExceptionOrderSubTypeCountResponse queryDeliveryExceptionOrderSubTypeAndStoresCountFallback(List<Long> storeIds, Throwable t) {
        log.error("queryDeliveryExceptionOrderSubTypeAndStoresCountFallback {} {}", storeIds, t.getMessage(), t);
        throw new com.sankuai.shangou.qnh.orderapi.exception.app.FallbackException("TmsServiceWrapper-queryDeliveryExceptionOrderSubTypeAndStoresCount 熔断降级");
    }

    private Integer countDeliveryExceptionOrderByStoreIdsFallback(List<Long> empowerStoreIds,  Throwable t) {
        log.error("deliveryStoreConfigModify {} {}", empowerStoreIds, t.getMessage(), t);
        throw new com.sankuai.shangou.qnh.orderapi.exception.app.FallbackException("TmsServiceWrapper-countDeliveryExceptionOrderByStoreIds 熔断降级");
    }

    @Degrade(rhinoKey = "TmsRemoteService.queryDeliveryOperateItem",
            fallBackMethod = "queryDeliveryOperateItemFallback",
            timeoutInMilliseconds = 2000,
            isDegradeOnException = true)
    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public Map<Long, DeliveryOperateItem> queryDeliveryOperateItem(Long tenantId, Long storeId, List<Long> orderIdList){
        QueryDeliveryOperateItemRequest request = new QueryDeliveryOperateItemRequest();
        request.setTenantId(tenantId);
        request.setStoreId(storeId);
        request.setOrderIdList(orderIdList);
        log.info("TmsRemoteService.queryDeliveryOperateItem request: {}", JSON.toJSONString(request));
        QueryDeliveryOperateItemResponse response = queryDeliveryInfoThriftService.queryDeliveryOperateItem(request);
        log.info("queryDeliveryOperateItem response:{}",response);
        if(response==null || response.getStatus()==null || response.getStatus().getCode()!= Status.SUCCESS.getCode()){
            throw new com.sankuai.shangou.qnh.orderapi.exception.app.CommonRuntimeException("列表获取失败", ResultCodeEnum.FAIL);
        }
        return response.getOperateItemMap();
    }

    public Map<Long, DeliveryOperateItem> queryDeliveryOperateItemFallback(Long tenantId,Long storeId,List<Long> orderIdList, Throwable t) {
        log.error("queryDeliveryOperateItemFallback {} {}", storeId, t.getMessage(), t);
        throw new com.sankuai.shangou.qnh.orderapi.exception.app.FallbackException("TmsServiceWrapper-queryDeliveryOperateItem 熔断降级");
    }
}
