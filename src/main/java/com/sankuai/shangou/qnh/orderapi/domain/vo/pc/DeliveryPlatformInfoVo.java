package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@TypeDoc(
        description = "配送管理平台信息"
)
@ApiModel("配送管理平台信息")
@Data
public class DeliveryPlatformInfoVo {

    @FieldDoc(
            description = "平台code", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "平台code", required = true)
    @NotNull(message = "平台code为空")
    private Integer platformCode;

    @FieldDoc(
            description = "是否开通、1开通、0关闭", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否开通、1开通、0关闭", required = true)
    @NotNull(message = "状态信息为空")
    private Integer status;

    @FieldDoc(
            description = "渠道类型", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "渠道类型")
    private Integer channelType;

    @FieldDoc(
            description = "配送发单节点"
    )
    @ApiModelProperty(value = "配送发单节点")
    private Integer deliveryLaunchPoint;

}
