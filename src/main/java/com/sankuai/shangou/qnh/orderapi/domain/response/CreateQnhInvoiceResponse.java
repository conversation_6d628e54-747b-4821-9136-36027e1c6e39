package com.sankuai.shangou.qnh.orderapi.domain.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;


@TypeDoc(
        description = "开发票返回值"
)
@ApiModel("查询数据权限组全部列表回参")
@Data
@Builder
public class CreateQnhInvoiceResponse {
    @FieldDoc(
            description = "发票链接"
    )
    @ApiModelProperty(name = "发票链接")
    private String invoiceUrl;

    @FieldDoc(
            description = "发票来源"
    )
    @ApiModelProperty(name = "发票来源")
    private Integer invoiceSource;

    @FieldDoc(
            description = "发票来源描述"
    )
    @ApiModelProperty(name = "发票来源描述")
    private String invoiceSourceDesc;
}
