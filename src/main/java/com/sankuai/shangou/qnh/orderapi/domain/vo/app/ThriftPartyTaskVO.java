package com.sankuai.shangou.qnh.orderapi.domain.vo.app;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @createTime 2023/08/23
 * @description
 */
@TypeDoc(
        description = "三方模块代办"
)
@Data
@ApiModel("三方模块代办")
public class ThriftPartyTaskVO {

    @FieldDoc(
            description = "三方待领取数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "三方待领取数量", required = true)
    @NotNull
    private Integer thirdWaitToTakeCount;


    @FieldDoc(
            description = "三方待拣货数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "三方待拣货数量", required = true)
    @NotNull
    private Integer thirdWaitToPickCount;


    @FieldDoc(
            description = "三方待发配送数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "三方待发配送数量", required = true)
    @NotNull
    private Integer thirdWaitToLaunchDeliveryCount;



    @FieldDoc(
            description = "三方待接单数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "三方待接单数量", required = true)
    @NotNull
    private Integer thirdWaitToAcceptDeliveryCount;


    @FieldDoc(
            description = "三方待取货数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "三方待取货数量", required = true)
    @NotNull
    private Integer thirdWaitToPickUpDeliveryCount;


    @FieldDoc(
            description = "三方配送中数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "三方配送中数量", required = true)
    @NotNull
    private Integer thirdOnDeliveryCount;

    @FieldDoc(
            description = "三方异常数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "三方异常数量", required = true)
    @NotNull
    private Integer thirdExceptionCount;


    public static ThriftPartyTaskVO thriftPartyInit(){
        ThriftPartyTaskVO thriftPartyTaskVO = new ThriftPartyTaskVO();
        thriftPartyTaskVO.setThirdWaitToTakeCount(0);
        thriftPartyTaskVO.setThirdWaitToPickCount(0);
        thriftPartyTaskVO.setThirdWaitToLaunchDeliveryCount(0);
        thriftPartyTaskVO.setThirdWaitToAcceptDeliveryCount(0);
        thriftPartyTaskVO.setThirdWaitToPickUpDeliveryCount(0);
        thriftPartyTaskVO.setThirdOnDeliveryCount(0);
        thriftPartyTaskVO.setThirdExceptionCount(0);
        return thriftPartyTaskVO;
    }

}
