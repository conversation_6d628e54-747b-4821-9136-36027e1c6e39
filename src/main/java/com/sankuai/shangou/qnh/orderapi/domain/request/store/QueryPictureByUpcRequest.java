package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description: 根据UPC列表查询图片
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2019-11-04
 **/
@TypeDoc(
        description = "根据UPC列表查询图片请求"
)
@Data
@ApiModel("根据UPC列表查询图片请求")
public class QueryPictureByUpcRequest {


    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户ID")
    @NotNull
    private Long tenantId;



    @FieldDoc(
            description = "upc", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "upc")
    @NotNull
    private String upc;

}
