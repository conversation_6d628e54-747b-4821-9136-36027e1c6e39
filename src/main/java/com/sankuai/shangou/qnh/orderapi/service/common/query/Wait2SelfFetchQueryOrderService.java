package com.sankuai.shangou.qnh.orderapi.service.common.query;

import com.meituan.shangou.saas.order.management.client.dto.request.revenue.OCMSListOrderRevenueDetailRequest;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OCMSListOrderResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.meituan.shangou.saas.order.management.client.enums.QueryOrderTypeQuantityEnum;
import com.meituan.shangou.saas.order.management.client.service.revenue.MerChantRevenueQueryService;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.OrderListResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.OrderVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.PageInfoVO;
import com.sankuai.shangou.qnh.orderapi.enums.app.OrderViewStatusEnum;
import com.sankuai.shangou.qnh.orderapi.enums.app.ResultCodeEnum;
import com.sankuai.shangou.qnh.orderapi.service.common.OrderListRequestContext;
import com.sankuai.shangou.qnh.orderapi.service.common.OrderRequestBuilder;
import com.sankuai.shangou.qnh.orderapi.service.common.PageUtil;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2024/7/17
 **/
@Service
@Slf4j
public class Wait2SelfFetchQueryOrderService extends QueryOrderService {

    @Resource
    private OrderRequestBuilder orderRequestBuilder;
    @Resource
    private MerChantRevenueQueryService merChantRevenueQueryService;

    @Override
    public Pair<List<OCMSOrderVO>, PageInfoVO> queryOrderInfo(OrderListRequestContext request) {
        OCMSListOrderRevenueDetailRequest ocmsListOrderRequest = orderRequestBuilder.queryWaitSelfFetchOrderRequest(request);
        log.info("调用ocmsQueryThriftService.listOrder request:{}", ocmsListOrderRequest);
        OCMSListOrderResponse response = merChantRevenueQueryService.listOrderRevenueDetail(ocmsListOrderRequest);
        log.info("调用ocmsQueryThriftService.listOrder response:{}", response);
        if (response.getStatus().getCode() != com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum.SUCCESS.getValue()) {
            throw new BizException(ResultCodeEnum.FAIL.getCode(), response.getStatus().getMessage());
        }
        PageInfoVO pageInfoVO = PageUtil.buildPageInfoVO(request.getPage(), request.getSize(), response.getTotalCount());
        return new Pair<>(response.getOcmsOrderList(), pageInfoVO);
    }

    @Override
    public Integer countAll(Long tenantId, List<Long> storeIdList, Integer entityType) {
        return queryOrderCountByTab(QueryOrderTypeQuantityEnum.WAIT_SELF_FETCH, tenantId, storeIdList, entityType);
    }


    @Override
    public void addExtraInfo(OrderListResponse orderListResponse, OrderListRequestContext request) {
        setOrderListResponseViewStatus(orderListResponse, OrderViewStatusEnum.WAIT_TO_SELF_FETCH);
        addOrderDeliveryStatusChangeTimeWithPayTime(orderListResponse.getOrderList());

    }




}
