package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

/**
 * @Author: wb_zhou<PERSON><EMAIL>
 * @Date: 2024/7/3 15:57
 * @Description:
 */
@Setter
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(
        description = "拣货员查询请求"
)
public class QueryShopPickerRequest implements BaseRequest {


    /**
     * 渠道订单号
     */
    @FieldDoc(
            description = "渠道订单号"
    )
    private String channelOrderId;

    /**
     * 渠道id
     */
    @FieldDoc(
            description = "渠道id"
    )
    private Integer channelId;

    @FieldDoc(
            description = "0:下发拣货任务/拣货员转派，1分配拣货员，2更换拣货员"
    )
    private Integer type = 0;


    @Override
    public void selfCheck() {
        AssertUtil.isTrue(StringUtils.isNotEmpty(channelOrderId), "渠道订单号不可为空");

        AssertUtil.isTrue(channelId != null && channelId > 0 , "渠道id不可为空要大于0");

    }
}
