package com.sankuai.shangou.qnh.orderapi.domain.vo.app;


import com.facebook.swift.codec.ThriftField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: <EMAIL>
 * @Date: 2024-06-15 14:57
 * @Description:
 */
@TypeDoc(
        description = "订单赔付信息"
)
@ApiModel("订单赔付信息")
@Data
public class CompensationVO {

    @FieldDoc(
            description = "赔付类型，类型见OrderCompensateTypeEnum", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "赔付类型，类型见OrderCompensateTypeEnum")
    public Integer compensateType;

    @FieldDoc(
            description = "赔付金额", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "赔付金额")
    public Integer amount;

    @FieldDoc(
            description = "原因", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "原因")
    public String reason;

    @FieldDoc(
            description = "赔付时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "赔付时间")
    public Long compensateTime;

    @FieldDoc(
            description = "责任方,详情见responsiblePartyEnum", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "责任方,详情见responsiblePartyEnum")
    public Integer responsibleParty;
}
