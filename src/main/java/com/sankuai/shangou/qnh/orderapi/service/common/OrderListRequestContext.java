package com.sankuai.shangou.qnh.orderapi.service.common;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.shangou.saas.o2o.dto.model.OrderBizTypeAndViewOrderIdModel;
import com.sankuai.meituan.reco.pickselect.query.dto.OrderPickDetailDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TOrderIdentifier;
import com.sankuai.shangou.qnh.orderapi.converter.app.DeliveryTypeModeConverter;
import com.sankuai.shangou.qnh.orderapi.domain.dto.app.IdentityInfo;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.*;
import com.sankuai.shangou.qnh.orderapi.domain.request.pda.QueryUnfinishedOrderRequest;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 * @since 2024/7/17
 **/
@Data
public class OrderListRequestContext {

    private Long tenantId;

    private Long storeId;
    /**
     * 全门店模式
     */
    private List<Long> storeIdList;

    @FieldDoc(
            description = "第几页", requiredness = Requiredness.REQUIRED
    )
    private Integer page;

    @FieldDoc(
            description = "每页行数", requiredness = Requiredness.REQUIRED
    )
    private Integer size;

    @FieldDoc(
            description = "门店类型 3-门店 5-中心仓 6-前置仓", requiredness = Requiredness.OPTIONAL
    )
    private Integer entityType;

    private Integer subType;

    private boolean showSalePrice;

    private boolean hasMaltFarmPermission;

    private Object request;

    private IdentityInfo identityInfo;

    //配送异常信息
    private List<TOrderIdentifier> tOrderIdentifiers;

    //待拣货信息
    private List<OrderPickDetailDto> waitToPickOrderList;

    //订单变化信息
    private List<OrderBizTypeAndViewOrderIdModel> changedOrderModelList;

    // 订单-待拣货列表排序类型（0：默认，1：下单时间从早到晚，2：下单时间从晚到早，3：剩余领取时长优先，4：剩余拣货时长优先，5：预计送达时间优先）
    private Integer sortType;

    /**
     * 自配送类型列表
     */
    private List<Integer> deliveryChannelIdList;

    /**
     * 平台配送类型列表
     */
    private List<Integer> originalDistributeTypeList;

    private static OrderListRequestContext buildBasicContext(IdentityInfo identityInfo) {
        OrderListRequestContext context = new OrderListRequestContext();
        Long tenantId = identityInfo.getUser().getTenantId();
        Long storeId = identityInfo.getStoreId();
        context.setTenantId(tenantId);
        context.setIdentityInfo(identityInfo);
        context.setStoreId(storeId);
        context.setStoreIdList(identityInfo.getStoreIdList());
        return context;
    }


    public static OrderListRequestContext buildWaitToAuditRefund(IdentityInfo identityInfo, QueryUnfinishedOrderRequest request) {
        OrderListRequestContext context = buildBasicContext(identityInfo);
        context.setPage(request.getPage());
        context.setSize(request.getSize());
        context.setEntityType(request.getEntityType());
        context.setRequest(request);
        context.setIdentityInfo(identityInfo);
        context.setSubType(request.getSubType());
        return context;
    }

    public static OrderListRequestContext buildOrderDetailRequest(IdentityInfo identityInfo, QueryOrderDetailRequest request) {
        OrderListRequestContext context = buildBasicContext(identityInfo);
        context.setPage(1);
        context.setSize(1);
        context.setRequest(request);
        context.setIdentityInfo(identityInfo);
        return context;
    }


    public static OrderListRequestContext buildWaitToAuditRefund(IdentityInfo identityInfo, Integer page, Integer size, Integer entityType) {
        OrderListRequestContext context = buildBasicContext(identityInfo);
        context.setPage(page);
        context.setSize(size);
        context.setIdentityInfo(identityInfo);
        context.setEntityType(entityType);
        return context;
    }

    public static OrderListRequestContext buildWaitToAuditRefund(IdentityInfo identityInfo, Integer page, Integer size, Integer entityType, Integer subType) {
        OrderListRequestContext context = buildBasicContext(identityInfo);
        context.setPage(page);
        context.setSize(size);
        context.setIdentityInfo(identityInfo);
        context.setEntityType(entityType);
        context.setSubType(subType);
        return context;
    }

    public static OrderListRequestContext buildWaitToAuditRefund(IdentityInfo identityInfo, OrderListRequest request) {
        OrderListRequestContext context = buildBasicContext(identityInfo);
        context.setPage(request.getPage());
        context.setSize(request.getPageSize());
        context.setIdentityInfo(identityInfo);
        context.setEntityType(request.getEntityType());
        context.setRequest(request);
        return context;
    }

    public static OrderListRequestContext buildWaitToAuditRefund(IdentityInfo identityInfo, ChangedOrderListForAppLocalCacheRequest request) {
        OrderListRequestContext context = buildBasicContext(identityInfo);
        context.setPage(request.getPageNo());
        context.setSize(request.getPageSize());
        context.setIdentityInfo(identityInfo);
        context.setEntityType(request.getEntityType());
        context.setRequest(request);
        return context;
    }

    public static OrderListRequestContext buildWaitToAuditRefund(IdentityInfo identityInfo, OrderListForAppLocalCacheRequest request) {
        OrderListRequestContext context = buildBasicContext(identityInfo);
        context.setPage(request.getPageNo());
        context.setSize(request.getPageSize());
        context.setIdentityInfo(identityInfo);
        context.setEntityType(request.getEntityType());
        context.setRequest(request);
        return context;
    }

    public static OrderListRequestContext buildWaitToAuditRefund(IdentityInfo identityInfo, QueryDeliveryErrorOrderBySubTypeRequest request) {
        OrderListRequestContext context = buildBasicContext(identityInfo);
        context.setPage(request.getPage());
        context.setSize(request.getSize());
        context.setIdentityInfo(identityInfo);
        context.setEntityType(5);
        context.setSubType(request.getSubType());
        context.setRequest(request);
        return context;
    }

    public static OrderListRequestContext buildWaitToAuditRefund(IdentityInfo identityInfo, QueryWaitToDeliveryOrderBySubTypeRequest request) {
        OrderListRequestContext context = buildBasicContext(identityInfo);
        context.setPage(request.getPage());
        context.setSize(request.getSize());
        context.setIdentityInfo(identityInfo);
        context.setEntityType(request.getEntityType());
        context.setSubType(request.getSubType());
        // 配送模式参数处理
        List<Integer> deliveryTypeModeList = request.getDeliveryTypeModeList();
        if (CollectionUtils.isNotEmpty(deliveryTypeModeList)) {
            javafx.util.Pair<List<Integer>, List<Integer>> deliveryParamPair = DeliveryTypeModeConverter
                    .getDeliveryParam(deliveryTypeModeList);
            // 平台配送
            if (CollectionUtils.isNotEmpty(deliveryParamPair.getKey())) {
                context.setOriginalDistributeTypeList(deliveryParamPair.getKey());
            }
            // 商家自送和聚合配送
            if (CollectionUtils.isNotEmpty(deliveryParamPair.getValue())) {
                context.setDeliveryChannelIdList(deliveryParamPair.getValue());
            }
        }
        context.setRequest(request);
        return context;
    }

    public static OrderListRequestContext buildSelectListByOrderIds(IdentityInfo identityInfo, OrderListQueryRequest request) {
        OrderListRequestContext context = buildBasicContext(identityInfo);
        context.setPage(1);
        context.setSize(request.getOrderList().size());
        context.setIdentityInfo(identityInfo);
        context.setEntityType(request.getEntityType());
        context.setRequest(request);
        return context;
    }

    public boolean isPadClient(){
        return Objects.nonNull(identityInfo) && Objects.equals(identityInfo.getAppId(), "585");
    }

    public boolean isAppClient(){
        return Objects.nonNull(identityInfo) && Objects.equals(identityInfo.getAppId(), "272");
    }



}
