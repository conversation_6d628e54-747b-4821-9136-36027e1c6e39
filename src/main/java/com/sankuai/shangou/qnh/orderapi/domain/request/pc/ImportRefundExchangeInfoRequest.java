package com.sankuai.shangou.qnh.orderapi.domain.request.pc;


import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.dto.request.SelfCheckable;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.util.List;

@Setter
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(
        description = "换货录入"
)
public class ImportRefundExchangeInfoRequest  implements BaseRequest {

    public Long orderId;

    public Long serviceId;

    public Long tenantId;

    public List<ExchangeRefundImportDetailRequest> detailRequestList;

    public Integer channelId;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ExchangeRefundImportDetailRequest implements SelfCheckable {
        public Long orderItemId;

        public Long afsItemId;

        @FieldDoc(
                description = "换货商品ID,父商品换货为OrderItemId"
        )
        public Long exchangeOrderItemId;

        @FieldDoc(
                description = "推款数量"
        )

        public Double refundCount;

        @FieldDoc(
                description = "退款数量"
        )

        private Double refundAmt;

        @FieldDoc(
                description = "组合商品子商品唯一id"
        )

        public Long exchangeFromServiceId;

        @FieldDoc(
                description = "组合商品子商品换货serviceid"
        )

        public Long exchangeServiceId;


        @FieldDoc(
                description = "1:原商品,2换货商品"
        )
        public Integer itemType;
    }

}
