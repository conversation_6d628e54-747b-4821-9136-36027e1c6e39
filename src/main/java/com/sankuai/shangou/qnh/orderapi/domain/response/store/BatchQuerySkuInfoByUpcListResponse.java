package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.SkuBasicInfoVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: WangSukuan
 * @create: 2019-12-02
 **/
@TypeDoc(
        description = "通过UPC列表查询标品信息响应"
)
@Data
@ApiModel("通过UPC列表查询标品信息响应")
public class BatchQuerySkuInfoByUpcListResponse {

    @FieldDoc(
            description = "当前页内容", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "当前页内容", required = true)
    private List<SkuBasicInfoVo> skuList;

}
