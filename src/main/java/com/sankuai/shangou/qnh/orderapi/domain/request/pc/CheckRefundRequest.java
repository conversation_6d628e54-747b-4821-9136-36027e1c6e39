package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;


@TypeDoc(
        description = "检查退款请求"
)
@ApiModel("检查退款请求")
@Data
public class CheckRefundRequest {

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID")
    @NotNull
    private Integer channelId;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道订单号")
    @NotNull
    public String channelOrderId;

    @FieldDoc(
            description = "退款类型 1-全单退款 2-部分退款", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "退款类型 1-全单退款 2-部分退款")
    @NotNull
    private Integer refundType;

    @FieldDoc(
            description = "操作类型，不传默认是发起退款和退货退款的二审驳回。2：驳回"
    )
    @ApiModelProperty(name = "操作类型，不传默认是发起退款和退货退款的二审驳回。2：驳回")
    public Integer refundOperationType;

    @FieldDoc(
            description = "售后单号"
    )
    @ApiModelProperty(name = "售后单号")
    public String afterSaleId;
}
