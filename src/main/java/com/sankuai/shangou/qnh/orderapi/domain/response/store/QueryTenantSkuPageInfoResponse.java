package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.TenantSku;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/6/12
 * desc:
 */
@TypeDoc(
        description = "分页查询租户商品列表响应"
)
@Data
@ApiModel("分页查询租户商品列表响应")
public class QueryTenantSkuPageInfoResponse {

    @FieldDoc(
            description = "租户商品列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "租户商品列表")
    private List<TenantSku> tenantSkuList;

    @FieldDoc(
            description = "是否有下一页 1-是  0-否", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否有下一页")
    private Integer hasMore;

    @FieldDoc(
            description = "总页数", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "总页数")
    private Integer totalPageCount;

    @FieldDoc(
            description = "总条数", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "总条数")
    private Integer totalCount;
}
