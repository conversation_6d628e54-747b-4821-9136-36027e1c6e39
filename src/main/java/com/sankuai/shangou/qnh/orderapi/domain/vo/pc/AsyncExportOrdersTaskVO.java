package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2019-10-31 21:30
 * @Description:
 */
@TypeDoc(
        description = "异步导出订单详情结果"
)
@Data
public class AsyncExportOrdersTaskVO {
    /**
     * 任务token
     */
    private String token;
    /**
     * 任务状态
     */
    private Integer taskStatus;
    /**
     * url列表
     */
    private List<String> fileUrlList;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 完成时间
     */
    private String finishTime;
}
