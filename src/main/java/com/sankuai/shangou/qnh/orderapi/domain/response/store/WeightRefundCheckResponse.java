package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.OrderItemWeightRefundCheckVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2020-03-31 20:17
 * @Description:
 */
@TypeDoc(
        description = "克重退款页面检查退款请求响应"
)
@ApiModel("克重退款页面检查退款请求响应")
@Data
public class WeightRefundCheckResponse {
    @FieldDoc(
            description = "退款商品列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "退款商品列表", required = true)
    private List<OrderItemWeightRefundCheckVO> weightRefundCheckVOList;
}
