package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.OrderForBoothVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/5/5
 * desc:
 */
@TypeDoc(
        description = "摊位订单列表响应"
)
@Data
@ApiModel("摊位订单列表响应")
public class GetOrderForBoothResponseVO {

    @FieldDoc(
            description = "总金额 单位:元", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "总金额 单位:元", required = true)
    private Double totalAmount;

    @FieldDoc(
            description = "订单列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单列表", required = true)
    private List<OrderForBoothVO> orders;
}
