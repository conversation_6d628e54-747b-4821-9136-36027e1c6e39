package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/2 11:38
 * @Description:
 */
@Setter
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(
        description = "订单导出请求"
)
public class ChannelOrderDownloadRequest extends PageRequest implements BaseRequest {

    /**
     * 收货人姓名
     */
    private String receiverName;

    /**
     * 收货人地址
     */
    private String receiverAddress;

    /**
     * 收货人电话
     */
    private String receiverPhone;

    /**
     * 门店名称
     */
    private String poiName;

    /**
     * 门店编码
     */
    private String poiId;

    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 开始时间
     */
    private String createStartTime;

    /**
     * 结束时间
     */
    private String createEndTime;

    /**
     * 状态
     */
    private String status;


    /**
     * 渠道列表
     */
    private String channelIds;


    /**
     * 退款标识
     */
    private String refundTypes;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 是否是会员
     */
    private String hasMemberCard;

    /**
     * 会员卡号
     */
    private String memberCard;


    /**
     * 导出开始行
     */
    private int startRow;

    /**
     * 导出结束行
     */
    private int endRow;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 预计送达开始时间
     */
    private String arrivalStartTime;

    /**
     * 预计送达结束时间
     */
    private String arrivalEndTime;

    /**
     * 流水号
     */
    @FieldDoc(
            description = "流水号"
    )
    private String orderSerialNumber;

    /**
     * 摊位ID
     */
    @FieldDoc(
            description = "摊位ID集合"
    )
    private List<Long> boothIds;

    /**
     * 问题订单(1进货价为空)
     */
    @FieldDoc(
            description = "问题订单(1进货价为空)"
    )
    private Integer orderProblemType;

    /**
     * 门店ID集合
     */
    private List<Long> poiIdList;
    /**
     * 仓id
     */
    private List<Long> warehouseIdList;

    /**
     * 配送方式筛选 2 自营配送  -4/4000+ 青云配送
     */
    private Integer deliveryChannelType;

    /**
     * 完成配送，筛选起始时间
     */
    private String finishDeliveryStartTime;
    /**
     * 完成配送，筛选结束时间
     */
    private String finishDeliveryEndTime;

    /**
     * 基础skuId
     */
    private String skuId;

    /**
     * 骑手牵牛花账号
     */
    private String riderAccount;

    /**
     * 订单标识列表
     */
    private String orderMarks;


    /**
     * 客户端来源
     */
    private String clientType;



    @Override
    public void selfCheck() {
        AssertUtil.isTrue(page > 0, "page不能小于等于0");
        AssertUtil.isTrue(pageSize > 0, "pageSize不能小于等于0");
    }
}
