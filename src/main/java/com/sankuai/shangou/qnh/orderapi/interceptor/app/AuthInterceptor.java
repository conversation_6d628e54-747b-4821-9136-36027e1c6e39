package com.sankuai.shangou.qnh.orderapi.interceptor.app;

import com.sankuai.shangou.qnh.orderapi.context.app.ApiMethodParamThreadLocal;
import com.sankuai.shangou.qnh.orderapi.enums.app.ResultCodeEnum;
import com.sankuai.shangou.qnh.orderapi.exception.app.CommonLogicException;
import com.sankuai.shangou.qnh.orderapi.annotation.app.Auth;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Slf4j
@Component("appAuthInterceptor")
public class AuthInterceptor extends HandlerInterceptorAdapter {
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {

        if (handler instanceof HandlerMethod) {
            // 只处理Controller
            HandlerMethod handlerMethod = (HandlerMethod) handler;

            Class<?> type = handlerMethod.getBeanType();

            AuthInfo authInfo = new AuthInfo(type.getDeclaredAnnotation(Auth.class),
                    handlerMethod.getMethodAnnotation(Auth.class));

            if (!authInfo.authenticate) {
                return true;
            }

            if (ApiMethodParamThreadLocal.getIdentityInfo().getUser() == null) {
                throw new CommonLogicException("获取用户数据失败", ResultCodeEnum.FAIL);
            }
            return true;
        } else {
            return true;
        }

    }

    @SuppressWarnings("unused")
    private static final class AuthInfo {
        private final boolean authenticate;

        private AuthInfo(Auth authOnClass, Auth authOnMethod) {

            if (authOnClass == null && authOnMethod == null) {
                authenticate = false;
            } else if (authOnMethod != null) {
                authenticate = authOnMethod.authenticate();
            } else {
                authenticate = authOnClass.authenticate();
            }
        }

        public boolean authenticate() {
            return authenticate;
        }
    }
}
