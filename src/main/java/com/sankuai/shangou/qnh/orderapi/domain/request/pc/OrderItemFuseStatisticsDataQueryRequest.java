package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import com.sankuai.shangou.qnh.orderapi.utils.pc.LionUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @program: reco_store_saas_e_api
 * @description:
 * @author: jinyi
 * @create: 2023-08-17 17:01
 **/

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(description = "订单明细统计数据查询请求")
public class OrderItemFuseStatisticsDataQueryRequest extends PageRequest implements BaseRequest {

    /**
     * 订单编号（渠道单号）
     */
    private String orderId;

    /**
     * 状态
     */
    private List<String> fuseOrderStatus;

    /**
     * 渠道列表
     */
    private List<String> channelIds;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 门店id列表
     */
    private List<Long> poiIdList;

    /**
     * 仓库id列表
     */
    private List<Long> warehouseIdList;

    /**
     * 商品id列表
     */
    private List<String> skuIdList;

    /**
     * 商品条码列表
     */
    private List<String> upcList;

    /**
     * 线上分类（店内分类）
     */
    private List<String> inStoreCategoryIds;

    /**
     * 订单创建开始时间
     */
    private String createStartTime;

    /**
     * 订单创建结束时间
     */
    private String createEndTime;

    /**
     * 商品erp列表
     */
    private List<String> erpCodeList;

    /**
     * 订单标识字符串列表
     */
    public List<String> orderMarkStrList;

    @Override
    public void selfCheck() {
        if (StringUtils.isNotEmpty(orderId)) {
            Integer limitLength = LionUtils.getOrderParamsLengthLimitConfig();
            AssertUtil.isTrue(orderId.length() <= limitLength, "渠道单号最多输入" + limitLength + "个数字");
        }
        if (CollectionUtils.isNotEmpty(skuIdList)) {
            for (String skuId : skuIdList) {
                AssertUtil.isTrue(skuId.length() <= 30, "商品SKU最多输入30个字符");
            }
        }

        if (CollectionUtils.isNotEmpty(erpCodeList)) {
            for (String erpCode : erpCodeList) {
                AssertUtil.isTrue(erpCode.length() <= 30, "ERP编码最多输入30个字符");
            }
        }
        if (CollectionUtils.isNotEmpty(upcList)) {
            for (String upc : upcList) {
                AssertUtil.isTrue(upc.length() <= 30, "商品条码最多输入30个字符");
            }
        }
    }
}
