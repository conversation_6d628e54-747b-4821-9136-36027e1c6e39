package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description: 聚合配送配置
 * @Author: z<PERSON>jian155
 * @Date: 2022/10/11 15:20
 */
@Data
public class StoreDeliveryConfigVO {

    @FieldDoc(
            description = "聚合配送门店配置"
    )
    @ApiModelProperty(value = "聚合配送门店配置")
    private List<DeliveryChannelConfigVo> deliveryChannelConfigVoList;
}
