package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.AfsExchangeProductVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.SubProductVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.ChannelLabelVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/7/16
 * desc: 售后申请记录详情
 */
@TypeDoc(
        description = "售后商品信息"
)
@ApiModel("售后商品信息")
@Data
public class AfterSaleRecordDetailVO {

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID", required = true)
    private Integer channelId;

    @FieldDoc(
            description = "售后服务唯一ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "售后服务唯一ID", required = true)
    private Long serviceId;

    @FieldDoc(
            description = "skuId", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "skuId", required = true)
    private String skuId;

    @FieldDoc(
            description = "upc码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "upc码", required = true)
    private String upcCode;

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称", required = true)
    private String skuName;

    @FieldDoc(
            description = "规格", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "规格", required = true)
    private String specification;

    @FieldDoc(
            description = "售卖单位", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "售卖单位", required = true)
    private String sellUnit;

    @FieldDoc(
            description = "单价  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "单价  单位:分", required = true)
    private Integer unitPrice;

    @FieldDoc(
            description = "退货数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "退货数量", required = true)
    private Integer count;

    @FieldDoc(
            description = "退款金额  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "退款金额  单位:分", required = true)
    private Integer refundAmt;

    @FieldDoc(
            description = "克重退款重量，单位:克", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "克重退款重量，单位:克")
    private Double refundWeight;

    @FieldDoc(
            description = "组合品子商品信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "组合品子商品信息")
    private List<SubProductVo> subProductVoList;

    @FieldDoc(
            description = "地址变更退费", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "地址变更退费")
    private String addressChangeFee;

    @FieldDoc(
            description = "地址变更退费注释", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "地址变更退费注释")
    private String addressChangeFeeNotes;

    @FieldDoc(
            description = "商品渠道标签列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品渠道标签列表")
    private List<ChannelLabelVO> channelLabelList;

    @FieldDoc(
            description = "售后商品对应的换货商品信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "售后商品对应的换货商品信息")
    private AfsExchangeProductVo afsExchangeProduct;

    @FieldDoc(
            description = "商品标签附加信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "商品标签附加信息")
    private String labelSubDesc;
}
