package com.sankuai.shangou.qnh.orderapi.remote;

import com.dianping.lion.client.util.CollectionUtils;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Maps;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiOperationModeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.DistrictDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.infra.osw.api.org.TOrgResourceService;
import com.sankuai.shangou.infra.osw.api.org.TOrgService;
import com.sankuai.shangou.infra.osw.api.org.dto.request.DepartmentDetailRequest;
import com.sankuai.shangou.infra.osw.api.org.dto.response.DepartmentDetailDTO;
import com.sankuai.shangou.infra.osw.api.org.dto.response.OrgDTO;
import com.sankuai.shangou.infra.osw.api.org.dto.response.OrgDetailDTO;
import com.sankuai.shangou.infra.osw.api.poi.store.dto.request.WarehouseIdsRequest;
import com.sankuai.shangou.infra.osw.api.poi.warehouse.TWarehouseService;
import com.sankuai.shangou.infra.osw.api.poi.warehouse.dto.request.OrgIdsRequest;
import com.sankuai.shangou.infra.osw.api.poi.warehouse.dto.response.WarehouseDTO;
import com.sankuai.shangou.qnh.orderapi.domain.bo.OrgParentInfoBo;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.OrgParentBO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OrgRemoteService {

    private static final int ORG_SERVICE_PAGE_SIZE = 200;
    private static final String FRANCHISE_PRE_STR = "加盟-";

    @Resource
    private TOrgService tOrgService;

    @Resource
    private TOrgResourceService tOrgResourceService;
    @Resource
    private TWarehouseService tWarehouseService;
    @Resource
    private PoiRemoteService poiRemoteService;


    public OrgParentBO queryParentOrg(Long tenantId, Long deptId) {
        try {
            DepartmentDetailRequest request = new DepartmentDetailRequest();
            request.setTenantId(tenantId);
            request.setDepartmentId(deptId);
            TResult<OrgDetailDTO> result = tOrgService.queryDepartmentDetail(request);
            if (result != null && result.isSuccess()) {
                OrgDetailDTO orgDetailDTO = result.getData();
                DepartmentDetailDTO departmentDetailDTO = orgDetailDTO != null ? orgDetailDTO.getDepartment() : null;
                if (departmentDetailDTO != null) {
                    return OrgParentBO.builder()
                            .parentId(departmentDetailDTO.getParentId())
                            .parentName(departmentDetailDTO.getParentName())
                            .build();
                }
            }
        } catch (Exception ex) {
            log.error("OrgRemoteService.queryDepartmentDetail error", ex);
        }
        return null;

    }

    public List<Long> queryFranchiseeIdByEmpId(Long tenantId, Long empId) throws TException {
        TResult<List<Long>> result = tOrgResourceService.queryFranchiseeIdByEmpId(tenantId, empId);
        if (result != null && result.isSuccess()) {
            return result.getData();
        }
        return new ArrayList<>();

    }

    /**
     * 批量查询组织信息
     * @param tenantId
     * @param orgIds
     * @return
     */
    public Map<Long, OrgDTO> batchQueryOrg(Long tenantId, List<Long> orgIds) {
        try {
            if (CollectionUtils.isEmpty(orgIds)) {
                return Maps.newHashMap();
            }
            OrgIdsRequest request = new OrgIdsRequest();
            request.setTenantId(tenantId);
            request.setOrgIds(orgIds);
            TResult<List<OrgDTO>> result = tOrgService.getByIds(request);
            if (result == null || !result.isSuccess() || CollectionUtils.isEmpty(result.getData())) {
                log.info("batchQueryOrg error tenantId:{}, orgIds:{}, result:{}", tenantId, orgIds, result);
                return Maps.newHashMap();
            }
            List<OrgDTO> orgDTOList = result.getData();
            return orgDTOList.stream().filter(Objects::nonNull).collect(Collectors.toMap(OrgDTO::getId, orgDTO-> orgDTO));
        } catch (Exception ex) {
            log.error("queryDepartmentDetail error tenantId:{}, shopIds:{}", tenantId, orgIds, ex);
        }
        return Maps.newHashMap();
    }

    /**
     * 批量查询门店的组织信息(门店+1，门店+2, 门店类型)的映射关系
     *
     * @param tenantId
     * @param shopIds
     * @return
     */
    public Map<Long, OrgParentInfoBo> getShopOrgParentMap(Long tenantId, List<Long> shopIds) {
        try {
            if (CollectionUtils.isEmpty(shopIds)) {
                return Maps.newHashMap();
            }
            List<Long> shopIdList = shopIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
            Map<Long, OrgParentInfoBo> resultMap = Maps.newHashMap();
            // osw接口批量一次查询200个
            List<List<Long>> partitionList = Lists.partition(shopIdList, ORG_SERVICE_PAGE_SIZE);
            for (int i = 0; i < partitionList.size(); i++) {
                Map<Long, OrgParentInfoBo> longOrgParentBoMap = queryShopOrgParentMap(tenantId, partitionList.get(i));
                if (MapUtils.isNotEmpty(longOrgParentBoMap)) {
                    resultMap.putAll(longOrgParentBoMap);
                }
            }
            return resultMap;
        } catch (Exception ex) {
            log.error("getShopOrgParentMap tenantId:{}, shopIds:{}, error", tenantId, shopIds, ex);
        }
        return Maps.newHashMap();
    }

    /**
     * 查询门店的组织信息(门店+1，门店+2, 门店类型)的映射关系
     *
     * @param tenantId
     * @param shopIdList
     * @return
     */
    private Map<Long, OrgParentInfoBo> queryShopOrgParentMap(Long tenantId, List<Long> shopIdList) {
        try {
            // 2.门店映射组织 3.组织映射上级组织 4.上级映射上上级
            Map<Long, PoiInfoDto> poiInfoDtoMap = poiRemoteService.queryPoiInfosByPoiIds(new HashSet<>(shopIdList));
            if (MapUtils.isEmpty(poiInfoDtoMap)) {
                return Maps.newHashMap();
            }
            // 查询门店的组织信息
            List<Long> shopDepartmentIdList = poiInfoDtoMap.values().stream().filter(Objects::nonNull)
                    .map(PoiInfoDto::getDepartmentId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            Map<Long, OrgDTO> orgIdToOrgDtoMap = batchQueryOrg(tenantId, shopDepartmentIdList);
            // 查询门店组织的上级组织信息
            List<Long> parentIdList = orgIdToOrgDtoMap.values().stream().filter(Objects::nonNull)
                    .map(OrgDTO::getParentId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            Map<Long, OrgDTO> parentOrgIdToOrgDtoMap = batchQueryOrg(tenantId, parentIdList);
            // 查询门店组织的上上级组织信息
            List<Long> parentOrgpPrentIdList = parentOrgIdToOrgDtoMap.values().stream().filter(Objects::nonNull)
                    .map(OrgDTO::getParentId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            Map<Long, OrgDTO> grandParentOrgIdToOrgDtoMap = batchQueryOrg(tenantId, parentOrgpPrentIdList);
            return shopIdList.stream().collect(Collectors.toMap(shopId -> shopId, shopId -> {
                // 获取基础信息
                PoiInfoDto poiInfoDto = poiInfoDtoMap.get(shopId);
                Integer manageMode = Optional.ofNullable(poiInfoDto).map(PoiInfoDto::getManageMode).orElse(null);
                String cityName = null, provinceName = null;
                DistrictDto districtDto = Optional.ofNullable(poiInfoDto).map(PoiInfoDto::getDistrict).orElse(null);
                if (Objects.nonNull(districtDto)) {
                    cityName = districtDto.getCityName();
                    provinceName = districtDto.getProvinceName();
                }
                String operationMode = Optional.ofNullable(manageMode)
                        .map(mode -> Objects.equals(mode, PoiOperationModeEnum.FRANCHISE.getKey()) ? "加盟" : "直营")
                        .orElse(null);
                // 通过门店组织信息获取上级组织信息
                Long orgId = Optional.ofNullable(poiInfoDto).map(PoiInfoDto::getDepartmentId).orElse(null);
                OrgDTO org = orgIdToOrgDtoMap.get(orgId);
                Long parentOrgId = Optional.ofNullable(org).map(OrgDTO::getParentId).orElse(null);
                OrgDTO parentOrg = parentOrgIdToOrgDtoMap.get(parentOrgId);
                String parentOrgName = processOrgName(Optional.ofNullable(parentOrg).map(OrgDTO::getName).orElse(null));
                // 通过门店的上级组织信息获取上上级组织信息
                Long grandParentOrgId = Optional.ofNullable(parentOrg).map(OrgDTO::getParentId).orElse(null);
                OrgDTO grandParentOrg = grandParentOrgIdToOrgDtoMap.get(grandParentOrgId);
                String grandParentOrgName = processOrgName(
                        Optional.ofNullable(grandParentOrg).map(OrgDTO::getName).orElse(null));
                return OrgParentInfoBo.builder().parentId(parentOrgId).parentName(parentOrgName)
                        .grandParentId(grandParentOrgId).grandParentName(grandParentOrgName).manageMode(manageMode)
                        .operationMode(operationMode).cityName(cityName).provinceName(provinceName).build();
            }, (existing, replacement) -> existing));
        } catch (Exception ex) {
            log.error("queryShopOrgParentMap tenantId:{}, shopIds:{}, error", tenantId, shopIdList, ex);
        }
        return Maps.newHashMap();
    }

    /**
     * 处理组织名称
     *
     * @param orgName
     * @return
     */
    private String processOrgName(String orgName) {
        if (StringUtils.isBlank(orgName)) {
            return null;
        }
        return orgName.contains(FRANCHISE_PRE_STR)
                ? orgName.replaceAll(FRANCHISE_PRE_STR, "")
                : orgName;
    }

}
