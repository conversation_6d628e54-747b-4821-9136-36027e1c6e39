package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/11/20
 * desc: 创建门店商品
 */
@TypeDoc(
        description = "创建门店商品"
)
@Data
@ApiModel("创建门店商品")
public class CreateStoreSkuVO {

    @FieldDoc(
            description = "商品编码"
    )
    @ApiModelProperty(name = "商品编码")
    private String skuId;

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称")
    @NotNull
    private String name;

    @FieldDoc(
            description = "规格"
    )
    @ApiModelProperty(name = "规格")
    private String spec;

    @FieldDoc(
            description = "UPC列表"
    )
    @ApiModelProperty(name = "UPC列表")
    private List<String> upcList;

    @FieldDoc(
            description = "重量"
    )
    @ApiModelProperty(name = "重量")
    private Integer weight;

    @FieldDoc(
            description = "品牌编码"
    )
    @ApiModelProperty(name = "品牌编码")
    private String brandCode;

    @FieldDoc(
            description = "商品类目编码"
    )
    @ApiModelProperty(name = "商品类目编码")
    private String categoryCode;

    @FieldDoc(
            description = "称重类型 1-称重计量 2-称重计件 3-非称重"
    )
    @ApiModelProperty(name = "称重类型")
    private Integer weightType;

    @FieldDoc(
            description = "商品类型 0-全部(默认) 1-商品 2-自动加工商品 3-组合商品 4-原料"
    )
    @ApiModelProperty(name = "称重类型")
    private Integer skuType;

    @FieldDoc(
            description = "商品图片", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品图片")
    @NotEmpty
    private List<String> imageList;

    @FieldDoc(
            description = "门店价 单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店价")
    @NotNull
    private Integer price;

    @FieldDoc(
            description = "单位", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "单位")
    @NotNull
    private String unit;

    @FieldDoc(
            description = "门店库存"
    )
    @ApiModelProperty(name = "门店库存")
    private Integer stock;

    @FieldDoc(
            description = "门店打标时返回"
    )
    @ApiModelProperty(name = "门店打标时返回")
    private Integer storeOnSale;

    @FieldDoc(
            description = "京东类目编码"
    )
    @ApiModelProperty(name = "京东类目编码")
    private String jdCategoryCode;

    @FieldDoc(
            description = "门店打标时返回"
    )
    @ApiModelProperty(name = "门店打标时返回")
    private String jdCategoryName;

    @FieldDoc(
            description = "门店打标时返回"
    )
    @ApiModelProperty(name = "门店打标时返回")
    private String jdBrandCode;

    @FieldDoc(
            description = "门店打标时返回"
    )
    @ApiModelProperty(name = "门店打标时返回")
    private String jdBrandName;

    @FieldDoc(
            description = "可售时间，如果为无限，此字段为null；" +
                    "key为可售日期，参考WeekDayEnum（0-monday ,1-tuesday ,2-wednesday ,3-thursday ,4-friday ,5-saturday ,6-sunday）；" +
                    "value为可售时间段，不允许有交集，个数不超过5个", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "可售时间")
    private Map<Integer, List<TimeSlotVO>> availableTimes;

    @FieldDoc(
            description = "是否为力荐商品，0-否， 1-是", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否为力荐商品")
    private Integer specialty;

    @FieldDoc(
            description = "商品描述,150字以内", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品描述")
    private String description;

    @FieldDoc(
            description = "商品属性,不超过十个属性", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品属性")
    private List<StoreSkuPropertyVO> properties;

    @FieldDoc(
            description = "美团渠道类目编码"
    )
    @ApiModelProperty(name = "美团渠道类目编码")
    private String mtCategoryCode;

    @FieldDoc(
            description = "美团渠道名称"
    )
    @ApiModelProperty(name = "美团渠道名称")
    private String mtCategoryName;
}
