package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description: 获取门店商品详情
 * @author: Wang<PERSON><PERSON><PERSON>
 * @create: 2020-01-02
 **/
@TypeDoc(
        description = "获取门店商品详情"
)
@Data
@ApiModel("获取门店商品详情")
public class QueryCdqStoreSkuDetailRequest {

    @FieldDoc(
            description = "租户Id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户Id", required = true)
    @NotNull
    private Long tenantId;
    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店id", required = true)
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "门店商品编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店商品编码", required = true)
    @NotNull
    private String storeSkuId;


}
