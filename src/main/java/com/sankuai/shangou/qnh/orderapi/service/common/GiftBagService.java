package com.sankuai.shangou.qnh.orderapi.service.common;


import com.google.common.collect.Lists;
import com.sankuai.qnh.ofc.ofw.client.thrift.dto.order.GiftBagItemDTO;
import com.sankuai.qnh.ofc.ofw.client.thrift.dto.order.GiftBagOrderDTO;
import com.sankuai.qnh.ofc.ofw.client.thrift.request.order.OrderGiftBagSearchRequest;
import com.sankuai.qnh.ofc.ofw.client.thrift.response.order.OrderGiftBagResponse;
import com.sankuai.qnh.ofc.ofw.client.thrift.service.order.FulfillmentOrderSearchThriftService;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.ChannelOrderDetailBO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.OrderVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.RefundApplyRecordVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.DrunkHorseGiftBagVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.ItemInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.OrderFuseDetailVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.OrderDetailVO;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/1/5
 */
@Service
@Slf4j
public class GiftBagService {

    @Resource
    private FulfillmentOrderSearchThriftService fulfillmentOrderSearchThriftService;


    public void appendGiftBagInfo(OrderFuseDetailVO detaiFuselVO, Long tenantId) {
        try {
            if (detaiFuselVO.getBaseInfo() == null || CollectionUtils.isEmpty(detaiFuselVO.getItemInfo()) || !MccConfigUtil.isDrunkHorseTenant(tenantId)) {
                return;
            }
            Long storeId = Objects.nonNull(detaiFuselVO.getBaseInfo().getWarehouseId()) ? detaiFuselVO.getBaseInfo().getWarehouseId() : detaiFuselVO.getBaseInfo().getPoiId();
            if (!MccConfigUtil.orderItemFuseSplitSwitch(tenantId, storeId)) {
                log.info("appendGiftBagInfo orderItemFuseSplitSwitch is close");
                return;
            }
            List<Long> orderIdList = Lists.newArrayList(Long.valueOf(detaiFuselVO.getBaseInfo().getEmpowerOrderId()));
            List<GiftBagOrderDTO> giftBagOrderDTOList = queryGiftBagInfo(tenantId, storeId, orderIdList);
            if (CollectionUtils.isEmpty(giftBagOrderDTOList) || CollectionUtils.isEmpty(giftBagOrderDTOList.get(0).getGiftBagItemList())) {
                detaiFuselVO.setGiftBagList(Lists.newArrayList());
            }
            List<DrunkHorseGiftBagVO> giftBagVOList = buildPcDrunkHorseGiftBag(giftBagOrderDTOList.get(0).getGiftBagItemList(), detaiFuselVO.getItemInfo());
            detaiFuselVO.setGiftBagList(giftBagVOList);
        } catch (Exception e) {
            log.error("appendGiftBagInfo error", e);
        }

    }


    public void appendGiftBagInfo(ChannelOrderDetailBO channelOrderDetailBO, Long tenantId) {
        try {
            if (channelOrderDetailBO == null || CollectionUtils.isEmpty(channelOrderDetailBO.getItemInfo()) || !MccConfigUtil.isDrunkHorseTenant(tenantId)) {
                return;
            }
            if (!MccConfigUtil.orderItemFuseSplitSwitch(tenantId, channelOrderDetailBO.getBaseInfo().getPoiId())) {
                log.info("appendGiftBagInfo orderItemFuseSplitSwitch is close");
                return;
            }
            Long storeId = Objects.nonNull(channelOrderDetailBO.getBaseInfo().getWarehouseId()) ? channelOrderDetailBO.getBaseInfo().getWarehouseId() : channelOrderDetailBO.getBaseInfo().getPoiId();
            List<Long> orderIdList = Lists.newArrayList(channelOrderDetailBO.getBaseInfo().getEmpowerOrderId());
            List<GiftBagOrderDTO> giftBagOrderDTOList = queryGiftBagInfo(tenantId, storeId, orderIdList);
            if (CollectionUtils.isEmpty(giftBagOrderDTOList) || CollectionUtils.isEmpty(giftBagOrderDTOList.get(0).getGiftBagItemList())) {
                channelOrderDetailBO.setGiftBagList(Lists.newArrayList());
            }
            List<DrunkHorseGiftBagVO> giftBagVOList = buildDrunkHorseGiftBag(giftBagOrderDTOList.get(0).getGiftBagItemList(), channelOrderDetailBO.getItemInfo());
            channelOrderDetailBO.setGiftBagList(giftBagVOList);
        } catch (Exception e) {
            log.error("appendGiftBagInfo error", e);
        }

    }

    public void appendRefundGiftBagInfo(List<RefundApplyRecordVO> refundApplyRecordVOList, Long tenantId, Long storeId) {
        try {
            if (!MccConfigUtil.isDrunkHorseTenant(tenantId)) {
                return;
            }
            if (!MccConfigUtil.orderItemFuseSplitSwitch(tenantId, storeId)) {
                log.info("appendGiftBagInfo orderItemFuseSplitSwitch is close");
                return;
            }
            List<Long> orderIdList = Optional.ofNullable(refundApplyRecordVOList)
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(Objects::nonNull)
                    .map(RefundApplyRecordVO::getOrderVO)
                    .filter(Objects::nonNull)
                    .map(OrderVO::getEmpowerOrderId)
                    .collect(Collectors.toList());
            List<GiftBagOrderDTO> giftBagOrderDTOList = queryGiftBagInfo(tenantId, storeId, orderIdList);
            Map<Long, List<GiftBagItemDTO>> orderIdGiftBagMap = Optional.ofNullable(giftBagOrderDTOList)
                    .orElse(Collections.emptyList()).stream()
                    .collect(Collectors.toMap(GiftBagOrderDTO::getOrderId, GiftBagOrderDTO::getGiftBagItemList, (k1, k2) -> k1));
            Optional.ofNullable(refundApplyRecordVOList).orElse(Collections.emptyList()).forEach(refundApplyRecordVO -> {
                if (refundApplyRecordVO.getOrderVO() == null) {
                    return;
                }
                Long orderId = refundApplyRecordVO.getOrderVO().getEmpowerOrderId();
                List<GiftBagItemDTO> giftBagItemDTOList = orderIdGiftBagMap.get(orderId);
                refundApplyRecordVO.getOrderVO().setGiftBagList(buildGiftBagVO(giftBagItemDTOList));
            });
        } catch (Exception e) {
            log.error("appendRefundGiftBagInfo error", e);
        }
    }

    public void appendGiftBagInfo(OrderDetailVO orderDetailVO, Long tenantId) {
        try {
            if (orderDetailVO == null || !MccConfigUtil.isDrunkHorseTenant(tenantId)) {
                return;
            }
            Long storeId = Objects.nonNull(orderDetailVO.getWarehouseId()) ? orderDetailVO.getWarehouseId() : orderDetailVO.getStoreId();
            if (!MccConfigUtil.orderItemFuseSplitSwitch(tenantId, storeId)) {
                log.info("appendGiftBagInfo orderItemFuseSplitSwitch is close");
                return;
            }
            List<Long> orderIdList = Lists.newArrayList(orderDetailVO.getOrderId());
            List<GiftBagOrderDTO> giftBagOrderDTOList = queryGiftBagInfo(tenantId, storeId, orderIdList);
            if (CollectionUtils.isEmpty(giftBagOrderDTOList) || CollectionUtils.isEmpty(giftBagOrderDTOList.get(0).getGiftBagItemList())) {
                orderDetailVO.setGiftBagList(Lists.newArrayList());
            }
            orderDetailVO.setGiftBagList(buildGiftBagVO(giftBagOrderDTOList.get(0).getGiftBagItemList()));
        } catch (Exception e) {
            log.error("appendGiftBagInfo error", e);
        }
    }

    public void appendGiftBagInfo(List<OrderVO> orderList) {
        try {
            if (CollectionUtils.isEmpty(orderList) || !MccConfigUtil.isDrunkHorseTenant(orderList.get(0).getTenantId())) {
                return;
            }
            Long tenantId = orderList.get(0).getTenantId();
            Long storeId = Objects.nonNull(orderList.get(0).getWarehouseId()) ? orderList.get(0).getWarehouseId() : orderList.get(0).getStoreId();
            if (!MccConfigUtil.orderItemFuseSplitSwitch(tenantId, storeId)) {
                log.info("appendGiftBagInfo orderItemFuseSplitSwitch is close");
                return;
            }
            List<Long> orderIdList = orderList.stream().map(OrderVO::getEmpowerOrderId).collect(Collectors.toList());
            List<GiftBagOrderDTO> giftBagOrderDTOList = queryGiftBagInfo(tenantId, storeId, orderIdList);
            Map<Long, List<GiftBagItemDTO>> giftBagMap = giftBagOrderDTOList.stream().collect(Collectors.toMap(GiftBagOrderDTO::getOrderId, GiftBagOrderDTO::getGiftBagItemList));
            orderList.forEach(order -> {
                List<GiftBagItemDTO> giftBagItemList = giftBagMap.get(order.getEmpowerOrderId());
                order.setGiftBagList(buildGiftBagVO(giftBagItemList));
            });
        } catch (Exception e) {
            log.error("appendGiftBagInfo error", e);
        }
    }

    public void appendGiftBagInfo(OrderVO orderVO) {
        try {
            if (!MccConfigUtil.isDrunkHorseTenant(orderVO.getTenantId())) {
                return;
            }
            Long tenantId = orderVO.getTenantId();
            Long storeId = Objects.nonNull(orderVO.getWarehouseId()) ? orderVO.getWarehouseId() : orderVO.getStoreId();
            if (!MccConfigUtil.orderItemFuseSplitSwitch(tenantId, storeId)) {
                log.info("appendGiftBagInfo orderItemFuseSplitSwitch is close");
                return;
            }
            List<Long> orderIdList = Lists.newArrayList(orderVO.getEmpowerOrderId());
            List<GiftBagOrderDTO> giftBagOrderDTOList = queryGiftBagInfo(tenantId, storeId, orderIdList);
            Map<Long, List<GiftBagItemDTO>> giftBagMap = giftBagOrderDTOList.stream().collect(Collectors.toMap(GiftBagOrderDTO::getOrderId, GiftBagOrderDTO::getGiftBagItemList));
            List<GiftBagItemDTO> giftBagItemList = giftBagMap.get(orderVO.getEmpowerOrderId());
            orderVO.setGiftBagList(buildGiftBagVO(giftBagItemList));
        } catch (Exception e) {
            log.error("appendGiftBagInfo error", e);
        }

    }

    public List<GiftBagOrderDTO> queryGiftBagInfo(Long tenantId, Long storeId, List<Long> orderIdList) {
        try {
            if (CollectionUtils.isEmpty(orderIdList)) {
                return Lists.newArrayList();
            }
            OrderGiftBagSearchRequest request = new OrderGiftBagSearchRequest();
            request.setTenantId(tenantId);
            request.setWarehouseId(storeId);
            request.setOrderIdList(orderIdList);
            OrderGiftBagResponse response = fulfillmentOrderSearchThriftService.searchGiftBagByOrderIdList(request);
            if (response == null || CollectionUtils.isEmpty(response.getGiftBagOrderList())) {
                log.info("queryGiftBagInfo response is null");
                return Lists.newArrayList();
            }
            return response.getGiftBagOrderList();
        } catch (Exception e) {
            log.error("queryGiftBagInfo error", e);
            return Lists.newArrayList();
        }

    }

    public static List<com.sankuai.shangou.qnh.orderapi.domain.vo.store.DrunkHorseGiftBagVO> buildGiftBagVO(List<GiftBagItemDTO> giftBagItemDTOList) {
        if (CollectionUtils.isEmpty(giftBagItemDTOList)) {
            return Lists.newArrayList();
        }
        return giftBagItemDTOList.stream().map(item -> {
            com.sankuai.shangou.qnh.orderapi.domain.vo.store.DrunkHorseGiftBagVO giftBagVO = new com.sankuai.shangou.qnh.orderapi.domain.vo.store.DrunkHorseGiftBagVO();
            giftBagVO.setBelongSkuId(item.getBelongSkuId());
            giftBagVO.setMaterialSkuId(item.getMaterialSkuId());
            giftBagVO.setMaterialSkuName(item.getMaterialSkuName());
            giftBagVO.setPicUrl(item.getPicUrl());
            giftBagVO.setType(item.getType());
            giftBagVO.setCnt(item.getCnt());
            giftBagVO.setResetCnt(item.getResetCnt());
            giftBagVO.setRefundCnt(item.getRefundCnt());
            giftBagVO.setSpec(item.getSpec());
            giftBagVO.setUpc(item.getUpc());
            return giftBagVO;
        }).collect(Collectors.toList());
    }

    public static List<DrunkHorseGiftBagVO> buildPcDrunkHorseGiftBag(List<GiftBagItemDTO> drunkHorseGiftBagVoList, List<ItemInfoVO> itemList) {
        if (CollectionUtils.isEmpty(drunkHorseGiftBagVoList)) {
            return Lists.newArrayList();
        }
        return drunkHorseGiftBagVoList.stream().map(giftBagItemDTO -> {
            ItemInfoVO itemInfoVO = itemList.stream().filter(item -> StringUtils.equals(item.getCustomSkuId(), giftBagItemDTO.getBelongSkuId()))
                    .findFirst().orElse(null);
            DrunkHorseGiftBagVO drunkHorseGiftBagVO = new DrunkHorseGiftBagVO();
            drunkHorseGiftBagVO.setBelongSkuId(giftBagItemDTO.getBelongSkuId());
            drunkHorseGiftBagVO.setMaterialSkuId(giftBagItemDTO.getMaterialSkuId());
            drunkHorseGiftBagVO.setMaterialSkuName(giftBagItemDTO.getMaterialSkuName());
            drunkHorseGiftBagVO.setPicUrl(giftBagItemDTO.getPicUrl());
            drunkHorseGiftBagVO.setType(giftBagItemDTO.getType());
            drunkHorseGiftBagVO.setCnt(giftBagItemDTO.getCnt());
            drunkHorseGiftBagVO.setResetCnt(giftBagItemDTO.getResetCnt());
            drunkHorseGiftBagVO.setRefundCnt(giftBagItemDTO.getRefundCnt());
            drunkHorseGiftBagVO.setSpec(giftBagItemDTO.getSpec());
            drunkHorseGiftBagVO.setUpc(giftBagItemDTO.getUpc());
            drunkHorseGiftBagVO.setParentSkuId(giftBagItemDTO.getBelongSkuId());
            if (itemInfoVO != null) {
                drunkHorseGiftBagVO.setParentSkuUpc(itemInfoVO.getUpc());
                drunkHorseGiftBagVO.setParentSkuName(itemInfoVO.getSkuName());
            }
            return drunkHorseGiftBagVO;
        }).collect(Collectors.toList());
    }


    public static List<DrunkHorseGiftBagVO> buildDrunkHorseGiftBag(List<GiftBagItemDTO> drunkHorseGiftBagVoList, List<ChannelOrderDetailBO.ItemInfo> itemList) {
        if (CollectionUtils.isEmpty(drunkHorseGiftBagVoList)) {
            return Lists.newArrayList();
        }
        return drunkHorseGiftBagVoList.stream().map(giftBag -> buildDrunkHorseGiftBagVO(giftBag, itemList)).collect(Collectors.toList());
    }


    private static DrunkHorseGiftBagVO buildDrunkHorseGiftBagVO(GiftBagItemDTO giftBagItemDTO, List<ChannelOrderDetailBO.ItemInfo> itemList) {
        ChannelOrderDetailBO.ItemInfo itemInfoVO = itemList.stream().filter(item -> StringUtils.equals(item.getCustomSkuId(), giftBagItemDTO.getBelongSkuId()))
                .findFirst().orElse(null);
        DrunkHorseGiftBagVO drunkHorseGiftBagVO = new DrunkHorseGiftBagVO();
        drunkHorseGiftBagVO.setBelongSkuId(giftBagItemDTO.getBelongSkuId());
        drunkHorseGiftBagVO.setMaterialSkuId(giftBagItemDTO.getMaterialSkuId());
        drunkHorseGiftBagVO.setMaterialSkuName(giftBagItemDTO.getMaterialSkuName());
        drunkHorseGiftBagVO.setPicUrl(giftBagItemDTO.getPicUrl());
        drunkHorseGiftBagVO.setType(giftBagItemDTO.getType());
        drunkHorseGiftBagVO.setCnt(giftBagItemDTO.getCnt());
        drunkHorseGiftBagVO.setResetCnt(giftBagItemDTO.getResetCnt());
        drunkHorseGiftBagVO.setRefundCnt(giftBagItemDTO.getRefundCnt());
        drunkHorseGiftBagVO.setSpec(giftBagItemDTO.getSpec());
        drunkHorseGiftBagVO.setUpc(giftBagItemDTO.getUpc());
        drunkHorseGiftBagVO.setParentSkuId(giftBagItemDTO.getBelongSkuId());
        if (itemInfoVO != null) {
            drunkHorseGiftBagVO.setParentSkuUpc(itemInfoVO.getUpc());
            drunkHorseGiftBagVO.setParentSkuName(itemInfoVO.getSkuName());
        }
        return drunkHorseGiftBagVO;
    }


}
