package com.sankuai.shangou.qnh.orderapi.interceptor.store;


import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.StatisticParams;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;

/**
 * Create by yujing10 on 2019/3/29.
 */
@Slf4j
@Component
public class ApiMethodStatisticsInterceptor extends HandlerInterceptorAdapter {

    private static final String PREFIX = "HttpRequestStatisticsLogs: ";
    private static final String HTTP_REQUEST_STATISTICS = "HttpRequestStatistics";
    private static final ThreadLocal<Transaction> threadLocal = new ThreadLocal<>();

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            //正常情况下tenantId不会为空，这里设置默认租户id为-1
            String tenantId = StringUtils.isBlank(request.getHeader("tenantid")) ? "-1" : request.getHeader("tenantid");
            Transaction transaction = Cat.newTransaction(tenantId + "_" + HTTP_REQUEST_STATISTICS, tenantId+ "_" + request.getServletPath());
            threadLocal.set(transaction);
            StatisticParams params = new StatisticParams();
            params.setUrlPath(request.getServletPath());
            params.setTenantId(Long.valueOf(tenantId));
            params.setTime(new Date());
            params.setControllerName(handlerMethod.getMethod().getDeclaringClass().getName());
            params.setMethodName(handlerMethod.getMethod().getName());
            log.info(PREFIX + JacksonUtils.toJson(params));//添加日志，以便通过日志自行统计调用情况
        }
        return true;
    }

    @Override
    public void postHandle(
            HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView)
            throws Exception {
        Transaction transaction = threadLocal.get();
        if (transaction != null) {
            transaction.setStatus(Transaction.SUCCESS);
            transaction.complete();
        }
        threadLocal.remove();
    }

}
