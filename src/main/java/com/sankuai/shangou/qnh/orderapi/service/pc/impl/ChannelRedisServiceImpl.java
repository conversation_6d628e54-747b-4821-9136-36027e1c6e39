package com.sankuai.shangou.qnh.orderapi.service.pc.impl;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.google.common.collect.ImmutableList;
import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.shangou.saas.common.enums.AuditTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.ChannelManageThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.channel.ChannelBaseDto;
import com.meituan.shangou.saas.tenant.thrift.dto.channel.request.ChannelBatchRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.channel.response.ChannelBaseListResponse;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.AfterSaleOperatorBo;
import com.sankuai.shangou.qnh.orderapi.service.pc.ChannelRedisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @create 2023/7/17 11:32
 */
@Slf4j
@Component
public class ChannelRedisServiceImpl implements ChannelRedisService {
    /**
     * 渠道id redis key 前缀
     */
    private static final String CHANNEL_ID_PREFIX = "settlement_channel_id_";
    /**
     * 渠道 orderBizType redis key 前缀
     */
    private static final String ORDER_BIZ_TYPE_PREFIX = "settlement_order_biz_type_";
    /**
     * 渠道 redis key 过期时间 保证缓存和运营端渠道数据一致性，非实时，有expireInSeconds时间的延迟
     * 单位秒 5分钟
     */

    private static final String CATEGORY = "store_operator_detail";

    private static final Integer EXPIRE_IN_SECONDS = 5 * 60;
    @Autowired
    private ChannelManageThriftService channelManageThriftService;

    private static final String AUDIT_TYPE = "auditType";

    @Resource(name = "drunkHorseRedisClient")
    private RedisStoreClient redisStoreClient;


    /**
     * 生成渠道的StoreKey，StoreKey组成 ：category、params（至少2个）
     * @param prefix
     * @param channelIdOrOrderBizType
     * @return
     */
    private String generateKey(String prefix,Integer channelIdOrOrderBizType){
        return prefix+channelIdOrOrderBizType;
    }

    /**
     * 从运营端获取渠道数据
     */
    private ChannelBaseDto getChannelBaseDto(ChannelBatchRequest channelBatchRequest) {
        ChannelBaseDto channelBaseDto = null;
        try {
            /*查运营端渠道接口*/
            ChannelBaseListResponse channelBaseListResponse = channelManageThriftService.batchQueryChannelBaseInfos(channelBatchRequest);
            if (channelBaseListResponse != null
                    && CollectionUtils.isNotEmpty(channelBaseListResponse.getChannelList())) {
                channelBaseDto = channelBaseListResponse.getChannelList().get(0);
                log.info("运营端查询参数 : {}, 从运营端中获取渠道数据 : {}", channelBatchRequest, channelBaseDto);
            }else {
                log.warn("{},在运营端没找查到！",channelBatchRequest);
            }
        } catch (Exception e) {
            log.error("运营端渠道查询接口调用失败！request：[{}]", channelBatchRequest, e);
        }
        return channelBaseDto;
    }


    public Integer getAuditType(Long viewOrderId) {
        try {
            StoreKey key = new StoreKey(AUDIT_TYPE, viewOrderId);
            log.info("drunkHorse getAuditType get key {}", viewOrderId);
            return redisStoreClient.get(key);
        } catch (Exception ex) {
            log.error("drunkHorse redis getAuditType error", ex);
            return AuditTypeEnum.UNKNOWN_AUDIT.getCode();
        }
    }

    public AfterSaleOperatorBo getOperatorDetail(String viewOrderId) {
        try {
            StoreKey key = new StoreKey(CATEGORY, viewOrderId);
            String value = redisStoreClient.get(key);
            if (StringUtils.isNotBlank(value)) {
                AfterSaleOperatorBo afterSaleOperatorBo = JacksonUtils.parse(value, AfterSaleOperatorBo.class);
                log.info("drunkHorse redis get key {} value {}", viewOrderId, afterSaleOperatorBo);
                return afterSaleOperatorBo;
            }
        } catch (Exception e) {
            log.error("redis store_operator_detail get error", e);
        }
        return null;
    }



}
