package com.sankuai.shangou.qnh.orderapi.exception.store;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.lion.client.Lion;
import com.dianping.rhino.exception.RhinoTimeoutException;
import com.google.common.annotations.VisibleForTesting;
import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.meituan.reco.pickselect.common.exception.FallbackException;
import com.meituan.reco.pickselect.common.exception.ParamException;
import com.meituan.reco.pickselect.common.flowcontrol.exception.FlowValveException;
import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.CommonResponse;
import com.sankuai.shangou.qnh.orderapi.utils.store.MccConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.core.Ordered;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.NoHandlerFoundException;
import org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

@Slf4j
public class CustomHandlerExceptionResolver extends AbstractHandlerExceptionResolver {

	private List<ExceptionHandler<?>> exceptionHandlerList = Arrays.asList(
			MethodArgumentNotValidExceptionHandler.INSTANCE,
			BindExceptionHandler.INSTANCE,
			ParamExceptionHandler.INSTANCE,
			NoHandlerFoundExceptionHandler.INSTANCE,
			ServletExceptionHandler.INSTANCE,
			MethodArgumentTypeMismatchExceptionHandler.INSTANCE,
			FlowValveExceptionHandler.INSTANCE,
			CommonLogicExceptionHandler.INSTANCE,
			CommonRuntimeExceptionHandler.INSTANCE,
			RhinoTimeoutExceptionHandler.INSTANCE,
			FallbackExceptionHandler.INSTANCE
	);

	@Override
	protected ModelAndView doResolveException(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
		String uri = request.getRequestURI();
		if (uri.startsWith("/api/v1")) {
			return null;
		}

		catEventStatistic(ex, request);//Cat按租户统计异常
		CommonResponse<?> commonResponse = null;

		for(ExceptionHandler exceptionHandler : exceptionHandlerList) {
			if(exceptionHandler.targetException().isAssignableFrom(ex.getClass())) {
				commonResponse = exceptionHandler.handle(ex, request, handler);
				break;
			}
		}

		if(commonResponse == null) {
			commonResponse = DefaultExceptionHandler.INSTANCE.handle(ex, request, handler);
		}

		responseResult(response, commonResponse);
		return new ModelAndView();
	}

	private void responseResult(HttpServletResponse response, CommonResponse<?> result) {
		response.setCharacterEncoding("UTF-8");
		response.setHeader("Content-type", "application/json;charset=UTF-8");
		response.setStatus(200);
		try {
			response.getWriter().write(JacksonUtils.toJson(result));
		} catch (IOException ex) {
			logger.error("IOException", ex);
		}
	}

	@Override
	public int getOrder() {
		return Ordered.HIGHEST_PRECEDENCE;
	}

	private static final class MethodArgumentNotValidExceptionHandler implements ExceptionHandler<MethodArgumentNotValidException> {
		public static MethodArgumentNotValidExceptionHandler INSTANCE = new MethodArgumentNotValidExceptionHandler();

		private MethodArgumentNotValidExceptionHandler() {

		}

		@Override
		public Class<MethodArgumentNotValidException> targetException() {
			return MethodArgumentNotValidException.class;
		}

		@Override
		public CommonResponse<?> handle(MethodArgumentNotValidException e, HttpServletRequest request, Object handler) {
			BindingResult bindingResult = e.getBindingResult();
			FieldError fieldError = bindingResult.getFieldError();
			String errorMsg = null;
			if (fieldError != null) {
				if(isCustomErrorMsg(fieldError.getDefaultMessage())) {
					errorMsg = resolveParamCheckErrorMsg(fieldError.getDefaultMessage());
				} else {
					errorMsg = "参数错误:" + fieldError.getField() + " " +
							fieldError.getDefaultMessage();
				}
			}

			return CommonResponse.fail(ResultCode.CHECK_PARAM_ERR, errorMsg);
		}
	}

	private static final class BindExceptionHandler implements ExceptionHandler<BindException> {

		public static final BindExceptionHandler INSTANCE = new BindExceptionHandler();

		private BindExceptionHandler() {

		}

		@Override
		public Class<BindException> targetException() {
			return BindException.class;
		}

		@Override
		public CommonResponse<?> handle(BindException e, HttpServletRequest request, Object handler) {
			FieldError fieldError = e.getFieldError();

			String errorMsg = null;
			if (fieldError != null) {
				if(isCustomErrorMsg(fieldError.getDefaultMessage())) {
					errorMsg = resolveParamCheckErrorMsg(fieldError.getDefaultMessage());
				} else {
					errorMsg = "参数错误:" + fieldError.getField() + " " +
							fieldError.getDefaultMessage();
				}
			}

			return CommonResponse.fail(ResultCode.CHECK_PARAM_ERR, errorMsg);
		}
	}

	private static final String PLACEHOLDER_PREFIX = "${";

	private static final String PLACEHOLDER_SUFFIX = "}";

	private static final String DEFAULT_SPLITTER = ":";

	@VisibleForTesting
	static boolean isCustomErrorMsg(String msg) {
		if(StringUtils.isEmpty(msg)) {
			return false;
		}

		msg = msg.trim();
		if(msg.startsWith(PLACEHOLDER_PREFIX) && msg.endsWith(PLACEHOLDER_SUFFIX)) {
			return true;
		}

		return false;
	}

	@VisibleForTesting
	static String resolveParamCheckErrorMsg(String msg) {
		if(!isCustomErrorMsg(msg)) {
			return msg;
		}

		if(StringUtils.isEmpty(msg)) {
			return msg;
		}

		msg = msg.trim();
		msg = msg.substring(PLACEHOLDER_PREFIX.length(), msg.length() - PLACEHOLDER_SUFFIX.length());

		if(StringUtils.isEmpty(msg)) {
			// ${}
			return msg;
		}

		String defaultMsg = "";
		int splitterIndex = msg.indexOf(DEFAULT_SPLITTER);
		if(splitterIndex >= 0) {
			// 存在默认值
			defaultMsg = msg.substring(splitterIndex + 1);
			msg = msg.substring(0, splitterIndex);

			if(StringUtils.isEmpty(msg) && StringUtils.isEmpty(defaultMsg)) {
				// ${:}
				return "";
			}

			if(StringUtils.isEmpty(defaultMsg)) {
				// ${test:}
				defaultMsg = msg;
			}

			if(StringUtils.isNotEmpty(msg)) {
				msg = Lion.getConfigRepository().get(msg, defaultMsg);
			} else {
				msg = defaultMsg;
			}

			return msg;
		} else {
			return Lion.getConfigRepository().get(msg, msg);
		}
	}

	private static final class ParamExceptionHandler implements ExceptionHandler<ParamException> {
		public static final ParamExceptionHandler INSTANCE = new ParamExceptionHandler();

		private ParamExceptionHandler() {

		}

		@Override
		public Class<ParamException> targetException() {
			return ParamException.class;
		}

		@Override
		public CommonResponse<?> handle(ParamException e, HttpServletRequest request, Object handler) {
			return CommonResponse.fail(ResultCode.PARAM_ERR, e.getErrMsg());
		}
	}

	private static final class NoHandlerFoundExceptionHandler implements ExceptionHandler<NoHandlerFoundException> {
		public static final NoHandlerFoundExceptionHandler INSTANCE = new NoHandlerFoundExceptionHandler();

		private NoHandlerFoundExceptionHandler() {}

		@Override
		public Class<NoHandlerFoundException> targetException() {
			return NoHandlerFoundException.class;
		}

		@Override
		public CommonResponse<?> handle(NoHandlerFoundException e, HttpServletRequest request, Object handler) {
			return CommonResponse.fail(ResultCode.NOT_FOUND, "接口 [" + request.getRequestURI() + "] 不存在");
		}
	}

	private static final class ServletExceptionHandler implements ExceptionHandler<ServletException> {
		public static final ServletExceptionHandler INSTANCE = new ServletExceptionHandler();

		private ServletExceptionHandler() {

		}

		@Override
		public Class<ServletException> targetException() {
			return ServletException.class;
		}

		@Override
		public CommonResponse<?> handle(ServletException e, HttpServletRequest request, Object handler) {
			return CommonResponse.fail(ResultCode.FAIL, e.getMessage());
		}
	}


	private static final class MethodArgumentTypeMismatchExceptionHandler implements ExceptionHandler<MethodArgumentTypeMismatchException> {
		public static final MethodArgumentTypeMismatchExceptionHandler INSTANCE = new MethodArgumentTypeMismatchExceptionHandler();

		private MethodArgumentTypeMismatchExceptionHandler() {

		}

		@Override
		public Class<MethodArgumentTypeMismatchException> targetException() {
			return MethodArgumentTypeMismatchException.class;
		}

		@Override
		public CommonResponse<?> handle(MethodArgumentTypeMismatchException e, HttpServletRequest request, Object handler) {
			return CommonResponse.fail(ResultCode.FAIL, "传入参数类型不合法，参数名称" + e.getName());
		}
	}


	private static final class FlowValveExceptionHandler implements ExceptionHandler<FlowValveException> {
		public static final FlowValveExceptionHandler INSTANCE = new FlowValveExceptionHandler();

		private FlowValveExceptionHandler() {}

		@Override
		public Class<FlowValveException> targetException() {
			return FlowValveException.class;
		}

		@Override
		public CommonResponse<?> handle(FlowValveException e, HttpServletRequest request, Object handler) {
			return CommonResponse.fail(e.getCode(), e.getMsg());
		}
	}


	private static final class CommonLogicExceptionHandler implements ExceptionHandler<CommonLogicException> {

		public static final CommonLogicExceptionHandler INSTANCE = new CommonLogicExceptionHandler();

		private CommonLogicExceptionHandler() {}

		@Override
		public Class<CommonLogicException> targetException() {
			return CommonLogicException.class;
		}

		@Override
		public CommonResponse<?> handle(CommonLogicException e, HttpServletRequest request, Object handler) {
			String message = null;
			if(e.isSetMessage()) {
				message = e.getMessage();
			}
			ResultCode errorCodeEnum = e.getResultCode();
			Object[] text = e.getText();

			log.error("请求业务logic异常", e);

			return buildResponse(message, text, errorCodeEnum);
		}
	}


	private static final class CommonRuntimeExceptionHandler implements ExceptionHandler<CommonRuntimeException> {

		public static final CommonRuntimeExceptionHandler INSTANCE = new CommonRuntimeExceptionHandler();

		private CommonRuntimeExceptionHandler() {}

		@Override
		public Class<CommonRuntimeException> targetException() {
			return CommonRuntimeException.class;
		}

		@Override
		public CommonResponse<?> handle(CommonRuntimeException e, HttpServletRequest request, Object handler) {
			String message = null;
			if(e.isSetMessage()) {
				message = e.getMessage();
			}
			ResultCode errorCodeEnum = e.getResultCode();
			Object[] text = e.getText();

			log.error("请求业务runtime失败", e);
			return buildResponse(message, text, errorCodeEnum);
		}
	}

	private static CommonResponse buildResponse(String message, Object[] text, ResultCode errorCodeEnum) {
		if (errorCodeEnum != null) {
			if(StringUtils.isEmpty(message)) {
				return CommonResponse.fail(errorCodeEnum, text);
			} else {
				return CommonResponse.fail(errorCodeEnum, message);
			}
		} else {
			return CommonResponse.fail(ResultCode.INTERNAL_SERVER_ERROR, message);
		}
	}


	public static final class RhinoTimeoutExceptionHandler implements ExceptionHandler<RhinoTimeoutException> {
		public static final RhinoTimeoutExceptionHandler INSTANCE = new RhinoTimeoutExceptionHandler();

		private RhinoTimeoutExceptionHandler() {}

		@Override
		public Class<RhinoTimeoutException> targetException() {
			return RhinoTimeoutException.class;
		}

		@Override
		public CommonResponse<?> handle(RhinoTimeoutException e, HttpServletRequest request, Object handler) {
			return CommonResponse.fail(ResultCode.RETRY_INNER_FAIL);
		}
	}


	private static final class FallbackExceptionHandler implements ExceptionHandler<FallbackException> {

		public static final FallbackExceptionHandler INSTANCE = new FallbackExceptionHandler();

		private FallbackExceptionHandler() {

		}

		@Override
		public Class<FallbackException> targetException() {
			return FallbackException.class;
		}

		@Override
		public CommonResponse<?> handle(FallbackException e, HttpServletRequest request, Object handler) {
			log.error("服务降级异常, detail = {}", e.getMessage());

			return CommonResponse.fail(ResultCode.INTERNAL_SERVER_ERROR);
		}
	}

	private static final class DefaultExceptionHandler implements ExceptionHandler<Exception> {
		public static final DefaultExceptionHandler INSTANCE = new DefaultExceptionHandler();

		private DefaultExceptionHandler() {}

		@Override
		public Class<Exception> targetException() {
			return Exception.class;
		}

		@Override
		public CommonResponse<?> handle(Exception e, HttpServletRequest request, Object handler) {
			String message;
			if (handler instanceof HandlerMethod) {
				HandlerMethod handlerMethod = (HandlerMethod) handler;
				message = String.format("接口 [%s] 出现异常，方法：%s.%s，异常摘要：%s",
						request.getRequestURI(),
						handlerMethod.getBean().getClass().getName(),
						handlerMethod.getMethod().getName(),
						e.getMessage());
				log.error("api interface error and msg={}", message);
				message = ResultCode.PARAM_ERR.getErrorMessage();
			} else {
				message = e.getMessage();
			}
			log.error(message, e);

			return CommonResponse.fail(ResultCode.INTERNAL_SERVER_ERROR, message);
		}
	}


	private void catEventStatistic(Exception ex, HttpServletRequest request) {
		if (MccConfigUtil.getCatEventStatisticSwitch()) {
			String HTTP_REQUEST_ERROR_CAT_EVENT = "HttpRequestErrorCatEvent";
			//正常情况下tenantId不会为空，这里设置默认租户id为-1
			String tenantId = org.apache.commons.lang3.StringUtils.isBlank(request.getHeader("tenantid")) ? "-1" : request.getHeader("tenantid");
			Transaction transaction = Cat.newTransaction(tenantId + "_" + HTTP_REQUEST_ERROR_CAT_EVENT, tenantId + "_" + request.getServletPath());
			transaction.setStatus(ex);
			transaction.complete();
		}
	}
}
