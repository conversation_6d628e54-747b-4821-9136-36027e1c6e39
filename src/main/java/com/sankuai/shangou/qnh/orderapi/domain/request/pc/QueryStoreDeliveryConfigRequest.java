package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description: 查询门店/仓库的聚合配送设置请求体
 * @Author: zhangjian155
 * @Date: 2022/10/11 15:20
 */
@TypeDoc(
        description = "查询门店/仓库的聚合配送设置请求体"
)
@ApiModel("查询门店/仓库的聚合配送设置请求体")
@Data
public class QueryStoreDeliveryConfigRequest implements BaseRequest {

    @FieldDoc(
            description = "门店ID"
    )
    @ApiModelProperty(value = "门店ID", required = true)
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "租户ID"
    )
    @ApiModelProperty(value = "租户ID", required = true)
    @NotNull
    private Long tenantId;
}
