package com.sankuai.shangou.qnh.orderapi.domain.vo.app;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/04/10 21:26
 */
@TypeDoc(
        description = "聚合运力配送跳转模块信息"
)
@Data
@ApiModel("聚合运力配送跳转模块信息")
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryRedirectModuleVo {

    @FieldDoc(
            description = "提示文案", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "提示文案", required = true)
    private String title;

    @FieldDoc(
            description = "链接文案", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "链接文案", required = true)
    private String urlText;

    @FieldDoc(
            description = "跳转url", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "跳转url", required = true)
    private String url;

    @FieldDoc(
            description = "是否展示跳转按钮", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否展示跳转按钮", required = false)
    private Boolean showButton;
}