package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.DeliveryCompleteInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.OrderDetailVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.PickInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.PickItemVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/7/11
 * desc: 查询订单详情响应
 */
@TypeDoc(
        description = "查询订单详情响应"
)
@ApiModel("查询订单详情响应")
@Data
public class OrderDetailResponse {

    @FieldDoc(
            description = "订单详情", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单详情", required = true)
    private OrderDetailVO orderDetail;

    @FieldDoc(
            description = "配送完成信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "配送完成信息")
    private DeliveryCompleteInfoVO deliveryCompleteInfo;

    @FieldDoc(
            description = "拣货相关信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "拣货相关信息")
    private PickInfoVO pickInfo;

    @FieldDoc(
            description = "拣货复核照片（支持多张照片）", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "拣货复核照片（支持多张照片）")
    @Deprecated
    private List<String> pickingCheckPictureUrlList;

    @FieldDoc(
            description = "拣货项信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "拣货项信息")
    @Deprecated
    private List<PickItemVO> pickItemInfoList;

}
