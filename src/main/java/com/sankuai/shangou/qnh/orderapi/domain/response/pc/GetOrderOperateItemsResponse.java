package com.sankuai.shangou.qnh.orderapi.domain.response.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@TypeDoc(description = "查询订单可操作列表响应")
@Data
@ApiModel("查询订单可操作列表响应")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetOrderOperateItemsResponse {

    @FieldDoc(description = "渠道订单号", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "渠道订单号", required = true)
    private String channelOrderId;

    @FieldDoc(description = "渠道类型", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "渠道类型", required = true)
    private Integer channelType;

    @FieldDoc(description = "可选操作", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "可选操作", required = true)
    private Integer operateItem;
}
