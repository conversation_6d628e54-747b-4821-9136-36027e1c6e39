package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import lombok.Data;

/**
 * @Author: <EMAIL>
 * @Date: 2019/4/22 15:45
 * @Description:
 */
@Data
public class RefundRequest implements BaseRequest {

    private String orderId;

    private String channelId;

    private int reasonCode;

    private String reason;

    /**
     * 服务单号
     */
    private String serviceId;


    @Override
    public void selfCheck() {
        AssertUtil.notEmpty(orderId,"订单号不能为空");
        AssertUtil.notEmpty(channelId, "channelId不能为空");
    }
}
