package com.sankuai.shangou.qnh.orderapi.domain.request.pc;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.ToString;

@TypeDoc(
        description = "查询履约看板请求"
)
@ApiModel("查询履约看板请求")
@Data
@ToString
public class OrderDetailReportReq {


    @FieldDoc(
            description = "租户ID"
    )

    public Long tenantId;

    @FieldDoc(
            description = "门店Id"
    )
    public Long shopId;

    @FieldDoc(
            description = "仓ID", requiredness = Requiredness.OPTIONAL
    )
    private Long warehouseId;
    @FieldDoc(
            description = "筛选类型 0：骑手到店未拣货,1：骑手超时未接单", requiredness = Requiredness.REQUIRED
    )
    private int exceptionType = 0;

}
