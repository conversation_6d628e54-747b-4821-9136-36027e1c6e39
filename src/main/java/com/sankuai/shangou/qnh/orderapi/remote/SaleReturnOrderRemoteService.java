package com.sankuai.shangou.qnh.orderapi.remote;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.meituan.reco.pickselect.common.cat.CatTransaction;
import com.meituan.reco.pickselect.common.exception.FallbackException;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.store.management.enums.ResultCodeEnum;
import com.sankuai.meituan.reco.store.management.thrift.common.BaseResult;
import com.sankuai.meituan.reco.store.management.thrift.common.CommonParam;
import com.sankuai.meituan.reco.store.management.thrift.common.UserParam;
import com.sankuai.meituan.reco.store.management.thrift.scmdelivery.SaleReturnOrderThriftService;
import com.sankuai.meituan.reco.store.management.thrift.scmdelivery.dto.SaleReturnOrderKey;
import com.sankuai.meituan.reco.store.management.thrift.scmdelivery.dto.TSaleReturnOrderInfo;
import com.sankuai.meituan.reco.store.management.thrift.scmdelivery.req.SaleReturnOrderCloseReq;
import com.sankuai.meituan.reco.store.management.thrift.scmdelivery.req.SaleReturnOrderCreationPreCheckReq;
import com.sankuai.meituan.reco.store.management.thrift.scmdelivery.req.SaleReturnOrderQueryBySaleOrderReq;
import com.sankuai.meituan.reco.store.management.thrift.scmdelivery.resp.SaleReturnOrderCreationPreCheckResp;
import com.sankuai.meituan.reco.store.management.thrift.scmdelivery.resp.SaleReturnOrderQueryBySaleOrderResp;
import com.sankuai.shangou.qnh.orderapi.context.app.ApiMethodParamThreadLocal;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import com.sankuai.shangou.qnh.orderapi.converter.app.SaleReturnConverter;
import com.sankuai.shangou.qnh.orderapi.domain.dto.app.IdentityInfo;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.CloseSaleReturnOrderRequest;
import com.sankuai.shangou.qnh.orderapi.domain.response.CommonResponse;
import com.sankuai.shangou.qnh.orderapi.exception.pc.CommonRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/12/7
 */
@Rhino
@Slf4j
@Component
public class SaleReturnOrderRemoteService {

	@Resource
	private SaleReturnOrderThriftService saleReturnOrderThriftService;

	@CatTransaction
	@MethodLog(logRequest = true, logResponse = true)
	@Degrade(rhinoKey = "SaleReturnOrderRemoteService.querySaleReturnOrdersBySaleOrder",
			fallBackMethod = "querySaleReturnOrdersBySaleOrderFallback",
			timeoutInMilliseconds = 5000, errorThresholdCount = 10)
	public Map<SaleReturnOrderKey, TSaleReturnOrderInfo> querySaleReturnOrdersBySaleOrder(Long tenantId, Long storeId, Integer orderBizType, String viewOrderId) {
		SaleReturnOrderQueryBySaleOrderReq request = SaleReturnOrderQueryBySaleOrderReq.builder()
				.tenantId(tenantId)
				.storeId(storeId)
				.saleOrderBizType(orderBizType)
				.saleOrderViewId(viewOrderId)
				.build();
		SaleReturnOrderQueryBySaleOrderResp response = saleReturnOrderThriftService.querySaleReturnOrderBySaleOrder(request);
		checkResponse(response.getStatus());
		return Optional.ofNullable(response.getSaleReturnOrders())
				.orElse(new ArrayList<>())
				.stream()
				.collect(Collectors.toMap(TSaleReturnOrderInfo::getSaleReturnOrderKey, Function.identity(), (o1, o2) -> o2));
	}

	private Map<SaleReturnOrderKey, TSaleReturnOrderInfo> querySaleReturnOrdersBySaleOrderFallback(Long tenantId, Long storeId, Integer orderBizType, String viewOrderId) {
		throw new FallbackException("SaleReturnOrderWrapper.querySaleReturnOrdersBySaleOrder 熔断降级");
	}

	@CatTransaction
	@MethodLog(logRequest = true, logResponse = true)
	@Degrade(rhinoKey = "SaleReturnOrderRemoteService.canCreateSaleReturnOrder", fallBackMethod = "canCreateSaleReturnOrderFallback",
			timeoutInMilliseconds = 5000, errorThresholdCount = 10)
	public boolean canCreateSaleReturnOrder(Long tenantId, Long storeId, Integer orderBizType, String viewOrderId) {
		SaleReturnOrderCreationPreCheckReq request = new SaleReturnOrderCreationPreCheckReq(tenantId, storeId, orderBizType, viewOrderId);
		SaleReturnOrderCreationPreCheckResp response = saleReturnOrderThriftService.preCheckSaleReturnOrderCreation(request);
		checkResponse(response.getStatus());
		return response.isCanCreateSaleReturnOrder();
	}

	private boolean canCreateSaleReturnOrderFallback(Long tenantId, Long storeId, Integer orderBizType, String viewOrderId) {
		throw new CommonRuntimeException("SaleReturnOrderWrapper.canCreateSaleReturnOrder 熔断降级");
	}

	@CatTransaction
	@MethodLog(logRequest = true, logResponse = true)
	@Degrade(rhinoKey = "SaleReturnOrderRemoteService.getSaleReturnOrderNo", fallBackMethod = "getSaleReturnOrderNoFallback",
			timeoutInMilliseconds = 5000, errorThresholdCount = 10)
	public SaleReturnOrderQueryBySaleOrderResp getSaleReturnOrderNo(Long tenantId, Long storeId, Integer orderBizType, String viewOrderId, String afterSaleId) {
		SaleReturnOrderQueryBySaleOrderReq request = SaleReturnOrderQueryBySaleOrderReq.builder()
				.tenantId(tenantId)
				.storeId(storeId)
				.saleOrderBizType(orderBizType)
				.saleOrderViewId(viewOrderId)
				.saleReturnId(afterSaleId)
				.build();
		SaleReturnOrderQueryBySaleOrderResp resp = saleReturnOrderThriftService.querySaleReturnOrderBySaleOrder(request);
		log.info("SaleReturnOrderWrapper.getSaleReturnOrderNo request:{}, response:{}", request, resp);
		return resp;
	}

	private boolean getSaleReturnOrderNoFallback(Long tenantId, Long storeId, Integer orderBizType, String viewOrderId, String afterSaleId) {
		throw new CommonRuntimeException("SaleReturnOrderWrapper.getSaleReturnOrderNo 熔断降级");
	}

	private void checkResponse(BaseResult result) {
		if (result.getCode() != ResultCodeEnum.SUCCESS.getCode()) {
			throw new CommonRuntimeException(result.getMsg());
		}
	}

	@CatTransaction
	@MethodLog(logRequest = true, logResponse = true)
	@Degrade(rhinoKey = "SaleReturnOrderRemoteService.webCloseSaleReturnOrder", fallBackMethod = "webCloseSaleReturnOrderFallback",
			timeoutInMilliseconds = 1000)
	public CommonResponse<Void> webCloseSaleReturnOrder(CloseSaleReturnOrderRequest request) throws TException {
		BaseResult baseResult = saleReturnOrderThriftService.closeOrder(convertReq(request));
		log.info("销退单关单操作执行结果：baseResult=[{}].", baseResult);
		return new CommonResponse<>(baseResult.getCode(), baseResult.getMsg(), null);
	}

	private CommonResponse<Void> webCloseSaleReturnOrderFallback(CloseSaleReturnOrderRequest request) throws TException {
		throw new com.meituan.reco.pickselect.common.exception.FallbackException("SaleReturnWrapper.webCloseSaleReturnOrder 熔断降级");
	}

	private SaleReturnOrderCloseReq convertReq(CloseSaleReturnOrderRequest orderRequest) {
		CommonParam commonParam = new CommonParam(orderRequest.getTenantId(), orderRequest.getStoreId(), orderRequest.getEntityId(), orderRequest.getEntityType());
		UserParam userParam = new UserParam(String.valueOf(ContextHolder.currentUserStaffId()), ContextHolder.currentUserName(), ContextHolder.currentUserStaffId());
		SaleReturnOrderCloseReq closeReq = new SaleReturnOrderCloseReq();
		closeReq.setCommonParam(commonParam);
		closeReq.setUserParam(userParam);
		closeReq.setSaleReturnOrderNo(orderRequest.getSaleReturnOrderNo());
		return closeReq;
	}

	@CatTransaction
	@MethodLog(logRequest = true, logResponse = true)
	@Degrade(rhinoKey = "SaleReturnOrderRemoteService.appCloseSaleReturnOrder", fallBackMethod = "appCloseSaleReturnOrderFallback",
			timeoutInMilliseconds = 1000)
	public com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse<Void> appCloseSaleReturnOrder(com.sankuai.shangou.qnh.orderapi.domain.request.app.CloseSaleReturnOrderRequest request) throws TException {
		IdentityInfo user = ApiMethodParamThreadLocal.getIdentityInfo();

		BaseResult baseResult = saleReturnOrderThriftService.closeOrder(SaleReturnConverter.convertReq(request, user));
		log.info("销退单关单操作执行结果：baseResult=[{}].", baseResult);

		return new com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse<>(baseResult.getCode(), baseResult.getMsg(), null);
	}

	private com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse<Void> appCloseSaleReturnOrderFallback(com.sankuai.shangou.qnh.orderapi.domain.request.app.CloseSaleReturnOrderRequest request) throws TException {
		throw new com.meituan.reco.pickselect.common.exception.FallbackException("SaleReturnWrapper.appCloseSaleReturnOrder 熔断降级");
	}

}
