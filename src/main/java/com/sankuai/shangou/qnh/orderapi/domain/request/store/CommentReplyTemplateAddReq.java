package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.exception.store.CommonRuntimeException;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@TypeDoc(
        description = "添加评价回复模板请求参数",
        authors = "hejunliang"
)
@Data
public class CommentReplyTemplateAddReq {

    @FieldDoc(
            description = "模板内容", requiredness = Requiredness.REQUIRED
    )
    private String templateContent;

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    private String storeId;

    public void validate() {
        if (StringUtils.isEmpty(this.templateContent)) {
            throw new CommonRuntimeException("模板内容不能为空", ResultCode.CHECK_PARAM_ERR);
        }

        if (StringUtils.isEmpty(this.storeId)) {
            throw new CommonRuntimeException("门店Id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
    }
}
