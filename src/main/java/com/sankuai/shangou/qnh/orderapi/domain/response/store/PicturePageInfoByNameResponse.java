package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.PageInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: WangSukuan
 * @create: 2019-11-05
 **/
@TypeDoc(
        description = "根据商品名称分页查询图片响应"
)
@Data
@ApiModel("根据商品名称分页查询图片响应")
public class PicturePageInfoByNameResponse {

    @FieldDoc(
            description = "分页信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "分页信息", required = true)
    private PageInfoVO pageInfo;

    @FieldDoc(
            description = "当前页内容", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "当前页内容", required = true)
    private List<String> picList;

}
