package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 中台门店解绑配送商门店请求.
 *
 * <AUTHOR>
 * @since 2021/3/8 14:30
 */
@Data
public class UnbindDeliveryCompanyPoiOnAggrRequest implements BaseRequest {
    @FieldDoc(
            description = "门店ID"
    )
    @ApiModelProperty(value = "门店ID", required = true)
    private Long poiId;

    @FieldDoc(
            description = "配送商编码"
    )
    @ApiModelProperty(value = "配送商编码")
    private Integer deliveryChannelId;

    @FieldDoc(
            description = "配送商门店ID"
    )
    @ApiModelProperty(value = "配送商门店ID")
    private String deliveryChannelPoiId;

    @Override
    public void selfCheck() {
        AssertUtil.notNull(poiId, "门店ID不能为空", "poiId");
        AssertUtil.notNull(deliveryChannelId, "配送商Code不能为空", "deliveryChannelId");
        AssertUtil.notNull(deliveryChannelPoiId, "配送商门店ID不能为空", "deliveryChannelPoiId");
    }
}
