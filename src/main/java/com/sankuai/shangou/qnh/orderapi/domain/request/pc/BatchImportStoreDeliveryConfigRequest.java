package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 批量修改门店配送配置请求体
 * <AUTHOR>
 */
@TypeDoc(
        description = "批量修改门店配送配置请求体"
)
@ApiModel("批量修改门店配送配置请求体")
@Data
public class BatchImportStoreDeliveryConfigRequest implements BaseRequest {

    @FieldDoc(
            description = "文件链接"
    )
    @ApiModelProperty(value = "文件链接", required = true)
    private String fileUrl;

    @Override
    public void selfCheck() {
        AssertUtil.notEmpty(fileUrl, "文件链接不能为空");
    }
}
