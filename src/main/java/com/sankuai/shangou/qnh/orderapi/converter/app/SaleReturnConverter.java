package com.sankuai.shangou.qnh.orderapi.converter.app;

import com.sankuai.meituan.reco.store.management.thrift.common.CommonParam;
import com.sankuai.meituan.reco.store.management.thrift.common.UserParam;
import com.sankuai.meituan.reco.store.management.thrift.scmdelivery.req.SaleReturnOrderCloseReq;
import com.sankuai.shangou.qnh.orderapi.domain.dto.app.IdentityInfo;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.CloseSaleReturnOrderRequest;

public class SaleReturnConverter {

    public static SaleReturnOrderCloseReq convertReq(CloseSaleReturnOrderRequest orderRequest, IdentityInfo user) {
        CommonParam commonParam = new CommonParam(orderRequest.getTenantId(), orderRequest.getStoreId(),
                orderRequest.getEntityId(), orderRequest.getEntityType());

        UserParam userParam = new UserParam(String.valueOf(user.getEmployeeId()), user.getEmployeeName(),
                user.getEmployeeId());

        SaleReturnOrderCloseReq closeReq = new SaleReturnOrderCloseReq();
        closeReq.setCommonParam(commonParam);
        closeReq.setUserParam(userParam);
        closeReq.setSaleReturnOrderNo(orderRequest.getSaleReturnOrderNo());

        return closeReq;
    }
}
