package com.sankuai.shangou.qnh.orderapi.service.common;


import com.alibaba.fastjson.JSON;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.OrderFuseFinanceDetailBO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.SubProductVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.*;
import com.sankuai.shangou.qnh.orderapi.service.pc.FuseOrderService;
import com.sankuai.shangou.qnh.orderapi.utils.CombinationProductUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @since 2024/7/15
 **/
@Service
@Slf4j
public class ComposeProductService {
    @Resource
    private FuseOrderService fuseOrderService;

    public void dealRefundComposeProduct(List<RefundApplyRecordVO> refundApplyRecordVOList) {
        try {
            List<Long> serviceIds = getServiceIdListHasCompose(refundApplyRecordVOList);
            if(CollectionUtils.isEmpty(serviceIds)){
                return;
            }
            RefundApplyRecordVO refundApplyRecordVO = refundApplyRecordVOList.get(0);
            OrderVO orderVO = refundApplyRecordVO.getOrderVO();
            if(Objects.isNull(orderVO) || Objects.isNull(orderVO.getStoreId()) || Objects.isNull(orderVO.getTenantId())){
                log.info("dealRefundComposeProduct 存在组合商品，shopId or tenantId is null is error");
                return;
            }

            List<OrderFuseFinanceDetailBO> orderFuseFinanceDetailBOList = fuseOrderService.queryFinanceDetails(serviceIds, orderVO.getStoreId(),
                    orderVO.getTenantId(), 2);
            log.info("OrderService.dealRefundComposeProduct orderFuseFinanceDetailBOList: {}", JSON.toJSONString(orderFuseFinanceDetailBOList));

            if(CollectionUtils.isEmpty(orderFuseFinanceDetailBOList)){
                return;
            }

            Map<Long, OrderFuseFinanceDetailBO> orderFuseFinanceDetailBOMap = orderFuseFinanceDetailBOList.stream()
                    .filter(item -> Objects.nonNull(item.getServiceId()))
                    .collect(Collectors.toMap(OrderFuseFinanceDetailBO::getServiceId, value -> value));

            // 二次转换
            Map<Long, List<OrderFuseFinanceDetailBO.ComposeItemFinanceInfo>> financeComposeItemMap = orderFuseFinanceDetailBOMap.values().stream()
                    .filter(list-> CollectionUtils.isNotEmpty(list.getItemInfos()))
                    .flatMap(list-> list.getItemInfos().stream())
                    .filter(item -> Objects.nonNull(item.getOrderItemId()) && CollectionUtils.isNotEmpty(item.getComposeItemFinanceInfos()))
                    .collect(Collectors.toMap(OrderFuseFinanceDetailBO.ItemFinanceInfo::getOrderItemId, OrderFuseFinanceDetailBO.ItemFinanceInfo::getComposeItemFinanceInfos));

            refundApplyRecordVOList.forEach(refundApplyRecord -> {
                setComposeProduct(refundApplyRecord, financeComposeItemMap);

            });


        }catch (Exception e){
            log.info("OrderService.dealRefundComposeProduct is error e= ", e);
        }
    }

    private void setComposeProduct(RefundApplyRecordVO refundApplyRecord, Map<Long, List<OrderFuseFinanceDetailBO.ComposeItemFinanceInfo>> financeComposeItemMap) {
        try {
            log.info("OrderService.setComposeProduct financeComposeItemMap: {}", JSON.toJSONString(financeComposeItemMap));
            if(financeComposeItemMap.isEmpty()){
                return;
            }

            //获取正单组合品数据
            Map<Long, List<SubProductVo>> dueComposeListMap = getDueComposeListMap(refundApplyRecord);

            // 处理 OrderVo /OrderRefundInfo 下 waitAuditRefund & waitAuditRefundList
            dealOrderVoOrderRefundInfoCompose(refundApplyRecord.getOrderVO(), dueComposeListMap, financeComposeItemMap);
            dealRefundApplyRecordDetailVOList(refundApplyRecord.getRefundApplyRecordDetailVOList(), dueComposeListMap, financeComposeItemMap);

        }catch (Exception e){
            log.info("OrderService.setComposeProduct is error orderVO: {}  ,e=", refundApplyRecord.getOrderVO(), e);
        }
    }

    private void dealOrderVoOrderRefundInfoCompose(OrderVO orderVO, Map<Long, List<SubProductVo>> dueComposeListMap, Map<Long, List<OrderFuseFinanceDetailBO.ComposeItemFinanceInfo>> financeComposeItemMap) {
        try {
            if(Objects.isNull(orderVO) || Objects.isNull(orderVO.getOrderRefundInfo())){
                return;
            }
            OrderRefundInfo orderRefundInfo = orderVO.getOrderRefundInfo();
            dealWaitAuditRefundCompose(orderRefundInfo.getWaitAuditRefund(), dueComposeListMap, financeComposeItemMap);
            dealWaitAuditRefundListCompose(orderRefundInfo.getWaitAuditRefundList(), dueComposeListMap, financeComposeItemMap);

        }catch (Exception e){
            log.info("OrderService.dealOrderVoOrderRefundInfoCompose is error e= ", e);
        }
    }

    private void dealWaitAuditRefundListCompose(List<RefundingRecordVO> waitAuditRefundList, Map<Long, List<SubProductVo>> dueComposeListMap, Map<Long, List<OrderFuseFinanceDetailBO.ComposeItemFinanceInfo>> financeComposeItemMap) {
        try {
            if(CollectionUtils.isEmpty(waitAuditRefundList)){
                return;
            }
            waitAuditRefundList.forEach(item -> {
                if(CollectionUtils.isEmpty(item.getRefundApplyRecordDetailVOList())){
                    return;
                }
                item.getRefundApplyRecordDetailVOList().forEach(refundApplyRecordDetailVO -> {
                    List<SubProductVo> subProductVoList = refundApplyRecordDetailVO.getSubProductVoList();
                    if(CollectionUtils.isEmpty(subProductVoList)){
                        return;
                    }
                    Long composeOrderItemId = subProductVoList.get(0).getComposeOrderItemId();
                    if(Objects.isNull(composeOrderItemId)){
                        log.info("OrderService.dealWaitAuditRefundListCompose subProductVo composeOrderItemId is null");
                        return;
                    }
                    CombinationProductUtil.dealAppRefundSaleAfterRecordCompose(subProductVoList, dueComposeListMap.get(composeOrderItemId), financeComposeItemMap.get(composeOrderItemId));
                });
            });
        }catch (Exception e){

        }
    }

    private void dealWaitAuditRefundCompose(RefundingRecordVO waitAuditRefund, Map<Long, List<SubProductVo>> dueComposeListMap, Map<Long, List<OrderFuseFinanceDetailBO.ComposeItemFinanceInfo>> financeComposeItemMap) {
        try {
            if(Objects.isNull(waitAuditRefund) || CollectionUtils.isEmpty(waitAuditRefund.getRefundApplyRecordDetailVOList())){
                return;
            }
            waitAuditRefund.getRefundApplyRecordDetailVOList().forEach(item -> {
                List<SubProductVo> subProductVoList = item.getSubProductVoList();
                if(CollectionUtils.isEmpty(subProductVoList)){
                    return;
                }
                Long composeOrderItemId = subProductVoList.get(0).getComposeOrderItemId();
                if(Objects.isNull(composeOrderItemId)){
                    log.info("OrderService.dealWaitAuditRefundCompose subProductVo composeOrderItemId is null");
                    return;
                }
                CombinationProductUtil.dealAppRefundSaleAfterRecordCompose(subProductVoList, dueComposeListMap.get(composeOrderItemId), financeComposeItemMap.get(composeOrderItemId));
            });
        }catch (Exception e){

        }
    }

    private void dealRefundApplyRecordDetailVOList(List<RefundApplyRecordDetailVO> refundApplyRecordDetailVOList, Map<Long, List<SubProductVo>> dueComposeListMap, Map<Long, List<OrderFuseFinanceDetailBO.ComposeItemFinanceInfo>> financeComposeItemMap) {
        if(CollectionUtils.isEmpty(refundApplyRecordDetailVOList)){
            return;
        }

        refundApplyRecordDetailVOList.forEach(refundApplyRecordDetailVO -> {
            List<SubProductVo> subProductVoList = refundApplyRecordDetailVO.getSubProductVoList();
            if(CollectionUtils.isEmpty(subProductVoList)){
                return;
            }
            log.info("OrderService.dealRefundApplyRecordDetailVOList subProductVoList: {}", JSON.toJSONString(subProductVoList));
            Long composeOrderItemId = subProductVoList.get(0).getComposeOrderItemId();
            if(Objects.isNull(composeOrderItemId)){
                log.info("OrderService.dealRefundApplyRecordDetailVOList refundApplyRecordDetailVO subProductVo composeOrderItemId is null");
                return;
            }
            CombinationProductUtil.dealAppRefundSaleAfterRecordCompose(subProductVoList, dueComposeListMap.get(composeOrderItemId), financeComposeItemMap.get(composeOrderItemId));
        });
    }

    private Map<Long, List<SubProductVo>> getDueComposeListMap(RefundApplyRecordVO refundApplyRecord) {
        Map<Long, List<SubProductVo>> dueComposeListMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(refundApplyRecord.getOrderVO().getProductList())){
            refundApplyRecord.getOrderVO().getProductList().forEach(item -> {
                if(CollectionUtils.isEmpty(item.getSubProductVoList())){
                    return;
                }
                List<SubProductVo> subProductVoList = item.getSubProductVoList();
                log.info("OrderService.getDueComposeListMap subProductVoList get first: {}", subProductVoList.get(0));
                Long composeOrderItemId = subProductVoList.get(0).getComposeOrderItemId();
                if(Objects.isNull(composeOrderItemId)){
                    log.info("OrderService.getDueComposeListMap composeOrderItemId is null");
                    return;
                }
                dueComposeListMap.put(composeOrderItemId, subProductVoList);
            });
        }
        return dueComposeListMap;
    }

    private List<Long> getServiceIdListHasCompose(List<RefundApplyRecordVO> refundApplyRecordVOList) {
        Set<Long> listSet = new HashSet<>();
        try {
            for(RefundApplyRecordVO refundApplyRecordVO : refundApplyRecordVOList){
                // serviceId需要从refundApplyRecordDetailVOList中获取,顺便判断有组合品的才加入list用于后续查询
                if(CollectionUtils.isNotEmpty(refundApplyRecordVO.getRefundApplyRecordDetailVOList())){
                    for (RefundApplyRecordDetailVO refundingRecordVO : refundApplyRecordVO.getRefundApplyRecordDetailVOList()) {
                        // 退单中存在组合品的serviceId才加入列表
                        if(CollectionUtils.isNotEmpty(refundingRecordVO.getSubProductVoList())){
                            listSet.add(refundingRecordVO.getServiceId());
                        }
                    }
                }
            }

            return new ArrayList<>(listSet);

        }catch (Exception e){
            log.warn("OrderService.getServiceIdListHasCompose is error afterSaleRecordList: {} , e= ", JSON.toJSONString(refundApplyRecordVOList), e);
        }
        return new ArrayList<>(listSet);
    }


}
