package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TypeDoc(
        description = "商品模糊查询信息集"
)
@Data
@ApiModel("商品模糊查询信息集")
public class QuoteSkuInfoQueryVO {

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称", required = true)
    private String skuName;

    @FieldDoc(
            description = "商品ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品ID", required = true)
    private String skuId;

    @FieldDoc(
            description = "店铺ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "店铺ID", required = true)
    private Long storeId;
}
