package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.QnhKaTenantChannelVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/6 11:26 AM
 * @description
 */
@TypeDoc(
        description = "牵牛花ka租户渠道配置项集"
)
@ApiModel("牵牛花ka租户渠道配置项集")
@Data
public class QnhKaTenantChannelConfig {

    private List<QnhKaTenantChannelVO> configs = new ArrayList<>();
}
