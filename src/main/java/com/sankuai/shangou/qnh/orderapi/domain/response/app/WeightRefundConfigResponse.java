package com.sankuai.shangou.qnh.orderapi.domain.response.app;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.WeightRefundRevenueConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: <EMAIL>
 * @Date: 2020-03-25 15:17
 * @Description:
 */
@TypeDoc(
        description = "退差价联动摊位营收响应"
)
@ApiModel("退差价联动摊位营收响应")
@Data
public class WeightRefundConfigResponse {
    @FieldDoc(
            description = "退差价联动摊位营收配置", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "退差价联动摊位营收配置", required = true)
    private WeightRefundRevenueConfig weightRefundRevenueConfig;
}
