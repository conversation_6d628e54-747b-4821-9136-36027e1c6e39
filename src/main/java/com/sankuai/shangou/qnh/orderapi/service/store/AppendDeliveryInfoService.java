package com.sankuai.shangou.qnh.orderapi.service.store;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.shangou.saas.order.platform.enums.BoolTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryEntityEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.QueryOrderDeliveryInfoKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryDetail;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryOrder;
import com.sankuai.shangou.qnh.orderapi.remote.DeliveryChannelRemoteService;
import com.sankuai.shangou.qnh.orderapi.remote.TmsRemoteService;
import jline.internal.TestAccessible;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service(value = "storeAppendDeliveryInfoService")
@Slf4j
public class AppendDeliveryInfoService {

    @Resource
    private TmsRemoteService tmsWrapper;

    @Autowired
    private DeliveryChannelRemoteService deliveryChannelWrapper;


    /**
     * 去tms系统查询实时的配送信息
     *
     * @param empowerOrderIds
     * @return
     */
    public Map<Long, TDeliveryDetail> queryRealTimeDeliveryInfo(List<QueryOrderDeliveryInfoKey> empowerOrderIds) {

        List<TDeliveryDetail> tDeliveryDetails = Lists.newArrayList();
        try {
            tDeliveryDetails = tmsWrapper.queryDeliveryInfoByOrderIds(empowerOrderIds);
        } catch (Exception e) {
            log.error("查询tms配送信息异常", e);
        }
        if (CollectionUtils.isEmpty(tDeliveryDetails)) {
            return Maps.newHashMap();
        }
        Map<Long, TDeliveryDetail> realDeliveryMap=tDeliveryDetails.stream().collect(Collectors.toMap(info -> info.bizOrderId, info -> info, (newEntry, oldEntry) -> newEntry));
        Set<Integer> channelCodes = tDeliveryDetails.stream().filter(e -> !deliveryChannelWrapper.checkTenantAndStore(Objects.isNull(empowerOrderIds.get(0))? null : empowerOrderIds.get(0).tenantId, null)).map(e-> e.deliveryChannelCode).collect(Collectors.toSet());
        Map<Integer, String> channelNameMap = deliveryChannelWrapper.getDeliveryChannelNameMap(channelCodes);
        List<Long> doneOrderIdList=new ArrayList<>();
        for (Long orderId : realDeliveryMap.keySet()){
            TDeliveryDetail real=realDeliveryMap.get(orderId);
            if(real.status==null){
                continue;
            }
            if(real.status!= DeliveryStatusEnum.DELIVERY_CANCELLED.getCode()){
                continue;
            }
            if(real.deliveryCount==null || real.deliveryCount==1){
                continue;
            }
            doneOrderIdList.add(orderId);
        }
        if(CollectionUtils.isEmpty(doneOrderIdList)){
            return realDeliveryMap;
        }
        List<TDeliveryOrder> doneDeliveryList = Lists.newArrayList();
        try {
            doneDeliveryList=tmsWrapper.queryActiveDeliveryInfoByOrderIds(doneOrderIdList);
        }catch (Exception e){
            log.error("queryActiveDeliveryInfoByOrderIds error doneOrderIdList:{}",doneOrderIdList,e);
        }
        if(CollectionUtils.isEmpty(doneDeliveryList)){
            return realDeliveryMap;
        }
        Map<Long,TDeliveryOrder> orderMap=new HashMap<>();
        for (TDeliveryOrder order : doneDeliveryList){
            orderMap.put(order.getOrderId(),order);
        }
        for (Long orderId : doneOrderIdList){
            TDeliveryOrder order=orderMap.get(orderId);
            TDeliveryDetail detail=realDeliveryMap.get(orderId);
            realDeliveryMap.put(orderId,convertToDetail(order,detail,channelNameMap));
        }
        return realDeliveryMap;
    }

    private TDeliveryDetail convertToDetail(TDeliveryOrder domain,TDeliveryDetail otherDetail,Map<Integer,String> channelNameMap){
        String channelName = null;
        if (!deliveryChannelWrapper.checkTenantAndStore(domain.getTenantId(), domain.getStoreId())){
            channelName = channelNameMap.getOrDefault(domain.getDeliveryChannel(),null);
        }else {
            channelName = Objects.isNull(DeliveryChannelEnum.valueOf(domain.getDeliveryChannel()))? null : DeliveryChannelEnum.valueOf(domain.getDeliveryChannel()).getName();
        }
        return TDeliveryDetail.builder().deliveryFee(domain.getDeliveryFee() == null? null : domain.getDeliveryFee().doubleValue())
                .deliveryDistance(domain.getDistance())
                .bizOrderId(domain.getOrderId())
                .deliveryEntity(otherDetail.deliveryEntity)
                .deliveryChannelName(channelName)
                .riderName( domain.getRiderName())
                .status(domain.getStatus())
                .tLaunchDeliveryType(otherDetail.tLaunchDeliveryType)
                .riderPhone(domain.getRiderPhone())
                .deliveryChannelCode(Optional.ofNullable(domain.getDeliveryChannel()).orElse(DeliveryChannelEnum.AGGREGATION_DELIVERY.getCode()))
                .deliveryCount(domain.getDeliveryCount()==null ? 1:domain.getDeliveryCount())
                .tipFee(domain.getTipAmount() == null ? null : domain.getTipAmount())
                .build();
    }

}
