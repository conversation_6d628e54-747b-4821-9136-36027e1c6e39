package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.sankuai.meituan.shangou.saas.common.aop.feature.Validatable;
import com.sankuai.shangou.qnh.orderapi.constant.pc.Constants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import static com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil.*;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2018/10/23
 * 部门新增请求
 **/
@ApiModel("部门创建请求参数")
@Data
public class DepartmentCreateRequest implements Validatable {

    @ApiModelProperty(value = "部门类型(1部门2门店组6合作商)", required = true)
    protected Integer departmentType;

    @ApiModelProperty(value = "部门名称", required = true)
    protected String departmentName;


    @ApiModelProperty(value = "父部门ID(0标识根部门）", required = true)
    protected Long parentDepartmentId;

    @ApiModelProperty(value = "物理城市，即部门管理范围")
    protected List<DistrictSim> depMgtScope;

    @ApiModelProperty(value = "关联合作商的ID")
    protected Long associatedCommercialAgentId;

    @ApiModelProperty(value = "摊位结算方式")
    protected Integer settleType;

    @ApiModelProperty(value = "摊位备货属性，1-自采备货 2-外采备货")
    protected Integer boothPickingType;

    @ApiModelProperty(value = "银行编码")
    protected String bankCode;

    @ApiModelProperty(value = "银行名称")
    protected String bankName;


    @ApiModelProperty(value = "账号")
    protected String bankCardNo;

    @ApiModelProperty(value = "收款人姓名")
    protected String accountName;

    @ApiModelProperty(value = "微信和支付宝时对应付款码图片地址")
    private String qrCodeUrl;

    @ApiModelProperty(value = "门店ID")
    protected String poiId;

    @ApiModelProperty(value = "微信/支付宝账户名")
    protected String mobilePayAccount;

    @ApiModelProperty(value = "摊位结算比例")
    protected String boothSettleRatio;

    @ApiModelProperty(value = "摊位检索码")
    protected String boothQuickSearchCode;

    @ApiModelProperty(value = "摊位的现结支付宝账户")
    protected String boothOnsiteAlipayAccount;

    @ApiModelProperty(value = "摊位的现结支付宝账户的真实姓名")
    protected String boothOnsiteAlipayRealname;

    @Override
    public void validate() {
        // betweenClose(departmentType, 1, 5, "部门类型");
        numInSet(departmentType, new HashSet<>(Arrays.asList(1, 5, 6)), "部门类型无效");
        notEmpty(departmentName, "部门名称");
        switch (departmentType) {
            case Constants.DepTyps.BOOTH_TYPE:
                isTrue(StringUtils.isNotEmpty(poiId), "门店ID");
                notNull(boothPickingType, "摊位备货类型不可为空");
                break;
            case Constants.DepTyps.COMMERCIAL_AGENT_TYPE:
                isTrue( parentDepartmentId >= 0, "父部门ID参数无效");
                isPositiveNumber(associatedCommercialAgentId, "合作商节点需填写关联合作商");
                break;
            default:
                isTrue( parentDepartmentId >= 0, "父部门ID参数无效");
                break;
        }
        return;
    }

}
