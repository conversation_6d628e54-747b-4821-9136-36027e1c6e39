package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "系统信息"
)
@Data
@ApiModel("系统信息")
public class SystemInfoVO {

    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户ID", required = true)
    private Long tenantId;

    @FieldDoc(
            description = "门店库存配置设置类型 1-手动 2-自动", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店库存配置设置类型 1-手动 2-自动", required = true)
    private Integer stockSetType;

    @FieldDoc(
            description = "账号对应摊位ID  不是摊主则为空", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "账号对应摊位ID  不是摊主则为空", required = true)
    private String boothId;

    @FieldDoc(
            description = "渠道信息列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道信息列表", required = true)
    private List<ChannelInfoVO> channelInfos;

    @FieldDoc(
            description = "是否为无限库存 1-是 0-否", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "是否为无限库存", required = true)
    private Integer isInfiniteStock;

    @FieldDoc(
            description = "自动上架时间（从0点开始的毫秒数，比如1点对应3600000）", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "自动上架时间（从0点开始的毫秒数，比如1点对应3600000）", required = true)
    private Long autoUploadTime;

    @FieldDoc(
            description = "摊位模式(1一门店单摊位2一门店多摊位)", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "摊位模式(1一门店单摊位2一门店多摊位)")
    private Integer boothMode;

    @FieldDoc(
            description = "是否手动定价", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否手动定价")
    private Boolean manualPrice;

    @FieldDoc(
            description = "是否审核商品创建", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否审核商品创建")
    private Boolean productAudit;

    @FieldDoc(
            description = "是否精细化管理库存(true精细化管理库存——只有库存模块可设置库存,false非精细化管理库存——库存和商品模块均可设置库存)"
            , requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否精细化管理库存")
    private Boolean accurateStockManagement;

}
