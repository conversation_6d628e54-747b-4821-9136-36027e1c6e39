package com.sankuai.shangou.qnh.orderapi.service.pc;

import com.sankuai.meituan.shangou.empower.auth.thrift.enums.TokenTypeEmum;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.AccountLoginBo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.LoginTicket;

/**
 * 登录服务
 *
 * <AUTHOR>
 */
public interface LoginManagerService {

    /**
     * 登录
     *
     * @param uid 用户账号
     * @param password 密码
     * @return 用户信息
     */
    AccountLoginBo check(String uid, String password);

    /**
     * 迁移Epassport之后登录
     *
     * @param uid 用户账号
     * @param password 密码
     * @return 用户信息
     */
    AccountLoginBo accountCheck(String uid, String password);

    /**
     * 添加缓存凭证
     *
     * @param ticket 凭证
     * @param loginTicket 凭证数据
     * @param expireTimeInSeconds 过期时间
     * @return
     */
    void addLoginTicket(String ticket, LoginTicket loginTicket, int expireTimeInSeconds);

    /**
     * 获取缓存凭证数据
     *
     * @param ticket 凭证
     * @return
     */
    LoginTicket getLoginTicket(String ticket);

    /**
     * 刷新缓存凭证
     *
     * @param ticket 凭证
     * @param  expireTimeInSeconds 过期时间
     * @return
     */
    void refreshLoginTicket(String ticket, int expireTimeInSeconds);

    /**
     * 删除缓存凭证 仅支持 authToken _t
     *
     * @param ticket 凭证
     * @return
     */
    @Deprecated
    void removeLoginTicket(String ticket);

    /**
     * 登出
     * @param tokenType: token 类型
     * @param ticket token
     */
    void loginOut(TokenTypeEmum tokenType, String ticket);
}
