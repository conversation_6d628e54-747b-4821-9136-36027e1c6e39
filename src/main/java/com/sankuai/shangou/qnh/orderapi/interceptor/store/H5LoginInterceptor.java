// Copyright (C) 2020 Meituan
// All rights reserved
package com.sankuai.shangou.qnh.orderapi.interceptor.store;


import com.dianping.zebra.util.StringUtils;
import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.sankuai.shangou.qnh.orderapi.constant.store.LoginConstants;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.CommonResponse;
import com.sankuai.shangou.qnh.orderapi.context.store.H5LoginContext;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.H5LoginInfo;
import com.sankuai.shangou.qnh.orderapi.cache.store.H5LoginCache;
import com.sankuai.shangou.qnh.orderapi.utils.store.CookieUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.util.Objects;

/**
 * h5登录拦截器
 * <AUTHOR>
 * @version 1.0
 * @created 2020/3/2 下午12:08
 **/
@Slf4j
@Component
public class H5LoginInterceptor extends HandlerInterceptorAdapter {

    @Resource
    private H5LoginCache h5LoginCache;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        try {
            if ("options".equalsIgnoreCase(request.getMethod())) {
                return true;
            }

            // 验证登录信息
            String cookie = getCookieInfo(request);
            if (StringUtils.isBlank(cookie)) {
                processUnLogin(request, response);
                return false;
            }

            H5LoginInfo h5LoginInfo = h5LoginCache.getCookie(LoginConstants.H5_LOGIN_SESSION_PREFIX + cookie);
            if (Objects.isNull(h5LoginInfo)) {
                processUnLogin(request, response);
                return false;
            }
            processLogin(request, response, h5LoginInfo);
            return true;
        } catch (Exception e){
            log.error("H5LoginInterceptor error", e);
            return false;
        }
    }

    private String getCookieInfo(HttpServletRequest request) {
        // 由于safari不支持cookie写入，需将cookie信息写入header，但写入header，浏览器刷新后，缓存容易丢失，
        // 应前端体验要求，优先支持cookie写入，同时支持cookie写入Header
        String cookie = CookieUtil.getCookieValue(request, LoginConstants.H5_LOGIN_COOKIE_PREFIX);
        if (!StringUtils.isBlank(cookie)) {
            return cookie;
        }

        return request.getHeader(LoginConstants.H5_LOGIN_HEADER_PARAM);
    }

    private void processUnLogin(HttpServletRequest request, HttpServletResponse response) throws Exception{

        CommonResponse result = new CommonResponse(ResultCode.UNAUTHORIZED.getCode(), "未登录", null);
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        String data = JacksonUtils.toJson(result);

        PrintWriter writer = response.getWriter();
        writer.write(data);
        writer.flush();
    }

    private void processLogin(HttpServletRequest request, HttpServletResponse response, H5LoginInfo h5LoginInfo) throws Exception{
        H5LoginContext.getLoginInfo().setCacheKey(h5LoginInfo.getCacheKey());
        H5LoginContext.getLoginInfo().setMobile(h5LoginInfo.getMobile());
    }
}
