package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.ChannelBrandVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.PageInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/8/26
 * desc: 查询渠道品牌响应
 */
@TypeDoc(
        description = "查询渠道品牌响应"
)
@Data
@ApiModel("查询渠道品牌响应")
public class GetChannelBrandResponse {

    @FieldDoc(
            description = "渠道品牌列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道品牌列表", required = true)
    private List<ChannelBrandVO> brandList;

    @FieldDoc(
            description = "分页信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "分页信息", required = true)
    private PageInfoVO pageInfo;
}
