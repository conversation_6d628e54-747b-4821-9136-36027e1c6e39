package com.sankuai.shangou.qnh.orderapi.service.pc;

import com.sankuai.shangou.qnh.orderapi.domain.request.pc.OrderDetailReportReq;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.OrderFulfillmentReportReq;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.FulfillReportDetailResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.OrderFulfillmentReportQueryResp;

import java.util.List;

public interface OrderReportService {


    OrderFulfillmentReportQueryResp asyncQueryReport(OrderFulfillmentReportReq request, List<Long> poiIds, List<Long> warehouseIds);

    FulfillReportDetailResponse queryReportOrderDetail(OrderDetailReportReq request);
}
