package com.sankuai.shangou.qnh.orderapi.filter.pc;

import com.alibaba.fastjson.JSON;
import com.sankuai.shangou.qnh.orderapi.constant.pc.CommonConstant;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.BaseResult;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.Result;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.ResultBuilder;
import com.sankuai.shangou.qnh.orderapi.utils.pc.MccConfigUtil;
import com.sankuai.shangou.qnh.orderapi.utils.pc.WebUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 安全过滤器
 *
 * <AUTHOR>
 */
@Slf4j
public class SecurityFilter implements Filter {

    private static final Pattern HEAD_END_BLANK_PATTERN = Pattern.compile("^\\s*|\\s*$");

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        if (!(servletRequest instanceof HttpServletRequest) || !(servletResponse instanceof HttpServletResponse)) {
            filterChain.doFilter(servletRequest, servletResponse);
            return;
        }

        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;
        boolean checkCSRF = checkCSRF(request, response);
        boolean checkSql = checkSql(request, response);
        boolean secure = checkCSRF && checkSql;

        if (!secure) {
            insecureResponse(request, response);
            return;
        }

        SecurityHttpServletRequestWrapper servletRequestWrapper = new SecurityHttpServletRequestWrapper(
                (HttpServletRequest) servletRequest);
        filterChain.doFilter(servletRequestWrapper, servletResponse);
    }

    @Override
    public void destroy() {

    }

    private boolean checkCSRF(HttpServletRequest request, HttpServletResponse response) {

        // 获取请求Referer
        String referer = request.getHeader(CommonConstant.HEAD_REFERER);
        if (StringUtils.isEmpty(referer)) {
            return true;
        }

        URL url = null;
        try {
            url = new URL(referer);
        } catch (MalformedURLException e) {
            log.warn("referer error", e);
            return false;
        }
        String host = url.getHost();

        // 白名单限制请求源域名
        String allowDomainWhitelist = MccConfigUtil.getAllowDomainWhitelist();
        if (StringUtils.isNotEmpty(allowDomainWhitelist)) {
            String[] allowedDomainWhitelistArr = allowDomainWhitelist.split(",");
            for (String allowedDomain : allowedDomainWhitelistArr) {
                if (allowedDomain != null && host.contains(allowedDomain.trim())) {
                    return true;
                }
            }
        }

        log.error("insecure request url:{}", url);
        return false;
    }

    private boolean checkSql(HttpServletRequest request, HttpServletResponse response) {
        // ORM使用mybatis， 主要检查orderBy, orderType, 这两个字段需要使用${}, 其余字段可以#{}来防止sql注入
        boolean validateOrderBy = validateOrderBy(request.getParameter(CommonConstant.REQUEST_PARAM_SORTED_BY));
        boolean validateOrderType = validateOrderType(request.getParameter(CommonConstant.REQUEST_PARAM_SORTED_TYPE));
        boolean secureSql = validateOrderBy && validateOrderType;
        return secureSql;
    }

    private static boolean validateOrderBy(String orderBy) {
        if (StringUtils.isNotBlank(orderBy)) {
            Matcher matcher = HEAD_END_BLANK_PATTERN.matcher(orderBy);
            orderBy = matcher.replaceAll("");
            String[] splitArr = orderBy.split("\\s+");
            if (splitArr.length > 1) {
                log.warn("error orderBy：{}", orderBy);
                return false;
            }
        }

        return true;
    }

    private static boolean validateOrderType(String orderType) {
        if (StringUtils.isNotBlank(orderType)) {
            Matcher matcher = HEAD_END_BLANK_PATTERN.matcher(orderType);
            orderType = matcher.replaceAll("");
            if (!CommonConstant.ORDER_TYPE_ASC.equalsIgnoreCase(orderType) &&
                    !CommonConstant.ORDER_TYPE_DESC.equalsIgnoreCase(orderType)) {
                log.warn("error orderType：{}", orderType);
                return false;
            }
        }
        return true;
    }

    private void insecureResponse(HttpServletRequest request, HttpServletResponse response) {
        String data = null;
        if (WebUtil.isAjaxRequest(request)) {
            Result result = ResultBuilder.buildResult(BaseResult.NOT_SECURITY);
            response.setContentType(CommonConstant.CONTENT_TYPE_APPLICATION_JSON);
            response.setCharacterEncoding(CommonConstant.CHARSET_UTF8);
            data = JSON.toJSONString(result);
        } else {
            response.setContentType(CommonConstant.CONTENT_TYPE_TEXT_HTML);
            data = String.valueOf(BaseResult.NOT_SECURITY.getCode());
        }

        try {
            PrintWriter writer = response.getWriter();
            writer.write(data);
            writer.flush();
        } catch (IOException e) {
            log.error("insecure response error", e);
        }

    }


    static class SecurityHttpServletRequestWrapper extends HttpServletRequestWrapper {

        HttpServletRequest orgRequest = null;

        /**
         * 不需要过滤XSS的参数（如编辑器相关参数 密码等参数)
         */
        private static final List<String> XSSWhiteList = Arrays.asList("password", "couponInfo", "remark", "description");

        public SecurityHttpServletRequestWrapper(HttpServletRequest request) {
            super(request);
            orgRequest = request;
        }

        /**
         * 覆盖getParameterValues方法，将参数值做xss过滤
         */
        @Override
        public String[] getParameterValues(String parameter) {

            String[] values = super.getParameterValues(parameter);
            if (values == null) {
                return null;
            }
            if (XSSWhiteList.contains(parameter)) {
                return values;
            }
            int count = values.length;
            String[] encodedValues = new String[count];
            for (int i = 0; i < count; i++) {
                encodedValues[i] = xssEncode(values[i]);
            }
            return encodedValues;
        }

        /**
         * 覆盖getParameter方法，将参数值做xss过滤
         */
        @Override
        public String getParameter(String parameter) {

            String value = super.getParameter(parameter);
            if (value == null) {
                return null;
            }

            if (XSSWhiteList.contains(parameter)) {
                return value;
            }

            return xssEncode(value);
        }

        /**
         * 将容易引起xss漏洞的半角字符直接替换成全角字符
         *
         * @param s
         * @return
         */
        private static String xssEncode(String s) {
            if (s == null || s.isEmpty()) {
                return s;
            }
            StringBuilder sb = new StringBuilder(s.length() + 16);
            for (int i = 0; i < s.length(); i++) {
                char c = s.charAt(i);
                switch (c) {
                    case '>':
                        // 全角大于号
                        sb.append("＞");
                        break;
                    case '<':
                        // 全角小于号
                        sb.append("＜");
                        break;
                    case '\'':
                        // 全角单引号
                        sb.append("’");
                        break;
                    case '\"':
                        // 全角双引号
                        sb.append("”");
                        break;
                    case '&':
                        // 全角&
                        sb.append("&");
                        break;
                    case '#':
                        // 全角#
                        sb.append("＃");
                        break;
                    case '\\':
                        // 全角斜线
                        sb.append('＼');
                        break;
                    case '%':
                        //全角%
                        sb.append('％');
                        break;
                    default:
                        sb.append(c);
                        break;
                }
            }
            return sb.toString();
        }
    }
}
