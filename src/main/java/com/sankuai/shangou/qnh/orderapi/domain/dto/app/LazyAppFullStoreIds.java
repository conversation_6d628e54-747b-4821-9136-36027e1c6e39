package com.sankuai.shangou.qnh.orderapi.domain.dto.app;


import com.sankuai.shangou.qnh.orderapi.exception.app.CommonRuntimeException;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.function.Supplier;

/**
 * APP 全部门店ID懒加载对象
 *
 * <AUTHOR>
 * @date 2023/9/21
 */
@Slf4j
public class LazyAppFullStoreIds {

    private List<Long> storeIds;

    private List<Long> miniAppStoreIds;

    private List<Long> storeAndWarehouseIds;

    //这个会返回门店和仓,但是调用的接口基础运营说快废弃了
    private final Supplier<List<Long>> fullStoreIdSupplier;

    //这个fullStore会返回门店及仓下面的门店
    private final Supplier<List<Long>> miniAppFullStoreIdsSupplier;

    //这个会返回门店及仓id列表,调用的权限接口(基础运营推荐)
    private final Supplier<List<Long>> fullStoreAndWarehouseIdSupplier;

    public LazyAppFullStoreIds(Supplier<List<Long>> fullStoreIdSupplier, Supplier<List<Long>> miniAppFullStoreIdsSupplier, Supplier<List<Long>> fullStoreAndWarehouseIdSupplier) {
        this.fullStoreIdSupplier = fullStoreIdSupplier;
        this.miniAppFullStoreIdsSupplier = miniAppFullStoreIdsSupplier;
        this.fullStoreAndWarehouseIdSupplier = fullStoreAndWarehouseIdSupplier;
    }

    /**
     * 获取全部门店ID
     * 从使用方式来说，这里暂不考虑并发问题
     *
     * @return 全部门店ID列表
     */
    @NotNull
    public List<Long> fetchStoreIds() {
        if (Objects.isNull(storeIds)) {
            try {
                storeIds = fullStoreIdSupplier.get();
                log.info("懒加载获取全部门店ID列表为：{}", storeIds);
            } catch (Exception e) {
                log.error("懒加载获取全部门店ID列表异常", e);
                throw new CommonRuntimeException("获取全部门店ID列表异常，请重置", e);
            }
        }
        if (Objects.isNull(storeIds)) {
            return Collections.emptyList();
        }
        return storeIds;
    }

    public List<Long> fetchStoreIds4MiniApp() {
        if (Objects.isNull(miniAppStoreIds)) {
            try {
                miniAppStoreIds = miniAppFullStoreIdsSupplier.get();
                log.info("懒加载获取小程序全部门店ID列表为：{}", miniAppStoreIds);
            } catch (Exception e) {
                log.error("懒加载获取全部门店ID列表异常", e);
                throw new CommonRuntimeException("获取全部门店ID列表异常，请重置", e);
            }
        }
        if (Objects.isNull(miniAppStoreIds)) {
            return Collections.emptyList();
        }
        return miniAppStoreIds;
    }

    /**
     * 获取全部门店及仓下门店id
     * @return
     */
    @NotNull
    public List<Long> fetchStoreAndWarehouseIds() {
        if (Objects.isNull(storeAndWarehouseIds)) {
            try {
                storeAndWarehouseIds = fullStoreAndWarehouseIdSupplier.get();
                log.info("懒加载获取全部门店,仓ID列表为：{}", storeAndWarehouseIds);
            } catch (Exception e) {
                log.error("懒加载获取全部门店ID列表异常", e);
                throw new CommonRuntimeException("获取全部门店ID列表异常，请重置", e);
            }
        }
        if (Objects.isNull(storeAndWarehouseIds)) {
            return Collections.emptyList();
        }
        return storeAndWarehouseIds;
    }

}