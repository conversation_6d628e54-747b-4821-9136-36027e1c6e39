package com.sankuai.shangou.qnh.orderapi.domain.response.pda;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pda.PdaOrderHomePageModuleVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pda.PdaOrderPendingTaskVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @createTime 2024/07/19
 * @description
 */
@TypeDoc(
        description = "订单首页查询返回"
)
@Data
@ApiModel("订单首页查询返回")
public class PdaOrderHomePageResponse {

    @FieldDoc(
            description = "订单首页模块", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单首页模块", required = true)
    @NotNull
    private PdaOrderHomePageModuleVO orderHomePageModuleVO;

    @FieldDoc(
            description = "订单首页待办", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单首页待办", required = true)
    @NotNull
    private PdaOrderPendingTaskVO orderPendingTaskVO;


}
