package com.sankuai.shangou.qnh.orderapi.remote;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.meituan.reco.pickselect.common.cat.CatTransaction;
import com.meituan.reco.pickselect.common.exception.FallbackException;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.meituan.shangou.saas.tenant.thrift.ConfigThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.enums.ConfigItemEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.TenantBusinessModeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.config.ConfigDto;
import com.meituan.shangou.saas.tenant.thrift.dto.config.request.ConfigQueryRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.config.response.TenantConfigResponse;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ResponseHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/8 16:34
 * @Description:
 */
@Rhino
@Service
@Slf4j
public class TenantConfigRemoteService {

    /**
     * 和租户约定好的初始化定价策略key值
     */
    private static final String TENANT_INITIAL_PRICE_STRATEGY_KEY = "tenantInitialPriceStrategy";
    private static final String BIZ_MODE_KEY = "biz_mode";
    private static final Integer MERCHANT_CHARGE_CONFIG_CODE = 44;
    private static final String MERCHANT_CHARGE_CONFIG_KEY = "zongbuguanpin";
    private static final Integer PRODUCT_MANAGEMENT_TYPE_CODE = 29;
    private static final String PRODUCT_MANAGEMENT_TYPE = "product_management_type";

    @Autowired
    private ConfigThriftService configThriftService;

    /**
     * 医药无人仓判断
     *
     * @param tenantId
     * @return
     */
    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    @Degrade(rhinoKey = "TenantConfigRemoteService.isMedicineUnmannedWarehouseTenant", fallBackMethod = "isMedicineUnmannedWarehouseTenantFallback", timeoutInMilliseconds = 2000)
    public boolean isMedicineUnmannedWarehouseTenant(Long tenantId) {
        String bizMode = queryTenantBizMode(tenantId);
        TenantBusinessModeEnum tenantBizModeEnum = TenantBusinessModeEnum.codeOf(bizMode);
        return TenantBusinessModeEnum.MEDICINE_UNMANNED_WAREHOUSE == tenantBizModeEnum;
    }

    private boolean isMedicineUnmannedWarehouseTenantFallback(Long tenantId) {
        throw new FallbackException("TenantConfigClient.isMedicineUnmannedWarehouseTenantFallback 熔断降级");
    }

    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    @Degrade(rhinoKey = "TenantConfigRemoteService.queryTenantBizMode", fallBackMethod = "queryTenantBizModeFallback", timeoutInMilliseconds = 2000)
    public String queryTenantBizMode(Long tenantId) {
        ConfigQueryRequest configQueryRequest = new ConfigQueryRequest();
        configQueryRequest.setTenantId(tenantId);
        configQueryRequest.setConfigId(ConfigItemEnum.TENANT_BIZ_MODE.getKey());
        configQueryRequest.setSubjectId(tenantId);
        TenantConfigResponse response = configThriftService.queryTenantConfig(configQueryRequest);
        ResponseHandler.handleStatus(response.getStatus());

        return Optional.ofNullable(response.getConfig())
                .map(ConfigDto::getConfigContent)
                .map(com.meituan.reco.pickselect.common.utils.JacksonUtils::fromJsonToMap)
                .map(it -> it.get(BIZ_MODE_KEY))
                .map(it -> (String) it)
                .orElse(StringUtils.EMPTY);
    }

    private String queryTenantBizModeFallback(Long tenantId) {
        throw new FallbackException("TenantConfigRemoteService.queryTenantBizMode 熔断降级");
    }

    public boolean isTenantManageBatchStock(Long tenantId) {
        return getConfig(tenantId, tenantId, ConfigItemEnum.BATCH_STOCK)
                .map(ConfigItemEnum.BATCH_STOCK::isMainConfigYesStr)
                .orElse(false);
    }

    private Optional<String> getConfig(Long tenantId, Long subjectId, ConfigItemEnum configKey) {
        TenantConfigResponse tenantConfigResponse;
        try {
            ConfigQueryRequest configQueryRequest = new ConfigQueryRequest();
            configQueryRequest.setTenantId(tenantId);
            configQueryRequest.setConfigId(configKey.getKey());
            configQueryRequest.setSubjectId(subjectId);
            tenantConfigResponse = configThriftService.queryTenantConfig(configQueryRequest);
        } catch (Exception e) {
            log.error("查询租户配置异常", e);
            throw new BizException("查询租户配置异常", e);
        }

        if (tenantConfigResponse.getStatus().code != 0) {
            log.error("查询租户配置失败,msg:{}", tenantConfigResponse.getStatus().getMessage());
            throw new BizException("查询租户配置失败");
        }

        return Optional.ofNullable(tenantConfigResponse.getConfig())
                .map(ConfigDto::getConfigContent);
    }


}
