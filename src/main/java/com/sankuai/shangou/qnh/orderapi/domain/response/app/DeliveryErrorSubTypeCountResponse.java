package com.sankuai.shangou.qnh.orderapi.domain.response.app;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description 异常订单子类型数量响应
 * @ClassName DeliveryErrorSubTypeCountResponse
 * <AUTHOR>
 * @Version 1.0
 * @Date 2022/1/11 11:53 上午
 */
@TypeDoc(
        description = "异常订单子类型数量响应"
)
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("异常订单子类型数量响应")
public class DeliveryErrorSubTypeCountResponse {

    @FieldDoc(
            description = "全部数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "全部数量", required = true)
    private Integer allSubTypeCount;

    @FieldDoc(
            description = "未接单数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "未接单数量", required = true)
    private Integer noRiderAcceptCount;

    @FieldDoc(
            description = "未到店数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "未到店数量", required = true)
    private Integer noArrivalStoreCount;

    @FieldDoc(
            description = "未取货数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "未取货数量", required = true)
    private Integer noRiderTakeGoodsCount;

    @FieldDoc(
            description = "配送超时数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "配送超时数量", required = true)
    private Integer deliveryTimeoutCount;

    @FieldDoc(
            description = "系统异常数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "系统异常数量", required = true)
    private Integer systemExceptionCount;

    @FieldDoc(
            description = "异常上报个数", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "异常上报个数")
    private Integer reportExceptionCount;

    @FieldDoc(
            description = "取货失败个数", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "取货失败个数")
    private Integer takeGoodsExceptionCount;


    public static DeliveryErrorSubTypeCountResponse init() {
        DeliveryErrorSubTypeCountResponse resp = new DeliveryErrorSubTypeCountResponse();
        resp.setAllSubTypeCount(0);
        resp.setNoRiderAcceptCount(0);
        resp.setNoArrivalStoreCount(0);
        resp.setNoRiderTakeGoodsCount(0);
        resp.setDeliveryTimeoutCount(0);
        resp.setSystemExceptionCount(0);
        resp.setReportExceptionCount(0);
        resp.setTakeGoodsExceptionCount(0);
        return resp;
    }

}
