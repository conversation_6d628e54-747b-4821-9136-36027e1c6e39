package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/7/10
 * desc: 订单信息
 */
@TypeDoc(
        description = "订单信息"
)
@ApiModel("订单信息")
@Data
public class OrderVO {

    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户ID", required = true)
    private Long tenantId;

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID", required = true)
    private Integer channelId;

    @FieldDoc(
            description = "渠道名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道名称", required = true)
    private String channelName;

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID", required = true)
    private Long storeId;

    @FieldDoc(
            description = "门店名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店名称", required = true)
    private String storeName;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道订单号", required = true)
    private String channelOrderId;

    @FieldDoc(
            description = "订单用户ID、0或-1为无效ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "订单用户ID、0或-1为无效ID")
    private Long userId;

    @FieldDoc(
            description = "订单流水", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单流水", required = true)
    private Long serialNo;

    @FieldDoc(
            description = "订单序号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单序号", required = true)
    private String serialNoStr;

    @FieldDoc(
            description = "商品数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品数量", required = true)
    private Integer itemCount;

    @FieldDoc(
            description = "订单实付金额  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单实付金额  单位:分", required = true)
    private Integer actualPayAmt;

    @FieldDoc(
            description = "商家实收金额  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商家实收金额   单位:分", required = true)
    private Integer bizReceiveAmt;

    @FieldDoc(
            description = "配送方式", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "配送方式", required = true)
    private Integer deliveryMethod;

    @FieldDoc(
            description = "配送方式描述", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "配送方式描述", required = true)
    private String deliveryMethodDesc;

    @FieldDoc(
            description = "配送人姓名", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "配送人姓名", required = true)
    private String deliveryUserName;

    @FieldDoc(
            description = "配送人电话", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "配送人电话", required = true)
    private String deliveryUserPhone;

    @FieldDoc(
            description = "收货人姓名", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "收货人姓名", required = true)
    private String receiverName;

    @FieldDoc(
            description = "收货人电话号码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "收货人电话号码", required = true)
    private String receiverPhone;

    @FieldDoc(
            description = "收货人地址", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "收货人地址", required = true)
    private String receiveAddress;

    @FieldDoc(
            description = "订单状态 10-新建订单 20-商家已确认 35-履约中 30-订单已完成 40-取消处理中 50-已取消", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单状态 10-新建订单 20-商家已确认 35-履约中 30-订单已完成 40-取消处理中 50-已取消", required = true)
    private Integer channelOrderStatus;

    @FieldDoc(
            description = "订单聚合状态 1-进行中订单 2-已完成订单 3-订单已取消", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单聚合状态 1-进行中订单 2-已完成订单 3-订单已取消", required = true)
    private Integer aggregationOrderStatus;

    @FieldDoc(
            description = "订单状态描述", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单状态描述", required = true)
    private String channelOrderStatusDesc;

    @FieldDoc(
            description = "订单创建时间戳", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单创建时间戳", required = true)
    private Long createTime;

    @FieldDoc(
            description = "部分退款金额，只有在部分退款的时候才有效", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "部分退款金额，只有在部分退款的时候才有效", required = true)
    private Integer refundAmt;

    @FieldDoc(
            description = "退款类型", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "退款类型", required = true)
    private Integer refundTagId;

    @FieldDoc(
            description = "退款类型描述", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "退款类型描述", required = true)
    private String refundTagDesc;

    @FieldDoc(
            description = "当前审核中的售后申请退款类型", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "当前审核中的售后申请退款类型", required = true)
    private Integer auditingRefundTagId;

    @FieldDoc(
            description = "当前审核中的售后申请退款类型描述", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "当前审核中的售后申请退款类型描述", required = true)
    private String auditingRefundTagDesc;

    @FieldDoc(
            description = "可操作列表 10-接单 20-完成拣货 30-补打小票 40-全单退款 50-部分退款 70-退差价", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "可操作列表 10-接单 20-完成拣货 30-补打小票 40-全单退款 50-部分退款 70-退差价", required = true)
    private List<Integer> couldOperateItemList;

    @FieldDoc(
            description = "配送订单类型  1-立即单 2-预约单", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "配送订单类型   1-立即单 2-预约单", required = true)
    private Integer deliveryOrderType;

    @FieldDoc(
            description = "配送订单类型名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "配送订单类型名称", required = true)
    private String deliveryOrderTypeName;

    @FieldDoc(
            description = "预计送达时间开始时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "预计送达时间开始时间", required = true)
    private Long estimatedSendArriveTimeStart;

    @FieldDoc(
            description = "预计送达时间截止时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "预计送达时间截止时间", required = true)
    private Long estimatedSendArriveTimeEnd;

    @FieldDoc(
            description = "拣货状态", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "拣货状态", required = true)
    private Integer pickupStatus;

    @FieldDoc(
            description = "拣货完成时间戳", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "拣货完成时间戳", required = true)
    private Long pickupCompleteTime;

    @FieldDoc(
            description = "配送状态", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "配送状态", required = true)
    private Integer distributeStatus;

    @FieldDoc(
            description = "线下订单状态", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "线下订单状态", required = true)
    private Integer offlineOrderStatus;

    @FieldDoc(
            description = "订单更新时间戳", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单更新时间戳", required = true)
    private Long updateTime;

    @FieldDoc(
            description = "渠道第二订单号 饿百渠道的饿了么订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道第二订单号 饿百渠道的饿了么订单号", required = true)
    private String channelExtraOrderId;

    @FieldDoc(
            description = "备注", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "备注", required = true)
    private String comments;

    @FieldDoc(
            description = "商品信息列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品信息列表", required = true)
    private List<ProductVO> productList;

    @FieldDoc(
            description = "订单用户标签", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单用户标签", required = true)
    private List<TagInfoVO> userTags;


    @FieldDoc(
            description = "订单支付时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单支付时间", required = true)
    private Long payTime;


    @FieldDoc(
            description = "订单用户类型", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单用户类型 10 普通用户，15 会员用户", required = true)
    /**
     * @see com.sankuai.meituan.shangou.empower.ocms.thrift.enums.OrderUserType
     */
    private Integer orderUserType;

    @FieldDoc(
            description = "是否有缺货情况", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否有缺货情况", required = false)
    private Boolean hasLackGoods = false;

    @FieldDoc(
            description = "拣货员姓名列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "拣货员姓名列表", required = false)
    private List<String> pickerNameList = new ArrayList<>();
}
