package com.sankuai.shangou.qnh.orderapi.domain.request.pc;
// Copyright (C) 2019 Meituan
// All rights reserved

import com.google.common.base.Preconditions;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * @author: <EMAIL>
 * @class: CommentReplyReq
 * @date: 2019-07-10 14:11:53
 * @desc:
 */
@Data
public class CommentReplyReq {

    /**
     * 回复评论ID
     */
    private String commentId;

    /**
     * 回复内容
     */
    private String replyDraft;


    @FieldDoc(
            description = "订单溯源用户关联订单号"
    )
    private String associationChannelOrderId;

    public void isValid(){
        Preconditions.checkNotNull(commentId, "评论Id不得为空");
        Preconditions.checkArgument(StringUtils.isNotEmpty(replyDraft), "评论内容不得为空");
    }

}
