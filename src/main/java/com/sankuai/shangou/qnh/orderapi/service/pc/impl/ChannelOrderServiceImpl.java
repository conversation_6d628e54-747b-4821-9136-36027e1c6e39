package com.sankuai.shangou.qnh.orderapi.service.pc.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.Lion;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.meituan.reco.pickselect.common.utils.PhoneUtil;
import com.meituan.shangou.goodscenter.dto.GoodsSkuRelationDto;
import com.meituan.shangou.saas.common.enums.ChannelTypeEnum;
import com.meituan.shangou.saas.o2o.dto.model.*;
import com.meituan.shangou.saas.o2o.dto.request.BizOrderQueryRequest;
import com.meituan.shangou.saas.o2o.dto.response.BizOrderQueryResponse;
import com.meituan.shangou.saas.o2o.service.BizOrderThriftService;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OcmsUnDoneOrderListReq;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.AfterSaleRecordDetailVo;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.meituan.shangou.saas.order.management.client.utils.DesensitizeReceiverInfoUtil;
import com.meituan.shangou.saas.order.management.client.utils.OrderUtil;
import com.meituan.shangou.saas.order.management.client.utils.param.DesensitizeReceiverInfoExtParam;
import com.meituan.shangou.saas.order.management.client.utils.result.DesensitizeReceiverInfoResult;
import com.meituan.shangou.saas.order.platform.client.dto.model.BigOrderSkuModel;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.DeliveryStatusEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderSourceEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.sankuai.meituan.reco.pickselect.consts.PickCompleteSource;
import com.sankuai.meituan.reco.pickselect.thrift.OpenPickRequest;
import com.sankuai.meituan.reco.pickselect.thrift.OpenPickResponse;
import com.sankuai.meituan.reco.pickselect.thrift.OpenPickThriftService;
import com.sankuai.meituan.reco.pickselect.thrift.print.OpenPrintResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ResponseCodeEnum;
import com.sankuai.meituan.shangou.empower.settlement.constant.BoothGlobals;
import com.sankuai.meituan.shangou.empower.settlement.dto.model.voucher.OrderChangeProductModel;
import com.sankuai.meituan.shangou.empower.settlement.dto.model.voucher.OrderChangeVoucherModel;
import com.sankuai.meituan.shangou.saas.common.data.PageResult;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.shangou.commons.thrift.publisher.utils.JsonUtils;
import com.sankuai.shangou.qnh.orderapi.configuration.pc.OrderConfiguration;
import com.sankuai.shangou.qnh.orderapi.constant.pc.Constants;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import com.sankuai.shangou.qnh.orderapi.converter.pc.ChannelOrderConverter;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.*;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.*;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.PageResultV2;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.UiOption;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.*;
import com.sankuai.shangou.qnh.orderapi.enums.app.ErrorCodeEnum;
import com.sankuai.shangou.qnh.orderapi.enums.pc.AdjustTargetItemEnum;
import com.sankuai.shangou.qnh.orderapi.enums.pc.ChannelOrderStatusEnum;
import com.sankuai.shangou.qnh.orderapi.remote.*;
import com.sankuai.shangou.qnh.orderapi.service.common.GiftBagService;
import com.sankuai.shangou.qnh.orderapi.service.pc.AuthService;
import com.sankuai.shangou.qnh.orderapi.service.pc.ChannelOrderService;
import com.sankuai.shangou.qnh.orderapi.remote.OrgRemoteService;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import com.sankuai.shangou.qnh.orderapi.utils.pc.MccConfigUtil;
import com.sankuai.shangou.qnh.orderapi.utils.pc.RpcInvoker;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/2 14:35
 * @Description:
 */
@Slf4j
@Service
public class ChannelOrderServiceImpl implements ChannelOrderService {

    @Autowired
    private OrderBizRemoteService orderBizRemoteService;

    @Autowired
    private PickSelectRemoteService pickSelectRemoteService;

    @Autowired
    private SettlementVoucherRemoteClient settlementVoucherRemoteClient;

    @Autowired
    private TenantRemoteService tenantRemoteService;

    @Resource
    private OpenPickThriftService.Iface openPickThriftService;

    @Autowired
    private BizOrderThriftService bizOrderThriftService;
    @Resource
    private OcmsChannelRemoteService ocmsChannelRemoteService;
    @Autowired
    private AuthRemoteService authClient;
    @Autowired
    private PoiRemoteService poiClient;
    @Autowired
    private OrgRemoteService orgClient;
    @Resource
    private ChannelOrderRemoteService channelOrderRemoteService;
    @Autowired
    private InvoiceRemoteService invoiceClient;
    @Autowired
    private GoodsCenterRemoteService goodsCenterClient;
    @Autowired
    private PriceRemoteService priceClient;
    @Autowired
    private OrderBizRemoteService orderBizClient;
    @Autowired
    private OrderTagRemoteService orderTagClient;
    @Autowired
    private ChannelOrderConverter channelOrderConverter;
    @Resource
    private GiftBagService giftBagService;

    private static final String PRODUCT_ADJUST_TARGET_TYPE = "1";
    private static final String FRANCHISE_PRE_STR = "加盟-";
    private static final String MERCHANT_AMOUNT_HOVER = "实收金额仅作参考，实际收入请以账单金额为准";

    private static final String REFUND_MERCHANT_AMOUNT_HOVER = ";有退款，商家实收（退款）=";

    private static final String COMMISION_AMOUNT_HOVER = "佣金规则请参考签署的最新版本《阶梯计费补充协议》";

    private static final String REFUND_COMMISION_AMOUNT_HOVER = ";有退款，佣金（退款）=";

    private static final String COSR_OF_GOODS_SOLD_HOVER = "商品成本仅供参考";

    private static final String REFUND_COST_OF_GOODS_SOLD_HOVER = ";有退款，商品成本（退款）=";

    private static final String ACTUAL_GROSS_PROFIT_HOVER = "实收毛利额仅供参考，等于商家实收-商品成本";

    private static final String REFUND_ACTUAL_GROSS_PROFIT_HOVER = ";有退款，实收毛利额（退款）=";

    private static final String ACTUAL_GROSS_RATE_HOVER = "实收毛利率仅供参考，等于实收毛利额/商家实收";

    private static final String REFUND_ACTUAL_GROSS_RATE_HOVER = ";有退款，实收毛利率（退款）=";

    private static final String CONSIGNMENT_HOVER = "注意：已剔除“代销商品”的金额";

    private static final Integer NO_REFUND = 0;

    private static final Integer ALL_REFUND = 1;

    private static final Integer PART_REFUND = 2;

    @Override
    public CommonDataBO<List<OCMSOrderAdjustLog>> queryOrderAdjustLog(Long tenantId, Integer channelId, String orderId) {
        return orderBizRemoteService.queryOrderAdjustLog(tenantId, channelId, orderId);
    }

    @Override
    public CommonResultBO tenantPartRefund(PartRefundRequest request) {
        if (StringUtils.equals(request.getChannelId(), String.valueOf(DynamicChannelType.MEITUAN.getChannelId()))) {
            // 美团名酒馆订单进行提示
            Pair<Boolean, Pair<Integer, String>> resultPair = ocmsChannelRemoteService.refundCheckMtFamousTavern(
                    request.getOrderId(), ContextHolder.currentUserTenantId(),
                    ErrorCodeEnum.REFUND_CHECK_MT_FAMOUS_TAVERN_PROMPT, ErrorCodeEnum.WEB_MT_FAMOUS_TAVERN_GATHER_PHONE_NOT_FIND);
            if (resultPair.getKey()) {
                return CommonResultBO.builder().success(false).message(resultPair.getValue().getValue()).build();
            }
        }
        return orderBizRemoteService.tenantPartRefund(request);
    }

    @Override
    public CommonResultBO tenantCancelOrder(RefundRequest request) {
        if (StringUtils.equals(request.getChannelId(), String.valueOf(DynamicChannelType.MEITUAN.getChannelId()))) {
            // 美团名酒馆订单进行提示
            Pair<Boolean, Pair<Integer, String>> resultPair = ocmsChannelRemoteService.refundCheckMtFamousTavern(
                    request.getOrderId(), ContextHolder.currentUserTenantId(),
                    ErrorCodeEnum.REFUND_CHECK_MT_FAMOUS_TAVERN_PROMPT, ErrorCodeEnum.WEB_MT_FAMOUS_TAVERN_GATHER_PHONE_NOT_FIND);
            if (resultPair.getKey()) {
                return CommonResultBO.builder().success(false).message(resultPair.getValue().getValue()).build();
            }
        }
        return orderBizRemoteService.tenantCancelOrder(request);
    }

    @Override
    public CommonResultBO printReceipt(String viewOrderId, Integer channelId,String poiId) {
        OpenPrintResponse response =  pickSelectRemoteService.printReceipt(ChannelOrderConvertUtils.sourceMid2Biz(channelId), viewOrderId,poiId);
        if (response.getCode() == ResponseCodeEnum.SUCCESS.getValue()){
            return CommonResultBO.builder().success(true).build();
        }else{
            return CommonResultBO.builder().success(false).message(response.getMsg()).build();
        }
    }

    @Override
    public CommonResultBO confirmOrder(String orderId, int channelId) {
        int orderBizType = ChannelOrderConvertUtils.sourceMid2Biz(channelId);
        return orderBizRemoteService.confirmOrder(orderId, orderBizType);
    }

    @Override
    public CommonDataBO<List<OrderAdjustRecordVO>> queryAdjustOrderRecord(Long tenantId, Integer channelId, String viewOrderId) {
        CommonDataBO<List<OrderAdjustRecordVO>> result = new CommonDataBO<>();
        // 获取开关，fromSettlement为true，表示查询结算系统，
        // 为false，表示查询订单系统，若订单系统查询失败或未查询到数据，用结算系统兜底
        if (!MccConfigUtil.queryOrderAdjustRecordFromSettlement()) {
            CommonDataBO<List<OCMSOrderAdjustLog>> orderResult = queryOrderAdjustLog(tenantId, channelId, viewOrderId);
            log.info("查询订单系统调整记录 viewOrderId:{},orderResult:{}", viewOrderId, orderResult);
            if (orderResult.getSuccess() && CollectionUtils.isNotEmpty(orderResult.getData())) {
                result.setSuccess(Boolean.TRUE);
                result.setData(convertOrderAdjustVO(orderResult.getData()));
                return result;
            }
        }

        // 获取结算系统中的调整凭证
        if (!MccConfigUtil.settlementSupportChannelIds().contains(channelId)) {
            log.info("结算系统不支持该渠道，直接返回空调整记录 channelId:{},viewOrderId:{}", channelId, viewOrderId);
            result.setSuccess(Boolean.TRUE);
            result.setData(Collections.emptyList());
            return result;
        }

        CommonDataBO<List<OrderChangeVoucherModel>> settlementResult = settlementVoucherRemoteClient.queryOrderChangeVouchers(tenantId, channelId, viewOrderId);
        log.info("查询结算系统调整记录 viewOrderId:{},settlementResult:{}", viewOrderId, settlementResult);
        if (settlementResult.getSuccess()) {
            result.setSuccess(Boolean.TRUE);
            result.setData(buildOrderAdjustRecordVOs(tenantId, settlementResult.getData()));
        }
        else {
            result.setSuccess(Boolean.FALSE);
            result.setMessage(settlementResult.getMessage());
        }
        return result;
    }

    @Override
    public CommonResultBO completePickUp(QueryDetailRequest request) {

        int orderBizType = ChannelOrderConvertUtils.sourceMid2Biz(NumberUtils.toInt(request.getChannelId()));
        OpenPickResponse response =  completePickUp(request.getOrderId(), orderBizType);

        List<Integer> notNeedHandleCodeList = Arrays.asList(ResultCode.OPERATING_FAIL.getCode(), ResultCode.NO_PICK_ORDER_EXISTS.getCode());

        if (notNeedHandleCodeList.contains(response.getCode()) && isCompletedPickUp(orderBizType, request.getOrderId())) {
            log.info("拣货已经完成，或者拣货订单已经被取消，或者属于团购订单，不需要再处理，返回成功,order:{}", request.getOrderId());
            return CommonResultBO.builder().success(true).build();
        }
        boolean success = response.getCode() == ResponseCodeEnum.SUCCESS.getValue();
        return CommonResultBO.builder()
                .success(success)
                .message(response.getMsg())
                .build();
    }

    private boolean isCompletedPickUp(int orderBizType, String viewOrderId) {
        int channelID = ChannelOrderConvertUtils.sourceBiz2Mid(orderBizType);
        BizOrderModel bizOrderModel = queryOrderModel(viewOrderId, channelID, ContextHolder.currentUserTenantId());
        if (bizOrderModel == null){
            return false;
        }
        int deliveryStatus = bizOrderModel.getDeliveryModel().getDeliveryStatus();
        log.info("order:{}, 线下订单拣货状态:{}", viewOrderId, deliveryStatus);
        return DeliveryStatusEnum.PICKED.getValue() == deliveryStatus
                || DeliveryStatusEnum.PICKING_NOT_CONFIRM.getValue() == deliveryStatus;
    }

    /**
     * 查询订单基本信息
     *
     * @param orderId
     * @param channelId
     * @param tenantId
     * @return
     */
    @MethodLog(logRequest = true, logResponse = true)
    private BizOrderModel queryOrderModel(String orderId, int channelId, Long tenantId) {
        BizOrderQueryRequest req = new BizOrderQueryRequest();
        req.setOrderBizType(ChannelOrderConvertUtils.sourceMid2Biz(channelId));
        req.setViewOrderId(orderId);
        req.setTenantId(tenantId);
        req.setOrderSource(OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue());
        req.setFromMaster(true);

        BizOrderQueryResponse resp = RpcInvoker.invoke(() -> bizOrderThriftService.query(req));

        if (resp.getBizOrderModel() == null) {
            throw new BizException("订单不存在");
        }
        return resp.getBizOrderModel();
    }

    private OpenPickResponse completePickUp(String viewOrderId, Integer orderBizType) {
        OpenPickRequest request = new OpenPickRequest();
        request.setSource(orderBizType);
        request.setUnifyOrderId(viewOrderId);
        request.setOpSource(PickCompleteSource.OCMS);
        request.setUserId(ContextHolder.currentUserStaffId() != null ? ContextHolder.currentUserStaffId() : 0L);
        request.setAppId(String.valueOf(ContextHolder.currentUserLoginAppId()));
        request.setOperatorAccountId(ContextHolder.currentUid() != null ? ContextHolder.currentUid() : 0L);
        CommonDataBO commonResultBO =  new CommonDataBO();
        log.info("收到出餐完成通知，req：{}", request);
        try {
            OpenPickResponse resp = openPickThriftService.pickComplete(request);
            log.info("order:{},出餐完成，通知拣货系统，结果:{}", viewOrderId, resp);

            handleTxdDoubleModeOrder(viewOrderId, orderBizType, ContextHolder.currentUserTenantId(), resp);
            return resp;
        } catch (Exception e) {
            log.error("拣接口发生异常,req:{}", request, e);
            OpenPickResponse response = new OpenPickResponse();
            response.setCode(ResponseCodeEnum.THRIFT_SERVICE_ERROR.getValue());
            response.setMsg(e.getMessage());
            return response;
        }
    }

    private void handleTxdDoubleModeOrder(String viewOrderId, Integer orderBizType, Long tenantId, OpenPickResponse resp) {
        if (DynamicOrderBizType.TAO_XIAN_DA.getValue() != orderBizType || !isDoubleModel(tenantId)) {
            return;
        }
        try {
            BizOrderModel bizOrderModel = queryOrderModel(viewOrderId, orderBizType, tenantId);
            // 双模型订单，自配送其实和单模型是一样的，所以也需要像单模型那样被排除
            BizOrderDeliveryModel deliveryModel = bizOrderModel.getDeliveryModel();
            if (deliveryModel == null || Objects.equals(deliveryModel.getIsSelfDelivery(), 1)) {
                return;
            }
            String extData = bizOrderModel.getNewExtData();
            if (StringUtils.isNotBlank(extData)) {
                HashMap<String, Object> extDataMap = JacksonUtils.fromJsonToMap(extData);
                Object orderOutBoundDispatchFlag = extDataMap.getOrDefault("orderOutBoundDispatchFlag", "0");
                if (!Objects.equals(orderOutBoundDispatchFlag, "1")) {
                    resp.setCode(ResultCode.FAIL.getCode());
                    resp.setMsg("渠道尚未推送仓作业单，无法回传拣货完成，系统会继续重试，请稍后查看");
                }
            }
        } catch (Exception e) {
            log.error("parse extData for {} error", viewOrderId, e);
        }
    }

    private boolean isDoubleModel(Long tenantId) {
        if (!MccConfigUtil.enableCheckOrderMode(tenantId)) {
            return false;
        }
        // 1. 先通过 tenantId 的 Lion 配置判断
        boolean singleModeByLion = MccConfigUtil.isSingleModeByLion(tenantId);
        boolean doubleModeByLion = MccConfigUtil.isDoubleModeByLion(tenantId);

        if (singleModeByLion) {
            return false;
        }
        if (doubleModeByLion) {
            return true;
        }

        // 2.  如果租户有接口了，在这里通过 tenantId 判断，现在默认按照之前的 id


        // 默认单模型
        return false;
    }

    /**
     * 转换订单调整记录,在一次调整中，同一个商品项的调整聚合成列表
     *
     * @param orderAdjustLogs
     * @return
     */
    private List<OrderAdjustRecordVO> convertOrderAdjustVO(List<OCMSOrderAdjustLog> orderAdjustLogs) {
        if (CollectionUtils.isEmpty(orderAdjustLogs)) {
            return Lists.newArrayList();
        }
        Map<Long, BoothInfoVo> boothMap = getBoothMap(orderAdjustLogs);


        List<OrderAdjustRecordVO> adjustRecordVos = Lists.newArrayList();
        for (OCMSOrderAdjustLog orderAdjustLog : orderAdjustLogs) {
            OrderAdjustRecordVO adjustRecordVO = new OrderAdjustRecordVO();
            adjustRecordVO.setOperator(orderAdjustLog.getOperatorName() + "(" + orderAdjustLog.getOperatorAccount() + ")");
            adjustRecordVO.setUpdateTime(ConverterUtils.nonNullConvert(orderAdjustLog.getCreateTime(), time -> DateFormatUtils.format(time, Constants.DateFormats.SHOW_FORMAT)));
            adjustRecordVO.setComments(orderAdjustLog.getReason());


            /**
             *  orderAdjustTargetLogList的结构是按照<调整对象1.调整项1>/<调整对象1.调整项2>/<调整对象2.调整项1>
             *  需要转换成<调整对象1.调整项1/调整项2>/<调整对象2.调整项1> 的格式
             */

            List<OrderAdjustTargetLog> orderAdjustTargetLogList = orderAdjustLog.getOrderAdjustTargetLogList();

            Map<String, List<OrderAdjustRecordVO.AdjustItem>> adjustItemListMap = Maps.newHashMap();

            for (OrderAdjustTargetLog targetLog : orderAdjustTargetLogList) {
                OrderAdjustRecordVO.AdjustItem adjustItem = new OrderAdjustRecordVO.AdjustItem();
                adjustItem.setAdjustTargetItem(String.valueOf(targetLog.getOrderAdjustTargetItem()));
                //摊位显示转换
                if (targetLog.getOrderAdjustTargetItem().equals(AdjustTargetItemEnum.BOOTH_ID.getCode())) {
                    adjustItem.setBeforeValue(getBoothViewName(targetLog.getBeforeValue(), boothMap));
                    adjustItem.setAfterValue(getBoothViewName(targetLog.getAfterValue(), boothMap));
                }
                else if (targetLog.getOrderAdjustTargetItem().equals(AdjustTargetItemEnum.OFFLINE_PRICE.getCode())) {
                    // 进货价由分转元
                    if (StringUtils.isBlank(targetLog.getBeforeValue())) {
                        adjustItem.setBeforeValue("");
                    }
                    else {
                        adjustItem.setBeforeValue(ConverterUtils.formatMoneyWithYuan(Integer.parseInt(targetLog.getBeforeValue())));
                    }

                    if (StringUtils.isBlank(targetLog.getAfterValue())) {
                        adjustItem.setAfterValue("");
                    }
                    else {
                        adjustItem.setAfterValue(ConverterUtils.formatMoneyWithYuan(Integer.parseInt(targetLog.getAfterValue())));
                    }
                }
                else {
                    adjustItem.setBeforeValue(targetLog.getBeforeValue());
                    adjustItem.setAfterValue(targetLog.getAfterValue());
                }

                adjustItemListMap.computeIfAbsent(adjustItemKey(targetLog), k -> new ArrayList<>()).add(adjustItem);
            }

            List<OrderAdjustRecordVO.OrderAdjustRecord> orderAdjustRecordList = Lists.newArrayList();
            for (Map.Entry<String, List<OrderAdjustRecordVO.AdjustItem>> entry : adjustItemListMap.entrySet()) {
                OrderAdjustRecordVO.OrderAdjustRecord orderAdjustRecord = new OrderAdjustRecordVO.OrderAdjustRecord();
                String[] adjustRecordInfo = entry.getKey().split("_");
                orderAdjustRecord.setAdjustTargetId(adjustRecordInfo[0]);
                orderAdjustRecord.setAdjustTargetType(adjustRecordInfo[1]);
                orderAdjustRecord.setAdjustTargetName(adjustRecordInfo[2]);
                orderAdjustRecord.setAdjustItemList(entry.getValue());
                orderAdjustRecordList.add(orderAdjustRecord);
            }
            adjustRecordVO.setOrderAdjustRecordList(orderAdjustRecordList);
            adjustRecordVos.add(adjustRecordVO);
        }
        return adjustRecordVos;
    }

    private String adjustItemKey(OrderAdjustTargetLog targetLog) {
        return targetLog.getAdjustTargetId() + "_" + targetLog.getOrderAdjustTargetType() + "_" + targetLog.getAdjustTargetName();
    }

    private Map<Long, BoothInfoVo> getBoothMap(List<OCMSOrderAdjustLog> orderAdjustLogs) {
        Map<Long, BoothInfoVo> boothMap = Maps.newHashMap();
        Long tenantId = orderAdjustLogs.get(0).getTenantId();
        try {
            Set<Long> boothIds = Sets.newHashSet();
            orderAdjustLogs.stream().filter(e -> CollectionUtils.isNotEmpty(e.getOrderAdjustTargetLogList())).flatMap(e -> e.getOrderAdjustTargetLogList().stream())
                    .filter(e -> AdjustTargetItemEnum.BOOTH_ID.getCode() == e.getOrderAdjustTargetItem())
                    .forEach(e -> {
                        if (!Strings.isNullOrEmpty(e.getAfterValue())) {
                            boothIds.add(Long.parseLong(e.getAfterValue()));
                        }
                        if (!Strings.isNullOrEmpty(e.getBeforeValue())) {
                            boothIds.add(Long.parseLong(e.getBeforeValue()));
                        }
                    });
            if (CollectionUtils.isEmpty(boothIds)) {
                return boothMap;
            }
            List<BoothInfoVo> boothInfoVos = tenantRemoteService.queryBoothInfoByBoothIds(tenantId, Lists.newArrayList(boothIds));
            return Maps.uniqueIndex(boothInfoVos, BoothInfoVo::getBoothId);
        } catch (Exception e) {
            log.error("error occurred in getBoothMap!", e);
        }
        return boothMap;
    }

    private String getBoothViewName(String boothIdStr, Map<Long, BoothInfoVo> map) {
        if (Strings.isNullOrEmpty(boothIdStr)) {
            return "默认摊位";
        }
        try {
            long boothId = Long.parseLong(boothIdStr);
            BoothInfoVo booth = map.get(boothId);
            if (booth != null) {
                return booth.getBoothName();
            }
        } catch (Exception e) {
            log.error("error occurred in getBoothViewName!", e);
        }
        return boothIdStr;
    }

    private List<OrderAdjustRecordVO> buildOrderAdjustRecordVOs(Long tenantId, List<OrderChangeVoucherModel> orderAdjustVoucherModels) {
        if (CollectionUtils.isEmpty(orderAdjustVoucherModels)) {
            return Collections.emptyList();
        }

        Set<Long> boothIdSet = new HashSet<>();

        // 将调整凭证按时间由大到小排序
        orderAdjustVoucherModels.sort(Comparator.comparingLong(OrderChangeVoucherModel::getAdjustTimestamp).reversed());
        orderAdjustVoucherModels.stream()
                .filter(voucher -> CollectionUtils.isNotEmpty(voucher.getProductList()))
                .flatMap(voucher -> voucher.getProductList().stream())
                .forEach(product -> {
                    if (product.getBeforeBoothId() != null) {
                        boothIdSet.add(product.getBeforeBoothId());
                    }
                    if (product.getBoothId() != null) {
                        boothIdSet.add(product.getBoothId());
                    }
                });

        Map<Long, BoothInfoVo> boothMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(boothIdSet)) {
            List<BoothInfoVo> boothInfoVos = tenantRemoteService.queryBoothInfoByBoothIds(tenantId, new ArrayList<>(boothIdSet));
            boothMap = Maps.uniqueIndex(boothInfoVos, BoothInfoVo::getBoothId);
        }

        List<OrderAdjustRecordVO> orderAdjustRecordVOS = new ArrayList<>();
        for (OrderChangeVoucherModel voucherModel : orderAdjustVoucherModels) {
            orderAdjustRecordVOS.add(buildOrderAdjustRecordVO(voucherModel, boothMap));
        }

        return orderAdjustRecordVOS;
    }

    private OrderAdjustRecordVO buildOrderAdjustRecordVO(OrderChangeVoucherModel orderAdjustVoucherModel, Map<Long, BoothInfoVo> boothMap) {
        OrderAdjustRecordVO adjustRecordVO = new OrderAdjustRecordVO();
        adjustRecordVO.setComments(orderAdjustVoucherModel.getAdjustReason());
        adjustRecordVO.setUpdateTime(ConverterUtils.nonNullConvert(orderAdjustVoucherModel.getAdjustTimestamp(),
                time -> DateFormatUtils.format(time, Constants.DateFormats.SHOW_FORMAT)));
        if (orderAdjustVoucherModel.getOperatorName() != null && orderAdjustVoucherModel.getOperatorAccount() != null) {
            adjustRecordVO.setOperator(orderAdjustVoucherModel.getOperatorName() + "(" + orderAdjustVoucherModel.getOperatorAccount() + ")");
        }

        adjustRecordVO.setOrderAdjustRecordList(orderAdjustVoucherModel.getProductList().stream()
                .map(productModel -> buildOrderAdjustRecord(productModel, boothMap))
                .collect(Collectors.toList()));
        return adjustRecordVO;
    }

    private OrderAdjustRecordVO.OrderAdjustRecord buildOrderAdjustRecord(OrderChangeProductModel productModel, Map<Long, BoothInfoVo> boothMap) {
        OrderAdjustRecordVO.OrderAdjustRecord orderAdjustRecord = new OrderAdjustRecordVO.OrderAdjustRecord();
        orderAdjustRecord.setAdjustTargetId(String.valueOf(productModel.getOrderItemId()));
        orderAdjustRecord.setAdjustTargetType(PRODUCT_ADJUST_TARGET_TYPE);
        orderAdjustRecord.setAdjustTargetName(productModel.getProductName());

        List<OrderAdjustRecordVO.AdjustItem> adjustItemList = new ArrayList<>();
        if (!Objects.equals(productModel.getOfflinePrice(), productModel.getBeforeOfflinePrice())) {
            OrderAdjustRecordVO.AdjustItem adjustItem = new OrderAdjustRecordVO.AdjustItem();
            adjustItem.setAdjustTargetItem(String.valueOf(AdjustTargetItemEnum.OFFLINE_PRICE.getCode()));
            adjustItem.setBeforeValue(showOfflinePrice(productModel.getBeforeOfflinePrice()));
            adjustItem.setAfterValue(showOfflinePrice(productModel.getOfflinePrice()));
            adjustItemList.add(adjustItem);
        }

        if (!Objects.equals(productModel.getBoothId(), productModel.getBeforeBoothId())) {
            OrderAdjustRecordVO.AdjustItem adjustItem = new OrderAdjustRecordVO.AdjustItem();
            adjustItem.setAdjustTargetItem(String.valueOf(AdjustTargetItemEnum.BOOTH_ID.getCode()));
            adjustItem.setBeforeValue(showBoothName(productModel.getBeforeBoothId(), boothMap));
            adjustItem.setAfterValue(showBoothName(productModel.getBoothId(), boothMap));
            adjustItemList.add(adjustItem);
        }

        orderAdjustRecord.setAdjustItemList(adjustItemList);

        return orderAdjustRecord;
    }

    private String showOfflinePrice(Integer offlinePrice) {
        if (offlinePrice == null) {
            return "";
        }
        return ConverterUtils.formatMoneyWithYuan(offlinePrice);
    }

    private String showBoothName(Long boothId, Map<Long, BoothInfoVo> boothMap) {
        if (boothId == null || boothId.equals(BoothGlobals.DEFAULT_BOOTH_ID)) {
            return "默认摊位";
        }
        return boothMap.containsKey(boothId) ? boothMap.get(boothId).getBoothName() : boothId.toString();
    }

    @Override
    public PageResult<ChannelOrderBO> queryOrders(ChannelOrderQueryBO channelOrderQueryBO) {
        // 如果当前用户有查看订单收货地址的权限，则不隐藏收货地址
        channelOrderQueryBO.setHideOrderRecvAddress(!checkAccountOrderRecvAddressPermissions());
        PageResult<ChannelOrderBO> result = channelOrderRemoteService.queryOrders(channelOrderQueryBO);
        if (CollectionUtils.isEmpty(result.getList())) {
            return result;
        }
        boolean privatePhonePermission = checkAccountPrivatePhonePermission();

        //查询操作列表
        Map<OCMSOrderKey, ChannelOrderBO> orderMap = Maps.uniqueIndex(result.getList(), order -> new OCMSOrderKey(order.getOrderId(), order.getChannelId()));
        Map<String, Long> orderStoreMap = result.getList().stream().collect(Collectors.toMap(ChannelOrderBO::getOrderId, ChannelOrderBO::getPoiId));
        Map<OCMSOrderKey, List<Integer>> orderOperateMap = orderBizRemoteService.queryOrderOperateItems(channelOrderQueryBO.getTenantId(),
                Lists.newArrayList(orderMap.keySet()), Collections.emptyList(), orderStoreMap, orderMap);
        orderMap.forEach((key, order) -> {
            dealDrunkHorsePhone(order, privatePhonePermission);
            order.setCouldOperateItemList(orderOperateMap.get(key));
            appendDhDeliveryExtraData(order);
            appendDhOrderPoiData(order);
        });
        return result;
    }

    /**
     * 检查当前用户是否有查看订单收货地址的权限
     *
     * @return
     */
    private boolean checkAccountOrderRecvAddressPermissions() {
        Map<String, Boolean> result = authClient.checkAccountPermissions(ContextHolder.currentUid(),
                ContextHolder.currentUserLoginAppId(), Collections.singletonList(MccConfigUtil.getOrderRecvAddressPermissionCode()));
        return Boolean.TRUE.equals(result.get(MccConfigUtil.getOrderRecvAddressPermissionCode()));
    }

    private boolean checkAccountPrivatePhonePermission() {
        Map<String, Boolean> result = authClient.checkAccountPermissions(ContextHolder.currentUid(),
                ContextHolder.currentUserLoginAppId(), Collections.singletonList(MccConfigUtil.getOrderPrivatePhonePermissionCode()));
        return Boolean.TRUE.equals(result.get(MccConfigUtil.getOrderPrivatePhonePermissionCode()));
    }

    private boolean isWaiMaiChannel(int channelId) {
        return ChannelTypeEnum.MEITUAN.getValue() == channelId;
    }

    private boolean isDrunkHorse(Long tenantId) {
        return MccConfigUtil.getDrunkHorseTenantId().contains(tenantId);
    }

    private boolean isDrunkHorseChannel(int channelId) {
        return ChannelTypeEnum.MT_DRUNK_HORSE.getValue() == channelId;
    }

    private ChannelOrderBO dealDrunkHorsePhone(ChannelOrderBO channelOrderBO, boolean privatePhonePermission) {

        Long tenantId = channelOrderBO.getTenantId();
        boolean hideOrderListPhoneSwitch = Lion.getConfigRepository("com.sankuai.waimai.sc.saascrmeapi").getBooleanValue("drunkhorse.orderlist.hide.phone.switch", true);
        if (hideOrderListPhoneSwitch && isDrunkHorse(tenantId) && !privatePhonePermission) {
            String receiverPhone = PhoneUtil.replaceWithStar(channelOrderBO.getReceiverPhone());
            log.info("歪马订单，用户没有权限查看真实手机号，隐藏:{}", receiverPhone);
            channelOrderBO.setReceiverPhone(receiverPhone);
        }
        return channelOrderBO;
    }

    public void appendDhDeliveryExtraData(ChannelOrderBO channelOrderBO) {
        try {
            if (channelOrderBO == null || !isDrunkHorse(channelOrderBO.getTenantId())) {
                return;
            }
            DhDeliveryExtraVO orderOfflineVO = channelOrderRemoteService.queryDhDeliveryExtraData(channelOrderBO.getOrderId());
            if (orderOfflineVO != null) {
                channelOrderBO.setDeliveryDistance(orderOfflineVO.getDeliveryDistance());
                channelOrderBO.setDeliveryWeight(orderOfflineVO.getDeliveryWeight());
            }
        } catch (Exception ex) {
            log.error("appendDhDeliveryExtraData error, channelOrderBO:{}", channelOrderBO, ex);
        }
    }

    public void appendDhOrderPoiData(ChannelOrderBO channelOrderBO) {
        try {
            if (channelOrderBO == null || !isDrunkHorse(channelOrderBO.getTenantId())) {
                return;
            }
            Long deptId = null;
            Map<Long, PoiInfoDto> poiMap = poiClient.queryPoiInfosByPoiIds(Sets.newHashSet(channelOrderBO.getPoiId()));
            if (poiMap != null && poiMap.containsKey(channelOrderBO.getPoiId())) {
                PoiInfoDto poiInfoDto = poiMap.get(channelOrderBO.getPoiId());
                if (poiInfoDto != null) {
                    channelOrderBO.setManageMode(poiInfoDto.getPoiExtendContentDto() != null ? poiInfoDto.getPoiExtendContentDto().getOperationMode() : null);
                    deptId = poiInfoDto.getDepartmentId();
                }
            }
            if (deptId != null) {
                OrgParentBO orgParentBO = orgClient.queryParentOrg(channelOrderBO.getTenantId(), deptId);
                if (orgParentBO != null) {
                    String parentOrgName = orgParentBO != null ? orgParentBO.getParentName() : null;
                    parentOrgName = StringUtils.isNotBlank(parentOrgName) && parentOrgName.contains(FRANCHISE_PRE_STR) ? parentOrgName.replaceAll(FRANCHISE_PRE_STR, "") : parentOrgName;
                    channelOrderBO.setParentOrgName(parentOrgName);
                    OrgParentBO orgGrandParentBO = orgClient.queryParentOrg(channelOrderBO.getTenantId(), orgParentBO.getParentId());
                    String grandParentOrgName = orgGrandParentBO != null ? orgGrandParentBO.getParentName() : null;
                    grandParentOrgName = StringUtils.isNotBlank(grandParentOrgName) && grandParentOrgName.contains(FRANCHISE_PRE_STR) ? grandParentOrgName.replaceAll(FRANCHISE_PRE_STR, "") : grandParentOrgName;
                    channelOrderBO.setGrandParentOrgName(grandParentOrgName);
                }
            }
        } catch (Exception ex) {
            log.error("appendDhOrderPoiData error, channelOrderBO:{}", channelOrderBO, ex);
        }
    }

    @Override
    public ChannelOrderDetailBO queryDetail(String orderId, Long tenantId, int channelId, Long uid, Boolean containsMaterialSku) {
        // 如果当前用户有查看订单收货地址的权限，则不隐藏收货地址
        boolean privatePhonePermission = checkAccountPrivatePhonePermission();
        ChannelOrderDetailBO channelOrderDetailBO = channelOrderRemoteService.queryDetail(orderId, tenantId, channelId, uid, !checkAccountOrderRecvAddressPermissions(), privatePhonePermission);
        /***
         * 这里处理歪马业务隐私号展示逻辑
         * 歪马业务，外卖渠道和微商城渠道，真实隐私号和小号字段是反的
         * 外卖渠道receiverPrivatePhone是真实手机号，receiverPhone。微商城则相反
         * ***/
        if (isDrunkHorse(tenantId)) {
            if (isWaiMaiChannel(channelId) && !privatePhonePermission) {
                log.info("隐藏用户真实手机号");
                // 这里的receiverPhone是小号
                String receiverPhone = channelOrderDetailBO.getBaseInfo().getReceiverPhone();
                if (NumberUtils.INTEGER_ZERO.equals(channelOrderDetailBO.getBaseInfo().getUsePrivacyPhone())) {
                    //没有使用隐私号保护，receiverPhone是真实手机号
                    receiverPhone = PhoneUtil.replaceWithStar(receiverPhone);
                }
                channelOrderDetailBO.getBaseInfo().setReceiverPrivacyPhone(receiverPhone);
            }
            if (isDrunkHorseChannel(channelId) && privatePhonePermission) {
                log.info("展示用户真实手机号");
                // 这里的receiverPhone是真实手机号
                channelOrderDetailBO.getBaseInfo().setReceiverPrivacyPhone(channelOrderDetailBO.getBaseInfo().getReceiverPhone());
            }
            appendDhDeliveryExtraData(channelOrderDetailBO);
            appendDhOrderPoiData(channelOrderDetailBO, tenantId);
            appendDhInvoice(channelOrderDetailBO);
            appendDhFranchiseeFee(channelId, tenantId, channelOrderDetailBO);
            if (containsMaterialSku != null && containsMaterialSku) {
                appendMaterialSku(tenantId, channelId, channelOrderDetailBO);
            }
            //歪马微商城渠道名称区分
            channelOrderDetailBO.getBaseInfo()
                    .setChannelName(ChannelOrderConvertUtils.getDrunkHorseChannelNameByChannelId(channelId,
                            channelOrderDetailBO.getBaseInfo().getClientType()));
            giftBagService.appendGiftBagInfo(channelOrderDetailBO, tenantId);
        }
        // 脱敏收货人信息
        desensitizeReceiverInfo(channelOrderDetailBO, tenantId);
        return channelOrderDetailBO;
    }

    /**
     * 脱敏收货人信息
     * @param channelOrderDetailBO
     * @param tenantId
     */
    private void desensitizeReceiverInfo(ChannelOrderDetailBO channelOrderDetailBO, Long tenantId){
        try {
            ChannelOrderDetailBO.BaseInfo baseInfo = channelOrderDetailBO.getBaseInfo();
            // 订单状态已完结 && 非歪马租户 && 租户支持隐藏隐私号
            Long orderStatusTime = getOrderTimeByLog(baseInfo.getOrderStatus(), channelOrderDetailBO.getOperateLog());
            if (OrderUtil.isOrderEnd(baseInfo.getOrderStatus())
                    && !isDrunkHorse(tenantId)
                    && DesensitizeReceiverInfoUtil.checkSupportDesensitizeReceiverInfoTenant(tenantId)) {
                // 订单超过时间，需要隐藏隐私号
                if (OrderUtil.isOverConfigTime(orderStatusTime, DesensitizeReceiverInfoUtil.getDesensitizeReceiverInfoTime())) {
                    baseInfo.setReceiverPhone(null);
                }
            }

            // 订单收货人信息脱敏处理
            DesensitizeReceiverInfoExtParam receiverInfoExtParam = DesensitizeReceiverInfoExtParam.build(
                    baseInfo.getReceiverName(), baseInfo.getReceiverAddress(), baseInfo.getReceiverPhone(),
                    baseInfo.getReceiverPrivacyPhone(), tenantId, baseInfo.getOrderStatus(), orderStatusTime);
            DesensitizeReceiverInfoResult desensitizeReceiverInfoResult = DesensitizeReceiverInfoUtil.desensitizeReceiverInfo(receiverInfoExtParam);
            if(Objects.isNull(desensitizeReceiverInfoResult)){
                return;
            }
            baseInfo.setReceiverName(desensitizeReceiverInfoResult.getReceiverName());
            baseInfo.setReceiverAddress(desensitizeReceiverInfoResult.getReceiverAddress());
            baseInfo.setReceiverPhone(desensitizeReceiverInfoResult.getReceiverPhone());
            baseInfo.setReceiverPrivacyPhone(desensitizeReceiverInfoResult.getReceiverPrivacyPhone());
        }catch (Exception e){
            log.warn("ChannelOrderServiceImpl脱敏收货人信息失败！channelOrderDetailBO: {}", JSON.toJSONString(channelOrderDetailBO), e);
        }
    }

    @Override
    public CommonDataBO<List<UiOption>> refundReasonList(QueryRefundReasonRequest request) {
        return orderBizClient.refundReasonList(request);
    }

    @Override
    public PageResultV2<ConfirmOrderVO> unDoneOrderList(MultiShopConfirmOrderQueryRequest confirmOrderQueryRequest) {
        OcmsUnDoneOrderListReq ocmsUnDoneOrderListReq = channelOrderConverter.ocmsUnDoneQueryRequestConvert(confirmOrderQueryRequest);
        return channelOrderRemoteService.unDoneOrderList(ocmsUnDoneOrderListReq);
    }

    @Override
    public PageResult<AuditOrderBO> queryAuditOrders(AuditOrderListQueryBO auditOrderListQueryBO) {
        return channelOrderRemoteService.queryAuditOrders(auditOrderListQueryBO);
    }

    @Override
    public PageResultV2<DhLateOrderVO> queryLateOrderVOList(QueryLateOrderRequest request) {
        return channelOrderRemoteService.queryLateOrderList(request);
    }

    @Override
    public void approveRefund(ApproveRefundBO approveRefundBO) {
        if (!OrderConfiguration.hitOcmsMigration(0L)) {
            doApproveRefundThroughOcms(approveRefundBO);
            return;
        }
        if (Objects.equals(approveRefundBO.getChannelId(), DynamicChannelType.MEITUAN.getChannelId())) {
            //美团名酒馆订单返回错误提示
            Pair<Boolean, String> booleanStringPair = channelOrderRemoteService.refundCheckMtFamousTavern(approveRefundBO.getOrderId(), approveRefundBO.getTenantId());
            if (booleanStringPair.getKey()) {
                throw new BizException(booleanStringPair.getValue());
            }
        }

        if (approveRefundBO.isAgree()) {
            orderBizClient.agreeRefund(approveRefundBO);
        } else {
            orderBizClient.rejectRefund(approveRefundBO);
        }
    }

    @Override
    public List<DhLateOrderVO> queryAllLateOrderVOList(LateOrderDownloadRequest request) {
        return channelOrderRemoteService.queryAllLateOrderList(request);
    }

    /**
     * 获取指定状态的订单时间
     * 时间降序排序，取第一个
     * @param status
     * @param logList
     * @return
     */
    private Long getOrderTimeByLog(Integer status, List<ChannelOrderDetailBO.OperateLog> logList){
        if (Objects.isNull(status) || CollectionUtils.isEmpty(logList)) {
            return null;
        }
        //  过滤出已完结状态的订单状态日志列表，并返回最近的一个完结时间
        ChannelOrderDetailBO.OperateLog orderLog = logList.stream()
                .filter(f -> Objects.equals(status, f.getTargetStatus()))
                .sorted(Comparator.comparing(ChannelOrderDetailBO.OperateLog::getOperateTime).reversed())
                .findFirst().orElse(null);
        if (Objects.isNull(orderLog) || Objects.isNull(orderLog.getOperateTime())) {
            return null;
        }
        return orderLog.getOperateTime().getTime();
    }

    public void appendDhDeliveryExtraData(ChannelOrderDetailBO channelOrderDetailBO) {
        try {
            if (channelOrderDetailBO == null || channelOrderDetailBO.getBaseInfo() == null) {
                return;
            }
            ChannelOrderDetailBO.BaseInfo baseInfo = channelOrderDetailBO.getBaseInfo();

            DhDeliveryExtraVO orderOfflineVO = channelOrderRemoteService.queryDhDeliveryExtraData(baseInfo.getOrderId());
            if (orderOfflineVO != null) {
                baseInfo.setDeliveryDistance(orderOfflineVO.getDeliveryDistance());
                baseInfo.setDeliveryWeight(orderOfflineVO.getDeliveryWeight());
            }
        } catch (Exception ex) {
            log.error("appendDhDeliveryExtraData error, channelOrderDetailBO:{}", channelOrderDetailBO, ex);
        }


    }

    public void appendDhOrderPoiData(ChannelOrderDetailBO channelOrderDetailBO, Long tenantId) {
        try {
            if (channelOrderDetailBO == null || channelOrderDetailBO.getBaseInfo() == null) {
                return;
            }
            ChannelOrderDetailBO.BaseInfo baseInfo = channelOrderDetailBO.getBaseInfo();
            Long deptId = null;
            Map<Long, PoiInfoDto> poiMap = poiClient.queryPoiInfosByPoiIds(Sets.newHashSet(baseInfo.getPoiId()));
            if (poiMap != null && poiMap.containsKey(baseInfo.getPoiId())) {
                PoiInfoDto poiInfoDto = poiMap.get(baseInfo.getPoiId());
                if (poiInfoDto != null) {
                    deptId = poiInfoDto.getDepartmentId();
                    baseInfo.setManageMode(poiInfoDto.getPoiExtendContentDto() != null ? poiInfoDto.getPoiExtendContentDto().getOperationMode() : null);
                    baseInfo.setCityName(poiInfoDto.getDistrict() != null ? poiInfoDto.getDistrict().getCityName() : null);
                }
            }
            if (deptId != null) {
                OrgParentBO orgParentBO = orgClient.queryParentOrg(tenantId, deptId);
                if (orgParentBO != null) {
                    String parentOrgName = orgParentBO != null ? orgParentBO.getParentName() : null;
                    parentOrgName = StringUtils.isNotBlank(parentOrgName) && parentOrgName.contains(FRANCHISE_PRE_STR) ? parentOrgName.replaceAll(FRANCHISE_PRE_STR, "") : parentOrgName;
                    baseInfo.setParentOrgName(parentOrgName);
                    OrgParentBO orgGrandParentBO = orgClient.queryParentOrg(tenantId, orgParentBO.getParentId());
                    String grandParentOrgName = orgGrandParentBO != null ? orgGrandParentBO.getParentName() : null;
                    grandParentOrgName = StringUtils.isNotBlank(grandParentOrgName) && grandParentOrgName.contains(FRANCHISE_PRE_STR) ? grandParentOrgName.replaceAll(FRANCHISE_PRE_STR, "") : grandParentOrgName;
                    baseInfo.setGrandParentOrgName(grandParentOrgName);
                }
            }
        } catch (Exception ex) {
            log.error("appendDhOrderPoiData error, channelOrderBO:{}", channelOrderDetailBO, ex);
        }
    }

    public void appendDhInvoice(ChannelOrderDetailBO channelOrderDetailBO) {
        try {
            if (channelOrderDetailBO == null || channelOrderDetailBO.getBaseInfo() == null) {
                return;
            }
            List<InvoiceApplyRecordBO> invoiceApplyRecordBOS = invoiceClient.queryInvoiceRecord(channelOrderDetailBO.getBaseInfo().getOrderId());
            List<InvoiceDetailBO> invoiceDetailBOS = ConverterUtils.convertList(invoiceApplyRecordBOS, InvoiceDetailBO::buildInvoiceDetailBO);
            if (CollectionUtils.isNotEmpty(invoiceDetailBOS)) {
                channelOrderDetailBO.setInvoiceList(invoiceDetailBOS);
            }
        } catch (Exception ex) {
            log.error("appendDhInvoice error, channelOrderDetailBO:{}", channelOrderDetailBO, ex);
        }
    }

    /**
     * 添加歪马加盟费率
     *
     * @param bo
     */
    private void appendDhFranchiseeFee(int channelId, Long tenantId, ChannelOrderDetailBO bo) {
        try {
            if (bo == null || bo.getBaseInfo() == null) {
                return;
            }
            if (!MccConfigUtil.checkIsFranchiseeFeePoi(tenantId, bo.getBaseInfo().getPoiId())) {
                return;
            }
            ChannelOrderDetailBO.BaseInfo baseInfo = bo.getBaseInfo();
            Boolean isFranchiseeOrder = baseInfo.getIsFranchiseeOrder();
            Boolean isConsignmentOrder = baseInfo.getIsConsignment();
            Integer refundType = NO_REFUND;
            if (Objects.equals(baseInfo.getStatus(), ChannelOrderStatusEnum.CANCELED.getDesc())) {
                refundType = ALL_REFUND;
            } else {
                refundType = CollectionUtils.isNotEmpty(bo.getDrunkhorseRefundFeeBOS()) ? PART_REFUND : NO_REFUND;
            }
            if (isWaiMaiChannel(channelId) || Objects.equals(refundType, ALL_REFUND)) { //闪购渠道/微商城全部退
                addMerchantAmountHover(bo, isFranchiseeOrder, refundType);
                addCommisionAmountHover(bo, isFranchiseeOrder, refundType);
                addCostOfGoodsSold(tenantId, bo, isFranchiseeOrder, refundType, isConsignmentOrder);
                addActualGross(bo, isFranchiseeOrder, refundType);

            } else { //微商城渠道无退、部分退
                addMerchantAmountHover(bo, isFranchiseeOrder, NO_REFUND);
                addCommisionAmountHover(bo, isFranchiseeOrder, NO_REFUND);
                addCostOfGoodsSold(tenantId, bo, isFranchiseeOrder, refundType, isConsignmentOrder);
                addActualGross(bo, isFranchiseeOrder, NO_REFUND);
            }
            if(isFranchiseeOrder && isConsignmentOrder) {
                appendConsignmentOrderHover(bo);
            }
        } catch (Exception exception) {
            log.error("appendDhFranchiseeFee error", exception);

        }
    }

    /**
     * 处理代销订单的金额、hover展示
     * @param bo
     */
    private void appendConsignmentOrderHover(ChannelOrderDetailBO bo) {
        ChannelOrderDetailBO.BaseInfo baseInfo = bo.getBaseInfo();
        baseInfo.setMerchantAmountHover(String.format("%s；%s", baseInfo.getMerchantAmountHover(), CONSIGNMENT_HOVER));
        baseInfo.setCommisionAmountHover(String.format("%s；%s", baseInfo.getCommisionAmountHover(), CONSIGNMENT_HOVER));
        baseInfo.setCostOfGoodsSoldHover(String.format("%s；%s", baseInfo.getCostOfGoodsSoldHover(), CONSIGNMENT_HOVER));
        baseInfo.setActualGrossProfitHover(String.format("%s；%s", baseInfo.getActualGrossProfitHover(), CONSIGNMENT_HOVER));
        baseInfo.setActualGrossRateHover(String.format("%s；%s", baseInfo.getActualGrossRateHover(), CONSIGNMENT_HOVER));
        baseInfo.setTotalDiscountHover(CONSIGNMENT_HOVER);
    }

    /**
     * 添加实收hover
     */
    public void addMerchantAmountHover(ChannelOrderDetailBO bo, Boolean isFranchiseeOrder, Integer retundType) {
        ChannelOrderDetailBO.BaseInfo baseInfo = bo.getBaseInfo();
        String hoverContext = MERCHANT_AMOUNT_HOVER;
        if (isFranchiseeOrder == false || Objects.equals(retundType, NO_REFUND)) {
            baseInfo.setMerchantAmountHover(hoverContext);
            return;
        }
        StringBuilder sb = new StringBuilder(hoverContext);
        if (Objects.equals(retundType, ALL_REFUND)) {
            sb.append(REFUND_MERCHANT_AMOUNT_HOVER).append(baseInfo.getFranchiseeMerchantAmount()).append("元");
        } else if (Objects.equals(retundType, PART_REFUND)) {
            for (DrunkhorseRefundFeeBO dto : bo.getDrunkhorseRefundFeeBOS()) {
                if (dto.getSettleAmount() == null) {
                    continue;
                }
                sb.append(REFUND_MERCHANT_AMOUNT_HOVER).append(dto.getSettleAmount()).append("元");
            }
        }
        baseInfo.setMerchantAmountHover(sb.toString());
    }

    /**
     * 添加佣金hover
     */
    public void addCommisionAmountHover(ChannelOrderDetailBO bo, Boolean isFranchiseeOrder, Integer retundType) {
        ChannelOrderDetailBO.BaseInfo baseInfo = bo.getBaseInfo();
        if (isFranchiseeOrder == false) {
            return;
        }
        String hoverContext = COMMISION_AMOUNT_HOVER;
        if (Objects.equals(retundType, NO_REFUND)) {
            baseInfo.setCommisionAmountHover(hoverContext);
            return;
        }
        StringBuilder sb = new StringBuilder(hoverContext);
        if (Objects.equals(retundType, ALL_REFUND)) {
            sb.append(REFUND_COMMISION_AMOUNT_HOVER).append(baseInfo.getCommisionAmount()).append("元");
        } else if (Objects.equals(retundType, PART_REFUND)) {
            for (DrunkhorseRefundFeeBO dto : bo.getDrunkhorseRefundFeeBOS()) {
                if (dto.getSettleAmount() == null) {
                    continue;
                }
                sb.append(REFUND_COMMISION_AMOUNT_HOVER).append(dto.getCommisionAmount()).append("元");
            }
        }
        baseInfo.setCommisionAmountHover(sb.toString());
    }


    /**
     * 添加商品成本及hover
     */
    public void addCostOfGoodsSold(Long tenantId, ChannelOrderDetailBO bo, Boolean isFranchiseeOrder, Integer retundType, Boolean isConsignmentOrder) {
        try {
            ChannelOrderDetailBO.BaseInfo baseInfo = bo.getBaseInfo();
            String hoverContext = COSR_OF_GOODS_SOLD_HOVER;
            baseInfo.setCostOfGoodsSoldHover(hoverContext);
            List<String> goodsCodeIds = bo.getItemInfo().stream().map(ChannelOrderDetailBO.ItemInfo::getSku).distinct().collect(Collectors.toList());
            List<GoodsSkuRelationDto> relationDtos = goodsCenterClient.queryGoodsSkuRelationByGoodsCodes(tenantId, goodsCodeIds);
            log.info("查询商货品信息:{}", relationDtos);
            List<String> goodsIds = relationDtos.stream().map(GoodsSkuRelationDto::getGoodsId).distinct().collect(Collectors.toList());
            Map<String, Double> goodsWacMap = priceClient.calculateTotalGoodsWACPrice(tenantId, bo.getBaseInfo().getPoiId(), goodsIds);
            log.info("basicSkuWacPriceQueryThriftService.batchQueryWarehouseBasicSkuWacPrice:{}", goodsWacMap);
            if (CollectionUtils.isEmpty(relationDtos) || MapUtils.isEmpty(goodsWacMap)) {
                log.error("查询商货品信息为空或wac价为空，orderId:{}", bo.getBaseInfo().getOrderId());
                return;
            }
            Map<String, List<GoodsSkuRelationDto>> skuIdToGoodsListMap = relationDtos.stream().collect(Collectors.groupingBy(
                    GoodsSkuRelationDto::getSkuId));
            BigDecimal costOfGoodsSold = new BigDecimal(0);
            if(isConsignmentOrder && isFranchiseeOrder) { //加盟代销品订单需要剔除代销品费用
                costOfGoodsSold = bo.getItemInfo().stream()
                        .filter(itemInfo -> !itemInfo.getIsConsignment())
                        .flatMap(itemInfo -> skuIdToGoodsListMap.get(itemInfo.getSku()).stream()
                                .map(dto -> {
                                    BigDecimal amount = new BigDecimal(dto.getAmount());
                                    BigDecimal wac = BigDecimal.valueOf(goodsWacMap.get(dto.getGoodsId()));
                                    BigDecimal itemQuantity = BigDecimal.valueOf(itemInfo.getQuantity());
                                    return amount.multiply(wac).multiply(itemQuantity);
                                })).reduce(BigDecimal.ZERO, BigDecimal::add);
            } else {
                costOfGoodsSold = bo.getItemInfo().stream().flatMap(itemInfo -> skuIdToGoodsListMap.get(itemInfo.getSku()).stream()
                        .map(dto -> {
                            BigDecimal amount = new BigDecimal(dto.getAmount());
                            BigDecimal wac = BigDecimal.valueOf(goodsWacMap.get(dto.getGoodsId()));
                            BigDecimal itemQuantity = BigDecimal.valueOf(itemInfo.getQuantity());
                            return amount.multiply(wac).multiply(itemQuantity);
                        })).reduce(BigDecimal.ZERO, BigDecimal::add);
            }

            baseInfo.setCostOfGoodsSold(costOfGoodsSold.setScale(2, RoundingMode.HALF_UP).toString());
            if (isFranchiseeOrder == false || Objects.equals(retundType, NO_REFUND)) {
                return;
            }
            StringBuilder sb = new StringBuilder(hoverContext);
            if (Objects.equals(retundType, ALL_REFUND)) {
                sb.append(REFUND_COST_OF_GOODS_SOLD_HOVER).append(baseInfo.getCostOfGoodsSold()).append("元");
            }
            if (Objects.equals(retundType, PART_REFUND)) {
                for (DrunkhorseRefundFeeBO dto : bo.getDrunkhorseRefundFeeBOS()) {
                    if (CollectionUtils.isEmpty(dto.getAfterSaleRecordDetailList())) {
                        continue;
                    }
                    List<String> refundGoodsCodeIds = dto.getAfterSaleRecordDetailList().stream().map(AfterSaleRecordDetailVo::getSkuId).distinct().collect(Collectors.toList());
                    List<GoodsSkuRelationDto> refundRelationDtos = goodsCenterClient.queryGoodsSkuRelationByGoodsCodes(tenantId, refundGoodsCodeIds);
                    List<String> refundGoodsIds = refundRelationDtos.stream().map(GoodsSkuRelationDto::getGoodsId).distinct().collect(Collectors.toList());
                    Map<String, Double> refundGoodsWacMap = priceClient.calculateTotalGoodsWACPrice(tenantId, bo.getBaseInfo().getPoiId(), refundGoodsIds);
                    if (CollectionUtils.isEmpty(refundRelationDtos) || MapUtils.isEmpty(refundGoodsWacMap)) {
                        continue;
                    }
                    Map<String, List<GoodsSkuRelationDto>> refundSkuIdToGoodsListMap = refundRelationDtos.stream().collect(Collectors.groupingBy(
                            GoodsSkuRelationDto::getSkuId));
                    BigDecimal refundCostOfGoodsSold = dto.getAfterSaleRecordDetailList().stream().flatMap(aftersale -> refundSkuIdToGoodsListMap.get(aftersale.getSkuId()).stream()
                            .map(item -> {
                                BigDecimal amount = new BigDecimal(item.getAmount());
                                BigDecimal wac = BigDecimal.valueOf(refundGoodsWacMap.get(item.getGoodsId()));
                                BigDecimal itemQuantity = BigDecimal.valueOf(aftersale.getCount());
                                return amount.multiply(wac).multiply(itemQuantity);
                            })).reduce(BigDecimal.ZERO, BigDecimal::add);
                    String refundCostOfGoodsSoldStr = refundCostOfGoodsSold.setScale(2, RoundingMode.HALF_UP).toString();
                    sb.append(REFUND_COST_OF_GOODS_SOLD_HOVER).append(refundCostOfGoodsSoldStr).append("元");
                    dto.setCostOfGoodsSold(refundCostOfGoodsSoldStr);
                }
            }
            baseInfo.setCostOfGoodsSoldHover(sb.toString());
        } catch (Exception ex) {
            log.error("addCostOfGoodsSold error", ex);
        }
    }

    /**
     * 添加毛利率、毛利额数据及hover
     */
    public void addActualGross(ChannelOrderDetailBO bo, Boolean isFranchiseeOrder, Integer retundType) {
        ChannelOrderDetailBO.BaseInfo baseInfo = bo.getBaseInfo();
        String profitHoverContext = ACTUAL_GROSS_PROFIT_HOVER;
        String rateHoverContext = ACTUAL_GROSS_RATE_HOVER;
        baseInfo.setActualGrossProfitHover(profitHoverContext);
        baseInfo.setActualGrossRateHover(rateHoverContext);
        if (isFranchiseeOrder == true && baseInfo.getFranchiseeMerchantAmount() == null) {
            log.info("加盟商家计算未完成，毛利额、毛利率不计算");
            return;
        }
        if (baseInfo.getCostOfGoodsSold() == null) {
            log.info("商品成本未计算完成，毛利额、毛利率不计算");
            return;
        }
        //计算并赋值
        BigDecimal settleAmount = isFranchiseeOrder == true ? new BigDecimal(baseInfo.getFranchiseeMerchantAmount()) : BigDecimal.valueOf(baseInfo.getMerchantAmount());
        BigDecimal costOfGoodsSold = new BigDecimal(baseInfo.getCostOfGoodsSold());
        BigDecimal actualGrossProfit = settleAmount.subtract(costOfGoodsSold);
        String actualGrossProfitStr = actualGrossProfit.setScale(2, RoundingMode.HALF_UP).toString();
        baseInfo.setActualGrossProfit(actualGrossProfitStr);
        if (settleAmount.compareTo(BigDecimal.ZERO) != 0) {
            BigDecimal actualGrossRate = actualGrossProfit.divide(settleAmount, 4, RoundingMode.HALF_UP);
            String actualGrossRateStr = actualGrossRate.toString();
            baseInfo.setActualGrossRate(convertToPercentage(actualGrossRateStr));
        }

        //设置hover文案
        if (isFranchiseeOrder == false || Objects.equals(retundType, NO_REFUND)) {
            return;
        }
        StringBuilder profitSb = new StringBuilder(profitHoverContext);
        StringBuilder rateSb = new StringBuilder(rateHoverContext);
        if (Objects.equals(retundType, ALL_REFUND)) {
            profitSb.append(REFUND_ACTUAL_GROSS_PROFIT_HOVER).append(baseInfo.getActualGrossProfit()).append("元");
            rateSb.append(REFUND_ACTUAL_GROSS_RATE_HOVER).append(convertToPercentage(baseInfo.getActualGrossRate()));
        }
        if (Objects.equals(retundType, PART_REFUND)) {
            for (DrunkhorseRefundFeeBO dto : bo.getDrunkhorseRefundFeeBOS()) {
                if (dto.getSettleAmount() == null || dto.getCostOfGoodsSold() == null) {
                    continue;
                }
                BigDecimal refundSettleAmount = new BigDecimal(dto.getSettleAmount());
                BigDecimal refundCostOfGoodsSold = new BigDecimal(dto.getCostOfGoodsSold());
                BigDecimal refundActualGrossProfit = refundSettleAmount.subtract(refundCostOfGoodsSold);
                String refundActualGrossProfitStr = refundActualGrossProfit.setScale(2, RoundingMode.HALF_UP).toString();
                profitSb.append(REFUND_ACTUAL_GROSS_PROFIT_HOVER).append(refundActualGrossProfitStr).append("元");
                if (refundSettleAmount.compareTo(BigDecimal.ZERO) != 0) {
                    BigDecimal refundActualGrossRate = refundActualGrossProfit.divide(refundSettleAmount, 4, RoundingMode.HALF_UP);
                    String refundActualGrossRateStr = refundActualGrossRate.toString();
                    rateSb.append(REFUND_ACTUAL_GROSS_RATE_HOVER).append(convertToPercentage(refundActualGrossRateStr));
                }

            }
        }
        baseInfo.setActualGrossProfitHover(profitSb.toString());
        baseInfo.setActualGrossRateHover(rateSb.toString());
    }

    /**
     * 小数字符串转为百分比
     *
     * @param str
     * @return
     */
    public String convertToPercentage(String str) {
        if (StringUtils.isBlank(str)) {
            return StringUtils.EMPTY;
        }
        BigDecimal decimal = new BigDecimal(str);
        BigDecimal percentageDecimal = decimal.multiply(BigDecimal.valueOf(100));
        BigDecimal roundedPercentage = percentageDecimal.setScale(2, RoundingMode.HALF_UP);
        return String.format("%s%%", roundedPercentage.toString());
    }

    private void appendMaterialSku(Long tenantId, int channelId, ChannelOrderDetailBO channelOrderDetailBO) {
        try {
            // 获取订单的基础sku
            BizOrderModel bizOrderModel = orderBizClient.queryOrderModelWithCompose(channelOrderDetailBO.getBaseInfo().getOrderId(), channelId, tenantId);
            List<MaterialSkuWithTagBO> materialSkuWithTagBOS = parseToMaterialSkuList(bizOrderModel);
            // 增加大单标记
            addBigOrderTag(tenantId, String.valueOf(channelOrderDetailBO.getBaseInfo().getOfflineOrderId()), materialSkuWithTagBOS);

            channelOrderDetailBO.setMaterialSkuWithTagBOS(materialSkuWithTagBOS);
        } catch (Exception e) {
            // 不影响主流程
            log.error("appendBigOrderTag error", e);
        }
    }

    private List<MaterialSkuWithTagBO> parseToMaterialSkuList(BizOrderModel bizOrderModel) {
        // 主品
        List<MaterialSkuWithTagBO> materialSkuWithTagBOS = getOrderMaterialSku(bizOrderModel);
        // 赠品
        List<MaterialSkuWithTagBO> giftMaterialSku = getGiftMaterialSku(bizOrderModel);
        // 合起来
        Map<String, MaterialSkuWithTagBO> skuMap = new HashMap<>();
        mergeMaterialSkuList(materialSkuWithTagBOS, skuMap);
        mergeMaterialSkuList(giftMaterialSku, skuMap);

        List<MaterialSkuWithTagBO> skuBos = new ArrayList<>(skuMap.values());
        skuBos.sort(Comparator.comparing(MaterialSkuWithTagBO::getMaterialSkuId));
        return skuBos;
    }

    private void addBigOrderTag(Long tenantId, String orderId, List<MaterialSkuWithTagBO> materialSkuWithTagBOS) {
        try {
            List<BigOrderSkuModel> bigOrderSkuModels = orderTagClient.getBigOrderTag(tenantId, orderId);

            Map<String, BigOrderSkuModel> skuTagMap = Optional.ofNullable(bigOrderSkuModels).orElse(new ArrayList<>())
                    .stream()
                    .collect(Collectors.toMap(BigOrderSkuModel::getMaterialSkuId, v -> v, (v1, v2) -> v1));
            for (MaterialSkuWithTagBO itemInfo : materialSkuWithTagBOS) {
                itemInfo.setMajorItemTag(skuTagMap.containsKey(itemInfo.getMaterialSkuId()));
            }
        } catch (Exception e) {
            log.error("查询大单标记失败", e);
        }
    }

    private void mergeMaterialSkuList(List<MaterialSkuWithTagBO> materialSkuWithTagBOS,
                                      Map<String, MaterialSkuWithTagBO> skuMap) {
        if (CollectionUtils.isEmpty(materialSkuWithTagBOS)) {
            return;
        }
        for (MaterialSkuWithTagBO materialSkuWithTagBO : materialSkuWithTagBOS) {
            String skuId = materialSkuWithTagBO.getMaterialSkuId();
            if (skuMap.containsKey(skuId)) {
                MaterialSkuWithTagBO tagBO = skuMap.get(skuId);
                tagBO.setQuantity(tagBO.getQuantity() + materialSkuWithTagBO.getQuantity());
            } else {
                skuMap.put(skuId, materialSkuWithTagBO);
            }
        }
    }

    private List<MaterialSkuWithTagBO> getOrderMaterialSku(BizOrderModel bizOrderModel) {
        List<ComposeSkuModel> majorSku = bizOrderModel.getComposeSkuModels();
        if (CollectionUtils.isEmpty(majorSku)) {
            return new ArrayList<>();
        }
        List<BizOrderItemModel> itemModels = bizOrderModel.getBizOrderItemModelList();

        Map<Long, BizOrderItemModel> itemMap = itemModels.stream()
                .collect(Collectors.toMap(BizOrderItemModel::getOrderItemId, Function.identity(), (v1, v2) -> v1));
        List<MaterialSkuWithTagBO> materialSkuWithTagBOS = new ArrayList<>();
        for (ComposeSkuModel composeSkuModel : majorSku) {
            Long itemId = composeSkuModel.getOrderItemId();
            if (!itemMap.containsKey(itemId)) {
                log.warn("数据异常，缺少订单项数据 {}", JsonUtils.toJson(bizOrderModel));
                continue;
            }
            Integer quantity = itemMap.get(itemId).getQuantity();
            for (ChildSkuModel detailModel : composeSkuModel.getChildItemList()) {
                MaterialSkuWithTagBO tagBO = new MaterialSkuWithTagBO();
                tagBO.setMaterialSkuId(detailModel.getSkuId());
                tagBO.setMaterialSkuName(detailModel.getSpuName());
                tagBO.setUnit(detailModel.getUnit());
                tagBO.setQuantity(quantity * (int) detailModel.getAmount());

                materialSkuWithTagBOS.add(tagBO);
            }
        }
        return materialSkuWithTagBOS;
    }

    private List<MaterialSkuWithTagBO> getGiftMaterialSku(BizOrderModel bizOrderModel) {
        List<GiftComposeSkuModel> giftComposeSkuModels = bizOrderModel.getGiftComposeSkuModels();
        if (CollectionUtils.isEmpty(giftComposeSkuModels)) {
            return new ArrayList<>();
        }
        List<BizOrderGiftModel> giftModels = bizOrderModel.getBizOrderGiftModelList();
        if (CollectionUtils.isEmpty(giftModels)) {
            return new ArrayList<>();
        }
        // 同一个sku需要加起来
        Map<String, Integer> giftSkuMap = new HashMap<>();
        for (BizOrderGiftModel giftModel : giftModels) {
            String skuId = giftModel.getSkuId();
            if (giftSkuMap.containsKey(skuId)) {
                giftSkuMap.put(skuId, giftSkuMap.get(skuId) + giftModel.getQuantity());
            } else {
                giftSkuMap.put(skuId, giftModel.getQuantity());
            }
        }

        List<MaterialSkuWithTagBO> tagBOS = new ArrayList<>();
        for (GiftComposeSkuModel skuModel : giftComposeSkuModels) {
            String skuId = skuModel.getSkuId();
            if (!giftSkuMap.containsKey(skuId)) {
                continue;
            }
            Integer quantity = giftSkuMap.get(skuId);
            for (ChildSkuModel childSkuModels : skuModel.getChildItemList()) {
                MaterialSkuWithTagBO tagBO = new MaterialSkuWithTagBO();
                tagBO.setMaterialSkuId(childSkuModels.getSkuId());
                tagBO.setMaterialSkuName(childSkuModels.getSpuName());
                tagBO.setUnit(childSkuModels.getUnit());
                tagBO.setQuantity(quantity * (int) childSkuModels.getAmount());
                tagBOS.add(tagBO);
            }
        }
        return tagBOS;
    }

    private void doApproveRefundThroughOcms(ApproveRefundBO approveRefundBO) {
        if (approveRefundBO.isAgree()) {
            channelOrderRemoteService.agreeRefund(approveRefundBO.getOrderId()
                    , approveRefundBO.getTenantId()
                    , approveRefundBO.getOperatorId()
                    , approveRefundBO.getOperator(), "OK", approveRefundBO.getChannelId()
                    , approveRefundBO.getRefundTagId()
                    , approveRefundBO.getServiceId(), approveRefundBO.getAfsApplyType());
        } else {
            channelOrderRemoteService.rejectRefund(approveRefundBO.getOrderId()
                    , approveRefundBO.getTenantId(), approveRefundBO.getOperatorId()
                    , approveRefundBO.getOperator()
                    , approveRefundBO.getReason(), approveRefundBO.getChannelId()
                    , approveRefundBO.getRefundTagId()
                    , approveRefundBO.getServiceId(), approveRefundBO.getAfsApplyType());
        }
    }


}
