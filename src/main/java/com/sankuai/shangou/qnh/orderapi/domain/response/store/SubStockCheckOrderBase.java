package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TypeDoc(
        description = "子盘点单基本信息",
        authors = {
                "qianteng"
        }
)
@Data
@ApiModel("子盘点单基本信息")
public class SubStockCheckOrderBase {

    @FieldDoc(
            description = "子盘点单号"
    )
    private String subStockCheckOrderId;
    @FieldDoc(
            description = "盘点商品种类"
    )
    @ApiModelProperty(name = "盘点商品种类")
    private int skuCount; // required
    @FieldDoc(
            description = "备注"
    )
    private String comment; // required
    @FieldDoc(
            description = "提交人"
    )
    @ApiModelProperty(name = "提交人")
    private String submitor; // required
    @FieldDoc(
            description = "提交时间"
    )
    @ApiModelProperty(name = "提交时间")
    private String submitTime; // required
}
