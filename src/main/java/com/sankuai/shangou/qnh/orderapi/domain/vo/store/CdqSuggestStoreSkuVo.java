package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.thrift.caidaquan.dto.sku.CdqSuggestStoreSkuDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "门店商品明细"
)
@Data
@ApiModel("门店商品明细")
public class CdqSuggestStoreSkuVo {


    @FieldDoc(
            description = "租户id", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "租户id")
    private Long tenantId;
    @FieldDoc(
            description = "城市商品存在从城市带入，否则从总部商品带入", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "推荐商品名称")
    private String suggestSkuName;
    @FieldDoc(
            description = "租户商品名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "租户商品名称")
    private String tenantSkuName;
    @FieldDoc(
            description = "区域商品名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "区域商品名称")
    private String regionSkuName;
    @FieldDoc(
            description = "租户商品和区域商品名称是否相同", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "租户商品和区域商品名称是否相同")
    private Boolean isTenantSkuNameEqualRegionSkuName;
    @FieldDoc(
            description = "带入总部商品编码，并以此作为门店商品编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品sku")
    private String skuId;
    @FieldDoc(
            description = "带入总部商品UPC", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "UPC编码表")
    private List<String> upcList;
    @FieldDoc(
            description = "带入总部商品规格", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "规格")
    private String spec;
    @FieldDoc(
            description = "带入总部商品净重", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "重量")
    private Integer weight;
    @FieldDoc(
            description = "产地", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "产地")
    private String productionArea;
    @FieldDoc(
            description = "带入总部商品图片地址", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "图片列表")
    private List<String> imageUrls;
    @FieldDoc(
            description = "带入总部商品基础价格", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "基础价格")
    private Integer basicPrice;
    @FieldDoc(
            description = "带入总部商品规格", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "规格")
    private String baseUnit;
    @FieldDoc(
            description = "带入总部商品分类", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "分类")
    private String categoryCode;
    @FieldDoc(
            description = "带入总部商品分类名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "分类名称")
    private String categoryName;
    @FieldDoc(
            description = "带入总部商品品牌", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "品牌")
    private String brandCode;
    @FieldDoc(
            description = "带入总部商品品牌名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "品牌名称")
    private String brandName;
    @FieldDoc(
            description = "带入总部商品值，未获取到自动填写默认值1", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "最小购买数量")
    private Integer minOrderCount = 1;
    @FieldDoc(
            description = "带入总部商品值，未获取到自动填写默认值1", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "包装盒数量")
    private Integer boxNum = 1;
    @FieldDoc(
            description = "带入总部商品值，未获取到自动填写默认值0.00", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "包装盒价格")
    private Integer boxPrice;
    @FieldDoc(
            description = "门店商品是否已经存在，0：未知，1:存在，2：不存在", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店商品是否已经存在")
    private Integer isPoiSkuExisted;
    @FieldDoc(
            description = "总部商品编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "总部商品编码")
    private String tenantSkuId;
    @FieldDoc(
            description = "称重属性"
    )
    @ApiModelProperty(name = "称重属性")
    private Integer weightType;

    public CdqSuggestStoreSkuVo(CdqSuggestStoreSkuDTO cdqSuggestStoreSkuDTO) {
        this.tenantId = cdqSuggestStoreSkuDTO.getTenantId();
        this.suggestSkuName = cdqSuggestStoreSkuDTO.getSuggestSkuName();
        this.tenantSkuName = cdqSuggestStoreSkuDTO.getTenantSkuName();
        this.regionSkuName = cdqSuggestStoreSkuDTO.getRegionSkuName();
        this.isTenantSkuNameEqualRegionSkuName = cdqSuggestStoreSkuDTO.isIsTenantSkuNameEqualRegionSkuName();
        this.skuId = cdqSuggestStoreSkuDTO.getSkuId();
        this.upcList = cdqSuggestStoreSkuDTO.getUpcList();
        this.spec = cdqSuggestStoreSkuDTO.getSpec();
        this.weight = cdqSuggestStoreSkuDTO.getWeight();
        this.productionArea = cdqSuggestStoreSkuDTO.getProductionArea();
        this.imageUrls = cdqSuggestStoreSkuDTO.getImageUrls();
        this.basicPrice = cdqSuggestStoreSkuDTO.getBasicPrice();
        this.baseUnit = cdqSuggestStoreSkuDTO.getBaseUnit();
        this.categoryCode = cdqSuggestStoreSkuDTO.getCategoryCode();
        this.categoryName = cdqSuggestStoreSkuDTO.getCategoryName();
        this.brandCode = cdqSuggestStoreSkuDTO.getBrandCode();
        this.brandName = cdqSuggestStoreSkuDTO.getBrandName();
        this.minOrderCount = cdqSuggestStoreSkuDTO.getMinOrderCount();
        this.boxNum = cdqSuggestStoreSkuDTO.getBoxNum();
        this.boxPrice = cdqSuggestStoreSkuDTO.getBoxPrice();
        this.isPoiSkuExisted = cdqSuggestStoreSkuDTO.getIsPoiSkuExisted();
        this.tenantSkuId = cdqSuggestStoreSkuDTO.getTenantSkuId();
        this.weightType = cdqSuggestStoreSkuDTO.getWeightType();
    }





}
