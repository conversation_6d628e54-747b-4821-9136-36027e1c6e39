package com.sankuai.shangou.qnh.orderapi.domain.request.store;
// Copyright (C) 2019 Meituan
// All rights reserved

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

@TypeDoc(
        description = "评论回复接口"
)
@Data
public class CommentReplyReq {

    @FieldDoc(
            description = "评论Id"
    )
    private String commentId;

    @FieldDoc(
            description = "评论回复内容"
    )
    private String replyDraft;

    @FieldDoc(
            description = "订单溯源用户关联订单号"
    )
    private String associationChannelOrderId;
}
