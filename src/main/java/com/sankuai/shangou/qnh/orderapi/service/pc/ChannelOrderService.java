package com.sankuai.shangou.qnh.orderapi.service.pc;

import com.meituan.shangou.saas.o2o.dto.model.OCMSOrderAdjustLog;
import com.sankuai.meituan.shangou.saas.common.data.PageResult;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.*;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.*;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.PageResultV2;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.UiOption;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.ConfirmOrderVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.DhLateOrderVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.OrderAdjustRecordVO;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/2 14:25
 * @Description:
 */
public interface ChannelOrderService {

    /**
     * 查询订单调整记录
     * @param tenantId
     * @param channelId
     * @param orderId
     * @return
     */
    CommonDataBO<List<OCMSOrderAdjustLog>> queryOrderAdjustLog(Long tenantId, Integer channelId, String orderId);

    /**
     * 商家发起部分退款
     * @param request
     * @return
     */
    CommonResultBO tenantPartRefund(PartRefundRequest request);

    /**
     * 商家取消订单
     * @param request
     * @return
     */
    CommonResultBO tenantCancelOrder(RefundRequest request);

    /**
     * 补打小票
     * @return
     */
    CommonResultBO printReceipt(String viewOrderId, Integer channelId,String poiId);

    /**
     * 确认订单
     * @return
     */
    CommonResultBO confirmOrder(String orderId, int channelId);

    /**
     * 查询订单调整记录
     * @param tenantId
     * @param channelId
     * @param viewOrderId
     * @return
     */
    CommonDataBO<List<OrderAdjustRecordVO>> queryAdjustOrderRecord(Long tenantId, Integer channelId, String viewOrderId);

    /**
     * 一键拣货
     * @return
     */
    CommonResultBO completePickUp(QueryDetailRequest request);

    /**
     * 查询订单列表
     * @param channelOrderQueryBO
     * @return
     */
    PageResult<ChannelOrderBO> queryOrders(ChannelOrderQueryBO channelOrderQueryBO);

    /**
     * 查询订单详情
     *
     * @param orderId
     * @param tenantId
     * @param channelId
     * @param uid
     * @param containsMaterialSku
     * @return
     */
    ChannelOrderDetailBO queryDetail(String orderId, Long tenantId, int channelId, Long uid, Boolean containsMaterialSku);

    /**
     * 查询退款原因列表
     * @param request
     * @return
     */
    CommonDataBO<List<UiOption>> refundReasonList(QueryRefundReasonRequest request);

    /**
     *查询待确认订单列表-支持多门店
     */
    PageResultV2<ConfirmOrderVO> unDoneOrderList(MultiShopConfirmOrderQueryRequest multiShopConfirmOrderQueryRequest);

    /**
     * 查询待审核订单列表
     * @param auditOrderListQueryBO
     * @return
     */
    PageResult<AuditOrderBO> queryAuditOrders(AuditOrderListQueryBO auditOrderListQueryBO);

    /**
     *查询超时订单列表
     */
    PageResultV2<DhLateOrderVO> queryLateOrderVOList(QueryLateOrderRequest request);

    /**
     * 审核退款订单
     * @param approveRefundBO
     */
    void approveRefund(ApproveRefundBO approveRefundBO);

    /**
     *全部查询超时订单列表
     */
    List<DhLateOrderVO> queryAllLateOrderVOList(LateOrderDownloadRequest request);
}
