package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2019/6/12
 * desc:
 */
@TypeDoc(
        description = "前台分类查询请求"
)
@Data
@ApiModel("前台分类查询请求")
public class FindChannelFrontCategoryRequest {

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID")
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "渠道ID  -1-线下 100-美团 200-饿了么 300-京东", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID", required = true)
    @NotNull
    private Integer channelId;

    @FieldDoc(
            description = "父类目ID，查询一级类目传入0", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "父类目ID", required = true)
    @NotNull
    private Long parentCategoryId;
}
