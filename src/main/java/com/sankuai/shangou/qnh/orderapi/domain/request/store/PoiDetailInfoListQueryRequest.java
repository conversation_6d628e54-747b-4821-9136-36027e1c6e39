package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2019/12/13 18:57
 * @Description:
 */
@TypeDoc(
        description = "查询门店详情列表 请求对象"
)
@Data
@ApiModel("查询门店详情列表 请求对象")
public class PoiDetailInfoListQueryRequest {

    @FieldDoc(
            description = "城市ID"
    )
    @ApiModelProperty(name = "城市ID")
    private String cityId;

    @FieldDoc(
            description = "渠道列表"
    )
    @ApiModelProperty(name = "渠道列表")
    private List<String> channelList;

    @FieldDoc(
            description = "营业状态列表"
    )
    @ApiModelProperty(name = "营业状态列表")
    private List<Integer> statusList;

    @FieldDoc(
            description = "门店名称或编码"
    )
    @ApiModelProperty(name = "门店名称或编码")
    private String poiNameOrId;

    @FieldDoc(
            description = "页码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "页码", required = true)
    @NotNull
    @Min(1)
    private Integer page = 1;

    @FieldDoc(
            description = "页面大小", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "页面大小", required = true)
    @NotNull
    @Min(1)
    private Integer pageSize = 20;
}
