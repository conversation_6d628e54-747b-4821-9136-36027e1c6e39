package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "盘点单请求",
        authors = {
                "qianteng"
        }
)
@Data
public class StockCheckOrderBaseRes {

    @FieldDoc(
            description = "是否还有下一页"
    )
    public boolean hasMore;
    @FieldDoc(
            description = "单据列表"
    )
    public List<StockCheckOrderBaseInfo> stockCheckOrderList;

    @TypeDoc(
            description = "盘点单基本信息"
    )
    @Data
    public static class StockCheckOrderBaseInfo {
        @FieldDoc(
                description = "盘点单号"
        )
        private String stockCheckId;
        @FieldDoc(
                description = "盘点单状态：-1-无效；1-待提交；2-待审核；3-审核通过；4-驳回"
        )
        private Integer status;
        @FieldDoc(
                description = "单据创建时间"
        )
        private String createTime;
        @FieldDoc(
                description = "盘点商品种类"
        )
        private Integer skuCount;
    }

}
