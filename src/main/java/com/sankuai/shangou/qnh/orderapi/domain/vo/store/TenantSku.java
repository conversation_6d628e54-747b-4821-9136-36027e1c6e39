package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.ChannelCategoryWithPathVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/6/12
 * desc:
 */
@TypeDoc(
        description = "租户商品信息"
)
@Data
@ApiModel("租户商品信息")
public class TenantSku {

    @FieldDoc(
            description = "商品编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品编码", required = true)
    @NotNull
    private String skuId;

    @FieldDoc(
            description = "商品图片url列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品图片url列表", required = true)
    @NotNull
    private List<String> imageUrls;

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称", required = true)
    @NotNull
    private String skuName;

    @FieldDoc(
            description = "规格", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "规格")
    private String spec;

    @FieldDoc(
            description = "upc编码列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "upc编码列表")
    private List<String> upcList;

    @FieldDoc(
            description = "品牌末级code", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "upc编码")
    private String brandCode;

    @FieldDoc(
            description = "品牌名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "品牌名称")
    private String brandName;

    @FieldDoc(
            description = "品牌code全路径", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "品牌code全路径")
    private String brandCodePath;

    @FieldDoc(
            description = "品牌名称全路径", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "品牌名称全路径")
    private String brandNamePath;

    @FieldDoc(
            description = "后台分类末级code", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "后台分类末级code", required = true)
    @NotNull
    private String categoryCode;

    @FieldDoc(
            description = "后台分类末级名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "后台分类末级名称")
    private String categoryName;

    @FieldDoc(
            description = "后台分类code全路径", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "后台分类code全路径")
    private String categoryCodePath;

    @FieldDoc(
            description = "后台分类末级名称全路径", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "后台分类末级名称全路径")
    private String categoryNamePath;

    @FieldDoc(
            description = "基础售价   单位-分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "基础售价", required = true)
    @NotNull
    private Integer basicSalePrice;

    @FieldDoc(
            description = "单位", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "单位", required = true)
    @NotNull
    private String unit;

    @FieldDoc(
            description = "重量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "重量")
    private Integer weight;

    @FieldDoc(
            description = "称重类型 1-称重计量 2-称重计件 3-非称重/标品", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "称重类型", required = true)
    @NotNull
    private Integer weightType;

    @FieldDoc(
            description = "商品类型 1-商品 2-自动加工商品 3-组合商品 4-原料商品 5-耗材 6-加工商品 7-熟食餐饮", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品类型", required = true)
    @NotNull
    private Integer skuType;

    @FieldDoc(
            description = "门店添加状态 0-未知，1-门店未添加，2-门店已添加", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店添加状态")
    private Integer storeOnSale;

    @FieldDoc(
            description = "京东分类ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "京东分类ID")
    private String jdCategoryId;

    @FieldDoc(
            description = "京东分类名", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "京东分类名")
    private String jdCategoryName;

    @FieldDoc(
            description = "京东品牌ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "京东品牌ID")
    private String jdBrandId;

    @FieldDoc(
            description = "京东品牌名", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "京东品牌名")
    private String jdBrandName;

    @FieldDoc(
            description = "美团渠道类目信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "美团渠道类目信息")
    private ChannelCategoryWithPathVO mtChannelCategory;
}
