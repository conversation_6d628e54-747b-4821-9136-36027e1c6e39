package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

@TypeDoc(
        description = "坐标"
)
@Data
public class CoordinateVO {
    @FieldDoc(
            description = "经度", requiredness = Requiredness.REQUIRED
    )
    private String longitude;
    @FieldDoc(
            description = "纬度", requiredness = Requiredness.REQUIRED
    )
    private String latitude;

    @FieldDoc(
            description = "纬度", requiredness = Requiredness.OPTIONAL
    )
    private long timestamp;
}
