package com.sankuai.shangou.qnh.orderapi.converter.store;

import com.google.common.collect.Lists;
import com.meituan.shangou.saas.tenant.thrift.common.enums.YesNoEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.ChannelPoiInfoDTO;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.ChannelShippingTimeDTO;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.PoiDetailInfoDTO;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.ShippingTimeDTO;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.request.PoiInfoListQueryRequest;
import com.meituan.shangou.saas.tenant.thrift.exceptions.TenantBizException;
import com.sankuai.shangou.qnh.orderapi.enums.store.ChannelTypeEnum;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.BatchPoiShippingTimeSetRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.PoiDetailInfoListQueryRequest;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.ChannelPoiInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.PoiDetailInfoVO;
import com.sankuai.shangou.qnh.orderapi.utils.store.ConvertUtils;
import com.sankuai.shangou.qnh.orderapi.utils.store.ApiMethodParamThreadLocal;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.shangou.saas.common.mobile.MobileUtil;

import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * @Author: <EMAIL>
 * @Date: 2019/12/13 18:07
 * @Description: 门店信息转换
 */
public class StoreConverter {

    /**
     * 将 PoiDetailInfoDTO 转换为 PoiDetailInfoVO
     * @param poiInfoDTO
     * @return
     */
    public static PoiDetailInfoVO poiDetailInfoDTO2VO(PoiDetailInfoDTO poiInfoDTO) {
        if (poiInfoDTO == null) {
            return null;
        }
        PoiDetailInfoVO poiInfoVO = new PoiDetailInfoVO();
        poiInfoVO.setPoiId(ConvertUtils.nonNullConvert(poiInfoDTO.getPoiId(), String::valueOf));
        poiInfoVO.setPoiName(poiInfoDTO.getPoiName());
        poiInfoVO.setAddress(poiInfoDTO.getPoiAddress());
        poiInfoVO.setCityName(poiInfoDTO.getDistrict().getCityName());
        poiInfoVO.setAreaName(poiInfoDTO.getDistrict().getAreaName());
        poiInfoVO.setContact(poiInfoDTO.getContactName());
        poiInfoVO.setMobile(poiInfoDTO.getPoiPhoneNum());
        poiInfoVO.setChannelPois(ConvertUtils.convertList(poiInfoDTO.getChannelPoiInfoList(), StoreConverter::channelPoiInfoDTO2VO));
        poiInfoVO.setLon(poiInfoDTO.getLongitude());
        poiInfoVO.setLat(poiInfoDTO.getLatitude());
        return poiInfoVO;
    }

    /**
     * 将 PoiDetailInfoDTO 转换为 PoiDetailInfoVO
     * @param poiInfoDTO
     * @return
     */
    public static PoiDetailInfoVO poiDetailInfoDTO2VOMark(PoiDetailInfoDTO poiInfoDTO) {
        if (poiInfoDTO == null) {
            return null;
        }
        PoiDetailInfoVO poiInfoVO = new PoiDetailInfoVO();
        poiInfoVO.setPoiId(ConvertUtils.nonNullConvert(poiInfoDTO.getPoiId(), String::valueOf));
        poiInfoVO.setPoiName(poiInfoDTO.getPoiName());
        poiInfoVO.setAddress(poiInfoDTO.getPoiAddress());
        poiInfoVO.setCityName(poiInfoDTO.getDistrict().getCityName());
        poiInfoVO.setAreaName(poiInfoDTO.getDistrict().getAreaName());
        poiInfoVO.setContact(poiInfoDTO.getContactName());
        poiInfoVO.setMobile(MobileUtil.mask(poiInfoDTO.getPoiPhoneNum()));
        poiInfoVO.setChannelPois(ConvertUtils.convertList(poiInfoDTO.getChannelPoiInfoList(), StoreConverter::channelPoiInfoDTO2VO));

        return poiInfoVO;
    }


    /**
     * 将 ChannelPoiInfoDTO 转换为 ChannelPoiInfoVO
     * @param channelPoiInfoDTO
     * @return
     */
    public static ChannelPoiInfoVO channelPoiInfoDTO2VO(ChannelPoiInfoDTO channelPoiInfoDTO) {
        ChannelPoiInfoVO channelPoiInfoVO = new ChannelPoiInfoVO();
        channelPoiInfoVO.setChannelId(ConvertUtils.nonNullConvert(channelPoiInfoDTO.getChannelId(), String::valueOf));
        channelPoiInfoVO.setChannelName(ConvertUtils.nonNullConvert(channelPoiInfoDTO.getChannelId(), id -> ChannelTypeEnum.findChannelNameByChannelId(id)));
        channelPoiInfoVO.setStatus(channelPoiInfoDTO.getBizStatus());
        channelPoiInfoVO.setPromotionInfo(channelPoiInfoDTO.getAnnouncement());
        channelPoiInfoVO.setShippingTime(channelPoiInfoDTO.getShippingTime());
        channelPoiInfoVO.setShippingDay(channelPoiInfoDTO.getDays());
        channelPoiInfoVO.setPreBook(Integer.valueOf(YesNoEnum.YES.getKey()).equals(channelPoiInfoDTO.getPreBook()) ? true : false);
        channelPoiInfoVO.setPrebookMinDays(channelPoiInfoDTO.getPrebookMinDays());
        channelPoiInfoVO.setPrebookMaxDays(channelPoiInfoDTO.getPrebookMaxDays());

        return channelPoiInfoVO;
    }


    /**
     * 将门店列表查询的请求转换为调用下游接口的thrift请求
     * @param request
     * @return
     */
    public static PoiInfoListQueryRequest poiListQuery2ThriftService(PoiDetailInfoListQueryRequest request) {
        PoiInfoListQueryRequest thriftRequest = new PoiInfoListQueryRequest();

        if (request == null) {
            throw new BizException("请求为空");
        }

        thriftRequest.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        thriftRequest.setCityId(ConvertUtils.nonNullConvert(request.getCityId(), Integer::parseInt));
        thriftRequest.setChannelIdList(ConvertUtils.convertList(request.getChannelList(), Integer::parseInt));
        thriftRequest.setPoiBizStatusList(request.getStatusList());
        thriftRequest.setPoiNameOrId(request.getPoiNameOrId());
        return thriftRequest;
    }


    public static ChannelShippingTimeDTO shippingTime2DTO(BatchPoiShippingTimeSetRequest.ChannelShippingTime channelShippingTime) {
        ChannelShippingTimeDTO channelShippingTimeDTO = new ChannelShippingTimeDTO();
        channelShippingTimeDTO.setChannelId(Integer.valueOf(channelShippingTime.getChannelId()));
        if (Objects.nonNull(channelShippingTime.getDays())) {
            channelShippingTimeDTO.setDays(channelShippingTime.getDays());
        } else {
            channelShippingTimeDTO.setDays("");
        }

        List<ShippingTimeDTO> shippingTimeDTOList = Lists.newArrayList();
        try {
            if (CollectionUtils.isEmpty(channelShippingTime.getTimes())) {
                ShippingTimeDTO shippingTimeDTO = new ShippingTimeDTO();
                shippingTimeDTO.setStartTime(channelShippingTime.getStartTime());
                shippingTimeDTO.setEndTime(channelShippingTime.getEndTime());
                shippingTimeDTOList.add(shippingTimeDTO);
            } else {
                shippingTimeDTOList = ConvertUtils.convertList(channelShippingTime.getTimes(), timeString -> {
                    String[] t = timeString.split("-");
                    ShippingTimeDTO shippingTimeDTO = new ShippingTimeDTO();
                    shippingTimeDTO.setStartTime(t[0]);
                    shippingTimeDTO.setEndTime(t[1]);
                    return shippingTimeDTO;
                });
            }
        } catch (Exception e) {
            throw new TenantBizException(1, "营业时间设置，参数转换异常");
        }

        channelShippingTimeDTO.setShippingTimeList(shippingTimeDTOList);
        return channelShippingTimeDTO;
    }

}
