package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2020/3/16
 * desc: 批量导入门店配送配置请求
 */
@Setter
@Getter
public class ImportShopDeliveryConfigListRequest implements BaseRequest {

    @FieldDoc(
            description = "配送配置文件URL"
    )
    @ApiModelProperty(value = "配送配置文件URL", required = true)
    private String configFileUrl;

    @Override
    public void selfCheck() {
        AssertUtil.notEmpty(configFileUrl, "配置文件URL不能为空" , "configFileUrl");
    }
}
