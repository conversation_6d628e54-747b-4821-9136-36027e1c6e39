package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.bo.ReturnDataPoiBo;
import com.sankuai.shangou.qnh.orderapi.domain.result.ReturnDataSecurity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Objects;

/**
 * @Author: <EMAIL>
 * @Date: 2022/12/8 16:13
 * @Description:
 */
@TypeDoc(
        description = "融合订单详情"
)
@ApiModel("融合订单详情")
@Setter
@Getter
@NoArgsConstructor
@ToString
public class AfsOrderFuseDetailVO implements ReturnDataSecurity {

    @FieldDoc(
            description = "基本信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "基本信息", required = true)
    private AfsBaseInfoVO baseInfo;

    @FieldDoc(
            description = "财务信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "财务信息", required = true)
    private BillInfoVO billInfo;

    @FieldDoc(
            description = "商品列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "商品列表", required = true)
    private List<AfsItemInfoVO> itemInfo;

    @Override
    public List<ReturnDataPoiBo> fetchReturnDataPoiBoList() {
        if (Objects.nonNull(this.baseInfo)) {
            ReturnDataPoiBo poiBo = ReturnDataPoiBo.builder().poiId(this.baseInfo.getPoiId())
                    .dispatchShopId(this.baseInfo.getDispatchShopId()).build();
            return Lists.newArrayList(poiBo);
        }
        return null;
    }

}
