package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/7/3  17:33
 * @since 1.0.0
 */
@TypeDoc(
        description = "任务分派信息"
)
@ApiModel("任务分派信息")
@Getter
@Setter
@ToString
@NoArgsConstructor
public class TransferOrderRequest implements BaseRequest {

    /**
     * 渠道订单号
     */
    @FieldDoc(
            description = "渠道订单号"
    )
    private String channelOrderId;

    /**
     * 渠道id
     */
    @FieldDoc(
            description = "渠道id"
    )
    private Integer channelId;

    /**
     * 转派人id
     */
    @FieldDoc(
            description = "转派人id"
    )
    private Long accountId;

    /**
     * 转派人名称
     */
    @FieldDoc(
            description = "转派人名称"
    )
    private String accountName;

    @FieldDoc(
            description = "类型，0：下发拣货任务，1：分配拣货员，2：更换拣货员"
    )
    private Integer type = 0;

    @Override
    public void selfCheck() {
        AssertUtil.isTrue(StringUtils.isNotEmpty(channelOrderId) , "渠道订单号不可为空");

        AssertUtil.isTrue(channelId != null && channelId > 0 , "渠道id不可为空要大于0");

        AssertUtil.isTrue(accountId != null , "转派人id不可为空");

        AssertUtil.isTrue(StringUtils.isEmpty(accountName) || accountName.length() <= 25 , "转派人名称最多输入25位");

    }

}
