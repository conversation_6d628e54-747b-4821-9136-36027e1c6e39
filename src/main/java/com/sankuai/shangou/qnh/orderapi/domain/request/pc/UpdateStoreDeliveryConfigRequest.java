package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.DeliveryPlatformInfoVo;
import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description: 修改门店/仓库的聚合配送设置请求体
 * @Author: yuanyu09
 * @Date: 2022/12/9
 */
@TypeDoc(
        description = "修改门店/仓库的聚合配送设置请求体"
)
@ApiModel("修改门店/仓库的聚合配送设置请求体")
@Data
public class UpdateStoreDeliveryConfigRequest implements BaseRequest {
    @FieldDoc(
            description = "门店ID"
    )
    @ApiModelProperty(value = "门店ID", required = true)
    private Long storeId;

    @FieldDoc(
            description = "租户ID"
    )
    @ApiModelProperty(value = "租户ID", required = true)
    private Long tenantId;

    @FieldDoc(
            description = "配送平台配置"
    )
    @ApiModelProperty(value = "配送平台配置")
    private List<DeliveryPlatformInfoVo> deliveryPlatformConfig;

    @Override
    public void selfCheck() {
        AssertUtil.notNull(storeId, "门店ID不能为空", "storeId");
        AssertUtil.notNull(tenantId, "租户ID不能为空", "tenantId");
        AssertUtil.notNull(deliveryPlatformConfig, "配送平台配置不能为空", "deliveryPlatformConfig");
    }
}
