package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2021-10-12 10:31
 * @Description:
 */
@Setter
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(
        description = "超时订单导出下载请求"
)
public class LateOrderDownloadRequest implements BaseRequest {
    @FieldDoc(
            description = "订单号"
    )
    @ApiModelProperty(value = "订单号", required = false)
    private String orderId;

    @FieldDoc(
            description = "渠道编码"
    )
    @ApiModelProperty(value = "渠道编码", required = false)
    private Integer channelId;


    @FieldDoc(
            description = "门店id列表"
    )
    @ApiModelProperty(value = "门店id列表", required = false)
    private List<Long> storeIds;

    @FieldDoc(
            description = "订单类型：0实时单，1预约单"
    )
    @ApiModelProperty(value = "订单类型", required = false)
    private Integer orderType;

    /**
     * 订单创建开始时间
     */
    @FieldDoc(
            description = "订单创建开始时间"
    )
    @ApiModelProperty(value = "订单创建开始时间", required = false)
    private String createStartTime;

    /**
     * 订单创建结束时间
     */
    @FieldDoc(
            description = "订单创建结束时间"
    )
    @ApiModelProperty(value = "订单创建结束时间", required = false)
    private String createEndTime;

    /**
     * 预计送达开始时间
     */
    @FieldDoc(
            description = "预计送达开始时间"
    )
    @ApiModelProperty(value = "预计送达开始时间", required = false)
    private String estimatedArrivalStartTime;

    /**
     * 预计送达结束时间
     */
    @FieldDoc(
            description = "预计送达结束时间"
    )
    @ApiModelProperty(value = "预计送达结束时间", required = false)
    private String estimatedArrivalEndTime;


    /**
     * 实际送达开始时间
     */
    @FieldDoc(
            description = "实际送达开始时间"
    )
    @ApiModelProperty(value = "实际送达开始时间", required = false)
    private String actualArrivalStartTime;

    /**
     * 实际送达结束时间
     */
    @FieldDoc(
            description = "实际送达结束时间"
    )
    @ApiModelProperty(value = "实际送达结束时间", required = false)
    private String actualArrivalEndTime;


    @FieldDoc(
            description = "是否超时发券"
    )
    @ApiModelProperty(value = "是否超时发券", required = false)
    private Boolean hasCompensated;
}
