package com.sankuai.shangou.qnh.orderapi.constant.pc;

/**
 * @description: 门店配置的基础配置常量
 * @author: caolingjiang
 * @date: 2023/4/10 14:21
 * Copyright (C) 2023 MTDP
 * All rights reserved
 */
public class StoreFulfillMccConfigConstant {

    /**
     * 最大获取mcc配置的key数量
     */
    public static final String MAX_MCC_KEYS_COUNT = "max_mcc_keys_count";

    /**
     * 批量更新门店配置时最大数量
     */
    public static final String MAX_BATCH_UPDATE_STORE_COUNT = "max_batch_update_store_count";

    /**
     * 批量更新门店打印配置时宣传语最大长度
     */
    public static final String MAX_BATCH_UPDATE_AFTER_SALE_TEXT_LEN = "max_batch_update_after_sale_text_len";

    /**
     * 批量更新门店打印配置时二维码链接最大长度
     */
    public static final String MAX_BATCH_UPDATE_AFTER_SALE_QR_LEN = "max_batch_update_after_sale_qr_len";

}
