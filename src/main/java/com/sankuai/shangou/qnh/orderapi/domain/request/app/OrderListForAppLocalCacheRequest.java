package com.sankuai.shangou.qnh.orderapi.domain.request.app;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * 全部订单列表分页查询请求.
 *
 * <AUTHOR>
 * @since 2021/7/2 11:16
 */
@TypeDoc(
        description = "全部订单列表分页查询请求"
)
@ApiModel("全部订单列表分页查询请求")
@Data
public class OrderListForAppLocalCacheRequest {

    @FieldDoc(
            description = "门店 ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店 ID")
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "下单截止时间，可以为空或0", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "下单截止时间")
    private Long orderTimeEnd;


    @FieldDoc(
            description = "每页行数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "每页行数", required = true)
    @NotNull
    private Integer pageSize;

    @FieldDoc(
            description = "页码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "页码", required = true)
    private Integer pageNo;

    @FieldDoc(
            description = "是否查询售后单", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "是否查询售后单", required = true)
    private boolean queryAfterSaleOrder;


    @FieldDoc(
            description = "门店类型 3-门店 5-中心仓 6-前置仓", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店类型")
    private Integer entityType;



    public String validate() {
        if (storeId == null) {
            return "门店ID和前置仓ID不能都为空";
        }
        // 判断时间范围有效性
        try {


        } catch (Exception e) {
            return "时间范围错误，请联系管理员";
        }


        return null;
    }
}
