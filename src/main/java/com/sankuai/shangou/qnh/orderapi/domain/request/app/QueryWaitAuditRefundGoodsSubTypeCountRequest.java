package com.sankuai.shangou.qnh.orderapi.domain.request.app;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 根据子类型统计售后订单数量请求
 * @author: caimengluan
 * @date: 2022/8/15
 * @time: 14:21
 * Copyright (C) 2019 Meituan
 * All rights reserved
 */
@TypeDoc(
        description = "根据子类型统计售后订单数量"
)
@ApiModel("根据子类型统计售后订单数量")
@Data
public class QueryWaitAuditRefundGoodsSubTypeCountRequest {
    @FieldDoc(
            description = "门店类型 3-门店 5-中心仓 6-前置仓", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店类型")
    private Integer entityType;
}

