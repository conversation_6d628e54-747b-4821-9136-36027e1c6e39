package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.CommentStatByConditionVO;
import com.sankuai.sgfulfillment.comment.thrift.dto.model.ChannelCommentLabelStatDTO;
import com.sankuai.sgfulfillment.comment.thrift.dto.model.ChannelCommentStatDTO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.CommentStatVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@TypeDoc(
        description = "查询评价状态返回",
        authors = "gonglei"
)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CommentStatResp {

    @FieldDoc(
            description = "评价回复模板列表", requiredness = Requiredness.OPTIONAL
    )
    private Map<String, CommentStatVO> commentStatMap;

    @FieldDoc(
            description = "评价统计列表", requiredness = Requiredness.OPTIONAL
    )
    private List<CommentStatByConditionVO> commentStatList;

    @FieldDoc(
            description = "是否为联系灰度门店", requiredness = Requiredness.OPTIONAL
    )
    private Boolean contactGrayStore;

    private static final Map<String, String> tag2NotRepliedTag = new HashMap<String, String>() {
        {
            put("BAD_COMMENT_COUNT", "NOT_REPLY_BAD_COMMENT_COUNT");
            put("GOOD_COMMENT_COUNT", "NOT_REPLY_GOOD_COMMENT_COUNT");
            put("COMMON_COMMENT_COUNT", "NOT_REPLY_COMMON_COMMENT_COUNT");
        }
    };

    public CommentStatResp(ChannelCommentStatDTO commentStatDTO, boolean isContactGrayStore) {
        if (commentStatDTO == null || CollectionUtils.isEmpty(commentStatDTO.getCommentLabelStatDTOList())) {
            return;
        }
        List<ChannelCommentLabelStatDTO> commentLabelStatDTOList = commentStatDTO.getCommentLabelStatDTOList();
        Map<String, ChannelCommentLabelStatDTO> commentLabelStatDTOMap = commentLabelStatDTOList.stream()
                .collect(Collectors.toMap(ChannelCommentLabelStatDTO::getCommentLabel, stat -> stat));
        this.commentStatMap = new HashMap<>();
        for (Map.Entry<String, String> statTag : tag2NotRepliedTag.entrySet()) {
            this.commentStatMap.put(statTag.getKey(),
                    new CommentStatVO(commentLabelStatDTOMap.get(statTag.getKey()).getCommentLabelCount(),
                            commentLabelStatDTOMap.get(statTag.getValue()).getCommentLabelCount()));
        }
        this.contactGrayStore = isContactGrayStore;
    }
}
