package com.sankuai.shangou.qnh.orderapi.domain.response.app;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/7/11
 * desc: 打印小票响应
 */
@TypeDoc(
        description = "打印小票响应"
)
@ApiModel("打印小票响应")
@Data
public class PrintReceiptResponse {

    @FieldDoc(
            description = "打印ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "打印ID", required = true)
    private String printId;
}