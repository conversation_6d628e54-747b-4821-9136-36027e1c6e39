package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2019/6/12
 * desc:
 */
@TypeDoc(
        description = "查询租户商品请求"
)
@Data
@ApiModel("分页查询租户商品列表请求")
public class QueryTenantSkuRequest {

    @FieldDoc(
            description = "商品编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品编码", required = true)
    @NotNull
    private String skuId;
}
