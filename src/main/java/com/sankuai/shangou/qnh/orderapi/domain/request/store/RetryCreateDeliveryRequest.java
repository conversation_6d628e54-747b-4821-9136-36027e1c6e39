package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2020/1/20
 * desc: 重发配送请求
 */
@TypeDoc(
        description = "重发配送请求"
)
@Data
@ApiModel("重发配送请求")
public class RetryCreateDeliveryRequest {

    @FieldDoc(
            description = "闪购赋能内部订单号",
            requiredness = Requiredness.REQUIRED
    )
    @NotNull(message = "闪购赋能内部订单号")
    @Min(value = 1, message = "闪购赋能内部订单号不合法")
    private Long orderId;
}
