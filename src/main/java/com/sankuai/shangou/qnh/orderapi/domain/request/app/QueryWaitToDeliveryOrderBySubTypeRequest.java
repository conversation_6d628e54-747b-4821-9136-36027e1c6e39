package com.sankuai.shangou.qnh.orderapi.domain.request.app;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2022/1/10
 * @description
 */
@TypeDoc(
        description = "根据子类型查询待配送订单列表"
)
@ApiModel("根据子类型查询待配送订单列表")
@Data
public class QueryWaitToDeliveryOrderBySubTypeRequest {

    @FieldDoc(
            description = "第几页", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "第几页", required = true)
    @NotNull
    private Integer page;

    @FieldDoc(
            description = "每页行数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "每页行数", required = true)
    @NotNull
    private Integer size;

    @FieldDoc(
            description = "子类型", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "子类型", required = true)
    @NotNull
    private Integer subType;

    @FieldDoc(
            description = "门店类型 3-门店 5-中心仓 6-前置仓", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店类型")
    private Integer entityType;

    @FieldDoc(
            description = "配送方式对应的配送模式 1-平台配送 2-聚合配送 3-商家自送", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "配送方式对应的配送模式 1-平台配送 2-聚合配送 3-商家自送")
    private List<Integer> deliveryTypeModeList;
}