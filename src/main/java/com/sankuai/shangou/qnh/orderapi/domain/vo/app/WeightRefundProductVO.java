package com.sankuai.shangou.qnh.orderapi.domain.vo.app;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: <EMAIL>
 * @Date: 2020-03-25 14:40
 * @Description:
 */
@TypeDoc(
        description = "克重退款商品"
)
@ApiModel("克重退款商品")
@Data
public class WeightRefundProductVO {
    @FieldDoc(
            description = "SKU编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "SKU编码")
    private String skuId;

    @FieldDoc(
            description = "线上渠道sku编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "线上渠道sku编码")
    private String customSkuId;

    @FieldDoc(
            description = "线上渠道spu编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "线上渠道spu编码")
    private String customerSpuId;

    @FieldDoc(
            description = "SKU名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "SKU名称")
    @NotNull
    private String skuName;

    @FieldDoc(
            description = "申请退款重量,单位：克", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "申请退款重量，单位：克")
    @NotNull
    private Double applyWeight;

    @FieldDoc(
            description = "渠道重量,单位：克", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道重量，单位：克")
    @NotNull
    private Double channelWeight;

    @FieldDoc(
            description = "商品itemId", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品itemId")
    private String itemId;
}

