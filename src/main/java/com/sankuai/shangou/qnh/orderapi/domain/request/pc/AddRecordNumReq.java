package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/11/25
 * @email jianglilin02@meituan
 */
@TypeDoc(
        description = "添加听音次数请求"
)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AddRecordNumReq {

    @FieldDoc(
            description = "记录id", requiredness = Requiredness.REQUIRED
    )
    private String recordId;

    public void valid() {
        AssertUtil.notBlank(this.getRecordId(), "记录Id不能为空");
    }
}
