package com.sankuai.shangou.qnh.orderapi.domain.response.app;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/8/30 15:36
 **/
@Data
public class ThirdWaitToDeliverySubTypeCountResponse {
    @FieldDoc(
            description = "全部数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "全部数量", required = true)
    private Integer allSubTypeCount;

    @FieldDoc(
            description = "待发配送数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "待发单数量", required = true)
    private Integer waitToLaunchDeliveryCount;

    @FieldDoc(
            description = "待接单数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "待接单数量", required = true)
    private Integer waitToRiderAcceptCount;
    @FieldDoc(
            description = "待取货数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "待取货数量", required = true)
    private Integer waitToTakeGoodsCount;

    @FieldDoc(
            description = "配送中数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "配送中数量", required = true)
    private Integer deliveringCount;

    @FieldDoc(
            description = "配送异常数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "配送异常数量", required = true)
    private Integer exceptionCount;
}
