package com.sankuai.shangou.qnh.orderapi.filter.pc;

import com.sankuai.shangou.qnh.orderapi.constant.pc.CommonConstant;
import com.sankuai.shangou.qnh.orderapi.context.pc.GrayscaleContextHolder;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * 请求结束时释放资源, 销毁上线文数据
 *
 * <AUTHOR>
 *
 */
@Slf4j
public class GrayContextFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        try {
            writeContext(request);
            chain.doFilter(request, response);
        } finally {
            GrayscaleContextHolder.release();
        }
    }

    private void writeContext(ServletRequest request) {
        if (request instanceof HttpServletRequest) {
            try {
                HttpServletRequest httpServletRequest = (HttpServletRequest) request;
                String ocmsOrderMigrate = httpServletRequest.getHeader(CommonConstant.HEAD_OCMS_MIGRATE_TEST);
                GrayscaleContextHolder.setOcmsOrderMigrateGray(ocmsOrderMigrate);
            } catch (Exception e) {
                log.error("解析header失败", e);
            }
        }

    }

    @Override
    public void init(FilterConfig arg0) {

    }

    @Override
    public void destroy() {

    }

}
