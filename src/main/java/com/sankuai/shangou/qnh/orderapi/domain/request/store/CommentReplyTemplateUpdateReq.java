package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import javax.validation.constraints.NotNull;

@TypeDoc(
        description = "添加评价回复模板请求参数"
)
@Data
public class CommentReplyTemplateUpdateReq {

    @FieldDoc(
            description = "模板id", requiredness = Requiredness.REQUIRED
    )
    @NotNull
    private Long templateId;

    @FieldDoc(
            description = "模板内容", requiredness = Requiredness.REQUIRED
    )
    @NotNull
    private String templateContent;
}
