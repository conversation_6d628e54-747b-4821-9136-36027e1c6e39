package com.sankuai.shangou.qnh.orderapi.domain.response.pc;

import java.util.List;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.PoiGroupVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * @Author: wang<PERSON>an
 * @Date: 2022/07/27
 * @Description:
 */
@TypeDoc(
    description = "门店分组列表"
)
@ApiModel("门店分组列表")
@Getter
@Setter
@ToString
@NoArgsConstructor
public class PoiGroupListResp {

  @FieldDoc(
      description = "最大分组数"
  )
  @ApiModelProperty(value = "最大分组数", required = true)
  private Integer maxPoiGroupSize;

  @FieldDoc(
      description = "门店分组列表"
  )
  @ApiModelProperty(value = "门店分组列表", required = true)
  private List<PoiGroupVo> poiGroupList;
}