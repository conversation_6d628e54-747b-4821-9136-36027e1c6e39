package com.sankuai.shangou.qnh.orderapi.remote;

import com.meituan.shangou.saas.tenant.thrift.ChannelManageThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.channel.request.ChannelBatchRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.channel.response.ChannelDetailListResponse;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.ChannelInfoBo;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ResponseHandler;
import com.sankuai.shangou.qnh.orderapi.utils.pc.RpcInvoker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class ChannelManageRemoteService {

    @Autowired
    private ChannelManageThriftService channelManageThriftService;

    public List<ChannelInfoBo> queryChannelDetails(List<Integer> channelIds) {
        return queryChannelDetailWithConfig(channelIds, Boolean.FALSE);
    }

    public List<ChannelInfoBo> queryChannelDetailWithConfig(List<Integer> channelIds, Boolean includeConfig) {
        if (CollectionUtils.isEmpty(channelIds)) {
            return Collections.emptyList();
        }
        ChannelBatchRequest request = new ChannelBatchRequest();
        request.setChannelIds(channelIds);
        request.setIncludeConfig(includeConfig);
        // 走缓存
        ChannelDetailListResponse response = RpcInvoker.invoke(() -> channelManageThriftService.batchQueryChannelDetails(request));

        ResponseHandler.checkResponseAndStatus(response, r -> r.getStatus().getCode(), r -> r.getStatus().getMessage());
        return ConverterUtils.convertList(response.getChannelList(), ChannelInfoBo::fromChannelDetailDto);
    }

    /**
     * 查询渠道名
     */
    public String queryChannelNameByChannelId(Integer channelId) {
        List<ChannelInfoBo> channelInfoBoList = queryChannelDetails(Collections.singletonList(channelId));
        if (CollectionUtils.isNotEmpty(channelInfoBoList)) {
            return channelInfoBoList.stream()
                    .filter(bo -> Objects.equals(bo.getChannelId(), channelId))
                    .findAny()
                    .map(ChannelInfoBo::getChannelName)
                    .orElse(null);
        }
        return null;
    }

}
