package com.sankuai.shangou.qnh.orderapi.controller.app;

import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.shangou.qnh.orderapi.annotation.app.Auth;
import com.sankuai.shangou.qnh.orderapi.context.app.ApiMethodParamThreadLocal;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.*;
import com.sankuai.shangou.qnh.orderapi.domain.request.pda.AggDeliveryUrlRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pda.QueryUnfinishedOrderRequest;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.pda.AggDeliveryUrlResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.pda.PdaOrderHomePageResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pda.CommonOrderListResponse;
import com.sankuai.shangou.qnh.orderapi.enums.app.ResultCodeEnum;
import com.sankuai.shangou.qnh.orderapi.service.app.OrderService;
import com.sankuai.shangou.qnh.orderapi.service.app.PdaOrderService;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2019/11/18
 * desc:
 */
@InterfaceDoc(
        displayName = "订单服务",
        type = "restful",
        scenarios = "包含查询各个状态的订单列表",
        description = "包含查询各个状态的订单列表",
        host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@Api(value = "订单服务")
@RestController
@RequestMapping("/pieapi/pda/order")
public class PdaController {

    @Resource
    private PdaOrderService pdaOrderService;
    @Resource
    private OrderService orderService;

    @MethodDoc(
            displayName = "分页查询未完结订单列表",
            description = "分页查询未完结订单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询未完结订单列表",
                            type = QueryUnfinishedOrderRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/pda/order/unfinished",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "分页查询进行中订单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/unfinished", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<CommonOrderListResponse> queryUnfinishedOrder(@Valid @RequestBody QueryUnfinishedOrderRequest request) {
        return pdaOrderService.queryUnfinishedOrder(ApiMethodParamThreadLocal.getIdentityInfo(), request);
    }



    @MethodDoc(
            displayName = "分页查询全部订单列表",
            description = "分页查询全部订单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "全部订单列表分页查询请求",
                            type = OrderListRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/orderList",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "分页查询全部订单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @RequestMapping(value = "/orderList", method = {RequestMethod.POST, RequestMethod.GET})
    public CommonResponse<CommonOrderListResponse> orderList(@Valid @RequestBody OrderListRequest request) {
        // 进行参数校验
        String validateResult = request.validate();
        if (validateResult != null) {
            return CommonResponse.fail(ResultCodeEnum.FAIL.getCode(), validateResult);
        }

        return pdaOrderService.orderList(request, ApiMethodParamThreadLocal.getIdentityInfo());
    }


    @MethodDoc(
            displayName = "分页查询全部订单列表",
            description = "分页查询全部订单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "全部订单列表分页查询请求",
                            type = OrderListRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/orderList",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "分页查询全部订单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @RequestMapping(value = "/search", method = {RequestMethod.POST, RequestMethod.GET})
    public CommonResponse<CommonOrderListResponse> orderSearch(@Valid @RequestBody OrderSearchRequest request) {
        return pdaOrderService.orderList(request.build(), ApiMethodParamThreadLocal.getIdentityInfo());
    }


    @MethodDoc(
            displayName = "分页查询全部订单列表",
            description = "分页查询全部订单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "全部订单列表分页查询请求",
                            type = OrderListRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/orderList",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "分页查询全部订单列表,app本地缓存专用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @RequestMapping(value = "/orderList/appcache", method = {RequestMethod.POST, RequestMethod.GET})
    public CommonResponse<CommonOrderListResponse> orderListForAppLocalCache(@Valid @RequestBody OrderListForAppLocalCacheRequest request) {
        // 进行参数校验
        String validateResult = request.validate();
        if (validateResult != null) {
            return CommonResponse.fail(ResultCodeEnum.FAIL.getCode(), validateResult);
        }
        if (Objects.isNull(request.getPageNo())) {
            request.setPageNo(1);
        }
        return pdaOrderService.orderListForAppLocalCache(request, ApiMethodParamThreadLocal.getIdentityInfo());
    }

    @Auth
    @ApiOperation(value = "分页查询全部订单列表,app本地缓存专用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @RequestMapping(value = "/changed/orderList/appcache", method = {RequestMethod.POST, RequestMethod.GET})
    public CommonResponse<CommonOrderListResponse> changedOrderListForAppLocalCache(@Valid @RequestBody ChangedOrderListForAppLocalCacheRequest request) {
        // 进行参数校验
        String validateResult = request.validate();
        if (Objects.isNull(request.getPageNo())) {
            request.setPageNo(1);
        }
        if (validateResult != null) {
            return CommonResponse.fail(ResultCodeEnum.FAIL.getCode(), validateResult);
        }
        return pdaOrderService.changedOrderListForAppLocalCache(request, ApiMethodParamThreadLocal.getIdentityInfo());
    }




    @MethodDoc(
            displayName = "订单拣货完成",
            description = "订单拣货完成",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "订单拣货完成请求",
                            type = OrderPickCompleteRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/order/pickcomplete",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "订单拣货完成")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/pickcomplete", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse pickComplete(@Valid @RequestBody OrderPickCompleteRequest request) {
        if (!MccConfigUtil.hitPickCompleteWithNewApiGrayTenant(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId())){
            return orderService.pickComplete(request);
        }
        return pdaOrderService.pickComplete(request);
    }

    @MethodDoc(
            displayName = "订单tab页首页查询接口",
            description = "订单tab页首页查询接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "订单tab页首页查询接口请求",
                            type = HttpServletRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/pda/order/homepage",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "订单tab页首页查询接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/homepage", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<PdaOrderHomePageResponse> homePage(@RequestBody HomePageRequest request) {
        CommonResponse<PdaOrderHomePageResponse> homePageResponse  = pdaOrderService.homePage(request);
        return homePageResponse;
    }


    @MethodDoc(
            displayName = "聚合配送url",
            description = "聚合配送url",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "订单tab页首页查询接口请求",
                            type = HttpServletRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/pieapi/pda/order/delivery/url",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "聚合配送url")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/delivery/url", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonResponse<AggDeliveryUrlResponse> homePage(@RequestBody AggDeliveryUrlRequest request) {
        if (MccConfigUtil.dapLinkNoAuthPdaSwitch()) {
            CommonResponse<AggDeliveryUrlResponse> homePageResponse  = pdaOrderService.deliveryUrl(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId(), request);
            return homePageResponse;
        }
        return pdaOrderService.deliveryAuthUrl(request);
    }



}