package com.sankuai.shangou.qnh.orderapi.domain.response.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description 订单子状态数量响应
 * @ClassName OrderSubStatusCountResponse
 * <AUTHOR>
 * @Version 1.0
 * @Date 2022/12/17 8:07 下午
 */
@TypeDoc(
        description = "订单子状态数量响应"
)
@Data
@ApiModel("订单子状态数量响应")
public class OrderSubStatusCountResponse {

    @FieldDoc(
            description = "全部数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "全部数量", required = true)
    private Integer allSubStatuseCount;

    @FieldDoc(
            description = "待接单数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "待接单数量", required = true)
    private Integer waitToTakeOrderCount;

    @FieldDoc(
            description = "待拣货数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "待拣货数量", required = true)
    private Integer waitToPickCount;

    @FieldDoc(
            description = "拣货完成数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "拣货完成数量", required = true)
    private Integer pickFinishCount;

    @FieldDoc(
            description = "配送中数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "配送中数量", required = true)
    private Integer deliveringCount;

    @FieldDoc(
            description = "配送异常数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "配送异常数量", required = true)
    private Integer deliveringErrorCount;

    @FieldDoc(
            description = "已完成数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "已完成数量", required = true)
    private Integer orderCompletedCount;

    @FieldDoc(
            description = "待取消数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "待取消数量", required = true)
    private Integer orderCancelingCount;

    @FieldDoc(
            description = "已取消数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "已取消数量", required = true)
    private Integer orderCanceledCount;


    @FieldDoc(
            description = "待自提数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "待自提数量", required = false)
    private Integer waitToSelfFetchCount = 0;

    @FieldDoc(
            description = "待取货(仅医药成人)", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "待取货数量(仅医药成人)", required = false)
    private Integer waitToTakeGoodsCount = 0;

}
