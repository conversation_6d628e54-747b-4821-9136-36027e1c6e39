package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@TypeDoc(
        description = "根据子类型查询待配送订单列表"
)
@Data
public class QueryDeliveryErrorOrderBySubTypeRequest {

    @FieldDoc(
            description = "第几页", requiredness = Requiredness.REQUIRED
    )
    @NotNull
    private Integer page;

    @FieldDoc(
            description = "每页行数", requiredness = Requiredness.REQUIRED
    )
    @NotNull
    private Integer size;

    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    @NotNull
    private Long tenantId;


    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.OPTIONAL
    )
    private String viewOrderId;


    @FieldDoc(
            description = "门店列表", requiredness = Requiredness.OPTIONAL
    )
    private List<Long> poiIdList;


    @FieldDoc(
            description = "仓库列表", requiredness = Requiredness.OPTIONAL
    )
    private List<Long> warehouseIdList;
}
