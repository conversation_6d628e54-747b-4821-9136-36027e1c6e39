package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.CdqStoreSkuWithChannelInfoVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description: 创建门店商品并上线
 * @author: WangSukuan
 * @create: 2020-01-02
 **/
@TypeDoc(
        description = "(创建或更新)门店商品并上线请求"
)
@Data
@ApiModel("(创建或更新)门店商品并上线请求")
public class SaveCdqStoreSkuRequest {

    @FieldDoc(
            description = "上线门店商品信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "上线门店商品信息", required = true)
    @NotNull
    private CdqStoreSkuWithChannelInfoVo cdqStoreSkuWithChannelInfoVo;

    @FieldDoc(
            description = "1-新建，2-更新", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "1-新建，2-更新", required = true)
    @NotNull
    private Integer saveType;

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店id", required = true)
    @NotNull
    private Long storeId;
    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户ID", required = true)
    @NotNull
    private Long tenantId;
    @FieldDoc(
            description = "是否无限库存配置下提交（仅App使用）"
    )
    @ApiModelProperty(name = "无限库存的标志位")
    private Boolean infiniteInventory;
}
