package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.TypeDoc;

import com.sankuai.shangou.qnh.orderapi.domain.request.pc.PageRequest;
import lombok.Data;

/**
 * @Author: wa<PERSON><PERSON><PERSON>
 * @Date: 2022/07/27
 * @Description:
 */
@TypeDoc(
        name = "创建或者修改门店分组请求对象",
        description = "创建或者修改门店分组请求对象"
)
@Data
public class PoiGroupLogRequest extends PageRequest {

    Integer poiGroupId;
}
