package com.sankuai.shangou.qnh.orderapi.constant.pc;

/**
 * 价格模块常量类
 * <AUTHOR>
 * @Date 2019/01/17 14:26
 */
public class PriceConstant {
    /**
     * 查询所有价格列表时默认每页记录数
     */
    public static int QUERY_PRICE_LIST_PAGE_SIZE = 100;
    //申请价格创建全部成功
    public static final int CREATE_PRICE_ALL_SUC = 1;
    //申请价格创建部分成功
    public static final int CREATE_PRICE_PART_SUC = 2;
    //租户下渠道关闭
    public static final int CHANNEL_NOT_OPENED = 1;
    //门店已关闭
    public static final int STORE_NOT_VALID = 2;
    //商品在门店中不存在
    public static final int SKU_NOT_EXIST_IN_STORE = 3;
    //门店在渠道下关闭
    public static final int STORE_CLOSED_IN_CHANNEL = 4;

    // 市调记录每次请求页大小
    public static final int DOWNLOAD_PAGE_SIZE = 200;

    // 市调最大下载记录数 十万 条
    public static final int MARKET_RESEARCH_DOWNLOAD_MAX_SIZE = 30000;
    // 市调最大下载记录数 2万 条
    public static final int DOWNLOAD_MAX_SIZE = 20000;

    // 每次读取50页数据
    public static final int READ_MAX_PAGE_DATA = 50;

    // 调价策略模块迁移开关
    public static final String PRICE_STRATEGY_MODULE_SWITCH = "priceStrategyModuleSwitch";
    public static final String PRICE_SYNC_MODULE_SWITCH = "priceSyncModuleSwitch";
    public static final String PRICE_ADJUST_MODULE_SWITCH = "priceAdjustModuleSwitch";

    public static final String PRICE_TASK_SWITCH = "priceTaskSwitch";

    public static final String PRICE_SYNC_STRATEGY_MODULE_SWITCH = "priceSycnStrategyModuleSwitch";

    public static final String PRICE_UPDATE_MODULE_SWITCH = "priceUpdateModuleSwitch";

    // 租户维度调价策略配置开关
    public static final String TENANT_PRICE_STRATEGY_CONFIG_SWITCH = "tenantPriceStrategyConfigSwitch";

    // 价格记录模块迁移开关
    public static final String PRICE_RECORD_MODULE_SWITCH = "priceRecordModuleSwitch";

    // 价格新应用迁移开关
    public static final String PRICE_NEW_APP_MIGRATE_SWITCH = "priceNewAppMigrateSwitch";
    // 进货价审核迁移开关
    public static final String OFFLINE_PRICE_MIGRATE_SWITCH = "offlinePriceMigrateSwitch";
    // 门店价格迁移开关
    public static final String STORE_PRICE_MODULE_SWITCH = "storePriceModuleSwitch";
    // 渠道价格迁移开关
    public static final String CHANNEL_PRICE_MODULE_SWITCH = "channelPriceModuleSwitch";

    public static final int ALL_STORE_FLAG = 0;

    public static final int SCENARIO_BOOTH_SKU_UPDATE = 7;

    public static final int SCENARIO_STORE_SKU_UPDATE = 8;

    public static final int SCENARIO_OVERRIDE_STORE_SKU = 14;


    public interface MarketResearch {
        // 批量导入市调价(零售价尾部价格计算而来，非真实市调)，需要用特殊的账号区分，不能使用当前操作人的账号
        long IMPORT_BATCH_MR_PRICE_OPR_ID = 123456789;
        String IMPORT_BATCH_MR_PRICE_OPR_NAME = "123456789";
    }
}
