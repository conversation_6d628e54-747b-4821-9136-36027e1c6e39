package com.sankuai.shangou.qnh.orderapi.configuration.pc;

import com.dianping.cat.servlet.CatFilter;
import com.sankuai.it.sso.sdk.spring.FilterFactoryBean;
import com.sankuai.meituan.reco.store.saas.infrastructure.shield.common.stereotypes.publiccloud.PublicCloud;
import com.sankuai.oceanus.http.filter.InfFilter;
import com.sankuai.shangou.qnh.orderapi.filter.pc.ContextFilter;
import com.sankuai.shangou.qnh.orderapi.filter.pc.CorsFilter;
import com.sankuai.shangou.qnh.orderapi.filter.pc.GrayContextFilter;
import com.sankuai.shangou.qnh.orderapi.filter.pc.SecurityFilter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.core.Ordered;

import javax.annotation.Resource;
import javax.servlet.DispatcherType;
import javax.servlet.Filter;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * 配置各类Filter
 *
 * <AUTHOR>
 */
@Configuration
@Profile("!local-test")
public class FilterConfiguration {

    private static final int INF_FILTER = 1;
    private static final int CONTEXT_FILTER_ORDER = 2;
    private static final int REPO_CONTEXT_FILTER_ORDER = 3;
    private static final int CAT_FILTER_ORDER = 4;
    private static final int CHARACTER_ENCODING_FILTER_ORDER = 5;
    private static final int SECURITY_FILTER_ORDER = 6;
    private static final int CORS_FILTER_ORDER = 7;
    private static final int BROWSER_FILTER_ORDER = 8;
    private static final int STORE_EMPOWER_FILTER_ORDER = 9;
    private static final int GRAY_SCALE_FILTER_ORDER = 10;
    private static final int UWMS_MULTI_TENANT_FILTER_ORDER = 11;

    // 数据鉴权准备 filter，这里放最后面
    private static final int DATA_SECURITY_PREPARE_FILTER_ORDER = 100;

    @Value("${sso.clientId}")
    private String ssoClientId;
    @Resource(name = "ssoSecret")
    private String ssoSecret;
    @Value("${sso.env}")
    private String ssoEnv;

    @Bean
    public FilterRegistrationBean contextFilter() {
        ContextFilter filter = new ContextFilter();
        FilterRegistrationBean registration = new FilterRegistrationBean(filter);
        registration.addUrlPatterns("/api/v1/orderfuse/*");
        registration.addUrlPatterns("/api/v1/channelOrder/*");
        registration.addUrlPatterns("/api/v1/channelComment/*");

        registration.setName("context-filter");
        registration.setOrder(CONTEXT_FILTER_ORDER);
        return registration;
    }

    @Bean
    public FilterRegistrationBean mtFilter(@Qualifier("mtFilterBean") Filter mtFilterBean) {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setFilter(mtFilterBean);
        registration.addUrlPatterns("/api/v1/orderfuse/*");
        registration.addUrlPatterns("/api/v1/channelOrder/*");
        registration.addUrlPatterns("/api/v1/channelComment/*");
        registration.setDispatcherTypes(DispatcherType.REQUEST);
        registration.setName("mtFilter");
        registration.setOrder(Ordered.HIGHEST_PRECEDENCE);
        return registration;
    }

    @Bean
    public FilterFactoryBean mtFilterBean() {
        FilterFactoryBean filterFactoryBean = new FilterFactoryBean();
        filterFactoryBean.setClientId(ssoClientId);
        filterFactoryBean.setSecret(ssoSecret);
        filterFactoryBean.setAccessEnv(ssoEnv);
        filterFactoryBean.setSchema("https");
        filterFactoryBean.setLoginSchema("https");
        filterFactoryBean.setSameSite(false);
        filterFactoryBean.setSsoListener(EapiSSOListener.class.getName());
        filterFactoryBean.setIncludedUriList("/api/v1/sso/loginRedirect, /api/v1/sso/uocLoginRedirect");
        filterFactoryBean.setLogoutUri("/api/v1/sso/logout");
        return filterFactoryBean;
    }

    @PublicCloud
    @Bean
    public FilterRegistrationBean catFilter() {
        CatFilter filter = new CatFilter();
        FilterRegistrationBean registration = new FilterRegistrationBean(filter);
        registration.addUrlPatterns("/api/v1/orderfuse/*");
        registration.addUrlPatterns("/api/v1/channelOrder/*");
        registration.addUrlPatterns("/api/v1/channelComment/*");
        registration.setName("cat-filter");
        registration.setOrder(CAT_FILTER_ORDER);
        return registration;
    }

    @Bean
    public FilterRegistrationBean infFilter() {
        // 1. http 限流接入oceanus-http 创建过滤器注册Bean
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean();

        // 2. 创建并设置InfFilter到注册Bean
        InfFilter filter = new InfFilter();
        filterRegistrationBean.setFilter(filter);

        // 3. 设置需要过滤的url
        List<String> urlPatterns = new ArrayList<>();
        urlPatterns.add("/*");
        filterRegistrationBean.setUrlPatterns(urlPatterns);

        // 4. 设置fileter参数
        //      limit参数配置是否打开限流功能
        filterRegistrationBean.addInitParameter("limit","true");
        filterRegistrationBean.setOrder(INF_FILTER);

        return filterRegistrationBean;
    }

    @Bean
    public FilterRegistrationBean securityFilter() {
        SecurityFilter filter = new SecurityFilter();
        FilterRegistrationBean registration = new FilterRegistrationBean(filter);
        registration.addUrlPatterns("/api/v1/orderfuse/*");
        registration.addUrlPatterns("/api/v1/channelOrder/*");
        registration.addUrlPatterns("/api/v1/channelComment/*");
        registration.setName("security-filter");
        registration.setOrder(SECURITY_FILTER_ORDER);
        return registration;
    }

    @Bean
    public FilterRegistrationBean corsFilter() {
        CorsFilter filter = new CorsFilter();
        FilterRegistrationBean registration = new FilterRegistrationBean(filter);
        registration.addUrlPatterns("/api/v1/orderfuse/*");
        registration.addUrlPatterns("/api/v1/channelOrder/*");
        registration.addUrlPatterns("/api/v1/channelComment/*");
        registration.setName("cors-filter");
        registration.setOrder(CORS_FILTER_ORDER);
        return registration;
    }

    @Bean
    public FilterRegistrationBean grayscaleTestFilter() {
        GrayContextFilter filter = new GrayContextFilter();
        FilterRegistrationBean registration = new FilterRegistrationBean(filter);
        registration.addUrlPatterns("/api/v1/orderfuse/*");
        registration.addUrlPatterns("/api/v1/channelOrder/*");
        registration.addUrlPatterns("/api/v1/channelComment/*");
        registration.setName("grayscale-filter");
        registration.setOrder(GRAY_SCALE_FILTER_ORDER);
        return registration;
    }

}
