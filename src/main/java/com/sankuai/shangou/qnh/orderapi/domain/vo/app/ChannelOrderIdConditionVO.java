package com.sankuai.shangou.qnh.orderapi.domain.vo.app;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TypeDoc(
        description = "订单号查询"
)
@ApiModel("订单号查询")
@Data
public class ChannelOrderIdConditionVO {

    @FieldDoc(
            description = "渠道订单展示号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道订单展示号")
    private String channelOrderId;

    @FieldDoc(
            description = "渠道id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道id")
    private Integer channelId;
}
