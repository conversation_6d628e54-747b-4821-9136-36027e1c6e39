package com.sankuai.shangou.qnh.orderapi.domain.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.OrderFuseFinanceDetailBO;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * pc app共用dto
 * 部分退 克重退 金额退 组合商品信息
 */
@TypeDoc(
        description = "售后商品对应单换货信息"
)
@ApiModel("换货售后商品")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AfsExchangeProductVo {

    @FieldDoc(
            description = "商品名-对应的原单商品信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品名-对应的原单商品信息", required = true)
    private String name;

    @FieldDoc(
            description = "skuId", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "skuId", required = true)
    private String skuId;

    @FieldDoc(
            description = "商品图片", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品图片")
    private String picUrl;

    @FieldDoc(
            description = "upcCode", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "upcCode")
    private List<String> upcCode;

    @FieldDoc(
            description = "erpCode", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "erpCode")
    private String erpCode;


    @FieldDoc(
            description = "规格", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "规格")
    private String specification;

    @FieldDoc(
            description = "重量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "重量")
    private String weight;

    @FieldDoc(
            description = "数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "数量")
    private String quantity;

    @FieldDoc(description = "取消数量", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(value = "取消数量")
    private String refundCount;


    @FieldDoc(
            description = "售价", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "售价")
    private String salePrice;

    @FieldDoc(
            description = "原价", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "原价")
    private String originPrice;

    @FieldDoc(
            description = "商品金额", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品金额")
    private String itemAmt;

    @FieldDoc(
            description = "平台整单优惠", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "平台整单优惠")
    private String platDiscount;

    @FieldDoc(
            description = "平台单品优惠", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "平台单品优惠")
    private String platItemDiscount;

    @FieldDoc(
            description = "商家整单优惠", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商家整单优惠")
    private String poiDiscount;

    @FieldDoc(
            description = "商家单品优惠", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商家单品优惠")
    private String poiItemDiscount;

    @FieldDoc(
            description = "售后明细对应单换入商品信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "售后明细对应单换入商品信息")
    private List<ExchangeToItem> exchangeToItemList;


    @ApiModel("售后明细对应单换入商品信息")
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ExchangeToItem {
        @FieldDoc(
                description = "商品名", requiredness = Requiredness.OPTIONAL
        )
        @ApiModelProperty(name = "商品名", required = true)
        private String name;
        @FieldDoc(
                description = "skuId", requiredness = Requiredness.OPTIONAL
        )
        @ApiModelProperty(name = "skuId", required = true)
        private String skuId;
        @FieldDoc(
                description = "单价（也可能均价）", requiredness = Requiredness.OPTIONAL
        )
        @ApiModelProperty(name = "单价（也可能均价）")
        private BigDecimal unitPrice;

        @FieldDoc(
                description = "商品图片", requiredness = Requiredness.OPTIONAL
        )
        @ApiModelProperty(name = "商品图片")
        private String picUrl;

        @FieldDoc(
                description = "upcCode", requiredness = Requiredness.OPTIONAL
        )
        @ApiModelProperty(name = "upcCode")
        private List<String> upcCode;

        @FieldDoc(
                description = "erpCode", requiredness = Requiredness.OPTIONAL
        )
        @ApiModelProperty(name = "erpCode")
        private String erpCode;


        @FieldDoc(
                description = "规格", requiredness = Requiredness.OPTIONAL
        )
        @ApiModelProperty(name = "规格")
        private String specification;

        @FieldDoc(
                description = "重量", requiredness = Requiredness.OPTIONAL
        )
        @ApiModelProperty(name = "重量")
        private String weight;

        @FieldDoc(
                description = "数量", requiredness = Requiredness.OPTIONAL
        )
        @ApiModelProperty(name = "数量")
        private String quantity;

        @FieldDoc(description = "取消数量", requiredness = Requiredness.OPTIONAL)
        @ApiModelProperty(value = "取消数量")
        private String refundCount;

        @FieldDoc(
                description = "商品金额", requiredness = Requiredness.OPTIONAL
        )
        @ApiModelProperty(name = "商品金额")
        private String itemAmt;

        @FieldDoc(
                description = "原价", requiredness = Requiredness.OPTIONAL
        )
        @ApiModelProperty(name = "原价")
        private String originPrice;

        @FieldDoc(
                description = "售价", requiredness = Requiredness.OPTIONAL
        )
        @ApiModelProperty(name = "售价")
        private String salePrice;

        @FieldDoc(
                description = "平台整单优惠", requiredness = Requiredness.OPTIONAL
        )
        @ApiModelProperty(name = "平台整单优惠")
        private String platDiscount;

        @FieldDoc(
                description = "平台单品优惠", requiredness = Requiredness.OPTIONAL
        )
        @ApiModelProperty(name = "平台单品优惠")
        private String platItemDiscount;

        @FieldDoc(
                description = "商家整单优惠", requiredness = Requiredness.OPTIONAL
        )
        @ApiModelProperty(name = "商家整单优惠")
        private String poiDiscount;

        @FieldDoc(
                description = "商家单品优惠", requiredness = Requiredness.OPTIONAL
        )
        @ApiModelProperty(name = "商家单品优惠")
        private String poiItemDiscount;

        @FieldDoc(
                description = "商品唯一标识", requiredness = Requiredness.OPTIONAL
        )
        @ApiModelProperty(name = "商品唯一标识")
        private Long orderItemId;
    }






}
