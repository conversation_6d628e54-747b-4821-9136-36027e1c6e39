package com.sankuai.shangou.qnh.orderapi.domain.vo.app;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.OrderDetailResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/1/9 10:34
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DeliveryCompleteInfoVO {
    @FieldDoc(
            description = "送达照片（支持多张照片）", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "送达照片（支持多张照片）")
    private List<ProofPhotoInfo> deliveryProofPhotoList;

    @FieldDoc(
            description = "签收方式 1-当面签收 2-放在指定地点并拍照 3-未选择，非自配骑手点已送达", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "签收方式 1-当面签收 2-放在指定地点并拍照 3-未选择，非自配骑手点已送达")
    private Integer signType;


    @FieldDoc(
            description = "是否是弱网", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否是弱网")
    private Boolean isWeakNetwork;

    @Data
    public static class ProofPhotoInfo {
        private String proofPhotoUrl;

        private Integer auditStatus;
    }
}
