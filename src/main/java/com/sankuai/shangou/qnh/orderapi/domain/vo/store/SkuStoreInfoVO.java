package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@TypeDoc(
        description = "商品店铺信息"
)
@Data
@ApiModel("商品店铺信息")
public class SkuStoreInfoVO {

    @FieldDoc(
            description = "商品ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品ID", required = true)
    @NotNull
    private String skuId;

    @FieldDoc(
            description = "店铺ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "店铺ID", required = true)
    @NotNull
    private Long storeId;
}
