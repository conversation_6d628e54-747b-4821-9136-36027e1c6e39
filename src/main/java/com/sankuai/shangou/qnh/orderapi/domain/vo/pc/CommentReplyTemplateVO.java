package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.CommentReplyTemplateBO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 评论回复模板VO
 *
 * <AUTHOR>
 */
@TypeDoc(
        name = "评论回复模板VO",
        description = "评论回复模板VO"
)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CommentReplyTemplateVO {

    /**
     * 模板id
     */
    private Long templateId;

    /**
     * 模板内容
     */
    private String templateContent;

    public static CommentReplyTemplateVO build(CommentReplyTemplateBO commentReplyTemplateBO) {
        if (commentReplyTemplateBO == null) {
            return null;
        }
        CommentReplyTemplateVO commentReplyTemplateVO = new CommentReplyTemplateVO();
        commentReplyTemplateVO.setTemplateId(commentReplyTemplateBO.getTemplateId());
        commentReplyTemplateVO.setTemplateContent(commentReplyTemplateBO.getTemplateContent());
        return commentReplyTemplateVO;
    }
}
