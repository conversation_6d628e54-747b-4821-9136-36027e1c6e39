package com.sankuai.shangou.qnh.orderapi.domain.vo.app;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(
        description = "配送异常信息VO"
)
@ApiModel("配送异常信息VO")
public class DeliveryExceptionInfoVO {
    @FieldDoc(
            description = "运单id"
    )
    @ApiModelProperty(value = "运单id", required = true)
    private String deliveryOrderId;

    @FieldDoc(
            description = "上报骑手id"
    )
    @ApiModelProperty(value = "上报骑手id", required = true)
    private String riderAccountId;

    @FieldDoc(
            description = "上报骑手名字"
    )
    @ApiModelProperty(value = "上报骑手名字", required = true)
    private String riderAccountName;

    @FieldDoc(
            description = "上报异常类型"
    )
    @ApiModelProperty(value = "上报异常类型", required = true)
    private Integer exceptionType;

    @FieldDoc(
            description = "上报异常类型描述"
    )
    @ApiModelProperty(value = "上报异常类型描述", required = true)
    private String exceptionTypeDesc;


    @FieldDoc(
            description = "上报异常时间"
    )
    @ApiModelProperty(value = "上报异常时间", required = true)
    private Long reportTimeStamp;

}