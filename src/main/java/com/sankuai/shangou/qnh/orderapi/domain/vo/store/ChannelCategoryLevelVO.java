package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ChannelCategoryDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/23 10:56
 * @Description:
 */
@TypeDoc(
        name = "ChannelCategoryLevelVO",
        description = "渠道类目VO对象"
)
@Setter
@Getter
@ToString
@EqualsAndHashCode
public class ChannelCategoryLevelVO {

    @FieldDoc(
            description = "渠道类目编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道类目编码")
    private String categoryCode;

    @FieldDoc(
            description = "渠道类目父级编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道类目父级编码")
    private String parentCode;

    @FieldDoc(
            description = "渠道类目名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道类目名称")
    private String categoryName;


    @FieldDoc(
            description = "是否有子类目信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否有子类目信息")
    private Boolean hasChildren;

    @FieldDoc(
            description = "渠道类目层级", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道类目层级")
    private Integer level;

    public ChannelCategoryLevelVO(ChannelCategoryDTO channelCategoryDTO) {
        categoryCode = channelCategoryDTO.getCategoryId();
        parentCode = channelCategoryDTO.getParentId();
        categoryName = channelCategoryDTO.getCategoryName();
        level = channelCategoryDTO.getDepth();
        hasChildren = channelCategoryDTO.getDepth() <= 2;
    }

}
