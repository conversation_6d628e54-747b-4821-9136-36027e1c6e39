package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/8/12
 * desc:
 */
@TypeDoc(
        description = "查询全部聚合渠道门店分类请求"
)
@Data
@ApiModel("查询全部聚合渠道门店分类请求")
public class GetAllAssemblyChannelStoreCategoryRequest {

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID", required = true)
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "单门店多渠道集合运算类型 1-并集 2-交集", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "单门店多渠道集合运算类型", required = true)
    @NotNull
    private Integer multiChannelSetOperationType;

    @FieldDoc(
            description = "渠道ID列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道ID列表")
    private List<Integer> channelIdList;
}
