package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@TypeDoc(
        description = "评价列表查询请求参数",
        authors = ""
)
@Data
@ApiModel("评价列表查询请求参数")
public class CommentReplyTemplateListQueryReq {

    @FieldDoc(
            description = "门店id",
            rule = "storeId"
    )
    private String storeId;

    @FieldDoc(
            description = "评价门店id,app用",
            rule = "commentShopId"
    )
    private Long commentShopId;

}
