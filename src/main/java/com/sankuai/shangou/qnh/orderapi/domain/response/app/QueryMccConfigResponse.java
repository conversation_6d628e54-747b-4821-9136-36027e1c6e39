package com.sankuai.shangou.qnh.orderapi.domain.response.app;

import java.util.HashMap;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import com.meituan.servicecatalog.api.annotations.FieldDoc;

/**
 * @description: 查询mcc配置
 * @author: sunlu<PERSON>
 * @date: 2024/01/05 14:06
 * Copyright (C) 2024 MTDP
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class QueryMccConfigResponse {

    @FieldDoc(
            description = "mcc配置，key：配置项，value：配置的值"
    )
    private Map<String, String> mccConfig = new HashMap<>();

}
