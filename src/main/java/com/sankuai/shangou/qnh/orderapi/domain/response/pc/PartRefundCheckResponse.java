package com.sankuai.shangou.qnh.orderapi.domain.response.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.OrderItemPartRefundCheckVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@TypeDoc(
        description = "部分退款页面检查退款请求响应"
)
@ApiModel("部分退款页面检查退款请求响应")
@Data
public class PartRefundCheckResponse {

    @FieldDoc(
            description = "退款商品列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "退款商品列表", required = true)
    private List<OrderItemPartRefundCheckVO> partRefundCheckVOList;
}
