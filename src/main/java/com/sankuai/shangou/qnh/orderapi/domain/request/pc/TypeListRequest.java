package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/12/5  5:21 下午
 * @since 1.0.0
 */
@TypeDoc(
        description = "根据筛选项查询枚举信息"
)
@ApiModel("根据筛选项查询枚举信息")
@Getter
@Setter
@ToString
@NoArgsConstructor
public class TypeListRequest {
    @FieldDoc(
            description = "筛选项数组"
    )
    @ApiModelProperty(value = "筛选项数组", required = true)
    private List<String> typeList;

}
