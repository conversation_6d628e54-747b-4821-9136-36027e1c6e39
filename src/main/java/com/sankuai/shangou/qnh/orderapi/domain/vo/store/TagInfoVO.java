package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: <EMAIL>
 * @Date: 2020-05-20 11:08
 * @Description:
 */
@TypeDoc(
        description = "标签信息"
)
@ApiModel("标签信息")
@Data
public class TagInfoVO {
    @FieldDoc(
            description = "标签类型 1-履约标签 2-挑选标准", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "原总价  单位:分", required = true)
    private Integer type;

    @FieldDoc(
            description = "标签名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "标签名称", required = true)
    private String name;


}

