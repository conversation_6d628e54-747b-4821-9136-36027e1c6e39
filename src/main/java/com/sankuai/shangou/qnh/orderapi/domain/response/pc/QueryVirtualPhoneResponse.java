package com.sankuai.shangou.qnh.orderapi.domain.response.pc;

import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.shangou.qnh.orderapi.domain.bo.ReturnDataPoiBo;
import com.sankuai.shangou.qnh.orderapi.domain.result.ReturnDataSecurity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Objects;

@Data
public class QueryVirtualPhoneResponse implements ReturnDataSecurity {
    @FieldDoc(
            description = "收货号码-隐私号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "收货号码-隐私号", required = true)
    private String phoneNo;

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店ID")
    private Long poiId;

    @FieldDoc(
            description = "转单门店ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "转单门店ID")
    private Long dispatchShopId;

    @Override
    public List<ReturnDataPoiBo> fetchReturnDataPoiBoList() {
        if (Objects.nonNull(this.poiId)) {
            return Lists.newArrayList(
                    ReturnDataPoiBo.builder().poiId(this.poiId).dispatchShopId(this.dispatchShopId).build());
        }
        return null;
    }
}
