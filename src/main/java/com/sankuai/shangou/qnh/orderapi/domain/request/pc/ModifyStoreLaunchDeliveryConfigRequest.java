package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.qnh.orderapi.constant.pc.Constants;
import com.sankuai.shangou.qnh.orderapi.constant.pc.DeliveryConstant;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.StoreLaunchDeliveryConfigVO;
import com.sankuai.shangou.qnh.orderapi.enums.pc.DeliveryLaunchPatternEnum;
import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/12/30
 */
@Data
public class ModifyStoreLaunchDeliveryConfigRequest implements BaseRequest {
    @FieldDoc(
            description = "门店发配送规则"
    )
    @ApiModelProperty(value = "门店发配送规则")
    private StoreLaunchDeliveryConfigVO storeLaunchDeliveryConfig;

    @FieldDoc(
            description = "门店ID"
    )
    @ApiModelProperty(value = "门店ID", required = true)
    private Long poiId;

    @Override
    public void selfCheck() {
        AssertUtil.notNull(poiId, "门店ID不能为空", "poiId");
        AssertUtil.notNull(storeLaunchDeliveryConfig, "配置不能为空", "storeLaunchDeliveryConfig");
        DeliveryLaunchPatternEnum deliveryLaunchPattern = DeliveryLaunchPatternEnum.enumOf(storeLaunchDeliveryConfig.getLaunchPattern());
        AssertUtil.notNull(deliveryLaunchPattern, "配送发单模式无效");
        if (deliveryLaunchPattern == DeliveryLaunchPatternEnum.AUTO_LAUNCH_DELIVERY) {
            // 自动发配送，进行进一步参数校验
            AssertUtil.notNull(storeLaunchDeliveryConfig.getLaunchRule(), "配送发单规则无效");
            AssertUtil.numInSet(storeLaunchDeliveryConfig.getAutoLaunchPoint(), DeliveryConstant.REALTIME_ORDER_LAUNCH_POINT_TYPE,
                    "实时单的发单时间类型无效");
            AssertUtil.isTrue(Objects.nonNull(storeLaunchDeliveryConfig.getAutoLaunchDelayMinutes()) &&
                    storeLaunchDeliveryConfig.getAutoLaunchDelayMinutes() >= Constants.TIME_START &&
                    storeLaunchDeliveryConfig.getAutoLaunchDelayMinutes() <= Constants.TIME_END, "实时单发单时间配置越界");
            AssertUtil.numInSet(storeLaunchDeliveryConfig.getBookingOrderAutoLaunchPoint(),
                    DeliveryConstant.BOOKING_ORDER_LAUNCH_POINT_TYPE, "预约单的发单时间类型无效");
            if (storeLaunchDeliveryConfig.getBookingOrderAutoLaunchPoint().equals(DeliveryConstant.BOOKING_ORDER_LAUNCH_BEFORE_DELIVERY)) {
                AssertUtil.isTrue(Objects.nonNull(storeLaunchDeliveryConfig.getBookingOrderAutoLaunchMinutes()) &&
                        (storeLaunchDeliveryConfig.getBookingOrderAutoLaunchMinutes() >= DeliveryConstant.BOOKING_ORDER_BEFORE_DELIVERY_MINUTES_MIN) &&
                        (storeLaunchDeliveryConfig.getBookingOrderAutoLaunchMinutes() <= DeliveryConstant.BOOKING_ORDER_BEFORE_DELIVERY_MINUTES_MAX),
                        "预约单发单时间配置越界");
            } else {
                AssertUtil.isTrue(Objects.nonNull(storeLaunchDeliveryConfig.getBookingOrderAutoLaunchMinutes()) &&
                        (storeLaunchDeliveryConfig.getBookingOrderAutoLaunchMinutes() >= Constants.TIME_START) &&
                        (storeLaunchDeliveryConfig.getBookingOrderAutoLaunchMinutes() <= Constants.TIME_END), "预约单发单时间配置越界");
            }
        }
    }

}
