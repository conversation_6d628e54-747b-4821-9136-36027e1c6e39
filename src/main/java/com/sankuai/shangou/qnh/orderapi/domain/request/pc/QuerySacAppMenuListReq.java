package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.sankuai.meituan.shangou.saas.common.aop.feature.Validatable;
import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020-12-09
 */
@Data
public class QuerySacAppMenuListReq implements Validatable {

    /**
     * 应用ID
     */
    private List<Integer> bizAppIdList;

    @Override
    public void validate() {
        AssertUtil.notEmpty(bizAppIdList, "bizAppIdList不能为空");
    }
}
