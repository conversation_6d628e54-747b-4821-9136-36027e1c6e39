package com.sankuai.shangou.qnh.orderapi.constant.pc;

import java.math.BigDecimal;

import lombok.Data;

/**
 * 公共常量
 *
 * <AUTHOR>
 */
public abstract class CommonConstant {

    /**
     * 默认账号
     */
    public static final String DEFAULT_ACCOUNT = "system";

    /** UTF-8字符集 **/
    public static final String CHARSET_UTF8 = "UTF-8";

    /** 开关 **/
    public static final String SWITCH_ON = "ON";
    public static final String SWITCH_OFF = "OFF";

    public static final String HEAD_OCMS_MIGRATE_TEST = "OcmsMigrateTest";

    /** HTTP请求相关 **/
    public static final String METHOD_POST = "POST";
    public static final String METHOD_GET = "GET";
    public static final String CONTENT_TYPE_TEXT_HTML = "text/html";
    public static final String CONTENT_TYPE_APPLICATION_JSON = "application/json";
    public static final String HEADER_X_REQUESTED_WITH = "X-Requested-With";
    public static final String HEAD_REFERER = "Referer";
    public static final String XML_HTTP_REQUEST = "XMLHttpRequest";
    public static final String ACCEPT_ENCODING = "Accept-Encoding";
    public static final String CONTENT_ENCODING = "Content-Encoding";
    public static final String CONTENT_ENCODING_GZIP = "gzip";

    /** SQL **/
    public static final String REQUEST_PARAM_SORTED_BY = "sortedBy";
    public static final String REQUEST_PARAM_SORTED_TYPE = "sortedType";
    public static final String ORDER_TYPE_ASC = "ASC";
    public static final String ORDER_TYPE_DESC = "DESC";
    public static final String ORDER_BY_COLUMN_ID = "id";

    /** 单位转换 **/
    public static final int SECOND_MILLISECOND_CONVERSION = 1000;
    /** 分转换为元 **/
    public static final int FEN_YUAN_CONVERSION = 100;
    /** 折扣转换 **/
    public static final int DISCOUNT_CONVERSION = 100;
    public static final BigDecimal BIG_DECIMAL_100 = new BigDecimal("100");

    public static final String COMMA = ",";
    public static final String DOT = ".";
    //门店权限组
    public static final int AUTH_TYPE_OF_STORE = 1;
    //仓库权限组
    public static final int AUTH_TYPE_OF_REPOSITORY = 3;
    //摊位权限组
    public static final int AUTH_TYPE_OF_BOOTH = 4;

    public static final int DEFAULT_PAGE_NUMBER = 1;
    public static final int DEFAULT_PAGE_SIZE = 100;

    public static final String TASK_STATUS_PENDING = "待处理";
    public static final String TASK_STATUS_PROCESSING = "处理中";
    public static final String TASK_STATUS_DONE = "已完成";

    //占位符
    public static final String PLACEHOLDER = "-";

    // 金额为0展示字符
    public static final String ZERO_AMOUNT_STRING = "0.00";

    public static final int qtBizAppId  =  1;

    public static final int BAI_CHUAN_APP_ID = 2;

    public static final long ALL_POI = 0L;

    public static final String PRIVACY_PROTECT_ADDRESS = "为保护用户隐私具体地址已隐藏";

    /**
     * 隐私号过期文案
     */
    public static final String PRIVACY_RECEIVER_PHONE_EXPIRED = "隐私号已过期";

    /**
     * 有赞门店自提单收货人地址
     */
    public static final String YOU_ZAN_STORE_DELIVERY_ADDRESS = "到店自取@#到店自取";

    public static final int FRANCHISEE_ORDER = 1;

    @Data
    public static class TenantChannel{
        private String channel;
    }
}
