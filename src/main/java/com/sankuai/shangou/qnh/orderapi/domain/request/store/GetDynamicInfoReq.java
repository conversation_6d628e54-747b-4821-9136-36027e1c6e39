package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.BaseOrderReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * GetDynamicInfoReq
 *
 * <AUTHOR>
 * @since 2023/3/21
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class GetDynamicInfoReq extends BaseOrderReq {

    @FieldDoc(description = "门店ID", requiredness = Requiredness.REQUIRED)
    @NotNull
    private Long storeId;

}
