package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TypeDoc(
        description = "错误信息"
)
@Data
@ApiModel("错误信息")
public class ErrorRecordVO {

    @FieldDoc(
            description = "sku编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "sku编码", required = true)
    private String sku;

    @FieldDoc(
            description = "门店编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店编码", required = true)
    private Long storeId;

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID", required = true)
    private Integer channelId;

    @FieldDoc(
            description = "错误类型", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "错误类型", required = true)
    private Integer errorType;

    @FieldDoc(
            description = "错误信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "错误信息", required = true)
    private String errorMsg;
}
