package com.sankuai.shangou.qnh.orderapi.service.common;

import com.dianping.cat.util.MetricHelper;
import com.google.common.collect.Maps;
import com.meituan.shangou.saas.common.enums.OrderCanOperateItem;
import com.meituan.shangou.saas.o2o.dto.model.OCMSOrderKey;
import com.meituan.shangou.saas.o2o.dto.request.OCMSOperateCheckRequest;
import com.meituan.shangou.saas.o2o.dto.response.OCMSOperateCheckResponse;
import com.meituan.shangou.saas.order.platform.enums.DeliveryStatusEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.meituan.shangou.saas.service.ocms.OCMSOrderOperateThriftService;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.OrderCouldOperateItem;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionSubTypeEnum;
import com.sankuai.shangou.qnh.orderapi.context.app.ApiMethodParamThreadLocal;
import com.sankuai.shangou.qnh.orderapi.domain.dto.app.IdentityInfo;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.OrderListResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.RefundApplyListResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.OrderVO;
import com.sankuai.shangou.qnh.orderapi.enums.app.ChannelTypeEnum;
import com.sankuai.shangou.qnh.orderapi.enums.app.OrderViewStatusEnum;
import com.sankuai.shangou.qnh.orderapi.remote.DHThirdDeliveryRemoteService;
import com.sankuai.shangou.qnh.orderapi.service.InvoiceService;
import com.sankuai.shangou.qnh.orderapi.service.pc.TenantService;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/7/15
 **/
@Slf4j
@Service
public class OrderOperateItemsService {
    @Resource
    private OCMSOrderOperateThriftService ocmsOrderOperateThriftService;

    @Resource
    private InvoiceService invoiceService;

    @Resource
    private DHThirdDeliveryRemoteService dhThirdDeliveryService;

    @Resource
    private TenantService tenantService;


    static final List<Integer> TO_CHECK_ITEMS = Lists.newArrayList(
            OrderCanOperateItem.ACCEPT_ORDER.getValue(),
            OrderCanOperateItem.COMPLETE_PICK.getValue(),
            OrderCanOperateItem.PRINT_RECEIPT.getValue(),
            OrderCanOperateItem.PART_ORDER_REFUND.getValue(),
            OrderCanOperateItem.FULL_ORDER_REFUND.getValue(),
            OrderCanOperateItem.WEIGHT_REFUND.getValue(),
            OrderCanOperateItem.AFTER_SALE_REFUND.getValue(),
            OrderCanOperateItem.SELF_FETCH_FINISH.getValue(),
            OrderCanOperateItem.MONEY_REFUND.getValue(),
            OrderCanOperateItem.DISPATCH_ORDER.getValue(),
            OrderCanOperateItem.CREATE_INVOICE.getValue(),
            OrderCanOperateItem.ORDER_SELF_DELIVERY_TASKS.getValue(),
            OrderCanOperateItem.SETTING_ORDER_TAG.getValue()
    );
    public OrderListResponse setCouldOperateItems(OrderListResponse orderListResponse, Long currentStoreId) {
        return setCouldOperateItems(orderListResponse, null, currentStoreId);
    }

    public OrderListResponse setCouldOperateItems(OrderListResponse orderListResponse, OrderCouldOperateItem defaultOperateItem, Long currentStoreId) {
        if (CollectionUtils.isNotEmpty(orderListResponse.getOrderList())) {
            setCouldOperateItems(orderListResponse.getOrderList(), defaultOperateItem, TO_CHECK_ITEMS,currentStoreId);
        }
        return orderListResponse;
    }

    public void setCouldOperateItems(Long tenantId, OrderListResponse orderListResponse, OrderCouldOperateItem defaultOperateItem,
                                      List<OrderCouldOperateItem> extraCheckItems, Long currentStoreId) {
        List<Integer> checkItems = Lists.newArrayList(TO_CHECK_ITEMS);
        if (MccConfigUtil.getDHTenantIdList().contains(tenantId.toString())) {
            // 歪马租户的检查按钮与其他租户不同，去掉了拣货完成按钮
            checkItems.removeIf(item -> item == OrderCouldOperateItem.COMPLETE_PICK.getValue());
        }
        if (CollectionUtils.isNotEmpty(extraCheckItems)) {
            checkItems.addAll(extraCheckItems.stream().map(OrderCouldOperateItem::getValue).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(orderListResponse.getOrderList())) {
            setCouldOperateItems(orderListResponse.getOrderList(), defaultOperateItem, checkItems, currentStoreId);
        }
    }

    protected void setCouldOperateItems(List<OrderVO> orderVOS, OrderCouldOperateItem defaultOperateItem, List<Integer> checkItems, Long currentStoreId) {
        if (CollectionUtils.isNotEmpty(orderVOS)) {
            Map<Integer, Boolean> accountAuthCodeMap = tenantService.queryAccountCode();
            IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
            long tenantId = identityInfo.getUser().getTenantId();
            Map<OCMSOrderKey, List<Integer>> ocmsOrderKeyListMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(orderVOS)) {
                OCMSOperateCheckRequest checkRequest = new OCMSOperateCheckRequest();
                checkRequest.setTenantId(tenantId);
                checkRequest.setToCheckOperateItems(checkItems);
                checkRequest.setOrderList(orderVOS.stream().map(e -> OCMSOrderKey.builder()
                        .channelOrderId(e.getChannelOrderId())
                        .channelType(e.getChannelId())
                        .build()).collect(Collectors.toList()));
                OCMSOperateCheckResponse response = null;
                try {
                    response = ocmsOrderOperateThriftService.checkOrderCouldOperateItems(checkRequest);
                    log.info("查询订单可操作项，request:{}, response:{}", checkRequest, response);
                    if (response.getCouldOperateItems() != null && !response.getCouldOperateItems().isEmpty()) {
                        ocmsOrderKeyListMap.putAll(response.getCouldOperateItems());
                    }
                } catch (TException e) {
                    log.error("查询可操作列表失败,request:{}", checkRequest, e);
                }
            }

            //  获取有开票按钮权限的门店
            List<Long> createInvoiceShopList = invoiceService.getCreateInvoiceShopList(tenantId);
            Map<String, Map<String, String>> invoiceUrlShopConfig = invoiceService.getInvoiceUrlShopConfig();
            String invoiceUrlForTenant = invoiceService.getInvoiceUrlForTenant(tenantId);

            //设置可操作项
            orderVOS.forEach(order -> {
                List<Integer> couldOperateItems = Lists.newArrayList();
                ocmsOrderKeyListMap.entrySet().stream()
                        .filter(entry -> StringUtils.equals(entry.getKey().getChannelOrderId(), order.getChannelOrderId())
                                && entry.getKey().getChannelType() == order.getChannelId())
                        .findFirst()
                        .ifPresent(entry -> {
                            if (CollectionUtils.isNotEmpty(entry.getValue())) {
                                couldOperateItems.addAll(entry.getValue());
                            }
                        });
                // 添加开票按钮权限值
                if(invoiceService.checkOrderCreateInvoice(createInvoiceShopList, order.getStoreId(), order.getOrderStatus(), tenantId, invoiceUrlForTenant, invoiceUrlShopConfig)){
                    couldOperateItems.add(OrderCanOperateItem.CREATE_INVOICE.getValue());
                }
                //有赞渠道不设置默认操作
                if (defaultOperateItem != null && !couldOperateItems.contains(defaultOperateItem.getValue())
                        && !Integer.valueOf(ChannelTypeEnum.YOU_ZAN.getChannelId()).equals(order.getChannelId())) {
                    couldOperateItems.add(defaultOperateItem.getValue());
                }
                //权限code与订单可操作项diff
                List<Integer> finalCouldOperateItems = couldOperateItems.stream().filter(operateItem->{
                    return Objects.isNull(accountAuthCodeMap.get(operateItem)) || BooleanUtils.isTrue(accountAuthCodeMap.get(operateItem));//没有配置，或者权限没有返回都当做有权限
                }).collect(Collectors.toList());
                log.info("权限过滤，order:{}, account:{}, couldOperateItems:{}, finalCouldOperateItems:{}", order.getChannelOrderId(), identityInfo.getUser().getAccountId(), couldOperateItems, finalCouldOperateItems);
                recordOrderOperatorMetrics(finalCouldOperateItems);
                if(MccConfigUtil.isDrunkHorseTenant(order.getTenantId())) {
                    List<Integer> deliveryChannelButtons = dhThirdDeliveryService.getDeliveryChannelButtons(order.getTenantId(), order.getStoreId(), order);
                    finalCouldOperateItems.addAll(deliveryChannelButtons);
                }
                if(order.isPlatformDelivery()){
                    finalCouldOperateItems = finalCouldOperateItems.stream().filter(e->e!=OrderCanOperateItem.DISPATCH_ORDER.getValue()).collect(Collectors.toList());
                }
                // 【库位指引】按钮
                if(checkRepertoryGuideOp(order.getTenantId(), order.getOrderStatus(), order.getPickStatus())){
                    finalCouldOperateItems.add(OrderCanOperateItem.REPERTORY_GUIDE.getValue());
                }
                if(currentStoreId !=null && order.getDispatchShopId() != null && !Objects.equals(currentStoreId,order.getDispatchShopId())){
                    finalCouldOperateItems = finalCouldOperateItems.stream().filter(e->Objects.equals(e,OrderCanOperateItem.PRINT_RECEIPT.getValue()) || Objects.equals(e,OrderCanOperateItem.SETTING_ORDER_TAG.getValue())).collect(Collectors.toList());
                }
                order.setOrderCouldOperateItems(finalCouldOperateItems);
            });
        }
    }

    /**
     * 检查是否需要【库位指引】操作
     * @param tenantId
     * @param pickStatus
     * @return
     */
    public static boolean checkRepertoryGuideOp(Long tenantId, Integer orderStatus, Integer pickStatus){
        try {
            // 租户是否支持
            if(!com.sankuai.shangou.qnh.orderapi.utils.pc.MccConfigUtil.checkSupportRepertoryGuideOpTenants(tenantId)){
                return false;
            }
            // 订单状态【未完成、未取消】
            boolean orderFlag = orderStatus == null || !(Objects.equals(OrderStatusEnum.COMPLETED.getValue(), orderStatus) || Objects.equals(OrderStatusEnum.CLOSED.getValue(), orderStatus) || Objects.equals(OrderStatusEnum.CANCELED.getValue(), orderStatus));
            // 拣货单的状态【拣货完成之前】
            boolean pickFlag = pickStatus == null || !(Objects.equals(DeliveryStatusEnum.PICKED.getValue(), pickStatus) || Objects.equals(DeliveryStatusEnum.CANCELED.getValue(), pickStatus));
            return orderFlag && pickFlag;
        }catch (Exception e){
            log.info("FuseOrderServiceImpl.checkRepertoryGuideOp error tenantId:{}, pickStatus:{}", tenantId, pickStatus, e);
            return false;
        }
    }



    private void recordOrderOperatorMetrics(List<Integer> finalCouldOperateItems) {
        MetricHelper.build().name("order.operateItem.show").tag("type", "apiCnt").count();
        if (CollectionUtils.isNotEmpty(finalCouldOperateItems)){
            finalCouldOperateItems.stream().forEach(operateItem->{
                MetricHelper.build().name("order.operateItem.show").tag("type", String.valueOf(operateItem)).count();
            });
        }
    }

    public void justKeepPartOperateItemsForDeliveryErrorOrder(OrderListResponse orderListResponse, List<OrderCanOperateItem> keepedOperateItems) {
        List<OrderVO> orderVOs = orderListResponse.getOrderList();
        if (CollectionUtils.isEmpty(orderVOs) || CollectionUtils.isEmpty(keepedOperateItems)) {
            return;
        }
        List<OrderVO> orderVOList = orderVOs.stream().filter(this::isDeliveryErrorOrder).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderVOList)){
            return;
        }
        List<Integer> keepedOperateCodes = keepedOperateItems.stream().map(OrderCanOperateItem::getValue).collect(Collectors.toList());
        setCouldOperateItems(orderVOList,null, TO_CHECK_ITEMS, null);
        orderVOList.forEach(order -> {
            List<Integer> orderCouldOperateItems = Lists.newArrayList(order.getOrderCouldOperateItems());
            orderCouldOperateItems.retainAll(keepedOperateCodes);
            order.setDeliveryExceptionOrderCouldOperateItems(orderCouldOperateItems);

            DeliveryExceptionSubTypeEnum deliveryExceptionSubTypeEnum = DeliveryExceptionSubTypeEnum.deliveryStatusCodeValueOfWithOutAll(
                    Optional.ofNullable(order.getDeliveryExceptionType()).orElse(null),
                    order.getDeliveryExceptionCode(),
                    order.getTmsDistributeStatus());
            order.setDeliveryExceptionViewStatus(getOrderViewStatusByExceptionOrderSubType(deliveryExceptionSubTypeEnum));
            order.setAllowLatestAuditTime(order.getAllowLatestAuditTime());
            order.setExceptionCode(order.getDeliveryExceptionCode());
        });
    }

    private boolean isDeliveryErrorOrder(OrderVO orderVO){
        if (Objects.nonNull(orderVO.getDeliveryExceptionCode()) && orderVO.getDeliveryExceptionCode() != 0){
            return true;
        }
        if (Objects.nonNull(orderVO.getDeliveryExceptionType()) && orderVO.getDeliveryExceptionType() != 0){
            return true;
        }
        return false;
    }

    private Integer getOrderViewStatusByExceptionOrderSubType(DeliveryExceptionSubTypeEnum deliveryExceptionSubTypeEnum){
        if (deliveryExceptionSubTypeEnum == null) {
            return null;
        }
        Integer orderViewStatus = null;
        switch (deliveryExceptionSubTypeEnum) {
            case NO_RIDER_ACCEPT:
                orderViewStatus = OrderViewStatusEnum.NO_RIDER_ACCEPT.getCode();
                break;
            case NO_ARRIVAL_STORE:
                orderViewStatus = OrderViewStatusEnum.NO_ARRIVAL_STORE.getCode();
                break;
            case NO_RIDER_TAKE_GOODS:
                orderViewStatus = OrderViewStatusEnum.NO_RIDER_TAKE_GOODS.getCode();
                break;
            case DELIVERY_TIMEOUT:
                orderViewStatus = OrderViewStatusEnum.DELIVERY_TIMEOUT.getCode();
                break;
            case SYSTEM_EXCEPTION:
                orderViewStatus = OrderViewStatusEnum.SYSTEM_EXCEPTION.getCode();
                break;
            case REPORT_EXCEPTION:
                orderViewStatus = OrderViewStatusEnum.REPORT_EXCEPTION.getCode();
                break;
            case TAKE_EXCEPTION:
                orderViewStatus = OrderViewStatusEnum.TAKE_EXCEPTION.getCode();
                break;
            default:
                return null;
        }
        return orderViewStatus;
    }

    public RefundApplyListResponse setCouldOperateItems(RefundApplyListResponse refundApplyListResponse) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        if (CollectionUtils.isNotEmpty(refundApplyListResponse.getRefundApplyRecordVOList()) && identityInfo.getUser() != null) {
            long tenantId = identityInfo.getUser().getTenantId();
            Map<Integer,Boolean> accountAuthCodeMap = tenantService.queryAccountCode();
            Map<OCMSOrderKey, List<Integer>> ocmsOrderKeyListMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(refundApplyListResponse.getRefundApplyRecordVOList())) {
                OCMSOperateCheckRequest checkRequest = new OCMSOperateCheckRequest();
                checkRequest.setTenantId(tenantId);
                checkRequest.setToCheckOperateItems(TO_CHECK_ITEMS);
                checkRequest.setOrderList(refundApplyListResponse.getRefundApplyRecordVOList().stream().map(e -> OCMSOrderKey.builder()
                        .channelOrderId(e.getOrderVO().getChannelOrderId())
                        .channelType(e.getOrderVO().getChannelId())
                        .build()).collect(Collectors.toList()));
                try {
                    OCMSOperateCheckResponse response = ocmsOrderOperateThriftService.checkOrderCouldOperateItems(checkRequest);
                    log.info("查询退款单可操作项，request:{}, response:{}", checkRequest, response);
                    if (response.getCouldOperateItems() != null && !response.getCouldOperateItems().isEmpty()) {
                        ocmsOrderKeyListMap.putAll(response.getCouldOperateItems());
                    }
                } catch (TException e) {
                    log.error("查询可操作列表失败,request:{}", checkRequest, e);
                }
            }

            // 获取租户下有开票按钮权限的门店
            List<Long> createInvoiceShopList = invoiceService.getCreateInvoiceShopList(tenantId);
            Map<String, Map<String, String>> invoiceUrlShopConfig = invoiceService.getInvoiceUrlShopConfig();
            String invoiceUrlForTenant = invoiceService.getInvoiceUrlForTenant(tenantId);

            //设置可操作项
            refundApplyListResponse.getRefundApplyRecordVOList().forEach(refundApplyRecordVO -> {
                List<Integer> couldOperateItems = Lists.newArrayList();
                ocmsOrderKeyListMap.entrySet().stream()
                        .filter(entry -> StringUtils.equals(entry.getKey().getChannelOrderId(), refundApplyRecordVO.getOrderVO().getChannelOrderId())
                                && entry.getKey().getChannelType() == refundApplyRecordVO.getOrderVO().getChannelId())
                        .findFirst()
                        .ifPresent(entry -> {
                            if (CollectionUtils.isNotEmpty(entry.getValue())) {
                                couldOperateItems.addAll(entry.getValue());
                            }
                        });
                // 设值开票按钮权限
                if(invoiceService.checkOrderCreateInvoice(createInvoiceShopList, refundApplyRecordVO.getOrderVO().getStoreId(), refundApplyRecordVO.getOrderVO().getOrderStatus(),tenantId, invoiceUrlForTenant, invoiceUrlShopConfig)){
                    couldOperateItems.add(OrderCanOperateItem.CREATE_INVOICE.getValue());
                }
                //权限code与订单可操作项diff
                List<Integer> finalCouldOperateItems = couldOperateItems.stream().filter(operateItem->{
                    return Objects.isNull(accountAuthCodeMap.get(operateItem)) || BooleanUtils.isTrue(accountAuthCodeMap.get(operateItem));//没有配置，或者权限没有返回都当做有权限
                }).collect(Collectors.toList());
                log.info("权限过滤，afterSaleId:{}, account:{}, couldOperateItems:{}, finalCouldOperateItems:{}", refundApplyRecordVO.getAfterSaleId(), identityInfo.getUser().getAccountId(), couldOperateItems, finalCouldOperateItems);
                recordOrderOperatorMetrics(finalCouldOperateItems);
                refundApplyRecordVO.setOrderCouldOperateItems(finalCouldOperateItems);
            });
        }
        return refundApplyListResponse;
    }

    /**
     * 仅保留订单的部分可操作按钮.
     *
     * @param orderListResponse  订单列表
     * @param keepedOperateItems 需要保留的操作
     */
    public void justKeepPartOperateItems(OrderListResponse orderListResponse, List<OrderCanOperateItem> keepedOperateItems) {
        List<OrderVO> orderVOs = orderListResponse.getOrderList();
        if (CollectionUtils.isEmpty(orderVOs) || CollectionUtils.isEmpty(keepedOperateItems)) {
            return;
        }
        List<Integer> keepedOperateCodes = keepedOperateItems.stream().map(OrderCanOperateItem::getValue).collect(Collectors.toList());
        orderVOs.forEach(order -> {
            order.getOrderCouldOperateItems().retainAll(keepedOperateCodes);
        });
    }
}
