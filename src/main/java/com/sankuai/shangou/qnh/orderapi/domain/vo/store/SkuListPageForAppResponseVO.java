package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "分页查询商品响应"
)
@Data
@ApiModel("分页查询商品响应")
public class SkuListPageForAppResponseVO {

    @FieldDoc(
            description = "商品信息列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品信息列表", required = true)
    private List<ChannelSkuForAppVO> skuInfoList;

    @FieldDoc(
            description = "分页信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "分页信息", required = true)
    private PageInfoVO pageInfo;
}
