package com.sankuai.shangou.qnh.orderapi.remote;

import com.meituan.shangou.saas.order.platform.client.dto.model.BigOrderSkuModel;
import com.meituan.shangou.saas.order.platform.client.dto.request.QueryBigOrderTagRequest;
import com.meituan.shangou.saas.order.platform.client.dto.response.BigOrderTagQueryResponse;
import com.meituan.shangou.saas.order.platform.client.service.OrderTagThriftService;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class OrderTagRemoteService {

    @Resource
    private OrderTagThriftService orderTagThriftService;

    public List<BigOrderSkuModel> getBigOrderTag(Long tenantId, String orderId) {
        QueryBigOrderTagRequest request = new QueryBigOrderTagRequest();
        request.setTenantId(tenantId);
        request.setOrderId(orderId);
        try {
            BigOrderTagQueryResponse response = orderTagThriftService.getBigOrderTagByOrder(request);
            return response.getBigOrderSkuList();
        } catch (Exception e) {
            log.error("查询大订单标签失败", e);
            throw new BizException("查询大订单标签失败");
        }
    }

}
