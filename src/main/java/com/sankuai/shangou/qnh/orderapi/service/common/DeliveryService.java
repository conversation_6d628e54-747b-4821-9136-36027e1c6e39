package com.sankuai.shangou.qnh.orderapi.service.common;

import com.dianping.lion.client.Lion;
import com.google.common.base.Function;
import com.google.common.collect.ArrayListMultimap;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.meituan.shangou.saas.order.management.client.enums.ChannelType;
import com.meituan.shangou.saas.order.platform.enums.DistributeStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.PlatformSourceEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.BatchStoreConfigQueryResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.TAggDeliveryPlatformConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.TStoreConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.dto.DeliveryChannelDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.DeliveryOperateItem;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryDetail;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryOrder;
import com.sankuai.shangou.qnh.orderapi.context.app.ApiMethodParamThreadLocal;
import com.sankuai.shangou.qnh.orderapi.domain.dto.app.IdentityInfo;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.OrderListResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.DeliveryExceptionSummaryVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.DeliveryRedirectModuleVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.OrderVO;
import com.sankuai.shangou.qnh.orderapi.enums.app.*;
import com.sankuai.shangou.qnh.orderapi.remote.DeliveryChannelRemoteService;
import com.sankuai.shangou.qnh.orderapi.remote.RiderDeliveryRemoteService;
import com.sankuai.shangou.qnh.orderapi.remote.TmsRemoteService;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/7/15
 **/
@Service
@Slf4j
public class DeliveryService {
    // TMS 系统，配送无异常的编码
    private static final Integer DELIVERY_NO_EXCEPTION = 0;
    @Autowired
    private TmsRemoteService tmsServiceWrapper;
    @Resource
    private DeliveryChannelRemoteService deliveryChannelWrapper;
    @Resource
    private RiderDeliveryRemoteService riderDeliveryRemoteService;

    /**
     *  仅仅 UrlText 为查看配送详情 去处理异常才需要展示跳转链接 为null或 "" 不展示
     * @param orderVOList
     * @param deliveryDetailMap
     * @param channelDtoMap
     * @param curStoreId
     */
    public void fillOrderMaltDeliveryPlatModuleWithoutUrl(List<OrderVO> orderVOList, Map<Long, TDeliveryDetail> deliveryDetailMap, Map<Integer, DeliveryChannelDto> channelDtoMap, Long curStoreId){
        if(CollectionUtils.isEmpty(orderVOList)) {
            return;
        }
        Map<Integer, Integer> deliveryChannelMap = deliveryChannelWrapper.tratranslateToChannelIntgerMap(channelDtoMap);
        orderVOList.forEach(order -> {
            Long shopId = Objects.nonNull(order.getDispatchShopId()) ? order.getDispatchShopId() : (Objects.nonNull(order.getWarehouseId()) ? order.getWarehouseId() : order.getStoreId());
            TDeliveryDetail deliveryDetail = deliveryDetailMap.get(order.getEmpowerOrderId());
            if(deliveryDetail == null){
                return;
            }
            boolean isMaltFarm = deliveryChannelWrapper.checkChannel(deliveryDetail.deliveryChannelCode, AggDeliveryPlatformEnum.MALT_FARM.getCode(), order.getTenantId(),order.getStoreId(), deliveryChannelMap);
            boolean isDapDelivery = deliveryChannelWrapper.checkChannel(deliveryDetail.deliveryChannelCode, AggDeliveryPlatformEnum.DAP_DELIVERY.getCode(), order.getTenantId(),order.getStoreId(), deliveryChannelMap);
            if (isDapDelivery || isMaltFarm){
                order.setDeliveryPlatformCode(isMaltFarm ? AggDeliveryPlatformEnum.MALT_FARM.getCode() : AggDeliveryPlatformEnum.DAP_DELIVERY.getCode());
                if (order.getRealDistributeStatus() == null || order.getRealDistributeStatus() <= TmsDeliveryStatusDescEnum.INIT.getCode() ||
                        // 针对麦芽田，配送拒单，也不展示链接
                        order.getRealDistributeStatus() == TmsDeliveryStatusDescEnum.DELIVERY_REJECTED.getCode() ||
                        // 麦芽田转自配送后，不展示链接
                        (isMaltFarm && deliveryDetail.deliveryChannelCode == DeliveryChannelEnum.FARM_DELIVERY_MERCHANT.getCode())) {
                    DeliveryRedirectModuleVo deliveryRedirectModuleVo = new DeliveryRedirectModuleVo();
                    deliveryRedirectModuleVo.setTitle("暂无配送状态");
                    deliveryRedirectModuleVo.setShowButton(false);
                    order.setDeliveryRedirectModule(deliveryRedirectModuleVo);
                    return;
                }
                AggDeliveryPlatformEnum aggDeliveryPlatformEnum = isMaltFarm ? AggDeliveryPlatformEnum.MALT_FARM : AggDeliveryPlatformEnum.DAP_DELIVERY;
                DeliveryRedirectModuleVo deliveryRedirectModuleVo = aggDeliveryPlatformEnum.fillDeliveryRedirectModuleWithoutUrl(
                        Objects.nonNull(order.getDeliveryExceptionType()) && !Objects.equals(order.getDeliveryExceptionType(), DELIVERY_NO_EXCEPTION));
                deliveryRedirectModuleVo.setShowButton(true);
                if(!Objects.equals(curStoreId, shopId)){
                    deliveryRedirectModuleVo.setUrl("");
                    deliveryRedirectModuleVo.setUrlText("");
                    deliveryRedirectModuleVo.setShowButton(false);
                }
                order.setDeliveryRedirectModule(deliveryRedirectModuleVo);
            }
        });
    }

    public void fillOrderMaltDeliveryPlatModuleWithSwitch(List<OrderVO> orderVOList, Map<Long, TDeliveryDetail> deliveryDetailMap, Map<Integer, DeliveryChannelDto> channelDtoMap, Long tenantId, Long curStoreId, boolean isPda){
        if (CollectionUtils.isEmpty(orderVOList)){
            return;
        }
        if (isPda || MccConfigUtil.orderListNotReturnDeliveryUrl(tenantId)){
            fillOrderMaltDeliveryPlatModuleWithoutUrl(orderVOList, deliveryDetailMap, channelDtoMap, curStoreId);
            return;
        }
        fillOrderMaltDeliveryPlatModule(orderVOList, deliveryDetailMap, channelDtoMap, curStoreId);
    }


    public void fillOrderMaltDeliveryPlatModule(List<OrderVO> orderVOList, Map<Long, TDeliveryDetail> deliveryDetailMap, Map<Integer, DeliveryChannelDto> channelDtoMap, Long curStoreId){
        if(CollectionUtils.isEmpty(orderVOList)) {
            return;
        }
        Long tenantId = orderVOList.get(0).getTenantId();
        List<TStoreConfig> tStoreConfigs = queryDeliveryStoreConfigByOrderList(orderVOList);
        if(CollectionUtils.isEmpty(tStoreConfigs)){
            return;
        }
        List<Integer> platformEnumList= Arrays.asList(AggDeliveryPlatformEnum.MALT_FARM.getCode(),AggDeliveryPlatformEnum.DAP_DELIVERY.getCode());
        Map<Long,Map<Integer,Map<Integer, TAggDeliveryPlatformConfig>>> tStoreConfigMap=new HashMap<>();
        for (TStoreConfig tStoreConfig : tStoreConfigs){
            List<TAggDeliveryPlatformConfig> platformConfigList=tStoreConfig.getAggPlatformConfigs();
            if(CollectionUtils.isEmpty(platformConfigList)){
                continue;
            }
            tStoreConfigMap.put(tStoreConfig.getStoreId(),toPlatformConfigMap(platformConfigList,platformEnumList));
        }

        Map<Integer, Integer> deliveryChannelMap = deliveryChannelWrapper.tratranslateToChannelIntgerMap(channelDtoMap);
        ArrayListMultimap<Long,Long> dapOrderIdMultiMap=ArrayListMultimap.create();
        ArrayListMultimap<Long,String> dapFulfillOrderIdMultiMap=ArrayListMultimap.create();
        Set<Long> poiIdList = new HashSet<>();
        for (OrderVO vo : orderVOList){
            TDeliveryDetail deliveryDetail = deliveryDetailMap.get(vo.getEmpowerOrderId());
            if(deliveryDetail==null){
                continue;
            }
            if(!deliveryChannelWrapper.checkChannel(deliveryDetail.deliveryChannelCode,AggDeliveryPlatformEnum.DAP_DELIVERY.getCode(),vo.getTenantId(),vo.getStoreId(),deliveryChannelMap)){
                continue;
            }
            if(deliveryDetail.platformSource!=null && Objects.equals(deliveryDetail.platformSource, PlatformSourceEnum.OFC.getCode()) ){
                Long shopId = vo.getStoreId();
                if(vo.getDispatchShopId()!=null){
                    shopId = vo.getDispatchShopId();
                }else if(vo.getWarehouseId()!=null){
                    shopId = vo.getWarehouseId();
                }
                poiIdList.add(shopId);
                dapFulfillOrderIdMultiMap.put(shopId,PlatformSourceEnum.OFC.toPrefixOrderId(deliveryDetail.fulfillOrderId+""));
            }else {
                if(vo.getWarehouseId()!=null){
                    dapOrderIdMultiMap.put(vo.getWarehouseId(),vo.getEmpowerOrderId());
                    poiIdList.add(vo.getWarehouseId());
                }else {
                    dapOrderIdMultiMap.put(vo.getStoreId(),vo.getEmpowerOrderId());
                    poiIdList.add(vo.getStoreId());
                }
            }
        }

        Map<String,String> urlMap=new HashMap<>();
        if(CollectionUtils.isNotEmpty(poiIdList)){
            for (Long poiId : poiIdList){
                List<Long> orderIdList=dapOrderIdMultiMap.get(poiId);
                try {
                    Map<String,String> url = tmsServiceWrapper.batchQueryOrderDeliveryUrl(tenantId,poiId,orderIdList,dapFulfillOrderIdMultiMap.get(poiId));
                    if(MapUtils.isEmpty(url)){
                        continue;
                    }
                    urlMap.putAll(url);
                }catch (Exception e){
                    log.error("获取URL失败 orderIdList:{}",orderIdList,e);
                }

            }
        }

        orderVOList.forEach(order -> {
            if(order.getStoreId() == null){
                return;
            }
            TAggDeliveryPlatformConfig tAggDeliveryPlatformConfig = null;
            Long shopId=order.getStoreId();
            if(order.getWarehouseId()!=null){
                shopId=order.getWarehouseId();
            }
            if(order.getDispatchShopId()!=null){
                shopId = order.getDispatchShopId();
            }

            TDeliveryDetail deliveryDetail = deliveryDetailMap.get(order.getEmpowerOrderId());

            if(deliveryDetail == null){
                return;
            }

            Map<Integer,Map<Integer,TAggDeliveryPlatformConfig>> platformConfigMap = tStoreConfigMap.get(shopId);
            if(MapUtils.isNotEmpty(platformConfigMap)){
                Map<Integer,TAggDeliveryPlatformConfig> configMap=new HashMap<>();
                if(platformConfigMap.containsKey(order.getChannelId())){
                    configMap=platformConfigMap.get(order.getChannelId());
                }else {
                    configMap=platformConfigMap.get(ChannelType.MEITUAN.getValue());
                }
                if(MapUtils.isNotEmpty(configMap)){
                    AggDeliveryPlatformEnum platformEnum = AggDeliveryPlatformEnum.MALT_FARM;
                    if(deliveryChannelWrapper.checkChannel(deliveryDetail.deliveryChannelCode,AggDeliveryPlatformEnum.DAP_DELIVERY.getCode(),order.getTenantId(),order.getStoreId(),deliveryChannelMap)){
                        platformEnum = AggDeliveryPlatformEnum.DAP_DELIVERY;
                    }
                    tAggDeliveryPlatformConfig = configMap.get(platformEnum.getCode());
                }
            }
            if(tAggDeliveryPlatformConfig == null){
                tAggDeliveryPlatformConfig = new TAggDeliveryPlatformConfig();
            }

            if(tAggDeliveryPlatformConfig.getPlatformCode() == null){
                return;
            }
            String oId = order.getEmpowerOrderId().toString();
            if(deliveryDetail.platformSource!=null && Objects.equals(deliveryDetail.platformSource,PlatformSourceEnum.OFC.getCode())){
                oId = PlatformSourceEnum.OFC.toPrefixOrderId(deliveryDetail.fulfillOrderId+"");
            }

            if(deliveryChannelWrapper.checkChannel(deliveryDetail.deliveryChannelCode,AggDeliveryPlatformEnum.MALT_FARM.getCode(),order.getTenantId(),order.getStoreId(),deliveryChannelMap)){
                order.setDeliveryPlatformCode(AggDeliveryPlatformEnum.MALT_FARM.getCode());
                if (order.getRealDistributeStatus() == null || order.getRealDistributeStatus() <= TmsDeliveryStatusDescEnum.INIT.getCode() ||
                        // 针对麦芽田，配送拒单，也不展示链接
                        order.getRealDistributeStatus() == TmsDeliveryStatusDescEnum.DELIVERY_REJECTED.getCode() ||
                        // 麦芽田转自配送后，不展示链接
                        deliveryDetail.deliveryChannelCode == DeliveryChannelEnum.FARM_DELIVERY_MERCHANT.getCode()) {
                    DeliveryRedirectModuleVo deliveryRedirectModuleVo = new DeliveryRedirectModuleVo();
                    deliveryRedirectModuleVo.setTitle("暂无配送状态");
                    deliveryRedirectModuleVo.setShowButton(false);
                    order.setDeliveryRedirectModule(deliveryRedirectModuleVo);
                    return;
                }
                DeliveryRedirectModuleVo deliveryRedirectModuleVo = AggDeliveryPlatformEnum.MALT_FARM.fillDeliveryRedirectModule(tAggDeliveryPlatformConfig,
                        oId, shopId,
                        Objects.nonNull(order.getDeliveryExceptionType()) && !Objects.equals(order.getDeliveryExceptionType(), DELIVERY_NO_EXCEPTION));
                if(!Objects.equals(curStoreId,shopId)){
                    deliveryRedirectModuleVo.setUrl("");
                    deliveryRedirectModuleVo.setUrlText("");
                    deliveryRedirectModuleVo.setShowButton(false);
                }
                order.setDeliveryRedirectModule(deliveryRedirectModuleVo);
            }else if(deliveryChannelWrapper.checkChannel(deliveryDetail.deliveryChannelCode,AggDeliveryPlatformEnum.DAP_DELIVERY.getCode(),order.getTenantId(),order.getStoreId(),deliveryChannelMap)){
                order.setDeliveryPlatformCode(AggDeliveryPlatformEnum.DAP_DELIVERY.getCode());
                if (order.getRealDistributeStatus() == null || order.getRealDistributeStatus() <= TmsDeliveryStatusDescEnum.INIT.getCode() ||
                        //配送拒单，也不展示链接
                        order.getRealDistributeStatus() == TmsDeliveryStatusDescEnum.DELIVERY_REJECTED.getCode()) {
                    DeliveryRedirectModuleVo deliveryRedirectModuleVo = new DeliveryRedirectModuleVo();
                    deliveryRedirectModuleVo.setTitle("暂无配送状态");
                    deliveryRedirectModuleVo.setShowButton(false);
                    order.setDeliveryRedirectModule(deliveryRedirectModuleVo);
                    return;
                }
                String url=urlMap.get(oId);
                DeliveryRedirectModuleVo deliveryRedirectModuleVo = AggDeliveryPlatformEnum.DAP_DELIVERY.fillDeliveryRedirectModule(url,
                        Objects.nonNull(order.getDeliveryExceptionType()) && !Objects.equals(order.getDeliveryExceptionType(), DELIVERY_NO_EXCEPTION));
                deliveryRedirectModuleVo.setShowButton(true);
                if(!Objects.equals(curStoreId,shopId)){
                    deliveryRedirectModuleVo.setUrl("");
                    deliveryRedirectModuleVo.setUrlText("");
                    deliveryRedirectModuleVo.setShowButton(false);
                }
                order.setDeliveryRedirectModule(deliveryRedirectModuleVo);
            }
        });
    }

    private List<TStoreConfig> queryDeliveryStoreConfigByOrderList(List<OrderVO> orderVOList){
        if(CollectionUtils.isEmpty(orderVOList)){
            return Collections.emptyList();
        }
        Long tenantId = orderVOList.get(0).getTenantId();
        Set<Long> storeIdSet = new HashSet<>();
        for (OrderVO vo : orderVOList){
            if(vo.getDispatchShopId()!=null){
                storeIdSet.add(vo.getDispatchShopId());
            }else if(vo.getWarehouseId()!=null){
                storeIdSet.add(vo.getWarehouseId());
            }else {
                storeIdSet.add(vo.getStoreId());
            }
        }
        if(tenantId <= 0 || CollectionUtils.isEmpty(storeIdSet)){
            return Collections.emptyList();
        }
        BatchStoreConfigQueryResponse batchStoreConfigQueryResponse = tmsServiceWrapper.batchDeliveryStoreConfigSearch(tenantId, new ArrayList<>(storeIdSet));
        if(batchStoreConfigQueryResponse == null || batchStoreConfigQueryResponse.getStatus() == null
                || batchStoreConfigQueryResponse.getStatus().getCode() != 0){
            return Collections.emptyList();
        }
        List<TStoreConfig> tStoreConfigs = batchStoreConfigQueryResponse.getTStoreConfigs();
        if(CollectionUtils.isEmpty(tStoreConfigs)){
            return Collections.emptyList();
        }
        return tStoreConfigs;
    }

    private Map<Integer,Map<Integer,TAggDeliveryPlatformConfig>> toPlatformConfigMap(List<TAggDeliveryPlatformConfig> aggPlatformConfigs, List<Integer> platformList){
        Map<Integer,Map<Integer,TAggDeliveryPlatformConfig>> platformConfigMap=new HashMap<>();
        ArrayListMultimap<Integer,TAggDeliveryPlatformConfig> platformChannelMultiMap=ArrayListMultimap.create();
        for (TAggDeliveryPlatformConfig config : aggPlatformConfigs){
            if(!platformList.contains(config.getPlatformCode())){
                continue;
            }
            platformChannelMultiMap.put(config.getChannelType()==null ? com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelType.MEITUAN.getValue():config.getChannelType(),config);
        }
        if(platformChannelMultiMap.isEmpty()){
            return null;
        }
        for (Integer channelType : platformChannelMultiMap.keySet()){
            List<TAggDeliveryPlatformConfig> configs=platformChannelMultiMap.get(channelType);
            if(CollectionUtils.isEmpty(configs)){
                continue;
            }
            Map<Integer,TAggDeliveryPlatformConfig> configMap=new HashMap<>();
            for (TAggDeliveryPlatformConfig config : configs){
                Integer platformId=AggDeliveryPlatformEnum.MALT_FARM.getCode();
                if(config.getPlatformCode()!=null){
                    platformId = config.getPlatformCode();
                }
                configMap.put(platformId,config);
            }
            platformConfigMap.put(channelType,configMap);
        }
        return platformConfigMap;
    }
    public Map<String, DeliveryExceptionSummaryVO> queryDeliveryExceptionVo(List<? extends OCMSOrderVO> ocmsOrderVOList) {

        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        Long tenantId = identityInfo.getUser().getTenantId();

        if (!isDrunkHorseTenant(tenantId) || CollectionUtils.isEmpty(ocmsOrderVOList)){
            return new HashMap<>();
        }

        return ocmsOrderVOList.stream()
                .collect(Collectors.groupingBy(OCMSOrderVO::getShopId))
                .entrySet().stream()
                .map(entry->{
                    long shopId = entry.getKey();
                    return riderDeliveryRemoteService.queryRiderReportException(tenantId, shopId,
                            entry.getValue().stream().map(order -> Pair.of(order.getViewOrderId(), order.getOrderBizType()))
                                    .collect(Collectors.toList()));
                })
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .collect(Collectors.toMap(DeliveryExceptionSummaryVO::getChannelOrderId, java.util.function.Function.identity(), (f,s) -> f));
    }

    private boolean isDrunkHorseTenant(Long tenantId) {
        List<Long> drunkHorseTenants = Lion.getConfigRepository().getList("drunkhorse.tenantIds", Long.class, new ArrayList<>());
        if (tenantId == null || CollectionUtils.isEmpty(drunkHorseTenants)) {
            return false;
        }
        return drunkHorseTenants.contains(tenantId);
    }

    public void fillDeliveryOperateItem(Map<Long, TDeliveryDetail> deliveryDetailMap,List<OrderVO> orderList,Long curStoreId){
        if(CollectionUtils.isEmpty(orderList)){
            return;
        }
        Long tenantId = orderList.get(0).getTenantId();
        ArrayListMultimap<Long,OrderVO> orderMultimap = ArrayListMultimap.create();
        for (OrderVO vo : orderList){
            if(vo.getDispatchShopId()!=null){
                orderMultimap.put(vo.getDispatchShopId(),vo);
            }else if(vo.getWarehouseId()!=null){
                orderMultimap.put(vo.getWarehouseId(),vo);
            }else {
                orderMultimap.put(vo.getStoreId(),vo);
            }
            TDeliveryDetail tDeliveryDetail = deliveryDetailMap.get(vo.getEmpowerOrderId());
            if(tDeliveryDetail==null){
                if(vo.getOriginalDistributeType()!=null && vo.getOriginalDistributeType() != 25){
                    vo.setPlatformDelivery(true);
                }
            }else if(tDeliveryDetail.deliveryEntity != null && tDeliveryDetail.deliveryEntity == 0){
                vo.setPlatformDelivery(true);
            }
        }

        for (Long storeId : orderMultimap.keySet()){
            List<OrderVO> orderVOList = orderMultimap.get(storeId);
            if(CollectionUtils.isEmpty(orderVOList)){
                continue;
            }
            if(!Objects.equals(storeId,curStoreId)){
                continue;
            }
            Map<Long, DeliveryOperateItem> operateItemMap = tmsServiceWrapper.queryDeliveryOperateItem(tenantId,storeId,orderVOList.stream().map(OrderVO::getEmpowerOrderId).collect(Collectors.toList()));
            for (OrderVO orderVO : orderVOList){
                List<Integer> operateList = new ArrayList<>();
                if(!operateItemMap.containsKey(orderVO.getEmpowerOrderId())){
                    continue;
                }
                DeliveryOperateItem item = operateItemMap.get(orderVO.getEmpowerOrderId());
                if(item == null || CollectionUtils.isEmpty(item.getOperateItemList())){
                    continue;
                }
                List<DeliveryOperateItemEnum> itemEnumList = DeliveryOperateItemEnum.tmsItemListToOperateItemList(item.getOperateItemList());
                if(CollectionUtils.isEmpty(itemEnumList)){
                    continue;
                }
                if(itemEnumList.contains(DeliveryOperateItemEnum.DELIVERY_TO_SELF)){
                    operateList.add(DeliveryOperateItemEnum.DELIVERY_TO_SELF.type);
                }
                if(itemEnumList.contains(DeliveryOperateItemEnum.DELIVERY_TO_MALTFARM)){
                    operateList.add(DeliveryOperateItemEnum.DELIVERY_TO_MALTFARM.type);
                }
                if(itemEnumList.contains(DeliveryOperateItemEnum.DELIVERY_TO_DAP)){
                    operateList.add(DeliveryOperateItemEnum.DELIVERY_TO_DAP.type);
                }
                if (itemEnumList.contains(DeliveryOperateItemEnum.DOUYIN_RECALL_DELIVERY)) {
                    operateList.add(DeliveryOperateItemEnum.DOUYIN_RECALL_DELIVERY.type);
                }
                if (itemEnumList.contains(DeliveryOperateItemEnum.DOUYIN_EXCEPTION_RECALL_DELIVERY)) {
                    operateList.add(DeliveryOperateItemEnum.DOUYIN_EXCEPTION_RECALL_DELIVERY.type);
                }
                if (itemEnumList.contains(DeliveryOperateItemEnum.RECALL_DELIVERY)) {
                    operateList.add(DeliveryOperateItemEnum.RECALL_DELIVERY.type);
                }
                orderVO.setDeliveryOperateItems(operateList);
            }
        }


    }

    public void buildDeliveryCount(OrderListResponse orderListResponse){
        if(orderListResponse==null || CollectionUtils.isEmpty(orderListResponse.getOrderList())){
            return;
        }
        List<Long> orderList= new ArrayList<>(new HashSet<>(com.google.common.collect.Lists.transform(orderListResponse.getOrderList(), new Function<OrderVO, Long>() {
            @Override
            public Long apply(OrderVO orderVO) {
                return orderVO.getEmpowerOrderId();
            }
        })));

        try {
            List<TDeliveryOrder> deliveryOrderList=tmsServiceWrapper.queryActiveDeliveryInfoByOrderIds(orderList);
            if(CollectionUtils.isEmpty(deliveryOrderList)){
                return;
            }
            Map<Long,TDeliveryOrder> orderMap=new HashMap<>();
            for (TDeliveryOrder order : deliveryOrderList){
                orderMap.put(order.getOrderId(),order);
            }
            for (OrderVO vo : orderListResponse.getOrderList()){
                TDeliveryOrder tDeliveryOrder=orderMap.get(vo.getEmpowerOrderId());
                if(tDeliveryOrder==null || tDeliveryOrder.getDeliveryCount()==null){
                    vo.setDeliveryCount(1);
                }else {
                    vo.setDeliveryCount(tDeliveryOrder.getDeliveryCount());
                }
            }
        }catch (Exception e){
            log.error("buildDeliveryCount error orderList:{}",orderList,e);
        }

    }
}
