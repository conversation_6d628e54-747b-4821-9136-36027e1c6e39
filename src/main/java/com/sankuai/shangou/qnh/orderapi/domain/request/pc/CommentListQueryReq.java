package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.order.management.client.dto.request.OrderRequest;
import com.meituan.shangou.saas.order.management.client.dto.request.OrderSearchRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.CommentReplyStatusEnum;
import com.sankuai.meituan.shangou.saas.common.datetime.DateUtil;

import com.sankuai.shangou.qnh.orderapi.constant.pc.Constants;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import com.sankuai.shangou.qnh.orderapi.exception.pc.ParamInvalidException;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.CommentListQueryBO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@TypeDoc(
        description = "评价列表查询请求参数",
        authors = "hejunliang"
)
@Data
@ApiModel("评价列表查询请求参数")
public class CommentListQueryReq {

    @FieldDoc(
            description = "门店id列表"
    )
    private List<Long> poiIds;

    @FieldDoc(
            description = "渠道id列表"
    )
    private List<Integer> channelIds;

    @FieldDoc(
            description = "开始时间",
            rule = "yyyy-MM-dd",
            requiredness = Requiredness.REQUIRED
    )
    private String startTime;

    @FieldDoc(
            description = "结束时间",
            rule = "yyyy-MM-dd",
            requiredness = Requiredness.REQUIRED
    )
    private String endTime;

    @FieldDoc(
            description = "评价级别",
            rule = "GOOD_COMMENT:好评, COMMON_COMMENT:中评, BAD_COMMENT:差评"
    )
    private String commentLevel;

    @FieldDoc(
            description = "回复状态",
            rule = "0:未回复, 1:已回复, 不传值代表全部"
    )
    private Integer replyStatusForPage;

    @FieldDoc(
            description = "评价内容类型，EXISTS:有内容, NOT_EXISTS:无内容, 不传值标识传布内容"
    )
    private String commentContentType;

    @FieldDoc(
            description = "页码", requiredness = Requiredness.REQUIRED
    )
    private Integer page;

    @FieldDoc(
            description = "每页记录数", requiredness = Requiredness.REQUIRED
    )
    private Integer pageSize;

    @FieldDoc(
            description = "渠道订单编号"
    )
    private String channelOrderId;

    @FieldDoc(
            description = "评价状态",
            rule = "0:删除, 1:正常, 不传值代表全部"
    )
    private Integer isValid;

    @FieldDoc(
            description = "是否导出系统匹配订单",
            rule = "false:否, true:是, 不传值代表否"
    )
    private Boolean exportMatchOrder;

    @FieldDoc(
            description = "订单号列表"
    )
    private List<String> matchChannelOrderIds;

    public void selfCheck(boolean exportComment) {
        if (StringUtils.isEmpty(this.startTime)) {
            throw new ParamInvalidException("开始日期不能为空");
        }
        Date startDate = DateUtil.parse(this.startTime, DateUtil.YYYY_MM_DD);
        if (startDate == null) {
            throw new ParamInvalidException("开始日期格式错误");
        }
        if (StringUtils.isEmpty(this.endTime)) {
            throw new ParamInvalidException("结束日期不能为空");
        }
        Date endDate = DateUtil.parse(this.endTime, DateUtil.YYYY_MM_DD);
        if (endDate == null) {
            throw new ParamInvalidException("结束日期格式错误");
        }
        if (endDate.compareTo(startDate) < 0) {
            throw new ParamInvalidException("结束日期不能小于开始日期");
        }
        if ((endDate.getTime() - startDate.getTime()) / Constants.Common.MILLISECOND_ONE_DAY  >
                Constants.Comment.COMMENT_LIST_QUERY_COMMENT_TIME_INTERVAL_DAY_MAX - 1) {
            throw new ParamInvalidException(String.format("最多只能查询%s天的数据",
                    Constants.Comment.COMMENT_LIST_QUERY_COMMENT_TIME_INTERVAL_DAY_MAX));
        }
        if (!exportComment) {
            if (this.page == null || this.page.compareTo(0) <= 0) {
                throw new ParamInvalidException("页码不能为空且必须大于0");
            }
            if (this.pageSize == null || this.pageSize.compareTo(0) <= 0) {
                throw new ParamInvalidException("每页记录条数不能为空且必须大于0");
            }
            if (this.pageSize.compareTo(Constants.Comment.COMMENT_LIST_PAGE_SIZE_MAX) > 0) {
                throw new ParamInvalidException(String.format("一页最多查询%s条记录",
                        Constants.Comment.COMMENT_LIST_PAGE_SIZE_MAX));
            }
        }
    }

    public CommentListQueryBO convertToCommentListQueryBO() {
        CommentListQueryBO queryBO = new CommentListQueryBO();
        queryBO.setTenantId(ContextHolder.currentUserTenantId());
        queryBO.setPoiIds(this.poiIds);
        queryBO.setChannelIds(this.channelIds);
        queryBO.setStartTime(StringUtils.isNotEmpty(this.startTime) ? DateUtil.getBeginOfDay(
                DateUtil.toLocalDateTime(DateUtil.parse(this.startTime, DateUtil.YYYY_MM_DD))) : null);
        queryBO.setEndTime(StringUtils.isNotEmpty(this.endTime) ? DateUtil.getEndOfDay(
                DateUtil.toLocalDateTime(DateUtil.parse(this.endTime, DateUtil.YYYY_MM_DD))) : null);
        queryBO.setReplyStatusList(getReplyStatusList(this.replyStatusForPage));
        queryBO.setCommentLevel(this.commentLevel);
        queryBO.setCommentContentType(this.commentContentType);
        queryBO.setPage(this.page);
        queryBO.setPageSize(this.pageSize);
        queryBO.setChannelOrderId(this.channelOrderId);
        queryBO.setIsValid(this.isValid);
        return queryBO;
    }

    private List<String> getReplyStatusList(Integer replyStatusForPage) {
        if (Constants.Comment.REPLY_STATUS_NOT_REPLY.equals(replyStatusForPage)) {
            return Arrays.asList(CommentReplyStatusEnum.NOT_REPLY.name(), CommentReplyStatusEnum.REPLY_FAILED.name());
        } else if (Constants.Comment.REPLY_STATUS_REPLIED.equals(replyStatusForPage)) {
            return Arrays.asList(CommentReplyStatusEnum.REPLYING.name(), CommentReplyStatusEnum.REPLIED.name());
        } else {
            return null;
        }
    }

    public OrderSearchRequest convertToOrderSearchRequest() {
        OrderSearchRequest request = new OrderSearchRequest();
        request.setTenantId(ContextHolder.currentUserTenantId());
        List<OrderRequest> orderRequestList = new ArrayList<>();
        this.matchChannelOrderIds.forEach(channelOrderId -> {
            OrderRequest orderRequest = new OrderRequest();
            orderRequest.setViewOrderId(channelOrderId);
            orderRequestList.add(orderRequest);
        });
        request.setOrderRequestList(orderRequestList);
        return request;
    }
}
