package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@TypeDoc(
        description = "商品信息查询请求"
)
@Data
@ApiModel("商品信息查询请求")
public class QueryQuoteSkuInfoRequest {

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称", required = true)
    @NotNull
    private String skuName;

    @FieldDoc(
            description = "门店id列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店id列表", required = true)
    @NotNull
    private String storeId;

    @FieldDoc(
            description = "查询第几页", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "查询第几页", required = true)
    private Integer pageNum;

    @FieldDoc(
            description = "每页查询数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "每页查询数量", required = true)
    private Integer pageSize;
}
