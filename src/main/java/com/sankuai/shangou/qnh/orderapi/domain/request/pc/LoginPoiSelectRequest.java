package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/5/6
 */
@TypeDoc(
        name = "登录后 POI 选择请求",
        description = "登录后 POI 选择请求"
)
@Data
@ToString
public class LoginPoiSelectRequest {

    @FieldDoc(description = "权限应用ID, 默认从 cookie 中获取，获取不到时取此参数兜底", requiredness = Requiredness.OPTIONAL)
    private Integer authAppId;

}
