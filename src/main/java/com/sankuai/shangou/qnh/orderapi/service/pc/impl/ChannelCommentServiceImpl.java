package com.sankuai.shangou.qnh.orderapi.service.pc.impl;

import com.github.pagehelper.Page;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelCommentLabelEnum;
import com.sankuai.meituan.shangou.saas.common.data.PageResult;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.*;
import com.sankuai.shangou.qnh.orderapi.domain.dto.pc.ChannelCommentReplyTemplateListDTO;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.QueryBadCommentProcessedReq;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.CommonResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.ManualCheckDeleteCommentVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.ManualCheckUpdateCommentVo;
import com.sankuai.shangou.qnh.orderapi.enums.pc.ChannelCommentStatEnum;
import com.sankuai.shangou.qnh.orderapi.remote.ChannelCommentRemoteService;
import com.sankuai.shangou.qnh.orderapi.service.pc.ChannelCommentService;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;
import com.sankuai.sgfulfillment.comment.thrift.dto.CommentListQueryRequest;
import com.sankuai.sgfulfillment.comment.thrift.dto.model.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 评价服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ChannelCommentServiceImpl implements ChannelCommentService {

    @Resource
    private ChannelCommentRemoteService channelCommentClient;

    /**
     * eapi中只需要未回复差评与未回复评价数量
     */
    private static final List<String> apiNeed = Lists.newArrayList(ChannelCommentLabelEnum.NOT_REPLY_BAD_COMMENT_COUNT.name(),
            ChannelCommentLabelEnum.NOT_REPLY_COMMENT_COUNT.name(), ChannelCommentStatEnum.TOTAL_COMMENT_COUNT.name(),
            ChannelCommentStatEnum.GOOD_COMMENT_COUNT.name(), ChannelCommentStatEnum.BAD_COMMENT_COUNT.name());

    @Override
    public void deleteCommentReplyTemplate(Long tenantId, Long templateId, Long operatorUid) {
        channelCommentClient.deleteCommentReplyTemplate(tenantId, templateId, operatorUid);
    }

    @Override
    public CommentReplyTemplateListBO queryCommentReplyTemplateList(Long tenantId) {

        ChannelCommentReplyTemplateListDTO commentReplyTemplateListDTO =
                channelCommentClient.queryCommentReplyTemplateList(tenantId);

        List<CommentReplyTemplateBO> commentReplyTemplateBOList = ConverterUtils.convertList(
                commentReplyTemplateListDTO.getCommentReplyTemplateDTOList(), CommentReplyTemplateBO::build);

        return new CommentReplyTemplateListBO(commentReplyTemplateBOList,
                commentReplyTemplateListDTO.getCommentReplyTemplateCountMax());
    }

    @Override
    public PageResult<CommentBO> queryCommentList(CommentListQueryBO queryBO) {
        CommentListQueryRequest request = queryBO.convertToCommentListQueryRequest();
        PageResult<ChannelCommentDTO> dtoPageResult = channelCommentClient.queryCommentList(request);
        return new PageResult<>(ConverterUtils.convertList(dtoPageResult.getList(), CommentBO::build),
                dtoPageResult.getPage(), dtoPageResult.getPageSize(), dtoPageResult.getTotal());
    }

    @Override
    public CommentBO getCommentByTenantIdAndCommentId(Long tenantId, String commentId) {
        ChannelCommentDTO commentDTO = channelCommentClient.getCommentByTenantIdAndCommentId(tenantId, commentId);
        return CommentBO.build(commentDTO);
    }

    @Override
    public List<CommentStatBO> queryCommentStat(Long tenantId, List<Integer> channelIds, List<Long> storeIds, String startTime, String endTime) {
        List<ChannelCommentLabelStatDTO> channelCommentLabelStatDTOS = channelCommentClient.queryCommentStat(tenantId, channelIds, storeIds, startTime, endTime);
        return channelCommentLabelStatDTOS.stream()
                .filter(dto -> apiNeed.contains(dto.getCommentLabel()))
                .map(CommentStatBO::new).collect(Collectors.toList());
    }

    @Override
    public List<CommentChannelStatBO> queryCommentChannelStat(Long tenantId, List<Integer> channelIds, List<Long> storeIds, String startTime, String endTime) {
        List<ChannelCommentStatChannelDTO> channelCommentStatChannelDTOS = channelCommentClient.queryCommentChannelStat(tenantId, channelIds, storeIds, startTime, endTime);
        List<CommentChannelStatBO> commentChannelStatBOS = channelCommentStatChannelDTOS.stream()
                .map(channelCommentStatChannelDTO -> new CommentChannelStatBO(channelCommentStatChannelDTO, apiNeed)).collect(Collectors.toList());
        return commentChannelStatBOS;
    }

    @Override
    public Page<CommentStoreStatBO> queryCommentStoreStat(Long tenantId, List<Integer> channelIds, List<Long> storeIds, String startTime, String endTime, Integer page, Integer pageSize) {
        Page<ChannelCommentStatStoreDTO> dtoPage = channelCommentClient.queryCommentStoreStat(tenantId, channelIds, storeIds, startTime, endTime, page, pageSize);
        Page<CommentStoreStatBO> pageData = new Page<>(page, pageSize);
        pageData.setTotal(dtoPage.getTotal());
        pageData.addAll(dtoPage.stream().map(CommentStoreStatBO::new).collect(Collectors.toList()));
        return pageData;
    }

    @Override
    public void replyComment(Long tenantId, Long uid, String commentId, String replyDraft) {
        channelCommentClient.reply(tenantId, uid, commentId, replyDraft);
    }

    @Override
    public CommentRuleBO queryCommentRule(Long tenantId) {
        List<String> commentLevelRules = channelCommentClient.queryCommentLevelRules(tenantId);
        return new CommentRuleBO(commentLevelRules);
    }

    public void addRecordListenNum(String recordId) {
        channelCommentClient.addRecordListenNum(recordId);
    }

    public PageResult<BadProcessedInfoBO> queryBadCommentProcessedList(QueryBadCommentProcessedReq req) {
        PageResult<BadProcessedInfoDTO> badProcessedInfoDTOPageResult = channelCommentClient.queryBadCommentProcessedList(req.convertToThrift());
        return new PageResult<>(
                ConverterUtils.convertList(badProcessedInfoDTOPageResult.getList(), BadProcessedInfoBO::convertToBO),
                badProcessedInfoDTOPageResult.getPage(),
                badProcessedInfoDTOPageResult.getPageSize(),
                badProcessedInfoDTOPageResult.getTotal()
        );
    }

    @Override
    public Long addCommentReplyTemplate(StoreCommentReplyTemplateBO templateBO) {

        return channelCommentClient.addCommentReplyTemplate(templateBO);
    }

    @Override
    public void updateCommentReplyTemplate(StoreCommentReplyTemplateBO templateBO) {

        channelCommentClient.updateCommentReplyTemplate(templateBO);
    }

    @Override
    public void commentAssociationChannelOrderId(Long tenantId, Long uid, String commentId, String associationChannelOrderId) {
        CommonResponse commonResponse = channelCommentClient.commentAssociationChannelOrderId(tenantId, uid, commentId,
                associationChannelOrderId);
        if (commonResponse.getCode() != 0) {
            throw new BizException(commonResponse.getMessage());
        }
    }

    @Override
    public ManualCheckUpdateCommentVo manualCheckUpdateComment(String commentId, Long tenantId) {
        ManualCheckUpdateCommentVo updateCommentVo = new ManualCheckUpdateCommentVo();
        CommonResponse<ManualCheckUpdateCommentVo> commonResponse = channelCommentClient.manualCheckUpdateComment(commentId, tenantId);
        if (commonResponse.getCode() != 0) {
            throw new BizException(commonResponse.getMessage());
        }
        return commonResponse.getData();
    }

    @Override
    public ManualCheckDeleteCommentVo manualCheckDeleteComment(String commentId, Long tenantId) {
        CommonResponse<ManualCheckDeleteCommentVo> commonResponse = channelCommentClient.manualCheckDeleteComment(commentId, tenantId);
        if (commonResponse.getCode() != 0) {
            throw new BizException(commonResponse.getMessage());
        }
        if(Objects.isNull(commonResponse.getData())){
            throw new BizException("处理结果异常");
        }
        return commonResponse.getData();
    }

    @Override
    public void confirmDeleteComment(String commentId, Long tenantId, Long operatorId) {
        CommonResponse<Void> commonResponse = channelCommentClient.confirmDeleteComment(commentId, tenantId, operatorId);
        if (commonResponse.getCode() != 0) {
            throw new BizException(commonResponse.getMessage());
        }
    }
}
