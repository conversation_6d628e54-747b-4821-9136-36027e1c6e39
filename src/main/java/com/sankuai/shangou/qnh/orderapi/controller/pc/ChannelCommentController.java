package com.sankuai.shangou.qnh.orderapi.controller.pc;

import com.github.pagehelper.Page;
import com.google.common.base.Preconditions;
import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelType;
import com.sankuai.meituan.shangou.saas.common.data.PageResult;
import com.sankuai.meituan.shangou.saas.common.datetime.DateUtil;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.shangou.saas.common.file.ExcelUtil;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.meituan.shangou.saas.common.storage.StorageService;
import com.sankuai.shangou.qnh.orderapi.constant.pc.Constants;
import com.sankuai.shangou.qnh.orderapi.constant.pc.Constants.ExcelExportTitles;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import com.sankuai.shangou.qnh.orderapi.controller.BaseController;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.*;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.*;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.CommentExportResp;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.CommentReplyTemplateAddResp;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.CommentReplyTemplateListQueryResp;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.CommentStoreStatExportResp;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.PoiResp;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.PageResultV2;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.Result;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.ResultBuilder;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.*;
import com.sankuai.shangou.qnh.orderapi.exception.pc.ParamInvalidException;
import com.sankuai.shangou.qnh.orderapi.remote.AuthRemoteService;
import com.sankuai.shangou.qnh.orderapi.remote.OCMSOrderRemoteService;
import com.sankuai.shangou.qnh.orderapi.remote.PoiRemoteService;
import com.sankuai.shangou.qnh.orderapi.service.pc.ChannelCommentService;
import com.sankuai.shangou.qnh.orderapi.service.pc.TenantService;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import com.sankuai.shangou.qnh.orderapi.utils.pc.LionUtils;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 中台评价Controller
 *
 * <AUTHOR>
 */
@InterfaceDoc(
        displayName = "中台评价接口",
        type = "restful",
        scenarios = "查询评价列表, 回复评价, 新增、删除、查询评价回复模板",
        description = "查询评价列表, 回复评价, 新增、删除、查询评价回复模板"
)
@Api(value = "中台评价接口")
@Slf4j
@RestController
@RequestMapping("/api/v1/channelComment")
public class ChannelCommentController {

    @Resource
    private PoiRemoteService poiRemoteService;

    @Resource
    private StorageService s3StorageService;

    @Resource
    private ChannelCommentService channelCommentService;

    @Autowired
    private AuthRemoteService authRemoteService;

    @Autowired
    private TenantService tenantService;
    @Resource
    private OCMSOrderRemoteService ocmsOrderRemoteService;

    @MethodDoc(
            displayName = "添加评价回复模板",
            description = "添加评价回复模板",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "查询指定渠道下品牌信息请求"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    )
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/api/v1/channelComment/addCommentReplyTemplate",
            restExamplePostData = "{\"templateContent\":\" 评价模板测试\"}",
            restExampleResponseData = "{\"code\":0,\"data\":{\"templateId\":101},\"msg\":\"成功\"}"
    )
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @RequestMapping(value = "/addCommentReplyTemplate", method = RequestMethod.POST)
    public Result<CommentReplyTemplateAddResp> addCommentReplyTemplate(@RequestBody CommentReplyTemplateAddReq req) {
        req.selfCheck();
        if (checkOpenELEMChannel() && req.getTemplateContent().length() > 150) {
            return ResultBuilder.buildFailResult("饿了么仅允许回复150个字符，请检查您的模板");
        }
        List<Long> storeIds = Optional.ofNullable(req.getStoreIds())
                .orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        StoreCommentReplyTemplateBO buildBO = StoreCommentReplyTemplateBO.builder()
                .tenantId(ContextHolder.currentUserTenantId())
                .templateContent(req.getTemplateContent())
                .operatorUid(ContextHolder.currentUid())
                .storeIdList(storeIds)
                .build();
        Long templateId = channelCommentService.addCommentReplyTemplate(buildBO);
        return ResultBuilder.buildSuccess(new CommentReplyTemplateAddResp(templateId));
    }



    @MethodDoc(
            displayName = "更新评论回复模板",
            description = "更新评论回复模板",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "更新评论回复模板请求参数"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    )
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/api/v1/channelComment/updateCommentReplyTemplate",
            restExamplePostData = "{\"templateId\":100,\"templateContent\":\"和这个有点意思\"}",
            restExampleResponseData = "{\"code\":0,\"msg\":\"成功\"}"
    )
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @RequestMapping(value = "/updateCommentReplyTemplate", method = RequestMethod.POST)
    public Result updateCommentReplyTemplate(@RequestBody CommentReplyTemplateUpdateReq req) {
        if (checkOpenELEMChannel() && req.getTemplateContent().length() > 150) {
            return ResultBuilder.buildFailResult("饿了么仅允许回复150个字符，请检查您的模板");
        }
        List<Long> storeIds = Optional.ofNullable(req.getStoreIds())
                .orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        StoreCommentReplyTemplateBO buildBO = StoreCommentReplyTemplateBO.builder()
                .tenantId(ContextHolder.currentUserTenantId())
                .templateContent(req.getTemplateContent())
                .operatorUid(ContextHolder.currentUid())
                .templateId(req.getTemplateId())
                .storeIdList(storeIds)
                .build();

        channelCommentService.updateCommentReplyTemplate(buildBO);
        return ResultBuilder.buildSuccessResult();
    }


    @MethodDoc(
            displayName = "删除评价回复模板",
            description = "删除评价回复模板",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "删除评价回复模板请求参数"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    )
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/api/v1/channelComment/deleteCommentReplyTemplate",
            restExamplePostData = "{\"templateId\":100}",
            restExampleResponseData = "{\"code\":0,\"msg\":\"成功\"}"
    )
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @RequestMapping(value = "/deleteCommentReplyTemplate", method = RequestMethod.POST)
    public Result deleteCommentReplyTemplate(@RequestBody CommentReplyTemplateDeleteReq req) {
        req.selfCheck();
        channelCommentService.deleteCommentReplyTemplate(ContextHolder.currentUserTenantId(),
                req.getTemplateId(), ContextHolder.currentUid());
        return ResultBuilder.buildSuccessResult();
    }

    @MethodDoc(
            displayName = "查询评价回复模板",
            description = "查询评价回复模板",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "查询评价回复模板请求参数"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    )
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/api/v1/channelComment/queryCommentReplyTemplateList",
            restExamplePostData = " ",
            restExampleResponseData = "{\"code\":0,\"data\":{\"list\":[{\"templateContent\":\" 评价模板测试\",\"templateId\":97},{\"templateContent\":\" 评价模板测试\",\"templateId\":98},{\"templateContent\":\" 评价模板测试\",\"templateId\":99}]},\"msg\":\"成功\"}"
    )
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @RequestMapping(value = "/queryCommentReplyTemplateList", method = RequestMethod.POST)
    public Result<CommentReplyTemplateListQueryResp> queryCommentReplyTemplateList() {

        CommentReplyTemplateListBO commentReplyTemplateListBO = channelCommentService.
                queryCommentReplyTemplateList(ContextHolder.currentUserTenantId());

        List<CommentReplyTemplateVO> replyTemplateVOList = ConverterUtils.convertList(
                commentReplyTemplateListBO.getCommentReplyTemplateBOList(), CommentReplyTemplateVO::build);
        return ResultBuilder.buildSuccess(new CommentReplyTemplateListQueryResp(replyTemplateVOList,
                commentReplyTemplateListBO.getCommentReplyTemplateCountMax()));
    }


    @MethodDoc(
            displayName = "查询评价统计数据",
            description = "查询商家评价统计数据, 门店过滤",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_PARAM,
                            name = "queryReq",
                            description = "查询评价统计数据"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    )
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/api/v1/channelComment/queryCommentStat",
            restExamplePostData = "{\"endTime\":\"2019-07-09 00:00:00\",\"startTime\":\"2019-07-01 00:00:00\",\"poiIds\":[1054763]}",
            restExampleResponseData = "{\"code\":0,\"msg\":\"成功\",\"data\":{\"statisticsMap\":{\"NOT_REPLY_COMMENT_COUNT\":3,\"NOT_REPLY_BAD_COMMENT_COUNT\":1},\"channelStatistics\":{\"100\":{\"NOT_REPLY_COMMENT_COUNT\":3,\"NOT_REPLY_BAD_COMMENT_COUNT\":1},\"200\":{\"NOT_REPLY_COMMENT_COUNT\":0,\"NOT_REPLY_BAD_COMMENT_COUNT\":0},\"300\":{\"NOT_REPLY_COMMENT_COUNT\":0,\"NOT_REPLY_BAD_COMMENT_COUNT\":0}}}}"
    )
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @RequestMapping(value = "/queryCommentStat", method = RequestMethod.POST)
    public Result<Map<String, Object>> queryCommentStat(@RequestBody CommentStatQueryReq queryReq) {
        Preconditions.checkNotNull(queryReq, "请求参数不得为空");
        queryReq.isValid();
        queryReq.setChannelIds(validateAndGetChannelIds(queryReq.getChannelIds()));
        List<Long> poiIds = validateAndGetPoiIds(queryReq.getPoiIds());
        List<CommentChannelStatBO> commentChannelStatBOS = channelCommentService.queryCommentChannelStat(ContextHolder.currentUserTenantId(),
                queryReq.getChannelIds(),
                poiIds,
                DateUtil.formatLocalDateTime(DateUtil.toLocalDateTime(DateUtil.parse(queryReq.getStartTime(), DateUtil.YYYY_MM_DD)), DateUtil.YYYY_MM_DD_HH_MM_SS),
                DateUtil.formatLocalDateTime(DateUtil.getEndOfDay(DateUtil.toLocalDateTime(DateUtil.parse(queryReq.getEndTime(), DateUtil.YYYY_MM_DD))), DateUtil.YYYY_MM_DD_HH_MM_SS));
        return ResultBuilder.buildSuccess(CommentStatVO.toMap(commentChannelStatBOS));
    }

    @MethodDoc(
            displayName = "查询门店评价统计数据",
            description = "查询商家评价统计数据, 门店过滤",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_PARAM,
                            name = "queryReq",
                            description = "查询评价统计数据"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    )
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/api/v1/channelComment/queryStoreCommentStatList",
            restExamplePostData = "{\"endTime\":\"2019-07-09 00:00:00\",\"poiIds\":[1054763],\"startTime\":\"2019-07-01 00:00:00\",\"page\":1,\"pageSize\":5}",
            restExampleResponseData = "{\"code\":0,\"msg\":\"成功\",\"data\":{\"list\":[{\"poiId\":1054763,\"storeName\":\"零售扫码购测试门店-甗默忪0066（北京开发用）\",\"totalStatistics\":{\"BAD_COMMENT_COUNT\":2,\"NOT_REPLY_COMMENT_COUNT\":3,\"TOTAL_COMMENT_COUNT\":4,\"NOT_REPLY_BAD_COMMENT_COUNT\":1},\"detailStatistics\":{\"100\":{\"BAD_COMMENT_COUNT\":2,\"NOT_REPLY_COMMENT_COUNT\":3,\"TOTAL_COMMENT_COUNT\":4,\"NOT_REPLY_BAD_COMMENT_COUNT\":1}}}],\"page\":1,\"pageSize\":5,\"total\":1}}"
    )
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @RequestMapping(value = "/queryStoreCommentStatList", method = RequestMethod.POST)
    public Result<PageResultV2<CommentStoreStatVO>> queryStoreCommentStatList(@RequestBody CommentStoreStatQueryReq queryReq) {
        Preconditions.checkNotNull(queryReq, "请求参数不得为空");
        queryReq.isValid();
        List<Long> poiIds = validateAndGetPoiIds(queryReq.getPoiIds());
        queryReq.setChannelIds(validateAndGetChannelIds(queryReq.getChannelIds()));
        Page<CommentStoreStatBO> commentStoreStatBOS = channelCommentService.queryCommentStoreStat(ContextHolder.currentUserTenantId(),
                queryReq.getChannelIds(),
                poiIds,
                DateUtil.formatLocalDateTime(DateUtil.toLocalDateTime(DateUtil.parse(queryReq.getStartTime(), DateUtil.YYYY_MM_DD)), DateUtil.YYYY_MM_DD_HH_MM_SS),
                DateUtil.formatLocalDateTime(DateUtil.getEndOfDay(DateUtil.toLocalDateTime(DateUtil.parse(queryReq.getEndTime(), DateUtil.YYYY_MM_DD))), DateUtil.YYYY_MM_DD_HH_MM_SS),
                queryReq.getPage(),
                queryReq.getPageSize());
        poiIds = commentStoreStatBOS.stream().map(CommentStoreStatBO::getPoiId).collect(Collectors.toList());
        HashMap<Long, PoiResp> storeId2StoreMap = poiRemoteService.batchQuery(poiIds);

        return ResultBuilder.buildSuccess(new PageResultV2<>(
                commentStoreStatBOS.stream().map(statBO -> new CommentStoreStatVO(statBO, storeId2StoreMap, isErpTenant(), false)).collect(Collectors.toList()),
                commentStoreStatBOS.getPageNum(),
                commentStoreStatBOS.getPageSize(),
                (int) commentStoreStatBOS.getTotal()));
    }

    @MethodDoc(
            displayName = "评价回复",
            description = "根据评价id, 回复对应的评价",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_PARAM,
                            name = "queryReq",
                            description = "查询评价统计数据"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    )
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/api/v1/channelComment/queryCommentStat",
            restExamplePostData = "{\"commentId\":1,\"replyDraft\":\"感谢您给出的建议, 我们会继续努力的!\"}",
            restExampleResponseData = "{\"code\":0,\"msg\":\"成功\" }"
    )
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @RequestMapping(value = "/reply", method = RequestMethod.POST)
    public Result reply(@RequestBody CommentReplyReq request) {
        try {
            if (StringUtils.isBlank(request.getAssociationChannelOrderId())) {
                Preconditions.checkNotNull(request, "请求参数不得为空");
                request.isValid();
                channelCommentService.replyComment(ContextHolder.currentUserTenantId(), ContextHolder.currentUid(), request.getCommentId(), request.getReplyDraft());
            } else {
                // 当绑定当订单号不为空时，执行绑定逻辑
                channelCommentService.commentAssociationChannelOrderId(ContextHolder.currentUserTenantId(),
                        ContextHolder.currentUid(), request.getCommentId(), request.getAssociationChannelOrderId());
            }
        } catch (BizException | IllegalArgumentException e) {
            return ResultBuilder.buildFailResult(e.getMessage());
        }
        return ResultBuilder.buildSuccess(null);
    }

    @MethodDoc(
            displayName = "查询评价列表",
            description = "查询评价列表",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "查询评价列表请求参数",
                            type = CommentListQueryReq.class
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    )
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/api/v1/channelComment/queryCommentList",
            restExamplePostData = "{\"startTime\":\"2018-01-01\",\"endTime\":\"2019-09-01\",\"page\":1,\"pageSize\":10}",
            restExampleResponseData = "{\"code\":0,\"msg\":\"成功\",\"data\":{\"list\":[{\"channelId\":100,\"channelName\":null,\"commentId\":\"1148435858760777807\",\"commentContent\":\"包装还不错，，  \",\"commentTime\":\"2019-07-08\",\"addCommentContent\":\"\",\"addCommentTime\":null,\"commentLevel\":\"GOOD_COMMENT\",\"orderScore\":3,\"qualityScore\":null,\"packingScore\":3,\"deliveryScore\":0,\"commentPictures\":[\"http://p0.meituan.net/wmcomment/a0d2db393d577a25d437ec3700860ea17267.jpg.webp\",\"http://p0.meituan.net/wmcomment/c9cf9c254aa722766edfb4b1f3bf89ea6874.jpg.webp\"],\"deliveryCommentLabels\":[\"仪表整洁\"],\"praiseItemList\":[],\"criticItemList\":[],\"replyContent\":null,\"replyStatus\":\"NOT_REPLY\",\"replyTime\":null,\"commentReplyExpireTime\":\"2019-07-15 23:59:59\",\"canReply\":true}],\"page\":1,\"pageSize\":10,\"total\":11}}"
    )
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryCommentList", method = RequestMethod.POST)
    public Result<Object> queryCommentList(@Valid @RequestBody CommentListQueryReq req) {
        if (CollectionUtils.isEmpty(req.getMatchChannelOrderIds())) {
            req.selfCheck(false);
            // 订单评价查询状态开关,默认返回true开启，表示只查询正常状态评价,false表示根据前端传参查询
            if (LionUtils.getOrderCommentStatusConfig()) {
                req.setIsValid(1);
            }
            CommentListQueryBO queryBO = req.convertToCommentListQueryBO();
            queryBO.setPoiIds(validateAndGetPoiIds(queryBO.getPoiIds()));
            queryBO.setChannelIds(validateAndGetChannelIds(queryBO.getChannelIds()));
            PageResult<CommentBO> pageResult = channelCommentService.queryCommentList(queryBO);
            List<CommentVO> commentVOList = ConverterUtils.convertList(pageResult.getList(), CommentVO::build);
            fillPoiNameForCommentVO(commentVOList);
            return ResultBuilder.buildSuccess(new PageResult<>(commentVOList, pageResult.getPage(),
                    pageResult.getPageSize(), pageResult.getTotal()));
        } else {
            // 当传入了订单号列表时，根据订单号列表查询订单信息
            return ocmsOrderRemoteService.getOrderListByPcComment(req.convertToOrderSearchRequest(),
                    req.getMatchChannelOrderIds());
        }
    }

    @MethodDoc(
            displayName = "查询评价详情",
            description = "查询评价详情",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "查询评价详情请求参数",
                            type = CommentListQueryReq.class
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    )
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/api/v1/channelComment/getComment?commentId=1234",
            restExamplePostData = " ",
            restExampleResponseData = "{\"code\":0,\"msg\":\"成功\",\"data\":{\"channelId\":100,\"channelName\":null,\"commentId\":\"1148435858760777807\",\"commentContent\":\"包装还不错，，  \",\"commentTime\":\"2019-07-08\",\"addCommentContent\":\"\",\"addCommentTime\":null,\"commentLevel\":\"GOOD_COMMENT\",\"orderScore\":3,\"qualityScore\":null,\"packingScore\":3,\"deliveryScore\":0,\"commentPictures\":[\"http://p0.meituan.net/wmcomment/a0d2db393d577a25d437ec3700860ea17267.jpg.webp\",\"http://p0.meituan.net/wmcomment/c9cf9c254aa722766edfb4b1f3bf89ea6874.jpg.webp\"],\"deliveryCommentLabels\":[\"仪表整洁\"],\"praiseItemList\":[],\"criticItemList\":[],\"replyContent\":null,\"replyStatus\":\"NOT_REPLY\",\"replyTime\":null,\"commentReplyExpireTime\":\"2019-07-15 23:59:59\",\"canReply\":true}}"
    )
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/getComment", method = RequestMethod.GET)
    public Result<CommentVO> getComment(String commentId) {
        if (StringUtils.isEmpty(commentId)) {
            throw new ParamInvalidException("评价id不能为空");
        }
        CommentBO commentBO = channelCommentService.
                getCommentByTenantIdAndCommentId(ContextHolder.currentUserTenantId(), commentId);
        CommentVO commentVO = CommentVO.build(commentBO);
        if (commentVO != null) {
            fillPoiNameForCommentVO(Arrays.asList(commentVO));
        }
        return ResultBuilder.buildSuccess(commentVO);
    }

    @MethodDoc(
            displayName = "导出评论",
            description = "导出指定门店列表的评论",
            parameters = {
                    @ParamDoc(name = "req", description = "导出请求参数")
            },
            returnValueDescription = "导出文件下载url",
            extensions = {
                    @ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "鉴权逻辑为："),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    )
            }
    )
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/downloadComment", method = RequestMethod.POST)
    public Result<CommentExportResp> exportComment(@Valid @RequestBody CommentListQueryReq req) {
        req.selfCheck(true);

        int maxRowsLimit = LionUtils.getCommentExportMaxRowsLimit();
        int pageSize = LionUtils.getCommentExportMaxPageLimit();

        CommentListQueryBO queryBO = req.convertToCommentListQueryBO();
        queryBO.setPoiIds(validateAndGetPoiIds(queryBO.getPoiIds()));
        queryBO.setChannelIds(validateAndGetChannelIds(queryBO.getChannelIds()));
        queryBO.setPage(1);
        queryBO.setPageSize(pageSize);
        queryBO.setExportScene(true);
        PageResult<CommentBO> pageResult = channelCommentService.queryCommentList(queryBO);
        if (pageResult.getTotal() <= 0) {
            throw new BizException("暂无评价");
        }
        if (pageResult.getTotal() > maxRowsLimit) {
            throw new BizException(String.format("超出导出数据量限制，单次导出最多%s条，请筛选后再导出", maxRowsLimit));
        }

        List<CommentExportVO> commentExportVOList = new ArrayList<>(pageResult.getPage() * pageResult.getPageSize());
        for (CommentBO commentBO : pageResult.getList()) {
            commentExportVOList.add(CommentExportVO.build(commentBO));
        }

        int totalPage = pageResult.getTotal() / pageResult.getPageSize()
                + (pageResult.getTotal() % pageResult.getPageSize() == 0 ? 0 : 1);
        if (totalPage > 1) {
            for (int page = 2; page <= totalPage; page++) {
                queryBO.setPage(page);
                pageResult = channelCommentService.queryCommentList(queryBO);
                if (CollectionUtils.isNotEmpty(pageResult.getList())) {
                    for (CommentBO commentBO : pageResult.getList()) {
                        commentExportVOList.add(CommentExportVO.build(commentBO));
                    }
                }
            }
        }
        Collections.reverse(commentExportVOList);
        boolean exportMatchOrder = BooleanUtils.toBoolean(req.getExportMatchOrder());
        boolean isSuperMarket = tenantService.isSuperMarketMode(queryBO.getTenantId());
        List<HashMap<String, String>> commentExportDataList = getCommentExportDataList(commentExportVOList,isSuperMarket, exportMatchOrder);
        List<String> commentExportTitles = new ArrayList<>(ExcelExportTitles.COMMENT_EXPORT_TITLES);
        // 非超市便利不展示ERP门店编码
        if (!isSuperMarket) {
            commentExportTitles.remove("ERP门店编码");
        }
        // 非导出匹配订单不展示【订单是否系统匹配】列表
        if (!exportMatchOrder) {
            commentExportTitles.remove("订单是否系统匹配");
        }
        HSSFWorkbook workbook = ExcelUtil.exportExcelFile("评价", commentExportTitles, commentExportDataList);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            workbook.write(outputStream);
        } catch (IOException e) {
            throw new BizException("评价记录写入流失败");
        }
        ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
        String exportFileName = s3StorageService.upload("评价记录.xls", inputStream);
        String downloadUrl = s3StorageService.getTempDownloadUrl(exportFileName, true);
        return ResultBuilder.buildSuccessResult(new CommentExportResp(downloadUrl));
    }

    private void fillPoiNameForCommentVO(List<? extends CommentVO> commentVOList) {
        if (CollectionUtils.isEmpty(commentVOList)) {
            return;
        }
        List<Long> poiIdList = commentVOList.stream().filter(commentVO -> commentVO.getPoiId() != null)
                .map(CommentVO::getPoiId).distinct().collect(Collectors.toList());
        HashMap<Long, PoiResp> poiRespMap = poiRemoteService.batchQuery(poiIdList);
        for (CommentVO commentVO : commentVOList) {
            if (poiRespMap.get(commentVO.getPoiId()) != null) {
                commentVO.setPoiName(poiRespMap.get(commentVO.getPoiId()).getPoiName());
            }
        }
    }

    private void fillPoiNameForCommentExportVO(List<CommentExportVO> commentExportVOList) {
        if (CollectionUtils.isEmpty(commentExportVOList)) {
            return;
        }

        List<Long> poiIdList = commentExportVOList.stream().filter(commentExportVO -> commentExportVO.getPoiId() != null)
                .map(CommentExportVO::getPoiId).distinct().collect(Collectors.toList());
        HashMap<Long, PoiResp> poiRespMap = poiRemoteService.batchQuery(poiIdList);
        commentExportVOList.parallelStream().forEach(commentExportVO ->{
            commentExportVO.setPoiName(poiRespMap.get(commentExportVO.getPoiId()).getPoiName());
            commentExportVO.setErpShopCode(poiRespMap.get(commentExportVO.getPoiId()).getOutPoiId());});
    }

    private List<HashMap<String, String>> getCommentExportDataList(List<CommentExportVO> commentExportVOList, boolean isSuperMarket, boolean exportMatchOrder) {
        fillPoiNameForCommentExportVO(commentExportVOList);
        return commentExportVOList.parallelStream()
                .map(commentExportVO -> commentExportVO.convertToCommentExportData(isSuperMarket, exportMatchOrder))
                .collect(Collectors.toList());
    }


    @MethodDoc(
            displayName = "导出评价统计数据",
            description = "导出商家评价统计数据, 门店过滤",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_PARAM,
                            name = "queryReq",
                            description = "查询评价统计数据"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    )
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/api/v1/channelComment/queryCommentStat",
            restExamplePostData = "{\"endTime\":\"2019-07-09 00:00:00\",\"startTime\":\"2019-07-01 00:00:00\",\"poiIds\":[1054763]}",
            restExampleResponseData = "{\"code\":0,\"msg\":\"成功\",\"data\":\"http://msstest.sankuai.com/eapi/%E8%AF%84%E4%BB%B7%E6%A6%82%E5%86%B5%E5%AF%BC%E5%87%BA_20190715_1563181999687.xls?Expires=1563182325&Signature=o%2BtLFbrFikpEnP4F8GFLDvnoWoM%3D\"}"
    )
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @RequestMapping(value = "/downloadStoreCommentStat", method = RequestMethod.POST)
    public Result<CommentStoreStatExportResp> downloadStoreCommentStat(@RequestBody CommentStatQueryReq queryReq) {
        queryReq.isValid();
        List<Long> poiIds = validateAndGetPoiIds(queryReq.getPoiIds());
        queryReq.setChannelIds(validateAndGetChannelIds(queryReq.getChannelIds()));
        Page<CommentStoreStatBO> commentStoreStatBOS = channelCommentService.queryCommentStoreStat(ContextHolder.currentUserTenantId(),
                queryReq.getChannelIds(),
                poiIds,
                DateUtil.formatLocalDateTime(DateUtil.toLocalDateTime(DateUtil.parse(queryReq.getStartTime(), DateUtil.YYYY_MM_DD)), DateUtil.YYYY_MM_DD_HH_MM_SS),
                DateUtil.formatLocalDateTime(DateUtil.getEndOfDay(DateUtil.toLocalDateTime(DateUtil.parse(queryReq.getEndTime(), DateUtil.YYYY_MM_DD))), DateUtil.YYYY_MM_DD_HH_MM_SS),
                1,
                Integer.MAX_VALUE);
        poiIds = commentStoreStatBOS.stream().map(CommentStoreStatBO::getPoiId).collect(Collectors.toList());
        HashMap<Long, PoiResp> storeId2StoreMap = poiRemoteService.batchQuery(poiIds);
        List<CommentStoreStatVO> commentStoreStastVOs = commentStoreStatBOS.stream().map(statBO -> new CommentStoreStatVO(statBO, storeId2StoreMap, isErpTenant(), true)).collect(Collectors.toList());
        List<HashMap<String, String>> excelData = new ArrayList<>();
        for (int i = 0; i < commentStoreStastVOs.size(); i++) {
            CommentStoreStatVO storeStatVO = commentStoreStastVOs.get(i);
            excelData.add(storeStatVO.getExcelData(i + 1));
        }
        try {
            HSSFWorkbook storeStatExcel = BaseController.exportExcelFile("导出核心评价指标", CommentStoreStatVO.getHeaders(queryReq.getChannelIds(), isErpTenant()), excelData, false);
            String fileName = "导出核心评价指标_" + DateUtil.formatLocalDateTime(LocalDateTime.now(), DateUtil.YYYYMMDD) + "_" + DateUtil.format(DateUtil.parse(queryReq.getStartTime(), DateUtil.YYYY_MM_DD), DateUtil.YYYYMMDD) + "-" + DateUtil.format(DateUtil.parse(queryReq.getEndTime(), DateUtil.YYYY_MM_DD), DateUtil.YYYYMMDD) + ".xls";
            ByteArrayOutputStream opStream = new ByteArrayOutputStream();
            storeStatExcel.write(opStream);
            String s3FileName = s3StorageService.upload(fileName, new ByteArrayInputStream(opStream.toByteArray()));
            return ResultBuilder.buildSuccess(new CommentStoreStatExportResp(s3StorageService.getTempDownloadUrl(s3FileName, true)));
        } catch (IOException e) {
            return ResultBuilder.buildFailResult("数据转换错误");
        }
    }

    @MethodDoc(
            displayName = "查询评价规则",
            description = "查询评价规则",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "查询评价回复模板请求参数"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    )
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/api/v1/channelComment/queryCommentRule",
            restExamplePostData = " ",
            restExampleResponseData = ""
    )
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @RequestMapping(value = "/queryCommentRule", method = RequestMethod.POST)
    public Result<CommentRuleVO> queryCommentRule() {
        CommentRuleBO commentRuleBO = channelCommentService.queryCommentRule(ContextHolder.currentUserTenantId());
        return ResultBuilder.buildSuccess(CommentRuleVO.build(commentRuleBO));
    }

    @MethodDoc(
            displayName = "查询评价规则",
            description = "查询评价规则",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "查询评价回复模板请求参数"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/api/v1/channelComment/queryCommentRule",
            restExamplePostData = " ",
            restExampleResponseData = ""
    )
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @RequestMapping(value = "/addListenNum", method = RequestMethod.POST)
    public Result<Void> addRecordListenNum(@RequestBody AddRecordNumReq req) {
        req.valid();
        channelCommentService.addRecordListenNum(req.getRecordId());
        return ResultBuilder.buildSuccess(null);
    }

    @MethodDoc(
            displayName = "查询评价规则",
            description = "查询评价规则",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "查询评价回复模板请求参数"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    )
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/api/v1/channelComment/queryCommentRule",
            restExamplePostData = " ",
            restExampleResponseData = ""
    )
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @RequestMapping(value = "/queryBadCommentProcessList", method = RequestMethod.POST)
    public Result<PageResult<BadProcessedInfoVO>> queryBadCommentProcessedList(@RequestBody QueryBadCommentProcessedReq req) {
        req.setTenantId(ContextHolder.currentUserTenantId());
        req.valid();
        req.setPoiIds(validateAndGetPoiIds(req.getPoiIds()));
        req.setChannelIds(validateAndGetChannelIds(req.getChannelIds()));
        PageResult<BadProcessedInfoBO> pageResult = channelCommentService.queryBadCommentProcessedList(req);
        List<BadProcessedInfoVO> badProcessedInfoVOS = ConverterUtils.convertList(pageResult.getList(), BadProcessedInfoVO::convertToVO);
        fillPoiNameForCommentVO(badProcessedInfoVOS);
        return ResultBuilder.buildSuccess(new PageResult<>(badProcessedInfoVOS, pageResult.getPage(), pageResult.getPageSize(), pageResult.getTotal()));
    }

    @MethodDoc(
            displayName = "评价更新",
            description = "根据评价id,查询渠道是否有更新",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "评价更新请求参数",
                            type = ManualCheckUpdateCommentReq.class
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    )
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/api/v1/channelComment/manualCheckUpdateComment",
            restExamplePostData = "{\"commentId\":\"1273689634\"}",
            restExampleResponseData = "{\"code\": 0,\"msg\": \"成功\",\"data\": {\"resultState\": 1,\"resultMessage\": \"评价内容已更新\"},\"success\": true\"}"
    )
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @RequestMapping(value = "/manualCheckUpdateComment", method = {RequestMethod.POST, RequestMethod.GET})
    public Result<ManualCheckUpdateCommentVo> manualCheckUpdateComment(@Valid @RequestBody ManualCheckUpdateCommentReq request) {
        try {
            request.setTenantId(ContextHolder.currentUserTenantId());
            request.isValid();
            return ResultBuilder.buildSuccess(channelCommentService.manualCheckUpdateComment(request.getCommentId(), request.getTenantId()));
        } catch (BizException | IllegalArgumentException e) {
            return ResultBuilder.buildFailResult(e.getMessage());
        }
    }

    @MethodDoc(
            displayName = "删除评价",
            description = "删除评价",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "删除评价请求参数",
                            type = ManualCheckDeleteCommentReq.class
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    )
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/api/v1/channelComment/manualCheckDeleteComment",
            restExamplePostData = "{\"commentId\":\"1273689634\"}",
            restExampleResponseData = "{\"code\": 0,\"msg\": \"成功\",\"data\": {\"status\": 0,\"resultMessage\": \"评价在渠道已被删除，系统内评价已更新为删除状态\"},\"success\": true\"}"
    )
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/manualCheckDeleteComment", method = RequestMethod.GET)
    public Result<ManualCheckDeleteCommentVo> manualCheckDeleteComment(String commentId) {
        try {
            ManualCheckDeleteCommentReq request = new ManualCheckDeleteCommentReq();
            request.setCommentId(commentId);
            request.setTenantId(ContextHolder.currentUserTenantId());
            request.isValid();
            return ResultBuilder.buildSuccess(channelCommentService.manualCheckDeleteComment(request.getCommentId(), request.getTenantId()));
        } catch (BizException | IllegalArgumentException e) {
            return ResultBuilder.buildFailResult(e.getMessage());
        }

    }

    @MethodDoc(
            displayName = "确认删除评价",
            description = "确认删除评价",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "确认删除评价请求参数",
                            type = CommentListQueryReq.class
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    )
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/api/v1/channelComment/confirmDeleteComment",
            restExamplePostData = "{\"commentId\":\"1273689634\"}",
            restExampleResponseData = "{\"code\":0,\"message\":\"\",\"data\":null\"}"
    )
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/confirmDeleteComment", method = RequestMethod.GET)
    public Result<Void> confirmDeleteComment(String commentId) {
        try {
            ConfirmDeleteCommentReq request = new ConfirmDeleteCommentReq();
            request.setCommentId(commentId);
            request.setTenantId(ContextHolder.currentUserTenantId());
            request.setOperatorId(ContextHolder.currentUid());
            request.isValid();
            channelCommentService.confirmDeleteComment(request.getCommentId(), request.getTenantId(), request.getOperatorId());
            return ResultBuilder.buildSuccess(null);
        } catch (BizException | IllegalArgumentException e) {
            return ResultBuilder.buildFailResult(e.getMessage());
        }

    }


    private List<Long> validateAndGetPoiIds(List<Long> poiIds) {
        List<Long> authPoiIds = authRemoteService.queryPermissionInfoOfCurrentUser(ContextHolder.currentUserTenantId(),
                ContextHolder.currentUid(), 3, Constants.PermissionType.AUTH_TYPE_OF_POI);

        if (CollectionUtils.isEmpty(authPoiIds)) {
            throw new BizException("无门店权限");
        }

        if (CollectionUtils.isEmpty(poiIds)) {
            return authPoiIds;
        } else {
            if (!authPoiIds.containsAll(poiIds)) {
                throw new BizException("无门店权限");
            }

        }
        return poiIds;
    }

    private List<Integer> validateAndGetChannelIds(List<Integer> channelIds) {
        List<Integer> openChannelIds = tenantService.queryChannels(ContextHolder.currentUserTenantId(), null).stream().map(ChannelInfoBo::getChannelId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(openChannelIds)) {
            throw new BizException("无已开通的渠道");
        }

        if (CollectionUtils.isEmpty(channelIds)) {
            return openChannelIds;
        } else {
            if (!openChannelIds.containsAll(channelIds)) {
                throw new BizException("存在未开通渠道");
            }
        }
        return channelIds;
    }

    private boolean checkOpenELEMChannel() {
        List<Integer> openChannelIds = tenantService.queryChannels(ContextHolder.currentUserTenantId()).stream().map(ChannelInfoBo::getChannelId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(openChannelIds)) {
            return openChannelIds.contains(ChannelType.ELEM.getValue());
        }
        return false;
    }

    /**
     * 是否erp租户
     *
     * @return
     */
    private boolean isErpTenant() {
        try {
            boolean isErpTenant = tenantService.queryTenantHasErp(ContextHolder.currentUserTenantId());
            return isErpTenant;
        } catch (Exception e) {
            log.error("", e);
        }
        return false;
    }

}
