/*
 * Copyright (c) 2019 Meituan.com. All Rights Reserved.
 */
package com.sankuai.shangou.qnh.orderapi.domain.result.store;

import com.sankuai.shangou.qnh.orderapi.enums.store.PendingTaskType;
import lombok.Data;

/**
 * <br><br>
 * Author: l<PERSON><PERSON><PERSON><PERSON> <br>
 * Date: 2019-06-03 Time: 10:45
 */
@Data
public class PendingTaskResult {

    private int count;
    private PendingTaskType type;

    private PendingTaskResult(Integer count, PendingTaskType type) {
        this.count = count;
        this.type = type;
    }

    /**
     * 创建数字角标
     * @param count 待处理任务数
     * @return result
     */
    public static PendingTaskResult createNumberMarker(int count) {
        return new PendingTaskResult(count, PendingTaskType.NUMBER);
    }

    /**
     * 创建红点角标
     * @return result
     */
    public static PendingTaskResult createRedDotMarker(int count) {
        return new PendingTaskResult(count, PendingTaskType.RED_DOT);
    }
}
