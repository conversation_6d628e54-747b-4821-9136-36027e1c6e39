package com.sankuai.shangou.qnh.orderapi.domain.response.pda;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 * @createTime 2024/07/19
 * @description
 */
@TypeDoc(
        description = "订单首页查询返回"
)
@Data
@ApiModel("订单首页查询返回")
public class AggDeliveryUrlResponse {

    @FieldDoc(
            description = "提示文案", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "提示文案", required = true)
    private String title;

    @FieldDoc(
            description = "链接文案", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "链接文案", required = true)
    private String urlText;

    @FieldDoc(
            description = "跳转url", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "跳转url", required = true)
    private String url;
}
