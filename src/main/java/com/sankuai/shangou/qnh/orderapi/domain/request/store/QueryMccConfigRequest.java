package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@TypeDoc(
        description = "获取mcc库位配置信息请求"
)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryMccConfigRequest {
    @FieldDoc(
            description = "门店id"
    )
    private Long storeId;
    @FieldDoc(
            description = "实体id"
    )
    private Long entityId;
    @FieldDoc(
            description = "实体类型"
    )
    private Integer entityType;
}
