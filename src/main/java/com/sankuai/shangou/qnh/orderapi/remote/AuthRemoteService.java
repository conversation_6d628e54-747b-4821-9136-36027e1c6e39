package com.sankuai.shangou.qnh.orderapi.remote;

import com.dianping.rhino.annotation.Degrade;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.linz.boot.util.Fun;
import com.meituan.reco.pickselect.common.cat.CatTransaction;
import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.meituan.shangou.sac.dto.model.TenantAndAccountAuthPermissionsDto;
import com.meituan.shangou.sac.dto.request.authenticate.AccountAuthPermissionsRequest;
import com.meituan.shangou.sac.dto.request.authenticate.TenantAndAccountAuthPermissionsRequest;
import com.meituan.shangou.sac.dto.response.SacCommonResponse;
import com.meituan.shangou.sac.dto.response.authenticate.AccountAuthPermissionsResponse;
import com.meituan.shangou.sac.thrift.authenticate.AuthenticateService;
import com.sankuai.meituan.reco.store.saas.infrastructure.shield.common.monitor.CommonMonitorTransaction;
import com.sankuai.meituan.shangou.empower.auth.sdk.bean.AppIdEnum;
import com.sankuai.meituan.shangou.empower.auth.sdk.bean.SessionInfo;
import com.sankuai.meituan.shangou.empower.auth.sdk.context.SessionContext;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.PermissionGroupTypeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.service.AuthThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.AccountInfoVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.AuthResourceRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.BatchQueryPermissionGroupRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.DataPermissionVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.PermissionGroupVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.PermissionVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryAccountInfoRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryAccountInfoResponse;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryPermissionGroupRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryPermissionGroupResponse;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QuerySimpleAccountInfoListResponse;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QuerySimpleAccountInfoRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.Result;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.RoleInfoVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.request.AuthPermissionAndDataAuthRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.request.PoiDataAuthRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.response.AuthPermissionAndDataAuthResponse;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.response.PoiDataAuthResponse;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.shangou.qnh.orderapi.constant.pc.Constants;
import com.sankuai.shangou.qnh.orderapi.context.app.ApiMethodParamThreadLocal;
import com.sankuai.shangou.qnh.orderapi.domain.dto.app.IdentityInfo;
import com.sankuai.shangou.qnh.orderapi.domain.dto.app.User;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.BaseResult;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.PermissionGroupVO;
import com.sankuai.shangou.qnh.orderapi.enums.TenantAndAccountAuthEnum;
import com.sankuai.shangou.qnh.orderapi.enums.app.AppAuthIdConstants;
import com.sankuai.shangou.qnh.orderapi.exception.app.FallbackException;
import com.sankuai.shangou.qnh.orderapi.exception.pc.CommonLogicException;
import com.sankuai.shangou.qnh.orderapi.exception.pc.CommonRuntimeException;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.shangou.qnh.orderapi.constant.pc.ConfigDefaultValueConstant.SAAS_B_APP_ID;
import static com.sankuai.shangou.qnh.orderapi.utils.pc.ResponseHandler.handleResult;
import static com.sankuai.shangou.qnh.orderapi.utils.pc.RpcInvoker.invokeReturn;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2018/10/25
 * 权限的外部请求接口
 **/
@Slf4j
@Service
public class AuthRemoteService {

    @Resource
    private AuthThriftService.Iface authThriftService;
    @Resource
    private AuthenticateService authenticateService;

    /**
     * 查询有权限的资源编码
     *
     * @param tenantId  租户 id
     * @param accountId 账号 id
     * @param appId     应用 id
     * @param type      权限类型，可参考 {@link Constants.AuthType}
     * @return 资源编码列表
     */
    public List<Long> queryPermissionInfoOfCurrentUser(long tenantId, long accountId, int appId, int type) {
        return queryPermissionInfoOfCurrentUser(tenantId, accountId, appId, type, Long::valueOf);
    }

    /**
     * 查询有权限的资源编码
     *
     * @param tenantId  租户 id
     * @param accountId 账号 id
     * @param appId     应用 id
     * @param type      权限类型，可参考 {@link Constants.AuthType}
     * @param transfer  资源编码转换逻辑
     * @return 资源编码列表
     */
    public <T> List<T> queryPermissionInfoOfCurrentUser(long tenantId, long accountId, int appId, int type, Function<String, T> transfer) {
        QueryPermissionGroupRequest request = new QueryPermissionGroupRequest();
        request.setAccountId(accountId);
        request.setAppId(appId);
        request.setTenantId(tenantId);
        request.setType(type);
        try {
            QueryPermissionGroupResponse response = authThriftService.queryPermissionGroupByTokenAndPermissionType(request);
            handleResult(response.getResult());
            return Fun.map(Fun.map(response.getPermissionGroupCodeList(), PermissionGroupVo::getCode), transfer);
        } catch (TException e) {
            log.error("AuthClient queryRepositoryOfCurrentUser error", e);
            throw new IllegalStateException(e);
        }
    }

    /**
     * 批量查询有权限的资源编码
     *
     * @param tenantId  租户 id
     * @param accountId 账号 id
     * @param appId     应用 id
     * @param typeList  权限类型列表，可参考 {@link Constants.AuthType}
     * @return 资源编码列表
     */
    public List<Long> batchQueryPermissionInfoOfCurrentUser(long tenantId, long accountId, int appId, List<Integer> typeList) {
        return batchQueryPermissionInfoOfCurrentUser(tenantId, accountId, appId, typeList, Long::valueOf);
    }

    /**
     * 批量查询有权限的资源编码
     *
     * @param tenantId  租户 id
     * @param accountId 账号 id
     * @param appId     应用 id
     * @param typeList  权限类型列表，可参考 {@link Constants.AuthType}
     * @param transfer  资源编码转换逻辑
     * @return 资源编码列表
     */
    public <T> List<T> batchQueryPermissionInfoOfCurrentUser(long tenantId, long accountId, int appId, List<Integer> typeList,
                                                             Function<String, T> transfer) {
        if (CollectionUtils.isEmpty(typeList)) {
            return Collections.emptyList();
        }
        BatchQueryPermissionGroupRequest request = new BatchQueryPermissionGroupRequest();
        request.setAccountId(accountId);
        request.setAppId(appId);
        request.setTenantId(tenantId);
        request.setTypeList(typeList);
        try {
            QueryPermissionGroupResponse response = authThriftService.batchQueryPermissionGroupByTokenAndPermissionType(request);
            handleResult(response.getResult());
            return Fun.map(Fun.map(response.getPermissionGroupCodeList(), PermissionGroupVo::getCode), transfer);
        } catch (TException e) {
            log.error("AuthClient batchQueryPermissionInfoOfCurrentUser error", e);
            throw new IllegalStateException(e);
        }
    }

    /**
     * 查询当前用户拥有的所有权限的所有 POI 实体类型的 poi id，目前包括门店、中心仓、共享前置仓
     * 注：此接口中为 POI 维度的接口，当后续 POI 实体类型增加时，这里也会对应添加
     *
     * @param tenantId 租户 id
     * @param accountId 账号 id
     * @param appId 应用 id
     * @return poi id
     */
    public List<Long> queryAllPoiPermissionOfCurrentUser(long tenantId, long accountId, int appId) {
        return batchQueryPermissionInfoOfCurrentUser(tenantId, accountId, appId,
                Arrays.asList(Constants.AuthType.POI_TYPE, Constants.AuthType.SHAREABLE_WAREHOUSE));
    }

    /**
     * 根据账号 id 批量查询数据权限组信息
     *
     * @param tenantId  租户id
     * @param accountId 账号id
     * @param typeList  权限类型列表，可参考 {@link Constants.AuthType}
     * @return List<PermissionGroupVO>
     */
    @MethodLog(logRequest = true, logResponse = true)
    public List<PermissionGroupVO> batchQueryAllPoiPermissionGroup(long tenantId, long accountId, List<Integer> typeList) {
        if (CollectionUtils.isEmpty(typeList)) {
            return Collections.emptyList();
        }
        BatchQueryPermissionGroupRequest request = new BatchQueryPermissionGroupRequest();
        request.setAccountId(accountId);
        request.setAppId(SAAS_B_APP_ID);
        request.setTenantId(tenantId);
        request.setTypeList(typeList);

        QueryPermissionGroupResponse response = invokeReturn(() -> authThriftService.batchQueryPermissionGroupByTokenAndPermissionType(request));
        handleResult(response.getResult());
        return Fun.map(response.getPermissionGroupCodeList(), PermissionGroupVO::new);
    }

    /**
     * 数据鉴权
     * @param request
     * @return
     */
    public boolean hasAuth(AuthResourceRequest request) {

        try {
            Result result = authThriftService.authResource(request);
            if (BaseResult.SUCCESS.getCode() == result.getCode()) {
                return true;
            }
        } catch (TException e) {
            log.error("数据鉴权失败，e=", e);
        }

        return false;
    }

    /**
     * 批量查询权限code是否有权限
     *
     * @param accountId
     * @param authId
     * @param permissionCodes
     * @return
     */
    public Map<String, Boolean> checkAccountPermissions(long accountId, int authId, List<String> permissionCodes) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(permissionCodes)) {
            return Collections.emptyMap();
        }
        Map<String, Boolean> result = permissionCodes.stream().distinct().collect(Collectors.toMap(Function.identity(), s -> false));
        AccountAuthPermissionsRequest request = new AccountAuthPermissionsRequest();
        request.setAccountId(accountId);
        request.setAppId(authId);
        request.setPermissionCodes(permissionCodes);
        AccountAuthPermissionsResponse response;
        try {
            log.info("批量查询权限code是否有权限 request:{}", request);
            response = authenticateService.accountAuthPermissions(request);
            log.info("批量查询权限code是否有权限 response:{}", response);
        } catch (Exception e) {
            log.error("批量查询权限code是否有权限异常 request:{}", request);
            return result;
        }
        if (response == null || response.sacStatus == null || response.sacStatus.code != 0) {
            log.error("批量查询权限code是否有权限失败,request:{},response:{}", request, response);
            return result;
        }
        if (MapUtils.isNotEmpty(response.getAuthResult())) {
            response.getAuthResult().forEach(result::put);
        }
        return result;
    }

    @Degrade(rhinoKey = "AuthRemoteService.getCurrentAccount",
            fallBackMethod = "getCurrentAccountFallback",
            timeoutInMilliseconds = 3000,
            ignoreExceptions = CommonLogicException.class)
    @com.meituan.reco.pickselect.common.methodlog.MethodLog(logRequest = true, logResponse = true)
    @CatTransaction
    @SuppressWarnings("WeakerAccess")
    public AccountInfoVo getCurrentAccount() throws TException {
        SessionInfo sessionInfo = SessionContext.getCurrentSession();
        QueryAccountInfoResponse resp = authThriftService.queryAccountInfoByAccountIdAndAppId(
                new QueryAccountInfoRequest(sessionInfo.getAccountId(), sessionInfo.getTenantId())
        );
        log.info("account resp, tenantId:{}, accountId:{}, resp:{}", sessionInfo.getTenantId(), sessionInfo.getAccountId(), resp);
        nullResponseCheck(resp, "查询权限异常");
        validateStatus(resp.getResult(), "查询权限异常");
        return resp.getAccountInfo();
    }

    private void nullResponseCheck(Object resp, String errorMsg) {
        if (resp == null) {
            throw new CommonRuntimeException(errorMsg + ", response is null");
        }
    }

    private void validateStatus(Result status, String errorMsg) {
        if (status == null) {
            throw new CommonRuntimeException(errorMsg + ", status is null");
        }

        if (status.getCode() != ResultCode.SUCCESS.getCode()) {
            throw new CommonRuntimeException(MessageFormat.format("{0}, code = {1}, detail = {2}",
                    errorMsg, status.getCode(), status.getMsg()));
        }
    }

    private List<DataPermissionVo> getCurrentAccountFallback() {
        throw new CommonRuntimeException("AuthRemoteService.Auth降级");
    }

    public AccountInfoVo getCurrentAccountWithoutPermission() throws TException {
        SessionInfo sessionInfo = SessionContext.getCurrentSession();

        QuerySimpleAccountInfoRequest queryRequest = new QuerySimpleAccountInfoRequest();
        queryRequest.setAccountIds(Arrays.asList(sessionInfo.getAccountId()));
        QuerySimpleAccountInfoListResponse response = authThriftService.querySimpleAccountInfoList(queryRequest);
        nullResponseCheck(response, "查询权限异常");
        validateStatus(response.getResult(), "查询权限异常");
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(response.getAccountInfoList())) {
            log.error("账号不存在, accountId:{}", sessionInfo.getAccountId());
            throw new com.sankuai.shangou.qnh.orderapi.exception.app.CommonLogicException("账号不存在");
        }
        return response.getAccountInfoList().get(0);
    }

    /**
     * 获取登录账号所有权限码
     *
     * @return account info
     * @throws TException Any
     */
    @Degrade(rhinoKey = "AuthRemoteService.getCurrentAccountAllPermissions",
            fallBackMethod = "getCurrentAccountAllPermissionCodesFallback",
            timeoutInMilliseconds = 3000,
            ignoreExceptions = com.sankuai.shangou.qnh.orderapi.exception.app.CommonLogicException.class)
    @MethodLog(logRequest = true, logResponse = true)
    @CommonMonitorTransaction
    @SuppressWarnings("WeakerAccess")
    @Deprecated
    public List<String> getCurrentAccountAllPermissionCodes() {
        try {
            AccountInfoVo accountInfoVo = getCurrentAccount();

            if (Objects.isNull(accountInfoVo) ||
                    org.apache.commons.collections4.CollectionUtils.isEmpty(accountInfoVo.getRoleList())) {
                return Collections.EMPTY_LIST;
            }

            List<String> permissionCodeList = Lists.newArrayList();
            for (RoleInfoVo roleInfoVo : accountInfoVo.getRoleList()) {
                for (PermissionVo permissionVo : roleInfoVo.getPermissionList()) {
                    if (AppAuthIdConstants.SHU_GUO_PAI.val() == permissionVo.getAppId()) {
                        permissionCodeList.add(permissionVo.getCode());
                    }
                }
            }
            return permissionCodeList;
        } catch (TException e) {
            throw new RuntimeException("查询权限异常", e);
        }
    }


    private List<DataPermissionVo> getCurrentAccountAllPermissionCodesFallback() {
        throw new FallbackException("AuthThriftWrapper.getCurrentAccountAllPermissionCodesFallback");
    }

    /**
     * 判断针对
     *
     * @param authCode
     * @return
     */
    public Boolean isCodeHasAuth(String authCode) {
        if (MccConfigUtil.useSacAuthenticationV2()) {
            Map<String, Boolean> codeAuthMap = isHasPermission(Arrays.asList(authCode));
            return BooleanUtils.isTrue(codeAuthMap.get(authCode));
        }
        else {
            List<String> codeHasAuth = getCurrentAccountAllPermissionCodes();
            return org.apache.commons.collections4.CollectionUtils.isNotEmpty(codeHasAuth) ? codeHasAuth.contains(authCode) : false;
        }
    }

    /**
     * 判断针对
     *
     * @param authCodes
     * @return
     */
    @Deprecated
    public Map<String, Boolean> isCodesHasAuth(List<String> authCodes) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(authCodes)) {
            return Collections.emptyMap();
        }
        List<String> accountAllPermissionCodes = getCurrentAccountAllPermissionCodes();
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(accountAllPermissionCodes)) {
            return authCodes
                    .stream()
                    .distinct()
                    .collect(Collectors.toMap(Function.identity(),
                            s -> false));
        }
        Map<String, Boolean> resultMap = new HashMap<>();
        for (String authCode : authCodes) {
            resultMap.put(authCode,accountAllPermissionCodes.contains(authCode));
        }
        return resultMap;
    }

    public List<String> queryAuthorizedCodes(List<String> authCodes) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(authCodes)) {
            return Collections.emptyList();
        }

        if (MccConfigUtil.useSacAuthenticationV2()) {
            SessionInfo sessionInfo = SessionContext.getCurrentSession();

            Map<String, Boolean> userPermissionCodeMap = checkAccountPermissions(sessionInfo.getAccountId(),
                    ApiMethodParamThreadLocal.getIdentityInfo().getAuthId(), authCodes);

            return userPermissionCodeMap.entrySet().stream().filter(Map.Entry::getValue).map(Map.Entry::getKey).collect(Collectors.toList());
        }
        else {
            return getCurrentAccountAllPermissionCodes();
        }

    }

    /**
     * 判断登录账号是否有指定权限码的权限
     * @param currPermissionCodes
     * @return
     */
    public Map<String/*permissionCode*/, Boolean> isHasPermission(List<String> currPermissionCodes) {

        if (MccConfigUtil.useSacAuthenticationV2()) {
            SessionInfo sessionInfo = SessionContext.getCurrentSession();
            int appId = ApiMethodParamThreadLocal.getIdentityInfo().getAuthId();
            return checkAccountPermissions(sessionInfo.getAccountId(), appId, currPermissionCodes);
        }
        else {
            return isHasPermissionByQueryAccount(currPermissionCodes);
        }
    }

    public Map<String, Boolean> isHasPermissionV2(List<String> currPermissionCodes) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        try {
            if (identityInfo != null
                    && identityInfo.getUser() != null){
                User user = identityInfo.getUser();
                long accountId = user.getAccountId();
                int appId = ApiMethodParamThreadLocal.getIdentityInfo().getAuthId();
                return checkAccountPermissions(accountId, appId, currPermissionCodes);
            }
        }catch (Exception e){
            log.error("查询权限元素权限失败", e);
        }
        log.info("没有配置待检查的元素权限");
        return Maps.newHashMap();
    }

    @Deprecated
    private Map<String, Boolean> isHasPermissionByQueryAccount(List<String> permissionCodes) {
        Map<String, Boolean> permissionMap = Maps.newHashMap();
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(permissionCodes)) {
            return permissionMap;
        }

        List<String> accountPermissionCodes = getCurrentAccountAllPermissionCodes();

        if (org.apache.commons.collections4.CollectionUtils.isEmpty(accountPermissionCodes)) {
            permissionCodes.forEach(code -> {
                permissionMap.put(code, false);
            });
            return permissionMap;
        }

        permissionCodes.forEach(code -> {
            if (accountPermissionCodes.contains(code)) {
                permissionMap.put(code, true);
            } else {
                permissionMap.put(code, false);
            }
        });

        return permissionMap;
    }

    /**
     * 对指定账号进行元素权限的鉴权
     *
     * @return account info
     * @throws TException Any
     */
    @Degrade(rhinoKey = "AuthRemoteService.authPermissionAndDataAuth",
            fallBackMethod = "authPermissionAndDataAuthFallback",
            timeoutInMilliseconds = 3000,
            ignoreExceptions = CommonLogicException.class)
    @MethodLog(logRequest = true, logResponse = true)
    @CommonMonitorTransaction
    @SuppressWarnings("WeakerAccess")
    public Map<String, Boolean> authPermissionAndDataAuth(Long accountId, Long shopId, List<String> permissionCodes) {
        try {
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(permissionCodes)){
                int dataAuthType = PermissionGroupTypeEnum.POI.getValue();
                int authId = ApiMethodParamThreadLocal.getIdentityInfo().getAuthId();
                if (authId == AppIdEnum.APP_9.getAuthAppId()) {
                    // 共享前置仓数据权限
                    dataAuthType = PermissionGroupTypeEnum.SHAREABLE_WAREHOUSE.getValue();
                }
                AuthPermissionAndDataAuthRequest request = new AuthPermissionAndDataAuthRequest();
                request.setAccountId(accountId);
                request.setAppId(authId);
                request.setDataAuthCode(String.valueOf(shopId));
                request.setDataAuthType(dataAuthType);
                request.setPermissionCodes(permissionCodes);
                AuthPermissionAndDataAuthResponse response = authThriftService.authPermissionAndDataAuth(request);
                return response.getIsAccountHaveAuth();
            }
        } catch (TException e) {
            log.error("请求元素权限鉴权出错,accountId:{}",accountId,  e);
        }
        return Maps.newHashMap();//默认返回空
    }

    private Map<String, Boolean> authPermissionAndDataAuthFallback(Long accountId, Long shopId, List<String> permissionCodes) {
        return Maps.newHashMap();//默认返回空
    }


    /**
     * 查询账号是否有的门店的权限
     *
     * @param poiIds
     * @param tenantId
     * @param accountId
     * @return
     */
    public Map<Long,Boolean> getAuthPoiDataAuth(List<Long> poiIds, Long tenantId, Long accountId) {
        try {
            PoiDataAuthRequest poiDataAuthRequest = new PoiDataAuthRequest();
            poiDataAuthRequest.setTenantId(tenantId);
            poiDataAuthRequest.setAccountId(accountId);
            poiDataAuthRequest.setPoiIds(poiIds);
            PoiDataAuthResponse response = authThriftService.authPoiDataAuth(poiDataAuthRequest);
            if (Objects.isNull(response) || Objects.isNull(response.getResult())
                    || response.getResult().getCode() != ResultCodeEnum.SUCCESS.getValue()) {
                log.error("authThriftService.authPoiDataAuth 返回异常, response:{}", response);
                return null;
            }
            return response.getIsAccountHaveAuth();
        } catch (Exception e) {
            log.error("authThriftService.authPoiDataAuth tenantId:{}, accountId:{}, poiIds:{} 调用异常", tenantId,
                    accountId, poiIds, e);
        }
        return null;
    }

    /**
     * 查询租户是否配置该权限点,且判断用户是否有该权限点
     *
     * @param tenantId
     * @param appId 权限应用ID
     * @param permissionCodeList 权限CODE列表
     * @param accountId 用户ID
     * @return
     */
    public Map<String, Integer> tenantAuthPermission(Long tenantId, Integer appId, List<String> permissionCodeList,
            Long accountId) {
        try {
            if (CollectionUtils.isEmpty(permissionCodeList)) {
                return new HashMap<>();
            }
            TenantAndAccountAuthPermissionsRequest request = new TenantAndAccountAuthPermissionsRequest();
            request.setTenantId(tenantId);
            request.setAppId(appId);
            request.setPermissionCodes(permissionCodeList);
            request.setAccountId(accountId);
            SacCommonResponse<TenantAndAccountAuthPermissionsDto> response = authenticateService
                    .tenantAndAccountAuthPermissions(request);
            if (Objects.isNull(response) || Objects.isNull(response.getSacStatus())
                    || response.getSacStatus().getCode() != ResultCodeEnum.SUCCESS.getValue()
                    || Objects.isNull(response.getResult())) {
                log.error("authenticateService tenantAndAccountAuthPermissions 返回异常, response:{}", response);
                return new HashMap<>();
            }
            TenantAndAccountAuthPermissionsDto result = response.getResult();
            // 租户是否开通权限
            Map<String, Boolean> tenantAuth = result.getTenantAuth();
            // 账号是否开通权限
            Map<String, Boolean> accountAuth = result.getAccountAuth();
            return permissionCodeList.stream().collect(Collectors.toMap(code -> code, code -> {
                if (BooleanUtils.isFalse(tenantAuth.get(code))) {
                    return TenantAndAccountAuthEnum.TENANT_NOT_AUTH.getCode();
                }
                return BooleanUtils.isTrue(accountAuth.get(code))
                        ? TenantAndAccountAuthEnum.TENANT_AND_USER_AUTH.getCode()
                        : TenantAndAccountAuthEnum.TENANT_AUTH_USER_NOT_AUTH.getCode();
            }));
        } catch (Exception e) {
            log.error("tenantId:{}, appId:{}, permissionCodeList:{} authenticateService tenantAndAccountAuthPermissions 调用异常",
                    tenantId, appId, permissionCodeList, e);
        }
        return new HashMap<>();
    }
}
