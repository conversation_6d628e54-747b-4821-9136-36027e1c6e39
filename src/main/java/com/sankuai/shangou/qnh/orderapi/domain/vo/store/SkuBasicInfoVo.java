package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

/**
 * @description:
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2019-12-02
 **/

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TypeDoc(
        description = "标品信息"
)
@Data
@ApiModel("标品信息")
public class SkuBasicInfoVo {


    @FieldDoc(
            description = "标品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "标品名称", required = true)
    private String name;

    @FieldDoc(
            description = "标品码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "标品码", required = true)
    private String upc;

    @FieldDoc(
            description = "规格", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "规格", required = true)
    private String spec;

    @FieldDoc(
            description = "单位", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "单位", required = true)
    private String unit;

    @FieldDoc(
            description = "头图(无水印)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "头图(无水印)", required = true)
    private String pic;

    @FieldDoc(
            description = "包装重量（单位：g）", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "包装重量（单位：g）", required = true)
    private Integer weight;

    @FieldDoc(
            description = "带单位的重量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "带单位的重量", required = true)
    private String weightForUnit;

    @FieldDoc(
            description = "重量单位", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "重量单位", required = true)
    private String weightUnit;

    @FieldDoc(
            description = "标品类目名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "标品类目名称", required = true)
    private String categoryName;

    @FieldDoc(
            description = "标品品牌ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "标品品牌ID", required = true)
    private String brandId;

    @FieldDoc(
            description = "标品品牌名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "标品品牌名称", required = true)
    private String brandName;

    @FieldDoc(
            description = "产地", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "产地", required = true)
    private String originName;

}
