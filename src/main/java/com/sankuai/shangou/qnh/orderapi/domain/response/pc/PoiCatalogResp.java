package com.sankuai.shangou.qnh.orderapi.domain.response.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.tenant.thrift.dto.group.GroupDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * @Author：<EMAIL>
 * @Date: 2018/9/18 下午4:01
 */
@TypeDoc(
    description = "门店类别"
)
@ApiModel("门店类别")
@Getter
@Setter
@ToString
@NoArgsConstructor
public class PoiCatalogResp {

  @FieldDoc(
      description = "门店类别id"
  )
  @ApiModelProperty(value = "门店类别id", required = true)
  private String id;

  @FieldDoc(
      description = "门店类别名称"
  )
  @ApiModelProperty(value = "门店类别名称", required = true)
  private String title;

  @FieldDoc(
      description = "门店子类别列表"
  )
  @ApiModelProperty(value = "门店子类别列表", required = false)
  private List<PoiCatalogResp> children;

  public PoiCatalogResp(GroupDto groupDto) {
    this.id = groupDto.groupId+"";
    this.title = groupDto.groupName;
  }

  public PoiCatalogResp(String catalogId ,String catalogName) {
    this.id = catalogId+"";
    this.title = catalogName;
  }
}