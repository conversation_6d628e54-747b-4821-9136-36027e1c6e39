package com.sankuai.shangou.qnh.orderapi.domain.dto.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 凭证数据
 *
 * <AUTHOR>
 */
@TypeDoc(description = "凭证数据", authors = {"wb_huangjunwei02"})
@ApiModel("凭证数据")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EvidenceDto {

    @FieldDoc(description = "售后审核拒绝原因枚举编码,1:图片，2:视频(暂不支持)，3:音频(暂不支持展示)，4:文字(暂不支持展示)")
    @ApiModelProperty(value = "售后审核拒绝原因枚举编码,1:图片，2:视频(暂不支持)，3:音频(暂不支持展示)，4:文字(暂不支持展示)")
    private int type;

    @FieldDoc(description = "凭证url")
    @ApiModelProperty(value = "凭证url")
    private String url;

    @FieldDoc(description = "凭证描述")
    @ApiModelProperty(value = "凭证描述")
    private String desc;
}
