package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.saas.crm.data.client.dto.price.PriceOfDateDTO;
import com.sankuai.meituan.shangou.saas.crm.data.client.dto.price.StoreSkuPriceTrendDetailDTO;
import com.sankuai.shangou.qnh.orderapi.constant.store.PriceTrendConstants;
import com.sankuai.shangou.qnh.orderapi.constant.store.ProjectConstants;
import com.sankuai.shangou.qnh.orderapi.utils.store.ConvertUtils;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 门店商品价格趋势
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class StoreSkuWithChannelPriceTrendVO {

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * skuId
     */
    private String skuId;

    /**
     * 数据日期列表
     */
    private List<String> dateList;

    /**
     * 线下价
     */
    private List<PriceOfDateVO> storePriceList;

    /**
     * 线下基准价
     */
    private List<PriceOfDateVO> cityBasePriceList;

    /**
     * 线上价
     */
    private Map<Integer, List<PriceOfDateVO>> channelPriceListMap;

    /**
     * 线上基准价
     */
    private Map<Integer, List<PriceOfDateVO>> channelCityBasePriceListMap;

    /**
     * 市调价
     */
    private List<PriceOfDateVO> mrPriceList;

    public static StoreSkuWithChannelPriceTrendVO buildStoreSkuWithChannelPriceTrendVO(
            StoreSkuPriceTrendDetailDTO priceTrendDetailDTO, List<String> dateList, StoreSkuPriceFilter filter) {

        if (priceTrendDetailDTO == null) {
            return null;
        }

        boolean enableWeightConversion = filter.getEnableWeightConversion();

        // 租户未开启价格重量转换开关返回价格趋势数据逻辑
        if (!enableWeightConversion) {
            return buildWhenNotEnableWeightConversion(priceTrendDetailDTO, dateList);
        }

        // 租户开启价格重量转换开关返回价格趋势数据逻辑
        return buildWhenEnableWeightConversion(priceTrendDetailDTO, dateList, filter);
    }

    public static StoreSkuWithChannelPriceTrendVO buildWhenNotEnableWeightConversion(
            StoreSkuPriceTrendDetailDTO priceTrendDetailDTO, List<String> dateList) {
        // 线下价
        List<PriceOfDateVO> storePriceVOList = ConvertUtils.convertList(priceTrendDetailDTO.getStorePriceList(),
                dto -> PriceOfDateVO.buildPriceOfDateVO(dto, Boolean.FALSE));
        // 线上价
        Map<Integer, List<PriceOfDateVO>> channelPriceVOListMap = Maps.newHashMap();
        if (MapUtils.isNotEmpty(priceTrendDetailDTO.getChannelPriceListMap())) {
            priceTrendDetailDTO.getChannelPriceListMap().forEach((channelId, channelPriceList) -> {
                List<PriceOfDateVO> channelPriceVOList = ConvertUtils.convertList(channelPriceList,
                        dto -> PriceOfDateVO.buildPriceOfDateVO(dto, Boolean.FALSE));
                channelPriceVOListMap.put(channelId, channelPriceVOList);
            });
        }

        StoreSkuWithChannelPriceTrendVO storeSkuWithChannelPriceTrendVO = new StoreSkuWithChannelPriceTrendVO();
        storeSkuWithChannelPriceTrendVO.setStoreId(priceTrendDetailDTO.getStoreId());
        storeSkuWithChannelPriceTrendVO.setSkuId(priceTrendDetailDTO.getSkuId());
        storeSkuWithChannelPriceTrendVO.setDateList(dateList);
        storeSkuWithChannelPriceTrendVO.setStorePriceList(storePriceVOList);
        storeSkuWithChannelPriceTrendVO.setCityBasePriceList(Collections.emptyList());
        storeSkuWithChannelPriceTrendVO.setChannelPriceListMap(channelPriceVOListMap);
        storeSkuWithChannelPriceTrendVO.setChannelCityBasePriceListMap(Collections.emptyMap());
        storeSkuWithChannelPriceTrendVO.setMrPriceList(Collections.emptyList());

        return storeSkuWithChannelPriceTrendVO;
    }

    public static StoreSkuWithChannelPriceTrendVO buildWhenEnableWeightConversion(
            StoreSkuPriceTrendDetailDTO priceTrendDetailDTO, List<String> dateList, StoreSkuPriceFilter filter) {

        // 规则重量是否大于0, 两种情况:(1)启用称重类型, 称重类型为称重且重量大于0; (2)不启用称重类型, 重量大于0
        boolean isRuleWeightGtZero = filter.getRuleWeightGtZero();

        if (!isRuleWeightGtZero) {
            // 注意：目前规则重量小于0的逻辑与未开启价格重量转换开关逻辑一致, 后续如果有特殊逻辑, 需要单独实现逻辑进行区分
            return buildWhenNotEnableWeightConversion(priceTrendDetailDTO, dateList);
        } else {
            // 返回线下价格趋势, 线下基准价格趋势, 线上价格趋势, 线上基准价格趋势, 市调价格趋势
            return buildWhenRuleWeightGtZero(priceTrendDetailDTO, dateList);
        }
    }

    public static StoreSkuWithChannelPriceTrendVO buildWhenRuleWeightGtZero(
            StoreSkuPriceTrendDetailDTO priceTrendDetailDTO, List<String> dateList) {

        // 线下价
        List<PriceOfDateVO> storePriceVOList = getPriceOfDateVOListWhenRuleWeightGtZero(priceTrendDetailDTO.getStorePriceList());
        // 线下基准价
        List<PriceOfDateVO> cityBasePriceVOList = ConvertUtils.convertList(priceTrendDetailDTO.getCityBasePriceList(),
                dto -> PriceOfDateVO.buildPriceOfDateVO(dto, Boolean.TRUE));
        // 线上价
        Map<Integer, List<PriceOfDateVO>> channelPriceVOListMap = Maps.newHashMap();
        if (MapUtils.isNotEmpty(priceTrendDetailDTO.getChannelPriceListMap())) {
            priceTrendDetailDTO.getChannelPriceListMap().forEach((channelId, channelPriceList) -> {
                List<PriceOfDateVO> channelPriceVOList = getPriceOfDateVOListWhenRuleWeightGtZero(channelPriceList);
                channelPriceVOListMap.put(channelId, channelPriceVOList);
            });
        }
        // 线上基准价
        Map<Integer, List<PriceOfDateVO>> channelCityBasePriceVOListMap = Maps.newHashMap();
        if (MapUtils.isNotEmpty(priceTrendDetailDTO.getChannelCityBasePriceListMap())) {
            priceTrendDetailDTO.getChannelCityBasePriceListMap().forEach((channelId, channelCityBasePriceList) -> {
                List<PriceOfDateVO> channelCityBasePriceVOList = ConvertUtils.convertList(channelCityBasePriceList,
                        dto -> PriceOfDateVO.buildPriceOfDateVO(dto, Boolean.TRUE));
                channelCityBasePriceVOListMap.put(channelId, channelCityBasePriceVOList);
            });
        }
        // 市调价
        List<PriceOfDateVO> mrPriceVOList = getPriceOfDateVOListWhenRuleWeightGtZero(priceTrendDetailDTO.getMrPriceList());

        StoreSkuWithChannelPriceTrendVO storeSkuWithChannelPriceTrendVO = new StoreSkuWithChannelPriceTrendVO();
        storeSkuWithChannelPriceTrendVO.setStoreId(priceTrendDetailDTO.getStoreId());
        storeSkuWithChannelPriceTrendVO.setSkuId(priceTrendDetailDTO.getSkuId());
        storeSkuWithChannelPriceTrendVO.setDateList(dateList);
        storeSkuWithChannelPriceTrendVO.setStorePriceList(storePriceVOList);
        storeSkuWithChannelPriceTrendVO.setCityBasePriceList(cityBasePriceVOList);
        storeSkuWithChannelPriceTrendVO.setChannelPriceListMap(channelPriceVOListMap);
        storeSkuWithChannelPriceTrendVO.setChannelCityBasePriceListMap(channelCityBasePriceVOListMap);
        storeSkuWithChannelPriceTrendVO.setMrPriceList(mrPriceVOList);

        return storeSkuWithChannelPriceTrendVO;
    }


    private static List<PriceOfDateVO> getPriceOfDateVOListWhenRuleWeightGtZero(List<PriceOfDateDTO> priceOfDateDTOList) {
        if (CollectionUtils.isEmpty(priceOfDateDTOList)) {
            return Collections.emptyList();
        }

        // 规则重量大于0, 价格取pricePer500g
        return priceOfDateDTOList.stream().filter(dto -> dto.getWeight() != null && dto.getWeight() > 0)
                .map(dto -> PriceOfDateVO.buildPriceOfDateVO(dto, Boolean.TRUE))
                .collect(Collectors.toList());
    }

    public ChannelSkuPriceTrendVO generateChannelSkuPriceTrendVO(int channelId, Map<String, Boolean> priceTrendPermissionMap) {

        List<PriceOfDateVO> priceOfDateVOList = Collections.emptyList();
        List<PriceOfDateVO> cityBasePriceOfDateVOList = Collections.emptyList();
        List<PriceOfDateVO> mrPriceOfDateVOList = Collections.emptyList();

        if (ProjectConstants.OFFLINE_CHANNEL_ID == channelId) {
            // 线下价
            priceOfDateVOList = this.storePriceList;
            // 线下基准价, 需要根据权限过滤
            if (MapUtils.isNotEmpty(priceTrendPermissionMap) && BooleanUtils.isTrue(priceTrendPermissionMap.get(
                    PriceTrendConstants.CITY_BASE_PRICE_TREND_PERMISSION_CODE))) {
                cityBasePriceOfDateVOList = this.cityBasePriceList;
            }
        } else {
            // 线上价
            if (MapUtils.isNotEmpty(this.channelPriceListMap)) {
                priceOfDateVOList = this.channelPriceListMap.get(channelId);
            }
            // 线上基准价, 需要根据权限过滤
            if (MapUtils.isNotEmpty(priceTrendPermissionMap) && BooleanUtils.isTrue(priceTrendPermissionMap.get(
                    PriceTrendConstants.CHANNEL_CITY_BASE_PRICE_TREND_PERMISSION_CODE))
                    && MapUtils.isNotEmpty(this.channelCityBasePriceListMap)) {
                cityBasePriceOfDateVOList = this.channelCityBasePriceListMap.get(channelId);
            }
        }

        // 市调价, 需要根据权限过滤
        if (MapUtils.isNotEmpty(priceTrendPermissionMap) &&
                BooleanUtils.isTrue(priceTrendPermissionMap.get(
                        PriceTrendConstants.MR_PRICE_TREND_PERMISSION_CODE))) {
            mrPriceOfDateVOList = this.mrPriceList;
        }

        ChannelSkuPriceTrendVO channelSkuPriceTrendVO = new ChannelSkuPriceTrendVO();
        channelSkuPriceTrendVO.setDateList(this.dateList);
        channelSkuPriceTrendVO.setPriceList(CollectionUtils.isNotEmpty(priceOfDateVOList) ?
                priceOfDateVOList : Collections.emptyList());
        channelSkuPriceTrendVO.setCityBasePriceList(CollectionUtils.isNotEmpty(cityBasePriceOfDateVOList) ?
                cityBasePriceOfDateVOList : Collections.emptyList());
        channelSkuPriceTrendVO.setMrPriceList(CollectionUtils.isNotEmpty(mrPriceOfDateVOList) ?
                mrPriceOfDateVOList : Collections.emptyList());

        return channelSkuPriceTrendVO;
    }

    public boolean isHasPriceTrend(Integer channelId) {

        if (channelId == ProjectConstants.OFFLINE_CHANNEL_ID) {
            return isHasStoreSkuPriceTrend();
        }

        // 判断是否有线上价格趋势
        boolean isHasChannelPriceTrend = MapUtils.isNotEmpty(this.channelPriceListMap)
                && CollectionUtils.isNotEmpty(this.channelPriceListMap.get(channelId));
        // 判断是否有线上基准价格趋势
        boolean isHasChannelCityBasePriceTrend = MapUtils.isNotEmpty(this.channelCityBasePriceListMap)
                && CollectionUtils.isNotEmpty(this.channelCityBasePriceListMap.get(channelId));
        // 判断是否有市调价格趋势
        boolean isHasMrPriceTrend = CollectionUtils.isNotEmpty(this.mrPriceList);

        // 只要有一种价格趋势有数据, 则认为有数据
        return (isHasChannelPriceTrend || isHasChannelCityBasePriceTrend || isHasMrPriceTrend);
    }

    private boolean isHasStoreSkuPriceTrend() {

        // 判断是否有线下价格趋势
        boolean isHasStorePriceTrend = CollectionUtils.isNotEmpty(this.storePriceList);
        // 判断是否有线下基准价格趋势
        boolean isHasCityBasePriceTrend = CollectionUtils.isNotEmpty(this.cityBasePriceList);
        // 判断是否有市调价格趋势
        boolean isHasMrPriceTrend = CollectionUtils.isNotEmpty(this.mrPriceList);

        // 只要有一种价格趋势有数据, 则认为有数据
        return (isHasStorePriceTrend || isHasCityBasePriceTrend || isHasMrPriceTrend);
    }

    public boolean isHasPriceTrend(int channelId, Map<String, Boolean> priceTrendPermissionMap) {

        // 根据权限过滤价格趋势数据, 然后判断是否有数据
        ChannelSkuPriceTrendVO channelSkuPriceTrendVO = generateChannelSkuPriceTrendVO(channelId,
                priceTrendPermissionMap);

        if (channelSkuPriceTrendVO == null) {
            return false;
        }

        return channelSkuPriceTrendVO.isHasPriceTrend();
    }
}


