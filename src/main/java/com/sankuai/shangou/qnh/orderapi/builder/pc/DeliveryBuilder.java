package com.sankuai.shangou.qnh.orderapi.builder.pc;

import com.google.common.collect.Lists;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.sankuai.meituan.shangou.empower.delivery.thrift.common.TSequentialStrategyConfig;
import com.sankuai.meituan.shangou.empower.delivery.thrift.request.*;
import com.sankuai.meituan.shangou.empower.delivery.thrift.response.DeliveryChannelInfo;
import com.sankuai.meituan.shangou.empower.delivery.thrift.response.DeliveryConfig;
import com.sankuai.meituan.shangou.empower.delivery.thrift.response.DeliveryService;
import com.sankuai.meituan.shangou.empower.delivery.thrift.response.DeliveryShop;
import com.sankuai.meituan.shangou.empower.delivery.thrift.response.FailReason;
import com.sankuai.meituan.shangou.empower.delivery.thrift.response.QueryShopDeliveryConfigsDetailResponse;
import com.sankuai.meituan.shangou.empower.delivery.thrift.response.ShopDeliveryConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request.ChannelDeliveryRangeSyncRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request.DeliveryRangeSyncRequest;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.DeleteShopDeliveryConfigRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.ImportShopDeliveryConfigListRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.ModifyDeliveryConfigRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.ModifyShopDeliveryConfigDetailRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.QueryShopDeliveryConfigDetailRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.QueryShopDeliveryConfigRequest;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.QueryShopDeliveryConfigDetailResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.*;
import com.sankuai.shangou.qnh.orderapi.utils.pc.MccConfigUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/3/16
 * desc: 配送Builder工具
 */
public class DeliveryBuilder {

    private static final BigDecimal HUNDRED = new BigDecimal(100);

    public static ModifyShopDeliveryConfigsRequest buildRequest(ModifyShopDeliveryConfigDetailRequest request) {
        ModifyShopDeliveryConfigsRequest modifyShopDeliveryConfigsRequest = new ModifyShopDeliveryConfigsRequest();
        modifyShopDeliveryConfigsRequest.setTenantId(ContextHolder.currentUserTenantId());
        modifyShopDeliveryConfigsRequest.setShopId(Long.valueOf(request.getPoiId()));
        modifyShopDeliveryConfigsRequest.setOperatorId(ContextHolder.currentUid());
        modifyShopDeliveryConfigsRequest.setOperatorName(ContextHolder.currentAccount());
        modifyShopDeliveryConfigsRequest.setModifyLaunchDeliveryConfig(buildModifyLaunchDeliveryConfig(request));
        modifyShopDeliveryConfigsRequest.setShopDeliveryConfigForModifys(buildShopDeliveryConfigForModifyList(request.getList()));
        return modifyShopDeliveryConfigsRequest;
    }

    private static List<ShopDeliveryConfigForModify> buildShopDeliveryConfigForModifyList(List<DeliveryChannelShopForModifyVO> deliveryChannelShopForModifyVOList) {
        if (CollectionUtils.isEmpty(deliveryChannelShopForModifyVOList)) {
            return Lists.newArrayList();
        }

        List<ShopDeliveryConfigForModify> shopDeliveryConfigForModifyList = Lists.newArrayList();
        for (DeliveryChannelShopForModifyVO deliveryChannelShopForModifyVO : deliveryChannelShopForModifyVOList) {
            ShopDeliveryConfigForModify shopDeliveryConfigForModify = new ShopDeliveryConfigForModify();
            shopDeliveryConfigForModify.setDeliveryChannelId(Integer.valueOf(deliveryChannelShopForModifyVO.getDeliveryChannelId()));
            shopDeliveryConfigForModify.setDeliveryShopId(deliveryChannelShopForModifyVO.getDeliveryShopId());
            shopDeliveryConfigForModify.setDeliveryShopName(deliveryChannelShopForModifyVO.getDeliveryShopName());
            shopDeliveryConfigForModify.setDeliveryServiceCodes(deliveryChannelShopForModifyVO.getDeliveryServiceCodes());
            if (Objects.nonNull(deliveryChannelShopForModifyVO.getStatus())) {
                shopDeliveryConfigForModify.setStatus(deliveryChannelShopForModifyVO.getStatus());
            }
            shopDeliveryConfigForModifyList.add(shopDeliveryConfigForModify);
        }
        return shopDeliveryConfigForModifyList;
    }

    private static ModifyLaunchDeliveryConfig buildModifyLaunchDeliveryConfig(ModifyShopDeliveryConfigDetailRequest request) {
	    ModifyLaunchDeliveryConfig modifyLaunchDeliveryConfig = new ModifyLaunchDeliveryConfig();
	    if (Objects.nonNull(request.getLaunchOnMerchantConfirm())) {
		    modifyLaunchDeliveryConfig.setLaunchOnMerchantConfirm(request.getLaunchOnMerchantConfirm());
	    }

	    if (Objects.nonNull(request.getLaunchOnMerchantConfirmDelayTime())) {
		    modifyLaunchDeliveryConfig.setLaunchOnMerchantConfirmDelayTime(request.getLaunchOnMerchantConfirmDelayTime());
	    }

	    if (Objects.nonNull(request.getLaunchOnPickupComplete())) {
		    modifyLaunchDeliveryConfig.setLaunchOnPickupComplete(request.getLaunchOnPickupComplete());
	    }

	    if (Objects.nonNull(request.getLaunchOnPickupCompleteDelayTime())) {
		    modifyLaunchDeliveryConfig.setLaunchOnPickupComplteDelayTime(request.getLaunchOnPickupCompleteDelayTime());
	    }

	    modifyLaunchDeliveryConfig.setDeliveryStrategy(Optional.ofNullable(request.getDeliveryStrategy()).filter(it -> it != 0).orElse(1));
	    if (request.getSequentialStrategyConfig() != null) {
		    modifyLaunchDeliveryConfig.setSequentialStrategyConfig(new TSequentialStrategyConfig(
				    request.getSequentialStrategyConfig().getOrderedDeliveryChannelIds(),
				    request.getSequentialStrategyConfig().getTimeoutForShiftDeliveryChannelInMinutes()
		    ));
	    }
	    return modifyLaunchDeliveryConfig;
    }

    public static SyncDeliveryRangeRequest buildRequest(com.sankuai.shangou.qnh.orderapi.domain.request.pc.SyncDeliveryRangeRequest request) {
        SyncDeliveryRangeRequest syncDeliveryRangeRequest = new SyncDeliveryRangeRequest();
        syncDeliveryRangeRequest.setTenantId(ContextHolder.currentUserTenantId());
        syncDeliveryRangeRequest.setShopId(Long.valueOf(request.getPoiId()));
        syncDeliveryRangeRequest.setOperatorId(ContextHolder.currentUid());
        syncDeliveryRangeRequest.setOperatorName(ContextHolder.currentAccount());
        syncDeliveryRangeRequest.setChannels(Lists.newArrayList());
        if (CollectionUtils.isNotEmpty(request.getChannelList())) {
            for (ChannelForSyncDeliveryRangeVO channelForSyncDeliveryRangeVO : request.getChannelList()) {
                ChannelForSyncDeliveryRange channelForSyncDeliveryRange = new ChannelForSyncDeliveryRange();
                channelForSyncDeliveryRange.setOrderbizType(ChannelOrderConvertUtils.sourceMid2Biz(channelForSyncDeliveryRangeVO.getChannelId()));
                if (Objects.nonNull(channelForSyncDeliveryRangeVO.getMinOrderPrice())) {
                    channelForSyncDeliveryRange.setMinOrderPrice(BigDecimal.valueOf(channelForSyncDeliveryRangeVO.getMinOrderPrice()).multiply(BigDecimal.valueOf(100)).intValue());
                }

                if (Objects.nonNull(channelForSyncDeliveryRangeVO.getDeliveryFee())) {
                    channelForSyncDeliveryRange.setDeliveryFee(BigDecimal.valueOf(channelForSyncDeliveryRangeVO.getDeliveryFee()).multiply(BigDecimal.valueOf(100)).intValue());
                }

                if (Objects.nonNull(channelForSyncDeliveryRangeVO.getDeliveryTime())) {
                    channelForSyncDeliveryRange.setDeliveryTime(channelForSyncDeliveryRangeVO.getDeliveryTime());
                }
                syncDeliveryRangeRequest.getChannels().add(channelForSyncDeliveryRange);
            }
        }

        return syncDeliveryRangeRequest;
    }

    public static DeliveryRangeSyncRequest buildTmsRequest(com.sankuai.shangou.qnh.orderapi.domain.request.pc.SyncDeliveryRangeRequest apiRequest) {
        return new DeliveryRangeSyncRequest(
                ContextHolder.currentUserTenantId(),
                Long.valueOf(apiRequest.getPoiId()),
                Optional.of(apiRequest.getChannelList())
                        .orElse(new ArrayList<>())
                        .stream()
                        .map(it -> new ChannelDeliveryRangeSyncRequest(
                                it.getChannelId(),
                                multiplyHundred(it.getMinOrderPrice()),
                                multiplyHundred(it.getDeliveryFee()),
                                it.getDeliveryTime()
                        ))
                        .collect(Collectors.toList()),
                ContextHolder.currentUid()
        );
    }

    private static Integer multiplyHundred(Double value) {
        return Optional.ofNullable(value)
                .map(BigDecimal::valueOf)
                .map(it -> it.multiply(HUNDRED))
                .map(BigDecimal::intValue)
                .orElse(null);
    }

    public static QueryShopDeliveryConfigsDetailRequest buildRequest(QueryShopDeliveryConfigDetailRequest request) {
        QueryShopDeliveryConfigsDetailRequest queryShopDeliveryConfigsDetailRequest = new QueryShopDeliveryConfigsDetailRequest();
        queryShopDeliveryConfigsDetailRequest.setTenantId(ContextHolder.currentUserTenantId());
        queryShopDeliveryConfigsDetailRequest.setShopId(Long.valueOf(request.getPoiId()));
        return queryShopDeliveryConfigsDetailRequest;
    }

    public static DeleteShopDeliveryCongfigsRequest buildRequest(DeleteShopDeliveryConfigRequest request) {
        DeleteShopDeliveryCongfigsRequest deleteShopDeliveryCongfigsRequest = new DeleteShopDeliveryCongfigsRequest();
        deleteShopDeliveryCongfigsRequest.setTenantId(ContextHolder.currentUserTenantId());
        deleteShopDeliveryCongfigsRequest.setShopId(Long.valueOf(request.getPoiId()));
        deleteShopDeliveryCongfigsRequest.setOperatorId(ContextHolder.currentUid());
        deleteShopDeliveryCongfigsRequest.setOperatorName(ContextHolder.currentAccount());
        return deleteShopDeliveryCongfigsRequest;
    }

    public static ImportShopDeliveryConfigsRequest buildRequest(ImportShopDeliveryConfigListRequest request) {
        ImportShopDeliveryConfigsRequest importShopDeliveryConfigsRequest = new ImportShopDeliveryConfigsRequest();
        importShopDeliveryConfigsRequest.setTenantId(ContextHolder.currentUserTenantId());
        importShopDeliveryConfigsRequest.setOperatorId(ContextHolder.currentUid());
        importShopDeliveryConfigsRequest.setOperatorName(ContextHolder.currentAccount());
        importShopDeliveryConfigsRequest.setConfigFileUrl(request.getConfigFileUrl());
        return importShopDeliveryConfigsRequest;
    }

    public static ModifyDeliveryConfigsRequest buildRequest(ModifyDeliveryConfigRequest request) {
        ModifyDeliveryConfigsRequest modifyDeliveryConfigsRequest = new ModifyDeliveryConfigsRequest();
        modifyDeliveryConfigsRequest.setTenantId(ContextHolder.currentUserTenantId());
        modifyDeliveryConfigsRequest.setOperatorId(ContextHolder.currentUid());
        modifyDeliveryConfigsRequest.setOperatorName(ContextHolder.currentAccount());
        modifyDeliveryConfigsRequest.setDeliveryConfigForModifys(buildDeliveryConfigForModifyList(request.getList()));
        return modifyDeliveryConfigsRequest;
    }

    public static QueryDeliveryConfigsRequest buildRequest() {
        QueryDeliveryConfigsRequest request = new QueryDeliveryConfigsRequest();
        request.setTenantId(ContextHolder.currentUserTenantId());
        return request;
    }

    public static QueryShopDeliveryConfigsRequest buildRequest(QueryShopDeliveryConfigRequest request) {
        QueryShopDeliveryConfigsRequest queryShopDeliveryConfigsRequest = new QueryShopDeliveryConfigsRequest();
        if (StringUtils.isNotEmpty(request.getPoiId())) {
            queryShopDeliveryConfigsRequest.setShopId(Long.valueOf(request.getPoiId()));
        }

        if (CollectionUtils.isNotEmpty(request.getPoiIds())) {
            List<Long> shopIdList = Lists.newArrayList();
            for (String poiId : request.getPoiIds()) {
                shopIdList.add(Long.valueOf(poiId));
            }
            queryShopDeliveryConfigsRequest.setShopIdList(shopIdList);
        }

        queryShopDeliveryConfigsRequest.setPageNo(request.getPage());
        queryShopDeliveryConfigsRequest.setPageSize(request.getPageSize());
        queryShopDeliveryConfigsRequest.setTenantId(ContextHolder.currentUserTenantId());
        queryShopDeliveryConfigsRequest.setOperatorId(ContextHolder.currentUid());
        return queryShopDeliveryConfigsRequest;
    }

    public static FailReasonVO build(FailReason failReason) {
        if (Objects.isNull(failReason)) {
            return null;
        }
        FailReasonVO failReasonVO = new FailReasonVO();
        failReasonVO.setChannelId(ChannelOrderConvertUtils.sourceBiz2Mid(failReason.getOrderBizType()));
        failReasonVO.setFailReasonDescription(failReason.getFailReasonDescription());

        return failReasonVO;
    }

    public static ShopDeliveryConfigVO build(ShopDeliveryConfig shopDeliveryConfig) {
        if (Objects.isNull(shopDeliveryConfig)) {
            return null;
        }

        ShopDeliveryConfigVO shopDeliveryConfigVO = new ShopDeliveryConfigVO();
        shopDeliveryConfigVO.setPoiId(String.valueOf(shopDeliveryConfig.getShopId()));
        shopDeliveryConfigVO.setPoiName(shopDeliveryConfig.getShopName());
        shopDeliveryConfigVO.setList(buildDeliveryChannelShopVOList(shopDeliveryConfig.getDeliveryShops()));
        return shopDeliveryConfigVO;
    }

    public static List<DeliveryShopVO> buildDeliveryChannelShopVOList(List<DeliveryShop> deliveryShopList) {
        if (CollectionUtils.isEmpty(deliveryShopList)) {
            return Lists.newArrayList();
        }

        List<DeliveryShopVO> deliveryShopVOList = Lists.newArrayList();
        for (DeliveryShop deliveryShop : deliveryShopList) {
            DeliveryShopVO deliveryShopVO = new DeliveryShopVO();
            deliveryShopVO.setDeliveryChannelId(String.valueOf(deliveryShop.getDeliveryChannelId()));
            deliveryShopVO.setDeliveryChannelName(deliveryShop.getDeliveryChannelName());
            deliveryShopVO.setDeliveryShopId(deliveryShop.getDeliveryShopId());
            deliveryShopVO.setDeliveryShopName(deliveryShop.getDeliveryShopName());
            deliveryShopVOList.add(deliveryShopVO);
        }
        return deliveryShopVOList;
    }

    public static DeliveryConfigVO build(DeliveryConfig deliveryConfig) {
        if (Objects.isNull(deliveryConfig)) {
            return null;
        }
        DeliveryConfigVO deliveryConfigVO = new DeliveryConfigVO();
        deliveryConfigVO.setDeliveryChannelId(String.valueOf(deliveryConfig.getDeliveryChannelId()));
        deliveryConfigVO.setDeliveryChannelName(deliveryConfig.getDeliveryChannelName());
        deliveryConfigVO.setAppKey(deliveryConfig.getAppKey());
        deliveryConfigVO.setSecret(deliveryConfig.getSecret());
        deliveryConfigVO.setEnable(deliveryConfig.getStatus());
        return deliveryConfigVO;
    }

    public static QueryShopDeliveryConfigDetailResponse build(QueryShopDeliveryConfigsDetailResponse response) {
        QueryShopDeliveryConfigDetailResponse queryShopDeliveryConfigDetailResponse = new QueryShopDeliveryConfigDetailResponse();
        queryShopDeliveryConfigDetailResponse.setLaunchOnMerchantConfirm(response.getLaunchOnMerchantConfirm());
        queryShopDeliveryConfigDetailResponse.setLaunchOnMerchantConfirmDelayTime(response.getLaunchOnMerchantConfirmDelayTime());
        queryShopDeliveryConfigDetailResponse.setLaunchOnPickupComplete(response.getLaunchOnPickupComplete());
        queryShopDeliveryConfigDetailResponse.setLaunchOnPickupCompleteDelayTime(response.getLaunchOnPickupComplteDelayTime());
        queryShopDeliveryConfigDetailResponse.setDeliveryStrategy(response.getDeliveryStrategy());
        if (response.getSequentialStrategyConfig() != null) {
            queryShopDeliveryConfigDetailResponse.setSequentialStrategyConfig(new SequentialDeliveryStrategyConfigVO(
                    response.getSequentialStrategyConfig().getOrderedDeliveryChannelIds(),
                    response.getSequentialStrategyConfig().getTimeoutForShiftDeliveryChannelInMinutes()
            ));
        }
        queryShopDeliveryConfigDetailResponse.setList(buildDeliveryChannelShopDetailVOList(response.getDeliveryShops()));
        return queryShopDeliveryConfigDetailResponse;
    }

    public static DeliveryChannelVO build(DeliveryChannelInfo deliveryChannelInfo) {
        if (Objects.isNull(deliveryChannelInfo)) {
            return null;
        }
        DeliveryChannelVO deliveryChannelVO = new DeliveryChannelVO();
        deliveryChannelVO.setDeliveryChannelId(String.valueOf(deliveryChannelInfo.getDeliveryChannelId()));
        deliveryChannelVO.setDeliveryChannelName(deliveryChannelInfo.getDeliveryChannelName());
        deliveryChannelVO.setDeliveryServiceList(buildDeliveryServiceVOList(deliveryChannelInfo.getDeliveryServices()));
        return deliveryChannelVO;
    }

    private static List<DeliveryServiceVO> buildDeliveryServiceVOList(List<DeliveryService> deliveryServiceList) {
        if (CollectionUtils.isEmpty(deliveryServiceList)) {
            return Lists.newArrayList();
        }

        boolean showTestServicePackage = MccConfigUtil.tenantNeedShowTestServicePackage(ContextHolder.currentUserTenantId());

        List<DeliveryServiceVO> deliveryServiceVOList = Lists.newArrayList();
        for (DeliveryService deliveryService : deliveryServiceList) {
            if (deliveryService.isTestOnly() && !showTestServicePackage) {
                break;
            }
            DeliveryServiceVO deliveryServiceVO = new DeliveryServiceVO();
            deliveryServiceVO.setDeliveryServiceCode(deliveryService.getDeliveryServiceCode());
            deliveryServiceVO.setDeliveryServiceDescription(deliveryService.getDeliveryServiceName());
            deliveryServiceVOList.add(deliveryServiceVO);
        }
        return deliveryServiceVOList;
    }

    private static List<DeliveryChannelShopDetailVO> buildDeliveryChannelShopDetailVOList(List<DeliveryShop> deliveryShopList) {
        if (CollectionUtils.isEmpty(deliveryShopList)) {
            return Lists.newArrayList();
        }

        List<DeliveryChannelShopDetailVO> deliveryChannelShopDetailVOList = Lists.newArrayList();
        for (DeliveryShop deliveryShop : deliveryShopList) {
            DeliveryChannelShopDetailVO deliveryChannelShopDetailVO = new DeliveryChannelShopDetailVO();
            deliveryChannelShopDetailVO.setDeliveryChannelId(String.valueOf(deliveryShop.getDeliveryChannelId()));
            deliveryChannelShopDetailVO.setDeliveryChannelName(deliveryShop.getDeliveryChannelName());
            deliveryChannelShopDetailVO.setDeliveryShopId(deliveryShop.getDeliveryShopId());
            deliveryChannelShopDetailVO.setDeliveryShopName(deliveryShop.getDeliveryShopName());
            deliveryChannelShopDetailVO.setDeliveryServiceCodes(deliveryShop.getDeliveryServiceCodes());
            deliveryChannelShopDetailVO.setStatus(deliveryShop.getStatus());
            deliveryChannelShopDetailVOList.add(deliveryChannelShopDetailVO);
        }
        return deliveryChannelShopDetailVOList;
    }

    private static List<DeliveryConfigForModify> buildDeliveryConfigForModifyList(List<DeliveryConfigForModifyVO> deliveryConfigForModifyVOList) {
        if (CollectionUtils.isEmpty(deliveryConfigForModifyVOList)) {
            return Lists.newArrayList();
        }

        List<DeliveryConfigForModify> deliveryConfigForModifyList = Lists.newArrayList();

        for (DeliveryConfigForModifyVO deliveryConfigForModifyVO : deliveryConfigForModifyVOList) {
            DeliveryConfigForModify deliveryConfigForModify = new DeliveryConfigForModify();
            deliveryConfigForModify.setDeliveryChannelId(Integer.parseInt(deliveryConfigForModifyVO.getDeliveryChannelId()));
            deliveryConfigForModify.setAppKey(deliveryConfigForModifyVO.getAppKey());
            deliveryConfigForModify.setSecret(deliveryConfigForModifyVO.getSecret());
            deliveryConfigForModify.setEnable(deliveryConfigForModifyVO.getEnable());
            deliveryConfigForModifyList.add(deliveryConfigForModify);
        }

        return deliveryConfigForModifyList;
    }
}
