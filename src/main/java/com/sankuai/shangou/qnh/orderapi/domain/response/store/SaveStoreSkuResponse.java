package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.ChannelSkuInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/6/12
 * desc:
 */
@TypeDoc(
        description = "新增门店商品响应"
)
@Data
@ApiModel("新增门店商品响应")
public class SaveStoreSkuResponse {

    @FieldDoc(
            description = "商品编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品编码")
    private String skuId;

    @FieldDoc(
            description = "是否新建商品  0-更新 1-新建", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否新建商品")
    private Integer created;

    @FieldDoc(
            description = "渠道商品信息列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道商品信息列表")
    private List<ChannelSkuInfoVO> channelSkuInfoList;
}
