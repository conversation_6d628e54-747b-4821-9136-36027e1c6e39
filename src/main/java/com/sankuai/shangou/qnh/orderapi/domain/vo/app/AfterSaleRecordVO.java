package com.sankuai.shangou.qnh.orderapi.domain.vo.app;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/7/10
 * desc: 售后信息详情
 */
@TypeDoc(
        description = "售后信息详情"
)
@ApiModel("售后信息详情")
@Data
public class AfterSaleRecordVO {

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID", required = true)
    private Long storeId;

    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户ID", required = true)
    private Long tenantId;

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID", required = true)
    private Integer channelId;

    @FieldDoc(
            description = "售后服务唯一ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "售后服务唯一ID", required = true)
    private Long serviceId;

    @FieldDoc(
            description = "是否需要审核", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "是否需要审核", required = true)
    private Integer isAudit;

    @FieldDoc(
            description = "售后状态 1:提交，3：审核中，4：已审核， 5：已申请驳回， 6：自动审核通过， 7：处理中， 9：已完成， 20：已取消", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "1:提交，3：审核中，4：已审核， 5：已申请驳回， 6：自动审核通过， 7：处理中， 9：已完成， 20：已取消", required = true)
    private Integer status;

    @FieldDoc(
            description = "申请原因", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "申请原因", required = true)
    private String applyReason;

    @FieldDoc(
            description = "售后模式, 1-整单退款，2-部分退款", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "售后模式, 1:整单退款，2：部分退款", required = true)
    private Integer afsPattern;

    @FieldDoc(
            description = "退款金额  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "退款金额  单位:分", required = true)
    private Integer refundAmt;

    @FieldDoc(
            description = "创建时间戳", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "创建时间戳", required = true)
    private Long createTime;

    @FieldDoc(
            description = "更新时间戳", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "更新时间戳", required = true)
    private Long updateTime;

    @FieldDoc(
            description = "售后申请类型   0-未知 1-售中申请  2-售后申请", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "售后申请类型   0-未知 1-售中申请  2-售后申请", required = true)
    private Integer afsApplyType;

    @FieldDoc(
            description = "售后申请人   0-未知 1-用户 2-商户 3-渠道", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "售后申请人   0-未知 1-用户 2-商户 3-渠道", required = true)
    private Integer whoApplyType;

    @FieldDoc(
            description = "售后申请详情列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "售后申请详情列表", required = true)
    private List<AfterSaleRecordDetailVO> afterSaleRecordDetailList;
}

