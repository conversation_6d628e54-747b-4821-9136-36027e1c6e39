package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023/9/17 21:50
 **/
@Data
public class PickItemVO {
    private String skuId;

    private Map<String, Integer> snCodeEnteringType;

    private String productName;

    private String attribute;

    private Integer shouldPickNum;

    private Integer stockoutEnteringType;

    private Boolean isSnProduct;

    private String spec;

    private Set<String> upc;

    private String picUrl;

    private List<String> realPicUrlList;

    private Integer expiration;

    private String expirationUnit;

    private Boolean isHighWacGoods;

    private Boolean isShortExpirationGoods;

    private Boolean showRealPic;

    private Boolean isSealDelivery;
}
