package com.sankuai.shangou.qnh.orderapi.domain.vo.app;

import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.sankuai.shangou.qnh.orderapi.enums.app.ShowTagEnum;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Data
public class ShowTag {

    /**
     * 标签文描
     */
    String tagDesc;

    /**
     * 标签枚举code
     */
    Integer tagCode;

    private ShowTag (String tagDesc, Integer tagCode) {
        this.tagDesc = tagDesc;
        this.tagCode = tagCode;
    }


    public static List<ShowTag> buildShowTagList(OCMSOrderVO ocmsOrderVO) {
        List<ShowTag> showTags = new ArrayList<>();
        if (Objects.equals(ocmsOrderVO.getWiderShippingArea(), true)) {
            showTags.add(new ShowTag(MccConfigUtil.getWiderShippingAreaHint(), ShowTagEnum.WIDER_SHIPPING_AREA.getCode()));

        }
        return showTags;
    }





}
