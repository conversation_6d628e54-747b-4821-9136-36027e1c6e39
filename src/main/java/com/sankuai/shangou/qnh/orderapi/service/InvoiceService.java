package com.sankuai.shangou.qnh.orderapi.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.meituan.shangou.saas.order.management.client.enums.StatusCodeEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.meituan.shangou.saas.tenant.thrift.ConfigThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.enums.ConfigItemEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.config.ConfigDto;
import com.meituan.shangou.saas.tenant.thrift.dto.config.request.ConfigQueryRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.config.response.TenantConfigResponse;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.sgfnqnh.finance.tax.thrift.dto.ShopInfo;
import com.sankuai.sgfnqnh.finance.tax.thrift.dto.response.ResultResponse;
import com.sankuai.sgfnqnh.finance.tax.thrift.dto.response.ShopInfoQueryResponse;
import com.sankuai.sgfnqnh.finance.tax.thrift.service.InvoiceQueryThriftService;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.request.invoice.FranchiseeInvoiceQueryRequest;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.request.invoice.OrderInvoiceApplyRecordRequest;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.response.dto.FranchiseeInvoiceDto;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.response.dto.InvoiceApplyRecordDto;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.response.invoice.FranchiseeInvoiceQueryResponse;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.response.invoice.OrderInvoiceApplyRecordListResponse;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.thrift.InvoiceRecordQueryThriftService;
import com.sankuai.shangou.qnh.orderapi.utils.pc.MccConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;


@Slf4j
@Service
public class InvoiceService {

    @Resource
    private InvoiceQueryThriftService invoiceQueryThriftService;
    @Resource
    private ConfigThriftService configThriftService;
    @Resource
    private InvoiceRecordQueryThriftService invoiceRecordQueryThriftService;

    /**
     * 判断该订单是否允许开票
     * 判断 【财务侧 && 门店级配置 && 租户级配置】
     * @param createInvoiceShopIdList
     * @return
     */
    public boolean checkOrderCreateInvoice(List<Long> createInvoiceShopIdList,
                                           Long shopId, Integer orderStatus,
                                           Long tenantId, String tenantInvoiceUrl,
                                           Map<String, Map<String,String>> configMap){
        try {
            // 校验租户或门店是否支持商家【开发票】操作
            if (!MccConfigUtil.checkMerchantSupportInvoiceTenantsAndShops(tenantId, shopId) || com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil.isDrunkHorseTenant(tenantId)) {
                // 不支持 || 歪马租户 --> 只检查财务侧是否支持
                return checkOrderCreateInvoice(createInvoiceShopIdList, shopId, orderStatus);
            }
            log.info("checkOrderCreateInvoice param: tenantId={}; shopId={}; orderStatus={}; tenantInvoiceUrl={}; createInvoiceShopIdList:{}; configMap:{}"
                    , tenantId,shopId,orderStatus,tenantInvoiceUrl,JSON.toJSONString(createInvoiceShopIdList),JSON.toJSONString(configMap));
            if (orderStatus == null) {
                return false;
            }
            // 订单状态为取消，不支持开票
            if (Objects.equals(OrderStatusEnum.CANCELED.getValue(), orderStatus)) {
                return false;
            }
            // 财务侧支持开发票 || 门店配置了发票链接 || 租户配置了发票链接，则支持开发票
            return (CollectionUtils.isNotEmpty(createInvoiceShopIdList) && createInvoiceShopIdList.contains(shopId))
                    || StringUtils.isNotBlank(getInvoiceUrlForShop(configMap, tenantId, shopId))
                    || StringUtils.isNotBlank(tenantInvoiceUrl);
        }catch (Exception e){
            log.error("checkOrderCreateInvoice is error", e);
            return false;
        }
    }

    public String getInvoiceUrlForShop(Map<String, Map<String,String>> configMap, Long tenantId, Long shopId){
        try {
            if(MapUtils.isEmpty(configMap) || tenantId == null || tenantId == 0L || shopId == null || shopId == 0L){
                log.warn("getInvoiceUrlForShop param is null, configMap={}, tenantId={}, shopId={}", JSON.toJSONString(configMap), tenantId, shopId);
                return null;
            }
            return Optional.ofNullable(configMap.get(tenantId.toString())).map(shopMap -> shopMap.get(shopId.toString())).orElse(null);
        }catch (Exception e){
            log.error("getInvoiceUrlForShop is error", e);
            return null;
        }
    }


    public Map<String, Map<String,String>> getInvoiceUrlShopConfig(){
        try {
            // lion读取门店二维码链接配置  {"tenantId":{"shopId1":"url1", "shopId2":"url2"}} 例：{"1000338":{"111111":"url1", "2222222":"url2"}}
            String configJson = MccConfigUtil.getInvoiceUrlForShopConfig();
            if(StringUtils.isBlank(configJson)){
                return null;
            }
            // 配置组成：Map<tenantId, Map<shopId, url>>
            return JSONObject.parseObject(configJson, new TypeReference<Map<String, Map<String,String>>>(){}.getType());
        }catch (Exception e){
            log.error("getInvoiceUrlShopConfig is error", e);
            return null;
        }
    }


    /**
     * 获取租户配置的发票url
     * @param tenantId
     * @return
     */
    public String getInvoiceUrlForTenant(Long tenantId){
        try {
            // 参数错误
            if(tenantId == null || tenantId == 0L){
                return null;
            }
            return Optional.ofNullable(getTenantConfig(tenantId, ConfigItemEnum.INVOICING_QR_CODE_LINK.getConfigId()))
                    .map(ConfigDto::getConfigContent)
                    .filter(StringUtils::isNotEmpty)
                    .map(JSONObject::parseObject)
                    .map(jsonObject -> jsonObject.getString(ConfigItemEnum.INVOICING_QR_CODE_LINK.getMainConfigKey()))
                    .orElse(StringUtils.EMPTY);
        }catch (Exception e){
            log.error("getInvoiceUrlForTenant is error", e);
            return null;
        }
    }

    /**
     * 获取租户配置
     *
     * @param tenantId
     * @param configId
     * @return
     */
    private ConfigDto getTenantConfig(Long tenantId, Integer configId) {
        try {
            ConfigQueryRequest configQueryRequest = new ConfigQueryRequest();
            configQueryRequest.setConfigId(configId);
            configQueryRequest.setTenantId(tenantId);
            configQueryRequest.setSubjectId(tenantId);
            TenantConfigResponse configResponse = configThriftService.queryTenantConfig(configQueryRequest);
            log.info("getTenantConfig request:{},resp:{}", JSON.toJSONString(configQueryRequest), JSON.toJSONString(configResponse));
            if (configResponse == null || configResponse.getConfig() == null) {
                return null;
            }
            return configResponse.getConfig();
        } catch (Exception e) {
            log.error("getTenantConfig error, tenantId:{}, configId:{}, error:{}", tenantId, configId, e);
            throw new BizException("get tenant config error, tenantId:" + tenantId + ", configId:" + configId);
        }
    }

    /**
     * 判断该订单是否允许开票
     * 仅判断【财务侧】
     * @param createInvoiceShopIdList
     * @return
     */
    public boolean checkOrderCreateInvoice (List<Long> createInvoiceShopIdList, Long shopId, Integer orderStatus){
        if(CollectionUtils.isEmpty(createInvoiceShopIdList) || shopId == null || shopId == 0L || orderStatus == null){
            return false;
        }
        return createInvoiceShopIdList.contains(shopId) && !Objects.equals(OrderStatusEnum.CANCELED.getValue(), orderStatus);
    }

    /**
     * 获取租户下具有开票权限的门店ID
     * @param tenantId
     * @return
     */
    public List<Long> getCreateInvoiceShopList(Long tenantId){
        try {
            ResultResponse<ShopInfoQueryResponse> response = invoiceQueryThriftService.queryAllShopByTenantId(tenantId);
            log.info("invoke queryAllShopByTenantId,tenantId = {},response = {}", tenantId, JSON.toJSONString(response));
            if(response.isSuccess() && Objects.nonNull(response.getData()) &&  CollectionUtils.isNotEmpty(response.getData().getShopInfoList())) {
                return response.getData().getShopInfoList().stream().map(ShopInfo::getShopId).collect(Collectors.toList());
            }
        }catch (Exception e){
            log.warn("invoke FuseOrderServiceImpl.getCreateInvoiceShopList error! tenantId={}", tenantId, e);
        }
        return Collections.emptyList();
    }

    /**
     * 直营门店开发票记录
     *
     * @param viewOrderId
     * @return
     */
    public List<InvoiceApplyRecordDto> queryInvoiceRecord(String viewOrderId) {
        try {
            OrderInvoiceApplyRecordListResponse response = invoiceRecordQueryThriftService
                    .queryOrderInvoiceApplyRecordList(new OrderInvoiceApplyRecordRequest(viewOrderId));
            if (Objects.nonNull(response) && response.getCode() == StatusCodeEnum.SUCCESS.getCode()) {
                return response.getInvoiceApplyRecordList();
            }
            log.warn("queryInvoiceRecord failed viewOrderId:{}, response:{}", viewOrderId, response);
        } catch (Exception ex) {
            log.error("viewOrderId:{} queryInvoiceRecord error", viewOrderId, ex);
        }
        return null;
    }

    /**
     * 加盟门店开发票记录
     *
     * @param viewOrderId
     * @param createTime 订单创建时间
     * @return
     */
    public List<FranchiseeInvoiceDto> queryFranchiseeInvoiceList(String viewOrderId, Long createTime) {
        try {
            if (StringUtils.isBlank(viewOrderId) || Objects.isNull(createTime)) {
                return null;
            }
            FranchiseeInvoiceQueryRequest request = new FranchiseeInvoiceQueryRequest();
            request.setViewOrderId(viewOrderId);
            // 查询订单创建时间到当前时间到开发票记录
            request.setCreateStartTime(createTime);
            request.setCreateEndTime(System.currentTimeMillis());
            request.setPage(1);
            request.setSize(10);
            FranchiseeInvoiceQueryResponse response = invoiceRecordQueryThriftService
                    .queryFranchiseeInvoiceList(request);
            if (Objects.nonNull(response) && response.getCode() == StatusCodeEnum.SUCCESS.getCode()) {
                return response.getFranchiseeInvoiceRecordList();
            }
            log.warn("queryFranchiseeInvoiceList failed viewOrderId:{}, response:{}", viewOrderId, response);
        } catch (Exception ex) {
            log.error("viewOrderId:{} queryFranchiseeInvoiceList error", viewOrderId, ex);
        }
        return null;
    }
}

