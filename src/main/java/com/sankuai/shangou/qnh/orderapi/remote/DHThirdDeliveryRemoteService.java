package com.sankuai.shangou.qnh.orderapi.remote;

import com.google.common.collect.Maps;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.RiderPickingThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.QueryThirdDeliveryOrderCountResponse;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.delivery.poi.SelfDeliveryPoiConfigThriftService;
import com.sankuai.shangou.logistics.delivery.poi.dto.SelfDeliveryPoiConfigDTO;
import com.sankuai.shangou.logistics.warehouse.TradeShippingCountService;
import com.sankuai.shangou.logistics.warehouse.dto.ThirdShippingCountDTO;
import com.sankuai.shangou.qnh.orderapi.constant.app.IntegerBooleanConstants;
import com.sankuai.shangou.qnh.orderapi.context.app.ApiMethodParamThreadLocal;
import com.sankuai.shangou.qnh.orderapi.domain.dto.app.DeliveryOperateOrderKey;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.ThirdWaitToDeliverySubTypeCountResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.OrderVO;
import com.sankuai.shangou.qnh.orderapi.enums.app.AuthCodeEnum;
import com.sankuai.shangou.qnh.orderapi.enums.app.OrderCouldOperateItemEnum;
import com.sankuai.shangou.qnh.orderapi.utils.app.DeliveryChannelUtils;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * @program: qnh_order_api
 * @description:
 * @author: jinyi
 * @create: 2023-10-23 19:48
 **/
@Service
@Slf4j
public class DHThirdDeliveryRemoteService {

    @Autowired
    private TmsRemoteService tmsRemoteService;

    @Resource
    private RiderPickingThriftService riderPickingThriftService;

    @Autowired
    private AuthRemoteService authRemoteService;

    @Resource
    private SelfDeliveryPoiConfigThriftService selfDeliveryPoiConfigThriftService;

    @Resource
    private TradeShippingCountService tradeShippingCountService;

    @Resource
    private DeliveryChannelRemoteService deliveryChannelRemoteService;


    /**
     * 查询待配送子tab数量
     * @return
     */
    public ThirdWaitToDeliverySubTypeCountResponse queryWaitDeliverySubTypeCount() {
        QueryThirdDeliveryOrderCountResponse statusCountResponse =
                tmsRemoteService.queryThirdDeliveryOrderCountByStatusList(getTenantId(), getStoreId());
        Integer exceptionCount = tmsRemoteService.countDeliveryExceptionOrCanceledOrder(getStoreId(),getTenantId());
        return buildResponse(statusCountResponse, exceptionCount);
    }

    private static Long getTenantId() {
        if (ApiMethodParamThreadLocal.getIdentityInfo() == null || ApiMethodParamThreadLocal.getIdentityInfo().getUser() == null) {
            throw new BizException("未获取到用户信息，请先登录");
        }

        return ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
    }

    private static Long getStoreId() {
        if (ApiMethodParamThreadLocal.getIdentityInfo() == null) {
            throw new BizException("未获取到用户信息，请先登录");
        }

        return ApiMethodParamThreadLocal.getIdentityInfo().getStoreId();
    }

    private static Long getAccountId() {
        if (ApiMethodParamThreadLocal.getIdentityInfo() == null) {
            throw new BizException("未获取到用户信息，请先登录");
        }

        return ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId();
    }

    private ThirdWaitToDeliverySubTypeCountResponse buildResponse(QueryThirdDeliveryOrderCountResponse statusCountResponse, Integer exceptionCount) {
        ThirdWaitToDeliverySubTypeCountResponse response = new ThirdWaitToDeliverySubTypeCountResponse();
        int allWaitToDeliveryCount = statusCountResponse.getWaitPlatformAcceptCount()
                + statusCountResponse.getWaitRiderAcceptCount()
                + statusCountResponse.getRiderAcceptedCount()
                + statusCountResponse.getWaitRiderArriveShopCount()
                + statusCountResponse.getRiderDeliveringCount();

        response.setAllSubTypeCount(allWaitToDeliveryCount);
        response.setWaitToLaunchDeliveryCount(statusCountResponse.getWaitPlatformAcceptCount());
        response.setWaitToRiderAcceptCount(statusCountResponse.getWaitRiderAcceptCount());
        response.setWaitToTakeGoodsCount(statusCountResponse.getRiderAcceptedCount() + statusCountResponse.getWaitRiderArriveShopCount());
        response.setDeliveringCount(statusCountResponse.getRiderDeliveringCount());
        response.setExceptionCount(exceptionCount);
        return response;
    }

    /**
     * 查询待领取拣货单数量
     * @return
     */
    @MethodLog(logResponse = true, logRequest = true)
    @Deprecated
    public Integer queryStoreUnAcceptedPickOrderCount() {
        return riderPickingThriftService.queryStoreUnAcceptedPickOrderCount(getTenantId(), getStoreId());
    }

    /**
     * 查询待拣货数量
     * @return
     */
    @MethodLog(logResponse = true, logRequest = true)
    @Deprecated
    public Integer queryWaitPickOrderCount() {
        return riderPickingThriftService.queryRiderWaitToPickOrderCount(getTenantId(), getStoreId(), getAccountId());
    }

    @MethodLog(logResponse = true, logRequest = true)
    public Pair<Integer, Integer> queryUnAcceptedPickOrderCountAndWaitPickCount() {
        try {
            ThirdShippingCountDTO thirdShippingCountDTO = tradeShippingCountService.queryThirdShippingCount(getStoreId(), getTenantId(), getAccountId());
            return Pair.of(thirdShippingCountDTO.getStoreUnAcceptedCount(), thirdShippingCountDTO.getWaitPickOrderCount());
        } catch (Exception e) {
            log.error("queryUnAcceptedPickOrderCountAndWaitPickCount error", e);
            return Pair.of(0, 0);
        }
    }


    public List<Integer> getDeliveryChannelButtons(long tenantId, long storeId, OrderVO order) {
        DeliveryOperateOrderKey deliveryOperateOrderKey =  new DeliveryOperateOrderKey();
        deliveryOperateOrderKey.setOrderId(order.getEmpowerOrderId());
        deliveryOperateOrderKey.setActualPayAmt(order.getActualPayAmt());
        if(DeliveryChannelUtils.isDapDeliveryChannel(order.getDeliveryChannelId())) {
            deliveryOperateOrderKey.setDeliveryPlatformEnum(DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM);
        } else if(DeliveryChannelUtils.isMerchantDeliveryChannel(order.getDeliveryChannelId())) {
            deliveryOperateOrderKey.setDeliveryPlatformEnum(DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY);
        }
        deliveryOperateOrderKey.setOrderStatusEnum(OrderStatusEnum.enumOf(order.getOrderStatus()));
        deliveryOperateOrderKey.setScene(order.getScene());
        deliveryOperateOrderKey.setIsSealOrder(order.getIsSealDelivery());
        Set<DeliveryOperateOrderKey> deliveryOperateSet = new HashSet<>();
        deliveryOperateSet.add(deliveryOperateOrderKey);
        Map<DeliveryOperateOrderKey, List<Integer>> deliveryOperateMap = fillDeliveryOperateItemsForAllOrder(tenantId, storeId, deliveryOperateSet);
        List<Integer> deliveryOperateList = deliveryOperateMap.get(deliveryOperateOrderKey);
        if(CollectionUtils.isNotEmpty(deliveryOperateList)) {
            return deliveryOperateList;
        }
        return new ArrayList<>();
    }

    public Map<DeliveryOperateOrderKey, List<Integer>> fillDeliveryOperateItemsForAllOrder(long tenantId, long storeId, Set<DeliveryOperateOrderKey> deliveryOperateOrderKeys) {
        if (CollectionUtils.isEmpty(deliveryOperateOrderKeys)) {
            return Maps.newHashMap();
        }
        Map<DeliveryOperateOrderKey, List<Integer>> resultMap = Maps.newHashMap();
        log.info("fillDeliveryOperateItemsForAllOrder request, tenantId = {}, storeId = {}, deliveryOperateOrderKeys = {}", tenantId, storeId, deliveryOperateOrderKeys);
        try {
            //非歪马租户直接返回
            if (!MccConfigUtil.getDHTenantIdList().contains(String.valueOf(tenantId))) {
                log.info("非歪马租户，不展示按钮。tenant ={}", tenantId);
                return Maps.newHashMap();
            }
            //1.操作人要有权限
            Map<String, Boolean> permissions = authRemoteService.isHasPermissionV2(Lists.newArrayList(AuthCodeEnum.TURN_AGG_DELIVERY.getAuthCode(), AuthCodeEnum.TURN_SELF_DELIVERY.getAuthCode()));
            boolean hasTurnAggAuth = permissions.getOrDefault(AuthCodeEnum.TURN_AGG_DELIVERY.getAuthCode(), Boolean.FALSE);
            boolean hasTurnSelfAuth = permissions.getOrDefault(AuthCodeEnum.TURN_SELF_DELIVERY.getAuthCode(), Boolean.FALSE);

            //校验转三方配置
            TResult<SelfDeliveryPoiConfigDTO> selfDeliveryPoiConfigDTOTResult = selfDeliveryPoiConfigThriftService.querySelfDeliveryConfig(tenantId, storeId);
            if (!selfDeliveryPoiConfigDTOTResult.isSuccess() || Objects.isNull(selfDeliveryPoiConfigDTOTResult.getData())) {
                throw new BizException("查询错误配送配置错误");
            }
            for (DeliveryOperateOrderKey deliveryOperateOrderKey : deliveryOperateOrderKeys) {
                if (Objects.equals(deliveryOperateOrderKey.getDeliveryPlatformEnum(), DeliveryPlatformEnum.MERCHANT_SELF_DELIVERY)) {
                    boolean canTurnAgg = Objects.equals(selfDeliveryPoiConfigDTOTResult.getData().getEnableTurnDelivery(), IntegerBooleanConstants.BOOLEAN_TRUE);
                    boolean actualPayAmtLimit = deliveryOperateOrderKey.getActualPayAmt() < MccConfigUtil.getMaxActualPayAmtForThirdDelivery();
                    boolean orderStatusAfterConfirmed = deliveryOperateOrderKey.getOrderStatusEnum().getValue() >= OrderStatusEnum.MERCHANT_CONFIRMED.getValue();
                    boolean orderStatusNotCompleted = !Objects.equals(deliveryOperateOrderKey.getOrderStatusEnum(), OrderStatusEnum.COMPLETED) && !Objects.equals(deliveryOperateOrderKey.getOrderStatusEnum(), OrderStatusEnum.CANCELED);
                    boolean notLimitScene = true;
                    boolean isSealOrder = deliveryOperateOrderKey.getIsSealOrder() != null ? deliveryOperateOrderKey.getIsSealOrder() : false;
                    if (StringUtils.isNotBlank(deliveryOperateOrderKey.getScene()) && MccConfigUtil.getDhTurnAggLimitSceneList().contains(deliveryOperateOrderKey.getScene())) {
                        notLimitScene = false;
                    }

                    //有权限 && 开启自配 && 实付小于150 && 接单<订单状态<终态 && 非指定场景
                    if (hasTurnAggAuth && canTurnAgg && actualPayAmtLimit && orderStatusAfterConfirmed && orderStatusNotCompleted && notLimitScene && !isSealOrder) {
                        resultMap.put(deliveryOperateOrderKey, Lists.newArrayList(OrderCouldOperateItemEnum.TURN_AGG_DELIVERY.getValue() ));
                    }

                } else if (Objects.equals(deliveryOperateOrderKey.getDeliveryPlatformEnum(), DeliveryPlatformEnum.DAP_DELIVERY_PLATFORM)) {
                    boolean orderStatusAfterConfirmed = deliveryOperateOrderKey.getOrderStatusEnum().getValue() >= OrderStatusEnum.MERCHANT_CONFIRMED.getValue();
                    boolean orderStatusNotCompleted = !Objects.equals(deliveryOperateOrderKey.getOrderStatusEnum(), OrderStatusEnum.COMPLETED) && !Objects.equals(deliveryOperateOrderKey.getOrderStatusEnum(), OrderStatusEnum.CANCELED);
                    //有权限 && 接单<订单状态<终态
                    if (hasTurnSelfAuth && orderStatusAfterConfirmed && orderStatusNotCompleted) {
                        resultMap.put(deliveryOperateOrderKey, Lists.newArrayList(OrderCouldOperateItemEnum.TURN_SELF_DELIVERY.getValue() ));
                    }
                }
            }
            log.info("fillDeliveryOperateItemsForAllOrder response, resultMap = {}", resultMap);
            return resultMap;
        } catch (Exception e) {
            log.error("fillDeliveryOperateItemsForAllOrder error");
            return resultMap;
        }
    }

}
