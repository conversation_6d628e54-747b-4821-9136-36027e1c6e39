package com.sankuai.shangou.qnh.orderapi.domain.request.app;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.WeightRefundProductVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2020-03-25 14:26
 * @Description:
 */
@TypeDoc(
        description = "发起按克重退差价请求"
)
@ApiModel("按克重退差价请求")
@Data
public class RefundByWeightRequest {
    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID")
    private Integer channelId;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道订单号")
    private String channelOrderId;

    @FieldDoc(
            description = "克重退款商品列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "克重退款商品列表")
    private List<WeightRefundProductVO> weightRefundProductVOList;
}
