package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

@TypeDoc(
        description = "分页查询商品请求"
)
@Data
@ApiModel("分页查询商品请求")
public class SkuListPageForAppRequest {

    @FieldDoc(
            description = "第几页", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "第几页", required = true)
    @NotNull
    private Integer page;

    @FieldDoc(
            description = "每页行数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "每页行数", required = true)
    @NotNull
    private Integer size;

    @FieldDoc(
            description = "搜索参数", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "搜索参数")
    private String keyword;

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID")
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "商家类目编号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商家类目编号")
    private List<String> categories;

    @FieldDoc(
            description = "上下线状态 -1-未上传 1-已上架 2-未上架", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "上下线状态  -1-未上传 1-已上架 2-未上架")
    private Integer skuStatus;


    @FieldDoc(
            description = "渠道id列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道id列表")
    private List<Integer> channelIds;

    @FieldDoc(
            description = "分渠道门店分类信息 channelId -> storeCategoryId", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "分渠道门店分类信息")
    private Map<Integer, String> channelStoreCategoryMap;

    @FieldDoc(
            description = "美团渠道商品是否可售,0-全部,1-可售,2-不可售", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "美团渠道商品是否可售")
    private Integer mtAllowSale;
}
