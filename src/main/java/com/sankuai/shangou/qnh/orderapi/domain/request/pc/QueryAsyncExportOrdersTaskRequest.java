package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Author: <EMAIL>
 * @Date: 2019-10-31 21:39
 * @Description:
 */
@TypeDoc(
        description = "查询异步导出订单详情任务结果请求"
)
@ApiModel(value = "查询异步导出订单详情任务结果")
@Setter
@Getter
@ToString
public class QueryAsyncExportOrdersTaskRequest implements BaseRequest {
    @FieldDoc(
            description = "任务token"
    )
    @ApiModelProperty(value = "任务token")
    private String token;

}
