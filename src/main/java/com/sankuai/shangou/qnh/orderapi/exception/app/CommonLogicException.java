package com.sankuai.shangou.qnh.orderapi.exception.app;


import com.sankuai.shangou.qnh.orderapi.enums.app.ResultCodeEnum;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018/3/8
 */
public class CommonLogicException extends RuntimeException {

    private ResultCodeEnum resultCodeEnum;
    private transient Object[] text;

    private boolean setMessage;

    public CommonLogicException() {
    }

    public CommonLogicException(String message) {
        super(message);
        setMessage = true;
    }

    public CommonLogicException(String message, Throwable cause) {
        super(message, cause);
        setMessage = true;
    }

    public CommonLogicException(Throwable cause) {
        super(cause);
    }

    public CommonLogicException(String message, ResultCodeEnum code) {
        super(message);
        setMessage = true;
        this.resultCodeEnum = code;
    }

    public CommonLogicException(String message, Throwable cause, ResultCodeEnum code) {
        super(message, cause);
        setMessage = true;
        this.resultCodeEnum = code;
    }

    public CommonLogicException(Throwable cause, ResultCodeEnum codeEnum) {
        super(cause);
        this.resultCodeEnum = codeEnum;
    }

    public CommonLogicException(ResultCodeEnum codeEnum) {
        this.resultCodeEnum = codeEnum;
    }

    public CommonLogicException(ResultCodeEnum codeEnum, Object... objects) {
        this.resultCodeEnum = codeEnum;
        this.text = objects;
    }

    public ResultCodeEnum getResultCode() {
        return resultCodeEnum;
    }

    public Object[] getText() {
        return text;
    }

    public boolean isSetMessage() {
        return setMessage;
    }
}