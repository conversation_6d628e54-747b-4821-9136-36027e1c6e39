package com.sankuai.shangou.qnh.orderapi.constant.pc;

/**
 * 配置系统Key常量
 *
 * <AUTHOR>
 */
public abstract class ConfigKeyConstant {

    /**
     * 应用key
     **/
    public static final String OCTO_APP_KEY = "octo.appkey";
    public static final String APP_KEY = "app.key";

    /**
     * applicationContext-client方法调用日志开关配
     **/
    public static final String THRIFT_CLIENT_METHOD_LOG_SWITCH = "thrift.client.method.log.switch";
    /**
     * 方法调用日志开关配
     **/
    public static final String METHOD_LOG_SWITCH = "method.log.switch";

    /**
     * 输入验证码开关
     **/
    public static final String INPUT_VERIFICATION_CODE_SWITCH = "input.verification.code.switch";
    /**
     * 登录秘钥
     **/
    public static final String LOGIN_SECRET_KEY = "login.secret.key";
    /**
     * 登录过期时间
     **/
    public static final String LOGIN_EXPIRE_TIME_IN_SECONDS = "login.expire.time.in.seconds";
    /**
     * 允许访问域名白名单
     **/
    public static final String ALLOW_DOMAIN_WHITELIST = "allow.domain.whitelist";
    /**
     * 用户列表
     **/
    public static final String USER_LIST = "user.list";
    /**
     * 用户租户关系
     **/
    public static final String USER_TENANT = "user.tenant";
    /**
     * 自定义发券模板MCC key
     */
    public static final String COUPON_CUSTOM_DELIVER_SMS_TEMPLATE_KEY = "COUPON_CUSTOM_DELIVER_SMS_TEMPLATE_KEY";

    /**
     * 仓库id
     **/
    public static final String SHELF_REPOSITORY_ID = "shelf_repository_id";
    /**
     * 仓库类型
     **/
    public static final String SHELF_REPOSITORY_TYPE = "shelf_repository_type";
    /**
     * 仓库接口租户id
     **/
    public static final String SHELF_TENANT_ID = "shelf_tenant_id";

    public static final String SHELF_DEFAULT_IMAGE = "shelf_default_img";

    /**
     * crm后台重置会员卡短信模版号
     **/
    public static final String SMS_TEMPLATE_RESET_PASS = "sms.template.resetpwd";

    /**
     * 储值卡流水导出最大记录数
     */
    public static final String ASSET_FLOW_EXPORT_MAX_ROWS_LIMIT = "asset.flow.export.max.rows.limit";

    /**
     * 储值卡流水导出每页查询记录数
     */
    public static final String ASSET_FLOW_EXPORT_QUERY_PAGE_SIZE = "asset.flow.export.query.page.size";

    /**
     * 选仓过期时间
     */
    public static final String SELECT_REPO_EXPIRE_TIME_IN_SECONDS = "select.repo.expire.time.in.seconds";
    /**
     * 摊位结算列表导出查询每页记录数
     */
    public static final String BOOTH_SETTLEMENT_EXPORT_QUERY_PAGE_SIZE = "booth.settlement.export.query.page.size";

    /**
     * 评价导出最大记录数
     */
    public static final String COMMENT_EXPORT_MAX_ROWS_LIMIT = "comment.export.max.rows.limit";

    /**
     * 评价导出每页查询记录数
     */
    public static final String COMMENT_EXPORT_QUERY_PAGE_SIZE = "comment.export.query.page.size";

    /**
     * 优惠券活动配置配置
     */
    public static final String COUPON_RECEIVE_ACTIVITY_POSITION = "coupon.receive.activity.position";

    /**
     * 会员下载分页查询数量
     */
    public static final String MEMBER_DOWNLOAD_PAGE_SIZE = "member.download.page.size";
    /**
     * 积分流水下载分页查询数量
     */
    public static final String SCORE_FLOW_DOWNLOAD_PAGE_SIZE = "score.flow.download.page.size";
    /**
     * 优惠券下载分页查询数量
     */
    public static final String COUPON_DOWNLOAD_PAGE_SIZE = "coupon.download.page.size";
    /**
     * 一门店多摊位租户列表
     */
    public static final String ONE_POI_WITH_SEVERAL_BOOTH_TENANT_LIST = "one.poi.with.several.booth.tenant.list";
    /**
     * 支持门店商品操作商品摊位关联的租户列表
     */
    public static final String SUPPORT_OPERATE_SKU_BOOTH_INFO_TENANT_LIST = "support.operate.sku.booth.info.tenant.list";
    /**
     * 线上商品拉取明细下载分页查询数量
     */
    public static final String PULL_SKU_DETAIL_DOWNLOAD_PAGE_SIZE = "pull.sku.detail.download.page.size";


    public static final String OCMS_TASK_SWITCH = "ocms.task.switch";

    public static final String STORE_NEW_LOGIC_TASK_SWITCH = "store.new.logic.task.switch";

    public static final String CDQ_TENANT_IDS = "cdq.tenant.ids";

    public static final String CDQ_TENANT_SPU_IDS = "cdq.tenant.spu.ids";


    public static final String CDQ_TENANT_POI_PRODUCT_SWITCH = "cdq.poi.product.switch";

    public static final String PRICE_MIGRATE_SWITCH = "price.migrate.switch";

    public static final String CDQ_BOOTH_BANKCARD_MESSAGE_NO = "cdq.booth.msg.no";

    public static final String OCMS_INTERFACE_SWITCH = "ocms.tenant.sku.interface.switch";
    public static final String REJECT_REASON_TYPE_CONFIG = "reject.reason.type.config";

    public static final String API_REJECT_SWITCH = "reject.all.switch";

    public static final String API_REJECT_TEXT = "reject.text";

    public static final String PRICE_TREND_ICON_DIRECT_SHOW = "price.trend.icon.direct.show";

    public static final String EXCLUDE_AUTH_PERMISSION_CODES = "exclude.auth.permission.codes";

    public static final String DOWNLOAD_MCC_CONFIG = "download.mcc.config";

    /**
     * 签约文案
     */
    public static final String TRANSFER_STATUS_MESSAGE = "transfer.status.message";

    /**
     * 区域规划-MCC账号权限的配置KEY
     */
    public static final String REGION_SELECTION_USER_LIST = "region.selection.user.list";


    public static final String NEED_PULL_POI = "poi.pull.auto_create";
    public static final String FULFILL_CONFIG_FROM_WMS_CONFIG_STORE_IDS_MCC_KEY = "fulfill.config.from.wms.config.store.ids";
    /**
     * 禁用自动刷新token
     */
    public static final String DISABLE_REFRESH_TICKET = "disable_refresh_ticket";

    public static final String QUERY_TASK_PERMISSION_SWITCH = "query.task.permission.switch";
    public static final String VALID_ACCOUNT_BIZ_APP_ID = "valid_account_biz_app_id";
    public static final String SAVE_POI_SKU_CONVERT_EXCEL = "save_poi_sku_conver_excel";
    public static final String VERSION_SUFFIX = ".version";

    //合作商模式租户列表
    public static final String AGENT_TENANT_LIST = "agent.tenant.list";

    // 使用履约模块化的租户列表
    public static final String FULFILL_MODULAR_OPEN = "fulfill.modular.open";

    public static final String ORDER_RECV_ADDRESS_PERMISSION_CODE = "order.recv.address.permission.code";

    /**
     * 牵牛花ka租户渠道配置key
     */
    public static final String QNH_KA_CHANNELS = "qnh_ka_channels";
    /**
     * token治理灰度租户配置
     */
    public static final String TOKEN_ENCRYPT_TENANT_LIST = "token.encrypt.tenant.id.list";

    // 查询组织结构树时，场景与节点类型的映射配置
    public static final String DEPARTMENT_TREE_MAP_SCENE = "department.tree.map.of.scene.nodetype";


    /**
     * 采购-要货单最大允许查询数量限制 key
     */
    public static final String PURCHASE_MAX_QUERY_POI_SIZE_KEY = "purchase.max.query.poi.size.key";

    /**
     * 采购最大允许查询数量限制 key
     */
    public static final String PURCHASE_ES_MAX_QUERY_POI_SIZE_KEY = "purchase.es.max.query.poi.size.key";

    public static final String PURCHASE_RECEIPT_REFRESH_TIME = "purchase.receipt.refresh.time";
}
