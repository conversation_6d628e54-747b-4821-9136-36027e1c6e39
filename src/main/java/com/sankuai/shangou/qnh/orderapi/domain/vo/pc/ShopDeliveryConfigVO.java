package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/16
 * desc: 门店配送配置
 */
@Getter
@Setter
public class ShopDeliveryConfigVO {

    @FieldDoc(
            description = "门店ID"
    )
    @ApiModelProperty(value = "门店ID")
    private String poiId;

    @FieldDoc(
            description = "门店名称"
    )
    @ApiModelProperty(value = "门店名称")
    private String poiName;

    @FieldDoc(
            description = "配送门店列表"
    )
    @ApiModelProperty(value = "配送门店列表")
    private List<DeliveryShopVO> list;
}
