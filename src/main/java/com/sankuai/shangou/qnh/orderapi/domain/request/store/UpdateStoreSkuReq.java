package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.CreateStoreSkuVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/11/20
 * desc: 编辑门店商品请求
 */
@TypeDoc(
        description = "编辑门店商品请求"
)
@Data
@ApiModel("编辑门店商品请求")
public class UpdateStoreSkuReq {

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID")
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "编辑门店商品参数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "编辑门店商品参数")
    @NotNull
    private CreateStoreSkuVO storeSku;

    @FieldDoc(
            description = "分渠道门店分类信息 channelId -> storeCategoryId", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "分渠道门店分类信息")
    private Map<Integer, String> channelStoreCategoryMap;

    @FieldDoc(
            description = "业务类型 1-商超 2-买菜 默认为1", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "业务类型")
    private Integer bizType;
}
