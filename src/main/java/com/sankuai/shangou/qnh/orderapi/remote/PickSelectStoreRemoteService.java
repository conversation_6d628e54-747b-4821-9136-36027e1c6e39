package com.sankuai.shangou.qnh.orderapi.remote;

import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.meituan.shangou.saas.tenant.thrift.common.enums.BizSourceEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.SceneTypeEnum;
import com.sankuai.meituan.reco.pickselect.consts.ResultCodeEnum;
import com.sankuai.meituan.reco.pickselect.thrift.picking.PickingThriftService;
import com.sankuai.meituan.reco.pickselect.thrift.picking.dto.PickWODetailDTO;
import com.sankuai.meituan.reco.pickselect.thrift.picking.request.PickWODetailRequest;
import com.sankuai.meituan.reco.pickselect.thrift.picking.response.PickWODetailResponse;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.RiderPickingThriftService;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.dto.RiderPickWorkOrderDTO;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.request.QueryRiderPickWorkOrderRequest;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.response.RiderPickWorkOrderResponse;
import com.sankuai.meituan.reco.pickselect.thrift.print.OpenPrintRequest;
import com.sankuai.meituan.reco.pickselect.thrift.print.OpenPrintResponse;
import com.sankuai.meituan.reco.pickselect.thrift.print.OpenPrintThriftService;
import com.sankuai.meituan.reco.pickselect.thrift.print.PrintResultReq;
import com.sankuai.meituan.reco.pickselect.thrift.print.PrintResultResp;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ResponseCodeEnum;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.QueryPrintStatusResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.User;
import com.sankuai.shangou.qnh.orderapi.exception.store.CommonRuntimeException;
import com.sankuai.shangou.qnh.orderapi.utils.store.ApiMethodParamThreadLocal;
import com.sankuai.shangou.qnh.orderapi.utils.store.MccConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/***
 * author : <EMAIL> 
 * data : 2021/3/15 
 * time : 下午7:35
 **/
@Service
@Slf4j
public class PickSelectStoreRemoteService {

    @Resource
    private OpenPrintThriftService.Iface openPrintThriftService;

    @Resource
    private RiderPickingThriftService riderPickingThriftService;

    @Resource
    private PickingThriftService pickingThriftService;

    public OpenPrintResponse printReceipt(int orderBizType, String viewOrderId, String storeId) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        OpenPrintRequest request = new OpenPrintRequest();
        request.setSource(orderBizType);
        request.setUnifyOrderId(viewOrderId);
        request.setPrintBizSource(BizSourceEnum.OCMS.getSource());
        Long operatorId = user.getAccountId();
        String operator = user.getUsername();
        if (operatorId != null){
            request.setOperateId(operatorId);
        }
        request.setOperateName(operator);
        request.setAppId(ApiMethodParamThreadLocal.getIdentityInfo().getAppId());
        request.setOperatorAccountId(user.getAccountId());
        request.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        if (StringUtils.isNotEmpty(storeId)) {
            request.setOfflineStoreId(Long.parseLong(storeId));
        }
        log.info("调用拣货打印小票，req:{}", request);
        try {
            OpenPrintResponse printResponse = openPrintThriftService.doPrint(request);
            log.info("打印小票结果:{}, order:{}", printResponse, viewOrderId);
            return printResponse;
        }catch (Exception e){
            log.error("打印小票失败", e);
            OpenPrintResponse response = new OpenPrintResponse();
            response.setCode(ResponseCodeEnum.THRIFT_SERVICE_ERROR.getValue());
            response.setMsg(ResultCode.INTERNAL_SERVER_ERROR.getErrorMessage());
            return response;
        }
    }

    public QueryPrintStatusResponse queryPrintStatus(String printId) throws TException {
        PrintResultReq printResultReq = new PrintResultReq();
        printResultReq.setRequestId(printId);
        printResultReq.setSceneType(SceneTypeEnum.PRINT_FOR_CUSTOMER.getKey());
        PrintResultResp printResultResp = openPrintThriftService.checkPrintResult(printResultReq);
        log.info("获取打印状态,printId:{}, resp:{}", printId, printResultResp);
        if (printResultResp.getCode() != ResultCode.SUCCESS.getCode()){
            throw new BizException("获取打印状态失败:" + printResultResp.getMsg());
        }
        QueryPrintStatusResponse resp = new QueryPrintStatusResponse();
        resp.setMsg(printResultResp.getMsg());
        resp.setPrintStatus(printResultResp.getPrintStatus());
        return resp;
    }

    /**
     * 查询拣货工单详情
     */
    @MethodLog(logRequest = true,logResponse = true)
    public RiderPickWorkOrderDTO getPickWorkOrder(String unifyOrderId, Integer channelId, Long storeId) {
        QueryRiderPickWorkOrderRequest tRequest = new QueryRiderPickWorkOrderRequest();

        tRequest.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        tRequest.setEmpowerStoreId(storeId);
        tRequest.setAccountId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId());
        tRequest.setOrderSource(ChannelOrderConvertUtils.sourceMid2Biz(channelId));
        tRequest.setUnifyOrderId(unifyOrderId);

        // 1. thrift 调用
        RiderPickWorkOrderResponse response = null;
        try {
            response = riderPickingThriftService.queryPickWorkOrderDetail(tRequest);
            log.info("Call RiderPickingThriftService#queryPickWorkOrderDetail. request:{} response:{}", tRequest, response);
        } catch (Exception e) {
            log.error("Call RiderPickingThriftService#queryPickWorkOrderDetail error. request:{}", tRequest, e);
            throw new CommonRuntimeException(ResultCode.INTERNAL_SERVER_ERROR.defaultMessage);
        }
        // 2. 处理返回结果
        if (response == null || response.getStatus() == null ||
                response.getStatus().getCode() != com.sankuai.meituan.reco.pickselect.consts.ResultCodeEnum.SUCCESS.getCode()) {
            log.warn("获取拣货工单信息失败, request:{}, response:{}", tRequest, response);
            throw new CommonRuntimeException(response.getStatus().getMessage());
        }
        return response.getPickWorkOrderDto();
    }

    /**
     * 查询对应类型工单详情
     */
    public List<PickWODetailDTO> getPickWorkOrder4OrderIdAndTypeList(Long orderId, List<Integer> workTypeCodeList,Long tenantId,Long offlineStoreId) {
        try {
            PickWODetailResponse response = null;
            if(MccConfigUtil.getPickSelectQueryJoinSwitch(tenantId)){
                PickWODetailRequest request = new PickWODetailRequest();
                request.setOrderId(orderId);
                request.setWorkTypeList(workTypeCodeList);
                request.setTenantId(tenantId);
                request.setOfflineStoreId(offlineStoreId);
                response = pickingThriftService.queryWODetailByOrderIdAndTypeListWithTenant(request);
            }else {
                response = pickingThriftService.queryWODetailByOrderIdAndTypeList(orderId, workTypeCodeList);
            }
            // 2. 处理返回结果
            if (response == null || response.getStatus() == null ||
                    response.getStatus().getCode() != ResultCodeEnum.SUCCESS.getCode()) {
                log.warn("获取拣货工单信息失败, orderId:{}, response:{}", orderId, response);
                return Collections.emptyList();
            }
            return response.getDto();
        } catch (Exception e) {
            log.error("Call PickingThriftService#queryWODetailByOrderIdAndTypeList error. orderId:{}, "
                    + "workTypeCodeList:{}", orderId, workTypeCodeList, e);
            return Collections.emptyList();
        }
    }
}
