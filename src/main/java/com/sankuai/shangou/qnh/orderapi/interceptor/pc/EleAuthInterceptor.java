package com.sankuai.shangou.qnh.orderapi.interceptor.pc;

import com.alibaba.fastjson.JSON;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.AuthResourceRequest;
import com.sankuai.shangou.qnh.orderapi.constant.pc.CommonConstant;
import com.sankuai.shangou.qnh.orderapi.constant.pc.Constants;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.BaseResult;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.Result;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.ResultBuilder;
import com.sankuai.shangou.qnh.orderapi.remote.AuthRemoteService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;

/**
 * 元素权限拦截器
 * @Author: <EMAIL>
 * @Date: 2018/12/24 下午3:20
 */
@Component
public class EleAuthInterceptor extends HandlerInterceptorAdapter {

    @Resource
    private AuthRemoteService authRemoteService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        String env = System.getProperty(Constants.Env.SPRING_PROFILE_ACTIVE);
        if (StringUtils.equalsIgnoreCase(env, Constants.Env.DEV) || StringUtils.equalsIgnoreCase(env, Constants.Env.TEST)) {
            return true;
        }

        AuthResourceRequest req = new AuthResourceRequest();
        req.setUrl(request.getRequestURI());
        req.setMethod(request.getMethod());
        req.setAccountId(ContextHolder.currentUid());
        req.setAppId(ContextHolder.currentUserLoginAppId());

        if (authRemoteService.hasAuth(req)) {
            return true;
        } else {
            noEleAuth(request, response);
            return false;
        }
    }

    public void noEleAuth(HttpServletRequest request, HttpServletResponse response) throws Exception {
        Result result = ResultBuilder.buildResult(BaseResult.UNAUTHORIZED);
        response.setContentType(CommonConstant.CONTENT_TYPE_APPLICATION_JSON);
        response.setCharacterEncoding(CommonConstant.CHARSET_UTF8);
        String data = JSON.toJSONString(result);

        PrintWriter writer = response.getWriter();
        writer.write(data);
        writer.flush();
    }
}
