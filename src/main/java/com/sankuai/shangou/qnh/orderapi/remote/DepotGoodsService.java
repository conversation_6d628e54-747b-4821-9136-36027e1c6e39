package com.sankuai.shangou.qnh.orderapi.remote;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Lists;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.meituan.shangou.goodscenter.dto.DepotGoodsDetailDto;
import com.meituan.shangou.goodscenter.request.DepotGoodsBatchQueryRequest;
import com.meituan.shangou.goodscenter.response.DepotGoodsDetailListResponse;
import com.meituan.shangou.goodscenter.thrift.DepotGoodsThriftService;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Rhino
@Slf4j
public class DepotGoodsService {
    @Resource
    private DepotGoodsThriftService depotGoodsThriftService;

    private final int BATCH_SIZE = 50;

    @Degrade(rhinoKey = "DepotGoodsThriftService.queryByGoodsIds", fallBackMethod = "queryByGoodsIdsFallBack", timeoutInMilliseconds = 1500)
    public Map<String, DepotGoodsDetailDto> queryByGoodsIds(Long tenantId, Long repositoryId, List<String> goodsIds) {
        DepotGoodsBatchQueryRequest req = new DepotGoodsBatchQueryRequest();
        req.setTenantId(tenantId);
        req.setDepotId(repositoryId);
        req.setGoodsIdList(goodsIds);
        Map<String, DepotGoodsDetailDto> result = new HashMap<>();
        List<List<String>> goodsIdLists = Lists.partition(goodsIds, BATCH_SIZE);
        for (List<String> goodsIdSubList : goodsIdLists) {
            req.setGoodsIdList(goodsIdSubList);
            log.info("start invoke depotGoodsThriftService.batchQueryDepotGoodsListByGoodsId, req: {}", req);
            DepotGoodsDetailListResponse resp = depotGoodsThriftService.batchQueryDepotGoodsListByGoodsId(req);
            log.info("end invoke depotGoodsThriftService.batchQueryDepotGoodsListByGoodsId, resp: {}", resp);

            if (resp.getCode() != 0) {
                log.warn("调用货品服务失败");
                throw new ThirdPartyException("depotGoodsThriftService.batchQueryDepotGoodsListByGoodsId failed");
            }

            resp.getData().forEach(good -> result.putIfAbsent(good.getGoodsId(), good));
        }

        return result;
    }

    public Map<String, DepotGoodsDetailDto> queryByGoodsIdsFallBack(Long tenantId, Long repositoryId, List<String> goodsIds) {
        log.warn("DepotGoodsWrapper.queryByGoodsIds 发生降级");
        return Collections.emptyMap();
    }
}
