package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.price.client.dto.PageInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

@TypeDoc(
        description = "分页信息"
)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel("分页信息")
public class PageInfoVO {

    @FieldDoc(
            description = "当前页", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "当前页", required = true)
    private Integer page;

    @FieldDoc(
            description = "当前行数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "当前行数", required = true)
    private Integer size;

    @FieldDoc(
            description = "总页数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "总页数", required = true)
    private Integer totalPage;

    @FieldDoc(
            description = "总行数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "总行数", required = true)
    private Integer totalSize;

    @FieldDoc(
            description = "是否存在下一页", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "是否存在下一页", required = true)
    private Boolean hasMore;

    public static PageInfoVO defaultPageInfo() {
        return PageInfoVO.builder()
                .page(1)
                .size(0)
                .totalPage(0)
                .totalSize(0)
                .hasMore(false)
                .build();
    }

    public static PageInfoVO valueOf(PageInfoDTO pageInfoDTO){
        if (Objects.isNull(pageInfoDTO)){
            return null;
        }

        return PageInfoVO.builder()
                .page(pageInfoDTO.getPage())
                .size(pageInfoDTO.getSize())
                .totalPage(pageInfoDTO.getTotalPage())
                .totalSize(Math.toIntExact(pageInfoDTO.getTotalSize()))
                .hasMore(pageInfoDTO.getNextPage())
                .build();

    }
}
