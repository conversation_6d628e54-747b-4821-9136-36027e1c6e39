package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@TypeDoc(
        description = "查询履约看板请求"
)
@ApiModel("查询履约看板请求")
@Data
@ToString
public class OrderFulfillmentReportReq extends PageRequest {
    @FieldDoc(
            description = "门店IDList", requiredness = Requiredness.OPTIONAL
    )
    private List<Long> poiIdList;
    @FieldDoc(
            description = "仓IDList", requiredness = Requiredness.OPTIONAL
    )
    private List<Long> warehouseIdList;
    @FieldDoc(
            description = "排序Id", requiredness = Requiredness.REQUIRED
    )
    private String sortKey;
    @FieldDoc(
            description = "筛选类型 0：门店,1：仓", requiredness = Requiredness.REQUIRED
    )
    private int filterType;
    @FieldDoc(
            description = "租户Id", requiredness = Requiredness.OPTIONAL
    )
    private Long tenantId;
}
