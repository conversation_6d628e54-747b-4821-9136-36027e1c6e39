package com.sankuai.shangou.qnh.orderapi.constant.pc;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.PermissionGroupTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.TaskType;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.text.ParseException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * @Author: <EMAIL>
 * @Date: 2018/12/28 10:48
 * @Description:
 */
public class Constants {

    /**
     * B端APP_ID
     */
    public static final int SAAS_B_APP_ID = 3;

    public static final Joiner MR_SKU_IMPORT_JOINER = Joiner.on("、");

    public static final String SEMICOLON_CN = "；";

    public static final String YES = "1";

    public static final int TIME_START = 0;
    public static final int TIME_END = 15;

    /**
     * 精确到分钟级别的时间字符串长度
     */
    public static final int TIME_STRING_SECONDS_LENGTH = 12;
    /**
     * 精确到秒级别的时间字符串长度
     */
    public static final int TIME_STRING_MILLISECONDS_LENGTH = 14;

    /**
     * 灰度控制时，使用该值表示全量灰度
     */
    public static final String GRAY_CONTROL_ALL = "-1";

    /**
     * freemarker的前缀, 防止数字被格式化成逗号分隔.
     */
    public static final String COMMON_FMK_PREFIX = "<#setting number_format=\"#\">";

    /**
     * 在导出的excel表中upc列的index值（从0开始计数）
     */
    public static int UPC_INDEX = 7;

    /**
     * 订单模糊查询分割位数（手机号、序号、订单号）
     */
    public static int ORDER_FUZZY_SEARCH_SPLIT_LENGTH = 4;

    /**
     * PC 端查询订单模糊查询分割位数（手机号、序号、订单号）
     */
    public static int PC_ORDER_FUZZY_SEARCH_SPLIT_LENGTH = 9;

    public interface Common {
        int MILLISECOND_ONE_DAY = 86400000;
        int HOUR_ONE_DAY = 24;

        int MINUS_ONE = -1;

        int ONE = 1;

        int TWO = 2;

        int ZERO = 0;

        int MAX_SKU_NUM = 50;

        int MAX_SPU_NUM = 50;
    }

    public interface ErrorMsg {
        String UNKNOWN_ERROR = "未知异常";
    }


    public interface DateFormats {

        String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";
        String YYYYMMDDHHMM = "yyyyMMddHHmm";
        String SHOW_FORMAT = "yyyy.MM.dd HH:mm:ss";
        String SHOW_FORMAT_MIN = "yyyy.MM.dd HH:mm";
        String SHOW_FORMAT_HLINE = "yyyy-MM-dd HH:mm:ss";
        String SHOW_FORMAT_DAY = "yyyy-MM-dd";
        String DAY_FORMAT = "yyyy.MM.dd";
        String MIN_FORMAT = "HH:mm";
        String DAY_FORMAT2 = "yyyyMMdd";
    }

    public interface ImageType {
        String JPG = "jpg";
        String JPEG = "jpeg";
        String PNG = "png";
        String GIF = "gif";
    }

    public interface Limits {
        List<String> IMG_SUFFIX = Arrays.asList(ImageType.PNG, ImageType.JPG, ImageType.JPEG, ImageType.GIF);


        int FILE_EXPIRE_SECONDS = 10 * 365 * 24 * 60 * 60;

        long IMAGE_MAX_SIZE = 500 * 1024;
        long IMAGE_MAX_SIZE_1M = 1024 * 1024;

        /**
         * 商品服务单次查询最大条数
         */
        int SKU_MAX_QUERY_SIZE = 50;

        int QUERY_MR_SKU_PAGE_SIZE = 200;

    }


    /**
     * 通用函数
     */
    public interface Functions {

        /**
         * 日期转换方法
         */
        Function<String, Date> DATE_PARSE_UN_SS_FUNCTION = (v) -> {
            try {
                return DateUtils.parseDate(v, DateFormats.YYYYMMDDHHMM);
            } catch (ParseException e) {
                throw new BizException("日期格式错误(" + v + "),期望格式:yyyyMMddHHmmss");
            }
        };

        /**
         * 日期转换方法
         */
        Function<String, Date> END_DATE_PARSE_UN_SS_FUNCTION = (v) -> {
            try {
                if (StringUtils.endsWith(v, "2359") || StringUtils.endsWith(v, "23:59")) {
                    Date date = DateUtils.parseDate(v, DateFormats.YYYYMMDDHHMM);
                    return new Date(date.getTime() + TimeUnit.MINUTES.toMillis(1) - 1);
                } else {
                    return DateUtils.parseDate(v, DateFormats.YYYYMMDDHHMM);
                }
            } catch (ParseException e) {
                throw new BizException("日期格式错误(" + v + "),期望格式:yyyyMMddHHmmss");
            }
        };

        /**
         * 日期转换方法
         */
        Function<String, Date> DATE_PARSE_FUNCTION = (v) -> {
            try {
                return DateUtils.parseDate(v, DateFormats.YYYYMMDDHHMMSS);
            } catch (ParseException e) {
                throw new BizException("日期格式错误(" + v + "),期望格式:yyyyMMddHHmmss");
            }
        };

        /**
         * 日期转换方法
         */
        Function<String, Date> DATE_DAY_PARSE_FUNCTION = (v) -> {
            try {
                return DateUtils.parseDate(v, DateFormats.DAY_FORMAT2);
            } catch (ParseException e) {
                throw new BizException("日期格式错误(" + v + "),期望格式:yyyyMMdd");
            }
        };


        /**
         * 日期转换方法
         */
        Function<String, Date> END_DATE_PARSE_FUNCTION = (v) -> {

            try {
                if (StringUtils.endsWith(v, "235959") || StringUtils.endsWith(v, "23:59:59")) {
                    Date date = DateUtils.parseDate(v, DateFormats.YYYYMMDDHHMMSS);
                    return new Date(date.getTime() + TimeUnit.SECONDS.toMillis(1) - 1);
                } else {
                    return DateUtils.parseDate(v, DateFormats.YYYYMMDDHHMMSS);
                }
            } catch (ParseException e) {
                throw new BizException("日期格式错误(" + v + "),期望格式:yyyyMMddHHmmss");
            }
        };


        Function<String, Date> DATE_PARSE_FUNCTION_2 = (v) -> {
            try {
                return DateUtils.parseDate(v, DateFormats.SHOW_FORMAT_HLINE);
            } catch (ParseException e) {
                throw new BizException("日期格式错误(" + v + "),期望格式:yyyy-MM-dd HH:mm:ss");
            }
        };

        Function<String, Date> DATE_PARSE_FUNCTION_SIMPLEDAY = (v) -> {
            try {
                return DateUtils.parseDate(v, DateFormats.SHOW_FORMAT_DAY);
            } catch (ParseException e) {
                throw new BizException("日期格式错误(" + v + "),期望格式:yyyy-MM-dd HH:mm:ss");
            }
        };

    }


    /**
     * Excel表头
     */
    public interface ExcelExportTitles {
        String ORDER_TITLES = "订单号,门店,渠道,订单类型,商品数量,收货人姓名,收货人电话,收货人地址,订单总金额,订单实付金额,商家实收金额,配送费,餐盒费,平台服务费,配送员姓名,配送员手机号,是否在线支付,订单状态,会员卡号,订单创建时间";
        String SKU_TITLES = "SKU编码,UPC码,商品名称,线上商品名称,线上规格,门店名称,规格,线上重量,净重(g),毛重(g),品牌名称,所属商家类目,基础售价,线上售卖单位,基本单位,门店毛利率,门店销售价（元）,门店会员价（元）,是否可用,是否可售,商品类型,称重类型";
        String STORE_SKU_TITLES = "SKU编码,UPC,商品名称,规格,重量(g),单位,门店编码,门店名称,门店线下售价（元）,美团外卖线上售价,美团外卖商品标识码,美团外卖上下架状态,美团外卖店内一级分类,美团外卖店内二级分类,饿了么线上售价,饿了么商品标识码,饿了么上下架状态,饿了么店内一级分类,饿了么店内二级分类,京东到家线上售价,京东到家商品标识码,京东到家上下架状态,京东到家店内一级分类,京东到家店内二级分类,商品类目一级,商品类目二级,商品类目三级,商品类目四级,商品类目五级,品牌,商品类型,称重类型,是否可用,是否可售";
        String ONLINE_SKU_TITLES = "SKU编码,UPC码,商品名称,规格,重量,单位,前台分类,所属商家类目,渠道,门店名称,价格,可用库存,称重类型,商品状态";
        String PRICE_TITLES = "SKU编码,商品名称,规格,门店,渠道,价格（元）,进货价（元）,价格来源";
        String TOTAL_STOCK = "所属租户,所属仓库,SKU编码,商品名称,商品品类,总库存,可用库存,锁定库存,冻结库存,安全库存,库存单位";
        String MEDICINE_TOTAL_STOCK = "门店,SKU编码,upc,商品名称,规格,生产企业,总库存,可用库存,锁定库存,冻结库存,下架库存";
        String CONTAINER_STOCK = "库位编号,SKU编码,商品名称,商品品类,所属租户,总库存,可用库存,锁定库存,库存单位";
        String BATCH_STOCK = "批号,SKU编码,商品名称,商品品类,所属租户,入库日期,库存数量,库存单位";
        String ONE_POI_WITH_ONE_BOOTH_SETTLE_TITLES = "完成时间,门店,摊位,渠道,结算金额（元）,结算方式,实收金额（元）,订单原价（元）,商家承担活动补贴（元）,包装费（元）,收款户名,收款银行或渠道,收款账户";
        String ONE_POI_WITH_SEVERAL_BOOTH_SETTLE_TITLES = "完成时间,门店,摊位,渠道,结算金额（元）,结算方式,收款户名,收款银行或渠道,收款账户";
        String NON_VENDOR_SETTLE_DETAIL_TITLES = "订单完成时间,订单号,门店,渠道,结算金额（元）,订单结算方式,订单实收金额（元）,订单原价（元）,商家承担活动补贴（元）,订单包装费（元）,订单扣点比例,商品sku,商品upc,商品名称,商品规格,商品单位,销售单价（元）,购买数量,实际拣货数量,商品摊位名称,商品线下售价（元）,商品摊位结算金额（元）,是否申请退款,申请退款数量";
        String VENDOR_SETTLE_DETAIL_TITLES = "订单完成日期,门店,渠道,商品sku,商品upc,商品名称,商品规格,购买数量,实际拣货数量,商品摊位名称,商品线下售价,商品摊位结算金额,是否申请退款,申请退款数量,对应订单号";
        String SKU_GENERAL_BASE_TITLES_V2 = "SKU编码,商品名称,规格,基本售价（元）,基本单位,重量(g),京东到家末级类目名称,京东到家品牌名称,商品一级类目名称,商品二级类目名称,商品三级类目名称,品牌名称,是否可用,是否可销售,称重类型,商品类型,毛重(g),线上重量(g),线上售卖单位,线上售卖单位值" +
                ",线上商品名称,线上规格,UPC,UPC2,UPC3,UPC4,UPC5,UPC6,最小购买数量,包装盒数量,包装盒价格";
        String SKU_GENERAL_BASE_TITLES = "SKU编码,商品名称,规格,商品一级类目名称,商品二级类目名称,商品三级类目名称,基本售价（元）,是否可用,是否可销售,称重类型,商品类型,基本单位,线上售卖单位,线上售卖单位值,线上商品名称,线上规格,品牌编码,品牌名称,净重(g),毛重(g),线上重量(g),UPC,UPC2,UPC3,UPC4,UPC5,UPC6";
        String SKU_GENERAL_BASE_EXCEL_TITLES = "SKU编码,UPC,商品名称,商品类目编码,商品类目名称,品牌编码,品牌名称,线下规格,净重(g),毛重(g),产地,是否可用,是否可销售,称重类型,商品类型,线上规格,线上重量(g),线上售卖单位,线上售卖单位值,基本售价（元）," +
                "最高价格,最低价格,最大折扣,最小折扣,基本单位,库存单位,库存单位比例,采购单位,采购单位比例,配送单位,配送单位比例";
        String SETTLEMENT_ADJUST_LOG_TITLES = "账单日期,所属门店,门店id,所属摊位,摊位id,sku,应结金额(元),调整说明";
        String SETTLEMENT_INFO_LIST = "完成时间,门店,摊位,结算方式,结算状态,账单金额(元),调整金额(元),应结金额(元),打款金额(元),有效笔数,美团待结算金额,美团有效笔数,饿了么待结算金额,饿了么有效笔数,京东待结算金额,京东有效笔数,结算规则,收款方式,收款户名,收款账户,操作人,备注";
        String SETTLEMENT_AGGREGATION_LIST = "账单日期,门店,摊位,结算方式,结算状态,账单金额(元),调整金额(元),应结金额(元),打款金额(元),有效笔数,美团待结算金额,美团有效笔数,饿了么待结算金额,饿了么有效笔数,京东待结算金额,京东有效笔数,结算规则,收款方式,收款户名,收款账户";
        String SETTLEMENT_INFO_LIST_SCOPE = "完成时间,门店,摊位,结算状态,待结算金额(元),有效笔数,美团待结算金额,美团有效笔数,饿了么待结算金额,饿了么有效笔数,京东待结算金额,京东有效笔数,结算方式,收款方式,收款户名,收款账户";
        String SKU_GENERAL_BASE_MAICAI_TITLES_V2 = "SKU编码,商品名称,规格,基本售价（元）,基本单位,重量(g),京东到家末级类目名称,京东到家品牌名称,商品一级类目名称,商品二级类目名称,商品三级类目名称" +
                ",品牌名称,最小购买数量,包装盒数量,包装盒价格,美团渠道末级类目名称";
        String SKU_GENERAL_BASE_MAICAI_TITLES = "SKU编码,商品名称,规格,商品一级类目名称,商品二级类目名称,商品三级类目名称,基本售价（元）,基本单位,品牌编码,品牌名称,净重(g)";
        List<String> COMMENT_EXPORT_TITLES = Arrays.asList("渠道", "ERP门店编码", "门店编码", "门店名称", "订单号", "订单是否系统匹配", "订单评分", "质量评分", "包装评分",
                "配送评分", "用户评价", "评价时间", "商家回复", "订单商品", "踩商品", "赞商品", "用户追评", "追评时间", "评价图片", "评价状态");


        String STORE_AREA_TIELES = "门店ID/仓库编码,商品SKU,商品名称,区域名称,区域编码,库位";

        String STOCK_TASK_TITLES = "盘点任务单号,名称,备注,盘点任务状态,计划SKU数,提交SKU数,盘点差异率,盘点员工数,任务创建时间,任务结束时间";

        String OTHER_STOCK_IN_TASK_TITLE = "单号,门店/仓,类型,状态,创建人,创建时间,完成时间,入库时间,备注";
        String OTHER_STOCK_OUT_TASK_TITLE = "单号,门店/仓,类型,状态,创建人,创建时间,完成时间,出库时间,备注";

        String OTHER_STOCK_IN_DETAIL_TITLE = "商品名称,SKUID,UPC,规格,可用库存,入库数量,成本单价（元）,总成本（元）";
        String OTHER_STOCK_OUT_DETAIL_TITLE = "商品名称,SKUID,UPC,规格,可用库存,出库数量,成本单价（元）,总成本（元）";


        String STOCK_TASK_SKU_TITLES = "盘点任务单号,SKU,商品名称,商品品类,盘点员工,盘点时间,实盘数量,系统库存,差异量,是否新入仓";
        String STOCK_DISTRIBUTION_ORDER_TITLES = "门店,配货单号,单据状态,SKU数,已收SKU数,采购日期,创建人,创建时间,最后操作人,最后操作时间";
        String STOCK_DISTRIBUTION_ORDER_SKU_TITLES = "SKU,商品名称,单位类型,单位值,规格,采购价(元),采购数量,可收数量,已收数量,供应商,商品备注";
        String STOCK_DISTRIBUTION_LOG_SKU_TITLES = "商品名称,SKU编码,单位,规格,要货量,实收量,偏差量,收货人,收货时间,商品备注";
        String STOCK_RECEIPT_TITLES = "门店,收货单号,配货单号,SKU数,收货人,收货时间";
        String STOCK_RECEIPT_SKU_TITLES = "SKU,商品名称,单位类型,单位值,规格,收货数量,供应商";
        String BOOTH_ITEM_TITLES = "商品名称,规格,SKU编码,供货价（元）,摊位名称,摊位编码,门店名称,门店编码";
        String BONUS_TITLES = "账单日期,门店,外卖门店ID,应结算金额(元),有效笔数,计算公式,外卖服务费(元),状态,审批意见,结算完成日期,结算发起者,结算审核者,调整时间,调整者,调整金额,调整原因";
        String BONUS_TITLES_WITHOUT_SERVICE_FEE = "账单日期,门店,外卖门店ID,应结算金额(元),有效笔数,计算公式,状态,审批意见,结算完成日期,结算发起者,结算审核者,调整时间,调整者,调整金额,调整原因";
        String PULL_SKU_DETAIL_TITLES = "操作类型,SKU编码,商品名称,规格,商品图片,美团外卖,饿了么,京东到家,进货价（元）,UPC,重量(g)";
        String PULL_CATEGORY_DETAIL_TITLES = "操作类型,渠道,店内分类code,店内分类名称,店内分类层级,父分类名称,排序值";
        String PULL_SKU_FAIL_DETAIL_TITLES = "渠道,SKU编码,商品名称,规格,进货价（元）,UPC,失败原因";

        String MARKET_RESEARCH_SKU_LIST_MERGE_MODE = "商品名称,SKU,规格,门店,商品类目,当时零售价（￥）,参考价均值（￥）,当时商品重量（g）,涨跌幅,涨跌平,参考价区间（￥）,商品参考价（￥）,市调原价（￥）,市调重量（g）";
        String MARKET_RESEARCH_SKU_LIST_ALONE_MODE = "商品名称,SKU,规格,门店,商品类目,当时零售价（￥）,商品参考价（￥）,当时商品重量（g）,涨跌幅,涨跌平,市调原价（￥）,市调重量（g）,市调人,市调时间";
        String COUNTRY_SKU_TITLES = "商品编码,商品名称,规格,基本售价（元）,基本单位,重量(g),商品一级类目名称,商品二级类目名称,商品三级类目名称,品牌名称,最小购买数量,包装盒数量,包装盒价格";
        String REGION_SKU_TITLES = "商品编码,商品名称,规格,基本售价（元）,基本单位,是否标品,重量(g),商品一级类目名称,商品二级类目名称,商品三级类目名称,品牌名称";
        String SKU_GENERAL_BASE_4_CDQ_TITLES = "商品编码,商品名称,规格,基本售价（元）,基本单位,是否标品,重量(g),商品一级类目名称,商品二级类目名称,商品三级类目名称,品牌名称,最小购买数量,包装盒数量,包装盒价格,拣货标准";

        List<String> POI_MODE_SECURITY_LIST_TITLES = Arrays.asList("门店", "城市", "外卖门店ID", "协议状态", "保证金状态", "应缴保证金(元)",
                "保证金余额(元)", "最晚缴费日期", "实际缴费日期", "协议签署日期", "门店停用时间");
        List<String> COMMERCIAL_AGENT_MODE_SECURITY_LIST_TITLES = Arrays.asList("门店",
                "合作商", "城市", "外卖门店ID", "协议状态", "保证金状态",
                "应缴保证金(元)", "保证金余额(元)", "最晚缴费日期", "实际缴费日期", "协议签署日期", "商户清退时间");
        String BOOTH_SETTLEMENT_BATCH_PAY_RESULT = "门店,摊位,结算金额(元),失败原因";
        String SKU_STOCK_BATCH_UPDATE_RESULT = "*租户id,*门店id,*sku编码,商品名称,*线上库存,*营业后置为无限库存";

        String SKU_STOCK_WARNING_THRESHOLD_BATCH_UPDATE_RESULT = "*sku编码,*安全库存,*库存容量";
        String SKU_STOCK_UN_LIMITED_BATCH_UPDATE_RESULT = "*门店ID,*SKU,*线上无限库存";

        String BATCH_UPDATE_STORE_CATEGORY_ORDER_GOODS_DAYS_RESULT = "一级类目,二级类目,订货天数";

        /*********** 提报价抬头  ***********/
        String QUOTE_REVIEW_TITLES_HEAD = "商品名称,SKU,规格,重量(g),商品类目,门店,当前报价,报价前,市调价,城市均价,";
        String MEITUAN_RETAIL_TITLES = "美团零售价,美团定价策略,";
        String ELME_RETAIL_TITLES = "饿了么零售价,饿了么定价策略,";
        String JD_RETAIL_TITLES = "京东零售价,京东定价策略,";
        String QUOTE_REVIEW_TITLES_TAIL = "状态,提报人,提报时间,审核人,审核时间,驳回原因,通过类型";
        /*********** 提报价抬头  ***********/

        String SPU_GENERAL_BASE_TITLES = "SPU编码,商品名称,是否标品,商品一级类目名称,商品二级类目名称,商品三级类目名称,品牌名称,产地";
        String STORE_SPU_UPDATE_STATUS_TITLES = "SPU编码,商品状态,目标门店编码,目标门店名称,渠道编码,渠道名称,零售价,店内一级分类,店内二级分类";
        String STORE_SPU_UPDATE_STATUS_BY_FORM_TITLES = "SPU编码,商品状态,目标门店编码,目标门店名称,渠道编码,渠道名称";
        String STORE_SPU_UPDATE_FRONT_CATEGORY_TITLES = "SPU编码,目标门店编码,目标门店名称,渠道编码,渠道名称,店内分类";
        String MULTI_CHANNEL_STORE_SPU_UPDATE_STATUS_TITLES = "SPU编码,商品状态,目标门店编码,目标门店名称,渠道编码,渠道名称,零售价";


        String BATCH_IMPORT_POI_SPU = "总部SPU编码,目标门店编码,SKU编码,进货价（元）";
        String BATCH_UPDATE_POI_SPU = "目标门店编码,SPU编码,SKU编码,最小购买数量,包装盒数量,包装盒价格,商品属性,商品卖点,是否力荐,商品描述";
        String POI_SPU_QUICK_ONLINE = "总部SPU编码,目标门店编码,上下架状态,店内一级分类名称,店内二级分类名称,SKU编码,进货价（元）,线上库存,商品卖点,是否力荐,商品描述";
        String MULTI_CHANNEL_POI_SPU_QUICK_ONLINE = "总部SPU编码,目标门店编码,上下架状态,SKU编码,线上库存,零售价,是否力荐";
        String MULTI_CHANNEL_BATCH_UPDATE_POI_SPU = "目标门店编码,SPU编码,SKU编码,最小购买数量,包装盒数量,包装盒价格,商品属性,是否力荐";
        String STORE_UPC_IMPORT_AND_ONLINE = "UPC,目标门店编码,定价方式,门店进货价（元),门店零售价（元）,门店线上库存,上下架状态,店内一级分类名称,店内二级分类名称,商品一级类目名称,商品二级类目名称,商品三级类目名称,品牌名称,产地,拣货标准";
        String CONVENIENCE_STORE_UPC_IMPORT_AND_ONLINE = "UPC,目标门店编码,门店零售价（元）,门店线上库存,上下架状态,店内一级分类名称,店内二级分类名称,商品一级类目名称,商品二级类目名称,商品三级类目名称,拣货标准";

        /*********** 门店管理信息导出抬头  ***********/
        /**
         * 基础文件头
         */
        String POI_MNG_INFO_TITLES_BASE = "门店id,门店名称,地区,地址,门店面积,门店老板,对应外卖门店id,ERP门店ID";
        // 非新供给&超市便利增加的列
        String POI_MNG_INFO_TITLES_1 = "摊位模式,结算模式,是否资金打款结算,收款人姓名";
        /**
         * 一仓多店格式增加的列
         */
        String POI_MNG_INFO_TITLES_2 = "发货方式,发货仓库";
        /*********** 门店管理信息导出抬头  ***********/

        /*********** 商户考核结果信息导出抬头***********/
        String DEFAULT_APPRAISAL_RESULT_TITLES = "考核周期,中台门店ID,外卖门店ID,门店,城市,结算状态,考核阶段,上线日期,上线天数,换商日期,换商天数";
        String MEET_CRITERION_APPRAISAL_TITLES = "考核周期,中台门店ID,外卖门店ID,门店,城市,考核结果,扣款金额(元),结算状态,考核阶段,上线日期,上线天数,换商日期,换商天数";
        String GOAL_BASED_INSPIRATION_TITLES = "考核周期,中台门店ID,外卖门店ID,门店,城市,奖励金额(元),结算状态,考核阶段,上线日期,上线天数,换商日期,换商天数";
        String PUNISHMENT_APPRAISE_TITLES = "考核周期,中台门店ID,外卖门店ID,门店,城市,扣款金额(元),结算状态,考核阶段,上线日期,上线天数,换商日期,换商天数";
        String SERVICE_STAR_APPRAISE_TITLES = "考核周期,中台门店ID,外卖门店ID,门店,城市,奖励金额(元),结算状态,考核阶段,上线日期,上线天数,换商日期,换商天数";

        String APPRAISE_RESULT_DETAIL_TITLES = "日期,中台门店ID,外卖门店ID,门店";

        // 医药 门店商品批量上下架失败Excel表头
        String MEDICINE_STORE_SKU_BATCH_UPDATE_STATUS_TITLES = "SKU编码,商品状态,目标门店编码,渠道编码,店内一级分类,店内二级分类";

        String LATE_ORDER_TITLES = "订单号,门店名称,渠道,实付金额,是否超时发券,订单类型,创建时间,预计送达时间";

    }

    /**
     * 库存同步策略内容模板
     */
    public interface StrategyContentTemp {
        String GENERAL_STRATEGY_TEMP = "每个渠道预留%s个库存";
        String HOT_STRATEGY_TEMP = "一级安全库存值：%s，二级安全库存值：%s";
        String FRESH_STRATEGY_TEMP = "损耗率：%s，预留库存：%s";
        String ALLOT_BY_PERCENT_TEMP = "按比例%s";
        String ALLOT_BY_REMAIN_NUM_TEMP = "按预留%s";
    }

    public interface OperateType {
        //保存
        int SAVE_TYPE = 1;
        //编辑
        int EDIT_TYPE = 2;
        //删除
        int DELETE_TYPE = 3;
    }

    /**
     * 中台商品促销类型
     */
    public interface ChannelItemPromotionType {
        /**
         * 折扣
         **/
        int DISCOUNT_TYPE = 1;
        /**
         * 特价
         **/
        int ACTIVITY_PRICE_TYPE = 2;

    }


    /**
     * 请统一使用 {@link AuthType}
     * 新增类型这里不再维护
     */
    @Deprecated
    public interface PermissionType {
        int AUTH_TYPE_OF_REPOSITORY = 3;
        int AUTH_TYPE_OF_POI = 1;
        int AREA_TYPE = 5;
        int AREA_GROUP_TYPE = 6;
        int DEP_TYPE = 7;
    }

    public interface Env {
        String SPRING_PROFILE_ACTIVE = "spring.profiles.active";

        String DEV = "dev";

        String TEST = "test";
    }


    public interface DepTyps {
        int BOOTH_TYPE = 5;
        int COMMERCIAL_AGENT_TYPE = 6;
    }


    /**
     * 摊位管理展示银行列表,暂时写死15大行
     */
    public interface Banks {

        String BANK_INFO = "ICBC:中国工商银行,CCB:中国建设银行,ABC:中国农业银行,BOC:中国银行" +
                ",COMM:交通银行,CMB:招商银行,SPDB:浦发银行,CIB:兴业银行,CMBC:中国民生银行,PSBC:中国邮政储蓄银行," +
                "CITIC:中信银行,CEB:中国光大银行,HXB:华夏银行,SZPAB:平安银行,CGB:广发银行";

    }


    public interface BatchPriceRaiseErrorTemp {
        String CHANNEL_OR_STORE_CLOSED = "%s已被关闭";
        String SKU_NOT_EXIST_IN_STORE = "skuId:%s在%s不存在";
        String STORE_CLOSED_IN_CHANNEL = "%s在%s已被关闭";
    }


    /**
     * 权限类型
     * 和 {@link PermissionGroupTypeEnum} 保持一致
     */
    public interface AuthType {

        /**
         * 门店 + 中心仓
         */
        int POI_TYPE = 1;
        int CATEGORY_TYPE = 2;
        int REPO_TYPE = 3;
        /**
         * 摊位
         */
        int BOOTH_TYPE = 4;
        /**
         * 库区
         */
        int STORE_AREA = 5;
        /**
         * 库区分组
         */
        int STORE_AREA_GROUP = 6;
        int POI_GROUP = 7;
        /**
         * 区域
         */
        int REGION_TYPE = 8;
        int DEPARTMENT = 9;
        /**
         * 共享前置仓
         */
        int SHAREABLE_WAREHOUSE = 10;
    }


    public interface ProductBizId {

        /**
         * 中台
         */
        int MID_PLATFORM_BIZID = 1;
    }

    public interface Size {
        int _10K = 10240;
    }


    public interface OcmsExtKey {
        String MIN_AMOUNT = "minOrderCount";
        String BOX_AMOUNT = "boxNum";
        String BOX_PRICE = "boxPrice";
        String JD_SORT_ID_PATH = "JD_SORT_ID_PATH";
        String JD_BRAND_NAME = "JD_BRAND_NAME";
        String JD_SORT_NAME = "JD_SORT_NAME";

    }


    public interface Text {
        String UN_LIMITED_TEXT = "无限";
        String STOCK_IGNORE_CHAR = "-";
    }

    public interface Comment {
        /**
         * 未回复
         **/
        Integer REPLY_STATUS_NOT_REPLY = 0;
        /**
         * 已回复
         **/
        Integer REPLY_STATUS_REPLIED = 1;
        /**
         * 评价列表一页最大记录数
         **/
        int COMMENT_LIST_PAGE_SIZE_MAX = 150;
        /**
         * 评价列表查询, 评价时间间隔天数最大值
         **/
        int COMMENT_LIST_QUERY_COMMENT_TIME_INTERVAL_DAY_MAX = 31;
        /**
         * 评价导出每页记录数
         **/
        int COMMENT_EXPORT_QUERY_PAGE_SIZE = 1000;
        /**
         * 评价导出最大记录数
         **/
        int COMMENT_EXPORT_MAX_ROWS_LIMIT = 50000;
        /**
         * 通过其他平台回复, 展示回复内容
         **/
        String COMMENT_REPLY_BY_OTHER_PLATFORM_SHOW_CONTENT = "商家已通过其他后台回复";
    }

    public interface Poi {
        long ALL_POI_FLAG = 0;
        String ALL_POI_FLAG_DES = "全部门店";
        String CATEGORY_SEPARATOR = "_";
        // 多门店的定时上架时间描述
        String POIS_TIMED_ON_SALE_TIME_DESC = "按照门店设置的定时上架时间自动上架，可通过牵牛花app更改";
        // 单门店的定时上架时间描述_前缀
        String POI_TIMED_ON_SALE_TIME_DESC_PRE = "当日 ";
        // 单门店的定时上架时间描述_后缀
        String POI_TIMED_ON_SALE_TIME_DESC_SUF = "（可通过牵牛花app更改默认定时上架时间）";
    }

    public interface ComputationRule {
        String template = "(%s) x (1-扣点比例)";
    }

    public interface Stock {
        /**
         * 配货收货单列表查询, 时间间隔天数最大值
         **/
        int DISTRIBUTION_RECEIPT_QUERY_TIME_INTERVAL_DAY_MAX = 184;
        /**
         * 配货收货单列表查询, 默认查询近**天
         **/
        int DISTRIBUTION_RECEIPT_QUERY_TIME_RECENT_DAYS = 30;
        /**
         * 配货收货单列表一页最大记录数
         **/
        int DISTRIBUTION_RECEIPT_PAGE_SIZE_MAX = 150;
    }

    // 门店库存相关常量
    public interface ChannelStock {
        String UNLIMITED_STOCK = "UNLIMITED";
        // 向UpdateStoreStockRequest请求传入的无效参数，即参数不产生作用
        int UPDATE_STORE_STOCK_REQ_INVALID_ARG = 0;

    }

    public interface RegionSkuType {
        /**
         * 全国商品
         */
        int CONTRY_SKU = 1;
        /**
         * 区域商品
         */
        int REGION_SKU = 2;
    }


    public interface Channel {
        /**
         * 线下渠道
         */
        int OFFLINE_CHANNEL_ID = -1;
    }

    public interface Weight {
        /**
         * 重量：500g
         */
        int WEIGHT_500G = 500;
    }

    public interface Quote {

        List<Long> SYS_OPERATOR_IDS = Lists.newArrayList(77777777L, 88888888L, 99999999L);
    }

    public interface Price {
        /**
         * 最多查询90天的价格变更记录
         */
        int PRICE_CHANGE_RECORD_INTERVAL_DAYS_MAX = 90;
    }


    public interface ConfigKey {
        String SPEC_STANDARD = "SPEC_STANDARD";
        String SPU_GRAY = "SPU_GRAY";
    }

    public interface ChannelPoiOnlineStatus {
        int ONLINE = 1;
        int OFFLINE = 0;
    }

    public static List<Integer> NEED_CHECK_TEMPLATE = Arrays.asList(TaskType.TENANT_BONUS_MEET_CRITERION_APPRAISAL.getValue(), TaskType.TENANT_BONUS_UPDATE_GOAL.getValue()
            , TaskType.TENANT_BONUS_UPDATE_PUNISHMENT.getValue(), TaskType.TENANT_BONUS_UPDATE_SERVICE_STAR.getValue());


    /**
     * 一天的毫秒数
     */
    public static final int ONE_DAY_MILLIS = 24 * 3600 * 1000;

    public static final String OPERATE_SOURCE_OPEN_PLATFORM = "开放平台";


    public static final List<Integer> MUNICIPALITY_IDS = Arrays.asList(110000, 120000, 310000, 500000);

    /**
     * 商品查询每页最大限制
     */
    public static final int SPU_QUERY_LIMIT_PER_PAGE = 50;


    public static final int ONE_MINUTE_MILLIS = 60 * 1000;

    //租户业态模式名称
    public static final String TENANT_BIZ_MODE_NAME = "tenant_biz_mode";

    public static class Purchase {
        /**
         * 中心仓批量导入补货设置，下载模版名称
         */
        public static final String REPLENISH_SETTING_IMPORT_TEMPLATE_NAME_REGIONAL_WAREHOUSE = "批量导入补货设置模板-中心仓";
        /**
         * 中心仓批量导入补货设置，模版文件名称
         */
        public static final String REPLENISH_SETTING_IMPORT_TEMPLATE_FILE_REGIONAL_WAREHOUSE = "批量导入补货设置模板-中心仓_v3.xlsx";
        /**
         * 中心仓批量修改采购状态，下载模版名称
         */
        public static final String BATCH_IMPORT_PURCHASE_SKU_STATUS_TEMPLATE_NAME_REGIONAL_WAREHOUSE = "批量修改采购状态模板-中心仓";
        /**
         * 中心仓批量修改采购状态，模版文件名称
         */
        public static final String BATCH_IMPORT_PURCHASE_SKU_STATUS_TEMPLATE_FILE_REGIONAL_WAREHOUSE = "批量修改采购状态模板-中心仓.xlsx";

        public static final Long DEFAULT_REFRESH_PURCHASE_RECEIPT_TIME = 2000L;
    }

    public static final String SEAL_TAG_CODE = "SEAL_GOODS";

    public static final String NEED_SEAL_TAG_VALUE_CODE = "SEAL_TRUE";

    public static final String NO_SEAL_TAG_VALUE_CODE = "SEAL_FALSE";
}
