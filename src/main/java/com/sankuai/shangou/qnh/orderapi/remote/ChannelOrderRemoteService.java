package com.sankuai.shangou.qnh.orderapi.remote;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.meituan.shangou.saas.o2o.dto.model.OCMSOrderKey;
import com.meituan.shangou.saas.order.management.client.dto.request.SearchCombinationProductReq;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OCMSListRefundOrderRequest;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OcmsOrderDetailReq;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OcmsUnDoneOrderListReq;
import com.meituan.shangou.saas.order.management.client.dto.response.online.*;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OrderDetailVo;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OrderInfoVo;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OrderListPageVo;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.PageInfoVo;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.DeliveryExtraResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.OrderSearchResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.model.DeliveryInfoExtend;
import com.meituan.shangou.saas.order.management.client.enums.StatusCodeEnum;
import com.meituan.shangou.saas.order.management.client.export.dto.request.FuseOrderListExportTaskCreateRequest;
import com.meituan.shangou.saas.order.management.client.export.dto.response.ExportOrderCountResponse;
import com.meituan.shangou.saas.order.management.client.export.service.FuseOrderListExportThriftService;
import com.meituan.shangou.saas.order.management.client.service.OrderSearchService;
import com.meituan.shangou.saas.order.management.client.service.online.OCMSQueryThriftService;
import com.meituan.shangou.saas.order.management.client.service.online.OcmsOrderSearchService;
import com.meituan.shangou.saas.order.management.client.service.online.OcmsOrderSearchThriftServiceV2;
import com.meituan.shangou.saas.order.management.client.service.online.OrderItemFuseSearchThriftService;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.AgreeRefundReq;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.AgreeRefundResp;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.RejectRefundReq;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.RejectRefundResp;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.ChannelOrderTenantThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryEntityEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.dto.DeliveryChannelDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.QueryOrderDeliveryInfoKey;
import com.sankuai.meituan.shangou.saas.common.data.PageResult;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.qnh.ofc.ofw.client.thrift.dto.order.FulfillmentOrderDetailDTO;
import com.sankuai.qnh.ofc.ofw.client.thrift.request.order.BatchFulfillmentOrderIdKeyReq;
import com.sankuai.qnh.ofc.ofw.client.thrift.request.order.ChannelOrderIdKeyReq;
import com.sankuai.qnh.ofc.ofw.client.thrift.response.order.FulfillmentOrderDetailResponse;
import com.sankuai.qnh.ofc.ofw.client.thrift.service.order.FulfillmentOrderSearchThriftService;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.request.DhOverTimeOrderAllQueryRequest;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.request.DhOverTimeOrderPageQueryRequest;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.response.CommonDataResponse;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.response.DHLateOrderQueryResponse;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.response.dto.OverTimeOrderVO;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.thrift.DrunkHorseOrderThriftService;
import com.sankuai.shangou.qnh.orderapi.converter.pc.ChannelOrderConverter;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.*;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.LateOrderDownloadRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.QueryLateOrderRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.RefundOrderQueryRequest;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.PageResultV2;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.ConfirmOrderVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.DhDeliveryExtraVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.DhLateOrderVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.OrderExportMaxCountVO;
import com.sankuai.shangou.qnh.orderapi.enums.pc.OrderCouldOperateItemEnum;
import com.sankuai.shangou.qnh.orderapi.service.pc.AppendDeliveryInfoService;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ResponseHandler;
import com.sankuai.shangou.qnh.orderapi.utils.pc.RpcInvoker;
import com.sankuai.shangou.qnh.orderapi.utils.pc.TimeUtil;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/2 14:37
 * @Description:
 */
@Service
@Slf4j
public class ChannelOrderRemoteService {

    @Autowired
    private OcmsOrderSearchService ocmsOrderSearchService;

    @Autowired
    private OcmsOrderSearchThriftServiceV2 orderSearchThriftServiceV2;

    @Autowired
    private OrderItemFuseSearchThriftService orderItemFuseSearchThriftService;

    @Resource
    private OCMSQueryThriftService ocmsQueryThriftService;

    @Autowired
    private FuseOrderListExportThriftService fuseOrderListExportThriftService;

    @Autowired
    private AppendDeliveryInfoService appendDeliveryInfoService;

    @Resource
    private FulfillmentOrderSearchThriftService fulfillmentOrderSearchThriftService;
    @Resource
    private OrderSearchService orderSearchService;
    @Autowired
    private ChannelOrderConverter channelOrderConverter;
    @Resource
    private OrderBizRemoteService orderBizRemoteService;
    @Autowired
    private DrunkHorseOrderThriftService drunkHorseOrderThriftService;
    @Autowired
    private ChannelOrderTenantThriftService.Iface channelOrderService;

    private final static List<Integer> DEFAULT_OCMS_CHECK_ITEMS =
            ImmutableList.copyOf(Arrays.stream(OrderCouldOperateItemEnum.values())
                    .map(OrderCouldOperateItemEnum::getValue).collect(Collectors.toList()));

    /**
     * 查询融合订单列表
     *
     * @param orderFuseQueryBO
     * @return
     */
    @MethodLog(logRequest = true, logResponse = true)
    public OcmsOrderSearchResponseV2 queryFuseOrders(OrderFuseQueryBO orderFuseQueryBO) {
        log.info("ChannelOrderClient.queryFuseOrders call orderFuseList request:{}", orderFuseQueryBO);
        OcmsOrderSearchResponseV2 resp = RpcInvoker.invoke(() -> orderSearchThriftServiceV2.orderFuseList(orderFuseQueryBO.toOcmsOrderAllFuseListReq()));
        log.info("ChannelOrderClient.queryFuseOrders call orderFuseList response:{}", resp);
        return resp;
    }

    /**
     * 查询融合订单列表统计数据
     *
     * @param orderFuseQueryStatisticsDataBO
     * @return
     */
    @MethodLog(logRequest = true, logResponse = true)
    public OrderStatisticsDataSearchResponse queryOrderFuseStatisticsData(OrderFuseQueryStatisticsDataBO orderFuseQueryStatisticsDataBO) {
        log.info("ChannelOrderClient.queryOrderFuseStatisticsData call OcmsOrderSearchThriftServiceV2.orderFuseStatisticsData request:{}", orderFuseQueryStatisticsDataBO);
        OrderStatisticsDataSearchResponse resp = RpcInvoker.invoke(() -> orderSearchThriftServiceV2.orderFuseStatisticsData(orderFuseQueryStatisticsDataBO.toOcmsOrderAllFuseListReq()));
        log.info("ChannelOrderClient.queryOrderFuseStatisticsData call OcmsOrderSearchThriftServiceV2.orderFuseStatisticsData response:{}", resp);
        return resp;
    }

    /**
     * 查询融合订单明细列表统计数据
     * 
     * @param req
     * @return
     */
    @MethodLog(logRequest = true, logResponse = true)
    public OrderItemStatisticsDataSearchResponse
            queryOrderItemFuseStatisticsData(OrderItemFuseQueryStatisticsDataBO req) {
        OrderItemStatisticsDataSearchResponse resp = RpcInvoker.invoke(() -> orderItemFuseSearchThriftService
                .orderItemFuseStatisticsData(req.toOrderItemFuseStatisticsDataReq()));
        return resp;
    }


    public OCMSAfterSaleListResponse queryRefundOrderList(RefundOrderQueryRequest refundOrderQueryRequest) {
        log.info("ChannelOrderClient.queryRefundOrderList call refundOrderQueryRequest:{}", refundOrderQueryRequest);
        OCMSListRefundOrderRequest ocmsListRefundOrderRequest = refundOrderQueryRequest.toOcmsListRefundOrderRequest();
        log.info("ChannelOrderClient.queryRefundOrderList call ocmsListRefundOrderRequest:{}", ocmsListRefundOrderRequest);
        OCMSAfterSaleListResponse ocmsAfterSaleListResponse = ocmsQueryThriftService.queryAfterSaleList(ocmsListRefundOrderRequest);
        log.info("ChannelOrderClient.queryRefundOrderList call ocmsAfterSaleListResponse:{}", ocmsAfterSaleListResponse);
        return ocmsAfterSaleListResponse;
    }

    /**
     * 查询融合订单商品列表
     *
     * @param orderItemFuseQueryBO
     * @return
     */
    @MethodLog(logRequest = true, logResponse = true)
    public OrderItemFuseSearchResponse queryFuseOrderItems(OrderItemFuseQueryBO orderItemFuseQueryBO) {
        log.info("ChannelOrderClient.queryFuseOrderItems request:{}", orderItemFuseQueryBO);
        OrderItemFuseSearchResponse resp = RpcInvoker.invoke(() -> orderItemFuseSearchThriftService.orderItemFuseList(orderItemFuseQueryBO.toOrderItemAllFuseListReq()));
        log.info("ChannelOrderClient.queryFuseOrderItems response:{}", resp);
        ResponseHandler.handleStatus(resp.getResponseStatus(), resp.getMsg());
        return resp;
    }

    /**
     * 根据批量订单号查询组合商品
     *
     * @return
     */
    @MethodLog(logRequest = true, logResponse = true)
    public CombinationProductSearchResponse queryCombinationProductByOrderIds(SearchCombinationProductReq req) {
        log.info("ChannelOrderClient.queryCombinationProductByOrderIds call OcmsOrderSearchThriftServiceV2.queryCombinationProductByOrderIds request:{}", req);
        CombinationProductSearchResponse resp = RpcInvoker.invoke(() -> orderItemFuseSearchThriftService.queryCombinationProductByOrderIds(req));
        log.info("ChannelOrderClient.queryCombinationProductByOrderIds call OcmsOrderSearchThriftServiceV2.queryCombinationProductByOrderIds response:{}", resp);
        ResponseHandler.handleStatus(resp.getResponseStatus(), resp.getMsg());
        return resp;
    }

    public OCMSAfterSaleCountResponse queryRefundCount(RefundOrderQueryRequest refundOrderQueryRequest) {
        log.info("ChannelOrderClient.queryRefundCount call refundOrderQueryRequest:{}", refundOrderQueryRequest);
        OCMSListRefundOrderRequest ocmsListRefundOrderRequest = refundOrderQueryRequest.toOcmsListRefundOrderRequest();

        ocmsListRefundOrderRequest.setPage(1);
        ocmsListRefundOrderRequest.setSize(1);
        log.info("ChannelOrderClient.queryRefundCount call ocmsListRefundOrderRequest:{}", ocmsListRefundOrderRequest);

        final OCMSAfterSaleCountResponse ocmsAfterSaleCountResponse = ocmsQueryThriftService.queryAfterSaleCount(ocmsListRefundOrderRequest);
        log.info("ChannelOrderClient.queryRefundCount call ocmsAfterSaleCountResponse:{}", ocmsAfterSaleCountResponse);
        return ocmsAfterSaleCountResponse;
    }

    public OrderExportMaxCountVO isOverOrderListAndDetailLimit(FuseOrderListExportTaskCreateRequest exportFuseOrderListRequest){
        OrderExportMaxCountVO vo = OrderExportMaxCountVO.builder()
                .isOverLimit(true)
                .build();
        ExportOrderCountResponse response = fuseOrderListExportThriftService.isOverListDetailExportLimit(exportFuseOrderListRequest.getOcmsOrderFuseListReq());
        if (response != null && StatusCodeEnum.SUCCESS.getCode() == response.getStatus().code){
            vo.setIsOverLimit(response.getIsOverLimit());
            vo.setLimit(response.getMaxCount());
        }
        return vo;
    }

    /**
     * 查询订单详情走ordermng
     *
     * @param orderId
     * @return
     */
    @MethodLog(logRequest = true, logResponse = true)
    public Pair<OrderFuseDetailBO,Map<Integer, DeliveryChannelDto>> queryFuseDetail(String orderId, Long tenantId, int channelId, Long uid, Boolean hideOrderRecvAddress) {

        OcmsOrderDetailReq req = new OcmsOrderDetailReq();
        req.setChannelOrderId(orderId);
        req.setChannelId(channelId);
        req.setTenantId(tenantId);
        req.setOperator(uid);
        req.setHideOrderRecvAddress(hideOrderRecvAddress);

        OcmsOrderDetailResponse resp = RpcInvoker.invoke(() -> ocmsOrderSearchService.orderDetail(req));

        ResponseHandler.checkResponseAndStatus(resp, r -> r.getResponseStatus(), r -> r.getMsg());

        if (resp.getOrder() == null) {
            throw new BizException("订单不存在");
        }


        OrderFuseDetailBO orderFuseDetailBO = new OrderFuseDetailBO(resp.getOrder());
        Map<Integer, DeliveryChannelDto> channelDtoMap = appendDeliveryInfo(orderFuseDetailBO, resp.getOrder());
        // 订单根据配送方式决定是否展示【贵品取货码】
        isShowExpensiveProductPickupCode(orderFuseDetailBO);

        return new Pair<>(orderFuseDetailBO,channelDtoMap);
    }

    /**
     * 订单根据配送方式决定是否展示【贵品取货码】
     * @param orderFuseDetailBO
     */
    private void isShowExpensiveProductPickupCode(OrderFuseDetailBO orderFuseDetailBO){
        try {
            // 无数据
            if (Objects.isNull(orderFuseDetailBO) || Objects.isNull(orderFuseDetailBO.getBaseInfo())) {
                return;
            }
            OrderFuseDetailBO.BaseInfo baseInfo = orderFuseDetailBO.getBaseInfo();
            // 如果不是平台配送，则不返回贵品取货码
            if(StringUtils.isNotEmpty(baseInfo.getExpensiveProductPickupCode()) && Objects.nonNull(baseInfo.getDeliveryChannelType()) && !Objects.equals(DeliveryEntityEnum.PLATFORM.getValue(), baseInfo.getDeliveryChannelType())){
                baseInfo.setExpensiveProductPickupCode(null);
            }
        }catch (Exception e){
            log.warn("web orderDetail checkExpensiveProductPickupCode is error");
        }
    }

    private Map<Integer, DeliveryChannelDto> appendDeliveryInfo(OrderFuseDetailBO channelOrderDetailBO, OrderDetailVo order) {
        Long shopId = order.getOrderBaseDto().getShopId();
        if(order.getOrderBaseDto().getDispatchShopId()!=null){
            shopId = order.getOrderBaseDto().getDispatchShopId();
        }
        QueryOrderDeliveryInfoKey deliveryInfoKey = new QueryOrderDeliveryInfoKey(order.getOrderBaseDto().getTenantId(),
                shopId,
                order.getOrderBaseDto().getOrderId(),
                order.getOrderBaseDto().getOrderStatus(),
                order.getOrderBaseDto().getOrderSource(),
                order.getOrderBaseDto().getDeliveryMethod(),
                order.getOrderBaseDto().getIsSelfDelivery());
        try {
            return appendDeliveryInfoService.appendDeliveryInfo(channelOrderDetailBO, deliveryInfoKey);
        } catch (Exception e) {
            log.error("appendDeliveryInfo error,order:{}", order, e);
        }
        return new HashMap<>();
    }




    public Map<String, Boolean> getSelfDeliveryOrderMap(Long tenantId, Long shopId, List<ChannelOrderIdKeyReq> orderKeyList) {
        try {
            BatchFulfillmentOrderIdKeyReq req = new BatchFulfillmentOrderIdKeyReq();
            req.setTenantId(tenantId);
            req.setWarehouseId(shopId);
            req.setChannelOrderIdKeyList(orderKeyList);
            FulfillmentOrderDetailResponse response = fulfillmentOrderSearchThriftService.searchFulfillmentOrderListByBatchFulfillmentOrderId(req);
            if (response != null) {
                return Optional.ofNullable(response.getFulfillmentOrderList()).orElse(Collections.emptyList()).stream().filter(order -> order.getIsContainSealProduct() != null)
                        .collect(Collectors.toMap(FulfillmentOrderDetailDTO::getChannelOrderId, FulfillmentOrderDetailDTO::getIsContainSealProduct));
            }
        } catch (Exception ex) {
            log.error("isSelfDeliveryOrder error,order:{}", orderKeyList, ex);

        }
        return new HashMap<>();
    }

    /**
     * 查询订单列表
     *
     * @param channelOrderQueryBO
     * @return
     */
    @MethodLog(logRequest = true, logResponse = true)
    public PageResult<ChannelOrderBO> queryOrders(ChannelOrderQueryBO channelOrderQueryBO) {

        OrderSearchResponse resp = RpcInvoker.invoke(() -> orderSearchService.list(channelOrderQueryBO.toOrderListRequest()));

        ResponseHandler.handleStatus(resp.getResponseStatus(), resp.getMsg());

        List<ChannelOrderBO> channelOrderBOS = resp.getOrderList() == null ? Lists.newArrayList() : ConverterUtils.convertList(resp.getOrderList(), ChannelOrderBO::new);

        PageResult<ChannelOrderBO> pageResult = new PageResult<>(channelOrderBOS,
                channelOrderQueryBO.getPage(), channelOrderQueryBO.getPageSize(), Optional.ofNullable(resp.getTotalCount()).orElse(0));

        return pageResult;
    }

    public DhDeliveryExtraVO queryDhDeliveryExtraData(String viewOrderId) {
        try {
            DeliveryExtraResponse response = orderSearchService.queryDhDeliveryExtraData(viewOrderId);
            if (response != null && response.getStatus().getCode() == StatusCodeEnum.SUCCESS.getCode()) {
                DhDeliveryExtraVO orderOfflineVO = DhDeliveryExtraVO.buildDhDeliveryExtraVO(response.getDhDeliverExtraInfoDTO());
                return orderOfflineVO;
            }
        } catch (Exception ex) {
            log.error("queryOrderOffLineData error, viewOrderId:{}", viewOrderId, ex);
        }
        return null;
    }

    /**
     * 查询订单详情走ordermng
     *
     * @param orderId
     * @return
     */
    @MethodLog(logRequest = true, logResponse = true)
    public ChannelOrderDetailBO queryDetail(String orderId, Long tenantId, int channelId, Long uid, Boolean hideOrderRecvAddress, boolean privatePhonePermission) {
        OcmsOrderDetailReq req = new OcmsOrderDetailReq();
        req.setChannelOrderId(orderId);
        req.setChannelId(channelId);
        req.setTenantId(tenantId);
        req.setOperator(uid);
        req.setHideOrderRecvAddress(hideOrderRecvAddress);
        OcmsOrderDetailResponse resp = RpcInvoker.invoke(() -> ocmsOrderSearchService.orderDetail(req));
        ResponseHandler.checkResponseAndStatus(resp, r -> r.getResponseStatus(), r -> r.getMsg());
        if (resp.getOrder() == null) {
            throw new BizException("订单不存在");
        }
        return new ChannelOrderDetailBO(resp.getOrder(), privatePhonePermission);
    }

    /**
     * 查询待处理订单列表
     *
     * @param ocmsUnDoneOrderListReq 请求
     * @return 待确认订单列表
     */
    @MethodLog(logRequest = true, logResponse = true)
    public PageResultV2<ConfirmOrderVO> unDoneOrderList(OcmsUnDoneOrderListReq ocmsUnDoneOrderListReq) {

        OcmsOrderSearchResponse resp = RpcInvoker.invoke(() -> ocmsOrderSearchService.unDoneOrderList(ocmsUnDoneOrderListReq));

        ResponseHandler.handleStatus(resp.getResponseStatus(), resp.getMsg());
        List<OrderInfoVo> orders = Optional.ofNullable(resp.getOrderListResp()).map(OrderListPageVo::getOrders).orElse(Collections.emptyList());
        Map<OCMSOrderKey, OrderInfoVo> orderKeyMap = orders.stream()
                .collect(Collectors.toMap(order -> OCMSOrderKey.builder()
                        .channelOrderId(order.getChannelOrderId())
                        .channelType(order.getChannelId()).build(), order -> order, (first, second) -> first));

        if(MapUtils.isNotEmpty(orderKeyMap)){
            Map<OCMSOrderKey, List<Integer>> ocmsOrderKeyListMap = Lists.partition(orderKeyMap.keySet().stream().collect(Collectors.toList()), 20)
                    .stream()
                    .map( orderKeyList->orderBizRemoteService.queryOrderOperateItems(ocmsUnDoneOrderListReq.getTenantId(), orderKeyList, DEFAULT_OCMS_CHECK_ITEMS))
                    .filter(Objects::nonNull)
                    .reduce(new HashMap<>(), (f, s) -> {
                        // 合并map
                        if (s != null && !s.isEmpty()){
                            f.putAll(s);
                        }
                        return f;
                    });

            if(MapUtils.isNotEmpty(ocmsOrderKeyListMap)){
                orderKeyMap.forEach((key, value) -> {
                    value.setCouldOperateItemList(ocmsOrderKeyListMap.get(key));
                });
            }
        }
        return channelOrderConverter.ocmsUnDoneResponseConvert(resp);
    }

    /**
     * 查询订单列表
     *
     * @param channelOrderQueryBO
     * @return
     */
    @MethodLog(logRequest = true, logResponse = true)
    public PageResult<AuditOrderBO> queryAuditOrders(AuditOrderListQueryBO channelOrderQueryBO) {

        OcmsOrderSearchResponse resp =
                RpcInvoker.invoke(() -> ocmsOrderSearchService.auditCancelOrderList(channelOrderQueryBO.toOcmsCancelAuditReq()));

        ResponseHandler.checkResponseAndStatus(resp, r -> r.getResponseStatus(), r -> r.getMsg());

        List<AuditOrderBO> channelOrderBOS = ConverterUtils.convertList(resp.getOrderListResp().getOrders(), AuditOrderBO::new);

        PageInfoVo page = resp.getOrderListResp().getPage();
        return new PageResult<>(channelOrderBOS, page.getPage(), page.getSize(), page.getTotalSize());
    }

    /**
     * 查询超时订单列表
     *
     * @return 超时订单列表
     */
    @MethodLog(logRequest = true, logResponse = true)
    public PageResultV2<DhLateOrderVO> queryLateOrderList(QueryLateOrderRequest request) {
        DhOverTimeOrderPageQueryRequest lateOrderPageQueryRequest = new DhOverTimeOrderPageQueryRequest();
        lateOrderPageQueryRequest.setPoiIdList(request.getStoreIds());
        lateOrderPageQueryRequest.setOrderId(request.getOrderId());
        lateOrderPageQueryRequest.setOrderType(request.getOrderType());
        lateOrderPageQueryRequest.setChannelId(request.getChannelId());
        if (StringUtils.isNotBlank(request.getCreateEndTime())) {
            lateOrderPageQueryRequest.setCreateEndTime(TimeUtil.convertStringTime(request.getCreateEndTime()));
        }
        if (StringUtils.isNotBlank(request.getCreateStartTime())) {
            lateOrderPageQueryRequest.setCreateStartTime(TimeUtil.convertStringTime(request.getCreateStartTime()));

        }
        if (StringUtils.isNotBlank(request.getActualArrivalEndTime())) {
            lateOrderPageQueryRequest.setActualArrivalEndTime(TimeUtil.convertStringTime(request.getActualArrivalEndTime()));

        }
        if (StringUtils.isNotBlank(request.getActualArrivalStartTime())) {
            lateOrderPageQueryRequest.setActualArrivalStartTime(TimeUtil.convertStringTime(request.getActualArrivalStartTime()));

        }
        if (StringUtils.isNotBlank(request.getEstimatedArrivalStartTime())) {
            lateOrderPageQueryRequest.setEstimatedArrivalStartTime(TimeUtil.convertStringTime(request.getEstimatedArrivalStartTime()));

        }
        if (StringUtils.isNotBlank(request.getEstimatedArrivalEndTime())) {
            lateOrderPageQueryRequest.setEstimatedArrivalEndTime(TimeUtil.convertStringTime(request.getEstimatedArrivalEndTime()));

        }
        lateOrderPageQueryRequest.setHasCompensated(request.getHasCompensated());
        lateOrderPageQueryRequest.setPage(request.getPage());
        lateOrderPageQueryRequest.setPageSize(request.getPageSize());

        DHLateOrderQueryResponse response = RpcInvoker.invoke(() -> drunkHorseOrderThriftService.lateOrderPageQuery(lateOrderPageQueryRequest));

        ResponseHandler.handleStatus(response.getStatus().getCode(), response.getStatus().getMsg());

        List<DhLateOrderVO> dhLateOrderVOS = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(response.getOverTimeOrderVOList())) {
            dhLateOrderVOS = response.getOverTimeOrderVOList().stream().map(DhLateOrderVO::convertVO).collect(Collectors.toList());

        }

        PageResultV2<DhLateOrderVO> pageResultV2 = new PageResultV2<>(dhLateOrderVOS, (int) response.getPageInfoDTO().getPage(), (int) response.getPageInfoDTO().getSize(),
                (int) response.getPageInfoDTO().getTotalSize(), (int) response.getPageInfoDTO().getTotalPage());
        return pageResultV2;

    }

    /**
     * 同意退款
     *
     * @param orderId
     * @param tenantId
     */
    @MethodLog(logRequest = true, logResponse = true)
    public void agreeRefund(String orderId, Long tenantId, Long operatorId, String operator, String reason, int channelId, int refundTagId, String serviceId, Integer afsApplyType) {

        AgreeRefundReq req = new AgreeRefundReq();
        req.setChannelType(channelId);
        req.setChannelOrderId(orderId);
        req.setOptUserId(operatorId);
        req.setOptUserName(operator);
        req.setTenantId(tenantId);
        req.setReason(reason);
        req.setRefundTagId(refundTagId);
        req.setAfsApplyType(afsApplyType);
        req.setAfterSaleId(serviceId);


        AgreeRefundResp resp = RpcInvoker.invoke(() -> channelOrderService.agreeRefund(req));

        ResponseHandler.checkResponseAndStatus(resp, r -> r.getStatus().getCode(), r -> r.getStatus().getMsg());
    }

    /**
     * 拒绝退款
     *
     * @param orderId
     * @param tenantId
     */
    @MethodLog(logRequest = true, logResponse = true)
    public void rejectRefund(String orderId, Long tenantId, Long operatorId, String operator, String reason, int channelId, int refundTagId, String serviceId, Integer afsApplyType) {

        RejectRefundReq req = new RejectRefundReq();
        req.setChannelType(channelId);
        req.setChannelOrderId(orderId);
        req.setOptUserId(operatorId);
        req.setOptUserName(operator);
        req.setTenantId(tenantId);
        req.setReason(reason);
        req.setRefundTagId(refundTagId);
        req.setAfterSaleId(serviceId);
        req.setAfsApplyType(afsApplyType);

        RejectRefundResp resp = RpcInvoker.invoke(() -> channelOrderService.rejectRefund(req));

        ResponseHandler.checkResponseAndStatus(resp, r -> r.getStatus().getCode(), r -> r.getStatus().getMsg());
    }

    /**
     * 审核时美团名酒馆订单提示
     *
     * @param viewOrderId
     * @param tenantId
     * @return
     */
    public Pair<Boolean, String> refundCheckMtFamousTavern(String viewOrderId, Long tenantId) {
        OcmsOrderDetailReq req = new OcmsOrderDetailReq();
        req.setChannelOrderId(viewOrderId);
        req.setChannelId(DynamicChannelType.MEITUAN.getChannelId());
        req.setTenantId(tenantId);

        OcmsOrderDetailResponse resp = RpcInvoker.invoke(() -> ocmsOrderSearchService.orderDetail(req));
        ResponseHandler.checkResponseAndStatus(resp, r -> r.getResponseStatus(), r -> r.getMsg());
        if (Objects.isNull(resp.getOrder()) || Objects.isNull(resp.getOrder().getOrderBaseDto())) {
            throw new BizException("订单不存在");
        }
        if (BooleanUtils.isFalse(resp.getOrder().getOrderBaseDto().getIsMtFamousTavern())) {
            return new Pair<>(false, "非美团名酒馆订单");
        }
        String gatherPoiPhone = null, gatherAfterSalesPhone = null, gatherCustomPhone = null, gatherPreSalePhone = null;
        if (Objects.nonNull(resp.getOrder().getDeliveryInfoDTO())
                && Objects.nonNull(resp.getOrder().getDeliveryInfoDTO().getExtData())) {
            DeliveryInfoExtend extData = resp.getOrder().getDeliveryInfoDTO().getExtData();
            gatherPoiPhone = extData.getGatherPoiPhone();
            gatherAfterSalesPhone = extData.getGatherAfterSalesPhone();
            gatherCustomPhone = extData.getGatherCustomPhone();
            gatherPreSalePhone = extData.getGatherPreSalePhone();
        }
        // 设置电话类型取值的优先级依次为：售后电话>商家电话>自定义电话>售前电话
        String gatherPhone = StringUtils.isNotBlank(gatherAfterSalesPhone) ? gatherAfterSalesPhone
                : StringUtils.isNotBlank(gatherPoiPhone) ? gatherPoiPhone : StringUtils.isNotBlank(gatherCustomPhone)
                ? gatherCustomPhone : StringUtils.isNotBlank(gatherPreSalePhone) ? gatherPreSalePhone : null;
        if (StringUtils.isNotBlank(gatherPhone)) {
            return new Pair<>(true, "名酒馆订单需联系名酒馆店员处理，联系方式" + gatherPhone);
        } else {
            return new Pair<>(true, "名酒馆订单需联系名酒馆店员处理，无法获取到名酒馆联系方式，可先到商家端后台处理。");
        }
    }

    /**
     * 查询超时订单列表
     *
     * @return 超时订单列表
     */
    @MethodLog(logRequest = true, logResponse = true)
    public List<DhLateOrderVO> queryAllLateOrderList(LateOrderDownloadRequest request) {
        DhOverTimeOrderAllQueryRequest lateOrderAllQueryRequest = new DhOverTimeOrderAllQueryRequest();
        lateOrderAllQueryRequest.setPoiIdList(request.getStoreIds());
        lateOrderAllQueryRequest.setOrderId(request.getOrderId());
        lateOrderAllQueryRequest.setOrderType(request.getOrderType());
        lateOrderAllQueryRequest.setChannelId(request.getChannelId());
        if (StringUtils.isNotBlank(request.getCreateEndTime())) {
            lateOrderAllQueryRequest.setCreateEndTime(TimeUtil.convertStringTime(request.getCreateEndTime()));
        }
        if (StringUtils.isNotBlank(request.getCreateStartTime())) {
            lateOrderAllQueryRequest.setCreateStartTime(TimeUtil.convertStringTime(request.getCreateStartTime()));

        }
        if (StringUtils.isNotBlank(request.getActualArrivalEndTime())) {
            lateOrderAllQueryRequest.setActualArrivalEndTime(TimeUtil.convertStringTime(request.getActualArrivalEndTime()));

        }
        if (StringUtils.isNotBlank(request.getActualArrivalStartTime())) {
            lateOrderAllQueryRequest.setActualArrivalStartTime(TimeUtil.convertStringTime(request.getActualArrivalStartTime()));

        }
        if (StringUtils.isNotBlank(request.getEstimatedArrivalStartTime())) {
            lateOrderAllQueryRequest.setEstimatedArrivalStartTime(TimeUtil.convertStringTime(request.getEstimatedArrivalStartTime()));

        }

        if (StringUtils.isNotBlank(request.getEstimatedArrivalEndTime())) {
            lateOrderAllQueryRequest.setEstimatedArrivalEndTime(TimeUtil.convertStringTime(request.getEstimatedArrivalEndTime()));

        }
        lateOrderAllQueryRequest.setHasCompensated(request.getHasCompensated());


        CommonDataResponse<List<OverTimeOrderVO>> response = RpcInvoker.invoke(() -> drunkHorseOrderThriftService.lateOrderAllQuery(lateOrderAllQueryRequest));

        ResponseHandler.handleStatus(response.getStatus().getCode(), response.getStatus().getMsg());
        List<DhLateOrderVO> res = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(response.getData())) {
            res = response.getData().stream().map(DhLateOrderVO::convertVO).collect(Collectors.toList());
        }
        return res;

    }


}
