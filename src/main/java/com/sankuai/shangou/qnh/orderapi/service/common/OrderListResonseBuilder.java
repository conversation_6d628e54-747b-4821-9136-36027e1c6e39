package com.sankuai.shangou.qnh.orderapi.service.common;



import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSWaitAuditOrderVO;
import com.meituan.shangou.saas.order.platform.client.dto.model.OrderLabelModel;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.OrderListResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.RefundApplyListResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/7/15
 **/
@Slf4j
@Component
public class OrderListResonseBuilder {

    @Resource
    private DeliveryService deliveryService;

    @Resource
    OrderVoConvertUtil orderVoConvertUtil;


    public OrderListResponse buildBasicOrderListResponse(List<? extends OCMSOrderVO> ocmsOrderVOList, PageInfoVO pageInfoVO, List<OrderLabelModel> showLabelList) {
        OrderListResponse orderListResponse = new OrderListResponse();
        orderListResponse.setPageInfo(pageInfoVO);
        if (CollectionUtils.isNotEmpty(ocmsOrderVOList)) {
            //查询骑手上报异常，查询不到不影响主流程
            Map<String, DeliveryExceptionSummaryVO> exceptionSummaryVOMap = deliveryService.queryDeliveryExceptionVo(ocmsOrderVOList);
            orderListResponse.setOrderList(
                    ocmsOrderVOList.stream()
                            .map(ocmsOrder -> orderVoConvertUtil.buildOrderVO(ocmsOrder, null, showLabelList))
                            .map(order ->{
                                DeliveryExceptionSummaryVO exceptionSummaryVO = exceptionSummaryVOMap.get(order.getChannelOrderId());
                                if (exceptionSummaryVO != null){
                                    order.setDeliveryExceptionSummaryVOS(exceptionSummaryVO);
                                }

                                return order;
                            })
                            .collect(Collectors.toList()));
        }
        return orderListResponse;
    }




    public RefundApplyListResponse buildBasicRefundApplyListResponse(List<OCMSWaitAuditOrderVO> ocmsWaitAuditOrderVOList,
                                                                PageInfoVO pageInfoVO, List<OrderLabelModel> showLabelList) {
        RefundApplyListResponse refundApplyListResponse = new RefundApplyListResponse();
        refundApplyListResponse.setPageInfo(pageInfoVO);
        if (CollectionUtils.isNotEmpty(ocmsWaitAuditOrderVOList)) {
            refundApplyListResponse.setRefundApplyRecordVOList(ocmsWaitAuditOrderVOList.stream()
                    .map(ocmsWaitAuditOrder -> orderVoConvertUtil.buildRefundApplyRecordVO(ocmsWaitAuditOrder, null, showLabelList))
                    .collect(Collectors.toList()));
        }
        return refundApplyListResponse;
    }



}
