package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2020/4/16
 * desc: 待同步配送范围渠道
 */
@Getter
@Setter
public class ChannelForSyncDeliveryRangeVO {

    @FieldDoc(
            description = "渠道ID 100:美团外卖  200:饿了么  300:京东到家"
    )
    @ApiModelProperty(value = "门店ID")
    private Integer channelId;

    @FieldDoc(
            description = "起送费 单位:元  美团、饿了么 必传"
    )
    @ApiModelProperty(value = "起送费")
    private Double minOrderPrice;

    @FieldDoc(
            description = "配送费 单位:元  单位:分 饿了么 必传"
    )
    @ApiModelProperty(value = "配送费")
    private Double deliveryFee;

    @FieldDoc(
            description = "配送时长 单位:分钟 饿了么 必传"
    )
    @ApiModelProperty(value = "配送时长")
    private Integer deliveryTime;
}
