/*
 * Copyright (c) 2019 Meituan.com. All Rights Reserved.
 */
package com.sankuai.shangou.qnh.orderapi.context.store;

import com.meituan.shangou.saas.tenant.thrift.dto.resource.module.AppModuleDto;
import com.sankuai.meituan.reco.store.management.thrift.StoreInfo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.AccountInfoVo;
import lombok.Data;
import org.assertj.core.util.Lists;

import java.util.List;

/**
 * <br><br>
 * Author: linjianyu <br>
 * Date: 2019-07-09 Time: 21:39
 */
public class AppAuthContext {

    private static final ThreadLocal<Context> CONTEXT = ThreadLocal.withInitial(Context::new);

    public static Context getContext() {
        return CONTEXT.get();
    }

    public static void clear() {
        CONTEXT.remove();
    }

    @SuppressWarnings("WeakerAccess")
    @Data
    public static class Context {

        private AccountInfoVo account;
        private List<AppModuleDto> modules;
        private List<StoreInfo> stores = Lists.newArrayList();
    }
}
