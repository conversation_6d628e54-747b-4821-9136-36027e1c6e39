package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.ChannelSkuInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2019/11/20
 * desc: 创建门店商品响应
 */
@TypeDoc(
        description = "创建门店商品响应"
)
@Data
@ApiModel("创建门店商品响应")
public class CreateStoreSkuResp {

    @FieldDoc(
            description = "商品编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品编码")
    private String skuId;

    @FieldDoc(
            description = "商品渠道信息列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品渠道信息列表", required = true)
    private List<ChannelSkuInfoVO> skuChannelsInfoList;
}
