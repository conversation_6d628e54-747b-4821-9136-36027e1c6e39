package com.sankuai.shangou.qnh.orderapi.service.pc;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.shangou.saas.order.platform.enums.BoolTypeEnum;
import com.meituan.shangou.saas.order.platform.enums.DistributeMethodEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryEntityEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DisplayCancelStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.dto.DeliveryChannelDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.*;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.shangou.qnh.orderapi.constant.pc.DeliveryInfoSourceConstant;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.OrderFuseDetailBO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.OrderFuseVO;
import com.sankuai.shangou.qnh.orderapi.enums.pc.AggDeliveryPlatformEnum;
import com.sankuai.shangou.qnh.orderapi.enums.pc.DeliveryEstimatedTimeOutExceptionEnum;
import com.sankuai.shangou.qnh.orderapi.enums.pc.DeliveryOperateItemEnum;
import com.sankuai.shangou.qnh.orderapi.enums.pc.DeliveryTypeEnum;
import com.sankuai.shangou.qnh.orderapi.enums.pc.TmsDeliveryStatusDescEnum;
import com.sankuai.shangou.qnh.orderapi.remote.DeliveryChannelRemoteService;
import com.sankuai.shangou.qnh.orderapi.remote.TmsRemoteService;
import com.sankuai.shangou.qnh.orderapi.utils.pc.LionUtils;
import com.sankuai.shangou.qnh.orderapi.utils.pc.MccConfigUtil;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/12/16
 */
@Service
@Slf4j
public class AppendDeliveryInfoService {

    @Resource
    private TmsRemoteService tmsRemoteService;

    @Autowired
    private DeliveryChannelRemoteService deliveryChannelRemoteService;

    private static final Integer INVALID_DELIVERY_EXCEPTION_CODE = 0;

    // 用户自提配送
    private static final Integer STORE_DELIVERY = 99;

    // 用户自提配送
    private static final String UN_KNOWN = "未知";
    /**
     * 为订单列表添加配送信息、可操作按钮
     *
     * @param orderList
     */
    @MethodLog(logRequest = true, logResponse = true)
    public Pair<Map<Long, TDeliveryDetail>, Map<Integer, DeliveryChannelDto>>  appendDeliveryInfo(List<OrderFuseVO> orderList) {

        //如果新的配送信息查询降级，直接不append新的配送信息
        if (!LionUtils.getOrderTabAppendTmsDeliveryInfoSwitch()) {
            return new Pair<>(new HashMap<>(),new HashMap<>());
        }
        List<QueryOrderDeliveryInfoKey> empowerOrderIds = collectEmpowerOrderKeys(orderList);
        if (CollectionUtils.isEmpty(empowerOrderIds)) {
            return new Pair<>(new HashMap<>(),new HashMap<>());
        }
        /**
         * 查询tms的配送信息
         */
        Pair<Map<Long, TDeliveryDetail>, Map<Integer, DeliveryChannelDto>> tmsDeliveryInfo = queryRealTimeDeliveryInfo(empowerOrderIds);
        if (MapUtils.isEmpty(tmsDeliveryInfo.getKey())) {
            orderList.stream().forEach(this::processPlatformDelivery);
            return new Pair<>(new HashMap<>(),tmsDeliveryInfo.getValue());
        }
        /**
         * 聚合tms的配送信息
         */
        mergeRealTimeDeliveryInfo(orderList, tmsDeliveryInfo.getKey(), tmsDeliveryInfo.getValue());
        return tmsDeliveryInfo;

    }

    @MethodLog(logRequest = true, logResponse = true)
    public Map<Integer, DeliveryChannelDto> appendDeliveryInfo(OrderFuseDetailBO orderFuseDetailBO, QueryOrderDeliveryInfoKey deliveryInfoKey) {

        //如果新的配送信息查询降级，直接不append新的配送信息
        if (!LionUtils.getOrderTabAppendTmsDeliveryInfoSwitch()) {
            return new HashMap<>();
        }
        if (Objects.isNull(deliveryInfoKey) || Objects.isNull(orderFuseDetailBO.getBaseInfo())) {
            return new HashMap<>();
        }
        /**
         * 查询tms的配送信息
         */
        final OrderFuseDetailBO.BaseInfo baseInfo = orderFuseDetailBO.getBaseInfo();
        Pair<Map<Long, TDeliveryDetail>, Map<Integer, DeliveryChannelDto>> tmsDeliveryInfo = queryRealTimeDeliveryInfo(Collections.singletonList(deliveryInfoKey));
        if (MapUtils.isEmpty(tmsDeliveryInfo.getKey())) {
            log.info("tmsDeliveryInfo is empty");
            processPlatformDelivery(baseInfo);
            return new HashMap<>();
        }
        /**
         * 聚合tms的配送信息
         */

        mergeSingleOrderDeliveryInfo(baseInfo, tmsDeliveryInfo.getKey().values().iterator().next());
        return tmsDeliveryInfo.getValue();

    }

    private void mergeRealTimeDeliveryInfo(List<OrderFuseVO> orderList, Map<Long, TDeliveryDetail> tmsDeliveryInfo,Map<Integer, DeliveryChannelDto> channelDtoMap) {
        orderList.forEach(orderVO -> mergeSingleOrderDeliveryInfo(orderVO, tmsDeliveryInfo.get(Long.parseLong(orderVO.getEmpowerOrderId())),channelDtoMap));
    }

    private void mergeSingleOrderDeliveryInfo(OrderFuseDetailBO.BaseInfo baseInfo, TDeliveryDetail tDeliveryDetail) {

        if (tDeliveryDetail == null) {
            processPlatformDelivery(baseInfo);
            return;
        }
        // 对接tms展示【三方物流单号】信息
        List<OriginWaybillDto> waybillList = tDeliveryDetail.waybillList;
        if (CollectionUtils.isNotEmpty(waybillList)) {
            // 根据配送状态过滤，再过滤【三方物流单号】为空的数据
            baseInfo.setOriginWaybillNoList(waybillList.stream()
                    .filter(dto -> MccConfigUtil.checkOriginWaybillDeliveryStatus(dto.deliveryStatus)
                            && StringUtils.isNotBlank(dto.getOriginWaybillNo()))
                    .map(OriginWaybillDto::getOriginWaybillNo).collect(Collectors.toList()));
        }
        //如果tms没有配送运单信息，则不需要聚合tms的配送数据
        if (tDeliveryDetail.deliveryChannelName == null) {
            processPlatformDelivery(baseInfo);
            return;
        }
        baseInfo.setDistributeStatusDesc(TmsDeliveryStatusDescEnum.getDescFromCode(tDeliveryDetail.status));
        // 仅当配送状态大于初始化状态时，才展示配送方式字段
        if (tDeliveryDetail.status > TmsDeliveryStatusDescEnum.INIT.getCode()) {
            baseInfo.setDeliveryChannel(setDeliveryDesc(tDeliveryDetail, baseInfo.getOriginalDistributeType(), baseInfo.getIsFastOrder()));
            baseInfo.setDeliveryChannelType(tDeliveryDetail.deliveryEntity);
        }
    }

    private void mergeSingleOrderDeliveryInfo(OrderFuseVO orderVO, TDeliveryDetail tDeliveryDetail, Map<Integer, DeliveryChannelDto> channelDtoMap) {

        Map<Integer, Integer> deliveryChannelMap = deliveryChannelRemoteService.tratranslateToChannelIntgerMap(channelDtoMap);

        if (tDeliveryDetail == null) {
            processPlatformDelivery(orderVO);
            return;
        }
        orderVO.setDeliveryOperateItem(buildOperateItem(tDeliveryDetail,orderVO.getTenantId(),orderVO.getStoreId(),deliveryChannelMap));
        //如果tms没有配送运单信息，则不需要聚合tms的配送数据
        if (tDeliveryDetail.deliveryChannelName == null) {
            processPlatformDelivery(orderVO);
            return;
        }
        orderVO.setDeliveryInfoSource(DeliveryInfoSourceConstant.FROM_TMS);
        orderVO.setRealDistributeStatus(tDeliveryDetail.status);
        orderVO.setDistributeStatusDesc(TmsDeliveryStatusDescEnum.getDescFromCode(tDeliveryDetail.status));
        // 仅当配送状态大于初始化状态时，才展示配送方式字段
        if (orderVO.getRealDistributeStatus() > TmsDeliveryStatusDescEnum.INIT.getCode()) {
            orderVO.setDeliveryChannel(setDeliveryDesc(tDeliveryDetail, orderVO.getOriginalDistributeType(), orderVO.getIsFastOrder()));
            orderVO.setDeliveryChannelType(tDeliveryDetail.deliveryEntity);
        }
        orderVO.setDeliveryExceptionType(tDeliveryDetail.deliveryExceptionType);
        if (tDeliveryDetail.deliveryExceptionCode == null || tDeliveryDetail.deliveryExceptionCode.equals(INVALID_DELIVERY_EXCEPTION_CODE)) {
            orderVO.setDeliveryExceptionDesc(tDeliveryDetail.deliveryException);
        } else {
            orderVO.setDeliveryExceptionDesc(DeliveryExceptionCodeEnum.enumOf(tDeliveryDetail.deliveryExceptionCode).getDesc());
        }
        if (tDeliveryDetail.deliveryExceptionCode != null && tDeliveryDetail.deliveryExceptionCode.equals(DeliveryExceptionCodeEnum.OPEN_API_DELIVERY_EXCEPTION.getCode())) {
            orderVO.setDeliveryExceptionDesc(tDeliveryDetail.deliveryException);
        }
        if (tDeliveryDetail.deliveryExceptionCode != null && tDeliveryDetail.deliveryExceptionCode.equals(DeliveryExceptionCodeEnum.ESTIMATED_TIME_OUT.getCode())) {
            // 超时未完成、异常详情匹配
            orderVO.setDeliveryExceptionDesc(DeliveryEstimatedTimeOutExceptionEnum.deliveryStatusCodeValueOf(tDeliveryDetail.status).getDesc());
            orderVO.setWholeTimeout("整单超时");
        }
        orderVO.setDeliveryDistance(Double.valueOf(tDeliveryDetail.deliveryDistance));
        orderVO.setDeliveryUserName(tDeliveryDetail.riderName);
        orderVO.setDeliveryUserPhone(tDeliveryDetail.riderPhone);
        orderVO.setDeliveryFee(tDeliveryDetail.deliveryFee);
        orderVO.setTipFee(tDeliveryDetail.tipFee);
        orderVO.setCurrentDeliveryStatusStartTime(tDeliveryDetail.startWaitAssignRiderTime);
        orderVO.setDeliveryStatusChangeTime(tDeliveryDetail.deliveryStatusChangeTime);
    }

    private String setDeliveryDesc(TDeliveryDetail tDeliveryDetail, Integer originalDistributeType, Integer isFastOrder) {
        DeliveryEntityEnum deliveryEntityEnum = DeliveryEntityEnum.findByValue(tDeliveryDetail.deliveryEntity);
        if(Objects.isNull(deliveryEntityEnum)){
            return appendFourWheelDeliveryDesc(getPlatformDesc(originalDistributeType, isFastOrder), tDeliveryDetail.isFourWheelDelivery);
        }
        switch (deliveryEntityEnum){
            case THIRD_PART:
            case DELIVER_BY_SELF:
                return getSelfDeliveryDesc(tDeliveryDetail.deliveryChannelCode);
            default:
                return getPlatformDesc(originalDistributeType, tDeliveryDetail, isFastOrder);
        }

    }

    /**
     * 自配送信息
     * @param deliveryChannelCode
     * @return
     */
    private String getSelfDeliveryDesc(Integer deliveryChannelCode){
        String desc = DeliveryTypeEnum.MERCHANT_DELIVERY.getDesc();
        DeliveryTypeEnum deliveryTypeEnum = queryDeliveryTypeEnum(deliveryChannelCode);
        if(Objects.nonNull(deliveryTypeEnum)){
            return desc + "-" + deliveryTypeEnum.getDesc();

        }
        return desc + "-" + UN_KNOWN;
    }

    /**
     * 平台配送信息
     * @param originalDistributeType
     * @return
     */
    private String getPlatformDesc(Integer originalDistributeType, Integer isFastOrder){
        String desc = DeliveryTypeEnum.ORDER_PLATFORM_DELIVERY.getDesc();

        DeliveryTypeEnum deliveryTypeEnum = queryDeliveryTypeEnum(originalDistributeType);
        if(Objects.nonNull(deliveryTypeEnum) && Objects.equals(isFastOrder, BoolTypeEnum.YES.getValue())){
            return desc + "-" + deliveryTypeEnum.getDesc() + "闪电送";
        }
        if(Objects.nonNull(deliveryTypeEnum)){
            return desc + "-" + deliveryTypeEnum.getDesc();
        }
        return desc + "-" + UN_KNOWN;
    }

    private String getPlatformDesc(Integer originalDistributeType, TDeliveryDetail tDeliveryDetail, Integer isFastOrder) {
        int platformCode = DeliveryPlatformEnum.ORDER_CHANNEL_DELIVERY_PLATFORM.getCode();
        if (Objects.equals(platformCode, tDeliveryDetail.platformCode) && StringUtils.isNotBlank(tDeliveryDetail.deliveryChannelName)
                && !Objects.equals(tDeliveryDetail.deliveryChannelCode, DeliveryChannelEnum.ORDER_CHANNEL_DELIVERY.getCode())) {
            return tDeliveryDetail.deliveryChannelName;
        }
        return appendFourWheelDeliveryDesc(getPlatformDesc(originalDistributeType, isFastOrder), tDeliveryDetail.isFourWheelDelivery);
    }

    /**
     * 汽车配送描述
     * @param desc 配送描述信息
     * @param isFourWheelDelivery 是否汽车配送
     * @return 配送描述信息
     */
    private String appendFourWheelDeliveryDesc(String desc, Integer isFourWheelDelivery) {
        if (Objects.equals(isFourWheelDelivery, 2)) {
            return desc + "-" + "汽车";
        }
        return desc;
    }

    /**
     * 根据配送方式查询配送类型
     * @param deliveryChannelCode
     * @return
     */
    private DeliveryTypeEnum queryDeliveryTypeEnum(Integer deliveryChannelCode) {
        if (Objects.isNull(deliveryChannelCode)) {
            return null;
        }

        // 查询二级具体配送方式
        DeliveryTypeEnum deliveryTypeEnum = DeliveryTypeEnum.getByChildId(Long.valueOf(deliveryChannelCode));
        if(Objects.nonNull(deliveryTypeEnum)){
            return deliveryTypeEnum;

        }
        // 查询一级配送方式
        DeliveryTypeEnum notConfirmDeliveryEnum = DeliveryTypeEnum.getByParentId(Long.valueOf(deliveryChannelCode));
        if(Objects.nonNull(notConfirmDeliveryEnum)){
            return notConfirmDeliveryEnum;

        }
        return null;
    }

    private void processPlatformDelivery(OrderFuseVO orderVO) {
        if (orderVO == null) {
            return;
        }
        if (orderVO.getDistributeStatus() != null && orderVO.getDistributeStatus() > 0 && orderVO.getDeliveryMethod() != null && orderVO.getDeliveryMethod() == DistributeMethodEnum.HOME_DELIVERY.getValue()) {
            //增加迁移订单，没有tms运单的逻辑判断
            if (Integer.valueOf(1).equals(orderVO.getMigrateFlag())) {
                processMigrateDelivery(orderVO);
            } else {
                //非迁移订单正常判断为平台配送
                orderVO.setDeliveryChannel(getPlatformDesc(orderVO.getOriginalDistributeType(),orderVO.getIsFastOrder()));
                orderVO.setDeliveryChannelType(DeliveryEntityEnum.PLATFORM.getValue());
            }
        }
        if (orderVO.getDeliveryMethod() != null && orderVO.getDeliveryMethod() == DistributeMethodEnum.STORE_DELIVERY.getValue()) {
            orderVO.setDeliveryChannel(DeliveryTypeEnum.STORE_DELIVERY.getDesc());
            orderVO.setDeliveryChannelType(STORE_DELIVERY);
        }
    }


    //处理迁移订单的配送信息
    private void processMigrateDelivery(OrderFuseVO orderVO) {
        //平台配送
        if (Integer.valueOf(0).equals(orderVO.getSelfDelivery())) {
            orderVO.setDeliveryChannel(getPlatformDesc(orderVO.getOriginalDistributeType(), orderVO.getIsFastOrder()));
            orderVO.setDeliveryChannelType(DeliveryEntityEnum.PLATFORM.getValue());
        } else {
            final DeliveryTypeEnum deliveryTypeEnum = Optional.ofNullable(queryDeliveryTypeEnum(orderVO.getDeliveryChannelId())).orElse(DeliveryTypeEnum.AGGREGATION_DELIVERY);
            if (Lists.newArrayList(DeliveryTypeEnum.MERCHANT_DELIVERY, DeliveryTypeEnum.MERCHANT_SELF_DELIVERY).contains(deliveryTypeEnum)) {
                //商家配送
                orderVO.setDeliveryChannel(getSelfDeliveryDesc(orderVO.getDeliveryChannelId()));
                orderVO.setDeliveryChannelType(DeliveryEntityEnum.DELIVER_BY_SELF.getValue());
            } else {
                //三方配送
                orderVO.setDeliveryChannel(getSelfDeliveryDesc(orderVO.getDeliveryChannelId()));
                orderVO.setDeliveryChannelType(DeliveryEntityEnum.THIRD_PART.getValue());
            }
        }
    }

    //处理迁移订单的配送信息
    private void processMigrateDelivery(OrderFuseDetailBO.BaseInfo baseInfo) {
        //平台配送
        if (Integer.valueOf(0).equals(baseInfo.getSelfDelivery())) {
            baseInfo.setDeliveryChannel(getPlatformDesc(baseInfo.getOriginalDistributeType(), baseInfo.getIsFastOrder()));
            baseInfo.setDeliveryChannelType(DeliveryEntityEnum.PLATFORM.getValue());
        } else {
            final DeliveryTypeEnum deliveryTypeEnum = Optional.ofNullable(queryDeliveryTypeEnum(baseInfo.getDeliveryChannelId())).orElse(DeliveryTypeEnum.AGGREGATION_DELIVERY);
            if (Lists.newArrayList(DeliveryTypeEnum.MERCHANT_DELIVERY, DeliveryTypeEnum.MERCHANT_SELF_DELIVERY).contains(deliveryTypeEnum)) {
                //商家配送
                baseInfo.setDeliveryChannel(getSelfDeliveryDesc(baseInfo.getDeliveryChannelId()));
                baseInfo.setDeliveryChannelType(DeliveryEntityEnum.DELIVER_BY_SELF.getValue());
            } else {
                //三方配送
                baseInfo.setDeliveryChannel(getSelfDeliveryDesc(baseInfo.getDeliveryChannelId()));
                baseInfo.setDeliveryChannelType(DeliveryEntityEnum.THIRD_PART.getValue());
            }
        }
    }

    private void processPlatformDelivery(OrderFuseDetailBO.BaseInfo baseInfo) {
        if (baseInfo.getDistributeStatus() != null && baseInfo.getDistributeStatus() > 0 && baseInfo.getDistributeMethod() != null && baseInfo.getDistributeMethod() == DistributeMethodEnum.HOME_DELIVERY.getValue()) {
            if (BooleanUtils.isTrue(baseInfo.getMigrateFlag())) {
                processMigrateDelivery(baseInfo);
            } else {
                baseInfo.setDeliveryChannel(getPlatformDesc(baseInfo.getOriginalDistributeType(), baseInfo.getIsFastOrder()));
                baseInfo.setDeliveryChannelType(DeliveryEntityEnum.PLATFORM.getValue());
            }
        }
        if (baseInfo.getDistributeMethod() != null && baseInfo.getDistributeMethod() == DistributeMethodEnum.STORE_DELIVERY.getValue()) {
            baseInfo.setDeliveryChannel(DeliveryTypeEnum.STORE_DELIVERY.getDesc());
            baseInfo.setDeliveryChannelType(STORE_DELIVERY);
        }
    }

    /**
     * 根据tms的订单可用发配送类型，提供发配送按钮
     *
     * @param tDeliveryDetail
     * @return
     */
    private Integer buildOperateItem(TDeliveryDetail tDeliveryDetail, Long tenantId, Long storeId, Map<Integer,Integer> deliveryChannelMap) {
        TLaunchDeliveryType tLaunchDeliveryType = tDeliveryDetail.tLaunchDeliveryType;
        if (tLaunchDeliveryType == null) {
            return null;
        }
        if (tLaunchDeliveryType.canRetryLaunch && LionUtils.getDeliveryOperateItemRetry()) {
            return DeliveryOperateItemEnum.RETRY_LAUNCH.type;
        }
        if (tLaunchDeliveryType.canSelfDelivery && LionUtils.getDeliveryOperateItemSelfDelivery()
                && !deliveryChannelRemoteService.checkChannel(tDeliveryDetail.deliveryChannelCode, AggDeliveryPlatformEnum.MALT_FARM.getCode(),tenantId,storeId,deliveryChannelMap)) {
            return DeliveryOperateItemEnum.EXCEPTION_TO_SELF.type;
        }

        if (tLaunchDeliveryType.canLaunchThirdPartWhenException && LionUtils.getDeliveryOperateItemExceptionToThird()) {
            return DeliveryOperateItemEnum.EXCEPTION_TO_THIRD_PART.type;
        }
        if (tLaunchDeliveryType.canManualLaunchThirdPart && LionUtils.getDeliveryOperateItemManualThird()) {
            return DeliveryOperateItemEnum.MANUAL_THIRD_PART.type;
        }

        if (tLaunchDeliveryType.canCancel && LionUtils.getDeliveryOperateItemCanCancel()) {
            return DeliveryOperateItemEnum.CANCEL_DELIVERY.type;
        }
        if (Objects.equals(tDeliveryDetail.displayCancelStatus, DisplayCancelStatusEnum.CANCELING.getCode()) && LionUtils.getDeliveryOperateItemCanceling()) {
            return DeliveryOperateItemEnum.CANCELING.type;
        }

        if (tLaunchDeliveryType.canRetryLaunchByMaltfarm && LionUtils.getDeliveryOperateItemRetryByMaltfarm()) {
            return DeliveryOperateItemEnum.RETRY_LAUNCH_BY_MALTFARM.type;
        }

        if (tLaunchDeliveryType.canRetryLaunchByHaiKui && LionUtils.getDeliveryOperateItemRetryByHaiKui()) {
            return DeliveryOperateItemEnum.RETRY_LAUNCH_BY_HAIKUI.type;
        }

        return null;
    }

    /**
     * 去tms系统查询实时的配送信息
     *
     * @param empowerOrderIds
     * @return
     */
    public Pair<Map<Long, TDeliveryDetail>, Map<Integer, DeliveryChannelDto>> queryRealTimeDeliveryInfo(List<QueryOrderDeliveryInfoKey> empowerOrderIds) {

        List<TDeliveryDetail> tDeliveryDetails = Lists.newArrayList();
        try {
            tDeliveryDetails = tmsRemoteService.queryDeliveryInfoByOrderIds(empowerOrderIds);
        } catch (Exception e) {
            log.error("查询tms配送信息异常", e);
        }
        if (CollectionUtils.isEmpty(tDeliveryDetails)) {
            return new Pair<>(Maps.newHashMap(),Maps.newHashMap());
        }
        Map<Long, TDeliveryDetail> realDeliveryMap=tDeliveryDetails.stream().collect(Collectors.toMap(info -> info.bizOrderId, info -> info, (newEntry, oldEntry) -> newEntry));
        Set<Integer> channelCodes = tDeliveryDetails.stream().filter(e -> !deliveryChannelRemoteService.checkTenantAndStore(Objects.isNull(empowerOrderIds.get(0))? null : empowerOrderIds.get(0).tenantId, null)).map(e-> e.deliveryChannelCode).collect(Collectors.toSet());
        Map<Integer, DeliveryChannelDto> channelDtoMap = deliveryChannelRemoteService.getDeliveryChannelDtoMap(channelCodes);

        List<Long> doneOrderIdList=new ArrayList<>();
        for (Long orderId : realDeliveryMap.keySet()){
            TDeliveryDetail real=realDeliveryMap.get(orderId);
            if(real.status==null){
                continue;
            }
            if(real.status!= DeliveryStatusEnum.DELIVERY_CANCELLED.getCode()){
                continue;
            }
            if(real.deliveryCount==null || real.deliveryCount==1){
                continue;
            }
            doneOrderIdList.add(orderId);
        }
        if(CollectionUtils.isEmpty(doneOrderIdList)){
            log.warn("queryRealTimeDeliveryInfo warn： doneOrderIdList is empty");
            return new Pair<>(realDeliveryMap,channelDtoMap);
        }
        List<TDeliveryOrder> doneDeliveryList = Lists.newArrayList();
        try {
            doneDeliveryList= tmsRemoteService.queryActiveDeliveryInfoByOrderIds(doneOrderIdList);
        }catch (Exception e){
            log.error("queryActiveDeliveryInfoByOrderIds error doneOrderIdList:{}",doneOrderIdList,e);
        }
        if(CollectionUtils.isEmpty(doneDeliveryList)){
            log.warn("queryActiveDeliveryInfoByOrderId warn: doneDeliveryList is empty");
            return new Pair<>(realDeliveryMap,channelDtoMap);
        }
        Map<Long,TDeliveryOrder> orderMap=new HashMap<>();
        for (TDeliveryOrder order : doneDeliveryList){
            orderMap.put(order.getOrderId(),order);
        }
        Set<Integer> doneDeliveryChannelCodes = doneDeliveryList.stream().filter(e -> !deliveryChannelRemoteService.checkTenantAndStore(Objects.isNull(empowerOrderIds.get(0))? null : empowerOrderIds.get(0).tenantId, null)).map(TDeliveryOrder::getDeliveryChannel).collect(Collectors.toSet());
        //所有运单信息（包括取消、激活）的配送渠道code
        doneDeliveryChannelCodes.addAll(channelCodes);
        Map<Integer, DeliveryChannelDto> deliveryChannelAllDtoMap = deliveryChannelRemoteService.getDeliveryChannelDtoMap(doneDeliveryChannelCodes);
        Map<Integer, String> doneDeliveryChannelNameMap = deliveryChannelAllDtoMap.entrySet().stream().filter(e -> Objects.nonNull(e.getValue())).collect(Collectors.toMap(Map.Entry::getKey,entry -> entry.getValue().getCarrierName()));
        for (Long orderId : doneOrderIdList){
            TDeliveryOrder order=orderMap.get(orderId);
            TDeliveryDetail detail=realDeliveryMap.get(orderId);
            realDeliveryMap.put(orderId,convertToDetail(order,detail,doneDeliveryChannelNameMap));
        }
        return new Pair<>(realDeliveryMap,deliveryChannelAllDtoMap);
    }

    private TDeliveryDetail convertToDetail(TDeliveryOrder domain,TDeliveryDetail otherDetail,Map<Integer,String> channelNameMap){
        String channelName = null;
        if (!deliveryChannelRemoteService.checkTenantAndStore(domain.getTenantId(), domain.getStoreId())){
            channelName = channelNameMap.getOrDefault(domain.getDeliveryChannel(),null);
        }else {
            channelName = Objects.isNull(DeliveryChannelEnum.valueOf(domain.getDeliveryChannel()))? null : DeliveryChannelEnum.valueOf(domain.getDeliveryChannel()).getName();
        }

        return TDeliveryDetail.builder().deliveryFee(domain.getDeliveryFee() == null? null : domain.getDeliveryFee().doubleValue())
                .deliveryDistance(domain.getDistance())
                .bizOrderId(domain.getOrderId())
                .deliveryEntity(otherDetail.deliveryEntity)
                .deliveryChannelName(channelName)
                .riderName( domain.getRiderName())
                .status(domain.getStatus())
                .tLaunchDeliveryType(otherDetail.tLaunchDeliveryType)
                .riderPhone(domain.getRiderPhone())
                .deliveryChannelCode(Optional.ofNullable(domain.getDeliveryChannel()).orElse(DeliveryChannelEnum.AGGREGATION_DELIVERY.getCode()))
                .deliveryCount(domain.getDeliveryCount()==null ? 1:domain.getDeliveryCount())
                .tipFee(domain.getTipAmount() == null ? null : domain.getTipAmount())
                .fulfillOrderId(domain.getFulfillmentOrderId())
                .platformSource(domain.getPlatformSourceCode())
                .waybillList(otherDetail.waybillList)
                .build();
    }

    private List<QueryOrderDeliveryInfoKey> collectEmpowerOrderKeys(List<OrderFuseVO> orderList) {

        if (CollectionUtils.isEmpty(orderList)) {
            return Lists.newArrayList();
        }
        return orderList.stream().map(this::buildQueryOrderKey).collect(Collectors.toList());
    }

    private QueryOrderDeliveryInfoKey buildQueryOrderKey(OrderFuseVO order) {
        Long shopId=order.getStoreId();
        if(order.getWarehouseId()!=null){
            shopId=order.getWarehouseId();
        }
        if(order.getDispatchShopId()!=null){
            shopId = order.getDispatchShopId();
        }
        long orderId = 0L;
        if(StringUtils.isNotEmpty(order.getEmpowerOrderId())){
            orderId = Long.parseLong(order.getEmpowerOrderId());
        }
        return new QueryOrderDeliveryInfoKey(order.getTenantId(), shopId, orderId, order.getOrderStatus(), order.getOrderSource(), order.getDeliveryMethod(), order.getSelfDelivery());

    }

}

