package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@TypeDoc(
        description = "盘点单明细信息",
        authors = {
                "qianteng"
        }
)
@Data
@ApiModel("盘点单明细信息")
public class StockCheckOrderDetailRes {
    @FieldDoc(
            description = "盘点单号"
    )
    private String stockCheckId;
    @FieldDoc(
            description = "创建时间"
    )
    private String createTime;
    @FieldDoc(
            description = "盘点类型：1-全盘；2-品类盘；3-随机盘；4-指定sku"
    )
    private Integer stockCheckType;
    @FieldDoc(
            description = "盘点单状态：-1-已失效；1-待盘点；2-待审核；3-已完成；4-驳回"
    )
    private Integer status;
    @FieldDoc(
            description = "提交时间"
    )
    private String submitTime;
    @FieldDoc(
            description = "提交人id"
    )
    private String submitter;
    @FieldDoc(
            description = "提交人名称"
    )
    private String submitterName;
    @FieldDoc(
            description = "备注"
    )
    private String Comment;
    @FieldDoc(
            description = "盘点库区类型:1-售卖区；8-半成品原料区；9-餐饮原料区"
    )
    private Integer checkAreaType;
    @FieldDoc(
            description = "盘点商品种类"
    )
    private Integer skuCount;
}
