package com.sankuai.shangou.qnh.orderapi.service.pc;

import com.meituan.shangou.saas.tenant.thrift.common.enums.TenantBusinessModeEnum;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.ChannelInfoBo;

import java.util.List;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/8 16:40
 * @Description:
 */
public interface TenantService {

    /**
     * 查询开通的渠道列表
     *
     * @param tenantId
     * @param module
     * @return
     */
    List<ChannelInfoBo> queryChannels(Long tenantId, String module);

    List<ChannelInfoBo> queryChannels(Long tenantId);

    /**
     * 查询租户是否有ERP系统
     *
     * @param tenantId
     * @return
     */
    Boolean queryTenantHasErp(Long tenantId);

    /**
     * 是否是超市便利
     *
     * @param tenantId
     * @return
     */
    boolean isSuperMarketMode(Long tenantId);

    /**
     * 根据租户id查询业态
     *
     * @param tenantId
     * @return
     */
    TenantBusinessModeEnum queryTenantBusinessMode(Long tenantId);

    Map<Integer, Boolean> queryAccountCode();
}
