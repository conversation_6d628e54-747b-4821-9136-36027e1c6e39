package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/16
 * desc: 查询门店配送配置请求
 */
@Getter
@Setter
public class QueryShopDeliveryConfigRequest implements BaseRequest {

    @FieldDoc(
            description = "门店ID列表"
    )
    @ApiModelProperty(value = "门店ID列表")
    private List<String> poiIds;

    @FieldDoc(
            description = "门店ID"
    )
    @ApiModelProperty(value = "门店ID")
    private String poiId;

    @FieldDoc(
            description = "查询页号"
    )
    @ApiModelProperty(value = "查询页号", required = true)
    private Integer page;

    @FieldDoc(
            description = "每页记录数"
    )
    @ApiModelProperty(value = "每页记录数", required = true)
    private Integer pageSize;

    @Override
    public void selfCheck() {
        AssertUtil.isPositiveNumber(page, "查询页号非法" , "page");
        AssertUtil.isPositiveNumber(pageSize, "每页记录数非法" , "page");
    }
}
