package com.sankuai.shangou.qnh.orderapi.service.store;

import com.meituan.reco.pickselect.common.cat.CatTransaction;
import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.meituan.shangou.saas.order.platform.enums.AfterSalePatternEnum;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.CommonResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.AfterSaleRecordVO;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.OrderDetailResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.OrderDetailVO;
import com.sankuai.shangou.qnh.orderapi.remote.SaleReturnOrderRemoteService;
import com.sankuai.shangou.qnh.orderapi.utils.store.ApiMethodParamThreadLocal;
import com.sankuai.shangou.qnh.orderapi.utils.store.OCMSUtils;
import com.sankuai.meituan.reco.store.management.enums.SaleReturnTypeEnum;
import com.sankuai.meituan.reco.store.management.thrift.scmdelivery.dto.SaleReturnOrderKey;
import com.sankuai.meituan.reco.store.management.thrift.scmdelivery.dto.TSaleReturnOrderInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/12/14
 */
@Slf4j
@Service(value = "storeSaleReturnService")
public class SaleReturnService {

	private static final String SALE_RETURN_AUTH_CODE = "SALE_RETURN_TASK";

	@Resource
	private AuthThriftWrapperService authThriftWrapper;
	@Resource
	private SaleReturnOrderRemoteService saleReturnOrderWrapper;

	@CatTransaction
	@MethodLog(logRequest = true, logResponse = true)
	public void tryAppendSaleReturnOrdersInfo(CommonResponse<OrderDetailResponse> orderDetailResponse) {
		try {
			if (orderDetailResponse.getCode() != ResultCode.SUCCESS.getCode()) {
				return;
			}

			if (!authThriftWrapper.isCodeHasAuth(SALE_RETURN_AUTH_CODE)) {
				return;
			}

			Optional.ofNullable(orderDetailResponse.getData())
					.map(OrderDetailResponse::getOrderDetail)
					.filter(it -> CollectionUtils.isNotEmpty(it.getAfterSaleRecordList()))
					.ifPresent(it -> {
						Map<SaleReturnOrderKey, TSaleReturnOrderInfo> saleReturnOrders = saleReturnOrderWrapper.querySaleReturnOrdersBySaleOrder(
								ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId(),
								it.getStoreId(), OCMSUtils.convertChannel2OrderBizType(it.getChannelId()), it.getChannelOrderId()
						);

						if (MapUtils.isEmpty(saleReturnOrders)) {
							return;
						}

						for (AfterSaleRecordVO each : it.getAfterSaleRecordList()) {
							getSaleReturnOrderKey(it, each)
									.map(saleReturnOrders::get)
									.ifPresent(each::fillSaleReturnOrderInfo);
						}
					});
		} catch (Exception e) {
			log.error("附加订单销退单信息时出现异常，默认不附加", e);
		}
	}

	private Optional<SaleReturnOrderKey> getSaleReturnOrderKey(OrderDetailVO orderDetail, AfterSaleRecordVO each) {
		AfterSalePatternEnum afsPattern = AfterSalePatternEnum.enumOf(each.getAfsPattern());
		if (afsPattern == null) {
			return Optional.empty();
		}
		switch (afsPattern) {
			case PART:
				return Optional.of(new SaleReturnOrderKey(
						OCMSUtils.convertChannel2OrderBizType(orderDetail.getChannelId()),
						orderDetail.getChannelOrderId(), SaleReturnTypeEnum.PARTIAL_REFUND_RETURN, each.getChannelAfterSaleId())
				);

			case ALL:
				return Optional.of(new SaleReturnOrderKey(
						OCMSUtils.convertChannel2OrderBizType(orderDetail.getChannelId()),
						orderDetail.getChannelOrderId(), SaleReturnTypeEnum.FULL_REFUND_RETURN, null)
				);
			default:
				return Optional.empty();
		}
	}
}
