package com.sankuai.shangou.qnh.orderapi.domain.response.app;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.OrderItemWeightRefundCheckVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2020-03-30 11:34
 * @Description:
 */
@TypeDoc(
        description = "克重退款页面检查退款请求响应"
)
@ApiModel("克重退款页面检查退款请求响应")
@Data
public class WeightRefundCheckResponse {
    @FieldDoc(
            description = "退款商品列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "退款商品列表", required = true)
    private List<OrderItemWeightRefundCheckVO> weightRefundCheckVOList;

    @FieldDoc(
            description = "应该获取集合店商家手机号,true:是", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "应该获取集合店商家手机号")
    private Boolean shouldGetGatherPhone;
}
