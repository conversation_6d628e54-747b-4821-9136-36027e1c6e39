package com.sankuai.shangou.qnh.orderapi.domain.response.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TypeDoc(
        description = "退单数量"
)
@Data
@ApiModel("退单数量")
public class RefundOrderCountResponse {


    @FieldDoc(
            description = "待处理数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "待处理数量", required = true)
    private Long all = 0L;

    @FieldDoc(
            description = "待处理数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "待处理数量", required = true)
    private Long waitDeal = 0L;

    @FieldDoc(
            description = "已取消", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "已取消", required = true)
    private Long cancel = 0L;


    @FieldDoc(
            description = "已完成", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "已完成", required = true)
    private Long complete = 0L;


    @FieldDoc(
            description = "已拒绝", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "已拒绝", required = true)
    private Long reject = 0L;


}
