package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@TypeDoc(
        description = "查询提价历史记录默认操作请求参数"
)
@Data
@ApiModel("查询提价历史记录默认操作请求参数")
public class QueryQuoteRecordDefulatRequest {

    @FieldDoc(
            description = "店铺ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "店铺ID", required = true)
    @NotNull
    private String storeId;

    @FieldDoc(
            description = "提价记录所处的审核状态", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "提价记录所处的审核状态", required = true)
    @NotNull
    private Integer reviewStatus;

    @FieldDoc(
            description = "查询第几页", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "查询第几页", required = true)
    private Integer pageNum;

    @FieldDoc(
            description = "每页查询数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "每页查询数量", required = true)
    private Integer pageSize;
}
