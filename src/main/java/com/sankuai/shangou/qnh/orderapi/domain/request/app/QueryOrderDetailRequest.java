package com.sankuai.shangou.qnh.orderapi.domain.request.app;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2019/11/18
 * desc: 确认接单
 */
@TypeDoc(
        description = "确认接单请求"
)
@ApiModel("确认接单请求")
@Data
public class QueryOrderDetailRequest {

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道ID")
    @NotNull
    private Integer channelId;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道订单号")
    @NotNull
    public String channelOrderId;

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID")
    //@NotNull
    private Long storeId;
}
