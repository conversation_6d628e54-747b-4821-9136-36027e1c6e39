package com.sankuai.shangou.qnh.orderapi.facade.auth;

import com.dianping.lion.client.util.JsonUtils;
import com.sankuai.meituan.shangou.empower.auth.sdk.bean.SessionInfo;
import com.sankuai.meituan.shangou.empower.auth.sdk.context.SessionContext;
import com.sankuai.shangou.qnh.orderapi.constant.pc.Constants;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.PermissionGroupVO;
import com.sankuai.shangou.qnh.orderapi.exception.store.CommonRuntimeException;
import com.sankuai.shangou.qnh.orderapi.remote.AuthRemoteService;
import com.sankuai.shangou.qnh.orderapi.remote.PoiRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AuthFacade {

    @Resource
    private AuthRemoteService authRemoteService;

    @Resource
    private PoiRemoteService poiRemoteService;

    public List<Long> getCurrentUserFullStoreIdList() {
        SessionInfo sessionInfo = SessionContext.getCurrentSession();
        try {
            List<PermissionGroupVO> permissionGroupVOS = authRemoteService.batchQueryAllPoiPermissionGroup(sessionInfo.getTenantId(), sessionInfo.getAccountId(),
                    Arrays.asList(Constants.AuthType.POI_TYPE, Constants.AuthType.SHAREABLE_WAREHOUSE));
            Map<Integer, List<Long>> type2Code = permissionGroupVOS.stream().collect(Collectors.groupingBy(PermissionGroupVO::getType, Collectors.mapping(item -> Long.valueOf(item.getCode()), Collectors.toList())));
            List<Long> poiIds = type2Code.getOrDefault(Constants.AuthType.POI_TYPE, Lists.newArrayList());
            List<Long> warehouseIds = type2Code.getOrDefault(Constants.AuthType.SHAREABLE_WAREHOUSE, Lists.newArrayList());
            if (CollectionUtils.isNotEmpty(warehouseIds)) {
                Map<Long, List<Long>> warehouseId2PoiIdMap = poiRemoteService.queryShareableWarehouseRelatedStoreId(sessionInfo.getTenantId(), warehouseIds);
                if (MapUtils.isNotEmpty(warehouseId2PoiIdMap)) {
                    poiIds.addAll(warehouseId2PoiIdMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList()));
                    poiIds = poiIds.stream().distinct().collect(Collectors.toList());
                }
            }
            return poiIds;
        } catch (Exception e) {
            log.error("getStoreIdList error", e);
            throw new CommonRuntimeException(e);
        }
    }

    public List<Long> getCurrentuserFullStoreAndWarehouseIdList() {
        SessionInfo sessionInfo = SessionContext.getCurrentSession();
        try {
            List<PermissionGroupVO> permissionGroupVOS = authRemoteService.batchQueryAllPoiPermissionGroup(sessionInfo.getTenantId(), sessionInfo.getAccountId(),
                    Arrays.asList(Constants.AuthType.POI_TYPE, Constants.AuthType.SHAREABLE_WAREHOUSE));
            Map<Integer, List<Long>> type2Code = permissionGroupVOS.stream().collect(Collectors.groupingBy(PermissionGroupVO::getType, Collectors.mapping(item -> Long.valueOf(item.getCode()), Collectors.toList())));
            List<Long> poiIds = type2Code.getOrDefault(Constants.AuthType.POI_TYPE, Lists.newArrayList());
            List<Long> warehouseIds = type2Code.getOrDefault(Constants.AuthType.SHAREABLE_WAREHOUSE, Lists.newArrayList());
            poiIds.addAll(warehouseIds);
            return poiIds;
        } catch (Exception e) {
            log.error("getStoreAndWarehouseIdList error", e);
            throw new CommonRuntimeException(e);
        }
    }
}
