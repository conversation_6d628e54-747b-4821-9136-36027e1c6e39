package com.sankuai.shangou.qnh.orderapi.configuration;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.google.common.collect.Maps;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurityInterceptor;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.qnh.orderapi.constant.pc.CommonConstant;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.BaseResult;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.Result;
import com.sankuai.shangou.qnh.orderapi.exception.pc.ParamInvalidException;
import com.sankuai.shangou.qnh.orderapi.exception.store.CustomHandlerExceptionResolver;
import com.sankuai.shangou.qnh.orderapi.interceptor.app.StandardSpecInterceptor;
import com.sankuai.shangou.qnh.orderapi.interceptor.pc.EleAuthInterceptor;
import com.sankuai.shangou.qnh.orderapi.interceptor.pc.LoginInterceptor;
import com.sankuai.shangou.qnh.orderapi.interceptor.store.ApiMethodParamInterceptor;
import com.sankuai.shangou.qnh.orderapi.interceptor.store.ApiMethodStatisticsInterceptor;
import com.sankuai.shangou.qnh.orderapi.interceptor.store.AuthInterceptor;
import com.sankuai.shangou.qnh.orderapi.interceptor.store.H5LoginInterceptor;
import com.sankuai.shangou.qnh.orderapi.interceptor.store.H5TokenInterceptor;
import com.sankuai.shangou.qnh.orderapi.interceptor.store.OrderGrayInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.jetty.io.EofException;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.HandlerExceptionResolver;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.NoHandlerFoundException;
import org.springframework.web.servlet.config.annotation.DefaultServletHandlerConfigurer;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 通过此类来配置WebMvc的配置
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
//@ComponentScan("com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity")
@Profile("!local-test")
public class MvcConfigurer implements WebMvcConfigurer {

    @Resource
    private LoginInterceptor loginInterceptor;

    /**
     * 数据鉴权拦截器，需要保证在登录拦截器,无人仓选仓拦截和多租户拦截之后即可
     */
    @Resource
    private DataSecurityInterceptor dataSecurityInterceptor;

    @Resource
    private EleAuthInterceptor eleAuthInterceptor;

    @Resource
    private H5LoginInterceptor h5LoginInterceptor;

    @Resource
    private H5TokenInterceptor h5TokenInterceptor;

    @Resource
    private ApiMethodParamInterceptor apiMethodParamInterceptor;

    @Resource(name = "appApiMethodParamInterceptor")
    private com.sankuai.shangou.qnh.orderapi.interceptor.app.ApiMethodParamInterceptor appApiMethodParamInterceptor;

    @Resource
    private AuthInterceptor authInterceptor;

    @Resource
    private OrderGrayInterceptor orderGrayInterceptor;

    @Resource
    private ApiMethodStatisticsInterceptor apiMethodStatisticsInterceptor;

    @Resource
    private StandardSpecInterceptor standardSpecInterceptor;

    @Resource(name = "appAuthInterceptor")
    private com.sankuai.shangou.qnh.orderapi.interceptor.app.AuthInterceptor appAuthInterceptor;

    @Resource(name = "appOrderGrayInterceptor")
    private com.sankuai.shangou.qnh.orderapi.interceptor.app.OrderGrayInterceptor appOrderGrayInterceptor;

    @Override
    public void configureDefaultServletHandling(DefaultServletHandlerConfigurer configurer) {
        configurer.enable();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 多个拦截器组成一个拦截器链
        // addPathPatterns 用于添加拦截规则
        // excludePathPatterns 用户排除拦截

        // 登录拦截器
        registry.addInterceptor(loginInterceptor)
                .addPathPatterns("/api/v1/orderfuse/**")
                .addPathPatterns("/api/v1/channelOrder/**")
                .addPathPatterns("/api/v1/channelComment/**")
                .excludePathPatterns("/api/v1/isLogined")
                .excludePathPatterns("/api/v1/accountName/check")
                .excludePathPatterns("/api/v1/account/sendVerificationCodeWithExplicitMobile")
                .excludePathPatterns("/api/v1/account/verifyAndRegister")
                .excludePathPatterns("/api/v1/account/password/sendForgetVerificationCode")
                .excludePathPatterns("/api/v1/account/password/verifyAndModify")
                .excludePathPatterns("/api/v1/login")
                .excludePathPatterns("/api/v1/eplogin/callback")
                .excludePathPatterns("/monitor/alive")
                .excludePathPatterns("/api/v1/accountLogin")
                .excludePathPatterns("/api/v1/loginOut")
                .excludePathPatterns("/api/v1/login/authorize")
                .excludePathPatterns("/static/**")
                .excludePathPatterns("/webjars/**")
                .excludePathPatterns("/swagger-resources/**")
                .excludePathPatterns("/v2/api-docs/**")
                .excludePathPatterns("/META-INF/**")
                .excludePathPatterns("/api/v1/sso/loginRedirect")
                .excludePathPatterns("/api/v1/sso/uocLoginRedirect")
                .excludePathPatterns("/api/v1/sso/logout")
                .excludePathPatterns("/configuration/**")
                .excludePathPatterns("/hystrix/**")
                .excludePathPatterns("/api/v1/storeempower/other-receipt/callback")
                .excludePathPatterns("swagger-ui.html");

        registry.addInterceptor(dataSecurityInterceptor)
                .addPathPatterns("/**");

        //元素权限拦截器
        registry.addInterceptor(eleAuthInterceptor)
                .addPathPatterns("/api/v2/department/create")
                .addPathPatterns("/api/v2/department/modify")
                .addPathPatterns("/api/v2/department/delete")
                .addPathPatterns("/api/v2/department/position/create")
                .addPathPatterns("/api/v2/department/position/modify")
                .addPathPatterns("/api/v2/department/position/delete")
                .addPathPatterns("/api/v1/department/create")
                .addPathPatterns("/api/v1/department/modify")
                .addPathPatterns("/api/v1/department/delete")
                .addPathPatterns("/api/v1/account/list")
                .addPathPatterns("/api/v1/account/create")
                .addPathPatterns("/api/v1/account/createnew")
                .addPathPatterns("/api/v1/account/modify")
                .addPathPatterns("/api/v1/account/modifynew")
                .addPathPatterns("/api/v1/empName/queryByMis")
                .addPathPatterns("/api/v1/account/bindauth")
                .addPathPatterns("/api/v1/account/status")
                .addPathPatterns("/api/v1/account/delete")
                .addPathPatterns("/api/v1/role/list")
                .addPathPatterns("/api/v1/role/filter/list")
                .addPathPatterns("/api/v1/role/create")
                .addPathPatterns("/api/v1/role/modify")
                .addPathPatterns("/api/v1/role/status")
                .addPathPatterns("/api/v1/role/delete")
                .addPathPatterns("/api/v1/common/sku/generalBase/query")
                .addPathPatterns("/api/v1/common/sku/generalBase/add")
                .addPathPatterns("/v1/common/sku/generalBase/savePoiSku")
                .addPathPatterns("/api/v1/item/putOnChannelSale")
                .addPathPatterns("/api/v1/common/sku/generalBase/batchImportPic")
                .addPathPatterns("/api/v1/common/sku/generalBase/batchImport")
                .addPathPatterns("/api/v1/common/sku/generalBase/downloadInfoList")
                .addPathPatterns("/api/v1/booth/item")
                .addPathPatterns("/api/v1/booth/item/add")
                .addPathPatterns("/api/v1/booth/item/edit")
                .addPathPatterns("/api/v1/quoteReview/query")
                .addPathPatterns("/api/v1/quoteReview/review")
                .addPathPatterns("/api/v1/review/saveExemptReviewConfig")
                .addPathPatterns("/api/v1/item/batchUpdateStatus")
                .addPathPatterns("/api/v1/item/batchSaveItemFrontCategory")
                .addPathPatterns("/api/v1/price/adjustOnlinePriceByChannel")
                .addPathPatterns("/api/v1/item/updateStoreSkuInfo")
                .addPathPatterns("/api/v1/tenant/saveChannelConfig")
                .addPathPatterns("/api/v1/item/initialFrontCategorys")
                .addPathPatterns("/api/v1/channelOrder/queryList")
                .addPathPatterns("/api/v1/channelOrder/partRefund")
                .addPathPatterns("/api/v1/channelOrder/refund")
                .addPathPatterns("/api/v1/channelOrder/confirmOrderList")
                .addPathPatterns("/api/v1/channelOrder/multiShopConfirmOrderList")
                .addPathPatterns("/api/v1/channelOrder/confirmOrder")
                .addPathPatterns("/api/v1/channelOrder/queryAuditOrderList")
                .addPathPatterns("/api/v1/channelOrder/multiShopQueryAuditOrderList")
                .addPathPatterns("/api/v1/channelOrder/approve")
                .addPathPatterns("/api/v1/promotion/instance/add")
                .addPathPatterns("/api/v1/promotion/instance/status")
                .addPathPatterns("/api/v1/promotion/instance/detail")
                .addPathPatterns("/api/v1/channelPromotion/activities")
                .addPathPatterns("/api/v1/channelPromotion/activityInfo")
                .addPathPatterns("/api/v1/channelPromotion/saveActivity")
                .addPathPatterns("/api/v1/stockTask/list")
                .addPathPatterns("/api/v1/stockTask/downloadTasks")
                .addPathPatterns("/api/v1/channelStock/query")
                .addPathPatterns("/api/v1/stockDeliveryReceipt/queryDistributionOrderList")
                .addPathPatterns("/api/v1/storeempower/query/receipt/nodeliveryorder")
                .addPathPatterns("/api/v1/stockDeliveryReceipt/queryReceiptList")
                .addPathPatterns("/api/v1/storeempower/operate/stockadjustment")
                .addPathPatterns("/api/v1/storeArea/insertAndUpdateStoreAreaGroup")
                .addPathPatterns("/api/v1/storeArea/addStoreArea")
                .addPathPatterns("/api/v1/member/card/search")
                .addPathPatterns("/api/v1/member/card/status/update")
                .addPathPatterns("/api/v1/member/card/passwd/reset")
                .addPathPatterns("/api/v1/asset/assetFlow/list")
                .addPathPatterns("/api/v1/asset/assetBalance/list")
                .addPathPatterns("/api/v1/couponBatch/list")
                .addPathPatterns("/api/v1/couponBatch/addOrUpdate")
                .addPathPatterns("/api/v1/couponDeliverActivity/addOrUpdate")
                .addPathPatterns("/api/v1/score/rechangeActivity/add")
                .addPathPatterns("/api/v1/score/rechangeActivity/verify")
                .addPathPatterns("/api/v1/score/rechangeActivity/disable")
                .addPathPatterns("/api/v1/score/exchangeActivity/add")
                .addPathPatterns("/api/v1/score/exchangeActivity/verify")
                .addPathPatterns("/api/v1/score/exchangeActivity/disable")
                .addPathPatterns("/api/v1/score/exchangeFlow/list")
                .addPathPatterns("/api/v1/score/scoreFlow/list")
                .addPathPatterns("/api/v1/score/scoreBalance/list")
                .addPathPatterns("/api/v1/sms/sendTask/list")
                .addPathPatterns("/api/v1/sms/sendTask/addOrUpdate")
                .addPathPatterns("/api/v1/sms/send/testSend")
                .addPathPatterns("/api/v1/costAccounting/queryReport")
                .addPathPatterns("/api/v1/channelComment/queryCommentStat")
                .addPathPatterns("/api/v1/channelComment/queryCommentList")
                .addPathPatterns("/api/v1/bonus/list")
                .addPathPatterns("/api/v1/bonus/adjustBudget")
                .addPathPatterns("/api/v1/bonus/queryBudget")
                .addPathPatterns("/api/v1/booth/settlement/settle/offlineBatchSettle")
                .addPathPatterns("/api/v1/storeDeliveryConfig/query")
                .addPathPatterns("/api/v1/storeDeliveryConfig/modifyDeliveryChannelConfig")
                .addPathPatterns("/api/v1/storeDeliveryConfig/modifyLaunchConfig")
                .addPathPatterns("/api/v1/tenant/allmodules")
                .addPathPatterns("/api/v1/tenant/detail")
                .addPathPatterns("/api/v1/tenant/open/bizmodule")
                .addPathPatterns("/api/v1/allot/diff/handle")
                .addPathPatterns("/api/v1/allot/diff/approve")
                .addPathPatterns("/api/v1/delivery/manage/deliverymonitoring")
                .addPathPatterns("/api/v1/medicine/channelStock/query")
                .addPathPatterns("/api/v1/flagshipstore/stock")
                .addPathPatterns("/api/v1/uwms/yyCenter/monitor/**")
                //门店分组相关
                .addPathPatterns("/api/v1/common/poi/group/**")
                .addPathPatterns("/api/v1/channelComment/manualCheckUpdateComment")
                .addPathPatterns("/api/v1/channelComment/confirmDeleteComment")
                .addPathPatterns("/api/v1/channelComment/manualCheckDeleteComment");
                //.addPathPatterns("/api/v1/orderfuse/**")

        /**
         * 拦截通用参数
         */
        registry.addInterceptor(apiMethodParamInterceptor)
                .addPathPatterns("/storemanagement/ocms/order/**")
                .addPathPatterns("/storemanagement/ocms/channelComment/**")
                .excludePathPatterns("/monitor/alive")
                .excludePathPatterns("/h5/**")
                .excludePathPatterns("/static/**")
                .excludePathPatterns("/webjars/**")
                .excludePathPatterns("/swagger-resources/**")
                .excludePathPatterns("/v2/api-docs/**")
                .excludePathPatterns("/META-INF/**")
                .excludePathPatterns("/configuration/**")
                .excludePathPatterns("swagger-ui.html");

        registry.addInterceptor(authInterceptor)
                .addPathPatterns("/storemanagement/ocms/order/**")
                .addPathPatterns("/storemanagement/ocms/channelComment/**")
                .excludePathPatterns("/monitor/alive")
                .excludePathPatterns("/h5/**")
                .excludePathPatterns("/static/**")
                .excludePathPatterns("/webjars/**")
                .excludePathPatterns("/swagger-resources/**")
                .excludePathPatterns("/v2/api-docs/**")
                .excludePathPatterns("/META-INF/**")
                .excludePathPatterns("/configuration/**")
                .excludePathPatterns("swagger-ui.html");

        registry.addInterceptor(apiMethodStatisticsInterceptor)
                .addPathPatterns("/storemanagement/ocms/order/**")
                .addPathPatterns("/storemanagement/ocms/channelComment/**")
                .excludePathPatterns("/monitor/alive")
                .excludePathPatterns("/static/**")
                .excludePathPatterns("/webjars/**")
                .excludePathPatterns("/swagger-resources/**")
                .excludePathPatterns("/v2/api-docs/**")
                .excludePathPatterns("/META-INF/**")
                .excludePathPatterns("/configuration/**")
                .excludePathPatterns("/h5/**")
                .excludePathPatterns("swagger-ui.html");
        registry.addInterceptor(h5TokenInterceptor)
                .addPathPatterns("/h5/**");
        registry.addInterceptor(h5LoginInterceptor)
                .addPathPatterns("/h5/**")
                .excludePathPatterns("/h5/storemanagement/login/login")
                .excludePathPatterns("/h5/storemanagement/login/verifycode/apply")
                .excludePathPatterns("/h5/storemanagement/mr/query/storename");

        registry.addInterceptor(orderGrayInterceptor)
                .addPathPatterns("/storemanagement/ocms/order/**")
                .addPathPatterns("/storemanagement/ocms/channelComment/**")
                .excludePathPatterns("/monitor/alive")
                .excludePathPatterns("/h5/**")
                .excludePathPatterns("/static/**")
                .excludePathPatterns("/webjars/**")
                .excludePathPatterns("/swagger-resources/**")
                .excludePathPatterns("/v2/api-docs/**")
                .excludePathPatterns("/META-INF/**")
                .excludePathPatterns("/configuration/**")
                .excludePathPatterns("swagger-ui.html");

        registry.addInterceptor(appApiMethodParamInterceptor)
                .addPathPatterns("/pieapi/order/**")
                .addPathPatterns("/pieapi/miniapp/order/**")
                .addPathPatterns("/pieapi/pda/order/**")
                .excludePathPatterns("/monitor/alive")
                .excludePathPatterns("/static/**")
                .excludePathPatterns("/webjars/**")
                .excludePathPatterns("/swagger-resources/**")
                .excludePathPatterns("/v2/api-docs/**")
                .excludePathPatterns("/META-INF/**")
                .excludePathPatterns("/configuration/**")
                .excludePathPatterns("swagger-ui.html");


        registry.addInterceptor(appAuthInterceptor)
                .addPathPatterns("/pieapi/order/**")
                .addPathPatterns("/pieapi/miniapp/order/**")
                .addPathPatterns("/pieapi/pda/order/**")
                .excludePathPatterns("/monitor/alive")
                .excludePathPatterns("/static/**")
                .excludePathPatterns("/webjars/**")
                .excludePathPatterns("/swagger-resources/**")
                .excludePathPatterns("/v2/api-docs/**")
                .excludePathPatterns("/META-INF/**")
                .excludePathPatterns("/configuration/**")
                .excludePathPatterns("swagger-ui.html");

        registry.addInterceptor(apiMethodStatisticsInterceptor)
                .addPathPatterns("/pieapi/order/**")
                .addPathPatterns("/pieapi/pda/order/**")
                .addPathPatterns("/pieapi/miniapp/order/**")
                .excludePathPatterns("/monitor/alive")
                .excludePathPatterns("/static/**")
                .excludePathPatterns("/webjars/**")
                .excludePathPatterns("/swagger-resources/**")
                .excludePathPatterns("/v2/api-docs/**")
                .excludePathPatterns("/META-INF/**")
                .excludePathPatterns("/configuration/**")
                .excludePathPatterns("swagger-ui.html");

        //该拦截器看着是灰度规格期间使用，目前不在需要
        registry.addInterceptor(standardSpecInterceptor)
//                .addPathPatterns("/pieapi/order/**")
//                .addPathPatterns("/pieapi/pda/order/**")
//                .addPathPatterns("/pieapi/miniapp/order/**")
                .excludePathPatterns("/monitor/alive")
                .excludePathPatterns("/static/**")
                .excludePathPatterns("/webjars/**")
                .excludePathPatterns("/swagger-resources/**")
                .excludePathPatterns("/v2/api-docs/**")
                .excludePathPatterns("/META-INF/**")
                .excludePathPatterns("/configuration/**")
                .excludePathPatterns("swagger-ui.html");

        registry.addInterceptor(appOrderGrayInterceptor)
                .addPathPatterns("/pieapi/order/**")
                .addPathPatterns("/pieapi/pda/order/**")
                .addPathPatterns("/pieapi/miniapp/order/**")
                .excludePathPatterns("/monitor/alive")
                .excludePathPatterns("/static/**")
                .excludePathPatterns("/webjars/**")
                .excludePathPatterns("/swagger-resources/**")
                .excludePathPatterns("/v2/api-docs/**")
                .excludePathPatterns("/META-INF/**")
                .excludePathPatterns("/configuration/**")
                .excludePathPatterns("swagger-ui.html");
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/**").addResourceLocations("classpath:/static/");
        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");
    }

    @Override
    public void configureHandlerExceptionResolvers(List<HandlerExceptionResolver> exceptionResolvers) {
        //pc
        exceptionResolvers.add((request, response, handler, e) -> {
            String uri = request.getRequestURI();
            if (!uri.startsWith("/api/v1")) {
                return null;
            }

            Result result = new Result();
            if (e instanceof BizException) {
                BizException bizE = (BizException) e;
                result.setCode(bizE.getErrorCode()).setMsg(e.getMessage());
            } else if (e instanceof ParamInvalidException) {
                result.code(BaseResult.PARAM_INVALID).setMsg(e.getMessage());
            } else if (e instanceof NoHandlerFoundException) {
                result.code(BaseResult.NOT_FOUND).setMsg("接口 [" + request.getRequestURI() + "] 不存在");
            } else if (e instanceof ServletException) {
                result.code(BaseResult.FAIL).setMsg(e.getMessage());
            } else if (e instanceof MethodArgumentTypeMismatchException || e instanceof BindException || e instanceof HttpMessageNotReadableException) {
                result.code(BaseResult.FAIL).setMsg("参数转换异常");
                log.warn("参数转换异常", e);
            } else if (e instanceof EofException) {
                log.warn("write stream error");
            } else if (e instanceof MethodArgumentNotValidException) {
                log.warn("param error");
                String errMsg = getParamErrMsg((MethodArgumentNotValidException) e);
                result.code(BaseResult.PARAM_INVALID).setMsg(errMsg);
            } else if (e instanceof IllegalArgumentException) {
                log.warn("param error");
                String errMsg = e.getMessage();
                result.code(BaseResult.PARAM_INVALID).setMsg(errMsg);
            } else {
                result.code(BaseResult.INTERNAL_SERVER_ERROR).setMsg("系统异常，请重试");
                String message;
                if (handler instanceof HandlerMethod) {
                    HandlerMethod handlerMethod = (HandlerMethod) handler;
                    message = String.format("接口 [%s] 出现异常，方法：%s.%s，异常摘要：%s",
                            request.getRequestURI(),
                            handlerMethod.getBean().getClass().getName(),
                            handlerMethod.getMethod().getName(),
                            e.getMessage());
                } else {
                    message = e.getMessage();
                }
                log.error(message, e);
            }
            responseResult(response, result);
            return new ModelAndView();
        });

        //storeapi&&pieapi
        exceptionResolvers.add(customHandlerExceptionResolver());
    }

    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        converters.add(customJackson2HttpMessageConverter());
    }

    private void responseResult(HttpServletResponse response, Result result) {
        response.setCharacterEncoding(CommonConstant.CHARSET_UTF8);
        response.setHeader("Content-type", "application/json;charset=UTF-8");
        response.setStatus(200);
        try {
            response.getWriter().write(JSON.toJSONString(result));
        } catch (Exception e) {
            log.warn("WebMvcConfigurer.responseResult, response:{}, result:{}, exceptions={}", response, result, e);
        }
    }

    private String getParamErrMsg(MethodArgumentNotValidException ex) {
        List<FieldError> fieldErrors = ex.getBindingResult().getFieldErrors();
        List<String> msgList = fieldErrors.stream().map(DefaultMessageSourceResolvable::getDefaultMessage).collect(Collectors.toList());
        return StringUtils.join(msgList, ";");
    }

    @Bean
    public HandlerExceptionResolver customHandlerExceptionResolver() {
        return new CustomHandlerExceptionResolver();
    }

    @Bean
    public MappingJackson2HttpMessageConverter customJackson2HttpMessageConverter() {
        MappingJackson2HttpMessageConverter jsonConverter = new MappingJackson2HttpMessageConverter();

        ObjectMapper objectMapper = new ObjectMapper();
        /**
         * 序列换成json时,将所有的long变成string
         * 因为js中得数字类型不能包含所有的java long值
         */
        SimpleModule simpleModule = new SimpleModule();
        simpleModule.addSerializer(Long.class, ToStringSerializer.instance);
        simpleModule.addSerializer(Long.TYPE, ToStringSerializer.instance);
        objectMapper.registerModule(simpleModule);

        //设置日期格式
        SimpleDateFormat smt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        objectMapper.setDateFormat(smt);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        jsonConverter.setObjectMapper(objectMapper);

        //设置中文编码格式
        List<MediaType> list = new ArrayList<MediaType>();
        list.add(MediaType.APPLICATION_JSON_UTF8);
        jsonConverter.setSupportedMediaTypes(list);

        return jsonConverter;
    }
}
