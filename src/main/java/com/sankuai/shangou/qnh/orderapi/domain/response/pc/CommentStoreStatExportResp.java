package com.sankuai.shangou.qnh.orderapi.domain.response.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@TypeDoc(
        description = "评价门店统计导出响应",
        authors = "hejunliang"
)
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Getter
@Setter
public class CommentStoreStatExportResp {

    @FieldDoc(
            description = "导出文件url"
    )
    private String downloadUrl;

}
