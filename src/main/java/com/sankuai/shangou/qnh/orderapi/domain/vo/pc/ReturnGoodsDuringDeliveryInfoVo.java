package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@TypeDoc(
        description = "返货信息"
)
@ApiModel("返货信息")
@Data
public class ReturnGoodsDuringDeliveryInfoVo {

    @FieldDoc(
            description = "返货配送单状态", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "返货配送单状态", required = false)
    private Integer status;

    @FieldDoc(
            description = "返货预计送达时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "返货预计送达时间", required = false)
    private Long deliveryTime;

    @FieldDoc(
            description = "商家验货结果", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "商家验货结果", required = false)
    private Integer poiCheckResult;

    @FieldDoc(
            description = "驳回原因", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "驳回原因", required = false)
    private String reason;

    @FieldDoc(
            description = "返货完成时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "返货完成时间", required = false)
    private Long completeTime;

    @FieldDoc(
            description = "驳回原因列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "驳回原因列表", required = false)
    private List<ReturnGoodsRejectAndCode> rejectReasonList;

    @FieldDoc(
            description = "状态变更时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "状态变更时间", required = false)
    private Long uTime;
}
