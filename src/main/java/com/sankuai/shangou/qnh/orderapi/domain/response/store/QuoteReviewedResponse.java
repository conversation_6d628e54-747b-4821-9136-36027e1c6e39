package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "提价记录查询结果响应"
)
@Data
@ApiModel("提价记录查询结果响应")
public class QuoteReviewedResponse {

    @FieldDoc(
            description = "审批结果提交失败信息集", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "审批结果提交失败信息集", required = true)
    private String failedData;
}
