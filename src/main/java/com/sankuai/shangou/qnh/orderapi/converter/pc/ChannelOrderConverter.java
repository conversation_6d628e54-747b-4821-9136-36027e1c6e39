package com.sankuai.shangou.qnh.orderapi.converter.pc;

import com.meituan.shangou.saas.order.management.client.dto.request.online.OcmsUnDoneOrderListReq;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OcmsOrderSearchResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OrderInfoVo;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.ProductInfoVo;
import com.meituan.shangou.saas.order.management.client.export.dto.request.ExportChannelOrderDetailRequest;
import com.meituan.shangou.saas.order.management.client.export.dto.response.ExportResultResponse;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.meituan.shangou.saas.order.platform.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.*;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.OrderRefundType;
import com.sankuai.meituan.shangou.saas.common.datetime.DateUtil;
import com.sankuai.shangou.qnh.orderapi.constant.pc.Constants;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.ConfirmOrderQueryRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.MultiShopConfirmOrderQueryRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.PartRefundRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.QueryDetailRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.QueryRefundReasonRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.RefundRequest;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.PageResultV2;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.Result;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.UiOption;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.AsyncExportOrdersTaskVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.ConfirmOrderVO;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.Arrays;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2019/3/30 14:56
 * @Description:
 */
@Mapper(componentModel = "spring", uses = {DateUtil.class, ContextHolder.class, ConverterUtils.class, DateFormatUtils.class, Constants.class, OrderRefundType.class, ChannelOrderConvertUtils.class}
                    , imports = {DateUtil.class, ContextHolder.class, ConverterUtils.class, DateFormatUtils.class, Constants.class
        ,ProductInfoDTO.class,DeliveryStatusEnum.class, OrderRefundType.class, ChannelOrderConvertUtils.class, Arrays.class,
        ProductInfoVo.class})
public interface ChannelOrderConverter {


    /**
     * 确认订单列表请求参数转换
     * @param param
     * @return
     */
    @Mapping(target = "tenantId", expression = "java(ContextHolder.currentUserTenantId())")
    @Mapping(target = "shopName", source = "poiName")
    @Mapping(target = "shopIdList",expression = "java(ConverterUtils.nonNullConvert(param.getPoiId(), p -> Arrays.asList(Long.valueOf(p))))")
    @Mapping(target = "channelIdList", expression = "java(ConverterUtils.convertList(param.getChannelIds(), Integer::valueOf))")
    @Mapping(target = "isPickedUpOrderFlag", expression = "java(ConverterUtils.nonNullConvert(param.getPickStatus(), Integer::valueOf,0))")
    @Mapping(target = "isTakenOrderFlag", expression = "java(ConverterUtils.nonNullConvert(param.getConfirmStatus(), Integer::valueOf,0))")
    @Mapping(target = "deliveryOrderType", expression = "java(ConverterUtils.nonNullConvert(param.getOrderType(), Integer::valueOf,0))")
    @Mapping(target = "page", expression = "java(returnSelf(param.getPage()))")
    @Mapping(target = "pageSize", expression = "java(returnSelf(param.getPageSize()))")
    @Mapping(target = "orderSerialNumber", source = "orderSerialNumber")
    @Mapping(target = "warehouseIdList", source = "warehouseIdList")
    OcmsUnDoneOrderListReq ocmsUnDoneQueryRequestConvert(ConfirmOrderQueryRequest param);

    /**
     * 确认订单列表请求参数转换
     * @param param
     * @return
     */
    @Mapping(target = "tenantId", expression = "java(ContextHolder.currentUserTenantId())")
    @Mapping(target = "shopName", source = "poiName")
    @Mapping(target = "shopIdList",expression = "java(ConverterUtils.convertList(param.getPoiIdList(), Long::valueOf))")
    @Mapping(target = "channelIdList", expression = "java(ConverterUtils.convertList(param.getChannelIds(), Integer::valueOf))")
    @Mapping(target = "isPickedUpOrderFlag", expression = "java(ConverterUtils.nonNullConvert(param.getPickStatus(), Integer::valueOf,0))")
    @Mapping(target = "isTakenOrderFlag", expression = "java(ConverterUtils.nonNullConvert(param.getConfirmStatus(), Integer::valueOf,0))")
    @Mapping(target = "deliveryOrderType", expression = "java(ConverterUtils.nonNullConvert(param.getOrderType(), Integer::valueOf,0))")
    @Mapping(target = "page", expression = "java(returnSelf(param.getPage()))")
    @Mapping(target = "pageSize", expression = "java(returnSelf(param.getPageSize()))")
    @Mapping(target = "orderSerialNumber", source = "orderSerialNumber")
    @Mapping(target = "warehouseIdList", source = "warehouseIdList")
    @Mapping(target = "clientType", source = "clientType")
    @Mapping(target = "orderMarkList", source = "orderMarks")
    OcmsUnDoneOrderListReq ocmsUnDoneQueryRequestConvert(MultiShopConfirmOrderQueryRequest param);

    /**
     * 确认订单列表请求参数转换
     * @param param
     * @return
     */
    @Mapping(target = "tenantId", expression = "java(ContextHolder.currentUserTenantId())")
    @Mapping(target = "shopName", source = "poiName")
    @Mapping(target = "shopIdList",expression = "java(ConverterUtils.nonNullConvert(param.getPoiId(), p -> Arrays.asList(Long.valueOf(p))))")
    @Mapping(target = "channelIdList", expression = "java(ConverterUtils.convertList(param.getChannelIds(), Integer::valueOf))")
    @Mapping(target = "isPickedUpOrderFlag", expression = "java(ConverterUtils.nonNullConvert(param.getPickStatus(), Integer::valueOf,0))")
    @Mapping(target = "isTakenOrderFlag", expression = "java(ConverterUtils.nonNullConvert(param.getConfirmStatus(), Integer::valueOf,0))")
    @Mapping(target = "deliveryOrderType", expression = "java(ConverterUtils.nonNullConvert(param.getOrderType(), Integer::valueOf,0))")
    @Mapping(target = "page", expression = "java(returnSelf(param.getPage()))")
    @Mapping(target = "pageSize", expression = "java(returnSelf(param.getPageSize()))")
    @Mapping(target = "orderSerialNumber", source = "orderSerialNumber")
    UnDoneOrderListReq unDoneQueryRequestConvert(ConfirmOrderQueryRequest param);


    /**
     * 待确认订单列表response对象转换
     * @param param
     * @return
     */
    @Mapping(target = "page", expression = "java(returnSelf(param.getOrderListResp().getPage().getPage()))")
    @Mapping(target = "pageSize", expression = "java(returnSelf(param.getOrderListResp().getPage().getSize()))")
    @Mapping(target = "total", expression = "java(returnSelf(param.getOrderListResp().getPage().getTotalSize()))")
    @Mapping(target = "list", source = "orderListResp.orders")
    PageResultV2<ConfirmOrderVO> unDoneResponseConvert(OrderSearchResp param);

    /**
     * 待确认订单列表response对象转换
     * @param param
     * @return
     */
    @Mapping(target = "page", expression = "java(returnSelf(param.getOrderListResp().getPage().getPage()))")
    @Mapping(target = "pageSize", expression = "java(returnSelf(param.getOrderListResp().getPage().getSize()))")
    @Mapping(target = "total", expression = "java(returnSelf(param.getOrderListResp().getPage().getTotalSize()))")
    @Mapping(target = "list", source = "orderListResp.orders")
    PageResultV2<ConfirmOrderVO> ocmsUnDoneResponseConvert(OcmsOrderSearchResponse param);

    /**
     * 待确认订单VO对象转换
     * @param param
     * @return
     */
    @Mapping(target = "poiName", source = "shopName")
    @Mapping(target = "channelId", expression = "java(String.valueOf(param.getChannelId()))")
    @Mapping(target = "channelName", source = "channelName")
    @Mapping(target = "orderId", source = "channelOrderId")
    @Mapping(target = "skuNames", expression = "java(ConverterUtils.convertList(param.getProducts(),ProductInfoDTO::getSkuName))")
    @Mapping(target = "orderAmt", expression = "java(ConverterUtils.formatMoney(param.getActualPayAmt()))")
    @Mapping(target = "orderType", source = "deliveryOrderTypeTypeName")
    @Mapping(target = "beginTime", expression = "java(beginTimeConvert(param.getDeliveryOrderType(), param.getCreateTime()))")
    @Mapping(target = "expectedTime",expression = "java(expectedTimeConvert(param.getDeliveryOrderType(), param.getEstimatedSendArriveTimeStart()))")
    @Mapping(target = "confirmStatus",expression = "java(confirmStatus(param.getChannelOrderStatus()))")
    @Mapping(target = "pickStatus",expression = "java(pickStatusConvert(param.getDeliveryStatus()))")
    @Mapping(target = "couldOperateItemList", expression = "java(ConverterUtils.convertList(param.getCouldOperateItemList(),String::valueOf))")
    @Mapping(target = "pickFinishTime",expression = "java(pickFinishTimeConvert(param.getDeliveryStatus(), param.getPickupCompleteTime()))")
    ConfirmOrderVO confirmOrderConvert(OrderInfoDTO param);


    /**
     * 待确认订单VO对象转换
     * @param param
     * @return
     */
    @Mapping(target = "poiName", source = "shopName")
    @Mapping(target = "channelId", expression = "java(String.valueOf(param.getChannelId()))")
    @Mapping(target = "channelName", source = "channelName")
    @Mapping(target = "orderId", source = "channelOrderId")
    @Mapping(target = "skuNames", expression = "java(ConverterUtils.convertList(param.getProducts(), ProductInfoVo::getSkuName))")
    @Mapping(target = "orderAmt", expression = "java(ConverterUtils.formatMoney(param.getActualPayAmt()))")
    @Mapping(target = "orderType", source = "deliveryOrderTypeTypeName")
    @Mapping(target = "beginTime", expression = "java(beginTimeConvert(param.getDeliveryOrderType(), param.getCreateTime()))")
    @Mapping(target = "expectedTime",expression = "java(expectedTimeConvert(param.getDeliveryOrderType(), param.getEstimatedSendArriveTimeStart()))")
    @Mapping(target = "confirmStatus",expression = "java(confirmStatus(param.getChannelOrderStatus()))")
    @Mapping(target = "pickStatus",expression = "java(pickStatusConvert(param.getDeliveryStatus()))")
    @Mapping(target = "couldOperateItemList", expression = "java(ConverterUtils.convertList(param.getCouldOperateItemList(),String::valueOf))")
    @Mapping(target = "pickFinishTime",expression = "java(pickFinishTimeConvert(param.getDeliveryStatus(), param.getPickupCompleteTime()))")
    @Mapping(target = "warehouseId",expression = "java(param.getWarehouseId())")
    @Mapping(target = "warehouseName",expression = "java(param.getWarehouseName())")
    ConfirmOrderVO ocmsConfirmOrderConvert(OrderInfoVo param);


    default  String pickFinishTimeConvert(int pickStatus, long pickupCompleteTime) {
        if (pickStatus <= 13) {
            return "";
        } else {
            return DateFormatUtils.format(pickupCompleteTime, Constants.DateFormats.SHOW_FORMAT);
        }
    }


    default String beginTimeConvert(int orderType, long createTime) {
        //预约送达
        if (orderType == 1) {
            return "预约发起";
        } else {
            return DateFormatUtils.format(createTime, Constants.DateFormats.SHOW_FORMAT);
        }
    }


    default String expectedTimeConvert(int orderType, long estimatedSendArriveTimeStart) {
        //预约送达
        if (orderType == 0) {
            return "立即送达";
        } else {
            return DateFormatUtils.format(estimatedSendArriveTimeStart, Constants.DateFormats.SHOW_FORMAT);
        }
    }


    default String pickStatusConvert(int pickStatus) {

        if (pickStatus <= 13) {
            return "未拣货";
        } else {
            return "拣货完成";
        }

    }


    /**
     * 接单状态转换
     * @param orderStatus
     * @return
     */
    default String confirmStatus(int orderStatus) {
        return 10 == orderStatus ? "未接单" : "已接单";
    }
    /**
     * 手工接单请求转换
     * @param param
     * @return
     */
    @Mapping(target = "tenantId", expression = "java(ContextHolder.currentUserTenantId())")
    @Mapping(target = "optUserName", expression = "java(ContextHolder.currentAccount())")
    @Mapping(target = "optUserId", expression = "java(ContextHolder.currentUid())")
    @Mapping(target = "channelId", expression = "java(ConverterUtils.nonNullConvert(param.getChannelId(), Integer::valueOf,0))")
    @Mapping(target = "channelOrderId", source = "orderId")
    AcceptOrderReq acceptReqConvert(QueryDetailRequest param);


    /**
     * 完成拣货请求转换
     * @param param
     * @return
     */
    @Mapping(target = "tenantId", expression = "java(ContextHolder.currentUserTenantId())")
    @Mapping(target = "optUserName", expression = "java(ContextHolder.currentAccount())")
    @Mapping(target = "optUserId", expression = "java(ContextHolder.currentUid())")
    @Mapping(target = "channelId", expression = "java(ConverterUtils.nonNullConvert(param.getChannelId(), Integer::valueOf,0))")
    @Mapping(target = "channelOrderId", source = "orderId")
    CompletePickUpReq pickReqConvert(QueryDetailRequest param);

    /**
     * 手工接单结果转换
     * @param resp
     * @return
     */
    @Mapping(target = "code", expression = "java(returnSelf(resp.getStatus().getCode()))")
    @Mapping(target = "msg", source = "status.msg")
    Result acceptResponseConvert(AcceptOrderResp resp);


    /**
     * 完成拣货结果转换
     * @param resp
     * @return
     */
    @Mapping(target = "code",  expression = "java(returnSelf(resp.getStatus().getCode()))")
    @Mapping(target = "msg", source = "status.msg")
    Result pickResponseConvert(CompletePickUpResp resp);



    @Mapping(target = "tenantId", expression = "java(ContextHolder.currentUserTenantId())")
    @Mapping(target = "optUserName", expression = "java(ContextHolder.currentAccount())")
    @Mapping(target = "optUserId", expression = "java(ContextHolder.currentUid())")
    @Mapping(target = "channelId", expression = "java(ConverterUtils.nonNullConvert(param.getChannelId(), Integer::valueOf,0))")
    @Mapping(target = "channelOrderId", source = "orderId")
    @Mapping(target = "partRefundProductInfo", source = "refundItems")
    @Mapping(target = "reason", source = "reason")
    @Mapping(target = "shopId", expression = "java(ConverterUtils.nonNullConvert(param.getPoiId(), Long::valueOf,0L))")
    TenantPartRefundReq partRefundRequestConvert(PartRefundRequest param);


    /**
     * 退款商品转换
     * @param param
     * @return
     */
    @Mapping(target = "skuId", source = "sku")
    @Mapping(target = "customSkuId", source = "customSkuId")
    @Mapping(target = "skuName", source = "skuName")
    @Mapping(target = "count", expression = "java(ConverterUtils.nonNullConvert(param.getCount(), Integer::valueOf,0))")
    TenantPartRefundProductInfo partRefundItemConvert(PartRefundRequest.PartRefundItem param);


    /**
     * 部分退款结果转换
     * @param resp
     * @return
     */
    @Mapping(target = "code",  expression = "java(returnSelf(resp.getStatus().getCode()))")
    @Mapping(target = "msg", source = "status.msg")
    Result partRefundResponseConvert(TenantPartRefundResp resp);


    /**
     * 全额退款请求转换
     * @param param
     * @return
     */
    @Mapping(target = "tenantId", expression = "java(ContextHolder.currentUserTenantId())")
    @Mapping(target = "optUserName", expression = "java(ContextHolder.currentAccount())")
    @Mapping(target = "optUserId", expression = "java(ContextHolder.currentUid())")
    @Mapping(target = "channelId", expression = "java(ConverterUtils.nonNullConvert(param.getChannelId(), Integer::valueOf,0))")
    @Mapping(target = "channelOrderId", source = "orderId")
    @Mapping(target = "reasonCode",  expression = "java(returnSelf(param.getReasonCode()))")
    @Mapping(target = "reason", source = "reason")
    TenantCancelOrderReq cancelRequestConvert(RefundRequest param);


    /**
     * 全额退款结果转换
     * @param resp
     * @return
     */
    @Mapping(target = "code", expression = "java(returnSelf(resp.getStatus().getCode()))")
    @Mapping(target = "msg", source = "status.msg")
    Result cancelResponseConvert(TenantCancelOrderResp resp);


    /**
     * 打印小票请求转换
     * @param param
     * @return
     */
    @Mapping(target = "tenantId", expression = "java(ContextHolder.currentUserTenantId())")
    @Mapping(target = "optUserName", expression = "java(ContextHolder.currentAccount())")
    @Mapping(target = "optUserId", expression = "java(ContextHolder.currentUid())")
    @Mapping(target = "channelId", expression = "java(ConverterUtils.nonNullConvert(param.getChannelId(), Integer::valueOf,0))")
    @Mapping(target = "channelOrderId", source = "orderId")
    PrintReceiptReq printReceiptReqConvert(QueryDetailRequest param);


    /**
     * 打印小票响应转换
     * @param resp
     * @return
     */
    @Mapping(target = "code", expression = "java(returnSelf(resp.getStatus().getCode()))")
    @Mapping(target = "msg", source = "status.msg")
    Result printReceiptRespConvert(PrintReceiptResp resp);


    @Mapping(target = "tenantId", expression = "java(ContextHolder.currentUserTenantId())")
    @Mapping(target = "channelId",expression = "java(ConverterUtils.nonNullConvert(param.getChannelId(), Integer::valueOf,0))")
    @Mapping(target = "refundType", expression = "java(returnSelf(param.getOrderType()))")
    RefundReasonAndCodeReq refundReasonRequestConvert(QueryRefundReasonRequest param);


    @Mapping(target = "data", source = "possibleRefundReasons")
    Result<List<UiOption>> refundReasonResponseConvert(RefundReasonAndCodeResp param);


    @Mapping(target = "code", expression = "java(String.valueOf(param.getCode()))")
    @Mapping(target = "value",source = "reason")
    UiOption uiOptionConvert(RefundReasonAndCode param);



    @Mapping(target = "tenantId", expression = "java(ContextHolder.currentUserTenantId())")
    @Mapping(target = "optUserName", expression = "java(ContextHolder.currentAccount())")
    @Mapping(target = "optUserId", expression = "java(ContextHolder.currentUid())")
    @Mapping(target = "channelId", expression = "java(ConverterUtils.nonNullConvert(param.getChannelId(), Integer::valueOf,0))")
    @Mapping(target = "channelOrderId", source = "orderId")
    TenantReceiveRefundProductsReq receiveRefundRequestConvert(QueryDetailRequest param);


    @Mapping(target = "code", expression = "java(returnSelf(resp.getStatus().getCode()))")
    @Mapping(target = "msg", source = "status.msg")
    Result receiveRefundResponseConvert(OrderCommonResp resp);


    @Mapping(target = "tenantId",expression = "java(ContextHolder.currentUserTenantId())")
    @Mapping(target = "operatorId",expression ="java(ContextHolder.currentUid())" )
    @Mapping(target = "receiverName",source ="receiverName" )
    @Mapping(target = "receiverAddress",source ="receiverAddress" )
    @Mapping(target = "receiverPrivacyPhoneLastFourDigits",source ="receiverPhoneLastFourDigits" )
    @Mapping(target = "shopName",source ="shopName" )
    @Mapping(target = "shopId",expression = "java(request.getShopId() == 0 ? null : request.getShopId())")
    @Mapping(target = "channelOrderId",source ="channelOrderId" )
    @Mapping(target = "beginCreateTime",source ="beginCreateTime" )
    @Mapping(target = "endCreateTime",source ="endCreateTime" )
    @Mapping(target = "orderStatusList",expression = "java(ChannelOrderConvertUtils.orderStatusMid2BizList(request.getChannelOrderStatusList()))" )
    @Mapping(target = "orderBizTypeList",expression = "java(ChannelOrderConvertUtils.sourceMid2BizList(request.getChannelIdList()))" )
    @Mapping(target = "refundTagIdList",expression = "java(ChannelOrderConvertUtils.refundTypeMid2BizList(request.getRefundTagIdList()))" )
    @Mapping(target = "orderBookingTypeList",source ="orderBookingTypeList" )
    @Mapping(target = "orderUserTypeList",source ="orderUserTypeList" )
    @Mapping(target = "memberCardNumFuzzy",source ="memberCardNumFuzzy" )
    @Mapping(target = "goodsNameFuzzy",source ="goodsNameFuzzy" )
    ExportChannelOrderDetailRequest asyncExportOrdersDetailRequestConvert(OrderAllListReq request);


    @Mapping(target = "token",source = "token")
    @Mapping(target = "taskStatus",expression = "java(returnSelf(response.getTaskStatus()))")
    @Mapping(target = "fileUrlList",source = "fileUrlList")
    @Mapping(target = "createTime",expression ="java(DateUtil.getStringDateFromMilliSeconds(response.getCreateTime(), Constants.DateFormats.DAY_FORMAT))" )
    @Mapping(target = "finishTime",expression ="java(DateUtil.getStringDateFromMilliSeconds(response.getFinishTime(), Constants.DateFormats.DAY_FORMAT))")
    AsyncExportOrdersTaskVO asyncExportOrdersDetailTaskVOConvert(ExportResultResponse response);

    default int returnSelf(int source) {
        return source;
    }

}
