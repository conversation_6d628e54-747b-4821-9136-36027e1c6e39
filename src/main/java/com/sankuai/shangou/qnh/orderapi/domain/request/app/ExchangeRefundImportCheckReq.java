package com.sankuai.shangou.qnh.orderapi.domain.request.app;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@TypeDoc(
        description = "售后换货信息校验"
)
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("售后换货信息校验")
public class ExchangeRefundImportCheckReq implements BaseRequest {

    @FieldDoc(
            description = "中台订单Id"
    )
    @NotNull(message = "订单Id")
    @ApiModelProperty(value = "订单Id", required = true)
    public Long orderId;

    @FieldDoc(
            description = "售后id"
    )
    @ApiModelProperty(value = "售后id", required = true)
    @NotNull(message = "售后id")
    public Long serviceId;

    @FieldDoc(
            description = "租户ID"
    )
    @ApiModelProperty(value = "租户ID", required = true)
    public Long tenantId;

    @FieldDoc(
            description = "channelId"
    )
    @ApiModelProperty(value = "channelId", required = true)
    public Integer channelId;

}
