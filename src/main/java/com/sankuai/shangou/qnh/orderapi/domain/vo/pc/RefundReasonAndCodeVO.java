package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * desc: 退款理由
 */
@TypeDoc(
        description = "退款理由"
)
@ApiModel("退款理由")
@Data
public class RefundReasonAndCodeVO {

    @FieldDoc(
            description = "退款理由Code", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "退款理由Code", required = true)
    private Integer code;

    @FieldDoc(
            description = "退款理由文案", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "退款理由文案", required = true)
    private String reason;

    @FieldDoc(description = "凭证描述文案")
    @ApiModelProperty(name = "退款理由文案")
    public String evidenceDescription;

    @FieldDoc(description = "是否需要上传凭证，Y必填，N非必填")
    @ApiModelProperty(name = "是否需要上传凭证，Y必填，N非必填")
    public String evidenceNeed;
}
