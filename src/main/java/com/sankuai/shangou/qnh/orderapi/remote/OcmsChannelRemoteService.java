package com.sankuai.shangou.qnh.orderapi.remote;

import com.dianping.cat.util.MetricHelper;
import com.dianping.lion.client.Lion;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.meituan.shangou.saas.common.enums.ChannelTypeEnum;
import com.meituan.shangou.saas.dto.StatusCodeEnum;
import com.meituan.shangou.saas.dto.response.PrivacyPhoneResponse;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderModel;
import com.meituan.shangou.saas.o2o.dto.model.DispatchOrderExtDataModel;
import com.meituan.shangou.saas.o2o.dto.request.BizOrderQueryByViewOrderIdRequest;
import com.meituan.shangou.saas.o2o.dto.request.BizOrderQueryRequest;
import com.meituan.shangou.saas.o2o.dto.request.QueryOrderPrivacyPhoneRequest;
import com.meituan.shangou.saas.o2o.dto.response.BizOrderQueryResponse;
import com.meituan.shangou.saas.o2o.dto.response.QueryOrderPrivacyPhoneResponse;
import com.meituan.shangou.saas.o2o.service.BizOrderThriftService;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OcmsOrderDetailReq;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OcmsOrderDetailResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OrderBaseVo;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OrderDetailVo;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OrderOptLogVo;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.model.DeliveryInfoExtend;
import com.meituan.shangou.saas.order.management.client.service.online.OcmsOrderSearchService;
import com.meituan.shangou.saas.order.management.client.utils.OrderUtil;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.enums.OrderSourceEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.meituan.shangou.saas.service.ocms.OCMSOrderThriftService;
import com.meituan.shangou.saas.tenant.thrift.DepartmentV2ThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiEntityTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.department.v2.dto.EmployeeInfoV2Dto;
import com.meituan.shangou.saas.tenant.thrift.dto.department.v2.request.EmployeeInfoInAllDepartmentsDetailV2Request;
import com.meituan.shangou.saas.tenant.thrift.dto.department.v2.response.EmployeeInfoInAllDepartmentsDetailV2Response;
import com.meituan.shangou.sac.thrift.search.SacAccountSearchThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.AccountInfoVo;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.*;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.ChannelOrderDockingThriftService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.service.QnhOrderDockingThriftService;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.QueryPhoneType;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.qnh.orderapi.converter.store.ChannelOrderConverter;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.QueryOrderDetailRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.QueryVirtualPhoneRequest;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.CommonFuseResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.CommonResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.QueryVirtualPhoneResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.User;
import com.sankuai.shangou.qnh.orderapi.enums.OrderTypeEnum;
import com.sankuai.shangou.qnh.orderapi.enums.app.ErrorCodeEnum;
import com.sankuai.shangou.qnh.orderapi.enums.app.ResultCodeEnum;
import com.sankuai.shangou.qnh.orderapi.enums.store.BizResponseCodeEnum;
import com.sankuai.shangou.qnh.orderapi.service.store.AuthThriftWrapperService;
import com.sankuai.shangou.qnh.orderapi.utils.store.ApiMethodParamThreadLocal;
import com.sankuai.shangou.qnh.orderapi.utils.store.MccConfigUtil;
import com.sankuai.shangou.qnh.orderapi.utils.store.MccDynamicConfigUtil;
import com.sankuai.shangou.qnh.orderapi.utils.store.OCMSUtils;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/***
 * author : <EMAIL> 
 * data : 2021/3/16 
 * time : 下午4:42
 **/
@Service
@Slf4j
public class OcmsChannelRemoteService {

    @Resource
    ChannelOrderDockingThriftService.Iface channelOrderDockingThriftService;

    @Resource
    private QnhOrderDockingThriftService.Iface qnhOrderThriftService;

    @Resource
    private BizOrderThriftService bizOrderThriftService;

    @Resource
    private OcmsOrderSearchService ocmsOrderSearchService;

    @Resource
    private AuthThriftWrapperService authThriftWrapper;

    @Resource
    private AuthRemoteService authRemoteService;

    @Resource
    private MtUserRemoteService mtUserWrapper;

    @Resource
    private PrivacyNumberRemoteService privacyNumberWrapper;

    @Resource
    private SacAccountSearchThriftService sacAccountSearchThriftService;
    @Resource
    private DepartmentV2ThriftService departmentV2ThriftService;

    @Resource
    private OCMSOrderThriftService ocmsOrderThriftService;

    public CommonResponse<QueryVirtualPhoneResponse> queryVirtualPhoneThroughChannel(QueryVirtualPhoneRequest virtualPhoneReq) throws TException {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        long tenantId = user.getTenantId();
        //直接请求channel
        CommonResponse<QueryVirtualPhoneResponse> resp = CommonResponse.fail(ResultCode.FAIL.code, ResultCode.FAIL.getErrorMessage(), null);
        if (virtualPhoneReq.getPhoneType() == QueryPhoneType.CUSTOMER.getValue()){
            // 属于歪马租户 && (全部门店使用AXB || 门店白名单包含当前门店)
            // 使用新逻辑
            if (MccDynamicConfigUtil.getAxbPrivacyPhoneTenantIds().contains(String.valueOf(tenantId))
                    && (MccDynamicConfigUtil.isAllUserAxBPrivacyPhone()
                    || MccDynamicConfigUtil.getAxBPrivacyPhonePoiIds().contains(String.valueOf(virtualPhoneReq.getStoreId()))) ) {
                callCustomerThroughAxB(virtualPhoneReq, tenantId, resp, virtualPhoneReq.getStoreId());
                // 否则使用老逻辑
            } else {
                OrderDetailVo orderDetail = getOrderDetailFromMng(virtualPhoneReq, tenantId, user, true);
                if (Objects.isNull(orderDetail) || Objects.isNull(orderDetail.getOrderBaseDto())) {
                    return CommonResponse.fail(ResultCode.FAIL.getCode(), "获取订单详情失败");
                }
                // 名酒馆订单平台配送-前端弹窗拦截
                if (isMtFamousTavernAndPlatformDelivery(orderDetail)) {
                    resp.setCode(ResultCode.SUCCESS.getCode());
                    resp.setMessage(ResultCode.SUCCESS.getErrorMessage());
                    QueryVirtualPhoneResponse virtualPhoneResponse = new QueryVirtualPhoneResponse();
                    virtualPhoneResponse.setShouldGetGatherPhone(true);
                    resp.setData(virtualPhoneResponse);
                    return resp;
                }
                //不使用入参的门店，改为使用订单的门店ID，防止转入单拨打顾客电话时的，前端传入转单后的门店ID，导致查询渠道失败
                virtualPhoneReq.setStoreId(orderDetail.getOrderBaseDto().getShopId());
                int orderType = Objects.isNull(virtualPhoneReq.getOrderType()) ? 0 : virtualPhoneReq.getOrderType();
                // 非歪马租户，走新逻辑 --> 所有渠道，在订单已完结【已完成/已取消/已关闭】状态后的配置时间段内，才允许获取手机号联系顾客
                if (!MccConfigUtil.isDrunkHorseTenant(tenantId) && MccDynamicConfigUtil.checkLimitTimeContactUserTenant(tenantId)) {
                    return queryVirtualPhoneThroughChannel(orderDetail, resp, virtualPhoneReq, tenantId, orderType);
                }
                // 以下是历史逻辑
                if (!isOrderCanCallUser24Hour(orderDetail.getOrderOpLogList()) || OrderTypeEnum.AFTER_SALE_ORDER.getCode() == orderType
                        //根据订单终态时间判断订单的隐私号是否过期
                        || isOrderEndCanCallUserOver24Hour(orderDetail)) {
                    // 抖音查询线上详情不会给号码，直接走老逻辑
                    if (virtualPhoneReq.getChannelId() == DynamicChannelType.DOU_YIN.getChannelId()) {
                        callCustomerThroughChannel(virtualPhoneReq, tenantId, resp);
                        return resp;
                    }

                    if (MccConfigUtil.isQueryVirtualPhoneFromChannel(tenantId) &&  DynamicChannelType.findOf(virtualPhoneReq.getChannelId()) == DynamicChannelType.MEITUAN) {
                        queryOrderPrivacyPhone(orderDetail, resp, virtualPhoneReq.getAfterSaleId());
                        // 返回手机号带*，则返回获取失败错误提示
                        if(Objects.equals(resp.getCode(), ErrorCodeEnum.SUCCESS.getCode())
                                && OCMSUtils.isPhoneHasMusk(resp.getData().getPhoneNo())){
                            resp.setCode(ErrorCodeEnum.ORDER_CANT_BE_CONTACT_24_HOUR.getCode());
                            resp.setMessage(ErrorCodeEnum.ORDER_CANT_BE_CONTACT_24_HOUR.getMessage());
                        }
                    } else if (OrderTypeEnum.AFTER_SALE_ORDER.getCode() == orderType){
                        queryOrderPrivacyPhone(orderDetail, resp, virtualPhoneReq.getAfterSaleId());
                    } else{
                        resp.setCode(ErrorCodeEnum.ORDER_CANT_BE_CONTACT_24_HOUR.getCode());
                        resp.setMessage(ErrorCodeEnum.ORDER_CANT_BE_CONTACT_24_HOUR.getMessage());
                    }
                    return resp;
                }
                callCustomerThroughChannel(virtualPhoneReq, tenantId, resp);
            }
        }else if (virtualPhoneReq.getPhoneType() == QueryPhoneType.RIDER.getValue()){
            Integer orderBizType = ChannelOrderConverter.convertChannelId2OrderBizType(virtualPhoneReq.getChannelId());
            //请求渠道网关，获取配送订单
            MetricHelper.build().name("ocms.query_virtual_phone.query").tag("type", "rider").tag("tenantId", String.valueOf(tenantId)).tag("channel", String.valueOf(virtualPhoneReq.getChannelId())).count();
            LogisticsStatusDTO logisticsStatusDTO = queryLogisticsStatus(virtualPhoneReq.getChannelOrderId(), DynamicChannelType.findOf(virtualPhoneReq.getChannelId()), tenantId);
            log.info("查询线上骑手电话,order:{},phone:{}", virtualPhoneReq.getChannelOrderId(), logisticsStatusDTO);
            String phone = null;
            if (logisticsStatusDTO != null){
                phone = logisticsStatusDTO.getRiderPhone();
            } else {
                //京东查不到配送状态，查询订单中的骑手信息
                BizOrderModel bizOrderModel = queryOfflineOrderModel(orderBizType, virtualPhoneReq.getChannelOrderId(), tenantId);
                phone = bizOrderModel.getDeliveryModel().getRiderPhone();
            }
            log.info("骑手电话,order:{},phone:{}", virtualPhoneReq.getChannelOrderId(), phone);
            if (StringUtils.isNotBlank(phone) && !OCMSUtils.isPhoneHasMusk(phone)){
                QueryVirtualPhoneResponse virtualPhoneResponse = new QueryVirtualPhoneResponse();
                virtualPhoneResponse.setPhoneNo(phone);
                resp = CommonResponse.success(virtualPhoneResponse);
                MetricHelper.build().name("ocms.query_virtual_phone.suc").tag("type", "rider").tag("tenantId", String.valueOf(tenantId)).tag("channel", String.valueOf(virtualPhoneReq.getChannelId())).count();
            }

            //如果是接入第三方配送订单，尝试直接从配送信息中获取
            if (resp.getData() == null || StringUtils.isEmpty(resp.getData().getPhoneNo())) {
                try {
                    BizOrderModel bizOrderModel = queryOfflineOrderModel(orderBizType, virtualPhoneReq.getChannelOrderId(), tenantId);
                    //接入了第三方配送的订单
                    if (bizOrderModel != null && bizOrderModel.getDeliveryModel() != null && StringUtils.isNotEmpty(bizOrderModel.getDeliveryModel().getChannelDeliveryId()) &&
                            StringUtils.isNotEmpty(bizOrderModel.getDeliveryModel().getRiderPhone())) {
                        log.info("此单为接入第三方配送订单");
                        QueryVirtualPhoneResponse virtualPhoneResponse = new QueryVirtualPhoneResponse();
                        virtualPhoneResponse.setPhoneNo(phone);
                        resp = CommonResponse.success(virtualPhoneResponse);
                        log.info("此单为接入第三方配送订单 返回:{}", resp);
                        MetricHelper.build().name("ocms.query_virtual_phone.suc").tag("type", "rider").tag("tenantId", String.valueOf(tenantId)).tag("channel", String.valueOf(virtualPhoneReq.getChannelId())).count();
                        return resp;
                    }
                } catch (Exception e) {
                    log.warn("查询线下订单信息失败", e);
                }
            }
        } else if (virtualPhoneReq.getPhoneType() == QueryPhoneType.MT_FAMOUS_TAVERN_GATHER_PHONE.getValue()) {
            OrderDetailVo orderDetail = getOrderDetailFromMng(virtualPhoneReq, tenantId, user, true);
            if (Objects.isNull(orderDetail) || Objects.isNull(orderDetail.getOrderBaseDto())) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), "获取订单详情失败");
            }
            String gatherPoiPhone = getGatherPhone(orderDetail);
            if (StringUtils.isNotBlank(gatherPoiPhone)) {
                QueryVirtualPhoneResponse virtualPhoneResponse = new QueryVirtualPhoneResponse();
                virtualPhoneResponse.setPhoneNo(gatherPoiPhone);
                resp.setData(virtualPhoneResponse);
                resp.setCode(ErrorCodeEnum.SUCCESS.getCode());
                resp.setMessage(ErrorCodeEnum.SUCCESS.getMessage());
            } else {
                resp.setCode(ErrorCodeEnum.QUERY_MT_FAMOUS_TAVERN_GATHER_PHONE_NOT_FIND.getCode());
                resp.setMessage(ErrorCodeEnum.QUERY_MT_FAMOUS_TAVERN_GATHER_PHONE_NOT_FIND.getMessage());
            }
        }
        log.info("[订单]查询手机号,req：{}, resp:{}", virtualPhoneReq, resp);
        return resp;
    }

    /**
     * 查询虚拟号
     * 除歪马租户外，所有渠道在订单已完结【已完成/已取消/已关闭】状态后的配置时间段内，才允许获取手机号联系顾客
     * @param orderDetail
     * @param resp
     * @param virtualPhoneReq
     * @param tenantId
     * @param orderType
     * @return
     * @throws TException
     */
    private CommonResponse<QueryVirtualPhoneResponse> queryVirtualPhoneThroughChannel(OrderDetailVo orderDetail,
                                                                                      CommonResponse<QueryVirtualPhoneResponse> resp,
                                                                                      QueryVirtualPhoneRequest virtualPhoneReq,
                                                                                      Long tenantId, Integer orderType) throws TException{
        // 订单已完结 && 超过配置时间
        if(isOrderEndAndOverTime(orderDetail)){
            resp.setCode(ErrorCodeEnum.ORDER_CANT_BE_CONTACT_OVER_HOUR.getCode());
            resp.setMessage(getOrderCantBeContactOverHourMsg(MccDynamicConfigUtil.getDesensitizeReceiverInfoTime()));
            return resp;
        }
        // 复刻历史逻辑，售后中订单，美团渠道的特殊处理
        if(OrderTypeEnum.AFTER_SALE_ORDER.getCode() == orderType
                && MccConfigUtil.isQueryVirtualPhoneFromChannel(tenantId)
                && DynamicChannelType.findOf(virtualPhoneReq.getChannelId()) == DynamicChannelType.MEITUAN){
            queryOrderPrivacyPhone(orderDetail, resp, virtualPhoneReq.getAfterSaleId());
            // 返回手机号带*，则返回获取失败错误提示
            if(Objects.equals(resp.getCode(), ErrorCodeEnum.SUCCESS.getCode())
                    && OCMSUtils.isPhoneHasMusk(resp.getData().getPhoneNo())){
                resp.setCode(ErrorCodeEnum.QUERY_ORDER_VIRTUAL_PHONE_FAIL.getCode());
                resp.setMessage(ErrorCodeEnum.QUERY_ORDER_VIRTUAL_PHONE_FAIL.getMessage());
            }
        }else{
            // 走历史逻辑
            callCustomerThroughChannel(virtualPhoneReq, tenantId, resp);
        }
        return resp;
    }

    private static final BigDecimal HOUR = new BigDecimal(3600);

    /**
     * 超过配置时间提示文案
     * @param configTime
     * @return
     */
    private String getOrderCantBeContactOverHourMsg(Long configTime){
        if(Objects.isNull(configTime)){
            return ErrorCodeEnum.ORDER_CANT_BE_CONTACT_OVER_HOUR.getMessage();
        }
        //秒转小时
        int hour = BigDecimal.valueOf(configTime).divide(HOUR, RoundingMode.DOWN).intValue();
        if(hour <= 0){
            return ErrorCodeEnum.ORDER_CANT_BE_CONTACT_OVER_HOUR.getMessage();
        }
        return "订单完成" + hour + "小时后虚拟号已失效，无法电话联系顾客";

    }


    /**
     * 查询虚拟号
     * 除歪马租户外，所有渠道在订单已完结【已完成/已取消/已关闭】状态后的配置时间段内，才允许获取手机号联系顾客
     * @param orderDetail
     * @param resp
     * @param virtualPhoneReq
     * @param tenantId
     * @param orderType
     * @return
     * @throws TException
     */
    private com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse<com.sankuai.shangou.qnh.orderapi.domain.response.app.QueryVirtualPhoneResponse> queryVirtualPhoneThroughChannel(OrderDetailVo orderDetail,
                                                                                      com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse<com.sankuai.shangou.qnh.orderapi.domain.response.app.QueryVirtualPhoneResponse> resp,
                                                                                      com.sankuai.shangou.qnh.orderapi.domain.request.app.QueryVirtualPhoneRequest virtualPhoneReq,
                                                                                      Long tenantId, Integer orderType) throws TException{
        // 订单已完结 && 超过配置时间
        if(isOrderEndAndOverTime(orderDetail)){
            resp.setCode(ErrorCodeEnum.ORDER_CANT_BE_CONTACT_OVER_HOUR.getCode());
            resp.setMessage(getOrderCantBeContactOverHourMsg(MccDynamicConfigUtil.getDesensitizeReceiverInfoTime()));
            return resp;
        }
        // 复刻历史逻辑，售后中订单，美团渠道的特殊处理
        if(OrderTypeEnum.AFTER_SALE_ORDER.getCode() == orderType
                && MccConfigUtil.isQueryVirtualPhoneFromChannel(tenantId)
                && DynamicChannelType.findOf(virtualPhoneReq.getChannelId()) == DynamicChannelType.MEITUAN){
            queryOrderPrivacyPhone(orderDetail, resp, virtualPhoneReq.getAfterSaleId());
            // 返回手机号带*，则返回获取失败错误提示
            if(Objects.equals(resp.getCode(), ErrorCodeEnum.SUCCESS.getCode())
                    && OCMSUtils.isPhoneHasMusk(resp.getData().getPhoneNo())){
                resp.setCode(ErrorCodeEnum.QUERY_ORDER_VIRTUAL_PHONE_FAIL.getCode());
                resp.setMessage(ErrorCodeEnum.QUERY_ORDER_VIRTUAL_PHONE_FAIL.getMessage());
            }
        }else{
            // 走历史逻辑
            callCustomerThroughChannel(virtualPhoneReq, tenantId, resp);
        }
        return resp;
    }

    /**
     * 查询虚拟号
     * 除歪马租户外，所有渠道在订单已完结【已完成/已取消/已关闭】状态后的配置时间段内，才允许获取手机号联系顾客
     * @param orderDetail
     * @param resp
     * @param virtualPhoneReq
     * @param tenantId
     * @param orderType
     * @return
     * @throws TException
     */
    private CommonFuseResponse<com.sankuai.shangou.qnh.orderapi.domain.response.pc.QueryVirtualPhoneResponse> queryVirtualPhoneThroughChannel(OrderDetailVo orderDetail,
                                                                                      CommonFuseResponse<com.sankuai.shangou.qnh.orderapi.domain.response.pc.QueryVirtualPhoneResponse> resp,
                                                                                      com.sankuai.shangou.qnh.orderapi.domain.request.pc.QueryVirtualPhoneRequest virtualPhoneReq,
                                                                                      Long tenantId, Integer orderType) throws TException{
        // 订单已完结 && 超过配置时间
        if(isOrderEndAndOverTime(orderDetail)){
            resp.setCode(ErrorCodeEnum.ORDER_CANT_BE_CONTACT_OVER_HOUR.getCode());
            resp.setMessage(getOrderCantBeContactOverHourMsg(MccDynamicConfigUtil.getDesensitizeReceiverInfoTime()));
            return resp;
        }
        // 复刻历史逻辑，售后中订单，美团渠道的特殊处理
        if(OrderTypeEnum.AFTER_SALE_ORDER.getCode() == orderType
                && MccConfigUtil.isQueryVirtualPhoneFromChannel(tenantId)
                && DynamicChannelType.findOf(virtualPhoneReq.getChannelId()) == DynamicChannelType.MEITUAN){
            queryOrderPrivacyPhone(orderDetail, resp, virtualPhoneReq.getAfterSaleId());
            // 返回手机号带*，则返回获取失败错误提示
            if(Objects.equals(resp.getCode(), ErrorCodeEnum.SUCCESS.getCode())
                    && OCMSUtils.isPhoneHasMusk(resp.getData().getPhoneNo())){
                resp.setCode(ErrorCodeEnum.QUERY_ORDER_VIRTUAL_PHONE_FAIL.getCode());
                resp.setMessage(ErrorCodeEnum.QUERY_ORDER_VIRTUAL_PHONE_FAIL.getMessage());
            }
        }else{
            // 走历史逻辑
            callCustomerThroughChannel(virtualPhoneReq, resp);
        }
        return resp;
    }

    /**
     * 订单是否已完结 && 超过配置时间
     * @param orderDetail
     * @return
     */
    private boolean isOrderEndAndOverTime(OrderDetailVo orderDetail){
        OrderBaseVo order = orderDetail.getOrderBaseDto();
        // 订单非已完结 -> 允许联系
        if(!OrderUtil.isOrderEnd(order.getOrderStatus())){
            return false;
        }
        Long orderEndTime = OrderUtil.getOrderTimeByLogVo(order.getOrderStatus(), orderDetail.getOrderOpLogList());
        // 是否超过配置时间
        return OrderUtil.isOverConfigTime(orderEndTime, MccDynamicConfigUtil.getDesensitizeReceiverInfoTime());
    }


    /**
     * 从ocms channel获取拨打号码
     * @param virtualPhoneReq 获取虚拟号请求体
     * @param tenantId 租户id
     * @param resp 请求体
     * @throws TException
     */
    private void callCustomerThroughChannel(QueryVirtualPhoneRequest virtualPhoneReq, long tenantId,
                                         CommonResponse<QueryVirtualPhoneResponse> resp) throws TException {
        // 共享仓模式清除门店id
        if(Objects.equals(virtualPhoneReq.getEntityType(), PoiEntityTypeEnum.SHAREABLE_WAREHOUSE.code())){
            virtualPhoneReq.setStoreId(null);
        }
        MetricHelper.build().name("ocms.query_virtual_phone.query").tag("type", "customer").tag("tenantId", String.valueOf(tenantId)).tag("channel", String.valueOf(virtualPhoneReq.getChannelId())).count();
        ChannelOrderDetailDTO channelOrderDetailDTO = queryOnlineOrderDetail(virtualPhoneReq.getChannelOrderId(), DynamicChannelType.findOf(virtualPhoneReq.getChannelId()), tenantId, virtualPhoneReq.getStoreId());
        if (channelOrderDetailDTO != null && channelOrderDetailDTO.getDeliveryDetail().isUserPhoneIsValid()
                && !OCMSUtils.isPhoneHasMusk(channelOrderDetailDTO.getDeliveryDetail().getUserPhone())){
            QueryVirtualPhoneResponse virtualPhoneResponse = new QueryVirtualPhoneResponse();
            virtualPhoneResponse.setPhoneNo(channelOrderDetailDTO.getDeliveryDetail().getUserPhone());
            // 如果是歪马渠道 && 使用了隐私号 && 当前城市隐私号未降级
            if (virtualPhoneReq.getChannelId().equals(ChannelTypeEnum.MT_DRUNK_HORSE.getValue())
                    && channelOrderDetailDTO.getDeliveryDetail().getUsePrivacyPhone() == NumberUtils.INTEGER_ONE
                    && StringUtils.isNotBlank(channelOrderDetailDTO.getDeliveryDetail().getUserPrivacyPhone())
                    && !channelOrderDetailDTO.isCityPrivacyDegrade()) {
                virtualPhoneResponse.setPhoneNo(channelOrderDetailDTO.getDeliveryDetail().getUserPrivacyPhone());
            }
            if (CollectionUtils.isNotEmpty(channelOrderDetailDTO.getDeliveryDetail().getBackUpUserPrivacyPhone())) {
                virtualPhoneResponse.setBackUpPhoneNo(channelOrderDetailDTO.getDeliveryDetail().getBackUpUserPrivacyPhone());
            }
            resp.setCode(BizResponseCodeEnum.SUCCESS.getCode());
            resp.setData(virtualPhoneResponse);
            MetricHelper.build().name("ocms.query_virtual_phone.suc").tag("type", "customer").tag("tenantId", String.valueOf(tenantId)).tag("channel", String.valueOf(virtualPhoneReq.getChannelId())).count();
        }
    }

    /**
     * 获取axb隐私号拨打号码
     * @param virtualPhoneReq 获取虚拟号请求体
     * @param tenantId 租户id
     * @param resp 请求体
     */
    private void callCustomerThroughAxB(QueryVirtualPhoneRequest virtualPhoneReq, long tenantId,
                                        CommonResponse<QueryVirtualPhoneResponse> resp, Long storeId) throws TException {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        OrderDetailVo orderDetail = getOrderDetailFromMng(virtualPhoneReq, tenantId, user, true);
        if (orderDetail == null) {
            throw new BizException("获取订单详情失败");
        }

        // 判断订单是否超过终态指定长度，若是不能联系用户
        if (!isOrderCanCallUser(orderDetail.getOrderOpLogList(), storeId, user.getTenantId(), user.getEmployeeId())) {
            resp.setCode(BizResponseCodeEnum.ORDER_CANT_BE_CONTACT.getCode());
            resp.setMessage(BizResponseCodeEnum.ORDER_CANT_BE_CONTACT.getMessage());
            return;
        }

        // 如果没有使用隐私号，直接返回真实手机号
        OrderBaseVo orderBase = orderDetail.getOrderBaseDto();
        if (NumberUtils.INTEGER_ZERO.equals(orderBase.getUsePrivacyPhone())) {
            // 歪马订单，没有选中隐私号保护的订单，订单完成后24小时后隐藏手机号
            if (MccConfigUtil.isDrunkHorseTenant(tenantId) && isOrderCompleteOverTime(orderBase)){
                resp.setCode(BizResponseCodeEnum.ORDER_CANT_BE_CONTACT.getCode());
                resp.setMessage(BizResponseCodeEnum.ORDER_CANT_BE_CONTACT.getMessage());
                return;
            }
            QueryVirtualPhoneResponse virtualPhoneResponse = new QueryVirtualPhoneResponse();
            virtualPhoneResponse.setPhoneNo(orderBase.getReceiverPhone());
            resp.setCode(BizResponseCodeEnum.SUCCESS.getCode());
            resp.setData(virtualPhoneResponse);
            return;
        }

        // 使用了隐私号，去获取真实手机号
        String receiverPhone = null;
        if (virtualPhoneReq.getChannelId() == ChannelTypeEnum.MT_DRUNK_HORSE.getValue()) {
            receiverPhone = orderBase.getReceiverPhone();
        } else if (virtualPhoneReq.getChannelId() == ChannelTypeEnum.MEITUAN.getValue()) {
            receiverPhone = orderBase.getReceiverPrivacyPhone();
        }
        if (StringUtils.isBlank(receiverPhone)) {
            throw new BizException("订单收货号码为空");
        }
        // 获取牵牛花登录账号手机号
        Map<Long, AccountInfoVo> accountInfoMap = authThriftWrapper.querySimplyAccountInfoByIds(Arrays.asList(user.getAccountId()));
        AccountInfoVo accountInfo = accountInfoMap.get(user.getAccountId());
        if (accountInfo == null || StringUtils.isBlank(accountInfo.getMobile())) {
            throw new BizException("获取登录账号信息失败--账号或其手机号为空");
        }
        QueryVirtualPhoneResponse data = new QueryVirtualPhoneResponse();
        PrivacyPhoneResponse riderReceiverResponse = privacyNumberWrapper.applyAxB(orderBase, accountInfo.getMobile(), receiverPhone);
        // 获取隐私号失败--使用原逻辑返回拨打号码
        if (!riderReceiverResponse.getStatus().code.equals(StatusCodeEnum.SUCCESS.getCode())) {
            if(Objects.equals(riderReceiverResponse.getStatus().code, StatusCodeEnum.AUTH_INFO_NOT_AUTHORIZED.getCode())) {
                // 虚拟号请求返回未认证
                resp.setCode(ErrorCodeEnum.VIRTUAL_PHONE_AUTH_INFO_NOT_AUTHORIZED.getCode());
                resp.setMessage(ErrorCodeEnum.VIRTUAL_PHONE_AUTH_INFO_NOT_AUTHORIZED.getMessage());
            } else {
                callCustomerThroughChannel(virtualPhoneReq, tenantId, resp);
            }
            return;
        } else {
            data.setPhoneNo(riderReceiverResponse.getPrivacyPhone());
            data.setBackUpPhoneNo(riderReceiverResponse.getBackupPrivacyPhones());
        }
        String mtBindPhone = mtUserWrapper.getMtUserInfoByUserId(orderBase.getUserId()).getPhone();
        if (!receiverPhone.equals(mtBindPhone)) {
            // 调用AXB获取备用隐私号
            PrivacyPhoneResponse riderBindPhoneResponse = privacyNumberWrapper.applyAxB(orderBase, accountInfo.getMobile(), mtBindPhone);
            // 调用成功
            if (riderBindPhoneResponse.getStatus().code.equals(StatusCodeEnum.SUCCESS.getCode())) {
                data.setBindPhoneNo(riderBindPhoneResponse.getPrivacyPhone());
                data.setBindBackupPhoneNo(riderBindPhoneResponse.getBackupPrivacyPhones());
            }
        }
        resp.setCode(BizResponseCodeEnum.SUCCESS.getCode());
        resp.setData(data);
    }

    private boolean isOrderCompleteOverTime(OrderBaseVo orderBase) {
        Integer orderStatus = orderBase.getOrderStatus();
        if (Objects.equals(OrderStatusEnum.COMPLETED.getValue(), orderStatus) || Objects.equals(OrderStatusEnum.CANCELED.getValue(), orderStatus)){
            Long completeTime = Objects.equals(OrderStatusEnum.COMPLETED.getValue(), orderStatus) && orderBase.getCompleteTime() != null
                    ? orderBase.getCompleteTime() : orderBase.getUpdateTime();
            int thresholdHour = MccConfigUtil.getDrunkhorsePrivatePhoneShowHour();
            long now = System.currentTimeMillis();
            return completeTime != null && TimeUnit.MILLISECONDS.toHours( now - completeTime) >= thresholdHour;
        }
        return false;
    }

    /**
     * 从ordermng获取订单详情
     * @param virtualPhoneReq 请求
     * @param tenantId 租户ID
     * @param user 当前登录用户
     */
    private OrderDetailVo getOrderDetailFromMng(QueryVirtualPhoneRequest virtualPhoneReq, long tenantId, User user, boolean useDbSearch) {
        OcmsOrderDetailReq orderDetailReq = new OcmsOrderDetailReq();
        orderDetailReq.setChannelId(virtualPhoneReq.getChannelId());
        orderDetailReq.setTenantId(tenantId);
        orderDetailReq.setChannelOrderId(virtualPhoneReq.getChannelOrderId());
        orderDetailReq.setOperator(user.getAccountId());
        orderDetailReq.setUseDbSearch(useDbSearch);
        try {
            OcmsOrderDetailResponse orderDetailResponse = ocmsOrderSearchService.orderDetail(orderDetailReq);
            if (orderDetailResponse.getResponseStatus() != 0) {
                log.warn("get orderDetail from ordermng fail, channelOrderId:{}, tenantId:{}, channelId:{}, reason:{}",
                        virtualPhoneReq.getChannelOrderId(), tenantId, virtualPhoneReq.getChannelId(), orderDetailResponse.getMsg());
                return null;
            }
            return orderDetailResponse.order;
        } catch (Exception ex) {
            log.warn("get orderDetail from ordermng fail, channelOrderId:{}, tenantId:{}, channelId:{}, reason:{}",
                    virtualPhoneReq.getChannelOrderId(), tenantId, virtualPhoneReq.getChannelId(), ex.getMessage());
            return null;
        }
    }

    /**
     * 判断订单是否没超过联系期限
     *
     * @param orderOpLogList 订单状态流转
     * @return true-未超过；false-超过
     */
    private boolean isOrderCanCallUser(List<OrderOptLogVo> orderOpLogList, Long storeId, Long tenantId, Long employeeId) {
        // 若为空，肯定未超过联系期限
        if (CollectionUtils.isEmpty(orderOpLogList)) {
            return true;
        }
        // 获取订单取消/完成时间
        long cancelTime = 0, doneTime = 0;
        for (OrderOptLogVo ele: orderOpLogList) {
            if (OrderStatusEnum.CANCELED.getDesc().equals(ele.getOptContent())) {
                cancelTime = ele.getOptTime() / 1000;
            } else if (OrderStatusEnum.COMPLETED.getDesc().equals(ele.getOptContent())) {
                doneTime = ele.getOptTime() / 1000;
            }
        }

        long currTime = System.currentTimeMillis() / 1000;
        if (Lion.getConfigRepository().getBooleanValue("minus_canCallUser_time_switch", true) && isMinusPrivatePhoneTimeStore(tenantId, storeId) && isMinusPrivatePhoneTimePosition(tenantId, employeeId)) {
            log.info("enter 隐私号码入口时间缩短,employeeId:{}, storeId:{}", employeeId, storeId);
            if (cancelTime != 0 && (currTime - cancelTime) > MccDynamicConfigUtil.newContactUserDuration()) {
                return false;
            }
            return doneTime == 0 || (currTime - doneTime) <= MccDynamicConfigUtil.newContactUserDuration();
        }
        // 取消时间距离当前超过7天
        if (cancelTime != 0 && (currTime - cancelTime) > MccDynamicConfigUtil.contactUserDuration()) {
            return false;
        }
        // 完成时间距离当前超过7天
        return doneTime == 0 || (currTime - doneTime) <= MccDynamicConfigUtil.contactUserDuration();
    }

    /**
     * 判断订单是否没超过联系期限-默认1天
     * 参考：https://tt.sankuai.com/ticket/detail?id=309512761
     *
     * @param orderOpLogList 订单状态流转
     * @return true-未超过；false-超过
     */
    private boolean isOrderCanCallUser24Hour(List<OrderOptLogVo> orderOpLogList) {
        // 若为空，肯定未超过联系期限
        if (CollectionUtils.isEmpty(orderOpLogList)) {
            return true;
        }
        // 获取订单取消/完成时间
        long cancelTime = 0, doneTime = 0;
        for (OrderOptLogVo ele: orderOpLogList) {
            if (ele.getOptTime() == null) {
                continue;
            }
            if (OrderStatusEnum.CANCELED.getDesc().equals(ele.getOptContent())) {
                cancelTime = ele.getOptTime() / 1000;
                break;
            } else if (OrderStatusEnum.COMPLETED.getDesc().equals(ele.getOptContent())) {
                doneTime = ele.getOptTime() / 1000;
                break;
            }
        }

        long currTime = System.currentTimeMillis() / 1000;
        // 取消时间距离当前超过1天
        if (cancelTime != 0 && (currTime - cancelTime) > MccDynamicConfigUtil.contactUserDuration24Hour()) {
            return false;
        }
        // 完成时间距离当前超过1天
        return doneTime == 0 || (currTime - doneTime) <= MccDynamicConfigUtil.contactUserDuration24Hour();
    }

    /**
     * 判断订单是否没超过联系期限-默认24小时
     * 根据订单的完成时间或者取消时间（取最小值）进行判断，是否超过该时间后的24小时
     *
     * @param orderDetail 订单详情
     * @return true-超过；false-未超过
     */
    private boolean isOrderEndCanCallUserOver24Hour(OrderDetailVo orderDetail) {
        // 美团外卖渠道，根据订单的完成时间或者取消时间进行判断，是否超过该时间后的24小时
        if (Objects.isNull(orderDetail) || Objects.isNull(orderDetail.getOrderBaseDto()) || !Objects
                .equals(orderDetail.getOrderBaseDto().getChannelId(), DynamicChannelType.MEITUAN.getChannelId())) {
            return false;
        }
        OrderBaseVo orderBaseDto = orderDetail.getOrderBaseDto();
        // 订单取消时间和订单完成时间为空或为1000，说明订单未到终态，隐私号未过期，订单完成时候和取消时间默认值为1000，时间大于1000才是有值的
        if ((Objects.isNull(orderBaseDto.getCompleteTime()) || orderBaseDto.getCompleteTime() <= 1000)
                && (Objects.isNull(orderBaseDto.getCancelTime()) || orderBaseDto.getCancelTime() <= 1000)) {
            return false;
        }
        long currentTimeMillis = System.currentTimeMillis();
        Long endTimeMillis = Stream.of(orderBaseDto.getCompleteTime(), orderBaseDto.getCancelTime())
                .filter(Objects::nonNull).filter(time -> time > 1000).min(Long::compare).orElse(currentTimeMillis);
        long endTime = endTimeMillis / 1000;
        long currTime = currentTimeMillis / 1000;
        // 根据完成时间或者取消时间的最小时间判断是否超过24小时
        return (currTime - endTime) > MccDynamicConfigUtil.contactUserDuration24Hour();
    }

    public Boolean isMinusPrivatePhoneTimeStore(Long tenantId, Long storeId) {
        if (!MccConfigUtil.isDrunkHorseTenant(tenantId)) {
            return false;
        }
        try {
            log.info("隐私号判断校验门店;{}",storeId);
            List<Long> stores = Lion.getConfigRepository().getList("minus.private.phone.stores", Long.class, Lists.newArrayList());
            //全量逻辑
            if (stores.size() == 1 && stores.get(0).equals(-1L)) {
                return true;
            }
            return stores.contains(storeId);
        } catch (Exception e) {
            log.error("isMinusPrivatePhoneTimeStore error", e);
            return false;
        }
    }

    public Boolean isMinusPrivatePhoneTimePosition(Long tenantId, Long employeeId) {

        try {
            List<Long> configPositionIds = getMinusPrivatePhonePositionIds();
            EmployeeInfoInAllDepartmentsDetailV2Request req = new EmployeeInfoInAllDepartmentsDetailV2Request();
            req.setTenantId(tenantId);
            req.setEmployeeId(employeeId);
            EmployeeInfoInAllDepartmentsDetailV2Response response = departmentV2ThriftService.queryEmployeeInfoInAllDepartments(req);
            log.info("EmployeeInfoInAllDepartmentsDetailV2Response:{}", response);
            Long positionId = 0l;
            List<Long> positionIdList = new ArrayList<>();
            if(response != null && CollectionUtils.isNotEmpty(response.getEmployeeInfoList())) {
                positionIdList = response.getEmployeeInfoList().stream().filter(employeeInfoV2Dto -> employeeId.equals(employeeInfoV2Dto.getEmployeeId())).map(EmployeeInfoV2Dto::getPositionId).collect(Collectors.toList());
                log.info("positionIdList:{}", positionIdList);
            }
            if(CollectionUtils.isNotEmpty(positionIdList)) {
                positionId = positionIdList.get(0);
            }
            return configPositionIds.contains(positionId);
        } catch (Exception e) {
            log.error("isMinusPrivatePhoneTimePosition error", e);
            return false;
        }
    }

    /**
     * 获取缩短隐私号时效的岗位类别id列表
     * @return
     */
    public static List<Long> getMinusPrivatePhonePositionIds() {
        return getMinusPrivatePhonePositionIdsByName(Lion.getConfigRepository().getList("minus.private.position.list", String.class,
                Lists.newArrayList("前置仓-临时骑手", "前置仓-地推人员", "前置仓-店员", "前置仓-短班店员")));
    }

    /**
     * 根据岗位中文名查岗位id列表，岗位id线上线下不同所以使用lion
     * @param positionNames 岗位中文名已配到lion上
     * @return 岗位id列表
     */
    private static List<Long> getMinusPrivatePhonePositionIdsByName(List<String> positionNames) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(positionNames)) {
            return Lists.newArrayList();
        }
        Map<String, Long> positionMap = Lion.getConfigRepository().getMap("minus.private.position.id.map", Long.class);
        if (MapUtils.isEmpty(positionMap)) {
            return Lists.newArrayList();
        }
        return positionNames.stream()
                .map(positionMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }


    public BizOrderModel queryOfflineOrderModel(Integer orderBizType, String channelOrderId, Long tenantId)throws TException{
        BizOrderQueryRequest queryRequest = new BizOrderQueryRequest();
        queryRequest.setOrderBizType(orderBizType);
        queryRequest.setTenantId(tenantId);
        queryRequest.setOrderSource(OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue());
        queryRequest.setViewOrderId(channelOrderId);
        BizOrderQueryResponse response = bizOrderThriftService.query(queryRequest);
        if (response == null || response.getBizOrderModel() == null || response.getBizOrderModel().getOrderStatus() == null) {
            throw new TException("查询订单失败,order:" + channelOrderId + ",渠道:" + orderBizType);
        }
        return  response.getBizOrderModel();
    }

    /**
     * 从渠道查订单详情
     * @param channelOrderId
     * @param channelType
     * @param tenantId
     * @param storeId
     * @return
     * @throws TException
     */
    public ChannelOrderDetailDTO queryOnlineOrderDetail(String channelOrderId, DynamicChannelType channelType, long tenantId, Long storeId) throws TException {
        GetChannelOrderDetailRequest request = new GetChannelOrderDetailRequest();
        request.setChannelId(channelType.getChannelId());
        request.setOrderId(channelOrderId);
        request.setTenantId(tenantId);
        BizOrderModel orderModel = queryOfflineOrderModel(ChannelOrderConverter.convertChannelId2OrderBizType(channelType.getChannelId()), channelOrderId, tenantId);
        if (storeId != null) {
            request.setSotreId(storeId);
        } else {
            request.setSotreId(orderModel.getShopId());
        }
        boolean ocmsChannel = !Integer.valueOf(OrderSourceEnum.GLORY.getValue()).equals(orderModel.getOrderSource());
        GetChannelOrderDetailResult result = ocmsChannel ? channelOrderDockingThriftService.getChannelOrderDetail(request)
                : qnhOrderThriftService.getChannelOrderDetail(request);
        log.info("查询渠道订单详情：request={},result={}", request, result);

        if (result.getChannelOrderDetail() == null)
            throw new TException("查询线上订单信息失败,orderId:" + channelOrderId + ",渠道:" + channelType.getChannelId());
        return result.getChannelOrderDetail();
    }

    /**
     * 从 db 查订单详情
     * @param channelOrderId
     * @param channelId
     * @param tenantId
     * @param storeId
     * @return
     */
    public BizOrderModel queryOnlineOrderDetail(String channelOrderId, Integer channelId, long tenantId, Long storeId) {
        BizOrderQueryByViewOrderIdRequest request = new BizOrderQueryByViewOrderIdRequest();
        request.setTenantId(tenantId);
        request.setViewOrderId(channelOrderId);
        request.setOrderBizType(channelId);
        request.setShopId(storeId);
        try {
            BizOrderQueryResponse response = bizOrderThriftService.queryByViewOrderId(request);
            if (response == null || response.getBizOrderModel() == null
                    || response.getBizOrderModel().getOrderStatus() == null) {
                throw new BizException("查询订单失败,order:" + channelOrderId + ",渠道:" + channelId);
            }
            return response.getBizOrderModel();
        } catch (TException e) {
            throw new BizException("查下订单");
        }
    }

    public LogisticsStatusDTO queryLogisticsStatus(String channelOrderId, DynamicChannelType channelType, long tenantId) throws TException {
        BizOrderModel orderModel = queryOfflineOrderModel(ChannelOrderConverter.convertChannelId2OrderBizType(channelType.getChannelId()), channelOrderId, tenantId);
        if(Integer.valueOf(OrderSourceEnum.GLORY.getValue()).equals(orderModel.getOrderSource())){
            // 牵牛花订单详情不返回骑手信息、需要查询线下订单获取
            LogisticsStatusDTO logisticsStatusDTO = new LogisticsStatusDTO();
            logisticsStatusDTO.setOrderId(orderModel.getViewOrderId());
            logisticsStatusDTO.setStatus(orderModel.getDeliveryModel().getDeliveryStatus());
            logisticsStatusDTO.setRiderPhone(orderModel.getDeliveryModel().getRiderPhone());
            logisticsStatusDTO.setRiderName(orderModel.getDeliveryModel().getRiderName());
            return logisticsStatusDTO;
        }
        GetLogisticsStatusRequest request = new GetLogisticsStatusRequest();
        request.setChannelId(channelType.getChannelId());
        request.setOrderId(channelOrderId);
        request.setTenantId(tenantId);
        request.setStoreId(orderModel.getShopId());
        GetLogisticsStatusResult result = channelOrderDockingThriftService.getLogisticsStatus(request);
        log.info("查询渠道配送状态：request={},result={}", request, result);
        //解决部分原有渠道对接时，ocms channel未设置默认值导致NPE的问题
        if (Objects.isNull(result)) {
            return null;
        }
        return result.getLogisticsStatus();
    }

    public com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse<com.sankuai.shangou.qnh.orderapi.domain.response.app.QueryVirtualPhoneResponse> queryVirtualPhoneThroughChannel(com.sankuai.shangou.qnh.orderapi.domain.request.app.QueryVirtualPhoneRequest virtualPhoneReq) throws TException {
        com.sankuai.shangou.qnh.orderapi.domain.dto.app.User user = com.sankuai.shangou.qnh.orderapi.context.app.ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        long tenantId = user.getTenantId();
        //直接请求channel
        com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse<com.sankuai.shangou.qnh.orderapi.domain.response.app.QueryVirtualPhoneResponse> resp = com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse.fail(ResultCodeEnum.FAIL.code, ResultCodeEnum.FAIL.getErrorMessage(), null);
        if (virtualPhoneReq.getPhoneType() == QueryPhoneType.CUSTOMER.getValue()){
            OrderDetailVo orderDetail = getOrderDetailFromMng(virtualPhoneReq, tenantId, user, true);
            if (orderDetail == null || orderDetail.getOrderBaseDto()==null) {
                throw new BizException("获取订单详情失败");
            }
            // 属于歪马租户 && (全部门店使用AXB || 门店白名单包含当前门店)
            // 使用新逻辑
            if (com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil.getAxbPrivacyPhoneTenantIds().contains(String.valueOf(tenantId))
                    && (com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil.isAllUserAxBPrivacyPhone()
                    || com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil.getAxBPrivacyPhonePoiIds().contains(String.valueOf(virtualPhoneReq.getStoreId()))) ) {
                callCustomerThroughAxB(virtualPhoneReq, tenantId, resp, orderDetail);
                // 否则使用老逻辑
            } else {
                // 名酒馆订单平台配送-前端弹窗拦截
                if (isMtFamousTavernAndPlatformDelivery(orderDetail)) {
                    resp.setCode(ResultCode.SUCCESS.getCode());
                    resp.setMessage(ResultCode.SUCCESS.getErrorMessage());
                    com.sankuai.shangou.qnh.orderapi.domain.response.app.QueryVirtualPhoneResponse virtualPhoneResponse = new com.sankuai.shangou.qnh.orderapi.domain.response.app.QueryVirtualPhoneResponse();
                    virtualPhoneResponse.setShouldGetGatherPhone(true);
                    resp.setData(virtualPhoneResponse);
                    return resp;
                }
                //不使用入参的门店，改为使用订单的门店ID，防止转入单拨打顾客电话时的，前端传入转单后的门店ID，导致查询渠道失败
                virtualPhoneReq.setStoreId(orderDetail.getOrderBaseDto().getShopId());
                // 判断订单是否超过终态指定长度，若是不能联系用户
                int orderType = Objects.isNull(virtualPhoneReq.getOrderType()) ? 0 : virtualPhoneReq.getOrderType();
                // 非歪马租户，走新逻辑 --> 所有渠道，在订单已完结【已完成/已取消/已关闭】状态后的配置时间段内，才允许获取手机号联系顾客
                if (!MccConfigUtil.isDrunkHorseTenant(tenantId) && MccDynamicConfigUtil.checkLimitTimeContactUserTenant(tenantId)) {
                    return queryVirtualPhoneThroughChannel(orderDetail, resp, virtualPhoneReq, tenantId, orderType);
                }
                // 以下是历史逻辑
                if (!isOrderCanCallUser24Hour(orderDetail.getOrderOpLogList()) || OrderTypeEnum.AFTER_SALE_ORDER.getCode() == orderType
                        //根据订单终态时间判断订单的隐私号是否过期
                        || isOrderEndCanCallUserOver24Hour(orderDetail)) {
                    // 抖音查询线上详情不会给号码，直接走老逻辑
                    if (virtualPhoneReq.getChannelId() == DynamicChannelType.DOU_YIN.getChannelId()) {
                        callCustomerThroughChannel(virtualPhoneReq, tenantId, resp);
                        return resp;
                    }

                    if (MccConfigUtil.isQueryVirtualPhoneFromChannel(tenantId) &&  DynamicChannelType.findOf(virtualPhoneReq.getChannelId()) == DynamicChannelType.MEITUAN) {
                        queryOrderPrivacyPhone(orderDetail, resp, virtualPhoneReq.getAfterSaleId());
                        // 返回手机号带*，则返回获取失败错误提示
                        if(Objects.equals(resp.getCode(), ErrorCodeEnum.SUCCESS.getCode())
                                && OCMSUtils.isPhoneHasMusk(resp.getData().getPhoneNo())){
                            resp.setCode(ErrorCodeEnum.ORDER_CANT_BE_CONTACT_24_HOUR.getCode());
                            resp.setMessage(ErrorCodeEnum.ORDER_CANT_BE_CONTACT_24_HOUR.getMessage());
                        }
                    } else if (OrderTypeEnum.AFTER_SALE_ORDER.getCode() == orderType){
                        queryOrderPrivacyPhone(orderDetail, resp, virtualPhoneReq.getAfterSaleId());
                    } else{
                        resp.setCode(ErrorCodeEnum.ORDER_CANT_BE_CONTACT_24_HOUR.getCode());
                        resp.setMessage(ErrorCodeEnum.ORDER_CANT_BE_CONTACT_24_HOUR.getMessage());
                    }
                    return resp;
                }
                callCustomerThroughChannel(virtualPhoneReq, tenantId, resp);
            }
        }else if (virtualPhoneReq.getPhoneType() == QueryPhoneType.RIDER.getValue()){
            Integer orderBizType = com.sankuai.shangou.qnh.orderapi.converter.app.ChannelOrderConverter.convertChannelId2OrderBizType(virtualPhoneReq.getChannelId());
            //请求渠道网关，获取配送订单
            MetricHelper.build().name("ocms.query_virtual_phone.query").tag("type", "rider").tag("tenantId", String.valueOf(tenantId)).tag("channel", String.valueOf(virtualPhoneReq.getChannelId())).count();
            LogisticsStatusDTO logisticsStatusDTO = queryLogisticsStatus(virtualPhoneReq.getChannelOrderId(), DynamicChannelType.findOf(virtualPhoneReq.getChannelId()), tenantId);
            log.info("查询线上骑手电话,order:{},phone:{}", virtualPhoneReq.getChannelOrderId(), logisticsStatusDTO);
            String phone = null;
            if (logisticsStatusDTO != null){
                phone = logisticsStatusDTO.getRiderPhone();
            } else {
                //牵牛花/京东查不到配送状态，查询订单中的骑手信息
                QueryOrderDetailRequest queryOrderDetailRequest = new QueryOrderDetailRequest();
                queryOrderDetailRequest.setChannelOrderId(virtualPhoneReq.getChannelOrderId());
                queryOrderDetailRequest.setChannelId(virtualPhoneReq.getChannelId());
                BizOrderModel bizOrderModel = queryOfflineOrderModel(orderBizType, virtualPhoneReq.getChannelOrderId(), tenantId);
                phone = bizOrderModel.getDeliveryModel().getRiderPhone();
            }
            log.info("骑手电话,order:{},phone:{}", virtualPhoneReq.getChannelOrderId(), phone);
            if (StringUtils.isNotBlank(phone) && !com.sankuai.shangou.qnh.orderapi.utils.app.OCMSUtils.isPhoneHasMusk(phone)){
                com.sankuai.shangou.qnh.orderapi.domain.response.app.QueryVirtualPhoneResponse virtualPhoneResponse = new com.sankuai.shangou.qnh.orderapi.domain.response.app.QueryVirtualPhoneResponse();
                virtualPhoneResponse.setPhoneNo(phone);
                resp = com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse.success(virtualPhoneResponse);
                MetricHelper.build().name("ocms.query_virtual_phone.suc").tag("type", "rider").tag("tenantId", String.valueOf(tenantId)).tag("channel", String.valueOf(virtualPhoneReq.getChannelId())).count();
            }

            //如果是接入第三方配送订单，尝试直接从配送信息中获取
            if (resp.getData() == null || StringUtils.isEmpty(resp.getData().getPhoneNo())) {
                try {
                    BizOrderModel bizOrderModel = queryOfflineOrderModel(orderBizType, virtualPhoneReq.getChannelOrderId(), tenantId);
                    //接入了第三方配送的订单
                    if (bizOrderModel != null && bizOrderModel.getDeliveryModel() != null && StringUtils.isNotEmpty(bizOrderModel.getDeliveryModel().getChannelDeliveryId()) &&
                            StringUtils.isNotEmpty(bizOrderModel.getDeliveryModel().getRiderPhone())) {
                        log.info("此单为接入第三方配送订单");
                        com.sankuai.shangou.qnh.orderapi.domain.response.app.QueryVirtualPhoneResponse virtualPhoneResponse = new com.sankuai.shangou.qnh.orderapi.domain.response.app.QueryVirtualPhoneResponse();
                        virtualPhoneResponse.setPhoneNo(phone);
                        resp = com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse.success(virtualPhoneResponse);
                        log.info("此单为接入第三方配送订单 返回:{}", resp);
                        MetricHelper.build().name("ocms.query_virtual_phone.suc").tag("type", "rider").tag("tenantId", String.valueOf(tenantId)).tag("channel", String.valueOf(virtualPhoneReq.getChannelId())).count();
                        return resp;
                    }
                } catch (Exception e) {
                    log.warn("查询线下订单信息失败", e);
                }
            }
        } else if (virtualPhoneReq.getPhoneType() == QueryPhoneType.MT_FAMOUS_TAVERN_GATHER_PHONE.getValue()) {
            OrderDetailVo orderDetail = getOrderDetailFromMng(virtualPhoneReq, tenantId, user, true);
            if (Objects.isNull(orderDetail) || Objects.isNull(orderDetail.getOrderBaseDto())) {
                throw new BizException("获取订单详情失败");
            }
            String gatherPoiPhone = getGatherPhone(orderDetail);
            if (StringUtils.isNotBlank(gatherPoiPhone)) {
                com.sankuai.shangou.qnh.orderapi.domain.response.app.QueryVirtualPhoneResponse virtualPhoneResponse = new com.sankuai.shangou.qnh.orderapi.domain.response.app.QueryVirtualPhoneResponse();
                virtualPhoneResponse.setPhoneNo(gatherPoiPhone);
                resp.setData(virtualPhoneResponse);
                resp.setCode(ErrorCodeEnum.SUCCESS.getCode());
                resp.setMessage(ErrorCodeEnum.SUCCESS.getMessage());
            } else {
                resp.setCode(ErrorCodeEnum.QUERY_MT_FAMOUS_TAVERN_GATHER_PHONE_NOT_FIND.getCode());
                resp.setMessage(ErrorCodeEnum.QUERY_MT_FAMOUS_TAVERN_GATHER_PHONE_NOT_FIND.getMessage());
            }
        }
        log.info("[订单]查询手机号,req：{}, resp:{}", virtualPhoneReq, resp);
        return resp;
    }

    /**
     * pc端查询虚拟号
     * @param request
     * @return
     */
    public CommonFuseResponse<com.sankuai.shangou.qnh.orderapi.domain.response.pc.QueryVirtualPhoneResponse> queryVirtualPhoneThroughChannel(com.sankuai.shangou.qnh.orderapi.domain.request.pc.QueryVirtualPhoneRequest request) {
        CommonFuseResponse<com.sankuai.shangou.qnh.orderapi.domain.response.pc.QueryVirtualPhoneResponse> resp = CommonFuseResponse.fail(ResultCodeEnum.FAIL.code, ResultCodeEnum.FAIL.getErrorMessage(), null);
        try {
            OrderDetailVo orderDetail = getOrderDetailFromMng(request);
            if (orderDetail == null || orderDetail.getOrderBaseDto()==null) {
                return resp;
            }
            // 美团名酒馆平台配送处理重新获取隐私号进行错误提示
            if (isMtFamousTavernAndPlatformDelivery(orderDetail)) {
                String gatherPhone = getGatherPhone(orderDetail);
                if (StringUtils.isNotBlank(gatherPhone)) {
                    resp.setCode(ErrorCodeEnum.QUERY_MT_FAMOUS_TAVERN_GATHER_AFTER_SALES_PHONE_RESULT.getCode());
                    resp.setMessage(ErrorCodeEnum.QUERY_MT_FAMOUS_TAVERN_GATHER_AFTER_SALES_PHONE_RESULT.getMessage() + gatherPhone);
                } else {
                    resp.setCode(ErrorCodeEnum.QUERY_MT_FAMOUS_TAVERN_GATHER_PHONE_NOT_FIND.getCode());
                    resp.setMessage(ErrorCodeEnum.QUERY_MT_FAMOUS_TAVERN_GATHER_PHONE_NOT_FIND.getMessage());
                }
                return resp;
            }
            //不使用入参的门店，改为使用订单的门店ID，防止转入单拨打顾客电话时的，前端传入转单后的门店ID，导致查询渠道失败
            request.setStoreId(orderDetail.getOrderBaseDto().getShopId());
            request.setDispatchShopId(orderDetail.getOrderBaseDto().getDispatchShopId());
            // 判断订单是否超过终态指定长度，若是不能联系用户
            int orderType = Objects.isNull(request.getOrderType()) ? 0 : request.getOrderType();
            Long tenantId = orderDetail.getOrderBaseDto().getTenantId();
            // 非歪马租户，走新逻辑 --> 所有渠道，在订单已完结【已完成/已取消/已关闭】状态后的配置时间段内，才允许获取手机号联系顾客
            if (!MccConfigUtil.isDrunkHorseTenant(tenantId) && MccDynamicConfigUtil.checkLimitTimeContactUserTenant(tenantId)) {
                return queryVirtualPhoneThroughChannel(orderDetail, resp, request, tenantId, orderType);
            }
            // 以下是历史逻辑
            if (!isOrderCanCallUser24Hour(orderDetail.getOrderOpLogList()) || OrderTypeEnum.AFTER_SALE_ORDER.getCode() == orderType
                    //根据订单终态时间判断订单的隐私号是否过期
                || isOrderEndCanCallUserOver24Hour(orderDetail)) {
                // 抖音查询线上详情不会给号码，直接走老逻辑
                if (orderDetail.getOrderBaseDto().getChannelId() == DynamicChannelType.DOU_YIN.getChannelId()) {
                    callCustomerThroughChannel(request, resp);
                    return resp;
                }

                if (MccConfigUtil.isQueryVirtualPhoneFromChannel(orderDetail.getOrderBaseDto().getTenantId()) && (DynamicChannelType.findOf(request.getChannelId()) == DynamicChannelType.MEITUAN)){
                    queryOrderPrivacyPhone(orderDetail, resp, request.getAfterSaleId());
                    // 返回手机号带*，则返回获取失败错误提示
                    if(Objects.equals(resp.getCode(), ErrorCodeEnum.SUCCESS.getCode())
                            && OCMSUtils.isPhoneHasMusk(resp.getData().getPhoneNo())){
                        resp.setCode(ErrorCodeEnum.QUERY_ORDER_VIRTUAL_PHONE_FAIL.getCode());
                        resp.setMessage(ErrorCodeEnum.QUERY_ORDER_VIRTUAL_PHONE_FAIL.getMessage());
                    }
                } else if (OrderTypeEnum.AFTER_SALE_ORDER.getCode() == orderType) {
                    queryOrderPrivacyPhone(orderDetail, resp, request.getAfterSaleId());
                } else {
                    resp.setCode(ErrorCodeEnum.ORDER_CANT_BE_CONTACT_24_HOUR.getCode());
                    resp.setMessage(ErrorCodeEnum.ORDER_CANT_BE_CONTACT_24_HOUR.getMessage());
                }
                return resp;
            }
            callCustomerThroughChannel(request, resp);
        } catch (Exception e) {
            log.error("查询虚拟号失败", e);
        }
        return resp;
    }

    private void queryOrderPrivacyPhone(OrderDetailVo orderDetail, com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse<com.sankuai.shangou.qnh.orderapi.domain.response.app.QueryVirtualPhoneResponse> resp, String afterSaleId){
        OrderBaseVo orderBaseDto = orderDetail.getOrderBaseDto();
        QueryOrderPrivacyPhoneRequest request = QueryOrderPrivacyPhoneRequest.builder()
                .orderBizType(ChannelOrderConverter.convertChannelId2OrderBizType(orderBaseDto.getChannelId()))
                .shopId(orderBaseDto.getShopId())
                .tenantId(orderBaseDto.getTenantId())
                .shopId(orderBaseDto.getShopId())
                .viewOrderId(orderBaseDto.getChannelOrderId())
                .afterSaleId(afterSaleId)
                .build();
        try {
            QueryOrderPrivacyPhoneResponse response = ocmsOrderThriftService.queryOrderPrivacyPhone(request);
            com.sankuai.shangou.qnh.orderapi.domain.response.app.QueryVirtualPhoneResponse virtualPhoneResponse = new com.sankuai.shangou.qnh.orderapi.domain.response.app.QueryVirtualPhoneResponse();
            resp.setData(virtualPhoneResponse);
            if (response != null && response.getStatus().getCode() == 0) {
                resp.setCode(ErrorCodeEnum.SUCCESS.getCode());
                virtualPhoneResponse.setPhoneNo(response.getPhoneNo());
                resp.setData(virtualPhoneResponse);
                return;
            }
        } catch (TException e) {
            log.error("OcmsChannelRemoteService.queryOrderPrivacyPhone error", e);
        }
        resp.setCode(ErrorCodeEnum.QUERY_ORDER_VIRTUAL_PHONE_FAIL.getCode());
        resp.setMessage(ErrorCodeEnum.QUERY_ORDER_VIRTUAL_PHONE_FAIL.getMessage());
    }

    private void queryOrderPrivacyPhone(OrderDetailVo orderDetail, CommonFuseResponse<com.sankuai.shangou.qnh.orderapi.domain.response.pc.QueryVirtualPhoneResponse> resp, String afterSaleId){
        OrderBaseVo orderBaseDto = orderDetail.getOrderBaseDto();
        QueryOrderPrivacyPhoneRequest request = QueryOrderPrivacyPhoneRequest.builder()
                .orderBizType(ChannelOrderConverter.convertChannelId2OrderBizType(orderBaseDto.getChannelId()))
                .shopId(orderBaseDto.getShopId())
                .tenantId(orderBaseDto.getTenantId())
                .shopId(orderBaseDto.getShopId())
                .viewOrderId(orderBaseDto.getChannelOrderId())
                .afterSaleId(afterSaleId)
                .build();
        try {
            QueryOrderPrivacyPhoneResponse response = ocmsOrderThriftService.queryOrderPrivacyPhone(request);
            com.sankuai.shangou.qnh.orderapi.domain.response.pc.QueryVirtualPhoneResponse virtualPhoneResponse = new com.sankuai.shangou.qnh.orderapi.domain.response.pc.QueryVirtualPhoneResponse();
            resp.setData(virtualPhoneResponse);
            if (response != null && response.getStatus().getCode() == 0) {
                resp.setCode(ErrorCodeEnum.SUCCESS.getCode());
                virtualPhoneResponse.setPhoneNo(response.getPhoneNo());
                virtualPhoneResponse.setPoiId(orderBaseDto.getShopId());
                virtualPhoneResponse.setDispatchShopId(orderBaseDto.getDispatchShopId());
                resp.setData(virtualPhoneResponse);
                return;
            }
        } catch (TException e) {
            log.error("OcmsChannelRemoteService.queryOrderPrivacyPhone error", e);
        }
        resp.setCode(ErrorCodeEnum.QUERY_ORDER_VIRTUAL_PHONE_FAIL.getCode());
        resp.setMessage(ErrorCodeEnum.QUERY_ORDER_VIRTUAL_PHONE_FAIL.getMessage());
    }

    // pda侧查询手机号
    private void queryOrderPrivacyPhone(OrderDetailVo orderDetail, CommonResponse<QueryVirtualPhoneResponse> resp, String afterSaleId){
        OrderBaseVo orderBaseDto = orderDetail.getOrderBaseDto();
        QueryOrderPrivacyPhoneRequest request = QueryOrderPrivacyPhoneRequest.builder()
                .orderBizType(ChannelOrderConverter.convertChannelId2OrderBizType(orderBaseDto.getChannelId()))
                .shopId(orderBaseDto.getShopId())
                .tenantId(orderBaseDto.getTenantId())
                .shopId(orderBaseDto.getShopId())
                .viewOrderId(orderBaseDto.getChannelOrderId())
                .afterSaleId(afterSaleId)
                .build();
        try {
            QueryOrderPrivacyPhoneResponse response = ocmsOrderThriftService.queryOrderPrivacyPhone(request);
            QueryVirtualPhoneResponse virtualPhoneResponse = new QueryVirtualPhoneResponse();
            resp.setData(virtualPhoneResponse);
            if (response != null && response.getStatus().getCode() == 0) {
                resp.setCode(ErrorCodeEnum.SUCCESS.getCode());
                virtualPhoneResponse.setPhoneNo(response.getPhoneNo());
                resp.setData(virtualPhoneResponse);
                return;
            }
        } catch (TException e) {
            log.error("OcmsChannelRemoteService.queryOrderPrivacyPhone error", e);
        }
        resp.setCode(ErrorCodeEnum.QUERY_ORDER_VIRTUAL_PHONE_FAIL.getCode());
        resp.setMessage(ErrorCodeEnum.QUERY_ORDER_VIRTUAL_PHONE_FAIL.getMessage());
    }
    /**
     * 从ocms channel获取拨打号码
     * @param virtualPhoneReq 获取虚拟号请求体
     * @param tenantId 租户id
     * @param resp 请求体
     * @throws TException
     */
    private void callCustomerThroughChannel(com.sankuai.shangou.qnh.orderapi.domain.request.app.QueryVirtualPhoneRequest virtualPhoneReq, long tenantId,
                                            com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse<com.sankuai.shangou.qnh.orderapi.domain.response.app.QueryVirtualPhoneResponse> resp) throws TException {
        // 共享仓模式清除门店id
        if(Objects.equals(virtualPhoneReq.getEntityType(), PoiEntityTypeEnum.SHAREABLE_WAREHOUSE.code())){
            virtualPhoneReq.setStoreId(null);
        }
        MetricHelper.build().name("ocms.query_virtual_phone.query").tag("type", "customer")
                .tag("tenantId", String.valueOf(tenantId))
                .tag("channel", String.valueOf(virtualPhoneReq.getChannelId())).count();
        if (DynamicChannelType.findOf(virtualPhoneReq.getChannelId()) == DynamicChannelType.DOU_YIN) {
            //由于抖音解密接口经常失败，隐私号查询改为从订单数据里面取
            BizOrderModel bizOrderModel = queryOnlineOrderDetail(virtualPhoneReq.getChannelOrderId(),
                    virtualPhoneReq.getChannelId(), tenantId, virtualPhoneReq.getStoreId());
            if (bizOrderModel.getDeliveryModel() != null
                    && StringUtils.isNotBlank(bizOrderModel.getDeliveryModel().getUserPhone())) {
                com.sankuai.shangou.qnh.orderapi.domain.response.app.QueryVirtualPhoneResponse virtualPhoneResponse = new com.sankuai.shangou.qnh.orderapi.domain.response.app.QueryVirtualPhoneResponse();
                virtualPhoneResponse.setPhoneNo(bizOrderModel.getDeliveryModel().getUserPhone());
                resp.setCode(ErrorCodeEnum.SUCCESS.getCode());
                resp.setData(virtualPhoneResponse);
                MetricHelper.build().name("ocms.query_virtual_phone.suc").tag("type", "customer")
                        .tag("tenantId", String.valueOf(tenantId))
                        .tag("channel", String.valueOf(virtualPhoneReq.getChannelId())).count();
                return;
            }
        }
        ChannelOrderDetailDTO channelOrderDetailDTO = queryOnlineOrderDetail(virtualPhoneReq.getChannelOrderId(), DynamicChannelType.findOf(virtualPhoneReq.getChannelId()), tenantId, virtualPhoneReq.getStoreId());
        if (channelOrderDetailDTO != null && channelOrderDetailDTO.getDeliveryDetail().isUserPhoneIsValid()
                && !com.sankuai.shangou.qnh.orderapi.utils.app.OCMSUtils.isPhoneHasMusk(channelOrderDetailDTO.getDeliveryDetail().getUserPhone())){
            com.sankuai.shangou.qnh.orderapi.domain.response.app.QueryVirtualPhoneResponse virtualPhoneResponse = new com.sankuai.shangou.qnh.orderapi.domain.response.app.QueryVirtualPhoneResponse();
            virtualPhoneResponse.setPhoneNo(channelOrderDetailDTO.getDeliveryDetail().getUserPhone());
            // 如果是歪马渠道 && 使用了隐私号 && 当前城市隐私号未降级
            if (virtualPhoneReq.getChannelId().equals(ChannelTypeEnum.MT_DRUNK_HORSE.getValue())
                    && channelOrderDetailDTO.getDeliveryDetail().getUsePrivacyPhone() == NumberUtils.INTEGER_ONE
                    && StringUtils.isNotBlank(channelOrderDetailDTO.getDeliveryDetail().getUserPrivacyPhone())
                    && !channelOrderDetailDTO.isCityPrivacyDegrade()) {
                virtualPhoneResponse.setPhoneNo(channelOrderDetailDTO.getDeliveryDetail().getUserPrivacyPhone());
            }
            if (CollectionUtils.isNotEmpty(channelOrderDetailDTO.getDeliveryDetail().getBackUpUserPrivacyPhone())) {
                virtualPhoneResponse.setBackUpPhoneNo(channelOrderDetailDTO.getDeliveryDetail().getBackUpUserPrivacyPhone());
            }
            resp.setCode(ErrorCodeEnum.SUCCESS.getCode());
            resp.setData(virtualPhoneResponse);
            MetricHelper.build().name("ocms.query_virtual_phone.suc").tag("type", "customer").tag("tenantId", String.valueOf(tenantId)).tag("channel", String.valueOf(virtualPhoneReq.getChannelId())).count();
        }
    }


    /**
     * 从ocms channel获取拨打号码
     * @param virtualPhoneReq 获取虚拟号请求体
     * @param resp 请求体
     * @throws TException
     */
    private void callCustomerThroughChannel(com.sankuai.shangou.qnh.orderapi.domain.request.pc.QueryVirtualPhoneRequest virtualPhoneReq,
                                            CommonFuseResponse<com.sankuai.shangou.qnh.orderapi.domain.response.pc.QueryVirtualPhoneResponse> resp) throws TException {

        if (DynamicChannelType.findOf(virtualPhoneReq.getChannelId()) == DynamicChannelType.DOU_YIN) {
            //由于抖音解密接口经常失败，隐私号查询改为从订单数据里面取
            BizOrderModel bizOrderModel = queryOnlineOrderDetail(virtualPhoneReq.getChannelOrderId(),
                    virtualPhoneReq.getChannelId(), virtualPhoneReq.getTenantId(), virtualPhoneReq.getStoreId());
            if (bizOrderModel.getDeliveryModel() != null
                    && StringUtils.isNotBlank(bizOrderModel.getDeliveryModel().getUserPhone())) {
                com.sankuai.shangou.qnh.orderapi.domain.response.pc.QueryVirtualPhoneResponse virtualPhoneResponse = new com.sankuai.shangou.qnh.orderapi.domain.response.pc.QueryVirtualPhoneResponse();
                virtualPhoneResponse.setPhoneNo(bizOrderModel.getDeliveryModel().getUserPhone());
                virtualPhoneResponse.setPoiId(bizOrderModel.getShopId());
                virtualPhoneResponse.setDispatchShopId(Optional.ofNullable(bizOrderModel.getDispatchOrderExtDataModel())
                        .map(DispatchOrderExtDataModel::getDispatchShopId).orElse(null));
                resp.setCode(ErrorCodeEnum.SUCCESS.getCode());
                resp.setData(virtualPhoneResponse);
                return;
            }
        }
        ChannelOrderDetailDTO channelOrderDetailDTO = queryOnlineOrderDetail(virtualPhoneReq.getChannelOrderId(), DynamicChannelType.findOf(virtualPhoneReq.getChannelId()), virtualPhoneReq.getTenantId(), virtualPhoneReq.getStoreId());
        if (channelOrderDetailDTO != null && channelOrderDetailDTO.getDeliveryDetail().isUserPhoneIsValid()
                && !com.sankuai.shangou.qnh.orderapi.utils.app.OCMSUtils.isPhoneHasMusk(channelOrderDetailDTO.getDeliveryDetail().getUserPhone())){
            com.sankuai.shangou.qnh.orderapi.domain.response.pc.QueryVirtualPhoneResponse virtualPhoneResponse = new com.sankuai.shangou.qnh.orderapi.domain.response.pc.QueryVirtualPhoneResponse();
            virtualPhoneResponse.setPhoneNo(channelOrderDetailDTO.getDeliveryDetail().getUserPhone());
            virtualPhoneResponse.setPoiId(virtualPhoneReq.getStoreId());
            virtualPhoneResponse.setDispatchShopId(virtualPhoneReq.getDispatchShopId());
            // 如果是歪马渠道 && 使用了隐私号 && 当前城市隐私号未降级
            if (virtualPhoneReq.getChannelId().equals(ChannelTypeEnum.MT_DRUNK_HORSE.getValue())
                    && channelOrderDetailDTO.getDeliveryDetail().getUsePrivacyPhone() == NumberUtils.INTEGER_ONE
                    && StringUtils.isNotBlank(channelOrderDetailDTO.getDeliveryDetail().getUserPrivacyPhone())
                    && !channelOrderDetailDTO.isCityPrivacyDegrade()) {
                virtualPhoneResponse.setPhoneNo(channelOrderDetailDTO.getDeliveryDetail().getUserPrivacyPhone());
            }
            resp.setCode(ErrorCodeEnum.SUCCESS.getCode());
            resp.setData(virtualPhoneResponse);
        }
    }

    /**
     * 获取axb隐私号拨打号码
     * @param virtualPhoneReq 获取虚拟号请求体
     * @param tenantId 租户id
     * @param resp 请求体
     */
    private void callCustomerThroughAxB(com.sankuai.shangou.qnh.orderapi.domain.request.app.QueryVirtualPhoneRequest virtualPhoneReq, long tenantId,
                                        com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse<com.sankuai.shangou.qnh.orderapi.domain.response.app.QueryVirtualPhoneResponse> resp,
                                        OrderDetailVo orderDetail) throws TException {
        com.sankuai.shangou.qnh.orderapi.domain.dto.app.User user = com.sankuai.shangou.qnh.orderapi.context.app.ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        // 判断订单是否超过终态指定长度，若是不能联系用户
        if (!isOrderCanCallUser(orderDetail.getOrderOpLogList(), virtualPhoneReq.getStoreId(), user.getTenantId(), user.getEmployeeId())) {
            resp.setCode(ErrorCodeEnum.ORDER_CANT_BE_CONTACT.getCode());
            resp.setMessage(ErrorCodeEnum.ORDER_CANT_BE_CONTACT.getMessage());
            return;
        }
        // 如果没有使用隐私号，直接返回真实手机号
        OrderBaseVo orderBase = orderDetail.getOrderBaseDto();
        if (NumberUtils.INTEGER_ZERO.equals(orderBase.getUsePrivacyPhone())) {
            // 歪马订单，没有选中隐私号保护的订单，订单完成后24小时后隐藏手机号
            if (com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil.isDrunkHorseTenant(tenantId) && isOrderCompleteOverTime(orderBase)){
                resp.setCode(ErrorCodeEnum.ORDER_CANT_BE_CONTACT.getCode());
                resp.setMessage(ErrorCodeEnum.ORDER_CANT_BE_CONTACT.getMessage());
                return;
            }
            com.sankuai.shangou.qnh.orderapi.domain.response.app.QueryVirtualPhoneResponse virtualPhoneResponse = new com.sankuai.shangou.qnh.orderapi.domain.response.app.QueryVirtualPhoneResponse();
            virtualPhoneResponse.setPhoneNo(orderBase.getReceiverPhone());
            resp.setCode(ErrorCodeEnum.SUCCESS.getCode());
            resp.setData(virtualPhoneResponse);
            return;
        }

        // 使用了隐私号，去获取真实手机号
        String receiverPhone = null;
        if (virtualPhoneReq.getChannelId() == ChannelTypeEnum.MT_DRUNK_HORSE.getValue()) {
            receiverPhone = orderBase.getReceiverPhone();
        } else if (virtualPhoneReq.getChannelId() == ChannelTypeEnum.MEITUAN.getValue()) {
            receiverPhone = orderBase.getReceiverPrivacyPhone();
        }
        if (StringUtils.isBlank(receiverPhone)) {
            throw new BizException("订单收货号码为空");
        }
        // 获取百川登录账号手机号
        AccountInfoVo accountInfo = authRemoteService.getCurrentAccountWithoutPermission();
        if (accountInfo == null || StringUtils.isBlank(accountInfo.getMobile())) {
            throw new BizException("获取登录账号信息失败--账号或其手机号为空");
        }
        com.sankuai.shangou.qnh.orderapi.domain.response.app.QueryVirtualPhoneResponse data = new com.sankuai.shangou.qnh.orderapi.domain.response.app.QueryVirtualPhoneResponse();
        PrivacyPhoneResponse riderReceiverResponse = privacyNumberWrapper.applyAxB(orderBase, accountInfo.getMobile(), receiverPhone);
        // 获取隐私号失败--使用原逻辑返回拨打号码
        if (!riderReceiverResponse.getStatus().code.equals(StatusCodeEnum.SUCCESS.getCode())) {
            if(Objects.equals(riderReceiverResponse.getStatus().code, StatusCodeEnum.AUTH_INFO_NOT_AUTHORIZED.getCode())) {
                // 虚拟号请求返回未认证
                resp.setCode(ErrorCodeEnum.VIRTUAL_PHONE_AUTH_INFO_NOT_AUTHORIZED.getCode());
                resp.setMessage(ErrorCodeEnum.VIRTUAL_PHONE_AUTH_INFO_NOT_AUTHORIZED.getMessage());
            } else {
                callCustomerThroughChannel(virtualPhoneReq, tenantId, resp);
            }
            return;
        } else {
            data.setPhoneNo(riderReceiverResponse.getPrivacyPhone());
            data.setBackUpPhoneNo(riderReceiverResponse.getBackupPrivacyPhones());
        }
        String mtBindPhone = mtUserWrapper.getMtUserInfoByUserId(orderBase.getUserId()).getPhone();
        if (!receiverPhone.equals(mtBindPhone)) {
            // 调用AXB获取备用隐私号
            PrivacyPhoneResponse riderBindPhoneResponse = privacyNumberWrapper.applyAxB(orderBase, accountInfo.getMobile(), mtBindPhone);
            // 调用成功
            if (riderBindPhoneResponse.getStatus().code.equals(StatusCodeEnum.SUCCESS.getCode())) {
                data.setBindPhoneNo(riderBindPhoneResponse.getPrivacyPhone());
                data.setBindBackupPhoneNo(riderBindPhoneResponse.getBackupPrivacyPhones());
            }
        }
        resp.setCode(ErrorCodeEnum.SUCCESS.getCode());
        resp.setData(data);
    }

    /**
     * 查询美团名酒馆集合店的号码，按照售后电话>商家电话>自定义电话>售前电话顺序
     *
     * @param orderDetail
     * @return
     */
    public String getGatherPhone(OrderDetailVo orderDetail) {
        String gatherPoiPhone = null, gatherAfterSalesPhone = null, gatherCustomPhone = null, gatherPreSalePhone = null;
        if (Objects.nonNull(orderDetail.getDeliveryInfoDTO())
                && Objects.nonNull(orderDetail.getDeliveryInfoDTO().getExtData())) {
            DeliveryInfoExtend deliveryInfoExtend = orderDetail.getDeliveryInfoDTO().getExtData();
            gatherPoiPhone = deliveryInfoExtend.getGatherPoiPhone();
            gatherAfterSalesPhone = deliveryInfoExtend.getGatherAfterSalesPhone();
            gatherCustomPhone = deliveryInfoExtend.getGatherCustomPhone();
            gatherPreSalePhone = deliveryInfoExtend.getGatherPreSalePhone();
        }
        // 如果订单详情中的配送信息为空或者配送信息的扩展数据为空，则从渠道订单详情中获取
        if (Stream.of(gatherPoiPhone, gatherAfterSalesPhone, gatherCustomPhone, gatherPreSalePhone)
                .allMatch(StringUtils::isBlank)) {
            OrderBaseVo orderBaseDto = orderDetail.getOrderBaseDto();
            try {
                // 未保存时，从渠道的详情接口中获取
                ChannelOrderDetailDTO channelOrderDetailDTO = queryOnlineOrderDetail(orderBaseDto.getChannelOrderId(),
                        Optional.ofNullable(DynamicChannelType.findOf(orderBaseDto.getChannelId())).orElse(
                                DynamicChannelType.MEITUAN),
                        orderBaseDto.getTenantId(), orderBaseDto.getShopId());
                if (Objects.nonNull(channelOrderDetailDTO)
                        && Objects.nonNull(channelOrderDetailDTO.getDeliveryDetail())) {
                    OrderDeliveryDetailDTO deliveryDetail = channelOrderDetailDTO.getDeliveryDetail();
                    gatherPoiPhone = deliveryDetail.getGatherPoiPhone();
                    gatherAfterSalesPhone = deliveryDetail.getGatherAfterSalesPhone();
                    gatherCustomPhone = deliveryDetail.getGatherCustomPhone();
                    gatherPreSalePhone = deliveryDetail.getGatherPreSalePhone();
                }
            } catch (Exception e) {
                log.error("channelOrderId: {} getGatherPoiPhone error", orderBaseDto.getChannelOrderId(), e);
            }
        }
        // 设置电话类型取值的优先级依次为：售后电话>商家电话>自定义电话>售前电话
        return Stream.of(gatherAfterSalesPhone, gatherPoiPhone, gatherCustomPhone, gatherPreSalePhone)
                .filter(StringUtils::isNotBlank).findFirst().orElse(null);
    }

    /**
     * 从ordermng获取订单详情
     * @param virtualPhoneReq 请求
     * @param tenantId 租户ID
     * @param user 当前登录用户
     */
    private OrderDetailVo getOrderDetailFromMng(com.sankuai.shangou.qnh.orderapi.domain.request.app.QueryVirtualPhoneRequest virtualPhoneReq, long tenantId, com.sankuai.shangou.qnh.orderapi.domain.dto.app.User user, boolean useDbSearch) {
        OcmsOrderDetailReq orderDetailReq = new OcmsOrderDetailReq();
        orderDetailReq.setChannelId(virtualPhoneReq.getChannelId());
        orderDetailReq.setTenantId(tenantId);
        orderDetailReq.setChannelOrderId(virtualPhoneReq.getChannelOrderId());
        orderDetailReq.setOperator(user.getAccountId());
        orderDetailReq.setUseDbSearch(useDbSearch);
        try {
            OcmsOrderDetailResponse orderDetailResponse = ocmsOrderSearchService.orderDetail(orderDetailReq);
            if (orderDetailResponse.getResponseStatus() != 0) {
                log.warn("get orderDetail from ordermng fail, channelOrderId:{}, tenantId:{}, channelId:{}, reason:{}",
                        virtualPhoneReq.getChannelOrderId(), tenantId, virtualPhoneReq.getChannelId(), orderDetailResponse.getMsg());
                return null;
            }
            return orderDetailResponse.order;
        } catch (Exception ex) {
            log.warn("get orderDetail from ordermng fail, channelOrderId:{}, tenantId:{}, channelId:{}, reason:{}",
                    virtualPhoneReq.getChannelOrderId(), tenantId, virtualPhoneReq.getChannelId(), ex.getMessage());
            return null;
        }
    }

    /**
     * 从ordermng获取订单详情
     * @param virtualPhoneReq 请求
     */
    private OrderDetailVo getOrderDetailFromMng(com.sankuai.shangou.qnh.orderapi.domain.request.pc.QueryVirtualPhoneRequest virtualPhoneReq) {
        OcmsOrderDetailReq orderDetailReq = new OcmsOrderDetailReq();
        orderDetailReq.setChannelId(virtualPhoneReq.getChannelId());
        orderDetailReq.setTenantId(virtualPhoneReq.getTenantId());
        orderDetailReq.setChannelOrderId(virtualPhoneReq.getChannelOrderId());
        try {
            OcmsOrderDetailResponse orderDetailResponse = ocmsOrderSearchService.orderDetail(orderDetailReq);
            if (orderDetailResponse.getResponseStatus() != 0) {
                log.warn("get orderDetail from ordermng fail, channelOrderId:{}, channelId:{}, reason:{}",
                        virtualPhoneReq.getChannelOrderId(), virtualPhoneReq.getChannelId(), orderDetailResponse.getMsg());
                return null;
            }
            return orderDetailResponse.order;
        } catch (Exception ex) {
            log.warn("get orderDetail from ordermng fail, channelOrderId:{}, channelId:{}, reason:{}",
                    virtualPhoneReq.getChannelOrderId(), virtualPhoneReq.getChannelId(), ex.getMessage());
            return null;
        }
    }

    /**
     * 判断是否为美团名酒馆平台配送判断
     * 
     * @param orderDetail
     * @return
     */
    private boolean isMtFamousTavernAndPlatformDelivery(OrderDetailVo orderDetail) {
        return BooleanUtils.isTrue(orderDetail.getOrderBaseDto().getIsMtFamousTavern())
                && Objects.nonNull(orderDetail.getDeliveryInfoDTO())
                && Objects.nonNull(orderDetail.getDeliveryInfoDTO().getExtData())
                && Objects.equals(orderDetail.getDeliveryInfoDTO().getExtData().getIsSelfDelivery(), 0);
    }

    /**
     * 从ordermng获取订单详情
     * 
     * @param tenantId
     * @param channelId
     * @param channelOrderId
     * @return
     */
    public OrderDetailVo getOrderDetailFromMng(Long tenantId, Integer channelId, String channelOrderId) {
        OcmsOrderDetailReq orderDetailReq = new OcmsOrderDetailReq();
        orderDetailReq.setChannelId(channelId);
        orderDetailReq.setTenantId(tenantId);
        orderDetailReq.setChannelOrderId(channelOrderId);
        try {
            OcmsOrderDetailResponse orderDetailResponse = ocmsOrderSearchService.orderDetail(orderDetailReq);
            if (orderDetailResponse.getResponseStatus() != 0) {
                log.warn("get orderDetail from ordermng fail, channelOrderId:{}, channelId:{}, reason:{}",
                        channelOrderId, channelId, orderDetailResponse.getMsg());
                return null;
            }
            return orderDetailResponse.order;
        } catch (Exception e) {
            log.error("get orderDetail from ordermng fail, channelOrderId:{}, channelId:{}, reason:{}", channelOrderId,
                    channelId, e.getMessage());
            return null;
        }
    }

    /**
     * 退款审核和发起退款时，检查订单是否为美团名酒馆订单,并给出错误提示
     *
     * @param channelOrderId
     * @param tenantId
     * @param errorCodeEnum 名酒馆的退款审核错误提示信息和发起退款的审核提示信息
     * @return
     */
    public Pair<Boolean, Pair<Integer, String>> refundCheckMtFamousTavern(String channelOrderId, Long tenantId,
            ErrorCodeEnum errorCodeEnum, ErrorCodeEnum notFindError) {
        OrderDetailVo orderDetail = getOrderDetailFromMng(tenantId, DynamicChannelType.MEITUAN.getChannelId(),
                channelOrderId);
        if (Objects.isNull(orderDetail) || Objects.isNull(orderDetail.getOrderBaseDto())) {
            return new Pair<>(true, new Pair<>(ResultCode.FAIL.getCode(), "订单不存在"));
        }
        if (BooleanUtils.isFalse(orderDetail.getOrderBaseDto().getIsMtFamousTavern())) {
            return new Pair<>(false, new Pair<>(ResultCode.SUCCESS.getCode(), "订单不是美团名酒馆订单"));
        }
        // 查询集合店手机号，构建返回提示
        String gatherPhone = getGatherPhone(orderDetail);
        Pair<Integer, String> resultMsg;
        if (StringUtils.isNotBlank(gatherPhone)) {
            resultMsg = new Pair<>(errorCodeEnum.getCode(), errorCodeEnum.getMessage() + gatherPhone);
        } else {
            // 返回对应没有集合店的联系电话的错误提示
            resultMsg = new Pair<>(notFindError.getCode(), notFindError.getMessage());
        }
        return new Pair<>(true, resultMsg);
    }


}