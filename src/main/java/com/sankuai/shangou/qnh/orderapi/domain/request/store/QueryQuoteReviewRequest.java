package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.SkuStoreInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@TypeDoc(
        description = "查询提价审核默认操作请求参数"
)
@Data
@ApiModel("查询提价审核默认操作请求参数")
public class QueryQuoteReviewRequest {

    @FieldDoc(
            description = "查询的门店ID列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "查询的门店ID列表", required = true)
    @NotNull
    private List<Long> storeIds;

    @FieldDoc(
            description = "查询的商品列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "查询的商品列表", required = true)
    @NotNull
    private List<SkuStoreInfoVO> skuStoreInfos;

    @FieldDoc(
            description = "提价起始时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "提价起始时间", required = true)
    @NotNull
    private String quoteStartTime;

    @FieldDoc(
            description = "提价截止时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "提价截止时间", required = true)
    @NotNull
    private String quoteEndTime;

    @FieldDoc(
            description = "提价人名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "提价人名称", required = true)
    private String quoterName;

    @FieldDoc(
            description = "提价人电话", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "提价人电话", required = true)
    private String telephone;

    @FieldDoc(
            description = "提价记录所处的审核状态", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "提价记录所处的审核状态", required = true)
    @NotNull
    private List<Integer> reviewStatusList;

    @FieldDoc(
            description = "提价记录查询模式：0-全部，1-最新", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "提价记录查询模式：0-全部，1-最新", required = true)
    @NotNull
    private Integer queryModeType;

    @FieldDoc(
            description = "查询第几页", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "查询第几页", required = true)
    private Integer pageNum;

    @FieldDoc(
            description = "每页查询数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "每页查询数量", required = true)
    private Integer pageSize;
}

