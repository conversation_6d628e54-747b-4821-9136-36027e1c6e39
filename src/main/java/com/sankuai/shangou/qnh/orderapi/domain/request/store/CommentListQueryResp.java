package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.CommentVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.apache.commons.lang3.math.NumberUtils;
import org.assertj.core.util.Lists;
import com.sankuai.sgfulfillment.comment.thrift.common.PageInfoDTO;
import com.sankuai.sgfulfillment.comment.thrift.dto.model.ChannelCommentDTO;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@TypeDoc(
        description = "评价列表查询响应"
)
@Data
@ApiModel("评价列表查询响应")
public class CommentListQueryResp {

    @FieldDoc(
            description = "是否有更多数据", requiredness = Requiredness.REQUIRED
    )
    private boolean hasMore;

    @FieldDoc(
            description = "总条数", requiredness = Requiredness.REQUIRED
    )
    private Integer totalCount;

    @FieldDoc(
            description = "评价列表", requiredness = Requiredness.REQUIRED
    )
    private List<CommentVO> commentVOList;

    public static CommentListQueryResp build(List<ChannelCommentDTO> commentDTOList, PageInfoDTO pageInfo) {
        CommentListQueryResp resp = new CommentListQueryResp();
        resp.setTotalCount(pageInfo.getTotalSize());
        resp.setHasMore(pageInfo.getTotalPage() > pageInfo.getPage());
        List<CommentVO> commentVOList = Optional.ofNullable(commentDTOList).map(List::stream).orElse(Stream.empty())
                .map(commentDTO -> CommentVO.build(commentDTO)).collect(Collectors.toList());
        resp.setCommentVOList(commentVOList);
        return resp;
    }

    public static CommentListQueryResp buildEmptyResp() {
        CommentListQueryResp resp = new CommentListQueryResp();
        resp.setTotalCount(NumberUtils.INTEGER_ZERO);
        resp.setHasMore(Boolean.FALSE);
        resp.setCommentVOList(Lists.newArrayList());
        return resp;
    }

}
