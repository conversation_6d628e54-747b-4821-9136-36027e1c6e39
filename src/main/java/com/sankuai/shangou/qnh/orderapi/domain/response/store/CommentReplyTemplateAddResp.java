package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@TypeDoc(
        description = "添加评价回复模板响应",
        authors = "hejunliang"
)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CommentReplyTemplateAddResp {

    @FieldDoc(
            description = "模板id", requiredness = Requiredness.OPTIONAL
    )
    private Long templateId;
}
