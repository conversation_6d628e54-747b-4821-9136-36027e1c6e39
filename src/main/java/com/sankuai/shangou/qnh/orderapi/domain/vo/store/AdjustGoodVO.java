package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TypeDoc(
        description = "调价商品"
)
@Data
public class AdjustGoodVO {
    @FieldDoc(
            description = "skuId"
    )
    @ApiModelProperty(value = "skuId")
    private String skuId;
    @FieldDoc(
            description = "sku名"
    )
    @ApiModelProperty(value = "sku名")
    private String skuName;
    @FieldDoc(
            description = "价格"
    )
    @ApiModelProperty(value = "价格")
    private String price;
    @FieldDoc(
            description = "单位"
    )
    @ApiModelProperty(value = "单位")
    private String unit;
    @FieldDoc(
            description = "是否需要称重"
    )
    @ApiModelProperty(value = "是否需要称重")
    private Boolean needWeight;
    @FieldDoc(
            description = "状态，1:可调价商品，2:不可调价商品"
    )
    @ApiModelProperty(value = "状态，1:可调价商品，2:不可调价商品")
    private Integer status;
    @FieldDoc(
            description = "错误信息"
    )
    @ApiModelProperty(value = "错误信息")
    private String errorMsg;
}
