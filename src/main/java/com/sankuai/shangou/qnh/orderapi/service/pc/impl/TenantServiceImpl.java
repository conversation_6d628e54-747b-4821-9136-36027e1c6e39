package com.sankuai.shangou.qnh.orderapi.service.pc.impl;

import com.google.common.base.Preconditions;
import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.shangou.saas.tenant.thrift.ConfigThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.StatusCodeEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.ConfigItemEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.TenantBusinessModeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.config.request.ConfigQueryRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.config.response.TenantConfigResponse;
import com.sankuai.shangou.qnh.orderapi.context.app.ApiMethodParamThreadLocal;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.ChannelInfoBo;
import com.sankuai.shangou.qnh.orderapi.domain.dto.app.IdentityInfo;
import com.sankuai.shangou.qnh.orderapi.domain.dto.app.User;
import com.sankuai.shangou.qnh.orderapi.enums.pc.infrastructure.ChannelTypeEnum;
import com.sankuai.shangou.qnh.orderapi.remote.AuthRemoteService;
import com.sankuai.shangou.qnh.orderapi.remote.TenantRemoteService;
import com.sankuai.shangou.qnh.orderapi.service.pc.TenantService;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/8 16:41
 * @Description:
 */
@Service
@Slf4j
public class TenantServiceImpl implements TenantService {

    @Autowired
    private TenantRemoteService tenantRemoteService;

    @Autowired
    private ConfigThriftService configThriftService;

    @Autowired
    private AuthRemoteService authRemoteService;

    private static final Map<String, List<Integer>> ALLOW_CHANNELS;

    static {
        ALLOW_CHANNELS = new HashMap<>();

        ALLOW_CHANNELS.put("PROMOTION", Arrays.asList(ChannelTypeEnum.MEITUAN.getCode(), ChannelTypeEnum.JD2HOME.getCode()));
    }

    @Override
    public List<ChannelInfoBo> queryChannels(Long tenantId, String module) {
        List<ChannelInfoBo> channels = tenantRemoteService.queryChannels(tenantId);

        if (StringUtils.isNotEmpty(module) && ALLOW_CHANNELS.containsKey(module) && CollectionUtils.isNotEmpty(channels)) {
            return channels.stream().filter(c -> ALLOW_CHANNELS.get(module).contains(c.getChannelId())).collect(Collectors.toList());
        }

        return channels;
    }

    @Override
    public List<ChannelInfoBo> queryChannels(Long tenantId) {
        return tenantRemoteService.queryChannels(tenantId);
    }

    @Override
    public Boolean queryTenantHasErp(Long tenantId) {
        try {

            ConfigQueryRequest request = new ConfigQueryRequest();
            request.setTenantId(tenantId);
            request.setConfigId(ConfigItemEnum.HAS_ERP.getKey());
            request.setSubjectId(tenantId);
            TenantConfigResponse response = configThriftService.queryTenantConfig(request);

            if (response.getStatus() != null && response.getStatus().getCode() == StatusCodeEnum.SUCCESS.getCode() && response.getConfig() != null && response.getConfig().getConfigContent() != null) {
                final Map<String, String> configMap = JacksonUtils.parseMap(response.getConfig().getConfigContent(), String.class, String.class);
                return configMap.getOrDefault(ConfigItemEnum.HAS_ERP.getMainConfigKey(), "NO").equals("YES");
            }
        } catch (Exception e) {
            log.error("queryTenantHasErp error", e);
            return Boolean.FALSE;
        }

        return Boolean.FALSE;
    }

    @Override
    public boolean isSuperMarketMode(Long tenantId) {
        Preconditions.checkNotNull(tenantId, "租户ID不能为空");
        TenantBusinessModeEnum tenantBizMode = tenantRemoteService.getTenantBizMode(tenantId);
        return tenantBizMode == TenantBusinessModeEnum.SUPER_MARKET;
    }

    @Override
    public TenantBusinessModeEnum queryTenantBusinessMode(Long tenantId) {
        Preconditions.checkNotNull(tenantId, "租户ID不能为空");
        return tenantRemoteService.getTenantBizMode(tenantId);
    }

    @Override
    public Map<Integer, Boolean> queryAccountCode() {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        try {
            Map<String, Integer> permissionCodeMap = fetchPermissionFromConfig();
            if (identityInfo != null
                    && identityInfo.getUser() != null
                    && permissionCodeMap != null
                    && permissionCodeMap.size() > 0
                    && identityInfo.getStoreIdList().size() == 1){
                User user = identityInfo.getUser();
                long storeId = identityInfo.getStoreId();
                long accountId = user.getAccountId();
                Map<String, Boolean> authMap = authRemoteService.authPermissionAndDataAuth(accountId, storeId, Lists.newArrayList(permissionCodeMap.keySet()));
                log.info("请求订单元素权限：{}， result:{}", accountId, authMap);
                if(MapUtils.isEmpty(authMap)){
                    return Maps.newHashMap();
                }
                return authMap.entrySet().stream().collect(Collectors.toMap(entry->permissionCodeMap.get(entry.getKey()), entry->entry.getValue(), (f, s)->s));
            }
        }catch (Exception e){
            log.error("查询权限元素权限失败", e);
        }
        log.info("没有配置待检查的元素权限");
        return Maps.newHashMap();
    }

    private Map<String, Integer> fetchPermissionFromConfig() {
        String permissionCodes = MccConfigUtil.getOrderOperatorAuthCodes();
        if (StringUtils.isNotBlank(permissionCodes)){
            /**
             * 解析  ACCEPT_ORDER,1;REFUND,2;.....
             * ***/
            List<String> permissionMap = Splitter.on(";").splitToList(permissionCodes);
            return permissionMap.stream().map(e-> Splitter.on(",").splitToList(e)).filter(e->e != null && e.size() >= 2).collect(Collectors.toMap(e->e.get(0), e-> NumberUtils.toInt(e.get(1)), (f, s)->s));
        }
        return Maps.newHashMap();
    }
}
