package com.sankuai.shangou.qnh.orderapi.service.pc.impl;

import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.meituan.shangou.saas.dto.StatusCodeEnum;
import com.meituan.shangou.saas.order.platform.client.dto.request.OrderDetailReportRequest;
import com.meituan.shangou.saas.order.platform.client.dto.response.FulfillDetailReportResponse;
import com.meituan.shangou.saas.order.platform.client.service.report.OrderFulfillmentReportThriftService;
import com.meituan.shangou.saas.tenant.thrift.ChannelPoiManageThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.PoiDetailInfoDTO;
import com.meituan.shangou.saas.tenant.thrift.dto.channelpoi.response.PoiDetailInfosResponse;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.OrderDetailReportReq;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.OrderFulfillmentReportReq;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.FulfillReportDetailResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.OrderFulfillmentReportQueryResp;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.AfsOrderCompleteDetail;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.ChannelShopInfo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.OrderFulfillmentReportVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.PageInfoVO;
import com.sankuai.shangou.qnh.orderapi.enums.pc.ReportSortKeyEnum;
import com.sankuai.shangou.qnh.orderapi.service.pc.OrderReportService;
import com.sankuai.shangou.qnh.orderapi.service.pc.asyncQuery.report.OrderReportAsyncService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OrderReportServiceImpl implements OrderReportService {
    @Resource
    private List<OrderReportAsyncService> orderReportAsyncServiceList;

    @Resource
    private ChannelPoiManageThriftService channelPoiManageThriftService;

    @Resource
    private OrderFulfillmentReportThriftService orderFulfillmentReportThriftService;

    private static ThreadPool asyncQueryReportThreadPool = Rhino.newThreadPool("async_query_report_thread_pool", DefaultThreadPoolProperties.Setter().withCoreSize(10).withMaxSize(10));

    @Override
    public OrderFulfillmentReportQueryResp asyncQueryReport(OrderFulfillmentReportReq request, List<Long> poiIds, List<Long> warehouseIds) {
        OrderFulfillmentReportQueryResp result = new OrderFulfillmentReportQueryResp();
        List<Future<OrderFulfillmentReportQueryResp>> futureList = Lists.newArrayList();
        if(CollectionUtils.isEmpty(poiIds)&&CollectionUtils.isEmpty(warehouseIds)){
            log.error("OrderReportServiceImpl#asyncQueryReport:{}", "门店IDList和仓IDList不能同时为空");
            return result;

        }
        for (OrderReportAsyncService orderReportAsyncService : orderReportAsyncServiceList) {
            try {
                Future<OrderFulfillmentReportQueryResp> future = asyncQueryReportThreadPool.submit(() -> orderReportAsyncService.asyncQueryReport(request, poiIds, warehouseIds));
                futureList.add(future);
            } catch (Exception e) {
                log.error("OrderReportServiceImpl#asyncQueryReport", e);
            }

        }
        for (Future<OrderFulfillmentReportQueryResp> future : futureList) {
            try {
                OrderFulfillmentReportQueryResp resp = future.get();
                if (resp != null && CollectionUtils.isNotEmpty(resp.getOrderFulfillmentDetailReportList())) {
                    handleResp(result, resp);
                }
            } catch (Exception e) {
                log.error("OrderReportServiceImpl#asyncQueryReport-result", e);
//                throw new RuntimeException(e);
            }

        }

        //合并总和数据and 合并仓数据
        mergeTotalDataAndWarehouseData(result, request);
        //根据排序字段进行排序
        List<OrderFulfillmentReportVO> orderFulfillmentDetailReportList = result.getOrderFulfillmentDetailReportList();
        if (CollectionUtils.isEmpty(orderFulfillmentDetailReportList)) {
            return result;
        }
        Collections.sort(orderFulfillmentDetailReportList, new Comparator<OrderFulfillmentReportVO>() {
            @Override
            public int compare(OrderFulfillmentReportVO o1, OrderFulfillmentReportVO o2) {
                if (ReportSortKeyEnum.getByType(request.getSortKey()) == null || ReportSortKeyEnum.getByType(request.getSortKey()) == ReportSortKeyEnum.ORDER) {
                    return Integer.compare(o2.getOrderCount(), o1.getOrderCount());
                }else if (ReportSortKeyEnum.getByType(request.getSortKey()) == ReportSortKeyEnum.DEALING_ORDER) {
                    return Integer.compare(o2.getDealingOrderCount(), o1.getDealingOrderCount());
                }else if (ReportSortKeyEnum.getByType(request.getSortKey()) == ReportSortKeyEnum.COMMENT_EXCEPTION) {
                    return Integer.compare(o2.getBadCommentUnReplyCount(), o1.getBadCommentUnReplyCount());
                } else if (ReportSortKeyEnum.getByType(request.getSortKey()) == ReportSortKeyEnum.PICK_EXCEPTION) {
                    return Integer.compare(o2.getPickTimeoutCount() + o2.getRiderReachUnpickCount(), o1.getPickTimeoutCount() + o1.getRiderReachUnpickCount());
                } else if (ReportSortKeyEnum.getByType(request.getSortKey()) == ReportSortKeyEnum.DELIVERY_EXCEPTION) {
                    return Integer.compare(o2.getRiderReceiveTimeoutCount(), o1.getRiderReceiveTimeoutCount());
                } else if (ReportSortKeyEnum.getByType(request.getSortKey()) == ReportSortKeyEnum.EXCEPTION) {
                    return Integer.compare(o2.getBadCommentUnReplyCount() + o2.getPickTimeoutCount() + o2.getRiderReachUnpickCount() + o2.getRiderReceiveTimeoutCount() + o2.getAfsOrderDealingCount()
                            , o1.getBadCommentUnReplyCount() + o1.getPickTimeoutCount() + o1.getRiderReachUnpickCount() + o1.getRiderReceiveTimeoutCount() + o1.getAfsOrderDealingCount());
                }
                return Integer.compare(o2.getOrderCount(), o1.getOrderCount());
            }
        });
        //根据分页参数进行分特参数填充并截取返回数据
        OrderFulfillmentReportQueryResp reportQueryResp = fillPageInfoAndCut(result, request);
        if (request.getFilterType() == 0 && CollectionUtils.isNotEmpty(reportQueryResp.getOrderFulfillmentDetailReportList())) {
            addChannelShopInfo(reportQueryResp, request);
        }

        return reportQueryResp;
    }

    @Override
    public FulfillReportDetailResponse queryReportOrderDetail(OrderDetailReportReq request) {
        FulfillReportDetailResponse response = new FulfillReportDetailResponse();
        OrderDetailReportRequest reportRequest  = new OrderDetailReportRequest();
        reportRequest.setShopId(request.getShopId());
        reportRequest.setWareHouseId(request.getWarehouseId());
        reportRequest.setFilterType(request.getExceptionType());
        reportRequest.setTenantId(request.getTenantId());
        try {
            FulfillDetailReportResponse fulfillDetailReportResponse = orderFulfillmentReportThriftService.queryOrderDetailReport(reportRequest);
            if(fulfillDetailReportResponse.getStatus().getCode()!= StatusCodeEnum.SUCCESS.getCode()){
               throw new RuntimeException("查询数据异常");
            }
            response.setViewOrderIdList(fulfillDetailReportResponse.getViewOrderIdList());

        } catch (TException e) {
            throw new RuntimeException("查询数据异常");
        }
        return response;
    }

    private void addChannelShopInfo(OrderFulfillmentReportQueryResp reportQueryResp, OrderFulfillmentReportReq request) {
        Map<Long, OrderFulfillmentReportVO> shopMap =
                reportQueryResp.getOrderFulfillmentDetailReportList()
                        .stream()
                        .collect(Collectors.toMap(OrderFulfillmentReportVO::getShopId, Function.identity(),(existing, replacement) -> existing));
        PoiDetailInfosResponse poiDetailInfosResponse = channelPoiManageThriftService.queryPoiDetailInfoByPoiIds(request.getTenantId(), new ArrayList<>(shopMap.keySet()));
        if (poiDetailInfosResponse.getStatus().getCode() != StatusCodeEnum.SUCCESS.getCode() || CollectionUtils.isEmpty(poiDetailInfosResponse.getPoiDetailInfoDTOs())) {
            return;
        }
        for (PoiDetailInfoDTO poiDetailInfoDTO : poiDetailInfosResponse.getPoiDetailInfoDTOs()) {
            OrderFulfillmentReportVO reportVO = shopMap.get(poiDetailInfoDTO.getPoiId());
            reportVO.setShopName(poiDetailInfoDTO.getPoiName());
            reportVO.setOutShopId(poiDetailInfoDTO.getOutPoiId());
            List<ChannelShopInfo> channelShopInfoList = poiDetailInfoDTO.getChannelPoiInfoList().stream().map(dto -> {
                ChannelShopInfo shopInfo = new ChannelShopInfo();
                shopInfo.setChannel(dto.getChannelId().toString());
                shopInfo.setChannelName(dto.getChannelName());
                shopInfo.setChannelPoiId(dto.getChannelPoiId());
                shopInfo.setChannelPoiName(dto.getChannelPoiName());
                shopInfo.setChannelPoiStatus(dto.getBizStatus());
                return shopInfo;
            }).collect(Collectors.toList());
            reportVO.setChannelShopInfoList(channelShopInfoList);
        }
        List<OrderFulfillmentReportVO> reportVOList = Lists.newArrayList();
        for (OrderFulfillmentReportVO reportVO : reportQueryResp.getOrderFulfillmentDetailReportList()) {
            reportVO = shopMap.get(reportVO.getShopId());
            reportVOList.add(reportVO);
        }
        reportQueryResp.setOrderFulfillmentDetailReportList(reportVOList);


    }

    private OrderFulfillmentReportQueryResp fillPageInfoAndCut(OrderFulfillmentReportQueryResp result, OrderFulfillmentReportReq request) {
        PageInfoVO pageInfoVO = new PageInfoVO();
        if (CollectionUtils.isEmpty(result.getOrderFulfillmentDetailReportList())) {
            pageInfoVO.setPage(request.getPage());
            pageInfoVO.setSize(request.getPageSize());
            pageInfoVO.setTotalPage(0);
            pageInfoVO.setTotalSize(0);
            result.setPageInfo(pageInfoVO);
            return result;
        }
        int total = result.getOrderFulfillmentDetailReportList().size();
        pageInfoVO.setTotalSize(total);
        pageInfoVO.setSize(request.getPageSize());
        pageInfoVO.setTotalPage(((total - 1) / request.getPageSize()) + 1);
        if (request.getPage() < 1) {
            request.setPage(1);
        }
        pageInfoVO.setPage(request.getPage());
        result.setPageInfo(pageInfoVO);
        if (CollectionUtils.isEmpty(result.getOrderFulfillmentDetailReportList())) {
            return result;
        }
        int start = (request.getPage() - 1) * request.getPageSize();
        int end = Math.min((start + request.getPageSize()), total);
        List<OrderFulfillmentReportVO> reportVOList = result.getOrderFulfillmentDetailReportList().subList(start, end);
        result.setOrderFulfillmentDetailReportList(reportVOList);
        return result;
    }

    private void mergeTotalDataAndWarehouseData(OrderFulfillmentReportQueryResp result, OrderFulfillmentReportReq request) {
        if (CollectionUtils.isEmpty(result.getOrderFulfillmentDetailReportList())) {
            return;
        }
        //计算总单量
        int totalOrderCount = 0;
        //计算总处理单量
        int totalDealingOrderCount = 0;
        //待处理退单
        int totalDealingAfsOrderCount = 0;
        //未回复差评
        int totalNoReplyBadCommentCount = 0;

        List<OrderFulfillmentReportVO> warehouseList = Lists.newArrayList();
        if (request.getFilterType() == 1) {
            Map<Long, List<OrderFulfillmentReportVO>> warehouseMap = result.getOrderFulfillmentDetailReportList().stream().filter(vo-> vo.getWarehouseId()!=null).collect(Collectors.groupingBy(OrderFulfillmentReportVO::getWarehouseId));
            for (Map.Entry<Long, List<OrderFulfillmentReportVO>> entry : warehouseMap.entrySet()) {
                List<OrderFulfillmentReportVO> list = entry.getValue();
                if (CollectionUtils.isNotEmpty(list)) {
                    OrderFulfillmentReportVO mergedVO = new OrderFulfillmentReportVO();
                    mergedVO.setWarehouseId(entry.getKey());
                    mergedVO.setWarehouseName(list.get(0).getWarehouseName());
                    for (OrderFulfillmentReportVO vo : list) {
                        if (vo != null) {
                            totalOrderCount += vo.getOrderCount();
                            totalDealingOrderCount += vo.getDealingOrderCount();
                            totalDealingAfsOrderCount += vo.getAfsOrderDealingCount();
                            totalNoReplyBadCommentCount += vo.getBadCommentUnReplyCount();
                            addTwoOrderFulfillmentReportVO(vo, mergedVO);

                        }
                    }
                    if(mergedVO.getTotalCommentCount()==0){
                        mergedVO.setBadCommentRatio("0");
                    }else{
                        mergedVO.setBadCommentRatio(BigDecimal.valueOf(mergedVO.getBadCommentCount()).divide(BigDecimal.valueOf(mergedVO.getTotalCommentCount()), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).setScale(2,RoundingMode.HALF_UP).toString());

                    }

                    warehouseList.add(mergedVO);
                }

            }
            result.setOrderFulfillmentDetailReportList(warehouseList);
        } else {
            for (OrderFulfillmentReportVO reportVO : result.getOrderFulfillmentDetailReportList()) {
                totalOrderCount += reportVO.getOrderCount();
                totalDealingOrderCount += reportVO.getDealingOrderCount();
                totalDealingAfsOrderCount += reportVO.getAfsOrderDealingCount();
                totalNoReplyBadCommentCount += reportVO.getBadCommentUnReplyCount();

            }
        }
        result.setTotalOrderCount(totalOrderCount);
        result.setTotalDealingOrderCount(totalDealingOrderCount);
        result.setTotalDealingAfsOrderCount(totalDealingAfsOrderCount);
        result.setTotalNoReplyBadCommentCount(totalNoReplyBadCommentCount);


    }

    public OrderFulfillmentReportVO addTwoOrderFulfillmentReportVO(OrderFulfillmentReportVO vo1, OrderFulfillmentReportVO result) {
        // 总单量
        result.setOrderCount(vo1.getOrderCount() + result.getOrderCount());
        // 履约中订单
        result.setDealingOrderCount(vo1.getDealingOrderCount() + result.getDealingOrderCount());
        // 已完成
        result.setCompleteOrderCount(vo1.getCompleteOrderCount() + result.getCompleteOrderCount());
        // 已取消
        result.setCancelOrderCount(vo1.getCancelOrderCount() + result.getCancelOrderCount());
        // 待拣货
        result.setUnPickOrderCount(vo1.getUnPickOrderCount() + result.getUnPickOrderCount());
        // 待领取
        result.setWaitReceiveOrderCount(vo1.getWaitReceiveOrderCount() + result.getWaitReceiveOrderCount());
        // 拣货中
        result.setPickingOrderCount(vo1.getPickingOrderCount() + result.getPickingOrderCount());
        // 拣货超时单数
        result.setPickTimeoutCount(vo1.getPickTimeoutCount() + result.getPickTimeoutCount());
        // 领取超时
        result.setReceivePickTimeoutCount(vo1.getReceivePickTimeoutCount() + result.getReceivePickTimeoutCount());
        // 骑手到店未拣货
        result.setRiderReachUnpickCount(vo1.getRiderReachUnpickCount() + result.getRiderReachUnpickCount());
        // 交付中
        result.setUndelivered(vo1.getUndelivered() + result.getUndelivered());
        // 拣货完成待配送
        result.setPickCompleteCount(vo1.getPickCompleteCount() + result.getPickCompleteCount());
        // 配送中
        result.setDelivering(vo1.getDelivering() + result.getDelivering());
        // 超时无骑手接单
        result.setRiderReceiveTimeoutCount(vo1.getRiderReceiveTimeoutCount() + result.getRiderReceiveTimeoutCount());
        // 差评未回复数量
        result.setBadCommentUnReplyCount(vo1.getBadCommentUnReplyCount() + result.getBadCommentUnReplyCount());
        //评论数
        result.setTotalCommentCount(vo1.getTotalCommentCount() + result.getTotalCommentCount());
        // 差评数
        result.setBadCommentCount(vo1.getBadCommentCount() + result.getBadCommentCount());

        // 售后申请待处理
        result.setAfsOrderDealingCount(vo1.getAfsOrderDealingCount() + result.getAfsOrderDealingCount());
        // 已完成退单数量统计
        result.setAfsOrderCompleteCount(vo1.getAfsOrderCompleteCount() + result.getAfsOrderCompleteCount());
        //仅退款
        result.setRefundMoneyCount(vo1.getRefundMoneyCount() + result.getRefundMoneyCount());
        //退货退款
        result.setRefundGoodCount(vo1.getRefundGoodCount() + result.getRefundGoodCount());
        // 待自提
        result.setUnSelfPickCount(vo1.getUnSelfPickCount() + result.getUnSelfPickCount());
        //已完成售后退单统计
        result.setAfsOrderCompleteDetailList(mergeAfsOrderCompleteData(vo1.getAfsOrderCompleteDetailList(), result.getAfsOrderCompleteDetailList()));

        return result;
    }

    private List<AfsOrderCompleteDetail> mergeAfsOrderCompleteData(List<AfsOrderCompleteDetail> afsOrderCompleteDetalList, List<AfsOrderCompleteDetail> result) {
        if (CollectionUtils.isEmpty(afsOrderCompleteDetalList)) {
            return Lists.newArrayList();
        }
        if (CollectionUtils.isEmpty(result)) {
            result = Lists.newArrayList();
        }
        Map<String, AfsOrderCompleteDetail> afsOrderCompleteDetailMap = result.stream().collect(Collectors.toMap(AfsOrderCompleteDetail::getAfsOrderType, Function.identity(), (oldValue, newValue) -> newValue));
        for (AfsOrderCompleteDetail detail : afsOrderCompleteDetalList) {
            AfsOrderCompleteDetail target = afsOrderCompleteDetailMap.get(detail.getAfsOrderType());
            if (target == null) {
                afsOrderCompleteDetailMap.put(detail.getAfsOrderType(), detail);
            } else {
                target.setAfsOrderCount(detail.getAfsOrderCount() + target.getAfsOrderCount());
                afsOrderCompleteDetailMap.put(target.getAfsOrderType(), target);
            }
        }
        return new ArrayList<>(afsOrderCompleteDetailMap.values());
    }


    private void handleResp(OrderFulfillmentReportQueryResp result, OrderFulfillmentReportQueryResp resp) {
        if (CollectionUtils.isEmpty(result.getOrderFulfillmentDetailReportList())) {
            result.setOrderFulfillmentDetailReportList(resp.getOrderFulfillmentDetailReportList());
            return;
        }
        List<OrderFulfillmentReportVO> orderFulfillmentDetailReportList = result.getOrderFulfillmentDetailReportList();
        Map<Long, OrderFulfillmentReportVO> shopReportMap =
                orderFulfillmentDetailReportList.stream().collect(Collectors.toMap(OrderFulfillmentReportVO::getShopId, Function.identity(), (oldValue, newValue) -> newValue));

        for (OrderFulfillmentReportVO orderFulfillmentReportVO : resp.getOrderFulfillmentDetailReportList()) {
            if (shopReportMap.get(orderFulfillmentReportVO.getShopId()) == null) {
                orderFulfillmentDetailReportList.add(orderFulfillmentReportVO);
            } else {
                OrderFulfillmentReportVO shopReport = shopReportMap.get(orderFulfillmentReportVO.getShopId());
                mergeResp(shopReport, orderFulfillmentReportVO);
            }
        }


    }

    private void mergeResp(OrderFulfillmentReportVO shopReport, OrderFulfillmentReportVO orderFulfillmentReportVO) {
        if (shopReport == null || orderFulfillmentReportVO == null) {
            return;
        }
        try {
            // 获取orderFulfillmentReportVO的所有属性
            PropertyDescriptor[] propertyDescriptors = Introspector.getBeanInfo(OrderFulfillmentReportVO.class, Object.class).getPropertyDescriptors();
            for (PropertyDescriptor propertyDescriptor : propertyDescriptors) {
                String propertyName = propertyDescriptor.getName();
                Method readMethod = propertyDescriptor.getReadMethod();
                Method writeMethod = propertyDescriptor.getWriteMethod();
                // 获取orderFulfillmentReportVO对应属性的值
                Object value = readMethod.invoke(orderFulfillmentReportVO);
                // 判断属性值是否为List
                if (value instanceof List) {
                    // 如果是List，进行合并操作
                    List originalList = (List) readMethod.invoke(shopReport);
                    List newList = (List) value;
                    if (originalList != null && newList != null) {
                        originalList.addAll(newList);
                        writeMethod.invoke(shopReport, originalList);
                    } else if (originalList == null && newList != null) {
                        writeMethod.invoke(shopReport, new ArrayList<>(newList));
                    }
                } else {
                    // 如果属性值不为null且不为"0"
                    if (value != null && !value.toString().equals("0")) {
                        // 将orderFulfillmentReportVO的属性值赋给shopReport的同名属性
                        writeMethod.invoke(shopReport, value);
                    }
                }
            }
        } catch (Exception e) {
            log.error("mergeResp-属性合并异常", e);
            throw new RuntimeException(e);
        }
    }
}
