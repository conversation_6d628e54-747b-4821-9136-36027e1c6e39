package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.sankuai.shangou.qnh.orderapi.domain.response.store.StoreInfoItem;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("根据storeid批量获取门店信息响应")
public class GetStoreByIdsResponse {

	@ApiModelProperty(name = "门店信息列表", required = true)
	private List<StoreInfoItem> storeInfoList;
}
