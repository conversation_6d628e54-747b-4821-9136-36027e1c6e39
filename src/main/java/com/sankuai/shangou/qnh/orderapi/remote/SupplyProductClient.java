package com.sankuai.shangou.qnh.orderapi.remote;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Lists;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.sankuai.meituan.shangou.xsupply.product.client.dto.Status;
import com.sankuai.meituan.shangou.xsupply.product.client.dto.api.tag.ProductTagDTO;
import com.sankuai.meituan.shangou.xsupply.product.client.request.tag.ProductTagConditionQueryRequest;
import com.sankuai.meituan.shangou.xsupply.product.client.response.api.tag.ProductTagListResponse;
import com.sankuai.meituan.shangou.xsupply.product.client.service.api.TagThriftApi;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/3/1 11:30
 **/
@Slf4j
@Rhino
public class SupplyProductClient {
    @Resource
    private TagThriftApi tagThriftApi;

    private final int BATCH_SIZE = 50;


    @Degrade(rhinoKey = "SupplyProductWrapper.batchGetProductHighPriceTag", fallBackMethod = "batchGetProductHighPriceTagFallBack", timeoutInMilliseconds = 1000)
    @MethodLog(logResponse = true, logRequest = true)
    public Map<String, Boolean> batchGetProductHighPriceTag(Long tenantId, List<String> skuIds) {
        MccConfigUtil.HighPriceTagConfig highPriceTagConfig = MccConfigUtil.getHighPriceTagConfig();
        if (highPriceTagConfig == null) {
            return Collections.emptyMap();
        }

        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyMap();
        }

        List<List<String>> lists = Lists.partition(skuIds.stream().distinct().collect(Collectors.toList()), BATCH_SIZE);
        List<ProductTagDTO> productTagDTOS = new ArrayList<>();
        for (List<String> subList : lists) {
            ProductTagConditionQueryRequest request = new ProductTagConditionQueryRequest();
            request.setTenantId(tenantId);
            request.setProductIdList(subList);
            request.setMaxSize(500);
            request.setTagCodeList(Arrays.asList(highPriceTagConfig.getTagCode()));
            log.info("start invoke tagThriftApi.batchQueryTagRelationList, request: {}", request);
            ProductTagListResponse response = tagThriftApi.batchQueryTagRelationList(request);
            log.info("end invoke tagThriftApi.batchQueryTagRelationList, response: {}", response);

            if (response == null || response.getStatus() == null || !Objects.equals(response.getStatus().getCode(), Status.SUCCESS.getCode())) {
                throw new ThirdPartyException("查询高价值标签失败");
            }

            productTagDTOS.addAll(Optional.ofNullable(response.getProductTagList()).orElse(Collections.emptyList()));
        }

        return productTagDTOS
                .stream()
                .collect(Collectors.toMap(
                        ProductTagDTO::getProductId,
                        productTagDTO -> Objects.equals(productTagDTO.getTagCode(), highPriceTagConfig.getTagCode()) && Objects.equals(productTagDTO.getTagValueCode(), highPriceTagConfig.getTagValueCode()),
                        (k1, k2) -> k1));
    }

    public Map<String, Boolean> batchGetProductHighPriceTagFallBack(Long tenantId, List<String> skuIds) {
        log.error("SupplyProductWrapper.batchGetProductHighPriceTag 发生降级, skuIds: {}", skuIds);
        return Collections.emptyMap();
    }

}
