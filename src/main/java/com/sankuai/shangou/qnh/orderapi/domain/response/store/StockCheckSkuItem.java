package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@TypeDoc(
        description = "盘点商品子项信息",
        authors = {
                "qianteng"
        }
)
@Data
@ApiModel("盘点商品子项信息")
public class StockCheckSkuItem {
    @FieldDoc(
            description = "商品id"
    )
    @ApiModelProperty(name = "商品id", required = true)
    @NotNull
    private String skuId;
    @FieldDoc(
            description = "商品名称"
    )
    @ApiModelProperty(name = "商品名称", required = true)
    @NotNull
    private String skuName;
    @FieldDoc(
            description = "实盘数量"
    )
    @ApiModelProperty(name = "实盘数量", required = true)
    @NotNull
    private String stock;
    @FieldDoc(
            description = "库存数量"
    )
    @ApiModelProperty(name = "库存数量")
    private String stockNum;
    @FieldDoc(
            description = "是否称重,0-不称重；1-称重，康品汇使用"
    )
    @ApiModelProperty(name = "是否称重,0-不称重；1-称重，康品汇使用")
    private Integer isWeight = 0;
    @FieldDoc(
            description = "称重类型：1-称重计量；2-称重计件；3-非称重，元初使用"
    )
    @ApiModelProperty(name = "称重类型：1-称重计量；2-称重计件；3-非称重，元初使用")
    private Integer weightType;
    @FieldDoc(
            description = "盘点单位"
    )
    @ApiModelProperty(name = "盘点单位", required = true)
    @NotNull
    private String unit;
    @FieldDoc(
            description = "商品单位类型（1基础，2采购，3库存,4配送"
    )
    @ApiModelProperty(name = "商品单位类型（1基础，2采购，3库存,4配送）", required = true)
    @NotNull
    private Integer unitType;
    @FieldDoc(
            description = "备注"
    )
    @ApiModelProperty(name = "备注")
    private String comment;
    @FieldDoc(
            description = "规格"
    )
    @ApiModelProperty(name = "规格")
    private String spec;
    @FieldDoc(
            description = "总重量"
    )
    @ApiModelProperty(name = "总重量")
    private String skuTotalWeight;
    @FieldDoc(
            description = "重量列表"
    )
    @ApiModelProperty(name = "重量列表")
    private List<String> skuWeightDetail;
    @FieldDoc(
            description = "库位编号"
    )
    @ApiModelProperty(name = "库位编号")
    private String locationCode;
    @FieldDoc(
            description = "商品是否可用：1-可用；2-不可用"
    )
    @ApiModelProperty(name = "商品是否可用：1-可用；2-不可用")
    private Integer isAvailable;
    @FieldDoc(
            description = "商品upc列表"
    )
    @ApiModelProperty(name = "商品upc列表")
    private List<String> upcList;
}
