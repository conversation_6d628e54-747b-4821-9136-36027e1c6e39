package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.ProductVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/7/11
 * desc: 订单详情
 */
@TypeDoc(
        description = "订单详情"
)
@ApiModel("订单详情")
@Data
public class ExchangeProductVO{

    @FieldDoc(
            description = "被换货数量（缺货数量）", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "被换货数量（缺货数量）", required = true)
    private int exchangeFromCnt;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "替换的数量（已换数量）", required = true)
    private int exchangeToCnt;

    @FieldDoc(
            description = "替换的商品详情", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "替换的商品详情", required = true)
    private ProductVO productVO;

}
