package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("根据storeid批量获取门店信息响应")
public class StoreInfoItem {

	@ApiModelProperty(name = "门店id", required = true)
	private long poiId;

	@ApiModelProperty(name = "门店名称", required = true)
	private String poiName;

	@ApiModelProperty(name = "租户id", required = true)
	private long tenantId;

	@ApiModelProperty(name = "租户名称", required = true)
	private String tenantName;

	@ApiModelProperty(name = "外卖门店id", required = true)
	private long wmPoiId;

	@ApiModelProperty(name = "外部门店id", required = false)
	private String outPoiId;

	@ApiModelProperty(name = "merchantId", required = false)
	private String payMerchantId;

	@ApiModelProperty(name = "地址", required = false)
	private String poiAddress;

	@ApiModelProperty(name = "状态", required = false)
	private String poiStatus;

	@ApiModelProperty(name = "类型:1前置仓,2普通仓,3门店", required = false)
	private int entityType;

	@ApiModelProperty(name = "实体id", required = false)
	private long entityId;

	@ApiModelProperty(name = "权限应用ID", required = true)
	private String authAppId;

	@ApiModelProperty(name = "权限子应用ID", required = true)
	private String authSubAppId;

}
