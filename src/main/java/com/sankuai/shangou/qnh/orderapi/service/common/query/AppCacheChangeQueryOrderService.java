package com.sankuai.shangou.qnh.orderapi.service.common.query;


import com.meituan.shangou.saas.common.enums.OrderCanOperateItem;
import com.meituan.shangou.saas.o2o.dto.model.OrderBizTypeAndViewOrderIdModel;
import com.meituan.shangou.saas.o2o.dto.request.BizOrderQueryChangedOrderIdsRequest;
import com.meituan.shangou.saas.o2o.dto.response.BizChangedOrderListResponse;
import com.meituan.shangou.saas.o2o.service.BizOrderThriftService;
import com.meituan.shangou.saas.order.management.client.dto.request.online.ViewIdCondition;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.FuseOrderStatusEnum;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.ChangedOrderListForAppLocalCacheRequest;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.OrderListResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.OrderVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.PageInfoVO;
import com.sankuai.shangou.qnh.orderapi.service.common.*;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 缓存不支持多门店，不支持仓
 * <AUTHOR>
 * @since 2024/7/17
 **/
@Service
@Slf4j
public class AppCacheChangeQueryOrderService extends QueryOrderService {

    @Resource
    private BizOrderThriftService bizOrderThriftService;
    @Resource
    private DeliveryService deliveryService;
    @Resource
    private OrderOperateItemsService orderOperateItemsService;
    @Resource
    private PickSelectService pickSelectService;

    private static final Integer ONE = 1;



    @Override
    public boolean supportParallelQuery(){
        return false;
    }

    @Override
    public Pair<List<OCMSOrderVO>, PageInfoVO> queryOrderInfo(OrderListRequestContext request) {
        ChangedOrderListForAppLocalCacheRequest changedOrderListForAppLocalCacheRequest = (ChangedOrderListForAppLocalCacheRequest) request.getRequest();
        if (StringUtils.isNotBlank(changedOrderListForAppLocalCacheRequest.getChannelOrderId())){
            List<OCMSOrderVO> ocmsOrderVOList = queryOCMSVoByViewOrderId(request.getStoreId(), Collections.singletonList(toViewIdCondition(changedOrderListForAppLocalCacheRequest)), request.isShowSalePrice());
            PageInfoVO pageInfoVO = new PageInfoVO(ONE, ONE, ONE, ONE, null);
            return new Pair<>(ocmsOrderVOList, pageInfoVO);
        }

        BizOrderQueryChangedOrderIdsRequest bizOrderQueryChangedOrderIdsRequest = new BizOrderQueryChangedOrderIdsRequest();
        bizOrderQueryChangedOrderIdsRequest.setTenantId(request.getTenantId());
        bizOrderQueryChangedOrderIdsRequest.setCurStoreId(request.getStoreId());
        bizOrderQueryChangedOrderIdsRequest.setPageNo(request.getPage());
        bizOrderQueryChangedOrderIdsRequest.setPageSize(request.getSize());
        bizOrderQueryChangedOrderIdsRequest.setStartTime(changedOrderListForAppLocalCacheRequest.getChangeStartTime());

        BizChangedOrderListResponse bizChangedOrderListResponse = null;
        try {
            bizChangedOrderListResponse = bizOrderThriftService.queryChangedOrderIds(bizOrderQueryChangedOrderIdsRequest);
        } catch (TException e) {
            log.error("bizOrderThriftService.queryChangedOrderIds error bizChangedOrderListResponse {}", bizChangedOrderListResponse, e);
        }
        log.info("变化订单列表, {}", bizChangedOrderListResponse);
        if (Objects.isNull(bizChangedOrderListResponse) || CollectionUtils.isEmpty(bizChangedOrderListResponse.getBizOrderModelList())) {
            PageInfoVO pageInfoVO = PageUtil.buildEmptyPageInfoVO();
            return new Pair<>(Collections.emptyList(), pageInfoVO);
        }
        request.setChangedOrderModelList(bizChangedOrderListResponse.getBizOrderModelList());
        List<OCMSOrderVO> ocmsOrderVOList = queryOCMSVoByViewOrderId(request.getTenantId(), toViewIdCondition(bizChangedOrderListResponse.getBizOrderModelList()), request.isShowSalePrice());
        ocmsOrderVOList = ocmsOrderVOList.stream().filter(ocmsOrderVO -> Objects.equals(ocmsOrderVO.getShopId(), request.getStoreId()) || Objects.equals(ocmsOrderVO.getDispatchShopId(), request.getStoreId())).collect(Collectors.toList());
        PageInfoVO pageInfoVO = PageUtil.buildPageInfoVO(request.getPage(), request.getSize(), bizChangedOrderListResponse.getCount());
        return new Pair<>(ocmsOrderVOList, pageInfoVO);
    }

    private List<ViewIdCondition> toViewIdCondition(List<OrderBizTypeAndViewOrderIdModel> bizOrderModelList) {
        return bizOrderModelList.stream().map(v -> new ViewIdCondition(v.getOrderBizType(), v.getViewOrderId())).collect(Collectors.toList());
    }

    private ViewIdCondition toViewIdCondition(ChangedOrderListForAppLocalCacheRequest request){
        ViewIdCondition channelOrderIdCondition = new ViewIdCondition();
        channelOrderIdCondition.setViewOrderId(request.getChannelOrderId());
        channelOrderIdCondition.setOrderBizType(DynamicOrderBizType.channelId2OrderBizType(request.getChannelId()).getValue());
        return channelOrderIdCondition;
    }

    @Override
    public void  addExtraInfo(OrderListResponse orderListResponse, OrderListRequestContext request) {
        OrderStatusUtil.fixOrdersStatusDescForAppCache(orderListResponse);
        deliveryService.buildDeliveryCount(orderListResponse);
        orderOperateItemsService.justKeepPartOperateItemsForDeliveryErrorOrder(orderListResponse, Lists.newArrayList(OrderCanOperateItem.FULL_ORDER_REFUND, OrderCanOperateItem.CREATE_INVOICE, OrderCanOperateItem.SETTING_ORDER_TAG));
        pickSelectService.fillExistsPickTaskForWait2PickOrder(orderListResponse, request.getStoreId());
        fillWait2ConfirmOrderDeliveryStatusChangeTimeWithPayTime(orderListResponse.getOrderList());
        deleteAfterSaleOrderViewFuseOrderStatus(orderListResponse.getOrderList());
        resetOrderLastChangeTime(orderListResponse, request);
    }

    private void resetOrderLastChangeTime(OrderListResponse orderListResponse, OrderListRequestContext request){
        if (CollectionUtils.isEmpty(orderListResponse.getOrderList())){
            return;
        }
        if (CollectionUtils.isEmpty(request.getChangedOrderModelList())){
            return;
        }
        Map<String, Long> viewId2ChangeTime = request.getChangedOrderModelList().stream().collect(Collectors.toMap(OrderBizTypeAndViewOrderIdModel::getViewOrderId, OrderBizTypeAndViewOrderIdModel::getLastChangeTime, (v1, v2)->v2));
        for (OrderVO orderVO : orderListResponse.getOrderList()){
            Long lastChangeTime = viewId2ChangeTime.get(orderVO.getChannelOrderId());
            if (Objects.nonNull(lastChangeTime)){
                orderVO.setLastChangeTime(lastChangeTime);
            }
        }

    }








}
