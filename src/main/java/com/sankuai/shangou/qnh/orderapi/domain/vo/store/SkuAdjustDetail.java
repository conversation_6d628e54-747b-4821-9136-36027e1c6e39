package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2018/9/25
 * @email jianglilin02@meituan
 */
@Data
@ApiModel("商品调价明细")
public class SkuAdjustDetail {
    @NotEmpty
    @ApiModelProperty(value = "skuId", required = true)
    private String skuId;
    @NotEmpty
    @ApiModelProperty(value = "sku名", required = true)
    private String skuName;
    @NotNull
    @ApiModelProperty(value = "修改前标准售价", required = true)
    private String preStandardPrice;
    @NotNull
    @ApiModelProperty(value = "修改前标准售价", required = true)
    private String preVipPrice;
    @NotNull
    @ApiModelProperty(value = "修改后标准售价", required = true)
    private String curStandardPrice;
    @NotNull
    @ApiModelProperty(value = "修改后vip售价", required = true)
    private String curVipPrice;
    @NotNull
    @ApiModelProperty(value = "单位", required = true)
    private String unit;
    @ApiModelProperty(value = "调价明细备注")
    @Length(max = 30)
    private String remark;
}
