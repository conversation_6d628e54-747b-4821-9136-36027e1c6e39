package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2020/3/16
 * desc: 删除门店配送配置请求
 */
@Setter
@Getter
public class DeleteShopDeliveryConfigRequest implements BaseRequest {

    @FieldDoc(
            description = "门店ID"
    )
    @ApiModelProperty(value = "门店ID", required = true)
    private String poiId;

    @Override
    public void selfCheck() {
        AssertUtil.notEmpty(poiId, "门店ID不能为空" , "poiId");
    }
}
