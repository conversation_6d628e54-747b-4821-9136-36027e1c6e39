package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.model.OrderItem;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@TypeDoc(description = "评价溯源查询订单列表的商品信息")
@ApiModel("查询订单详情响应")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommentOrderItemVO {

    @FieldDoc(description = "商品名称")
    private String skuName;

    @FieldDoc(description = "规格")
    private String specification;

    @FieldDoc(description = "渠道重量")
    private Integer channelWeight;

    @FieldDoc(description = "数量或者份数")
    private Integer quantity;

    @FieldDoc(description = "是否是组合商品")
    private Boolean isCombinationChildProduct;

    public static List<CommentOrderItemVO> buildByOrderItemList(List<OrderItem> orderItemList) {
        List<CommentOrderItemVO> commentOrderItemVOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(orderItemList)) {
            return commentOrderItemVOList;
        }
        for (OrderItem orderItem : orderItemList) {
            CommentOrderItemVO commentOrderItemVO = CommentOrderItemVO.builder().skuName(orderItem.getSkuName())
                    .specification(orderItem.getSpecification()).channelWeight(orderItem.getChannelWeight())
                    .quantity(orderItem.getQuantity())
                    .isCombinationChildProduct(CollectionUtils.isNotEmpty(orderItem.getCombinationChildProductList()))
                    .build();
            commentOrderItemVOList.add(commentOrderItemVO);
        }
        return commentOrderItemVOList;
    }
}
