package com.sankuai.shangou.qnh.orderapi.domain.vo.app;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: <EMAIL>
 * @Date: 2020-02-20 17:53
 * @Description:
 */
@TypeDoc(
        description = "退款下架商品"
)
@ApiModel("退款下架商品")
@Data
public class RefundGoodsTakenOffVO {

    @FieldDoc(
            description = "SKU编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "SKU编码")
    private String skuId;

    @FieldDoc(
            description = "线上渠道sku编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "线上渠道sku编码")
    private String customSkuId;

    @FieldDoc(
            description = "SKU名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "SKU名称")
    @NotNull
    private String skuName;
}
