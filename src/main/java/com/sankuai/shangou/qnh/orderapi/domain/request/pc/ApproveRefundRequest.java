package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import lombok.Getter;
import lombok.Setter;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/2 19:25
 * @Description:
 */
@Getter
@Setter
public class ApproveRefundRequest implements BaseRequest {

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 决策
     */
    private String decision;

    /**
     * 原因
     */
    private String note;


    /**
     * 渠道编码
     */
    private String channelId;


    /**
     * 退款类型
     */
    private String refundTagId;


    private String serviceId;

    private String afsApplyType;


    @Override
    public void selfCheck() {
        AssertUtil.notEmpty(orderId, "订单号不能为空" , "orderId");
        AssertUtil.notEmpty(channelId, "渠道ID不能为空" , "channelId");
        AssertUtil.notEmpty(decision, "决策不能为空","decision");
        AssertUtil.notEmpty(refundTagId, "退款类型不能为空","refundTagId");

        if (!"AGREE".equals(decision)) {
            AssertUtil.notEmpty(note, "拒绝时理由不能为空", "note");
        }
    }
}
