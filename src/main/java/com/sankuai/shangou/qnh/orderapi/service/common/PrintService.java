package com.sankuai.shangou.qnh.orderapi.service.common;


import com.meituan.shangou.saas.tenant.thrift.common.enums.PrintOpLogStatusEnum;
import com.sankuai.meituan.reco.pickselect.consts.ResultCodeEnum;
import com.sankuai.meituan.reco.pickselect.logic.thrift.print.dto.OrderPrintResultDto;
import com.sankuai.meituan.reco.pickselect.logic.thrift.print.dto.PrintDeviceInfoDto;
import com.sankuai.meituan.reco.pickselect.query.thrift.print.PrintThriftService;
import com.sankuai.meituan.reco.pickselect.query.thrift.print.request.PrintOrderQueryRequest;
import com.sankuai.meituan.reco.pickselect.query.thrift.print.response.PrintOrderQueryResponse;
import com.sankuai.shangou.qnh.orderapi.converter.app.ChannelOrderConverter;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.OrderPrintStatusVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.OrderVO;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/7/15
 **/
@Service
@Slf4j
public class PrintService {
    @Resource
    private PrintThriftService printThriftService;


    public List<com.sankuai.meituan.reco.pickselect.query.thrift.print.dto.OrderPrintResultDto> queryOrderPrintResultV2(long tenantId, List<OrderVO> orderList) {
        if (CollectionUtils.isNotEmpty(orderList)) {
            PrintOrderQueryRequest resultQueryRequest = new PrintOrderQueryRequest();
            resultQueryRequest.setTenantId(tenantId);
            resultQueryRequest.setOrderList(orderList.stream().map(e -> {
                PrintOrderQueryRequest.PrintOrderInfo orderPrintQueryDto = new PrintOrderQueryRequest.PrintOrderInfo();
                orderPrintQueryDto.setChannelOrderId(e.getChannelOrderId());
                orderPrintQueryDto.setOrderBizType(ChannelOrderConverter.convertChannelId2OrderBizType(e.getChannelId()));
                orderPrintQueryDto.setStoreId(e.getStoreId());
                return orderPrintQueryDto;
            }).collect(Collectors.toList()));
            try {
                PrintOrderQueryResponse response = printThriftService.queryOrderPrintResult(resultQueryRequest);
                log.info("请求履约获取订单打印状态，request:{}, response:{}", resultQueryRequest, response);
                if (Objects.nonNull(response) && Objects.equals(response.getStatus().getCode(), ResultCodeEnum.SUCCESS.getCode())) {
                    return response.getOrderPrintResultList();
                }
            } catch (Exception e) {
                log.error("请求订单打印状态失败, tenantId:{}", tenantId, e);
            }
        }
        return Lists.newArrayList();
    }



    public OrderPrintStatusVo buildPrintStatusVo(OrderPrintResultDto printResultDto) {
        OrderPrintStatusVo printStatusVo = new OrderPrintStatusVo();
        printStatusVo.setPrintStatus(printResultDto.getPrintStatus());
        printStatusVo.setPrintFailToast(generatePrintToast(printResultDto.getPrintStatus()));
        printStatusVo.setDeviceInfo(printResultDto.getDeviceInfo());
        return printStatusVo;
    }

    public OrderPrintStatusVo buildPrintStatusVo(com.sankuai.meituan.reco.pickselect.query.thrift.print.dto.OrderPrintResultDto printResultDto) {
        OrderPrintStatusVo printStatusVo = new OrderPrintStatusVo();
        printStatusVo.setPrintStatus(printResultDto.getPrintStatus());
        printStatusVo.setPrintFailToast(generatePrintToast(printResultDto.getPrintStatus()));
        printStatusVo.setDeviceInfo(convert2DeviceInfo(printResultDto.getDeviceInfo()));
        return printStatusVo;
    }

    private PrintDeviceInfoDto convert2DeviceInfo(com.sankuai.meituan.reco.pickselect.query.thrift.print.dto.PrintDeviceInfoDto deviceInfo) {
        PrintDeviceInfoDto printDeviceInfoDto = new PrintDeviceInfoDto();
        printDeviceInfoDto.setDeviceId(deviceInfo.getDeviceId());
        printDeviceInfoDto.setDeviceName(deviceInfo.getDeviceName());
        printDeviceInfoDto.setDeviceType(deviceInfo.getDeviceType());
        printDeviceInfoDto.setDeviceNo(deviceInfo.getDeviceNo());
        printDeviceInfoDto.setBindName(deviceInfo.getBindName());
        printDeviceInfoDto.setDeviceType(deviceInfo.getDeviceType());
        printDeviceInfoDto.setDeviceSubType(deviceInfo.getDeviceSubType());

        return printDeviceInfoDto;
    }

    private String generatePrintToast(int printStatus) {
        String toast = null;
        /***
         * 打印状态，40，45，99，999 都归类成“打印失败”
         * **/
        PrintOpLogStatusEnum printStatusEnum = PrintOpLogStatusEnum.enumOf(printStatus);
        if (printStatusEnum != null) {
            switch (printStatusEnum) {
                case PRINT_TIME_OUT:
                    toast = MccConfigUtil.getOrderPrintTimeoutTips();
                    break;
                case SERVICE_NOT_AVAILABLE:
                case PRINTER_OFFLINE:
                case PRINTER_ABNORMAL:
                case UNKNOWN:
                    toast = MccConfigUtil.getOrderPrintFailTips();
                    break;
                case PRINTING:
                case WAITING_FOR_PRINT:
                case PRINT_SUCCESS:
                    toast = null;
                    break;
                default:
                    log.error("未知打印状态码:{}", printStatusEnum);
            }
        }
        return toast;
    }
}
