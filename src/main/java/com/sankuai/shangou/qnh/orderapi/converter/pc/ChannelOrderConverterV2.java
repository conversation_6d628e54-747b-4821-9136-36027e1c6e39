package com.sankuai.shangou.qnh.orderapi.converter.pc;

import com.meituan.shangou.saas.common.enums.ChannelTypeEnum;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.DistributeStatusEnum;
import com.meituan.shangou.saas.order.platform.enums.OperatorTypeEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.meituan.shangou.saas.order.platform.enums.RefundTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelOrderStatus;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelType;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.OrderRefundTag;
import com.sankuai.shangou.qnh.orderapi.utils.pc.LionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @Date: 2019-11-22 14:43
 * @Description:
 */
@Slf4j
public class ChannelOrderConverterV2 {

    private static final Map<Integer, Integer> CHANNEL_REFUND_TAG_BIZ_REFUND_TYPE = new HashMap<>();

    private static final Map<Integer, Integer> CHANNEL_ORDER_STATUS_BIZ_ORDER_STATUS = new HashMap<>();

    static {
        CHANNEL_REFUND_TAG_BIZ_REFUND_TYPE.put(OrderRefundTag.HAS_NO_REFUND.getValue(), RefundTypeEnum.NO.getValue());
        CHANNEL_REFUND_TAG_BIZ_REFUND_TYPE.put(OrderRefundTag.PART_REFUND.getValue(), RefundTypeEnum.PART.getValue());
        CHANNEL_REFUND_TAG_BIZ_REFUND_TYPE.put(OrderRefundTag.FULL_REFUND.getValue(), RefundTypeEnum.ALL.getValue());

        CHANNEL_ORDER_STATUS_BIZ_ORDER_STATUS.put(ChannelOrderStatus.NEW_ORDER.getValue(), OrderStatusEnum.SUBMIT.getValue());
        CHANNEL_ORDER_STATUS_BIZ_ORDER_STATUS.put(ChannelOrderStatus.BIZ_CONFIRMED.getValue(), OrderStatusEnum.MERCHANT_CONFIRMED.getValue());
        CHANNEL_ORDER_STATUS_BIZ_ORDER_STATUS.put(ChannelOrderStatus.FULFILLMENT.getValue(), OrderStatusEnum.PICKING.getValue());
        CHANNEL_ORDER_STATUS_BIZ_ORDER_STATUS.put(ChannelOrderStatus.FINISHED.getValue(), OrderStatusEnum.COMPLETED.getValue());
        CHANNEL_ORDER_STATUS_BIZ_ORDER_STATUS.put(ChannelOrderStatus.CANCEL_APPLIED.getValue(), OrderStatusEnum.REFUND_APPLIED.getValue());
        CHANNEL_ORDER_STATUS_BIZ_ORDER_STATUS.put(ChannelOrderStatus.CANCELED.getValue(), OrderStatusEnum.CANCELED.getValue());
        CHANNEL_ORDER_STATUS_BIZ_ORDER_STATUS.put(ChannelOrderStatus.LOCKED.getValue(), OrderStatusEnum.LOCKED.getValue());
    }

    public static Integer convertChannelId2OrderBizType(Integer channelId) {
        Integer resultBizType = null;
        if (channelId != null) {
            resultBizType = DynamicOrderBizType.channelId2OrderBizTypeValue(channelId);

            if (resultBizType == null) {
                log.error("未知渠道类型:{}", channelId);
            }

        }
        return resultBizType;
    }

    public static String convertOperateMsg(OperatorTypeEnum operatorTypeEnum) {
        Map<String, String> operateTypeMsgMap = LionUtils.getOrderStatusOperateMsm();
        String msg = null;
        if (operateTypeMsgMap != null) {
            msg = operateTypeMsgMap.get(String.valueOf(operatorTypeEnum.getValue()));
        }
        return StringUtils.isBlank(msg) ? operatorTypeEnum.getDesc() : msg;
    }

    public static Integer sourceMid2Biz(Integer channelType) {
        return DynamicOrderBizType.channelId2OrderBizTypeValue(channelType);
    }

    public static Integer sourceBiz2Mid(Integer orderBizType) {
        return DynamicOrderBizType.orderBizTypeValue2ChannelId(orderBizType);
    }

    public static Integer refundTypeMid2Biz(Integer channelRefundTag) {
        return CHANNEL_REFUND_TAG_BIZ_REFUND_TYPE.get(channelRefundTag);
    }

    public static Integer refundTypeBiz2Mid(Integer bizRefundType) {
        for (Map.Entry<Integer, Integer> entry : CHANNEL_REFUND_TAG_BIZ_REFUND_TYPE.entrySet()) {
            if (entry.getValue().equals(bizRefundType)) {
                return entry.getKey();
            }
        }
        return null;
    }

    public static List<Integer> refundTypeMid2BizList(List<Integer> channelRefundTagList) {
        List<Integer> bizRefundTypeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(channelRefundTagList)) {
            for (Integer channelRefundTag : channelRefundTagList) {
                Integer bizRefundType = refundTypeMid2Biz(channelRefundTag);
                if (bizRefundType != null) {
                    bizRefundTypeList.add(bizRefundType);
                }
            }
        }

        return bizRefundTypeList;
    }

    public static Integer orderStatusMid2Biz(Integer channelOrderStatus) {
        Integer bizOrderStatus = CHANNEL_ORDER_STATUS_BIZ_ORDER_STATUS.get(channelOrderStatus);
        if (bizOrderStatus == null) {
            log.error("订单状态转换失败，channelOrderStatus:{}", channelOrderStatus);
        }
        return bizOrderStatus;
    }

    public static Integer orderStatusBiz2Mid(Integer bizOrderStatus) {
        //用户申述特殊处理
        if (bizOrderStatus == OrderStatusEnum.APPEAL_APPLIED.getValue()) {
            return ChannelOrderStatus.CANCEL_APPLIED.getValue();
        }
        for (Map.Entry<Integer, Integer> entry : CHANNEL_ORDER_STATUS_BIZ_ORDER_STATUS.entrySet()) {
            if (entry.getValue().equals(bizOrderStatus)) {
                return entry.getKey();
            }
        }
        log.error("订单状态转换失败，bizOrderStatus:{}", bizOrderStatus);
        return null;
    }

    public static List<Integer> orderStatusMid2BizList(List<Integer> channelOrderStatusList) {
        List<Integer> bizOrderStatusList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(channelOrderStatusList)) {
            for (Integer channelOrderStatus : channelOrderStatusList) {
                Integer bizOrderStatus = orderStatusMid2Biz(channelOrderStatus);
                if (bizOrderStatus != null) {
                    bizOrderStatusList.add(bizOrderStatus);
                }
            }
        }

        return bizOrderStatusList;
    }

    public static String getChannelNameByChannelId(Integer channelId) {
        if (channelId != null) {

            if (channelId.equals(ChannelType.ELEM.getValue())) {
                return "饿了么";
            } else if (channelId.equals(ChannelType.JD2HOME.getValue())) {
                return "京东到家";
            } else if (channelId.equals(ChannelType.MEITUAN.getValue())) {
                return "美团外卖";
            } else if (channelId.equals(ChannelType.YOU_ZAN.getValue())) {
                return "有赞";
            } else if (channelId.equals(ChannelTypeEnum.MT_DRUNK_HORSE.getValue())) {
                return "微商城";
            }
        }
        return null;
    }

    public static boolean isOnSale(Integer offlineOrderStatus, Integer distributeStatus) {
        if (offlineOrderStatus == null || distributeStatus == null) {
            return true;
        }
        return offlineOrderStatus <= ChannelOrderConverterV2.OfflineOrderStatus.FULFILLMENT && distributeStatus < DistributeStatusEnum.RIDER_TAKE_GOODS.getValue();
    }

    public static interface OfflineOrderStatus {
        int SUBMIT = OrderStatusEnum.SUBMIT.getValue();
        int MERCHANT_CONFIRM = OrderStatusEnum.MERCHANT_CONFIRMED.getValue();
        int FULFILLMENT = OrderStatusEnum.PICKING.getValue(); //履约中
        int COMPLETED = OrderStatusEnum.COMPLETED.getValue(); //已完成
        int CLOSED = OrderStatusEnum.CLOSED.getValue(); //已关闭
        int REFUND_APPLIED = OrderStatusEnum.REFUND_APPLIED.getValue();//已发起退款,正在处理中
        int CANCELED = OrderStatusEnum.CANCELED.getValue();//订单已取消
        int LOCKED = OrderStatusEnum.LOCKED.getValue();//锁定中
        int APPEAL_LOCKED = OrderStatusEnum.APPEAL_APPLIED.getValue(); //申述锁定中
    }
}
