package com.sankuai.shangou.qnh.orderapi.domain.response.pc;

import com.facebook.swift.codec.ThriftField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.pickselect.thrift.fulfill.response.AccountInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

import static com.facebook.swift.codec.ThriftField.Requiredness.OPTIONAL;

/**
 * <AUTHOR>
 * @date 2024/7/3
 * desc: 查询拣货员列表响应
 */
@TypeDoc(
        description = "查询拣货员列表响应"
)
@Data
@ApiModel("查询拣货员列表响应")
public class QueryShopPickerResponse {


    @FieldDoc(
            description = "拣货员集合"
    )
    @ApiModelProperty(value = "拣货员集合")
    public List<AccountInfo> accountInfoList;

    @FieldDoc(
            description = "自动派单，0：非，1：是"
    )
    @ApiModelProperty(value = "自动派单")
    public Integer autoReceiveMode = 0;
}
