package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/8/21
 * desc:
 */
@TypeDoc(
        description = "聚合渠道门店分类"
)
@Data
@ApiModel("聚合渠道门店分类")
public class ChannelStoreFrontCategoryKeyVO {

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID", required = true)
    private Integer channelId;

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID", required = true)
    private Long storeId;

    @FieldDoc(
            description = "前台分类ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "前台分类ID", required = true)
    private Long frontCategoryId;
}
