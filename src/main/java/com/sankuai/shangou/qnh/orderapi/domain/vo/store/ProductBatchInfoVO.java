package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.facebook.swift.codec.ThriftField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ProductBatchInfoVO {
    @FieldDoc(
            description = "库位", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "库位")
    private String locationCode;
    @FieldDoc(
            description = "批次号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "批次号")
    private String batchNo;
    @FieldDoc(
            description = "生产日期", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "生产日期")
    private String productionDate;
    @FieldDoc(
            description = "数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "数量")
    private Integer quantity;
    @FieldDoc(
            description = "标签", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "标签")
    private String periodStatus;

    @FieldDoc(
            description = "是否批次（1-批次商品 0-非批次商品）", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否批次（1-批次商品 0-非批次商品）")
    private Integer enableBatch;

    @FieldDoc(
            description = "批次日期类型（1-生产日期 2-到期日）", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "批次日期类型（1-生产日期 2-到期日）")
    private Integer batchType;

    @FieldDoc(
            description = "保质期（单位：天）", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "保质期（单位：天）")
    private Integer expirationDate;
}
