package com.sankuai.shangou.qnh.orderapi.domain.response.pc;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.PermissionGroupVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2018/12/4 下午2:22
 */
@TypeDoc(
        description = "查询数据权限组全部列表回参"
)
@ApiModel("查询数据权限组全部列表回参")
@Data
public class PermissionGroupListResponse {
    private List<PermissionGroupVO> list ;
}
