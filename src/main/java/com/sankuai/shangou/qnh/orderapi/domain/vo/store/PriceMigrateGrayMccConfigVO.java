package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * author: guo<PERSON><PERSON>@meituan.com
 * date: 2020-02-13 16:14:21
 * <p>
 * desc: 价格迁移灰度MCC配置对象
 */
@Data
@NoArgsConstructor
public class PriceMigrateGrayMccConfigVO {

    /**
     * 价格模块迁移开关配置，若迁移模块未设置开关，则表示所有模块走新接口
     */
    private Map<String, PriceMigrateConfigDetailVO> priceModelSwitchMap;

}
