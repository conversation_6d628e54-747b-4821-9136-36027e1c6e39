package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OCMSListRefundOrderRequest;
import com.meituan.shangou.saas.order.management.client.enums.SortByEnum;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.meituan.shangou.saas.order.platform.enums.AfterSaleApplyStatusEnum;
import com.meituan.shangou.saas.order.platform.enums.AfterSalePatternEnum;
import com.meituan.shangou.saas.order.platform.enums.AfterSaleTypeEnum;
import com.meituan.shangou.saas.order.platform.enums.OperatorTypeEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderPosStatusEnum;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import com.sankuai.shangou.qnh.orderapi.enums.pc.ApplyUserEnum;
import com.sankuai.shangou.qnh.orderapi.enums.pc.PosStatusEnum;
import com.sankuai.shangou.qnh.orderapi.enums.pc.RefundStatusEnum;
import com.sankuai.shangou.qnh.orderapi.enums.pc.RefundTypeEnum;
import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import com.sankuai.shangou.qnh.orderapi.utils.pc.DateTimeUtils;
import com.sankuai.shangou.qnh.orderapi.utils.pc.LionUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Setter
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(
        description = "退单列表分页查询"
)
public class RefundOrderQueryRequest extends PageRequest implements BaseRequest {

    /**
     * 订单编号
     */
    private Long tenantId;

    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 流水号,订单序号
     */
    private String orderSerialNumber;


    /**
     * 退单号
     */
    private String afterSaleId;

    /**
     * 渠道列表
     */
    private List<String> channelIds;


    /**
     * 申请开始时间
     */
    private String applyStartTime;

    /**
     * 申请结束时间
     */
    private String applyEndTime;


    /**
     * 审核开始时间
     */
    private String auditStartTime;


    /**
     * 审核结束时间
     */
    private String auditEndTime;

    /**
     * 核销开始时间
     */
    private String writeOffStartTime;

    /**
     * 核销开始时间
     */
    private String writeOffEndTime;

    /**
     * 核销状态
     */
    private List<Integer> writeOffStatus;

    /**
     * 退单类型
     *
     * @see RefundTypeEnum
     */
    private List<String> refundTypeList;


    /**
     * 退单状态
     *
     * @see RefundStatusEnum
     */
    private List<String> refundStatusList;


    /**
     * 门店ID集合
     */
    private List<Long> poiIdList;


    /**
     * 仓
     */
    private List<Long> warehouseIdList;

    /**
     * 店内分类
     */
    private String skuCategory;


    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 商品编码
     */
    private String skuId;


    /**
     * 商品条码
     */
    private String upcCode;


    /**
     * 售后发起方
     *
     * @see ApplyUserEnum
     */
    private List<String> applyUserTypeList;


    /**
     * 商品ERP
     */
    private String erpItemCode;

    /**
     * 导出字段
     */
    private List<String> exportFields;

    /**
     * 订单标识列表
     */
    public List<Integer> orderMarks;

    /**
     * 商品编码列表
     */
    private List<String> skuIdList;

    /**
     * 商品条码列表
     */
    private List<String> upcCodeList;

    /**
     * ERP编码列表
     */
    private List<String> erpCodeList;

    @Override
    public void selfCheck() {
        if (StringUtils.isNotEmpty(orderId)) {
            Integer limitLength = LionUtils.getOrderParamsLengthLimitConfig();
            AssertUtil.isTrue(orderId.length() <= limitLength && orderId.length() >= 4, "订单号最多输入" + limitLength + "位，最少4位字符串");
        }

        if (StringUtils.isNotEmpty(orderSerialNumber)) {
            AssertUtil.isTrue(orderSerialNumber.length() <= 10, "订单序号最多输入10个数字");
        }

        if (StringUtils.isNotEmpty(afterSaleId)) {
            AssertUtil.isTrue(afterSaleId.length() <= 25, "退单号最多输入25个数字");
        }

        if (StringUtils.isNotEmpty(skuName)) {
            AssertUtil.isTrue(skuName.length() <= 25, "商品名称最多输入25个字符");
        }

        if (StringUtils.isNotEmpty(skuId)) {
            AssertUtil.isTrue(skuId.length() <= 20, "商品编码最多输入20个字符");
        }

        if (StringUtils.isNotEmpty(upcCode)) {
            AssertUtil.isTrue(upcCode.length() <= 20, "商品条码最多输入20个字符");
        }
        if (CollectionUtils.isNotEmpty(skuIdList)) {
            AssertUtil.isTrue(skuIdList.size() <= 50, "商品编码列表最多输入50条");
        }
        if (CollectionUtils.isNotEmpty(upcCodeList)) {
            AssertUtil.isTrue(upcCodeList.size() <= 50, "商品条码列表最多输入50条");
        }
        if (CollectionUtils.isNotEmpty(erpCodeList)) {
            AssertUtil.isTrue(erpCodeList.size() <= 50, "商品ERP列表最多输入50条");
        }
    }


    private void setTimeNull() {
        setApplyStartTime(null);
        setApplyEndTime(null);
        setAuditStartTime(null);
        setAuditEndTime(null);
        setWriteOffStartTime(null);
        setWriteOffEndTime(null);
    }

    private void setTime(OCMSListRefundOrderRequest ocmsListRefundOrderRequest) {
        if (StringUtils.isNotEmpty(this.applyStartTime)) {
            ocmsListRefundOrderRequest.setApplyStartTime(Long.valueOf(this.applyStartTime));
        }
        if (StringUtils.isNotEmpty(this.applyEndTime)) {
            ocmsListRefundOrderRequest.setApplyEndTime(Long.valueOf(this.applyEndTime));
        }
        if (StringUtils.isNotEmpty(this.auditStartTime)) {
            ocmsListRefundOrderRequest.setAuditStartTime(Long.valueOf(this.auditStartTime));
        }
        if (StringUtils.isNotEmpty(this.auditEndTime)) {
            ocmsListRefundOrderRequest.setAuditEndTime(Long.valueOf(this.auditEndTime));
        }
        if (StringUtils.isNotEmpty(this.writeOffStartTime)) {
            ocmsListRefundOrderRequest.setWriteOffStartTime(Long.valueOf(this.writeOffStartTime));
        }
        if (StringUtils.isNotEmpty(this.writeOffEndTime)) {
            ocmsListRefundOrderRequest.setWriteOffEndTime(Long.valueOf(this.writeOffEndTime));
        }
        if (StringUtils.isEmpty(this.applyStartTime) && StringUtils.isEmpty(this.applyEndTime)
                && StringUtils.isEmpty(this.auditStartTime) && StringUtils.isEmpty(this.auditEndTime)
                && StringUtils.isEmpty(this.writeOffStartTime) && StringUtils.isEmpty(this.writeOffEndTime)) {
            //如果所有时间都不传，默认按照申请时间查询6个自然月内的退单数据
            ocmsListRefundOrderRequest.setApplyStartTime(DateTimeUtils.getMonthAgoFirstDay(6));
            ocmsListRefundOrderRequest.setApplyEndTime(new Date().getTime());
        }
    }

    public OCMSListRefundOrderRequest toOcmsListRefundOrderRequest() {
        OCMSListRefundOrderRequest ocmsListRefundOrderRequest = new OCMSListRefundOrderRequest();
        if (StringUtils.isNotEmpty(this.orderId) && this.orderId.length() > 4) {
            ocmsListRefundOrderRequest.setOrderId(this.orderId);
            ocmsListRefundOrderRequest.setPage(this.page);
            ocmsListRefundOrderRequest.setSize(this.pageSize);
            ocmsListRefundOrderRequest.setTenantId(this.tenantId);
            if (CollectionUtils.isNotEmpty(this.poiIdList)) {
                ocmsListRefundOrderRequest.setShopIdList(poiIdList);
            }
            if (CollectionUtils.isNotEmpty(this.warehouseIdList)) {
                ocmsListRefundOrderRequest.setWarehouseIdList(warehouseIdList);
            }
            if (CollectionUtils.isNotEmpty(this.refundStatusList)) {
                List<Integer> collectStatus = this.refundStatusList.stream().flatMap(item -> convertToAfterSaleStatus(item).stream())
                        .map(AfterSaleApplyStatusEnum::getValue).distinct().collect(Collectors.toList());
                ocmsListRefundOrderRequest.setAfterSaleApplyStatusList(collectStatus);
            }
        } else {
            ocmsListRefundOrderRequest.setPage(this.page);
            ocmsListRefundOrderRequest.setSize(this.pageSize);
            ocmsListRefundOrderRequest.setTenantId(this.tenantId);

            if (StringUtils.isNotEmpty(this.orderId)) {
                ocmsListRefundOrderRequest.setOrderIdFuzzy(this.orderId);
            }

            if (StringUtils.isNotEmpty(this.afterSaleId)) {
                ocmsListRefundOrderRequest.setAfterSaleIdFuzzy(this.afterSaleId);
            }

            if (StringUtils.isNotEmpty(this.orderSerialNumber)) {
                ocmsListRefundOrderRequest.setOrderSerialNumber(this.orderSerialNumber);
                ocmsListRefundOrderRequest.setOrderSerialNumberStr(this.orderSerialNumber);
            }

            if (CollectionUtils.isNotEmpty(this.channelIds)) {
                final List<Integer> collect = this.channelIds.stream().map(Integer::valueOf).collect(Collectors.toList());
                ocmsListRefundOrderRequest.setOrderBizTypeList(ChannelOrderConvertUtils.sourceMid2BizList(collect));
            }

            setTime(ocmsListRefundOrderRequest);

            if (CollectionUtils.isNotEmpty(this.refundTypeList)) {
                final List<Integer> collectRefundType = this.refundTypeList.stream().map(this::convertToAfterSaleType).filter(Objects::nonNull)
                        .map(item -> item.getValue()).distinct().collect(Collectors.toList());
                ocmsListRefundOrderRequest.setAfterSaleApplyTypeList(collectRefundType);

                final List<Integer> afterSalePatterns = this.refundTypeList.stream().map(this::convertToAfterSalePattern).filter(Objects::nonNull)
                        .map(item -> item.getValue()).distinct().collect(Collectors.toList());
                ocmsListRefundOrderRequest.setAfterSalePattern(afterSalePatterns);
            }

            if (CollectionUtils.isNotEmpty(this.refundStatusList)) {
                final List<Integer> collectStatus = this.refundStatusList.stream().
                        flatMap(item -> convertToAfterSaleStatus(item).stream()).
                        map(AfterSaleApplyStatusEnum::getValue).distinct().
                        collect(Collectors.toList());
                ocmsListRefundOrderRequest.setAfterSaleApplyStatusList(collectStatus);
            } else {
                final List<Integer> collectAllStatus = convertToAfterSaleStatus(String.valueOf(RefundStatusEnum.ALL.getCode())).stream().map(AfterSaleApplyStatusEnum::getValue).distinct().
                        collect(Collectors.toList());
                ocmsListRefundOrderRequest.setAfterSaleApplyStatusList(collectAllStatus);
            }

            if (CollectionUtils.isNotEmpty(this.poiIdList)) {
                ocmsListRefundOrderRequest.setShopIdList(poiIdList);
            }

            if (CollectionUtils.isNotEmpty(this.warehouseIdList)) {
                ocmsListRefundOrderRequest.setWarehouseIdList(warehouseIdList);
            }

            if (StringUtils.isNotEmpty(this.skuCategory)) {
                ocmsListRefundOrderRequest.setSkuCategory(skuCategory);
            }

            if (StringUtils.isNotEmpty(this.skuName)) {
                ocmsListRefundOrderRequest.setSkuNameFuzzy(skuName);
            }

            if (StringUtils.isNotEmpty(this.skuId)) {
                ocmsListRefundOrderRequest.setSkuIdFuzzy(skuId);
            }

            if (StringUtils.isNotEmpty(this.upcCode)) {
                ocmsListRefundOrderRequest.setUpcCodeFuzzy(upcCode);
            }

            if (CollectionUtils.isNotEmpty(this.applyUserTypeList)) {
                final List<Integer> collectOperatorType = this.applyUserTypeList.stream().flatMap(item -> convertToOperatorType(item).stream()).distinct().map(OperatorTypeEnum::getValue).collect(Collectors.toList());
                ocmsListRefundOrderRequest.setApplyUserType(collectOperatorType);
            }

            if (StringUtils.isNotEmpty(this.erpItemCode)) {
                ocmsListRefundOrderRequest.setErpItemCode(erpItemCode);
            }

            if (CollectionUtils.isNotEmpty(this.writeOffStatus)){
                if (writeOffStatus.contains(OrderPosStatusEnum.NEW.getValue())){
                    writeOffStatus.add(OrderPosStatusEnum.DOWN.getValue());
                }
                ocmsListRefundOrderRequest.setWriteOffStatus(writeOffStatus);
                for (Integer status: writeOffStatus){
                    if(Objects.equals(status, PosStatusEnum.ALL.getCode())){
                        ocmsListRefundOrderRequest.setWriteOffStatus(new ArrayList<>());
                        break;
                    }
                }
            }

            if(CollectionUtils.isNotEmpty(this.orderMarks)){
                ocmsListRefundOrderRequest.setOrderMarkList(this.orderMarks);
            }
            if (CollectionUtils.isNotEmpty(this.skuIdList)) {
                ocmsListRefundOrderRequest.setSkuIdList(skuIdList);
            }
            if (CollectionUtils.isNotEmpty(this.upcCodeList)) {
                ocmsListRefundOrderRequest.setUpcCodeList(upcCodeList);
            }
            if (CollectionUtils.isNotEmpty(this.erpCodeList)) {
                ocmsListRefundOrderRequest.setErpCodeList(erpCodeList);
            }
        }
        ocmsListRefundOrderRequest.setSort(SortByEnum.DESC);

        return ocmsListRefundOrderRequest;
    }

    private List<OperatorTypeEnum> convertToOperatorType(String applyUser) {
        final ApplyUserEnum applyUserEnum = ApplyUserEnum.getByCode(Integer.valueOf(applyUser));
        if (applyUserEnum == null) {
            return Arrays.stream(OperatorTypeEnum.values()).collect(Collectors.toList());
        }
        final List<OperatorTypeEnum> operatorTypeEnums = Lists.newArrayList(OperatorTypeEnum.MERCHANT, OperatorTypeEnum.CUSTOMER, OperatorTypeEnum.CHANNEL_PLATFORM, OperatorTypeEnum.CALL_CENTER);
        switch (applyUserEnum) {
            case MERCHANT:
                return Lists.newArrayList(OperatorTypeEnum.MERCHANT);
            case CUSTOMER:
                return Lists.newArrayList(OperatorTypeEnum.CUSTOMER);
            case CHANNEL:
                return Lists.newArrayList(OperatorTypeEnum.CHANNEL_PLATFORM, OperatorTypeEnum.CALL_CENTER);
            case OTHER:
                return Arrays.stream(OperatorTypeEnum.values()).filter(item -> !operatorTypeEnums.contains(item)).collect(Collectors.toList());
            default:
                return Arrays.stream(OperatorTypeEnum.values()).collect(Collectors.toList());
        }
    }

    private AfterSaleTypeEnum convertToAfterSaleType(String refundType) {
        final RefundTypeEnum refundTypeEnum = RefundTypeEnum.getByCode(Integer.valueOf(refundType));
        if (refundTypeEnum == null) {
            return null;
        }

        switch (refundTypeEnum) {
            case PART_REFUND:
            case ALL_REFUND:
            case AMOUNT_REFUND:
            case WEIGHT_REFUND:
                return AfterSaleTypeEnum.REFUND;
            case PART_REFUND_GOODS:
            case ALL_REFUND_GOODS:
            case AMOUNT_REFUND_GOODS:
                return AfterSaleTypeEnum.REFUND_GOODS;
            case REJECT_BY_CUSTOMER:
                return AfterSaleTypeEnum.REJECT_BY_CUSTOMER;
            case APPEAL:
                return AfterSaleTypeEnum.APPEAL;
            default:
                return null;
        }
    }

    private AfterSalePatternEnum convertToAfterSalePattern(String refundType) {
        final RefundTypeEnum refundTypeEnum = RefundTypeEnum.getByCode(Integer.valueOf(refundType));
        if (refundTypeEnum == null) {
            return null;
        }

        switch (refundTypeEnum) {
            case WEIGHT_REFUND:
                return AfterSalePatternEnum.WEIGHT;
            case AMOUNT_REFUND:
            case AMOUNT_REFUND_GOODS:
                return AfterSalePatternEnum.AMOUNT;
            case PART_REFUND:
            case PART_REFUND_GOODS:
                return AfterSalePatternEnum.PART;
            case ALL_REFUND:
            case ALL_REFUND_GOODS:
                return AfterSalePatternEnum.ALL;
            default:
                return null;
        }
    }

    private List<AfterSaleApplyStatusEnum> convertToAfterSaleStatus(String refundStatus) {
        final RefundStatusEnum refundStatusEnum = RefundStatusEnum.getByCode(Integer.valueOf(refundStatus));
        //暂存,暂存已处理状态不显示
        final ArrayList<AfterSaleApplyStatusEnum> afterSaleApplyStatusEnums = Lists.newArrayList(AfterSaleApplyStatusEnum.DRAFT, AfterSaleApplyStatusEnum.DRAFT_DONE);
        final List<AfterSaleApplyStatusEnum> collect = Arrays.stream(AfterSaleApplyStatusEnum.values()).filter(item -> !afterSaleApplyStatusEnums.contains(item)).collect(Collectors.toList());
        if (refundStatusEnum == null || RefundStatusEnum.ALL.equals(refundStatusEnum)) {
            return collect;
        }

        switch (refundStatusEnum) {
            case DEAL_ING:
                return Lists.newArrayList(AfterSaleApplyStatusEnum.WAIT_ASSIGN, AfterSaleApplyStatusEnum.COMMIT, AfterSaleApplyStatusEnum.FIRST_AUDITED, AfterSaleApplyStatusEnum.FIRST_AUTO_AUDITED);
            case CANCEL:
                return Lists.newArrayList(AfterSaleApplyStatusEnum.CANCEL);
            case COMPLETE:
                return Lists.newArrayList(AfterSaleApplyStatusEnum.AUDITED, AfterSaleApplyStatusEnum.FINISH, AfterSaleApplyStatusEnum.AUTO_AUDITED);
            case REJECT:
                return Lists.newArrayList(AfterSaleApplyStatusEnum.AUDITED_REJECT, AfterSaleApplyStatusEnum.FIRST_AUDITED_REJECT);
            default:
                return collect;
        }

    }

}
