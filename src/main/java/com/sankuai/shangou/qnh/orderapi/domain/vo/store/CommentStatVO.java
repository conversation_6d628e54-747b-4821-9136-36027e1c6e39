package com.sankuai.shangou.qnh.orderapi.domain.vo.store;
// Copyright (C) 2019 Meituan
// All rights reserved

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ChannelCommentLabelStatDTO;
import lombok.Data;

/**
 * @author: <EMAIL>
 * @class: CommentStatVO
 * @date: 2019-07-04 16:00:48
 * @desc:
 */
@Data
public class CommentStatVO {

    public int total;

    public int not_replied_count;

    public int replied_count;

    public CommentStatVO(int total, int not_replied_count) {
        this.total = total;
        this.not_replied_count = not_replied_count;
        this.replied_count = total - this.not_replied_count;
    }
}
