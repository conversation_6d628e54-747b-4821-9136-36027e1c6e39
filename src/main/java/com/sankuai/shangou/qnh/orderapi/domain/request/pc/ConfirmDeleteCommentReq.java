package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import java.util.Objects;

@Data
public class ConfirmDeleteCommentReq {

    /**
     * 评论ID
     */
    private String commentId;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 操作人Id
     */
    private Long operatorId;

    public void isValid(){
        Assert.isTrue(StringUtils.isNotBlank(commentId), "评论Id不能为空");
        Assert.isTrue(Objects.nonNull(tenantId), "租户不存在");
        Assert.isTrue(Objects.nonNull(operatorId), "操作人Id不存在");
    }
}
