package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.shangou.saas.o2o.dto.model.OCMSOrderKey;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.DeliveryConfigForModifyVO;
import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/16
 * desc: 修改配送配置请求
 */
@Getter
@Setter
public class GetOrderOperateItemsRequest implements BaseRequest {

    @FieldDoc(
            description = "渠道订单号列表"
    )
    @ApiModelProperty(value = "渠道订单号列表", required = true)
    private List<OCMSOrderKey> orderKeys;

    @FieldDoc(
            description = "可选操作项列表"
    )
    @ApiModelProperty(value = "可选操作项列表", required = true)
    private List<Integer> checkItems;

    @Override
    public void selfCheck() {
        AssertUtil.notEmpty(orderKeys, "渠道订单号列表不能为空" , "orderKeys");
    }
}
