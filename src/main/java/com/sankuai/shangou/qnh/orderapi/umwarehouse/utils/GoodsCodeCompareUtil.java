package com.sankuai.shangou.qnh.orderapi.umwarehouse.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @description: 货架码排序工具类
 * @author: guolei28
 * @date: 2024/5/27
 * @time: 15:05
 * Copyright (C) 2024 Meituan
 * All rights reserved
 */
public class GoodsCodeCompareUtil {

    private static final String GOODS_CODE_PATTERN = "(\\d+)([a-zA-Z]*)|(^[a-zA-Z]+)(\\d+)";

    public static int compareByGoodsCode(String code1, String code2) {
        // 处理空值, null放在后面
        if (StringUtils.isBlank(code1) && StringUtils.isBlank(code2)) {
            return 0;
        }
        if (StringUtils.isBlank(code1)) {
            return 1;
        }
        if (StringUtils.isBlank(code2)) {
            return -1;
        }
        Pattern pattern = Pattern.compile(GOODS_CODE_PATTERN);
        Matcher matcher1 = pattern.matcher(code1);
        Matcher matcher2 = pattern.matcher(code2);

        if (matcher1.find() && matcher2.find()) {
            // 数字前缀比较
            if (matcher1.group(1) != null && matcher2.group(1) != null) {
                int num1 = Integer.parseInt(matcher1.group(1));
                int num2 = Integer.parseInt(matcher2.group(1));
                int numComp = Integer.compare(num1, num2);
                if (numComp != 0) {
                    return numComp;
                }
                // 字母后缀比较
                String alpha1 = matcher1.group(2);
                String alpha2 = matcher2.group(2);
                if (alpha1 != null && alpha2 != null) {
                    int alphaComp = alpha1.compareTo(alpha2);
                    if (alphaComp != 0) {
                        return alphaComp;
                    }
                }
            }
            // 字母前缀和数字后缀比较
            String prefixAlpha1 = matcher1.group(3);
            String prefixAlpha2 = matcher2.group(3);
            if (prefixAlpha1 != null && prefixAlpha2 != null) {
                int alphaComp = prefixAlpha1.compareTo(prefixAlpha2);
                if (alphaComp != 0) {
                    return alphaComp;
                }

                int num1 = Integer.parseInt(matcher1.group(4));
                int num2 = Integer.parseInt(matcher2.group(4));
                return Integer.compare(num1, num2);
            }
        }
        // 默认比较
        return code1.compareTo(code2);
    }
}
