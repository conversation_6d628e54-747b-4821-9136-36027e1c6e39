package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.constant.pc.Constants;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import com.sankuai.shangou.qnh.orderapi.exception.pc.ParamInvalidException;
import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import com.sankuai.shangou.qnh.orderapi.utils.pc.LionUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2023/12/8
 * @Description:
 */
@Setter
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(
        description = "PC客户端订单分页查询请求"
)
public class OrderPCFuseQueryRequest extends PageRequest implements BaseRequest {

    /**
     * 搜索关键字(订单序号、手机号、订单号)
     */
    private String smartQuery;

    /**
     * 收货人姓名
     */
    private String receiverName;

    /**
     * 收货人地址
     */
    private String receiverAddress;

    /**
     * 订单创建开始时间
     */
    private String createStartTime;

    /**
     * 订单创建结束时间
     */
    private String createEndTime;

    /**
     * 预计送达开始时间
     */
    private String arrivalStartTime;

    /**
     * 预计送达结束时间
     */
    private String arrivalEndTime;

    /**
     * 订单完成时间开始点，毫秒
     */
    private Long completeTimeStart;

    /**
     * 订单完成时间结束点，毫秒
     */
    private Long completeTimeEnd;

    /**
     * 核销时间开始点，毫秒
     */
    private Long posTimeStart;

    /**
     * 核销时间结束点，毫秒
     */
    private Long posTimeEnd;

    /**
     * 状态
     */
    private List<Integer> status;


    /**
     * 渠道列表
     */
    private List<String> channelIds;


    /**
     * 退款标识
     */
    private List<String> refundTypes;


    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 是否是会员
     */
    private String hasMemberCard;

    /**
     * 会员卡号
     */
    private String memberCard;


    /**
     * 摊位ID
     */
    @FieldDoc(
            description = "摊位ID集合"
    )
    private List<Long> boothIds;

    /**
     * 问题订单(1进货价为空)
     */
    @FieldDoc(
            description = "问题订单(1进货价为空)"
    )
    private Integer orderProblemType;

    /**
     * 门店ID集合
     */
    private List<Long> poiIdList;

    /**
     * 仓库id
     */
    private List<Long> warehouseIdList;

    /**
     * 核销状态列表
     */
    public List<String> writeOffStatus;

    /**
     * 订单标识列表
     */
    public List<Integer> orderMarks;

    /**
     * 自配送类型列表
     */
    private List<Integer> deliveryChannelIds;

    /**
     * 平台配送类型列表
     */
    private List<Integer> originalDistributeTypes;

    /**
     * 融合订单状态
     */
    public List<String> fuseOrderStatus;

    /**
     * 配送方式
     */
    private Integer distributeMethod;

    /**
     * 导出字段
     */
    public List<String> exportFields;

    /**
     * 订单标识字符串列表
     */
    public List<String> orderMarkStrList;

    /**
     * 骑手牵牛花账号
     */
    private String riderAccountName;

    /**
     *送达时间-起始（13位时间戳）
     */
    private Long finishDeliveryStartTime;

    /**
     *送达时间-截止（13位时间戳）
     */
    private Long finishDeliveryEndTime;



    @Override
    public void selfCheck() {

        if (StringUtils.isNotEmpty(smartQuery)) {
            if (smartQuery.length() < Constants.ORDER_FUZZY_SEARCH_SPLIT_LENGTH && !isDigit(smartQuery)) {
                throw new ParamInvalidException("搜索订单号至少输入4位", "");
            }
            Integer limitLength = LionUtils.getOrderParamsLengthLimitConfig();
            if (smartQuery.length() > limitLength) {
                throw new ParamInvalidException("关键字搜索最多输入" + limitLength + "个字符", "");
            }
        }

        if (StringUtils.isNotEmpty(skuName)) {
            AssertUtil.isTrue(skuName.length() <= 25, "商品名称最多输入25个字符");
        }

        if (StringUtils.isNotEmpty(receiverName)) {
            AssertUtil.isTrue(receiverName.length() <= 10, "顾客名称最多输入10个字符");
        }


        if (StringUtils.isNotEmpty(receiverAddress)) {
            AssertUtil.isTrue(receiverAddress.length() <= 25, "顾客地址最多输入25个字符");
        }

        if (StringUtils.isNotEmpty(memberCard)) {
            AssertUtil.isTrue(memberCard.length() <= 25, "会员卡号最多输入25位数字");
        }

    }

    private boolean isDigit(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }
        String reg="^\\d+$";
        return str.matches(reg);
    }
}
