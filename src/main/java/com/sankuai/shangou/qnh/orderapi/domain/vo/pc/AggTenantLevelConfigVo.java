package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.linz.boot.util.Fun;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.shangou.saas.tenant.thrift.dto.config.AggTenantLevelConfigDto;
import lombok.Data;
import lombok.ToString;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/4/26
 */
@Data
@ToString
public class AggTenantLevelConfigVo {

    @FieldDoc(
            description = "租户ID"
    )
    public Long tenantId;

    @FieldDoc(
            description = "租户开通的业务模块列表"
    )
    public List<AggBizModuleVo> openBizModules;

    @FieldDoc(
            description = "租户开通的业务模块列表"
    )
    public List<ConfigContentVo> tenantConfigs;

    public static AggTenantLevelConfigVo fromDto(AggTenantLevelConfigDto dto) {
        if (Objects.isNull(dto)) {
            return null;
        }
        AggTenantLevelConfigVo vo = new AggTenantLevelConfigVo();
        vo.setTenantId(dto.getTenantId());
        vo.setOpenBizModules(Fun.map(dto.getOpenBizModules(), AggBizModuleVo::fromDto));
        vo.setTenantConfigs(Fun.map(dto.getTenantConfigs(), ConfigContentVo::fromDto));
        return vo;
    }

}
