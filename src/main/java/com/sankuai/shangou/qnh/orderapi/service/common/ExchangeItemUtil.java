package com.sankuai.shangou.qnh.orderapi.service.common;


import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import com.meituan.shangou.saas.o2o.dto.model.CombinationProductModel;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.*;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.model.AfterSaleApplyDetail;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.model.OrderItem;
import com.meituan.shangou.saas.order.management.client.utils.ExchangeUtil;
import com.meituan.shangou.saas.order.platform.client.dto.model.AfsDetailExchangeItemModel;
import com.meituan.shangou.saas.order.platform.client.dto.model.AfsDetailExchangeModel;
import com.meituan.shangou.saas.order.platform.common.model.OrderItemExchangeModel;
import com.sankuai.shangou.qnh.orderapi.domain.vo.AfsExchangeProductVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.ExchangeProductVo;

import com.sankuai.shangou.qnh.orderapi.domain.vo.SubProductVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.*;
import com.sankuai.shangou.qnh.orderapi.utils.CombinationProductUtil;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @since 2024/7/16
 **/
@Slf4j
public class ExchangeItemUtil {



    public static List<ExchangeProductVo> getExchangeProductList(OCMSOrderItemVO currentItem, List<OCMSOrderItemVO> ocmsOrderItemVOList) {
        ExchangeUtil.ExchangeDO exchangeDO = ExchangeUtil.loadExchangeMetaData(currentItem.getExtData());
        //如果是换货商品，直接不展示，过滤掉
        if(exchangeDO.getExchangeSourceOrderItemId() > 0){
            return Lists.newArrayList();
        }
        List<ExchangeProductVo> exchangeProductVoList = Lists.newArrayList();
        for (OCMSOrderItemVO ocmsOrderItemVO : ocmsOrderItemVOList) {
            if (Objects.equals(ocmsOrderItemVO.getOrderItemId(), currentItem.getOrderItemId())) {
                continue;
            }
            ExchangeUtil.ExchangeDO exchangeItemDO = ExchangeUtil.loadExchangeMetaData(ocmsOrderItemVO.getExtData());
            if(exchangeItemDO.getExchangeSourceOrderItemId() == currentItem.getOrderItemId()){
                exchangeProductVoList.add(buildExchangedProduct(ocmsOrderItemVO, exchangeItemDO));
            }
        }
        return exchangeProductVoList;
    }

    public static List<ExchangeProductVo> getExchangeProductList(ProductInfoVo currentItem, List<ProductInfoVo> ocmsOrderItemVOList) {
        ExchangeUtil.ExchangeDO exchangeDO = ExchangeUtil.loadExchangeMetaData(currentItem.getExtData());
        //如果是换货商品，直接不展示，过滤掉
        if(exchangeDO.getExchangeSourceOrderItemId() > 0){
            return Lists.newArrayList();
        }
        List<ExchangeProductVo> exchangeProductVoList = Lists.newArrayList();
        for (ProductInfoVo ocmsOrderItemVO : ocmsOrderItemVOList) {
            if (Objects.equals(ocmsOrderItemVO.getOrderItemId(), currentItem.getOrderItemId())) {
                continue;
            }
            ExchangeUtil.ExchangeDO exchangeItemDO = ExchangeUtil.loadExchangeMetaData(ocmsOrderItemVO.getExtData());
            if(exchangeItemDO.getExchangeSourceOrderItemId() == currentItem.getOrderItemId()){
                exchangeProductVoList.add(buildExchangedProduct(ocmsOrderItemVO, exchangeItemDO));
            }
        }
        return exchangeProductVoList;
    }

    public static List<ExchangeProductVo> getChildExchangeProductList(CombinationChildProductVo currentItem, List<CombinationChildProductVo> combinationChildProductVoList) {
        ExchangeUtil.ExchangeDO exchangeDO = ExchangeUtil.loadExchangeMetaData(currentItem.getExtData());
        //如果是换货商品，直接不展示，过滤掉
        if(exchangeDO.getExchangeSourceOrderItemId() > 0){
            return Lists.newArrayList();
        }
        List<ExchangeProductVo> exchangeProductVoList = Lists.newArrayList();
        for (CombinationChildProductVo ocmsOrderItemVO : combinationChildProductVoList) {
            if (Objects.equals(ocmsOrderItemVO.getServiceId(), currentItem.getServiceId())) {
                continue;
            }
            ExchangeUtil.ExchangeDO exchangeItemDO = ExchangeUtil.loadExchangeMetaData(ocmsOrderItemVO.getExtData());
            if(Objects.equals(exchangeItemDO.getExchangeSourceOrderItemId(), currentItem.getServiceId())){
                exchangeProductVoList.add(buildChildExchangedProduct(ocmsOrderItemVO, exchangeItemDO));
            }
        }
        return exchangeProductVoList;
    }

    private static ExchangeProductVo buildChildExchangedProduct(CombinationChildProductVo ocmsOrderItemVO, ExchangeUtil.ExchangeDO exchangeItemDO) {
        ExchangeProductVo exchangeProductVo = new ExchangeProductVo();
        exchangeProductVo.setSkuId(ocmsOrderItemVO.getSkuId());
        exchangeProductVo.setSkuName(ocmsOrderItemVO.getName());
        exchangeProductVo.setMultiPicUrl(StringUtils.isNotEmpty(ocmsOrderItemVO.getPicUrl())
                ? Splitter.on(",").splitToList(ocmsOrderItemVO.getPicUrl()) : new ArrayList<>());
        exchangeProductVo.setPicUrl(ocmsOrderItemVO.getPicUrl());
        exchangeProductVo.setSpecification(ocmsOrderItemVO.getSpec());
        exchangeProductVo.setUpcCode(ocmsOrderItemVO.getUpcCode());
        exchangeProductVo.setExchangeToCnt(Double.parseDouble(ocmsOrderItemVO.getQuantity()));
        exchangeProductVo.setExchangeFromCnt((double) exchangeItemDO.getOrderQuantity());
        exchangeProductVo.setErpItemCode(ocmsOrderItemVO.getErpCode());
        exchangeProductVo.setOrderItemId(Long.valueOf(ocmsOrderItemVO.getCombinationItemId()));
        exchangeProductVo.setSalePrice(
                ConverterUtils.formatMoney(ConverterUtils.divideHundred(ocmsOrderItemVO.getSalePrice())));
        exchangeProductVo.setOriginPrice(
                ConverterUtils.formatMoney(ConverterUtils.divideHundred(ocmsOrderItemVO.getSalePrice())));
        exchangeProductVo.setTotalPrice(ConverterUtils
                .formatMoney(ConverterUtils.divideHundred(ConverterUtils.valueOf(ocmsOrderItemVO.getSalePrice())
                        .multiply(ConverterUtils.valueOf(ocmsOrderItemVO.getQuantity())).intValue())));
        exchangeProductVo.setSellUnit(ocmsOrderItemVO.getSellUnit());
        exchangeProductVo.setQuantity(CombinationProductUtil.formatThreeDecimal(ocmsOrderItemVO.getQuantity()));
        exchangeProductVo.setWeight(ocmsOrderItemVO.getWeight());
        return exchangeProductVo;
    }

    private static ExchangeProductVo buildExchangedProduct(ProductInfoVo ocmsOrderItemVO, ExchangeUtil.ExchangeDO exchangeItemDO) {
        ExchangeProductVo exchangeProductVo = new ExchangeProductVo();
        exchangeProductVo.setSkuId(ocmsOrderItemVO.getSkuId());
        exchangeProductVo.setSkuName(ocmsOrderItemVO.getSkuName());
        exchangeProductVo.setMultiPicUrl(StringUtils.isNotEmpty(ocmsOrderItemVO.getPicUrl())
                ? Splitter.on(",").splitToList(ocmsOrderItemVO.getPicUrl()) : new ArrayList<>());
        exchangeProductVo.setPicUrl(ocmsOrderItemVO.getPicUrl());
        exchangeProductVo.setSpecification(ocmsOrderItemVO.getSpecification());
        exchangeProductVo.setUpcCode(ocmsOrderItemVO.getUpcCode());
        exchangeProductVo.setExchangeToCnt(Double.valueOf(ocmsOrderItemVO.getCount()));
        exchangeProductVo.setExchangeFromCnt((double) exchangeItemDO.getExchangeSourceOrderItemCnt());
        exchangeProductVo.setErpItemCode(ocmsOrderItemVO.getErpItemCode());
        exchangeProductVo.setOrderItemId(ocmsOrderItemVO.getOrderItemId());
        exchangeProductVo.setSalePrice(
                ConverterUtils.formatMoney(ConverterUtils.divideHundred(ocmsOrderItemVO.getCurrentPrice())));
        exchangeProductVo.setOriginPrice(
                ConverterUtils.formatMoney(ConverterUtils.divideHundred(ocmsOrderItemVO.getOriginalPrice())));
        exchangeProductVo.setTotalPrice(ConverterUtils.formatMoney(ConverterUtils
                .divideHundred(new BigDecimal(Optional.ofNullable(ocmsOrderItemVO.getCurrentPrice()).orElse(0))
                        .multiply(new BigDecimal(Optional.ofNullable(ocmsOrderItemVO.getCount()).orElse(0)))
                        .intValue())));
        exchangeProductVo.setSellUnit(ocmsOrderItemVO.getSellUnit());
        exchangeProductVo.setQuantity(String.valueOf(ocmsOrderItemVO.getCount()));
        exchangeProductVo.setWeight(new BigDecimal(Optional.ofNullable(ocmsOrderItemVO.getWeight()).orElse(0))
                .multiply(new BigDecimal(Optional.ofNullable(ocmsOrderItemVO.getCount()).orElse(0)))
                .setScale(0, RoundingMode.HALF_UP).toString());
        return exchangeProductVo;
    }


    private static ExchangeProductVo buildExchangedProduct(OCMSOrderItemVO ocmsOrderItemVO, ExchangeUtil.ExchangeDO exchangeItemDO) {
        ExchangeProductVo exchangeProductVo = new ExchangeProductVo();
        exchangeProductVo.setSkuId(ocmsOrderItemVO.getInstoreSkuId2());
        exchangeProductVo.setSkuName(ocmsOrderItemVO.getSkuName());
        exchangeProductVo.setMultiPicUrl(StringUtils.isNotEmpty(ocmsOrderItemVO.getPicUrl())
                ? Splitter.on(",").splitToList(ocmsOrderItemVO.getPicUrl()) : new ArrayList<>());
        exchangeProductVo.setPicUrl(ocmsOrderItemVO.getPicUrl());
        exchangeProductVo.setSpecification(ocmsOrderItemVO.getSpecification());
        exchangeProductVo.setUpcCode(ocmsOrderItemVO.getBarCode());
        exchangeProductVo.setExchangeToCnt(Double.valueOf(ocmsOrderItemVO.getQuantity()));
        exchangeProductVo.setExchangeFromCnt((double) exchangeItemDO.getExchangeSourceOrderItemCnt());
        exchangeProductVo.setErpItemCode(ocmsOrderItemVO.getErpItemCode());
        exchangeProductVo.setOrderItemId(ocmsOrderItemVO.getOrderItemId());
        exchangeProductVo.setSalePrice(
                ConverterUtils.formatMoney(ConverterUtils.divideHundred(ocmsOrderItemVO.getCurrentPrice())));
        exchangeProductVo.setTotalPrice(ConverterUtils.formatMoney(ConverterUtils
                .divideHundred(new BigDecimal(Optional.ofNullable(ocmsOrderItemVO.getCurrentPrice()).orElse(0))
                        .multiply(new BigDecimal(Optional.ofNullable(ocmsOrderItemVO.getQuantity()).orElse(0)))
                        .intValue())));
        exchangeProductVo.setOriginPrice(
                ConverterUtils.formatMoney(ConverterUtils.divideHundred(ocmsOrderItemVO.getOriginalPrice())));
        exchangeProductVo.setSellUnit(ocmsOrderItemVO.getSellUnit());
        exchangeProductVo.setQuantity(String.valueOf(ocmsOrderItemVO.getQuantity()));
        exchangeProductVo.setWeight(new BigDecimal(Optional.ofNullable(ocmsOrderItemVO.getChannelWeight()).orElse(0))
                .multiply(new BigDecimal(Optional.ofNullable(ocmsOrderItemVO.getQuantity()).orElse(0)))
                .setScale(0, RoundingMode.HALF_UP).toString());
        return exchangeProductVo;
    }
    //todo未处理数量问题
    public static void fillRefundExchangeInfo(OrderVO orderVO, OCMSOrderVO ocmsOrderVO)  {
        if(Objects.isNull(orderVO.getOrderRefundInfo()) || (Objects.isNull(orderVO.getOrderRefundInfo().getWaitAuditRefund()) && CollectionUtils.isNotEmpty(orderVO.getOrderRefundInfo().getWaitAuditRefundList()))){
            return;
        }
        Map<Long, List<ExchangeProductVo>> exchangeProductVoMap = Maps.newHashMap();
        Map<Long, Integer> itemId2Count = Maps.newHashMap();
        for (ProductVO productVO : orderVO.getProductList()) {
            if(CollectionUtils.isNotEmpty(productVO.getExchangeProductVoList())){
                exchangeProductVoMap.put(productVO.getOrderItemId(), productVO.getExchangeProductVoList());
                Double sum = productVO.getExchangeProductVoList().stream().map(ExchangeProductVo::getExchangeFromCnt).reduce(0D, Double::sum);
                if (Objects.equals(sum, productVO.getCount())){
                    itemId2Count.put(productVO.getOrderItemId(), productVO.getCount());
                }
            }
            if (CollectionUtils.isNotEmpty(productVO.getSubProductVoList())) {
                for (SubProductVo subProductVo : productVO.getSubProductVoList()) {
                    if (CollectionUtils.isNotEmpty(subProductVo.getExchangeProductVoList())) {
                        Double sum =subProductVo.getExchangeProductVoList().stream().map(ExchangeProductVo::getExchangeFromCnt).reduce(0D, Double::sum);
                        if (new BigDecimal(subProductVo.getQuantity()).compareTo(BigDecimal.valueOf(sum)) == 0){
                            itemId2Count.put(subProductVo.getServiceId(), productVO.getCount());
                        }
                        exchangeProductVoMap.put(subProductVo.getServiceId(), subProductVo.getExchangeProductVoList());
                    }
                }
            }
        }
        if (MapUtils.isEmpty(exchangeProductVoMap)) {
            return;
        }
        if (Objects.isNull(orderVO.getOrderRefundInfo().getWaitAuditRefund()) || CollectionUtils
                .isEmpty(orderVO.getOrderRefundInfo().getWaitAuditRefund().getRefundApplyRecordDetailVOList())) {
            return;
        }
        for (RefundApplyRecordDetailVO refundApplyRecordDetailVO : orderVO.getOrderRefundInfo().getWaitAuditRefund().getRefundApplyRecordDetailVOList()) {
            if (!exchangeProductVoMap.containsKey(refundApplyRecordDetailVO.getOrderItemId())){
                continue;
            }
            refundApplyRecordDetailVO.setExchangeProductVoList(exchangeProductVoMap.get(refundApplyRecordDetailVO.getOrderItemId()));

            if (CollectionUtils.isNotEmpty(refundApplyRecordDetailVO.getSubProductVoList())) {
                for (SubProductVo subProductVo : refundApplyRecordDetailVO.getSubProductVoList()) {
                    if (exchangeProductVoMap.containsKey(subProductVo.getServiceId())) {
                        subProductVo.setExchangeProductVoList(exchangeProductVoMap.get(subProductVo.getServiceId()));
                    }
                }
            }
        }



    }


    public static List<ExchangeProductVo> buildExchangeProductVoList(Long orderItemId, OrderDetailVO orderDetail){
        List<ExchangeProductVo> exchangeProductVoList = new ArrayList<>();
        Map<Long, List<ProductVOForRefund>> productVOForRefundMap = new HashMap<>();
        try{
            if(CollectionUtils.isNotEmpty(orderDetail.getProductList())){
                List<ProductVOForRefund> productList = orderDetail.getProductList();
                productVOForRefundMap = Optional.ofNullable(productList).orElse(new ArrayList<>()).stream().collect(Collectors.groupingBy(ProductVOForRefund::getOrderItemId));
            }

            if(MapUtils.isEmpty(productVOForRefundMap)){
                return exchangeProductVoList;
            }
            List<ProductVOForRefund> productVOForRefunds = productVOForRefundMap.get(orderItemId);
            if(CollectionUtils.isNotEmpty(productVOForRefunds)){
                productVOForRefunds.forEach(productVOForRefund -> exchangeProductVoList.addAll(productVOForRefund.getExchangeProductVoList()));
            }
        }catch (Exception e){
            log.error("buildExchangeProductVoList error ", e);
        }
        return exchangeProductVoList;
    }

    /**
     * 构建组合品的换货商品列表
     * 
     * @param currentItem
     * @param combinationChildProductModelList
     * @return
     */
    public static List<ExchangeProductVo> getChildExchangeProductList(CombinationProductModel currentItem,
            List<CombinationProductModel> combinationChildProductModelList) {
        ExchangeUtil.ExchangeDO exchangeDO = ExchangeUtil.loadExchangeMetaData(currentItem.getExtData());
        // 如果是换货商品，直接不展示，过滤掉
        if (exchangeDO.getExchangeSourceOrderItemId() > 0) {
            return Lists.newArrayList();
        }
        List<ExchangeProductVo> exchangeProductVoList = Lists.newArrayList();
        for (CombinationProductModel ocmsOrderItemModel : combinationChildProductModelList) {
            if (Objects.equals(ocmsOrderItemModel.getServiceId(), currentItem.getServiceId())) {
                continue;
            }
            ExchangeUtil.ExchangeDO exchangeItemDO = ExchangeUtil.loadExchangeMetaData(ocmsOrderItemModel.getExtData());
            if (exchangeItemDO.getExchangeSourceOrderItemId() == currentItem.getServiceId()) {
                exchangeProductVoList.add(buildChildExchangedProduct(ocmsOrderItemModel, exchangeItemDO));
            }
        }
        return exchangeProductVoList;
    }

    private static ExchangeProductVo buildChildExchangedProduct(CombinationProductModel ocmsOrderItemModel,
            ExchangeUtil.ExchangeDO exchangeItemDO) {
        ExchangeProductVo exchangeProductVo = new ExchangeProductVo();
        exchangeProductVo.setSkuId(ocmsOrderItemModel.getSkuId());
        exchangeProductVo.setSkuName(ocmsOrderItemModel.getName());
        exchangeProductVo.setMultiPicUrl(StringUtils.isNotEmpty(ocmsOrderItemModel.getPicUrl())
                ? Splitter.on(",").splitToList(ocmsOrderItemModel.getPicUrl()) : new ArrayList<>());
        exchangeProductVo.setPicUrl(ocmsOrderItemModel.getPicUrl());
        exchangeProductVo.setSpecification(ocmsOrderItemModel.getSpec());
        exchangeProductVo.setUpcCode(ocmsOrderItemModel.getUpcCode());
        exchangeProductVo.setExchangeToCnt(Double.parseDouble(ocmsOrderItemModel.getQuantity()));
        exchangeProductVo.setExchangeFromCnt((double)exchangeItemDO.getExchangeSourceOrderItemCnt());
        exchangeProductVo.setErpItemCode(ocmsOrderItemModel.getErpCode());
        exchangeProductVo.setOrderItemId(Long.valueOf(ocmsOrderItemModel.getCombinationItemId()));
        exchangeProductVo.setSalePrice(
                ConverterUtils.formatMoney(ConverterUtils.divideHundred(ocmsOrderItemModel.getSalePrice())));
        exchangeProductVo.setTotalPrice(ConverterUtils
                .formatMoney(ConverterUtils.divideHundred(ConverterUtils.valueOf(ocmsOrderItemModel.getSalePrice())
                        .multiply(ConverterUtils.valueOf(ocmsOrderItemModel.getQuantity())).intValue())));
        exchangeProductVo.setOriginPrice(
                ConverterUtils.formatMoney(ConverterUtils.divideHundred(ocmsOrderItemModel.getSalePrice())));
        exchangeProductVo.setQuantity(CombinationProductUtil.formatThreeDecimal(ocmsOrderItemModel.getQuantity()));
        exchangeProductVo.setWeight(ocmsOrderItemModel.getWeight());
        return exchangeProductVo;
    }


    /**
     * 获取换货商品列表
     * 
     * @param currentOrderItem
     * @param orderItemList
     * @return
     */
    public static List<ExchangeProductVo> getExchangeProductList(OrderItem currentOrderItem,
            List<OrderItem> orderItemList) {
        ExchangeUtil.ExchangeDO exchangeDO = ExchangeUtil.loadExchangeMetaData(currentOrderItem.getExtData());
        // 如果是换货商品，直接不展示，过滤掉
        if (exchangeDO.getExchangeSourceOrderItemId() > 0) {
            return Lists.newArrayList();
        }
        List<ExchangeProductVo> exchangeProductVoList = Lists.newArrayList();
        for (OrderItem orderItem : orderItemList) {
            if (Objects.equals(currentOrderItem.getOrderItemId(), orderItem.getOrderItemId())) {
                continue;
            }
            ExchangeUtil.ExchangeDO exchangeItemDO = ExchangeUtil.loadExchangeMetaData(orderItem.getExtData());
            if (exchangeItemDO.getExchangeSourceOrderItemId() == currentOrderItem.getOrderItemId()) {
                exchangeProductVoList.add(buildExchangedProduct(orderItem, exchangeItemDO));
            }
        }
        return exchangeProductVoList;
    }

    private static ExchangeProductVo buildExchangedProduct(OrderItem orderItem,
            ExchangeUtil.ExchangeDO exchangeItemDO) {
        ExchangeProductVo exchangeProductVo = new ExchangeProductVo();
        exchangeProductVo.setSkuId(orderItem.getInstoreSkuId());
        exchangeProductVo.setSkuName(orderItem.getSkuName());
        exchangeProductVo.setMultiPicUrl(StringUtils.isNotEmpty(orderItem.getPicUrl())
                ? Splitter.on(",").splitToList(orderItem.getPicUrl()) : new ArrayList<>());
        exchangeProductVo.setPicUrl(orderItem.getPicUrl());
        exchangeProductVo.setSpecification(orderItem.getSpecification());
        exchangeProductVo.setUpcCode(orderItem.getBarCode());
        exchangeProductVo.setExchangeToCnt(Double.valueOf(orderItem.getQuantity()));
        exchangeProductVo.setExchangeFromCnt((double)exchangeItemDO.getExchangeSourceOrderItemCnt());
        exchangeProductVo.setErpItemCode(orderItem.getErpItemCode());
        exchangeProductVo.setOrderItemId(orderItem.getOrderItemId());
        exchangeProductVo
                .setSalePrice(ConverterUtils.formatMoney(ConverterUtils.divideHundred(orderItem.getCurrentPrice())));
        exchangeProductVo.setTotalPrice(ConverterUtils.formatMoney(
                ConverterUtils.divideHundred(new BigDecimal(Optional.ofNullable(orderItem.getCurrentPrice()).orElse(0))
                        .multiply(new BigDecimal(Optional.ofNullable(orderItem.getQuantity()).orElse(0))).intValue())));
        exchangeProductVo.setOriginPrice(
                ConverterUtils.formatMoney(ConverterUtils.divideHundred(orderItem.getOriginalPrice())));
        exchangeProductVo.setSellUnit(orderItem.getSellUnit());
        exchangeProductVo.setQuantity(String.valueOf(orderItem.getQuantity()));
        exchangeProductVo.setWeight(new BigDecimal(Optional.ofNullable(orderItem.getChannelWeight()).orElse(0))
                .multiply(new BigDecimal(Optional.ofNullable(orderItem.getQuantity()).orElse(0)))
                .setScale(0, RoundingMode.HALF_UP).toString());
        return exchangeProductVo;
    }


    /**
     * 构建售后组合品的换货商品列表
     *
     * @param afsItemExchangeModelList
     * @param combinationProductMap
     * @param serviceId 售后组合品对应正单组合品的serviceId
     * @return
     */
    public static AfsExchangeProductVo buildAfsCombinationProduct(String afsItemExchangeModelList,
            Map<Long, CombinationChildProductVo> combinationProductMap, Long serviceId) {
        try {
            log.info("buildAfsCombinationProduct afsItemExchangeModelList {}, serviceId:{}", afsItemExchangeModelList, serviceId);
            if (StringUtils.isBlank(afsItemExchangeModelList) || Objects.isNull(serviceId)) {
                return null;
            }
            List<AfsDetailExchangeModel> afsItemExchangeModels = JSON.parseArray(afsItemExchangeModelList,
                    AfsDetailExchangeModel.class);
            if (CollectionUtils.isEmpty(afsItemExchangeModels)) {
                return null;
            }
            // 获取退单明细的换货信息，通过serviceId为不为空表示子商品的换货信息信息
            Map<Long, AfsDetailExchangeModel> exchangeModelMap = afsItemExchangeModels.stream()
                    .filter(afsDetailExchangeModel -> Objects.nonNull(afsDetailExchangeModel.getServiceId()))
                    .collect(Collectors.toMap(AfsDetailExchangeModel::getServiceId, Function.identity()));
            if (exchangeModelMap.isEmpty()) {
                return null;
            }
            AfsDetailExchangeModel afsDetailExchangeModel = exchangeModelMap.get(serviceId);
            // 子商品无换货信息
            if (Objects.isNull(afsDetailExchangeModel)) {
                return null;
            }
            List<AfsDetailExchangeItemModel> exchangeItemList = afsDetailExchangeModel.getAfsItemExchangeModelList();
            // 子商品无换货明细
            if (CollectionUtils.isEmpty(exchangeItemList)) {
                return null;
            }
            return buildCombinationAfsExchangeProductVo(combinationProductMap, serviceId, exchangeItemList,
                    afsDetailExchangeModel);
        } catch (Exception e) {
            log.error("buildAfsCombinationProduct: afsItemExchangeModelList:{}, combinationProductMap:{}, serviceId:{}",
                    afsItemExchangeModelList, combinationProductMap, serviceId, e);
        }
        return null;
    }

    /**
     * 构建售后商品对应单换货信息
     *
     * @param combinationProductMap 正单组合子商品serviceId对应的map
     * @param serviceId 退单子商品对应的serviceId
     * @param exchangeItemList 退单子商品对应的换货商品退款明细
     * @param afsDetailExchangeModel 退单子商品对应的换货信息
     * @return
     */
    private static AfsExchangeProductVo buildCombinationAfsExchangeProductVo(
            Map<Long, CombinationChildProductVo> combinationProductMap, Long serviceId,
            List<AfsDetailExchangeItemModel> exchangeItemList, AfsDetailExchangeModel afsDetailExchangeModel) {
        CombinationChildProductVo originalCombinationProduct = combinationProductMap.get(serviceId);
        // 获取原单的换货数量信息
        int exchangeSourceOrderItemCnt = OrderItemExchangeModel.build(originalCombinationProduct.getExtData())
                .getExchangeSourceOrderItemCnt();
        AfsExchangeProductVo afsExchangeProductVo = AfsExchangeProductVo.builder()
                .name(originalCombinationProduct.getName()).skuId(originalCombinationProduct.getSkuId())
                .picUrl(originalCombinationProduct.getPicUrl()).picUrl(originalCombinationProduct.getPicUrl())
                .upcCode(StringUtils.isNotEmpty(originalCombinationProduct.getUpcCode())
                        ? Splitter.on(",").splitToList(originalCombinationProduct.getUpcCode()) : new ArrayList<>())
                .erpCode(originalCombinationProduct.getErpCode()).specification(originalCombinationProduct.getSpec())
                // 退货数量*商品重量
                .weight(ConverterUtils.valueOf(originalCombinationProduct.getWeight())
                        .multiply(ConverterUtils.valueOf(afsDetailExchangeModel.getAfsExchangeFromCount()))
                        .setScale(0, RoundingMode.HALF_UP).toString())
                // 原商品：订单上的销售数量-缺货数量
                .quantity(CombinationProductUtil.formatThreeDecimal(ConverterUtils
                        .valueOf(originalCombinationProduct.getQuantity())
                        .subtract(BigDecimal.valueOf(exchangeSourceOrderItemCnt)).setScale(3, RoundingMode.HALF_UP)))
                .refundCount(
                        CombinationProductUtil.formatThreeDecimal(afsDetailExchangeModel.getAfsExchangeFromCount()))
                .salePrice(ConverterUtils
                        .formatMoney(ConverterUtils.divideHundred(originalCombinationProduct.getSalePrice())))
                .originPrice(ConverterUtils
                        .formatMoney(ConverterUtils.divideHundred(originalCombinationProduct.getSalePrice())))
                // 商品售价*退货数量
                .itemAmt(ConverterUtils.formatMoney(
                        ConverterUtils.divideHundred(ConverterUtils.valueOf(originalCombinationProduct.getSalePrice())
                                .multiply(ConverterUtils.valueOf(afsDetailExchangeModel.getAfsExchangeFromCount()))
                                .setScale(2, RoundingMode.HALF_UP).toString())))
                .build();
        // 构建换货商品信息
        List<AfsExchangeProductVo.ExchangeToItem> exchangeToItems = Lists.newArrayList();
        for (AfsDetailExchangeItemModel exchangeItem : exchangeItemList) {
            // 获取订单明细中对应的换货信息
            CombinationChildProductVo combinationProduct = combinationProductMap.get(exchangeItem.getOrderItemId());
            if (Objects.isNull(combinationProduct)) {
                log.error("buildAfsCombinationProduct: 退单查询正单的子商品换货商品信息为空, orderItemId:{}, combinationProductMap:{}",
                        exchangeItem.getOrderItemId(), combinationProductMap);
                continue;
            }
            AfsExchangeProductVo.ExchangeToItem exchangeToItem = AfsExchangeProductVo.ExchangeToItem.builder()
                    .name(combinationProduct.getName()).skuId(combinationProduct.getSkuId())
                    .unitPrice(ConverterUtils.valueOf(combinationProduct.getSalePrice())).picUrl(combinationProduct.getPicUrl())
                    .upcCode(StringUtils.isNotEmpty(combinationProduct.getUpcCode())
                            ? Splitter.on(",").splitToList(combinationProduct.getUpcCode()) : new ArrayList<>())
                    .erpCode(combinationProduct.getErpCode()).specification(combinationProduct.getSpec())
                    // 退货数量*商品重量
                    .weight(ConverterUtils.valueOf(combinationProduct.getWeight())
                            .multiply(ConverterUtils.valueOf(exchangeItem.getCount())).setScale(0, RoundingMode.HALF_UP)
                            .toString())
                    .quantity(CombinationProductUtil.formatThreeDecimal(combinationProduct.getQuantity()))
                    .refundCount(CombinationProductUtil.formatThreeDecimal(exchangeItem.getCount()))
                    // 商品售价*退货数量
                    .itemAmt(ConverterUtils.formatMoney(
                            ConverterUtils.divideHundred(ConverterUtils.valueOf(combinationProduct.getSalePrice())
                                    .multiply(ConverterUtils.valueOf(exchangeItem.getCount())).toString())))
                    .originPrice(ConverterUtils.formatMoney(ConverterUtils.divideHundred(combinationProduct.getSalePrice())))
                    .salePrice(ConverterUtils.formatMoney(ConverterUtils.divideHundred(combinationProduct.getSalePrice())))
                    .orderItemId(combinationProduct.getServiceId()).build();
            exchangeToItems.add(exchangeToItem);
        }
        afsExchangeProductVo.setExchangeToItemList(exchangeToItems);
        return afsExchangeProductVo;
    }

    /**
     * 构建售后普通商品的换货商品列表
     * 
     * @param afterSaleApplyDetail
     * @param productInfoVoMap
     * @return
     */
    public static AfsExchangeProductVo buildAfsExchangeProduct(AfterSaleApplyDetail afterSaleApplyDetail,
            Map<Long, ProductInfoVo> productInfoVoMap) {
        try {
            // 获取退单明细对应换货信息
            String afsItemExchangeModelList = afterSaleApplyDetail.getAfsItemExchangeModelList();
            if (StringUtils.isBlank(afsItemExchangeModelList)) {
                return null;
            }
            // 退单明细的换货信息，包含父商品的换货和对应的子商品的换货信息
            List<AfsDetailExchangeModel> afsItemExchangeModels = JSON.parseArray(afsItemExchangeModelList,
                    AfsDetailExchangeModel.class);
            if (CollectionUtils.isEmpty(afsItemExchangeModels)) {
                return null;
            }
            // 获取退单明细的换货信息，通过serviceId为空表示父商品的换货信息信息
            List<AfsDetailExchangeModel> afsExchangeModels = afsItemExchangeModels.stream()
                    .filter(afsDetailExchangeModel -> Objects.isNull(afsDetailExchangeModel.getServiceId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(afsExchangeModels)) {
                return null;
            }
            return buildAfsExchangeProductVo(productInfoVoMap, afterSaleApplyDetail.getOrderItemId(),
                    afsExchangeModels);
        } catch (Exception e) {
            log.error("buildAfsExchangeProduct: afterSaleApplyDetail:{}, productInfoVoMap:{}", afterSaleApplyDetail,
                    productInfoVoMap, e);
        }
        return null;
    }

    /**
     * 构建售后普通商品的换货商品列表
     *
     * @param afterSaleApplyDetail
     * @param orderItemMap
     * @return
     */
    public static AfsExchangeProductVo buildAfsExchangeProduct(OCMSAfterSaleApplyDetailVO afterSaleApplyDetail,
            Map<Long, OCMSOrderItemVO> orderItemMap) {
        try {
            // 获取退单明细对应换货信息
            String afsItemExchangeModelList = afterSaleApplyDetail.getAfsItemExchangeModelList();
            if (StringUtils.isBlank(afsItemExchangeModelList)) {
                return null;
            }
            // 退单明细的换货信息，包含父商品的换货和对应的子商品的换货信息
            List<AfsDetailExchangeModel> afsItemExchangeModels = JSON.parseArray(afsItemExchangeModelList,
                    AfsDetailExchangeModel.class);
            if (CollectionUtils.isEmpty(afsItemExchangeModels)) {
                return null;
            }
            // 获取退单明细的换货信息，通过serviceId为空表示父商品的换货信息信息
            List<AfsDetailExchangeModel> afsExchangeModels = afsItemExchangeModels.stream()
                    .filter(afsDetailExchangeModel -> Objects.isNull(afsDetailExchangeModel.getServiceId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(afsExchangeModels)) {
                return null;
            }
            // 获取退单明细对应原单信息
            OCMSOrderItemVO originalProduct = orderItemMap.get(afterSaleApplyDetail.getOrderItemId());
            // 获取原单的换货数量信息
            int exchangeSourceOrderItemCnt = OrderItemExchangeModel.build(originalProduct.getExtData())
                    .getExchangeSourceOrderItemCnt();
            // 原单换货信息，原单换出商品只会有一个明细
            AfsDetailExchangeModel afsDetailExchangeModel = afsExchangeModels.get(0);
            AfsExchangeProductVo afsExchangeProductVo = AfsExchangeProductVo.builder()
                    .name(originalProduct.getSkuName()).skuId(originalProduct.getInstoreSkuId2())
                    .picUrl(originalProduct.getPicUrl()).picUrl(originalProduct.getPicUrl())
                    .upcCode(StringUtils.isNotEmpty(originalProduct.getSkuCode())
                            ? Splitter.on(",").splitToList(originalProduct.getSkuCode()) : new ArrayList<>())
                    .erpCode(originalProduct.getErpItemCode()).specification(originalProduct.getSpecification())
                    // 退货数量*商品重量
                    .weight(BigDecimal.valueOf(Optional.ofNullable(originalProduct.getChannelWeight()).orElse(0))
                            .multiply(ConverterUtils.valueOf(afsDetailExchangeModel.getAfsExchangeFromCount()))
                            .setScale(0, RoundingMode.HALF_UP).toString())
                    // 原商品：订单上的销售数量-缺货数量
                    .refundCount(CombinationProductUtil.formatThreeDecimal(
                            BigDecimal.valueOf(Optional.ofNullable(originalProduct.getQuantity()).orElse(0))
                                    .subtract(ConverterUtils.valueOf(afsDetailExchangeModel.getAfsExchangeFromCount()))
                                    .setScale(3, RoundingMode.HALF_UP)))
                    // 原商品：订单上的销售数量-缺货数量
                    .quantity(
                            CombinationProductUtil.formatThreeDecimal(BigDecimal.valueOf(originalProduct.getQuantity())
                                    .subtract(BigDecimal.valueOf(exchangeSourceOrderItemCnt))
                                    .setScale(3, RoundingMode.HALF_UP)))
                    .refundCount(
                            CombinationProductUtil.formatThreeDecimal(afsDetailExchangeModel.getAfsExchangeFromCount()))
                    .salePrice(ConverterUtils.formatMoney(ConverterUtils.divideHundred(originalProduct.getCurrentPrice())))
                    .originPrice(ConverterUtils.formatMoney(ConverterUtils.divideHundred(originalProduct.getOriginalPrice())))
                    // 商品售价*退货数量
                    .itemAmt(ConverterUtils.formatMoney(ConverterUtils.divideHundred(BigDecimal
                            .valueOf(Optional.ofNullable(originalProduct.getCurrentPrice()).orElse(0))
                            .multiply(ConverterUtils.valueOf(afsDetailExchangeModel.getAfsExchangeFromCount()))
                            .intValue())))
                    .build();
            // 构建换货商品信息
            List<AfsExchangeProductVo.ExchangeToItem> exchangeToItems = Lists.newArrayList();
            for (AfsDetailExchangeModel afsExchangeModel : afsExchangeModels) {
                List<AfsDetailExchangeItemModel> exchangeItemList = afsExchangeModel.getAfsItemExchangeModelList();
                if (CollectionUtils.isEmpty(exchangeItemList)) {
                    continue;
                }
                for (AfsDetailExchangeItemModel exchangeItem : exchangeItemList) {
                    // 获取订单明细中对应的换货信息
                    OCMSOrderItemVO originalExchangeProduct = orderItemMap.get(exchangeItem.getOrderItemId());
                    if (Objects.isNull(originalExchangeProduct)) {
                        log.error("buildAfsExchangeProduct: 退单查询正单的换货商品信息为空, orderItemId:{}, originalExchangeProduct:{}",
                                exchangeItem.getOrderItemId(), originalExchangeProduct);
                        continue;
                    }
                    AfsExchangeProductVo.ExchangeToItem exchangeToItem = AfsExchangeProductVo.ExchangeToItem.builder()
                            .name(originalExchangeProduct.getSkuName())
                            .skuId(originalExchangeProduct.getInstoreSkuId2())
                            .unitPrice(new BigDecimal(originalExchangeProduct.getUnitPrice()))
                            .picUrl(originalExchangeProduct.getPicUrl())
                            .upcCode(StringUtils.isNotEmpty(originalExchangeProduct.getSkuCode())
                                    ? Splitter.on(",").splitToList(originalExchangeProduct.getSkuCode())
                                    : new ArrayList<>())
                            .erpCode(originalExchangeProduct.getErpItemCode())
                            .specification(originalExchangeProduct.getSpecification())
                            // 退货数量*商品重量
                            .weight(BigDecimal
                                    .valueOf(Optional.ofNullable(originalExchangeProduct.getChannelWeight()).orElse(0))
                                    .multiply(ConverterUtils.valueOf(exchangeItem.getCount()))
                                    .setScale(0, RoundingMode.HALF_UP).toString())
                            .quantity(String.valueOf(originalExchangeProduct.getQuantity()))
                            .refundCount(CombinationProductUtil.formatThreeDecimal(exchangeItem.getCount()))
                            // 商品售价*退货数量
                            .itemAmt(ConverterUtils.formatMoney(ConverterUtils.divideHundred(BigDecimal
                                    .valueOf(Optional.ofNullable(originalExchangeProduct.getCurrentPrice()).orElse(0))
                                    .multiply(ConverterUtils.valueOf(exchangeItem.getCount()))
                                    .intValue())))
                            .originPrice(ConverterUtils.formatMoney(ConverterUtils.divideHundred(originalExchangeProduct.getOriginalPrice())))
                            .salePrice(ConverterUtils.formatMoney(ConverterUtils.divideHundred(originalExchangeProduct.getCurrentPrice())))
                            .orderItemId(originalExchangeProduct.getOrderItemId()).build();
                    exchangeToItems.add(exchangeToItem);
                }
            }
            afsExchangeProductVo.setExchangeToItemList(exchangeToItems);
            return afsExchangeProductVo;
        } catch (Exception e) {
            log.error("buildAfsExchangeProduct: afterSaleApplyDetail:{}, orderItemMap:{}", afterSaleApplyDetail,
                    orderItemMap, e);
        }
        return null;
    }

    /**
     * 构建售后普通商品的换货商品列表
     *
     * @param afterSaleApplyDetail
     * @param productInfoVoMap
     * @return
     */
    public static AfsExchangeProductVo buildAfsExchangeProduct(AfterSaleRecordDetailVo afterSaleApplyDetail,
            Map<Long, ProductInfoVo> productInfoVoMap) {
        try {
            // 获取退单明细对应换货信息
            String afsItemExchangeModelList = afterSaleApplyDetail.getAfsItemExchangeModelList();
            if (StringUtils.isBlank(afsItemExchangeModelList)) {
                return null;
            }
            // 退单明细的换货信息，包含父商品的换货和对应的子商品的换货信息
            List<AfsDetailExchangeModel> afsItemExchangeModels = JSON.parseArray(afsItemExchangeModelList,
                    AfsDetailExchangeModel.class);
            if (CollectionUtils.isEmpty(afsItemExchangeModels)) {
                return null;
            }
            // 获取退单明细的换货信息，通过serviceId为空表示父商品的换货信息信息
            List<AfsDetailExchangeModel> afsExchangeModels = afsItemExchangeModels.stream()
                    .filter(afsDetailExchangeModel -> Objects.isNull(afsDetailExchangeModel.getServiceId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(afsExchangeModels)) {
                return null;
            }
            return buildAfsExchangeProductVo(productInfoVoMap, afterSaleApplyDetail.getOrderItemId(),
                    afsExchangeModels);
        } catch (Exception e) {
            log.error("buildAfsExchangeProduct: afterSaleApplyDetail:{}, productInfoVoMap:{}", afterSaleApplyDetail,
                    productInfoVoMap, e);
        }
        return null;
    }

    /**
     * 构建售后商品对应单换货信息
     * 
     * @param productInfoVoMap 正单商品orderItemId对应的map
     * @param orderItemId 退单对应的orderItemId
     * @param afsExchangeModels 退单对应的换货信息列表
     * @return
     */
    private static AfsExchangeProductVo buildAfsExchangeProductVo(Map<Long, ProductInfoVo> productInfoVoMap,
            Long orderItemId, List<AfsDetailExchangeModel> afsExchangeModels) {
        // 获取退单明细对应原单信息
        ProductInfoVo originalProduct = productInfoVoMap.get(orderItemId);
        // 获取原单的换货数量信息
        int exchangeSourceOrderItemCnt = OrderItemExchangeModel.build(originalProduct.getExtData())
                .getExchangeSourceOrderItemCnt();
        // 原单换货信息，原单换出商品只会有一个明细
        AfsDetailExchangeModel afsDetailExchangeModel = afsExchangeModels.get(0);
        AfsExchangeProductVo afsExchangeProductVo = AfsExchangeProductVo.builder().name(originalProduct.getSkuName())
                .skuId(originalProduct.getSkuId()).picUrl(originalProduct.getPicUrl())
                .picUrl(originalProduct.getPicUrl())
                .upcCode(StringUtils.isNotEmpty(originalProduct.getUpcCode())
                        ? Splitter.on(",").splitToList(originalProduct.getUpcCode()) : new ArrayList<>())
                .erpCode(originalProduct.getErpItemCode()).specification(originalProduct.getSpecification())
                // 退货数量*商品重量
                .weight(BigDecimal.valueOf(Optional.ofNullable(originalProduct.getWeight()).orElse(0))
                        .multiply(ConverterUtils.valueOf(afsDetailExchangeModel.getAfsExchangeFromCount()))
                        .setScale(0, RoundingMode.HALF_UP).toString())
                // 原商品：订单上的销售数量-缺货数量
                .quantity(CombinationProductUtil.formatThreeDecimal(BigDecimal.valueOf(originalProduct.getCount())
                        .subtract(BigDecimal.valueOf(exchangeSourceOrderItemCnt)).setScale(3, RoundingMode.HALF_UP)))
                .refundCount(
                        CombinationProductUtil.formatThreeDecimal(afsDetailExchangeModel.getAfsExchangeFromCount()))
                .salePrice(ConverterUtils.formatMoney(ConverterUtils.divideHundred(originalProduct.getCurrentPrice())))
                .originPrice(ConverterUtils.formatMoney(ConverterUtils.divideHundred(originalProduct.getOriginalPrice())))
                // 商品售价*退货数量
                .itemAmt(ConverterUtils.formatMoney(ConverterUtils.divideHundred(BigDecimal
                        .valueOf(Optional.ofNullable(originalProduct.getCurrentPrice()).orElse(0))
                        .multiply(ConverterUtils.valueOf(afsDetailExchangeModel.getAfsExchangeFromCount()))
                        .intValue())))
                .build();
        // 构建换货商品信息
        List<AfsExchangeProductVo.ExchangeToItem> exchangeToItems = Lists.newArrayList();
        for (AfsDetailExchangeModel afsExchangeModel : afsExchangeModels) {
            List<AfsDetailExchangeItemModel> exchangeItemList = afsExchangeModel.getAfsItemExchangeModelList();
            if (CollectionUtils.isEmpty(exchangeItemList)) {
                continue;
            }
            for (AfsDetailExchangeItemModel exchangeItem : exchangeItemList) {
                // 获取订单明细中对应的换货信息
                ProductInfoVo originalExchangeProduct = productInfoVoMap.get(exchangeItem.getOrderItemId());
                if (Objects.isNull(originalExchangeProduct)) {
                    log.error("buildAfsExchangeProduct: 退单查询正单的换货商品信息为空, orderItemId:{}, originalExchangeProduct:{}",
                            exchangeItem.getOrderItemId(), originalExchangeProduct);
                    continue;
                }
                AfsExchangeProductVo.ExchangeToItem exchangeToItem = AfsExchangeProductVo.ExchangeToItem.builder()
                        .name(originalExchangeProduct.getSkuName()).skuId(originalExchangeProduct.getSkuId())
                        .unitPrice(new BigDecimal(originalExchangeProduct.getUnitPrice()))
                        .picUrl(originalExchangeProduct.getPicUrl())
                        .upcCode(StringUtils.isNotEmpty(originalExchangeProduct.getUpcCode())
                                ? Splitter.on(",").splitToList(originalExchangeProduct.getUpcCode())
                                : new ArrayList<>())
                        .erpCode(originalExchangeProduct.getErpItemCode())
                        .specification(originalExchangeProduct.getSpecification())
                        // 退货数量*商品重量
                        .weight(BigDecimal.valueOf(Optional.ofNullable(originalExchangeProduct.getWeight()).orElse(0))
                                .multiply(ConverterUtils.valueOf(exchangeItem.getCount()))
                                .setScale(0, RoundingMode.HALF_UP).toString())
                        .quantity(String.valueOf(originalExchangeProduct.getCount()))
                        .refundCount(CombinationProductUtil.formatThreeDecimal(exchangeItem.getCount()))
                        // 商品售价*退货数量
                        .itemAmt(ConverterUtils.formatMoney(ConverterUtils.divideHundred(BigDecimal
                                .valueOf(Optional.ofNullable(originalExchangeProduct.getCurrentPrice()).orElse(0))
                                .multiply(ConverterUtils.valueOf(exchangeItem.getCount()))
                                .intValue())))
                        .originPrice(ConverterUtils.formatMoney(originalExchangeProduct.getOriginalPrice()))
                        .salePrice(
                                ConverterUtils.formatMoney(ConverterUtils.divideHundred(originalExchangeProduct.getCurrentPrice())))
                        .orderItemId(originalExchangeProduct.getOrderItemId()).build();
                exchangeToItems.add(exchangeToItem);
            }
        }
        afsExchangeProductVo.setExchangeToItemList(exchangeToItems);
        return afsExchangeProductVo;
    }
}
