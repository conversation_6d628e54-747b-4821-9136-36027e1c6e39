package com.sankuai.shangou.qnh.orderapi.remote;

import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.tenant.thrift.common.enums.BizSourceEnum;
import com.sankuai.meituan.reco.pickselect.consts.PickCompleteSource;
import com.sankuai.meituan.reco.pickselect.thrift.*;
import com.sankuai.meituan.reco.pickselect.thrift.fulfill.FulfillThriftService;
import com.sankuai.meituan.reco.pickselect.thrift.fulfill.request.ShopPickerRequest;
import com.sankuai.meituan.reco.pickselect.thrift.fulfill.response.AccountInfo;
import com.sankuai.meituan.reco.pickselect.thrift.fulfill.response.ShopPickerResponse;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.RiderPickingThriftService;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.dto.RiderPickWorkOrderDTO;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.request.QueryRiderPickWorkOrderRequest;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.response.RiderPickWorkOrderResponse;
import com.sankuai.meituan.reco.pickselect.thrift.print.OpenPrintRequest;
import com.sankuai.meituan.reco.pickselect.thrift.print.OpenPrintResponse;
import com.sankuai.meituan.reco.pickselect.thrift.print.OpenPrintThriftService;
import com.sankuai.meituan.shangou.empower.auth.sdk.bean.AppIdEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ResponseCodeEnum;
import com.sankuai.shangou.qnh.orderapi.context.app.ApiMethodParamThreadLocal;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.CommonDataBO;
import com.sankuai.shangou.qnh.orderapi.domain.dto.app.User;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.QueryShopPickerRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.TransferOrderRequest;
import com.sankuai.shangou.qnh.orderapi.enums.pc.TransferPickerTypeEnum;
import com.sankuai.shangou.qnh.orderapi.exception.pc.CommonRuntimeException;
import com.sankuai.shangou.qnh.orderapi.utils.app.RequestContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/***
 * author : <EMAIL> 
 * data : 2021/3/15 
 * time : 下午7:35
 **/
@Service
@Slf4j
public class PickSelectRemoteService {

    @Resource
    private OpenPrintThriftService.Iface openPrintThriftService;

    @Resource
    private OpenPickThriftService.Iface openPickThriftService;

    @Resource
    private RiderPickingThriftService riderPickingThriftService;

    @Resource
    private TenantRemoteService tenantRemoteService;

    @Resource
    private FulfillThriftService fulfillThriftService;


    public OpenPrintResponse printReceipt(int orderBizType, String viewOrderId, String poiId){
        OpenPrintRequest request = new OpenPrintRequest();
        request.setSource(orderBizType);
        request.setUnifyOrderId(viewOrderId);
        request.setPrintBizSource(BizSourceEnum.OCMS.getSource());
        Long operatorId = ContextHolder.currentUserStaffId();
        if (operatorId != null){
            request.setOperateId(operatorId);
        }
        request.setOperateName(ContextHolder.currentUserName());
        request.setAppId(String.valueOf(ContextHolder.currentUserLoginAppId()));
        request.setOperatorAccountId(ContextHolder.currentUid());
        request.setTenantId(ContextHolder.currentUserTenantId());

        if (Objects.equals(ContextHolder.currentUserLoginAppId(), AppIdEnum.APP_30.getAuthAppId()) && StringUtils.isNotEmpty(poiId)) {
            request.setOfflineStoreId(Long.parseLong(poiId));
        }
        log.info("调用拣货打印小票，req:{}", request);
        try {
            OpenPrintResponse printResponse = openPrintThriftService.doPrint(request);
            log.info("打印小票结果:{}, order:{}", printResponse, viewOrderId);
            return printResponse;
        }catch (Exception e){
            log.error("打印小票失败", e);
            OpenPrintResponse response = new OpenPrintResponse();
            response.setCode(ResponseCodeEnum.THRIFT_SERVICE_ERROR.getValue());
            response.setMsg(e.getMessage());
            return response;
        }
    }

    public OpenPrintResponse printReceiptByApp(int orderBizType, String viewOrderId){
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        final String appId = ApiMethodParamThreadLocal.getIdentityInfo().getAppId();
        OpenPrintRequest request = new OpenPrintRequest();
        request.setSource(orderBizType);
        request.setUnifyOrderId(viewOrderId);
        request.setPrintBizSource(BizSourceEnum.OCMS.getSource());
        request.setAppId(appId);
        Long operatorId = user.getAccountId();
        long employeeId = user.getEmployeeId();
        String operator = tenantRemoteService.getEmployeeName(user.getTenantId(), employeeId);
        if (operatorId != null){
            request.setOperateId(operatorId);
        }
        request.setOperateName(operator);
        request.setOperatorAccountId(user.getAccountId());
        request.setTenantId(user.getTenantId());
        request.setOfflineStoreId(RequestContextUtils.getHeaderStoreId());
        log.info("调用拣货打印小票，req:{}", request);
        try {
            OpenPrintResponse printResponse = openPrintThriftService.doPrint(request);
            log.info("打印小票结果:{}, order:{}", printResponse, viewOrderId);
            return printResponse;
        }catch (Exception e){
            log.error("打印小票失败", e);
            OpenPrintResponse response = new OpenPrintResponse();
            response.setCode(ResponseCodeEnum.THRIFT_SERVICE_ERROR.getValue());
            response.setMsg(e.getMessage());
            return response;
        }
    }

    public OpenPickResponse completePickUp(String viewOrderId, Integer orderBizType) {
        OpenPickRequest request = new OpenPickRequest();
        request.setSource(orderBizType);
        request.setUnifyOrderId(viewOrderId);
        request.setOpSource(PickCompleteSource.OCMS);
        request.setUserId(ContextHolder.currentUserStaffId() != null ? ContextHolder.currentUserStaffId() : 0L);
        request.setAppId(String.valueOf(ContextHolder.currentUserLoginAppId()));
        request.setOperatorAccountId(ContextHolder.currentUid() != null ? ContextHolder.currentUid() : 0L);
        CommonDataBO commonResultBO =  new CommonDataBO();
        log.info("收到出餐完成通知，req：{}", request);
        try {
            OpenPickResponse resp = openPickThriftService.pickComplete(request);
            log.info("order:{},出餐完成，通知拣货系统，结果:{}", viewOrderId, resp);
            return resp;
        } catch (Exception e) {
            log.error("拣接口发生异常,req:{}", request, e);
            OpenPickResponse response = new OpenPickResponse();
            response.setCode(ResponseCodeEnum.THRIFT_SERVICE_ERROR.getValue());
            response.setMsg(e.getMessage());
            return response;
        }
    }

    /**
     * 查询拣货工单详情
     */
    @MethodLog(logRequest = true,logResponse = true)
    public RiderPickWorkOrderDTO getPickWorkOrder(Long tenantId,String unifyOrderId, Integer channelId,Long storeId) {
        QueryRiderPickWorkOrderRequest tRequest = new QueryRiderPickWorkOrderRequest();

        tRequest.setTenantId(tenantId);
        tRequest.setEmpowerStoreId(storeId);
        tRequest.setAccountId(ContextHolder.currentUserStaffId());
        tRequest.setOrderSource(ChannelOrderConvertUtils.sourceMid2Biz(channelId));
        tRequest.setUnifyOrderId(unifyOrderId);

        // 1. thrift 调用
        RiderPickWorkOrderResponse response = null;
        try {
            response = riderPickingThriftService.queryPickWorkOrderDetail(tRequest);
            log.info("Call RiderPickingThriftService#queryPickWorkOrderDetail. request:{} response:{}", tRequest, response);
        } catch (Exception e) {
            log.error("Call RiderPickingThriftService#queryPickWorkOrderDetail error. request:{}", tRequest, e);
            throw new CommonRuntimeException(ResultCode.INTERNAL_SERVER_ERROR.defaultMessage);
        }
        // 2. 处理返回结果
        if (response == null || response.getStatus() == null ||
                response.getStatus().getCode() != com.sankuai.meituan.reco.pickselect.consts.ResultCodeEnum.SUCCESS.getCode()) {
            log.warn("获取拣货工单信息失败, request:{}, response:{}", tRequest, response);
            throw new CommonRuntimeException(response.getStatus().getMessage());
        }
        return response.getPickWorkOrderDto();
    }


    /**
     * 查询拣货员列表
     * @param request
     * @return
     */
    public ShopPickerResponse queryShopPicker(QueryShopPickerRequest request) {
        try {
            ShopPickerRequest shopPickerRequest = new ShopPickerRequest();
            shopPickerRequest.setViewOrderId(request.getChannelOrderId());
            shopPickerRequest.setOrderBizType(DynamicOrderBizType.channelId2OrderBizTypeValue(request.getChannelId()));
            shopPickerRequest.setType(request.getType());
            ShopPickerResponse shopPickerResponse = fulfillThriftService.queryShopPicker(shopPickerRequest);
            log.info("fulfillThriftService.queryShopPicker request:{} response:{}", shopPickerRequest, shopPickerResponse);
            if (shopPickerResponse != null
                    && shopPickerResponse.getStatus() != null){
                if(Objects.equals(shopPickerResponse.getStatus().getCode(), ResultCodeEnum.SUCCESS.getValue())){
                    return shopPickerResponse;
                } else {
                    throw new CommonRuntimeException(shopPickerResponse.getStatus().getMessage());
                }
            } else {
                throw new CommonRuntimeException(ResultCode.INTERNAL_SERVER_ERROR.defaultMessage);
            }
        } catch (CommonRuntimeException e){
            throw e;
        } catch (Exception e){
            log.error("Call fulfillThriftService.queryShopPicker error. request:{}", request, e);
            throw new CommonRuntimeException(e.getMessage());
        }
    }

    /**
     * 分派或者下发拣货员
     * @param request
     * @return
     */
    public boolean transferPicker(TransferOrderRequest request, Integer appId, Long accountSourceId, Long tenantId) {
        try {
            if(appId == null
                    || accountSourceId == null
                    || tenantId == null) {
                throw new CommonRuntimeException(ResultCode.LOGIN_TENANT_INFO_ERROR.defaultMessage);
            }
            // 参数构造
            TransferRequest transferRequest = new TransferRequest();
            transferRequest.setViewOrderId(request.getChannelOrderId());
            transferRequest.setOrderBizType(Optional.ofNullable(DynamicOrderBizType.channelId2OrderBizTypeValue(request.getChannelId())).orElse(0));
            transferRequest.setType(TransferPickerTypeEnum.convertType(request.getType()).getCode());
            transferRequest.setAccountId(request.getAccountId());
            transferRequest.setAccountName(request.getAccountName());
            transferRequest.setAppId(appId);
            transferRequest.setAccountSourceId(accountSourceId);
            transferRequest.setTenantId(tenantId);

            TransferResponse transferResponse = openPickThriftService.transferFulfill(transferRequest);
            log.info("openPickThriftService.transferFulfill request:{} response:{}", transferRequest, transferResponse);
            if(transferResponse != null) {
                if(Objects.equals(transferResponse.getCode(),ResultCodeEnum.SUCCESS.getValue())) {
                    return true;
                } else {
                    throw new CommonRuntimeException(transferResponse.getMessage());
                }
            } else {
                return false;
            }
        } catch (CommonRuntimeException e) {
            throw e;
        } catch (Exception e){
            log.error("Call fulfillThriftService.transferPicker error. request:{}", request, e);
            throw new CommonRuntimeException(e.getMessage());
        }
    }



}
