package com.sankuai.shangou.qnh.orderapi.domain.response.app;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.o2o.dto.response.ImportExchangePair;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
@TypeDoc(
        description = "售后换货信息校验"
)
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("售后换货信息校验")
public class ExchangeRefundImportCheckResponse {
    /**
     * 售后换货信息校验
     */
    private List<ImportExchangePair> importExchangePairList;
}
