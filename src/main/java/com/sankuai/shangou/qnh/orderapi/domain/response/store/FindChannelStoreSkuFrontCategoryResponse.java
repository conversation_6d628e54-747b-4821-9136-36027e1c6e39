package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.FrontCategoryVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/6/12
 * desc:
 */
@TypeDoc(
        description = "查询渠道门店商品分类响应"
)
@Data
@ApiModel("查询渠道门店商品分类响应")
public class FindChannelStoreSkuFrontCategoryResponse {

    @FieldDoc(
            description = "分类列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "分类列表", required = true)
    private List<FrontCategoryVO> categoryList;


}
