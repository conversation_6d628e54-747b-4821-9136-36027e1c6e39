package com.sankuai.shangou.qnh.orderapi.domain.vo.app;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.codehaus.jackson.annotate.JsonProperty;

/**
 * <AUTHOR>
 * @date 2024/8/15
 * desc: 商品标签
 */
@TypeDoc(
        description = "商品标签"
)
@ApiModel("商品标签")
@Data
public class ItemLabelVO {

    @FieldDoc(
            description = "标签类型", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "标签类型")
    @JsonProperty("type")
    private Long type;

    @FieldDoc(
            description = "标签名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "标签名称")
    @JsonProperty("name")
    private String name;

    @FieldDoc(
            description = "标签描述", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "标签描述")
    @JsonProperty("desc")
    private String desc;

}
