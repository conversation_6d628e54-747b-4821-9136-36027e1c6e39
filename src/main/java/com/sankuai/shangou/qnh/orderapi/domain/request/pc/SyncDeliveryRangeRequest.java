package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.ChannelForSyncDeliveryRangeVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2020/4/16
 * desc: 同步配送范围请求
 */
@Getter
@Setter
public class SyncDeliveryRangeRequest {

    @FieldDoc(
            description = "门店ID"
    )
    @ApiModelProperty(value = "门店ID")
    private String poiId;

    @FieldDoc(
            description = "待同步渠道列表"
    )
    @ApiModelProperty(value = "待同步渠道列表")
    private List<ChannelForSyncDeliveryRangeVO> channelList;
}
