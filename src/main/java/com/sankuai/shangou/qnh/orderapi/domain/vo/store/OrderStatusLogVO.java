package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/7/18
 * desc: 订单状态日志
 */
@TypeDoc(
        description = "订单状态日志"
)
@ApiModel("订单状态日志")
@Data
public class OrderStatusLogVO {

    @FieldDoc(
            description = "日志时间戳", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "日志时间戳", required = true)
    private Long time;

    @FieldDoc(
            description = "状态描述", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "状态描述", required = true)
    private String statusDesc;
}
