package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/2 11:38
 * @Description:
 */
@Setter
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(
        description = "订单分页查询请求"
)
public class ChannelOrderQueryRequest extends PageRequest implements BaseRequest {

    /**
     * 收货人姓名
     */
    private String receiverName;

    /**
     * 收货人地址
     */
    private String receiverAddress;

    /**
     * 收货人电话
     */
    private String receiverPhone;

    /**
     * 门店名称
     */
    private String poiName;

    /**
     * 门店编码
     */
    private String poiId;

    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 订单创建开始时间
     */
    private String createStartTime;

    /**
     * 订单创建结束时间
     */
    private String createEndTime;

    /**
     * 预计送达开始时间
     */
    private String arrivalStartTime;

    /**
     * 预计送达结束时间
     */
    private String arrivalEndTime;

    /**
     * 状态
     */
    private String status;


    /**
     * 渠道列表
     */
    private List<String> channelIds;


    /**
     * 退款标识
     */
    private List<String> refundTypes;


    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 是否是会员
     */
    private String hasMemberCard;

    /**
     * 会员卡号
     */
    private String memberCard;

    /**
     * 流水号
     */
    @FieldDoc(
            description = "流水号"
    )
    private String orderSerialNumber;

    /**
     * 摊位ID
     */
    @FieldDoc(
            description = "摊位ID集合"
    )
    private List<Long> boothIds;

    /**
     * 问题订单(1进货价为空)
     */
    @FieldDoc(
            description = "问题订单(1进货价为空)"
    )
    private Integer orderProblemType;

    /**
     * 门店ID集合
     */
    private List<Long> poiIdList;

    /**
     * 仓库id
     */
    private List<Long> warehouseIdList;

    /**
     * 核销状态列表
     */
    public List<Integer> writeOffStatusList;

    /**
     * 订单标识列表
     */
    public List<Integer> orderMarkList;

    /**
     * 自配送类型列表
     */
    private List<Integer> deliveryChannelIdList;

    /**
     * 平台配送类型列表
     */
    private List<Integer> originalDistributeTypeList;

    /**
     * 融合订单状态
     */
    private List<Integer> fuseOrderStatusList;

    /**
     * 配送方式
     */
    private Integer distributeMethod;




    /**
     * 订单用户类型
     */
    private Integer orderUserType;


    /**
     * 配送方式筛选 2 自营配送  -4 青云配送
     */
    private Integer deliveryChannelType;

    /**
     * 完成配送，筛选起始时间
     */
    private String finishDeliveryStartTime;

    /**
     * 完成配送，筛选结束时间
     */
    private String finishDeliveryEndTime;


    /**
     * 基础skuId
     */
    private String skuId;

    /**
     * 骑手账户id
     */
    private String riderAccount;

    /**
     * 客户端来源
     */
    private String clientType;

    /**
     * 订单标识列表
     */
    public List<Integer> orderMarks;




    @Override
    public void selfCheck() {
        AssertUtil.isTrue(page > 0, "page不能小于等于0");
        AssertUtil.isTrue(pageSize > 0, "pageSize不能小于等于0");
    }
}
