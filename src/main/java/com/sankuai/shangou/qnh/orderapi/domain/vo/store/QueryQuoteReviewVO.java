package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.ChannelRetailPriceInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.QuotePriceTrendVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "提价审核信息集"
)
@Data
@ApiModel("提价审核信息集")
public class QueryQuoteReviewVO {

    @FieldDoc(
            description = "提价记录编码ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "提价记录编码ID", required = true)
    private Long quoteRecordId;

    @FieldDoc(
            description = "商品图片地址", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品图片地址", required = true)
    private String picUrl;

    @FieldDoc(
            description = "店铺名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "店铺名称", required = true)
    private String storeName;

    @FieldDoc(
            description = "商品ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品ID", required = true)
    private String skuCode;

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称", required = true)
    private String skuName;

    @FieldDoc(
            description = "商品规格", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品规格", required = true)
    private String spec;

    @FieldDoc(
            description = "商品规格", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品规格", required = true)
    private Integer weight;

    @FieldDoc(
            description = "本次提价", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "本次提价", required = true)
    private String quotePrice;

    @FieldDoc(
            description = "提报价市斤价", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "提报价市斤价", required = true)
    private String quotePricePer500g;

    @FieldDoc(
            description = "当前市斤价", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "当前市斤价", required = true)
    private String currentPricePer500g;

    @FieldDoc(
            description = "当前报价和上次报价涨跌百分比", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "当前报价和上次报价涨跌百分比", required = true)
    private String currentPricePer500gRatio;

    @FieldDoc(
            description = "当前报价环比结果标识", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "当前报价环比结果标识", required = true)
    private int currentPricePer500gRatioFlag;

    @FieldDoc(
            description = "市调价市斤价", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "市调价市斤价", required = true)
    private String avgReferencePricePer500g;

    @FieldDoc(
            description = "市调价涨跌幅", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "市调价涨跌幅", required = true)
    private String avgReferencePricePer500gRatio;

    @FieldDoc(
            description = "市调价涨跌幅环比结果标识", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "市调价涨跌幅环比结果标识", required = true)
    private int avgReferencePricePer500gRatioFlag;

    @FieldDoc(
            description = "基准价市斤价", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "基准价市斤价", required = true)
    private String basePricePer500g;

    @FieldDoc(
            description = "基准价市斤价涨跌幅", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "基准价市斤价涨跌幅", required = true)
    private String basePricePer500gRatio;

    @FieldDoc(
            description = "基准价市斤价涨跌幅标识", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "基准价市斤价涨跌幅标识", required = true)
    private int basePricePer500gRatioFlag;

    @FieldDoc(
            description = "当前提价记录审批状态", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "当前提价记录审批状态", required = true)
    private int reviewStatus;

    @FieldDoc(
            description = "审核描述", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "审核描述", required = true)
    private String reviewDescription;

    @FieldDoc(
            description = "提价人ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "提价人ID", required = true)
    private Long quoterId;

    @FieldDoc(
            description = "提价人名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "提价人名称", required = true)
    private String quoterName;

    @FieldDoc(
            description = "提价时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "提价时间", required = true)
    private String quoteTime;


    @FieldDoc(
            description = "审批人ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "审批人ID", required = true)
    private Long reviewerId;

    @FieldDoc(
            description = "审批人名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "审批人名称", required = true)
    private String reviewerName;

    @FieldDoc(
            description = "审批时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "审批时间", required = true)
    private String reviewTime;

    @FieldDoc(
            description = "当前记录是否有权限进行审核"
    )
    @ApiModelProperty(name = "当前记录是否有权限进行审核")
    private boolean checkFlag;

    @FieldDoc(
            description = "价格趋势信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "价格趋势信息", required = true)
    private QuotePriceTrendVO priceTrendInfo;

    @FieldDoc(
            description = "渠道零售价信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道零售价信息")
    private List<ChannelRetailPriceInfoVO> channelRetailPriceInfos;

    @FieldDoc(
            description = "活动不能改价信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "活动不能改价信息")
    private Boolean atPromotion;
}
