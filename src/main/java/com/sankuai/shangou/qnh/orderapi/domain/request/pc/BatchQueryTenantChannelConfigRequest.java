package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.tenant.thrift.dto.config.request.ChannelConfigBatchQueryRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.config.request.MultiTenantBatchQueryChannelConfigRequest;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ListConvertUtil;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/30
 */
@Data
@ToString
@TypeDoc(
        description = "批量查询租户渠道配置"
)
public class BatchQueryTenantChannelConfigRequest {

    @FieldDoc(description = "渠道 ID 列表")
    private List<Integer> channelIds;

    @FieldDoc(description = "配置主体 ID 列表")
    private List<Long> subjectIds;

    @FieldDoc(description = "配置项 ID 列表")
    private List<Integer> configIds;


    public void validate() {
        if (CollectionUtils.isEmpty(channelIds)) {
            throw new IllegalArgumentException("渠道 ID 列表不能为空");
        }
        if (CollectionUtils.isEmpty(subjectIds)) {
            throw new IllegalArgumentException("配置主体 ID 列表不能为空");
        }
        if (CollectionUtils.isEmpty(configIds)) {
            throw new IllegalArgumentException("配置项 ID 列表不能为空");
        }
    }

    public boolean emptyRequest() {
        return ListConvertUtil.convertToNullSafeList(channelIds).isEmpty()
                || ListConvertUtil.convertToNullSafeList(subjectIds).isEmpty()
                || ListConvertUtil.convertToNullSafeList(configIds).isEmpty();
    }

    public MultiTenantBatchQueryChannelConfigRequest toMultiRequest(Long tenantId) {
        MultiTenantBatchQueryChannelConfigRequest request = new MultiTenantBatchQueryChannelConfigRequest();
        ChannelConfigBatchQueryRequest configBatchQueryRequest = new ChannelConfigBatchQueryRequest();
        configBatchQueryRequest.setTenantId(tenantId);
        configBatchQueryRequest.setChannelIdList(this.channelIds);
        configBatchQueryRequest.setSubjectIdList(this.subjectIds);
        configBatchQueryRequest.setConfigIdList(this.configIds);
        request.setQueryList(Collections.singletonList(configBatchQueryRequest));
        return request;
    }

}
