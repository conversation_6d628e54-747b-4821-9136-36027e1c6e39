package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.ErrorRecordVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/6/17
 * desc:
 */
@TypeDoc(
        description = "编辑租户商品响应"
)
@Data
@ApiModel("编辑租户商品响应")
public class SaveTenantSkuResponse {

    @FieldDoc(
            description = "错误记录集合", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "错误记录集合", required = true)
    private List<ErrorRecordVO> errorRecordVOList;
}
