package com.sankuai.shangou.qnh.orderapi.constant.pc;

import com.google.common.collect.Sets;

import java.util.Set;

/**
 * 配送相关常量.
 *
 * <AUTHOR>
 * @since 2021/3/5 17:33
 */
public abstract class DeliveryConstant {

    /**
     * 实时单的发单时间类型，1-商家接单后，2-拣货完成后
     */
    public static final Set<Integer> REALTIME_ORDER_LAUNCH_POINT_TYPE = Sets.newHashSet(1, 2);

    /**
     * 预约单的发单时间类型，1-预计送达前，2-拣货完成后
     */
    public static final Set<Integer> BOOKING_ORDER_LAUNCH_POINT_TYPE = Sets.newHashSet(1, 2);

    /**
     * 预约单-预计送达前发单
     */
    public static final Integer BOOKING_ORDER_LAUNCH_BEFORE_DELIVERY = 1;

    /**
     * 预约单-预计送达前分钟数的最小值
     */
    public static final int BOOKING_ORDER_BEFORE_DELIVERY_MINUTES_MIN = 15;

    /**
     * 预约单-预计送达前分钟数的最大值
     */
    public static final int BOOKING_ORDER_BEFORE_DELIVERY_MINUTES_MAX = 1440;
}
