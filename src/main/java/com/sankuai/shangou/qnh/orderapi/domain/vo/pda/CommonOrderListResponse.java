package com.sankuai.shangou.qnh.orderapi.domain.vo.pda;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.OrderVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.PageInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/11/19
 * desc: 分页查询订单列表响应
 */
@TypeDoc(
        description = "分页查询订单列表响应"
)
@Data
@ApiModel("分页查询订单列表响应")
@AllArgsConstructor
@NoArgsConstructor
public class CommonOrderListResponse {

    @FieldDoc(
            description = "分页信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "分页信息", required = true)
    private PageInfoVO pageInfo;

    @FieldDoc(
            description = "订单列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单列表", required = true)
    private List<CommonOrderVO> orderList;
}
