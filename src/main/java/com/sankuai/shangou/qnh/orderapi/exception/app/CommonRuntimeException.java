package com.sankuai.shangou.qnh.orderapi.exception.app;


import com.sankuai.shangou.qnh.orderapi.enums.app.ResultCodeEnum;

/**
 * <AUTHOR>
 * email <EMAIL>
 * date 2018/5/10
 */
public class CommonRuntimeException extends RuntimeException {

    private ResultCodeEnum resultCodeEnum;
    private transient Object[] text;

    private boolean setMessage;

    public CommonRuntimeException() {
    }

    public CommonRuntimeException(String message) {
        super(message);
        setMessage = true;
    }

    public CommonRuntimeException(String message, Throwable cause) {
        super(message, cause);
        setMessage = true;
    }

    public CommonRuntimeException(Throwable cause) {
        super(cause);
    }

    public CommonRuntimeException(String message, ResultCodeEnum code) {
        super(message);
        setMessage = true;
        this.resultCodeEnum = code;
    }

    public CommonRuntimeException(String message, Throwable cause, ResultCodeEnum code) {
        super(message, cause);
        setMessage = true;
        this.resultCodeEnum = code;
    }

    public CommonRuntimeException(Throwable cause, ResultCodeEnum codeEnum) {
        super(cause);
        this.resultCodeEnum = codeEnum;
    }

    public CommonRuntimeException(ResultCodeEnum codeEnum) {
        this.resultCodeEnum = codeEnum;
    }

    public CommonRuntimeException(ResultCodeEnum codeEnum, Object... objects) {
        this.resultCodeEnum = codeEnum;
        this.text = objects;
    }

    public ResultCodeEnum getResultCode() {
        return resultCodeEnum;
    }

    public Object[] getText() {
        return text;
    }

    public boolean isSetMessage() {
        return setMessage;
    }
}
