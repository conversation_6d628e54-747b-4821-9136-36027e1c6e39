package com.sankuai.shangou.qnh.orderapi.service.pc.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.meituan.shangou.saas.dto.StatusCodeEnum;
import com.meituan.shangou.saas.dto.request.ocms.QueryWeightRefundGoodsRequest;
import com.meituan.shangou.saas.dto.request.ocms.TenantWeightRefundRequest;
import com.meituan.shangou.saas.dto.request.ocms.WeightRefundCalculateRequest;
import com.meituan.shangou.saas.dto.response.model.WeightRefundGoodsModel;
import com.meituan.shangou.saas.dto.response.ocms.QueryOnlineWeightRefundGoodsResponse;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderItemMoneyRefundCheckModel;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderWeightRefundItemModel;
import com.meituan.shangou.saas.o2o.dto.model.OCMSRefundReasonAndCodeModel;
import com.meituan.shangou.saas.o2o.dto.request.OCMSCheckRefundRequest;
import com.meituan.shangou.saas.o2o.dto.response.OCMSRefundCheckResponse;
import com.meituan.shangou.saas.o2o.dto.response.WeightRefundCalculateResponse;
import com.meituan.shangou.saas.order.management.client.dto.request.OrderDetailSearchRequest;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.OrderDetailResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.model.*;
import com.meituan.shangou.saas.order.management.client.service.OrderSearchService;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.meituan.shangou.saas.order.platform.common.model.OrderItemExchangeModel;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.enums.AfterSalePatternEnum;
import com.meituan.shangou.saas.order.platform.enums.DeliveryStatusEnum;
import com.meituan.shangou.saas.service.ocms.OCMSOrderOperateThriftService;
import com.meituan.shangou.saas.service.ocms.OCMSOrderThriftService;
import com.meituan.shangou.saas.tenant.thrift.ConfigThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.enums.ConfigItemEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.config.request.ConfigQueryRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.config.response.TenantConfigResponse;
import com.sankuai.meituan.shangou.empower.finance.dto.model.OrderFinanceDetailModel;
import com.sankuai.meituan.shangou.empower.finance.dto.model.OrderFinanceModel;
import com.sankuai.meituan.shangou.empower.finance.dto.request.OrderFinanceQueryRequest;
import com.sankuai.meituan.shangou.empower.finance.dto.responce.OrderFinanceQueryResponse;
import com.sankuai.meituan.shangou.empower.finance.service.OrderFinanceThriftService;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.command.MerchantSpuIdListCommand;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.domain.MerchantSpuInfo;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.result.QueryMerchantSpuListResult;
import com.sankuai.meituan.shangou.platform.empower.product.thrift.service.EmpowerMerchantSpuThriftService;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import com.sankuai.shangou.qnh.orderapi.converter.pc.ChannelOrderConverterV2;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.CommonResultBO;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.*;
import com.sankuai.shangou.qnh.orderapi.domain.response.CommonResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.WeightRefundCheckResponse;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.RefundCalculateResult;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.WeightRefundCalculateResult;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.*;
import com.sankuai.shangou.qnh.orderapi.enums.app.ErrorCodeEnum;
import com.sankuai.shangou.qnh.orderapi.enums.pc.ChannelTypeEnum;
import com.sankuai.shangou.qnh.orderapi.exception.pc.CommonRuntimeException;
import com.sankuai.shangou.qnh.orderapi.exception.pc.ParamInvalidException;
import com.sankuai.shangou.qnh.orderapi.remote.OCMSOrderRemoteService;
import com.sankuai.shangou.qnh.orderapi.remote.OcmsChannelRemoteService;
import com.sankuai.shangou.qnh.orderapi.remote.OrderBizRemoteService;
import com.sankuai.shangou.qnh.orderapi.service.common.ExchangeItemUtil;
import com.sankuai.shangou.qnh.orderapi.service.pc.FuseAfterSaleApplyService;
import com.sankuai.shangou.qnh.orderapi.utils.CombinationProductUtil;
import com.sankuai.shangou.qnh.orderapi.utils.pc.MccConfigUtil;
import com.sankuai.shangou.qnh.orderapi.utils.pc.OrderUtils;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/3/3
 **/
@Slf4j
@Service
public class FuseAfterSaleApplyServiceImpl implements FuseAfterSaleApplyService {
    private static final String SYMBOL_COMMA = ",";
    private static final String NOT_CAN_REFUND_GOOD = "当前无可克重退的商品";

    @Resource
    private OrderSearchService orderSearchService;

    @Autowired
    private ConfigThriftService configThriftService;

    @Autowired
    private EmpowerMerchantSpuThriftService.Iface empowerMerchantSpuThriftService;

    @Resource
    private OCMSOrderThriftService ocmsOrderThriftService;

    @Autowired
    private OrderBizRemoteService orderBizRemoteService;

    @Resource
    private OCMSOrderRemoteService ocmsOrderRemoteService;

    @Resource
    private OCMSOrderOperateThriftService ocmsOrderOperateThriftService;

    @Autowired
    private OrderFinanceThriftService orderFinanceThriftService;
    @Resource
    private OcmsChannelRemoteService ocmsChannelRemoteService;

    @Override
    public CommonResponse<WeightRefundCheckResponse> weightRefundCheck(WeightRefundCheckRequest request) {
        Long tenantId = ContextHolder.currentUserTenantId();
        OrderDetailSearchRequest orderDetailSearchRequest = new OrderDetailSearchRequest();
        orderDetailSearchRequest.setViewOrderId(request.getChannelOrderId());
        orderDetailSearchRequest.setTenantId(tenantId);
        orderDetailSearchRequest.setOrderBizType(ChannelOrderConverterV2.convertChannelId2OrderBizType(request.getChannelId()));
        log.info("OCMSOrderServiceWrapper.queryLatestOrder  orderSearchService.getOrderDetail request:{}", orderDetailSearchRequest);
        OrderDetailResponse response = null;
        WeightRefundCheckResponse weightRefundCheckResponse = null;
        try {
            response = orderSearchService.getOrderDetail(orderDetailSearchRequest);
            log.info("OCMSOrderServiceWrapper.queryLatestOrder  orderSearchService.getOrderDetail response:{}", response);
            if (Objects.isNull(response)) {
                return CommonResponse.fail2(ResultCode.INTERNAL_SERVER_ERROR);
            }
            if (response.getResponseStatus() != StatusCodeEnum.SUCCESS.getCode()) {
                return CommonResponse.fail(response.getResponseStatus(), response.getMsg());
            }
            if (Objects.isNull(response.getOrderDetail()) || Objects.isNull(response.getOrderDetail().getOrderBase())) {
                return CommonResponse.fail(StatusCodeEnum.ORDER_NOT_FOUNT.getCode(),
                        StatusCodeEnum.ORDER_NOT_FOUNT.getMessage());
            }
            if (BooleanUtils.isTrue(response.getOrderDetail().getOrderBase().getIsMtFamousTavern())) {
                // 名酒馆订单进行错误提示
                Pair<Boolean, Pair<Integer, String>> resultPair = ocmsChannelRemoteService.refundCheckMtFamousTavern(
                        request.getChannelOrderId(), tenantId, ErrorCodeEnum.REFUND_CHECK_MT_FAMOUS_TAVERN_PROMPT, ErrorCodeEnum.WEB_MT_FAMOUS_TAVERN_GATHER_PHONE_NOT_FIND);
                return CommonResponse.fail(resultPair.getValue().getKey(), resultPair.getValue().getValue());
            }

            Integer orderSource = response.getOrderDetail().getOrderBase().getOrderSource();

            // 过滤线下赠品商品
            if (CollectionUtils.isNotEmpty(response.getOrderDetail().getOrderItemList())) {
                List<OrderItem> filteredProducts = response.getOrderDetail().getOrderItemList().stream()
                        .filter(product -> !Objects.equals(1, OrderUtils.getExtDataAsInt("giftType", product.getExtData())))
                        .collect(Collectors.toList());
                response.getOrderDetail().setOrderItemList(filteredProducts);
            }

            if(orderSource != null && Objects.equals(ChannelTypeEnum.MEITUAN.getChannelId(), request.getChannelId()) ){
                QueryWeightRefundGoodsRequest weightRefundGoodsRequest = new QueryWeightRefundGoodsRequest();
                weightRefundGoodsRequest.setOrderBizType(ChannelOrderConvertUtils.convertBizType(request.getChannelId()));
                weightRefundGoodsRequest.setTenantId(tenantId);
                weightRefundGoodsRequest.setViewOrderId(request.getChannelOrderId());
                QueryOnlineWeightRefundGoodsResponse queryOnlineWeightRefundGoodsResponse = ocmsOrderThriftService.queryWeightRefundGoods(weightRefundGoodsRequest);
                if(queryOnlineWeightRefundGoodsResponse.getStatus().getCode() != StatusCodeEnum.SUCCESS.getCode()){
                    return CommonResponse.fail(queryOnlineWeightRefundGoodsResponse.getStatus().getCode(),
                            queryOnlineWeightRefundGoodsResponse.getStatus().getMessage());
                }
                WeightRefundCheckResponse result = buildOnlineOrderWeightRefundCheckResponse(response, queryOnlineWeightRefundGoodsResponse, tenantId);
                if (Objects.isNull(result) ||CollectionUtils.isEmpty(result.getWeightRefundCheckVOList())){
                    return CommonResponse.fail(ResultCode.FAIL.getCode(), NOT_CAN_REFUND_GOOD);
                }
                return CommonResponse.success(result);
            }

            weightRefundCheckResponse = buildWeightRefundCheckResponse(response);
            List<OrderItemWeightRefundCheckVO> itemWeightRefundCheckVOS = weightRefundCheckResponse.getWeightRefundCheckVOList().stream()
                    .filter(Objects::nonNull)
                    .filter(v -> Objects.nonNull(v.getCanRefundCount()) && v.getCanRefundCount() > 0)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(itemWeightRefundCheckVOS)){
                return CommonResponse.fail(ResultCode.FAIL.getCode(), NOT_CAN_REFUND_GOOD);
            }
            weightRefundCheckResponse.setWeightRefundCheckVOList(itemWeightRefundCheckVOS);
        } catch (TException e) {
            log.error("OCMSOrderServiceWrapper.weightRefundCheck  orderSearchService.getOrderDetail TException", e);
            throw new CommonRuntimeException(e);
        }
        return CommonResponse.success(weightRefundCheckResponse);
    }

    @Override
    public CommonResponse weightRefund(RefundByWeightRequest request) {
        if (Objects.equals(request.getChannelId(), DynamicChannelType.MEITUAN.getChannelId())) {
            // 美团名酒馆订单进行提示
            Pair<Boolean, Pair<Integer, String>> resultPair = ocmsChannelRemoteService.refundCheckMtFamousTavern(
                    request.getChannelOrderId(), ContextHolder.currentUserTenantId(),
                    ErrorCodeEnum.REFUND_CHECK_MT_FAMOUS_TAVERN_PROMPT, ErrorCodeEnum.WEB_MT_FAMOUS_TAVERN_GATHER_PHONE_NOT_FIND);
            if (resultPair.getKey()) {
                return CommonResponse.fail(resultPair.getValue().getKey(), resultPair.getValue().getValue());
            }
        }
        CommonResponse commonResponse = CommonResponse.success(null);
        TenantWeightRefundRequest tenantWeightRefundRequest = new TenantWeightRefundRequest();
        tenantWeightRefundRequest.setTenantId(ContextHolder.currentUserTenantId());
        tenantWeightRefundRequest.setChannelId(request.getChannelId());
        tenantWeightRefundRequest.setOperatorUserId(ContextHolder.currentUid());
        tenantWeightRefundRequest.setOperatorUserName(ContextHolder.currentUserName());
        tenantWeightRefundRequest.setViewOrderId(request.getChannelOrderId());
        tenantWeightRefundRequest.setBizOrderWeightRefundItemModels(buildBizOrderWeightRefundItemModelList(request.getWeightRefundProductVOList()));
        tenantWeightRefundRequest.setAppId(ContextHolder.currentUserLoginAppId().toString());
        tenantWeightRefundRequest.setOperatorAccountId(ContextHolder.currentUid());
        try {
            log.info("OCMSOrderServiceWrapper.weightRefund  ocmsOrderThriftService.tenantWeightRefund request:{}", tenantWeightRefundRequest);
            com.meituan.shangou.saas.o2o.dto.response.CommonResponse response = ocmsOrderThriftService.tenantWeightRefund(tenantWeightRefundRequest);
            log.info("OCMSOrderServiceWrapper.weightRefund  ocmsOrderThriftService.tenantWeightRefund response:{}", response);
            if (response.getStatus().getCode() != StatusCodeEnum.SUCCESS.getCode()) {
                commonResponse = CommonResponse.fail(StatusCodeEnum.FAIL.getCode(), response.getStatus().getMessage());
            }
        } catch (TException e) {
            log.error("OCMSOrderServiceWrapper.weightRefund  ocmsOrderThriftService.tenantWeightRefund TException", e);
            throw new CommonRuntimeException(e);
        }
        return commonResponse;
    }

    @Override
    public CommonResponse<WeightRefundCalculateResult> weightRefundCalculate(RefundByWeightRequest request) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(request.getWeightRefundProductVOList())) {
            return CommonResponse.fail2(ResultCode.PARAM_ERR);
        }
        WeightRefundProductVO vo = request.getWeightRefundProductVOList().get(0);

        WeightRefundCalculateRequest req = new WeightRefundCalculateRequest();
        req.setApplyWeight(vo.getActualWeight());
        req.setTenantId(ContextHolder.currentUserTenantId());
        req.setChannelId(request.getChannelId());
        req.setViewOrderId(request.getChannelOrderId());
        req.setCustomSkuId(vo.getCustomSkuId());
        req.setCustomerSpuId(vo.getCustomerSpuId());
        req.setSkuName(vo.getSkuName());
        req.setSkuId(vo.getInnerSkuId());
        req.setItemId(vo.getItemId());
        try {
            CommonResponse<WeightRefundCalculateResult> commonResponse = new CommonResponse();
            WeightRefundCalculateResponse response = ocmsOrderThriftService.tenantWeightRefundCalculate(req);
            if (response.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
                commonResponse.setCode(response.getStatus().getCode());
                commonResponse.setMessage(response.getStatus().getMessage());
            } else {
                commonResponse.setCode(ResultCode.SUCCESS.getCode());
                WeightRefundCalculateResult result = new WeightRefundCalculateResult();
                result.setRefundCalculateResults(
                        Lists.newArrayList(new RefundCalculateResult(response.getSkuId(), response.getRefundMoney())));
                commonResponse.setData(result);
            }
            return commonResponse;
        } catch (Exception e) {
            log.error("weightRefundCalculate error", e);
        }
        return CommonResponse.fail2(ResultCode.INTERNAL_SERVER_ERROR);

    }


    private List<BizOrderWeightRefundItemModel> buildBizOrderWeightRefundItemModelList(List<WeightRefundProductVO> VOList) {
        List<BizOrderWeightRefundItemModel> orderWeightRefundItemModels = Lists.newArrayList();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(VOList)) {
            for (WeightRefundProductVO vo : VOList) {
                if (Objects.isNull(vo) || Objects.isNull(vo.getActualWeight()) || Objects.equals(0D, vo.getActualWeight()) || StringUtils.isBlank(vo.getCustomSkuId())) {
                    continue;
                }
                BizOrderWeightRefundItemModel model = new BizOrderWeightRefundItemModel();
                model.setSkuId(vo.getInnerSkuId());
                model.setCustomSkuId(vo.getCustomSkuId());
                model.setCustomerSpuId(vo.getCustomerSpuId());
                model.setApplyWeight(vo.getActualWeight());
                model.setSkuName(vo.getSkuName());
                model.setItemId(vo.getItemId());
                orderWeightRefundItemModels.add(model);
            }
        }
        return orderWeightRefundItemModels;
    }


    private WeightRefundCheckResponse buildOnlineOrderWeightRefundCheckResponse(OrderDetailResponse response,
                                                                                QueryOnlineWeightRefundGoodsResponse goodsResponse, Long tenantId) {
        if (Objects.isNull(response) || goodsResponse == null
                || CollectionUtils.isEmpty(goodsResponse.getPartRefundGoodsModelList())) {
            return null;
        }
        WeightRefundCheckResponse weightRefundCheckResponse = new WeightRefundCheckResponse();
        OrderBase orderBase = response.getOrderDetail().getOrderBase();
        weightRefundCheckResponse.setPoiId(orderBase.getShopId());
        weightRefundCheckResponse.setDispatchShopId(orderBase.getDispatchShopId());
        List<OrderItemWeightRefundCheckVO> orderItemWeightRefundCheckVOS = Lists.newArrayList();
        Map<String, WeightRefundGoodsModel> weightRefundGoodsModelMap = goodsResponse.getPartRefundGoodsModelList().stream().filter(item -> StringUtils.isNotEmpty(item.getItemId())).collect(Collectors.toMap(WeightRefundGoodsModel::getItemId, v -> v, (a, b) -> a));
        for (WeightRefundGoodsModel refundGoodsModel : goodsResponse.getPartRefundGoodsModelList()){
            OrderItem orderItem = response.getOrderDetail()
                    .getOrderItemList()
                    .stream()
                    .filter(item -> StringUtils.isNotBlank(item.getExtData()))
                    .filter(item->Objects.nonNull(item.getCurrentPrice()) && item.getCurrentPrice() > 0)
                    .filter(item->{
                        String outItemId = getOutItemId(item.getExtData());
                        return StringUtils.isNotEmpty(outItemId) && outItemId.equals(refundGoodsModel.getItemId());
                    })
                    .findAny()
                    .orElse(null);
            if(orderItem == null){
                continue;
            }
            OrderItemWeightRefundCheckVO orderItemWeightRefundCheckVO = buildOnlineOrderWeightRefundCheckVo(response, orderItem, weightRefundGoodsModelMap);
            orderItemWeightRefundCheckVOS.add(orderItemWeightRefundCheckVO);
        }
        weightRefundCheckResponse.setWeightRefundCheckVOList(orderItemWeightRefundCheckVOS);
        if (isAllowRefundAccording2Amount()) {
            return weightRefundCheckResponse;
        }

        List<String> spuList = orderItemWeightRefundCheckVOS.stream().map(OrderItemWeightRefundCheckVO::getSpu).filter(Objects::nonNull).collect(Collectors.toList());
        MerchantSpuIdListCommand merchantSpuIdListCommand = new MerchantSpuIdListCommand();
        merchantSpuIdListCommand.setMerchantId(tenantId);
        merchantSpuIdListCommand.setSpuIds(spuList);
        if (CollectionUtils.isEmpty(spuList)){
            return weightRefundCheckResponse;
        }
        try {
            QueryMerchantSpuListResult queryMerchantSpuListResult = empowerMerchantSpuThriftService.queryMerchantSpuByIds(merchantSpuIdListCommand);
            orderItemWeightRefundCheckVOS = orderItemWeightRefundCheckVOS.stream().filter(vo -> spuAllowRefund(queryMerchantSpuListResult, vo.getSpu())).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("empowerMerchantSpuThriftService.queryMerchantSpuByIds error", e);
        }
        weightRefundCheckResponse.setWeightRefundCheckVOList(orderItemWeightRefundCheckVOS);
        return weightRefundCheckResponse;


    }

    private String getOutItemId(String extDataJson) {
        try {
            JSONObject extData = JSON.parseObject(extDataJson);
            if (!Objects.isNull(extData) && StringUtils.isNotEmpty(extData.getString("outItemId"))) {
                return extData.getString("outItemId");
            }
        } catch (Exception e) {
            log.error("parse extData error", e);
        }
        return StringUtils.EMPTY;
    }

    private OrderItemWeightRefundCheckVO buildOnlineOrderWeightRefundCheckVo(OrderDetailResponse response, OrderItem orderItem, Map<String, WeightRefundGoodsModel> weightRefundGoodsModelMap) {
        OrderItemWeightRefundCheckVO orderItemWeightRefundCheckVO = new OrderItemWeightRefundCheckVO();
        orderItemWeightRefundCheckVO.setInnerSkuId(orderItem.getInstoreSkuId());
        orderItemWeightRefundCheckVO.setCustomerSkuId(orderItem.getCustomerSkuId());
        orderItemWeightRefundCheckVO.setCustomerSpuId(orderItem.getCustomerSpu());
        orderItemWeightRefundCheckVO.setSkuName(orderItem.getSkuName());
        orderItemWeightRefundCheckVO.setChannelWeight(orderItem.getChannelWeight());
        String outItemId = getOutItemId(orderItem.getExtData());
        if(MapUtils.isNotEmpty(weightRefundGoodsModelMap) && StringUtils.isNotEmpty(outItemId) && weightRefundGoodsModelMap.containsKey(outItemId)){
            orderItemWeightRefundCheckVO.setCanRefundCount(weightRefundGoodsModelMap.get(outItemId).getCanRefundCount());
        }else {
            orderItemWeightRefundCheckVO.setCanRefundCount(orderItem.getCanWeightRefundCount());
        }
        orderItemWeightRefundCheckVO.setPicUrl(processPicUrl(orderItem.getPicUrl()));
        orderItemWeightRefundCheckVO.setCurrentPrice(orderItem.getCurrentPrice());
        orderItemWeightRefundCheckVO.setSpecification(orderItem.getSpecification());
        orderItemWeightRefundCheckVO.setErpItemCode(orderItem.getErpItemCode());
        orderItemWeightRefundCheckVO.setUpc(orderItem.getBarCode());
        orderItemWeightRefundCheckVO.setSpu(orderItem.getSpu());
        orderItemWeightRefundCheckVO.setItemId(getOutItemId(orderItem.getExtData()));
        orderItemWeightRefundCheckVO.setWeightRefundAfterSaleRecordVOS(buildWeightRefundAfterSaleRecordList(orderItem, response.getOrderDetail().getAfterSaleApplyList()));
        //组合商品
        orderItemWeightRefundCheckVO.setSubProduct(CombinationProductUtil.buildSubProduct(orderItem.getCombinationChildProductList(), orderItem.getQuantity()));
        // 处理换货商品信息
        orderItemWeightRefundCheckVO.setExchangeProductVoList(
                ExchangeItemUtil.getExchangeProductList(orderItem, response.getOrderDetail().getOrderItemList()));
        return orderItemWeightRefundCheckVO;
    }

    private WeightRefundCheckResponse buildWeightRefundCheckResponse(OrderDetailResponse response) {
        WeightRefundCheckResponse weightRefundCheckResponse = new WeightRefundCheckResponse();
        List<OrderItemWeightRefundCheckVO> orderItemWeightRefundCheckVOS = Lists.newArrayList();
        for (OrderItem orderItem : response.getOrderDetail().getOrderItemList()) {
            OrderItemWeightRefundCheckVO orderItemWeightRefundCheckVO = buildWeightRefundCheckVo(response, orderItem);
            if (Objects.nonNull(orderItem.getCurrentPrice()) && orderItem.getCurrentPrice() <= 0){
                continue;
            }
            orderItemWeightRefundCheckVOS.add(orderItemWeightRefundCheckVO);
        }
        OrderBase orderBase = response.getOrderDetail().getOrderBase();
        weightRefundCheckResponse.setPoiId(orderBase.getShopId());
        weightRefundCheckResponse.setDispatchShopId(orderBase.getDispatchShopId());
        weightRefundCheckResponse.setWeightRefundCheckVOList(filterRefundDifferenceMark(orderItemWeightRefundCheckVOS));
        return weightRefundCheckResponse;
    }

    private List<OrderItemWeightRefundCheckVO> filterRefundDifferenceMark(List<OrderItemWeightRefundCheckVO> orderItemWeightRefundCheckVOS) {
        if (CollectionUtils.isEmpty(orderItemWeightRefundCheckVOS) || isAllowRefundAccording2Amount()){
            return orderItemWeightRefundCheckVOS;
        }
        List<String> spuList = orderItemWeightRefundCheckVOS.stream()
                .map(OrderItemWeightRefundCheckVO::getSpu)
                .filter(Objects::nonNull).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(spuList)){
            return orderItemWeightRefundCheckVOS;
        }

        MerchantSpuIdListCommand merchantSpuIdListCommand = new MerchantSpuIdListCommand();
        merchantSpuIdListCommand.setMerchantId(ContextHolder.currentUserTenantId());
        merchantSpuIdListCommand.setSpuIds(spuList);
        try {
            QueryMerchantSpuListResult queryMerchantSpuListResult = empowerMerchantSpuThriftService.queryMerchantSpuByIds(merchantSpuIdListCommand);
            return orderItemWeightRefundCheckVOS.stream().filter(v -> spuAllowRefund(queryMerchantSpuListResult, v.getSpu())).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("empowerMerchantSpuThriftService.queryMerchantSpuByIds error", e);
        }
        return orderItemWeightRefundCheckVOS;
    }

    private boolean spuAllowRefund(QueryMerchantSpuListResult queryMerchantSpuListResult, String spu){
        if (StringUtils.isBlank(spu)){
            return true;
        }
        if (CollectionUtils.isEmpty(queryMerchantSpuListResult.getSpuList())){
            return true;
        }
        Map<String, MerchantSpuInfo> merchantSpuInfoMap = queryMerchantSpuListResult.getSpuList().stream().collect(Collectors.toMap(MerchantSpuInfo::getSpuId, Function.identity(), (v1, v2) -> v2));
        if (merchantSpuInfoMap.containsKey(spu)){
            MerchantSpuInfo merchantSpuInfo = merchantSpuInfoMap.get(spu);
            if (merchantSpuInfo != null && merchantSpuInfo.getMerchantSpuErpPvExtend() != null){
                return Objects.equals(merchantSpuInfo.getMerchantSpuErpPvExtend().allowWeightRefund, 1);
            }
        }
        return true;
    }

    private boolean isAllowRefundAccording2Amount(){
        ConfigQueryRequest request = new ConfigQueryRequest();
        request.setTenantId(ContextHolder.currentUserTenantId());
        request.setConfigId(ConfigItemEnum.IS_RESTRICT_NOT_ALL_AFTER_SALE.getKey());
        request.setSubjectId(ContextHolder.currentUserTenantId());
        try{
            TenantConfigResponse response = configThriftService.queryTenantConfig(request);
            if (response.getStatus() != null && response.getStatus().getCode() == StatusCodeEnum.SUCCESS.getCode() && response.getConfig() != null && response.getConfig().getConfigContent() != null) {
                return !ConfigItemEnum.IS_RESTRICT_NOT_ALL_AFTER_SALE.isMainConfigYesStr(response.getConfig().getConfigContent());
            }
        } catch (Exception e){
            log.error("queryTenantConfig isAllowRefundAccording2Amount error", e);
        }
        return true;
    }


    private OrderItemWeightRefundCheckVO buildWeightRefundCheckVo(OrderDetailResponse response, OrderItem orderItem) {
        OrderItemWeightRefundCheckVO orderItemWeightRefundCheckVO = new OrderItemWeightRefundCheckVO();
        orderItemWeightRefundCheckVO.setInnerSkuId(orderItem.getInstoreSkuId());
        orderItemWeightRefundCheckVO.setCustomerSkuId(orderItem.getCustomerSkuId());
        orderItemWeightRefundCheckVO.setCustomerSpuId(orderItem.getCustomerSpu());
        orderItemWeightRefundCheckVO.setSkuName(orderItem.getSkuName());
        orderItemWeightRefundCheckVO.setChannelWeight(orderItem.getChannelWeight());
        orderItemWeightRefundCheckVO.setCanRefundCount(orderItem.getCanWeightRefundCount());
        orderItemWeightRefundCheckVO.setPicUrl(processPicUrl(orderItem.getPicUrl()));
        orderItemWeightRefundCheckVO.setCurrentPrice(orderItem.getCurrentPrice());
        orderItemWeightRefundCheckVO.setSpecification(orderItem.getSpecification());
        orderItemWeightRefundCheckVO.setSpu(orderItem.getSpu());
        orderItemWeightRefundCheckVO.setWeightRefundAfterSaleRecordVOS(buildWeightRefundAfterSaleRecordList(orderItem, response.getOrderDetail().getAfterSaleApplyList()));
        //组合商品
        orderItemWeightRefundCheckVO.setSubProduct(CombinationProductUtil.buildSubProduct(orderItem.getCombinationChildProductList(), orderItem.getQuantity()));
        // 处理换货商品信息
        orderItemWeightRefundCheckVO.setExchangeProductVoList(
                ExchangeItemUtil.getExchangeProductList(orderItem, response.getOrderDetail().getOrderItemList()));
        OrderItemExchangeModel exchangeModel = OrderItemExchangeModel.build(orderItem.getExtData());
        orderItemWeightRefundCheckVO.setExchangeFromCount(exchangeModel.getExchangeSourceOrderItemCnt());
        orderItemWeightRefundCheckVO.setExchangeToCount(exchangeModel.getExchangeOrderItemToCnt());
        return orderItemWeightRefundCheckVO;
    }

    private List<WeightRefundAfterSaleRecordVO> buildWeightRefundAfterSaleRecordList(OrderItem orderItem, List<AfterSaleApply> afterSaleApplyList) {
        List<WeightRefundAfterSaleRecordVO> weightRefundAfterSaleRecordVOS = Lists.newArrayList();
        for (AfterSaleApply afterSaleApply : afterSaleApplyList) {
            for (AfterSaleApplyDetail afterSaleApplyDetail : afterSaleApply.getAfterSaleApplyDetailList()) {
                //若当前商品项发生过克重退款
                if (orderItem.getOrderItemId().equals(afterSaleApplyDetail.getOrderItemId()) && afterSaleApply.getAfsPattern() == AfterSalePatternEnum.WEIGHT.getValue()) {
                    WeightRefundAfterSaleRecordVO weightRefundAfterSaleRecordVO = new WeightRefundAfterSaleRecordVO();
                    weightRefundAfterSaleRecordVO.setRefundAmount(afterSaleApplyDetail.getRefundAmt());
                    weightRefundAfterSaleRecordVO.setRefundWeight(afterSaleApplyDetail.getRefundWeight());
                    weightRefundAfterSaleRecordVO.setSkuName(afterSaleApplyDetail.getSkuName());
                    weightRefundAfterSaleRecordVOS.add(weightRefundAfterSaleRecordVO);
                }
            }
        }
        return weightRefundAfterSaleRecordVOS;
    }

    private String processPicUrl(String picUrl) {
        if (Strings.isEmpty(picUrl)) {
            return picUrl;
        }
        if (picUrl.contains(SYMBOL_COMMA)) {
            String[] picUrls = picUrl.split(SYMBOL_COMMA);
            if (picUrls.length > 0) {
                return picUrls[0];
            }
        }
        return picUrl;
    }

    @Override
    public List<OrderItemMoneyRefundCheckVO> moneyRefundCheck(OrderMoneyRefundCheckRequest request) {
        // 查询订单详情
        OrderDetail orderDetail = ocmsOrderRemoteService.getOrderDetail(request.getChannelOrderId(),
                request.getChannelId(), ContextHolder.currentUserTenantId());
        List<BizOrderItemMoneyRefundCheckModel> moneyRefundCheckModels;
        if (Objects.equals(request.getChannelId(), DynamicChannelType.TAO_XIAN_DA.getChannelId()) && isBeforePickCompleted(orderDetail.getDeliveryInfoDTO())) {
            moneyRefundCheckModels = moneyRefundCheckForDraft(orderDetail);
        } else {
            moneyRefundCheckModels = orderBizRemoteService.moneyRefundCheck(request);
        }
        if (CollectionUtils.isEmpty(moneyRefundCheckModels)) {
            return Lists.newArrayList();
        }

        //金额为0的不做展示
        List<OrderItemMoneyRefundCheckVO> orderItemMoneyRefundCheckVOS = moneyRefundCheckModels.stream()
                .filter(model -> model.getCanRefundMoney() != null && model.getCanRefundMoney() > 0)
                .map(OrderItemMoneyRefundCheckVO::buildOrderItemMoneyRefundCheckVO).collect(Collectors.toList());

        if (orderDetail != null && CollectionUtils.isNotEmpty(orderDetail.getOrderItemList())) {
            Map<Long, OrderItem> orderItemMap = orderDetail.getOrderItemList().stream()
                    .collect(Collectors.toMap(OrderItem::getOrderItemId, item -> item));
            //根据OrderItemId匹配订单详情，返回商品重量，并计算订单商品总重量
            orderItemMoneyRefundCheckVOS.forEach(vos -> {
                // 设置门店ID，后续数字鉴权使用
                vos.setShopId(Optional.ofNullable(orderDetail.getOrderBase()).map(OrderBase::getShopId).orElse(null));
                vos.setDispatchShopId(Optional.ofNullable(orderDetail.getOrderBase()).map(OrderBase::getDispatchShopId).orElse(null));
                OrderItem orderItem;
                if (StringUtils.isNotBlank(vos.getOrderItemId())
                        && (orderItem = orderItemMap.get(Long.valueOf(vos.getOrderItemId()))) != null) {
                    Integer channelWeight = Optional.ofNullable(orderItem.getChannelWeight()).orElse(0);
                    vos.setChannelWeight(channelWeight);
                    if (channelWeight != null && orderItem.getQuantity() != null) {
                        vos.setTotalChannelWeight(channelWeight * orderItem.getQuantity());
                    }
                    //需要查看是否有组合品，获取图片
                    CombinationProductUtil.separateDealPicUrl(vos.getSubProduct(), orderItem);
                    //处理换货商品信息
                    vos.setExchangeProductVoList(ExchangeItemUtil.getExchangeProductList(orderItem, orderDetail.getOrderItemList()));

                    OrderItemExchangeModel exchangeModel = OrderItemExchangeModel.build(orderItem.getExtData());
                    vos.setExchangeFromCount(exchangeModel.getExchangeSourceOrderItemCnt());
                    vos.setExchangeToCount(exchangeModel.getExchangeOrderItemToCnt());
                }
            });
        }
        //淘鲜达需要补充currentPrice字段值,克重转换金额使用
        if (orderDetail != null && Objects.equals(DynamicChannelType.TAO_XIAN_DA.getChannelId(), request.getChannelId())) {
            fillCurrentPrice(orderDetail, orderItemMoneyRefundCheckVOS);
        }
        if (isAllowRefundAccording2Amount()) {
            return orderItemMoneyRefundCheckVOS;
        }

        List<String> spuList = moneyRefundCheckModels.stream().map(BizOrderItemMoneyRefundCheckModel::getSpu).filter(Objects::nonNull).collect(Collectors.toList());
        MerchantSpuIdListCommand merchantSpuIdListCommand = new MerchantSpuIdListCommand();
        merchantSpuIdListCommand.setMerchantId(ContextHolder.currentUserTenantId());
        merchantSpuIdListCommand.setSpuIds(spuList);
        if (CollectionUtils.isEmpty(spuList)){
            return orderItemMoneyRefundCheckVOS;
        }
        try {
            QueryMerchantSpuListResult queryMerchantSpuListResult = empowerMerchantSpuThriftService.queryMerchantSpuByIds(merchantSpuIdListCommand);
            return orderItemMoneyRefundCheckVOS.stream().filter(vo -> spuAllowRefund(queryMerchantSpuListResult, vo.getSpu())).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("empowerMerchantSpuThriftService.queryMerchantSpuByIds error", e);
        }
        return orderItemMoneyRefundCheckVOS;
    }

    private void fillCurrentPrice(OrderDetail orderDetail, List<OrderItemMoneyRefundCheckVO> orderItemMoneyRefundCheckVOS) {
        try {
            if (CollectionUtils.isEmpty(orderItemMoneyRefundCheckVOS)) {
                return;
            }
            boolean present = orderItemMoneyRefundCheckVOS.stream().filter(i -> Objects.isNull(i.getCurrentPrice())).findAny().isPresent();
            if (!present) {
                return;
            }
            OrderFinanceModel orderFinanceModel = queryOrderFinance(orderDetail);
            if (orderFinanceModel == null) {
                throw new BizException("查询商品开票价格失败");
            }
            Map<Long, OrderFinanceDetailModel> financeDetailModelMap = orderFinanceModel.getOrderItemCreateModelList().stream()
                    .collect(Collectors.toMap(OrderFinanceDetailModel::getOrderItemId, java.util.function.Function.identity(), (o1, o2) -> o1));
            for (OrderItemMoneyRefundCheckVO checkVo : orderItemMoneyRefundCheckVOS) {
                OrderFinanceDetailModel financeDetailModel = financeDetailModelMap.get(Long.valueOf(checkVo.getOrderItemId()));
                if (financeDetailModel == null) {
                    continue;
                }
                checkVo.setCurrentPrice(financeDetailModel.getBillAmt());
            }
        } catch (Exception e) {
            log.error("淘鲜达赋值开票金额异常,channelOrderId:{}", orderDetail.getOrderBase().getViewOrderId(), e);
        }

    }

    private List<BizOrderItemMoneyRefundCheckModel> moneyRefundCheckForDraft(OrderDetail orderDetail) {
        List<BizOrderItemMoneyRefundCheckModel> list =  new ArrayList<>();
        Map<Long, BigDecimal> refundedCountMap = getDraftRefundCount(orderDetail);
        OrderFinanceModel orderFinanceModel = queryOrderFinance(orderDetail);
        if (orderFinanceModel == null) {
            throw new BizException("查询订单财务数据失败");
        }
        Map<Long, OrderFinanceDetailModel> financeDetailModelMap = orderFinanceModel.getOrderItemCreateModelList().stream()
                .collect(Collectors.toMap(i -> i.getOrderItemId(), Function.identity(), (o1, o2) -> o1));
        for (OrderItem orderItem : orderDetail.getOrderItemList()) {
            OrderFinanceDetailModel financeDetailModel = financeDetailModelMap.get(orderItem.getOrderItemId());
            if (financeDetailModel == null) {
                continue;
            }
            BigDecimal refundedCount = refundedCountMap.get(orderItem.getOrderItemId());
            if (refundedCount == null) {
                refundedCount = BigDecimal.ZERO;
            }
            BizOrderItemMoneyRefundCheckModel checkVO = new BizOrderItemMoneyRefundCheckModel();
            checkVO.setOrderItemId(orderItem.getOrderItemId());
            BigDecimal refundedMoney = refundedCount.multiply(BigDecimal.valueOf(financeDetailModel.getBillPrice()));
            int canRefundMoney = BigDecimal.valueOf(financeDetailModel.getBillAmt()).subtract(refundedMoney).intValue();
            checkVO.setCanRefundMoney(Math.max(0, canRefundMoney));
            checkVO.setSkuName(orderItem.getSkuName());
            checkVO.setCustomerSkuId(orderItem.getCustomerSkuId());
            checkVO.setCurrentPrice(financeDetailModel.getBillAmt());
            checkVO.setSpu(orderItem.getSpu());
            checkVO.setBarCode(orderItem.getBarCode());
            checkVO.setInnerSkuId(orderItem.getInstoreSkuId());
            checkVO.setPicUrl(orderItem.getPicUrl());
            checkVO.setSpecification(orderItem.getSpecification());
            checkVO.setErpItemCode(orderItem.getErpItemCode());
            checkVO.setSkuCount(orderItem.getQuantity() - ((int) Math.floor(refundedCount.doubleValue())));
            list.add(checkVO);

        }
        return list.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    private Map<Long, BigDecimal> getDraftRefundCount(OrderDetail orderDetail) {
        Map<Long, BigDecimal> refundedCountMap = new HashMap<>();
        for (AfterSaleApply afterSaleApply : orderDetail.getAfterSaleApplyList()) {

            if (AfterSalePatternEnum.WEIGHT.getValue() == afterSaleApply.getAfsPattern()
                    || AfterSalePatternEnum.AMOUNT.getValue() == afterSaleApply.getAfsPattern()) {
                for (AfterSaleApplyDetail afterSaleApplyDetail : afterSaleApply.getAfterSaleApplyDetailList()) {
                    BigDecimal refundedCount = refundedCountMap.getOrDefault(afterSaleApplyDetail.getOrderItemId(), BigDecimal.ZERO);
                    refundedCountMap.put(afterSaleApplyDetail.getOrderItemId() ,refundedCount.add(BigDecimal.valueOf(afterSaleApplyDetail.getPartialRefundCount())));
                }
            }else {
                for (AfterSaleApplyDetail afterSaleApplyDetail : afterSaleApply.getAfterSaleApplyDetailList()) {
                    BigDecimal refundedCount = refundedCountMap.getOrDefault(afterSaleApplyDetail.getOrderItemId(), BigDecimal.ZERO);
                    refundedCountMap.put(afterSaleApplyDetail.getOrderItemId() ,refundedCount.add(BigDecimal.valueOf(afterSaleApplyDetail.getCount())));
                }
            }
        }
        return refundedCountMap;
    }

    private OrderFinanceModel queryOrderFinance(OrderDetail orderDetail) {
        OrderFinanceQueryRequest financeQueryRequest = OrderFinanceQueryRequest.builder()
                .ids(Lists.newArrayList(orderDetail.getOrderBase().getOrderId()))
                .includeDetail(true)
                .type(1)
                .shopId(orderDetail.getOrderBase().getShopId())
                .tenantId(orderDetail.getOrderBase().getTenantId()).build();
        try {
            log.info("查询订单参悟数据,request:{}", financeQueryRequest);
            OrderFinanceQueryResponse response = orderFinanceThriftService.queryOrderFinanceInfo(financeQueryRequest);
            log.info("查询订单财务数据,Response:{}", response);
            if (response == null || response.getStatus() == null || response.getStatus().getCode() != StatusCodeEnum.SUCCESS.getCode()) {
                return null;
            }
            return response.getOrderFinanceModelList().stream().findFirst().orElse(null);
        }catch (Exception e) {
            log.error("查询订单财务数据异常", e);
            return null;
        }
    }

    private boolean isBeforePickCompleted(DeliveryInfoDTO deliveryInfoDTO) {
        Integer deliveryStatus = Optional.ofNullable(deliveryInfoDTO).orElse(new DeliveryInfoDTO()).getDeliveryStatus();
        if (deliveryStatus != null) {
            if (deliveryStatus != DeliveryStatusEnum.WAIT_TO_CONFIRM.getValue()
                    && deliveryStatus != DeliveryStatusEnum.WAIT_TO_PICK.getValue()
                    && deliveryStatus != DeliveryStatusEnum.PICKING.getValue()) {
                return false;
            }
        }
        return true;
    }

    @Override
    public CommonResponse moneyRefund(OrderMoneyRefundRequest request) {
        if (DynamicChannelType.MEITUAN.getChannelId() == request.getChannelId()) {
            // 美团名酒馆订单进行提示
            Pair<Boolean, Pair<Integer, String>> resultPair = ocmsChannelRemoteService.refundCheckMtFamousTavern(
                    request.getChannelOrderId(), ContextHolder.currentUserTenantId(),
                    ErrorCodeEnum.REFUND_CHECK_MT_FAMOUS_TAVERN_PROMPT, ErrorCodeEnum.WEB_MT_FAMOUS_TAVERN_GATHER_PHONE_NOT_FIND);
            if (resultPair.getKey()) {
                return CommonResponse.fail(resultPair.getValue().getKey(), resultPair.getValue().getValue());
            }
            mtAmountRefundCheck4Price(request);
        }
        CommonResultBO commonResultBO = orderBizRemoteService.moneyRefund(request);
        if (commonResultBO.getSuccess()) {
            return CommonResponse.success(null);
        } else {
            return CommonResponse.fail(StatusCodeEnum.FAIL.getCode(), commonResultBO.getMessage());
        }
    }

    /**
     * 美团金额退验证
     * @param request
     */
    private void mtAmountRefundCheck4Price(OrderMoneyRefundRequest request) {
        OrderMoneyRefundCheckRequest req = new OrderMoneyRefundCheckRequest();
        req.setChannelId(request.getChannelId());
        req.setChannelOrderId(request.getChannelOrderId());
        List<BizOrderItemMoneyRefundCheckModel> bizOrderItemMoneyRefundCheckModels = orderBizRemoteService.moneyRefundCheck(req);
        if (CollectionUtils.isEmpty(bizOrderItemMoneyRefundCheckModels)) {
            throw new BizException("无可灵活金额退商品");
        }
        Map<Long, List<BizOrderItemMoneyRefundCheckModel>> checkMap = bizOrderItemMoneyRefundCheckModels.stream()
                .collect(Collectors.groupingBy(BizOrderItemMoneyRefundCheckModel::getOrderItemId));
        //美团接口可能会把同itemId商品返回多行,这里可退金额取同itemId商品中最大值进行校验
        for (MoneyRefundItemVo refundItem : request.getRefundItems()) {
            if (CollectionUtils.isEmpty(checkMap.get(refundItem.getOrderItemId()))) {
                continue;
            }
            List<BizOrderItemMoneyRefundCheckModel> checkModelList = checkMap.get(refundItem.getOrderItemId());
            Optional<Integer> maxRefundMoney = checkModelList.stream().map(BizOrderItemMoneyRefundCheckModel::getCanRefundMoney).max(Integer::compare);
            if (!maxRefundMoney.isPresent()) {
                continue;
            }
            //refundItem的退款金额为综合,checkModel的可退金额为单个可退需要与申请数量相乘得到最大可退金额并进行比较
            if (refundItem.getRefundMoney() == (maxRefundMoney.get() * refundItem.getRefundCount())) {
                throw new ParamInvalidException("退款金额等于可退金额请使用按件部分退");
            }
            if (refundItem.getRefundMoney() > (maxRefundMoney.get() * refundItem.getRefundCount())) {
                throw new ParamInvalidException("输入退款金额不能大于可退金额");
            }
        }
    }

    @Override
    public CommonResponse afterSaleRefund(AfterSaleRefundRequest request) {
        CommonResultBO commonResultBO = orderBizRemoteService.tenantAfterSaleRefund(request);
        if (commonResultBO.getSuccess()) {
            String dealMsg = dealProductOrStock(request.getChannelId(), request.getChannelOrderId(), request.getStoreId(),
                    request.getRefundGoodsSoldOutVOList(), request.getRefundGoodsTakenOffVOList());
            if (StringUtils.isNotEmpty(dealMsg)) {
                return new CommonResponse(ResultCode.SUCCESS.getCode(), dealMsg, null);
            }
            return CommonResponse.success(null);
        } else {
            return CommonResponse.fail(StatusCodeEnum.FAIL.getCode(), commonResultBO.getMessage());
        }
    }

    @Override
    public List<RefundReasonAndCodeVO> queryRefundReasons(RefundReasonAndCodeRequest request) {
        try {
            OCMSRefundCheckResponse ocmsRefundCheckResponse = ocmsOrderOperateThriftService.refundCheck(OCMSCheckRefundRequest.builder()
                    .orderBizType(request.getOrderBizType())
                    .viewOrderId(request.getViewOrderId())
                    .tenantId(ContextHolder.currentUserTenantId())
                    .refundType(request.getRefundType())
                    .refundOperationType(request.getRefundOperationType())
                    .build());
            if (ocmsRefundCheckResponse == null
                    || ocmsRefundCheckResponse.getStatus() == null
                    || ocmsRefundCheckResponse.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
                log.error("获取退款理由失败,request:{}", request);
                return Lists.newArrayList();
            }
            List<RefundReasonAndCodeVO> reasons = ocmsRefundCheckResponse.getPossibleRefundReasons().stream()
                    .map(this::convertRefundReason).collect(Collectors.toList());
            return reasons;
        } catch (Exception e) {
            log.error("获取退款理由异常,request:{}", request, e);
            return Lists.newArrayList();
        }
    }

    private RefundReasonAndCodeVO convertRefundReason(OCMSRefundReasonAndCodeModel model) {
        RefundReasonAndCodeVO vo = new RefundReasonAndCodeVO();
        vo.setCode(model.getCode());
        vo.setReason(model.getReason());
        vo.setEvidenceNeed(model.getEvidenceNeed());
        vo.setEvidenceDescription(model.getEvidenceDescription());
        return vo;
    }

    private String dealProductOrStock(Integer channelId, String channelOrderId, Long storeId, List<RefundGoodsSoldOutVO> refundGoodsSoldOutVOList,
                                      List<RefundGoodsTakenOffVO> refundGoodsTakenOffVOList) {
        //这里只处理库存清空，暂不处理商品售罄逻辑，如需要，可参考app逻辑
        if (CollectionUtils.isNotEmpty(refundGoodsSoldOutVOList) && !MccConfigUtil.getDrunkHorseTenantId().contains(ContextHolder.currentUserTenantId())) {
            List<PartRefundRequest.PartRefundItem> refundGoodsSoldOutVOs = refundGoodsSoldOutVOList.stream().map(vo -> {
                PartRefundRequest.PartRefundItem item = new PartRefundRequest.PartRefundItem();
                item.setSku(vo.getSkuId());
                item.setCustomSkuId(vo.getCustomSkuId());
                item.setSkuName(vo.getSkuName());
                return item;
            }).collect(Collectors.toList());
            return orderBizRemoteService.setSKuStoreStockZero(ContextHolder.currentUserTenantId(), storeId, ContextHolder.currentUserStaffId(),
                    ContextHolder.currentUserName(), refundGoodsSoldOutVOs);
        }
        return StringUtils.EMPTY;
    }

}
