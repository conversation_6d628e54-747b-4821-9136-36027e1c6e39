package com.sankuai.shangou.qnh.orderapi.service.common.query;


import com.meituan.shangou.saas.common.enums.OrderCanOperateItem;
import com.meituan.shangou.saas.order.management.client.dto.request.revenue.OCMSListOrderRevenueDetailRequest;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OCMSListOrderResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.meituan.shangou.saas.order.management.client.service.revenue.MerChantRevenueQueryService;
import com.meituan.shangou.saas.order.platform.enums.FuseOrderStatusEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiEntityTypeEnum;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;

import com.sankuai.shangou.qnh.orderapi.domain.request.app.OrderListForAppLocalCacheRequest;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.OrderListResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.PageInfoVO;
import com.sankuai.shangou.qnh.orderapi.enums.app.ResultCodeEnum;
import com.sankuai.shangou.qnh.orderapi.service.common.*;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;

import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;


/**
 * <AUTHOR>
 * @since 2024/7/17
 **/
@Service
@Slf4j
public class AppCacheListQueryOrderService extends QueryOrderService {

    @Resource
    private MerChantRevenueQueryService merChantRevenueQueryService;
    @Resource
    private DeliveryService deliveryService;
    @Resource
    private OrderOperateItemsService orderOperateItemsService;
    @Resource
    private PickSelectService pickSelectService;

    @Override
    public boolean supportParallelQuery(){
        return false;
    }

    @Override
    public Pair<List<OCMSOrderVO>, PageInfoVO> queryOrderInfo(OrderListRequestContext request) {
        OrderListForAppLocalCacheRequest cacheRequest = (OrderListForAppLocalCacheRequest) request.getRequest();
        OCMSListOrderResponse response = merChantRevenueQueryService.listOrderRevenueDetail(buildAppLocalCacheQueryReq(request, cacheRequest));
        if (response.getStatus().getCode() != com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum.SUCCESS.getValue()) {
            throw new BizException(ResultCodeEnum.FAIL.getCode(), response.getStatus().getMessage());
        }
        PageInfoVO pageInfoVO = PageUtil.buildPageInfoVO(request.getPage(), request.getSize(), response.getTotalCount());
        return new Pair<>(response.getOcmsOrderList(), pageInfoVO);
    }

    private OCMSListOrderRevenueDetailRequest buildAppLocalCacheQueryReq(OrderListRequestContext request, OrderListForAppLocalCacheRequest cacheRequest) {
        OCMSListOrderRevenueDetailRequest tRequest = new OCMSListOrderRevenueDetailRequest();
        tRequest.setPage(request.getPage());
        tRequest.setSize(request.getSize());
        tRequest.setTenantId(request.getTenantId());
        tRequest.setIncludeRevenueDetail(request.isShowSalePrice());
        if (Objects.isNull(request.getEntityType()) || request.getEntityType() == PoiEntityTypeEnum.STORE.code()) {
            tRequest.setShopIdList(Collections.singletonList(request.getStoreId()));
        } else if (request.getEntityType() == PoiEntityTypeEnum.SHAREABLE_WAREHOUSE.code()) {
            tRequest.setWarehouseIdList(Lists.newArrayList(request.getStoreId()));
        }
        LocalDateTime beginDateTime = LocalDateTime.now().minusDays(MccConfigUtil.getAppLocalCacheMaxDayLength());
        LocalDateTime orderTimeEnd = LocalDateTime.now();
        if (Objects.nonNull(cacheRequest.getOrderTimeEnd()) && cacheRequest.getOrderTimeEnd() > 1000) {
            orderTimeEnd = LocalDateTime.ofInstant(java.time.Instant.ofEpochMilli(cacheRequest.getOrderTimeEnd()), ZoneId.systemDefault());
        }
        tRequest.setFuseOrderStatusList(Arrays.asList(FuseOrderStatusEnum.SUBMIT.getValue(), FuseOrderStatusEnum.PICKED.getValue(),
                FuseOrderStatusEnum.SHIPPING.getValue(), FuseOrderStatusEnum.MERCHANT_CONFIRMED.getValue(), FuseOrderStatusEnum.WAIT_SELF_FETCH.getValue()));
        tRequest.setBeginCreateTime(beginDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        if (cacheRequest.isQueryAfterSaleOrder()){
            //pda查询出售后单
            tRequest.setFuseOrderStatusList(Arrays.asList(FuseOrderStatusEnum.REFUND_APPLIED.getValue()));
            tRequest.setBeginCreateTime(AfterSaleQueryOrderService.getAfterSaleQueryStartTime());
        }
        tRequest.setEndCreateTime(orderTimeEnd.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        return tRequest;
    }

    @Override
    public void addExtraInfo(OrderListResponse orderListResponse, OrderListRequestContext request) {
        OrderStatusUtil.fixOrdersStatusDescForAppCache(orderListResponse);
        deliveryService.buildDeliveryCount(orderListResponse);
        orderOperateItemsService.justKeepPartOperateItemsForDeliveryErrorOrder(orderListResponse, Lists.newArrayList(OrderCanOperateItem.FULL_ORDER_REFUND, OrderCanOperateItem.CREATE_INVOICE, OrderCanOperateItem.SETTING_ORDER_TAG));
        pickSelectService.fillExistsPickTaskForWait2PickOrder(orderListResponse, request.getStoreId());
        fillWait2ConfirmOrderDeliveryStatusChangeTimeWithPayTime(orderListResponse.getOrderList());
        deleteAfterSaleOrderViewFuseOrderStatus(orderListResponse.getOrderList());
    }





}
