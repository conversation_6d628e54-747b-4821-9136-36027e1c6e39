package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.shangou.saas.tenant.thrift.dto.department.request.DepartmentModRequest;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2018/10/23
 * 部门新增请求
 **/
@ApiModel("部门创建请求参数")
@Data
public class DepartmentModifyRequest extends DepartmentCreateRequest {

    @ApiModelProperty(value = "部门ID", required = true)
    private Long departmentId;


    @Override
    public void validate() {
        AssertUtil.isPositiveNumber(departmentId, "部门Id非空");
        super.validate();
    }

    public DepartmentModRequest convert2Mod() {
        DepartmentModRequest req = new DepartmentModRequest();
        req.setDepartmentId(departmentId);
        req.setDepartmentName(departmentName);
        req.setDepartmentType(departmentType);
        req.setTenantId(ContextHolder.currentUserTenantId());
        req.setOptUser(ContextHolder.currentAccount());
        req.setParentId(parentDepartmentId);
        return req;
    }


}
