package com.sankuai.shangou.qnh.orderapi.service.pc;

import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.PoiInfoBO;

import java.util.List;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/16 11:08
 * @Description:
 */
public interface PoiService {

    /**
     * 根据门店ID列表查询门店信息
     *
     * @param tenantId
     * @param poiIds
     * @return
     */
    Map<Long, PoiInfoBO> queryPoiByIds(Long tenantId, List<Long> poiIds);

}
