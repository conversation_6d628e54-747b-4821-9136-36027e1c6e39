package com.sankuai.shangou.qnh.orderapi.domain.request.app;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 5/6/22
 **/
@TypeDoc(
        description = "根据子类型统计待配送订单数量"
)
@ApiModel("根据子类型统计待配送订单数量")
@Data
public class QueryWaitDeliverySubTypeCountRequest {
    @FieldDoc(
            description = "门店类型 3-门店 5-中心仓 6-前置仓", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店类型")
    private Integer entityType;


    @FieldDoc(
            description = "配送方式对应的配送模式 1-平台配送 2-聚合配送 3-商家自送", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "配送方式对应的配送模式 1-平台配送 2-聚合配送 3-商家自送")
    private List<Integer> deliveryTypeModeList;
}
