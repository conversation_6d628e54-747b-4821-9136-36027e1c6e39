// Copyright (C) 2020 Meituan
// All rights reserved
package com.sankuai.shangou.qnh.orderapi.constant.store;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2020/3/2 下午1:31
 **/
public class LoginConstants {
    public static final String H5_LOGIN_COOKIE_PREFIX = "h5_c";
    public static final String H5_LOGIN_SESSION_PREFIX = "h5_s_";
    public static final String H5_URL_ENCRYPT_KEY = "h5_token_key";
    public static final String H5_LOGIN_HEADER_PARAM = "loginFlag";

    /**
     * h5界面cookie的有效时间是24小时
     */
    public static final int COOKIE_EXPIRE_SECONDS = 24 * 60 * 60;

    /**
     * 短信验证码发送间隔最少1分钟
     */
    public static final int SEND_VERIFY_CODE_INTERVAL_SEC = 1 * 60;
    public static final int SEND_VERIFY_CODE_INTERVAL_MS = 1 * 60 * 1000;

    /**
     * 验证码过期时间（秒）
     */
    public static final int VERIFY_CODE_EXPIRE_SEC = 5 * 60;

    /**
     * token 失效返回码
     */
    public static final int TOKEN_EXPIRED_CODE = 203;
}
