package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.common.enums.ChannelTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.junit.Assert;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2019/7/10
 * desc: 查询订单详情请求
 */
@TypeDoc(
        description = "查询订单详情请求"
)
@ApiModel("查询订单详情请求")
@Data
public class OrderDetailRequest {

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID")
    private Integer channelId;

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID")
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道订单号")
    @NotNull
    private String channelOrderId;

    public void validate(){
        Assert.assertEquals("unsupport channel:"+channelId,channelId.intValue(), ChannelTypeEnum.MEITUAN.getValue());
    }
}
