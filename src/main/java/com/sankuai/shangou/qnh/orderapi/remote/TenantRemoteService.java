package com.sankuai.shangou.qnh.orderapi.remote;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.linz.boot.util.Fun;
import com.meituan.linz.boot.util.JacksonUtils;
import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.meituan.shangou.saas.tenant.thrift.BoothThriftService;
import com.meituan.shangou.saas.tenant.thrift.ChannelManageThriftService;
import com.meituan.shangou.saas.tenant.thrift.ConfigThriftService;
import com.meituan.shangou.saas.tenant.thrift.EmployeeThriftService;
import com.meituan.shangou.saas.tenant.thrift.PoiRelationThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.Status;
import com.meituan.shangou.saas.tenant.thrift.common.enums.ConfigItemEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiRelationTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.TenantBusinessModeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.booth.BoothInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.booth.response.BoothListResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.channel.request.ChannelBatchRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.channel.response.ChannelDetailListResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.config.ConfigDto;
import com.meituan.shangou.saas.tenant.thrift.dto.config.request.ConfigQueryRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.config.request.TenantSwitchGetRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.config.response.TenantConfigResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.config.response.TenantSwitchGetResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.employee.response.EmployDepInfoResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.request.PoiRelationQueryRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiRelationSimpleMapResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.Channel;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.ChannelThriftResponse;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.ChannelThriftService;
import com.sankuai.meituan.shangou.empower.payment.thrift.constant.ResultCodeEnum;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.ChannelInfoBo;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.TenantBizModeBo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.BoothInfoVo;
import com.sankuai.shangou.qnh.orderapi.exception.store.CommonLogicException;
import com.sankuai.shangou.qnh.orderapi.exception.store.CommonRuntimeException;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import com.sankuai.shangou.qnh.orderapi.utils.pc.MccConfigUtil;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ResponseHandler;
import com.sankuai.shangou.qnh.orderapi.utils.pc.RpcInvoker;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.meituan.shangou.saas.tenant.thrift.common.enums.ConfigItemEnum.AUTO_RESTOCK_AFTER_CLOSURE;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/8 16:34
 * @Description:
 */
@Service
@Slf4j
@Rhino
public class TenantRemoteService {

    @Resource
    private PoiRelationThriftService poiRelationThriftService;

    @Resource
    private ChannelManageThriftService channelManageThriftService;

    @Autowired
    private ChannelThriftService.Iface channelThriftService;

    @Autowired
    private ConfigThriftService configThriftService;

    @Autowired
    private BoothThriftService boothThriftService;

    @Autowired
    private ChannelManageRemoteService channelManageRemoteService;

    @Resource(name = "employThriftService")
    private EmployeeThriftService employeeThriftService;

    @SuppressWarnings("SameParameterValue")
    private void nullResponseCheck(Object resp, String errorMsg) {
        if (resp == null) {
            throw new CommonLogicException(errorMsg + ", response is null");
        }
    }
    @SuppressWarnings("SameParameterValue")
    private void validateModuleStatus(Status status, String errorMsg) {
        if (status == null) {
            throw new CommonLogicException(errorMsg + ", status is null");
        }

        if (status.getCode() != ResultCode.SUCCESS.getCode()) {
            throw new CommonLogicException(MessageFormat.format("{0}, code = {1}, detail = {2}",
                    errorMsg, status.getCode(), status.getMessage()));
        }
    }

    @MethodLog(logRequest = true, logResponse = true)
    public List<Long> queryBoothListByStoreId(Long tenantId, Long storeId) {
        BoothListResponse response = boothThriftService.queryBoothListByPoiId(tenantId, storeId);
        List<Long> boothIdList = Lists.newArrayList();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(response.getBoothList())) {
            return boothIdList;
        }
        for (BoothInfoDto boothInfoDto : response.getBoothList()) {
            boothIdList.add(boothInfoDto.getBoothId());
        }
        return boothIdList;
    }

    public List<Channel> queryChannelIds(Long tenantId) {
        try {
            ChannelThriftResponse response = channelThriftService.getTenantChannels(tenantId);
            log.info("ChannelThriftService.getTenantChannels, tenantId:{}, response:{}", tenantId, response);

            return response.getChannelList();
        } catch (TException e) {
            log.error("ChannelThriftService.getTenantChannels, tenantId:{}", tenantId, e);
            throw new CommonRuntimeException(e);
        }
    }

    @MethodLog(logRequest = true, logResponse = true)
    private Map<Long, List<Long>> batchQueryRelationMapByPoiIds(PoiRelationQueryRequest request) {

        PoiRelationSimpleMapResponse resp = poiRelationThriftService.batchQueryRelationMapByPoiIds(request);
        nullResponseCheck(resp, "查询poi关联关系异常");
        validateModuleStatus(resp.getStatus(), "查询poi关联关系异常");
        return resp.getPoiRelationMap();
    }

    /**
     * 根据共享前置仓id映射门店id
     * @param tenantId
     * @param warehouseIds
     * @return
     */
    public Map<Long, List<Long>> mapPoiIdsByWarehouseId(Long tenantId, List<Long> warehouseIds) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(warehouseIds) || Objects.isNull(tenantId)) {
            return Maps.newHashMap();
        }
        PoiRelationQueryRequest request = new PoiRelationQueryRequest();
        request.setTenantId(tenantId);
        request.setPoiIdList(warehouseIds);
        request.setRelationType(PoiRelationTypeEnum.STORE_SHAREABLE_WAREHOUSE_RELATION.code());
        request.setReverseRelation(Boolean.TRUE);
        return batchQueryRelationMapByPoiIds(request);
    }

    /**
     * 批量查询渠道信息
     * TODO 此方法后续可替换为租户客户端中带缓存的方法
     *
     * @param channelIds 渠道ID集合
     */
    @MethodLog(logRequest = true, logResponse = true)
    public List<com.sankuai.shangou.qnh.orderapi.domain.bo.store.ChannelInfoBo> batchQueryChannelByChannelIds(List<Integer> channelIds) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(channelIds)) {
            return Collections.emptyList();
        }
        ChannelBatchRequest request = new ChannelBatchRequest();
        request.setChannelIds(channelIds);
        ChannelDetailListResponse response = channelManageThriftService.batchQueryChannelDetails(request);
        nullResponseCheck(response, "批量查询渠道信息异常");
        validateModuleStatus(response.getStatus(), "批量查询渠道信息异常");
        return Fun.map(response.getChannelList(), com.sankuai.shangou.qnh.orderapi.domain.bo.store.ChannelInfoBo::fromChannelInfoDto);
    }

    /**
     * 查询渠道名
     */
    @com.meituan.reco.pickselect.common.methodlog.MethodLog(logRequest = true, logResponse = true)
    public String queryChannelNameByChannelId(Integer channelId) {
        List<com.sankuai.shangou.qnh.orderapi.domain.bo.store.ChannelInfoBo> channelInfoBoList = batchQueryChannelByChannelIds(Collections.singletonList(channelId));
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(channelInfoBoList)) {
            return channelInfoBoList.stream()
                    .filter(bo -> Objects.equals(bo.getChannelId(), channelId))
                    .findAny()
                    .map(com.sankuai.shangou.qnh.orderapi.domain.bo.store.ChannelInfoBo::getChannelName)
                    .orElse(null);
        }
        return null;
    }

    /**
     * 查询租户开通的渠道
     *
     * @param tenantId
     * @return
     */
    public List<ChannelInfoBo> queryChannels(Long tenantId) {

        ChannelThriftResponse response = RpcInvoker.invoke(() -> channelThriftService.getTenantChannels(tenantId));

        ResponseHandler.checkResponseAndStatus(response, ChannelThriftResponse::getCode, ChannelThriftResponse::getMsg);
        List<Integer> channelIds = ConverterUtils.convertList(response.getChannelList(), Channel::getId);
        return channelManageRemoteService.queryChannelDetails(channelIds);
    }

    /**
     * 查询租户业务模式、异常降级为未知模式
     *
     * @param tenantId 租户id
     * @return 业务模式
     */
    public TenantBusinessModeEnum getTenantBizMode(Long tenantId) {
        try {
            if (tenantId == null) {
                return null;
            }
            ConfigQueryRequest configQueryRequest = new ConfigQueryRequest();
            configQueryRequest.setConfigId(ConfigItemEnum.TENANT_BIZ_MODE.getKey());
            configQueryRequest.setTenantId(tenantId);
            configQueryRequest.setSubjectId(tenantId);
            TenantConfigResponse configResponse = configThriftService.queryTenantConfig(configQueryRequest);
            Status status = Optional.ofNullable(configResponse).map(TenantConfigResponse::getStatus).orElse(null);
            if (status == null || status.getCode() != com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum.SUCCESS.getValue()) {
                log.error("search tenant config info error,tenantId:{},msg:{}", tenantId,
                        Optional.ofNullable(status).map(Status::getMessage).orElse(null));
                return TenantBusinessModeEnum.UNKNOWN;
            }
            String config = Optional.ofNullable(configResponse.getConfig())
                    .map(ConfigDto::getConfigContent)
                    .orElse(null);
            if (StringUtils.isEmpty(config)) {
                return TenantBusinessModeEnum.UNKNOWN;
            }
            TenantBizModeBo tenantBizModeBo = JacksonUtils.parse(configResponse.getConfig().getConfigContent(), new TypeReference<TenantBizModeBo>() {});
            if (tenantBizModeBo == null) {
                return TenantBusinessModeEnum.UNKNOWN;
            }
            return StringUtils.isNotEmpty(tenantBizModeBo.getBizMode()) ? TenantBusinessModeEnum.codeOf(tenantBizModeBo.getBizMode()) : TenantBusinessModeEnum.UNKNOWN;
        }
        catch (Exception e) {
            log.error("search tenant config info exception,tenantId:{}, stack:", tenantId, e);
            return TenantBusinessModeEnum.UNKNOWN;
        }
    }

    /**
     * 查询租户业务模式、异常降级为未知模式
     *
     * @param tenantId 租户id
     * @return 业务模式
     */
    public Boolean getTenantResumeInfiniteStock(Long tenantId) {
        try {
            boolean isSupport = MccConfigUtil.checkSupportUpdateStockByConfigTenants(tenantId);
            if (!isSupport) {
                return Boolean.FALSE;
            }
            ConfigQueryRequest configQueryRequest = new ConfigQueryRequest();
            configQueryRequest.setConfigId(AUTO_RESTOCK_AFTER_CLOSURE.getKey());
            configQueryRequest.setTenantId(tenantId);
            configQueryRequest.setSubjectId(tenantId);
            TenantConfigResponse configResponse = configThriftService.queryTenantConfig(configQueryRequest);
            Status status = Optional.ofNullable(configResponse).map(TenantConfigResponse::getStatus).orElse(null);
            if (status == null || status.getCode() != com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum.SUCCESS.getValue()) {
                log.error("search tenant config info error,tenantId:{},msg:{}", tenantId,
                        Optional.ofNullable(status).map(Status::getMessage).orElse(null));
                return Boolean.FALSE;
            }
            if (Objects.isNull(configResponse.getConfig()) || StringUtils.isEmpty(configResponse.getConfig().getConfigContent())) {
                return Boolean.FALSE;
            }
            return AUTO_RESTOCK_AFTER_CLOSURE.isMainConfigYesStr(configResponse.getConfig().getConfigContent());
        }
        catch (Exception e) {
            log.error("search tenant config info exception,tenantId:{}, stack:", tenantId, e);
        }
        return Boolean.FALSE;
    }


    public List<BoothInfoVo> queryBoothInfoByBoothIds(long tenantId, List<Long> boothIds){

        if (CollectionUtils.isEmpty(boothIds)){
            return Lists.newArrayList();
        }
        List<BoothInfoDto> boothInfoDtos = new ArrayList<>();
        try {
            BoothListResponse boothListResponse = boothThriftService.queryBoothListByBoothIds(tenantId, boothIds);
            if (null != boothListResponse && ResultCodeEnum.SUCCESS.getValue() == boothListResponse.getStatus().getCode()){
                boothInfoDtos = boothListResponse.getBoothList();
            }
        } catch (Exception e) {
            log.error("TenantClient boothThriftService.queryBoothListByBoothIds error", e);
            throw new IllegalStateException("TenantClient boothThriftService.queryBoothListByBoothIds error", e);
        }

        List<BoothInfoVo> boothInfoVos = new ArrayList<>();
        boothInfoDtos.forEach(boothInfoDto -> {
            BoothInfoVo boothInfoVo = new BoothInfoVo().build(boothInfoDto);
            boothInfoVos.add(boothInfoVo);

        });
        return boothInfoVos;

    }

    @MethodLog(logRequest = true, logResponse = true)
    public Map<String, String> getTenantSwitch(Long tenantId, List<String> switchKeys) {
        TenantSwitchGetRequest request = new TenantSwitchGetRequest();
        request.setTenantId(tenantId);
        request.setSwitchKey(switchKeys);
        TenantSwitchGetResponse response = RpcInvoker.invoke(() -> configThriftService.getTenantSwitch(request));
        ResponseHandler.checkResponseAndStatus(response, r -> r.getStatus().getCode(), r -> r.getStatus().getMessage());

        return response.getSwitchValue();
    }

    /**
     * 根据员工id获取员工姓名
     *
     * @param tenantId   租户id
     * @param employeeId 员工id
     * @return
     */
    @Degrade(rhinoKey = "TenantRemoteService.getEmployeeName",
            fallBackMethod = "getEmployeeNameFallback", isDegradeOnException = true,
            timeoutInMilliseconds = 1200)
    public String getEmployeeName(Long tenantId, Long employeeId) {
        log.info("getEmployeeName employeeThriftService.queryEmployeeById, tenantId:{}, employeeId:{}", tenantId, employeeId);
        EmployDepInfoResponse response = employeeThriftService.queryEmployeeById(employeeId, tenantId);
        log.info("getEmployeeName employeeThriftService.queryEmployeeById, response:{}", response);
        if (response != null && response.getEmployeeInfo() != null) {
            return response.getEmployeeInfo().getEmployeeName();
        } else {
            log.error("getEmployeeName employeeThriftService.queryEmployeeById return no employeeInfo, tenantId:{}, employeeId:{}", tenantId, employeeId);
            return getEmployeeNameFallback(tenantId, employeeId);
        }
    }

    /**
     * 根据员工id获取员工姓名降级方法
     *
     * @param tenantId
     * @param employeeId
     * @return
     */
    public String getEmployeeNameFallback(Long tenantId, Long employeeId) {
        log.error("执行getEmployeeName降级方法");
        return "";
    }

    public Map<String, String> queryTenantSwitch(Long tenantId, List<String> switchKeyList) {

        try {
            if (org.apache.commons.collections.CollectionUtils.isEmpty(switchKeyList)) {
                return Collections.emptyMap();
            }

            TenantSwitchGetRequest request = new TenantSwitchGetRequest();
            request.setTenantId(tenantId);
            request.setSwitchKey(switchKeyList);

            TenantSwitchGetResponse response = configThriftService.getTenantSwitch(request);
            log.info("查询租户开关配置, request:{}, response:{}", request, response);

            return response.getSwitchValue();
        } catch (Exception e) {
            log.error("查询租户开关配置错误, tenantId:{}, switchKeyList:{}", tenantId, switchKeyList, e);
            throw new com.sankuai.shangou.qnh.orderapi.exception.app.CommonRuntimeException(e);
        }
    }

    /**
     * 根据员工id获取员工信息
     *
     * @param tenantId   租户id
     * @param employeeId 员工id
     * @return
     */
    @Degrade(rhinoKey = "TenantRemoteService.getEmployeeInfo",
            fallBackMethod = "getEmployeeInfoFallback", isDegradeOnException = true,
            timeoutInMilliseconds = 1200)
    public EmployeeBaseInfo getEmployeeInfo(Long tenantId, Long employeeId) {
        log.info("getEmployeeInfo employeeThriftService.queryEmployeeById, tenantId:{}, employeeId:{}", tenantId, employeeId);
        EmployDepInfoResponse response = employeeThriftService.queryEmployeeById(employeeId, tenantId);
        log.info("getEmployeeInfo employeeThriftService.queryEmployeeById, response:{}", response);
        if (response != null && response.getEmployeeInfo() != null) {
            return new EmployeeBaseInfo(response.getEmployeeInfo().getEmployeeName(), response.getEmployeeInfo().getEmployeePhone());
        } else {
            log.error("getEmployeeInfo employeeThriftService.queryEmployeeById return no employeeInfo, tenantId:{}, employeeId:{}", tenantId, employeeId);
            return getEmployeeInfoFallback(tenantId, employeeId);
        }
    }

    /**
     * 根据员工id获取员工信息降级方法
     *
     * @param tenantId
     * @param employeeId
     * @return
     */
    public EmployeeBaseInfo getEmployeeInfoFallback(Long tenantId, Long employeeId) {
        log.error("执行getEmployeeName降级方法");
        return new EmployeeBaseInfo("", "");
    }



    @Data
    static class TenantPriceConfig {
        private Integer channel;
        private Integer costStyle;
    }


    public static class EmployeeBaseInfo {
        private String employeeName;

        private String employeePhone;

        public EmployeeBaseInfo(String employeeName, String employeePhone) {
            this.employeeName = employeeName;
            this.employeePhone = employeePhone;
        }

        public String getEmployeeName() {
            return employeeName;
        }

        public void setEmployeeName(String employeeName) {
            this.employeeName = employeeName;
        }

        public String getEmployeePhone() {
            return employeePhone;
        }

        public void setEmployeePhone(String employeePhone) {
            this.employeePhone = employeePhone;
        }
    }


}