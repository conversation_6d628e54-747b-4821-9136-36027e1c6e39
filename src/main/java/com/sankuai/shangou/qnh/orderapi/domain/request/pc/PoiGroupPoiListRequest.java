package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.TypeDoc;

import com.sankuai.shangou.qnh.orderapi.domain.request.pc.PageRequest;
import lombok.Data;

/**
 * @Author: wa<PERSON><PERSON><PERSON>
 * @Date: 2022/07/27
 * @Description:
 */
@TypeDoc(
        name = "门店分组门店列表请求对象",
        description = "门店分组门店列表请求对象"
)
@Data
public class PoiGroupPoiListRequest extends PageRequest {

    private Long poiId;

    private String poiName;

    private Long poiGroupId;
}
