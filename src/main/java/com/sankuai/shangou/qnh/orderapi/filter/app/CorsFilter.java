package com.sankuai.shangou.qnh.orderapi.filter.app;

import com.sankuai.security.sdk.SecSdk;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @author: <EMAIL>
 * @date: 2017/11/20
 * @time: 下午4:15
 */
@Slf4j
public class CorsFilter implements Filter {

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        log.info("init CorsFilter");
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        String[] allowedDomain = {"*.sankuai.com", "*.meituan.com", "localhost"};
        HttpServletRequest httpRequest = (HttpServletRequest) servletRequest;
        String origin = httpRequest.getHeader("Origin");
        HttpServletResponse httpResponse = (HttpServletResponse) servletResponse;
        if (SecSdk.securityCORS(origin, allowedDomain)) {
            httpResponse.addHeader("Access-Control-Allow-Origin", origin);
            httpResponse.addHeader("Access-Control-Allow-Credentials", "true");
            httpResponse.addHeader("Access-Control-Allow-Methods", "POST,GET,PUT,PATCH,DELETE,OPTIONS");
            httpResponse.addHeader("Access-Control-Allow-Headers",
                    "Origin, X-Requested-With, Content-Type, Accept, X-Token, access-token, Content-disposition," +
                            "WG-App-Version, WG-Device-Id, WG-Network-Type, WG-Vendor, WG-OS-Type, WG-OS-Version, " +
                            "WG-Device-Model, WG-CPU, WG-Sid, WG-App-Id, WG-Token,ssoToken");
            httpResponse.addHeader("Access-Control-Max-Age", "1728000");
        }
        if (httpRequest.getMethod().equals("OPTIONS")) {
            httpResponse.getWriter().println("ok");
            return;
        }
        filterChain.doFilter(httpRequest, httpResponse);
    }

    @Override
    public void destroy() {
        log.info("destroy CorsFilter");
    }

}
