package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.model.Order;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@TypeDoc(description = "评价溯源查询订单列表返回体")
@ApiModel("评价溯源查询订单列表返回体")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderListByCommentVO {

    @FieldDoc(description = "渠道订单号")
    private String channelOrderId;

    @FieldDoc(description = "渠道ID")
    private Integer channelId;

    @FieldDoc(description = "订单完成时间")
    private Long completeTime;

    @FieldDoc(description = "订单商品列表")
    private List<CommentOrderItemVO> orderItemList;

    public static List<OrderListByCommentVO> buildByOrderList(List<Order> orderList, List<String> matchChannelOrderIds) {
        List<OrderListByCommentVO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(orderList)) {
            return result;
        }
        for (Order order : orderList) {
            DynamicOrderBizType orderBizType = DynamicOrderBizType.findOf(order.getOrderBase().getOrderBizType());
            OrderListByCommentVO orderListByCommentVO = OrderListByCommentVO.builder()
                    .channelOrderId(order.getOrderBase().getViewOrderId())
                    // 编译NPE提示问题解决，应该不会存在该问题
                    .channelId(Objects.nonNull(orderBizType) ? orderBizType.getChannelId() : -1)
                    .orderItemList(CommentOrderItemVO.buildByOrderItemList(order.getOrderItemList()))
                    .completeTime(order.getOrderBase().getCompleteTime()).build();
            result.add(orderListByCommentVO);
        }
        // 按照入参列表重新进行排序
        result.sort((o1, o2) -> {
            int index1 = matchChannelOrderIds.indexOf(o1.getChannelOrderId());
            int index2 = matchChannelOrderIds.indexOf(o2.getChannelOrderId());
            return Integer.compare(index1, index2);
        });
        return result;
    }
}
