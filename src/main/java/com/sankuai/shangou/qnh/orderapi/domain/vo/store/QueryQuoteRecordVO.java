package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TypeDoc(
        description = "提价记录信息"
)
@Data
@ApiModel("提价记录信息")
public class QueryQuoteRecordVO {

    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户ID", required = true)
    private Long tenantId;

    @FieldDoc(
            description = "提价记录编码ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "提价记录编码ID", required = true)
    private Long quoteId;

    @FieldDoc(
            description = "商品ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品ID", required = true)
    private String skuId;

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称", required = true)
    private String skuName;

    @FieldDoc(
            description = "商品图片地址", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品图片地址", required = true)
    private String picUrl;

    @FieldDoc(
            description = "商品规格", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品规格", required = true)
    private String skuSpec;

    @FieldDoc(
            description = "商品重量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品重量", required = true)
    private Integer weight;


    @FieldDoc(
            description = "商品类型", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品类型", required = true)
    private Integer weightType;

    @FieldDoc(
            description = "本次提价", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "本次提价", required = true)
    private String quotePrice;

    @FieldDoc(
            description = "本次提价市斤转换", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "本次提价市斤转换", required = true)
    private String quotePricePer500g;

    @FieldDoc(
            description = "提价时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "提价时间", required = true)
    private String quoteTime;

    @FieldDoc(
            description = "当前提价记录审批状态", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "当前提价记录审批状态", required = true)
    private int reviewStatus;

    @FieldDoc(
            description = "审批人ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "审批人ID", required = true)
    private Long reviewerId;

    @FieldDoc(
            description = "审批人名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "审批人名称", required = true)
    private String reviewerName;

    @FieldDoc(
            description = "审批时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "审批时间", required = true)
    private String reviewTime;

    @FieldDoc(
            description = "审核描述", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "审核描述", required = true)
    private String reviewDescription;
}
