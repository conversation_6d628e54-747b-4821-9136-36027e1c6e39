package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/6/12
 * desc:
 */
@TypeDoc(
        description = "商品品牌信息"
)
@Data
@ApiModel("商品品牌信息")
public class SkuBrandInfoVO {

    @FieldDoc(
            description = "品牌code", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "品牌code", required = true)
    private String brandCode;

    @FieldDoc(
            description = "父品牌code", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "父品牌code", required = true)
    private String parentBrandCode;

    @FieldDoc(
            description = "节点层级路径", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "节点层级路径", required = true)
    private String codePath;

    @FieldDoc(
            description = "中英文名称全路径", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "中英文名称全路径", required = true)
    private String namePath;

    @FieldDoc(
            description = "英文名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "英文名称", required = true)
    private String enName;

    @FieldDoc(
            description = "中文名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "中文名称", required = true)
    private String zhName;

    @FieldDoc(
            description = "品牌类型 1-国内品牌 2-国际品牌", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "品牌类型 1-国内品牌 2-国际品牌", required = true)
    private Integer brandType;

    @FieldDoc(
            description = "品牌状态 1-启用 2-停用", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "品牌状态 1-启用 2-停用", required = true)
    private Integer brandStatus;

    @FieldDoc(
            description = "品牌所处级别", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "品牌所处级别", required = true)
    private Integer level;

    @FieldDoc(
            description = "品牌logo图片地址", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "品牌logo图片地址", required = true)
    private String logoPic;
}
