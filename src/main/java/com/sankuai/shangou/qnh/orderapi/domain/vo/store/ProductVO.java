package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.OrderFuseFinanceDetailBO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.SubProductVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.ChannelLabelVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.PropertiesViewVO;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.MapUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/7/10
 * desc: 商品信息
 */
@TypeDoc(
        description = "商品信息"
)
@ApiModel("商品信息")
@Data
public class ProductVO {

    @FieldDoc(
            description = "skuId", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "skuId")
    private String skuId;

    @FieldDoc(
            description = "upc码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "upc码", required = true)
    private String upcCode;

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称", required = true)
    private String skuName;

    @FieldDoc(
            description = "商品图片URL", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品图片URL", required = true)
    private String picUrl;

    @FieldDoc(
            description = "多张商品图片URL", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "多张商品图片URL", required = true)
    private List<String> multiPicUrl;

    @FieldDoc(
            description = "规格", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "规格", required = true)
    private String specification;

    @FieldDoc(
            description = "售卖单位", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "售卖单位", required = true)
    private String sellUnit;

    @FieldDoc(
            description = "原总价  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "原总价  单位:分", required = true)
    private Integer originalTotalPrice;

    @FieldDoc(
            description = "实付金额  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "实付金额  单位:分", required = true)
    private Integer totalPayAmount;

    @FieldDoc(
            description = "优惠金额  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "优惠金额  单位:分", required = true)
    private Integer totalDiscountAmount;

    @FieldDoc(
            description = "单价  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "单价  单位:分", required = true)
    private Integer unitPrice;

    @FieldDoc(
            description = "购买数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "购买数量", required = true)
    private Integer count;

    @FieldDoc(
            description = "是否退货 0-否 1-是", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "是否退货 0-否 1-是", required = true)
    private Integer isRefund;

    @FieldDoc(
            description = "申请取消数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "申请取消数量", required = true)
    private Integer refundCount;

    @FieldDoc(
            description = "摊位名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "摊位名称", required = true)
    private String boothName;

    @FieldDoc(
            description = "线下售卖价格  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "线下售卖价格  单位:分", required = true)
    private Integer offlinePrice;

    @FieldDoc(
            description = "摊位结算金额  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "摊位结算金额  单位:分", required = true)
    private Integer stallSettleAmt;

    @FieldDoc(
            description = "实际拣货数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "实际拣货数量", required = true)
    private Integer realQuantity;

    @FieldDoc(
            description = "商家在渠道的skuId", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商家在渠道的skuId")
    private String customerSkuId;


    @FieldDoc(
            description = "商品标签信息列表(1履约标签、2拣货标签)、客户端将type用于修改样式、新标签需要使用tagInfos", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "商品标签信息列表(1履约标签、2拣货标签)、客户端将type用于修改样式、新标签需要使用tagInfos")
    private List<TagInfoVO> tagInfoList;

    @FieldDoc(
            description = "商品标签信息列表(除1履约标签、2拣货标签之外的其它新标签都用此对象)  3商品属性标签", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "商品标签信息列表(除1履约标签、2拣货标签之外的其它新标签都用此对象)  3商品属性标签")
    private List<TagInfoVO> tagInfos;

    @FieldDoc(
            description = "换货商品详情", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "换货商品详情")
    private List<ExchangeProductVO> exchangeOrderDetailVOList;

    @FieldDoc(
            description = "换货数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "换货数量")
    private Integer exchangeCount = 0;

    @FieldDoc(
            description = "换货商品数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "换货商品数量")
    private Integer exchangeGoodsCount = 0;

    @FieldDoc(
            description = "商品对应的orderItemId", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品对应的orderItemId")
    private Long orderItemId;

    @FieldDoc(
            description = "下单时的商品数量，不会因为缺货/退货/换货而改变", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "下单时的商品数量，不会因为缺货/退货/换货而改变")
    private Integer orderQuantity;

    @FieldDoc(
            description = "下单时的商品数量，不会因为缺货/退货/换货而改变", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "批次信息")
    private List<ProductBatchInfoVO> batchInfo;

    @FieldDoc(
            description = "erp item 编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "erp item 编码")
    public String erpItemCode;

    @FieldDoc(
            description = "商品原单价", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品原单价")
    private Integer originalPrice;

    @FieldDoc(
            description = "当前单价", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "当前单价")
    private Integer currentPrice;

    @FieldDoc(
            description = "组合品子商品", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "组合品子商品")
    private List<SubProductVo> subProductVoList;

    @FieldDoc(
            description = "是否包含缺货商品", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否包含缺货商品")
    private Boolean isIncludeStockLackGoods;

    @FieldDoc(
            description = "按配置解析后的属性", requiredness = Requiredness.OPTIONAL
    )
    private List<PropertiesViewVO> parsedProperties;

    @FieldDoc(
            description = "赠品类型 0-平台赠品 1-自定义拣货赠品", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "赠品类型")
    private Integer giftType;

    @FieldDoc(
            description = "货架码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "货架码")
    private String goodsCode;

    @FieldDoc(
            description = "商品标签信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "商品标签信息")
    private List<ChannelLabelVO> channelLabelList;

    @FieldDoc(
            description = "商品标签附加信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "商品标签附加信息")
    private String labelSubDesc;

    @FieldDoc(
            description = "赠品对应主品sku", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "赠品对应主品sku")
    private String belongSkuId;


    public void mergeFinanceInfo(OrderFuseFinanceDetailBO.ItemFinanceInfo itemFinanceInfo) {
        if (itemFinanceInfo == null) {
            return;
        }
        if(itemFinanceInfo.getComposeItemFinanceInfos() != null){
            Map<Long, OrderFuseFinanceDetailBO.ComposeItemFinanceInfo> financeInfoMap = itemFinanceInfo.getComposeItemFinanceInfos().stream().collect(Collectors.toMap(OrderFuseFinanceDetailBO.ComposeItemFinanceInfo::getServiceId, item -> item));
            subProductVoList.forEach(item -> {
                if (MapUtils.isNotEmpty(financeInfoMap) && Objects.nonNull(financeInfoMap.get(item.getServiceId()))) {
                    OrderFuseFinanceDetailBO.ComposeItemFinanceInfo composeItemFinanceInfo = financeInfoMap.get(item.getServiceId());
                    item.setOriginPrice(ConverterUtils.formatMoney(composeItemFinanceInfo.getOriginPrice()));
                    item.setSalePrice(ConverterUtils.formatMoney(composeItemFinanceInfo.getSalePrice()));
                    item.setTotalPrice(ConverterUtils.formatMoney(composeItemFinanceInfo.getItemSaleAmt()));
                    item.setPlatItemDiscount(ConverterUtils.formatMoney(composeItemFinanceInfo.getPlatItemPromotion()));
                    item.setPlatDiscount(ConverterUtils.formatMoney(composeItemFinanceInfo.getPlatPromotion()));
                    item.setPoiDiscount(ConverterUtils.formatMoney(composeItemFinanceInfo.getPoiPromotion()));
                    item.setPoiItemDiscount(ConverterUtils.formatMoney(composeItemFinanceInfo.getPoiItemPromotion()));
                    item.setBillPrice(ConverterUtils.formatMoney(composeItemFinanceInfo.getBillPrice()));
                    item.setBillValue(ConverterUtils.formatMoney(composeItemFinanceInfo.getBillAmt()));
                    item.setServiceId(composeItemFinanceInfo.getServiceId() != null ? composeItemFinanceInfo.getServiceId() : 0);
                    item.setOriginalTotalPrice(ConverterUtils.formatMoney(composeItemFinanceInfo.getOriginalTotalPrice()));
                    item.setTotalPayAmount(ConverterUtils.formatMoney(composeItemFinanceInfo.getTotalPayAmount()));
                }
            });
        }

    }

}
