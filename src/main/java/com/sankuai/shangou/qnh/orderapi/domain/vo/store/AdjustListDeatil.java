package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2018/9/25
 * @email jianglilin02@meituan
 */
@Data
@ApiModel("查询调价单列表响应")
public class AdjustListDeatil {
    @ApiModelProperty(value = "调价单单号", required = true)
    @NotEmpty
    private String priceOrderNo;

    @ApiModelProperty(value = "操作人姓名", required = true)
    @NotEmpty
    private String operatorName;

    @ApiModelProperty(value = "调价操作时间", required = true)
    @NotNull
    private String operatorTime;

    @ApiModelProperty(value = "调价单包含调价商品数量", required = true)
    @NotNull
    private Integer skuCount;
}
