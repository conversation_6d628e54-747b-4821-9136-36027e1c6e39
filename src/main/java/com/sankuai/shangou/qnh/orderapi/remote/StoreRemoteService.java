package com.sankuai.shangou.qnh.orderapi.remote;

import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Maps;
import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.meituan.shangou.saas.tenant.thrift.PoiThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.response.PoiMapResponse;
import com.sankuai.shangou.qnh.orderapi.exception.store.CommonRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
@Rhino
@Slf4j
public class StoreRemoteService {

    @Resource
    private PoiThriftService poiThriftService;

    /* Rhino需要pubic */
    @SuppressWarnings("WeakerAccess")
    public Map<Long, String> batchQueryStoreNameByStoreIds(Long tenantId, List<Long> storeIds) {
        if (tenantId == null) {
            throw new CommonRuntimeException("租户id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (CollectionUtils.isEmpty(storeIds)) {
            throw new CommonRuntimeException("门店id不能为空", ResultCode.CHECK_PARAM_ERR);
        }

        PoiMapResponse response = poiThriftService.queryTenantPoiInfoMapByPoiIds(storeIds, tenantId);
        log.info("PoiThriftService.queryTenantPoiInfoMapByPoiIds, storeIds:{}, tenantId:{}, response:{}",
                storeIds, tenantId, response);

        Map<Long, String> map = Maps.newHashMap();
        if (MapUtils.isNotEmpty(response.poiInfoMap)) {
            for (Map.Entry<Long, PoiInfoDto> entry : response.poiInfoMap.entrySet()) {
                map.put(entry.getKey(), entry.getValue().getPoiName());
            }
        }
        return map;
    }

}
