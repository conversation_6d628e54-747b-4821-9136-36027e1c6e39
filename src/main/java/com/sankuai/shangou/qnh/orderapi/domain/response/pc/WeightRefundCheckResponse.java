package com.sankuai.shangou.qnh.orderapi.domain.response.pc;

/**
 * <AUTHOR>
 * @since 2023/3/2
 **/

import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.bo.ReturnDataPoiBo;
import com.sankuai.shangou.qnh.orderapi.domain.result.ReturnDataSecurity;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.OrderItemWeightRefundCheckVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * @Author: <EMAIL>
 * @Date: 2020-03-30 11:34
 * @Description:
 */
@TypeDoc(
        description = "克重退款页面检查退款请求响应"
)
@ApiModel("克重退款页面检查退款请求响应")
@Data
public class WeightRefundCheckResponse implements ReturnDataSecurity {
    @FieldDoc(
            description = "退款商品列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "退款商品列表", required = true)
    private List<OrderItemWeightRefundCheckVO> weightRefundCheckVOList;

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店ID")
    private Long poiId;

    @FieldDoc(
            description = "转单门店ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "转单门店ID")
    private Long dispatchShopId;

    @Override
    public List<ReturnDataPoiBo> fetchReturnDataPoiBoList() {
        if (Objects.nonNull(this.poiId)) {
            ReturnDataPoiBo poiBo = ReturnDataPoiBo.builder().poiId(this.poiId).dispatchShopId(this.dispatchShopId)
                    .build();
            return Lists.newArrayList(poiBo);
        }
        return null;
    }
}
