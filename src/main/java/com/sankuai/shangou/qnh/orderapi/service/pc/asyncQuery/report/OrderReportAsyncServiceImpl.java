package com.sankuai.shangou.qnh.orderapi.service.pc.asyncQuery.report;

import com.meituan.shangou.saas.order.platform.client.dto.model.OrderReport;
import com.meituan.shangou.saas.order.platform.client.dto.request.OrderReportRequest;
import com.meituan.shangou.saas.order.platform.client.dto.response.OrderReportResponse;
import com.meituan.shangou.saas.order.platform.client.enums.StatusCodeEnum;
import com.meituan.shangou.saas.order.platform.client.service.report.OrderFulfillmentReportThriftService;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.OrderFulfillmentReportReq;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.OrderFulfillmentReportQueryResp;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.OrderFulfillmentReportVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class OrderReportAsyncServiceImpl implements OrderReportAsyncService {

    @Resource
    private OrderFulfillmentReportThriftService orderFulfillmentReportThriftService;

    @Override
    public OrderFulfillmentReportQueryResp asyncQueryReport(OrderFulfillmentReportReq request, List<Long> poiIds, List<Long> warehouseIds) {
        OrderFulfillmentReportQueryResp resp = new OrderFulfillmentReportQueryResp();
        log.info("OrderReportAsyncServiceImpl asyncQueryReport request:{}, poiIds:{}, warehouseIds:{}", request, poiIds, warehouseIds);
        OrderReportRequest orderReportRequest = new OrderReportRequest();
        orderReportRequest.setTenantId(request.getTenantId());
        orderReportRequest.setShopIdList(poiIds);
        orderReportRequest.setWarehouseIdList(warehouseIds);
        orderReportRequest.setFilterType(request.getFilterType());
        try {
            OrderReportResponse orderReportResponse = orderFulfillmentReportThriftService.queryOrderReport(orderReportRequest);
            if (orderReportResponse.getStatus().getCode() != StatusCodeEnum.SUCCESS.getValue()) {
                log.error("OrderReportAsyncServiceImpl asyncQueryReport 查询失败：param {}，result:{}", orderReportRequest, orderReportResponse);
                return resp;
            }
            if (CollectionUtils.isEmpty(orderReportResponse.getOrderReportList())) {
                resp.setOrderFulfillmentDetailReportList(Lists.newArrayList());
                return resp;
            }
            List<OrderFulfillmentReportVO> reportVOList = orderReportResponse.getOrderReportList().stream().map(orderReport -> toBuild(orderReport)).collect(Collectors.toList());
            resp.setOrderFulfillmentDetailReportList(reportVOList);
        } catch (TException e) {
            log.error("OrderReportAsyncServiceImpl asyncQueryReport error", e);
            throw new RuntimeException(e);
        }

        return resp;

    }

    private OrderFulfillmentReportVO toBuild(OrderReport orderReport) {
        OrderFulfillmentReportVO reportVO = new OrderFulfillmentReportVO();
        reportVO.setShopId(orderReport.getShopId());
        reportVO.setShopName(orderReport.getShopName());
        reportVO.setWarehouseId(orderReport.getWarehouseId());
        reportVO.setWarehouseName(orderReport.getWarehouseName());
        reportVO.setOutShopId(orderReport.getOutShopId());
        reportVO.setOrderCount(orderReport.getOrderCount());
        reportVO.setUnPickOrderCount(orderReport.getUnPickOrderCount());
        reportVO.setDealingOrderCount(orderReport.getDealingOrderCount());
        reportVO.setCompleteOrderCount(orderReport.getCompleteOrderCount());
        reportVO.setCancelOrderCount(orderReport.getCancelOrderCount());
        reportVO.setDelivering(orderReport.getDeliveringOrderCount());
        reportVO.setPickCompleteCount(orderReport.getPickCompleteCount());
        reportVO.setUnSelfPickCount(orderReport.getUnSelfPickCount());
        reportVO.setUndelivered(orderReport.getUnSelfPickCount() + orderReport.getDeliveringOrderCount() + orderReport.getPickCompleteCount());
        return reportVO;
    }
}
