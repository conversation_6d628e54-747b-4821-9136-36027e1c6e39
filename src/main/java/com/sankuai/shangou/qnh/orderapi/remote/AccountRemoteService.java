package com.sankuai.shangou.qnh.orderapi.remote;

import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.service.AccountThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.service.AuthThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.*;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.request.QueryAccountSessionReq;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ResponseHandler;
import com.sankuai.shangou.qnh.orderapi.utils.pc.RpcInvoker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2018/10/24 权限外部服务
 **/
@Service
@Slf4j
public class AccountRemoteService {

    @Resource
    private AuthThriftService.Iface authThriftService;

    @Resource
    private AccountThriftService.Iface authAccountThriftService;

    /**
     * 查询账号详情不包含权限
     */
    @MethodLog(logRequest = true, logResponse = true, logger = "thrift")
    public AccountInfoVo querySimpleAccountInfoById(Long accountId) {
        QuerySimpleAccountInfoRequest querySimpleAccountInfoRequest = new QuerySimpleAccountInfoRequest();
        querySimpleAccountInfoRequest.setAccountIds(Arrays.asList(accountId));
        QuerySimpleAccountInfoListResponse response = RpcInvoker.invokeReturn(
                () -> authThriftService.querySimpleAccountInfoList(querySimpleAccountInfoRequest));
        ResponseHandler.handleResult(response.getResult());
        if(CollectionUtils.isEmpty(response.getAccountInfoList())) {
            throw new BizException("租户账号不存在");
        }
        return response.getAccountInfoList().get(0);
    }

    @MethodLog(logRequest = true, logResponse = true, logger = "thrift")
    public AccountSessionVO queryAccountSessionInfo(QueryAccountSessionReq queryAccountSessionReq) {
        QueryAccountSessionResponse response = RpcInvoker.invokeReturn(
            () -> authAccountThriftService.queryAccountSessionInfo(queryAccountSessionReq));
        if (response.getResult().getCode() != ResultCodeEnum.SUCCESS.getValue()) {
            log.info("根据token 查询账号信息 失败 request:{},msg:{}", queryAccountSessionReq, response.getResult().getMsg());
            return null;
        }
        return response.getAccountSessionVO();
    }

    /**
     * 根据staffId查询账号详情
     */
    @MethodLog(logRequest = true, logResponse = true, logger = "thrift")
    public AccountInfoVo queryAccountInfoByStaffId(Long staffId) {
        QueryAccountDetailByTenantIdAndStaffIdRequest request = new QueryAccountDetailByTenantIdAndStaffIdRequest();
        request.setTenantId(ContextHolder.currentUserTenantId());
        request.setStaffId(staffId);
        QueryAccountInfoResponse response = RpcInvoker.invokeReturn(
                () -> authThriftService.queryAccountDetailByTenantIdAndStaffId(request));
        ResponseHandler.handleResult(response.getResult());
        return response.getAccountInfo();
    }

}
