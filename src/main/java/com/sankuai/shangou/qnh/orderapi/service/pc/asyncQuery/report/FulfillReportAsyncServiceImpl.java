package com.sankuai.shangou.qnh.orderapi.service.pc.asyncQuery.report;

import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.meituan.shangou.saas.order.platform.client.dto.model.FulfillReport;
import com.meituan.shangou.saas.order.platform.client.dto.request.OrderReportRequest;
import com.meituan.shangou.saas.order.platform.client.dto.response.FulfillReportResponse;
import com.meituan.shangou.saas.order.platform.client.enums.StatusCodeEnum;
import com.meituan.shangou.saas.order.platform.client.service.report.OrderFulfillmentReportThriftService;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.OrderFulfillmentReportReq;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.OrderFulfillmentReportQueryResp;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.OrderFulfillmentReportVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class FulfillReportAsyncServiceImpl implements OrderReportAsyncService {

    @Resource
    private OrderFulfillmentReportThriftService orderFulfillmentReportThriftService;

    @Override
    public OrderFulfillmentReportQueryResp asyncQueryReport(OrderFulfillmentReportReq request, List<Long> poiIds, List<Long> warehouseIds) {
        OrderFulfillmentReportQueryResp resp = new OrderFulfillmentReportQueryResp();
        log.info("FulfillReportAsyncServiceImpl asyncQueryReport request:{}, poiIds:{}, warehouseIds:{}", request, poiIds, warehouseIds);
        OrderReportRequest orderReportRequest = new OrderReportRequest();
        orderReportRequest.setTenantId(request.getTenantId());
        orderReportRequest.setShopIdList(poiIds);
        orderReportRequest.setWarehouseIdList(warehouseIds);
        orderReportRequest.setFilterType(request.getFilterType());
        try {

            FulfillReportResponse fulfillReportResponse = orderFulfillmentReportThriftService.queryFulfillReport(orderReportRequest);
            if (fulfillReportResponse == null || fulfillReportResponse.getStatus() == null || fulfillReportResponse.getStatus().getCode() != StatusCodeEnum.SUCCESS.getValue()) {
                log.error("FulfillReportAsyncServiceImpl asyncQueryReport 查询失败：param {}，result:{}", orderReportRequest, fulfillReportResponse);
                return resp;
            }
            log.info("fulfillReportResponse:{}", JacksonUtils.toJson(fulfillReportResponse));
            if (CollectionUtils.isEmpty(fulfillReportResponse.getFulfillReportList())) {
                resp.setOrderFulfillmentDetailReportList(Lists.newArrayList());
                return resp;
            }
            List<OrderFulfillmentReportVO> reportVOList = fulfillReportResponse.getFulfillReportList().stream().map(fulfillReport -> toBuild(fulfillReport, request.getFilterType())).collect(Collectors.toList());
            resp.setOrderFulfillmentDetailReportList(reportVOList);
        } catch (TException e) {
            log.error("FulfillReportAsyncServiceImpl asyncQueryReport error", e);
            throw new RuntimeException(e);
        }

        return resp;

    }

    private OrderFulfillmentReportVO toBuild(FulfillReport fulfillReport, int filterType) {
        OrderFulfillmentReportVO reportVO = new OrderFulfillmentReportVO();
        if (filterType == 1) {
            reportVO.setWarehouseId(fulfillReport.getWarehouseId());
        }
        reportVO.setShopId(fulfillReport.getShopId());
        reportVO.setWaitReceiveOrderCount(fulfillReport.getWaitReceiveOrderCount());
        reportVO.setPickingOrderCount(fulfillReport.getPickingOrderCount());
        reportVO.setPickTimeoutCount(fulfillReport.getPickTimeoutCount());
        reportVO.setRiderReceiveTimeoutCount(fulfillReport.getRiderReceiveTimeoutCount());
        reportVO.setRiderReachUnpickCount(fulfillReport.getRiderReachUnpickCount());

        return reportVO;
    }
}
