package com.sankuai.shangou.qnh.orderapi.remote;

import com.meituan.linz.boot.util.Fun;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.sankuai.meituan.shangou.saas.crm.data.client.dto.orderfinance.OrderProfitViewDto;
import com.sankuai.meituan.shangou.saas.crm.data.client.request.orderfinance.BatchQueryOrderProfitRequest;
import com.sankuai.meituan.shangou.saas.crm.data.client.request.orderfinance.OrderUniqueAttribute;
import com.sankuai.meituan.shangou.saas.crm.data.client.response.orderfinance.BatchQueryOrderProfitResponse;
import com.sankuai.meituan.shangou.saas.crm.data.client.service.OrderFinanceThriftService;
import com.sankuai.sgdata.query.thrift.DataQueryThriftService;
import com.sankuai.sgdata.query.thrift.request.QueryRequest;
import com.sankuai.sgdata.query.thrift.response.QueryResponse;
import com.sankuai.shangou.qnh.orderapi.enums.app.ResultCodeEnum;
import com.sankuai.shangou.qnh.orderapi.utils.app.DataOrderUtils;
import com.sankuai.shangou.qnh.orderapi.utils.app.FormatUtil;
import com.sankuai.shangou.qnh.orderapi.utils.app.MoneyUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: qnh_order_api
 * @description: 数据模块相关查询
 * @author: jinyi
 * @create: 2023-10-23 20:18
 **/
@Slf4j
@Service
public class SaasCrmDataRemoteService {

    @Autowired
    private OrderFinanceThriftService orderFinanceThriftService;

    @Autowired
    private DataQueryThriftService dataQueryThriftService;



    /**
     * 获取订单毛利
     *
     * @param orderVOS
     * @return
     */
    public Map<String, OrderProfitView> queryNetProfit(List<OCMSOrderVO> orderVOS) {
        try {
            HashMap<String, OrderProfitView> resultMap = new HashMap<>();
            LocalDate begin = LocalDate.MAX;
            LocalDate end = LocalDate.MIN;
            ArrayList<OCMSOrderVO> realTimeOcmsOrderVOS = new ArrayList<>(orderVOS.size());
            ArrayList<OCMSOrderVO> offlineOcmsOrderVOS = new ArrayList<>(orderVOS.size());
            for (OCMSOrderVO ocmsOrderVO : orderVOS) {
                Long createTime = ocmsOrderVO.getCreateTime();
                LocalDate localDate = LocalDateTime.ofInstant(Instant.ofEpochMilli(createTime), ZoneId.systemDefault()).toLocalDate();
                boolean realTime = DataOrderUtils.isRealTime(localDate, localDate);
                if (realTime) {
                    realTimeOcmsOrderVOS.add(ocmsOrderVO);
                }
                else {
                    if (localDate.isBefore(begin)) {
                        begin = localDate;
                    }
                    if (localDate.isAfter(end)) {
                        end = localDate;
                    }
                    offlineOcmsOrderVOS.add(ocmsOrderVO);
                }
            }

            if (CollectionUtils.isNotEmpty(offlineOcmsOrderVOS)) {
                queryOffLineNetProfit(resultMap, begin, end, offlineOcmsOrderVOS);
            }

            if (CollectionUtils.isNotEmpty(realTimeOcmsOrderVOS)) {
                queryRealTimeNetProfit(resultMap, realTimeOcmsOrderVOS);
            }
            return resultMap;
        }
        catch (Exception e) {
            return Collections.emptyMap();
        }
    }


    /**
     * 获取订单毛利 不区分实时还是离线
     *
     * @param orderVOS
     * @return
     */
    public Map<String, OrderProfitView> queryNetProfitV2(List<OCMSOrderVO> orderVOS) {
        if (CollectionUtils.isEmpty(orderVOS)){
            return Collections.emptyMap();
        }
        try {
            Map<String, OrderProfitView> resultMap = new HashMap<>();
            BatchQueryOrderProfitRequest batchQueryOrderProfitRequest = new BatchQueryOrderProfitRequest();
            List<OrderUniqueAttribute> orderUniqueAttributes = Fun.map(orderVOS, ocmsOrderVO -> {
                OrderUniqueAttribute orderUniqueAttribute = new OrderUniqueAttribute();
                orderUniqueAttribute.setOrderBizType(ocmsOrderVO.getOrderBizType());
                orderUniqueAttribute.setViewOrderId(ocmsOrderVO.getViewOrderId());
                orderUniqueAttribute.setTenantId(ocmsOrderVO.getTenantId());
                orderUniqueAttribute.setPoiId(ocmsOrderVO.getShopId());
                orderUniqueAttribute.setOrderId(ocmsOrderVO.getOrderId());
                orderUniqueAttribute.setCreateTime(DateFormatUtils.format(ocmsOrderVO.getCreateTime(), "yyyyMMdd"));
                return orderUniqueAttribute;
            });
            batchQueryOrderProfitRequest.setOrderUniqueAttributes(orderUniqueAttributes);
            BatchQueryOrderProfitResponse batchQueryOrderProfitResponse = orderFinanceThriftService.uniformBatchQueryOrderProfit(batchQueryOrderProfitRequest);
            if (batchQueryOrderProfitResponse.getCode().getCode() == ResultCodeEnum.SUCCESS.getCode() && CollectionUtils.isNotEmpty(batchQueryOrderProfitResponse.getViewDtos())) {
                for (OrderProfitViewDto viewDto : batchQueryOrderProfitResponse.getViewDtos()) {
                    resultMap.put(viewDto.getViewOrderId(), new OrderProfitView(viewDto.getProfit(), viewDto.getWithDeliveryCost()));
                }
            }
            return resultMap;
        }
        catch (Exception e) {
            return Collections.emptyMap();
        }
    }

    private void queryRealTimeNetProfit(HashMap<String, OrderProfitView> resultMap, ArrayList<OCMSOrderVO> realTimeOcmsOrderVOS) throws TException {
        BatchQueryOrderProfitRequest batchQueryOrderProfitRequest = new BatchQueryOrderProfitRequest();
        List<OrderUniqueAttribute> orderUniqueAttributes = Fun.map(realTimeOcmsOrderVOS, ocmsOrderVO -> {
            OrderUniqueAttribute orderUniqueAttribute = new OrderUniqueAttribute();
            orderUniqueAttribute.setOrderBizType(ocmsOrderVO.getOrderBizType());
            orderUniqueAttribute.setViewOrderId(ocmsOrderVO.getViewOrderId());
            return orderUniqueAttribute;
        });
        batchQueryOrderProfitRequest.setOrderUniqueAttributes(orderUniqueAttributes);
        BatchQueryOrderProfitResponse batchQueryOrderProfitResponse = orderFinanceThriftService.batchQueryOrderProfit(batchQueryOrderProfitRequest);
        if (batchQueryOrderProfitResponse.getCode().getCode() == ResultCodeEnum.SUCCESS.getCode()) {
            for (OrderProfitViewDto viewDto : batchQueryOrderProfitResponse.getViewDtos()) {
                resultMap.put(viewDto.getViewOrderId(), new OrderProfitView(viewDto.getProfit(), viewDto.getWithDeliveryCost()));
            }
        }
    }
    private void queryOffLineNetProfit(HashMap<String, OrderProfitView> resultMap, LocalDate begin, LocalDate end, ArrayList<OCMSOrderVO> offlineOcmsOrderVOS) throws TException {
        List<String> offlineViewOrderIds = Fun.map(offlineOcmsOrderVOS, OCMSOrderVO::getViewOrderId);
        List<Long> poiIds = offlineOcmsOrderVOS.stream().map(OCMSOrderVO::getShopId).distinct().collect(Collectors.toList());
        QueryRequest queryRequest = new QueryRequest();
        queryRequest.setAppCode("shangou_reco_saas_data");
        queryRequest.setApiCode("b_service_baichuan_fin_poi_order_data");
        queryRequest.setNeedPage(false);
        Map<String, String> params = new HashMap<>();

        params.put("beginDate", begin.format(DateTimeFormatter.ISO_DATE).replace("-", ""));
        params.put("endDate", end.format(DateTimeFormatter.ISO_DATE).replace("-", ""));


        params.put("poiId", StringUtils.join(poiIds, ","));
        params.put("viewOrderId", FormatUtil.formatViewOrderIds(offlineViewOrderIds));
        //1 不限；2 亏损；3 盈利；
        params.put("profitType", "1");
        queryRequest.setParam(params);
        log.info("queryOffLineNetProfit request:{}", com.meituan.linz.boot.util.JacksonUtils.toJson(queryRequest));
        QueryResponse response = dataQueryThriftService.query(queryRequest);
        log.info("queryOffLineNetProfit response:{}", com.meituan.linz.boot.util.JacksonUtils.toJson(response));
        if (response.getCode() == ResultCodeEnum.SUCCESS.getCode()) {
            List<Map<String, String>> dataList = response.getData().getDataList();
            for (Map<String, String> data : dataList) {
                String viewOrderId = data.get("viewOrderId");
                long netProfitOnline = MoneyUtils.yuanToCent(data.get("netProfitOnline"));
                resultMap.put(viewOrderId, new OrderProfitView(netProfitOnline, Boolean.TRUE));
            }
        }
    }

    @Getter
    @Setter
    @ToString
    @AllArgsConstructor
    public static class OrderProfitView {

        private Long profit;

        private Boolean withDeliveryCost;
    }
}
