package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/18 17:07
 * @Description:
 */
@TypeDoc(
        description = "列表查询请求封装"
)
@ApiModel(value = "列表查询请求封装")
@Setter
@Getter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class PageRequest implements BaseRequest {

    @FieldDoc(
            description = "页码"
    )
    @ApiModelProperty(value = "页码")
    protected int page = 1;

    @FieldDoc(
            description = "每页记录数量"
    )
    @ApiModelProperty(value = "每页记录数量")
    protected int pageSize = 20;

}
