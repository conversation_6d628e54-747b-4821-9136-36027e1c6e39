package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.AssemblyChannelStoreFrontCategoryVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/8/12
 * desc:
 */
@TypeDoc(
        description = "查询单门店多渠道分类响应"
)
@Data
@ApiModel("查询单门店多渠道分类响应")
public class GetAllAssemblyChannelStoreCategoryResponse {

    @FieldDoc(
            description = "聚合渠道店内分类列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "聚合渠道店内分类列表", required = true)
    private List<AssemblyChannelStoreFrontCategoryVO> assemblyChannelStoreCategoryList;
}
