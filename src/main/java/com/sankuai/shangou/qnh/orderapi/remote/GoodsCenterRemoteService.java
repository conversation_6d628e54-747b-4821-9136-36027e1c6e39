package com.sankuai.shangou.qnh.orderapi.remote;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Lists;
import com.meituan.shangou.goodscenter.dto.DepotGoodsDetailDto;
import com.meituan.shangou.goodscenter.dto.GoodsSkuRelationDto;
import com.meituan.shangou.goodscenter.request.DepotGoodsBatchQueryRequest;
import com.meituan.shangou.goodscenter.request.GoodsSkuRelationSkuIdQueryRequest;
import com.meituan.shangou.goodscenter.response.DepotGoodsDetailListResponse;
import com.meituan.shangou.goodscenter.response.GoodsSkuRelationListResponse;
import com.meituan.shangou.goodscenter.thrift.DepotGoodsThriftService;

import com.meituan.shangou.goodscenter.thrift.GoodsSkuRelationThriftService;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.qnh.orderapi.enums.app.ResultCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/12/14 10:46
 **/
@Rhino
@Slf4j
public class GoodsCenterRemoteService {
    @Resource
    private DepotGoodsThriftService depotGoodsThriftService;
    @Resource
    private GoodsSkuRelationThriftService goodsSkuRelationThriftService;


    private static final int BATCH_SIZE = 50;

    @Degrade(rhinoKey = "GoodsCenterRemoteService.queryGoodsInfo", fallBackMethod = "queryGoodsInfoFallBack", timeoutInMilliseconds = 2000L)
    public List<DepotGoodsDetailDto> queryGoodsInfo(Long tenantId, Long offlineStoreId, List<String> goodsIdList) {
        if(CollectionUtils.isEmpty(goodsIdList)) {
            return Collections.emptyList();
        }

        DepotGoodsBatchQueryRequest request = new DepotGoodsBatchQueryRequest();
        request.setTenantId(tenantId);
        request.setDepotId(offlineStoreId);

        List<List<String>> goodsIdLists = Lists.partition(goodsIdList.stream().distinct().collect(Collectors.toList()), BATCH_SIZE);

        List<DepotGoodsDetailDto> depotGoodsDetailDtos = new ArrayList<>();
            for (List<String> goodsIds : goodsIdLists) {
                request.setGoodsIdList(goodsIds);
                log.info("start invoke goods center, request: {}", request);
                DepotGoodsDetailListResponse response = depotGoodsThriftService.batchQueryDepotGoodsListByGoodsId(request);
                log.info("end invoke goods center, response: {}", response);
                if (response == null || response.getCode() == null) {
                    throw new BizException("调用货品中心查询货品信息失败");
                }

                if (response.getCode() != ResultCodeEnum.SUCCESS.getCode()) {
                    throw new BizException("调用货品中心查询货品信息失败");
                }

                depotGoodsDetailDtos.addAll(response.getData());
            }

        return depotGoodsDetailDtos;
    }


    public List<DepotGoodsDetailDto> queryGoodsInfoFallBack(Long tenantId, Long offlineStoreId, List<String> goodsIdList) {
        log.warn("GoodsCenterWrapper.queryGoodsInfo 发生降级");
        return Collections.emptyList();
    }

    /**
     * 通过货号查询商货品关系
     */
    public List<GoodsSkuRelationDto> queryGoodsSkuRelationByGoodsCodes(Long tenantId, List<String> goodsCodes) {
        try {
            GoodsSkuRelationSkuIdQueryRequest request = new GoodsSkuRelationSkuIdQueryRequest();
            request.setTenantId(tenantId);
            request.setSkuIdList(goodsCodes);
            GoodsSkuRelationListResponse response = goodsSkuRelationThriftService.queryBySkuIds(request);
            if(response != null && response.success()) {
                List<GoodsSkuRelationDto> goodsSkuRelationDtos = response.getData();
                return goodsSkuRelationDtos;
            }
            return new ArrayList<>();
        } catch (Exception ex) {
            log.error("调用GoodsSkuRelationThriftService.queryGoodSkuRelationByGoodsCodes错误, goodsCode:{}, ex: ", goodsCodes, ex);
        }
        return new ArrayList<>();
    }

}
