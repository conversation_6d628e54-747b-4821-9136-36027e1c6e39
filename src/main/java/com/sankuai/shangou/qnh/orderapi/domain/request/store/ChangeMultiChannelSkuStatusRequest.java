package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.ChannelStatusChangeParamVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/6/12
 * desc:
 */
@TypeDoc(
        description = "商品上下架请求"
)
@Data
@ApiModel("商品上下架请求")
public class ChangeMultiChannelSkuStatusRequest {

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID")
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "商品SKU编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品SKU编码", required = true)
    @NotNull
    private String skuId;

    @FieldDoc(
            description = "渠道上下架信息列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道上下架信息列表", required = true)
    @NotEmpty
    private List<ChannelStatusChangeParamVO> channelStatusChangeParamList;

    @FieldDoc(
            description = "业务类型 1-商超 2-买菜 默认为1", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "业务类型")
    private Integer bizType;

    @FieldDoc(
            description = "校验是否允许上架 （true-校验，false-不校验）", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "校验是否允许上架")
    private boolean checkPermit = false;
}
