package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TypeDoc(
        description = "商品提报价相应"
)
@Data
@ApiModel("商品提报价相应")
public class QuoteSkuPriceResponse {

    @FieldDoc(
            description = "返回信息"
    )
    @ApiModelProperty(name = "返回信息")
    private String info;


    @FieldDoc(
            description = "标识是否需要审核"
    )
    @ApiModelProperty(name = "标识是否需要审核")
    private boolean needReview ;
}
