package com.sankuai.shangou.qnh.orderapi.domain.response.pc;

import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.bo.ReturnDataPoiBo;
import com.sankuai.shangou.qnh.orderapi.domain.result.ReturnDataSecurity;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.OrderItemMoneyRefundCheckVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.RefundReasonAndCodeVO;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * Created by suxiaoyu on 2023/3/7 14:19
 */
@TypeDoc(
        description = "金额退页面检查的response"
)
@Data
public class OrderMoneyRefundCheckResponse implements ReturnDataSecurity {
    @FieldDoc(
            description = "金额退页面检查的商品信息"
    )
    private List<OrderItemMoneyRefundCheckVO> moneyRefundCheckVOList;

    @FieldDoc(
            description = "退款原因及编码"
    )
    private List<RefundReasonAndCodeVO> refundReasons;

    @Override
    public List<ReturnDataPoiBo> fetchReturnDataPoiBoList() {
        if (CollectionUtils.isNotEmpty(moneyRefundCheckVOList)) {
            Long shopId = moneyRefundCheckVOList.get(0).getShopId();
            if (Objects.nonNull(shopId)) {
                return Lists.newArrayList(ReturnDataPoiBo.builder().poiId(shopId)
                        .dispatchShopId(moneyRefundCheckVOList.get(0).getDispatchShopId()).build());
            }
        }
        return null;
    }
}
