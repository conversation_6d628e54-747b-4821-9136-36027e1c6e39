package com.sankuai.shangou.qnh.orderapi.domain.request.app;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2019/7/11
 * desc: 打印小票请求
 */
@TypeDoc(
        description = "打印小票请求"
)
@ApiModel("打印小票请求")
@Data
public class PrintReceiptRequest {

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道ID")
    @NotNull
    private Integer channelId;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道订单号")
    @NotNull
    public String channelOrderId;
}