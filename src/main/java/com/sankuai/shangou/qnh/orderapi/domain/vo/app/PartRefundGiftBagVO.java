package com.sankuai.shangou.qnh.orderapi.domain.vo.app;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2019/7/11
 * desc: 部分退款商品
 */
@TypeDoc(
        description = "部分退款礼袋"
)
@ApiModel("部分退款礼袋")
@Data
public class PartRefundGiftBagVO {

    @FieldDoc(
            description = "主品skuId", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "主品skuId")
    private String belongSkuId;

    @FieldDoc(
            description = "礼袋货品skuId", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "礼袋货品skuId")
    private String materialSkuId;

    @FieldDoc(
            description = "礼袋货品名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "礼袋货品名称")
    private String materialSkuName;

    @FieldDoc(
            description = "退款数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "退款数量")
    @NotNull
    private Integer count;

    @FieldDoc(
            description = "1:品牌礼袋， 2:歪马礼袋", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "1:品牌礼袋， 2:歪马礼袋")
    @NotNull
    private Integer type;

}