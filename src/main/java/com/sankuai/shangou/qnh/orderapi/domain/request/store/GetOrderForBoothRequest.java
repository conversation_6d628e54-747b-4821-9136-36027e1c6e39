package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;


/**
 * <AUTHOR>
 * @date 2019/5/5
 * desc:
 */
@TypeDoc(
        description = "查询摊位订单列表请求"
)
@Data
@ApiModel("查询摊位订单列表请求")
public class GetOrderForBoothRequest {

    @FieldDoc(
            description = "日期  格式:yyyy-mm-dd", requiredness = Requiredness.REQUIRED
    )
    @NotNull
    private String date;

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID")
    @NotNull
    private Long storeId;
}
