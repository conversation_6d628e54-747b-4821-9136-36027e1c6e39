package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * author: g<PERSON><PERSON><PERSON>@meituan.com
 * date: 2020-07-21 16:54:45
 * <p>
 * desc: 价格迁移配置详情
 */
@Data
@NoArgsConstructor
public class PriceMigrateConfigDetailVO {

    /**
     * 价格模块迁移开关开启走新接口
     */
    private Boolean switchStatus;

    /**
     * 在功能模块开关开启下，若灰度租户列表为空，则表示所有租户都走新接口；
     * 若当前租户下的门店列表为空，或包含当前门店，则走新接口
     * 若当前租户下的门店列表不包含当前门店，则走老接口
     */
    private Map<Long/*tenantId*/, List<Long>/*storeIds*/> crayTenantStoreMap;
}
