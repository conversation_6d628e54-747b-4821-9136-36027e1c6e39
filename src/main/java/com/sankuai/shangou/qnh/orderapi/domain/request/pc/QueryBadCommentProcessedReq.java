package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import com.sankuai.sgfulfillment.comment.thrift.dto.QueryBadCommentProcessedListReq;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/11/18
 * @email jianglilin02@meituan
 */
@Data
public class QueryBadCommentProcessedReq {
    /**
     * @FieldDoc( description = "租户id",
     * example = {}
     * )
     */
    public Long tenantId; // required
    /**
     * @FieldDoc( description = "门店id列表",
     * example = {}
     * )
     */
    public List<Long> poiIds; // optional
    /**
     * @FieldDoc( description = "渠道id列表",
     * example = {}
     * )
     */
    public List<Integer> channelIds; // optional
    /**
     * @FieldDoc( description = "活动开始日期",
     * example = {}
     * )
     */
    public String startTime; // required
    /**
     * @FieldDoc( description = "活动截止日期",
     * example = {}
     * )
     */
    public String endTime; // required
    /**
     * @FieldDoc( description = "差评是否删除",
     * example = {}
     * )
     */
    public Boolean commentHasDeleted; // optional
    /**
     * @FieldDoc( description = "当前页",
     * example = {}
     * )
     */
    public Integer page; // required
    /**
     * @FieldDoc( description = "每页行数",
     * example = {}
     * )
     */
    public Integer pageSize; // required

    public QueryBadCommentProcessedListReq convertToThrift() {
        DateTimeFormatter from = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter to = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        QueryBadCommentProcessedListReq req = new QueryBadCommentProcessedListReq();
        req.setTenantId(this.getTenantId());
        req.setStoreIds(this.getPoiIds());
        req.setChannelIds(this.getChannelIds());
        req.setStartTime(getBeginOfTheDay(this.getStartTime(), from, to));
        req.setEndTime(getEndOfTheDay(this.getEndTime(), from, to));
        if (Objects.nonNull(this.getCommentHasDeleted())) {
            req.setCommentHasDeleted(this.getCommentHasDeleted());
        }
        req.setPageNum(this.getPage());
        req.setPageSize(this.getPageSize());
        return req;
    }

    public void valid() {
        AssertUtil.notNull(this.getTenantId(), "租户Id不能为空");
        AssertUtil.notBlank(this.getStartTime(), "起始时间不能为空");
        AssertUtil.notBlank(this.getEndTime(), "结束时间不能为空");
        AssertUtil.notNull(this.getPage(), "页码不能为空");
        AssertUtil.notNull(this.getPageSize(), "页大小不能为空");
    }

    private String getBeginOfTheDay(String startTime, DateTimeFormatter from, DateTimeFormatter to) {
        if (StringUtils.isBlank(startTime)) {
            return null;
        }

        LocalDate localDate = LocalDate.parse(startTime, from);
        return LocalDateTime.of(localDate, LocalTime.MIN).format(to);
    }

    private String getEndOfTheDay(String endTime, DateTimeFormatter from, DateTimeFormatter to) {
        if (StringUtils.isBlank(endTime)) {
            return null;
        }
        LocalDate localDate = LocalDate.parse(endTime, from);
        return LocalDateTime.of(localDate, LocalTime.MAX).format(to);
    }

}
