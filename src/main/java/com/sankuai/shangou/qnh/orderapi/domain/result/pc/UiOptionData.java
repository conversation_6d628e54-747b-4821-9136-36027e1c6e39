package com.sankuai.shangou.qnh.orderapi.domain.result.pc;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.HashMap;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2022/12/02 19:50
 * @Description:
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
public class UiOptionData {

    private HashMap<String,List<UiOption>> UiOptionList;


}
