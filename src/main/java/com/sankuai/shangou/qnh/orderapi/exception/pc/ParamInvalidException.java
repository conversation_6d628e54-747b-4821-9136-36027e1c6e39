package com.sankuai.shangou.qnh.orderapi.exception.pc;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: ParamInvalidException
 * @Description: 参数异常
 * @date 25/06/2018 8:04 PM
 **/

public class ParamInvalidException extends RuntimeException {

  /**
   * 校验字段名称
   */
  private String fielddName;

  public String getFielddName() {
    return fielddName;
  }

  public void setFielddName(String fielddName) {
    this.fielddName = fielddName;
  }

  public ParamInvalidException() {
    super();
  }

  public ParamInvalidException(String message) {
    super(message);
  }

  public ParamInvalidException(Throwable cause) {
    super(cause);
  }

  public ParamInvalidException(String message, Throwable cause) {
    super(message, cause);
  }

  public ParamInvalidException(String message, String fielddName) {
    super(message);
    this.fielddName = fielddName;
  }

  public ParamInvalidException(Throwable cause, String fielddName) {
    super(cause);
    this.fielddName = fielddName;
  }

  public ParamInvalidException(String message, Throwable cause, String fielddName) {
    super(message, cause);
    this.fielddName = fielddName;
  }


}
