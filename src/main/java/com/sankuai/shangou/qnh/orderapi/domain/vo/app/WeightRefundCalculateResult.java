package com.sankuai.shangou.qnh.orderapi.domain.vo.app;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/3/17
 * desc: 克重退差试算结果
 */
@TypeDoc(
        description = "检查退款请求响应"
)
@ApiModel("检查退款请求响应")
@Data
public class WeightRefundCalculateResult {



    @FieldDoc(
            description = "订单详情", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "退差试算结果", required = true)
    private List<RefundCalculateResult> refundCalculateResults;
}
