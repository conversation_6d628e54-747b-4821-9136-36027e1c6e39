package com.sankuai.shangou.qnh.orderapi.interceptor.store;

import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.sankuai.shangou.qnh.orderapi.enums.store.FunctionPowers;
import com.sankuai.shangou.qnh.orderapi.exception.store.CommonLogicException;
import com.sankuai.shangou.qnh.orderapi.context.store.AppAuthContext;
import com.sankuai.shangou.qnh.orderapi.annotation.store.Auth;
import com.sankuai.shangou.qnh.orderapi.utils.store.ApiMethodParamThreadLocal;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ArrayUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Slf4j
@Component
public class AuthInterceptor extends HandlerInterceptorAdapter {
	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
		try {
			if(handler instanceof HandlerMethod) {
				// 只处理Controller
				HandlerMethod handlerMethod = (HandlerMethod)handler;

				Class<?> type = handlerMethod.getBeanType();

				AuthInfo authInfo = new AuthInfo(type.getDeclaredAnnotation(Auth.class),
						handlerMethod.getMethodAnnotation(Auth.class));

				if(!authInfo.authenticate) {
					return true;
				}

				if(ApiMethodParamThreadLocal.getIdentityInfo().getUser() == null) {
					throw new CommonLogicException("获取用户数据失败", ResultCode.FAIL);
				}
				return true;
			} else {
				return true;
			}
		} finally {
			AppAuthContext.clear();
		}
	}

	@SuppressWarnings("unused")
	private static final class AuthInfo {
		private final boolean authenticate;
		private final boolean needNewest;
		private final List<Integer> permissionList;

		private AuthInfo(Auth authOnClass, Auth authOnMethod) {
			FunctionPowers[] permission;

			if(authOnClass == null && authOnMethod == null) {
				authenticate = false;
				needNewest = false;
				permission = new FunctionPowers[0];
			} else if(authOnMethod != null) {
				authenticate = authOnMethod.authenticate();
				needNewest = authOnMethod.needNewest();
				permission = authOnMethod.permission();
			} else {
				authenticate = authOnClass.authenticate();
				needNewest = authOnClass.needNewest();
				permission = authOnClass.permission();
			}

			if(ArrayUtils.isEmpty(permission)) {
				permissionList = Collections.emptyList();
			} else {
				permissionList = new ArrayList<>();

				for(FunctionPowers p : permission) {
					permissionList.add(p.code());
				}
			}
		}

		public boolean authenticate() {
			return authenticate;
		}

		public boolean needNewest() {
			return needNewest;
		}

		public List<Integer> permission() {
			return permissionList;
		}
	}
}
