package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * BaseOrderReq
 *
 * <AUTHOR>
 * @since 2023/3/21
 */
@Data
public class BaseOrderReq {

    @FieldDoc(description = "渠道ID", requiredness = Requiredness.REQUIRED)
    @NotNull(message = "渠道id不能为null")
    private Integer channelId;

    @FieldDoc(description = "渠道订单号", requiredness = Requiredness.REQUIRED)
    @NotNull(message = "渠道订单号不能为null")
    private String channelOrderId;

    public void validate(){
    }

}
