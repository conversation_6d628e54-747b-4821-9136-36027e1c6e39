package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.DeliveryChannelShopForModifyVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.SequentialDeliveryStrategyConfigVO;
import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/16
 * desc: 修改门店配送配置详情请求
 */
@Setter
@Getter
public class ModifyShopDeliveryConfigDetailRequest implements BaseRequest {

    @FieldDoc(
            description = "门店ID"
    )
    @ApiModelProperty(value = "门店ID", required = true)
    private String poiId;

    @FieldDoc(
            description = "待修改门店配送配置列表"
    )
    @ApiModelProperty(value = "待修改门店配送配置列表")
    private List<DeliveryChannelShopForModifyVO> list;

    @FieldDoc(
            description = "在商家接单时发起配送 0:否  1:是"
    )
    @ApiModelProperty(value = "在商家接单时发起配送 0:否  1:是")
    private Integer launchOnMerchantConfirm;

    @FieldDoc(
            description = "在商家接单时发起配送延迟时间"
    )
    @ApiModelProperty(value = "在商家接单时发起配送延迟时间")
    private Integer launchOnMerchantConfirmDelayTime;

    @FieldDoc(
            description = "在拣货完成后发起配送 0:否  1:是"
    )
    @ApiModelProperty(value = "在拣货完成后发起配送 0:否  1:是")
    private Integer launchOnPickupComplete;

    @FieldDoc(
            description = "在拣货完成后发起配送延迟时间"
    )
    @ApiModelProperty(value = "在拣货完成后发起配送延迟时间")
    private Integer launchOnPickupCompleteDelayTime;

    @FieldDoc(
            description = "配送策略 1：多渠道顺序轮询， 2：多渠道抢单"
    )
    @ApiModelProperty(value = "配送策略 1：多渠道顺序轮询， 2：多渠道抢单")
    private Integer deliveryStrategy;

    @FieldDoc(
            description = "顺序轮询配送策略配置信息"
    )
    @ApiModelProperty(value = "顺序轮询配送策略配置信息")
    private SequentialDeliveryStrategyConfigVO sequentialStrategyConfig;

    @Override
    public void selfCheck() {
        AssertUtil.notEmpty(poiId, "门店ID不能为空" , "poiId");
    }
}
