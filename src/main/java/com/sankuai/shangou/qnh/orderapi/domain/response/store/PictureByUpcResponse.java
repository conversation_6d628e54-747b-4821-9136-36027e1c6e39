package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2019-11-05
 **/
@TypeDoc(
        description = "UPC列表查询图片响应"
)
@Data
@ApiModel("UPC列表查询图片响应")
public class PictureByUpcResponse {

    @FieldDoc(
            description = "图片内容", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "图片内容", required = true)
    private String pictureUrl;

}
