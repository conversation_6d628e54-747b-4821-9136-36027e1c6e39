package com.sankuai.shangou.qnh.orderapi.interceptor.pc;

import com.alibaba.fastjson.JSON;
import com.meituan.shangou.saas.order.management.client.utils.BusinessIdTracer;
import com.meituan.shangou.saas.tenant.thrift.dto.employee.EmployeeDepDto;
import com.meituan.shangou.sac.dto.model.SacManagerAccountDto;
import com.sankuai.meituan.auth.util.UserUtils;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.meituan.shangou.empower.auth.sdk.bean.LoginAuthResult;
import com.sankuai.meituan.shangou.empower.auth.sdk.utils.LoginAuthUtil;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.TokenTypeEmum;
import com.sankuai.meituan.shangou.empower.auth.thrift.service.AccountThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.AccountSessionVO;
import com.sankuai.shangou.qnh.orderapi.constant.pc.CommonConstant;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.BaseResult;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.Result;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.ResultBuilder;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.LoginTicket;
import com.sankuai.shangou.qnh.orderapi.enums.pc.CookieTokenTypeEnum;
import com.sankuai.shangou.qnh.orderapi.remote.AccountRemoteService;
import com.sankuai.shangou.qnh.orderapi.remote.EmployeeRemoteService;
import com.sankuai.shangou.qnh.orderapi.remote.SacAccountRemoteService;
import com.sankuai.shangou.qnh.orderapi.remote.TenantRemoteService;
import com.sankuai.shangou.qnh.orderapi.service.pc.LoginManagerService;
import com.sankuai.shangou.qnh.orderapi.utils.pc.CookieUtil;
import com.sankuai.shangou.qnh.orderapi.utils.pc.LoginUtil;
import com.sankuai.shangou.qnh.orderapi.utils.pc.MccConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.util.Arrays;
import java.util.Map;
import java.util.Optional;

/**
 * 登录拦截器
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class LoginInterceptor extends HandlerInterceptorAdapter {

    @Resource
    private LoginManagerService loginManagerService;

    @Resource
    private AccountRemoteService accountClient;

    @Resource
    private TenantRemoteService tenantClient;

    @Autowired
    private SacAccountRemoteService sacAccountClient;

    @Autowired
    private EmployeeRemoteService employeeRemoteService;

    @Resource
    private AccountThriftService.Iface authAccountThriftService;

    @Value("${sso.clientId}")
    private String ssoClientId;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String ssoToken = CookieUtil.getCookieValue(request, ssoClientId + CookieTokenTypeEnum.SSO_TOKEN.getName());
        // 场景一：M 端 SSO 一键登录牵牛花
        if (StringUtils.isNotEmpty(ssoToken)) {
            User user = UserUtils.getUser(ssoToken);
            if (user != null) {
                // 1、执行统一的 SSO 登录鉴权拦截逻辑（必选）
                LoginAuthResult loginAuthResult = LoginAuthUtil.ssoLoginAuthForLoginFilter(authAccountThriftService, user.getLogin(), request, response);
                if (!loginAuthResult.loginAuthSuccess()) {
                    // 注：如果登录鉴权失败，统一逻辑已经写出了合理的响应，无需业务处理
                    return false;
                }
                // 2、注册用户账号到上下文信息中（可选，但一般每个服务都有自定义的 ContextHolder）
                AccountSessionVO accountSessionVO = loginAuthResult.getAccountSessionVO();
                ContextHolder.registerSsoUser(accountSessionVO, request);

                // 3、其他逻辑（可选）
                // 将租户ID放在trace中，方便ordermng进行es索引路由
                BusinessIdTracer.putTenantId(accountSessionVO.getTenantId());

                return true;
            }
        }

        // 场景二：普通 EP 账号密码登录

        // 1、执行统一的 EP 登录鉴权拦截逻辑（必选）
        LoginAuthResult loginAuthResult = LoginAuthUtil.loginAuthForLoginFilter(authAccountThriftService, request, response);
        if (!loginAuthResult.loginAuthSuccess()) {
            // 注：如果登录鉴权失败，统一逻辑已经写出了合理的响应，无需业务处理
            return false;
        }
        // 2、注册用户账号到上下文信息中（可选，但一般每个服务都有自定义的 ContextHolder）
        AccountSessionVO accountSessionVO = loginAuthResult.getAccountSessionVO();
        ContextHolder.registerCurrentUser(accountSessionVO, loginAuthResult.getToken(), request);

        // 3、其他逻辑（可选）
        // 将租户ID放在trace中，方便ordermng进行es索引路由
        BusinessIdTracer.putTenantId(accountSessionVO.getTenantId());
        return true;
    }

    private String getEmpName(Long staffId) {
        try {
            Map<Long, EmployeeDepDto> employeeDepDtoMap = employeeRemoteService.queryEmployeeByIds(Arrays.asList(staffId), 0L);
            return Optional.ofNullable(employeeDepDtoMap.get(staffId)).map(EmployeeDepDto::getEmployeeName).orElse(null);
        } catch (Exception e) {
            log.error("查询员工失败, empId:{}", staffId, e);
        }
        return null;
    }

    private SacManagerAccountDto getSacAccount(String misId) {
        return sacAccountClient.getManagerAccountByMisId(misId);
    }

    private void processUnLoginAndLoginOut(HttpServletRequest request, HttpServletResponse response, String ticket) throws Exception {
        if (StringUtils.isNotBlank(ticket)) {
            loginManagerService.loginOut(TokenTypeEmum.E_TOKEN, ticket);
        }
        processUnLogin(request, response);

    }

    private void processUnLogin(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // TODO:与前端对接的时候, 再与前端做返回值具体确认
        Result result = ResultBuilder.buildResult(BaseResult.NOT_LOGIN);
        response.setContentType(CommonConstant.CONTENT_TYPE_APPLICATION_JSON);
        response.setCharacterEncoding(CommonConstant.CHARSET_UTF8);
        String data = JSON.toJSONString(result);

        PrintWriter writer = response.getWriter();
        writer.write(data);
        writer.flush();
    }

    private void processServiceUnavailable(HttpServletRequest request, HttpServletResponse response) throws Exception {
        Result result = ResultBuilder.buildResult(BaseResult.SERVICE_UNAVAILABLE.getCode(), MccConfigUtil.getRejectText(), null);
        response.setContentType(CommonConstant.CONTENT_TYPE_APPLICATION_JSON);
        response.setCharacterEncoding(CommonConstant.CHARSET_UTF8);
        String data = JSON.toJSONString(result);

        PrintWriter writer = response.getWriter();
        writer.write(data);
        writer.flush();
    }

    // 登录逻辑处理 by eToken
    private void processLoginedByEToken(AccountSessionVO accountSessionVO, String token, HttpServletRequest request, HttpServletResponse response) {
        // 注册用户账号到上下文信息中
        ContextHolder.registerCurrentUser(accountSessionVO, token, request);
    }

    // 登录逻辑处理 by authToken
    private void processLoginedByAuthToken(LoginTicket loginTicket, String ticket, HttpServletRequest request, HttpServletResponse response) {
        // 注册用户账号到上下文信息中
        ContextHolder.registerCurrentUser(loginTicket.getUid(), loginTicket.getAccount(), loginTicket.getTenantId(), loginTicket.getToken(),
                loginTicket.getStaffId(), LoginUtil.getAppId(request));

        //增加禁用开关 避免 epassport 登录界面上线后 用户一直不退出。
        if (MccConfigUtil.disableRefreshTicket()) {
            return;
        }
        // 刷新凭证逻辑
        loginManagerService.addLoginTicket(ticket, loginTicket, MccConfigUtil.getLoginExpireTimeInSeconds());
    }

    private boolean isAjaxRequest(HttpServletRequest request) {
        String xRequestedWithHead = request.getHeader(CommonConstant.HEADER_X_REQUESTED_WITH);
        return CommonConstant.XML_HTTP_REQUEST.equals(xRequestedWithHead);
    }
}
