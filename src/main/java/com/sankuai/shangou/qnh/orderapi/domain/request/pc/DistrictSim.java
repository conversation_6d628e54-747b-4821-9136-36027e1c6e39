package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.shangou.saas.tenant.thrift.dto.DistrictSimDto;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: <EMAIL>
 * @Date: 2020/2/4 18:06
 * @Description: 简化地区信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DistrictSim {

    /**
     * 地区编码
     */
    private String districtCode;

    /**
     * 地区名称，如 四川/成都/武侯区
     */
    private String districtName;

    public DistrictSimDto toDto() {
        DistrictSimDto dto = new DistrictSimDto();
        dto.setDistrictCode(ConverterUtils.nonNullConvert(districtCode, Integer::valueOf));
        dto.setDistrictName(districtName);
        return dto;
    }
}
