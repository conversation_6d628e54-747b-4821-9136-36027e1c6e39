package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: <EMAIL>
 * @Date: 2020-04-02 17:19
 * @Description:
 */
@TypeDoc(
        description = "商品项退差价记录"
)
@ApiModel("商品项退差价记录")
@Data
public class WeightRefundAfterSaleRecordVO {

    @FieldDoc(
            description = "已退重量，单位:克", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "已退重量，单位:克", required = true)
    private Double refundWeight;

    @FieldDoc(
            description = "已退金额，单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "已退金额，单位:分", required = true)
    private Integer refundAmount;

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称", required = true)
    private String skuName;
}

