package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
@TypeDoc(
        name = "订单履约看板退单完成明细",
        description = "订单履约看板退单完成明细"
)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AfsOrderCompleteDetail {
    @FieldDoc(
            description = "退单类型"
    )
    private String afsOrderType;
    @FieldDoc(
            description = "退单类型描述"
    )
    private String afsOrderTypeDesc;
    @FieldDoc(
            description = "完成退单数量"
    )
    private int afsOrderCount;
}
