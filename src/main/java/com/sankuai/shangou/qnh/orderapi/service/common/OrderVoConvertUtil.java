package com.sankuai.shangou.qnh.orderapi.service.common;

import com.dianping.cat.util.MetricHelper;
import com.dianping.lion.client.Lion;
import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.meituan.shangou.saas.common.enums.ReturnGoodsStatusEnum;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.*;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.model.OrderStatusLog;
import com.meituan.shangou.saas.order.management.client.dto.response.revenue.OrderAmountInfo;
import com.meituan.shangou.saas.order.management.client.dto.response.revenue.OrderRevenueDetailResponse;
import com.meituan.shangou.saas.order.management.client.enums.ChannelType;
import com.meituan.shangou.saas.order.management.client.enums.WaitToAuditRefundGoodsOrderSubTypeEnum;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.meituan.shangou.saas.order.management.client.utils.DesensitizeReceiverInfoUtil;
import com.meituan.shangou.saas.order.management.client.utils.ExchangeUtil;
import com.meituan.shangou.saas.order.management.client.utils.OrderUtil;
import com.meituan.shangou.saas.order.management.client.utils.param.DesensitizeReceiverInfoExtParam;
import com.meituan.shangou.saas.order.management.client.utils.result.DesensitizeReceiverInfoResult;
import com.meituan.shangou.saas.order.platform.client.dto.model.OrderLabelModel;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.*;
import com.meituan.shangou.saas.utils.AddressSceneConvertUtils;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.DeliveryOrderType;
import com.sankuai.shangou.qnh.orderapi.constant.pc.CommonConstant;
import com.sankuai.shangou.qnh.orderapi.converter.app.ActionTagConverter;
import com.sankuai.shangou.qnh.orderapi.converter.app.DeliveryTypeModeConverter;
import com.sankuai.shangou.qnh.orderapi.converter.store.ChannelOrderConverter;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.RefundApplyListResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.SubProductVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.*;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.OrderTagVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.SortedTagVO;
import com.sankuai.shangou.qnh.orderapi.enums.OcmsRefundAuditType;
import com.sankuai.shangou.qnh.orderapi.enums.app.OrderViewStatusEnum;
import com.sankuai.shangou.qnh.orderapi.enums.app.UserTagTypeEnum;
import com.sankuai.shangou.qnh.orderapi.remote.SaasCrmDataRemoteService;
import com.sankuai.shangou.qnh.orderapi.utils.CombinationProductUtil;
import com.sankuai.shangou.qnh.orderapi.utils.CommonUsedUtil;
import com.sankuai.shangou.qnh.orderapi.utils.CompensationUtil;
import com.sankuai.shangou.qnh.orderapi.utils.ProductLabelUtil;
import com.sankuai.shangou.qnh.orderapi.utils.app.FormatUtil;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import com.sankuai.shangou.qnh.orderapi.utils.pc.LionUtils;
import com.sankuai.shangou.qnh.orderapi.utils.pc.OrderUtils;
import com.sankuai.shangou.qnh.orderapi.utils.store.MccDynamicConfigUtil;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.assertj.core.util.Lists;
import org.codehaus.jackson.type.TypeReference;
import org.joda.time.Instant;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.meituan.shangou.saas.order.platform.enums.AfterSaleApplyStatusEnum.*;

/**
 * <AUTHOR>
 * @since 2024/7/15
 **/
@Slf4j
@Component
public class OrderVoConvertUtil {

    //履约标签
    private static final int FULFILLMENT_TAG = 1;

    //拣货标准
    private static final int PICK_TAG = 2;

    private static final String SYMBOL_COMMA = ",";

    private static final String UN_KNOW = "未知";

    private static final int IS_SELF_DELIVERY_NO = 0;

    private static final int FRANCHISEE_ORDER = 1;


    public OrderVO buildOrderVO(OCMSOrderVO ocmsOrderVO, SaasCrmDataRemoteService.OrderProfitView orderProfit, List<OrderLabelModel> showLabelList) {
        List<OCMSOrderItemVO> ocmsOrderItemVOList = ocmsOrderVO.getOcmsOrderItemVOList();
        OCMSDeliveryInfoVO ocmsDeliveryInfoVO = ocmsOrderVO.getOcmsDeliveryInfoVO();
        OrderVO orderVO = new OrderVO();
        orderVO.setTenantId(ocmsOrderVO.getTenantId());
        orderVO.setIsHasCanRefundGoods(ocmsOrderVO.getIsHasCanRefundGoods());
        Integer channelId = ChannelOrderConvertUtils.sourceBiz2Mid(ocmsOrderVO.getOrderBizType());
        orderVO.setChannelId(channelId);
        if (Objects.nonNull(DynamicChannelType.findOf(channelId))) {
            //订单页展示渠道简称
            orderVO.setChannelName(Objects.requireNonNull(DynamicChannelType.findOf(channelId)).getDesc());
        }
        orderVO.setEmpowerOrderId(ocmsOrderVO.getOrderId());
        //已经存在erp门店编码不需要单独查询
        orderVO.setErpShopCode(ocmsOrderVO.getOutShopId());
        orderVO.setUserId(ocmsOrderVO.getUserId());
        orderVO.setStoreId(ocmsOrderVO.getShopId());
        orderVO.setStoreName(ocmsOrderVO.getShopName());
        orderVO.setDispatchShopId(ocmsOrderVO.getDispatchShopId());
        orderVO.setDispatchSerialNo(ocmsOrderVO.getDispatchSerialNo());
        orderVO.setDispatchShopName(ocmsOrderVO.getDispatchShopName());
        orderVO.setDispatchTenantId(ocmsOrderVO.getDispatchTenantId());
        orderVO.setDispatchTime(ocmsOrderVO.getDispatchTime());
        orderVO.setWarehouseId(ocmsOrderVO.getWarehouseId());
        orderVO.setWarehouseName(ocmsOrderVO.getWarehouseName());
        orderVO.setChannelOrderId(ocmsOrderVO.getViewOrderId());
        orderVO.setSerialNo(ocmsOrderVO.getOrderSerialNumber());
        orderVO.setSerialNoStr(ocmsOrderVO.getOrderSerialNumberStr());
        orderVO.setOrderSource(ocmsOrderVO.getOrderSource());
        //地址变更费
        CommonUsedUtil.dealAddressChangeFeeToOrderVO(ocmsOrderVO.getAddressChangeFee(), orderVO, true);
        //发财酒标识
        orderVO.setIsFacaiWine(ocmsOrderVO.getIsFacaiWine());
        // 支付时间
        orderVO.setPayTime(ocmsOrderVO.getPayTime() != null ? ocmsOrderVO.getPayTime() : ocmsOrderVO.getCreateTime());
        //商品数量为所有商品数量之和

        orderVO.setActualPayAmt(ocmsOrderVO.getActualPayAmt());
        orderVO.setBizReceiveAmt(ocmsOrderVO.getMerchantAmount());
        if (isDeliveryInfoNotNull(ocmsDeliveryInfoVO)){
            orderVO.setLockOrderState(ocmsDeliveryInfoVO.getLockOrderState());
            orderVO.setDeliveryMethod(ocmsDeliveryInfoVO.getDistributeMethod());
            orderVO.setDeliveryMethodDesc(ocmsDeliveryInfoVO.getDistributeMethodName());
            orderVO.setDeliveryUserName(ocmsDeliveryInfoVO.getRiderName());
            orderVO.setDeliveryUserPhone(ocmsDeliveryInfoVO.getRiderPhone());
            orderVO.setReceiverName(buildReceiverName(ocmsOrderVO));
            orderVO.setReceiverPhone(buildReceiverPhone(ocmsOrderVO));
            orderVO.setReceiverAddress(buildReceiverAddress(ocmsOrderVO));
            orderVO.setLastFourDigitsOfUser(buildLastFourDigitsOfUser(ocmsOrderVO));
            orderVO.setScene(AddressSceneConvertUtils.convertCategory2Scene(ocmsDeliveryInfoVO.getCategory()));
            orderVO.setEstimateArriveTimeStart(ocmsDeliveryInfoVO.getArrivalTime());
            orderVO.setEstimateArriveTimeEnd(ocmsDeliveryInfoVO.getArrivalEndTime());
            orderVO.setPickStatus(ocmsDeliveryInfoVO.getDeliveryStatus());
            orderVO.setPickCompleteTime(ocmsDeliveryInfoVO.getCompleteTime());
            orderVO.setDistributeStatus(ocmsDeliveryInfoVO.getDistributeStatus());
            orderVO.setSelfDelivery(ocmsDeliveryInfoVO.getIsSelfDelivery());
            orderVO.setSupportDeliveryUserPrivacyPhone(judgeSupportDeliveryUserPrivacyPhone(ocmsDeliveryInfoVO.getIsSelfDelivery()));
            if (ocmsDeliveryInfoVO.getDeliveryPauseFlag() != null){
                orderVO.setDeliveryStatusLocked(ocmsDeliveryInfoVO.getDeliveryPauseFlag());
            }
            if(StringUtils.isNotEmpty(ocmsDeliveryInfoVO.getSelfFetchCode())){
                orderVO.setSelfFetchCode(ocmsDeliveryInfoVO.getSelfFetchCode());
            }
            orderVO.setSelfFetchStatus(ocmsDeliveryInfoVO.getSelfFetchStatus());
            orderVO.setDeliveryChannelId(ocmsDeliveryInfoVO.getDeliveryChannelId());
            orderVO.setExpensiveProductPickupCode(ocmsDeliveryInfoVO.getExpensiveProductPickupCode());
            // 配送模式返回给前端，前端本地库进行筛选
            orderVO.setDeliveryTypeMode(DeliveryTypeModeConverter.getDeliveryTypeMode(
                    ocmsDeliveryInfoVO.getOriginalDistributeType(), ocmsDeliveryInfoVO.getDeliveryChannelId()));
        }
        String distributeStatusDesc = Objects.isNull(ocmsDeliveryInfoVO) ||Objects.isNull(ocmsDeliveryInfoVO.getDistributeStatus())|| Objects.isNull(DistributeStatusEnum.enumOf(ocmsDeliveryInfoVO.getDistributeStatus()))
                ? "" : DistributeStatusEnum.enumOf(ocmsDeliveryInfoVO.getDistributeStatus()).getDesc();
        orderVO.setDistributeStatusDesc(distributeStatusDesc.equals(UN_KNOW) ? "" : distributeStatusDesc);

        if (orderVO.getDeliveryMethod() != null && DistributeMethodEnum.STORE_DELIVERY.getValue() == orderVO.getDeliveryMethod()){
            orderVO.setDistributeStatusDesc("无需配送");
        }
        orderVO.setOrderStatus(ocmsOrderVO.getOrderStatus());
        OrderStatusEnum orderStatus = OrderStatusEnum.enumOf(ocmsOrderVO.getOrderStatus());
        orderVO.setOrderStatusDesc(orderStatus == null? "未知状态": orderStatus.getDesc());
        orderVO.setCreateTime(ocmsOrderVO.getCreateTime());
        // == 1 会出现NPE
        orderVO.setDeliveryOrderType(Integer.valueOf(1).equals(ocmsOrderVO.getIsBooking()) ? DeliveryOrderType.DELIVERY_BY_BOOK_TIME.getValue() : DeliveryOrderType.DELIVERY_RIGHT_NOW.getValue());
        orderVO.setDeliveryOrderTypeName(getDeliveryOrderTypeName(orderVO.getDeliveryOrderType()));
        orderVO.setUpdateTime(ocmsOrderVO.getUpdateTime());
        orderVO.setChannelExtraOrderId(ocmsOrderVO.getExtOrderId());
        //备注不为空且不为0才展示
        if (StringUtils.isNotEmpty(ocmsOrderVO.getComments()) && !ocmsOrderVO.getComments().equals("0")) {
            orderVO.setComments(ocmsOrderVO.getComments());
        }
        orderVO.setTotalOfflinePrice(ocmsOrderVO.getTotalOfflinePrice());
        orderVO.setSelfPickPullNewOrder(ocmsOrderVO.getSelfPickPullNewOrder());
        Map<String, OCMSOrderItemVO> orderItemMap = new HashMap<>();
        List<GiftVO> dhItemGiftVO = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ocmsOrderItemVOList)) {
            orderItemMap = ocmsOrderItemVOList.stream().filter(item -> StringUtils.isNotBlank(item.getInstoreSkuId2())).collect(Collectors.toMap(OCMSOrderItemVO::getInstoreSkuId2, item -> item, (k1, k2) -> k1));
            orderVO.setProductList(ocmsOrderItemVOList.stream().filter(Objects::nonNull)
                    .filter(item -> CommonUsedUtil.isNeedFilterGiftProduct(ocmsOrderVO.getTenantId(), item.getExtData()))
                    .map(ocmsOrderItemVO -> buildProductVO(ocmsOrderVO.getTenantId(), ocmsOrderVO.getShopId(), ocmsOrderItemVO, ocmsOrderItemVOList)).filter(Objects::nonNull).collect(Collectors.toList()));
            Integer itemCount = orderVO.getProductList().stream().filter(Objects::nonNull).mapToInt(ProductVO::getCount).sum();
            orderVO.setItemCount(itemCount);

            orderVO.setFuseProductList(ocmsOrderItemVOList.stream().filter(Objects::nonNull).map(ocmsOrderItemVO -> buildProductVO(ocmsOrderVO.getTenantId(), ocmsOrderVO.getShopId(), ocmsOrderItemVO, ocmsOrderItemVOList)).filter(Objects::nonNull).collect(Collectors.toList()));
            Integer fuseItemCount = orderVO.getFuseProductList().stream().filter(Objects::nonNull).mapToInt(ProductVO::getCount).sum();
            orderVO.setFuseItemCount(fuseItemCount);

            // 歪马使用
            if (MccConfigUtil.orderItemFuseSplitSwitch(ocmsOrderVO.getTenantId(), ocmsOrderVO.getShopId())) {
                dhItemGiftVO = ocmsOrderItemVOList.stream()
                        .filter(Objects::nonNull)
                        .filter(item -> CommonUsedUtil.isDhGiftProduct(ocmsOrderVO.getTenantId(), item.getExtData()))
                        .map(ocmsOrderItemVO -> buildDhGiftVO(orderVO.getStoreId(), ocmsOrderItemVO)).filter(Objects::nonNull).collect(Collectors.toList());
            }

        }else{
            orderVO.setItemCount(0);
            orderVO.setProductList(ocmsOrderItemVOList.stream().filter(Objects::nonNull)
                    .filter(item -> CommonUsedUtil.isNeedFilterGiftProduct(ocmsOrderVO.getTenantId(), item.getExtData()))
                    .map(ocmsOrderItemVO -> buildProductVO(ocmsOrderVO.getTenantId(), ocmsOrderVO.getShopId(), ocmsOrderItemVO, ocmsOrderItemVOList)).collect(Collectors.toList()));

            orderVO.setFuseItemCount(0);
            orderVO.setFuseProductList(ocmsOrderItemVOList.stream().filter(Objects::nonNull).map(ocmsOrderItemVO -> buildProductVO(ocmsOrderVO.getTenantId(), ocmsOrderVO.getShopId(), ocmsOrderItemVO, ocmsOrderItemVOList)).collect(Collectors.toList()));

        }
        List<GiftVO> giftVOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ocmsOrderVO.getOnlineGiftVOS())) {
            if (MccDynamicConfigUtil.getWmsjTenantIds().contains(ocmsOrderVO.getTenantId())) {
                giftVOList = ocmsOrderVO.getOnlineGiftVOS().stream().filter(Objects::nonNull).map(onlineGiftVO -> buildDhGiftVO(ocmsOrderVO.getShopId(), onlineGiftVO)).collect(Collectors.toList());
            } else {
                giftVOList = ocmsOrderVO.getOnlineGiftVOS().stream().filter(Objects::nonNull).map(this::buildGiftVO).collect(Collectors.toList());
            }
        }

        int giftCount = Objects.nonNull(ocmsOrderVO.getOnlineGiftVOS()) ? ocmsOrderVO.getOnlineGiftVOS().stream().filter(Objects::nonNull).mapToInt(OnlineGiftVO::getGiftQuantity).sum() : 0;
        if (CollectionUtils.isNotEmpty(dhItemGiftVO)) {
            giftVOList.addAll(dhItemGiftVO);
            giftCount += dhItemGiftVO.stream().filter(Objects::nonNull).filter(item->Objects.nonNull(item.getGiftQuantity())).mapToInt(GiftVO::getGiftQuantity).sum();
        }
        orderVO.setGiftVOList(giftVOList);
        orderVO.setGiftCount(giftCount);;

        Map<Integer, ProductLabelVO> productLabelVOMap = ProductLabelUtil.buildMapFromOCMSOrderItemVO(ocmsOrderItemVOList);
        //build refund info
        if (CollectionUtils.isNotEmpty(ocmsOrderVO.getAfterSaleApplyVOList())) {
            Map<Long, OCMSOrderItemVO> orderItemIdMap = ocmsOrderVO.getOcmsOrderItemVOList().stream()
                    .collect(Collectors.toMap(OCMSOrderItemVO::getOrderItemId, v -> v));
            Map<Long, CombinationChildProductVo> combinationChildProductMap = CollectionUtils
                    .isNotEmpty(ocmsOrderVO.getCombinationChildProductVoList())
                            ? ocmsOrderVO.getCombinationChildProductVoList().stream()
                                    .filter(v -> Objects.nonNull(v.getServiceId()))
                                    .collect(Collectors.toMap(CombinationChildProductVo::getServiceId, v -> v))
                            : new HashMap();
            Pair<Map<Long, OCMSOrderItemVO>, Map<Long, CombinationChildProductVo>> afsDetailBuildPair = new Pair<>(
                    orderItemIdMap, combinationChildProductMap);
            OrderRefundInfo orderRefundInfo = new OrderRefundInfo();
            removeInvalidAfterSaleApply(ocmsOrderVO, ocmsOrderVO.getAfterSaleApplyVOList());
            //只处理用户退款，不处理用户申述
            if (Objects.equals(OrderStatusEnum.REFUND_APPLIED.getValue(), ocmsOrderVO.getOrderStatus())) {
                OCMSAfterSaleApplyVO afterSaleApplyVO = Optional.ofNullable(ocmsOrderVO.getAfterSaleApplyVOList()).map(List::stream).orElse(Stream.empty())
                        .filter(e->e.isWait2Audit() || AfterSaleApplyStatusEnum.WAIT_ASSIGN.getValue().equals(e.getStatus())).findFirst().orElse(null);
                OrderStatusLog orderStatusLog = ocmsOrderVO.getOrderStatusLogList().stream()
                        .filter(e -> Objects.equals(OrderStatusEnum.REFUND_APPLIED.getValue(), e.getTargetStatus()))
                        .max(Comparator.comparingLong(e -> e.getCreateTime()))
                        .orElse(null);
                if (afterSaleApplyVO != null && orderStatusLog != null) {
                    //设置等待退款的申请，这里的待审批信息会关联订单状态，退差价不会改变订单状态
                    orderRefundInfo.setWaitAuditRefund(buildWaitAuditRefund(orderStatusLog, afterSaleApplyVO, orderItemMap, orderVO.getProductList(), productLabelVOMap, ocmsDeliveryInfoVO, afsDetailBuildPair));
                }
                // 设置多条待审核售后单
                if(MccConfigUtil.isShowWaitAuditRefundList()){
                    List<OCMSAfterSaleApplyVO> afterSaleApplyVOList = Optional.ofNullable(ocmsOrderVO.getAfterSaleApplyVOList()).map(List::stream).orElse(Stream.empty())
                            .filter(OCMSAfterSaleApplyVO::isWait2Audit).collect(Collectors.toList());
                    if (afterSaleApplyVO != null && orderStatusLog != null) {
                        //设置等待退款的申请列表，这里的待审批信息会关联订单状态，退差价不会改变订单状态
                        orderRefundInfo.setWaitAuditRefundList(buildWaitAuditRefundList(orderStatusLog, afterSaleApplyVOList, orderItemMap, orderVO.getProductList(), productLabelVOMap, ocmsDeliveryInfoVO, afsDetailBuildPair));
                    }
                }
            }
            //这里的退款信息会关联订单状态，退差价不会改变订单状态
            List<RefundLog> refundLogList = ocmsOrderVO.getOrderStatusLogList().stream().filter(e -> isRefundConcernStatusChange(e))
                    .map(orderStatusLog -> {
                        boolean isAuditRefund = Objects.equals(OrderStatusEnum.REFUND_APPLIED.getValue(), orderStatusLog.getSourceStatus());
                        OCMSAfterSaleApplyVO afterSaleApplyVO = findNearestTimeAfterSale(orderStatusLog.getCreateTime(), ocmsOrderVO.getAfterSaleApplyVOList(), isAuditRefund);
                        //只处理处理完的售后申请, 京东的暂存售后单也不显示给前端
                        if (afterSaleApplyVO != null && !afterSaleApplyVO.isWait2Audit()
                                && AfterSaleApplyStatusEnum.DRAFT.getValue().equals(afterSaleApplyVO.getStatus())
                                && AfterSaleApplyStatusEnum.DRAFT_DONE.getValue().equals(afterSaleApplyVO.getStatus())) {
                            return RefundLog.buildRefundLog(orderStatusLog, afterSaleApplyVO);
                        }
                        return null;
                    }).filter(Objects::nonNull).sorted(Comparator.comparingLong(RefundLog::getOptTime).reversed()).collect(Collectors.toList());
            //添加退差价信息,重排序
            refundLogList.addAll(addWeightRefundLog(ocmsOrderVO));
            refundLogList = refundLogList.stream().sorted(Comparator.comparingLong(RefundLog::getOptTime).reversed()).collect(Collectors.toList());
            orderRefundInfo.setRefundLogs(refundLogList);
            orderVO.setOrderRefundInfo(orderRefundInfo);
        }

        // 订单营收信息
        if(ocmsOrderVO.getOrderRevenueDetailVo() != null && ocmsOrderVO.getOrderRevenueDetailVo().getOrderAmountInfo() != null){
            OrderAmountInfo orderAmountInfo = ocmsOrderVO.getOrderRevenueDetailVo().getOrderAmountInfo();
            RevenueDetailVo revenueDetailVo = new RevenueDetailVo();
            revenueDetailVo.setPromotionInfos(ocmsOrderVO.getOrderRevenueDetailVo().getPromotionInfos());
            revenueDetailVo.setActualPayAmount(orderAmountInfo.getActualPayAmt());
            if(!Integer.valueOf(OrderSourceEnum.GLORY.getValue()).equals(ocmsOrderVO.getOrderSource())){
                // todo 牵牛花一期不返回活动分摊信息、二期适配后再放开
                revenueDetailVo.setBizActivityAmount(orderAmountInfo.getBizCharge());
            }
            revenueDetailVo.setDeliveryAmount(orderAmountInfo.getDeliveryFee());
            revenueDetailVo.setPackageAmount(orderAmountInfo.getPackageAmount());
            revenueDetailVo.setRevenueAmount(orderAmountInfo.getBizReceiveAmount());
            if (orderProfit != null) {
                revenueDetailVo.setNetProfitOnline(orderProfit.getProfit().intValue());
                revenueDetailVo.setWithDeliveryCost(orderProfit.getWithDeliveryCost());
            }
            orderVO.setRevenueDetail(revenueDetailVo);
        }
        orderVO.setUserTags(UserTagTypeEnum.getTags(ocmsOrderVO.getTags()));
        orderVO.setIsNeedInvoice(ocmsOrderVO.getIsNeedInvoice());
        orderVO.setActionTagList(ActionTagConverter.ocmsOrderVoConvertTag(ocmsOrderVO));
        orderVO.setOrderUserType(ocmsOrderVO.getUserType());
        orderVO.setMigrateFlag(ocmsOrderVO.getMigrateFlag());
        // 开票信息
        orderVO.setInvoiceTitle(ocmsOrderVO.getInvoiceTitle());
        orderVO.setInvoiceType(ocmsOrderVO.getInvoiceType());
        orderVO.setInvoiceTaxNo(ocmsOrderVO.getInvoiceTaxNo());
        if(ocmsOrderVO.getOcmsDeliveryInfoVO()!=null){
            orderVO.setOriginalDistributeType(ocmsOrderVO.getOcmsDeliveryInfoVO().getOriginalDistributeType());
        }
        orderVO.setOrderTagList(OrderTagVO.convertOrderTagList(ocmsOrderVO, showLabelList));
        orderVO.setSortedTagList(SortedTagVO.convertSortedTagList(ocmsOrderVO, com.sankuai.shangou.qnh.orderapi.utils.pc.MccConfigUtil.getNeedSortOrderLabelIds(), showLabelList));
        orderVO.setToken(OrderUtils.generateMD5Token(ocmsOrderVO.getViewOrderId(), channelId, ocmsOrderVO.getTenantId()));
        orderVO.setViewFuseOrderStatus(buildViewFuseOrderStatus(ocmsOrderVO));
        orderVO.setLastChangeTime(ocmsOrderVO.getLastChangeTime());
        if(isDrunkHorseTenant(ocmsOrderVO.getTenantId()) && MccConfigUtil.getGrayWiderShippingAreaStores(ocmsOrderVO.getShopId())) {
            orderVO.setShowTags(ShowTag.buildShowTagList(ocmsOrderVO));
            if (Objects.equals(ocmsOrderVO.getOrderBizType(), OrderBizTypeEnum.MEITUAN_WAIMAI.getValue())) {
                orderVO.setReceiverTailPhoneNumber(FormatUtil.getLastNumCharacters(ocmsOrderVO.getRecipientPhone(), 4));
            } else if (Objects.equals(ocmsOrderVO.getOrderBizType(), OrderBizTypeEnum.MEITUAN_DRUNK_HOURSE.getValue())) {
                orderVO.setReceiverTailPhoneNumber(FormatUtil.getLastNumCharacters(ocmsOrderVO.getOcmsDeliveryInfoVO().getUserPhone(), 4));
            }
        }

        if(MccConfigUtil.checkIsFranchiseeFeePoi(ocmsOrderVO.getTenantId(), ocmsOrderVO.getShopId())
                && Objects.equals(ocmsOrderVO.getFranchiseeOrder(), FRANCHISEE_ORDER)) {
            orderVO.setBizReceiveAmt(ocmsOrderVO.getSettleAmount());
            orderVO.setIsFranchiseeOrder(true);
            fixHistoryFeeShow(orderVO, ocmsOrderVO);

        }
        orderVO.setIsFastOrder(ocmsOrderVO.getIsFastOrder());
        orderVO.setIsFastToSelfDelivery(ocmsOrderVO.getIsFastToSelfDelivery());
        orderVO.setCompensationModel(CompensationUtil.buildCompensationModel(ocmsOrderVO.getCompensation()));
        orderVO.setCompensationModelList(CompensationUtil.buildCompensationModelList(ocmsOrderVO.getCompensation()));
        orderVO.setFastDeliveryAmt(ocmsOrderVO.getFastDeliveryAmt());
        ExchangeItemUtil.fillRefundExchangeInfo(orderVO, ocmsOrderVO);
        orderVO.setDownFlag(ocmsOrderVO.getDownFlag());
        orderVO.setDegradeModules(ocmsOrderVO.getDegradeModules());
        orderVO.setDeliveryPosition(MccConfigUtil.buildDeliveryPosition(ocmsOrderVO.getDeliveryPosition()));
        orderVO.setStockOutInfo(ocmsOrderVO.getStockOutInfo());
        return orderVO;
    }

    /**
     * 收货人姓名处理
     *
     * @param ocmsOrderVO
     * @return
     */
    private String buildReceiverName(OCMSOrderVO ocmsOrderVO) {
        OCMSDeliveryInfoVO ocmsDeliveryInfoVO = ocmsOrderVO.getOcmsDeliveryInfoVO();
        // 订单完成或者取消超过24小时且订单无待审核退单，则进行脱敏用户
        if (OrderUtil.isOrderEnd(ocmsOrderVO.getOrderStatus()) && OrderUtil.isOverConfigTime(
                OrderUtil.getOrderTimeByLog(ocmsOrderVO.getOrderStatus(), ocmsOrderVO.getOrderStatusLogList()),
                MccDynamicConfigUtil.getDesensitizeReceiverInfoTime())) {
            return DesensitizeReceiverInfoUtil.desensitizeReceiverName(ocmsDeliveryInfoVO.getUserName());
        }
        return ocmsDeliveryInfoVO.getUserName();
    }

    /**
     * 收货人手机号尾号(真实手机号的尾号)处理
     *
     * @param ocmsOrderVO
     * @return
     */
    private String buildLastFourDigitsOfUser(OCMSOrderVO ocmsOrderVO) {
        OCMSDeliveryInfoVO ocmsDeliveryInfoVO = ocmsOrderVO.getOcmsDeliveryInfoVO();
        String userPrivacyPhone = ocmsDeliveryInfoVO.getUserPrivacyPhone();
        // 歪马租户的微商城渠道，收货人手机号和收货人隐私号取值相反
        if (MccConfigUtil.drunkHorseReceiverPhoneOpposite(ocmsOrderVO.getTenantId())
                && Objects.equals(ocmsOrderVO.getOrderBizType(), DynamicOrderBizType.MEITUAN_DRUNK_HOURSE.getValue())) {
            userPrivacyPhone = ocmsDeliveryInfoVO.getUserPhone();
        }
        if (StringUtils.isBlank(userPrivacyPhone) || userPrivacyPhone.length() <= 4) {
            return userPrivacyPhone;
        }
        return userPrivacyPhone.substring(userPrivacyPhone.length() - 4);
    }

    /**
     * 收货人手机号（隐私号）处理
     *
     * @param ocmsOrderVO
     * @return
     */
    private String buildReceiverPhone(OCMSOrderVO ocmsOrderVO) {
        OCMSDeliveryInfoVO ocmsDeliveryInfoVO = ocmsOrderVO.getOcmsDeliveryInfoVO();
        String receiverPhone = ocmsDeliveryInfoVO.getUserPhone();
        // 歪马租户的微商城渠道，收货人手机号和收货人隐私号取值相反
        if (MccConfigUtil.drunkHorseReceiverPhoneOpposite(ocmsOrderVO.getTenantId())
                && Objects.equals(ocmsOrderVO.getOrderBizType(), DynamicOrderBizType.MEITUAN_DRUNK_HOURSE.getValue())) {
            receiverPhone = ocmsDeliveryInfoVO.getUserPrivacyPhone();
        }
        // 美团名酒馆平台配送不展示收货人手机号
        if (BooleanUtils.isTrue(ocmsOrderVO.getIsMtFamousTavern())
                && Objects.equals(ocmsDeliveryInfoVO.getIsSelfDelivery(), 0)) {
            return null;
        }
        // 订单完成或者取消超过24小时且订单无待审核退单，则隐藏用户隐私号
        if (OrderUtil.isOrderEnd(ocmsOrderVO.getOrderStatus()) && OrderUtil.isOverConfigTime(
                OrderUtil.getOrderTimeByLog(ocmsOrderVO.getOrderStatus(), ocmsOrderVO.getOrderStatusLogList()),
                MccDynamicConfigUtil.getDesensitizeReceiverInfoTime())) {
            return null;
        }
        return receiverPhone;
    }

    /**
     * 收货人地址处理
     *
     * @param ocmsOrderVO
     * @return
     */
    private String buildReceiverAddress(OCMSOrderVO ocmsOrderVO) {
        OCMSDeliveryInfoVO ocmsDeliveryInfoVO = ocmsOrderVO.getOcmsDeliveryInfoVO();
        boolean selfMention = DistributeMethodEnum.STORE_DELIVERY.getDesc()
                .equals(ocmsDeliveryInfoVO.getDistributeMethodName());
        if (selfMention
                && Objects.equals(ocmsOrderVO.getOrderBizType(), DynamicOrderBizType.YOU_ZAN_MIDDLE.getValue())) {
            // 有赞渠道自提隐藏用户地址
            return CommonConstant.YOU_ZAN_STORE_DELIVERY_ADDRESS;
        }
        // 订单完成或者取消超过24小时且订单无待审核退单，则隐藏
        if (OrderUtil.isOrderEnd(ocmsOrderVO.getOrderStatus()) && OrderUtil.isOverConfigTime(
                OrderUtil.getOrderTimeByLog(ocmsOrderVO.getOrderStatus(), ocmsOrderVO.getOrderStatusLogList()),
                MccDynamicConfigUtil.getDesensitizeReceiverInfoTime())) {
            return CommonConstant.PRIVACY_PROTECT_ADDRESS;
        }
        return ocmsDeliveryInfoVO.getUserAddress();
    }

    /**
     * 2024-08-08 01:00:00全量前的加盟标识订单，若无新佣金实收字段则读老字段
     **/
    public void fixHistoryFeeShow(OrderVO order, OCMSOrderVO ocmsOrderVO) {
        try {
            if (order == null || order.getCreateTime() == null || ocmsOrderVO == null) {
                return;
            }
            Date createTime = new Date(order.getCreateTime());
            String targetDateTimeStr = "2024-08-08 01:00:00";
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date targetDateTime = sdf.parse(targetDateTimeStr);
            boolean isBefore = createTime.before(targetDateTime);
            if (isBefore && ocmsOrderVO.getSettleAmount() == null) {
                order.setIsFranchiseeOrder(false);
                order.setBizReceiveAmt(ocmsOrderVO.getMerchantAmount());
            }
        } catch (Exception e) {
            log.error("fixHistoryFeeShow error", e);
        }
    }

    private Integer buildViewFuseOrderStatus(OCMSOrderVO ocmsOrderVO) {
        if (Objects.nonNull(ocmsOrderVO.getViewFuseOrderStatus())){
            return ocmsOrderVO.getViewFuseOrderStatus();
        }
        if (Objects.isNull(ocmsOrderVO.getFuseOrderStatus())){
            return ocmsOrderVO.getOrderStatus();
        }
        if (Objects.equals(ocmsOrderVO.getFuseOrderStatus(), FuseOrderStatusEnum.SHIPPING.getValue()) && Objects.equals(ocmsOrderVO.getOcmsDeliveryInfoVO().getDeliveryStatus(), DeliveryStatusEnum.PICKING.getValue())){
            return FuseOrderStatusEnum.MERCHANT_CONFIRMED.getValue();
        }
        if (ocmsOrderVO.getFuseOrderStatus() != OrderStatusEnum.REFUND_APPLIED.getValue()) {
            return ocmsOrderVO.getFuseOrderStatus();
        }
        if (!Objects.equals(ocmsOrderVO.getOcmsDeliveryInfoVO().getDeliveryStatus(), DeliveryStatusEnum.PICKED.getValue())){
            return FuseOrderStatusEnum.MERCHANT_CONFIRMED.getValue();
        }
        if (Objects.nonNull(ocmsOrderVO.getOrderCompleteTime()) && ocmsOrderVO.getOrderCompleteTime() > 1000){
            return FuseOrderStatusEnum.COMPLETED.getValue();
        }
        if (ocmsOrderVO.getOcmsDeliveryInfoVO() != null && ocmsOrderVO.getOcmsDeliveryInfoVO().getSelfFetchStatus() != null){
            if (ocmsOrderVO.getOcmsDeliveryInfoVO().getSelfFetchStatus() == SelfFetchStatusEnum.WAIT_TO_SELF_FETCH.getValue()){
                return FuseOrderStatusEnum.WAIT_SELF_FETCH.getValue();
            }
            if (ocmsOrderVO.getOcmsDeliveryInfoVO().getSelfFetchStatus() == SelfFetchStatusEnum.SELF_FETCH_FINISH.getValue()){
                return FuseOrderStatusEnum.COMPLETED.getValue();
            }
        }
        return FuseOrderStatusEnum.SHIPPING.getValue();
    }


    public RefundApplyRecordVO buildRefundApplyRecordVO(OCMSWaitAuditOrderVO ocmsWaitAuditOrderVO, SaasCrmDataRemoteService.OrderProfitView orderProfit, List<OrderLabelModel> showLabelList) {
        RefundApplyRecordVO refundApplyRecordVO = new RefundApplyRecordVO();
        refundApplyRecordVO.setOrderVO(buildOrderVO(ocmsWaitAuditOrderVO, orderProfit, showLabelList));

        OCMSAfterSaleApplyVO ocmsAfterSaleApplyVO = ocmsWaitAuditOrderVO.getOcmsAfterSaleApplyVO();
        refundApplyRecordVO.setServiceId(ocmsAfterSaleApplyVO.getServiceId());
        refundApplyRecordVO.setAfterSaleId(ocmsAfterSaleApplyVO.getAfterSaleId());
        refundApplyRecordVO.setIsAudit(ocmsAfterSaleApplyVO.getIsAudit());
        refundApplyRecordVO.setStatus(ocmsAfterSaleApplyVO.getStatus());
        refundApplyRecordVO.setApplyReason(ocmsAfterSaleApplyVO.getApplyReason());
        refundApplyRecordVO.setAfsPattern(ocmsAfterSaleApplyVO.getAfsPattern());
        refundApplyRecordVO.setRefundAmt(ocmsAfterSaleApplyVO.getRefundAmt());
        refundApplyRecordVO.setCreateTime(ocmsAfterSaleApplyVO.getCreateTime());
        refundApplyRecordVO.setUpdateTime(ocmsAfterSaleApplyVO.getUpdateTime());
        refundApplyRecordVO.setAfsApplyType(ocmsAfterSaleApplyVO.getApplyType());
        refundApplyRecordVO.setWhoApplyType(ocmsAfterSaleApplyVO.getApplyUserType());
        refundApplyRecordVO.setDispatchShopId(ocmsAfterSaleApplyVO.getDispatchShopId());
        refundApplyRecordVO.setDealTime(ocmsAfterSaleApplyVO.getDealTime());
        //返货
        refundApplyRecordVO.setIsCanReturnGoods(ocmsAfterSaleApplyVO.getIsCanReturnGoods());
        List<OCMSAfterSaleApplyDetailVO> ocmsAfterSaleApplyDetailVOList = ocmsAfterSaleApplyVO.getOcmsAfterSaleApplyDetailVOList();
        ocmsWaitAuditOrderVO.getOcmsOrderItemVOList();
        Map<Integer, ProductLabelVO> productLabelVOMap = ProductLabelUtil.buildMapFromOCMSOrderItemVO(ocmsWaitAuditOrderVO.getOcmsOrderItemVOList());
        Map<Long, OCMSOrderItemVO> orderItemMap = ocmsWaitAuditOrderVO.getOcmsOrderItemVOList().stream()
                .collect(Collectors.toMap(OCMSOrderItemVO::getOrderItemId, v -> v));
        Map<Long, CombinationChildProductVo> combinationChildProductMap = org.apache.commons.collections.CollectionUtils
                .isNotEmpty(ocmsWaitAuditOrderVO.getCombinationChildProductVoList())
                        ? ocmsWaitAuditOrderVO.getCombinationChildProductVoList().stream()
                                .filter(v -> Objects.nonNull(v.getServiceId()))
                                .collect(Collectors.toMap(CombinationChildProductVo::getServiceId, v -> v))
                        : new HashMap();
        Pair<Map<Long, OCMSOrderItemVO>, Map<Long, CombinationChildProductVo>> afsDetailBuildPair = new Pair<>(
                orderItemMap, combinationChildProductMap);
        if (CollectionUtils.isNotEmpty(ocmsAfterSaleApplyDetailVOList)) {
            refundApplyRecordVO.setRefundApplyRecordDetailVOList(ocmsAfterSaleApplyDetailVOList.stream().filter(Objects::nonNull).map(item -> buildRefundApplyRecordDetailVO(item, productLabelVOMap, afsDetailBuildPair)).collect(Collectors.toList()));
            // 将商品图片赋值到售后信息中
            setRefundApplyDetailPicurl(refundApplyRecordVO);

            refundApplyRecordVO.setRefundProductCount(ocmsAfterSaleApplyDetailVOList.stream().filter(Objects::nonNull).mapToInt(OCMSAfterSaleApplyDetailVO::getCount).sum());
        }
        if (refundApplyRecordVO.getOrderVO() != null) {
            refundApplyRecordVO.setOrderRefundInfo(refundApplyRecordVO.getOrderVO().getOrderRefundInfo());
        }
        refundApplyRecordVO.setRefundPicList(ocmsAfterSaleApplyVO.getRefundPicList());

        if (ocmsWaitAuditOrderVO.getOcmsAfterSaleApplyVO().getApplyType() != null) {
            AfterSaleTypeEnum afterSaleTypeEnum = AfterSaleTypeEnum.enumOf(ocmsWaitAuditOrderVO.getOcmsAfterSaleApplyVO().getApplyType());
            refundApplyRecordVO.setRefundApplyType(WaitToAuditRefundGoodsOrderSubTypeEnum.getSubTypeCode(afterSaleTypeEnum));
            setWorryFreeReturn(ocmsAfterSaleApplyVO,refundApplyRecordVO);
        }
        refundApplyRecordVO.setIsNotImport(Objects.equals(ocmsAfterSaleApplyVO.getExchangeStatusType(),
                AfterSaleExchangeTypeEnum.NOT_IMPORT.getValue()));

        return refundApplyRecordVO;
    }


    /**
     * 插入退差价到退款日志流
     *
     * @param ocmsOrderVO
     * @return
     */
    private List<RefundLog> addWeightRefundLog(OCMSOrderVO ocmsOrderVO) {
        List<OCMSAfterSaleApplyVO> weightRefundAfterSaleApplyList = ocmsOrderVO.getAfterSaleApplyVOList().stream().filter(afs -> Objects.nonNull(afs.getAfsPattern())&&afs.getAfsPattern()== AfterSalePatternEnum.WEIGHT.getValue() && AfterSaleApplyStatusEnum.AUDITED.getValue().equals(afs.getStatus())).collect(Collectors.toList());
        List<RefundLog> refundLogs = Lists.newArrayList();
        for (OCMSAfterSaleApplyVO applyVO : weightRefundAfterSaleApplyList) {
            OperatorTypeEnum operatorTypeEnum = OperatorTypeEnum.enumOf(applyVO.getApplyUserType());
            RefundLog refundLog = new RefundLog();
            refundLog.setOperator("");
            refundLog.setOptTime(applyVO.getCreateTime());
            refundLog.setOptContent("商家按重量退差价");
            refundLog.setAuditType(OcmsRefundAuditType.WeightRefund.getCode());
            refundLog.setOperatorType(operatorTypeEnum.getValue());
            refundLog.setRefundAmount(applyVO.getRefundAmt());
            refundLog.setOptDesc(applyVO.getApplyReason());
            List<RefundApplyRecordDetailVO> refundApplyRecordDetailVOS = Lists.newArrayList();
            for (OCMSAfterSaleApplyDetailVO ocmsAfterSaleApplyDetailVO : applyVO.getOcmsAfterSaleApplyDetailVOList()) {
                RefundApplyRecordDetailVO recordDetailVO = RefundApplyRecordDetailVO.buildRefundApplyRecordDetailVO(ocmsAfterSaleApplyDetailVO);
                //如果是克重退款展示渠道退款价格，如果是部分退款老逻辑展示线下价格
                recordDetailVO.setRefundAmt(ocmsAfterSaleApplyDetailVO.getRefundAmt());
                recordDetailVO.setTotalRefundAmt(ocmsAfterSaleApplyDetailVO.getRefundAmt());
                refundApplyRecordDetailVOS.add(recordDetailVO);
            }
            refundLog.setRefundApplyRecordDetailVOList(refundApplyRecordDetailVOS);
            refundLog.setRefundPicList(applyVO.getRefundPicList());
            refundLogs.add(refundLog);
        }


        return refundLogs;
    }

    private boolean isDrunkHorseTenant(Long tenantId) {
        List<Long> drunkHorseTenants = Lion.getConfigRepository().getList("drunkhorse.tenantIds", Long.class, new ArrayList<>());
        if (tenantId == null || CollectionUtils.isEmpty(drunkHorseTenants)) {
            return false;
        }
        return drunkHorseTenants.contains(tenantId);
    }


    public ProductVO buildProductVO(long tenantId, long storeId, OCMSOrderItemVO ocmsOrderItemVO, List<OCMSOrderItemVO> allOCMSOrderItemVO) {
        ProductVO productVO = new ProductVO();
        productVO.setSkuId(ocmsOrderItemVO.getCustomerSkuId());
        productVO.setOrderItemId(ocmsOrderItemVO.getOrderItemId());
        productVO.setUpcCode(ocmsOrderItemVO.getSkuCode());
        productVO.setSkuName(ocmsOrderItemVO.getSkuName());
        productVO.setPicUrl(ocmsOrderItemVO.getPicUrl());
        productVO.setSpecification(ocmsOrderItemVO.getSpecification());
        productVO.setSellUnit(ocmsOrderItemVO.getSellUnit());
        productVO.setErpItemCode(ocmsOrderItemVO.getErpItemCode());
        productVO.setGoodsCode(ocmsOrderItemVO.getGoodsCode());


        ExchangeUtil.ExchangeDO exchangeDO = ExchangeUtil.loadExchangeMetaData(ocmsOrderItemVO.getExtData());
        //如果是换货商品，直接不展示，过滤掉
        if(exchangeDO.getExchangeSourceOrderItemId() > 0){
            return null;
        }
        productVO.setExchangeProductVoList(ExchangeItemUtil.getExchangeProductList(ocmsOrderItemVO, allOCMSOrderItemVO));
        int quantity = exchangeDO.getOrderQuantity() == 0?ocmsOrderItemVO.getQuantity():exchangeDO.getOrderQuantity();
        productVO.setCount(quantity);
        if (Objects.nonNull(ocmsOrderItemVO.getOriginalPrice())) {
            productVO.setOriginalTotalPrice(ocmsOrderItemVO.getOriginalPrice() * quantity);
        }
        productVO.setTotalPayAmount(ocmsOrderItemVO.getTotalPayAmount());
        productVO.setUnitPrice(ocmsOrderItemVO.getUnitPrice());

        productVO.setOrderItemOfflinePrice(ocmsOrderItemVO.getOfflinePrice());
        List<TagInfoVO> tagInfoVOS = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(ocmsOrderItemVO.getFulfillmentTagList())) {
            for (String fulfillmentTag : ocmsOrderItemVO.getFulfillmentTagList()) {
                TagInfoVO tagInfoVO = new TagInfoVO();
                tagInfoVO.setName(fulfillmentTag);
                tagInfoVO.setType(FULFILLMENT_TAG);
                tagInfoVOS.add(tagInfoVO);
            }
        }
        if (CollectionUtils.isNotEmpty(ocmsOrderItemVO.getPickTagList())) {
            for (String pickTag : ocmsOrderItemVO.getPickTagList()) {
                TagInfoVO tagInfoVO = new TagInfoVO();
                tagInfoVO.setName(pickTag);
                tagInfoVO.setType(PICK_TAG);
                tagInfoVOS.add(tagInfoVO);

            }
        }
        //商品标签(需要排除履约和拣货标签)
        productVO.setTagInfos(Optional.ofNullable(ocmsOrderItemVO.getTagInfos())
                .orElse(Collections.emptyList()).stream().map(tag -> {
                    TagInfoVO tagInfoVO = new TagInfoVO();
                    tagInfoVO.setName(tag.getName());
                    tagInfoVO.setType(tag.getType());
                    return tagInfoVO;
                }).collect(Collectors.toList()));
        productVO.setTagInfoList(tagInfoVOS);
        productVO.setCurrentPrice(ocmsOrderItemVO.getCurrentPrice());
        productVO.setSubProductVoList(CombinationProductUtil.getSubProductVoList(ocmsOrderItemVO.getCombinationChildProductList()));

        //为歪马填充新属性
        if (MccDynamicConfigUtil.getWmsjTenantIds().contains(tenantId) && MccDynamicConfigUtil.isNewPickGrayStore(storeId)) {
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(ocmsOrderItemVO.getPropertyList())) {
                List<PropertiesViewVO> propertiesViewVOList = ocmsOrderItemVO.getPropertyList().stream()
                        .filter(property -> MccDynamicConfigUtil.getPropertyColorMap().containsKey(property))
                        .map(property -> new PropertiesViewVO(property, MccDynamicConfigUtil.getPropertyColorMap().get(property)))
                        .collect(Collectors.toList());
                productVO.setParsedProperties(propertiesViewVOList);
            }
            if (!MccConfigUtil.orderItemFuseSplitSwitch(tenantId, storeId)) {
                //歪马tags不需要了,用parsedProperty代替
                productVO.setTagInfos(Lists.newArrayList());
            }
        }
        productVO.setMultiPicUrl(processMultiPicUrl(ocmsOrderItemVO.getPicUrl()));

        // extData确认是否为赠品，设置赠品参数 0-平台赠品，1-自定义赠品
        productVO.setGiftType(OrderUtils.getExtDataAsInt("giftType", ocmsOrderItemVO.getExtData()));
        productVO.setBelongSkuId(OrderUtils.getExtDataAsString("mainSkuId", ocmsOrderItemVO.getExtData()));
        productVO.setChannelLabelList(ProductLabelUtil.buildChannelLabelVOList(ocmsOrderItemVO.getChannelLabelList()));
        productVO.setLabelSubDesc(ProductLabelUtil.buildChannelLabelSubDesc(ocmsOrderItemVO.getChannelLabelList(), ocmsOrderItemVO.getExtData()));
        return productVO;
    }

    private static List<String> processMultiPicUrl(String picUrls) {
        if (Strings.isEmpty(picUrls)) {
            return new ArrayList<String>();
        }
        return Arrays.asList(picUrls.split(SYMBOL_COMMA));
    }

    public GiftVO buildDhGiftVO(Long storeId, OnlineGiftVO onlineGiftVO) {
        if (onlineGiftVO != null) {
            GiftVO giftVO = new GiftVO();
            giftVO.setGiftName(onlineGiftVO.getGiftName());
            giftVO.setGiftQuantity(onlineGiftVO.getGiftQuantity());
            giftVO.setBelongSkuId(onlineGiftVO.getMainSkuId());
            giftVO.setSku(giftVO.getSku());
            if (MccDynamicConfigUtil.isNewPickGrayStore(storeId) && StringUtils.isNotBlank(onlineGiftVO.getGiftSpec())) {
                giftVO.setSpecification(onlineGiftVO.getGiftSpec());
            }
            return giftVO;
        }
        return null;
    }

    public GiftVO buildDhGiftVO(Long storeId, OCMSOrderItemVO ocmsOrderItemVO) {
        if (ocmsOrderItemVO != null) {
            GiftVO giftVO = new GiftVO();
            giftVO.setGiftName(ocmsOrderItemVO.getSkuName());
            giftVO.setGiftQuantity(ocmsOrderItemVO.getQuantity());
            giftVO.setBelongSkuId(OrderUtils.getExtDataAsString("mainSkuId", ocmsOrderItemVO.getExtData()));
            giftVO.setSku(giftVO.getSku());
            if (MccDynamicConfigUtil.isNewPickGrayStore(storeId) && StringUtils.isNotBlank(ocmsOrderItemVO.getSpecification())) {
                giftVO.setSpecification(ocmsOrderItemVO.getSpecification());
            }
            return giftVO;
        }
        return null;
    }


    private void removeInvalidAfterSaleApply(OCMSOrderVO ocmsOrderVO, List<OCMSAfterSaleApplyVO> afterSaleApplyVOList) {
        if (CollectionUtils.isNotEmpty(afterSaleApplyVOList)){
            Iterator<OCMSAfterSaleApplyVO> it = afterSaleApplyVOList.iterator();
            while(it.hasNext()){
                OCMSAfterSaleApplyVO applyVO = it.next();
                if (StringUtils.isBlank(applyVO.getAfterSaleId()) || applyVO.getCreateTime() == null || applyVO.getUpdateTime() == null || applyVO.getOrderId() == null){
                    log.info("售后索引，缺少必要信息，可能是Es索引写入延迟问题,serviceId:{}, order:{}", applyVO.getServiceId(), ocmsOrderVO.getViewOrderId());
                    it.remove();
                    MetricHelper.build().name("order.afterSaleRecordInvalid.err").tag("tenantId", String.valueOf(ocmsOrderVO.getTenantId())).tag("storeId", String.valueOf(ocmsOrderVO.getShopId())).count();
                }
            }
        }
    }

    /**
     * !!! 重要 操作同样需要在 buildWaitAuditRefundList中添加
     * @param orderStatusLog
     * @param afterSaleApplyVO
     * @param orderItemMap
     * @param productList
     * @return
     */
    private RefundingRecordVO buildWaitAuditRefund(OrderStatusLog orderStatusLog, OCMSAfterSaleApplyVO afterSaleApplyVO, Map<String, OCMSOrderItemVO> orderItemMap, List<ProductVO> productList, Map<Integer, ProductLabelVO> productLabelVOMap, OCMSDeliveryInfoVO ocmsDeliveryInfoVO, Pair<Map<Long, OCMSOrderItemVO>, Map<Long, CombinationChildProductVo>> afsDetailBuildPair) {
        RefundingRecordVO refundApplyRecordVO = new RefundingRecordVO();
        refundApplyRecordVO.setServiceId(afterSaleApplyVO.getServiceId());
        refundApplyRecordVO.setAfterSaleId(afterSaleApplyVO.getAfterSaleId());
        refundApplyRecordVO.setIsAudit(afterSaleApplyVO.getIsAudit());
        refundApplyRecordVO.setStatus(afterSaleApplyVO.getStatus());
        refundApplyRecordVO.setApplyReason(afterSaleApplyVO.getApplyReason());
        refundApplyRecordVO.setAfsPattern(afterSaleApplyVO.getAfsPattern());
        refundApplyRecordVO.setCreateTime(afterSaleApplyVO.getCreateTime());
        refundApplyRecordVO.setUpdateTime(afterSaleApplyVO.getUpdateTime());
        refundApplyRecordVO.setAfsApplyType(afterSaleApplyVO.getApplyType());
        refundApplyRecordVO.setWhoApplyType(afterSaleApplyVO.getApplyUserType());
        refundApplyRecordVO.setAssignShopId(afterSaleApplyVO.getDispatchShopId());
        refundApplyRecordVO.setRefundAmtTotal(afterSaleApplyVO.getRefundAmt());
        refundApplyRecordVO.setRefundOptContent(RefundLog.buildRefundContent(orderStatusLog, afterSaleApplyVO));
        Map<Long, SubProductVo> serviceId2SubProduct = productList.stream().map(ProductVO::getSubProductVoList).filter(CollectionUtils::isNotEmpty).flatMap(List::stream).collect(Collectors.toMap(SubProductVo::getServiceId, Function.identity(), (v1, v2) -> v2));

        if (afterSaleApplyVO.getApplyType() != null) {
            AfterSaleTypeEnum afterSaleTypeEnum = AfterSaleTypeEnum.enumOf(afterSaleApplyVO.getApplyType());
            refundApplyRecordVO.setRefundApplyType(WaitToAuditRefundGoodsOrderSubTypeEnum.getSubTypeCode(afterSaleTypeEnum));
        }
        List<OCMSAfterSaleApplyDetailVO> ocmsAfterSaleApplyDetailVOList = afterSaleApplyVO.getOcmsAfterSaleApplyDetailVOList();
        if (CollectionUtils.isNotEmpty(ocmsAfterSaleApplyDetailVOList)) {
            List<RefundApplyRecordDetailVO> refundApplyRecordDetailVOList = ocmsAfterSaleApplyDetailVOList.stream().filter(Objects::nonNull).map(item -> buildRefundApplyRecordDetailVO(item, productLabelVOMap, afsDetailBuildPair)).collect(Collectors.toList());
            // 将订单中的商品信息赋值到售后信息中
            if(MapUtils.isNotEmpty(orderItemMap)){
                refundApplyRecordDetailVOList.stream().forEach(item -> {
                    if(orderItemMap.containsKey(item.getSkuId())){
                        item.setPicUrl(orderItemMap.get(item.getSkuId()).getPicUrl());
                        item.setOrderItemOfflinePrice(orderItemMap.get(item.getSkuId()).getOfflinePrice());
                        item.setTagInfos(orderItemMap.get(item.getSkuId()).getTagInfos());

                        if (CollectionUtils.isNotEmpty(item.getSubProductVoList())){
                            item.getSubProductVoList().forEach(v -> v.setName(Optional.ofNullable(serviceId2SubProduct.get(v.getServiceId())).map(SubProductVo::getName).orElse(StringUtils.EMPTY)));
                        }
                    }
                });
            }
            refundApplyRecordVO.setRefundApplyRecordDetailVOList(refundApplyRecordDetailVOList);
            refundApplyRecordVO.setRefundProductCount(ocmsAfterSaleApplyDetailVOList.stream().filter(Objects::nonNull).mapToInt(OCMSAfterSaleApplyDetailVO::getCount).sum());
            // 退款总金额计算
            if(CollectionUtils.isNotEmpty(refundApplyRecordVO.getRefundApplyRecordDetailVOList())){
                refundApplyRecordVO.setRefundAmt(refundApplyRecordVO.getRefundApplyRecordDetailVOList()
                        .stream().filter(Objects::nonNull)
                        .mapToInt(RefundApplyRecordDetailVO::getTotalRefundAmt)
                        .filter(Objects::nonNull).sum());
            }
        }
        refundApplyRecordVO.setRefundPicList(afterSaleApplyVO.getRefundPicList());
        setWorryFreeReturn(afterSaleApplyVO,refundApplyRecordVO);
        setWarnDurtion(afterSaleApplyVO,refundApplyRecordVO);
        refundApplyRecordVO.setProcessDeadline(afterSaleApplyVO.getProcessDeadline());
        refundApplyRecordVO.setReturnGoodsStatus(afterSaleApplyVO.getReturnGoodsStatus());
        refundApplyRecordVO.setChannelExtRefundType(afterSaleApplyVO.getChannelExtRefundType());
        refundApplyRecordVO.setRefundGoodWay(afterSaleApplyVO.getRefundGoodWay());
        refundApplyRecordVO.setRefundGoodFreightType(afterSaleApplyVO.getRefundGoodFreightType());
        if (Objects.nonNull(afterSaleApplyVO.getPickUpRefundGoodsAddress()) && !afterSaleApplyVO.getPickUpRefundGoodsAddress().equals(ocmsDeliveryInfoVO.getUserAddress())) {
            refundApplyRecordVO.setPickUpRefundGoodsAddress(afterSaleApplyVO.getPickUpRefundGoodsAddress());
        }
        refundApplyRecordVO.setIsNotImport(Objects.equals(afterSaleApplyVO.getExchangeStatusType(),
                AfterSaleExchangeTypeEnum.NOT_IMPORT.getValue()));
        return refundApplyRecordVO;
    }

    private void setWarnDurtion(OCMSAfterSaleApplyVO ocmsAfterSaleVO, RefundingRecordVO refundingRecordVO) {
        try {
            refundingRecordVO.setCurrentTime(Instant.now().getMillis());
            final String channelTimeOut = Lion.getConfigRepository().get("channel.aftersale.timeout");
            if (StringUtils.isEmpty(channelTimeOut)) {
                return;
            }
            //渠道超时映射
            final Map<String, Map<String, Map<String, String>>> channelTimeOutMap = JacksonUtils.fromJson(channelTimeOut, new TypeReference<Map<String, Map<String, Map<String, String>>>>() {
            });
            //售后类型超时映射
            final Map<String, Map<String, String>> bizTypeMap = channelTimeOutMap.getOrDefault(String.valueOf(ocmsAfterSaleVO.getOrderBizType()), new HashMap<>());
            //状态超时映射，使用状态区分一审二审
            final Map<String, String> afsApplyTypeMap = bizTypeMap.getOrDefault(String.valueOf(refundingRecordVO.getAfsApplyType()), new HashMap<>());
            //租户单独配置
            final String tenantDurationTimeout = afsApplyTypeMap.get(ocmsAfterSaleVO.getTenantId() + "-" + ocmsAfterSaleVO.getStatus());
            if (StringUtils.isEmpty(tenantDurationTimeout)) {
                final String defaultWarnDuration = afsApplyTypeMap.get(String.valueOf(ocmsAfterSaleVO.getStatus()));
                refundingRecordVO.setWarnDuration(StringUtils.isEmpty(defaultWarnDuration) ? null : Long.valueOf(defaultWarnDuration));
            } else {
                refundingRecordVO.setWarnDuration(Long.valueOf(tenantDurationTimeout));
            }
        } catch (Exception e) {
            log.error("set warn duration error", e);
        }
    }

    private void setWorryFreeReturn(OCMSAfterSaleApplyVO item, RefundApplyRecordVO refundApplyRecordVO) {
        AfterSaleApplyStatusEnum afterSaleApplyStatusEnum = AfterSaleApplyStatusEnum.enumof(item.getStatus());
        AfterSaleTypeEnum afterSaleTypeEnum = AfterSaleTypeEnum.enumOf(item.getApplyType());
        if (afterSaleApplyStatusEnum != null && item.getOrderBizType()== OrderBizTypeEnum.MEITUAN_WAIMAI.getValue()
                && (afterSaleApplyStatusEnum == COMMIT || afterSaleApplyStatusEnum == FIRST_AUDIT_ING || isChannelPassiveAfs(item))) {
            if (afterSaleTypeEnum != null && afterSaleTypeEnum == AfterSaleTypeEnum.REFUND_GOODS) {
                refundApplyRecordVO.setDirectRefundFlag(1);
            }
        }
        refundApplyRecordVO.setPreReturnFreight(item.getPreReturnFreight());
    }

    private void setWorryFreeReturn(OCMSAfterSaleApplyVO item, RefundingRecordVO refundApplyRecordVO) {
        AfterSaleApplyStatusEnum afterSaleApplyStatusEnum = AfterSaleApplyStatusEnum.enumof(item.getStatus());
        AfterSaleTypeEnum afterSaleTypeEnum = AfterSaleTypeEnum.enumOf(item.getApplyType());
        if (afterSaleApplyStatusEnum != null && item.getOrderBizType()== OrderBizTypeEnum.MEITUAN_WAIMAI.getValue()
                && (afterSaleApplyStatusEnum == COMMIT || afterSaleApplyStatusEnum == FIRST_AUDIT_ING || isChannelPassiveAfs(item))) {
            if (afterSaleTypeEnum != null && afterSaleTypeEnum == AfterSaleTypeEnum.REFUND_GOODS) {
                refundApplyRecordVO.setDirectRefundFlag(1);
            }
        }
        refundApplyRecordVO.setPreReturnFreight(item.getPreReturnFreight());
    }



    /**
     * 是否是渠道消极处理的售后单
     * @param item 售后单信息
     * @return
     */
    private boolean isChannelPassiveAfs(OCMSAfterSaleApplyVO item){
        // 是否支持消极处理的售后单
        boolean isSupprott = com.sankuai.shangou.qnh.orderapi.utils.pc.MccConfigUtil.isSupportMtFirstAutoRefundGoods(item.getTenantId());
        if(!isSupprott){
            return false;
        }
        Integer firstAutoNegoType = item.getFirstAutoNegoType();
        // 非消极处理的退单
        if(Objects.isNull(firstAutoNegoType) || firstAutoNegoType != 1){
            return false;
        }
        // 消极处理的退单，需要判断【退单状态=初审同意】和【退货状态=待用户呼叫骑手】
        return Objects.equals(FIRST_AUDITED.getValue(), item.getStatus()) && Objects.equals(ReturnGoodsStatusEnum.WAITING_FOR_RIDER.getCode(), item.getReturnGoodsStatus());
    }

    private List<RefundingRecordVO> buildWaitAuditRefundList(OrderStatusLog orderStatusLog, List<OCMSAfterSaleApplyVO> afterSaleApplyVOList, Map<String, OCMSOrderItemVO> orderItemMap, List<ProductVO> productList, Map<Integer, ProductLabelVO> productLabelVOMap, OCMSDeliveryInfoVO ocmsDeliveryInfoVO, Pair<Map<Long, OCMSOrderItemVO>, Map<Long, CombinationChildProductVo>> afsDetailBuildPair) {
        List<RefundingRecordVO> refundingRecordVOList = new ArrayList<>();
        Map<Long, SubProductVo> serviceId2SubProduct = productList.stream().map(ProductVO::getSubProductVoList).filter(CollectionUtils::isNotEmpty).flatMap(List::stream).collect(Collectors.toMap(SubProductVo::getServiceId, Function.identity(), (v1, v2) -> v2));
        Optional.ofNullable(afterSaleApplyVOList).map(List::stream).orElse(Stream.empty()).forEach(afterSaleApplyVO->{
            RefundingRecordVO refundApplyRecordVO = new RefundingRecordVO();
            refundApplyRecordVO.setServiceId(afterSaleApplyVO.getServiceId());
            refundApplyRecordVO.setAfterSaleId(afterSaleApplyVO.getAfterSaleId());
            refundApplyRecordVO.setIsAudit(afterSaleApplyVO.getIsAudit());
            refundApplyRecordVO.setStatus(afterSaleApplyVO.getStatus());
            refundApplyRecordVO.setApplyReason(afterSaleApplyVO.getApplyReason());
            refundApplyRecordVO.setAfsPattern(afterSaleApplyVO.getAfsPattern());
            refundApplyRecordVO.setCreateTime(afterSaleApplyVO.getCreateTime());
            refundApplyRecordVO.setUpdateTime(afterSaleApplyVO.getUpdateTime());
            refundApplyRecordVO.setAfsApplyType(afterSaleApplyVO.getApplyType());
            refundApplyRecordVO.setWhoApplyType(afterSaleApplyVO.getApplyUserType());
            refundApplyRecordVO.setAssignShopId(afterSaleApplyVO.getDispatchShopId());
            refundApplyRecordVO.setRefundAmtTotal(afterSaleApplyVO.getRefundAmt());
            refundApplyRecordVO.setRefundOptContent(RefundLog.buildRefundContent(orderStatusLog, afterSaleApplyVO));
            if (afterSaleApplyVO.getApplyType() != null) {
                AfterSaleTypeEnum afterSaleTypeEnum = AfterSaleTypeEnum.enumOf(afterSaleApplyVO.getApplyType());
                refundApplyRecordVO.setRefundApplyType(WaitToAuditRefundGoodsOrderSubTypeEnum.getSubTypeCode(afterSaleTypeEnum));
            }
            List<OCMSAfterSaleApplyDetailVO> ocmsAfterSaleApplyDetailVOList = afterSaleApplyVO.getOcmsAfterSaleApplyDetailVOList();
            if (CollectionUtils.isNotEmpty(ocmsAfterSaleApplyDetailVOList)) {
                List<RefundApplyRecordDetailVO> refundApplyRecordDetailVOList = ocmsAfterSaleApplyDetailVOList.stream().filter(Objects::nonNull).map(item -> buildRefundApplyRecordDetailVO(item, productLabelVOMap, afsDetailBuildPair)).collect(Collectors.toList());
                // 将订单中的商品信息赋值到售后信息中
                if(MapUtils.isNotEmpty(orderItemMap)){
                    refundApplyRecordDetailVOList.stream().forEach(item -> {
                        if(orderItemMap.containsKey(item.getSkuId())){
                            item.setPicUrl(orderItemMap.get(item.getSkuId()).getPicUrl());
                            item.setOrderItemOfflinePrice(orderItemMap.get(item.getSkuId()).getOfflinePrice());
                            item.setTagInfos(orderItemMap.get(item.getSkuId()).getTagInfos());
                            if (CollectionUtils.isNotEmpty(item.getSubProductVoList())){
                               item.getSubProductVoList().forEach(v -> v.setName(Optional.ofNullable(serviceId2SubProduct.get(v.getServiceId())).map(SubProductVo::getName).orElse(StringUtils.EMPTY)));
                            }
                        }
                    });
                }
                refundApplyRecordVO.setRefundApplyRecordDetailVOList(refundApplyRecordDetailVOList);
                refundApplyRecordVO.setRefundProductCount(ocmsAfterSaleApplyDetailVOList.stream().filter(Objects::nonNull).mapToInt(OCMSAfterSaleApplyDetailVO::getCount).sum());
                // 退款总金额计算
                if(CollectionUtils.isNotEmpty(refundApplyRecordVO.getRefundApplyRecordDetailVOList())){
                    refundApplyRecordVO.setRefundAmt(refundApplyRecordVO.getRefundApplyRecordDetailVOList()
                            .stream().filter(Objects::nonNull)
                            .mapToInt(RefundApplyRecordDetailVO::getTotalRefundAmt)
                            .filter(Objects::nonNull).sum());
                }
            }
            refundApplyRecordVO.setRefundPicList(afterSaleApplyVO.getRefundPicList());
            refundApplyRecordVO.setProcessDeadline(afterSaleApplyVO.getProcessDeadline());
            refundApplyRecordVO.setReturnGoodsStatus(afterSaleApplyVO.getReturnGoodsStatus());
            refundApplyRecordVO.setChannelExtRefundType(afterSaleApplyVO.getChannelExtRefundType());
            refundApplyRecordVO.setRefundGoodWay(afterSaleApplyVO.getRefundGoodWay());
            refundApplyRecordVO.setRefundGoodFreightType(afterSaleApplyVO.getRefundGoodFreightType());
            if (Objects.nonNull(afterSaleApplyVO.getPickUpRefundGoodsAddress()) && !afterSaleApplyVO.getPickUpRefundGoodsAddress().equals(ocmsDeliveryInfoVO.getUserAddress())) {
                refundApplyRecordVO.setPickUpRefundGoodsAddress(afterSaleApplyVO.getPickUpRefundGoodsAddress());
            }
            setWorryFreeReturn(afterSaleApplyVO,refundApplyRecordVO);
            setWarnDurtion(afterSaleApplyVO,refundApplyRecordVO);
            refundApplyRecordVO.setIsNotImport(Objects.equals(afterSaleApplyVO.getExchangeStatusType(),
                    AfterSaleExchangeTypeEnum.NOT_IMPORT.getValue()));
            refundingRecordVOList.add(refundApplyRecordVO);
        });
        return refundingRecordVOList;
    }

    /**
     * 判断是否支持拨打骑手隐私号（目前仅平台配送支持查询骑手隐私号）
     *
     * @param isSelfDelivery 是否为商家自配送 1:是  0:否
     * @return 是否支持拨打骑手隐私号
     */
    private static boolean judgeSupportDeliveryUserPrivacyPhone(Integer isSelfDelivery) {
        if (Objects.equals(isSelfDelivery, IS_SELF_DELIVERY_NO)) {
            // 平台配送，支持查询骑手隐私号
            return true;
        }
        return false;
    }

    private boolean isRefundConcernStatusChange(OrderStatusLog e) {
        if (e != null) {
            //不展示申述
            return Objects.equals(OrderStatusEnum.REFUND_APPLIED.getValue(), e.getSourceStatus()) || Objects.equals(OrderStatusEnum.REFUND_APPLIED.getValue(), e.getTargetStatus());
        }
        return false;
    }

    public GiftVO buildGiftVO(OnlineGiftVO onlineGiftVO) {
        if (onlineGiftVO != null) {
            GiftVO giftVO = new GiftVO();
            giftVO.setGiftName(onlineGiftVO.getGiftName());
            giftVO.setGiftQuantity(onlineGiftVO.getGiftQuantity());
            return giftVO;
        }
        return null;
    }

    private OCMSAfterSaleApplyVO findNearestTimeAfterSale(Long orderStatusCreateTime, List<OCMSAfterSaleApplyVO> afterSaleApplyVOList, boolean isAuditRefund) {
        return afterSaleApplyVOList.stream()
                .filter(e -> !(isAuditRefund && e.isWait2Audit()))//如果是审批类型orderStatusLog，去掉那些还在处于退款审批中的记录
                .min(Comparator.comparingDouble(e -> {
                    long time = isAuditRefund ? e.getUpdateTime() : e.getCreateTime();
                    return Math.abs(time - orderStatusCreateTime);
                }))
                .orElse(null);
    }

    private RefundApplyRecordDetailVO buildRefundApplyRecordDetailVO(OCMSAfterSaleApplyDetailVO ocmsAfterSaleApplyDetailVO, Map<Integer, ProductLabelVO> productLabelVOMap, Pair<Map<Long, OCMSOrderItemVO>, Map<Long, CombinationChildProductVo>> afsDetailBuildPair) {
        return RefundApplyRecordDetailVO.buildRefundApplyRecordDetailVO(ocmsAfterSaleApplyDetailVO, productLabelVOMap, afsDetailBuildPair);
    }

    private String getDeliveryOrderTypeName(Integer deliveryOrderType) {
        DeliveryOrderType deliveryOrderTypeEnum = DeliveryOrderType.findByValue(deliveryOrderType);
        if (Objects.isNull(deliveryOrderTypeEnum)) {
            return "未知";
        }
        switch (deliveryOrderTypeEnum) {
            case DELIVERY_RIGHT_NOW:
                return "立即送达";
            case DELIVERY_BY_BOOK_TIME:
                return "预订";
            default:
                return "未知";
        }
    }

    private boolean isDeliveryInfoNotNull(OCMSDeliveryInfoVO ocmsDeliveryInfoVO) {
        return ocmsDeliveryInfoVO != null &&
                ocmsDeliveryInfoVO.getCreateTime() != null &&
                ocmsDeliveryInfoVO.getOrderId() != null &&
                ocmsDeliveryInfoVO.getDeliveryStatus() != null &&
                ocmsDeliveryInfoVO.getTenantId() != null &&
                ocmsDeliveryInfoVO.getShopId() != null;
    }

    public void setRefundPriceDisplayType4OrderList(List<OCMSOrderVO> ocmsOrderVOS, int type) {
        if(CollectionUtils.isEmpty(ocmsOrderVOS)){
            return;
        }
        ocmsOrderVOS.stream()
                .filter(Objects::nonNull)
                .map(OCMSOrderVO::getAfterSaleApplyVOList)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .map(OCMSAfterSaleApplyVO::getOcmsAfterSaleApplyDetailVOList)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .forEach(detailVO -> detailVO.setPriceDisplayType(type));
    }

    public void setRefundPriceDisplayType4WaitAuditOrderList(List<OCMSWaitAuditOrderVO> waitAuditOrderVOS, int type) {
        waitAuditOrderVOS.stream()
                .filter(Objects::nonNull)
                .map(auditOrder -> {
                    List<OCMSAfterSaleApplyVO> afterSaleList = new LinkedList<>();
                    if(auditOrder.getOcmsAfterSaleApplyVO() != null){
                        afterSaleList.add(auditOrder.getOcmsAfterSaleApplyVO());
                    }
                    if(CollectionUtils.isNotEmpty(auditOrder.getAfterSaleApplyVOList())){
                        afterSaleList.addAll(auditOrder.getAfterSaleApplyVOList());
                    }
                    return afterSaleList;
                })
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .map(OCMSAfterSaleApplyVO::getOcmsAfterSaleApplyDetailVOList)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .forEach(detailVO -> detailVO.setPriceDisplayType(type));
    }

    private void setRefundApplyDetailPicurl(RefundApplyRecordVO refundApplyRecordVO){
        final Map<String, ProductVO> productItemMap = new HashMap<>();
        OrderVO orderVO = refundApplyRecordVO.getOrderVO();
        if (orderVO != null && CollectionUtils.isNotEmpty(orderVO.getProductList())) {
            productItemMap.putAll(orderVO.getProductList().stream().filter(item -> StringUtils.isNotBlank(item.getSkuId())).collect(Collectors.toMap(ProductVO::getSkuId, item -> item, (k1, k2) -> k1)));
        }
        if(MapUtils.isNotEmpty(productItemMap)){
            refundApplyRecordVO.getRefundApplyRecordDetailVOList().stream().forEach(item -> {
                item.setPicUrl(productItemMap.containsKey(item.getSkuId()) ? productItemMap.get(item.getSkuId()).getPicUrl() : null);
            });
        }
    }

    public void setRefundApplyListResponseViewStatus(RefundApplyListResponse response) {
        if (Objects.isNull(response) || CollectionUtils.isEmpty(response.getRefundApplyRecordVOList())) {
            return;
        }
        for (RefundApplyRecordVO each : response.getRefundApplyRecordVOList()) {
            if (Objects.isNull(each.getAfsPattern()) || Objects.isNull(each.getOrderVO())) {
                continue;
            }
            if (Objects.equals(RefundTypeEnum.ALL.getValue(), each.getAfsPattern())) {
                each.getOrderVO().setViewStatus(OrderViewStatusEnum.ALL_REFUND.getCode());
            } else if (Objects.equals(RefundTypeEnum.PART.getValue(), each.getAfsPattern())) {
                each.getOrderVO().setViewStatus(OrderViewStatusEnum.PART_REFUND.getCode());
            } else {
                log.warn("退款审核订单设置展示状态时退款类型不属于整单退款或部分退款, 渠道id:{}, 渠道订单号:{}, 退款类型:{}", each.getOrderVO().getChannelId(), each.getOrderVO().getChannelOrderId(), each.getAfsPattern());
            }
        }
    }

    private CompensationVO buildCompensationModel(String compensation){
        try {
            if(StringUtils.isEmpty(compensation)){
                return null;
            }
            return JacksonUtils.fromJson(compensation, CompensationVO.class);
        }catch (Exception e){
            log.error("OrderService.buildCompensationVO error", e);
        }
        return null;
    }

    public RefundApplyRecordVO buildRefundApplyRecordVO(OCMSWaitAuditOrderVO ocmsWaitAuditOrderVO,
                                                         OrderRevenueDetailResponse orderRevenueDetailResponse, SaasCrmDataRemoteService.OrderProfitView orderProfit, List<OrderLabelModel> showLabelList) {
        RefundApplyRecordVO refundApplyRecordVO = new RefundApplyRecordVO();
        refundApplyRecordVO.setOrderVO(buildOrderVO(ocmsWaitAuditOrderVO, orderProfit, showLabelList));

        OCMSAfterSaleApplyVO ocmsAfterSaleApplyVO = ocmsWaitAuditOrderVO.getOcmsAfterSaleApplyVO();
        refundApplyRecordVO.setServiceId(ocmsAfterSaleApplyVO.getServiceId());
        refundApplyRecordVO.setAfterSaleId(ocmsAfterSaleApplyVO.getAfterSaleId());
        refundApplyRecordVO.setIsAudit(ocmsAfterSaleApplyVO.getIsAudit());
        refundApplyRecordVO.setStatus(ocmsAfterSaleApplyVO.getStatus());
        refundApplyRecordVO.setApplyReason(ocmsAfterSaleApplyVO.getApplyReason());
        refundApplyRecordVO.setAfsPattern(ocmsAfterSaleApplyVO.getAfsPattern());
        refundApplyRecordVO.setRefundAmt(ocmsAfterSaleApplyVO.getRefundAmt());
        refundApplyRecordVO.setCreateTime(ocmsAfterSaleApplyVO.getCreateTime());
        refundApplyRecordVO.setUpdateTime(ocmsAfterSaleApplyVO.getUpdateTime());
        refundApplyRecordVO.setAfsApplyType(ocmsAfterSaleApplyVO.getApplyType());
        refundApplyRecordVO.setWhoApplyType(ocmsAfterSaleApplyVO.getApplyUserType());
        refundApplyRecordVO.setDispatchShopId(ocmsAfterSaleApplyVO.getDispatchShopId());
        refundApplyRecordVO.setDealTime(ocmsAfterSaleApplyVO.getDealTime());
        //返货
        refundApplyRecordVO.setIsCanReturnGoods(ocmsAfterSaleApplyVO.getIsCanReturnGoods());
        List<OCMSAfterSaleApplyDetailVO> ocmsAfterSaleApplyDetailVOList = ocmsAfterSaleApplyVO.getOcmsAfterSaleApplyDetailVOList();

        Map<Integer, ProductLabelVO> productLabelVOMap = ProductLabelUtil.buildMapFromOCMSOrderItemVO(ocmsWaitAuditOrderVO.getOcmsOrderItemVOList());
        if (CollectionUtils.isNotEmpty(ocmsAfterSaleApplyDetailVOList)) {
            refundApplyRecordVO.setRefundApplyRecordDetailVOList(ocmsAfterSaleApplyDetailVOList.stream().filter(Objects::nonNull).map(item -> buildRefundApplyRecordDetailVO(item, productLabelVOMap, null)).collect(Collectors.toList()));
            // 将商品图片赋值到售后信息中
            setRefundApplyDetailPicurl(refundApplyRecordVO);

            refundApplyRecordVO.setRefundProductCount(ocmsAfterSaleApplyDetailVOList.stream().filter(Objects::nonNull).mapToInt(OCMSAfterSaleApplyDetailVO::getCount).sum());
        }
        if (refundApplyRecordVO.getOrderVO() != null) {
            refundApplyRecordVO.setOrderRefundInfo(refundApplyRecordVO.getOrderVO().getOrderRefundInfo());
        }
        refundApplyRecordVO.setRefundPicList(ocmsAfterSaleApplyVO.getRefundPicList());

        if (ocmsWaitAuditOrderVO.getOcmsAfterSaleApplyVO().getApplyType() != null) {
            AfterSaleTypeEnum afterSaleTypeEnum = AfterSaleTypeEnum.enumOf(ocmsWaitAuditOrderVO.getOcmsAfterSaleApplyVO().getApplyType());
            refundApplyRecordVO.setRefundApplyType(WaitToAuditRefundGoodsOrderSubTypeEnum.getSubTypeCode(afterSaleTypeEnum));
            setWorryFreeReturn(ocmsAfterSaleApplyVO,refundApplyRecordVO);
        }
        refundApplyRecordVO.setIsFastOrder(ocmsWaitAuditOrderVO.getIsFastOrder());
        refundApplyRecordVO.setIsFastToSelfDelivery(ocmsWaitAuditOrderVO.getIsFastToSelfDelivery());
        refundApplyRecordVO.setCompensationModel(CompensationUtil.buildCompensationModel(ocmsWaitAuditOrderVO.getCompensation()));
        refundApplyRecordVO.setCompensationModelList(CompensationUtil.buildCompensationModelList(ocmsWaitAuditOrderVO.getCompensation()));
        refundApplyRecordVO.setFastDeliveryAmt(ocmsWaitAuditOrderVO.getFastDeliveryAmt());
        return refundApplyRecordVO;
    }

}
