package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/12/30
 */
@Data
public class StoreLaunchDeliveryConfigVO {
    @FieldDoc(
            description = "自动发配送时间点，1-商家接单，2-拣货完成"
    )
    @ApiModelProperty(value = "自动发配送时间点")
    private int autoLaunchPoint = 1;

    @FieldDoc(
            description = "自动发配送延时，单位（分钟）"
    )
    @ApiModelProperty(value = "自动发配送延时")
    private Integer autoLaunchDelayMinutes = 0;

    @FieldDoc(
            description = "预约单-自动发配送时间点，1-预计送达前，2-拣货完成后"
    )
    @ApiModelProperty(value = "预约单-自动发配送时间点")
    private Integer bookingOrderAutoLaunchPoint = 1;

    @FieldDoc(
            description = "预约单-自动发配送分钟数，如 预计送达前x分钟/拣货完成后x分钟（默认：预计送达前60分钟发起预订单配送）"
    )
    @ApiModelProperty(value = "预约单-自动发配送分钟数，如 预计送达前x分钟/拣货完成后x分钟（默认：预计送达前60分钟发起预订单配送）")
    private Integer bookingOrderAutoLaunchMinutes = 60;

    @FieldDoc(
            description = "发配送方式，1-自动发配送，2-手动发配送"
    )
    @ApiModelProperty(value = "发配送方式")
    private Integer launchPattern = 1;

    @FieldDoc(
            description = "发配送规则，1-低价优先"
    )
    @ApiModelProperty(value = "发配送规则")
    private Integer launchRule = 1;

    @FieldDoc(
            description = "可用的发配送规则"
    )
    @ApiModelProperty(value = "可用的发配送规则")
    private List<LaunchRuleVO> availableLaunchRules;
}
