package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Map;

/**
 * @Author: <EMAIL>
 * @Date: 2019/2/18 16:21
 * @Description:
 */
@TypeDoc(
        name = "门店基本信息(包含渠道门店信息)",
        description = "门店基本信息(包含渠道门店信息)"
)
@Setter
@Getter
@ToString
@EqualsAndHashCode
public class PoiInfoWithChannelInfoVO {

    @FieldDoc(
            description = "门店id"
    )
    @ApiModelProperty(value = "门店id", required = true)
    private String poiId;

    @FieldDoc(
            description = "门店名称"
    )
    @ApiModelProperty(value = "门店名称", required = true)
    private String poiName;

    @FieldDoc(
            description = "门店地址"
    )
    @ApiModelProperty(value = "门店地址", required = true)
    private String address;

    @FieldDoc(
            description = "门店状态"
    )
    @ApiModelProperty(value = "门店状态", required = true)
    private String status;

    @FieldDoc(
            description = "外部门店编码"
    )
    @ApiModelProperty(value = "外部门店编码", required = true)
    private String outPoiId;

    @FieldDoc(
            description = "渠道门店信息"
    )
    private Map<Integer, String> channelPois;

    @FieldDoc(
            description = "渠道门店详情信息"
    )
    private Map<Integer, ChannelStoreInfoVO> channelPoiInfo;


}
