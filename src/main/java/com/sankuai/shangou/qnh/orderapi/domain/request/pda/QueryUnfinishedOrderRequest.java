package com.sankuai.shangou.qnh.orderapi.domain.request.pda;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @createTime 2024/07/08
 * @description
 */
@TypeDoc(
        description = "查询进行中订单列表"
)
@ApiModel("查询进行中订单列表")
@Data
public class QueryUnfinishedOrderRequest {

    @FieldDoc(
            description = "订单类型 10:待接单 20:待拣货 30:待配送 40:待自提 50:异常 60:售后", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店类型")
    private Integer orderType;

    @FieldDoc(
            description = "二级子类型, 可为空", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "二级子类型, 可为空")
    private Integer subType;

    @FieldDoc(
            description = "第几页", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "第几页", required = true)
    @NotNull
    private Integer page;

    @FieldDoc(
            description = "每页行数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "每页行数", required = true)
    @NotNull
    private Integer size;

    @FieldDoc(
            description = "门店类型 3-门店 5-中心仓 6-前置仓", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店类型")
    private Integer entityType;

    @FieldDoc(
            description = "订单-待拣货列表排序类型（0：默认，1：下单时间从早到晚，2：下单时间从晚到早，3：剩余领取时长优先，4：剩余拣货时长优先，5：预计送达时间优先）",
            requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "订单-待拣货列表排序类型")
    private Integer sortType;


}