package com.sankuai.shangou.qnh.orderapi.service.pc;

import com.github.pagehelper.Page;
import com.sankuai.meituan.shangou.saas.common.data.PageResult;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.*;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.QueryBadCommentProcessedReq;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.ManualCheckDeleteCommentVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.ManualCheckUpdateCommentVo;

import java.util.List;

/**
 * 评价服务
 *
 * <AUTHOR>
 */
public interface ChannelCommentService {


    /**
     * 删除评价回复模板
     * @param tenantId 租户id
     * @param templateId 模板id
     * @param operatorUid 操作人id
     * @return
     */
    void deleteCommentReplyTemplate(Long tenantId, Long templateId, Long operatorUid);

    /**
     * 查询评价回复模板列表
     * @param tenantId 租户id
     * @return
     */
    CommentReplyTemplateListBO queryCommentReplyTemplateList(Long tenantId);

    /**
     * 分页查询评价列表
     * @param queryBO
     * @return
     */
    PageResult<CommentBO> queryCommentList(CommentListQueryBO queryBO);

    /**
     * 查询评价详情
     * @param tenantId 租户id
     * @param commentId 评价id
     * @return
     */
    CommentBO getCommentByTenantIdAndCommentId(Long tenantId, String commentId);


    /**
     * @author: <EMAIL>
     * @date: 2019-07-04 17:11:50
     *
     * @method: queryCommentStat
     * @params: [tenantId, storeIds, startTime, endTime]
     * @return: java.util.List<com.sankuai.meituan.shangou.saas.crm.eapi.service.channelcomment.bo.CommentStatBO>
     * @desc: 查询评论统计
     */
    List<CommentStatBO> queryCommentStat(Long tenantId, List<Integer> channelIds, List<Long> storeIds, String startTime, String endTime);

    /**
     * 分渠道查询评价统计
     * @param tenantId      租户id
     * @param channelIds    渠道id
     * @param storeIds      门店id
     * @param startTime     查询时间范围的开始时间
     * @param endTime       查询时间范围的结束时间
     * @return java.util.List<com.sankuai.meituan.shangou.saas.crm.eapi.service.channelcomment.bo.CommentChannelStatBO>
     */
    List<CommentChannelStatBO> queryCommentChannelStat(Long tenantId, List<Integer> channelIds, List<Long> storeIds, String startTime, String endTime);

    /**
     * 按门店查询评价统计
     * @param tenantId      租户id
     * @param channelIds    渠道id
     * @param storeIds      门店id
     * @param startTime     查询时间范围的开始时间
     * @param endTime       查询时间范围的结束时间
     * @param page          需要查询的页码
     * @param pageSize      页码大小——每页多少条数据
     * @return java.util.List<com.sankuai.meituan.shangou.saas.crm.eapi.service.channelcomment.bo.CommentChannelStatBO>
     */
    Page<CommentStoreStatBO> queryCommentStoreStat(Long tenantId, List<Integer> channelIds, List<Long> storeIds, String startTime, String endTime, Integer page, Integer pageSize);

    void replyComment(Long tenantId, Long uid, String commentId, String replyDraft);

    /**
     * 查询评价规则
     * @param tenantId 租户id
     * @return
     */
    CommentRuleBO queryCommentRule(Long tenantId);

    void addRecordListenNum(String recordId);

    PageResult<BadProcessedInfoBO> queryBadCommentProcessedList(QueryBadCommentProcessedReq req);

    /**
     * 添加评价回复模板
     * @param templateBO 模版BO
     * @return
     */
    Long addCommentReplyTemplate(StoreCommentReplyTemplateBO templateBO);

    void updateCommentReplyTemplate(StoreCommentReplyTemplateBO buildBO);

    /**
     * 评论关联渠道订单id
     * 
     * @param tenantId
     * @param uid
     * @param commentId
     * @param associationChannelOrderId
     */
    void commentAssociationChannelOrderId(Long tenantId, Long uid, String commentId, String associationChannelOrderId);

    ManualCheckUpdateCommentVo manualCheckUpdateComment(String commentId, Long tenantId);

    ManualCheckDeleteCommentVo manualCheckDeleteComment(String commentId, Long tenantId);

    void confirmDeleteComment(String commentId, Long tenantId, Long operatorId);
}
