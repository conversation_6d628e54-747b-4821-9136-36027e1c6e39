package com.sankuai.shangou.qnh.orderapi.service.common.query;

import com.meituan.shangou.saas.common.enums.OrderCanOperateItem;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OcmsOrderListReq;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OcmsOrderSearchResponseV2;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.meituan.shangou.saas.order.management.client.service.online.OcmsOrderSearchThriftServiceV2;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.OrderListQueryRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.OrderListRequest;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.OrderListResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.PageInfoVO;
import com.sankuai.shangou.qnh.orderapi.enums.app.ResultCodeEnum;
import com.sankuai.shangou.qnh.orderapi.service.common.*;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class SelectListByOrderIdsService extends QueryOrderService {

    @Resource
    private OrderRequestBuilder orderRequestBuilder;
    @Resource
    private OcmsOrderSearchThriftServiceV2 orderSearchThriftServiceV2;
    @Resource
    private DeliveryService deliveryService;
    @Resource
    private OrderOperateItemsService orderOperateItemsService;
    @Resource
    private DrunkHorseApiService drunkHorseApiService;

    @Override
    public Pair<List<OCMSOrderVO>, PageInfoVO> queryOrderInfo(OrderListRequestContext request) {
        OrderListQueryRequest orderListQueryRequest = (OrderListQueryRequest)request.getRequest();
        OcmsOrderListReq ocmsOrderListReq = orderRequestBuilder.buildSelectByOrderIdsReq(orderListQueryRequest);
        log.info("OrderService.selectListByOrderIds call OcmsOrderSearchThriftServiceV2.orderList request:{}",
                ocmsOrderListReq);
        OcmsOrderSearchResponseV2 response = null;
        try {
            response = orderSearchThriftServiceV2.orderList(ocmsOrderListReq);
            log.info("调用ocmsQueryThriftService.listOrder response:{}", response);
            if (response.getResponseStatus() != ResultCodeEnum.SUCCESS.getCode()) {
                throw new BizException(ResultCodeEnum.FAIL.getCode(), response.getMsg());
            }
        } catch (TException e) {
            log.error("调用ocmsQueryThriftService.listOrder request:{}", request);
        }
        if (response == null) {
            return new Pair<>(Collections.emptyList(), PageUtil.buildEmptyPageInfoVO());
        }
        PageInfoVO pageInfoVO = PageUtil.buildPageInfoVO(response.getPage());
        return new Pair<>(response.getOcmsOrderList(), pageInfoVO);
    }

    @Override
    public void addExtraInfo(OrderListResponse orderListResponse, OrderListRequestContext request) {
        OrderStatusUtil.fixOrdersStatusDesc(orderListResponse);
        deliveryService.buildDeliveryCount(orderListResponse);
        orderOperateItemsService.justKeepPartOperateItemsForDeliveryErrorOrder(orderListResponse,
                Lists.newArrayList(OrderCanOperateItem.FULL_ORDER_REFUND, OrderCanOperateItem.CREATE_INVOICE,
                        OrderCanOperateItem.SETTING_ORDER_TAG));
        drunkHorseApiService.appendLackTag(request.getStoreId(), orderListResponse);
        drunkHorseApiService.appendSealTag(request.getTenantId(), request.getStoreId(), orderListResponse);
    }

    @Override
    public boolean needFillOrderRevenueDetailInfoAlone(OrderListRequestContext request){
        return true;
    }
}
