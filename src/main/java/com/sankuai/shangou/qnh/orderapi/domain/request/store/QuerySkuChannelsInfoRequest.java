package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2019/6/17
 * desc:
 */
@TypeDoc(
        description = "查询门店商品上线信息请求"
)
@Data
@ApiModel("查询门店商品上线信息请求")
public class QuerySkuChannelsInfoRequest {

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID")
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "商品编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品编码")
    @NotNull
    private String skuId;
}
