package com.sankuai.shangou.qnh.orderapi.service.store;

import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.sankuai.meituan.reco.pickselect.thrift.OpenTraceListResponse;
import com.sankuai.meituan.reco.pickselect.thrift.OpenTraceResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.OrderVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.RefundApplyRecordVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.OrderFuseVO;
import com.sankuai.shangou.qnh.orderapi.enums.print.PrintTimesEnum;
import com.sankuai.shangou.qnh.orderapi.remote.PickSelectOrderPrintService;
import com.sankuai.shangou.qnh.orderapi.remote.PickSelectOrderTraceService;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import com.sankuai.shangou.qnh.orderapi.utils.pc.LionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.shangou.qnh.orderapi.remote.OCMSOrderRemoteService.getPickNames;

@Service
@Slf4j
public class OrderVoService {
    @Autowired
    private PickSelectOrderTraceService orderTraceService;

    @Autowired
    private PickSelectOrderPrintService orderPrintService;

    private <T> void setPickerNamesInfo(List<T> orderList, Function<T, Integer> channelIdExtractor, Function<T, String> channelOrderIdExtractor) {
        try {
            if (!MccConfigUtil.getOrderShowPickerNamesSwitch() || CollectionUtils.isEmpty(orderList)) {
                return;
            }
            OpenTraceListResponse response = orderTraceService.getOrderListTraceByOpTypes(orderList, channelIdExtractor, channelOrderIdExtractor);
            if (response == null || response.getCode() == ResultCode.FAIL.getCode() || MapUtils.isEmpty(response.getDetailMap())) {
                return;
            }
            orderList.forEach(
                    order -> {
                        OpenTraceResponse openTraceResponse = response.getDetailMap().get(channelOrderIdExtractor.apply(order));
                        if (order instanceof OrderFuseVO) {
                            ((OrderFuseVO) order).setPickerNameList(getPickNames(openTraceResponse));
                        } else if (order instanceof OrderVO) {
                            ((OrderVO) order).setPickerNameList(getPickNames(openTraceResponse));
                        } else if (order instanceof RefundApplyRecordVO) {
                            ((RefundApplyRecordVO) order).setPickerNameList(getPickNames(openTraceResponse));
                        }
                    }
            );
        } catch (Exception e) {
            log.error("setPickerNamesInfo error", e);
        }
    }
    public void setOrderFuseVOPickerNamesInfo(List<OrderFuseVO> orderList) {
        setPickerNamesInfo(orderList, OrderFuseVO::getChannelId, OrderFuseVO::getChannelOrderId);
    }

    public void setOrderVOPickerNamesInfo(List<OrderVO> orderList) {
        setPickerNamesInfo(orderList, OrderVO::getChannelId, OrderVO::getChannelOrderId);
    }
    public void setRefundApplyRecordVOPickerNamesInfo(List<RefundApplyRecordVO> orderList) {
        //过滤orderList中为order和order.getOrderVO()为null的元素
        List<RefundApplyRecordVO> copyeOrderList = orderList.stream().filter(Objects::nonNull).filter(order -> order.getOrderVO() != null).collect(Collectors.toList());
        setPickerNamesInfo(copyeOrderList, order -> order.getOrderVO().getChannelId(), order -> order.getOrderVO().getChannelOrderId());
    }

    public <T> void fillPrintTimes(List<T> orderList) {
        try{
            if (!shouldProcess(orderList)) {
                return;
            }
            Map<Long, Long> orderIdGroup = groupOrderIdsByStoreId(orderList);
            Map<Long, Integer> printTimesMap = orderPrintService.queryPrintTimesForStores(orderIdGroup);
            updateOrderListWithPrintTimes(orderList, printTimesMap);
        }catch (Exception e){

            log.error("fillPrintTimes error", e);
        }

    }

    private <T> boolean shouldProcess(List<T> orderList) {
        if (CollectionUtils.isEmpty(orderList)){
            return false;
        }
        Optional<Long> tenantId = orderList.stream().findFirst().map(order -> {
            if (order instanceof OrderFuseVO) {
                return ((OrderFuseVO) order).getTenantId();
            } else if (order instanceof OrderVO) {
                return ((OrderVO) order).getTenantId();
            }
            return null;
        });
        return MccConfigUtil.showPrintTimesSwitch(tenantId);
    }

    private <T> Map<Long, Long> groupOrderIdsByStoreId(List<T> orderList) {
        Map<Long, Long> orderIdGroup = new HashMap<>();
        orderList.forEach(order -> {
            if (order instanceof OrderFuseVO) {
                OrderFuseVO fuseOrder = (OrderFuseVO) order;
                orderIdGroup.put(fuseOrder.getOrderId(), getStoreId(fuseOrder.getDispatchShopId(), fuseOrder.getWarehouseId(), fuseOrder.getStoreId()));
            } else if (order instanceof OrderVO) {
                OrderVO voOrder = (OrderVO) order;
                orderIdGroup.put(voOrder.getEmpowerOrderId(), getStoreId(voOrder.getDispatchShopId(), voOrder.getWarehouseId(), voOrder.getStoreId()));
            }
        });
        return orderIdGroup;
    }


    private <T> void updateOrderListWithPrintTimes(List<T> orderList, Map<Long, Integer> printTimesMap) {
        orderList.forEach(order -> {
            if (order instanceof OrderFuseVO) {
                OrderFuseVO fuseOrder = (OrderFuseVO) order;
                fuseOrder.setPrintTimes(getPrintTimesStr(printTimesMap.get(fuseOrder.getOrderId())));
            } else if (order instanceof OrderVO) {
                OrderVO voOrder = (OrderVO) order;
                voOrder.setPrintTimes(getPrintTimesStr(printTimesMap.get(voOrder.getEmpowerOrderId())));
            }
        });
    }
    public Long getStoreId(Long dispatchId, Long warehouseId, Long storeId) {
        if (Objects.nonNull(dispatchId)){
            return dispatchId;
        }
        if (Objects.nonNull(warehouseId)){
            return warehouseId;
        }
        return storeId;
    }

    public String getPrintTimesStr(Integer printTimes) {
        if (Objects.nonNull(printTimes) && printTimes > 0){
            return String.valueOf(printTimes);
        }
        return PrintTimesEnum.DEFAULT.getStr();
    }

}