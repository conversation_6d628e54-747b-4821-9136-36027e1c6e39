package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sgfulfillment.comment.thrift.dto.model.ChannelCommentReplyTemplateDTO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.CommentReplyTemplateVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@TypeDoc(
        description = "查询评价回复模板响应",
        authors = "hejunliang"
)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CommentReplyTemplateListQueryResp {

    @FieldDoc(
            description = "评价回复模板列表", requiredness = Requiredness.OPTIONAL
    )
    private List<CommentReplyTemplateVO> commentReplyTemplateList;

    @FieldDoc(
            description = "评价回复模板数量最大值", requiredness = Requiredness.REQUIRED
    )
    private Integer commentReplyTemplateCountMax;

    public static CommentReplyTemplateListQueryResp build(List<ChannelCommentReplyTemplateDTO> replyTemplateDTOList,
                                                          Integer commentReplyTemplateCountMax) {
        List<CommentReplyTemplateVO> replyTemplateVOList =
                Optional.ofNullable(replyTemplateDTOList).map(List::stream).orElse(Stream.empty())
                        .map(replyTemplateDTO -> CommentReplyTemplateVO.build(replyTemplateDTO))
                        .collect(Collectors.toList());
        return new CommentReplyTemplateListQueryResp(replyTemplateVOList, commentReplyTemplateCountMax);
    }
}
