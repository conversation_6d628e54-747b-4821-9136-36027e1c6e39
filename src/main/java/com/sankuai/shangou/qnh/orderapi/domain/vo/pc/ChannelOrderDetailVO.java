package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.logistics.warehouse.dto.PackingPictureInfoDTO;
import com.sankuai.shangou.qnh.orderapi.domain.bo.ReturnDataPoiBo;
import com.sankuai.shangou.qnh.orderapi.domain.result.ReturnDataSecurity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/4 16:13
 * @Description:
 */
@TypeDoc(
        description = "订单详情"
)
@ApiModel("订单详情")
@Setter
@Getter
@NoArgsConstructor
@ToString
public class ChannelOrderDetailVO implements ReturnDataSecurity {


    /**
     * 基础信息
     */
    private BaseInfoVO baseInfo;


    /**
     * 商品列表
     */
    private List<ItemInfoVO> itemInfo;


    /**
     * 促销信息
     */
    private List<PromotionInfoVO> promotionInfo;

    /**
     * 操作日志
     */
    private List<OperateLogVO> operateLog;


    /**
     * 配置详情
     */
    private List<DeliveryDetailVO> deliveryDetail;


    /**
     * 订单是否有调整
     */
    @FieldDoc(
            description = "订单是否有调整"
    )
    @ApiModelProperty(value = "订单是否有调整", required = true)
    private Boolean hasOrderAdjustLog;

    /**
     * 配送信息列表
     */
    private List<DeliveryInfoVo> deliveryInfoList;

    /**
     * pickingCheckPictureUrlList 是原来的封签照片，该字段自保真送2.0需求后废弃，使用 packingPictureInfo 字段替代
     * @see ChannelOrderDetailVO#packingPictureInfo
     * 拣货复核照片
     */
    @FieldDoc(
            description = "拣货复核照片URL（支持多张照片）"
    )
    @ApiModelProperty(value = "拣货复核照片URL（支持多张照片）", required = false)
    private List<String> pickingCheckPictureUrlList;

    @FieldDoc(
            description = "送达照片URL（支持多张照片）"
    )
    @ApiModelProperty(value = "送达照片URL（支持多张照片）", required = false)
    @Deprecated
    private List<DeliveryCompleteInfoVO.ProofPhotoInfo> deliveryProofPhotoList;

    @FieldDoc(
            description = "拣货项信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "拣货项信息")
    @Deprecated
    private List<PickItemVO> pickItemInfoList;

    @FieldDoc(
            description = "拣货项信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "拣货项信息")
    private PickInfoVO pickInfo;

    @FieldDoc(
            description = "配送完成信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "配送完成信息")
    private DeliveryCompleteInfoVO deliveryCompleteInfo;

    @FieldDoc(
            description = "退款模块信息"
    )
    @ApiModelProperty(value = "退款模块信息", required = false)
    private List<AfterSaleVO> afterSaleList;

    @FieldDoc(
            description = "发票信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "发票信息")
    private List<InvoiceDetailVO> invoiceList;

    @FieldDoc(
            description = "基础商品sku标记", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "基础商品sku标记")
    private List<MaterialSkuVO> materialSkuList;

    @FieldDoc(description = "歪马礼袋", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "歪马礼袋")
    private List<DrunkHorseGiftBagVO> giftBagList;

    /** 订单出库支持上传三组图片 */
    @FieldDoc(description = "出库图片", requiredness = Requiredness.OPTIONAL)
    @ApiModelProperty(name = "出库图片")
    private PackingPictureInfo packingPictureInfo;

    @Override
    public List<ReturnDataPoiBo> fetchReturnDataPoiBoList() {
        if (Objects.nonNull(this.baseInfo)) {
            ReturnDataPoiBo poiBo = ReturnDataPoiBo.builder().poiId(this.baseInfo.getPoiId())
                    .dispatchShopId(this.baseInfo.getDispatchShopId()).build();
            return Lists.newArrayList(poiBo);
        }
        return null;
    }

    @Setter
    @Getter
    @ToString
    public static class BaseInfoVO {
        /**
         * 订单号
         */
        private String orderId;

        /**
         * 门店 ID
         */
        private Long poiId;

        /**
         * 转单门店ID
         */
        private Long dispatchShopId;

        /**
         * 门店名称
         */
        private String poiName;


        /**
         * 渠道名称
         */
        private String channelName;


        /**
         * 收货人名称
         */
        private String receiverName;

        /**
         * 收货人电话
         */
        private String receiverPhone;

        /**
         * 收货人隐私号码
         */
        private String receiverPrivacyPhone;

        /**
         * 收货人地址
         */
        private String receiverAddress;

        /**
         * 订单金额
         */
        private String amount;

        /**
         * 实付金额
         */
        private String paidAmount;


        /**
         * 商家实收金额
         */
        private String merchantAmount;

        /**
         *
         */
        private String deliveryAmount;

        /**
         * 餐盒费
         */
        private String packageAmount;

        /**
         * 平台服务费
         */
        private String platformAmount;


        /**
         * 是否需要发票
         */
        private int needInvoice;


        /**
         * 发票抬头
         */
        private String invoiceTitle;

        /**
         * 税号
         */
        private String invoiceTaxNo;


        /**
         * 配送方式
         */
        private String deliveryMethod;


        /**
         * 配送单创建时间
         */
        private String deliveryOrderCreateTime;


        /**
         * 配送员姓名
         */
        private String riderName;


        /**
         * 配送员电话
         */
        private String riderPhone;

        /**
         * 是否在线支付
         */
        private int paidOnline;

        /**
         * 订单状态
         */
        private String status;

        /**
         * 创建时间
         */
        private String createTime;

        /**
         * 申请退款原因
         */
        private String refundReason;


        /**
         * 退款状态编码
         */
        private String refundTagId;


        /**
         * 售后审核状态
         */
        private String afterSaleApplyStatus;


        private String refundableOrderAmount;


        /**
         * 服务单号
         */
        private String serviceId;

        /**
         * 发起者
         */
        private String applicant;


        /**
         * 售后请求状态
         */
        private String afsApplyType;


        private String orderType;

        private String memberCard;

        /**
         * 商家活动支出
         */
        private String bizActivityAmt;

        /**
         * 线下总价
         */
        private String offlineTotalAmt;
        /**
         * 销售利润
         */
        private String saleBenefit;

        /**
         * 预计送达时间
         */
        private String arrivalTime;

        /**
         * 备注
         */
        private String comment;

        /**
         * 仓 ID
         */
        private Long warehouseId;

        /**
         * 仓库名称
         */
        private String warehouseName;

        /**
         * 订单用户类型
         * @see com.meituan.shangou.saas.order.platform.enums.UserTypeEnum
         */
        private Integer orderUserType;

        /**
         * 真实医药无人仓erp订单来源渠道id
         */
        private String realMedicineOrderChannelId;

        /**
         * 真实医药无人仓er订单来源渠道名称
         */
        private String realMedicineOrderChannelName;

        /**
         * 承运商
         */
        private String deliveryPlatform;

        /**
         * 代签点信息
         */
        private String deliveryPosition;


        /**
         * 场景
         */
        private String scene;

        /**
         * 品类
         */
        private String category;


        /**
         * 门店经营模式 1 直营 2 加盟
         */
        private Integer manageMode;

        /**
         * 地推单 1 推广现提  0/null 空
         */
        private Integer pullNewOrder;

        /**
         * 用户身份tag
         */
        private String userTag;

        /**
         * 实际送达时间
         */
        private String finishDeliveryTime;


        /**
         * 组织结构二级
         */
        private String parentOrgName;

        /**
         * 组织结构三级
         */
        private String grandParentOrgName;

        /**
         * 订单重量
         */
        private Long deliveryWeight;

        /**
         * 配送导航距离
         */
        private Long deliveryDistance;

        /**
         * 物理城市
         */
        private String cityName;

        /**
         * 商家实收hover后的文案
         */
        private String merchantAmountHover;

        /**
         * 佣金
         */
        private String commisionAmount;

        /**
         * 佣金hover后的文案
         */
        private String commisionAmountHover;

        /**
         * 商品成本
         */
        private String costOfGoodsSold;

        /**
         * 商品成本hover后的文案
         */
        private String costOfGoodsSoldHover;

        /**
         * 实收毛利额
         */
        private String actualGrossProfit;

        /**
         * 实收毛利额hover后的文案
         */
        private String actualGrossProfitHover;

        /**
         * 实收毛率
         */
        private String actualGrossRate;

        /**
         * 实收毛率hover后的文案
         */
        private String actualGrossRateHover;

        /**
         * 是否代销品订单
         */
        private Boolean isConsignment;

        /**
         * 商家活动支出hover文案
         */
        private String totalDiscountHover;

        /**
         * 是否发财酒订单
         */
        private Boolean isFacaiWine;

        private List<SortedTagVO> sortedTagList;
    }


    @Setter
    @Getter
    @ToString
    public static class ItemInfoVO {
        /**
         * sku编码
         */
        private String sku;

        /**
         * 线上渠道sku编码
         */
        private String customSkuId;

        /**
         * 商品名称
         */
        private String skuName;

        /**
         * upc编码
         */
        private String upc;

        /**
         * 规格
         */
        private String spec;

        /**
         * 单位
         */
        private String unit;

        /**
         * 单价
         */
        private String unitPrice;

        /**
         * 数量
         */
        private int  quantity;

        /**
         * 总价
         */
        private String totalPrice;


        /**
         * 是否取消
         */
        private String isRefund;

        /**
         * 取消数量
         */
        private String refundCount;
        /**
         * 摊位名称
         */
        private String boothName;
        /**
         * 商品线下售价
         */
        private String offlinePrice;
        /**
         * 实际拣货数量
         */
        private String realQuantity;
        /**
         * 摊位结算金额
         */
        private String boothSettleAmount;

        /**
         * 属性
         * **/
        private List<String> propertyList;
        /**
         * 换货商品信息
         */
        private List<ExchangeItemInfoVO> exchangeItemInfoVOList;
        /**
         * 单据原始数量
         */
        private Long orderItemId;
        /**
         * 用户下单时候的quantity
         */
        private Integer orderQuantity;

        /**
         * 是否代销商品
         */
        private Boolean isConsignment;
    }

    @Setter
    @Getter
    @ToString
    public static class ExchangeItemInfoVO {

        private int exchangeFromCnt;

        private int exchangeToCnt;

        private ItemInfoVO itemInfoVO;
    }


    @Setter
    @Getter
    @ToString
    public static class PromotionInfoVO {
        /**
         * 优惠名称
         */
        private String promotionName;

        /**
         * 优惠金额
         */
        private String promotionAmount;

        /**
         * 平台承担金额
         */
        private String platformAmount;

        /**
         * 商家承担金额
         */
        private String merchantAmount;

        /**
         * 代理商承担金额
         */
        private String agentAmount;

        /**
         * 物流承担金额
         */
        private String logisticsBearAmount;

        /**
         * uoc活动类型
         */
        private String uocActType;
    }


    @Setter
    @Getter
    @ToString
    public static class OperateLogVO {
        /**
         * 操作员
         */
        private String operator;

        /**
         * 操作时间
         */
        private String operateTime;

        /**
         * 操作类型
         */
        private String operateType;

        /**
         * 描述
         */
        private String desc;
    }

    @Setter
    @Getter
    @ToString
    public static class InvoiceDetailVO {

        //1 线上 2 线下
        private Integer InvoiceChannel;

        //申请时间
        private String applyInvoiceTime;

        //发票抬头
        private String title;

        //税号
        private String taxpayerNo;

        //开票金额
        private String InvoiceAmount;

        //发票状态
        private Integer InvoiceStatus;

        //开票完成时间
        private String completeInvoiceTime;

        //发票号码
        private String invoiceNumber;

        //发票下载链接
        private String invoiceDownloadUrl;
    }

    /**
     * 订单出库图片
     */
    @Getter
    @Setter
    public static class PackingPictureInfo {

        /** 出库订单商品图片 */
        private List<String> goodsPictureUrlList;

        /** 出库订单包装图片 */
        private List<String> packingPictureUrlList;

        /** 出库订单封签图片（该字段仅封签交付订单才有）*/
        private List<String> sealPictureUrlList;

        /**
         * 拷贝 DTO 对象里面的三组图片到当前结构体中
         *
         * @param packingPictureInfo  交易出库单出库图片DTO对象
         * @return                    return this
         */
        public PackingPictureInfo copyFromPackingPicDTO(PackingPictureInfoDTO packingPictureInfo) {
            if (packingPictureInfo != null) {
                this.goodsPictureUrlList = packingPictureInfo.getGoodsPictureUrlList();
                this.packingPictureUrlList = packingPictureInfo.getPackingPictureUrlList();
                this.sealPictureUrlList = packingPictureInfo.getSealPictureUrlList();
            }
            return this;
        }

    }

}
