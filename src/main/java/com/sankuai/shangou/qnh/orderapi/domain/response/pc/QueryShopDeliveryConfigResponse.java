package com.sankuai.shangou.qnh.orderapi.domain.response.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.ShopDeliveryConfigVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/16
 * desc: 查询门店配送配置响应
 */
@Getter
@Setter
public class QueryShopDeliveryConfigResponse {

    @FieldDoc(
            description = "门店配送配置列表"
    )
    @ApiModelProperty(value = "门店配送配置列表", required = true)
    private List<ShopDeliveryConfigVO> list;

    @FieldDoc(
            description = "当前页号"
    )
    @ApiModelProperty(value = "当前页号", required = true)
    private String page;

    @FieldDoc(
            description = "当前页号"
    )
    @ApiModelProperty(value = "当前页号", required = true)
    private String pageSize;

    @FieldDoc(
            description = "总记录数"
    )
    @ApiModelProperty(value = "总记录数", required = true)
    private String total;
}
