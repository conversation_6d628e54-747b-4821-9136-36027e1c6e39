package com.sankuai.shangou.qnh.orderapi.domain.request.app;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * 全部订单列表分页查询请求.
 *
 * <AUTHOR>
 * @since 2021/7/2 11:16
 */
@TypeDoc(
        description = "全部订单列表分页查询请求"
)
@ApiModel("全部订单列表分页查询请求")
@Data
public class OrderListRequest {

    @FieldDoc(
            description = "门店 ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店 ID")
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "订单创建起始日期 格式:2021-07-01", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单创建起始日期 格式:2021-07-01")
    @NotNull
    private String beginCreateDate;

    @FieldDoc(
            description = "订单创建结束日期 格式:2021-07-01", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单创建结束日期 格式:2021-07-01")
    @NotNull
    private String endCreateDate;

    @FieldDoc(
            description = "渠道ID列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道ID列表")
    private List<Integer> channelIdList;

    @FieldDoc(
            description = "配送订单类型  1-立即单 2-预约单", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "配送订单类型  1-立即单 2-预约单")
    private Integer deliveryOrderType;

    @FieldDoc(
            description = "聚合订单状态  1-进行中订单 2-已完成订单 3-已取消订单", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "聚合订单状态")
    private Integer aggregationOrderStatus;

    @FieldDoc(
            description = "第几页", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "第几页", required = true)
    @NotNull
    private Integer page;

    @FieldDoc(
            description = "每页行数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "每页行数", required = true)
    @NotNull
    private Integer pageSize;

    @FieldDoc(
            description = "搜索关键词", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "搜索关键词", required = true)
    private String keyword;

    @FieldDoc(
            description = "门店类型 3-门店 5-中心仓 6-前置仓", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店类型")
    private Integer entityType;


    @FieldDoc(
            description = "配送方式  1-平台配送  2-聚合配送 3-自提", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "配送方式")
    private List<Integer> distributeTypeList;


    @FieldDoc(
            description = "配送订单类型  1-立即单 2-预约单", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "配送订单类型")
    private List<Integer> deliveryOrderTypeList;

    @FieldDoc(
            description = "聚合订单状态  1-进行中订单 2-已完成订单 3-已取消订单", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "聚合订单状态 - 支持多选")
    private List<Integer> aggregationOrderStatusList;

    @FieldDoc(
            description = "配送平台类型筛选  2-自营配送 -4-青云配送", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "配送平台类型筛选 - 单选")
    private Integer deliveryChannelType;

    @FieldDoc(
            description = "游标", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "游标")
    private String scrollId;

    public String validate() {
        if (storeId == null) {
            return "门店ID和前置仓ID不能都为空";
        }
        // 判断时间范围有效性
        try {
            LocalDate beginDateTime = LocalDate.parse(beginCreateDate);
            LocalDate endDateTime = LocalDate.parse(endCreateDate).plusDays(1L);
            // 仅允许从 5 个月前的 1 号选择
            LocalDate earliestDateRange = LocalDate.now().minusMonths(5L).withDayOfMonth(1);
            LocalDate latestDateRange = LocalDate.now().plusDays(1L);
            if (beginDateTime.isBefore(earliestDateRange)) {
                return "6 个月之前的时间不可选择";
            }
            if (endDateTime.isAfter(latestDateRange)) {
                return "结束时间不能超过当天";
            }
            long days = endDateTime.toEpochDay() - beginDateTime.toEpochDay();
            if (days < 1) {
                return "开始日期不能大于结束日期";
            }
            if (days > 60) {
                return "最多可选择 60 天跨度的订单";
            }
        } catch (Exception e) {
            return "时间范围错误，请联系管理员";
        }

        if (page == null || page < 1 || pageSize == null || pageSize < 1) {
            return "分页参数不合法";
        }
        return null;
    }
}
