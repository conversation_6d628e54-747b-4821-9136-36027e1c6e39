package com.sankuai.shangou.qnh.orderapi.domain.response.app;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.enums.app.ResultCodeEnum;
import com.sankuai.shangou.qnh.orderapi.utils.app.ParamCheckUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang.StringUtils;

import java.text.MessageFormat;
import java.util.Objects;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/9.
 */
@TypeDoc(
        description = "通用返回结果"
)
@ApiModel("通用返回结构")
public class CommonResponse<T> {

    @FieldDoc(
            description = "错误码"
    )
    @ApiModelProperty(value = "错误码", required = true)
    private int code;

    @FieldDoc(
            description = "错误消息"
    )
    @ApiModelProperty(value = "错误消息")
    private String message;

    @FieldDoc(
            description = "返回内容"
    )
    private T data;

    public CommonResponse(int code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public CommonResponse() {
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public static <U> CommonResponse<U> success(U val) {
        return new CommonResponse<>(ResultCodeEnum.SUCCESS.getCode(), "", val);
    }

    public static <U> CommonResponse<U> fail(int resultCode, String msg) {
        return new CommonResponse<>(resultCode, msg, null);
    }

    public static <U> CommonResponse<U> fail(int resultCode, String msg, U val) {
        return new CommonResponse<>(resultCode, msg, val);
    }

    public static CommonResponse<Void> fail(ResultCodeEnum resultCodeEnum) {
        return fail(resultCodeEnum, (String) null);
    }

    public static <T> CommonResponse<T> fail2(ResultCodeEnum resultCodeEnum) {
        return fail(resultCodeEnum.code, resultCodeEnum.getErrorMessage());
    }

    public static CommonResponse<Void> fail(ResultCodeEnum resultCodeEnum, String msg) {
        ParamCheckUtils.nullCheck(resultCodeEnum, "resultCodeEnum cannot be null");

        String errorMsg = msg;
        if (StringUtils.isEmpty(errorMsg)) {
            errorMsg = resultCodeEnum.getErrorMessage();
        }
        return new CommonResponse<>(resultCodeEnum.getCode(), errorMsg, null);
    }

    public static CommonResponse<Void> fail(ResultCodeEnum resultCodeEnum, Throwable e) {
        Objects.requireNonNull(e, "exception must not be null");
        return fail(resultCodeEnum, e.getMessage());
    }

    public static CommonResponse<Void> fail(ResultCodeEnum resultCodeEnum, Object[] params) {
        ParamCheckUtils.nullCheck(resultCodeEnum, "resultCodeEnum cannot be null");

        return new CommonResponse<>(resultCodeEnum.getCode(), MessageFormat.format(resultCodeEnum.getErrorMessage(), params), null);
    }

    public boolean isSuccess() {
        return this.code == ResultCodeEnum.SUCCESS.getCode();
    }

}
