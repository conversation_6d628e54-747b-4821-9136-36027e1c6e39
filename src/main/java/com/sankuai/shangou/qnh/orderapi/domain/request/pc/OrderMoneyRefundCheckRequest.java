package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.o2o.dto.request.BizOrderMoneyRefundCheckRequest;
import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import lombok.Data;

import java.text.ParseException;

/**
 * Created by suxiaoyu on 2023/3/7 11:57
 */
@TypeDoc(
        description = "金额退页面检查的request"
)
@Data
public class OrderMoneyRefundCheckRequest {

    @FieldDoc(
            description = "渠道id"
    )
    private Integer channelId;

    @FieldDoc(
            description = "渠道订单号"
    )
    private String channelOrderId;

    @FieldDoc(
            description = "租户id"
    )
    private Long tenantId;

    public void selfCheck() throws ParseException {
        AssertUtil.notNull(channelId, "渠道id不能为空");
        AssertUtil.notNull(channelOrderId, "渠道订单号不能为空");
    }

    public BizOrderMoneyRefundCheckRequest convertToBizOrderMoneyRefundCheckRequest() {
        return BizOrderMoneyRefundCheckRequest.builder()
                .channelId(this.channelId)
                .channelOrderId(this.channelOrderId)
                .build();
    }
}
