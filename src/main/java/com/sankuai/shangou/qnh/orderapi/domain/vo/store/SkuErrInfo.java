package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

@TypeDoc(
        description = "sku错误信息"
)
@Data
public class SkuErrInfo {

    @FieldDoc(
            description = "校验失败sku"
    )
    private String skuId;

    @FieldDoc(
            description = "错误信息"
    )
    private String errMsg;

    @FieldDoc(
            description = "sku可修改状态：0:不可修改 1:可修改"
    )
    private int status;
}
