package com.sankuai.shangou.qnh.orderapi.domain.vo.app;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.ExchangeProductVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.SubProductVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2020-02-24 11:57
 * @Description:
 */
@TypeDoc(
        description = "商品信息"
)
@ApiModel("商品信息")
@Data
public class ProductVOForRefund {

    @FieldDoc(
            description = "skuId", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "skuId")
    private String skuId;

    @FieldDoc(
            description = "upc码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "upc码", required = true)
    private String upcCode;

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称", required = true)
    private String skuName;

    @FieldDoc(
            description = "商品图片URL", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品图片URL", required = true)
    private String picUrl;

    @FieldDoc(
            description = "规格", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "规格", required = true)
    private String specification;

    @FieldDoc(
            description = "售卖单位", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "售卖单位", required = true)
    private String sellUnit;

    @FieldDoc(
            description = "原总价  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "原总价  单位:分", required = true)
    private Integer originalTotalPrice;

    @FieldDoc(
            description = "实付金额  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "实付金额  单位:分", required = true)
    private Integer totalPayAmount;

    @FieldDoc(
            description = "优惠金额  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "优惠金额  单位:分", required = true)
    private Integer totalDiscountAmount;

    @FieldDoc(
            description = "单价  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "单价  单位:分", required = true)
    private Integer unitPrice;

    @FieldDoc(
            description = "购买数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "购买数量", required = true)
    private Integer count;

    @FieldDoc(
            description = "缺货数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "缺货数量", required = true)
    private Integer exchangeFromCount;

    @FieldDoc(
            description = "换货数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "换货数量", required = true)
    private Integer exchangeToCount;

    @FieldDoc(
            description = "是否退货 0-否 1-是", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "是否退货 0-否 1-是", required = true)
    private Integer isRefund;

    @FieldDoc(
            description = "申请取消数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "申请取消数量", required = true)
    private Integer refundCount;

    @FieldDoc(
            description = "摊位名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "摊位名称", required = true)
    private String boothName;

    @FieldDoc(
            description = "线下售卖价格  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "线下售卖价格  单位:分", required = true)
    private Integer offlinePrice;

    @FieldDoc(
            description = "摊位结算金额  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "摊位结算金额  单位:分", required = true)
    private Integer stallSettleAmt;

    @FieldDoc(
            description = "实际拣货数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "实际拣货数量", required = true)
    private Integer realQuantity;

    @FieldDoc(
            description = "商家在渠道的skuId", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商家在渠道的spuId")
    private String customerSkuId;

    @FieldDoc(
            description = "商家在渠道的skuId", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商家在渠道的spuId")
    private String customerSpuId;

    @FieldDoc(
            description = "赋能spu", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "赋能spu")
    private String spu;

    @FieldDoc(
            description = "商家门店SKU编码 对应channelSkuId", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商家门店SKU编码 对应channelSkuId")
    private String extCustomSkuId;

    @FieldDoc(
            description = "可退数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "可退数量")
    public Integer canRefundCount;

    @FieldDoc(
            description = "组合商品", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "组合商品")
    private List<SubProductVo> subProductVoList;

    @FieldDoc(
            description = "换货商品列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "换货商品列表")
    private List<ExchangeProductVo> exchangeProductVoList;


    @FieldDoc(
            description = "是否包含缺货货品", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否包含缺货货品")
    private Boolean isIncludeStockLackGoods;

    @FieldDoc(
            description = "解析后的商品属性", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "解析后的商品属性")
    private List<PropertiesViewVO> parsedProperties;

    @FieldDoc(
            description = "透传orderItemId信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "透传orderItemId信息")
    public Long orderItemId;

    @FieldDoc(
            description = "退款价格", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "退款价格")
    public String refundPrice;

    @FieldDoc(
            description = "赠品类型 0-平台赠品 1-线下赠品（api可能会过滤掉该类型商品）", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "赠品类型 0-平台赠品 1-线下赠品（api可能会过滤掉该类型商品）")
    private Integer giftType;


}