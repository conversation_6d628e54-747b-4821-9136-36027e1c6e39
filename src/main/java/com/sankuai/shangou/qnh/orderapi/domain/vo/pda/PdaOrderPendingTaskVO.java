package com.sankuai.shangou.qnh.orderapi.domain.vo.pda;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @createTime 2019/11/19
 * @description
 */
@TypeDoc(
        description = "订单首页模块代办"
)
@Data
@ApiModel("订单首页模块代办")
public class PdaOrderPendingTaskVO {

    @FieldDoc(
            description = "待接单数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "待接单数量", required = true)
    @NotNull
    private Integer waitToTakeOrderCount;


    @FieldDoc(
            description = "待拣货数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "待拣货数量", required = true)
    @NotNull
    private Integer waitToPickCount;


    @FieldDoc(
            description = "配送异常数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "配送异常数量", required = true)
    @NotNull
    private Integer deliveryErrorCount;


    @FieldDoc(
            description = "退款待审核数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "退款待审核数量", required = true)
    @NotNull
    private Integer waitToAuditRefundCount;


    @FieldDoc(
            description = "退货退款待审核数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "退货退款待审核数量", required = true)
    @NotNull
    private Integer waitToAuditRefundGoodsCount;

    @FieldDoc(
            description = "待配送数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "待配送数量", required = true)
    @NotNull
    private Integer waitToDeliveryCount;

    @FieldDoc(
            description = "待自提的订单数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "待自提的订单数量", required = true)
    @NotNull
    private Integer waitToSelfFetchCount;



    public static PdaOrderPendingTaskVO homePageInit(){
        PdaOrderPendingTaskVO orderPendingTaskVO = new PdaOrderPendingTaskVO();
        orderPendingTaskVO.setWaitToPickCount(0);
        orderPendingTaskVO.setWaitToTakeOrderCount(0);
        orderPendingTaskVO.setDeliveryErrorCount(0);
        orderPendingTaskVO.setWaitToAuditRefundCount(0);
        orderPendingTaskVO.setWaitToDeliveryCount(0);
        orderPendingTaskVO.setWaitToSelfFetchCount(0);
        orderPendingTaskVO.setWaitToAuditRefundGoodsCount(0);
        return orderPendingTaskVO;
    }

}
