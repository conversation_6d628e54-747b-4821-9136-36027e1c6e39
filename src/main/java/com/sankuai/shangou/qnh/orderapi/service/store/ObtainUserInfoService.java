package com.sankuai.shangou.qnh.orderapi.service.store;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.meituan.reco.pickselect.common.utils.ParamCheckUtils;
import com.meituan.shangou.saas.tenant.thrift.dto.employee.EmployeeDepDto;
import com.sankuai.meituan.reco.store.management.thrift.EmpowerTaskLogicException;
import com.sankuai.meituan.reco.store.management.thrift.Power;
import com.sankuai.meituan.reco.store.management.thrift.StorePower;
import com.sankuai.meituan.reco.store.management.thrift.UserInfo;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.PermissionGroupTypeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ValidTypeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.PermissionGroupVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryPermissionGroupRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryPermissionGroupResponse;
import com.sankuai.shangou.qnh.orderapi.cache.store.LoginCacheWrapper;
import com.sankuai.shangou.qnh.orderapi.configuration.SecretConfiguration;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.ObtainUserInfoReq;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.PoiInfo;
import com.sankuai.shangou.qnh.orderapi.exception.store.CommonLogicException;
import com.sankuai.shangou.qnh.orderapi.remote.AutoManageRemoteService;
import com.sankuai.shangou.qnh.orderapi.remote.EmployeeRemoteService;
import com.sankuai.shangou.qnh.orderapi.utils.store.CommonParamVerify;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.xerces.parsers.SecurityConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class ObtainUserInfoService {

    @Autowired
    private LoginCacheWrapper loginCacheWrapper;

    @Autowired
    private AutoManageRemoteService autoManageRemoteService;

    @Autowired
    private EmployeeRemoteService employeeRemoteService;


    private static final Long DEFAULT_POI_KEY = 1L;

    private static final int CACHE_EXPIRE_TIME = 60 * 5;

    //将storeempower中的逻辑 迁移到storeapi
    public UserInfo getUserInfo(ObtainUserInfoReq obtainUserInfoRequest) throws EmpowerTaskLogicException {
        ParamCheckUtils.nullCheck(obtainUserInfoRequest, "obtainUserInfoRequest cannot be null");
        ParamCheckUtils.emptyCheck(obtainUserInfoRequest.getToken(), "token cannot be empty");
        CommonParamVerify.checkLessThanOrEqualZero(obtainUserInfoRequest.getAppId(), "appId must >0");//获取数据组权限使用
        CommonParamVerify.checkLessThanOrEqualZero(obtainUserInfoRequest.getAccountId(), "accountId must >0");//获取数据组权限使用
        CommonParamVerify.checkLessThanOrEqualZero(obtainUserInfoRequest.getEmployeeId(), "employeeId must >0");//获取用户信息(userid username)使用

        // 从缓存中获取用户信息
        UserInfo userInfo = null;
        //如果获取缓存抛出异常，那就走降级直接查询RPC接口
        try {
            userInfo = loginCacheWrapper.getUserInfo(String.valueOf(obtainUserInfoRequest.getAccountId()));
            if (userInfo != null && !obtainUserInfoRequest.isNeedNewest()) {
                return userInfo;
            }
        } catch (Exception e) {
            log.error("get userInfo from cache is error and req= {}", obtainUserInfoRequest, e);
        }


        // 重新获取用户数据,保存cache,同步
        userInfo = new UserInfo();
        userInfo.setTenantId(obtainUserInfoRequest.getTenantId());

        // 根据employeeId从组织服务获取用户信息
        EmployeeDepDto employeeDep = getEmployeeInfo(obtainUserInfoRequest);

        userInfo.setUserId(employeeDep.getEmployeeNo());
        userInfo.setUsername(employeeDep.getEmployeeName());
        userInfo.setToken(obtainUserInfoRequest.getToken());
        // 固定token,后续有新的商家接入时,这里需要按tenantId来判断具体使用哪个token
        userInfo.setTenantToken(SecretConfiguration.getErpSkt());


        // 获取权限
        QueryPermissionGroupResponse response = queryPerByType(obtainUserInfoRequest.getTenantId(), obtainUserInfoRequest.getAccountId(),
                obtainUserInfoRequest.getAppId());

        //构建门店信息列表
        List<StorePower> storePowerList = buildStorePowerList(getStoreInfoList(response), getPermissionGroupCodeMap(response), obtainUserInfoRequest);

        userInfo.setStorePowerList(storePowerList);

        try {
            loginCacheWrapper.cacheUserInfo(userInfo, String.valueOf(obtainUserInfoRequest.getAccountId()), CACHE_EXPIRE_TIME);
        } catch (Exception e) {
            log.error("cache User Info error, userInfo = {}, key = {}", userInfo, obtainUserInfoRequest.getAccountId(), e);
        }

        return userInfo;
    }

    private List<StorePower> buildStorePowerList(List<PoiInfo> storeList, Map<Long, List<String>> dataGroupPowerMap, ObtainUserInfoReq obtainUserInfoRequest) {
        List<StorePower> storePowerList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(storeList)) {
            // 组装门店信息
            for (PoiInfo poiInfo : storeList) {
                StorePower storePower = buildStorePower(poiInfo, dataGroupPowerMap, obtainUserInfoRequest.getAppId());
                storePowerList.add(storePower);
            }
        }
        return storePowerList;
    }

    private StorePower buildStorePower(PoiInfo poiInfo, Map<Long, List<String>> dataGroupPowerMap, int appId) {
        StorePower storePower = new StorePower();
        storePower.setStoreId(poiInfo.getPoiId());
        storePower.setStoreName(poiInfo.getPoiName());
        storePower.setType(poiInfo.getType());
        if (dataGroupPowerMap.containsKey(DEFAULT_POI_KEY)) {
            Power power = new Power();
            power.setAppType(appId);
            power.setDataGroupPowerList(dataGroupPowerMap.get(DEFAULT_POI_KEY));
            storePower.setPowerList(Arrays.asList(power));
        }
        return storePower;
    }

    //查询用户信息
    private EmployeeDepDto getEmployeeInfo(ObtainUserInfoReq obtainUserInfoRequest) {
        // 根据employeeId从组织服务获取用户信息
        EmployeeDepDto employeeDep = employeeRemoteService.queryEmployeeById(obtainUserInfoRequest.getEmployeeId(), obtainUserInfoRequest.getTenantId());
        if (employeeDep == null || StringUtils.isEmpty(employeeDep.getEmployeeName())) {
            log.error("员工信息不完整, employeeDep = {}", JacksonUtils.toJson(employeeDep));
            throw new CommonLogicException("获取员工信息失败", ResultCode.FAIL);
        }
        return employeeDep;
    }

    //获取权限组信息
    private Map<Long, List<String>> getPermissionGroupCodeMap(QueryPermissionGroupResponse response) {
        Map<Long, List<String>> permissionGroupCodeMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(response.getPermissionGroupCodeList())) {
            return permissionGroupCodeMap;
        }
        for (PermissionGroupVo groupVo : response.getPermissionGroupCodeList()) {
            if (StringUtils.isNotEmpty(groupVo.getCode()) && groupVo.getValid() == ValidTypeEnum.NORMAL.getValue()) {
                // 如果是数据权限组,将期挂到所有门店/仓库下(所有门店/仓库共用同一份数据权限组)
                if (groupVo.getType() == PermissionGroupTypeEnum.DATA_PERMISSION_GROUP.getValue()) {
                    if (permissionGroupCodeMap.containsKey(DEFAULT_POI_KEY)) {
                        permissionGroupCodeMap.get(DEFAULT_POI_KEY).add(groupVo.getCode());
                    } else {
                        // 目前权限中心定义一个账号的所有门店共享一份数据权限,示是未来需要按门店配置不同数据权限,将DEFAULT_POI_KEY替换成poiId
                        permissionGroupCodeMap.put(DEFAULT_POI_KEY, Lists.newArrayList(groupVo.getCode()));
                    }

                }
            }
        }
        return permissionGroupCodeMap;
    }

    //查询门店信息
    private List<PoiInfo> getStoreInfoList(QueryPermissionGroupResponse response) {
        List<PoiInfo> storeList = Lists.newArrayList();
        boolean needCheckBindStore = true;
        if (CollectionUtils.isEmpty(response.getPermissionGroupCodeList())) {
            log.error("当前账号未找到可用门店, autoManageWrapper.queryPermissionGroup, response = {}", response);
            throw new CommonLogicException("该账号未绑定门店", ResultCode.NO_ACCOUNT_RELATED_STORE);
        }
        for (PermissionGroupVo groupVo : response.getPermissionGroupCodeList()) {
            if (StringUtils.isNotEmpty(groupVo.getCode()) && groupVo.getValid() == ValidTypeEnum.NORMAL.getValue()) {
                // 如果是门店/仓库,统计处理在门店列表中(兼容门店一期)
                if (groupVo.getType() == PermissionGroupTypeEnum.POI.getValue()
                        || groupVo.getType() == PermissionGroupTypeEnum.WAREHOUSE.getValue()
                        || groupVo.getType() == PermissionGroupTypeEnum.SHAREABLE_WAREHOUSE.getValue()) {
                    try {
                        storeList.add(new PoiInfo(Long.parseLong(groupVo.getCode()), groupVo.getName(), groupVo.getType()));
                    } catch (Exception e) {
                        log.error("门店/仓库 id转换失败, groupVo = {}", JacksonUtils.toJson(groupVo), e);
                    }
                } else if (groupVo.getType() == PermissionGroupTypeEnum.BOOTH.getValue()) {
                    // 如果是摊位权限,不需要验证当前账号是否绑定门店
                    needCheckBindStore = false;
                }
            }
        }

        if (needCheckBindStore && CollectionUtils.isEmpty(storeList)) {
            log.error("当前账号未找到可用门店, autoManageWrapper.queryPermissionGroup, response = {}", response);
            throw new CommonLogicException("该账号未绑定门店", ResultCode.NO_ACCOUNT_RELATED_STORE);
        }
        return storeList;
    }

    // 查询权限信息（门店 or 数据组权限）
    private QueryPermissionGroupResponse queryPerByType(long tenantId, long accountId, int appId){
        QueryPermissionGroupRequest request = new QueryPermissionGroupRequest();
        request.setTenantId(tenantId);
        request.setAccountId(accountId);
        request.setAppId(appId);
        //request.setType(type);
        return autoManageRemoteService.queryPermissionGroup(request);
    }
}
