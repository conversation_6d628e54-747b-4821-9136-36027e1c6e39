package com.sankuai.shangou.qnh.orderapi.constant.app;

/**
 * @description:
 * @author: gong_qish<PERSON>
 * @date: 2023/7/11
 * @time: 17:35
 * Copyright (C) 2015 Meituan
 * All rights reserved
 */
public class MiniAppConstants {

    /**
     * lion 默认列表查询起始时间
     */
    public static final String DEFAULT_TIME_CONFIG = "mini.app.order.list.begin.time";

    /**
     * 历史订单系统时间的初始值是1000
     */
    public static final long MIN_VALID_TIMESTAMP = 1000 + 1;

    /**
     * 小程序首页page
     */
    public static final Integer ORDER_COUNT_QUERY_PAGE = 1;

    /**
     * 小程序首页size
     */
    public static final Integer ORDER_COUNT_QUERY_SIZE = 0;

    /**
     * 非自提
     */
    public static final int IS_SELF_DELIVERY_NO = 0;

    /**
     * 未知
     */
    public static final String UN_KNOW = "未知";

    /**
     * 履约标签
     */
    public static final int FULFILLMENT_TAG = 1;

    /**
     * 拣货标准
     */
    public static final int PICK_TAG = 2;

    /**
     * TMS 系统，配送无异常的编码
     */
    public static final int DELIVERY_NO_EXCEPTION = 0;

    /**
     *
     */
    public static final String DEFAULT_STORE_SIZE_CONFIG = "mini.app.order.max.store.size";

    /**
     * 订单搜索默认最大时间跨度
     */
    public static final int ORDER_SEARCH_DEFAULT_MAXIMUM_DAY = 60;
    /**
     * 订单搜索最大时间跨度 -> lion配置
     */
    public static final String ORDER_SEARCH_MAXIMUM_DAY = "mini.app.order.search.maximum.day";
}

