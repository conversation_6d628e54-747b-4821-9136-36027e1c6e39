package com.sankuai.shangou.qnh.orderapi.service.pc;

import com.sankuai.meituan.shangou.empower.auth.thrift.vo.AccountInfoVo;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.AfterSaleOperatorBo;
import com.sankuai.shangou.qnh.orderapi.remote.AccountRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;


/**
 * 运营端渠道缓存api 封装成工具类，通过静态方法进行调用
 * <AUTHOR> zhan<PERSON><PERSON><PERSON>
 * @date : 2023/8/4 19:32
 */
@Slf4j
@Component
public class ChannelRedisServiceUtil implements ApplicationContextAware {

    private static ChannelRedisService service;

    private static AccountRemoteService accountClient;

    public static  Integer getAuditType(Long viewOrderId) {
        return service.getAuditType(viewOrderId);
    }

    public static AfterSaleOperatorBo getOperatorDetail(String viewOrderId) {
        return service.getOperatorDetail(viewOrderId);
    }

    public static AccountInfoVo queryAccountInfoByStaffId(Long staffId) {
        try {
            return accountClient.queryAccountInfoByStaffId(staffId);
        } catch (Exception ex) {
            log.error("queryAccountInfoByStaffId ex", ex);
            return null;
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        service = applicationContext.getBean(ChannelRedisService.class);
        accountClient = applicationContext.getBean(AccountRemoteService.class);
    }
}
