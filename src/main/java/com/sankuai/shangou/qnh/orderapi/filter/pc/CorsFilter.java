package com.sankuai.shangou.qnh.orderapi.filter.pc;

import com.sankuai.security.sdk.SecSdk;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @author: <EMAIL>
 * @date: 2017/11/20
 * @time: 下午4:15
 */
@Slf4j
public class CorsFilter implements Filter {

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        log.info("init CorsFilter");
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        String[] allowedDomain = {"*.sankuai.com", "*.meituan.com", "localhost"};
        HttpServletRequest httpRequest = (HttpServletRequest) servletRequest;
        String origin = httpRequest.getHeader("Origin");
        if (SecSdk.securityCORS(origin, allowedDomain)) {
            HttpServletResponse httpResponse = (HttpServletResponse) servletResponse;
            httpResponse.setHeader("Access-Control-Allow-Origin", origin);
            httpResponse.setHeader("Access-Control-Allow-Credentials", "true");
            httpResponse.setHeader("Access-Control-Allow-Methods", "POST,GET,PUT,PATCH,DELETE,OPTIONS");
            httpResponse.setHeader("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept, X-Token, access-token, Content-disposition");
            httpResponse.setHeader("Access-Control-Max-Age", "1728000");
        }
        // 过滤OPTIONS请求
        if (!"options".equalsIgnoreCase(httpRequest.getMethod())) {
            filterChain.doFilter(servletRequest, servletResponse);
        }
    }

    @Override
    public void destroy() {
        log.info("destroy CorsFilter");
    }

}
