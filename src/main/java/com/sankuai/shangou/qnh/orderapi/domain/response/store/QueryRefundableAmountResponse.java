package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/7/12
 * desc: 查询退款金额响应
 */
@TypeDoc(
        description = "查询退款金额响应"
)
@ApiModel("查询退款金额响应")
@Data
public class QueryRefundableAmountResponse {

    @FieldDoc(
            description = "可退金额 单位:分   -1-无法获取退款金额", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "可退金额 单位:分   -1-无法获取退款金额", required = true)
    private Integer refundCent;
}
