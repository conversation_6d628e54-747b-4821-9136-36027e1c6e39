package com.sankuai.shangou.qnh.orderapi.converter.app;


import com.google.common.collect.Lists;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.OrderBizTypeEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2019-11-22 14:43
 * @Description:
 */
@Slf4j
public class ChannelOrderConverter {

    public static final List<Integer> ALL_BIZ_TYPE_LIST = Lists.newArrayList(
            OrderBizTypeEnum.MEITUAN_WAIMAI.getValue(),
            OrderBizTypeEnum.ELE_ME.getValue(),
            OrderBizTypeEnum.JING_DONG.getValue(),
            OrderBizTypeEnum.YOU_ZAN_MIDDLE.getValue(),
            OrderBizTypeEnum.MEITUAN_DRUNK_HOURSE.getValue(),
            OrderBizTypeEnum.QUAN_QIU_WA.getValue(),
            OrderBizTypeEnum.SELF_PLATFORM.getValue()
    );

    /**
     * 根据渠道ID转换为订单业务类型
     *
     * @param channelId 渠道ID
     * @return 订单业务类型
     */
    public static Integer convertChannelId2OrderBizType(Integer channelId) {
        if (channelId == null) {
            return null;
        }

        Integer orderBizType = DynamicOrderBizType.channelId2OrderBizTypeValue(channelId);
        if (orderBizType == null) {
            log.error("未知渠道类型:{}", channelId);
        }

        return orderBizType;
    }
}
