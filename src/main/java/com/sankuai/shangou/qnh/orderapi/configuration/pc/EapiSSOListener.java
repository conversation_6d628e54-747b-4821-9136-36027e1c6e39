package com.sankuai.shangou.qnh.orderapi.configuration.pc;

import com.sankuai.it.sso.sdk.enums.AuthFailedCodeEnum;
import com.sankuai.it.sso.sdk.listener.SSOListener;
import com.sankuai.meituan.auth.vo.User;
import com.sankuai.shangou.qnh.orderapi.constant.pc.CommonConstant;
import com.sankuai.shangou.qnh.orderapi.constant.pc.Constants;
import com.sankuai.shangou.qnh.orderapi.enums.pc.CookieTokenTypeEnum;
import com.sankuai.shangou.qnh.orderapi.utils.pc.LoginUtil;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @since 2021/10/28
 */
public class EapiSSOListener implements SSOListener {
    @Override
    public String onRedirectingToSSOLogin(HttpServletRequest req, HttpServletResponse res, String callbackUri) {
        return "/api/v1" + callbackUri;
    }

    @Override
    public String onRedirectingToOriginalUrl(HttpServletRequest req, HttpServletResponse res, String ssoid, String originalUrl) {
        LoginUtil.removeLoginCookie(req, res);
        LoginUtil.addCookieUnTop(CookieTokenTypeEnum.BIZ_APP_ID, String.valueOf(CommonConstant.BAI_CHUAN_APP_ID), req, res);
        LoginUtil.addCookieUnTop(CookieTokenTypeEnum.APP_ID, String.valueOf(Constants.SAAS_B_APP_ID), req, res);
        return null;
    }

    @Override
    public boolean onSSOAuthed(HttpServletRequest req, HttpServletResponse res, User user) {
        return true;
    }

    @Override
    public boolean onSSOAuthFailed(HttpServletRequest req, HttpServletResponse res, AuthFailedCodeEnum failedCode) {
        return false;
    }

    @Override
    public void onSSOLogouted(HttpServletRequest req, HttpServletResponse res, User user) {
        LoginUtil.removeLoginCookie(req, res);
    }
}
