package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/12/30
 */
@Setter
@Getter
public class AggrPlatformDeliveryConfigVO {


    @FieldDoc(
            description = "配送渠道ID"
    )
    @ApiModelProperty(value = "配送渠道ID")
    private Integer deliveryChannelId;

    @FieldDoc(
            description = "配送渠道名称，暂时无用，前端以channelId为准自己映射名称"
    )
    @ApiModelProperty(value = "配送渠道名称")
    private String deliveryChannelName;

    @FieldDoc(
            description = "是否已开通渠道,0-未开通，1-开通"
    )
    @ApiModelProperty(value = "是否已开通")
    private int isOpen;

    @FieldDoc(
            description = "配送渠道门店id"
    )
    @ApiModelProperty(value = "配送渠道门店ID")
    private String deliveryChannelPoiId;

    @FieldDoc(
            description = "配送渠道门店的扩展信息，JSON 字符串"
    )
    @ApiModelProperty(value = "配送渠道门店的扩展信息，JSON 字符串")
    private String deliveryChannelPoiExt;

    @FieldDoc(
            description = "配送渠道商户ID"
    )
    @ApiModelProperty(value = "配送渠道商户ID")
    private String deliveryChannelMerchantId;
}
