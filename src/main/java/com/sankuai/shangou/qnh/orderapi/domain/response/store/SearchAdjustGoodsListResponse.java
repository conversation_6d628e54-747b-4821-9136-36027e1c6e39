package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.AdjustGoodVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "查询调价商品列表响应"
)
@Data
public class SearchAdjustGoodsListResponse {

    @FieldDoc(
            description = "商品名"
    )
    @ApiModelProperty(value = "是否有更多")
    private Boolean hasMore;

    @FieldDoc(
            description = "商品名"
    )
    @ApiModelProperty(value = "总数")
    private Integer totalCount;

    @FieldDoc(
            description = "调价商品列表"
    )
    private List<AdjustGoodVO> adjustGoods;

}
