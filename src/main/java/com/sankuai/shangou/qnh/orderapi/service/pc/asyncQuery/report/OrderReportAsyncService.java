package com.sankuai.shangou.qnh.orderapi.service.pc.asyncQuery.report;

import com.sankuai.shangou.qnh.orderapi.domain.request.pc.OrderFulfillmentReportReq;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.OrderFulfillmentReportQueryResp;

import java.util.List;

public interface OrderReportAsyncService {

    OrderFulfillmentReportQueryResp asyncQueryReport(OrderFulfillmentReportReq request, List<Long> poiIds, List<Long> warehouseIds);
}
