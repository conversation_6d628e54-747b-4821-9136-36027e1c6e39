package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.ChannelSkuInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/6/17
 * desc:
 */
@TypeDoc(
        description = "查询门店商品上线信息响应"
)
@Data
@ApiModel("查询门店商品上线信息响应")
public class QuerySkuChannelsInfoResponse {

    @FieldDoc(
            description = "商品编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品编码", required = true)
    private String skuCode;

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称", required = true)
    private String skuName;

    @FieldDoc(
            description = "商品渠道信息列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品渠道信息列表", required = true)
    private List<ChannelSkuInfoVO> skuChannelsInfoList;
}
