package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.BatchPoiSetBaseRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2019/12/12 19:32
 * @Description:
 */
@TypeDoc(
        description = "批量设置门店营业状态请求对象"
)
@Data
@NoArgsConstructor
public class BatchPoiShippingTimeSetRequest extends BatchPoiSetBaseRequest {

    private List<String> poiIds;

    private List<ChannelShippingTime> shippingTimes;



    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChannelShippingTime {
        private String channelId;

        private String days;

        /**
         * 如：["10:00-12:00","13:00-17:00"]
         */
        private List<String> times;

        private String startTime;

        private String endTime;
    }

}
