package com.sankuai.shangou.qnh.orderapi.controller.pc;

import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.AccountTypeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.AccountInfoVo;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.shangou.qnh.orderapi.constant.pc.Constants;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.*;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.CommonFuseResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.FulfillReportDetailResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.OrderFulfillmentReportQueryResp;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.PageInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.PermissionGroupVO;
import com.sankuai.shangou.qnh.orderapi.remote.AccountRemoteService;
import com.sankuai.shangou.qnh.orderapi.remote.AuthRemoteService;
import com.sankuai.shangou.qnh.orderapi.remote.PoiRemoteService;
import com.sankuai.shangou.qnh.orderapi.service.pc.OrderReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 中台评价Controller
 *
 * <AUTHOR>
 */
@InterfaceDoc(
        displayName = "订单履约看板",
        type = "restful",
        scenarios = "查询订单履约看板",
        description = "查询订单履约看板"
)
@Api(value = "订单履约看板")
@Slf4j
@RestController
@RequestMapping("/api/v1/orderfuse")
public class OrderFulfillmentReportController {


    @Autowired
    protected AccountRemoteService accountRemoteService;

    @Autowired
    protected PoiRemoteService poiRemoteService;

    @Autowired
    private AuthRemoteService authRemoteService;
    @Resource
    private OrderReportService orderReportService;


    @MethodDoc(
            displayName = "查询订单履约看板",
            description = "查询订单履约看板",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "查询订单履约看板请求参数",
                            type = OrderFulfillmentReportReq.class
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    )
            },
            returnValueDescription = "",
            restExampleUrl = "https://fnsaas.waimai.test.sankuai.com/api/v1/orderfuse/queryOrderFulfillmentReport",
            restExamplePostData = "type=xxx",
            restExampleResponseData = "{\"code\":0,\"data\":{\"list\":[{\"code\":\"string\",\"value\":\"string\"}]},\"msg\":\"string\"}"


    )
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @ApiOperation(value = "查询订单履约看板")
    @DataSecurity({
            @SecurityParam(value = "poiIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY),
            @SecurityParam(value = "warehouseIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @RequestMapping(value = "/queryOrderFulfillmentReport", method = RequestMethod.POST)
    public CommonFuseResponse<OrderFulfillmentReportQueryResp> queryOrderFulfillmentReport(@Valid @RequestBody OrderFulfillmentReportReq request) {
        List<Long> poiIds = Lists.newArrayList();
        List<Long> warehouseIds = Lists.newArrayList();
        request.setTenantId(ContextHolder.currentUserTenantId());
        boolean hasPermission = fillPermissionParam(request.getPoiIdList(), request.getWarehouseIdList(), poiIds, warehouseIds);
        if (!hasPermission) {
            OrderFulfillmentReportQueryResp response = new OrderFulfillmentReportQueryResp();
            PageInfoVO pageInfo = new PageInfoVO();
            pageInfo.setPage(request.getPage());
            pageInfo.setSize(request.getPageSize());
            pageInfo.setTotalPage(0);
            pageInfo.setTotalSize(0);
            response.setPageInfo(pageInfo);
            return CommonFuseResponse.success(response);
        }
        OrderFulfillmentReportQueryResp reportQueryResp = orderReportService.asyncQueryReport(request, poiIds, warehouseIds);
        return CommonFuseResponse.success(reportQueryResp);
    }


    @MethodDoc(
            displayName = "查询履约异常订单号",
            description = "查询履约异常订单号",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "查询履约异常订单号请求参数",
                            type = OrderDetailReportReq.class
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    )
            },
            returnValueDescription = "",
            restExampleUrl = "https://fnsaas.waimai.test.sankuai.com/api/v1/orderfuse/queryReportOrderDetail",
            restExamplePostData = "type=xxx",
            restExampleResponseData = "{\"code\":0,\"data\":{\"list\":[{\"code\":\"string\",\"value\":\"string\"}]},\"msg\":\"string\"}"


    )
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @ApiOperation(value = "查询履约异常订单号")
    @DataSecurity({
            @SecurityParam(value = "shopId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY),
            @SecurityParam(value = "warehouseId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @RequestMapping(value = "/queryReportOrderDetail", method = RequestMethod.POST)
    public CommonFuseResponse<FulfillReportDetailResponse> queryReportOrderDetail(@Valid @RequestBody OrderDetailReportReq request) {

        request.setTenantId(ContextHolder.currentUserTenantId());
        FulfillReportDetailResponse reportQueryResp = new FulfillReportDetailResponse();
        try {
            reportQueryResp = orderReportService.queryReportOrderDetail(request);
        } catch (Exception e) {
            log.error("queryReportOrderDetail error, request:{}", request, e);
            return CommonFuseResponse.fail(-1, e.getMessage());
        }

        return CommonFuseResponse.success(reportQueryResp);
    }


    /**
     * 填充有权限的门店或仓库id
     *
     * @param paramPoiIds
     * @param paramWarehouseIds
     * @param permissionPoiIds
     * @param permissionWarehouseIds
     * @return
     */
    private boolean fillPermissionParam(List<Long> paramPoiIds, List<Long> paramWarehouseIds, List<Long> permissionPoiIds, List<Long> permissionWarehouseIds) {
        if (CollectionUtils.isNotEmpty(paramPoiIds) || CollectionUtils.isNotEmpty(paramWarehouseIds)) {
            Optional.ofNullable(paramPoiIds).ifPresent(permissionPoiIds::addAll);

            Optional.ofNullable(paramWarehouseIds).ifPresent(permissionWarehouseIds::addAll);
            return true;
        }

        AccountInfoVo accountInfo = accountRemoteService.querySimpleAccountInfoById(ContextHolder.currentUid());
//        if (Objects.nonNull(accountInfo) && Objects.equals(accountInfo.getAccountType(), AccountTypeEnum.ADMIN.getValue())) {
//            return true;
//        }
        Long tenantId = ContextHolder.currentUserTenantId();
        //账号下的门店/仓
        List<PermissionGroupVO> permissionGroupVOS = authRemoteService.batchQueryAllPoiPermissionGroup(tenantId, ContextHolder.currentUid(), Arrays.asList(Constants.AuthType.POI_TYPE, Constants.AuthType.SHAREABLE_WAREHOUSE));
        Map<Integer, List<Long>> type2Code = permissionGroupVOS.stream().collect(Collectors.groupingBy(PermissionGroupVO::getType, Collectors.mapping(item -> Long.valueOf(item.getCode()), Collectors.toList())));
        List<Long> poiIds = type2Code.getOrDefault(Constants.AuthType.POI_TYPE, Lists.newArrayList());
        List<Long> warehouseIds = type2Code.getOrDefault(Constants.AuthType.SHAREABLE_WAREHOUSE, Lists.newArrayList());
        // 区分门店/仓id
        // 查询仓关联的poi
        if (CollectionUtils.isNotEmpty(warehouseIds)) {
            permissionWarehouseIds.addAll(warehouseIds);
            Map<Long, List<Long>> warehouseId2PoiIdMap = poiRemoteService.queryShareableWarehouseRelatedStoreId(tenantId, warehouseIds);
            if (MapUtils.isNotEmpty(warehouseId2PoiIdMap)) {
                poiIds.addAll(warehouseId2PoiIdMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList()));
                poiIds = poiIds.stream().distinct().collect(Collectors.toList());
            }
        }
        if (CollectionUtils.isNotEmpty(poiIds)) {
            permissionPoiIds.addAll(poiIds);
            return true;
        }


        return false;
    }


}
