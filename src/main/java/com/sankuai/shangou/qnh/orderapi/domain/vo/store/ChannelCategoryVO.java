package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/8/26
 * desc:
 */
@TypeDoc(
        description = "渠道分类信息"
)
@Data
@ApiModel("渠道分类信息")
public class ChannelCategoryVO {

    @FieldDoc(
            description = "分类ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "分类ID", required = true)
    private String sortCode;

    @FieldDoc(
            description = "上级分类编号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "父类ID", required = true)
    private String parentSortCode;

    @FieldDoc(
            description = "分类名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "分类名称", required = true)
    private String sortName;

    @FieldDoc(
            description = "分类等级", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "分类等级", required = true)
    private Integer depth;

    @FieldDoc(
            description = "子分类数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "子分类数量", required = true)
    private Integer subAmount;
}
