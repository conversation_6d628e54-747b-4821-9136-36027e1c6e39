package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.SkuPriceAndStock;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@TypeDoc(
        description = "商品价格库存设置请求"
)
@Data
@ApiModel("商品价格库存设置请求")
public class UpdatePriceAndStockForAppRequest {

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID", required = true)
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "商品价格库存信息列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品价格库存信息列表", required = true)
    @NotNull
    private List<SkuPriceAndStock> skuPriceAndStocks;
}
