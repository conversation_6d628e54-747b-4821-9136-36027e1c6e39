package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.sankuai.meituan.shangou.empower.price.client.dto.config.StoreSkuPriceFilterConditionDTO;
import lombok.Data;

/**
 * 门店商品价格过滤器查询条件
 *
 * <AUTHOR>
 */
@Data
public class StoreSkuPriceFilterCondition {

    /**
     * 门店id
     */
    private Long storeId;

    /**
     * skuId
     */
    private String skuId;

    /**
     * 称重类型
     */
    private Integer weightType;

    /**
     * 重量
     */
    private Integer weight;

    public static StoreSkuPriceFilterCondition build(ChannelSkuForAppVO skuInfo) {

        StoreSkuPriceFilterCondition filterConditionDomain = new StoreSkuPriceFilterCondition();
        filterConditionDomain.setStoreId(skuInfo.getStoreId());
        filterConditionDomain.setSkuId(skuInfo.getSku());
        filterConditionDomain.setWeightType(skuInfo.getWeightType());
        filterConditionDomain.setWeight(skuInfo.getWeight());

        return filterConditionDomain;
    }

    public static StoreSkuPriceFilterCondition build(Long storeId, StoreSkuVO storeSkuVO) {

        StoreSkuPriceFilterCondition filterConditionDomain = new StoreSkuPriceFilterCondition();
        filterConditionDomain.setStoreId(storeId);
        filterConditionDomain.setSkuId(storeSkuVO.getSku());
        filterConditionDomain.setWeightType(storeSkuVO.getWeightType());
        filterConditionDomain.setWeight(storeSkuVO.getWeight());

        return filterConditionDomain;
    }



    public StoreSkuPriceFilterConditionDTO convert2StoreSkuPriceFilterConditionDTO() {
        StoreSkuPriceFilterConditionDTO dto = new StoreSkuPriceFilterConditionDTO();
        dto.setStoreId(this.storeId);
        dto.setSkuId(this.skuId);
        dto.setWeightType(this.weightType);
        dto.setWeight(this.weight);
        return dto;
    }

}
