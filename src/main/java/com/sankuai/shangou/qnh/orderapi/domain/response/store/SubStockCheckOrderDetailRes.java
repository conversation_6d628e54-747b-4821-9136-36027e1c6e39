package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "盘点子单明细信息",
        authors = {
                "qianteng"
        }
)
@Data
@ApiModel("盘点子单明细信息")
public class SubStockCheckOrderDetailRes {
    @FieldDoc(
            description = "子单id"
    )
    private String subStockCheckOrderId; // required
    @FieldDoc(
            description = "sku数量"
    )
    private int skuCount; // required
    @FieldDoc(
            description = "标注"
    )
    private String comment; // required
    @FieldDoc(
            description = "提交者"
    )
    private String submitor; // required
    @FieldDoc(
            description = "提交时间"
    )
    private String submitTime; // required
    @FieldDoc(
            description = "sku列表"
    )
    private List<StockCheckSkuItem> skuCheckList; // required
}
