package com.sankuai.shangou.qnh.orderapi.domain.response.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.CommentReplyTemplateBO;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.PageRequest;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.CommentReplyTemplateVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.OrderFulfillmentReportVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.PageInfoVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@TypeDoc(
        description = "查询订单履约看板响应",
        authors = "xihaiyu"
)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class OrderFulfillmentReportQueryResp {
    @FieldDoc(
            description = "总单量", requiredness = Requiredness.OPTIONAL
    )
    private int totalOrderCount;
    @FieldDoc(
            description = "履约中订单", requiredness = Requiredness.OPTIONAL
    )
    private int totalDealingOrderCount;
    @FieldDoc(
            description = "待处理退单", requiredness = Requiredness.OPTIONAL
    )
    private int totalDealingAfsOrderCount;
    @FieldDoc(
            description = "未回复差评", requiredness = Requiredness.OPTIONAL
    )
    private int totalNoReplyBadCommentCount;

    @FieldDoc(
            description = "订单履约看板门店数据", requiredness = Requiredness.OPTIONAL
    )
    private List<OrderFulfillmentReportVO> orderFulfillmentDetailReportList;


    @FieldDoc(
            description = "分页信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "分页信息", required = true)
    private PageInfoVO pageInfo;

}
