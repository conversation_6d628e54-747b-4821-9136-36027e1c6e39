package com.sankuai.shangou.qnh.orderapi.domain.result.pc;


import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: Result
 * @Description: API响应结果生成工具
 * @date 25/06/2018 7:42 PM
 **/
public class ResultBuilder {


  public static <T> Result<T>  buildSuccessResult() {
    return new Result<>(BaseResult.SUCCESS);
  }

  public static Result buildSuccessResult(Object data) {
    return new Result(BaseResult.SUCCESS,data);
  }


  public static <T> Result<ListResultData<T>> buildSuccessListResult(List<T> list) {
    return new Result(BaseResult.SUCCESS, new ListResultData<>(list));
  }


  public static <T> Result<T> buildSuccess(T data) {
    return new Result(BaseResult.SUCCESS, data);
  }

  public static <T> Result<T> buildFailResult(int code, String message) {
    return buildResult(code, message, null);
  }

  public static Result buildSuccessResult(String message, Object data) {
    return new Result()
        .setCode(BaseResult.SUCCESS.getCode())
        .setMsg(message)
        .setData(data);
  }

  public static Result buildFailResult() {
    return new Result(BaseResult.FAIL);
  }

  public static Result buildFailResult(String message) {
    return new Result()
        .setCode(BaseResult.FAIL.getCode())
        .setMsg(message);
  }

  public static Result buildFailResult(String message, Object data) {
    return new Result()
        .setCode(BaseResult.FAIL.getCode())
        .setMsg(message)
        .setData(data);
  }



  public static Result buildParamInvalidResult(String message, String fieldName) {
    return new Result()
            .setCode(BaseResult.PARAM_INVALID.getCode())
            .setMsg(message)
            .setData(fieldName);
  }


  public static Result buildFailResult(Object data) {
    return new Result(BaseResult.FAIL,data);
  }

  public static Result buildBizFailResult(Object data) {
    return new Result(BaseResult.BIZ_FAIL,data);
  }

  public static Result buildBizFailResult(String message, Object data) {
    return new Result(BaseResult.BIZ_FAIL,data).setMsg(message);
  }

  public static Result buildResult(int code, String message, Object data) {
    return new Result()
        .setCode(code)
        .setMsg(message)
        .setData(data);
  }

  public static Result buildResult(BaseResult baseResult, Object data) {
    return new Result(baseResult,data);
  }

  public static Result buildResult(BaseResult baseResult) {
    return new Result(baseResult);
  }

  private ResultBuilder() {
  }
}
