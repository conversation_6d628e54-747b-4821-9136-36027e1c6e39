package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.facebook.swift.codec.ThriftField;
import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.DrunkHorseGiftBagVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/***
 * author : <EMAIL>
 * date : 2024/8/21
 * time : 14:38
 * 描述 :
 **/
@TypeDoc(
        description = "歪马礼袋"
)
@ApiModel("歪马礼袋")
@Data
public class DrunkHorseGiftBagVO {

    @FieldDoc(
            description = "主品货号"
    )
    @ApiModelProperty(name = "主品货号", required = true)
    private String belongSkuId;

    @FieldDoc(
            description = "礼袋货品skuId"
    )
    @ApiModelProperty(name = "主品货号", required = true)
    private String materialSkuId;

    @FieldDoc(
            description = "礼袋货品名称"
    )
    @ApiModelProperty(name = "主品货号", required = true)
    private String materialSkuName;

    @FieldDoc(
            description = "礼袋图片"
    )
    @ApiModelProperty(name = "主品货号", required = true)
    private String picUrl;


    @FieldDoc(
            description = "礼袋类型，1:品牌礼袋， 2:歪马礼袋"
    )
    @ApiModelProperty(name = "主品货号", required = true)
    private int type;

    @FieldDoc(
            description = "礼袋应赠数量"
    )
    @ApiModelProperty(name = "主品货号", required = true)
    private int cnt;

    @FieldDoc(
            description = "调整后的数量"
    )
    @ApiModelProperty(name = "主品货号", required = true)
    private int resetCnt;

    @FieldDoc(
            description = "退款数量"
    )
    @ApiModelProperty(name = "主品货号", required = true)
    private int refundCnt;

    @FieldDoc(
            description = "规格"
    )
    @ApiModelProperty(name = "主品货号", required = true)
    private String spec;

    @FieldDoc(
            description = "upc"
    )
    @ApiModelProperty(name = "主品货号", required = true)
    private String upc;

    @FieldDoc(
            description = "是否缺货"
    )
    @ApiModelProperty(name = "是否缺货", required = false)
    private Boolean isIncludeStockLackGoods;


    public static List<DrunkHorseGiftBagVO> buildDrunkHorseGiftBag(List<DrunkHorseGiftBagVo> drunkHorseGiftBagVoList) {
        if (CollectionUtils.isEmpty(drunkHorseGiftBagVoList)) {
            return Lists.newArrayList();
        }
        return drunkHorseGiftBagVoList.stream().map(DrunkHorseGiftBagVO::buildDrunkHorseGiftBagVO).collect(Collectors.toList());
    }


    private static DrunkHorseGiftBagVO buildDrunkHorseGiftBagVO(DrunkHorseGiftBagVo drunkHorseGiftBagVo){
        DrunkHorseGiftBagVO drunkHorseGiftBagVO = new DrunkHorseGiftBagVO();
        drunkHorseGiftBagVO.setBelongSkuId(drunkHorseGiftBagVo.getBelongSkuId());
        drunkHorseGiftBagVO.setMaterialSkuId(drunkHorseGiftBagVo.getMaterialSkuId());
        drunkHorseGiftBagVO.setMaterialSkuName(drunkHorseGiftBagVo.getMaterialSkuName());
        drunkHorseGiftBagVO.setPicUrl(drunkHorseGiftBagVo.getPicUrl());
        drunkHorseGiftBagVO.setType(drunkHorseGiftBagVo.getType());
        drunkHorseGiftBagVO.setCnt(drunkHorseGiftBagVo.getCnt());
        drunkHorseGiftBagVO.setResetCnt(drunkHorseGiftBagVo.getResetCnt());
        drunkHorseGiftBagVO.setRefundCnt(drunkHorseGiftBagVo.getRefundCnt());
        drunkHorseGiftBagVO.setSpec(drunkHorseGiftBagVo.getSpec());
        drunkHorseGiftBagVO.setUpc(drunkHorseGiftBagVo.getUpc());
//        drunkHorseGiftBagVO.setIsIncludeStockLackGoods();
        return drunkHorseGiftBagVO;
    }

}
