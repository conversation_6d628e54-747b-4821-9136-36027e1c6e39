package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;


@Setter
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(
        description = "查询轨迹"
)
public class TrackRequest implements BaseRequest {

    @FieldDoc(
            description = "订单号"
    )
    @ApiModelProperty(value = "订单号", required = true)
    private String orderId;

    @FieldDoc(
            description = "渠道编码"
    )
    @ApiModelProperty(value = "渠道编码", required = true)
    private Integer channelId;


    @FieldDoc(
            description = "操作类型"
    )
    @ApiModelProperty(value = "操作类型", required = false)
    /**
     * @see com.sankuai.shangou.qnh.orderapi.enums.pc.TrackTypeEnum
     */
    private Integer operatorType;

}



