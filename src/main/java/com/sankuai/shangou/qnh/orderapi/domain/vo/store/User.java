package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.google.common.collect.Lists;
import com.sankuai.meituan.reco.store.management.thrift.Power;
import com.sankuai.meituan.reco.store.management.thrift.StorePower;
import com.sankuai.meituan.reco.store.management.thrift.UserInfo;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.PermissionGroupTypeEnum;
import com.sankuai.shangou.qnh.orderapi.configuration.SecretConfiguration;
import com.sankuai.shangou.qnh.orderapi.constant.store.ProjectConstants;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

@Getter
public class User {
	private long tenantId;

	private String userId;

	private String username;

	// 业务交互token，当前是一个与康品会约定的固定值
	private String tenantToken;

	private List<AppStorePower> storePowerList;

	private long employeeId;

	private long accountId;

	private int accountType;

	public User(UserInfo userInfo) {
		this.userId = userInfo.getUserId();
		this.username = userInfo.getUsername();
		// 内部使用tenantToken完成业务交互
		this.tenantToken = SecretConfiguration.getErpSkt();
		this.tenantId = userInfo.getTenantId();

		if(CollectionUtils.isNotEmpty(userInfo.getStorePowerList())) {
			storePowerList = Lists.transform(userInfo.getStorePowerList(), AppStorePower::new);
		}
	}

	public User(UserInfo userInfo, long employeeId) {
		this.userId = userInfo.getUserId();
		this.username = userInfo.getUsername();
		// 内部使用tenantToken完成业务交互
		this.tenantToken = SecretConfiguration.getErpSkt();
		this.tenantId = userInfo.getTenantId();
		this.employeeId = employeeId;

		if(CollectionUtils.isNotEmpty(userInfo.getStorePowerList())) {
			storePowerList = Lists.transform(userInfo.getStorePowerList(), AppStorePower::new);
		}
	}

	public User(UserInfo userInfo, long employeeId, long accountId, int accountType) {
		this.userId = userInfo.getUserId();
		this.username = userInfo.getUsername();
		// 内部使用tenantToken完成业务交互
		this.tenantToken = SecretConfiguration.getErpSkt();
		this.tenantId = userInfo.getTenantId();
		this.employeeId = employeeId;
		this.accountId = accountId;
		this.accountType = accountType;
		if(CollectionUtils.isNotEmpty(userInfo.getStorePowerList())) {
			storePowerList = Lists.transform(userInfo.getStorePowerList(), AppStorePower::new);
		}
	}

	// 根据门店id找到对应的数据权限组，没找到返回null
	public AppStorePower findPowerByStore(long storeId){
		if(CollectionUtils.isNotEmpty(storePowerList)){
			for(AppStorePower power : storePowerList){
				if(power.getStoreId() == storeId){
					return power;
				}
			}
		}
		return null;
	}

	// 根据门店id找到对应的数据权限组，没找到返回null
	public AppStorePower findEntity(long entityId, int entityType){
		if(CollectionUtils.isNotEmpty(storePowerList)){
			for(AppStorePower power : storePowerList){
				if(power.getStoreId() == entityId){
					PermissionGroupTypeEnum e;
					return power;
				}
			}
		}
		return null;
	}

	@Getter
	public static class AppStorePower {
		private long storeId;

		private String storeName;

		private int type;

		private List<Integer> functionPowerList;

		private List<String> dataGroupList;

		AppStorePower(StorePower storePower) {
			this.storeId = storePower.getStoreId();
			this.storeName = storePower.getStoreName();
			this.type = storePower.getType();

			if(CollectionUtils.isNotEmpty(storePower.getPowerList())) {
				for(Power power : storePower.getPowerList()) {
					if(power.getAppType() == ProjectConstants.APP_TYPE) {
						this.functionPowerList = power.getFunctionPowerList();
						this.dataGroupList = power.getDataGroupPowerList();
						break;
					}
				}
			}
		}
	}
}
