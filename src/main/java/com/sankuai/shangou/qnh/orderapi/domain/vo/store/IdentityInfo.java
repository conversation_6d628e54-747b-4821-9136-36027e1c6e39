package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.sankuai.meituan.shangou.empower.auth.sdk.bean.AppIdEnum;
import com.sankuai.meituan.shangou.empower.auth.sdk.bean.SessionInfo;
import com.sankuai.meituan.shangou.empower.auth.sdk.context.SessionContext;
import com.sankuai.shangou.qnh.orderapi.constant.app.ProjectConstants;


import com.sankuai.shangou.qnh.orderapi.domain.dto.app.LazyAppFullStoreIds;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.User;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/10.
 */
@Data
public class IdentityInfo {
    private User user;

    // 与前端交互的token
    private String token;

    private String uuid;

    private String appId;

    private String os;

    private String appVersion;//大版本号

    private String jsVersion;//小版本号

    private String mrnApp;      //  js checkUpdateV2 需要app参数

    private String mrnVersion;  //  js checkUpdateV2 需要version参数

    /**
     * app授权Id
     * 取数逻辑：先取 Header 中 subAuthId，没有就取 Header 中 subAuthId
     * 如果要明确取权限应用 ID，请加上 @Auth 注解后使用 {@link SessionContext#getCurrentSession()} {@link SessionInfo#getAuthAppId()}
     * 如果要明确取权限子应用 ID，请加上 @Auth 注解后使用 {@link SessionContext#getCurrentSession()} {@link SessionInfo#getAuthSubAppId()} ()}
     */
    private String authId;


    /**
     * 渠道信息
     */
    private String appChannel;

    @Getter(value = AccessLevel.PRIVATE)
    private String storeIds;


    @Getter(value = AccessLevel.PRIVATE)
    private LazyAppFullStoreIds lazyAppFullStoreIds;

    public List<Long> getStoreIdList() {
        if (StringUtils.isBlank(storeIds)) {
            return Lists.newArrayList();
        }
        /**
         * 目前仅小程序支持,其余端仍保持原有逻辑
         */
        boolean isFullStoreId = isFullStoreMode();
        if (isMiniApp() && isFullStoreId && Objects.nonNull(lazyAppFullStoreIds)) {
            return lazyAppFullStoreIds.fetchStoreIds4MiniApp();
        }

        return Arrays.stream(storeIds.split(","))
                .distinct()
                .filter(e -> !e.isEmpty())
                .map(Long::valueOf)
                .collect(Collectors.toList());
    }

    public boolean isFullStoreMode() {
        return isFullStoreId();
    }

    private boolean isFullStoreId() {
        return ProjectConstants.FULL_STORE_ID.equals(storeIds);
    }

    public boolean isMiniApp() {
        return Optional.ofNullable(SessionContext.getCurrentSession())
                .map(SessionInfo::getAuthAppId)
                .map(authAppId -> Objects.equals(authAppId, AppIdEnum.APP_40.getAuthAppId()))
                .orElse(Boolean.FALSE);
    }
}
