// Copyright (C) 2020 Meituan
// All rights reserved
package com.sankuai.shangou.qnh.orderapi.interceptor.store;


import com.dianping.zebra.util.StringUtils;
import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.sankuai.meituan.shangou.saas.common.encrypt.AESUtil;
import com.sankuai.security.sdk.SecSdk;
import com.sankuai.shangou.qnh.orderapi.constant.store.LoginConstants;
import com.sankuai.shangou.qnh.orderapi.context.store.H5LoginContext;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.CommonResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.H5MRTokenDetail;
import com.sankuai.shangou.qnh.orderapi.utils.store.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.util.Date;
import java.util.Objects;

/**
 * h5登录拦截器
 * <AUTHOR>
 * @version 1.0
 * @created 2020/3/2 下午12:08
 **/
@Slf4j
@Component
public class H5TokenInterceptor extends HandlerInterceptorAdapter {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        try {
            setResponseHeaderInfo(request, response);
            if ("options".equalsIgnoreCase(request.getMethod())) {
                return true;
            }

            String urlToken = request.getHeader("urlToken");
            if (verifyToken(urlToken)) {
                return true;
            }
        } catch (Exception e){
            log.error("H5TokenInterceptor error.", e);
        }

        // 链接错误，按链接失效处理
        try {
            CommonResponse result = new CommonResponse(LoginConstants.TOKEN_EXPIRED_CODE, "不好意思，该链接已失效", null);
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            String data = JacksonUtils.toJson(result);
            PrintWriter writer = response.getWriter();
            writer.write(data);
            writer.flush();
        } catch (Exception e) {
            log.error("H5TokenInterceptor response error.", e);
        }
        return false;
    }

    private boolean verifyToken(String urlToken) {
        if (StringUtils.isBlank(urlToken)) {
            return false;
        }
        String tokenJson = AESUtil.decrypt(urlToken, LoginConstants.H5_URL_ENCRYPT_KEY);
        try {
            H5MRTokenDetail tokenDetail = JacksonUtils.fromJson(tokenJson, H5MRTokenDetail.class);
            String currentDay = DateUtils.format(new Date(), DateUtils.YYYY_MM_DD);
            boolean isValid = StringUtils.equals(currentDay, tokenDetail.getDateFormat())
                    && Objects.nonNull(tokenDetail.getTenantId())
                    && Objects.nonNull(tokenDetail.getStoreId())
                    && tokenDetail.getTenantId().longValue() > 0
                    && tokenDetail.getStoreId().longValue() > 0;
            if (isValid) {
                H5LoginContext.getLoginInfo().setH5MRTokenDetail(tokenDetail);
            }
            return isValid;
        } catch (Exception e) {
            log.warn("url token is error, token [{}].", tokenJson, e);
        }
        return false;
    }

    private void setResponseHeaderInfo(HttpServletRequest request, HttpServletResponse response) {

        String[] allowedDomain = {"*.sankuai.com","*.meituan.com","localhost"};
        String origin = request.getHeader("Origin");
        if (SecSdk.securityCORS(origin, allowedDomain)) {
            response.setHeader("Access-Control-Allow-Origin", origin);
            response.setHeader("Access-Control-Allow-Credentials", "true");
            response.setHeader("Access-Control-Allow-Methods", "POST,OPTIONS");
            response.setHeader("Access-Control-Allow-Headers",
                    "Origin, X-Requested-With, Content-Type, Accept, X-Token, access-token, Content-disposition, urlToken,"
                            + LoginConstants.H5_LOGIN_HEADER_PARAM);
            response.setHeader("Access-Control-Max-Age", "1728000");
        }
    }
}