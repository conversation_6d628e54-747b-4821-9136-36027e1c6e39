package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.RecentPurchasePriceVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;

@TypeDoc(
        description = "获取调价商品信息响应"
)
@Data
@ApiModel("获取调价商品信息响应")
public class GetSkuPriceInfoResponse {
    @FieldDoc(
            description = "商品名", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "商品名", required = true)
    @NotEmpty
    public String name;

    @FieldDoc(
            description = "商品编号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "商品编号", required = true)
    @NotEmpty
    public String skuId;

    @FieldDoc(
            description = "基本单位", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "基本单位", required = true)
    @NotEmpty
    public String unit;

    @FieldDoc(
            description = "标准售价", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "标准售价", required = true)
    @NotNull
    public String storePrice;

    @FieldDoc(
            description = "VIP售价", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "VIP售价", required = true)
    @NotNull
    public String storeMemberPrice;

    @FieldDoc(
            description = "允许最小售价", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "允许最小售价", required = true)
    @NotNull
    public String minPrice;

    @FieldDoc(
            description = "允许最大售价", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "允许最大售价", required = true)
    @NotNull
    public String maxPrice;

    @FieldDoc(
            description = "上次调价时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "上次调价时间", required = true)
    public String lastAdjustTime;

    @FieldDoc(
            description = "最近收货信息列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "最近收货信息列表", required = true)
    public List<RecentPurchasePriceVO> recentPurchasePriceList;
}
