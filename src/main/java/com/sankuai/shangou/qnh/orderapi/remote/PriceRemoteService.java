package com.sankuai.shangou.qnh.orderapi.remote;

import com.sankuai.meituan.shangou.xsupply.price.client.wac.price.query.BasicSkuWacPriceQueryThriftService;
import com.sankuai.meituan.shangou.xsupply.price.client.wac.price.query.request.WarehouseBasicSkuParam;
import com.sankuai.meituan.shangou.xsupply.price.client.wac.price.query.request.WarehouseBasicSkuWacPriceQueryRequest;
import com.sankuai.meituan.shangou.xsupply.price.client.wac.price.query.response.WarehouseBasicSkuWacPriceQueryResponse;
import com.sankuai.meituan.shangou.xsupply.price.client.wac.price.query.dto.WarehouseBasicSkuWacPriceDto;
import com.sankuai.shangou.qnh.orderapi.utils.pc.MoneyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/2 14:44
 * @Description:
 */
@Slf4j
@Service
public class PriceRemoteService {

    @Resource
    private BasicSkuWacPriceQueryThriftService basicSkuWacPriceQueryThriftService;

    /**
     * 批量获取货品的wac价(元，两位小数)
     * @param tenantId
     * @param shopId
     * @return
     */
    public Map<String, Double> calculateTotalGoodsWACPrice(Long tenantId, Long shopId, List<String> goodsIds) {
        try {
            WarehouseBasicSkuWacPriceQueryRequest request = new WarehouseBasicSkuWacPriceQueryRequest();
            request.setTenantId(tenantId);
            if(CollectionUtils.isEmpty(goodsIds)) {
                return new HashMap<>();
            }
            List<WarehouseBasicSkuParam> params = goodsIds.stream().map(goodsId -> new WarehouseBasicSkuParam(shopId, goodsId)).collect(Collectors.toList());
            request.setWarehouseBasicSkuParamList(params);
            WarehouseBasicSkuWacPriceQueryResponse response = basicSkuWacPriceQueryThriftService.batchQueryWarehouseBasicSkuWacPrice(request);
            if(response != null && CollectionUtils.isNotEmpty(response.getWarehouseBasicSkuWacPriceList())) {
                return response.getWarehouseBasicSkuWacPriceList().stream().collect(Collectors.toMap(
                        WarehouseBasicSkuWacPriceDto::getSkuId,
                        goods -> MoneyUtils.yuanStrToYuanDouble(goods.getInclTaxLogisticsWac())
                ));
            }
        } catch (Exception ex) {
            log.error("批量获取货品的wac价(元，两位小数)失败, tenantId:{}, shopId:{}, goodsIds:{}", tenantId, shopId, goodsIds, ex);
            return new HashMap<>();
        }
        return new HashMap<>();
    }
}
