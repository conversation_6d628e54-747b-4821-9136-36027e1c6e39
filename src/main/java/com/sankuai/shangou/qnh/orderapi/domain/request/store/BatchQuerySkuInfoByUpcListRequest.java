package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @description:
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2019-12-02
 **/
@TypeDoc(
        description = "通过UPC列表查询标品信息"
)
@Data
@ApiModel("通过UPC列表查询标品信息")
public class BatchQuerySkuInfoByUpcListRequest {

    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户ID")
    @NotNull
    private Long tenantId;

    @FieldDoc(
            description = "upc列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "upc列表")
    @NotNull
    private List<String> upcList;

}
