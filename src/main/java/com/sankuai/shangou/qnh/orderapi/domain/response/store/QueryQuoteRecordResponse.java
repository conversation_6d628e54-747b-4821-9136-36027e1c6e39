package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.QueryQuoteRecordVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "提价记录查询结果响应"
)
@Data
@ApiModel("提价记录查询结果响应")
public class QueryQuoteRecordResponse {

    @FieldDoc(
            description = "是否存在更多记录", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "是否存在更多记录", required = true)
    private Boolean hasMore;

    @FieldDoc(
            description = "总记录数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "总记录数", required = true)
    private Integer totalCount;

    @FieldDoc(
            description = "提价历史记录信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "提价历史记录信息", required = true)
    private List<QueryQuoteRecordVO> queryQuoteRecords;
}
