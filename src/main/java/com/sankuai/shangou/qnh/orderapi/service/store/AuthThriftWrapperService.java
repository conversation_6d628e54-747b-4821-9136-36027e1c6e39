package com.sankuai.shangou.qnh.orderapi.service.store;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.meituan.reco.pickselect.common.exception.FallbackException;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.store.saas.infrastructure.shield.common.monitor.CommonMonitorTransaction;
import com.sankuai.meituan.shangou.empower.auth.sdk.bean.SessionInfo;
import com.sankuai.meituan.shangou.empower.auth.sdk.context.SessionContext;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.PermissionGroupTypeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.service.AuthThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.AccountInfoVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.DataPermissionVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.PermissionGroupVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.PermissionVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryAccountInfoRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryAccountInfoResponse;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryPermissionGroupRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryPermissionGroupResponse;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QuerySimpleAccountInfoListResponse;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QuerySimpleAccountInfoRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.ResourceInfoVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.Result;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.RoleInfoVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.request.AuthPermissionAndDataAuthRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.response.AuthPermissionAndDataAuthResponse;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.qnh.orderapi.context.store.AppAuthContext;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.User;
import com.sankuai.shangou.qnh.orderapi.enums.store.AppAuthIdConstants;
import com.sankuai.shangou.qnh.orderapi.exception.store.CommonLogicException;
import com.sankuai.shangou.qnh.orderapi.remote.SacAccountRemoteService;
import com.sankuai.shangou.qnh.orderapi.utils.store.ApiMethodParamThreadLocal;
import com.sankuai.shangou.qnh.orderapi.utils.store.MccDynamicConfigUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 权限相关
 * Created by yangli on 19/1/30.
 * Mod by linjiayu
 */
@Component
@Slf4j
@Rhino
public class AuthThriftWrapperService {
    @Resource
    private AuthThriftService.Iface authThriftService;
    @Autowired
    private SacAccountRemoteService sacAccountRemoteService;

    @MethodLog(logRequest = true, logResponse = true)
    public <T> List<T> queryPermissionGroupId(User user, PermissionGroupTypeEnum type, Function<String, T> transfer) {
        QueryPermissionGroupRequest request = new QueryPermissionGroupRequest();
        request.setTenantId(user.getTenantId());
        request.setAccountId(user.getAccountId());
        request.setType(type.getValue());

        try {
            log.info("authThriftService, req:{}", request);
            QueryPermissionGroupResponse response = authThriftService.queryPermissionGroupByTokenAndPermissionType(request);
            log.info("authThriftService, response:{}", response);
            if (CollectionUtils.isEmpty(response.getPermissionGroupCodeList())) {
                log.warn("authThriftService, 返回AccountPermissionGroupRelVo为空");
                return Lists.newArrayList();
            }

            return response.getPermissionGroupCodeList().stream().map(PermissionGroupVo::getCode).map(transfer).collect(Collectors.toList());

        } catch (TException e) {
            log.error("authThriftService.queryAccountBindPermissionGroupByResourceCode()错误:", e);
            return Lists.newArrayList();
        }
    }

    @MethodLog(logRequest = true, logResponse = true)
    @CommonMonitorTransaction
    @SuppressWarnings("WeakerAccess")
    public Map<Integer, List<String>> getCurrentAccountAllPermissionCodes() {
        Map<Integer, List<String>> permissionCodeMap = new HashMap<>();
        try {
            AccountInfoVo accountInfoVo = getCurrentAccount();
            for (RoleInfoVo roleInfoVo : accountInfoVo.getRoleList()) {
                for (PermissionVo permissionVo : roleInfoVo.getPermissionList()) {
                    if (permissionCodeMap.containsKey(permissionVo.getAppId())){
                        List<String> permissionCodes = permissionCodeMap.get(permissionVo.getAppId());
                        permissionCodes.add(permissionVo.getCode());
                        permissionCodeMap.put(permissionVo.getAppId(),permissionCodes);
                    }else {
                        List<String> permissionCodes = Lists.newArrayList();
                        permissionCodes.add(permissionVo.getCode());
                        permissionCodeMap.put(permissionVo.getAppId(),permissionCodes);
                    }
                }
            }
            return permissionCodeMap;
        } catch (TException e) {
            throw new RuntimeException("查询权限异常", e);
        }
    }

    /**
     * 对指定账号进行元素权限的鉴权
     * @return Map<permissionCode, Boolean>
     * @throws TException Any
     */
    @Degrade(rhinoKey = "AuthThriftWrapperService.authPermissionAndDataAuth",
            fallBackMethod = "authPermissionAndDataAuthFallback",
            timeoutInMilliseconds = 3000,
            ignoreExceptions = CommonLogicException.class)
    @MethodLog(logRequest = true, logResponse = true)
    @CommonMonitorTransaction
    @SuppressWarnings("WeakerAccess")
    public Map<String, Boolean> authPermissionAndDataAuth(long accountId, long shopId, List<String> permissionCodes) {
        try {
            if (CollectionUtils.isNotEmpty(permissionCodes)){
                int dataAuthType = PermissionGroupTypeEnum.POI.getValue();
                String authId = ApiMethodParamThreadLocal.getIdentityInfo().getAuthId();
                if (StringUtils.isNotBlank(authId) && AppAuthIdConstants.SHARED_WAREHOUSE.val() == Integer.parseInt(authId)) {
                    // 共享前置仓数据权限
                    dataAuthType = PermissionGroupTypeEnum.SHAREABLE_WAREHOUSE.getValue();
                }
                int appId;
                if(StringUtils.isEmpty(authId)) {
                    appId = AppAuthIdConstants.LV_YUE_ZHU_SHOU.val();
                } else {
                    appId = Integer.parseInt(authId);
                }

                AuthPermissionAndDataAuthRequest request = new AuthPermissionAndDataAuthRequest();
                request.setAccountId(accountId);
                request.setAppId(appId);
                request.setDataAuthCode(String.valueOf(shopId));
                request.setDataAuthType(dataAuthType);
                request.setPermissionCodes(permissionCodes);
                AuthPermissionAndDataAuthResponse response = authThriftService.authPermissionAndDataAuth(request);
                return response.getIsAccountHaveAuth();
            }
        } catch (TException e) {
            log.error("请求元素权限鉴权出错,accountId:{}",accountId,  e);
        }
        return Maps.newHashMap();//默认返回空
    }


    private Map<String, Boolean> authPermissionAndDataAuthFallback(long accountId, long shopId, List<String> permissionCodes) {
        return Maps.newHashMap();//默认返回空
    }

    /**
     * 查询登录账户信息
     *
     * @return account info
     * @throws TException Any
     */
    @Degrade(rhinoKey = "AuthThriftWrapperService.getCurrentAccount",
            fallBackMethod = "getCurrentAccountFallback",
            timeoutInMilliseconds = 3000,
            ignoreExceptions = CommonLogicException.class)
    @MethodLog(logRequest = true, logResponse = true)
    @CommonMonitorTransaction
    @SuppressWarnings("WeakerAccess")
    public AccountInfoVo getCurrentAccount() throws TException {
        if (AppAuthContext.getContext().getAccount() != null) {
            return AppAuthContext.getContext().getAccount();
        }
        SessionInfo sessionInfo = SessionContext.getCurrentSession();
        QueryAccountInfoResponse resp = authThriftService.queryAccountInfoByAccountIdAndAppId(
                new QueryAccountInfoRequest(sessionInfo.getAccountId(), sessionInfo.getTenantId())
        );
        log.info("account resp, tenantId:{}, accountId:{}, resp:{}", sessionInfo.getTenantId(), sessionInfo.getAccountId(), resp);
        nullResponseCheck(resp, "查询权限异常");
        validateStatus(resp.getResult(), "查询权限异常");
        AppAuthContext.getContext().setAccount(resp.getAccountInfo());
        return resp.getAccountInfo();
    }

    private List<DataPermissionVo> getCurrentAccountFallback() {
        throw new FallbackException("AuthThriftWrapper.getCurrentAccount降级");
    }

    private void nullResponseCheck(Object resp, String errorMsg) {
        if (resp == null) {
            throw new CommonLogicException(errorMsg + ", response is null");
        }
    }

    private void validateStatus(Result status, String errorMsg) {
        if (status == null) {
            throw new CommonLogicException(errorMsg + ", status is null");
        }

        if (status.getCode() != ResultCode.SUCCESS.getCode()) {
            throw new CommonLogicException(MessageFormat.format("{0}, code = {1}, detail = {2}",
                    errorMsg, status.getCode(), status.getMsg()));
        }
    }

    @Data
    private class ResourceData {
        private List<String> authCodes = Lists.newArrayList();
        private List<ResourceTree> resourceTrees = Lists.newArrayList();
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    private static class ResourceTree extends ResourceInfoVo {

        private List<ResourceTree> leaves = Lists.newArrayList();

        private static ResourceTree transform(ResourceInfoVo raw) {
            ResourceTree tree = new ResourceTree();
            tree.code = raw.code;
            tree.id = raw.id;
            tree.method = raw.method;
            tree.name = raw.name;
            tree.type = raw.type;
            tree.appId = raw.appId;
            tree.field = raw.field;
            tree.parentCode = raw.parentCode;
            tree.valid = raw.valid;
            tree.rank = raw.rank;
            return tree;
        }
    }

    /* rhino要求必须是public */
    @SuppressWarnings("WeakerAccess")
    public Map<Long, AccountInfoVo> querySimplyAccountInfoByIds(List<Long> accountIds) {

        if (CollectionUtils.isEmpty(accountIds)) {
            return Maps.newHashMap();
        }
        Set<Long> finalAccountIds = accountIds.stream().filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(accountIds)) {
            return Maps.newHashMap();
        }

        QuerySimpleAccountInfoRequest request = new QuerySimpleAccountInfoRequest();
        request.setAccountIds(Lists.newArrayList(finalAccountIds));
        try {
            QuerySimpleAccountInfoListResponse response = authThriftService.querySimpleAccountInfoList(request);
            if (response.getResult().code != ResultCodeEnum.SUCCESS.getValue()) {
                log.info("获取账号信息错误 response [{}].", request);
                throw new BizException(response.result.getCode(), response.result.getMsg());
            }

            return CollectionUtils.isEmpty(response.getAccountInfoList()) ? Maps.newHashMap()
                    : Maps.uniqueIndex(response.getAccountInfoList(), AccountInfoVo::getAccountId);
        } catch (Exception e) {
            log.warn("查询账号信息异常：", e);
            throw new BizException("查询账号信息异常");
        }
    }

    /**
     * 获取登录账号所有权限码
     */
    @MethodLog(logRequest = true, logResponse = true)
    @CommonMonitorTransaction
    @SuppressWarnings("WeakerAccess")
    @Deprecated
    public List<String> getCurrentAccountAllPermissionCodeList() {

        try {
            AccountInfoVo accountInfoVo = getCurrentAccount();

            if (Objects.isNull(accountInfoVo) ||
                    CollectionUtils.isEmpty(accountInfoVo.getRoleList())) {
                return Collections.EMPTY_LIST;
            }

            List<String> permissionCodeList = Lists.newArrayList();
            int appId = AppAuthIdConstants.LV_YUE_ZHU_SHOU.val();
            String authId = ApiMethodParamThreadLocal.getIdentityInfo().getAuthId();
            try {
                if (StringUtils.isNotBlank(authId)) {
                    appId = Integer.parseInt(authId);
                }
            } catch (NumberFormatException e) {
                log.error("APP ID错误 authId [{}].", authId, e);
            }

            for (RoleInfoVo roleInfoVo : accountInfoVo.getRoleList()) {
                for (PermissionVo permissionVo : roleInfoVo.getPermissionList()) {
                    if (appId == permissionVo.getAppId()) {
                        permissionCodeList.add(permissionVo.getCode());
                    }
                }
            }
            return permissionCodeList;
        } catch (TException e) {
            throw new RuntimeException("查询权限异常", e);
        }
    }


    /**
     * 判断是否有权限
     *
     * @param authCode
     * @return
     */
    public Boolean isCodeHasAuth(String authCode) {
        Map<String, Boolean> codeAuthMap = isHasPermission(Arrays.asList(authCode));
        return BooleanUtils.isTrue(codeAuthMap.get(authCode));
    }


    /**
     * 判断登录账号是否有指定权限码的权限
     *
     * @param currPermissionCodes
     * @return
     */
    public Map<String/*permissionCode*/, Boolean> isHasPermission(List<String> currPermissionCodes) {

        if (MccDynamicConfigUtil.useSacAuthentication()) {
            SessionInfo sessionInfo = SessionContext.getCurrentSession();
            String appId = ApiMethodParamThreadLocal.getIdentityInfo().getAuthId();
            return sacAccountRemoteService.accountAuthPermissions(sessionInfo.getAccountId(), Integer.parseInt(appId), currPermissionCodes);
        }
        else {
            return isHasPermissionByQueryAccount(currPermissionCodes);
        }


    }

    @Deprecated
    private Map<String, Boolean> isHasPermissionByQueryAccount(List<String> currPermissionCodes) {
        Map<String, Boolean> permissionMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(currPermissionCodes)) {
            return permissionMap;
        }

        List<String> accountPermissionCodes = getCurrentAccountAllPermissionCodeList();

        if (CollectionUtils.isEmpty(accountPermissionCodes)) {
            currPermissionCodes.forEach(code -> {
                permissionMap.put(code, false);
            });
            return permissionMap;
        }

        currPermissionCodes.forEach(code -> {
            if (accountPermissionCodes.contains(code)) {
                permissionMap.put(code, true);
            } else {
                permissionMap.put(code, false);
            }
        });

        return permissionMap;
    }

}
