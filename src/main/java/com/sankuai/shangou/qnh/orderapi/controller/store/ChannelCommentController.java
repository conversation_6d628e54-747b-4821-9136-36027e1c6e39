package com.sankuai.shangou.qnh.orderapi.controller.store;

import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.meituan.shangou.saas.order.management.client.dto.request.OrderSearchRequest;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.*;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.ConfirmDeleteCommentReq;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.ManualCheckDeleteCommentReq;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.ManualCheckUpdateCommentReq;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.CommentReplyTemplateAddReq;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.CommonResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.CommentReplyTemplateAddResp;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.ResultBuilder;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.ManualCheckDeleteCommentVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.ManualCheckUpdateCommentVo;
import com.sankuai.shangou.qnh.orderapi.remote.ChannelCommentRemoteService;

import com.sankuai.shangou.qnh.orderapi.domain.vo.store.CommentVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.CommentQueryBaseInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.CommentRuleVO;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.CommentStatResp;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.VirtualNumberInfo;
import com.sankuai.shangou.qnh.orderapi.exception.store.CommonRuntimeException;
import com.sankuai.shangou.qnh.orderapi.annotation.store.Auth;
import com.sankuai.shangou.qnh.orderapi.remote.OCMSOrderRemoteService;
import com.sankuai.shangou.qnh.orderapi.utils.store.ApiMethodParamThreadLocal;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@InterfaceDoc(
        displayName = "中台评价服务",
        type = "restful",
        scenarios = "查询评价统计，评价列表，评价回复，新增评价回复模板，删除评价回复模板，查询评价回复模板",
        description = "查询评价统计，评价列表，评价回复，新增评价回复模板，删除评价回复模板，查询评价回复模板",
        host = "https://storeapi-empower.meituan.com/"
)
@Slf4j
@Api(value = "中台评价服务")
@RestController(value = "appChannelCommentController")
@RequestMapping("/storemanagement/ocms/channelComment")
public class ChannelCommentController {

    @Resource
    private ChannelCommentRemoteService channelCommentWrapper;
    @Resource
    private OCMSOrderRemoteService ocmsOrderRemoteService;

    @MethodDoc(
            displayName = "添加评价回复模板",
            description = "添加评价回复模板",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "查询指定渠道下品牌信息请求",
                            type = CommentReplyTemplateAddReq.class
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口")
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "/storemanagement/ocms/channelComment/addCommentReplyTemplate",
            restExamplePostData = "{\"templateContent\":\" 评价模板测试\"}",
            restExampleResponseData = "{\"code\":0,\"data\":{\"templateId\":101},\"msg\":\"成功\"}"
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/addCommentReplyTemplate", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<CommentReplyTemplateAddResp> addCommentReplyTemplate(@RequestBody CommentReplyTemplateAddReq req) {
        req.validate();

        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return channelCommentWrapper.addCommentReplyTemplate(req, user);
    }

    @MethodDoc(
            displayName = "删除评价回复模板",
            description = "删除评价回复模板",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "删除评价回复模板请求参数",
                            type = CommentReplyTemplateDeleteReq.class
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口")
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "/storemanagement/ocms/channelComment/deleteCommentReplyTemplate",
            restExamplePostData = "{\"templateId\":100}",
            restExampleResponseData = "{\"code\":0,\"msg\":\"成功\"}"
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/deleteCommentReplyTemplate", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse deleteCommentReplyTemplate(@RequestBody CommentReplyTemplateDeleteReq req) {
        req.validate();
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return channelCommentWrapper.deleteCommentReplyTemplate(req, user);
    }


    @MethodDoc(
            displayName = "更新评论回复模板",
            description = "更新评论回复模板",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            type = CommentReplyTemplateUpdateReq.class,
                            description = "更新评论回复模板请求参数"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口")
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "/storemanagement/ocms/channelComment/updateCommentReplyTemplate",
            restExamplePostData = "{\"templateId\":100,\"templateContent\":\"模板新内容\"}",
            restExampleResponseData = "{\"code\":0,\"msg\":\"成功\"}"
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/updateCommentReplyTemplate", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse updateCommentReplyTemplate(@Valid @RequestBody CommentReplyTemplateUpdateReq req) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return channelCommentWrapper.updateCommentReplyTemplate(req, user);
    }

    @MethodDoc(
            displayName = "查询评价回复模板",
            description = "查询评价回复模板",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "查询评价回复模板请求参数"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口")
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "/storemanagement/ocms/channelComment/queryCommentReplyTemplateList",
            restExamplePostData = " ",
            restExampleResponseData = "{\"code\":0,\"message\":\"\",\"data\":{\"commentReplyTemplateList\":[{\"templateId\":97,\"templateContent\":\" 评价模板测试\"},{\"templateId\":98,\"templateContent\":\" 评价模板测试\"}]}}"
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryCommentReplyTemplateList", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse queryCommentReplyTemplateList(@Valid @RequestBody CommentReplyTemplateListQueryReq req) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return channelCommentWrapper.queryCommentReplyTemplateList(user, req);
    }

    @MethodDoc(
            displayName = "查询评价列表",
            description = "查询评价列表",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "查询评价列表请求参数",
                            type = CommentListQueryReq.class
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口")
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "/storemanagement/ocms/channelComment/queryCommentList",
            restExamplePostData = "{\"startTime\":\"2018-01-01\",\"endTime\":\"2019-09-01\",\"page\":1,\"pageSize\":10}",
            restExampleResponseData = "{\"code\":0,\"message\":\"\",\"data\":{\"hasMore\":true,\"totalCount\":48,\"commentVOList\":[{\"storeId\":\"1000017\",\"storeName\":\"中台测试门店(共用饿了么京东渠道)\",\"channelId\":100,\"channelName\":\"美团外卖\",\"commentId\":\"1154314093281259615\",\"commentContent\":\"不好吃 太难吃了\",\"commentTime\":\"2019-07-24\",\"addCommentContent\":\"太难吃\",\"addCommentTime\":\"2019-07-24\",\"commentLevel\":\"BAD_COMMENT\",\"orderScore\":1,\"qualityScore\":null,\"packingScore\":4,\"deliveryScore\":5,\"commentPictures\":[\"http://p0.meituan.net/wmcomment/267040aa1a714921fca55293840294dc223634.jpg.webp\",\"http://p0.meituan.net/wmcomment/e49ce618058ff780baab403e3108ffad225128.jpg.webp\",\"http://p0.meituan.net/wmcomment/e7d488a7f6797fbeb30b1dcfbb094fd6209142.jpg.webp\",\"http://p0.meituan.net/wmcomment/9cbf685cd8a76a1e743c8666b3ef2e98244916.jpg.webp\",\"http://p0.meituan.net/wmcomment/d06f7e9b8b480b4b1494e161ab6fbf81241760.jpg.webp\"],\"deliveryCommentLabels\":[\"风雨无阻\",\"礼貌热情\",\"货品完好\",\"快速准时\"],\"praiseItemList\":[\"批量1928\"],\"criticItemList\":[],\"orderItemList\":null,\"replyContent\":\"商家已通过其他后台回复\",\"replyStatus\":\"REPLIED\",\"replyTime\":null,\"replyExpireHourAfterComment\":168,\"commentReplyExpireInMinute\":0,\"canReply\":false,\"canContactUser\":false,\"userVirtualPhone\":null}]}}"
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryCommentList", method = RequestMethod.POST)
    @ResponseBody
    public Object queryCommentList(@RequestBody CommentListQueryReq req) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        if (CollectionUtils.isEmpty(req.getMatchChannelOrderIds())) {
            req.validate();
            return channelCommentWrapper.queryCommentList(user, req);
        } else {
            // 查询的订单号列表不为空时，查询订单明细
            OrderSearchRequest orderSearchRequest = req.convertToOrderSearchRequest();
            orderSearchRequest.setTenantId(user.getTenantId());
            return ocmsOrderRemoteService.getOrderListByAppComment(orderSearchRequest, req.getMatchChannelOrderIds());
        }
    }

    @MethodDoc(
            displayName = "查询评价详情",
            description = "查询评价详情",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "查询评价详情请求参数",
                            type = String.class
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口")
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "/storemanagement/ocms/channelComment/getComment?commentId=1234",
            restExamplePostData = " ",
            restExampleResponseData = "{\"code\":0,\"message\":\"\",\"data\":{\"storeId\":\"1000017\",\"storeName\":\"中台测试门店(共用饿了么京东渠道)\",\"channelId\":100,\"channelName\":\"美团外卖\",\"commentId\":\"1154314093281259615\",\"commentContent\":\"不好吃 太难吃了\",\"commentTime\":\"2019-07-24\",\"addCommentContent\":\"太难吃\",\"addCommentTime\":\"2019-07-24\",\"commentLevel\":\"BAD_COMMENT\",\"orderScore\":1,\"qualityScore\":null,\"packingScore\":4,\"deliveryScore\":5,\"commentPictures\":[\"http://p0.meituan.net/wmcomment/267040aa1a714921fca55293840294dc223634.jpg.webp\",\"http://p0.meituan.net/wmcomment/e49ce618058ff780baab403e3108ffad225128.jpg.webp\",\"http://p0.meituan.net/wmcomment/e7d488a7f6797fbeb30b1dcfbb094fd6209142.jpg.webp\",\"http://p0.meituan.net/wmcomment/9cbf685cd8a76a1e743c8666b3ef2e98244916.jpg.webp\",\"http://p0.meituan.net/wmcomment/d06f7e9b8b480b4b1494e161ab6fbf81241760.jpg.webp\"],\"deliveryCommentLabels\":[\"风雨无阻\",\"礼貌热情\",\"货品完好\",\"快速准时\"],\"praiseItemList\":[\"批量1928\"],\"criticItemList\":[],\"orderItemList\":null,\"replyContent\":\"商家已通过其他后台回复\",\"replyStatus\":\"REPLIED\",\"replyTime\":null,\"replyExpireHourAfterComment\":168,\"commentReplyExpireInMinute\":0,\"canReply\":false,\"canContactUser\":false,\"userVirtualPhone\":null}}"
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/getComment", method = {RequestMethod.GET, RequestMethod.POST})
    public CommonResponse<CommentVO> getComment(String commentId) {
        if (StringUtils.isEmpty(commentId)) {
            throw new CommonRuntimeException("评价id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return channelCommentWrapper.getCommentByTenantIdAndCommentId(user, commentId);
    }

    @MethodDoc(
            displayName = "查看评价统计信息",
            description = "查看门店中评价统计信息",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            type = CommentStatQueryReq.class,
                            description = "查询评价回复模板请求参数"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口")
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "/storemanagement/ocms/channelComment/queryCommentStat",
            restExamplePostData = "{\"storeId\":1,\"startTime\":\"2019-07-05 00:00:00\",\"endTime\":\"2019-05-01 00:00:00\"}",
            restExampleResponseData = "{\"code\":0,\"message\":\"\",\"data\":{\"commentStatList\":[{\"level\":1,\"levelName\":\"commentContentType\",\"levelStatList\":[{\"label\":\"EXIST\",\"count\":10},{\"label\":\"NOT_EXISTS\",\"count\":5}]},{\"level\":2,\"levelName\":\"commentContactType\",\"levelStatList\":[{\"label\":\"INIT\",\"count\":3},{\"label\":\"DONE\",\"count\":2}]}]}}"
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryCommentStat", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<CommentStatResp> queryCommentStat(@Valid @RequestBody CommentStatQueryReq request) {
        request.validate();
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return channelCommentWrapper.queryCommentStat(user, request);
    }


    @MethodDoc(
            displayName = "回复评价信息",
            description = "回复评价内容, 返回成功失败",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            type = CommentReplyReq.class,
                            description = "查询评价回复模板请求参数"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口")
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "/storemanagement/ocms/channelComment/reply",
            restExamplePostData = "{\"commentId\":1,\"replyDraft\":\"感谢您给出的建议, 我们会继续努力的!\"}",
            restExampleResponseData = "{\"code\":0,\"message\":\"\"}"
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/reply", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse reply(@Valid @RequestBody CommentReplyReq request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        if(StringUtils.isBlank(request.getAssociationChannelOrderId())) {
            return channelCommentWrapper.replyComment(user, request);
        } else {
            // 当用户绑定订单号不为空时，执行绑定操作
            return channelCommentWrapper.commentAssociationChannelOrderId(user.getTenantId(), user.getAccountId(),
                    request.getCommentId(), request.getAssociationChannelOrderId());
        }
    }

    @MethodDoc(
            displayName = "获取评价查询基本信息",
            description = "获取评价查询基本信息    ",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口")
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "/storemanagement/ocms/channelComment/getCommentQueryBaseInfo",
            restExamplePostData = " ",
            restExampleResponseData = "{\"code\":0,\"message\":\"\",\"data\":{\"channelInfoVOList\":[{\"code\":\"MEITUAN\",\"id\":100,\"name\":\"美团外卖\"},{\"code\":\"ELEM\",\"id\":200,\"name\":\"饿了么\"},{\"code\":\"JD2HOME\",\"id\":300,\"name\":\"京东到家\"}]}}"
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/getCommentQueryBaseInfo", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<CommentQueryBaseInfoVO> getCommentQueryBaseInfo() {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return channelCommentWrapper.getCommentQueryBaseInfo(user);
    }


    @MethodDoc(
            displayName = "获取评价查询基本信息",
            description = "获取评价查询基本信息    ",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口")
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "/storemanagement/ocms/channelComment/getCommentQueryBaseInfo",
            restExamplePostData = " ",
            restExampleResponseData = "{\"code\":0,\"message\":\"\",\"data\":{\"channelInfoVOList\":[{\"code\":\"MEITUAN\",\"id\":100,\"name\":\"美团外卖\"},{\"code\":\"ELEM\",\"id\":200,\"name\":\"饿了么\"},{\"code\":\"JD2HOME\",\"id\":300,\"name\":\"京东到家\"}]}}"
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryCommentRule", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<CommentRuleVO> queryCommentRule() {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return channelCommentWrapper.queryCommentRule(user.getTenantId());
    }

    @MethodDoc(
            displayName = "查询虚拟号",
            description = "查询虚拟号，注意此接口会生成虚拟号",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "查询评价详情请求参数",
                            type = String.class
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口")
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "/storemanagement/ocms/channelComment/getComment?commentId=1234",
            restExamplePostData = " ",
            restExampleResponseData = "{\"code\":0,\"message\":\"\",\"data\":{\"storeId\":\"1000017\",\"storeName\":\"中台测试门店(共用饿了么京东渠道)\",\"channelId\":100,\"channelName\":\"美团外卖\",\"commentId\":\"1154314093281259615\",\"commentContent\":\"不好吃 太难吃了\",\"commentTime\":\"2019-07-24\",\"addCommentContent\":\"太难吃\",\"addCommentTime\":\"2019-07-24\",\"commentLevel\":\"BAD_COMMENT\",\"orderScore\":1,\"qualityScore\":null,\"packingScore\":4,\"deliveryScore\":5,\"commentPictures\":[\"http://p0.meituan.net/wmcomment/267040aa1a714921fca55293840294dc223634.jpg.webp\",\"http://p0.meituan.net/wmcomment/e49ce618058ff780baab403e3108ffad225128.jpg.webp\",\"http://p0.meituan.net/wmcomment/e7d488a7f6797fbeb30b1dcfbb094fd6209142.jpg.webp\",\"http://p0.meituan.net/wmcomment/9cbf685cd8a76a1e743c8666b3ef2e98244916.jpg.webp\",\"http://p0.meituan.net/wmcomment/d06f7e9b8b480b4b1494e161ab6fbf81241760.jpg.webp\"],\"deliveryCommentLabels\":[\"风雨无阻\",\"礼貌热情\",\"货品完好\",\"快速准时\"],\"praiseItemList\":[\"批量1928\"],\"criticItemList\":[],\"orderItemList\":null,\"replyContent\":\"商家已通过其他后台回复\",\"replyStatus\":\"REPLIED\",\"replyTime\":null,\"replyExpireHourAfterComment\":168,\"commentReplyExpireInMinute\":0,\"canReply\":false,\"canContactUser\":false,\"userVirtualPhone\":null}}"
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryCommentVirtualNumber", method = {RequestMethod.POST})
    public CommonResponse<VirtualNumberInfo> queryCommentVirtualNumber(String commentId) {
        if (StringUtils.isEmpty(commentId)) {
            throw new CommonRuntimeException("评价id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        return channelCommentWrapper.queryCommentVirtualNumber(user, commentId);
    }

    @MethodDoc(
            displayName = "更新评价",
            description = "根据评价id,查询渠道是否有更新",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "更新评价请求参数",
                            type = String.class
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口")
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "/storemanagement/ocms/channelComment/manualCheckUpdateComment",
            restExamplePostData = " ",
            restExampleResponseData = "{\"code\": 0,\"msg\": \"成功\",\"data\": {\"resultState\": 1,\"resultMessage\": \"评价内容已更新\"},\"success\": true\"}"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @Auth
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/manualCheckUpdateComment", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public CommonResponse<ManualCheckUpdateCommentVo> manualCheckUpdateComment(@RequestBody ManualCheckUpdateCommentReq request) {
        request.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        request.isValid();
        CommonResponse<ManualCheckUpdateCommentVo> commonResponse = channelCommentWrapper.manualCheckUpdateComment(request.getCommentId(), request.getTenantId());
        if (commonResponse.getCode() != 0) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), commonResponse.getMessage());
        }
        ManualCheckUpdateCommentVo updateCommentVo = new ManualCheckUpdateCommentVo();
        updateCommentVo.setResultState(commonResponse.getData().getResultState());
        updateCommentVo.setResultMessage(commonResponse.getData().getResultMessage());
        return CommonResponse.success(updateCommentVo);
    }

    @MethodDoc(
            displayName = "删除评价",
            description = "删除评价",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "删除评价请求参数",
                            type = String.class
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口")
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "/storemanagement/ocms/channelComment/manualCheckDeleteComment",
            restExamplePostData = " ",
            restExampleResponseData = "{\"code\": 0,\"msg\": \"成功\",\"data\": {\"status\": 0,\"resultMessage\": \"评价在渠道已被删除，系统内评价已更新为删除状态\"},\"success\": true\"}"
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/manualCheckDeleteComment", method = {RequestMethod.GET, RequestMethod.POST})
    public CommonResponse<ManualCheckDeleteCommentVo> manualCheckDeleteComment(String commentId) {
        ManualCheckDeleteCommentReq request = new ManualCheckDeleteCommentReq();
        request.setCommentId(commentId);
        request.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        request.isValid();
        CommonResponse<ManualCheckDeleteCommentVo> response = channelCommentWrapper.manualCheckDeleteComment(request.getCommentId(), request.getTenantId());
        if (response.getCode() != 0) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), response.getMessage());
        }
        return CommonResponse.success(response.getData());
    }

    @MethodDoc(
            displayName = "确认删除评价",
            description = "确认删除评价",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "确认删除评价请求参数",
                            type = String.class
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：校验用户token，判断当前用户是否登录，是否可访问当前接口")
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "/storemanagement/ocms/channelComment/confirmDeleteComment",
            restExamplePostData = " ",
            restExampleResponseData = "{\"code\":0,\"message\":\"\",\"data\":null\"}"
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/confirmDeleteComment", method = {RequestMethod.GET, RequestMethod.POST})
    public CommonResponse<Void> confirmDeleteComment(String commentId) {
        ConfirmDeleteCommentReq request = new ConfirmDeleteCommentReq();
        request.setCommentId(commentId);
        request.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        request.setOperatorId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId());
        request.isValid();
        CommonResponse<Void> response = channelCommentWrapper.confirmDeleteComment(request.getCommentId(), request.getTenantId(), request.getOperatorId());
        if (response.getCode() != 0) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), response.getMessage());
        }
        return CommonResponse.success(null);
    }
}
