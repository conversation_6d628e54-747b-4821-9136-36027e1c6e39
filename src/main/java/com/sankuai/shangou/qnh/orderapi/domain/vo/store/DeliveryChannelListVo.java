package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2019/12/10
 **/

@TypeDoc(
        description = "配送渠道详情列表对象"
)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DeliveryChannelListVo {


    @FieldDoc(
            description = "配送渠道列表"
    )
    private List<DeliveryChannelVo> deliveryChannelList;


    @TypeDoc(
            description = "配送渠道详情对象"
    )
    @Data
    public static class DeliveryChannelVo {
        @FieldDoc(
                description = "配送渠道ID"
        )
        public Integer channelId;

    }
}
