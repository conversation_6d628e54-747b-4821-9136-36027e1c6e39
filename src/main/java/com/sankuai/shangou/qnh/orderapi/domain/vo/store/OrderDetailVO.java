package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.facebook.swift.codec.ThriftField;
import com.google.gson.annotations.SerializedName;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.OrderFuseFinanceDetailBO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.SubProductVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.CompensationVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.GiftVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.LackStockGoodsVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.OrderTagVO;
import com.sankuai.shangou.qnh.orderapi.utils.CombinationProductUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/7/11
 * desc: 订单详情
 */
@TypeDoc(
        description = "订单详情"
)
@ApiModel("订单详情")
@Data
public class OrderDetailVO {

    @FieldDoc(
            description = "赋能订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "赋能订单号", required = true)
    private Long orderId;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道订单号", required = true)
    private String channelOrderId;

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID", required = true)
    private Integer channelId;

    @FieldDoc(
            description = "订单流水", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单流水", required = true)
    private Long serialNo;

    @FieldDoc(
            description = "订单序号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单序号", required = true)
    private String serialNoStr;

    @FieldDoc(
            description = "商品数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品数量", required = true)
    private Integer itemCount;

    @FieldDoc(
            description = "融合商品数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "融合商品数量", required = false)
    private Integer fuseItemCount;

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID", required = true)
    private Long storeId;

    @FieldDoc(
            description = "门店名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店名称", required = true)
    private String storeName;

    @FieldDoc(
            description = "渠道名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道名称", required = true)
    private String channelName;

    @FieldDoc(
            description = "订单用户ID、0或-1为无效ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "订单用户ID、0或-1为无效ID")
    private Long userId;

    @FieldDoc(
            description = "可操作列表 10-接单 20-完成拣货 30-补打小票 40-全单退款 50-部分退款 70退差价", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "可操作列表 10-接单 20-完成拣货 30-补打小票 40-全单退款 50-部分退款 70退差价", required = true)
    private List<Integer> couldOperateItemList;

    @FieldDoc(
            description = "收货人姓名", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "收货人姓名", required = true)
    private String receiverName;

    @FieldDoc(
            description = "收货人电话", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "收货人电话", required = true)
    private String receiverPhone; // required


    @FieldDoc(
            description = "收货人隐私号电话，如果无隐私号则使用真实号码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "收货人隐私号电话，如果无隐私号则使用真实号码", required = true)
    private String receiverAxPhone; // required


    @FieldDoc(
            description = "收货人地址", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "收货人地址", required = true)
    private String receiveAddress; // required

    @FieldDoc(
            description = "订单原金额 单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单原金额 单位:分", required = true)
    private Integer originalAmt;

    @FieldDoc(
            description = "订单实付金额  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单实付金额  单位:分", required = true)
    private Integer actualPayAmt;

    @FieldDoc(
            description = "商品金额小计  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品金额小计  单位:分", required = true)
    private Integer productTotalPayAmount;

    @FieldDoc(
            description = "商家实收金额  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商家实收金额  单位:分", required = true)
    private Integer bizReceiveAmt;

    @FieldDoc(
            description = "运费  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "运费  单位:分", required = true)
    private Integer freight;

    @FieldDoc(
            description = "打包费  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "打包费  单位:分", required = true)
    private Integer packageAmt;

    @FieldDoc(
            description = "平台服务费  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "平台服务费  单位:分", required = true)
    private Integer platformFee;

    @FieldDoc(
            description = "是否开发票  0-否 1-是", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "是否开发票  0-否 1-是", required = true)
    private Integer isNeedInvoice; // required

    @FieldDoc(
            description = "发票抬头", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "发票抬头", required = true)
    private String invoiceTitle; // required

    @FieldDoc(
            description = "发票税号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "发票税号", required = true)
    private String taxNo;

    @FieldDoc(
            description = "配送方式", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "配送方式", required = true)
    private int deliveryMethod;

    @FieldDoc(
            description = "配送方式描述", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "配送方式描述", required = true)
    private String deliveryMethodName;

    @FieldDoc(
            description = "配送人姓名", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "配送人姓名", required = true)
    private String deliveryUserName;

    @FieldDoc(
            description = "配送人电话", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "配送人电话", required = true)
    private String deliveryUserPhone;

    @FieldDoc(
            description = "是否支持骑手电话使用隐私号"
    )
    private Boolean supportDeliveryUserPrivacyPhone;

    @FieldDoc(
            description = "配送订单类型   0-立即送达 1-预约送达", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "配送订单类型   0-立即送达 1-预约送达", required = true)
    private Integer deliveryOrderType;

    @FieldDoc(
            description = "配送订单类型名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "配送订单类型名称", required = true)
    private String deliveryOrderTypeName;

    @FieldDoc(
            description = "超时配送异常标志(0否1是)"
    )
    private Integer isDeliveryOvertime;

    @FieldDoc(
            description = "配送异常描述"
    )
    private String deliveryExceptionDescription;

    @FieldDoc(
            description = "配送渠道 ID"
    )
    private Integer deliveryChannelId;

    @FieldDoc(
            description = "配送类型"
    )
    private Integer distributeType;

    @FieldDoc(
            description = "配送类型名称"
    )
    private String distributeTypeName;

    @FieldDoc(
            description = "预计送达时间开始时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "预计送达时间开始时间", required = true)
    private Long estimatedSendArriveTimeStart;

    @FieldDoc(
            description = "预计送达时间截止时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "预计送达时间截止时间", required = true)
    private Long estimatedSendArriveTimeEnd;

    @FieldDoc(
            description = "支付方式", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "支付方式", required = true)
    private Integer payMethod;

    @FieldDoc(
            description = "支付方式描述", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "支付方式描述", required = true)
    private String payMethodDesc;

    @FieldDoc(
            description = "订单状态 10-新建订单 20-商家已确认 35-履约中 30-订单已完成 40-取消处理中 50-已取消", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单状态 10-新建订单 20-商家已确认 35-履约中 30-订单已完成 40-取消处理中 50-已取消", required = true)
    private Integer channelOrderStatus;

    @FieldDoc(
            description = "订单聚合状态 1-进行中订单 2-已完成订单 3-订单已取消", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单聚合状态 1-进行中订单 2-已完成订单 3-订单已取消", required = true)
    private Integer aggregationOrderStatus;

    @FieldDoc(
            description = "订单状态描述", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单状态描述", required = true)
    private String channelOrderStatusDesc;

    @FieldDoc(
            description = "创建时间戳", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "创建时间戳", required = true)
    private long createTime;

    @FieldDoc(
            description = "最新售后申请类型", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "最新售后申请类型", required = true)
    private Integer lastAfterSaleApplyRefundTagId;

    @FieldDoc(
            description = "最新售后申请原因", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "最新售后申请原因", required = true)
    private String lastAfterSaleApplyReason;

    @FieldDoc(
            description = "最新售后申请状态", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "最新售后申请状态", required = true)
    private Integer lastAfterSaleApplyStatus;

    @FieldDoc(
            description = "最新售后申请驳回原因", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "最新售后申请驳回原因", required = true)
    private String lastAfterSaleApplyRejectReason;

    @FieldDoc(
            description = "可退订单金额（实付金额 - 申请退款金额）单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "可退订单金额（实付金额 - 申请退款金额）单位:分", required = true)
    private Integer refundableOrderAmount;

    @FieldDoc(
            description = "更新时间戳", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "更新时间戳", required = true)
    private Long updateTime;

    @FieldDoc(
            description = "配送状态", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "配送状态", required = true)
    private Integer distributeStatus;

    @FieldDoc(
            description = "配送状态名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "配送状态名称", required = true)
    private String distributeStatusName;

    @FieldDoc(
            description = "拣货状态", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "拣货状态", required = true)
    private Integer pickupStatus;

    @FieldDoc(
            description = "渠道第二订单号 饿百渠道的饿了么订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道第二订单号 饿百渠道的饿了么订单号", required = true)
    private String channelExtraOrderId;

    @FieldDoc(
            description = "备注", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "备注", required = true)
    private String comments;

    @FieldDoc(
            description = "订单商品列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单商品列表", required = true)
    private List<ProductVO> productList;

    @FieldDoc(
            description = "融合订单商品列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "融合订单商品列表", required = false)
    private List<ProductVO> fuseProductList;

    @FieldDoc(
            description = "促销列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "促销列表", required = true)
    private List<PromotionVO> promotionList;

    @FieldDoc(
            description = "订单状态日志列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单状态日志列表", required = true)
    private List<OrderStatusLogVO> orderStatuslogList;

    @FieldDoc(
            description = "售后记录列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "售后记录列表", required = true)
    private List<AfterSaleRecordVO> afterSaleRecordList;

    @FieldDoc(
            description = "用户标签信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "用户标签信息", required = true)
    private List<TagInfoVO> userTags;

    @FieldDoc(
            description = "支付时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "支付时间", required = true)
    private Long payTime;

    @FieldDoc(
            description = "已完成配送骑手信息（去除最新一个）", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "已完成配送骑手信息（去除最新一个）", required = true)
    private List<RiderInfoVO> riderInfoList;

    @FieldDoc(
            description = "仓库名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "仓库名称")
    private String warehouseName;

    @FieldDoc(
            description = "仓库ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "仓库名称")
    private Long warehouseId;

    @FieldDoc(
            description = "订单用户类型", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "订单用户类型")
    private Integer orderUserType;

    @FieldDoc(
            description = "erp门店code",
            example = {}
    )
    @ApiModelProperty(value = "erp门店code")
    public String erpShopCode;

    @FieldDoc(
            description = "歪马地推自提订单标记",
            example = {}
    )
    @ApiModelProperty(value = "歪马地推自提订单标记")
    private Boolean selfPickPullNewOrder;


    @FieldDoc(
            description = "转单门店ID"
    )
    @ApiModelProperty(value = "转单门店ID")
    private Long dispatchShopId;
    @FieldDoc(
            description = "转单门店后的订单序列编码"
    )
    @ApiModelProperty(value = "转单门店后的订单序列编码")
    private String dispatchSerialNo;
    @FieldDoc(
            description = "转单门店名称"
    )
    @ApiModelProperty(value = "转单门店名称")
    private String dispatchShopName;
    @FieldDoc(
            description = "转单门店所在租户ID"
    )
    @ApiModelProperty(value = "转单门店所在租户ID")
    private Long dispatchTenantId;
    @FieldDoc(
            description = "转单时间"
    )
    @ApiModelProperty(value = "转单时间")
    private Long dispatchTime;

    @FieldDoc(
            description = "异常单缺货项", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "异常单缺货项", required = true)
    private List<LackStockGoodsVO> lackStockGoodsList;

    @FieldDoc(
            description = "赠品项", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "赠品项", required = true)
    private List<GiftVO> giftVOList;

    @FieldDoc(
            description = "秘钥"
    )
    @ApiModelProperty(value = "秘钥")
    private String token;

    @FieldDoc(
            description = "地址场景"
    )
    @ApiModelProperty(value = "地址场景")
    private String scene;

    @FieldDoc(
            description = "拣货员姓名列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "拣货员姓名列表", required = false)
    private List<String> pickerNameList = new ArrayList<>();

    @FieldDoc(
            description = "收货人纬度, e.g. 30.559691"
    )
    @ApiModelProperty(value = "收货人纬度")
    private String receiverLatitude;

    @FieldDoc(
            description = "收货人经度, e.g. 104.057508"
    )
    @ApiModelProperty(value = "收货人经度")
    private String receiverLongitude;

    @FieldDoc(
            description = "歪马礼袋"
    )
    @ApiModelProperty(value = "歪马礼袋")
    private List<DrunkHorseGiftBagVO> giftBagList;

    @FieldDoc(
            description = "是否展示拣货记录按钮,1展示"
    )
    @ApiModelProperty(value = "是否展示拣货记录按钮,1展示")
    private Integer pickRecordButton;

    @FieldDoc(
            description = "是否拣配分离"
    )
    @ApiModelProperty(value = "是否拣配分离")
    private Boolean isPickDeliverySplit;

    @FieldDoc(
            description = "订单赔付信息列表"
    )
    @ApiModelProperty(value = "订单赔付信息列表")
    private List<CompensationVO> compensationModelList;

    @FieldDoc(
            description = "是否为美团名酒馆订单，true：是"
    )
    @ApiModelProperty(value = "是否为美团名酒馆订单，true：是")
    private Boolean isMtFamousTavern;
    @FieldDoc(
            description = "是否为美团发财酒订单，true：是", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否为美团发财酒订单，true：是")
    private Boolean isFacaiWine;

    @FieldDoc(
            description = "订单tag", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "订单tag")
    private List<OrderTagVO> orderTagList;

    @FieldDoc(
            description = "骑手放置位置"
    )
    @ApiModelProperty(value = "骑手放置位置")
    private String deliveryPosition;

    @FieldDoc(
            description = "用户下单时“如遇缺货”处的信息"
    )
    @ApiModelProperty(value = "用户下单时“如遇缺货”处的信息")
    private String stockOutInfo;

    public void mergeFinanceInfo(OrderFuseFinanceDetailBO orderFuseFinanceDetailBO){
        if(orderFuseFinanceDetailBO == null){
            return;
        }
        productList.forEach(v->{
            if (CollectionUtils.isNotEmpty(orderFuseFinanceDetailBO.getItemInfos())){
                Optional<OrderFuseFinanceDetailBO.ItemFinanceInfo> itemFinanceInfo = orderFuseFinanceDetailBO.getItemInfos()
                        .stream().filter(item-> Objects.equals(v.getOrderItemId(), item.getOrderItemId())).findFirst();
                itemFinanceInfo.ifPresent(v::mergeFinanceInfo);
            }
        });
    }

    public void mergeFinanceRefundCompose(List<OrderFuseFinanceDetailBO> orderFuseFinanceDetailBOList) {
        try{
            if(CollectionUtils.isEmpty(orderFuseFinanceDetailBOList)){
                return;
            }
            if(CollectionUtils.isEmpty(afterSaleRecordList)){
                return;
            }
            //正单组合品子商品
            Map<Long, SubProductVo> dueSubProductVoMap = getDueComposeProduct();
            Map<Long, OrderFuseFinanceDetailBO> orderFuseFinanceDetailBOMap = orderFuseFinanceDetailBOList.stream()
                    .filter(item -> Objects.nonNull(item.getServiceId()))
                    .collect(Collectors.toMap(OrderFuseFinanceDetailBO::getServiceId, value -> value));
            afterSaleRecordList.forEach(afterSaleRecordVO -> {
                if(CollectionUtils.isEmpty(afterSaleRecordVO.getAfterSaleRecordDetailList()) || Objects.isNull(afterSaleRecordVO.getServiceId())){
                    return;
                }
                OrderFuseFinanceDetailBO orderFuseFinanceDetailBO = orderFuseFinanceDetailBOMap.get(afterSaleRecordVO.getServiceId());
                if(Objects.isNull(orderFuseFinanceDetailBO) || CollectionUtils.isEmpty(orderFuseFinanceDetailBO.getItemInfos())){
                    return;
                }
                CombinationProductUtil.dealAppOrderDetailAfterSaleCompose(afterSaleRecordVO, orderFuseFinanceDetailBO, dueSubProductVoMap);
            });
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    private Map<Long, SubProductVo> getDueComposeProduct() {
        //获取正单组合品子商品
        Map<Long, SubProductVo> dueComposeProduct = new HashMap<>();
        try {
            if(CollectionUtils.isEmpty(productList)){
                return dueComposeProduct;
            }
            productList.forEach(item -> {
                List<SubProductVo> subProductVoList = item.getSubProductVoList();
                if(CollectionUtils.isEmpty(subProductVoList)){
                    return;
                }
                subProductVoList.forEach(subProductVo -> {
                    if(Objects.isNull(subProductVo.getServiceId())){
                        return;
                    }
                    dueComposeProduct.put(subProductVo.getServiceId(), subProductVo);
                });
            });
        }catch (Exception e){

        }
        return dueComposeProduct;
    }

}
