package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2020/3/16
 * desc: 修改配送配置
 */
@Setter
@Getter
public class DeliveryConfigForModifyVO {

    @FieldDoc(
            description = "配送渠道ID"
    )
    @ApiModelProperty(value = "配送渠道ID", required = true)
    private String deliveryChannelId;

    @FieldDoc(
            description = "配送渠道AppKey"
    )
    @ApiModelProperty(value = "配送渠道AppKey", required = true)
    private String appKey;

    @FieldDoc(
            description = "配送渠道Secret"
    )
    @ApiModelProperty(value = "配送渠道Secret", required = true)
    private String secret;

    @FieldDoc(
            description = "开启状态 0:关闭 1:开启"
    )
    @ApiModelProperty(value = "开启状态 0:关闭 1:开启", required = true)
    private int enable;
}
