package com.sankuai.shangou.qnh.orderapi.service.app;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.shangou.saas.order.platform.enums.BoolTypeEnum;
import com.meituan.shangou.saas.order.platform.enums.DistributeMethodEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryEntityEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DisplayCancelStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.dto.DeliveryChannelDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.QueryOrderDeliveryInfoKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryDetail;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TLaunchDeliveryType;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.shangou.qnh.orderapi.constant.app.DeliveryInfoSource;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.OrderVO;
import com.sankuai.shangou.qnh.orderapi.enums.app.*;
import com.sankuai.shangou.qnh.orderapi.remote.DeliveryChannelRemoteService;
import com.sankuai.shangou.qnh.orderapi.remote.TmsRemoteService;
import com.sankuai.shangou.qnh.orderapi.utils.app.DeliveryChannelUtils;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import javafx.util.Pair;
import jline.internal.TestAccessible;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021/1/5
 */
@Service(value = "appDeliveryInfoService")
@Slf4j
public class AppendDeliveryInfoService {

    @Resource
    private TmsRemoteService tmsServiceWrapper;

    @Resource
    private DeliveryChannelRemoteService deliveryChannelWrapper;

    private static final Integer INVALID_DELIVERY_EXCEPTION_CODE = 0;

    /**
     * 为订单列表添加配送信息、可操作按钮
     *
     * @param orderList
     */
    @MethodLog(logRequest = true, logResponse = true)
    public Pair<Map<Long, TDeliveryDetail>, Map<Integer, DeliveryChannelDto>> appendDeliveryInfo(List<OrderVO> orderList) {

        //如果新的配送信息查询降级，直接不append新的配送信息
        if (!MccConfigUtil.getOrderTabAppendTmsDeliveryInfoSwitch()) {
            return new Pair<>(new HashMap<>(),new HashMap<>());
        }
        List<QueryOrderDeliveryInfoKey> empowerOrderIds = collectEmpowerOrderKeys(orderList);
        if (CollectionUtils.isEmpty(empowerOrderIds)) {
            return new Pair<>(new HashMap<>(),new HashMap<>());
        }
        /**
         * 查询tms的配送信息
         */
        Pair<Map<Long, TDeliveryDetail>, Map<Integer, DeliveryChannelDto>> tmsDeliveryInfo = queryRealTimeDeliveryInfo(empowerOrderIds);
        if (MapUtils.isEmpty(tmsDeliveryInfo.getKey())) {
            orderList.stream().forEach(this::processPlatformDelivery);
            return new Pair<>(new HashMap<>(),tmsDeliveryInfo.getValue());
        }
        /**
         * 聚合tms的配送信息
         */
        mergeRealTimeDeliveryInfo(orderList, tmsDeliveryInfo.getKey(),tmsDeliveryInfo.getValue());
        return tmsDeliveryInfo;

    }

    private void mergeRealTimeDeliveryInfo(List<OrderVO> orderList, Map<Long, TDeliveryDetail> tmsDeliveryInfo, Map<Integer, DeliveryChannelDto> channelDtoMap) {

        orderList.forEach(orderVO -> mergeSingleOrderDeliveryInfo(orderVO, tmsDeliveryInfo.get(orderVO.getEmpowerOrderId()),channelDtoMap));
    }

    private void mergeSingleOrderDeliveryInfo(OrderVO orderVO, TDeliveryDetail tDeliveryDetail, Map<Integer, DeliveryChannelDto> channelDtoMap) {

        if (tDeliveryDetail == null) {
            processPlatformDelivery(orderVO);
            return;
        }
        Map<Integer, Integer> channelMap = deliveryChannelWrapper.tratranslateToChannelIntgerMap(channelDtoMap);

        orderVO.setDeliveryOperateItem(buildOperateItem(tDeliveryDetail,orderVO.getTenantId(),orderVO.getStoreId(),channelMap));
        //如果tms没有配送运单信息，则不需要聚合tms的配送数据
        if (tDeliveryDetail.deliveryChannelName == null) {
            processPlatformDelivery(orderVO);
            return;
        }
        orderVO.setDeliveryInfoSource(DeliveryInfoSource.FROM_TMS);
        orderVO.setRealDistributeStatus(tDeliveryDetail.status);
        orderVO.setDistributeStatusDesc(TmsDeliveryStatusDescEnum.getDescFromCode(tDeliveryDetail.status));
        // 仅当配送状态大于初始化状态时，才展示配送方式字段
        if (orderVO.getRealDistributeStatus() > TmsDeliveryStatusDescEnum.INIT.getCode()) {
            orderVO.setDeliveryChannel(formatChannelName(tDeliveryDetail.deliveryEntity, tDeliveryDetail.deliveryChannelName, tDeliveryDetail.isFourWheelDelivery));
            if(MccConfigUtil.isDrunkHorseTenant(orderVO.getTenantId())) {
                //青云配送信息拆分“青云配送”和承运商两个字段,自营新增“歪马”承运商字段
                if (DeliveryChannelUtils.isDapDeliveryChannel(orderVO.getDeliveryChannelId())) {
                    orderVO.setDeliveryChannel("青云配送");
                }
                orderVO.setDeliveryPlatform(DeliveryChannelUtils.getDeliveryPlatform(orderVO.getDeliveryChannelId(), tDeliveryDetail.status));
            }
            orderVO.setDeliveryChannelType(tDeliveryDetail.deliveryEntity);
        }
        orderVO.setDeliveryExceptionType(tDeliveryDetail.deliveryExceptionType);
        orderVO.setDeliveryExceptionCode(tDeliveryDetail.deliveryExceptionCode);
        orderVO.setTmsDistributeStatus(tDeliveryDetail.status);
        if (tDeliveryDetail.deliveryExceptionCode == null || tDeliveryDetail.deliveryExceptionCode.equals(INVALID_DELIVERY_EXCEPTION_CODE)) {
            orderVO.setDeliveryExceptionDesc(tDeliveryDetail.deliveryException);
        } else {
            orderVO.setDeliveryExceptionDesc(DeliveryExceptionCodeEnum.enumOf(tDeliveryDetail.deliveryExceptionCode).getDesc());
        }
        if (tDeliveryDetail.deliveryExceptionCode != null && tDeliveryDetail.deliveryExceptionCode.equals(com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionCodeEnum.OPEN_API_DELIVERY_EXCEPTION.getCode())) {
            orderVO.setDeliveryExceptionDesc(tDeliveryDetail.deliveryException);
        }
        if (tDeliveryDetail.deliveryExceptionCode != null && tDeliveryDetail.deliveryExceptionCode.equals(DeliveryExceptionCodeEnum.ESTIMATED_TIME_OUT.getCode())) {
            // 超时未完成、异常详情匹配
            orderVO.setDeliveryExceptionDesc(DeliveryEstimatedTimeOutExceptionEnum.deliveryStatusCodeValueOf(tDeliveryDetail.status).getDesc());
            orderVO.setWholeTimeout("整单超时");
        }
        orderVO.setDeliveryDistance(Double.valueOf(tDeliveryDetail.deliveryDistance));
        orderVO.setDeliveryUserName(tDeliveryDetail.riderName);
        orderVO.setDeliveryUserPhone(tDeliveryDetail.riderPhone);
        orderVO.setDeliveryFee(tDeliveryDetail.deliveryFee);
        orderVO.setCurrentDeliveryStatusStartTime(tDeliveryDetail.startWaitAssignRiderTime);
        orderVO.setDeliveryStatusChangeTime(tDeliveryDetail.deliveryStatusChangeTime);
    }

    private void processPlatformDelivery(OrderVO orderVO) {
        if(orderVO==null||orderVO.getDistributeStatus()==null){
            return;
        }
        if (orderVO.getDistributeStatus() > 0) {
            //迁移订单
            if (Integer.valueOf(1).equals(orderVO.getMigrateFlag())) {
                processMigrateDelivery(orderVO);
            }else {
                orderVO.setDeliveryChannel("平台配送");
                orderVO.setDeliveryChannelType(DeliveryEntityEnum.PLATFORM.getValue());
            }
        }
    }
    //处理迁移订单的配送信息,迁移订单，映射成自配送和聚合运力配送，不需要查渠道配置化
    private void processMigrateDelivery(OrderVO orderVO) {
        //订单自提
        if (orderVO.getDeliveryMethod() != null && orderVO.getDeliveryMethod() == DistributeMethodEnum.STORE_DELIVERY.getValue()) {
            log.info("到店自提订单不显示配送信息");
        }else if (Integer.valueOf(0).equals(orderVO.getSelfDelivery())) {
            orderVO.setDeliveryChannel("平台配送");
            orderVO.setDeliveryChannelType(DeliveryEntityEnum.PLATFORM.getValue());
        } else {
            final Integer deliveryChannelId = Optional.ofNullable(orderVO.getDeliveryChannelId()).orElse(DeliveryChannelEnum.AGGREGATION_DELIVERY.getCode());
            final DeliveryChannelEnum channelEnum = DeliveryChannelEnum.valueOf(deliveryChannelId);
            //商家自配送
            orderVO.setDeliveryChannel(channelEnum.getName());
            orderVO.setDeliveryChannelType(DeliveryChannelEnum.MERCHANT_DELIVERY.equals(channelEnum) ?
                    DeliveryEntityEnum.DELIVER_BY_SELF.getValue() :
                    DeliveryEntityEnum.THIRD_PART.getValue());

        }
    }

    /**
     * 根据tms的订单可用发配送类型，提供发配送按钮
     *
     * @param tDeliveryDetail
     * @return
     */
    private Integer buildOperateItem(TDeliveryDetail tDeliveryDetail,Long tenantId,Long storeId,Map<Integer,Integer> deliveryChannelMap) {
        TLaunchDeliveryType tLaunchDeliveryType = tDeliveryDetail.tLaunchDeliveryType;
        if (tLaunchDeliveryType == null) {
            return null;
        }
        boolean isShow = isShowDeliveryItem(tenantId);
        if (tLaunchDeliveryType.canRetryLaunch && MccConfigUtil.getDeliveryOperateItemRetry()) {
            return (!isShow) ? null: DeliveryOperateItemEnum.RETRY_LAUNCH.type;
        }
        if (tLaunchDeliveryType.canSelfDelivery && MccConfigUtil.getDeliveryOperateItemSelfDelivery()
                && !deliveryChannelWrapper.checkChannel(tDeliveryDetail.deliveryChannelCode, AggDeliveryPlatformEnum.MALT_FARM.getCode(),tenantId,storeId,deliveryChannelMap)) {
            return (!isShow) ? null:DeliveryOperateItemEnum.EXCEPTION_TO_SELF.type;
        }
        if (tLaunchDeliveryType.canLaunchThirdPartWhenException && MccConfigUtil.getDeliveryOperateItemExceptionToThird()) {
            return (!isShow) ? null:DeliveryOperateItemEnum.EXCEPTION_TO_THIRD_PART.type;
        }
        if (tLaunchDeliveryType.canManualLaunchThirdPart && MccConfigUtil.getDeliveryOperateItemManualThird()) {
            return (!isShow) ? null:DeliveryOperateItemEnum.MANUAL_THIRD_PART.type;
        }

        if (tLaunchDeliveryType.canCancel && MccConfigUtil.getDeliveryOperateItemCanCancel()) {
            return (!isShow) ? null:DeliveryOperateItemEnum.CANCEL_DELIVERY.type;
        }
        if (Objects.equals(tDeliveryDetail.displayCancelStatus, DisplayCancelStatusEnum.CANCELING.getCode()) && MccConfigUtil.getDeliveryOperateItemCanceling()) {
            return (!isShow) ? null:DeliveryOperateItemEnum.CANCELING.type;
        }

        if (tLaunchDeliveryType.canRetryLaunchByMaltfarm && MccConfigUtil.getDeliveryOperateItemRetryByMaltfarm()) {
            return (!isShow) ? null:DeliveryOperateItemEnum.RETRY_LAUNCH_BY_MALTFARM.type;
        }

        if (tLaunchDeliveryType.canRetryLaunchByHaiKui && MccConfigUtil.getDeliveryOperateItemRetryByHaiKui()) {
            return (!isShow) ? null:DeliveryOperateItemEnum.RETRY_LAUNCH_BY_HAIKUI.type;
        }

        return null;
    }

    private boolean isShowDeliveryItem(Long tenantId){
        if(MccConfigUtil.isShowDeliveryOperateItem()){
            return true;
        }
        if (tenantId==null ||
                MccConfigUtil.getDHTenantIdList().contains(tenantId+"")){
            return true;
        }
        return false;
    }


    /**
     * 配送渠道:
     * <p>
     * - 平台配送
     * <p>
     * 展示"平台配送"即可
     * <p>
     * - 商家自配："商家自配"
     * <p>
     * - 三方配送：如接入了三方配送并成功发起，则覆盖原有的配送方式
     * <p>
     * 美团海葵
     * <p>
     * 达达快送
     * <p>
     * 蜂鸟即配
     *
     * @param deliveryEntity
     * @param tmsDeliveryChannelName
     * @return
     */
    @TestAccessible
    public String formatChannelName(Integer deliveryEntity, String tmsDeliveryChannelName, Integer isFourWheelDelivery) {

        DeliveryEntityEnum deliveryEntityEnum = DeliveryEntityEnum.findByValue(deliveryEntity);
        if (Objects.isNull(deliveryEntityEnum)) {
            return StringUtils.EMPTY;
        }
        
        if (Objects.equals(isFourWheelDelivery, 2)) {
            return tmsDeliveryChannelName + "-" + "汽车";
        }
        return tmsDeliveryChannelName;
    }

    /**
     * 去tms系统查询实时的配送信息
     *
     * @param empowerOrderIds
     * @return
     */
    private Pair<Map<Long, TDeliveryDetail>, Map<Integer, DeliveryChannelDto>> queryRealTimeDeliveryInfo(List<QueryOrderDeliveryInfoKey> empowerOrderIds) {
        List<TDeliveryDetail> tDeliveryDetails = Lists.newArrayList();
        try {
            tDeliveryDetails = tmsServiceWrapper.queryDeliveryInfoByOrderIds(empowerOrderIds);
        } catch (Exception e) {
            log.error("查询tms配送信息异常", e);
        }
        if (CollectionUtils.isEmpty(tDeliveryDetails)) {
            return new Pair<>(Maps.newHashMap(),Maps.newHashMap());
        }
        Map<Long, TDeliveryDetail> realDeliveryMap=tDeliveryDetails.stream().collect(Collectors.toMap(info -> info.bizOrderId, info -> info, (newEntry, oldEntry) -> newEntry));
        //没有做控制。
        Set<Integer> channelCodes = tDeliveryDetails.stream().filter(e -> !deliveryChannelWrapper.checkTenantAndStore(Objects.isNull(empowerOrderIds.get(0))? null : empowerOrderIds.get(0).tenantId, null)).map(e-> e.deliveryChannelCode).collect(Collectors.toSet());
        Map<Integer, DeliveryChannelDto> deliveryChannelDtoMap = deliveryChannelWrapper.getDeliveryChannelDtoMap(channelCodes);

        List<Long> doneOrderIdList=new ArrayList<>();
        for (Long orderId : realDeliveryMap.keySet()){
            TDeliveryDetail real=realDeliveryMap.get(orderId);
            if(real.status==null){
                continue;
            }
            if(real.status!=DeliveryStatusEnum.DELIVERY_CANCELLED.getCode()){
                continue;
            }
            if(real.deliveryCount==null || real.deliveryCount==1){
                continue;
            }
            doneOrderIdList.add(orderId);
        }
        if(CollectionUtils.isEmpty(doneOrderIdList)){
            log.warn("queryRealTimeDeliveryInfo warn： doneOrderIdList is empty");
            return new Pair<>(realDeliveryMap,deliveryChannelDtoMap);
        }
        List<TDeliveryOrder> doneDeliveryList = Lists.newArrayList();
        try {
            doneDeliveryList=tmsServiceWrapper.queryActiveDeliveryInfoByOrderIds(doneOrderIdList);
        }catch (Exception e){
            log.error("queryActiveDeliveryInfoByOrderIds error doneOrderIdList:{}",doneOrderIdList,e);
        }
        if(CollectionUtils.isEmpty(doneDeliveryList)){
            log.warn("queryActiveDeliveryInfoByOrderId warn: doneDeliveryList is empty");
            return new Pair<>(realDeliveryMap,deliveryChannelDtoMap);
        }
        Map<Long,TDeliveryOrder> orderMap=new HashMap<>();
        for (TDeliveryOrder order : doneDeliveryList){
            orderMap.put(order.getOrderId(),order);
        }
        Set<Integer> doneDeliveryChannelCodes = doneDeliveryList.stream().filter(e -> !deliveryChannelWrapper.checkTenantAndStore(Objects.isNull(empowerOrderIds.get(0))? null : empowerOrderIds.get(0).tenantId, null)).map(e-> e.getDeliveryChannel()).collect(Collectors.toSet());
        //所有运单信息（包括取消、激活）的配送渠道code
        doneDeliveryChannelCodes.addAll(channelCodes);
        Map<Integer, DeliveryChannelDto> deliveryChannelAllDtoMap = deliveryChannelWrapper.getDeliveryChannelDtoMap(doneDeliveryChannelCodes);

        for (Long orderId : doneOrderIdList){
            TDeliveryOrder order=orderMap.get(orderId);
            TDeliveryDetail detail=realDeliveryMap.get(orderId);
            String channelName = Objects.nonNull(deliveryChannelAllDtoMap.get(order.getDeliveryChannel()))?deliveryChannelAllDtoMap.get(order.getDeliveryChannel()).getCarrierName() : null;
            if (deliveryChannelWrapper.checkTenantAndStore(order.getTenantId(),order.getStoreId())){
                channelName = order.getDeliveryChannel() == null ? null : DeliveryChannelEnum.valueOf(order.getDeliveryChannel()).getName();
            }
            realDeliveryMap.put(orderId,convertToDetail(order,detail,channelName));
        }
        return new Pair<>(realDeliveryMap,deliveryChannelAllDtoMap);
    }

    private TDeliveryDetail convertToDetail(TDeliveryOrder domain,TDeliveryDetail otherDetail,String deliveryChannelName){
        return TDeliveryDetail.builder().deliveryFee(domain.getDeliveryFee() == null? null : domain.getDeliveryFee().doubleValue())
                .deliveryDistance(domain.getDistance())
                .bizOrderId(domain.getOrderId())
                .deliveryEntity(otherDetail.deliveryEntity)
                .deliveryChannelName(StringUtils.isEmpty(deliveryChannelName)? null : deliveryChannelName)
                .riderName( domain.getRiderName())
                .status(domain.getStatus())
                .tLaunchDeliveryType(otherDetail.tLaunchDeliveryType)
                .riderPhone(domain.getRiderPhone())
                .deliveryChannelCode(Optional.ofNullable(domain.getDeliveryChannel()).orElse(DeliveryChannelEnum.AGGREGATION_DELIVERY.getCode()))
                .deliveryCount(domain.getDeliveryCount()==null ? 1:domain.getDeliveryCount())
                .createTime(otherDetail.createTime)
                .fulfillOrderId(domain.getFulfillmentOrderId())
                .platformSource(domain.getPlatformSourceCode())
                .transType(1)
                .build();
    }

    private List<QueryOrderDeliveryInfoKey> collectEmpowerOrderKeys(List<OrderVO> orderList) {

        if (CollectionUtils.isEmpty(orderList)) {
            return Lists.newArrayList();
        }
        return orderList.stream().map(this::buildQueryOrderKey).collect(Collectors.toList());
    }

    private QueryOrderDeliveryInfoKey buildQueryOrderKey(OrderVO order) {
        Long shopId=order.getStoreId();
        if(order.getWarehouseId()!=null){
            shopId=order.getWarehouseId();
        }
        if(order.getDispatchShopId()!=null){
            shopId = order.getDispatchShopId();
        }
        return new QueryOrderDeliveryInfoKey(order.getTenantId(), shopId, order.getEmpowerOrderId(), order.getOrderStatus(), order.getOrderSource(), order.getDeliveryMethod(), order.getSelfDelivery());

    }

    /**
     * 为了单个订单添加配送信息、可操作按钮
     *
     * @param orderVO
     */
    public void appendDeliveryInfo(OrderVO orderVO) {
        appendDeliveryInfo(Lists.newArrayList(orderVO));
    }
}
