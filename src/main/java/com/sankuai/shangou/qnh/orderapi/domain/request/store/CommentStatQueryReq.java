package com.sankuai.shangou.qnh.orderapi.domain.request.store;
// Copyright (C) 2019 Meituan
// All rights reserved

import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.exception.store.CommonRuntimeException;
import com.sankuai.shangou.qnh.orderapi.utils.store.DateUtils;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;
import java.util.List;

@TypeDoc(
        description = "租户统计数据查询"
)
@Data
public class CommentStatQueryReq {

    @FieldDoc(
            description = "门店Id",
            rule = "storeId, 使用英文逗号分隔"
    )
    @NotNull
    private String storeId;


    @FieldDoc(
            description = "渠道id列表"
    )
    private List<Integer> channelIds;

    @FieldDoc(
            description = "查询开始时间"
    )
    @NotNull
    private String startTime;

    @FieldDoc(
            description = "查询结束时间"
    )
    @NotNull
    private String endTime;

    @FieldDoc(
            description = "门店类型3-门店 5-中心仓 6-共享前置仓", requiredness = Requiredness.OPTIONAL
    )
    private Integer entityType;

    public void validate(){
        if (StringUtils.isEmpty(this.storeId)) {
            throw new CommonRuntimeException("门店/仓不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (StringUtils.isEmpty(this.startTime)) {
            throw new CommonRuntimeException("开始日期不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (!DateUtils.isValidDate(this.startTime, DateUtils.YYYY_MM_DD)) {
            throw new CommonRuntimeException("开始日期格式错误", ResultCode.CHECK_PARAM_ERR);
        }
        if (StringUtils.isEmpty(this.endTime)) {
            throw new CommonRuntimeException("结束日期不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (!DateUtils.isValidDate(this.endTime, DateUtils.YYYY_MM_DD)) {
            throw new CommonRuntimeException("结束日期格式错误", ResultCode.CHECK_PARAM_ERR);
        }
    }
}
