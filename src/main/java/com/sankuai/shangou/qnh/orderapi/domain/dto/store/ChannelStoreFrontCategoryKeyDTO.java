package com.sankuai.shangou.qnh.orderapi.domain.dto.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2019/8/21
 * desc:
 */
@TypeDoc(
        description = "聚合渠道门店分类"
)
@Data
@ApiModel("聚合渠道门店分类")
public class ChannelStoreFrontCategoryKeyDTO {

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID", required = true)
    @NotNull
    private Integer channelId;

    @FieldDoc(
            description = "前台分类ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "前台分类ID", required = true)
    @NotNull
    private Long frontCategoryId;
}
