package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/4/30
 * desc:
 */
@TypeDoc(
        description = "渠道信息"
)
@Data
@ApiModel("渠道信息")
public class ChannelInfoVO {

    @FieldDoc(
            description = "渠道ID  -1-线下 100-美团 200-饿了么 300-京东到家", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID  -1-线下 100-美团 200-饿了么 300-京东到家", required = true)
    private Integer id;

    @FieldDoc(
            description = "渠道名称  ", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道名称", required = true)
    private String name;
}
