package com.sankuai.shangou.qnh.orderapi.remote;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.meituan.reco.pickselect.common.exception.FallbackException;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.meituan.shangou.saas.tenant.thrift.common.StatusCodeEnum;
import com.sankuai.meituan.reco.store.saas.infrastructure.shield.common.monitor.CommonMonitorTransaction;
import com.sankuai.meituan.shangou.empower.auth.thrift.service.AuthThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryPermissionGroupRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryPermissionGroupResponse;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.Result;
import com.sankuai.shangou.qnh.orderapi.exception.store.CommonLogicException;
import com.sankuai.shangou.qnh.orderapi.exception.store.CommonRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;

/**
 * Created by yangli on 18/9/17.
 */
@Slf4j
@Rhino
@Service
public class AutoManageRemoteService {

    @Autowired
    private AuthThriftService.Iface authThriftService;

    @Degrade(rhinoKey = "AutoManageRemoteService.queryPermissionGroup",
            fallBackMethod = "queryPermissionGroupFallback",
            timeoutInMilliseconds = 500,
            ignoreExceptions = CommonLogicException.class)
    @MethodLog(logResponse = true)
    @CommonMonitorTransaction
    public QueryPermissionGroupResponse queryPermissionGroup(QueryPermissionGroupRequest request){
        final String errorMsg = "账户权限信息获取失败";
        try {
            QueryPermissionGroupResponse response = authThriftService.queryPermissionGroupByTokenAndPermissionType(request);
            nullResponseCheck(response, errorMsg);
            validateStatus(response.getResult(), errorMsg);
            return response;
        } catch (TException e) {
            log.error("账户权限信息获取失败, request = {}", request, e);
            throw new CommonRuntimeException(errorMsg, e);
        }
    }

    private QueryPermissionGroupResponse queryPermissionGroupFallback(QueryPermissionGroupRequest request) {
        throw new FallbackException("AutoManageWrapper.queryPermissionGroup接口降级");
    }

    private static void nullResponseCheck(Object resp, String errorMsg) {
        if(resp == null) {
            throw new CommonLogicException(errorMsg + ", response is null");
        }
    }

    private static void validateStatus(Result status, String errorMsg) {
        if (status == null) {
            throw new CommonLogicException(errorMsg + ", status is null");
        }

        if (status.getCode() != StatusCodeEnum.SUCCESS.getCode()) {
            throw new CommonLogicException(MessageFormat.format("{0}, code = {1}, detail = {2}",
                    errorMsg, status.getCode(), status.getMsg()));
        }
    }

}
