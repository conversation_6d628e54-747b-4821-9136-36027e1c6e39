package com.sankuai.shangou.qnh.orderapi.remote;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.qnh.fulfill.print.client.thrift.PrintLogThriftService;
import com.sankuai.qnh.fulfill.print.client.thrift.dto.print.request.QueryOrderPrintTimesReq;
import com.sankuai.qnh.fulfill.print.client.thrift.dto.print.request.QueryPrintTimesSingleReq;
import com.sankuai.qnh.fulfill.print.client.thrift.dto.print.response.QueryPrintTimesResp;
import com.sankuai.meituan.reco.pickselect.consts.OpenOpType;
import com.sankuai.meituan.reco.pickselect.consts.ResultCodeEnum;
import com.sankuai.meituan.reco.pickselect.thrift.OpenPickThriftService;
import com.sankuai.meituan.reco.pickselect.thrift.OpenTraceListQueryRequest;
import com.sankuai.meituan.reco.pickselect.thrift.OpenTraceListResponse;
import com.sankuai.meituan.reco.pickselect.thrift.OpenTraceQueryRequest;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import com.sankuai.shangou.qnh.orderapi.utils.pc.LionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.shangou.qnh.orderapi.remote.OrderMngRemoteService.convertBizType;

@Component
@Slf4j
@Rhino
public class PickSelectOrderPrintService {


    @Autowired
    private PrintLogThriftService newPrintLogThriftService;


    @Degrade(rhinoKey = "PickSelectOrderPrintService-queryPrintTimesForStores",
            fallBackMethod = "queryPrintTimesForStoresFallback",
            timeoutInMilliseconds = 400)
    public Map<Long, Integer> queryPrintTimesForStores(Map<Long, Long> orderIdGroup) {
        try{
            Map<Long, Integer> printTimesMap = new HashMap<>();
            QueryOrderPrintTimesReq req = getNewQueryOrderPrintTimesReq(orderIdGroup);
            QueryPrintTimesResp resp = newPrintLogThriftService.queryOrderPrintTimes(req);
            if(resp == null || resp.getStatus() == null || resp.getStatus().getCode()!= 0 || MapUtils.isEmpty(resp.getPrintSuccessfulTimesMap())){
                return Collections.emptyMap();
            }
            printTimesMap.putAll(resp.getPrintSuccessfulTimesMap());
            return printTimesMap;
        }catch (Exception e){
            log.info("queryPrintTimesForStores error", e);
        }
        return Collections.emptyMap();

    }

    // 新打印服务
    private QueryOrderPrintTimesReq getNewQueryOrderPrintTimesReq(Map<Long, Long> orderIdGroup) {
        QueryOrderPrintTimesReq req = new QueryOrderPrintTimesReq();
        ArrayList<QueryPrintTimesSingleReq> list = new ArrayList<>();
        for (Map.Entry<Long, Long> entry : orderIdGroup.entrySet()) {
            QueryPrintTimesSingleReq singleReq = new QueryPrintTimesSingleReq();
            singleReq.setOrderId(entry.getKey());
            singleReq.setStoreId(entry.getValue());
            list.add(singleReq);
        }
        req.setStoreIdAndOrderIdsList(list);
        return req;
    }


    public QueryPrintTimesResp queryPrintTimesForStoresFallback(Map<Long, Long> orderIdGroup) {
        log.warn("PickSelectOrderPrintService.queryPrintTimesForStores 发生降级");
        return new QueryPrintTimesResp();
    }
}