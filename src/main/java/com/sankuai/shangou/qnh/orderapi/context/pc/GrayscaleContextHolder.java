package com.sankuai.shangou.qnh.orderapi.context.pc;

import lombok.Data;
import org.apache.commons.lang.BooleanUtils;

/***
 * author : <EMAIL> 
 * data : 2021/3/15 
 * time : 下午4:37
 **/
public class GrayscaleContextHolder {

    private static final ThreadLocal<GraySwitch> LOCAL = ThreadLocal.withInitial(GraySwitch::new);


    public static void setOcmsOrderMigrateGray(String ocmsOrderMigrate){
        LOCAL.get().setOcmsOrderMigrate(ocmsOrderMigrate);
    }

    public static boolean isOcmsOrderMigrateGray(){
        return BooleanUtils.toBoolean(LOCAL.get().ocmsOrderMigrate);
    }

    @Data
    static class GraySwitch{

        private String ocmsOrderMigrate;

    }

    /**
     * 销毁上下文数据
     */
    public static void release() {
        LOCAL.remove();
    }
}
