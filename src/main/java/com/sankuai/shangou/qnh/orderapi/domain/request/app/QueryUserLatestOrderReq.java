package com.sankuai.shangou.qnh.orderapi.domain.request.app;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@TypeDoc(
        description = "查询用户最近订单请求(新)"
)
@ApiModel("查询用户最近订单请求(新)")
@Data
public class QueryUserLatestOrderReq {
    @FieldDoc(
            description = "美团用户Id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "美团用户Id")
    @NotNull
    private String userId;

    @FieldDoc(
            description = "可能是仓ID或者门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "可能是仓ID或者门店ID")
    @NotNull
    private String poiId;

    @FieldDoc(
            description = "渠道类型", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "渠道类型")
    private List<Integer> orderBizTypeList;

    @FieldDoc(
            description = "会话Id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "会话Id")
    private Long pubId;
}
