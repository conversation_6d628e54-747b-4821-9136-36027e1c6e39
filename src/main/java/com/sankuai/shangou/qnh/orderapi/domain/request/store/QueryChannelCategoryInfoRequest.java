package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2019/8/29
 * desc:
 */
@TypeDoc(
        description = "根据商品类目查询渠道类目请求"
)
@Data
@ApiModel("根据商品类目查询渠道类目请求")
public class QueryChannelCategoryInfoRequest {
    @FieldDoc(
            description = "商品后台类目", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品后台类目")
    @NotNull
    private String erpCategoryCode;
}
