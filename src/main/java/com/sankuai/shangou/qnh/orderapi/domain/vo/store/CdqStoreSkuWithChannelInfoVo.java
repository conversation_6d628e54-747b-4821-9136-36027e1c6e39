package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @description:门店商品明细
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-01-02
 **/
@TypeDoc(
        description = "门店商品明细"
)
@Data
@ApiModel("门店商品明细")
public class CdqStoreSkuWithChannelInfoVo {

    @FieldDoc(
            description = "门店商品信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店商品信息", required = true)
    @NotNull
    private CdqStoreSkuVo cdqStoreSkuVo;

    @FieldDoc(
            description = "线上商品信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "线上商品信息", required = true)
    @NotNull
    private List<ChannelSkuDetailInfoVo> channelSkuDetailInfoVos;



}
