package com.sankuai.shangou.qnh.orderapi.domain.response.store;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.order.ChannelInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/7/15
 * desc: 查询系统信息响应
 */
@TypeDoc(
        description = "查询系统信息响应"
)
@ApiModel("查询系统信息响应")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QuerySystemInfoResponse {

    @FieldDoc(
            description = "开通渠道列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "开通渠道列表", required = true)
    private List<ChannelInfoVO> channelInfos;
}
