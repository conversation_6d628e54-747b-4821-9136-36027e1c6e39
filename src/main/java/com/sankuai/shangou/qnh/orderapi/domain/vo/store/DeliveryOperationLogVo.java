package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import lombok.Getter;
import lombok.ToString;

import java.util.Collections;
import java.util.List;

/**
 * DeliveryOperationLogVo
 *
 * <AUTHOR>
 * @since 2023/3/6
 */
@Getter
@ToString
public class DeliveryOperationLogVo extends DeliveryLogVo {

    /**
     * 操作码
     */
    private final Integer code;

    /**
     * 详细信息，例如配送异常的具体原因
     */
    private final List<String> details;

    /**
     * 图片url，例如配送异常上传的图片
     */
    private final List<String> pictureUrls;

    public DeliveryOperationLogVo(int code, String recordedName, String description, long time,
            List<String> details, List<String> pictureUrls) {
        super(recordedName, description, DeliveryLogCategory.OPERATION_LOG, time);
        this.code = code;
        this.details = details;
        this.pictureUrls = pictureUrls;
    }

    public DeliveryOperationLogVo(int code, String recordedName, String description, long time) {
        this(code, recordedName, description, time, Collections.emptyList(), Collections.emptyList());
    }

}
