package com.sankuai.shangou.qnh.orderapi.interceptor.pc;

import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.ResultBuilder;
import com.sankuai.shangou.qnh.orderapi.utils.pc.MccConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 11/3/21 11:55 AM
 * @Description:
 */
@Aspect
@Component
@Slf4j
public class DrunkHorseDataAuthAspect {


    @Around(value = "@annotation(com.sankuai.shangou.qnh.orderapi.interceptor.pc.DrunkHorseData)")
    public Object aroundInvoke(ProceedingJoinPoint joinPoint) throws Throwable {

        String className = joinPoint.getTarget().getClass().getSimpleName();

        final String methodName = joinPoint.getSignature().getName();
        final MethodSignature methodSignature = (MethodSignature) joinPoint
                .getSignature();
        Method method = methodSignature.getMethod();
        DrunkHorseData annotation = AnnotationUtils.findAnnotation(method, DrunkHorseData.class);
        if (annotation == null) {
            return joinPoint.proceed();
        } else {

            //如果是歪马租户则不拦截
            List<Long> drunkHorseTenantList = MccConfigUtil.getDrunkHorseTenantId();
            Long currentTenant = ContextHolder.currentUserTenantId();
            if (CollectionUtils.isNotEmpty(drunkHorseTenantList) && drunkHorseTenantList.contains(currentTenant)) {
                return joinPoint.proceed();
            }
            log.warn("非歪马租户不能获取歪马接口数据");
            Class returnClass = method.getReturnType();
            if (returnClass.getSimpleName().equals("Result")) {
                return ResultBuilder.buildSuccessResult();
            } else {
                return null;
            }
        }

    }
}
