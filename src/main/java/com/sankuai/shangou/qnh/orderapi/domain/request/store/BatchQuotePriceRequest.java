package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.SkuPriceVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@TypeDoc(
        description = "查询提价审核默认操作请求参数"
)
@Data
@ApiModel("查询提价审核默认操作请求参数")
public class BatchQuotePriceRequest {
    @FieldDoc(
            description = "门店id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店id", required = true)
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "商品SPUID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品SPUID")
    private String spuId;

    @FieldDoc(
            description = "报价详情信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "报价详情信息")
    private List<SkuPriceVO> quotes;
}
