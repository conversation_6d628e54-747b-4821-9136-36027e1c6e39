package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.order.platform.utils.GsonUtil;
import com.sankuai.shangou.qnh.orderapi.enums.store.ChannelTypeEnum;
import com.sankuai.shangou.qnh.orderapi.constant.store.CommentConstants;
import com.sankuai.shangou.qnh.orderapi.utils.store.DateUtils;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.CommentReplyStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import com.sankuai.sgfulfillment.comment.thrift.dto.model.ChannelCommentDTO;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@TypeDoc(
        description = "评价",
        authors = {"hejunliang"}
)
@Data
public class CommentVO {

    @FieldDoc(
            description = "门店id"
    )
    private Long storeId;

    @FieldDoc(
            description = "门店名称"
    )
    private String storeName;

    @FieldDoc(
            description = "erp门店code"
    )
    private String erpShopCode;

    @FieldDoc(
            description = "渠道id"
    )
    private Integer channelId;

    @FieldDoc(
            description = "渠道名称"
    )
    private String channelName;

    @FieldDoc(
            description = "中台评价id"
    )
    private String commentId;

    @FieldDoc(
            description = "评价内容"
    )
    private String commentContent;

    @FieldDoc(
            description = "评价时间",
            rule = "yyyy-MM-dd"
    )
    private String commentTime;

    @FieldDoc(
            description = "追评内容"
    )
    private String addCommentContent;

    @FieldDoc(
            description = "追评时间",
            rule = "yyyy-MM-dd"
    )
    private String addCommentTime;

    @FieldDoc(
            description = "评价级别"

    )
    private String commentLevel;

    @FieldDoc(
            description = "订单评分"
    )
    private Integer orderScore;

    @FieldDoc(
            description = "质量评分"
    )
    private Integer qualityScore;

    @FieldDoc(
            description = "包装评分"
    )
    private Integer packingScore;

    @FieldDoc(
            description = "配送评分"
    )
    private Integer deliveryScore;

    @FieldDoc(
            description = "图片地址"

    )
    private List<String> commentPictures;

    @FieldDoc(
            description = "配送标签"
    )
    private List<String> deliveryCommentLabels;

    @FieldDoc(
            description = "赞商品列表"
    )
    private List<String> praiseItemList;

    @FieldDoc(
            description = "踩商品列表"
    )
    private List<String> criticItemList;

    @FieldDoc(
            description = "订单商品列表"
    )
    private List<String> orderItemList;

    @FieldDoc(
            description = "评论回复内容"
    )
    private String replyContent;

    @FieldDoc(
            description = "回复状态",
            rule = "NOT_REPLY:未回复, REPLYING:审核中, REPLY_FAILED:审核失败, REPLIED:已回复"
    )
    private String replyStatus;

    @FieldDoc(
            description = "回复时间",
            rule = "yyyy-MM-dd HH:mm:ss"
    )
    private String replyTime;

    @FieldDoc(
            description = "评价后回复过期小时"
    )
    private Integer replyExpireHourAfterComment;

    @FieldDoc(
            description = "评论回复过期分钟数(commentReplyExpireInMinute分钟以内过期)"
    )
    private Integer commentReplyExpireInMinute;

    @FieldDoc(
            description = "是否可以回复"
    )
    private Boolean canReply;

    @FieldDoc(
            description = "联系用户过期小时"
    )
    private Integer contactUserExpireHourAfterComment;

    @FieldDoc(
            description = "是否可以联系用户"
    )
    private Boolean canContactUser;

    @FieldDoc(
            description = "虚拟手机号",
            rule = "getComment请求, 如果可以联系用户, 返回手机号"
    )
    private String userVirtualPhone;

    @FieldDoc(
            description = "渠道订单号"
    )
    private String channelOrderId;

    @FieldDoc(
            description = "最大可拨打数"
    )
    private int maxCallNum;

    @FieldDoc(
            description = "评价是否回访，INIT:未回访, DONE:已回访",
            rule = "INIT:未回访, DONE:已回访"
    )
    private Integer commentContactType;

    @FieldDoc(
            description = "评价过期时间，为空不展示"
    )
    private String contactExpireTime;

    @FieldDoc(
            description = "履约人员列表，为空不展示"
    )
    private List<String> fulfillOperatorNames;

    @FieldDoc(
            description = "回访人员列表，为空不展示"
    )
    private List<CommentContactOperator> commentContactOperators;
    @FieldDoc(
            description = "追平评论列表，目前仅有赞渠道使用"
    )
    private List<CommentContentVO> addCommentList;
    @FieldDoc(
            description = "商家回复评论列表，目前仅有赞渠道使用"
    )
    private List<CommentContentVO> replyCommentList;
    @FieldDoc(
            description = "评价状态"
    )
    private Boolean isValid;

    @FieldDoc(description = "评价溯源系统匹配订单状态")
    private String matchOrderStatus;

    @FieldDoc(description = "评价溯源系统匹配订单id列表")
    private List<String> matchChannelOrderIds;

    public static CommentVO build(ChannelCommentDTO commentDTO) {
        if (commentDTO == null) {
            return null;
        }
        CommentVO commentVO = new CommentVO();
        commentVO.setStoreId(commentDTO.getStoreId());
        commentVO.setStoreName(null);
        commentVO.setChannelId(commentDTO.getChannelId());
        commentVO.setChannelName(ChannelTypeEnum.findChannelNameByChannelId(commentDTO.getChannelId()));
        commentVO.setCommentId(commentDTO.getCommentId());
        commentVO.setCommentContent(commentDTO.getCommentContent());
        commentVO.setCommentTime(StringUtils.isNotEmpty(commentDTO.getCommentTime()) ?
                DateUtils.format(DateUtils.parse(commentDTO.getCommentTime(), DateUtils.YYYY_MM_DD_HH_MM_SS),
                        DateUtils.YYYY_MM_DD) : StringUtils.EMPTY);

        commentVO.setCommentLevel(commentDTO.getCommentLevel());
        commentVO.setOrderScore(commentDTO.getOrderScore() != CommentConstants.COMMENT_THRIFT_INT_VALUE_NULL ? commentDTO.getOrderScore() : null);
        commentVO.setQualityScore(commentDTO.getQualityScore() != CommentConstants.COMMENT_THRIFT_INT_VALUE_NULL ? commentDTO.getQualityScore() : null);
        commentVO.setPackingScore(commentDTO.getPackingScore() != CommentConstants.COMMENT_THRIFT_INT_VALUE_NULL ? commentDTO.getPackingScore() : null);
        commentVO.setDeliveryScore(commentDTO.getDeliveryScore() != CommentConstants.COMMENT_THRIFT_INT_VALUE_NULL ? commentDTO.getDeliveryScore() : null);
        commentVO.setCommentPictures(CollectionUtils.isNotEmpty(commentDTO.getCommentPictures()) ?
                commentDTO.getCommentPictures() : Collections.emptyList());
        commentVO.setDeliveryCommentLabels(CollectionUtils.isNotEmpty(commentDTO.getDeliveryCommentLabels()) ?
                commentDTO.getDeliveryCommentLabels() : Collections.emptyList());
        commentVO.setPraiseItemList(CollectionUtils.isNotEmpty(commentDTO.getPraiseItemList()) ?
                commentDTO.getPraiseItemList() : Collections.emptyList());
        commentVO.setCriticItemList(CollectionUtils.isNotEmpty(commentDTO.getCriticItemList()) ?
                commentDTO.getCriticItemList() : Collections.emptyList());
        commentVO.setOrderItemList(CollectionUtils.isNotEmpty(commentDTO.getOrderItemList()) ?
                commentDTO.getOrderItemList() : Collections.emptyList());

        if(commentDTO.getChannelId() == ChannelTypeEnum.YOU_ZAN.getChannelId()){
            commentVO.setAddCommentList(buildCommentContentVOList(commentDTO.getAddCommentContent()));
            commentVO.setReplyCommentList(calculateReplyContentList(commentDTO.getReplyStatus(), commentDTO.getReplyContent(), commentDTO.getReplyDraft()));
        }else{
            commentVO.setAddCommentContent(commentDTO.getAddCommentContent());
            commentVO.setReplyContent(calculateReplyContent(commentDTO.getReplyStatus(), commentDTO.getReplyContent(), commentDTO.getReplyDraft()));
        }
        commentVO.setAddCommentTime(StringUtils.isNotEmpty(commentDTO.getAddCommentTime()) ?
                DateUtils.format(DateUtils.parse(commentDTO.getAddCommentTime(), DateUtils.YYYY_MM_DD_HH_MM_SS),
                        DateUtils.YYYY_MM_DD) : StringUtils.EMPTY);
        commentVO.setReplyStatus(commentDTO.getReplyStatus());
        commentVO.setReplyTime(calculateReplyTime(commentDTO.getReplyStatus(), commentDTO.getReplyTime(), commentDTO.getReplyDraftTime()));
        commentVO.setReplyExpireHourAfterComment(commentDTO.getReplyExpireHourAfterComment());
        commentVO.setCommentReplyExpireInMinute(commentDTO.getCommentReplyExpireInMinute());
        commentVO.setCanReply(commentDTO.getCanReply());
        commentVO.setContactUserExpireHourAfterComment(commentDTO.getContactUserExpireHourAfterComment());
        commentVO.setCanContactUser(commentDTO.getCanContactUser());
        commentVO.setChannelOrderId(commentDTO.getChannelOrderId());
        commentVO.setMaxCallNum(commentDTO.getMaxCallNum());
        commentVO.setIsValid(commentDTO.getValid());

        commentVO.setCommentContactType(commentDTO.getCommentContactType());
        commentVO.setContactExpireTime(commentDTO.getContactExpireTime());
        commentVO.setFulfillOperatorNames(commentDTO.getFulfillOperatorNames());
        commentVO.setCommentContactOperators(
                Optional.ofNullable(commentDTO.getContactOperators())
                .orElse(Lists.newArrayList())
                .stream()
                .map(
                        operatorDTO -> {
                            CommentContactOperator commentContactOperator = new CommentContactOperator();
                            commentContactOperator.setContactOperatorName(operatorDTO.getContactOperatorName());
                            commentContactOperator.setContactOperatorPhone(operatorDTO.getContactOperatorPhone());
                            return commentContactOperator;
                        }
                ).collect(Collectors.toList())
        );
        commentVO.setMatchOrderStatus(commentDTO.getMatchOrderStatus());
        commentVO.setMatchChannelOrderIds(commentDTO.getMatchChannelOrderIds());
        return commentVO;
    }
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CommentContentVO{

        public String commentId; // optional

        public String commentTime; // required

        public String commentContent; // required

        public List<String> pictureList; // optional

        public CommentContentVO(String commentContent){
            this.commentContent = commentContent;
            this.commentTime = DateUtils.format(new Date(),DateUtils.YYYY_MM_DD_HH_MM_SS);
        }
    }

    private static String calculateReplyContent(String replyStatus, String replyContent, String replyDraft) {
        if (CommentReplyStatusEnum.REPLIED.name().equals(replyStatus)) {
            return StringUtils.isNotEmpty(replyContent) ? replyContent : CommentConstants.COMMENT_REPLY_BY_OTHER_PLATFORM_SHOW_CONTENT;
        } else {
            return replyDraft;
        }
    }

    /**
     * 有赞渠道新增字段，为了兼容多条回复与追评
     * @param replyStatus
     * @param replyContent
     * @param replyDraft
     * @return
     */
    private static List<CommentContentVO> calculateReplyContentList(String replyStatus, String replyContent, String replyDraft) {
        if (CommentReplyStatusEnum.REPLIED.name().equals(replyStatus)) {
            return StringUtils.isNotEmpty(replyContent) ? buildCommentContentVOList(replyContent): Lists.newArrayList(new CommentContentVO(CommentConstants.COMMENT_REPLY_BY_OTHER_PLATFORM_SHOW_CONTENT));
        } else {
            return Lists.newArrayList(new CommentContentVO(replyDraft));
        }
    }

    private static List<CommentContentVO> buildCommentContentVOList(String content) {
        if(StringUtils.isNotBlank(content)){
            return GsonUtil.fromJSonList(content,CommentContentVO.class);
        }
        return null;
    }

    private static String calculateReplyTime(String replyStatus, String replyTime, String replyDraftTime) {
        if (CommentReplyStatusEnum.REPLIED.name().equals(replyStatus)) {
            return StringUtils.isNotEmpty(replyTime) ? replyTime : null;
        } else {
            return StringUtils.isNotEmpty(replyDraftTime) ? replyDraftTime : null;
        }
    }
}
