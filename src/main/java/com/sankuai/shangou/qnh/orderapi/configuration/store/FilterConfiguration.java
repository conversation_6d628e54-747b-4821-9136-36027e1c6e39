package com.sankuai.shangou.qnh.orderapi.configuration.store;

import com.sankuai.meituan.shangou.empower.auth.sdk.filter.AuthFilterFactoryBean;
import com.sankuai.meituan.shangou.empower.auth.sdk.filter.LoginFilterFactoryBean;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.web.filter.DelegatingFilterProxy;

import javax.servlet.DispatcherType;

/**
 * Created by liujia on 2018/8/15.
 */
@Configuration(value = "storeFilterConfiguration")
public class FilterConfiguration {
    private static final int CHARACTER_ENCODING_FILTER_ORDER = 2;

    /**
     * 确保加载顺序
     */
    private static final int JMONITOR_LISTENER_ORDER = Ordered.HIGHEST_PRECEDENCE + 100;
    private static final int MT_CONTEXT_LISTENER_ORDER = JMONITOR_LISTENER_ORDER + 1;
    private static final int HTTP_MONITOR_FILTER_ORDER = MT_CONTEXT_LISTENER_ORDER + 1;
    private static final int CROS_MONITOR_FILTER_ORDER = HTTP_MONITOR_FILTER_ORDER + 1;
    private static final int LOGIN_FILTER_ORDER = CROS_MONITOR_FILTER_ORDER + 1;
    private static final int CAT_MONITOR_FILTER_ORDER = LOGIN_FILTER_ORDER + 1;
    private static final int ENCODING_MONITOR_FILTER_ORDER = CAT_MONITOR_FILTER_ORDER + 1;
    private static final int AUTH_FILTER_ORDER = ENCODING_MONITOR_FILTER_ORDER + 1;


    // 数据鉴权准备 filter，这里放最后面
    private static final int DATA_SECURITY_PREPARE_FILTER_ORDER = 100;

    //登录校验filter
    @Bean
    public FilterRegistrationBean loginFilter(){
        DelegatingFilterProxy filter = new DelegatingFilterProxy();
        FilterRegistrationBean registration = new FilterRegistrationBean();
        filter.setTargetBeanName("loginFilterBean");
        filter.setTargetFilterLifecycle(true);

        registration.setFilter(filter);
        registration.addUrlPatterns("/storemanagement/purchase/*");
        registration.addUrlPatterns("/storemanagement/delivery/*");
        registration.addUrlPatterns("/storemanagement/store/*");
        registration.addUrlPatterns("/storemanagement/break/*");
        registration.addUrlPatterns("/storemanagement/goods/*");
        registration.addUrlPatterns("/storemanagement/homepage/*");
        registration.addUrlPatterns("/storemanagement/return/*");
        registration.addUrlPatterns("/storemanagement/refund/*");
        registration.addUrlPatterns("/storemanagement/device/*");
        registration.addUrlPatterns("/storemanagement/order/*");
        registration.addUrlPatterns("/storemanagement/adjust/*");
        registration.addUrlPatterns("/storemanagement/stockcheck/*");
        registration.addUrlPatterns("/storemanagement/location/*");
        registration.addUrlPatterns("/storemanagement/employee/device/confirm");
        registration.addUrlPatterns("/storemanagement/employee/device/batch-confirm");
        registration.addUrlPatterns("/storemanagement/employee/device/reportDeviceToWm");
        registration.addUrlPatterns("/storemanagement/employee/device/genWmPoiXmAccount");
        registration.addUrlPatterns("/storemanagement/employee/device/getXmUidByMtUid");
        registration.addUrlPatterns("/storemanagement/process/*");
        registration.addUrlPatterns("/storemanagement/transfer/*");
        registration.addUrlPatterns("/storemanagement/auth/*");
        registration.addUrlPatterns("/storemanagement/hwhGoods/*");
        registration.addUrlPatterns("/storemanagement/marketsurvey/*");
        registration.addUrlPatterns("/storemanagement/ocms/skuandstock/*");
        registration.addUrlPatterns("/storemanagement/ocms/order/*");
        registration.addUrlPatterns("/quote/ocms/*");
        registration.addUrlPatterns("/quote/channel/price/*");
        registration.addUrlPatterns("/storemanagement/vegetablemarket/*");
        registration.addUrlPatterns("/storemanagement/operation/*");
        registration.addUrlPatterns("/storemanagement/stocktake/*");
        registration.addUrlPatterns("/price/syncStrategy/*");
        /* 1000017L营收 start */
        registration.addUrlPatterns("/order/revenue/booth/*");
        registration.addUrlPatterns("/order/currently/revenue/booth/*");
        registration.addUrlPatterns("/order/revenue/cashier/*");
        registration.addUrlPatterns("/order/revenue/store/*");
        /* 订单中台营收 end */
        registration.addUrlPatterns("/pie/settle/*");//获取

        registration.addUrlPatterns("/storemanagement/ocms/channelComment/*");
        registration.addUrlPatterns("/storemanagement/deliveryservice/*");

        registration.addUrlPatterns("/storemanagement/marketresearch/*");
        registration.addUrlPatterns("/storemanagement/push/config/getConfig");
        registration.addUrlPatterns("/storemanagement/push/config/saveConfig");
        registration.addUrlPatterns("/storemanagement/push/config/getUserBizAuth");
        registration.addUrlPatterns("/storemanagement/push/config/getBizCodeAuth");
        registration.addUrlPatterns("/api/v1/price/*");

        //智能秤复核相关接口
        registration.addUrlPatterns("/storemanagement/ocms/spu/*");

        // 配送异常订单相关接口
        registration.addUrlPatterns("/order/exception/delivery/*");

        registration.setDispatcherTypes(DispatcherType.REQUEST);
        registration.setName("loginFilter");

        registration.setOrder(LOGIN_FILTER_ORDER);
        return registration;
    }

    //登录factoryBean 和相关配置
    @Bean
    public LoginFilterFactoryBean loginFilterBean(){
        LoginFilterFactoryBean loginFilterFactoryBean = new LoginFilterFactoryBean();
        loginFilterFactoryBean.setSecret("");//账号系统分配
        loginFilterFactoryBean.setLogEnable(true);//info日志打印
        loginFilterFactoryBean.setExcludedUriList("/api/account/**");//不校验配置urlList（ANT风格） 逗号分隔
        loginFilterFactoryBean.setIncludedUriList("");//校验配置urlList（ANT风格） 逗号分隔,优先级高于exludedUriList，同时配置则只有include生效
        // loginFilterFactoryBean.setUnAuthorizedResponse("{\"code\": 401,\"message\": \"未登录，请登录后再试\"}");//校验不通过给前端返回信息
        return loginFilterFactoryBean;
    }

    //鉴权filter
    @Bean
    public FilterRegistrationBean authFilter(){
        DelegatingFilterProxy filter = new DelegatingFilterProxy();
        FilterRegistrationBean registration = new FilterRegistrationBean();
        filter.setTargetBeanName("authFilterBean");
        filter.setTargetFilterLifecycle(true);

        registration.setFilter(filter);
        //接口鉴权url
        registration.addUrlPatterns("/storemanagement/purchase/*");
        registration.addUrlPatterns("/storemanagement/delivery/*");
        registration.addUrlPatterns("/storemanagement/break/*");
        registration.addUrlPatterns("/storemanagement/goods/*");
        registration.addUrlPatterns("/storemanagement/return/*");
        registration.addUrlPatterns("/storemanagement/refund/*");
        registration.addUrlPatterns("/storemanagement/order/*");
        registration.addUrlPatterns("/quote/ocms/*");
        registration.addUrlPatterns("/quote/channel/price/*");
        registration.addUrlPatterns("/storemanagement/adjust/*");
        registration.addUrlPatterns("/storemanagement/stockcheck/*");
        registration.addUrlPatterns("/storemanagement/location/*");
        registration.addUrlPatterns("/storemanagement/process/*");
        registration.addUrlPatterns("/storemanagement/transfer/*");
        registration.addUrlPatterns("/storemanagement/hwhGoods/*");
        registration.addUrlPatterns("/storemanagement/marketsurvey/*");
        registration.addUrlPatterns("/storemanagement/ocms/skuandstock/updatepriceandstock"); //中台商品价格库存更新验证
        registration.addUrlPatterns("/storemanagement/ocms/skuandstock/changechannelskustatus"); //中台商品上下架验证
        registration.addUrlPatterns("/storemanagement/vegetablemarket/*");
        registration.addUrlPatterns("/storemanagement/operation/*");
        registration.addUrlPatterns("/price/syncStrategy/*");

        registration.addUrlPatterns("/storemanagement/stocktake/*");
        /* 订单中台营收 start */
        registration.addUrlPatterns("/order/revenue/booth/*");
        registration.addUrlPatterns("/order/revenue/cashier/*");
        registration.addUrlPatterns("/order/revenue/store/*");
        /* 订单中台营收 end */
        registration.addUrlPatterns("/storemanagement/ocms/channelComment/*");

        //按钮权限验证
        registration.addUrlPatterns("/storemanagement/break/updateOrderStatus");//报损按钮权限验证
        registration.addUrlPatterns("/storemanagement/return/updateOrderStatus");//退货按钮权限验证
        registration.addUrlPatterns("/storemanagement/delivery/updatenotifyorderstatus");//收货按钮权限验证

        registration.addUrlPatterns("/pie/settle/*");//获取

        registration.addUrlPatterns("/storemanagement/marketresearch/*");
        registration.addUrlPatterns("/api/v1/price/*");

        //智能秤复核相关接口
        registration.addUrlPatterns("/storemanagement/ocms/spu/*");

        registration.setDispatcherTypes(DispatcherType.REQUEST);
        registration.setName("authFilter");

        registration.setOrder(AUTH_FILTER_ORDER);
        return registration;
    }

    //鉴权factorybean 和相关配置
    @Bean
    public AuthFilterFactoryBean authFilterBean(){
        AuthFilterFactoryBean authFilterFactoryBean = new AuthFilterFactoryBean();
        authFilterFactoryBean.setSecret("");//账号系统分配
        // 账号相关的接口不需要做功能鉴权,goods/list,goods/detail目前已经是公共接口,不需要做接口鉴权
        String excludedUriList = "/api/account/**,/storemanagement/goods/list,/storemanagement/goods/detail,/quote/ocms/rejectReasonNo/query";
        authFilterFactoryBean.setExcludedUriList(excludedUriList);//不校验配置urlList（ANT风格） 逗号分隔
        authFilterFactoryBean.setLogEnable(true);//info日志打印
        authFilterFactoryBean.setIncludedUriList("");//校验配置urlList（ANT风格） 逗号分隔, 优先级高于exludedUriList，同时配置则只有include生效
        authFilterFactoryBean.setAuthFailedResponse("{\"code\": 402,\"message\": \"认证不通过\"}");//校验不通过给前端返回信息,

        return authFilterFactoryBean;
    }

}
