package com.sankuai.shangou.qnh.orderapi.domain.request.app;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderMoneyRefundItemModel;
import com.meituan.shangou.saas.o2o.dto.request.BizOrderMoneyRefundRequest;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.MoneyRefundItemVo;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by suxiaoyu on 2023/3/13 10:42
 */
@TypeDoc(
        description = "金额退request"
)
@Data
public class OrderMoneyRefundRequest {

    @FieldDoc(
            description = "渠道id"
    )
    @NotNull(message = "渠道id不能为空")
    private Integer channelId;

    @FieldDoc(
            description = "渠道订单号"
    )
    @NotNull(message = "渠道订单号不能为空")
    private String channelOrderId;

    @FieldDoc(
            description = "退款商品列表"
    )
    @NotNull(message = "退款商品列表不能为空")
    private List<MoneyRefundItemVo> refundItems;

    @FieldDoc(
            description = "退款原因编码"
    )
    private String refundReasonCode;

    @FieldDoc(
            description = "退款原因"
    )
    private String refundReason;

    public BizOrderMoneyRefundRequest convertToBizOrderMoneyRefundRequest() {
        return BizOrderMoneyRefundRequest.builder()
                .channelOrderId(this.channelOrderId)
                .channelId(this.channelId)
                .moneyRefundItemModels(refundItems.stream().map(this::convertToBizOrderMoneyRefundItemModel).collect(Collectors.toList()))
                .refundReason(this.refundReason)
                .refundReasonCode(this.refundReasonCode)
                .build();
    }

    private BizOrderMoneyRefundItemModel convertToBizOrderMoneyRefundItemModel(MoneyRefundItemVo vo) {
        return BizOrderMoneyRefundItemModel.builder()
                .refundMoney(vo.getRefundMoney())
                .applyWeight(vo.getApplyWeight())
                .currentPrice(vo.getCurrentPrice())
                .customerSkuId(vo.getCustomerSkuId())
                .innerSkuId(vo.getInnerSkuId())
                .orderItemId(vo.getOrderItemId())
                .count(vo.getRefundCount())
                .customSpu(vo.getCustomSpu())
                .build();
    }
}
