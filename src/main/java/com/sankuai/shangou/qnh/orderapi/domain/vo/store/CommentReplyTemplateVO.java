package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sgfulfillment.comment.thrift.dto.model.ChannelCommentReplyTemplateDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Optional;

@TypeDoc(
        description = "评价回复模板",
        authors = "hejunliang"
)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CommentReplyTemplateVO {

    @FieldDoc(
            description = "模板id", requiredness = Requiredness.REQUIRED
    )
    private Long templateId;

    @FieldDoc(
            description = "模板内容", requiredness = Requiredness.REQUIRED
    )
    private String templateContent;

    @FieldDoc(
            description = "是否好评自动回复，1是，0否", requiredness = Requiredness.OPTIONAL
    )
    private Integer autoGoodReply;

    public static CommentReplyTemplateVO build(ChannelCommentReplyTemplateDTO commentReplyTemplateDTO) {
        if (commentReplyTemplateDTO == null) {
            return null;
        }
        CommentReplyTemplateVO commentReplyTemplateVO = new CommentReplyTemplateVO();
        commentReplyTemplateVO.setTemplateId(commentReplyTemplateDTO.getTemplateId());
        commentReplyTemplateVO.setTemplateContent(commentReplyTemplateDTO.getTemplateContent());
        commentReplyTemplateVO.setAutoGoodReply(Optional.ofNullable(commentReplyTemplateDTO.getAutoGoodReply()).orElse(0));
        return commentReplyTemplateVO;
    }
}
