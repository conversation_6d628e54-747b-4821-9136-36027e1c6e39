package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/12/5  5:21 下午
 * @since 1.0.0
 */
@TypeDoc(
        description = "根据导出类型查询导出字段信息"
)
@ApiModel("根据导出类型查询导出字段信息")
@Getter
@Setter
@ToString
@NoArgsConstructor
public class CreateExportTaskRequest {
    @FieldDoc(
            description = "任务类型"
    )
    @ApiModelProperty(value = "任务类型", required = true)
    private String taskType;


    @FieldDoc(
            description = "导出查询参数"
    )
    @ApiModelProperty(value = "导出查询参数", required = true)
    private String params;


    @FieldDoc(
            description = "导出字段"
    )
    @ApiModelProperty(value = "导出字段", required = false)
    private List<String> exportFields;
}
