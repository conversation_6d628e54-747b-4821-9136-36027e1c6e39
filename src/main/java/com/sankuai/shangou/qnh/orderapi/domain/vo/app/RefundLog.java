package com.sankuai.shangou.qnh.orderapi.domain.vo.app;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSAfterSaleApplyVO;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.model.OrderStatusLog;
import com.meituan.shangou.saas.order.platform.enums.*;
import com.sankuai.shangou.qnh.orderapi.enums.OcmsRefundAuditType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/***
 * author : <EMAIL>
 * data : 2020/2/10
 * time : 上午11:49
 **/
@TypeDoc(
        description = "退款日志"
)
@ApiModel("退款日志")
@Data
@Slf4j
public class RefundLog {

    @FieldDoc(
            description = "操作人", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "操作人",required = false)
    String operator;

    @FieldDoc(
            description = "操作时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "操作时间",required = true)
    Long optTime;

    @FieldDoc(
            description = "操作内容", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "操作内容",required = true)
    String optContent;

    @FieldDoc(
            description = "退款描述信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "退款描述信息",required = true)
    String optDesc;

    @FieldDoc(
            description = "退款金额（单位分）", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "退款金额（单位分）",required = true)
    Integer refundAmount;

    @FieldDoc(
            description = "售后申请详情商品列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "售后申请商品列表", required = false)
    private List<RefundApplyRecordDetailVO> refundApplyRecordDetailVOList;

    @FieldDoc(
            description = "操作人类型，10：用户，20：商家，30：平台", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "操作人类型，10：用户，20：商家，30：平台",required = true)
    Integer operatorType;


    @FieldDoc(
            description = "退款操作类型，1：申请退款，11：审批同意退款， 13：审批驳回退款， 20：用户取消退款,30：退差价", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "退款操作类型，1：申请退款，11：审批同意退款，13：审批驳回退款， 20：用户取消退款,30：退差价",required = true)
    Integer auditType;

    @FieldDoc(
            description = "售后相关图片"
    )
    @ApiModelProperty(value = "售后相关图片",required = true)
    public List<String> refundPicList;


    public static RefundLog buildRefundLog(OrderStatusLog orderStatusLog, OCMSAfterSaleApplyVO afterSaleApplyVO) {
        RefundLog refundLog = new RefundLog();
        refundLog.setOperator(orderStatusLog.getOperatorName());
        refundLog.setOptDesc(orderStatusLog.getDescription());
        refundLog.setOptTime(orderStatusLog.getCreateTime());
        int targetStatus = orderStatusLog.getTargetStatus();
        String optContent = Optional.ofNullable(OrderStatusEnum.enumOf(targetStatus).getDesc()).orElse(orderStatusLog.getDescription());
        OperatorTypeEnum operatorTypeEnum =  OperatorTypeEnum.enumOf(afterSaleApplyVO.getApplyUserType());
        String refundType = "";
        if (afterSaleApplyVO.getAfsPattern() == AfterSalePatternEnum.ALL.getValue()) {
            refundType = "全单退款";
        } else if (afterSaleApplyVO.getAfsPattern() == AfterSalePatternEnum.AMOUNT.getValue()) {
            refundType = "金额退差";
        } else if (afterSaleApplyVO.getAfsPattern() == AfterSalePatternEnum.PART.getValue()) {
            refundType = "部分退款";
        }
        if (operatorTypeEnum != null){
            if (targetStatus == OrderStatusEnum.REFUND_APPLIED.getValue()){
                //冻结操作
                //判断急速退款和商家退款
                refundLog.setAuditType(OcmsRefundAuditType.Apply.getCode());
                refundLog.setOperatorType(operatorTypeEnum.getValue());
                switch (operatorTypeEnum) {
                    case CUSTOMER:
                        //用户发起退款
                        optContent = "顾客申请" + refundType;
                        break;
                    case MERCHANT:
                    case O2O:
                        optContent = "商家发起" + refundType;
                        break;
                    case CHANNEL_PLATFORM:
                    case SYSTEM:
                        if(afterSaleApplyVO.getOrderBizType() == OrderBizTypeEnum.JING_DONG.getValue()) {
                            // 京东商家端发起部分退款，利用回调消息生成的售后单
                            optContent = "京东商家端部分退款自动通过";
                        } else {
                            optContent = "商家开启极速退款服务，用户申请系统自动通过";
                        }

                        break;
                    default:
                        log.error("未知退款申请人:{}, orderStatusLog:{}", operatorTypeEnum, orderStatusLog);
                }
            }else{
                //首先排除急速退款和商家发起的退款审批
                if (OperatorTypeEnum.CHANNEL_PLATFORM.equals(operatorTypeEnum)
                        && StringUtils.contains(afterSaleApplyVO.getApplyReason(), "急速退款")){
                    log.info("急速退款审批，忽略该状态:{}, afterSaleApply:{}", orderStatusLog, afterSaleApplyVO);
                    return null;
                }else if (OperatorTypeEnum.MERCHANT.equals(operatorTypeEnum) ||
                        OperatorTypeEnum.O2O.equals(operatorTypeEnum)){
                    log.info("商家发起的退款，忽略该状态:{}, afterSaleApply:{}", orderStatusLog, afterSaleApplyVO);
                    return null;
                }

                int auditStatus = afterSaleApplyVO.getStatus();
                if (Objects.equals(AfterSaleApplyStatusEnum.CANCEL.getValue(), auditStatus)) {
                    optContent = "顾客取消退款申请";
                    refundLog.setAuditType(OcmsRefundAuditType.CancelRefund.getCode());
                    refundLog.setOperatorType(OperatorTypeEnum.CUSTOMER.getValue());
                    refundLog.setOptDesc("该订单的退款申请已被顾客主动取消");
                }else if (Objects.equals(AfterSaleApplyStatusEnum.AUDITED.getValue(), auditStatus) ||
                        Objects.equals(AfterSaleApplyStatusEnum.AUTO_AUDITED.getValue(), auditStatus)){
                    if (OperatorTypeEnum.CHANNEL_PLATFORM.getValue() == orderStatusLog.getOpeartorType().intValue()){
                        optContent = "系统自动通过退款申请";
                        refundLog.setAuditType(OcmsRefundAuditType.AgreeRefund.getCode());
                        refundLog.setOperatorType(OperatorTypeEnum.MERCHANT.getValue());
                        refundLog.setOptDesc("超时未处理，系统自动通过退款申请");
                    }else{
                        optContent = "商家同意" + refundType;
                        refundLog.setAuditType(OcmsRefundAuditType.AgreeRefund.getCode());
                        refundLog.setOperatorType(OperatorTypeEnum.CHANNEL_PLATFORM.getValue());
                        refundLog.setOptDesc("该笔钱会自动退回给顾客");
                    }
                }else if (Objects.equals(AfterSaleApplyStatusEnum.AUDITED_REJECT.getValue(), auditStatus)){
                    optContent = "商家拒绝" + refundType;
                    refundLog.setAuditType(OcmsRefundAuditType.DenyRefund.getCode());
                    refundLog.setOperatorType(OperatorTypeEnum.MERCHANT.getValue());
                }

            }
        }
        refundLog.setOptContent(optContent);
        if (afterSaleApplyVO.isPartRefund() && CollectionUtils.isNotEmpty(afterSaleApplyVO.getOcmsAfterSaleApplyDetailVOList())){
            //只有部分退款才展示退款金额和商品详情
            refundLog.setRefundApplyRecordDetailVOList(afterSaleApplyVO.getOcmsAfterSaleApplyDetailVOList().stream()
                    .map(RefundApplyRecordDetailVO::buildRefundApplyRecordDetailVO).collect(Collectors.toList()));
            refundLog.setRefundAmount(
                    CollectionUtils.isNotEmpty(refundLog.getRefundApplyRecordDetailVOList()) ?
                            refundLog.getRefundApplyRecordDetailVOList().stream().
                                    filter(Objects::nonNull)
                                    .mapToInt(RefundApplyRecordDetailVO::getTotalRefundAmt)
                                    .filter(Objects::nonNull).sum() : 0);
        }
        refundLog.setRefundPicList(afterSaleApplyVO.getRefundPicList());
        return refundLog;
    }

    public static String buildRefundContent(OrderStatusLog orderStatusLog, OCMSAfterSaleApplyVO afterSaleApplyVO){
        RefundLog refundLog = buildRefundLog(orderStatusLog, afterSaleApplyVO);
        return refundLog != null ? refundLog.getOptContent() : StringUtils.EMPTY;
    }

}

