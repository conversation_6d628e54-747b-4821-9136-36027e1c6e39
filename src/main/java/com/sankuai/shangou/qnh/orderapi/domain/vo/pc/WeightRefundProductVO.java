package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

/**
 * <AUTHOR>
 * @since 2023/3/2
 **/

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author: <EMAIL>
 * @Date: 2020-03-25 14:40
 * @Description:
 */
@TypeDoc(
        description = "克重退款商品"
)
@ApiModel("克重退款商品")
@Data
public class WeightRefundProductVO {
    @FieldDoc(
            description = "SKU编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "SKU编码")
    private String innerSkuId;

    @FieldDoc(
            description = "线上渠道sku编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "线上渠道sku编码")
    private String customSkuId;

    @FieldDoc(
            description = "线上渠道spu编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "线上渠道spu编码")
    private String customerSpuId;

    @FieldDoc(
            description = "SKU名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "SKU名称")
    private String skuName;

    @FieldDoc(
            description = "实际拣货重量,单位：克", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "实际拣货重量，单位：克")
    @NotNull
    private Double actualWeight;

    @FieldDoc(
            description = "渠道重量,单位：克", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道重量，单位：克")
    @NotNull
    private Double channelWeight;

    @FieldDoc(
            description = "订单内商品行维度的商品标识id", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "订单内商品行维度的商品标识id")
    public String itemId;
}
