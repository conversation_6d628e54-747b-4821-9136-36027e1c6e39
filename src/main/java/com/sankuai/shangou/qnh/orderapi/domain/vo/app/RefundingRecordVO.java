package com.sankuai.shangou.qnh.orderapi.domain.vo.app;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/11/18
 * desc: 退款申请记录
 */
@TypeDoc(
        description = "正在处理的退款申请"
)
@ApiModel("正在处理的退款申请")
@Data
public class RefundingRecordVO {

    @FieldDoc(
            description = "退款申请唯一ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "退款申请唯一ID", required = true)
    private Long serviceId;

    @FieldDoc(
            description = "渠道售后id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道售后id", required = true)
    private String afterSaleId;

    @FieldDoc(
            description = "是否需要审核", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否需要审核", required = true)
    private Integer isAudit;

    @FieldDoc(
            description = "售后状态 1:提交，3：审核中，4：已审核， 5：已申请驳回， 6：自动审核通过， 7：处理中， 9：已完成， 20：已取消", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "1:提交，3：审核中，4：已审核， 5：已申请驳回， 6：自动审核通过， 7：处理中， 9：已完成， 20：已取消", required = true)
    private Integer status;

    @FieldDoc(
            description = "申请原因", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "申请原因", required = true)
    private String applyReason;

    @FieldDoc(
            description = "退款类型, 1-整单退款，2-部分退款", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "退款类型, 1:整单退款，2：部分退款", required = true)
    private Integer afsPattern;

    @FieldDoc(
            description = "退款金额（仅商品行相加）  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "退款金额  单位:分", required = true)
    private Integer refundAmt;

    @FieldDoc(
            description = "退款金额（包含全部）  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "退款金额  单位:分", required = true)
    private Integer refundAmtTotal;

    @FieldDoc(
            description = "创建时间戳", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "创建时间戳", required = true)
    private Long createTime;

    @FieldDoc(
            description = "更新时间戳", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "更新时间戳", required = true)
    private Long updateTime;

    @FieldDoc(
            description = "售后申请类型   0-未知 1-售中申请  2-售后申请", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "售后申请类型   0-未知 1-售中申请  2-售后申请", required = true)
    private Integer afsApplyType;

    @FieldDoc(
            description = "售后申请人   0-未知 1-用户 2-商户 3-渠道", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "售后申请人   0-未知 1-用户 2-商户 3-渠道", required = true)
    private Integer whoApplyType;

    @FieldDoc(
            description = "售后申请详情列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "售后申请详情列表", required = true)
    private List<RefundApplyRecordDetailVO> refundApplyRecordDetailVOList;

    @FieldDoc(
            description = "售后商品总数量", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "售后商品总数量", required = false)
    private Integer refundProductCount;


    @FieldDoc(
            description = "退款操作内容", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "退款操作内容", required = true)
    private String refundOptContent;


    @FieldDoc(
            description = "售后相关图片"
    )
    @ApiModelProperty(value = "售后相关图片")
    public List<String> refundPicList;

    @FieldDoc(
            description = "退款申请类型, 10-仅退款，20-退货退款"
    )
    @ApiModelProperty(value = "退款申请类型")
    public Integer refundApplyType;



    @FieldDoc(
            description = "放心退,预计退货运费"
    )
    @ApiModelProperty(value = "预计退货运费")
    private Integer preReturnFreight;

    @FieldDoc(
            description = "是否可以直接退款", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否可以直接退款")
    private Integer directRefundFlag;

    @FieldDoc(
            description = "超时时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "超时时间", required = false)
    private Long warnDuration;
    @FieldDoc(
            description = "当前时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "当前时间", required = false)
    private Long currentTime;

    @FieldDoc(
            description = "转单处理的门店ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "转单处理的门店ID")
    public Long assignShopId;

    @FieldDoc(
            description = "是否支持返货", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否支持返货", required = false)
    private Boolean isCanReturnGoods;

    @FieldDoc(
            description = "审核截止时间"
    )
    @ApiModelProperty(value = "审核截止时间")
    private Long processDeadline;

    @FieldDoc(
            description = "退货状态"
    )
    @ApiModelProperty(value = "退货状态")
    private Integer returnGoodsStatus;

    @FieldDoc(
            description = "退货地址"
    )
    @ApiModelProperty(value = "退货地址")
    private String pickUpRefundGoodsAddress;

    @FieldDoc(description = "售后换货信息录入是否未录入，ture：未录入，false：已录入")
    @ApiModelProperty(value = "售后换货信息录入是否未录入，ture：未录入，false：已录入")
    private Boolean isNotImport;

    @FieldDoc(
            description = "渠道退单额外类型", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "渠道退单额外类型")
    private Integer channelExtRefundType;

    @FieldDoc(
            description = "退货方式，1:用户自行送回,2:用户呼叫平台骑手送回, 3:商家自行取回"
    )
    @ApiModelProperty(value = "退货方式，1:用户自行送回,2:用户呼叫平台骑手送回, 3:商家自行取回", required = false)
    private Integer refundGoodWay;

    @FieldDoc(
            description = "退货运费承担方", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "退货运费承担方")
    private Integer refundGoodFreightType;
}

