package com.sankuai.shangou.qnh.orderapi.service.pc;

import com.sankuai.shangou.qnh.orderapi.constant.pc.Constants;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * @Author: <EMAIL>
 * @Date: 2019/3/25 09:31
 * @Description:
 */
@Service
public class ThriftService implements ApplicationContextAware {

    private ApplicationContext applicationContext;


    private final ConcurrentHashMap<Class, Object> beanCache = new ConcurrentHashMap<>(16);

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }


    /**
     * 统一的thrift调用方法
     * @param request controller层的请求对象
     * @param reqConvertFunc 将controller层的请求对象转化为thrift接口的请求对象
     * @param resConvertFunc 将thrift服务返回的响应转化为VO对象
     * @param invokeType 要调用thrift服务的类型
     * @param invokeFunc 调用的方法
     * @param checker 检查结果是否正常的方法
     * @param <RES> thrift方法的响应类型
     * @param <REQ> thrift方法的请求类型
     * @param <VO> VO类型
     * @param <T> thrift类型
     * @param <OR> controller请求类型
     * @param mockFunc mock服务
     * @return
     */
    public <RES,REQ,VO,T,OR extends BaseRequest> VO invoke(OR request
            , Function<OR, REQ> reqConvertFunc
            , Function<RES,VO> resConvertFunc
            , Class<T> invokeType
            , BiFunction<T,REQ,RES> invokeFunc
            , Consumer<RES> checker
            , Function<REQ,RES> mockFunc) {
        AssertUtil.notNull(invokeFunc,"invokeFunc不能为空");
        AssertUtil.notNull(resConvertFunc,"resConvertFunc不能为空");

        REQ req = ConverterUtils.nonNullConvert(request, reqConvertFunc);

        //mock服务
        if (mockFunc != null && StringUtils.equalsIgnoreCase(System.getProperty(Constants.Env.SPRING_PROFILE_ACTIVE), Constants.Env.DEV)) {
            RES res = mockFunc.apply(req);
            return ConverterUtils.nonNullConvert(res,resConvertFunc);
        }

        T thriftBean = null;

        if (beanCache.containsKey(invokeType)) {
            thriftBean = (T)beanCache.get(invokeType);
        } else {
            thriftBean = applicationContext.getBean(invokeType);
            beanCache.putIfAbsent(invokeType, thriftBean);
        }

        if (thriftBean == null) {
            throw new RuntimeException("未找到类型为(" + invokeType +")的bean");
        }

        RES thriftRes = invokeFunc.apply(thriftBean, req);
        if (checker != null) {
            checker.accept(thriftRes);
        }
        return ConverterUtils.nonNullConvert(thriftRes,resConvertFunc);
    }



    /**
     * 统一的thrift调用方法
     * @param request controller层的请求对象
     * @param reqConvertFunc 将controller层的请求对象转化为thrift接口的请求对象
     * @param resConvertFunc 将thrift服务返回的响应转化为VO对象
     * @param invokeType 要调用thrift服务的类型
     * @param invokeFunc 调用的方法
     * @param checker 检查结果是否正常的方法
     * @param <RES> thrift方法的响应类型
     * @param <REQ> thrift方法的请求类型
     * @param <VO> VO类型
     * @param <T> thrift类型
     * @param <OR> controller请求类型
     * @return
     */
    public <RES,REQ,VO,T,OR extends BaseRequest> VO invoke(OR request
            , Function<OR, REQ> reqConvertFunc
            , Function<RES,VO> resConvertFunc
            , Class<T> invokeType
            , BiFunction<T,REQ,RES> invokeFunc
            , Consumer<RES> checker) {
        return invoke(request, reqConvertFunc, resConvertFunc, invokeType, invokeFunc, checker, null);
    }
}
