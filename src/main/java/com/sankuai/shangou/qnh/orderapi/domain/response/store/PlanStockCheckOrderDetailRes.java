package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.SubStockCheckOrderBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "盘点单明细信息",
        authors = {
                "qianteng"
        }
)
@Data
@ApiModel("盘点单明细信息")
public class PlanStockCheckOrderDetailRes {
    @FieldDoc(
            description = "盘点计划单号"
    )
    public String stockCheckOrderPlanId; // required
    @FieldDoc(
            description = "盘点类型：1-全盘；2-品类盘；3-随机盘；4-指定sku"
    )
    public int planType; // required
    @FieldDoc(
            description = "盘点商品品类"
    )
    public List<String> categoryIds; // required
    @FieldDoc(
            description = "备注"
    )
    public String comment; // required
    @FieldDoc(
            description = "计划盘点日期"
    )
    public String planExecuteTime; // required
    @FieldDoc(
            description = "盘点单状态：-1-无效；1-待提交；2-待审核；3-审核通过；4-驳回"
    )
    public int status; // required
    @FieldDoc(
            description = "已完成商品种类"
    )
    public int doneSkuCount; // required
    @FieldDoc(
            description = "总待盘点商品种类"
    )
    public int skuCount; // required
    @FieldDoc(
            description = "子盘点单基本信息列表"
    )
    @ApiModelProperty(name = "子盘点单基本信息列表")
    public List<SubStockCheckOrderBase> subStockCheckOrderList; // required
}
