package com.sankuai.shangou.qnh.orderapi.domain.result.pc;

import com.alibaba.fastjson.JSON;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * ClassName: Result
 * Description: 统一API响应结果封装
 * date 25/06/2018 7:42 PM
 **/

@TypeDoc(
        description = "统一API响应结果封装",
        authors = {
                "RAUL.CHENG"
        },
        version = "V1.0"
)
@ApiModel("统一API响应结果封装")
@NoArgsConstructor
public class Result<T> {

    @FieldDoc(
            description = "返回码"
    )
    @ApiModelProperty(value = "返回码", required = true)
    private int code;
    @FieldDoc(
            description = "描述"
    )
    @ApiModelProperty(value = "描述", required = true)
    private String msg;

    @FieldDoc(
            description = "返回数据"
    )
    private T data;


    public Result(BaseResult baseResult, T data) {
        this.code = baseResult.getCode();
        this.msg = baseResult.getMsg();
        this.data = data;
    }

    public Result(BaseResult baseResult) {
        this.code = baseResult.getCode();
        this.msg = baseResult.getMsg();
    }

    public Result(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Result(int code, T data) {
        this.code = code;
        this.data = data;
    }

    public Result(int code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }


    public Result code(BaseResult baseResult) {
        this.code = baseResult.getCode();
        return this;
    }

    public static Result<Void> fail(BaseResult result, Throwable e) {
        Objects.requireNonNull(result, "result must not be null");
        return new Result<>(result.getCode(), e.getMessage());
    }

    public static Result fail(int code, String msg) {
        return new Result<>(code, msg);
    }

    public int getCode() {
        return code;
    }

    public Result setCode(int code) {
        this.code = code;
        return this;
    }

    public String getMsg() {
        return msg;
    }

    public Result setMsg(String msg) {
        this.msg = msg;
        return this;
    }


    public T getData() {
        return data;
    }

    public Result setData(T data) {
        this.data = data;
        return this;
    }

    public static <T> Result<T> success(T data) {
        Result<T> result = new Result<T>();
        result.setData(data);
        result.setCode(0);
        result.setMsg("success");
        return result;
    }

    public boolean isSuccess() {
        return this.code == BaseResult.SUCCESS.getCode();
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
