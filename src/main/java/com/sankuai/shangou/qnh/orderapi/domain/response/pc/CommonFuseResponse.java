package com.sankuai.shangou.qnh.orderapi.domain.response.pc;

import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.meituan.reco.pickselect.common.utils.ParamCheckUtils;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang.StringUtils;

import java.text.MessageFormat;

/**
 * Created by fanxiaolin on 2022/12/6.
 */
@TypeDoc(
        description = "通用返回结果"
)
@ApiModel("通用返回结构")
public class CommonFuseResponse<T> {
    @FieldDoc(
            description = "错误码"
    )
    @ApiModelProperty(value = "错误码", required = true)
    private int code;

    @FieldDoc(
            description = "错误消息"
    )
    @ApiModelProperty(value = "错误消息")
    private String message;

    @FieldDoc(
            description = "返回内容"
    )
    private T data;

    public CommonFuseResponse(int code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public CommonFuseResponse() {
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public static <U> CommonFuseResponse<U> success(U val) {
        return new CommonFuseResponse<>(ResultCode.SUCCESS.getCode(), "", val);
    }

    public static <U> CommonFuseResponse<U> fail(int resultCode, String msg) {
        return new CommonFuseResponse<>(resultCode, msg, null);
    }

    public static <U> CommonFuseResponse<U> fail(int resultCode, String msg, U val) {
        return new CommonFuseResponse<>(resultCode, msg, val);
    }

    public static CommonFuseResponse<Void> fail(ResultCode resultCode) {
        return fail(resultCode, (String)null);
    }

    public static <T> CommonFuseResponse<T> fail2(ResultCode resultCode) {
        return fail(resultCode.code, resultCode.getErrorMessage());
    }

    public static CommonFuseResponse<Void> fail(ResultCode resultCode, String msg) {
        ParamCheckUtils.nullCheck(resultCode, "resultCode cannot be null");

        String errorMsg = msg;
        if(StringUtils.isEmpty(errorMsg)) {
            errorMsg = resultCode.getErrorMessage();
        }
        return new CommonFuseResponse<>(resultCode.getCode(), errorMsg, null);
    }

    public static CommonFuseResponse<Void> fail(ResultCode resultCode, Object[] params) {
        ParamCheckUtils.nullCheck(resultCode, "resultCode cannot be null");

        return new CommonFuseResponse<>(resultCode.getCode(), MessageFormat.format(resultCode.getErrorMessage(), params), null);
    }

    public boolean isSuccess() {
        return this.code == ResultCode.SUCCESS.getCode();
    }
}

