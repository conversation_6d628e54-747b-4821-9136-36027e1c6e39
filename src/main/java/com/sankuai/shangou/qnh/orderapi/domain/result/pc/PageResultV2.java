package com.sankuai.shangou.qnh.orderapi.domain.result.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.supplychain.purchase.client.thrift.common.PageInfoResp;
import com.sankuai.meituan.shangou.empower.ocms.client.common.dto.PageInfoDTO;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * @Author: <EMAIL>
 * @Date: 2019/3/30 15:00
 * @Description:
 */
@TypeDoc(
        description = "列表查询响应结果封装V2",
        authors = {
                "RAUL.CHENG"
        },
        version = "V1.0"
)
@ApiModel("列表查询响应结果封装V2")
@Setter
@Getter
@NoArgsConstructor
@Builder
public class PageResultV2<T> {


    @FieldDoc(
            description = "列表项"
    )
    @ApiModelProperty(value = "列表项", required = true)
    protected transient List<T> list;

    @FieldDoc(
            description = "当前结果所在页，不一定和请求页相等"
    )
    @ApiModelProperty(value = "当前结果所在页，不一定和请求页相等", required = true)
    protected int page;

    @FieldDoc(
            description = "每页大小"
    )
    @ApiModelProperty(value = "每页大小", required = true)
    protected int pageSize;

    @FieldDoc(
            description = "总结果数量"
    )
    @ApiModelProperty(value = "总结果数量", required = true)
    protected int total;

    @FieldDoc(
            description = "总页数"
    )
    @ApiModelProperty(value = "总页数", required = true)
    protected int totalPage;

    @FieldDoc(
            description = "maxExportSize"
    )
    @ApiModelProperty(value = "maxExportSize", required = false)
    protected int maxExportSize;

    public PageResultV2(List<T> list, int page, int pageSize, int total) {
        this.list = list;
        this.page = page;
        this.pageSize = pageSize;
        this.total = total;
    }

    public PageResultV2(List<T> list, int page, int pageSize, int total, int totalPage) {
        this.list = list;
        this.page = page;
        this.pageSize = pageSize;
        this.total = total;
        this.totalPage = totalPage;
    }

    public PageResultV2(List<T> list, PageResultV2 source) {
        this.list = list;
        this.page = Optional.ofNullable(source).map(PageResultV2::getPage).orElse(1);
        this.pageSize = Optional.ofNullable(source).map(PageResultV2::getPageSize).orElse(1);
        this.total = Optional.ofNullable(source).map(PageResultV2::getTotal).orElse(0);
        this.totalPage = Optional.ofNullable(source).map(PageResultV2::getTotalPage).orElse(0);
    }

    public PageResultV2(List<T> list, int page, int pageSize, int total, int totalPage, int maxExportSize) {
        this.list = list;
        this.page = page;
        this.pageSize = pageSize;
        this.total = total;
        this.totalPage = totalPage;
        this.maxExportSize = maxExportSize;
    }

    @Override
    public String toString() {
        return "PageResult{" +
                "list=" + list +
                ", page=" + page +
                ", pageSize=" + pageSize +
                ", total=" + total +
                ", totalPage=" + totalPage +
                '}';
    }

    public void initPageInfo(PageInfoDTO pageInfoDTO) {
        if (pageInfoDTO == null) {
            return;
        }
        this.page = pageInfoDTO.getPage();
        this.pageSize = pageInfoDTO.getSize();
        this.total = pageInfoDTO.getTotal();
        this.totalPage = pageInfoDTO.getTotalPage();
    }

    public void initPageInfo(PageInfoResp page) {
        if (page == null) {
            return;
        }
        this.page = page.getPageNo();
        this.pageSize = page.getPageSize();
        this.total = Math.toIntExact(page.getTotalCount());
        this.totalPage = Math.toIntExact(page.getTotalPage());
    }

    public void init(com.sankuai.meituan.shangou.empower.productbiz.client.dto.PageInfoDTO pageInfoDTO) {
        if (pageInfoDTO == null) {
            return;
        }
        this.page = pageInfoDTO.getPage();
        this.pageSize = pageInfoDTO.getSize();
        this.total = pageInfoDTO.getTotal().intValue();
        this.totalPage = pageInfoDTO.getTotalPage();
    }

    public static <T> PageResultV2<T> emptyResult(PageRequest pageRequest) {
        PageResultV2<T> result = new PageResultV2<>();
        result.setPage(pageRequest.getPage());
        result.setPageSize(pageRequest.getPageSize());
        result.setList(Collections.emptyList());
        result.setTotalPage(0);
        result.setTotal(0);
        return result;
    }

    public static int calculateTotalPage(int total, int pageSize) {
        if (pageSize <= 0) {
            return 0;
        }
        return total % pageSize == 0 ? total / pageSize : total / pageSize + 1;
    }

    public static <T> PageResultV2<T> emptyResult(Integer page,Integer pageSize) {
        PageResultV2<T> result = new PageResultV2<>();
        result.setPage(page);
        result.setPageSize(pageSize);
        result.setList(Collections.emptyList());
        result.setTotalPage(0);
        result.setTotal(0);
        return result;
    }
    
    public static <T> PageResultV2<T> success(List<T> list, Integer pageNo, Integer pageSize, Long total, Long totalPage) {
        PageResultV2<T> result = new PageResultV2<>();
        result.setList(list);
        result.setPage(pageNo);
        result.setPageSize(Optional.ofNullable(pageSize).orElse(0));
        result.setTotal(Optional.ofNullable(total).map(Long::intValue).orElse(0));
        result.setTotalPage(Optional.ofNullable(totalPage).map(Long::intValue).orElse(calculateTotalPage(result.getTotal(), result.getPageSize())));
        return result;
    }
}
