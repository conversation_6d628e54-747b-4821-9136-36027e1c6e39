package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.tenant.thrift.dto.config.request.ConfigBatchQueryRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.config.request.MultiTenantBatchQueryConfigRequest;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ListConvertUtil;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/30
 */
@Data
@ToString
@TypeDoc(
        description = "批量查询租户配置"
)
public class BatchQueryTenantConfigRequest {

    @FieldDoc(description = "配置主体 ID 列表")
    private List<Long> subjectIds;

    @FieldDoc(description = "配置项 ID 列表")
    private List<Integer> configIds;


    public void validate() {
        if (CollectionUtils.isEmpty(subjectIds)) {
            throw new IllegalArgumentException("配置主体 ID 列表不能为空");
        }
        if (CollectionUtils.isEmpty(configIds)) {
            throw new IllegalArgumentException("配置项 ID 列表不能为空");
        }
    }

    public boolean emptyRequest() {
        return ListConvertUtil.convertToNullSafeList(subjectIds).isEmpty()
                || ListConvertUtil.convertToNullSafeList(configIds).isEmpty();
    }

    public MultiTenantBatchQueryConfigRequest toMultiRequest(Long tenantId) {
        MultiTenantBatchQueryConfigRequest request = new MultiTenantBatchQueryConfigRequest();
        ConfigBatchQueryRequest configBatchQueryRequest = new ConfigBatchQueryRequest();
        configBatchQueryRequest.setTenantId(tenantId);
        configBatchQueryRequest.setSubjectIdList(this.subjectIds);
        configBatchQueryRequest.setConfigIdList(this.configIds);
        request.setQueryList(Collections.singletonList(configBatchQueryRequest));
        return request;
    }

}
