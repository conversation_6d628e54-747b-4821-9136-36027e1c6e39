package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.TenantSku;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/6/12
 * desc:
 */
@TypeDoc(
        description = "查询租户商品响应"
)
@Data
@ApiModel("查询租户商品响应")
public class QueryTenantSkuResponse {

    @FieldDoc(
            description = "租户商品信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "租户商品信息")
    private TenantSku tenantSku;
}
