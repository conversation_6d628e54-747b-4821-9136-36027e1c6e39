package com.sankuai.shangou.qnh.orderapi.remote;

import com.meituan.shangou.saas.dto.StatusCodeEnum;
import com.meituan.shangou.saas.order.platform.client.dto.model.OrderLabelModel;
import com.meituan.shangou.saas.order.platform.client.dto.model.condition.OrderLabelQueryCondition;
import com.meituan.shangou.saas.order.platform.client.dto.request.OrderLabelQueryRequest;
import com.meituan.shangou.saas.order.platform.client.dto.response.OrderLabelQueryResponse;
import com.meituan.shangou.saas.order.platform.client.service.OrderLabelThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class OrderLabelRemoteService {

    @Resource
    private OrderLabelThriftService orderLabelThriftService;

    public List<OrderLabelModel> queryTenantLabel(Long tenantId, List<Integer> sourceList, List<Integer> typeList, boolean containDeleteData) {
        if (tenantId == null) {
            return new ArrayList<>();
        }
        OrderLabelQueryRequest request = new OrderLabelQueryRequest();
        OrderLabelQueryCondition condition = OrderLabelQueryCondition.builder()
                .tenantId(tenantId)
                .sourceList(sourceList)
                .typeList(typeList)
                .containDeleteData(containDeleteData)
                .build();
        request.setCondition(condition);
        try {
            log.info("OrderLabelRemoteService queryTenantLabel request:{}", request);
            OrderLabelQueryResponse response = orderLabelThriftService.query(request);
            if (response.getStatus().getCode() == StatusCodeEnum.SUCCESS.getCode()) {
                return response.getLabelList();
            }
        } catch (TException e) {
           log.error("OrderLabelRemoteService queryTenantLabel 查询标签失败 tenantId:{}", tenantId);
        }
        return new ArrayList<>();
    }
}
