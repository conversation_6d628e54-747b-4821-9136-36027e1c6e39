package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/28
 * @email jianglilin02@meituan
 */
@TypeDoc(
        description = "层级信息",
        authors = "jianglilin02"
)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommentStatByConditionVO {

    @FieldDoc(
            description = "层级数，从0开始", requiredness = Requiredness.OPTIONAL
    )
    private int level;

    @FieldDoc(
            description = "层级名称，如commentContentType。与请求保持一致", requiredness = Requiredness.OPTIONAL
    )
    private String levelName;

    @FieldDoc(
            description = "层级统计列表", requiredness = Requiredness.OPTIONAL
    )
    private List<CommentLevelStatVO> levelStatList;


}
