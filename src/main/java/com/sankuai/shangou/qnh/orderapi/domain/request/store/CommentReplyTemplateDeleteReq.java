package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.exception.store.CommonRuntimeException;
import lombok.Data;

import javax.validation.constraints.NotNull;

@TypeDoc(
        description = "删除评价回复模板请求参数",
        authors = "hejunliang"
)
@Data
public class CommentReplyTemplateDeleteReq {

    @FieldDoc(
            description = "模板id", requiredness = Requiredness.REQUIRED
    )
    @NotNull(message = "模板id不能为空")
    private Long templateId;

    public void validate() {
        if (this.templateId == null) {
            throw new CommonRuntimeException("模板id不能为空", ResultCode.CHECK_PARAM_ERR);
        }
    }
}
