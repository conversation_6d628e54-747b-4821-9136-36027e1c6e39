package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.OrderItemForBoothVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/5/5
 * desc: 摊位订单
 */
@TypeDoc(
        description = "摊位订单信息"
)
@Data
@ApiModel("摊位订单信息")
public class OrderForBoothVO {

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道订单号", required = true)
    private String channelOrderId ;

    @FieldDoc(
            description = "渠道类型 100-美团 200-饿了么 300-京东到家", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道类型 100-美团 200-饿了么 300-京东到家", required = true)
    private Integer channelId;

    @FieldDoc(
            description = "金额 单位:元", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "金额 单位:元", required = true)
    private Double amount;

    @FieldDoc(
            description = "商品列表", requiredness = Requiredness.OPTIONAL_IN_REQUIRED_OUT
    )
    @ApiModelProperty(name = "商品列表")
    private List<OrderItemForBoothVO> orderItems;
}
