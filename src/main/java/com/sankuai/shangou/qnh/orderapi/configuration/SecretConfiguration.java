package com.sankuai.shangou.qnh.orderapi.configuration;


import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
public class SecretConfiguration {

    @Getter
    private static String erpSkt;

    @Autowired
    private String erpSktInstance;

    @PostConstruct
    public void init() {
        erpSkt = erpSktInstance;
    }

}
