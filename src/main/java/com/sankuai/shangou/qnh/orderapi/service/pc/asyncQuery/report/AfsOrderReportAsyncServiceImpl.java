package com.sankuai.shangou.qnh.orderapi.service.pc.asyncQuery.report;

import com.meituan.shangou.saas.order.platform.client.dto.model.AfsOrderCompleteDetail;
import com.meituan.shangou.saas.order.platform.client.dto.model.OrderReturnReport;
import com.meituan.shangou.saas.order.platform.client.dto.request.OrderReportRequest;
import com.meituan.shangou.saas.order.platform.client.dto.response.OrderReturnReportResponse;
import com.meituan.shangou.saas.order.platform.client.enums.StatusCodeEnum;
import com.meituan.shangou.saas.order.platform.client.service.report.OrderFulfillmentReportThriftService;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.OrderFulfillmentReportReq;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.OrderFulfillmentReportQueryResp;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.OrderFulfillmentReportVO;
import com.sankuai.shangou.qnh.orderapi.enums.pc.RefundTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AfsOrderReportAsyncServiceImpl implements OrderReportAsyncService {

    @Resource
    private OrderFulfillmentReportThriftService orderFulfillmentReportThriftService;

    @Override
    public OrderFulfillmentReportQueryResp asyncQueryReport(OrderFulfillmentReportReq request, List<Long> poiIds, List<Long> warehouseIds) {
        OrderFulfillmentReportQueryResp resp = new OrderFulfillmentReportQueryResp();
        log.info("AfsOrderReportAsyncServiceImpl asyncQueryReport request:{}, poiIds:{}, warehouseIds:{}", request, poiIds, warehouseIds);
        OrderReportRequest orderReportRequest = new OrderReportRequest();
        orderReportRequest.setTenantId(request.getTenantId());
        orderReportRequest.setShopIdList(poiIds);
        orderReportRequest.setWarehouseIdList(warehouseIds);
        orderReportRequest.setFilterType(request.getFilterType());
        try {
            OrderReturnReportResponse orderReportResponse = orderFulfillmentReportThriftService.queryReturnOrderReport(orderReportRequest);
            if (orderReportResponse.getStatus().getCode() != StatusCodeEnum.SUCCESS.getValue()) {
                log.error("AfsOrderReportAsyncServiceImpl asyncQueryReport 查询失败：param {}，result:{}", orderReportRequest, orderReportResponse);
                return resp;
            }
            if (CollectionUtils.isEmpty(orderReportResponse.getOrderReturnReportList())) {
                resp.setOrderFulfillmentDetailReportList(Lists.newArrayList());
                return resp;
            }
            List<OrderFulfillmentReportVO> reportVOList = orderReportResponse.getOrderReturnReportList().stream().map(orderReport -> toBuild(orderReport)).collect(Collectors.toList());
            resp.setOrderFulfillmentDetailReportList(reportVOList);
        } catch (TException e) {
            log.error("AfsOrderReportAsyncServiceImpl asyncQueryReport error", e);
            throw new RuntimeException(e);
        }

        return resp;

    }

    private OrderFulfillmentReportVO toBuild(OrderReturnReport orderReport) {
        OrderFulfillmentReportVO reportVO = new OrderFulfillmentReportVO();
        reportVO.setShopId(orderReport.getShopId());
        reportVO.setShopName(orderReport.getShopName());
        reportVO.setWarehouseId(orderReport.getWarehouseId());
        reportVO.setWarehouseName(orderReport.getWarehouseName());
        reportVO.setAfsOrderDealingCount(orderReport.getDealingAfsOrderCount());
        reportVO.setAfsOrderCompleteCount(orderReport.getAfsOrderCompleteCount());
        reportVO.setOutShopId(orderReport.getOutShopId());
        if (CollectionUtils.isNotEmpty(orderReport.getAfsOrderCompleteDetailList())) {
            List<com.sankuai.shangou.qnh.orderapi.domain.vo.pc.AfsOrderCompleteDetail> list = new ArrayList<>();
            for (AfsOrderCompleteDetail afsOrderCompleteDetail : orderReport.getAfsOrderCompleteDetailList()) {

                com.sankuai.shangou.qnh.orderapi.domain.vo.pc.AfsOrderCompleteDetail buildDetail = toBuildDetail(afsOrderCompleteDetail);
                list.add(buildDetail);
                if(RefundTypeEnum.getGoodsRefundList().contains(Integer.parseInt(afsOrderCompleteDetail.getAfsOrderType()))){
                    reportVO.setRefundGoodCount(reportVO.getRefundGoodCount() + afsOrderCompleteDetail.getAfsOrderCount());
                }else if(RefundTypeEnum.getAmountRefundList().contains(Integer.parseInt(afsOrderCompleteDetail.getAfsOrderType()))){
                    reportVO.setRefundMoneyCount(reportVO.getRefundMoneyCount() + afsOrderCompleteDetail.getAfsOrderCount());
                }
            }
            reportVO.setAfsOrderCompleteDetailList(list);
        }

        return reportVO;
    }

    private com.sankuai.shangou.qnh.orderapi.domain.vo.pc.AfsOrderCompleteDetail toBuildDetail(AfsOrderCompleteDetail afsOrderCompleteDetail) {
        com.sankuai.shangou.qnh.orderapi.domain.vo.pc.AfsOrderCompleteDetail detail = new com.sankuai.shangou.qnh.orderapi.domain.vo.pc.AfsOrderCompleteDetail();
        detail.setAfsOrderCount(afsOrderCompleteDetail.getAfsOrderCount());
        detail.setAfsOrderType(afsOrderCompleteDetail.getAfsOrderType());
        RefundTypeEnum refundTypeEnum = RefundTypeEnum.enumOf(Integer.parseInt(afsOrderCompleteDetail.getAfsOrderType()));
        if (refundTypeEnum != null) {
            detail.setAfsOrderTypeDesc(refundTypeEnum.getDesc());
        }
        return detail;
    }
}
