package com.sankuai.shangou.qnh.orderapi.domain.response.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.StoreDeliveryConfigVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 查询门店/仓库的聚合配送设置返回体
 * @Author: zhangjian155
 * @Date: 2022/10/11 15:20
 */
@TypeDoc(
        description = "查询门店/仓库的聚合配送设置返回体"
)
@ApiModel("查询门店/仓库的聚合配送设置返回体")
@Data
public class QueryStoreDeliveryConfigResponse {

    @FieldDoc(
            description = "门店聚合配送配置"
    )
    @ApiModelProperty(value = "门店聚合配送配置")
    private StoreDeliveryConfigVO storeAggDeliveryConfig;
}
