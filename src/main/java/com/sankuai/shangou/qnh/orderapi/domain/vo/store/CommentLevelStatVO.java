package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/12/28
 * @email jianglilin02@meituan
 */
@TypeDoc(
        description = "层级统计信息",
        authors = "jianglilin02"
)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommentLevelStatVO {

    @FieldDoc(
            description = "标签,如EXISTS，与请求保持一致", requiredness = Requiredness.OPTIONAL
    )
    private String label;

    @FieldDoc(
            description = "数量", requiredness = Requiredness.OPTIONAL
    )
    private int count;

}
