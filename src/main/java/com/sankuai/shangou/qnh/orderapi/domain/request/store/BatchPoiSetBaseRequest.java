package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2019/12/23 16:11
 * @Description:
 */
@TypeDoc(
        description = "门店管理批量设置，基础请求对象"
)
@ApiModel(value = "门店管理批量设置，基础请求对象")
@Data
public class BatchPoiSetBaseRequest {

    @FieldDoc(
            description = "全选模式"
    )
    @ApiModelProperty(value = "全选模式")
    protected Boolean allSelected = false;

    @FieldDoc(
            description = "全选模式下，被取消勾选的门店列表"
    )
    @ApiModelProperty(value = "全选模式下，被取消勾选的门店列表")
    protected List<String> excludedPoiIds;

    @FieldDoc(
            description = "全选模式下的筛选条件——城市编码"
    )
    @ApiModelProperty(value = "全选模式下的筛选条件——城市编码")
    protected String cityId;

    @FieldDoc(
            description = "全选模式下的筛选条件——渠道Id列表"
    )
    @ApiModelProperty(value = "全选模式下的筛选条件——渠道Id列表")
    protected List<String> channelList;

    @FieldDoc(
            description = "全选模式下的筛选条件——营业状态列表"
    )
    @ApiModelProperty(value = "全选模式下的筛选条件——营业状态列表")
    protected List<Integer> statusList;
}
