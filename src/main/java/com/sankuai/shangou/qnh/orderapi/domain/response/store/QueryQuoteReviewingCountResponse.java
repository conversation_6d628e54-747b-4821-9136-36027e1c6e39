package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TypeDoc(
        description = "待审核提价记录数查询结果响应"
)
@Data
@ApiModel("待审核提价记录数查询结果响应")
public class QueryQuoteReviewingCountResponse {

    @FieldDoc(
            description = "待审核的提价记录数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "待审核的提价记录数", required = true)
    private int count;
}
