package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/6/20 14:23
 */
@TypeDoc(
        name = "门店基本信息(包含层级关系)",
        description = "门店基本信息(包含层级关系)"
)
@Setter
@Getter
@ToString
@EqualsAndHashCode
public class PoiInfoWithHierarchyVO {
    @FieldDoc(description = "门店id")
    @ApiModelProperty(value = "门店id", required = true)
    private Long poiId;

    @FieldDoc(description = "门店名称")
    @ApiModelProperty(value = "门店名称", required = true)
    private String poiName;

    @FieldDoc(description = "门店地址")
    @ApiModelProperty(value = "门店地址", required = true)
    private String address;

    @FieldDoc(description = "门店状态")
    @ApiModelProperty(value = "门店状态", required = true)
    private String status;

    @FieldDoc(description = "门店状态编码")
    @ApiModelProperty(value = "门店状态编码")
    private String statusCode;

    @FieldDoc(description = "城市名称")
    private String cityName;

    @FieldDoc(description = "城市编号")
    private String areaCode;

    @FieldDoc(description = "外部门店编号")
    @ApiModelProperty(value = "外部门店编号", required = true)
    private String outPoiId;

    @FieldDoc(description = "门店类型")
    private Integer entityType;

    @FieldDoc(description = "配送方式")
    private Integer shippingMode;

    @FieldDoc(description = "子门店信息")
    private List<PoiInfoVO> children;

    @FieldDoc(description = "是否是门店整体")
    private Boolean global;
}
