package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.store.management.thrift.scmdelivery.dto.TSaleReturnOrderInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/7/10
 * desc: 售后信息详情
 */
@TypeDoc(
        description = "售后信息详情"
)
@ApiModel("售后信息详情")
@Data
public class AfterSaleRecordVO {

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID", required = true)
    private Long storeId;

    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户ID", required = true)
    private Long tenantId;

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID", required = true)
    private Integer channelId;

    @FieldDoc(
            description = "售后服务唯一ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "售后服务唯一ID", required = true)
    private Long serviceId;

    @FieldDoc(
            description = "渠道收货记录id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道收货记录id", required = true)
    private String channelAfterSaleId;

    @FieldDoc(
            description = "是否需要审核", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "是否需要审核", required = true)
    private Integer isAudit;

    @FieldDoc(
            description = "售后状态 1:提交，3：审核中，4：已审核， 5：已申请驳回， 6：自动审核通过， 7：处理中， 9：已完成， 20：已取消", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "1:提交，3：审核中，4：已审核， 5：已申请驳回， 6：自动审核通过， 7：处理中， 9：已完成， 20：已取消", required = true)
    private Integer status;

    @FieldDoc(
            description = "申请原因", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "申请原因", required = true)
    private String applyReason;

    @FieldDoc(
            description = "售后模式, 1-整单退款，2-部分退款，3克重退款", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "售后模式, 1:整单退款，2：部分退款，3克重退款", required = true)
    private Integer afsPattern;

    @FieldDoc(
            description = "退款金额  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "退款金额  单位:分", required = true)
    private Integer refundAmt;

    @FieldDoc(
            description = "创建时间戳", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "创建时间戳", required = true)
    private Long createTime;

    @FieldDoc(
            description = "更新时间戳", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "更新时间戳", required = true)
    private Long updateTime;

    @FieldDoc(
            description = "售后申请类型   0-未知 1-售中申请  2-售后申请", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "售后申请类型   0-未知 1-售中申请  2-售后申请", required = true)
    private Integer afsApplyType;

    @FieldDoc(
            description = "售后申请人   0-未知 1-用户 2-商户 3-渠道", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "售后申请人   0-未知 1-用户 2-商户 3-渠道", required = true)
    private Integer whoApplyType;

    @FieldDoc(
            description = "售后申请详情列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "售后申请详情列表", required = true)
    private List<AfterSaleRecordDetailVO> afterSaleRecordDetailList;


    @FieldDoc(
            description = "售后相关图片", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "售后相关图片")
    private List<String> refundPicList;

    @FieldDoc(
            description = "销退单单号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "销退单单号")
    private String saleReturnOrderNo;

    @FieldDoc(
            description = "销退单状态", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "销退单状态")
    private Integer saleReturnOrderStatus;

    @FieldDoc(
            description = "处理的转单门店ID"
    )
    @ApiModelProperty(name = "处理的转单门店ID")
    private Long dispatchShopId;
    @FieldDoc(
            description = "分配时间"
    )
    @ApiModelProperty(name = "分配时间")
    private Long dealTime;

    @FieldDoc(
            description = "地址变更退费", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "地址变更退费")
    private String addressChangeFee;

    @FieldDoc(
            description = "地址变更退费注释", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "地址变更退费注释")
    private String addressChangeFeeNotes;

    @FieldDoc(
            description = "退货状态", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "退货地址")
    private Integer returnGoodsStatus;

    @FieldDoc(
            description = "退货地址", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "退货地址")
    private String pickUpRefundGoodsAddress;

    @FieldDoc(
            description = "退款类型 10-仅退款，40-退货退款，50-用户拒收，101-用户申诉", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "退款类型")
    private Integer refundApplyType;

    @FieldDoc(
            description = "售后换货信息录入是否未录入，ture：未录入，false：已录入", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "售后换货信息录入是否未录入，ture：未录入，false：已录入")
    private Boolean isNotImport;

    @FieldDoc(
            description = "渠道退单额外类型", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "渠道退单额外类型")
    private Integer channelExtRefundType;

    @FieldDoc(
            description = "退货方式，1:用户自行送回,2:用户呼叫平台骑手送回, 3:商家自行取回"
    )
    @ApiModelProperty(value = "退货方式，1:用户自行送回,2:用户呼叫平台骑手送回, 3:商家自行取回", required = false)
    private Integer refundGoodWay;

    @FieldDoc(
            description = "退货运费承担方", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "退货运费承担方")
    private Integer refundGoodFreightType;

    @FieldDoc(
            description = "放心退,预计退货运费"
    )
    @ApiModelProperty(value = "预计退货运费")
    private Integer preReturnFreight;

    public void fillSaleReturnOrderInfo(TSaleReturnOrderInfo saleReturnOrderInfo){
        this.saleReturnOrderNo = saleReturnOrderInfo.getSaleReturnOrderNo();
        this.saleReturnOrderStatus = saleReturnOrderInfo.getStatus();
    }
}
