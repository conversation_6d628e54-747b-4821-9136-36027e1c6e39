package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.saas.common.money.PriceUtils;
import com.sankuai.shangou.qnh.orderapi.constant.store.ProjectConstants;
import com.sankuai.shangou.qnh.orderapi.utils.store.MccDynamicConfigUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/11/19
 * desc: 门店商品
 */
@TypeDoc(
        description = "门店商品"
)
@Data
@ApiModel("门店商品")
public class StoreSkuVO {

    @FieldDoc(
            description = "商品编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品编码")
    @NotNull
    private String sku;

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称")
    @NotNull
    private String name;

    @FieldDoc(
            description = "UPC列表"
    )
    @ApiModelProperty(name = "UPC列表")
    private List<String> upcList;

    @FieldDoc(
            description = "商品图片 第一个为主图", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品图片")
    @NotEmpty
    private List<String> imageList;

    @FieldDoc(
            description = "规格"
    )
    @ApiModelProperty(name = "规格")
    private String spec;

    @FieldDoc(
            description = "单位"
    )
    @ApiModelProperty(name = "单位")
    private String unit;

    @FieldDoc(
            description = "重量"
    )
    @ApiModelProperty(name = "重量")
    private Integer weight;

    @FieldDoc(
            description = "品牌编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "品牌编码")
    @NotNull
    private String brandCode;

    @FieldDoc(
            description = "品牌名称"
    )
    @ApiModelProperty(name = "品牌名称")
    private String brandName;

    @FieldDoc(
            description = "品牌编码全路径"
    )
    @ApiModelProperty(name = "品牌编码全路径")
    private String brandCodePath;

    @FieldDoc(
            description = "品牌名称全路径"
    )
    @ApiModelProperty(name = "品牌名称全路径")
    private String brandNamePath;

    @FieldDoc(
            description = "商品类目编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品类目编码")
    @NotNull
    private String categoryCode;

    @FieldDoc(
            description = "商品类目名称"
    )
    @ApiModelProperty(name = "商品类目名称")
    private String categoryName;

    @FieldDoc(
            description = "后台分类编码全路径"
    )
    @ApiModelProperty(name = "后台分类编码全路径")
    private String categoryCodePath;

    @FieldDoc(
            description = "后台分类名称全路径"
    )
    @ApiModelProperty(name = "后台分类名称全路径")
    private String categoryNamePath;

    @FieldDoc(
            description = "称重类型 1-称重计量 2-称重计件 3-非称重"
    )
    @ApiModelProperty(name = "称重类型")
    private Integer weightType;

    @FieldDoc(
            description = "商品类型 0-全部(默认) 1-商品 2-自动加工商品 3-组合商品 4-原料"
    )
    @ApiModelProperty(name = "称重类型")
    private Integer skuType;

    @FieldDoc(
            description = "可用状态 1-是，2-否"
    )
    @ApiModelProperty(name = "可用状态")
    private Integer available;

    @FieldDoc(
            description = "可售状态 1-是，2-否"
    )
    @ApiModelProperty(name = "可售状态")
    private Integer allowSale;

    @FieldDoc(
            description = "门店价 单位:元", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店价")
    @NotNull
    private Double price;

    @FieldDoc(
            description = "门店库存"
    )
    @ApiModelProperty(name = "门店库存")
    private Integer stock;

    @FieldDoc(
            description = "摊位ID"
    )
    @ApiModelProperty(name = "摊位ID")
    private Long boothId;

    @FieldDoc(
            description = "之前的摊位ID"
    )
    @ApiModelProperty(name = "之前的摊位ID")
    private Long oldBoothId;

    @FieldDoc(
            description = "门店是否有效，门店一摊位是无效的 1-有效展示 2-不展示"
    )
    @ApiModelProperty(name = "门店是否有效")
    private Integer storeStatus;

    @FieldDoc(
            description = "无限库存模式下自定义库存标记 0：非自定义库存 1：自定义库存,非无限库存模式下这个字段不需要", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店是否有效")
    @NotNull
    private Integer customizeStockFlag;

    @FieldDoc(
            description = "线上商品名称"
    )
    @ApiModelProperty(name = "线上商品名称")
    private String onlineName;

    @FieldDoc(
            description = "线上规格"
    )
    @ApiModelProperty(name = "线上规格")
    private String onlineSpec;

    @FieldDoc(
            description = "线上重量"
    )
    @ApiModelProperty(name = "线上重量")
    private Integer onlineWeight;

    @FieldDoc(
            description = "线上售卖单位"
    )
    @ApiModelProperty(name = "线上售卖单位")
    private String onlineSaleUnit;

    @FieldDoc(
            description = "第二天是否自动恢复无限库存 0-不自动恢复 1-自动恢复  默认为0"
    )
    @ApiModelProperty(name = "第二天是否自动恢复无限库存")
    private Integer autoResumeInfiniteStock;

    @FieldDoc(
            description = "自定义库存数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "自定义库存数量", required = true)
    private Integer customizeStockQuantity;

    @FieldDoc(
            description = "商品渠道信息列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品渠道信息列表", required = true)
    private List<ChannelSkuInfoVO> skuChannelsInfoList;

    @FieldDoc(
            description = "可售时间，如果为无限，此字段为null；" +
                    "key为可售日期，参考WeekDayEnum（0-monday ,1-tuesday ,2-wednesday ,3-thursday ,4-friday ,5-saturday ,6-sunday）；" +
                    "value为可售时间段，不允许有交集，个数不超过5个"
    )
    @ApiModelProperty(name = "可售时间")
    private Map<Integer, List<TimeSlotVO>> availableTimes;

    @FieldDoc(
            description = "是否为力荐商品，0-否， 1-是"
    )
    @ApiModelProperty(name = "是否为力荐商品")
    private Integer specialty;

    @FieldDoc(
            description = "商品描述,150字以内"
    )
    @ApiModelProperty(name = "商品描述")
    private String description;

    @FieldDoc(
            description = "商品属性,不超过十个属性"
    )
    @ApiModelProperty(name = "商品属性")
    private List<StoreSkuPropertyVO> properties;


    /**
     * 设置门店商品价格趋势信息
     * @param storeSkuPriceFilter 门店商品价格过滤器
     * @param storeSkuWithChannelPriceTrendVO 门店商品价格趋势
     * @param priceTrendPermissionMap 用户价格趋势权限
     */
    public void fillStoreSkuWithChannelPriceTrendInfo(
            StoreSkuPriceFilter storeSkuPriceFilter,
            StoreSkuWithChannelPriceTrendVO storeSkuWithChannelPriceTrendVO,
            Map<String, Boolean> priceTrendPermissionMap) {

        if (CollectionUtils.isEmpty(this.skuChannelsInfoList)) {
            return;
        }

        this.skuChannelsInfoList.forEach(channelSkuInfoVO -> {

            Integer channelId = channelSkuInfoVO.getChannelId();

            // 计算市斤价
            if (storeSkuPriceFilter.getCanCalculatePriceByWeight() && channelSkuInfoVO.getPrice() != null
                    && this.weight != null) {
                long priceByFen = PriceUtils.yuan2Fen(channelSkuInfoVO.getPrice());
                long pricePer500gByFen = PriceUtils.calculatePriceByWeight(priceByFen, this.weight, ProjectConstants.WEIGHT_500G);
                channelSkuInfoVO.setPricePer500g(PriceUtils.fen2Yuan(pricePer500gByFen));
            }

            // 判断是否显示价格趋势图标
            // 计算是否展示价格趋势图标
            // 若降级到直接展示价格趋势图标, 直接返回可以展示图标; 反之, 则需要判断是否有价格趋势数据
            // 备注：趋势图标展示不判断权限, 只判断是否有数据
            boolean priceTrendIconDirectShow = MccDynamicConfigUtil.isPriceTrendIconDirectShow();
            boolean hasPriceTrend = storeSkuWithChannelPriceTrendVO != null
                    && storeSkuWithChannelPriceTrendVO.isHasPriceTrend(channelSkuInfoVO.getChannelId());
            boolean showPriceTrendIcon = priceTrendIconDirectShow || hasPriceTrend;
            channelSkuInfoVO.setShowPriceTrendIcon(showPriceTrendIcon);

            // 设置渠道价格趋势, 需要根据权限过滤价格趋势数据
            ChannelSkuPriceTrendVO priceTrend = null;
            if (storeSkuWithChannelPriceTrendVO != null) {
                priceTrend = storeSkuWithChannelPriceTrendVO.generateChannelSkuPriceTrendVO(
                        channelId, priceTrendPermissionMap);
            }
            if (priceTrend == null) {
                priceTrend = ChannelSkuPriceTrendVO.empty();
            }

            channelSkuInfoVO.setPriceTrend(priceTrend);
        });
    }

    /**
     * 库存描述字段设置
     */
    public void fillStockDesc() {
        if (CollectionUtils.isEmpty(this.skuChannelsInfoList) || this.customizeStockFlag == null) {
            return;
        }
        if (this.customizeStockFlag == 0) {//非自定义
            this.skuChannelsInfoList.forEach(channelSku -> channelSku.setStockDesc("无限库存"));
        } else {
            this.skuChannelsInfoList.forEach(channelSku -> channelSku.setStockDesc(channelSku.getStock() == null ? "0" : channelSku.getStock().toString()));
        }
    }
}
