package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QuerySubsetPermissionByCodeRequest;
import com.sankuai.shangou.qnh.orderapi.constant.pc.ConfigDefaultValueConstant;
import lombok.Data;

/**
 * @Author: <EMAIL>
 * @Date: 2018/12/24 下午5:44
 */
@Data
public class QueryAuthByUidCodeRequest {

    @FieldDoc(
        description = "账户id"
    )
    private long accountId;

    @FieldDoc(
        description = "appid"
    )
    private int appId;

    @FieldDoc(
            description = "资源code"
    )
    private String code;

    @FieldDoc(
            description = "查询类型 0-全部(默认)，1-菜单，2-页面，3-按钮，4-字段"
    )
    private int type = 0;

    public QuerySubsetPermissionByCodeRequest convert2ThriftRequest(){
        QuerySubsetPermissionByCodeRequest request = new QuerySubsetPermissionByCodeRequest();

        request.setAccountId(this.accountId);
        request.setAppId(ConfigDefaultValueConstant.SAAS_B_APP_ID);
        request.setCode(this.code);
        request.setType(this.type);

        return request;
    }
}
