package com.sankuai.shangou.qnh.orderapi.remote;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.meituan.reco.pickselect.consts.OpenOpType;
import com.sankuai.meituan.reco.pickselect.consts.ResultCodeEnum;
import com.sankuai.meituan.reco.pickselect.thrift.OpenPickThriftService;
import com.sankuai.meituan.reco.pickselect.thrift.OpenTraceListQueryRequest;
import com.sankuai.meituan.reco.pickselect.thrift.OpenTraceListResponse;
import com.sankuai.meituan.reco.pickselect.thrift.OpenTraceQueryRequest;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.shangou.qnh.orderapi.remote.OrderMngRemoteService.convertBizType;

@Component
@Slf4j
@Rhino
public class PickSelectOrderTraceService {
    @Autowired
    private OpenPickThriftService.Iface openPickThriftService;

    @Degrade(rhinoKey = "PickSelectOrderTraceService-getOrderListTraceByOpTypes",
            fallBackMethod = "getOrderListTraceByOpTypesFallback",
            timeoutInMilliseconds = 2000)
    public  <T> OpenTraceListResponse getOrderListTraceByOpTypes(List<T> orderList, Function<T, Integer> channelIdExtractor, Function<T, String> channelOrderIdExtractor) {
        if (!MccConfigUtil.getOrderShowPickerNamesSwitch() || CollectionUtils.isEmpty(orderList)) {
            return null;
        }
        try {
            List<OpenTraceQueryRequest> requests = orderList.stream()
                    .filter(order -> StringUtils.isNotEmpty(channelOrderIdExtractor.apply(order)))
                    .map(order -> new OpenTraceQueryRequest()
                            .setSource(convertBizType(channelIdExtractor.apply(order)))
                            .setUnifyOrderId(channelOrderIdExtractor.apply(order)))
                    .collect(Collectors.toList());
            OpenTraceListResponse response = openPickThriftService.queryOrderListTrace(new OpenTraceListQueryRequest(requests, Arrays.asList(OpenOpType.pickEvent)));
            return response;
        } catch (TException e) {
            log.error("getOrderListTraceByOpenOpType.pickEvent error", e);
        }
        return new OpenTraceListResponse(ResultCodeEnum.FAIL.getCode(),"", new HashMap<>());
    }

    public OpenTraceListResponse getOrderListTraceByOpTypesFallback(List<T> orderList, Function<T, Integer> channelIdExtractor, Function<T, String> channelOrderIdExtractor) {
        log.warn("PickSelectOrderTraceService.getOrderListTraceByOpTypes 发生降级");
        return new OpenTraceListResponse(ResultCodeEnum.FAIL.getCode(),"", new HashMap<>());
    }
}