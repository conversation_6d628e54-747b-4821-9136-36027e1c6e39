package com.sankuai.shangou.qnh.orderapi.annotation.store;

import java.lang.annotation.*;
import com.sankuai.shangou.qnh.orderapi.enums.store.FunctionPowers;

/**
 * 可以标记给Controller类或者方法。方法配置优先于类配置
 * */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ ElementType.METHOD, ElementType.TYPE })
public @interface Auth {
	/**
	 * 是否验证登录。
	 *
	 * authenticate = false，表示禁用登录验证
	 * */
	boolean authenticate() default true;

	/**
	 * 是否获取最近用户数据, needNewest = true,表示获取最新的用户数据(用户信息&权限),不走缓存
     */
	boolean needNewest() default false;

	/**
	 * 权限集合。如果为空，则只验证是否登录
	 * */
	FunctionPowers[] permission() default {};
}
