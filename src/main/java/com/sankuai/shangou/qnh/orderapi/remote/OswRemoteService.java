package com.sankuai.shangou.qnh.orderapi.remote;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.infra.osw.api.org.TEmployeeService;
import com.sankuai.shangou.infra.osw.api.org.dto.request.AccountIdsRequest;
import com.sankuai.shangou.infra.osw.api.org.dto.response.EmployeeDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/5/14 17:30
 **/
@Rhino
@Slf4j
public class OswRemoteService {

    @Resource
    private TEmployeeService tEmployeeService;

    @Degrade(rhinoKey = "OswClient.queryEmpByAccountId", fallBackMethod = "queryEmpByAccountIdFallback", timeoutInMilliseconds = 2000)
    public Optional<EmployeeDTO> queryEmpByAccountId(Long tenantId, Long accountId) {
        AccountIdsRequest request = new AccountIdsRequest();
        request.setAccountIds(Collections.singletonList(accountId));
        request.setTenantId(tenantId);
        TResult<List<EmployeeDTO>> tResult;
        try {
            log.info("start invoke tEmployeeService.queryEmpByAccountIds, request: {}", request);
            tResult = tEmployeeService.queryEmpByAccountIds(request);
            log.info("end invoke tEmployeeService.queryEmpByAccountIds, result: {}", tResult);
        } catch (Exception e) {
            log.error("查账号信息失败", e);
            throw new ThirdPartyException("查账号信息失败");
        }

        if (!tResult.isSuccess()) {
            throw new BizException("查账号信息失败");
        }

        if (CollectionUtils.isEmpty(tResult.getData())) {
            return Optional.empty();
        }

        return Optional.of(tResult.getData().get(0));
    }

    public Optional<EmployeeDTO> queryEmpByAccountIdFallback(Long tenantId, Long accountId) {
        log.error("OswClient.queryEmpByAccountId 发生降级");
        return Optional.empty();
    }
}
