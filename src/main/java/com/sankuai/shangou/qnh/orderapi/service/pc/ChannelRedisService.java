package com.sankuai.shangou.qnh.orderapi.service.pc;


import com.meituan.shangou.saas.tenant.thrift.dto.channel.ChannelBaseDto;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.AfterSaleOperatorBo;

/**
 * <AUTHOR>
 * @create 2023/7/17 11:31
 *
 * 运营端渠道缓存api
 */
public interface ChannelRedisService {

    Integer getAuditType(Long viewOrderId);

    AfterSaleOperatorBo getOperatorDetail(String viewOrderId);
}
