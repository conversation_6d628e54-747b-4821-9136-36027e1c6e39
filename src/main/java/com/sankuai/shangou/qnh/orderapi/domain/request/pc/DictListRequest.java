package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/20
 */
@Data
@ToString
@TypeDoc(
        description = "批量查询字典请求"
)
public class DictListRequest {

    @FieldDoc(description = "字典列表")
    private List<String> dictQueryList;


    public void validate() {
        if (CollectionUtils.isEmpty(dictQueryList)) {
            throw new IllegalArgumentException("字典列表不能为空");
        }
    }

}
