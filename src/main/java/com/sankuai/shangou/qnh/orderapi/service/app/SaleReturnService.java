package com.sankuai.shangou.qnh.orderapi.service.app;

import com.meituan.reco.pickselect.common.cat.CatTransaction;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.store.management.enums.ResultCodeEnum;
import com.sankuai.meituan.reco.store.management.thrift.common.BaseResult;
import com.sankuai.meituan.reco.store.management.thrift.scmdelivery.resp.SaleReturnOrderQueryBySaleOrderResp;
import com.sankuai.shangou.qnh.orderapi.enums.app.PermissionCodeEnum;
import com.sankuai.shangou.qnh.orderapi.context.app.ApiMethodParamThreadLocal;
import com.sankuai.shangou.qnh.orderapi.converter.app.ChannelOrderConverter;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.CloseSaleReturnOrderRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.RefundApplyAuditRequest;
import com.sankuai.shangou.qnh.orderapi.exception.app.CommonRuntimeException;
import com.sankuai.shangou.qnh.orderapi.remote.AuthRemoteService;
import com.sankuai.shangou.qnh.orderapi.remote.SaleReturnOrderRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 * @date 2021/12/14
 */
@Slf4j
@Service(value = "appSaleReturnService")
public class SaleReturnService {

    @Resource
    private SaleReturnOrderRemoteService saleReturnOrderRemoteService;
    @Resource
    private AuthRemoteService authThriftWrapper;

    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public boolean checkCanCreateSaleReturnOrder(RefundApplyAuditRequest request) {
        try {
            //判断当前用户是否有销退模块权限
            if(!authThriftWrapper.isCodeHasAuth(PermissionCodeEnum.SALE_RETURN_TASK.getCode())){
                return false;
            }

            //判断是否会创建销退单
            return saleReturnOrderRemoteService.canCreateSaleReturnOrder(
                    ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId(),
                    ApiMethodParamThreadLocal.getIdentityInfo().getStoreId(),
                    ChannelOrderConverter.convertChannelId2OrderBizType(request.getChannelId()),
                    request.getChannelOrderId()
            );
        } catch (Exception e) {
            log.error("判断订单退款后是否会触发销退流程异常，将默认降级至不会", e);
            return false;
        }
    }

    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public String getSaleReturnOrderNo(RefundApplyAuditRequest request) {
        try {
            //判断当前用户是否有销退模块权限
            if(!authThriftWrapper.isCodeHasAuth(PermissionCodeEnum.SALE_RETURN_TASK.getCode())){
                return null;
            }

            //获取销退单号
            SaleReturnOrderQueryBySaleOrderResp response = saleReturnOrderRemoteService.getSaleReturnOrderNo(
                    ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId(),
                    ApiMethodParamThreadLocal.getIdentityInfo().getStoreId(),
                    ChannelOrderConverter.convertChannelId2OrderBizType(request.getChannelId()),
                    request.getChannelOrderId(),
                    String.valueOf(request.getAfterSaleId())
            );
            checkResponse(response.getStatus());
            return CollectionUtils.isNotEmpty(response.getSaleReturnOrders()) ? response.getSaleReturnOrders().get(0).getSaleReturnOrderNo() : null;

        } catch (Exception e) {
            log.error("获取销退单号异常", e);
            return null;
        }
    }

    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public void closeSaleReturnOrder(RefundApplyAuditRequest request, String saleReturnOrderNo) {
        CloseSaleReturnOrderRequest closeSaleReturnOReq = new CloseSaleReturnOrderRequest();
        closeSaleReturnOReq.setSaleReturnOrderNo(saleReturnOrderNo);
        closeSaleReturnOReq.setEntityId(request.getEntityId());
        closeSaleReturnOReq.setEntityType(request.getEntityType());
        closeSaleReturnOReq.setStoreId(ApiMethodParamThreadLocal.getIdentityInfo().getStoreId());
        closeSaleReturnOReq.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
        try {
            //关闭建销退单
            saleReturnOrderRemoteService.appCloseSaleReturnOrder(closeSaleReturnOReq);
        } catch (Exception e) {
            log.error("关闭销退单异常", e);
        }
    }

    private void checkResponse(BaseResult result) {
        if (result.getCode() != ResultCodeEnum.SUCCESS.getCode()) {
            throw new CommonRuntimeException(result.getMsg());
        }
    }
}
