package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;


@TypeDoc(
        description = "收货查询请求公共入参"
)
@Data
@ApiModel("收货查询请求公共入参")
public class CommonRequest {
    @FieldDoc(
            description = "租户id"
    )
    @ApiModelProperty(value = "租户id",required = true)
    @NotNull(message = "tenantId不能为空")
    private Long tenantId;

    @FieldDoc(
            description = "门店id"
    )
    @ApiModelProperty(value = "门店id",required = true)
    @NotNull(message = "storeId不能为空")
    private Long storeId;

    @FieldDoc(
            description = "实体id"
    )
    @ApiModelProperty(value = "实体id",required = true)
    private Long entityId;

    @FieldDoc(
            description = "实体类型"
    )
    @ApiModelProperty(value = "实体类型",required = true)
    private Integer entityType;
}
