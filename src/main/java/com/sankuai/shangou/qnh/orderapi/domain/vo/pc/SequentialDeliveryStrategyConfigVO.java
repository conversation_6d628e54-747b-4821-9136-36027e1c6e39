package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/9/15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SequentialDeliveryStrategyConfigVO {

	@FieldDoc(
			description = "多渠道配送顺序"
	)
	@ApiModelProperty(value = "多渠道配送顺序")
	private List<Integer> orderedDeliveryChannelIds;

	@FieldDoc(
			description = "自动切换渠道超时时间"
	)
	@ApiModelProperty(value = "自动切换渠道超时时间")
	private Integer timeoutForShiftDeliveryChannelInMinutes;
}
