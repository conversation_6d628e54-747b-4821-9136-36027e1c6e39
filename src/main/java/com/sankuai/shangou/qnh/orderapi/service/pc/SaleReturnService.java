package com.sankuai.shangou.qnh.orderapi.service.pc;

import com.google.common.collect.Lists;
import com.meituan.reco.pickselect.common.cat.CatTransaction;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OcmsOrderDetailReq;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.sankuai.meituan.reco.store.management.enums.ResultCodeEnum;
import com.sankuai.meituan.reco.store.management.thrift.common.BaseResult;
import com.sankuai.meituan.reco.store.management.thrift.scmdelivery.resp.SaleReturnOrderQueryBySaleOrderResp;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.CloseSaleReturnOrderRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.RefundApplyAuditRequest;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.OrderDetailResponse;
import com.sankuai.shangou.qnh.orderapi.enums.pc.PermissionCodeEnum;
import com.sankuai.shangou.qnh.orderapi.exception.pc.CommonRuntimeException;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.PoiInfoBO;
import com.sankuai.shangou.qnh.orderapi.remote.SaleReturnOrderRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Map;

@Slf4j
@Service
public class SaleReturnService {

    @Resource
    private SaleReturnOrderRemoteService saleReturnOrderRemoteService;

    @Resource
    private AuthService authService;

    @Resource
    private OrderMngService orderMngService;

    @Resource
    private PoiService poiService;

    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public boolean checkCanCreateSaleReturnOrder(RefundApplyAuditRequest request) {
        try {
            //判断当前用户是否有销退模块权限
            if (!authService.isCodeHasAuth(PermissionCodeEnum.SALE_RETURN_TASK.getCode())) {
                return false;
            }

            OrderDetailResponse orderDetailResponse = queryOrderDetail(request.getChannelId(), request.getChannelOrderId());
            if (orderDetailResponse.getOrderDetail() == null) {
                return false;
            }

            //判断是否会创建销退单
            return saleReturnOrderRemoteService.canCreateSaleReturnOrder(
                    ContextHolder.currentUserTenantId(),
                    orderDetailResponse.getOrderDetail().getStoreId(),
                    ChannelOrderConvertUtils.convertBizType(request.getChannelId()),
                    request.getChannelOrderId());
        } catch (Exception e) {
            log.error("判断订单退款后是否会触发销退流程异常，将默认降级至不会", e);
            return false;
        }
    }

    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public String getSaleReturnOrderNo(RefundApplyAuditRequest request) {
        try {
            //判断当前用户是否有销退模块权限
            if (!authService.isCodeHasAuth(PermissionCodeEnum.SALE_RETURN_TASK.getCode())) {
                return null;
            }

            OrderDetailResponse orderDetailResponse = queryOrderDetail(request.getChannelId(), request.getChannelOrderId());
            if (orderDetailResponse.getOrderDetail() == null) {
                return null;
            }
            //获取销退单号
            SaleReturnOrderQueryBySaleOrderResp response = saleReturnOrderRemoteService.getSaleReturnOrderNo(
                    ContextHolder.currentUserTenantId(),
                    orderDetailResponse.getOrderDetail().getStoreId(),
                    ChannelOrderConvertUtils.convertBizType(request.getChannelId()),
                    request.getChannelOrderId(),
                    String.valueOf(request.getAfterSaleId())
            );
            checkResponse(response.getStatus());
            return CollectionUtils.isNotEmpty(response.getSaleReturnOrders()) ? response.getSaleReturnOrders().get(0).getSaleReturnOrderNo() : null;

        } catch (Exception e) {
            log.error("获取销退单号异常", e);
            return null;
        }
    }

    @CatTransaction
    @MethodLog(logRequest = true, logResponse = true)
    public void closeSaleReturnOrder(RefundApplyAuditRequest request, String saleReturnOrderNo) {
        try {
            OrderDetailResponse orderDetailResponse = queryOrderDetail(request.getChannelId(), request.getChannelOrderId());
            if (orderDetailResponse.getOrderDetail() == null) {
                return;
            }

            final Long storeId = orderDetailResponse.getOrderDetail().getStoreId();
            CloseSaleReturnOrderRequest closeSaleReturnOReq = new CloseSaleReturnOrderRequest();
            closeSaleReturnOReq.setSaleReturnOrderNo(saleReturnOrderNo);
            closeSaleReturnOReq.setEntityId(storeId);
            final Map<Long, PoiInfoBO> longPoiInfoBOMap = poiService.queryPoiByIds(ContextHolder.currentUserTenantId(), Lists.newArrayList(storeId));
            closeSaleReturnOReq.setEntityType(longPoiInfoBOMap.get(storeId) == null ? 3 : longPoiInfoBOMap.get(storeId).getEntityType());
            closeSaleReturnOReq.setStoreId(storeId);
            closeSaleReturnOReq.setTenantId(ContextHolder.currentUserTenantId());

            //关闭建销退单
            saleReturnOrderRemoteService.webCloseSaleReturnOrder(closeSaleReturnOReq);
        } catch (Exception e) {
            log.error("关闭销退单异常", e);
        }
    }

    private void checkResponse(BaseResult result) {
        if (result.getCode() != ResultCodeEnum.SUCCESS.getCode()) {
            throw new CommonRuntimeException(result.getMsg());
        }
    }

    private OrderDetailResponse queryOrderDetail(Integer channelId, String channelOrderId) {
        OcmsOrderDetailReq orderDetailReq = new OcmsOrderDetailReq();
        orderDetailReq.setTenantId(ContextHolder.currentUserTenantId());
        orderDetailReq.setOperator(ContextHolder.currentUserStaffId());
        orderDetailReq.setChannelId(channelId);
        orderDetailReq.setChannelOrderId(channelOrderId);
        try {
            return orderMngService.getOrderDetail(orderDetailReq);
        } catch (Exception e) {
            log.error("getOrderDetail error orderMngFacade.getOrderDetail error", e);
            throw new CommonRuntimeException(e);
        }
    }
}
