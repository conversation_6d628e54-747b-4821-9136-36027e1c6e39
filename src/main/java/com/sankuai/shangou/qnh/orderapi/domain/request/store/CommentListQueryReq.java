package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.google.common.base.Splitter;
import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.order.management.client.dto.request.OrderRequest;
import com.meituan.shangou.saas.order.management.client.dto.request.OrderSearchRequest;
import com.sankuai.shangou.qnh.orderapi.constant.store.CommentConstants;
import com.sankuai.shangou.qnh.orderapi.constant.store.ProjectConstants;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.IdentityInfo;
import com.sankuai.shangou.qnh.orderapi.exception.store.CommonRuntimeException;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import com.sankuai.shangou.qnh.orderapi.utils.store.ApiMethodParamThreadLocal;
import com.sankuai.shangou.qnh.orderapi.utils.store.DateUtils;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import com.sankuai.sgfulfillment.comment.thrift.common.CommentReplyStatusEnum;
import com.sankuai.sgfulfillment.comment.thrift.dto.CommentListQueryRequest;
import com.sankuai.sgfulfillment.comment.thrift.enums.CommentContactEnum;

import java.util.*;
import java.util.stream.Collectors;

@TypeDoc(
        description = "评价列表查询请求参数",
        authors = "hejunliang"
)
@Data
@ApiModel("评价列表查询请求参数")
public class CommentListQueryReq {


    @FieldDoc(
            description = "门店id",
            rule = "storeId, 使用逗号隔开"
    )
    private String storeId;

    @FieldDoc(
            description = "渠道id列表"
    )
    private List<Integer> channelIds;

    @FieldDoc(
            description = "开始时间",
            rule = "yyyy-MM-dd",
            requiredness = Requiredness.REQUIRED
    )
    private String startTime;

    @FieldDoc(
            description = "结束时间",
            rule = "yyyy-MM-dd",
            requiredness = Requiredness.REQUIRED
    )
    private String endTime;

    @FieldDoc(
            description = "评价级别,GOOD_COMMENT:好评, COMMON_COMMENT:中评, BAD_COMMENT:差评，后续废弃",
            rule = "GOOD_COMMENT:好评, COMMON_COMMENT:中评, BAD_COMMENT:差评，后续废弃"
    )
    @Deprecated
    private String commentLevel;

    @FieldDoc(
            description = "回复状态",
            rule = "0:未回复, 1:已回复, 不传值代表全部"
    )
    private Integer replyStatusForPage;

    @FieldDoc(
            description = "评价内容类型，EXISTS:有内容, NOT_EXISTS:无内容, 不传值标识传布内容"
    )
    private String commentContentType;

    @FieldDoc(
            description = "评价是否回访，0:未回访, 1:已回访, 不传值标识全部内容"
    )
    private Integer commentContactType;

    @FieldDoc(
            description = "评价级别列表,GOOD_COMMENT:好评, COMMON_COMMENT:中评, BAD_COMMENT:差评",
            rule = "GOOD_COMMENT:好评, COMMON_COMMENT:中评, BAD_COMMENT:差评"
    )
    private List<String> commentLevelList;

    @FieldDoc(
            description = "页码", requiredness = Requiredness.REQUIRED
    )
    private Integer page;

    @FieldDoc(
            description = "每页记录数", requiredness = Requiredness.REQUIRED
    )
    private Integer pageSize;

    @FieldDoc(
            description = "门店类型3-门店 5-中心仓 6-共享前置仓", requiredness = Requiredness.OPTIONAL
    )
    private Integer entityType;

    @FieldDoc(
            description = "星级:1-Very poor,2-Poor,3-Average,4-Good,5-Excellent", requiredness = Requiredness.OPTIONAL
    )
    private List<String> orderScoreList;

    @FieldDoc(
            description = "是否有图: 0-无图 1-有图 ", requiredness = Requiredness.OPTIONAL
    )
    private String withImage;

    @FieldDoc(
            description = "评价状态",
            rule = "0:删除, 1:正常, 不传值代表全部"
    )
    private Integer isValid;

    @FieldDoc(
            description = "订单号查询列表"
    )
    private List<String> matchChannelOrderIds;

    public void validate() {
        if (StringUtils.isEmpty(this.storeId)) {
            throw new CommonRuntimeException("门店/仓不能为空", ResultCode.CHECK_PARAM_ERR);
        }
        if (StringUtils.isEmpty(this.startTime)) {
            throw new CommonRuntimeException("开始日期不能为空", ResultCode.CHECK_PARAM_ERR);
        }
		Date startDate = DateUtils.parse(this.startTime, DateUtils.YYYY_MM_DD);
		if (startDate == null) {
			throw new CommonRuntimeException("开始日期格式错误", ResultCode.CHECK_PARAM_ERR);
		}
		if (StringUtils.isEmpty(this.endTime)) {
            throw new CommonRuntimeException("结束日期不能为空", ResultCode.CHECK_PARAM_ERR);
        }
		Date endDate = DateUtils.parse(this.endTime, DateUtils.YYYY_MM_DD);
		if (endDate == null) {
            throw new CommonRuntimeException("结束日期格式错误", ResultCode.CHECK_PARAM_ERR);
        }
        if (endDate.compareTo(startDate) < 0) {
			throw new CommonRuntimeException("结束日期不能小于开始日期", ResultCode.CHECK_PARAM_ERR);
		}
		if ((endDate.getTime() - startDate.getTime()) / ProjectConstants.MILLISECOND_ONE_DAY >
				CommentConstants.COMMENT_LIST_QUERY_COMMENT_TIME_INTERVAL_DAY_MAX -1) {
			throw new CommonRuntimeException(String.format("最多只能查询%s天的数据",
					CommentConstants.COMMENT_LIST_QUERY_COMMENT_TIME_INTERVAL_DAY_MAX), ResultCode.CHECK_PARAM_ERR);
		}
        if (this.page == null || this.page.compareTo(0) <= 0) {
            throw new CommonRuntimeException("页码不能为空且必须大于0", ResultCode.CHECK_PARAM_ERR);
        }
        if (this.pageSize == null || this.pageSize.compareTo(0) <= 0) {
            throw new CommonRuntimeException("每页记录数不能为空且必须大于0", ResultCode.CHECK_PARAM_ERR);
        }
        if (this.pageSize.compareTo(CommentConstants.COMMENT_LIST_PAGE_SIZE_MAX) > 0) {
            throw new CommonRuntimeException(String.format("一页最多查询%s条记录",
                    CommentConstants.COMMENT_LIST_PAGE_SIZE_MAX), ResultCode.CHECK_PARAM_ERR);
        }
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        int maxStoreSize = MccConfigUtil.getMiniAppOrderMaxStoreSize();
        if (identityInfo.isMiniApp() && maxStoreSize < identityInfo.getStoreIdList().size()) {
            throw new CommonRuntimeException(String.format("最多能查%s家门店", maxStoreSize), ResultCode.CHECK_PARAM_ERR);
        }
    }

    public CommentListQueryRequest convertToCommentListQueryRequest() {
        CommentListQueryRequest request = new CommentListQueryRequest();
        request.setStoreIds(Splitter.on(ProjectConstants.ENGLISH_COMMA_DELIMITER).trimResults()
                .splitToList(this.storeId).stream().map(storeIdStr -> Long.valueOf(storeIdStr))
                .collect(Collectors.toList()));
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        //如果是小程序且查全门店使用header中的参数
        if (identityInfo.isMiniApp() && identityInfo.isFullStoreMode()) {
            request.setStoreIds(identityInfo.getStoreIdList());
        }
        request.setChannelIds(this.channelIds);
        request.setStartTime(DateUtils.format(DateUtils.getDayBeginTime(
                DateUtils.parse(this.startTime, DateUtils.YYYY_MM_DD)), DateUtils.YYYY_MM_DD_HH_MM_SS));
        request.setEndTime(DateUtils.format(DateUtils.getDayEndTime(
                DateUtils.parse(this.endTime, DateUtils.YYYY_MM_DD)), DateUtils.YYYY_MM_DD_HH_MM_SS));
        request.setReplyStatusList(getReplyStatusList(this.replyStatusForPage));
        request.setCommentLevel(this.commentLevel);
        request.setCommentContentType(this.commentContentType);
        request.setPageNum(this.page);
        request.setPageSize(this.pageSize);
        request.setCommentLevelList(this.commentLevelList);
        request.setCommentContactType(Objects.nonNull(this.commentContactType)? this.commentContactType : CommentContactEnum.ALL.getValue());
        request.setOrderScoreList(this.orderScoreList);
        request.setWithImage(this.withImage);
        request.setIsValid(this.isValid);
        return request;
    }

    private List<String> getReplyStatusList(Integer replyStatusForPage) {
        if (CommentConstants.REPLY_STATUS_NOT_REPLY.equals(replyStatusForPage)) {
            return Arrays.asList(CommentReplyStatusEnum.NOT_REPLY.name(), CommentReplyStatusEnum.REPLY_FAILED.name());
        } else if (CommentConstants.REPLY_STATUS_REPLIED.equals(replyStatusForPage)) {
            return Arrays.asList(CommentReplyStatusEnum.REPLYING.name(), CommentReplyStatusEnum.REPLIED.name());
        } else {
            return null;
        }
    }

    public OrderSearchRequest convertToOrderSearchRequest() {
        OrderSearchRequest request = new OrderSearchRequest();
        List<OrderRequest> orderRequestList = new ArrayList<>();
        this.matchChannelOrderIds.forEach(channelOrderId -> {
            OrderRequest orderRequest = new OrderRequest();
            orderRequest.setViewOrderId(channelOrderId);
            orderRequestList.add(orderRequest);
        });
        request.setOrderRequestList(orderRequestList);
        return request;
    }
}
