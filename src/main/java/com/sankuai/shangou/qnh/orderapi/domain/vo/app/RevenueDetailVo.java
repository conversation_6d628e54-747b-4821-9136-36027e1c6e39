package com.sankuai.shangou.qnh.orderapi.domain.vo.app;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 订单营收详情
 *
 * <AUTHOR>
 * @since 2020/12/17 14:09
 */
@TypeDoc(
        description = "订单营收信息"
)
@ApiModel("订单营收信息")
@Data
public class RevenueDetailVo {

    @FieldDoc(
            description = "商家预计收入", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "商家预计收入")
    private Integer revenueAmount;

    @FieldDoc(
            description = "打包费", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "打包费")
    private Integer packageAmount;

    @FieldDoc(
            description = "配送费", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "配送费")
    private Integer deliveryAmount;

    @FieldDoc(
            description = "活动支出", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "活动支出")
    private Integer bizActivityAmount;

    @FieldDoc(
            description = "用户实付金额", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "用户实付金额")
    private Integer actualPayAmount;

    @FieldDoc(
            description = "商品活动详情", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "商品活动详情")
    private List<String> promotionInfos;

    @FieldDoc(
            description = "订单预计毛利", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "订单预计毛利")
    private Integer netProfitOnline;

    @FieldDoc(
            description = "订单预计毛利是否包含配送成本", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "订单预计毛利是否包含配送成本")
    private Boolean withDeliveryCost;
}
