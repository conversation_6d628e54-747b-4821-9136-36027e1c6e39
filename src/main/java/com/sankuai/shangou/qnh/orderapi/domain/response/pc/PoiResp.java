package com.sankuai.shangou.qnh.orderapi.domain.response.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * @Author：<EMAIL>
 * @Date: 2018/9/18 下午3:49
 */
@TypeDoc(
    description = "门店信息"
)
@ApiModel("门店信息")
@Getter
@Setter
@ToString
@NoArgsConstructor
public class PoiResp {

  @FieldDoc(
      description = "门店id"
  )
  @ApiModelProperty(value = "门店id", required = true)
  private Long poiId;

  @FieldDoc(
      description = "门店名称"
  )
  @ApiModelProperty(value = "门店名称", required = true)
  private String poiName;

  @FieldDoc(
          description = "商户code"
  )
  @ApiModelProperty(value = "商户code", required = true)
  private String merChantCode;  //add by daiqiang04 2018-11-13,订单&结算管理（一期）需求，查询打款列表需要传入商户code，但是前端只能提供门店id


  private Long depId;


  private String outPoiId;

  public PoiResp(Long poiId,String poiName,String merChantCode){
    this.poiId = poiId;
    this.poiName = poiName;
    this.merChantCode = merChantCode;
  }


}