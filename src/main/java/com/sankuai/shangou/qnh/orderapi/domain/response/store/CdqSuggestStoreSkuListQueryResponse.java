package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.CdqSuggestStoreSkuVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.PageInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "商品名称联想查询分页查询响应"
)
@Data
@ApiModel("商品名称联想查询分页查询响应")
public class CdqSuggestStoreSkuListQueryResponse {

    @FieldDoc(
            description = "门店商品明细", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店商品明细")
    private List<CdqSuggestStoreSkuVo> suggestStoreSku;
    @FieldDoc(
            description = "分页信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "分页信息")
    private PageInfoVO pageInfo;

}
