package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.TagInfoVO;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "用户信息"
)
@Data
public class CustomerInfoVO {
    @FieldDoc(
            description = "用户姓名", requiredness = Requiredness.REQUIRED
    )
    private String name;

    @FieldDoc(
            description = "用户标签", requiredness = Requiredness.REQUIRED
    )
    private List<TagInfoVO> tags;
    @FieldDoc(
            description = "用户手机号", requiredness = Requiredness.REQUIRED
    )
    private String phoneNum;
    @FieldDoc(
            description = "用户地址", requiredness = Requiredness.REQUIRED
    )
    private String address;
    @FieldDoc(
            description = "预计到达时间", requiredness = Requiredness.REQUIRED
    )
    private String expectArrayTime;

}
