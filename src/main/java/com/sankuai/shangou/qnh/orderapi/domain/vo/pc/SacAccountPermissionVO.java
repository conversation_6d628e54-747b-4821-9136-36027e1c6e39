package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.util.List;

/**
 * 账号权限对象
 *
 * <AUTHOR>
 * @since 2020/12/23
 */
@TypeDoc(
        description = "账号权限对象",
        since = "2020/12/23",
        authors = {"zhangshengyue"}
)
@Data
public class SacAccountPermissionVO {

    /**
     * 账号id
     */
    @FieldDoc(
            description = "账号id"
    )
    private Long accountId;

    /**
     * 权限信息
     */
    @FieldDoc(
            description = "权限信息"
    )
    private List<SacPermissionVo> sacPermissionVos;
}
