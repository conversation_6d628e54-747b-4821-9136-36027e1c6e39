package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@TypeDoc(
        description = "检查设备ID是否在灰度列表中"
)
@Data
@ApiModel("检查设备ID是否在灰度列表中")
public class EquipmentCheckRequest {

    @FieldDoc(
            description = "设备ID"
    )
    @ApiModelProperty(value = "设备ID", required = true)
    @NotNull
    private String uid;

}
