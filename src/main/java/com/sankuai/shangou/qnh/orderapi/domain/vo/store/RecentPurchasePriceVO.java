package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2018/9/28
 * @email jianglilin02@meituan
 */
@Data
@ApiModel("最近收货信息列表item")
public class RecentPurchasePriceVO {
    @ApiModelProperty(value = "收货时间", required = true)
    @NotNull
    public String time;
    @ApiModelProperty(value = "收货价格", required = true)
    @NotNull
    public Double price;
    @ApiModelProperty(value = "收货价格基本单位", required = true)
    @NotEmpty
    public String unit;
}
