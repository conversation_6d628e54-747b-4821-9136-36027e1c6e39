package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.StoreDeliveryConfigVO;
import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 修改门店/仓库的聚合配送设置请求体
 * @Author: zhangjian155
 * @Date: 2022/10/11 15:20
 */
@TypeDoc(
        description = "修改门店/仓库的聚合配送设置请求体"
)
@ApiModel("修改门店/仓库的聚合配送设置请求体")
@Data
public class ModifyStoreDeliveryConfigRequest implements BaseRequest {

    @FieldDoc(
            description = "门店ID"
    )
    @ApiModelProperty(value = "门店ID", required = true)
    private Long storeId;

    @FieldDoc(
            description = "租户ID"
    )
    @ApiModelProperty(value = "租户ID", required = true)
    private Long tenantId;

    @FieldDoc(
            description = "门店聚合配送配置"
    )
    @ApiModelProperty(value = "门店聚合配送配置")
    private StoreDeliveryConfigVO storeAggDeliveryConfig;

    @Override
    public void selfCheck() {
        AssertUtil.notNull(storeId, "门店ID不能为空", "storeId");
        AssertUtil.notNull(tenantId, "租户ID不能为空", "tenantId");
        AssertUtil.notNull(storeAggDeliveryConfig, "门店聚合配送配置不能为空", "storeLaunchDeliveryConfig");
        AssertUtil.notEmpty(storeAggDeliveryConfig.getDeliveryChannelConfigVoList(), "渠道发配送节点数组不能为空",
                "deliveryChannelLaunchPointVos");
    }
}
