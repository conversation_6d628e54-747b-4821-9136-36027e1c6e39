package com.sankuai.shangou.qnh.orderapi.remote;

import com.meituan.linz.boot.util.Fun;
import com.sankuai.meituan.reco.store.management.thrift.ObtainStoreInfoRequest;
import com.sankuai.meituan.reco.store.management.thrift.ObtainStoreInfoResult;
import com.sankuai.meituan.reco.store.management.thrift.StoreInfo;
import com.sankuai.meituan.shangou.empower.auth.sdk.bean.SessionInfo;
import com.sankuai.meituan.shangou.empower.auth.sdk.context.SessionContext;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.TokenTypeEmum;
import com.sankuai.meituan.shangou.empower.auth.thrift.service.LoginThriftService;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.LoginRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.LoginResponse;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.ResetPasswordRequest;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.Result;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.AccountLoginBo;
import com.sankuai.shangou.qnh.orderapi.exception.app.CommonRuntimeException;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ResponseHandler;
import com.sankuai.shangou.qnh.orderapi.utils.pc.RpcInvoker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2018/11/1
 * 登录的外部服务
 **/
@Component
@Slf4j
public class LoginRemoteService {

    @Resource
    LoginThriftService.Iface loginThriftService;

    @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
    @Resource
    private com.sankuai.meituan.reco.store.management.thrift.LoginThriftService.Iface storeLoginThriftService;


    @MethodLog(logRequest = true, logResponse = true, logger = "thrift")
    public AccountLoginBo login(LoginRequest request) {
        LoginResponse response = RpcInvoker.invokeReturn(() -> loginThriftService.login(request));
        ResponseHandler.handleResult(response.getResult());
        return new AccountLoginBo().build(response);
    }

    @MethodLog(logRequest = true, logResponse = true, logger = "thrift")
    public AccountLoginBo accountLogin(LoginRequest request) {
        LoginResponse response = RpcInvoker.invokeReturn(() -> loginThriftService.accountLogin(request));
        ResponseHandler.handleResult(response.getResult());
        return new AccountLoginBo().build(response);
    }

    /**
     * 注销接口
     *
     * @param token 登录后的token
     */
    @MethodLog(logRequest = true, logResponse = true, logger = "thrift")
    public void loginOut(TokenTypeEmum tokenType, String token) {
        Result result = RpcInvoker.invokeReturn(() -> loginThriftService.loginOut(tokenType.getValue(), token));
        if (result.getCode() != ResultCodeEnum.SUCCESS.getValue()) {
            log.info("登出 失败 tokenType:{},token:{}", tokenType, token);
        }
    }

    public List<Long> getCurrentUserFullStoreList() {
        // 获取当前账户信息
        SessionInfo sessionInfo = SessionContext.getCurrentSession();
        ObtainStoreInfoRequest request = new ObtainStoreInfoRequest();
        request.setTenantId(sessionInfo.getTenantId());
        request.setAccountId(sessionInfo.getAccountId());
        request.setAppId(sessionInfo.getAuthAppId());
        try {
            ObtainStoreInfoResult result = storeLoginThriftService.obtainStoreInfo(request);
            return Fun.map(result.getStoreInfoList(), StoreInfo::getPoiId);
        } catch (Exception e) {
            log.error("loginThriftService.obtainStoreInfo error", e);
            throw new CommonRuntimeException(e);
        }
    }

}
