package com.sankuai.shangou.qnh.orderapi.domain.vo.app;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023-12-29
 * @email <EMAIL>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PropertiesViewVO {

    @FieldDoc(
            description = "解析后的商品属性文案", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "解析后的商品属性文案")
    private String text;

    @FieldDoc(
            description = "解析后的商品属性颜色", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "解析后的商品属性颜色")
    private String color;

}
