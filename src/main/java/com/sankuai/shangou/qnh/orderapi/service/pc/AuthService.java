package com.sankuai.shangou.qnh.orderapi.service.pc;

import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.sankuai.meituan.reco.store.saas.infrastructure.shield.common.monitor.CommonMonitorTransaction;
import com.sankuai.meituan.shangou.empower.auth.sdk.bean.SessionInfo;
import com.sankuai.meituan.shangou.empower.auth.sdk.context.SessionContext;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.AccountInfoVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.DataPermissionVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.PermissionVo;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.RoleInfoVo;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import com.sankuai.shangou.qnh.orderapi.enums.pc.AppAuthIdConstantsEnum;
import com.sankuai.shangou.qnh.orderapi.exception.pc.CommonLogicException;
import com.sankuai.shangou.qnh.orderapi.exception.pc.CommonRuntimeException;
import com.sankuai.shangou.qnh.orderapi.remote.AuthRemoteService;
import com.sankuai.shangou.qnh.orderapi.utils.pc.MccConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
@Slf4j
@Rhino
public class AuthService {

    @Resource
    private AuthRemoteService authRemoteService;

    /**
     * 判断针对
     *
     * @param authCode
     * @return
     */
    public Boolean isCodeHasAuth(String authCode) {
        if (MccConfigUtil.useSacAuthenticationV2()) {
            Map<String, Boolean> codeAuthMap = isHasPermission(Arrays.asList(authCode));
            return BooleanUtils.isTrue(codeAuthMap.get(authCode));
        }
        else {
            List<String> codeHasAuth = getCurrentAccountAllPermissionCodes();
            return CollectionUtils.isNotEmpty(codeHasAuth) && codeHasAuth.contains(authCode);
        }
    }

    /**
     * 判断登录账号是否有指定权限码的权限
     * @param currPermissionCodes
     * @return
     */
    public Map<String/*permissionCode*/, Boolean> isHasPermission(List<String> currPermissionCodes) {
        if (MccConfigUtil.useSacAuthenticationV2()) {
            SessionInfo sessionInfo = SessionContext.getCurrentSession();
            int appId = ContextHolder.currentUserLoginAppId();
            return authRemoteService.checkAccountPermissions(sessionInfo.getAccountId(), appId, currPermissionCodes);
        } else {
            return isHasPermissionByQueryAccount(currPermissionCodes);
        }
    }

    @Deprecated
    private Map<String, Boolean> isHasPermissionByQueryAccount(List<String> permissionCodes) {
        Map<String, Boolean> permissionMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(permissionCodes)) {
            return permissionMap;
        }

        List<String> accountPermissionCodes = getCurrentAccountAllPermissionCodes();

        if (CollectionUtils.isEmpty(accountPermissionCodes)) {
            permissionCodes.forEach(code -> {
                permissionMap.put(code, false);
            });
            return permissionMap;
        }

        permissionCodes.forEach(code -> {
            if (accountPermissionCodes.contains(code)) {
                permissionMap.put(code, true);
            } else {
                permissionMap.put(code, false);
            }
        });

        return permissionMap;
    }


    /**
     * 获取登录账号所有权限码
     *
     * @return account info
     * @throws TException Any
     */
    @Degrade(rhinoKey = "AuthService.getCurrentAccountAllPermissions",
            fallBackMethod = "getCurrentAccountAllPermissionCodesFallback",
            timeoutInMilliseconds = 3000,
            ignoreExceptions = CommonLogicException.class)
    @MethodLog(logRequest = true, logResponse = true)
    @CommonMonitorTransaction
    @SuppressWarnings("WeakerAccess")
    @Deprecated
    public List<String> getCurrentAccountAllPermissionCodes() {
        try {
            AccountInfoVo accountInfoVo = authRemoteService.getCurrentAccount();
            if (Objects.isNull(accountInfoVo) || CollectionUtils.isEmpty(accountInfoVo.getRoleList())) {
                return Collections.EMPTY_LIST;
            }

            List<String> permissionCodeList = Lists.newArrayList();
            for (RoleInfoVo roleInfoVo : accountInfoVo.getRoleList()) {
                for (PermissionVo permissionVo : roleInfoVo.getPermissionList()) {
                    if (AppAuthIdConstantsEnum.SHU_GUO_PAI.val() == permissionVo.getAppId()) {
                        permissionCodeList.add(permissionVo.getCode());
                    }
                }
            }
            return permissionCodeList;
        } catch (TException e) {
            throw new CommonRuntimeException("查询权限异常" + e.getMessage());
        }
    }

    private List<DataPermissionVo> getCurrentAccountAllPermissionCodesFallback() {
        throw new CommonRuntimeException("AuthThriftWrapper.getCurrentAccountAllPermissionCodesFallback");
    }

}