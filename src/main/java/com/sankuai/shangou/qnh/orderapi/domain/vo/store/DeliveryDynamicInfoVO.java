package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.DeliveryStatusLogVo;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "配送路径动态信息"
)
@Data
@ApiModel("动态信息信息")
public class DeliveryDynamicInfoVO {

    /**
     * @deprecated 路径日志被包含在配送日志里，用{@link #logs}
     */
    @FieldDoc(
            description = "路径日志", requiredness = Requiredness.REQUIRED
    )
    @Deprecated
    private List<DeliveryStatusLogVo> pathLog;

    /**
     * 配送日志
     */
    private List<DeliveryLogVo> logs;

    @FieldDoc(
            description = "当前配送状态", requiredness = Requiredness.REQUIRED
    )
    private String deliveryStatus;

    @FieldDoc(
            description = "骑手姓名", requiredness = Requiredness.REQUIRED
    )
    private String riderName;

    @FieldDoc(
            description = "骑手电话", requiredness = Requiredness.REQUIRED
    )
    private String riderPhone;

    @FieldDoc(
            description = "配送方式", requiredness = Requiredness.REQUIRED
    )
    private String distributeMethodName;

    @FieldDoc(
            description = "骑手坐标", requiredness = Requiredness.REQUIRED
    )
    private CoordinateVO riderCoordinate;

    @FieldDoc(
            description = "可操作按钮", requiredness = Requiredness.REQUIRED
    )
    private List<Integer> deliveryOperateCode;

    @FieldDoc(
            description = "锁单状态，1锁单中，0未锁单", requiredness = Requiredness.OPTIONAL
    )
    private Integer lockOrderState;
}
