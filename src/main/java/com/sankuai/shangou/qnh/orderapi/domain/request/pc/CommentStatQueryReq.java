package com.sankuai.shangou.qnh.orderapi.domain.request.pc;
// Copyright (C) 2019 Meituan
// All rights reserved

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.saas.common.datetime.DateUtil;
import com.sankuai.shangou.qnh.orderapi.exception.pc.ParamInvalidException;
import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

@TypeDoc(
        description = "评价统计查询请求"
)
@Data
public class CommentStatQueryReq {

    @FieldDoc(
            description = "要查询的门店列表"
    )
    private List<Long> poiIds;

    @FieldDoc(
            description = "渠道id列表"
    )
    private List<Integer> channelIds;

    @FieldDoc(
            description = "查询开始时间"
    )
    private String startTime;

    @FieldDoc(
            description = "查询结束时间"
    )
    private String endTime;


    public void isValid(){
        if (StringUtils.isEmpty(this.startTime)) {
            throw new ParamInvalidException("开始日期不能为空");
        }
        if (StringUtils.isEmpty(this.endTime)) {
            throw new ParamInvalidException("结束日期不能为空");
        }
        Date start = DateUtil.parse(this.startTime,DateUtil.YYYY_MM_DD);
        if (start == null) {
            throw new ParamInvalidException("开始日期格式错误");
        }
        Date end = DateUtil.parse(this.endTime, DateUtil.YYYY_MM_DD);
        if (end == null) {
            throw new ParamInvalidException("结束日期格式错误");
        }
        AssertUtil.isTrue(end.compareTo(start) >= 0, "结束时间必须在开始时间之后");
    }
}
