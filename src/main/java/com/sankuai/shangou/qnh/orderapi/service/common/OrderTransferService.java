package com.sankuai.shangou.qnh.orderapi.service.common;

import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.meituan.shangou.saas.order.platform.enums.DeliveryStatusEnum;
import com.meituan.shangou.saas.order.platform.enums.DistributeStatusEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.sankuai.qnh.ofc.transfer.client.thrift.transferorder.request.RecommendTransferSearchReq;
import com.sankuai.qnh.ofc.transfer.client.thrift.transferorder.response.RecommendTransferSearchResponse;
import com.sankuai.qnh.ofc.transfer.client.thrift.transferorder.service.OrderTransferWarehousePlanService;
import com.sankuai.shangou.qnh.orderapi.context.app.ApiMethodParamThreadLocal;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.OrderListResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.OrderVO;
import com.sankuai.shangou.qnh.orderapi.enums.app.OrderViewStatusEnum;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/7/15
 **/
@Service
@Slf4j
public class OrderTransferService {
    @Resource
    private OrderTransferWarehousePlanService orderTransferWarehousePlanService;

    public void setOrderRecommendTransfer(OrderListResponse response) {
        Long tenantId = null;
        try {
            tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
            if (Objects.isNull(tenantId) || Objects.isNull(response) || CollectionUtils.isEmpty(response.getOrderList())) {
                return;
            }

            if (MccConfigUtil.isNotRecommendTransferOrderTenant(tenantId)) {
                return;
            }

            // 限制推荐订单状态范围（待拣货、待配送等），转单门店存在的无需查询
            List<Long> orderIdList = response.getOrderList().stream().
                    filter(orderVO -> Objects.nonNull(orderVO.getEmpowerOrderId())
                            && Objects.isNull(orderVO.getDispatchShopId())
                            && isShowRecommendTransferOrderViewStatus(orderVO.getViewStatus(), orderVO.getOrderStatus(), orderVO.getPickStatus(), orderVO.getDistributeStatus()))
                    .map(OrderVO::getEmpowerOrderId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(orderIdList)) {
                return;
            }
            RecommendTransferSearchReq request = new RecommendTransferSearchReq();
            request.setTenantId(tenantId);
            request.setOrderIdList(orderIdList.stream()
                    .map(String::valueOf)
                    .collect(Collectors.toList()));
            RecommendTransferSearchResponse result = orderTransferWarehousePlanService.batchSearchRecommendTransferOrder(request);
            if (Objects.isNull(result) || Objects.isNull(result.getCode()) || result.getCode() != 0) {
                log.warn("查询推荐转单失败, request:{}, response:{}", JacksonUtils.toJson(request), JacksonUtils.toJson(result));
                return;
            }

            if (Objects.isNull(result.getData()) || CollectionUtils.isEmpty(result.getData().getRecommendOrderId())) {
                return;
            }

            // 存在转单推荐订单
            for (OrderVO each : response.getOrderList()) {
                if (result.getData().getRecommendOrderId().contains(String.valueOf(each.getEmpowerOrderId()))) {
                    each.setIsRecommendDispatch(Boolean.TRUE);
                }
            }
        } catch (Exception e) {
            log.error("Error occurred while setting order recommend transfer, tenantId: {}, msg: {}", tenantId, e.getMessage(), e);
        }
    }

    private boolean isShowRecommendTransferOrderViewStatus(Integer viewStatus, Integer orderStatus, Integer pickStatus, Integer distributeStatus) {
        // 存在展示状态：推荐展示状态范围如下
        if (viewStatus != null &&
                (viewStatus == OrderViewStatusEnum.WAIT_TO_PICK_ORDER.getCode()
                        || viewStatus == OrderViewStatusEnum.WAIT_TO_RIDER_ACCEPT.getCode()
                        || viewStatus == OrderViewStatusEnum.WAIT_TO_ARRIVE_SHOP.getCode()
                        || viewStatus == OrderViewStatusEnum.WAIT_TO_TAKE_GOODS.getCode()
                )) {
            return true;
        }

        // 订单状态为商家已接单允许展示
        if (Objects.nonNull(orderStatus)
                && OrderStatusEnum.MERCHANT_CONFIRMED.getValue() == orderStatus) {
            return true;
        }

        // 拣货状态为待拣货、拣货中允许展示
        if (Objects.nonNull(pickStatus) &&
                (DeliveryStatusEnum.PICKING.getValue() == pickStatus
                        || DeliveryStatusEnum.WAIT_TO_PICK.getValue() == pickStatus)) {
            return true;
        }

        // 配送状态为待取货、待到店、待接单则允许展示
        if (Objects.nonNull(distributeStatus) &&
                (distributeStatus == DistributeStatusEnum.WAIT_FOR_ASSIGN_RIDER.getValue()
                        || distributeStatus == DistributeStatusEnum.DISTRIBUTE_REJECTED.getValue()
                        || distributeStatus == DistributeStatusEnum.RIDER_ASSIGNED.getValue()
                        || distributeStatus == DistributeStatusEnum.RIDER_REACH_SHOP.getValue()
                        || distributeStatus == DistributeStatusEnum.RIDER_TAKE_GOODS_FAILED.getValue()

                )) {
            return true;
        }

        return false;
    }

}
