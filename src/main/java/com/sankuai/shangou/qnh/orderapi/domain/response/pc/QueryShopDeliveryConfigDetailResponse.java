package com.sankuai.shangou.qnh.orderapi.domain.response.pc;

import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.DeliveryChannelShopDetailVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.SequentialDeliveryStrategyConfigVO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/16
 * desc: 查询门店配送配置详情响应
 */
@Setter
@Getter
public class QueryShopDeliveryConfigDetailResponse {

    private List<DeliveryChannelShopDetailVO> list;

    private int launchOnMerchantConfirm;

    private int launchOnMerchantConfirmDelayTime;

    private int launchOnPickupComplete;

    private int launchOnPickupCompleteDelayTime;

    private int deliveryStrategy;

    private SequentialDeliveryStrategyConfigVO sequentialStrategyConfig;
}
