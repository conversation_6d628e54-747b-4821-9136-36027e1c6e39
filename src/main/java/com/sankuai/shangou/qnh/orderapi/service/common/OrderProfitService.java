package com.sankuai.shangou.qnh.orderapi.service.common;

import com.dianping.lion.client.Lion;
import com.dianping.rhino.annotation.Degrade;
import com.meituan.linz.boot.util.Fun;
import com.meituan.shangou.saas.dto.StatusCodeEnum;
import com.meituan.shangou.saas.order.management.client.dto.request.online.ViewIdCondition;
import com.meituan.shangou.saas.order.management.client.dto.request.revenue.MerchantOrderRevenueDetailRequest;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.meituan.shangou.saas.order.management.client.dto.response.revenue.MerchantOrderListRevenueDetailResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.revenue.OrderRevenueDetailResponse;
import com.meituan.shangou.saas.order.management.client.service.revenue.MerChantRevenueQueryService;
import com.meituan.shangou.saas.order.platform.enums.OrderBizTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.TenantBusinessModeEnum;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse;
import com.sankuai.shangou.qnh.orderapi.enums.app.MccKeyEnum;
import com.sankuai.shangou.qnh.orderapi.exception.app.CommonRuntimeException;
import com.sankuai.shangou.qnh.orderapi.remote.SaasCrmDataRemoteService;
import com.sankuai.shangou.qnh.orderapi.remote.TenantRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/7/15
 **/
@Slf4j
@Service
public class OrderProfitService {
    @Autowired
    private SaasCrmDataRemoteService saasCrmDataWrapper;
    @Resource
    private TenantRemoteService tenantWrapper;
    @Autowired
    private MerChantRevenueQueryService merChantRevenueQueryService;

    public Map<String, SaasCrmDataRemoteService.OrderProfitView> getOrderProfitMapV2(Long tenantId, List<OCMSOrderVO> orderVOS) {
        try {
            // 获取租户业务模式，只处理便利店租户
            List<OCMSOrderVO> orders = needOrderProfit(tenantId, orderVOS);
            if (CollectionUtils.isEmpty(orders)) {
                return Collections.emptyMap();
            }

            Map<String, SaasCrmDataRemoteService.OrderProfitView> profitMap = saasCrmDataWrapper.queryNetProfitV2(orders);
            log.info("查询订单毛利 v2, orders:{},profitMap:{}", Fun.map(orders, OCMSOrderVO::getViewOrderId), profitMap);
            return profitMap;
        }
        catch (Exception e) {
            log.warn("获取门店预计毛利失败 v2, tenantId:{},orderVOS:{}", tenantId, Fun.map(orderVOS, OCMSOrderVO::getViewOrderId));
            return Collections.emptyMap();
        }
    }
    private List<OCMSOrderVO> needOrderProfit(Long tenantId, List<OCMSOrderVO> orderVOS) {
        String channels = Lion.getConfigRepository().get(MccKeyEnum.SHOW_ORDER_NET_PROFIT_CHANNEL.key,
                String.valueOf(OrderBizTypeEnum.MEITUAN_WAIMAI.getValue()));
        if (StringUtils.isBlank(channels)) {
            return Collections.emptyList();
        }
        List<String> channelList = Arrays.asList(channels.trim().split(","));
        List<OCMSOrderVO> orders = Fun.filter(orderVOS, orderVO -> channelList.contains(String.valueOf(orderVO.getOrderBizType())));
        if (CollectionUtils.isNotEmpty(orders)) {
            TenantBusinessModeEnum tenantBizMode = tenantWrapper.getTenantBizMode(tenantId);
            log.info("tenantId:{}; tenantBizMode: {}", tenantId, tenantBizMode);
            if (TenantBusinessModeEnum.CONVENIENCE_STORE.equals(tenantBizMode) || TenantBusinessModeEnum.MTSG_FLAGSHIP_STORE.equals(tenantBizMode)) {
                return orders;
            }
        }
        return Collections.emptyList();
    }

    public List<OrderRevenueDetailResponse> getOrderListRevenueDetail4TenantAndViewIds(Long tenantId,
                                                                                        List<OCMSOrderVO> orderVOS) {

        if(CollectionUtils.isEmpty(orderVOS)){
            return Collections.emptyList();
        }

        MerchantOrderRevenueDetailRequest request = new MerchantOrderRevenueDetailRequest();
        request.setTenantId(tenantId);
        List<ViewIdCondition> conditions = orderVOS.stream().map(order -> ViewIdCondition.builder()
                        .orderBizType(order.getOrderBizType())
                        .viewOrderId(order.getViewOrderId()).build())
                .collect(Collectors.toList());
        request.setViewIdConditionList(conditions);

        try {
            MerchantOrderListRevenueDetailResponse revenueDetailResponse = orderListRevenueDetail(request).getData();
            return Optional.ofNullable(revenueDetailResponse)
                    .map(MerchantOrderListRevenueDetailResponse::getOrderListRevenueDetailResponse)
                    .orElse(Collections.emptyList());
        } catch (Exception e) {
            // 查询营收数据异常、返回空、前端不显示订单零售金额相关数据
            return Collections.emptyList();
        }
    }


    @Degrade(rhinoKey = "OrderService.orderListRevenueDetail", fallBackMethod = "orderListRevenueDetailFallback",
            timeoutInMilliseconds = 1000)
    public CommonResponse<MerchantOrderListRevenueDetailResponse> orderListRevenueDetail(MerchantOrderRevenueDetailRequest request) {
        CommonResponse<MerchantOrderListRevenueDetailResponse> response = new CommonResponse<>();
        try {
            log.info("OrderService.orderRevenueDetail->调用MerChantRevenueQueryService.orderRevenueDetail "
                    + "request:{}", request);
            MerchantOrderListRevenueDetailResponse merChantRevenueResponse = merChantRevenueQueryService
                    .orderListRevenueDetail(request);
            log.info("OrderService.orderRevenueDetail->调用调用MerChantRevenueQueryService.orderRevenueDetail "
                    + "response:{}", merChantRevenueResponse);
            if(merChantRevenueResponse == null){
                response.setCode(StatusCodeEnum.FAIL.getCode());
                response.setMessage(StatusCodeEnum.FAIL.getMessage());
                return response;
            }
            if (Integer.valueOf(com.meituan.shangou.saas.order.management.client.enums.StatusCodeEnum.SUCCESS.getCode())
                    .equals(merChantRevenueResponse.getStatus())) {
                response.setCode(StatusCodeEnum.SUCCESS.getCode());
                response.setMessage(StatusCodeEnum.SUCCESS.getMessage());
                response.setData(merChantRevenueResponse);
                return response;
            }
            response.setCode(merChantRevenueResponse.getStatus());
            response.setMessage(merChantRevenueResponse.getMsg());
            return response;
        } catch (Exception e) {
            log.error("OrderService.orderRevenueDetail->merChantRevenueQueryService.orderRevenueDetail"
                    + " error", e);
            throw new CommonRuntimeException(e);
        }
    }
}
