package com.sankuai.shangou.qnh.orderapi.remote;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.Lion;
import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.mtrace.thread.pool.ExecutorServiceTraceWrapper;
import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.meituan.reco.pickselect.common.domain.orderTrack.TrackOpType;
import com.meituan.shangou.saas.common.enums.OrderCanOperateItem;
import com.meituan.shangou.saas.dto.StatusCodeEnum;
import com.meituan.shangou.saas.dto.request.ocms.CreateInvoiceRequest;
import com.meituan.shangou.saas.dto.request.ocms.OCMSConfirmOrderRequest;
import com.meituan.shangou.saas.dto.request.ocms.OCMSOrderCancelRequest;
import com.meituan.shangou.saas.dto.request.ocms.OCMSOrderPartRefundRequest;
import com.meituan.shangou.saas.dto.request.ocms.OCMSOrderQueryAdjustRecordRequest;
import com.meituan.shangou.saas.dto.response.ocms.CreateInvoiceResponse;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderItemMoneyRefundCheckModel;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderModel;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderOffShelfGoodsModel;
import com.meituan.shangou.saas.o2o.dto.model.OCMSOrderAdjustLog;
import com.meituan.shangou.saas.o2o.dto.model.OCMSOrderKey;
import com.meituan.shangou.saas.o2o.dto.model.OCMSOrderPartRefundGiftBagModel;
import com.meituan.shangou.saas.o2o.dto.model.OCMSOrderPartRefundProductModel;
import com.meituan.shangou.saas.o2o.dto.request.*;
import com.meituan.shangou.saas.o2o.dto.request.ReturnGoodsAuditRequest;
import com.meituan.shangou.saas.o2o.dto.response.*;
import com.meituan.shangou.saas.o2o.service.BizOrderThriftService;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.OrderSourceEnum;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.meituan.shangou.saas.service.ocms.OCMSOrderOperateThriftService;
import com.meituan.shangou.saas.service.ocms.OCMSOrderThriftService;
import com.meituan.shangou.saas.tenant.highlevelclient.QnhBizModes;
import com.meituan.shangou.saas.tenant.thrift.ConfigThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.enums.ConfigItemEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiOperationModeEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.TenantBusinessModeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.config.ConfigDto;
import com.meituan.shangou.saas.tenant.thrift.dto.config.request.ConfigQueryRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.config.response.TenantConfigResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.sac.dto.request.authenticate.AccountAuthPermissionsRequest;
import com.meituan.shangou.sac.dto.response.authenticate.AccountAuthPermissionsResponse;
import com.meituan.shangou.sac.thrift.authenticate.AuthenticateService;
import com.sankuai.meituan.shangou.empower.finance.dto.request.MultiShopOrderFinanceQueryRequest;
import com.sankuai.meituan.shangou.empower.finance.dto.request.OrderFinanceQueryRequest;
import com.sankuai.meituan.shangou.empower.finance.dto.responce.OrderFinanceQueryResponse;
import com.sankuai.meituan.shangou.empower.finance.service.OrderFinanceThriftService;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.StoreSkuBaseInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.UpdateStoreStockRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.SkuStoreStockThriftService;
import com.sankuai.meituan.shangou.empower.settlement.dto.model.OrderBizTypeViewIdPair;
import com.sankuai.meituan.shangou.empower.settlement.dto.request.voucher.VoucherProductQueryCanAdjustRequest;
import com.sankuai.meituan.shangou.empower.settlement.dto.response.voucher.VoucherProductQueryCanAdjustResponse;
import com.sankuai.meituan.shangou.empower.settlement.services.SettlementVoucherThriftService;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.qnh.orderapi.context.app.ApiMethodParamThreadLocal;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.*;
import com.sankuai.shangou.qnh.orderapi.domain.dto.app.IdentityInfo;
import com.sankuai.shangou.qnh.orderapi.domain.dto.app.User;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.CancelOrderRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.ExchangeRefundImportCheckReq;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.*;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.ExchangeRefundImportCheckResponse;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.UiOption;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.PartRefundGiftBagVO;
import com.sankuai.shangou.qnh.orderapi.enums.app.ResultCodeEnum;
import com.sankuai.shangou.qnh.orderapi.service.pc.PoiService;
import com.sankuai.shangou.qnh.orderapi.utils.pc.MccConfigUtil;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ResponseHandler;
import com.sankuai.shangou.qnh.orderapi.utils.pc.RpcInvoker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.sankuai.shangou.qnh.orderapi.constant.pc.MessageConstant.SYSTEM_ERROR;
/**
 * <AUTHOR>
 * @createTime 2020/4/19
 * @description
 */

@Rhino
@Slf4j
public class OrderBizRemoteService {

    @Autowired
    private BizOrderThriftService bizOrderThriftService;

    @Resource
    OCMSOrderOperateThriftService ocmsOrderOperateThriftService;

    @Autowired
    private OCMSOrderThriftService ocmsOrderThriftService;

    @Autowired
    private PoiRemoteService poiRemoteService;

    @Autowired
    private SettlementVoucherThriftService settlementVoucherThriftService;

    @Autowired
    private OrderFinanceThriftService orderFinanceThriftService;

    @Autowired
    private TenantRemoteService tenantRemoteService;

    @Resource
    private SkuStoreStockThriftService.Iface skuStoreStockThriftService;

    @Autowired
    private QnhBizModes qnhBizModes;

    @Autowired
    private WmsConfigRemoteService wmsConfigRemoteService;

    @Resource
    private ConfigThriftService configThriftService;
    @Autowired
    private PoiRemoteService poiClient;
    @Resource
    private AuthRemoteService authRemoteService;
    @Autowired
    private AuthenticateService authenticateService;;

    private static final String ORDER_SEARCH_FULL_REFUND = "ORDER_SEARCH_FULL_REFUND";

    private static final String ORDER_SEARCH_PARTIAL_REFUND = "ORDER_SEARCH_PARTIAL_REFUND";

    private static final int BATCH_ORDER_SIZE = 50;

    private ExecutorService bizClientExecutor = new ExecutorServiceTraceWrapper(
            new ThreadPoolExecutor(20, 20, 10, TimeUnit.SECONDS,
                    new LinkedBlockingQueue<>(2000),
                    new ThreadFactoryBuilder().setNameFormat("OrderBizClientThreadPool-%d").build()
            )
    );

    public CommonDataBO<List<OCMSOrderAdjustLog>> queryOrderAdjustLog(Long tenantId, Integer channelId, String orderId) {
        OCMSOrderQueryAdjustRecordRequest request = new OCMSOrderQueryAdjustRecordRequest();
        request.setTenantId(tenantId);
        request.setOrderBizType(ChannelOrderConvertUtils.sourceMid2Biz(channelId));
        request.setViewOrderId(orderId);
        CommonDataBO<List<OCMSOrderAdjustLog>> dataBO = new CommonDataBO<List<OCMSOrderAdjustLog>>();
        try {
            CommonDataResponse<List<OCMSOrderAdjustLog>> response = ocmsOrderThriftService.queryAdjustOrderRecord(request);

            if (response == null || response.getStatus() == null || StatusCodeEnum.SUCCESS.getCode() != response.getStatus().getCode()) {
                dataBO.setSuccess(Boolean.FALSE);
                dataBO.setMessage(SYSTEM_ERROR);
            } else {
                dataBO.setSuccess(Boolean.TRUE);
                dataBO.setData(response.getData());
            }
        } catch (TException tex) {
            log.error("ocmsOrderThriftService.queryAdjustOrderRecord TException request:{}", request, tex);
            dataBO.setSuccess(Boolean.FALSE);
            dataBO.setMessage(SYSTEM_ERROR);
        }
        return dataBO;
    }

    /**
     * 查询订单可操作列表
     *
     * @param tenantId  租户ID
     * @param orderKeys 渠道订单号
     * @param checkItems 注：orderbiz已不处理 170 值
     * @return 可操作列表
     */
    public Map<OCMSOrderKey, List<Integer>> queryOrderOperateItems(Long tenantId, List<OCMSOrderKey> orderKeys, List<Integer> checkItems) {

        boolean orderAdjustFromSettlement = MccConfigUtil.orderAdjustFromSettlement();
        Integer orderAdjustOperateItem = OrderCanOperateItem.ORDER_ADJUST.getValue();

        // 若未传入可操作项，则查询所有
        if (CollectionUtils.isEmpty(checkItems)) {
            checkItems = Arrays.stream(OrderCanOperateItem.values())
                    .map(OrderCanOperateItem::getValue)
                    .collect(Collectors.toList());
        }

        // 通过订单系统查询的可操作项
        List<Integer> checkItemFromOrderBiz = new ArrayList<>(checkItems);
        if (orderAdjustFromSettlement) {
            checkItemFromOrderBiz.remove(orderAdjustOperateItem);
        }

        Map<OCMSOrderKey, List<Integer>> orderKeyListMap = queryOrderOperateItemsFromOrderBiz(tenantId, orderKeys, checkItemFromOrderBiz);
        log.info("查询订单系统订单可操作项 tenantId:{},orderKeys:{},checkItems:{},orderKeyListMap:{}", tenantId, orderKeys, checkItems, orderKeyListMap);

        // 订单调整若查询结算系统，则是否可调整查询结算系统
        //判断租户所属业态是否需要查询
        boolean needQuerySettlement = true;
        try {
            String bizMode = qnhBizModes.qnhBizMode(tenantId);
            needQuerySettlement = MccConfigUtil.hitConfigKey(bizMode,MccConfigUtil.getQuerySettlementBizModes());
        } catch (Exception e) {
            log.info("查询租户bizMode失败");
        }
        if (orderAdjustFromSettlement && checkItems.contains(orderAdjustOperateItem) && needQuerySettlement) {
            Map<OCMSOrderKey, Boolean> orderCanAdjustMap = queryOrderCanAdjustFromSettlement(tenantId, orderKeys);
            log.info("查询结算系统订单是否可调整结果 orderKeys:{},orderCanAdjustMap :{}", orderKeys, orderCanAdjustMap);
            // 遍历订单的可操作项，将订单可调整的结果覆盖为结算系统的结果
            orderCanAdjustMap.forEach((orderKey, canAdjust) -> {
                if (Boolean.TRUE.equals(canAdjust)) {
                    if (orderKeyListMap.containsKey(orderKey)) {
                        orderKeyListMap.get(orderKey).add(orderAdjustOperateItem);
                    }
                    else {
                        orderKeyListMap.put(orderKey, Arrays.asList(orderAdjustOperateItem));
                    }
                }
            });
        }
        return orderKeyListMap;
    }


    @Degrade(rhinoKey = "OrderBizRemoteService.queryOrderDetail",
            fallBackMethod = "queryOrderDetailFallback",
            timeoutInMilliseconds = 2000)
    public BizOrderModel queryOrderDetail(Long tenantId, Long storeId, Integer channelId, String viewOrderId) throws TException {

        BizOrderQueryRequest request = new BizOrderQueryRequest();
        request.setTenantId(tenantId);
        request.setViewOrderId(viewOrderId);
        request.setShopId(storeId);
        request.setOrderBizType(ChannelOrderConvertUtils.convertBizType(channelId));
        request.setContainsComposeSku(true);
        log.info("start invoke bizOrderThriftService.query, request: {}", request);
        BizOrderQueryResponse response = bizOrderThriftService.query(request);
        log.info("end invoke bizOrderThriftService.query, response: {}", response);

        if (!Objects.equals(response.getStatus().getCode(),ResultCode.SUCCESS.getCode())) {
            throw new ThirdPartyException("调用订单服务查询订单明细失败");
        }

        return response.getBizOrderModel();
    }

    public BizOrderModel queryOrderDetailFallback(Long tenantId, Long storeId, Integer orderSource, String viewOrderId) throws TException {
        log.warn("OrderBizRemoteService.queryOrderDetail 发生降级");
        return null;
    }




    private Map<OCMSOrderKey, Boolean> queryOrderCanAdjustFromSettlement(Long tenantId, List<OCMSOrderKey> orderKeys) {
        // 只保留结算系统所支持渠道的订单
        Set<OrderBizTypeViewIdPair> pairs = orderKeys.stream()
                .filter(orderKey -> MccConfigUtil.settlementSupportChannelIds().contains(orderKey.getChannelType()))
                .map(orderKey -> new OrderBizTypeViewIdPair(ChannelOrderConvertUtils.sourceMid2Biz(orderKey.getChannelType()), orderKey.getChannelOrderId()))
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(pairs)) {
            log.info("查询订单是否可调整，只保留结算系统所支持渠道的订单，订单为空 tenantId:{},orderKeys:{}", tenantId, orderKeys);
            return Collections.emptyMap();
        }

        // 查询结算系统，订单是否可以调整（未查出来，默认为不调整）
        VoucherProductQueryCanAdjustRequest request = new VoucherProductQueryCanAdjustRequest();
        request.setTenantId(tenantId);
        request.setOrderBizTypeViewIdPairSet(pairs);
        VoucherProductQueryCanAdjustResponse response = RpcInvoker.invokeCatch(() -> settlementVoucherThriftService.queryVoucherProductCanAdjust(request));
        if (response == null || response.getVoucherProductCanAdjustResult() == null) {
            return Collections.emptyMap();
        }
        //还需要校验租户是否开启了摊位采购 未开启返回空
        if(!isPurchaseByBooth(tenantId)){
            log.info("queryOrderCanAdjustFromSettlement tenant is not purchaseByBooth tenantId: {}", tenantId);
            return Collections.emptyMap();
        }

        // 构造返回值
        Map<OCMSOrderKey, Boolean> orderKeyWithResultMap = new HashMap<>();
        response.getVoucherProductCanAdjustResult().forEach((orderPair, canAdjust) -> {
            OCMSOrderKey orderKey = new OCMSOrderKey();
            orderKey.setChannelType(ChannelOrderConvertUtils.sourceBiz2Mid(orderPair.getOrderBizType()));
            orderKey.setChannelOrderId(orderPair.getViewOrderId());
            orderKeyWithResultMap.put(orderKey, canAdjust);
        });
        return orderKeyWithResultMap;
    }

    private boolean isPurchaseByBooth(Long tenantId) {
        try {
            ConfigQueryRequest configQueryRequest = new ConfigQueryRequest();
            configQueryRequest.setTenantId(tenantId);
            configQueryRequest.setSubjectId(tenantId);
            configQueryRequest.setConfigId(ConfigItemEnum.PURCHASE_BY_BOOTH.getKey());
            TenantConfigResponse tenantConfigResponse = configThriftService.queryTenantConfig(configQueryRequest);
            log.info("queryTenantConfig isPurchaseByBooth tenantId:{},resp:{}", tenantId, tenantConfigResponse);
            if (Objects.nonNull(tenantConfigResponse)
                    && Objects.nonNull(tenantConfigResponse.getStatus())
                    && com.sankuai.meituan.reco.store.management.enums.ResultCodeEnum.SUCCESS.getCode() == tenantConfigResponse.getStatus().getCode()) {
                ConfigDto config = tenantConfigResponse.getConfig();
                if (Objects.nonNull(config) && org.apache.commons.lang3.StringUtils.isNotBlank(config.getConfigContent())) {
                    String configContent = config.getConfigContent();
                    return ConfigItemEnum.PURCHASE_BY_BOOTH.isMainConfigYesStr(configContent);
                }
            }
        } catch (Exception e) {
            log.info("queryTenantConfig isPurchaseByBooth is error tenantId: {}, e= ", tenantId, e);
        }
        return Boolean.FALSE;
    }

    /**
     * 查询订单可操作列表
     *
     * @param tenantId  租户ID
     * @param orderKeys 渠道订单号
     * @return 可操作列表
     */
    public Map<OCMSOrderKey, List<Integer>> queryOrderOperateItemsFromOrderBiz(Long tenantId, List<OCMSOrderKey> orderKeys, List<Integer> operateItems) {
        if (CollectionUtils.isEmpty(orderKeys)) {
            return Collections.emptyMap();
        }
        int maxSize = MccConfigUtil.getQueryOrderOperateItemsMaxSize();
        if(orderKeys.size() > maxSize){
            log.error("queryOrderOperateItemsFromOrderBiz查询数量超过最大限制, tenantId:{}, orderKeys:{}, maxSize:{}", tenantId, orderKeys.size(), maxSize);
            return Collections.emptyMap();
        }
        Map<OCMSOrderKey, List<Integer>> result = new HashMap<>();
        List<List<OCMSOrderKey>> partition = Lists.partition(orderKeys, BATCH_ORDER_SIZE);
        partition.forEach(list -> {
            OCMSOperateCheckRequest request = new OCMSOperateCheckRequest();
            request.setTenantId(tenantId);
            request.setOrderList(list);
            request.setToCheckOperateItems(operateItems);
            OCMSOperateCheckResponse response = RpcInvoker.invokeCatch(() -> ocmsOrderOperateThriftService.checkOrderCouldOperateItems(request));
            if (Objects.nonNull(response) && Objects.nonNull(response.getCouldOperateItems())) {
                result.putAll(response.getCouldOperateItems());
            }
        });

        return result;
    }

    public CommonResultBO tenantPartRefund(PartRefundRequest request) {
        //处理缺货下架商品
        dealOffShelfGoods(request);

        try {
            OCMSOrderPartRefundRequest bizRequest = buildPartRefundRequest(request);
            CommonResponse response = ocmsOrderOperateThriftService.tenantPartRefund(bizRequest);
            log.info("商家部分退款接口调用request：{}, response:{}", bizRequest, response);
            if (success(response)){
                //清空库存
                stockReset(request);
                return CommonResultBO.builder().success(true).message(StatusCodeEnum.SUCCESS.getMessage()).build();
            }
            return  CommonResultBO.builder().success(false).message(responseMsg(response)).build();
        }catch (Exception e){
            log.error("orderbiz tenantPartRefund TException request:{}", request, e);
            return CommonResultBO.builder().success(false).message(SYSTEM_ERROR).build();
        }
    }

    private void dealOffShelfGoods(PartRefundRequest request) {
        try {
            List<PartRefundRequest.PartRefundItem> refundGoodsTakenOffVOList = request.getRefundItems().stream()
                    .filter(item -> BooleanUtils.isTrue(item.getOffShelfFlag())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(refundGoodsTakenOffVOList)) {
                return;
            }

            Long tenantId = ContextHolder.currentUserTenantId();
            Long operatorId = ContextHolder.currentUid();
            Integer appId = ContextHolder.currentUserLoginAppId();
            String operator = ContextHolder.currentUserName();

            BizOrderOffShelfGoodsRequest offShelfGoodsRequest = new BizOrderOffShelfGoodsRequest();

            offShelfGoodsRequest.setAppId(appId);
            offShelfGoodsRequest.setOperatorName(operator);
            offShelfGoodsRequest.setViewOrderId(request.getOrderId());
            offShelfGoodsRequest.setTenantId(tenantId);
            offShelfGoodsRequest.setShopId(Long.valueOf(request.getPoiId()));
            offShelfGoodsRequest.setUid(operatorId);
            offShelfGoodsRequest.setOrderBizType(DynamicOrderBizType.channelId2OrderBizTypeValue(Integer.valueOf(request.getChannelId())));
            offShelfGoodsRequest.setTrackOpType(TrackOpType.ORDER_PART_REFUND_SOLD_OUT_OFF_SHELF);
            offShelfGoodsRequest.setOffShelfGoodsList(refundGoodsTakenOffVOList.stream().map(item ->
                            new BizOrderOffShelfGoodsModel(item.getSku(), item.getCustomSkuId(), item.getSkuName()))
                    .collect(Collectors.toList()));

            bizClientExecutor.submit(() -> {
                bizOrderThriftService.offShelfOrderGoods(offShelfGoodsRequest);
            });
        } catch (Exception e) {
            log.info("执行商品下架操作异常",e);
        }
    }

    private void stockReset(PartRefundRequest request) {
        try {
            final List<PartRefundRequest.PartRefundItem> refundGoodsSoldOutVOList = request.getRefundItems().stream().filter(item -> BooleanUtils.isTrue(item.getStockResetFlag())).collect(Collectors.toList());
            //非歪马租户
            if (!MccConfigUtil.getDrunkHorseTenantId().contains(ContextHolder.currentUserTenantId()) && CollectionUtils.isNotEmpty(refundGoodsSoldOutVOList)) {
                setSKuStoreStockZero(ContextHolder.currentUserTenantId(), Long.valueOf(request.getPoiId()), ContextHolder.currentUserStaffId(), ContextHolder.currentUserName(), refundGoodsSoldOutVOList);
            }

        } catch (Exception e) {
            log.error("库存清空报错,", e);
        }
    }

    public String setSKuStoreStockZero(Long tenantId, Long shopId, Long operatorId, String operateName,
                                       List<PartRefundRequest.PartRefundItem> refundGoodsSoldOutVOList) {
        //查询门店是否开启外部库存对接
        Integer stockDockingConfig = wmsConfigRemoteService.queryStockDockingConfig(ContextHolder.currentUserTenantId(), shopId);

        if(WmsConfigRemoteService.STOCK_BY_POS.equals(stockDockingConfig)) {
            log.info("当前门店开启外部库存对接，不进行库存清空");
            return "当前门店开启外部库存对接，不进行库存清空";
        }

        //如果是非无限库存租户一定能修改库存成功且不会自动恢复，如果是无限库存租户，则能设置库存成功且自动恢复
        //自定义库存标记 0：非自定义库存 1：自定义库存,非无限库存模式下传0
        int customizeStockFlag = 1;
        //第二天是否自动恢复无限库存 0-不自动恢复 1-自动恢复
        int autoResumeInfiniteStock = 1;
        TenantBusinessModeEnum tenantBizMode = tenantRemoteService.getTenantBizMode(tenantId);
        // 新供给门店不自动恢复为无限库存
        if (TenantBusinessModeEnum.CONVENIENCE_STORE.equals(tenantBizMode) || TenantBusinessModeEnum.MEDICINE_UNMANNED_WAREHOUSE.equals(tenantBizMode)) {
            autoResumeInfiniteStock = NumberUtils.INTEGER_ZERO;
        }
        // 菜大全迁移到新供给后，需要通过租户的配置，决定是否自动恢复为无线库存
        if(TenantBusinessModeEnum.CONVENIENCE_STORE.equals(tenantBizMode)){
            autoResumeInfiniteStock = tenantRemoteService.getTenantResumeInfiniteStock(tenantId) ? NumberUtils.INTEGER_ONE : NumberUtils.INTEGER_ZERO;
        }
        UpdateStoreStockRequest request = new UpdateStoreStockRequest();
        request.setTenantId(tenantId);
        request.setOperatorId(operatorId);
        request.setOperatorName(operateName);
        request.setCustomizeStockFlag(customizeStockFlag);
        request.setAutoResumeInfiniteStock(autoResumeInfiniteStock);
        Set<StoreSkuBaseInfoDTO> storeSkuBaseInfoDTOS = new TreeSet<>();
        for (PartRefundRequest.PartRefundItem vo : refundGoodsSoldOutVOList) {
            StoreSkuBaseInfoDTO dto = new StoreSkuBaseInfoDTO();
            dto.setSkuCode(vo.getSku());
            dto.setStoreId(shopId);
            storeSkuBaseInfoDTOS.add(dto);
        }
        request.setStoreSkuList(new ArrayList<>(storeSkuBaseInfoDTOS));
        try {
            log.info("退款原因为商品已售完，需要修改库存为0  skuStoreStockThriftService.updateStoreStock request:{}", request);
            com.sankuai.meituan.shangou.empower.ocms.thrift.dto.CommonResponse response = skuStoreStockThriftService.updateStoreStock(request);
            log.info("退款原因为商品已售完，需要修改库存为0  skuStoreStockThriftService.updateStoreStock response:{}", response);
            if (ResultCode.SUCCESS.getCode() != response.getCode()) {
                return "退款成功，库存修改失败";
            }
        } catch (TException e) {
            log.error("updateStoreStock error,需要手动设置库存为0,request{}", request, e);
        }
        return "退款成功，库存已修改为0";
    }

    public CommonResultBO tenantCancelOrder(RefundRequest request) {
        try {
            OCMSOrderCancelRequest bizRequest = buildCancelOrderRequest(request);
            CommonResponse response = ocmsOrderOperateThriftService.tenantCancelOrder(bizRequest);
            log.info("商家全单取消接口调用request：{}, response:{}", bizRequest, response);
            if (success(response)){
                return CommonResultBO.builder().success(true).message(StatusCodeEnum.SUCCESS.getMessage()).build();
            }
            return  CommonResultBO.builder().success(false).message(responseMsg(response)).build();
        }catch (Exception e){
            log.error("orderbiz tenantCancelOrder TException request:{}", request, e);
            return CommonResultBO.builder().success(false).message(SYSTEM_ERROR).build();
        }
    }

    private OCMSOrderCancelRequest buildCancelOrderRequest(RefundRequest request) {
        OCMSOrderCancelRequest bizRequest = new OCMSOrderCancelRequest();
        bizRequest.setOperatorUserId(ContextHolder.currentUserStaffId());
        bizRequest.setOperatorUserName(ContextHolder.currentUserName());
        bizRequest.setAppId(String.valueOf(ContextHolder.currentUserLoginAppId()));
        bizRequest.setOperatorAccountId(ContextHolder.currentUid());
        bizRequest.setOrderBizType(ChannelOrderConvertUtils.sourceMid2Biz(
                NumberUtils.toInt(request.getChannelId())
        ));
        bizRequest.setReason(request.getReason());
        bizRequest.setReasonCode(request.getReasonCode());
        bizRequest.setTenantId(ContextHolder.currentUserTenantId());
        bizRequest.setViewOrderId(request.getOrderId());
        return bizRequest;
    }


    private String responseMsg(CommonResponse response) {
        return response != null && response.getStatus() != null ? response.getStatus().getMessage() : StatusCodeEnum.FAIL.getMessage();
    }

    private boolean success(CommonResponse response) {
        return response != null && response.getStatus() != null
                && Objects.equals(response.getStatus().getCode(), StatusCodeEnum.SUCCESS.getCode());
    }
    private boolean success(OCMSRefundReasonListResponse response) {
        return response != null && response.getStatus() != null
                && Objects.equals(response.getStatus().getCode(), StatusCodeEnum.SUCCESS.getCode());
    }

    private OCMSOrderPartRefundRequest buildPartRefundRequest(PartRefundRequest request) {
        OCMSOrderPartRefundRequest bizRequest = new OCMSOrderPartRefundRequest();
        bizRequest.setOperatorUserId(ContextHolder.currentUserStaffId());
        bizRequest.setOperatorUserName(ContextHolder.currentUserName());
        bizRequest.setAppId(String.valueOf(ContextHolder.currentUserLoginAppId()));
        bizRequest.setOperatorAccountId(ContextHolder.currentUid());
        bizRequest.setOrderBizType(
                ChannelOrderConvertUtils.sourceMid2Biz(
                    NumberUtils.toInt(request.getChannelId())
                ));
        bizRequest.setPartRefundProductModelList(request.getRefundItems().stream().map(refundItem->{
            OCMSOrderPartRefundProductModel model = new OCMSOrderPartRefundProductModel();
            model.setCount(NumberUtils.toInt(refundItem.getCount()));
            model.setCustomSkuId(refundItem.getCustomSkuId());
            model.setCustomerSpuId(refundItem.getCustomerSpuId());
            model.setSkuId2(refundItem.getSku());
            model.setSkuName(refundItem.getSkuName());
            model.setOrderItemId(refundItem.getOrderItemId());
            if(StringUtils.isNotBlank(refundItem.getExtCustomSkuId())){
                model.setExtCustomSkuId(refundItem.getExtCustomSkuId());
            }
            return model;
        }).collect(Collectors.toList()));
        bizRequest.setReason(request.getReason());
        bizRequest.setReasonCode(request.getReasonCode());
        bizRequest.setShopId(NumberUtils.toLong(request.getPoiId()));
        bizRequest.setTenantId(ContextHolder.currentUserTenantId());
        bizRequest.setViewOrderId(request.getOrderId());
        bizRequest.setRequestId(System.currentTimeMillis() + request.getOrderId());
        return bizRequest;
    }


    public CommonResultBO confirmOrder(String orderId, int orderBizType) {
        OCMSConfirmOrderRequest request = new OCMSConfirmOrderRequest();
        request.setOperatorUserId(ContextHolder.currentUserStaffId());
        request.setOperatorUserName(ContextHolder.currentUserName());
        request.setOrderBizType(orderBizType);
        request.setTenantId(ContextHolder.currentUserTenantId());
        request.setViewOrderId(orderId);
        try {
            CommonResponse response = ocmsOrderOperateThriftService.tenantConfirmOrder(request);
            if (success(response)){
                return CommonResultBO.builder().success(true)
                        .message(StatusCodeEnum.SUCCESS.getMessage())
                        .build();
            }
            return  CommonResultBO.builder().success(false)
                    .message(responseMsg(response))
                    .build();
        } catch (Exception e) {
            log.error("商家确认订单失败:{}", orderId, e);
            return  CommonResultBO.builder().success(false).message("商家确认订单异常:" + e.getMessage()).build();
        }
    }

    public OrderFuseFinanceDetailBO queryFinanceDetail(Long orderId, Long shopId, Long tenantId, int type) {
        OrderFinanceQueryRequest request = OrderFinanceQueryRequest.builder()
                .type(type)
                .tenantId(tenantId)
                .shopId(shopId)
                .ids(Collections.singletonList(orderId))
                .includeDetail(true)
                .build();
        try {
            OrderFinanceQueryResponse response = orderFinanceThriftService.queryOrderFinanceInfo(request);
            if (!Objects.equals(response.getStatus().getCode(), StatusCodeEnum.SUCCESS.getCode()) ||
                    CollectionUtils.isEmpty(response.getOrderFinanceModelList())) {
                return null;
            }
            return new OrderFuseFinanceDetailBO(response.getOrderFinanceModelList().get(0));
        } catch (Exception e) {
            log.error("查询订单财务数据失败:{}", orderId, e);
            return null;
        }
    }

    public List<OrderFuseFinanceDetailBO> queryMultiShopFinanceDetail(List<Long> orderIdList, List<Long> shopIdList, Long tenantId, int type) {
        MultiShopOrderFinanceQueryRequest request = MultiShopOrderFinanceQueryRequest.builder()
                .type(type)
                .tenantId(tenantId)
                .shopIdList(shopIdList)
                .ids(orderIdList)
                .includeDetail(true)
                .build();
        try {
            log.info("查询订单财务数据请求,request:{}", request);
            OrderFinanceQueryResponse response = orderFinanceThriftService.queryMultiShopOrderFinanceInfo(request);
            log.info("查询订单财务数据请求,response:{}", response);
            if (!Objects.equals(response.getStatus().getCode(), StatusCodeEnum.SUCCESS.getCode()) ||
                    CollectionUtils.isEmpty(response.getOrderFinanceModelList())) {
                return null;
            }
            return response.getOrderFinanceModelList().stream().map(OrderFuseFinanceDetailBO::new).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询订单财务数据失败:{}", orderIdList, e);
            return null;
        }
    }

    public List<BizOrderItemMoneyRefundCheckModel> moneyRefundCheck(OrderMoneyRefundCheckRequest request) {
        try {
            BizOrderMoneyRefundCheckRequest moneyRefundCheckRequest = request.convertToBizOrderMoneyRefundCheckRequest();
            moneyRefundCheckRequest.setTenantId(ContextHolder.currentUserTenantId());
            BizOrderMoneyRefundCheckResponse response = ocmsOrderOperateThriftService.moneyRefundCheck(moneyRefundCheckRequest);
            log.info("moneyRefundCheck request:{}, response:{}", moneyRefundCheckRequest, response);
            if (response == null || response.getStatus() == null) {
                throw StatusCodeEnum.FAIL.toBizException("查询金额退可退差商品列表接口返回空");
            }
            if (!Objects.equals(response.getStatus().getCode(), StatusCodeEnum.SUCCESS.getCode())) {
                throw StatusCodeEnum.FAIL.toBizException(response.getStatus().getMessage());
            }
            return response.getBizOrderItemMoneyRefundCheckModelList();
        } catch (TException e) {
            log.error("moneyRefundCheck error request:{}", request, e);
            throw StatusCodeEnum.FAIL.toBizException("查询金额退可退差商品列表异常");
        }
    }

    public CommonResultBO moneyRefund(OrderMoneyRefundRequest request) {
        try {
            BizOrderMoneyRefundRequest moneyRefundRequest = request.convertToBizOrderMoneyRefundRequest();
            moneyRefundRequest.setTenantId(ContextHolder.currentUserTenantId());
            moneyRefundRequest.setOperatorAccountId(ContextHolder.currentUid());
            moneyRefundRequest.setAppId(ContextHolder.currentUserLoginAppId().toString());
            CommonResponse response = ocmsOrderOperateThriftService.moneyRefund(moneyRefundRequest);
            log.info("moneyRefund request:{}, response:{}", moneyRefundRequest, response);
            if (success(response)) {
                return CommonResultBO.builder().success(true).message(StatusCodeEnum.SUCCESS.getMessage()).build();
            }
            return CommonResultBO.builder().success(false).message(responseMsg(response)).build();
        } catch (Exception e) {
            log.error("moneyRefund error request:{}", request, e);
            return CommonResultBO.builder().success(false).message(SYSTEM_ERROR).build();
        }
    }

    public CommonResultBO tenantAfterSaleRefund(AfterSaleRefundRequest request) {
        try {
            OCMSOrderPartRefundRequest bizRequest = request.convertOCMSOrderPartRefundRequest();
            CommonResponse response = ocmsOrderOperateThriftService.tenantAfterSaleRefund(bizRequest);
            log.info("商家售后退款接口调用afterSaleRefund bizRequest:{}, response:{}", bizRequest, response);
            if (success(response)) {
                return CommonResultBO.builder().success(true).message(StatusCodeEnum.SUCCESS.getMessage()).build();
            }
            return CommonResultBO.builder().success(false).message(responseMsg(response)).build();
        } catch (Exception e) {
            log.error("orderbiz tenantAfterSaleRefund TException request:{}", request, e);
            return CommonResultBO.builder().success(false).message(SYSTEM_ERROR).build();
        }
    }



    public com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse tenantPartRefund(com.sankuai.shangou.qnh.orderapi.domain.request.app.PartRefundRequest request) {
        try {
            OCMSOrderPartRefundRequest bizRequest = buildPartRefundRequest(request);
            com.meituan.shangou.saas.o2o.dto.response.CommonResponse response = ocmsOrderOperateThriftService.tenantPartRefund(bizRequest);
            log.info("商家部分退款接口调用request：{}, response:{}", bizRequest, response);
            if (success(response)){
                return com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse.success(null);
            }
            return com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse.fail(response.getStatus().getCode(), response.getStatus().getMessage());
        }catch (Exception e){
            log.error("orderbiz tenantPartRefund TException request:{}", request, e);
            return com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse.fail(ResultCodeEnum.FAIL);
        }
    }

    public com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse tenantAfterSaleRefund(com.sankuai.shangou.qnh.orderapi.domain.request.app.PartRefundRequest request) {
        try {
            OCMSOrderPartRefundRequest bizRequest = buildPartRefundRequest(request);
            com.meituan.shangou.saas.o2o.dto.response.CommonResponse response = ocmsOrderOperateThriftService.tenantAfterSaleRefund(bizRequest);
            log.info("商家售后退款接口调用request：{}, response:{}", bizRequest, response);
            if (success(response)){
                return com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse.success(null);
            }
            return com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse.fail(response.getStatus().getCode(), response.getStatus().getMessage());
        }catch (Exception e){
            log.error("orderbiz tenantAfterSaleRefund TException request:{}", request, e);
            return com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse.fail(ResultCodeEnum.FAIL);
        }
    }


    public com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse tenantCancelOrder(CancelOrderRequest request) {
        try {
            OCMSOrderCancelRequest bizRequest = buildCancelOrderRequest(request);
            com.meituan.shangou.saas.o2o.dto.response.CommonResponse response = ocmsOrderOperateThriftService.tenantCancelOrder(bizRequest);
            log.info("商家全单取消接口调用request：{}, response:{}", bizRequest, response);
            if (success(response)){
                return com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse.success(null);
            }
            return  com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse.fail(ResultCodeEnum.FAIL, responseMsg(response));
        }catch (Exception e){
            log.error("orderbiz tenantCancelOrder TException request:{}", request, e);
            return  com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse.fail(ResultCodeEnum.FAIL, "未知异常");
        }
    }

    private OCMSOrderCancelRequest buildCancelOrderRequest(CancelOrderRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        OCMSOrderCancelRequest bizRequest = new OCMSOrderCancelRequest();
        bizRequest.setOperatorUserId(user.getAccountId());
        bizRequest.setOperatorUserName(user.getOperatorName());
        bizRequest.setAppId(ApiMethodParamThreadLocal.getIdentityInfo().getAppId());
        bizRequest.setOperatorAccountId(user.getAccountId());
        bizRequest.setOrderBizType(ChannelOrderConvertUtils.sourceMid2Biz(
                request.getChannelId()
        ));
        bizRequest.setReason(request.getReason());
        bizRequest.setReasonCode(request.getReasonCode());
        bizRequest.setTenantId(user.getTenantId());
        bizRequest.setViewOrderId(request.getChannelOrderId());
        return bizRequest;
    }

    private OCMSOrderPartRefundRequest buildPartRefundRequest(com.sankuai.shangou.qnh.orderapi.domain.request.app.PartRefundRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        OCMSOrderPartRefundRequest bizRequest = new OCMSOrderPartRefundRequest();
        bizRequest.setOperatorUserId(user.getAccountId());
        bizRequest.setOperatorUserName(user.getOperatorName());
        bizRequest.setAppId(ApiMethodParamThreadLocal.getIdentityInfo().getAppId());
        bizRequest.setOperatorAccountId(user.getAccountId());
        bizRequest.setOrderBizType(
                ChannelOrderConvertUtils.sourceMid2Biz(
                        request.getChannelId()
                ));
        bizRequest.setPartRefundProductModelList(request.getPartRefundProductList().stream().map(refundItem->{
            OCMSOrderPartRefundProductModel model = new OCMSOrderPartRefundProductModel();
            model.setCount(refundItem.getCount());
            model.setCustomSkuId(refundItem.getCustomSkuId());
            model.setCustomerSpuId(refundItem.getCustomerSpuId());
            model.setSkuId2(refundItem.getSkuId());
            model.setSkuName(refundItem.getSkuName());
            if(StringUtils.isNotBlank(refundItem.getExtCustomSkuId())){
                model.setExtCustomSkuId(refundItem.getExtCustomSkuId());
            }
            model.setOrderItemId(refundItem.getOrderItemId());
            return model;
        }).collect(Collectors.toList()));
        bizRequest.setReason(request.getReason());
        bizRequest.setReasonCode(request.getReasonCode());
        bizRequest.setShopId(request.getStoreId());
        bizRequest.setTenantId(user.getTenantId());
        bizRequest.setViewOrderId(request.getChannelOrderId());
        bizRequest.setRequestId(System.currentTimeMillis() + request.getChannelOrderId());
        bizRequest.setPartRefundGiftBagList(buildPartRefundGiftBagList(request.getPartRefundGiftBagList()));
        return bizRequest;
    }

    private List<OCMSOrderPartRefundGiftBagModel> buildPartRefundGiftBagList(List<PartRefundGiftBagVO> partRefundGiftBagList) {
        if (CollectionUtils.isEmpty(partRefundGiftBagList)){
            return null;
        }
        List<OCMSOrderPartRefundGiftBagModel> oCMSOrderPartRefundGiftBagModellist = Lists.newArrayList();
        for (PartRefundGiftBagVO partRefundGiftBagVO : partRefundGiftBagList) {
        	oCMSOrderPartRefundGiftBagModellist.add(convertFromPartRefundGiftBagVO(partRefundGiftBagVO));
        }
        return oCMSOrderPartRefundGiftBagModellist;

    }

    private OCMSOrderPartRefundGiftBagModel convertFromPartRefundGiftBagVO(PartRefundGiftBagVO partRefundGiftBagVO) {
        OCMSOrderPartRefundGiftBagModel oCMSOrderPartRefundGiftBagModel = new OCMSOrderPartRefundGiftBagModel();
        oCMSOrderPartRefundGiftBagModel.setBelongSkuId(partRefundGiftBagVO.getBelongSkuId());
        oCMSOrderPartRefundGiftBagModel.setMaterialSkuId(partRefundGiftBagVO.getMaterialSkuId());
        oCMSOrderPartRefundGiftBagModel.setMaterialSkuName(partRefundGiftBagVO.getMaterialSkuName());
        oCMSOrderPartRefundGiftBagModel.setCount(partRefundGiftBagVO.getCount());
        oCMSOrderPartRefundGiftBagModel.setType(partRefundGiftBagVO.getType());
        return oCMSOrderPartRefundGiftBagModel;
    }

    public List<BizOrderItemMoneyRefundCheckModel> moneyRefundCheck(com.sankuai.shangou.qnh.orderapi.domain.request.app.OrderMoneyRefundCheckRequest request) {
        try {
            BizOrderMoneyRefundCheckRequest moneyRefundCheckRequest = request.convertToBizOrderMoneyRefundCheckRequest();
            moneyRefundCheckRequest.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
            BizOrderMoneyRefundCheckResponse response = ocmsOrderOperateThriftService.moneyRefundCheck(moneyRefundCheckRequest);
            log.info("moneyRefundCheck request:{}, response:{}", moneyRefundCheckRequest, response);
            if (response == null || response.getStatus() == null) {
                throw StatusCodeEnum.FAIL.toBizException("查询金额退可退差商品列表接口返回空");
            }
            if (!Objects.equals(response.getStatus().getCode(), StatusCodeEnum.SUCCESS.getCode())) {
                throw StatusCodeEnum.FAIL.toBizException(response.getStatus().getMessage());
            }
            return response.getBizOrderItemMoneyRefundCheckModelList();
        } catch (TException e) {
            log.error("moneyRefundCheck error request:{}", request, e);
            throw StatusCodeEnum.FAIL.toBizException("查询金额退可退差商品列表异常");
        }
    }

    public com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse moneyRefund(com.sankuai.shangou.qnh.orderapi.domain.request.app.OrderMoneyRefundRequest request) {
        try {
            BizOrderMoneyRefundRequest moneyRefundRequest = request.convertToBizOrderMoneyRefundRequest();
            moneyRefundRequest.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
            moneyRefundRequest.setOperatorAccountId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId());
            moneyRefundRequest.setAppId(ApiMethodParamThreadLocal.getIdentityInfo().getAppId());
            com.meituan.shangou.saas.o2o.dto.response.CommonResponse response = ocmsOrderOperateThriftService.moneyRefund(moneyRefundRequest);
            log.info("moneyRefund request:{}, response:{}", moneyRefundRequest, response);
            if (success(response)) {
                return com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse.success(null);
            }
            return com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse.fail(response.getStatus().getCode(), response.getStatus().getMessage());
        } catch (Exception e) {
            log.error("moneyRefund error request:{}", request, e);
            return com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse.fail(ResultCodeEnum.FAIL);
        }
    }


    /**
     * 创建发票
     * @param tenantId
     * @param channelId
     * @param channelOrderId
     * @return
     * @throws TException
     */
    public CreateInvoiceResponse createInvoice(Long tenantId, Integer channelId, String channelOrderId) throws TException {
        CreateInvoiceRequest request = new CreateInvoiceRequest();
        request.setTenantId(tenantId);
        request.setChannelId(channelId);
        request.setChannelOrderId(channelOrderId);
        CreateInvoiceResponse response = ocmsOrderOperateThriftService.createInvoiceFromOrderList(request);
        log.info("OrderBizRemoteService.createInvoice request:{}, response:{}", request, response);
        return response;
    }


    public List<OrderFuseFinanceDetailBO> queryFinanceDetails(List<Long> serviceIds, Long shopId, Long tenantId, int type) {
        OrderFinanceQueryRequest request = OrderFinanceQueryRequest.builder()
                .type(type)
                .tenantId(tenantId)
                .shopId(shopId)
                .ids(serviceIds)
                .includeDetail(true)
                .build();
        try {
            OrderFinanceQueryResponse response = orderFinanceThriftService.queryOrderFinanceInfo(request);
            if (!Objects.equals(response.getStatus().getCode(), StatusCodeEnum.SUCCESS.getCode()) ||
                    CollectionUtils.isEmpty(response.getOrderFinanceModelList())) {
                return null;
            }
            return response.getOrderFinanceModelList().stream().map(OrderFuseFinanceDetailBO::new).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询订单财务数据失败:{}", JSON.toJSONString(serviceIds), e);
            return null;
        }
    }

    public com.sankuai.shangou.qnh.orderapi.domain.response.CommonResponse<Void> returnGoodsAudit(String viewOrderId, Long tenantId, String afterSaleId, Integer channelId, Integer auditResult, Integer rejectReasonCode, String rejectOtherReason) {

        com.sankuai.shangou.qnh.orderapi.domain.response.CommonResponse result = new com.sankuai.shangou.qnh.orderapi.domain.response.CommonResponse(StatusCodeEnum.SUCCESS.getCode(), StatusCodeEnum.SUCCESS.getMessage(), null);
        try {
            ReturnGoodsAuditRequest request = new ReturnGoodsAuditRequest();
            request.setViewOrderId(viewOrderId);
            request.setTenantId(tenantId);
            request.setAfterSaleId(afterSaleId);
            request.setChannelId(channelId);
            request.setAuditResult(auditResult);
            request.setRejectReasonCode(rejectReasonCode);
            request.setRejectOtherReason(rejectOtherReason);
            request.setAppId(String.valueOf(ContextHolder.currentUserLoginAppId()));
            request.setOperatorAccountId(ContextHolder.currentUid());
            log.info("OrderBizRemoteService.returnGoodsAudit request: {}", request);
            BizOrderReturnGoodsAuditResponse response = bizOrderThriftService.returnGoodsAudit(request);
            log.info("OrderBizRemoteService.returnGoodsAudit response: {}", response);
            if (!StatusCodeEnum.SUCCESS.toStatus().equals(response.getStatus())) {
                throw new IllegalArgumentException(response.getMsg());
            }
        }catch (Exception e){
            result.setCode(StatusCodeEnum.FAIL.getCode());
            result.setMessage(StatusCodeEnum.FAIL.getMessage());
            log.error("OrderBizRemoteService.returnGoodsAudit error viewOrderId: {}, e= ", viewOrderId, e);
        }
        return result;

    }


    public com.sankuai.shangou.qnh.orderapi.domain.response.CommonResponse<Void> importRefundExchangeInfo(ImportRefundExchangeInfoRequest request){
        com.sankuai.shangou.qnh.orderapi.domain.response.CommonResponse result = new com.sankuai.shangou.qnh.orderapi.domain.response.CommonResponse(StatusCodeEnum.SUCCESS.getCode(), StatusCodeEnum.SUCCESS.getMessage(), null);

        ExchangeItemRefundImportRequest bizRequest = new ExchangeItemRefundImportRequest();
        bizRequest.setOrderId(request.getOrderId());
        bizRequest.setTenantId(request.getTenantId());
        bizRequest.setOrderBizType(DynamicOrderBizType.channelId2OrderBizType(request.getChannelId()).getValue());
        bizRequest.setServiceId(request.getServiceId());
        bizRequest.setDetailRequestList(request.getDetailRequestList().stream().map(this::convertToBizRequest).collect(Collectors.toList()));
        try {
            CommonResponse commonResponse = bizOrderThriftService.importRefundExchangeInfo(bizRequest);
            if (!StatusCodeEnum.SUCCESS.toStatus().equals(commonResponse.getStatus())) {
                throw new IllegalArgumentException(commonResponse.getStatus().getMessage());
            }
        }catch (Exception e){
            result.setCode(StatusCodeEnum.FAIL.getCode());
            result.setMessage(StatusCodeEnum.FAIL.getMessage());
            log.error("importRefundExchangeInfo {}, e= ", request, e);
        }
        return result;
    }

    private ExchangeRefundImportDetailRequest convertToBizRequest(ImportRefundExchangeInfoRequest.ExchangeRefundImportDetailRequest exchangeRefundImportDetailRequest) {
        ExchangeRefundImportDetailRequest bizRequest = new ExchangeRefundImportDetailRequest();
        bizRequest.setRefundCount(exchangeRefundImportDetailRequest.getRefundCount());
        bizRequest.setAfsItemId(exchangeRefundImportDetailRequest.getAfsItemId());
        bizRequest.setExchangeFromServiceId(exchangeRefundImportDetailRequest.getExchangeFromServiceId());
        bizRequest.setOrderItemId(exchangeRefundImportDetailRequest.getOrderItemId());
        bizRequest.setItemType(exchangeRefundImportDetailRequest.getItemType());
        bizRequest.setRefundAmt(exchangeRefundImportDetailRequest.getRefundAmt());
        bizRequest.setExchangeOrderItemId(exchangeRefundImportDetailRequest.getExchangeOrderItemId());
        if (Objects.nonNull(exchangeRefundImportDetailRequest.getExchangeServiceId()) && Objects.nonNull(exchangeRefundImportDetailRequest.getExchangeFromServiceId())) {
            bizRequest.setExchangeOrderItemId(exchangeRefundImportDetailRequest.getExchangeServiceId());
        }
        return bizRequest;
    }

    public com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse<ExchangeRefundImportCheckResponse> checkRefundExchangeInfo(ExchangeRefundImportCheckReq req) {
        com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse<ExchangeRefundImportCheckResponse> result = new com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse<>(
                StatusCodeEnum.SUCCESS.getCode(), StatusCodeEnum.SUCCESS.getMessage(), null);
        ExchangeRefundImportCheckResponse response = new ExchangeRefundImportCheckResponse();
        ExchangeRefundImportCheckRequest checkRequest = new ExchangeRefundImportCheckRequest();
        checkRequest.setOrderId(req.getOrderId());
        checkRequest.setServiceId(req.getServiceId());
        checkRequest.setTenantId(req.getTenantId());
        checkRequest.setChannelId(req.getChannelId());
        try {
            ImportExchangeImportResponse importExchangeImportResponse = bizOrderThriftService
                    .checkRefundExchangeInfo(checkRequest);
            if (!Objects.equals(StatusCodeEnum.SUCCESS.getCode(), importExchangeImportResponse.getCode())) {
                return com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse
                        .fail(importExchangeImportResponse.getCode(), importExchangeImportResponse.getMessage());
            }
            response.setImportExchangePairList(importExchangeImportResponse.getData());
            result.setData(response);
        } catch (Exception e) {
            log.error("checkRefundExchangeInfo error, req:{}", req, e);
            result.setCode(StatusCodeEnum.FAIL.getCode());
            result.setMessage(StatusCodeEnum.FAIL.getMessage());
        }
        return result;
    }

    /**
     * 查询订单可操作列表
     *
     * @param tenantId  租户ID
     * @param orderKeys 渠道订单号
     * @return 可操作列表
     */
    public Map<OCMSOrderKey, List<Integer>> queryOrderOperateItems(Long tenantId, List<OCMSOrderKey> orderKeys, List<Integer> checkItems, Map<String, Long> orderToShopMap, Map<OCMSOrderKey, ChannelOrderBO> orderMap) {

        boolean orderAdjustFromSettlement = MccConfigUtil.orderAdjustFromSettlement();
        Integer orderAdjustOperateItem = OrderCanOperateItem.ORDER_ADJUST.getValue();

        // 若未传入可操作项，则查询所有
        if (CollectionUtils.isEmpty(checkItems)) {
            checkItems = Arrays.stream(OrderCanOperateItem.values())
                    .map(OrderCanOperateItem::getValue)
                    .collect(Collectors.toList());
        }

        // 通过订单系统查询的可操作项
        List<Integer> checkItemFromOrderBiz = new ArrayList<>(checkItems);
        if (orderAdjustFromSettlement) {
            checkItemFromOrderBiz.remove(orderAdjustOperateItem);
        }

        Map<OCMSOrderKey, List<Integer>> orderKeyListMap = queryOrderOperateItemsFromOrderBiz(tenantId, orderKeys, checkItemFromOrderBiz);
        log.info("查询订单系统订单可操作项 tenantId:{},orderKeys:{},checkItems:{},orderKeyListMap:{}", tenantId, orderKeys, checkItems, orderKeyListMap);
        removeDhOrderOperateItems(tenantId, orderKeyListMap, orderToShopMap);
        // 订单调整若查询结算系统，则是否可调整查询结算系统
        if (orderAdjustFromSettlement && checkItems.contains(orderAdjustOperateItem)) {
            Map<OCMSOrderKey, Boolean> orderCanAdjustMap = queryOrderCanAdjustFromSettlement(tenantId, orderKeys);
            log.info("查询结算系统订单是否可调整结果 orderKeys:{},orderCanAdjustMap:{}", orderKeys, orderCanAdjustMap);
            // 遍历订单的可操作项，将订单可调整的结果覆盖为结算系统的结果
            orderCanAdjustMap.forEach((orderKey, canAdjust) -> {
                if (Boolean.TRUE.equals(canAdjust)) {
                    if (orderKeyListMap.containsKey(orderKey)) {
                        orderKeyListMap.get(orderKey).add(orderAdjustOperateItem);
                    }
                    else {
                        orderKeyListMap.put(orderKey, Arrays.asList(orderAdjustOperateItem));
                    }
                }
            });
        }
        //设置歪马租户开发票按钮
        setDhOrderInvoiceOperateItem(tenantId, orderKeyListMap, orderToShopMap, orderMap);
        return orderKeyListMap;
    }

    private void removeDhOrderOperateItems(Long tenantId, Map<OCMSOrderKey, List<Integer>> orderKeyListMap, Map<String, Long> orderToShopMap) {
        if(Lion.getConfigRepository().getBooleanValue("web_auth_code_switch", true)  && MccConfigUtil.isDhTenant(tenantId) ) {
            log.info("orderKeyListMap:{}, orderToShopMap:{}",orderKeyListMap,orderToShopMap);
            AccountAuthPermissionsRequest request = new AccountAuthPermissionsRequest();
            request.setAccountId(ContextHolder.currentUid());
            request.setAppId(ContextHolder.currentUserLoginAppId());
            request.setPermissionCodes(org.assertj.core.util.Lists.newArrayList(ORDER_SEARCH_FULL_REFUND, ORDER_SEARCH_PARTIAL_REFUND));
            AccountAuthPermissionsResponse resp = authenticateService.accountAuthPermissions(request);
            if(resp == null || MapUtils.isEmpty(resp.getAuthResult())) {
                return;
            }
            if(Objects.equals(resp.getAuthResult().get(ORDER_SEARCH_FULL_REFUND), false)) {
                orderKeyListMap.keySet().stream().filter(key -> Objects.equals(isConfigButtonPoi(key.getChannelOrderId(), orderToShopMap), true)).map(orderKeyListMap::get).forEach(orderOperateItems -> orderOperateItems.removeAll(Collections.singleton(OrderCanOperateItem.FULL_ORDER_REFUND.getValue())));
            }
            if(Objects.equals(resp.getAuthResult().get(ORDER_SEARCH_PARTIAL_REFUND), false)) {
                orderKeyListMap.keySet().stream().filter(key -> Objects.equals(isConfigButtonPoi(key.getChannelOrderId(), orderToShopMap), true)).map(orderKeyListMap::get).forEach(orderOperateItems -> orderOperateItems.removeAll(Collections.singleton(OrderCanOperateItem.PART_ORDER_REFUND.getValue())));
            }
            log.info("orderKeyListMap:{}", orderKeyListMap);
        }
    }

    /**
     * 设置歪马订单的发票按钮
     *
     * @param tenantId
     * @param orderKeyListMap
     * @param orderToShopMap
     * @param orderMap
     */
    private void setDhOrderInvoiceOperateItem(Long tenantId, Map<OCMSOrderKey, List<Integer>> orderKeyListMap,
                                              Map<String, Long> orderToShopMap, Map<OCMSOrderKey, ChannelOrderBO> orderMap) {
        // 非歪马租户不处理
        if (!MccConfigUtil.isDhTenant(tenantId)) {
            return;
        }
        // 当歪马门店为加盟商门店时，设置开发票按钮
        try {
            Map<Long, PoiInfoDto> poiInfoDtoMap = poiClient.queryPoiInfosByPoiIds(new HashSet<>(orderToShopMap.values()));
            if (MapUtils.isEmpty(poiInfoDtoMap)) {
                return;
            }
            orderMap.entrySet().stream()
                    // 订单未取消且为加盟商门店才展示开发票按钮
                    .filter(entry -> {
                        ChannelOrderBO channelOrderBO = entry.getValue();
                        return Objects.nonNull(channelOrderBO)
                                && !Objects.equals(channelOrderBO.getOrderStatus(), OrderStatusEnum.CANCELED.getValue())
                                && isFranchiseStore(channelOrderBO, poiInfoDtoMap);
                    }).forEach(entry -> {
                        // 在订单操作类型上添加开发票按钮
                        if (CollectionUtils.isNotEmpty(orderKeyListMap.get(entry.getKey()))) {
                            orderKeyListMap.get(entry.getKey()).add(OrderCanOperateItem.CREATE_INVOICE.getValue());
                        } else {
                            orderKeyListMap.put(entry.getKey(),
                                    org.assertj.core.util.Lists.newArrayList(OrderCanOperateItem.CREATE_INVOICE.getValue()));
                        }
                    });
        } catch (Exception e) {
            log.error("setDhOrderInvoiceButton error", e);
        }
    }

    /**
     * 判断是否为加盟商门店
     *
     * @param channelOrderBO
     * @param poiInfoDtoMap
     * @return
     */
    private boolean isFranchiseStore(ChannelOrderBO channelOrderBO, Map<Long, PoiInfoDto> poiInfoDtoMap) {
        Long shopId = channelOrderBO.getPoiId();
        //  判断门店是否能开发票，不能开发票则不进行展示开发票按钮
        if (Objects.isNull(shopId) || !MccConfigUtil.checkDhShopOrderCreateInvoice(shopId)) {
            return false;
        }
        PoiInfoDto poiInfo = poiInfoDtoMap.get(shopId);
        if (Objects.isNull(poiInfo) || Objects.isNull(poiInfo.getPoiExtendContentDto())) {
            return false;
        }
        return Objects.equals(poiInfo.getPoiExtendContentDto().getOperationMode(),
                PoiOperationModeEnum.FRANCHISE.getKey());
    }

    /**
     * 门店灰度过滤，灰度门店的单才会根据权限决定是否展示按钮，其他单直接展示不remove
     * @return
     */
    public Boolean isConfigButtonPoi(String orderId, Map<String, Long> orderToShopMap) {
        try {

            List<Long> stores = Lion.getConfigRepository().getList("config.button.stores", Long.class, Lists.newArrayList());
            //全量逻辑
            if (stores.size() == 1 && stores.get(0).equals(-1L)) {
                return true;
            }
            Long storeId = orderToShopMap.get(orderId);
            return stores.contains(storeId);
        } catch (Exception e) {
            log.error("getDHByPositionSwitch error", e);
            return false;
        }
    }

    /**
     * 查询订单基本信息
     *
     * @param orderId
     * @param channelId
     * @param tenantId
     * @return
     */
    @MethodLog(logRequest = true, logResponse = true)
    public BizOrderModel queryOrderModelWithCompose(String orderId, int channelId, Long tenantId) {
        BizOrderQueryRequest req = new BizOrderQueryRequest();
        req.setOrderBizType(ChannelOrderConvertUtils.sourceMid2Biz(channelId));
        req.setViewOrderId(orderId);
        req.setTenantId(tenantId);
        req.setOrderSource(OrderSourceEnum.OTO_ONLINE_MIDDLE.getValue());
        req.setFromMaster(false);
        req.setContainsComposeSku(true);

        BizOrderQueryResponse resp = RpcInvoker.invoke(() -> bizOrderThriftService.query(req));

        if (resp.getBizOrderModel() == null) {
            throw new BizException("订单不存在");
        }
        return resp.getBizOrderModel();
    }

    public CommonDataBO<List<UiOption>> refundReasonList(QueryRefundReasonRequest request) {
        CommonDataBO<List<UiOption>> responseData = new CommonDataBO();
        try {
            OCMSRefundReasonListRequest bizRequest = new OCMSRefundReasonListRequest();
            bizRequest.setRefundType(request.getOrderType());
            bizRequest.setTenantId(ContextHolder.currentUserTenantId());
            OCMSRefundReasonListResponse response = ocmsOrderOperateThriftService.refundReasonCodeList(bizRequest);
            if (success(response)){
                responseData.setSuccess(true);
                responseData.setData(response.getPossibleRefundReasons().stream()
                        .map(model-> new UiOption(String.valueOf(model.getCode()), model.getReason()))
                        .collect(Collectors.toList()));
            }
            return responseData;
        }catch (Exception e){
            log.error("orderbiz refundReasonList TException request:{}", request, e);
            responseData.setSuccess(false);
            responseData.setMessage(SYSTEM_ERROR);
            return responseData;
        }
    }

    public void agreeRefund(ApproveRefundBO approveRefundBO) {
        OCMSTenantAgreeRefundRequest bizRequest = new OCMSTenantAgreeRefundRequest();
        bizRequest.setAfsApplyType(approveRefundBO.getAfsApplyType());
        bizRequest.setAfterSaleId(approveRefundBO.getServiceId());
        bizRequest.setOperatorUserId(ContextHolder.currentUserStaffId());
        bizRequest.setOperatorUserName(ContextHolder.currentUserName());
        bizRequest.setAppId(String.valueOf(ContextHolder.currentUserLoginAppId()));
        bizRequest.setOperatorAccountId(ContextHolder.currentUid());
        bizRequest.setOrderBizType(
                ChannelOrderConvertUtils.sourceMid2Biz(approveRefundBO.getChannelId())
        );
        bizRequest.setReason(StringUtils.defaultString(approveRefundBO.getReason(), "OK"));
        bizRequest.setTenantId(ContextHolder.currentUserTenantId());
        bizRequest.setViewOrderId(approveRefundBO.getOrderId());
        try {
            CommonResponse resp = ocmsOrderOperateThriftService.agreeRefundByTenant(bizRequest);
            ResponseHandler.checkResponseAndStatus(resp, r -> r.getStatus().getCode(), r -> r.getStatus().getMessage());
        }catch (BizException bizException){
            log.error("orderBiz agreeRefund 失败 request:{}", approveRefundBO, bizException);
            throw bizException;
        }catch (Exception e){
            log.error("orderBiz agreeRefund TException request:{}", approveRefundBO, e);
            throw new BizException("审批退款异常", e);
        }
    }

    public void rejectRefund(ApproveRefundBO approveRefundBO) {
        OCMSTenantRejectRefundRequest bizRequest = new OCMSTenantRejectRefundRequest();
        bizRequest.setAfsApplyType(approveRefundBO.getAfsApplyType());
        bizRequest.setAfterSaleId(approveRefundBO.getServiceId());
        bizRequest.setOperatorUserId(ContextHolder.currentUserStaffId());
        bizRequest.setOperatorUserName(ContextHolder.currentUserName());
        bizRequest.setAppId(String.valueOf(ContextHolder.currentUserLoginAppId()));
        bizRequest.setOperatorAccountId(ContextHolder.currentUid());
        bizRequest.setOrderBizType(
                ChannelOrderConvertUtils.sourceMid2Biz(approveRefundBO.getChannelId())
        );
        bizRequest.setReason(StringUtils.defaultString(approveRefundBO.getReason(), "OK"));
        bizRequest.setTenantId(ContextHolder.currentUserTenantId());
        bizRequest.setViewOrderId(approveRefundBO.getOrderId());
        try {
            CommonResponse resp = ocmsOrderOperateThriftService.rejectRefundByTenant(bizRequest);
            ResponseHandler.checkResponseAndStatus(resp, r -> r.getStatus().getCode(), r -> r.getStatus().getMessage());
        }catch (BizException bizException){
            log.error("orderBiz rejectRefund 失败 request:{}", approveRefundBO, bizException);
            throw bizException;
        }catch (Exception e){
            log.error("orderBiz rejectRefund TException request:{}", approveRefundBO, e);
            throw new BizException("审批退款异常", e);
        }
    }

}