package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.saas.common.aop.feature.Validatable;
import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: Wang<PERSON>uku<PERSON>
 * @create: 2020-12-01
 **/
@TypeDoc(
        description = "账号可选角色列表查询请求参数"
)
@Data
public class SacAccountBindRoleListReq implements Validatable {

    @FieldDoc(
            description = "账号ID"
    )
    private Long accountId;

    @FieldDoc(
            description = "业务端id"
    )
    private Integer bizAppId;

    @FieldDoc(
            description = "角色类型列表"
    )
    private List<Integer> roleTypeList;

    @Override
    public void validate() {
        AssertUtil.isPositiveNumber(accountId, "账号ID不能小于0");
    }
}
