package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/8/21
 * desc:
 */
@TypeDoc(
        description = "聚合渠道门店分类"
)
@Data
@ApiModel("聚合渠道门店分类")
public class AssemblyChannelStoreFrontCategoryVO {

    @FieldDoc(
            description = "名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "名称", required = true)
    private String name;

    @FieldDoc(
            description = "上级店内分类名称 （当前分类是顶级分类时为空）", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "上级店内分类名称", required = true)
    private String parentName;

    @FieldDoc(
            description = "层级", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "层级", required = true)
    private Integer level;

    @FieldDoc(
            description = "是否有商品 1-是 0-否", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "是否有商品", required = true)
    private Integer hasSku;

    @FieldDoc(
            description = "下一级数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "下一级数量", required = true)
    private Integer subAmount;

    @FieldDoc(
            description = "渠道ID集合", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID集合", required = true)
    private List<Integer> channelIdList;

    @FieldDoc(
            description = "店内分类列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "店内分类列表", required = true)
    private List<ChannelStoreFrontCategoryKeyVO> categoryList;

    @FieldDoc(
            description = "店内分类名称全路径", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "店内分类名称全路径", required = true)
    private String namePath;

    @FieldDoc(
            description = "父店内分类名称全路径 (一级分类时为空字符串)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "父店内分类名称全路径", required = true)
    private String parentNamePath;
}
