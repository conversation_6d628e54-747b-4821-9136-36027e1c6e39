package com.sankuai.shangou.qnh.orderapi.domain.vo.app;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.qnh.orderapi.domain.vo.ExchangeProductVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.SubProductVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2020-02-17 16:33
 * @Description:
 */
@TypeDoc(
        description = "部分退款页面检查退款vo"
)
@ApiModel("部分退款页面检查退款请求vo")
@Data
public class OrderItemPartRefundCheckVO implements Cloneable{
    @FieldDoc(
            description = "商品sku,customer skuId，渠道sku", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品sku", required = true)
    private String skuId;

    @FieldDoc(
            description = "商品sku,customer skuId，渠道sku", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品spu", required = true)
    private String customerSpuId;

    @FieldDoc(
            description = "中台sku", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "中台sku ", required = true)
    private String inStoreSkuId;

    @FieldDoc(
            description = "退款价格", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "退款价格", required = true)
    private String refundPrice;

    @FieldDoc(
            description = "可退数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "可退数量", required = true)
    private Integer canRefundCount;


    @FieldDoc(
            description = "现价", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "现价,单位为分", required = true)
    private Integer currentPrice;

    @FieldDoc(
            description = "组合商品", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "组合商品")
    private List<SubProductVo> subProduct;

    @FieldDoc(
            description = "换货商品列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "换货商品列表")
    private List<ExchangeProductVo> exchangeProductVoList;


    @Override
    public OrderItemPartRefundCheckVO clone() {
        try {
            return (OrderItemPartRefundCheckVO) super.clone();
        } catch (Exception e) {
            throw new BizException("clone失败", e);
        }
    }
}

