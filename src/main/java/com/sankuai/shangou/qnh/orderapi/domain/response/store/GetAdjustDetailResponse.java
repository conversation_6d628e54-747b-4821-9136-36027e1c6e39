package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.SkuAdjustDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

@TypeDoc(
        description = "获取调价单详情响应"
)
@Data
@ApiModel("获取调价单详情响应")
public class GetAdjustDetailResponse {
    @FieldDoc(
            description = "调价单单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "调价单单号", required = true)
    @NotEmpty
    private String priceOrderNo;

    @FieldDoc(
            description = "操作人姓名", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "操作人姓名", required = true)
    @NotEmpty
    private String operatorName;

    @FieldDoc(
            description = "操作时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "操作时间", required = true)
    @NotEmpty
    private String operatorTime;

    @FieldDoc(
            description = "sku列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "sku列表", required = true)
    @NotEmpty
    private List<SkuAdjustDetail> skuList;
}
