package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 评价规则VO
 *
 * <AUTHOR>
 */
@TypeDoc(
        name = "评价规则VO",
        description = "评价规则VO"
)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CommentRuleVO {

    @FieldDoc(
            description = "评分规则"
    )
    private List<String> commentLevelRules;
}
