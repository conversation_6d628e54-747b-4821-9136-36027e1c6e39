package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import java.util.List;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.dto.pc.EvidenceDto;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.RefundGoodsTakenOffVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;


@TypeDoc(
        description = "退款审核请求"
)
@ApiModel("退款审核请求")
@Data
public class RefundApplyAuditRequest {

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道ID")
    @NotNull
    private Integer channelId;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道订单号")
    @NotNull
    private String channelOrderId;

    @FieldDoc(
            description = "退款申请唯一ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "退款申请唯一ID", required = true)
    @NotNull
    private Long serviceId;

    @FieldDoc(
            description = "渠道退款申请唯一ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道退款申请唯一ID", required = true)
    @NotNull
    private String afterSaleId;

    @FieldDoc(
            description = "审核结果(1:同意通过 2:驳回申请)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "审核结果(1:同意通过 2:驳回申请)", required = true)
    @NotNull
    private Integer auditResult;


    @FieldDoc(
            description = "原因", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "原因", required = false)
    private String auditReason;


    @FieldDoc(
            description = "退款审核类型(1:仅退款 2:退货退款)", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "退款审核类型", required = false)
    private Integer refundType;

    @FieldDoc(
            description = "审核阶段(1:初审 2:终审)", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "审核阶段", required = false)
    private Integer auditStage;

    @FieldDoc(
            description = "退款原因code", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "退款原因code", required = false)
    private Integer auditReasonCode;

    @FieldDoc(
            description = "缺货下架列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "缺货下架列表")
    private List<RefundGoodsTakenOffVO> refundGoodsTakenOffVOList;

    @FieldDoc(
            description = "门店id", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店id")
    private String poiId;

    @FieldDoc(
            description = "仅退款无需退货", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "仅退款无需退货",required = false)
    private Boolean onlyRefundWithoutReturnGoods;

    @FieldDoc(
            description = "orderId", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "orderId")
    private String qnhOrderId;

    @FieldDoc(
            description = "凭证列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "凭证列表",required = false)
    private List<EvidenceDto> evidenceList;

    @FieldDoc(
            description = "是否返货", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "是否返货", required = false)
    private Boolean isReturnGoods;
}
