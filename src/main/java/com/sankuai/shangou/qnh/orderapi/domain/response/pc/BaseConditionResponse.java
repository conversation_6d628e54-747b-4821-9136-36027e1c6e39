package com.sankuai.shangou.qnh.orderapi.domain.response.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.UiOption;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@TypeDoc(
        description = "商家配置信息"
)
@ApiModel("查询数据权限组全部列表回参")
@Data
public class BaseConditionResponse {

    @FieldDoc(
            description = "是否开启核销", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "是否开启核销", required = true)
    private Boolean enableErpWriteOff = Boolean.FALSE;

    @FieldDoc(
            description = "是否开启京东整合营销", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "是否开启京东整合营销", required = true)
    private Boolean enableJDDJMarketingIntegration = Boolean.FALSE;

    @FieldDoc(
            description = "核销状态码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "核销状态码", required = true)
    private List<UiOption>  writeOffStatus;

    @FieldDoc(
            description = "是否ERP商户", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "是否ERP商户", required = true)
    private Boolean enableErpTenant = Boolean.FALSE;
}
