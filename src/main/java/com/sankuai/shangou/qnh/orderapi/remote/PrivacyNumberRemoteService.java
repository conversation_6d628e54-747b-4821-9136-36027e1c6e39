package com.sankuai.shangou.qnh.orderapi.remote;

import com.meituan.shangou.saas.dto.request.AxBPrivacyPhoneRequest;
import com.meituan.shangou.saas.dto.response.PrivacyPhoneResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OrderBaseVo;
import com.meituan.shangou.saas.service.PrivacyPhoneThriftService;
import com.sankuai.shangou.qnh.orderapi.enums.store.PrivateNumberBizScenarioType;
import com.sankuai.shangou.qnh.orderapi.utils.store.MccDynamicConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/7/18 11:14 AM
 */
@Slf4j
@Component
public class PrivacyNumberRemoteService {

    @Autowired
    private PrivacyPhoneThriftService privacyPhoneThriftService;

    /**
     * 获取订单对应的axb隐私号
     *
     * @param orderBaseVo 订单基本信息
     * @param callPhone   拨打手机号
     * @param calledPhone 被拨打手机号
     * @return
     */
    public PrivacyPhoneResponse applyAxB(OrderBaseVo orderBaseVo, String callPhone, String calledPhone) {
        AxBPrivacyPhoneRequest request = new AxBPrivacyPhoneRequest();
        request.setUserId(orderBaseVo.getUserId());
        request.setUuid(orderBaseVo.getChannelOrderId());
        request.setChannelId(orderBaseVo.getChannelId());
        request.setUserPhone(callPhone);
        request.setCalledPhone(calledPhone);
        request.setCityId(orderBaseVo.getCityId());
        request.setDuration(MccDynamicConfigUtil.axbPrivacyPhoneValidTime());
        // 骑手呼叫用户
        request.setScenarioType(PrivateNumberBizScenarioType.WAIMAI_RIDER_CALL_CUSTOMER.code);
        return privacyPhoneThriftService.applyAxbPhone(request);
    }
}
