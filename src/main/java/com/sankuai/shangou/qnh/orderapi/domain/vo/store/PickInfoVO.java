package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/9/3 14:53
 **/
@Data
public class PickInfoVO {
    @FieldDoc(
            description = "拣货项信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "拣货项信息")
    private List<PickItemVO> pickItemInfoList;

    @FieldDoc(
            description = "拣货状态", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "拣货状态")
    private Integer pickStatus;

    @FieldDoc(
            description = "是否是拣配分离", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "是否是拣配分离")
    private Boolean isPickDeliverySplit;

    @FieldDoc(
            description = "拣货员名字", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "拣货员名字")
    private String pickerName;

    @FieldDoc(
            description = "拣货员账号id", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "拣货员账号id")
    private Long pickAccountId;

    @FieldDoc(
            description = "拣货复核照片（支持多张照片）", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "拣货复核照片（支持多张照片）")
    private List<String> pickingCheckPictureUrlList;
}
