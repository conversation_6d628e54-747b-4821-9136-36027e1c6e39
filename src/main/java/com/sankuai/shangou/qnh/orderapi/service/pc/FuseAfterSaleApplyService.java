package com.sankuai.shangou.qnh.orderapi.service.pc;

import com.sankuai.shangou.qnh.orderapi.domain.request.pc.*;
import com.sankuai.shangou.qnh.orderapi.domain.response.CommonResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.WeightRefundCheckResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.OrderItemMoneyRefundCheckVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.RefundReasonAndCodeVO;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2022/12/8 14:25
 * @Description:
 */
public interface FuseAfterSaleApplyService {

    /**
     * 老牵牛花订单不提供支持
     * @param weightRefundCheckRequest
     * @return
     */
    CommonResponse<WeightRefundCheckResponse> weightRefundCheck(WeightRefundCheckRequest weightRefundCheckRequest);

    CommonResponse weightRefund(RefundByWeightRequest request);

    CommonResponse weightRefundCalculate(RefundByWeightRequest request);

    /**
     * 全部金额退可退商品列表
     * @param request
     * @return
     */
    List<OrderItemMoneyRefundCheckVO> moneyRefundCheck(OrderMoneyRefundCheckRequest request);

    /**
     * 商家金额退
     * @param request
     * @return
     */
    CommonResponse moneyRefund(OrderMoneyRefundRequest request);

    /**
     * 售后退款（发起售后）
     * 目前只有京东渠道有
     * @param request
     * @return
     */
    CommonResponse afterSaleRefund(AfterSaleRefundRequest request);

    List<RefundReasonAndCodeVO> queryRefundReasons(RefundReasonAndCodeRequest request);
}
