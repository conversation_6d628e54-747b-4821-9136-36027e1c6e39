package com.sankuai.shangou.qnh.orderapi.remote;

import com.github.pagehelper.Page;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiEntityTypeEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.Channel;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ChannelType;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.OperateSourceEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.QueryPhoneType;
import com.sankuai.meituan.shangou.saas.common.data.BaseResult;
import com.sankuai.meituan.shangou.saas.common.data.PageResult;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.sgfulfillment.comment.thrift.ChannelCommentThriftService;
import com.sankuai.sgfulfillment.comment.thrift.CommentContactThriftService;
import com.sankuai.sgfulfillment.comment.thrift.common.PageInfoDTO;
import com.sankuai.sgfulfillment.comment.thrift.dto.*;
import com.sankuai.sgfulfillment.comment.thrift.dto.model.BadProcessedInfoDTO;
import com.sankuai.sgfulfillment.comment.thrift.dto.model.ChannelCommentDTO;
import com.sankuai.sgfulfillment.comment.thrift.dto.model.ChannelCommentLabelStatDTO;
import com.sankuai.sgfulfillment.comment.thrift.dto.model.ChannelCommentReplyTemplateDTO;
import com.sankuai.sgfulfillment.comment.thrift.dto.model.ChannelCommentRuleDTO;
import com.sankuai.sgfulfillment.comment.thrift.dto.model.ChannelCommentStatChannelDTO;
import com.sankuai.sgfulfillment.comment.thrift.dto.model.ChannelCommentStatStoreDTO;
import com.sankuai.sgfulfillment.comment.thrift.dto.model.CommentContactDTO;
import com.sankuai.shangou.qnh.orderapi.constant.store.ProjectConstants;
import com.sankuai.shangou.qnh.orderapi.context.app.ApiMethodParamThreadLocal;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.StoreCommentReplyTemplateBO;
import com.sankuai.shangou.qnh.orderapi.domain.dto.app.IdentityInfo;
import com.sankuai.shangou.qnh.orderapi.domain.dto.pc.ChannelCommentReplyTemplateListDTO;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.*;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.CommentReplyTemplateAddResp;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.CommentReplyTemplateListQueryResp;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.CommentStatResp;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.CommonResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.QueryVirtualPhoneResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.ManualCheckDeleteCommentVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.ManualCheckUpdateCommentVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.ChannelInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.CommentQueryBaseInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.CommentRuleVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.CommentVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.User;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.VirtualNumberInfo;
import com.sankuai.shangou.qnh.orderapi.exception.store.CommonLogicException;
import com.sankuai.shangou.qnh.orderapi.exception.store.CommonRuntimeException;
import com.sankuai.shangou.qnh.orderapi.utils.pc.LionUtils;
import com.sankuai.shangou.qnh.orderapi.utils.store.DateUtils;
import com.sankuai.shangou.qnh.orderapi.utils.store.MccConfigUtil;
import com.sankuai.shangou.qnh.orderapi.utils.store.OCMSUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.logging.log4j.util.Strings;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 评价中台服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ChannelCommentRemoteService {

    @Resource
    private ChannelCommentThriftService channelCommentThriftService;

    @Resource
    private CommentContactThriftService commentContactThriftService;

    @Resource
    private StoreRemoteService storeWrapper;

    @Resource
    private TenantRemoteService tenantWrapper;

    @Resource
    private OCMSOrderRemoteService ocmsOrderServiceWrapper;

    @Autowired
    private PoiRemoteService poiServiceFacade;

    private static final int SUCCESS_RESPONSE = 0;

    //评价支持的渠道
    public static final Set<Integer> COMMENT_SUPPORT_CHANNEL_ID_SET =
            Sets.newHashSet(
                    ChannelType.MEITUAN.getValue(),
                    ChannelType.ELEM.getValue(),
                    ChannelType.JD2HOME.getValue(),
                    ChannelType.YOU_ZAN.getValue()
            );

    public void updateCommentReplyTemplate(StoreCommentReplyTemplateBO templateBO) {
        CommentReplyTemplateUpdateRequest request = new CommentReplyTemplateUpdateRequest();
        request.setTenantId(templateBO.getTenantId());
        request.setTemplateContent(templateBO.getTemplateContent());
        request.setOperatorUid(templateBO.getOperatorUid());
        request.setTemplateId(templateBO.getTemplateId());
        request.setStoreIds(templateBO.getStoreIdList());
        try {
            CommentReplyTemplateUpdateResponse response = channelCommentThriftService.updateCommentReplyTemplate(request);
            log.info("ChannelCommentThriftService.updateCommentReplyTemplate, request:{}, response:{}", request, response);
            if (response.getStatus().getCode() != 0) {
                throw new BizException(response.getStatus().getMessage());
            }
            } catch (TException e) {
                log.info("ChannelCommentThriftService.updateCommentReplyTemplate Exception, request:{}", request, e);
                throw new BizException(BaseResult.INTERNAL_SERVER_ERROR.getCode(), "添加回复模板错误");
            }
        }

    public void deleteCommentReplyTemplate(Long tenantId, Long templateId, Long operatorUid) {
        CommentReplyTemplateDeleteRequest request = new CommentReplyTemplateDeleteRequest();
        request.setTenantId(tenantId);
        request.setTemplateIdList(Arrays.asList(templateId));
        request.setOperatorUid(operatorUid);
        try {
            CommentReplyTemplateDeleteResponse response = channelCommentThriftService.deleteCommentReplyTemplate(request);
            log.info("ChannelCommentThriftService.deleteCommentReplyTemplate, request:{}, response:{}", request, response);

            if (response.getStatus().getCode() != 0) {
                throw new BizException(response.getStatus().getMessage());
            }
        } catch (TException e) {
            log.info("ChannelCommentThriftService.deleteCommentReplyTemplate Exception, request:{}", request, e);
            throw new BizException(BaseResult.INTERNAL_SERVER_ERROR.getCode(), "删除回复模板错误");
        }
    }

    public ChannelCommentReplyTemplateListDTO queryCommentReplyTemplateList(Long tenantId) {
        CommentReplyTemplateListQueryRequest request = new CommentReplyTemplateListQueryRequest();
        request.setTenantId(tenantId);
        try {
            CommentReplyTemplateListQueryResponse response = channelCommentThriftService.queryCommentReplyTemplateList(request);
            log.info("ChannelCommentThriftService.queryCommentReplyTemplateList, request:{}, response:{}", request, response);

            if (response.getStatus().getCode() != 0) {
                throw new BizException(response.getStatus().getMessage());
            }
            return new ChannelCommentReplyTemplateListDTO(response.getCommentReplyTemplateDTOList(),
                    response.getCommentReplyTemplateCountMax());
        } catch (TException e) {
            log.info("ChannelCommentThriftService.queryCommentReplyTemplateList Exception, request:{}", request, e);
            throw new BizException(BaseResult.INTERNAL_SERVER_ERROR.getCode(), "查询回复模板错误");
        }
    }

    public List<ChannelCommentLabelStatDTO> queryCommentStat(Long tenantId, List<Integer> channelIds, List<Long> storeIds, String startTime, String endTime) {
        CommentStatQueryRequest request = new CommentStatQueryRequest();
        request.setTenantId(tenantId);
        request.setStoreIds(storeIds);
        request.setStartTime(startTime);
        request.setEndTime(endTime);
        request.setChannelIds(channelIds);
        try {
            CommentStatQueryResponse response = channelCommentThriftService.queryCommentStat(request);
            if (response.getStatus().getCode() != 0) {
                throw new BizException(response.getStatus().getMessage());
            }
            if (response.getCommentStatDTO() == null || org.springframework.util.CollectionUtils.isEmpty(response.getCommentStatDTO().getCommentLabelStatDTOList())) {
                return new ArrayList<>();
            }
            return response.getCommentStatDTO().getCommentLabelStatDTOList();
        } catch (TException e) {
            log.error("ChannelCommentThriftService.queryCommentStat Exception, request:{}", request, e);
            throw new BizException(BaseResult.INTERNAL_SERVER_ERROR.getCode(), "查询统计信息错误");
        }
    }

    public List<ChannelCommentStatChannelDTO> queryCommentChannelStat(Long tenantId, List<Integer> channelIds, List<Long> storeIds, String startTime, String endTime) {
        CommentStatChannelListQueryRequest request = new CommentStatChannelListQueryRequest();
        request.setTenantId(tenantId);
        request.setStoreIds(storeIds);
        request.setStartTime(startTime);
        request.setEndTime(endTime);
        request.setChannelIds(channelIds);
        try {
            CommentStatChannelListQueryResponse response = channelCommentThriftService.queryChannelCommentStatList(request);
            log.info("ChannelCommentThriftService.queryChannelCommentStatList, request:{}, response:{}", request, response);
            if (response.getStatus().getCode() != 0) {
                throw new BizException(response.getStatus().getMessage());
            }
            if (response.getCommentStatChannelDTOList() == null || org.springframework.util.CollectionUtils.isEmpty(response.getCommentStatChannelDTOList())) {
                return new ArrayList<>();
            }
            return response.getCommentStatChannelDTOList();
        } catch (TException e) {
            log.error("ChannelCommentThriftService.queryChannelCommentStatList Exception, request:{}", request, e);
            throw new BizException(BaseResult.INTERNAL_SERVER_ERROR.getCode(), "查询渠道统计信息错误");
        }
    }

    public Page<ChannelCommentStatStoreDTO> queryCommentStoreStat(Long tenantId, List<Integer> channelIds, List<Long> storeIds, String startTime, String endTime, Integer page, Integer pageSize) {
        CommentStatStoreListQueryRequest request = new CommentStatStoreListQueryRequest();
        request.setTenantId(tenantId);
        request.setStoreIds(storeIds);
        request.setStartTime(startTime);
        request.setEndTime(endTime);
        request.setPageNum(page);
        request.setPageSize(pageSize);
        request.setChannelIds(channelIds);
        try {
            CommentStatStoreListQueryResponse response = channelCommentThriftService.queryStoreCommentStatList(request);
            log.info("ChannelCommentThriftService.queryStoreCommentStatList, request:{}, response:{}", request, response);
            if (response.getStatus().getCode() != 0) {
                throw new BizException(response.getStatus().getMessage());
            }
            Page<ChannelCommentStatStoreDTO> pageData = new Page<>(page, pageSize);
            pageData.setTotal(response.getPageInfoDTO().getTotalSize());
            pageData.addAll(response.getCommentStatStoreDTOList());
            return pageData;
        } catch (TException e) {
            log.error("ChannelCommentThriftService.queryStoreCommentStatList Exception, request:{}", request, e);
            throw new BizException(BaseResult.INTERNAL_SERVER_ERROR.getCode(), "查询门店统计信息错误");
        }
    }

    public void reply(Long tenantId, Long uid, String commentId, String replyDraft) {
        CommentReplyRequest request = new CommentReplyRequest();
        request.setTenantId(tenantId);
        request.setCommentId(commentId);
        request.setReplyDraftUid(uid);
        request.setReplyDraft(replyDraft);
        request.setReplyDraftSource(OperateSourceEnum.MERCHANT_MANAGEMENT_SYSTEM.name());
        try {
            CommentReplyResponse response = channelCommentThriftService.reply(request);
            if (response.getStatus().getCode() != 0) {
                throw new BizException(response.getStatus().getMessage());
            }
        } catch (TException e) {
            log.error("ChannelCommentThriftService.reply Exception, request:{}", request, e);
            throw new BizException(BaseResult.INTERNAL_SERVER_ERROR.getCode(), "提交超时！请重试");
        }
    }

    public PageResult<ChannelCommentDTO> queryCommentList(CommentListQueryRequest request) {
        try {
            CommentListQueryResponse response = channelCommentThriftService.queryCommentList(request);
            log.info("ChannelCommentThriftService.queryCommentList, request:{}, response:{}", request, response);

            if (response.getStatus().getCode() != 0) {
                throw new BizException(response.getStatus().getMessage());
            }
            PageInfoDTO pageInfoDTO = response.getPageInfoDTO();
            List<ChannelCommentDTO> commentDTOList = response.getCommentDTOList();

            return new PageResult<>(commentDTOList, pageInfoDTO.getPage(), pageInfoDTO.getSize(),
                    pageInfoDTO.getTotalSize());
        } catch (TException e) {
            log.info("ChannelCommentThriftService.queryCommentList Exception, request:{}", request, e);
            throw new BizException(BaseResult.INTERNAL_SERVER_ERROR.getCode(), "查询评价错误");
        }
    }

    public ChannelCommentDTO getCommentByTenantIdAndCommentId(Long tenantId, String commentId) {
        CommentGetRequest request = new CommentGetRequest();
        request.setTenantId(tenantId);
        request.setCommentId(commentId);
        try {
            CommentGetResponse response = channelCommentThriftService.getCommentByTenantIdAndCommentId(request);
            log.info("ChannelCommentThriftService.getCommentByTenantIdAndCommentId, request:{}, response:{}", request, response);

            if (response.getStatus().getCode() != 0) {
                throw new BizException(response.getStatus().getMessage());
            }
            return response.getCommentDTO();
        } catch (TException e) {
            log.info("ChannelCommentThriftService.getCommentByTenantIdAndCommentId Exception, request:{}", request, e);
            throw new BizException(BaseResult.INTERNAL_SERVER_ERROR.getCode(), "查询评价详情错误");
        }
    }

    /**
     * 查询评价评分规则
     *
     * @param tenantId 租户id
     * @return
     */
    public List<String> queryCommentLevelRules(Long tenantId) {
        CommentRuleQueryRequest request = new CommentRuleQueryRequest();
        request.setTenantId(tenantId);
        try {
            CommentRuleQueryResponse response = channelCommentThriftService.queryCommentRule(request);
            log.info("ChannelCommentThriftService.queryCommentLevelRules, request:{}, response:{}", request, response);

            if (response.getStatus().getCode() != 0) {
                throw new BizException(response.getStatus().getMessage());
            }

            ChannelCommentRuleDTO commentRuleDTO = response.getCommentRuleDTO();
            return commentRuleDTO != null ? commentRuleDTO.getCommentLevelRules() : Collections.emptyList();
        } catch (TException e) {
            log.error("ChannelCommentThriftService.queryCommentLevelRules TException, request:{}", request, e);
        }

        return Collections.emptyList();
    }

    public void addRecordListenNum(String recordId) {
        try {
            commentContactThriftService.addRecordListenNum(recordId);
        } catch (TException e) {
            log.error("invoke ommentContactThriftService.addRecordListenNum error", e);
        }
    }

    public PageResult<BadProcessedInfoDTO> queryBadCommentProcessedList(QueryBadCommentProcessedListReq req) {
        try {
            BadCommentProcessedListResp response = commentContactThriftService.queryBadCommentProcessedList(req);
            if (response.getStatus().getCode() != 0) {
                throw new BizException(response.getStatus().getMessage());
            }
            List<BadProcessedInfoDTO> badProcessedInfoList = response.getBadProcessedInfoList();
            PageInfoDTO pageInfoDTO = response.getPageInfoDTO();
            return new PageResult<>(badProcessedInfoList, pageInfoDTO.getPage(), pageInfoDTO.getSize(),
                    pageInfoDTO.getTotalSize());
        } catch (TException e) {
            log.error("invoke commentContactThriftService.queryBadCommentProcessedList error", e);
            throw new BizException("invoke commentContactThriftService.queryBadCommentProcessedList error", e);
        }
    }

    public CommonResponse<CommentReplyTemplateAddResp> addCommentReplyTemplate(CommentReplyTemplateAddReq req, User user) {
        CommentReplyTemplateAddRequest request = new CommentReplyTemplateAddRequest();
        request.setTemplateContent(req.getTemplateContent());
        request.setTenantId(user.getTenantId());
        request.setOperatorUid(user.getAccountId());
        try {
            request.setStoreIds(Splitter.on(ProjectConstants.ENGLISH_COMMA_DELIMITER).trimResults()
                    .splitToList(req.getStoreId()).stream().map(storeIdStr -> Long.valueOf(storeIdStr))
                    .collect(Collectors.toList()));
            CommentReplyTemplateAddResponse response = channelCommentThriftService.addCommentReplyTemplate(request);
            log.info("ChannelCommentThriftService.addCommentReplyTemplate, request:{}, response:{}", request, response);
            if (response.getStatus().getCode() != 0) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), response.getStatus().getMessage());
            }
            CommentReplyTemplateAddResp resp = new CommentReplyTemplateAddResp(response.getTemplateId());
            return CommonResponse.success(resp);
        } catch (TException e) {
            log.error("ChannelCommentThriftService.addCommentReplyTemplate Exception, request:{}", request, e);
            throw new CommonRuntimeException(e);
        }
    }

    public Long addCommentReplyTemplate(StoreCommentReplyTemplateBO templateBO) {
        CommentReplyTemplateAddRequest request = new CommentReplyTemplateAddRequest();
        request.setTenantId(templateBO.getTenantId());
        request.setTemplateContent(templateBO.getTemplateContent());
        request.setOperatorUid(templateBO.getOperatorUid());
        request.setStoreIds(templateBO.getStoreIdList());
        try {
            CommentReplyTemplateAddResponse response = channelCommentThriftService.addCommentReplyTemplate(request);
            log.info("ChannelCommentThriftService.addCommentReplyTemplate, request:{}, response:{}", request, response);
            if (response.getStatus().getCode() != 0) {
                throw new BizException(response.getStatus().getMessage());
            }
            return response.getTemplateId();

        } catch (TException e) {
            log.info("ChannelCommentThriftService.addCommentReplyTemplate Exception, request:{}", request, e);
            throw new BizException(BaseResult.INTERNAL_SERVER_ERROR.getCode(), "添加回复模板错误");
        }
    }

    public CommonResponse deleteCommentReplyTemplate(CommentReplyTemplateDeleteReq req, User user) {
        CommentReplyTemplateDeleteRequest request = new CommentReplyTemplateDeleteRequest();
        request.setTemplateIdList(Arrays.asList(req.getTemplateId()));
        request.setTenantId(user.getTenantId());
        request.setOperatorUid(user.getAccountId());
        try {
            CommentReplyTemplateDeleteResponse response = channelCommentThriftService.deleteCommentReplyTemplate(request);
            log.info("ChannelCommentThriftService.deleteCommentReplyTemplate, request:{}, response:{}", request, response);

            if (response.getStatus().getCode() != 0) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), response.getStatus().getMessage());
            }

            return CommonResponse.success(null);
        } catch (TException e) {
            log.error("ChannelCommentThriftService.deleteCommentReplyTemplate Exception, request:{}", request, e);
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse updateCommentReplyTemplate(CommentReplyTemplateUpdateReq req, User user) {
        CommentReplyTemplateUpdateRequest request = new CommentReplyTemplateUpdateRequest();
        request.setTemplateId(req.getTemplateId());
        request.setTenantId(user.getTenantId());
        request.setTemplateContent(req.getTemplateContent());
        request.setOperatorUid(user.getAccountId());
        try {
            CommentReplyTemplateUpdateResponse response = channelCommentThriftService.updateCommentReplyTemplate(request);
            log.info("ChannelCommentThriftService.deleteCommentReplyTemplate, request:{}, response:{}", request, response);

            if (response.getStatus().getCode() != 0) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), response.getStatus().getMessage());
            }

            return CommonResponse.success(null);
        } catch (TException e) {
            log.error("ChannelCommentThriftService.deleteCommentReplyTemplate Exception, request:{}", request, e);
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse<CommentReplyTemplateListQueryResp> queryCommentReplyTemplateList(User user, CommentReplyTemplateListQueryReq req) {
        CommentReplyTemplateListQueryRequest request = new CommentReplyTemplateListQueryRequest();
        request.setTenantId(user.getTenantId());
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        if(Objects.nonNull(req) && StringUtils.isNotBlank(req.getStoreId())){
            String storeId = req.getStoreId().split(ProjectConstants.ENGLISH_COMMA_DELIMITER)[0];
            request.setStoreId(Long.parseLong(storeId));
        }
        if(Objects.nonNull(req.getCommentShopId())){
            request.setStoreId(req.getCommentShopId());
        }
        try {
            CommentReplyTemplateListQueryResponse response = channelCommentThriftService.queryCommentReplyTemplateList(request);
            log.info("ChannelCommentThriftService.queryCommentReplyTemplateList, request:{}, response:{}", request, response);

            if (response.getStatus().getCode() != 0) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), response.getStatus().getMessage());
            }
            List<ChannelCommentReplyTemplateDTO> commentReplyTemplateDTOList = response.getCommentReplyTemplateDTOList();
            CommentReplyTemplateListQueryResp resp = CommentReplyTemplateListQueryResp.build(commentReplyTemplateDTOList,
                    response.getCommentReplyTemplateCountMax());
            return CommonResponse.success(resp);
        } catch (TException e) {
            log.error("ChannelCommentThriftService.queryCommentReplyTemplateList Exception, request:{}", request, e);
            throw new CommonRuntimeException(e);
        }
    }

    private List<Long> warehouseId2StoreIds(long tenantId, String warehouseIdStr) {
        List<Long> warehouseIdList = Splitter.on(ProjectConstants.ENGLISH_COMMA_DELIMITER).trimResults()
                .splitToList(warehouseIdStr).stream().filter(NumberUtils::isDigits).map(Long::valueOf)
                .collect(Collectors.toList());
        Map<Long, List<Long>> warehouseId2PoiIdMap = tenantWrapper.mapPoiIdsByWarehouseId(tenantId, warehouseIdList);
        return warehouseId2PoiIdMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
    }

    public CommonResponse<CommentListQueryResp> queryCommentList(User user, CommentListQueryReq req) {
        // 仓id转门店id
        CommentListQueryRequest request = req.convertToCommentListQueryRequest();
        request.setTenantId(user.getTenantId());
        //订单评价查询状态开关,默认返回true开启，表示只查询正常状态评价,false表示根据前端传参查询
        if (LionUtils.getOrderCommentStatusConfig()){
            request.setIsValid(1);
        }
        request.setChannelIds(validateAndGetChannelIds(user.getTenantId(), req.getChannelIds()));
        if (Objects.equals(PoiEntityTypeEnum.SHAREABLE_WAREHOUSE.code(), req.getEntityType())) {
            List<Long> storeIds = warehouseId2StoreIds(user.getTenantId(), req.getStoreId());
            if (CollectionUtils.isEmpty(storeIds)) {
                // 共享前置仓无绑定门店，直接返回空
                return CommonResponse.success(CommentListQueryResp.buildEmptyResp());
            }
            request.setStoreIds(storeIds);
        }

        try {
            CommentListQueryResponse response = channelCommentThriftService.queryCommentList(request);
            log.info("ChannelCommentThriftService.queryCommentList, request:{}, response:{}", request, response);

            if (response.getStatus().getCode() != 0) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), response.getStatus().getMessage());
            }
            List<ChannelCommentDTO> commentDTOList = response.getCommentDTOList();

            PageInfoDTO pageInfo = response.getPageInfoDTO();
            CommentListQueryResp resp = CommentListQueryResp.build(commentDTOList, pageInfo);

            // 设置门店名称
            fillStoreName(user.getTenantId(), resp.getCommentVOList());

            // 设置erp门店code
            Map<Long, String> shopId2ErpShopCodeMap = poiServiceFacade.queryShopId2ErpShopCodes(request.getTenantId(), request.getStoreIds());
            resp.getCommentVOList().forEach(vo -> {
                String storeId = shopId2ErpShopCodeMap.get(vo.getStoreId());
                vo.setErpShopCode(StringUtils.isBlank(storeId) ? Strings.EMPTY : storeId);
            });

            return CommonResponse.success(resp);
        } catch (TException e) {
            log.error("ChannelCommentThriftService.queryCommentList Exception, request:{}", request, e);
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse<CommentVO> getCommentByTenantIdAndCommentId(User user, String commentId) {
        CommentGetRequest request = new CommentGetRequest();
        request.setTenantId(user.getTenantId());
        request.setCommentId(commentId);
        try {
            CommentGetResponse response = channelCommentThriftService.getCommentByTenantIdAndCommentId(request);
            log.info("ChannelCommentThriftService.getCommentByTenantIdAndCommentId, request:{}, response:{}", request, response);

            if (response.getStatus().getCode() != 0) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), response.getStatus().getMessage());
            }
            ChannelCommentDTO commentDTO = response.getCommentDTO();
            CommentVO commentVO = CommentVO.build(commentDTO);
            if (commentVO != null) {
                // 设置门店名称
                fillStoreName(user.getTenantId(), Lists.newArrayList(commentVO));
                // 设置用户虚拟手机号,已经被其他接口替换
                //fillUserVirtualPhone(commentVO);
            }
            return CommonResponse.success(commentVO);
        } catch (TException e) {
            log.error("ChannelCommentThriftService.getCommentByTenantIdAndCommentId Exception, request:{}", request, e);
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse<CommentStatResp> queryCommentStat(User user, CommentStatQueryReq req) {
        CommentStatQueryRequest request = new CommentStatQueryRequest();
        request.setTenantId(user.getTenantId());
        request.setStartTime(DateUtils.format(DateUtils.getDayBeginTime(
                DateUtils.parse(req.getStartTime(), DateUtils.YYYY_MM_DD)), DateUtils.YYYY_MM_DD_HH_MM_SS));
        request.setEndTime(DateUtils.format(DateUtils.getDayEndTime(
                DateUtils.parse(req.getEndTime(), DateUtils.YYYY_MM_DD)), DateUtils.YYYY_MM_DD_HH_MM_SS));
        request.setStoreIds(Splitter.on(ProjectConstants.ENGLISH_COMMA_DELIMITER).trimResults()
                .splitToList(req.getStoreId()).stream().map(storeIdStr -> Long.valueOf(storeIdStr))
                .collect(Collectors.toList()));
        request.setChannelIds(validateAndGetChannelIds(user.getTenantId(), req.getChannelIds()));
        if (Objects.equals(PoiEntityTypeEnum.SHAREABLE_WAREHOUSE.code(), req.getEntityType())) {
            List<Long> storeIds = warehouseId2StoreIds(user.getTenantId(), req.getStoreId());
            if (CollectionUtils.isEmpty(storeIds)) {
                // 共享前置仓无绑定门店，直接返回空
                return CommonResponse.success(new CommentStatResp());
            }
            request.setStoreIds(storeIds);
        }

        try {
            CommentStatQueryResponse response = channelCommentThriftService.queryCommentStat(request);
            log.info("ChannelCommentThriftService.queryCommentStat, request:{}, response:{}", request, response);
            if (response.getStatus().getCode() != 0) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), response.getStatus().getMessage());
            }
            return CommonResponse.success(new CommentStatResp(response.getCommentStatDTO(),response.getIsContactGrayStore()));
        } catch (TException e) {
            log.error("ChannelCommentThriftService.queryCommentStat Exception, request:{}", request, e);
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse<VirtualNumberInfo> queryCommentVirtualNumber(User user, String commentId) {
        try {
            CommentContactResponse response = commentContactThriftService.queryCommentVirtualNumberInfo(user.getTenantId(), commentId);
            if (response.getStatus().getCode() != SUCCESS_RESPONSE || Objects.isNull(response.getCommentContactDTO())) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), "未获取到用户电话，不可联系");
            }
            CommentContactDTO commentcontactDTO = response.getCommentContactDTO();
            return CommonResponse.success(new VirtualNumberInfo(commentcontactDTO.getCanContactUser(), commentcontactDTO.getUserVirtualPhone()));
        } catch (Exception e) {
            log.error("CommentContactThriftService.queryCommentVirtualNumber Exception, commentId:{}", commentId, e);
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse replyComment(User user, CommentReplyReq req) {
        CommentReplyRequest request = new CommentReplyRequest();
        request.setTenantId(user.getTenantId());
        request.setReplyDraft(req.getReplyDraft());
        request.setCommentId(req.getCommentId());
        request.setReplyDraftSource(OperateSourceEnum.EMPOWER_ASSISTENT_APP.name());
        request.setReplyDraftUid(user.getAccountId());
        try {
            CommentReplyResponse response = channelCommentThriftService.reply(request);
            log.info("ChannelCommentThriftService.reply, request:{}, response:{}", request, response);
            if (response.getStatus().getCode() != 0) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), response.getStatus().getMessage());
            }
            return CommonResponse.success(null);
        } catch (TException e) {
            log.error("ChannelCommentThriftService.reply Exception, request:{}", request, e);
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse<CommentQueryBaseInfoVO> getCommentQueryBaseInfo(User user) {
        List<Channel> channels = tenantWrapper.queryChannelIds(user.getTenantId());
        if (CollectionUtils.isEmpty(channels)) {
            return CommonResponse.success(new CommentQueryBaseInfoVO(Lists.newArrayList()));
        }
        if (MccConfigUtil.getCommentFilterChannel()) {
            channels = channels.stream().filter(channel -> COMMENT_SUPPORT_CHANNEL_ID_SET.contains(channel.getId())).collect(Collectors.toList());
        }

        List<ChannelInfoVO> channelInfoVOList = OCMSUtils.convertChannelInfoVOList(channels);
        return CommonResponse.success(new CommentQueryBaseInfoVO(channelInfoVOList));
    }

    public CommonResponse<CommentRuleVO> queryCommentRule(Long tenantId) {
        CommentRuleQueryRequest request = new CommentRuleQueryRequest();
        request.setTenantId(tenantId);
        try {
            CommentRuleQueryResponse response = channelCommentThriftService.queryCommentRule(request);
            log.info("ChannelCommentThriftService.queryCommentRule, request:{}, response:{}", request, response);
            if (response.getStatus().getCode() != 0) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), response.getStatus().getMessage());
            }
            ChannelCommentRuleDTO commentRuleDTO = response.getCommentRuleDTO();
            List<String> commentLevelRules = Collections.emptyList();
            if (commentRuleDTO != null && CollectionUtils.isNotEmpty(commentRuleDTO.getCommentLevelRules())) {
                commentLevelRules = commentRuleDTO.getCommentLevelRules();
            }
            return CommonResponse.success(new CommentRuleVO(commentLevelRules));
        } catch (TException e) {
            log.error("ChannelCommentThriftService.queryCommentRule Exception, request:{}", request, e);
            return CommonResponse.success(new CommentRuleVO(Collections.emptyList()));
        }
    }

    private void fillStoreName(Long tenantId, List<CommentVO> commentVOList) {
        if (CollectionUtils.isEmpty(commentVOList)) {
            return;
        }
        Set<Long> storeIdSet = commentVOList.stream().map(CommentVO::getStoreId).collect(Collectors.toSet());
        Map<Long, String> storeIdToNameMap = storeWrapper.batchQueryStoreNameByStoreIds(tenantId,
                Lists.newArrayList(storeIdSet));
        commentVOList.stream().forEach(commentVO -> commentVO.setStoreName(storeIdToNameMap.get(commentVO.getStoreId())));
    }

    private List<Integer> validateAndGetChannelIds(Long tenantId, List<Integer> channelIds) {

        if (CollectionUtils.isNotEmpty(channelIds)) {
            return channelIds;
        }

        // 查询已开通的渠道
        List<Channel> channels = tenantWrapper.queryChannelIds(tenantId);
        Map<Integer, String> channelIdToNameMap = Optional.ofNullable(channels).map(List::stream).orElse(Stream.empty())
                .collect(Collectors.toMap(Channel::getId, Channel::getName));
        if (MapUtils.isEmpty(channelIdToNameMap)) {
            throw new CommonLogicException(ResultCode.FAIL, "无已开通的渠道");
        }

        return Lists.newArrayList(channelIdToNameMap.keySet());
    }

    private void fillUserVirtualPhone(CommentVO commentVO) {

        if (commentVO.getCanContactUser() == null || !commentVO.getCanContactUser()) {
            return;
        }
        QueryVirtualPhoneRequest request = new QueryVirtualPhoneRequest();
        request.setChannelId(commentVO.getChannelId());
        request.setChannelOrderId(commentVO.getChannelOrderId());
        request.setPhoneType(QueryPhoneType.CUSTOMER.getValue());
        try {
            CommonResponse<QueryVirtualPhoneResponse> response = ocmsOrderServiceWrapper.queryVirtualPhone(request);
            QueryVirtualPhoneResponse data = response.getData();
            commentVO.setUserVirtualPhone(data != null ? data.getPhoneNo() : null);
        } catch (Exception e) {
            log.error("查询隐私号错误, channelId:{}, channelOrderId:{}", commentVO.getChannelId(), commentVO.getChannelOrderId(), e);
            throw new CommonLogicException(ResultCode.FAIL, "查询隐私号错误");
        }

    }

    /**
     * 评价溯源-用户重新绑定订单号
     * 
     * @param tenantId
     * @param uid
     * @param commentId
     * @param associationChannelOrderId
     * @return
     */
    public CommonResponse commentAssociationChannelOrderId(Long tenantId, Long uid, String commentId,
            String associationChannelOrderId) {
        try {
            CommentAssociationChannelOrderIdRequest request = new CommentAssociationChannelOrderIdRequest();
            request.setTenantId(tenantId);
            request.setCommentId(commentId);
            request.setAssociationChannelOrderId(associationChannelOrderId);
            request.setOperateUid(uid);
            CommentAssociationChannelOrderIdResponse response = channelCommentThriftService
                    .commentAssociationChannelOrderId(request);
            log.info("ChannelCommentThriftService.commentAssociationChannelOrderId, request:{}, response:{}", request,
                    response);
            if (response.getStatus().getCode() != 0) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), response.getStatus().getMessage());
            } else {
                return CommonResponse.success(null);
            }
        } catch (Exception e) {
            log.error("operateUid:{} commentId:{} associationChannelOrderId:{} commentAssociationChannelOrderId error",
                    uid, commentId, associationChannelOrderId, e);
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "评论关联渠道订单号失败");
        }
    }

    public CommonResponse<ManualCheckUpdateCommentVo> manualCheckUpdateComment(String commentId, Long tenantId) {
        ManualCheckUpdateCommentRequest request = new ManualCheckUpdateCommentRequest();
        request.setCommentId(commentId);
        request.setTenantId(tenantId);
        try {
            ManualCheckUpdateCommentResponse response = channelCommentThriftService.manualCheckUpdateComment(request);
            log.info("ChannelCommentThriftService.manualCheckUpdateComment, request:{}, response:{}", request,
                    response);
            if (response.getStatus().getCode() != 0) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), response.getStatus().getMessage());
            } else {
                ManualCheckUpdateCommentVo vo = new ManualCheckUpdateCommentVo();
                vo.setResultState(response.getResultState());
                vo.setResultMessage(response.getResultTips());
                return CommonResponse.success(vo);
            }
        } catch (TException e) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "查询处理更新失败");
        }
    }

    public CommonResponse<ManualCheckDeleteCommentVo> manualCheckDeleteComment(String commentId, Long tenantId) {
        ManualCheckDeleteCommentRequest request = new ManualCheckDeleteCommentRequest();
        request.setCommentId(commentId);
        request.setTenantId(tenantId);
        try {
            ManualCheckDeleteCommentResponse response = channelCommentThriftService.manualCheckDeleteComment(request);
            log.info("ChannelCommentThriftService.manualCheckDeleteComment, request:{}, response:{}", request,
                    response);
            if (response.getStatus().getCode() != 0) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), response.getStatus().getMessage());
            } else {
                return CommonResponse.success(ManualCheckDeleteCommentVo.builder().status(response.getResultCode()).resultMessage(response.getResultTips()).build());
            }
        } catch (TException e) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "删除评价处理失败");
        }
    }

    public CommonResponse<Void> confirmDeleteComment(String commentId, Long tenantId, Long operatorId) {

        try {
            ConfirmDeleteCommentRequest request = new ConfirmDeleteCommentRequest();
            request.setCommentId(commentId);
            request.setTenantId(tenantId);
            request.setOperatorId(operatorId);
            ConfirmDeleteCommentResponse response = channelCommentThriftService.confirmDeleteComment(request);
            log.info("ChannelCommentThriftService.confirmDeleteComment, request:{}, response:{}", request, response);
            if (response.getStatus().getCode() != 0) {
                return CommonResponse.fail(ResultCode.FAIL.getCode(), response.getStatus().getMessage());
            } else {
                return CommonResponse.success(null);
            }
        } catch (Exception e) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), "删除评价处理失败");
        }
    }
}
