package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.SkuSortInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "查询商品分类响应"
)
@Data
@ApiModel("查询商品分类响应")
public class QuerySkuSortInfoVO {

    @FieldDoc(
            description = "品类信息列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "品类信息列表", required = true)
    private List<SkuSortInfoVO> skuSortInfos;
}
