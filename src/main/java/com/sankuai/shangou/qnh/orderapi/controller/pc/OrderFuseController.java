package com.sankuai.shangou.qnh.orderapi.controller.pc;

import com.alibaba.fastjson.JSONObject;
import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.meituan.servicecatalog.api.annotations.ExtensionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ParamType;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.shangou.saas.common.enums.PickRecordButtonEnum;
import com.meituan.shangou.saas.dto.StatusCodeEnum;
import com.meituan.shangou.saas.order.management.client.enums.RecoBusinessIdEnum;
import com.meituan.shangou.saas.order.management.client.export.dto.request.ExportLimitRequest;
import com.meituan.shangou.saas.order.management.client.export.dto.request.FuseOrderDetailListExportTaskCreateRequest;
import com.meituan.shangou.saas.order.management.client.export.dto.response.ExportLimitResponse;
import com.meituan.shangou.saas.order.management.client.export.dto.response.ExportResponse;
import com.meituan.shangou.saas.order.management.client.export.service.FuseOrderListExportThriftService;
import com.meituan.shangou.saas.order.management.client.utils.BusinessIdTracer;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.tenant.thrift.common.enums.TenantBusinessModeEnum;
import com.sankuai.meituan.reco.pickselect.thrift.picking.rider.dto.RiderPickWorkOrderDTO;
import com.sankuai.meituan.shangou.empower.auth.sdk.bean.AppIdEnum;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.AccountTypeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.AccountInfoVo;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.dto.DeliveryChannelDto;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.shangou.qnh.orderapi.annotation.ResultDataSecurity;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import com.sankuai.shangou.qnh.orderapi.controller.BaseController;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.CommonDataBO;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.DeliveryInfoBO;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.OrderFuseDetailBO;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.OrderFuseFinanceDetailBO;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.OrderFuseQueryBO;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.OrderFuseQueryStatisticsDataBO;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.OrderItemFuseQueryBO;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.OrderItemFuseQueryStatisticsDataBO;
import com.sankuai.shangou.qnh.orderapi.domain.request.CreateQnhInvoiceRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.QueryVirtualPhoneRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.*;
import com.sankuai.shangou.qnh.orderapi.domain.response.CreateQnhInvoiceResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.*;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.QueryVirtualPhoneResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.CheckRefundResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.CheckSelfDeliveryCodeResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.CommonFuseResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.CommonResponse;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.*;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.*;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.AfsOrderFuseDetailVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.BaseInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.DeliveryStatusVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.ExportFieldsVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.ItemInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.OrderAdjustRecordVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.OrderExportMaxCountVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.OrderFuseDetailVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.OrderItemMoneyRefundCheckVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.OrderTrackVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.PageInfoVO;
import com.sankuai.shangou.qnh.orderapi.enums.app.ErrorCodeEnum;
import com.sankuai.shangou.qnh.orderapi.enums.pc.*;
import com.sankuai.shangou.qnh.orderapi.enums.AuditResultEnum;
import com.sankuai.shangou.qnh.orderapi.enums.AuditStageEnum;
import com.sankuai.shangou.qnh.orderapi.exception.pc.CommonRuntimeException;
import com.sankuai.shangou.qnh.orderapi.exception.pc.ParamInvalidException;
import com.sankuai.shangou.qnh.orderapi.facade.auth.AuthFacade;
import com.sankuai.shangou.qnh.orderapi.remote.*;
import com.sankuai.shangou.qnh.orderapi.service.common.GiftBagService;
import com.sankuai.shangou.qnh.orderapi.service.pc.ChannelOrderService;
import com.sankuai.shangou.qnh.orderapi.service.pc.FuseAfterSaleApplyService;
import com.sankuai.shangou.qnh.orderapi.service.pc.FuseOrderService;
import com.sankuai.shangou.qnh.orderapi.service.pc.OrderFuseService;
import com.sankuai.shangou.qnh.orderapi.service.pc.SaleReturnService;
import com.sankuai.shangou.qnh.orderapi.service.pc.TenantService;
import com.sankuai.shangou.qnh.orderapi.umwarehouse.utils.GoodsCodeCompareUtil;
import com.sankuai.shangou.qnh.orderapi.utils.CommonUsedUtil;
import com.sankuai.shangou.qnh.orderapi.utils.pc.CookieUtil;
import com.sankuai.shangou.qnh.orderapi.utils.pc.MccConfigUtil;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ResponseHandler;
import com.sankuai.shangou.qnh.orderapi.utils.pc.RpcInvoker;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 订单组件
 * @Author: <EMAIL>
 * @Date: 2022/12/05 下午3:22
 */
@InterfaceDoc(
        displayName = "订单融合相关接口",
        type = "restful",
        scenarios = "用于管理后台通用订单组件",
        description = "订单融合相关接口"
)
@Api(tags = "订单融合相关接口")
@Slf4j
@RestController
@RequestMapping("/api/v1/orderfuse")
public class OrderFuseController extends BaseController {

    @Autowired
    private ChannelOrderService channelOrderService;

    @Autowired
    private AuthRemoteService authRemoteService;

    @Autowired
    private AccountRemoteService accountRemoteService;

    @Autowired
    private FuseOrderService fuseOrderService;

    @Autowired
    private DeliveryRemoteService deliveryRemoteSercie;

    @Autowired
    private PickSelectRemoteService pickSelectRemoteService;

    @Autowired
    private OrderFuseService orderFuseService;

    @Autowired
    private SaleReturnService saleReturnService;

    @Autowired
    private FuseOrderListExportThriftService fuseOrderListExportThriftService;

    @Autowired
    private DeliveryChannelRemoteService deliveryChannelRemoteService;

    @Autowired
    private FuseAfterSaleApplyService fuseAfterSaleApplyService;

    @Autowired
    private TenantService tenantService;

    @Autowired
    private AuthFacade authFacade;
    @Resource
    private OCMSOrderRemoteService ocmsOrderRemoteService;
    @Resource
    private OcmsChannelRemoteService ocmsChannelRemoteService;

    @Resource
    private GiftBagService giftBagService;

    @MethodDoc(
            displayName = "查询选项数据列表",
            description = "查询选项数据列表",
            parameters = {
                    @ParamDoc(
                            name = "typeList",
                            description = "模板类型列表",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无需鉴权")
            },
            returnValueDescription = "",
            restExampleUrl = "https://fnsaas.waimai.test.sankuai.com/api/v1/common/orderfuse/getOptionsList",
            restExamplePostData = "type=xxx",
            restExampleResponseData = "{\"code\":0,\"data\":{\"list\":[{\"code\":\"string\",\"value\":\"string\"}]},\"msg\":\"string\"}"
    )
    @ApiOperation(value = "查询选项数据")
    @RequestMapping(value = "/getOptionsList", method = RequestMethod.POST)
    public Result<ListResultData<UiOptionData>> getOptionsList(@Valid @RequestBody TypeListRequest request) {
        if(CollectionUtils.isEmpty(request.getTypeList())){
            throw new BizException("参数不能为空");
        }
        HashMap<String, List<UiOption>> uiOptionMap = new HashMap<>();
        try {
            request.getTypeList().forEach(type -> {
                Optionable optionable = getTypeEnum(type);
                if (optionable == null) {
                    throw new BizException("类型非法");
                }
                uiOptionMap.put(type, optionable.toOptions());
            });
            return ResultBuilder.buildSuccessResult(uiOptionMap);
        } catch (BizException e) {
            log.debug("getOptionsList error", e);
            return ResultBuilder.buildFailResult(e.getMessage());
        } catch (Exception e) {
            log.error("getOptionsList error", e);
            return ResultBuilder.buildFailResult();
        }
    }


    @MethodDoc(
            displayName = "查询多级选项数据列表",
            description = "查询多级选项数据列表",
            parameters = {
                    @ParamDoc(
                            name = "type",
                            description = "模板类型列表",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "无需鉴权")
            },
            returnValueDescription = "",
            restExampleUrl = "https://fnsaas.waimai.test.sankuai.com/api/v1/common/orderfuse/getMultistageOptions",
            restExamplePostData = "type=xxx",
            restExampleResponseData = "{\"code\":0,\"data\":{\"list\":[{\"code\":\"string\",\"value\":\"string\"}]},\"msg\":\"string\"}"
    )
    @ApiOperation(value = "查询多级选项数据列表")
    @RequestMapping(value = "/getMultistageOptions", method = RequestMethod.GET)
    public Result getMultistageOptions(String type) {
        try {
            MultiStageOptionable optionable = getMultiStageEnum(type);
            if (optionable == null) {
                throw new BizException("类型非法");
            }
            Long tenantId = ContextHolder.currentUserTenantId();
            if(MccConfigUtil.isUseNewOrderMark(tenantId) && optionable == OrderLabelEnum.POS){
                List<UiOptionMultiStage> uiOptionMultiStages = fuseOrderService.queryOrderSearchLabel(tenantId);
                return ResultBuilder.buildSuccessResult(uiOptionMultiStages);
            }
            if(MccConfigUtil.isUseNewOrderMark(tenantId) && optionable == RefundOrderLabelEnum.SUB_CHANNEL){
                List<UiOptionMultiStage> uiOptionMultiStages = fuseOrderService.queryRefundSearchLabel(tenantId);
                return ResultBuilder.buildSuccessResult(uiOptionMultiStages);
            }
            // 歪马页面特殊处理标签筛选条件
            if (optionable == OrderLabelEnum.DRUNK_HORSE_LABLE) {
                return ResultBuilder
                        .buildSuccessResult(optionable.toOptions(Arrays.asList(OrderLabelEnum.MT_FAMOUS_TAVERN,OrderLabelEnum.MT_FACAI_WINE)));
            } else if (optionable == RefundOrderLabelEnum.DRUNK_HORSE_REFUND_LABLE) {
                return ResultBuilder
                        .buildSuccessResult(optionable.toOptions(Arrays.asList(RefundOrderLabelEnum.MT_FAMOUS_TAVERN,RefundOrderLabelEnum.MT_FACAI_WINE)));
            }
            return ResultBuilder.buildSuccessResult(optionable.toOptions());
        } catch (BizException e) {
            log.debug("getMultistageOptions error", e);
            return ResultBuilder.buildFailResult(e.getMessage());
        } catch (Exception e) {
            log.error("getMultistageOptions error", e);
            return ResultBuilder.buildFailResult();
        }
    }


    /**
     * 查询订单列表
     *
     * @param request
     * @return
     */
    @MethodDoc(
            displayName = "分页查询全部订单列表",
            description = "分页查询全部订单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "全部订单列表分页查询请求",
                            type = OrderFuseQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/v1/orderfuse/newQueryList",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "poiIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY),
            @SecurityParam(value = "warehouseIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiOperation(value = "分页查询全部订单列表")
    @RequestMapping(value = "/newQueryList", method = {RequestMethod.POST, RequestMethod.GET})
    public CommonFuseResponse<OrderFuseListResponse> orderList(@Valid @RequestBody OrderFuseQueryRequest request) {
        request.selfCheck();
        Long currentStoreId = getCurrentStoreId(request.getPoiIdList());
        BusinessIdTracer.putBusiness(RecoBusinessIdEnum.OPEN_HISTORY_SEARCH);
        List<Long> poiIds = Lists.newArrayList();
        List<Long> warehouseIds = Lists.newArrayList();
        boolean hasPermission;
        // 订单号大于4位，则查当前账号全部门店
        if(StringUtils.isNotEmpty(request.getOrderId()) && request.getOrderId().length() > 4){
            hasPermission = fillPermissionParam(null, null, poiIds, warehouseIds);
        }else {
            hasPermission = fillPermissionParam(request.getPoiIdList(), request.getWarehouseIdList(), poiIds, warehouseIds);
        }
        if (!hasPermission) {
            OrderFuseListResponse response = new OrderFuseListResponse();
            PageInfoVO pageInfo = new PageInfoVO();
            pageInfo.setPage(request.getPage());
            pageInfo.setSize(request.getPageSize());
            pageInfo.setTotalPage(0);
            pageInfo.setTotalSize(0);
            response.setPageInfo(pageInfo);
            return CommonFuseResponse.success(response);
        }
        request.setPoiIdList(poiIds);
        request.setWarehouseIdList(warehouseIds);
        OrderFuseQueryBO orderFuseQueryBO = new OrderFuseQueryBO(request);
        orderFuseQueryBO.setTenantId(ContextHolder.currentUserTenantId());
        return fuseOrderService.queryFuseOrders(orderFuseQueryBO,currentStoreId);
    }

    private Long getCurrentStoreId(List<Long> poiIdList) {
        if(CollectionUtils.isEmpty(poiIdList)){
            return null;
        }
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String appId = CookieUtil.getCookieValue(servletRequestAttributes.getRequest(), CookieTokenTypeEnum.APP_ID.getName());
        if(StringUtils.equals(appId, String.valueOf(AppIdEnum.APP_30.getAuthAppId()))) {
            return poiIdList.get(0);
        }
        return null;
    }

    /**
     * 查询拣货员列表
     *
     * @param request
     * @return
     */
    @MethodDoc(
            displayName = "查询拣货员列表",
            description = "查询拣货员列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询拣货员列表请求",
                            type = QueryShopPickerRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "  \"code\": 0,\n" +
                    "  \"message\": \"\",\n" +
                    "  \"data\": {\n" +
                    "      \"accountInfoList\":[\n" +
                    "            {\n" +
                    "                \"accountId\":134,\n" +
                    "                \"accountName\":\"14124\",\n" +
                    "                \"account\":\"141\",\n" +
                    "                \"pickedCount\":123,\n" +
                    "                \"waitPickCount\":1243,\n" +
                    "                 \"workStatus\":0\n" +
                    "            }\n" +
                    "      \n" +
                    "      ]\n" +
                    "  }\n" +
                    " }",
            restExampleUrl = "/api/v1/orderfuse/queryShopPicker",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "查询拣货员列表")
    @RequestMapping(value = "/queryShopPicker", method = {RequestMethod.POST})
    public CommonFuseResponse<QueryShopPickerResponse> queryShopPicker(@Valid @RequestBody QueryShopPickerRequest request) {
        request.selfCheck();
        return fuseOrderService.queryShopPicker(request);
    }

    @MethodDoc(
            displayName = "任务分派",
            description = "任务分派",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "任务分派",
                            type = TransferOrderRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/v1/orderfuse/transfer",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "任务分派(直接下发传递accountId 0,accountName system)")
    @RequestMapping(value = "/transfer", method = {RequestMethod.POST})
    @ResponseBody
    public CommonFuseResponse<String> transfer(@Valid @RequestBody TransferOrderRequest request) {
        request.selfCheck();
        return fuseOrderService.transferPicker(request);
    }


    @MethodDoc(
            displayName = "订单子状态数量查询",
            description = "订单子状态数量查询",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "订单子状态数量查询",
                            type = HttpServletRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/v1/orderfuse/queryordersubstatuscount",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "poiIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY),
            @SecurityParam(value = "warehouseIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiOperation(value = "订单子状态数量查询")
    @RequestMapping(value = "/queryordersubstatuscount", method = {RequestMethod.POST, RequestMethod.GET})
    public CommonFuseResponse<OrderSubStatusCountResponse> queryOrderSubStatusCount(@Valid @RequestBody OrderFuseQueryRequest request) {
        request.selfCheck();
        BusinessIdTracer.putBusiness(RecoBusinessIdEnum.OPEN_HISTORY_SEARCH);
        List<Long> poiIds = Lists.newArrayList();
        List<Long> warehouseIds = Lists.newArrayList();
        boolean hasPermission;
        // 订单号大于4位，则查当前账号全部门店
        if(StringUtils.isNotEmpty(request.getOrderId()) && request.getOrderId().length() > 4){
            hasPermission = fillPermissionParam(null, null, poiIds, warehouseIds);
        }else {
            hasPermission = fillPermissionParam(request.getPoiIdList(), request.getWarehouseIdList(), poiIds, warehouseIds);
        }
        if (!hasPermission) {
            OrderSubStatusCountResponse response = new OrderSubStatusCountResponse();
            response.setAllSubStatuseCount(0);
            response.setWaitToTakeOrderCount(0);
            response.setWaitToPickCount(0);
            response.setOrderCancelingCount(0);
            response.setOrderCompletedCount(0);
            response.setDeliveringCount(0);
            response.setPickFinishCount(0);
            response.setOrderCanceledCount(0);
            response.setDeliveringErrorCount(0);
            response.setOrderCancelingCount(0);
            return CommonFuseResponse.success(response);
        }
        request.setPoiIdList(poiIds);
        request.setWarehouseIdList(warehouseIds);
        OrderFuseQueryBO orderFuseQueryBO = new OrderFuseQueryBO(request);
        orderFuseQueryBO.setTenantId(ContextHolder.currentUserTenantId());
        return fuseOrderService.queryWaitDeliverySubTypeCount(orderFuseQueryBO);
    }


    /**
     * 查询订单列表统计数据
     *
     * @param request
     * @return
     */
    @MethodDoc(
            displayName = "查询订单列表统计数据",
            description = "查询订单列表统计数据",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询订单列表统计数据",
                            type = OrderFuseQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/v1/orderfuse/getOrderStatisticsData",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "poiIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY),
            @SecurityParam(value = "warehouseIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiOperation(value = "查询订单列表统计数据")
    @RequestMapping(value = "/getOrderStatisticsData", method = {RequestMethod.POST, RequestMethod.GET})
    public CommonFuseResponse<OrderFuseStatisticsResponse> getOrderStatisticsData(@Valid @RequestBody OrderFuseStatisticsDataQueryRequest request) {
        request.selfCheck();
        BusinessIdTracer.putBusiness(RecoBusinessIdEnum.OPEN_HISTORY_SEARCH);
        if(request.isContainFuzzSearch()){
            return CommonFuseResponse.success(OrderFuseStatisticsResponse.builder().build());
        }
        List<Long> poiIds = Lists.newArrayList();
        List<Long> warehouseIds = Lists.newArrayList();
        boolean hasPermission;
        // 订单号大于4位，则查当前账号全部门店
        if(StringUtils.isNotEmpty(request.getOrderId()) && request.getOrderId().length() > 4){
            hasPermission = fillPermissionParam(null, null, poiIds, warehouseIds);
        }else {
            hasPermission = fillPermissionParam(request.getPoiIdList(), request.getWarehouseIdList(), poiIds, warehouseIds);
        }
        if (!hasPermission) {
            return CommonFuseResponse.success(OrderFuseStatisticsResponse.builder().build());
        }
        request.setPoiIdList(poiIds);
        request.setWarehouseIdList(warehouseIds);
        OrderFuseQueryStatisticsDataBO orderFuseQueryStatisticsDataBO = new OrderFuseQueryStatisticsDataBO(request);
        orderFuseQueryStatisticsDataBO.setTenantId(ContextHolder.currentUserTenantId());
        return fuseOrderService.queryFuseOrderStatisticsData(orderFuseQueryStatisticsDataBO);
    }

    /**
     * 查询订单明细列表统计数据
     *
     * @param request
     * @return
     */
    @MethodDoc(
            displayName = "查询订单明细列表统计数据", description = "查询订单明细列表统计数据",
            parameters = {@ParamDoc(
                    name = "request", description = "查询订单明细列表统计数据",
                    type = OrderItemFuseStatisticsDataQueryRequest.class, paramType = ParamType.REQUEST_BODY,
                    rule = "非空", requiredness = Requiredness.REQUIRED
            )}, restExampleResponseData = "", restExampleUrl = "/api/v1/orderfuse/getOrderItemStatisticsData",
            extensions = {@ExtensionDoc(name = "SECURITY_PRIVILEGE", content = "数据鉴权逻辑：门店权限"),
                @ExtensionDoc(name = "SECURITY_PRIVILEGE_VERIFY", content = "True"),}
    )
    @DataSecurity(
        {@SecurityParam(value = "poiIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY),
            @SecurityParam(value = "warehouseIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)}
    )
    @ApiOperation(value = "查询订单明细列表统计数据")
    @RequestMapping(value = "/getOrderItemStatisticsData", method = {RequestMethod.POST, RequestMethod.GET})
    public CommonFuseResponse<OrderItemFuseStatisticsResponse>
            getOrderItemStatisticsData(@Valid @RequestBody OrderItemFuseStatisticsDataQueryRequest request) {
        request.selfCheck();
        BusinessIdTracer.putBusiness(RecoBusinessIdEnum.OPEN_HISTORY_SEARCH);
        List<Long> poiIds = Lists.newArrayList();
        List<Long> warehouseIds = Lists.newArrayList();
        boolean hasPermission = fillPermissionParam(request.getPoiIdList(), request.getWarehouseIdList(), poiIds,
                warehouseIds);
        if (!hasPermission) {
            OrderItemFuseStatisticsResponse response = new OrderItemFuseStatisticsResponse();
            return CommonFuseResponse.success(response);
        }
        request.setPoiIdList(poiIds);
        request.setWarehouseIdList(warehouseIds);

        OrderItemFuseQueryStatisticsDataBO orderItemFuseQueryStatisticsDataBO = new OrderItemFuseQueryStatisticsDataBO(request);
        orderItemFuseQueryStatisticsDataBO.setTenantId(ContextHolder.currentUserTenantId());
        return fuseOrderService.queryItemFuseOrderStatisticsData(orderItemFuseQueryStatisticsDataBO);
    }

    /**
     * 查询订单明细列表
     *
     * @param request
     * @return
     */
    @MethodDoc(
            displayName = "分页查询全部订单明细列表",
            description = "分页查询全部订单明细列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "全部订单明细列表分页查询请求",
                            type = OrderFuseQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/v1/orderfuse/getOrderProductsList",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "poiIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY),
            @SecurityParam(value = "warehouseIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiOperation(value = "分页查询全部订单明细列表")
    @RequestMapping(value = "/getOrderProductsList", method = {RequestMethod.POST, RequestMethod.GET})
    public CommonFuseResponse<OrderItemFuseListResponse> getOrderProductsList(@Valid @RequestBody OrderItemFuseQueryRequest request) {
        request.selfCheck();
        BusinessIdTracer.putBusiness(RecoBusinessIdEnum.OPEN_HISTORY_SEARCH);
        List<Long> poiIds = Lists.newArrayList();
        List<Long> warehouseIds = Lists.newArrayList();
        boolean hasPermission = fillPermissionParam(request.getPoiIdList(), request.getWarehouseIdList(), poiIds, warehouseIds);
        if (!hasPermission) {
            OrderItemFuseListResponse response = new OrderItemFuseListResponse();
            PageInfoVO pageInfo = new PageInfoVO();
            pageInfo.setPage(request.getPage());
            pageInfo.setSize(request.getPageSize());
            pageInfo.setTotalPage(0);
            pageInfo.setTotalPage(0);
            response.setPageInfo(pageInfo);
            return CommonFuseResponse.success(response);
        }
        request.setPoiIdList(poiIds);
        request.setWarehouseIdList(warehouseIds);
        OrderItemFuseQueryBO orderItemFuseQueryBO = new OrderItemFuseQueryBO(request);
        orderItemFuseQueryBO.setTenantId(ContextHolder.currentUserTenantId());
        return fuseOrderService.queryFuseOrderItems(orderItemFuseQueryBO);
    }



    /**
     * 查询订单详情
     *
     * @param request
     * @returnu
     */
    @MethodDoc(
            displayName = "查询中台订单详情接口",
            description = "查询中台订单详情接口",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "查询在线订单列表请求"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "/api/v1/orderfuse/detail",
            restExamplePostData = "{\"channelId\": \"100\",\"orderId\": \"001\"}",
            restExampleResponseData = "{\"code\": 0,\"data\": {\"baseInfo\": {\"afterSaleApplyStatus\": \"string\",\"amount\": \"string\",\"channelName\": \"string\",\"createTime\": \"string\",\"deliveryAmount\": \"string\",\"deliveryMethod\": \"string\",\"deliveryOrderCreateTime\": \"string\",\"invoiceTaxNo\": \"string\",\"invoiceTitle\": \"string\",\"merchantAmount\": \"string\",\"needInvoice\": 0,\"orderId\": \"string\",\"packageAmount\": \"string\",\"paidAmount\": \"string\",\"paidOnline\": 0,\"platformAmount\": \"string\",\"poiName\": \"string\",\"receiverAddress\": \"string\",\"receiverName\": \"string\",\"receiverPhone\": \"string\",\"refundReason\": \"string\",\"refundTagId\": \"string\",\"riderName\": \"string\",\"riderPhone\": \"string\",\"status\": \"string\"},\"deliveryDetail\": [{\"deliveryStatus\": \"string\",\"updateTime\": \"string\"}],\"itemInfo\": [{\"isRefund\": \"string\",\"quantity\": 0,\"refundCount\": \"string\",\"sku\": \"string\",\"skuName\": \"string\",\"spec\": \"string\",\"totalPrice\": \"string\",\"unit\": \"string\",\"unitPrice\": \"string\",\"upc\": \"string\"}],\"operateLog\": [{\"opContent\": \"string\",\"opTime\": \"string\",\"opType\": \"string\",\"operator\": \"string\"}],\"promotionInfo\": [{\"agentAmount\": \"string\",\"logisticsBearAmount\": \"string\",\"merchantAmount\": \"string\",\"platformAmount\": \"string\",\"promotionAmount\": \"string\",\"promotionName\": \"string\"}]},\"msg\": \"string\"}"
    )
    @ApiOperation(value = "查询中台订单详情")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @ResultDataSecurity
    @RequestMapping(value = "/detail", method = RequestMethod.POST)
    public Result<OrderFuseDetailVO> queryDetail(@RequestBody QueryDetailRequest request) {
        BusinessIdTracer.putBusiness(RecoBusinessIdEnum.OPEN_HISTORY_SEARCH);
        return process(request, "queryDetail", r -> {
            Pair<OrderFuseDetailBO, Map<Integer, DeliveryChannelDto>> orderFuseDetailBO = fuseOrderService.queryDetail(request.getOrderId()
                    , ContextHolder.currentUserTenantId(), Integer.valueOf(r.getChannelId()), ContextHolder.currentUid());
            OrderFuseFinanceDetailBO orderFuseFinanceDetailBO = fuseOrderService.queryFinanceDetail(orderFuseDetailBO.getKey().getBaseInfo().getOfflineOrderId(), orderFuseDetailBO.getKey().getBaseInfo().getPoiId(),
                    ContextHolder.currentUserTenantId(), 1);
            orderFuseDetailBO.getKey().mergeFinanceInfo(orderFuseFinanceDetailBO);
            OrderFuseDetailVO detaiFuselVO = orderFuseDetailBO.getKey().toOrderFuseDetailVO();
            // 查询是否发生过调整
            CommonDataBO<List<OrderAdjustRecordVO>> commonDataBO = channelOrderService.queryAdjustOrderRecord(ContextHolder.currentUserTenantId(),
                    Integer.parseInt(request.getChannelId()), request.getOrderId());
            detaiFuselVO.setHasOrderAdjustLog(CollectionUtils.isNotEmpty(commonDataBO.getData()));
            //租户是否为一门店多摊位租户
            detaiFuselVO.setIsOneStoreMoreBooth(checkOneStoreMoreBooth(ContextHolder.currentUserTenantId()));

            List<DeliveryInfoBO> deliveryInfoBOList=new ArrayList<>();
            if(detaiFuselVO.getBaseInfo()!=null){
                deliveryInfoBOList=deliveryRemoteSercie.queryActiveAndSucDeliveryOrderByOrderId(orderFuseDetailBO.getKey().getBaseInfo().getOfflineOrderId());
                //歪马租户查拣货复核照片
                try {
                    if (MccConfigUtil.checkQueryPickWorkOrder(ContextHolder.currentUserTenantId())) {
                        RiderPickWorkOrderDTO pickWorkOrder = pickSelectRemoteService.getPickWorkOrder(ContextHolder.currentUserTenantId(),request.getOrderId(),
                                Integer.valueOf(request.getChannelId()),
                                detaiFuselVO.getBaseInfo().getPoiId());

                        detaiFuselVO.setPickingCheckPictureUrl(pickWorkOrder.getPickingCheckPictureUrl());
                        detaiFuselVO.setPickingCheckPictureUrlList(pickWorkOrder.getPickingCheckPictureUrlList());
                    }
                } catch(Exception e) {
                    //不影响主流程
                    log.warn("查询拣货工单信息失败,orderId:{}",request.getOrderId(),e);
                }
            }

            Map<Integer, String> channelMap = null;
            if((Objects.nonNull(ContextHolder.currentUserTenantId()) || Objects.nonNull(detaiFuselVO.getBaseInfo().getPoiId())) && !deliveryChannelRemoteService.checkTenantAndStore(ContextHolder.currentUserTenantId(),detaiFuselVO.getBaseInfo().getPoiId())){
                channelMap = deliveryChannelRemoteService.tratranslateToChannelNameMap(orderFuseDetailBO.getValue());
                detaiFuselVO.setDeliveryInfoList(orderFuseDetailBO.getKey().toDeliveryInfoVo(deliveryInfoBOList,channelMap));
            }else {
                detaiFuselVO.setDeliveryInfoList(orderFuseDetailBO.getKey().toDeliveryInfoVo(deliveryInfoBOList, null));
            }
            addGift2ItemAndSort(orderFuseDetailBO.getKey(), detaiFuselVO.getItemInfo(), detaiFuselVO.getBaseInfo());
            //拣货记录按钮
            dealAddPickRecordButton(detaiFuselVO, ContextHolder.currentUserTenantId(), orderFuseDetailBO);

            giftBagService.appendGiftBagInfo(detaiFuselVO, ContextHolder.currentUserTenantId());

            return ResultBuilder.buildSuccess(detaiFuselVO);
        });
    }

    private Integer checkOneStoreMoreBooth(Long tenantId) {
        try {
            if(com.sankuai.shangou.qnh.orderapi.utils.store.MccConfigUtil.checkIsOnePoiWithSeveralBoothTenant(tenantId)){
                return IsOneStoreMoreBoothEnum.IS_ONE_STORE_MORE_BOOTH.getValue();
            }
        } catch (Exception e) {
            log.info("checkOneStoreMoreBooth is error tenantId: {}, e= ", tenantId, e);
        }
        return IsOneStoreMoreBoothEnum.NO_ONE_STORE_MORE_BOOTH.getValue();
    }

    private void dealAddPickRecordButton(OrderFuseDetailVO detaiFuselVO, Long tenantId, Pair<OrderFuseDetailBO, Map<Integer, DeliveryChannelDto>> orderFuseDetailBO) {
        try {
            if(Objects.isNull(detaiFuselVO)){
                return;
            }
            if(Objects.isNull(tenantId) || Objects.isNull(orderFuseDetailBO)){
                detaiFuselVO.setPickRecordButton(PickRecordButtonEnum.CLOSE_BUTTON.getValue());
                return;
            }
            Boolean erpOrDrunkHorseTenant = ocmsOrderRemoteService.isErpOrDrunkHorseTenant(tenantId);
            detaiFuselVO.setPickRecordButton(CommonUsedUtil.setPickGoodsRecordButton(detaiFuselVO.getBaseInfo().getChannelId(), orderFuseDetailBO.getKey().getPickStatus(), tenantId, erpOrDrunkHorseTenant));
        } catch (Exception e) {
            log.info("dealAddPickRecordButton is error detaiFuselVO: {} ", detaiFuselVO);
        }
    }

    /**
     * 查询订单详情
     *
     * @param request
     * @return
     */
    @MethodDoc(
            displayName = "查询中台退单详情接口",
            description = "查询中台退单详情接口",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "查询在线订单列表请求"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "/api/v1/orderfuse/afs/detail",
            restExamplePostData = "{\"channelId\": \"100\",\"orderId\": \"001\"}",
            restExampleResponseData = "{\"code\": 0,\"data\": {\"baseInfo\": {\"afterSaleApplyStatus\": \"string\",\"amount\": \"string\",\"channelName\": \"string\",\"createTime\": \"string\",\"deliveryAmount\": \"string\",\"deliveryMethod\": \"string\",\"deliveryOrderCreateTime\": \"string\",\"invoiceTaxNo\": \"string\",\"invoiceTitle\": \"string\",\"merchantAmount\": \"string\",\"needInvoice\": 0,\"orderId\": \"string\",\"packageAmount\": \"string\",\"paidAmount\": \"string\",\"paidOnline\": 0,\"platformAmount\": \"string\",\"poiName\": \"string\",\"receiverAddress\": \"string\",\"receiverName\": \"string\",\"receiverPhone\": \"string\",\"refundReason\": \"string\",\"refundTagId\": \"string\",\"riderName\": \"string\",\"riderPhone\": \"string\",\"status\": \"string\"},\"deliveryDetail\": [{\"deliveryStatus\": \"string\",\"updateTime\": \"string\"}],\"itemInfo\": [{\"isRefund\": \"string\",\"quantity\": 0,\"refundCount\": \"string\",\"sku\": \"string\",\"skuName\": \"string\",\"spec\": \"string\",\"totalPrice\": \"string\",\"unit\": \"string\",\"unitPrice\": \"string\",\"upc\": \"string\"}],\"operateLog\": [{\"opContent\": \"string\",\"opTime\": \"string\",\"opType\": \"string\",\"operator\": \"string\"}],\"promotionInfo\": [{\"agentAmount\": \"string\",\"logisticsBearAmount\": \"string\",\"merchantAmount\": \"string\",\"platformAmount\": \"string\",\"promotionAmount\": \"string\",\"promotionName\": \"string\"}]},\"msg\": \"string\"}"
    )
    @ApiOperation(value = "查询中台退单详情")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @ResultDataSecurity
    @RequestMapping(value = "/afs/detail", method = RequestMethod.POST)
    public Result<AfsOrderFuseDetailVO> queryAfsDetail(@RequestBody QueryAfsDetailRequest request) {
        BusinessIdTracer.putBusiness(RecoBusinessIdEnum.OPEN_HISTORY_SEARCH);
        return fuseOrderService.queryAfsDetail(request);
    }

    @MethodDoc(
            displayName = "订单轨迹",
            description = "订单轨迹",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "订单轨迹"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "/api/v1/orderfuse/queryOrderRoute",
            restExamplePostData = "{\"channelId\": \"100\",\"orderId\": \"001\"}",
            restExampleResponseData = "{\"code\": 0,\"data\": {\"baseInfo\": {\"afterSaleApplyStatus\": \"string\",\"amount\": \"string\",\"channelName\": \"string\",\"createTime\": \"string\",\"deliveryAmount\": \"string\",\"deliveryMethod\": \"string\",\"deliveryOrderCreateTime\": \"string\",\"invoiceTaxNo\": \"string\",\"invoiceTitle\": \"string\",\"merchantAmount\": \"string\",\"needInvoice\": 0,\"orderId\": \"string\",\"packageAmount\": \"string\",\"paidAmount\": \"string\",\"paidOnline\": 0,\"platformAmount\": \"string\",\"poiName\": \"string\",\"receiverAddress\": \"string\",\"receiverName\": \"string\",\"receiverPhone\": \"string\",\"refundReason\": \"string\",\"refundTagId\": \"string\",\"riderName\": \"string\",\"riderPhone\": \"string\",\"status\": \"string\"},\"deliveryDetail\": [{\"deliveryStatus\": \"string\",\"updateTime\": \"string\"}],\"itemInfo\": [{\"isRefund\": \"string\",\"quantity\": 0,\"refundCount\": \"string\",\"sku\": \"string\",\"skuName\": \"string\",\"spec\": \"string\",\"totalPrice\": \"string\",\"unit\": \"string\",\"unitPrice\": \"string\",\"upc\": \"string\"}],\"operateLog\": [{\"opContent\": \"string\",\"opTime\": \"string\",\"opType\": \"string\",\"operator\": \"string\"}],\"promotionInfo\": [{\"agentAmount\": \"string\",\"logisticsBearAmount\": \"string\",\"merchantAmount\": \"string\",\"platformAmount\": \"string\",\"promotionAmount\": \"string\",\"promotionName\": \"string\"}]},\"msg\": \"string\"}"
    )
    @ApiOperation(value = "订单轨迹")
    @ResultDataSecurity
    @RequestMapping(value = "/queryOrderRoute", method = RequestMethod.POST)
    public Result<OrderTrackVO> queryOrderTrack(@Valid @RequestBody TrackRequest request) {
        try {

            final OrderTrackVO orderTrackVO = orderFuseService.queryAllOrderTrackList(request);

            return ResultBuilder.buildSuccessResult(orderTrackVO);

        } catch (Exception e) {
            log.error("queryOrderTrack", e);
            return ResultBuilder.buildFailResult();
        }
    }

    @MethodDoc(
            displayName = "检查退款",
            description = "检查退款，同时会将退款理由列表返回",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "检查退款",
                            type = CheckRefundRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": -1,\n" +
                    "    \"message\": \"已取消或锁定中的订单不能发起订单取消\",\n" +
                    "    \"data\": {\n" +
                    "        \"refundReasons\": [],\n" +
                    "        \"orderInfo\": null\n" +
                    "    }\n" +
                    "}",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：校验用户token，判断当前用户是否登录，是否可访问当前接口，校验用户所在的门店的权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @ApiOperation(value = "检查退款")
    @ResultDataSecurity
    @RequestMapping(value = "/checkrefund", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<CheckRefundResponse> checkRefund(@Valid @RequestBody CheckRefundRequest checkRefundRequest) {
        return orderFuseService.checkRefund(checkRefundRequest);
    }

    @MethodDoc(
            displayName = "退款审核",
            description = "退款审核",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "退款审核请求",
                            type = RefundApplyAuditRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/v1/orderfuse/refundaudit",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "退款审核")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @RequestMapping(value = "/refundaudit", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<RefundApplyAuditResponse> refundAudit(@Valid @RequestBody RefundApplyAuditRequest request) {
        CommonResponse auditResponse = orderFuseService.refundAudit(request);
        String saleReturnOrderNo = null;
        boolean canCreateSaleReturnOrder = false;
        if (auditResponse.getCode() == ResultCode.SUCCESS.getCode()) {
            canCreateSaleReturnOrder = saleReturnService.checkCanCreateSaleReturnOrder(request);
            // 退货退款终审阶段，查询销退单号
            if (canCreateSaleReturnOrder
                    && request.getAuditStage() != null
                    && request.getAuditStage() == AuditStageEnum.FINAL.getValue()) {
                saleReturnOrderNo = saleReturnService.getSaleReturnOrderNo(request);
            }
            // 退货退款终审阶段，驳回情况下，如果有销退单，关闭销退单
            if (StringUtils.isNotBlank(saleReturnOrderNo)
                    && request.getAuditStage() == AuditStageEnum.FINAL.getValue()
                    && request.getAuditResult() == AuditResultEnum.REJECT.getValue()) {
                saleReturnService.closeSaleReturnOrder(request, saleReturnOrderNo);
            }
        } else {
            return CommonResponse.fail(auditResponse.getCode(), auditResponse.getMessage());
        }
        return CommonResponse.success(new RefundApplyAuditResponse(canCreateSaleReturnOrder, saleReturnOrderNo));
    }


    @MethodDoc(
            displayName = "根据子类型分页查询异常订单列表",
            description = "根据子类型分页查询异常订单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "根据子类型分页查询异常订单列表请求",
                            type = QueryDeliveryErrorOrderBySubTypeRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/v1/orderfuse/querydeliveryerrorbysubtype",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "根据子类型分页查询异常订单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/querydeliveryerrorbysubtype", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonFuseResponse<OrderFuseListResponse> queryDeliveryErrorBySubType(@Valid @RequestBody QueryDeliveryErrorOrderBySubTypeRequest request) {
        Long currentStoreId = getCurrentStoreId(request.getPoiIdList());
        return fuseOrderService.queryDeliveryErrorOrderList(request,currentStoreId);
    }


    @MethodDoc(
            displayName = "查询退单列表",
            description = "查询退单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询退单列表",
                            type = QueryDeliveryErrorOrderBySubTypeRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/v1/orderfuse/queryRefundList",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "poiIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY),
            @SecurityParam(value = "warehouseIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiOperation(value = "查询退单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryRefundList", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonFuseResponse<RefundOrderListResponse> queryRefundList(@Valid @RequestBody RefundOrderQueryRequest request) {
        request.selfCheck();
        BusinessIdTracer.putBusiness(RecoBusinessIdEnum.OPEN_HISTORY_SEARCH);
        List<Long> poiIds = Lists.newArrayList();
        List<Long> warehouseIds = Lists.newArrayList();
        boolean hasPermission;
        // 订单号大于4位，则查当前账号全部门店
        if(StringUtils.isNotEmpty(request.getOrderId()) && request.getOrderId().length() > 4){
            hasPermission = fillPermissionParam(null, null, poiIds, warehouseIds);
        }else {
            hasPermission = fillPermissionParam(request.getPoiIdList(), request.getWarehouseIdList(), poiIds, warehouseIds);
        }
        if (!hasPermission) {
            RefundOrderListResponse response = new RefundOrderListResponse();
            PageInfoVO pageInfo = new PageInfoVO();
            pageInfo.setPage(request.getPage());
            pageInfo.setSize(request.getPageSize());
            pageInfo.setTotalPage(0);
            pageInfo.setTotalSize(0);
            response.setPageInfo(pageInfo);
            return CommonFuseResponse.success(response);
        }
        request.setPoiIdList(poiIds);
        request.setWarehouseIdList(warehouseIds);
        request.setTenantId(ContextHolder.currentUserTenantId());
        Long currentStoreId = getCurrentStoreId(poiIds);
        return fuseOrderService.queryRefundOrderList(request,currentStoreId);
    }

    @MethodDoc(
            displayName = "查询退单列表数量",
            description = "查询退单列表数量",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询退单列表",
                            type = QueryDeliveryErrorOrderBySubTypeRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/v1/orderfuse/querydeliveryerrorbysubtype",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "poiIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY),
            @SecurityParam(value = "warehouseIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiOperation(value = "查询退单列表数量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/queryRefundCount", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonFuseResponse<RefundOrderCountResponse> queryRefundCount(@Valid @RequestBody RefundOrderQueryRequest request) {
        request.selfCheck();
        List<Long> poiIds = Lists.newArrayList();
        List<Long> warehouseIds = Lists.newArrayList();
        boolean hasPermission = fillPermissionParam(request.getPoiIdList(), request.getWarehouseIdList(), poiIds, warehouseIds);
        if (!hasPermission) {
            RefundOrderCountResponse response = new RefundOrderCountResponse();
            return CommonFuseResponse.success(response);
        }
        request.setPoiIdList(poiIds);
        request.setWarehouseIdList(warehouseIds);
        request.setTenantId(ContextHolder.currentUserTenantId());
        return fuseOrderService.queryRefundOrderCount(request);
    }


    @MethodDoc(
            displayName = "查询导出选项接口",
            description = "查询导出选项接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询导出选项接口",
                            type = ExportTypeRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/v1/orderfuse/getExportFields",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "查询导出选项接口")
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/getExportFields", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonFuseResponse<List<ExportFieldsVO>> getExportFields(@Valid @RequestBody ExportTypeRequest request) {
        return fuseOrderService.queryExportProfitLoss(request);
    }

    @MethodDoc(
            displayName = "异步导出融合订单接口",
            description = "异步导出融合订单接口",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：session.tenantId=data.tenantId，校验用户所在的门店的权限"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "异步导出融合订单接口")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @DataSecurity({
            @SecurityParam(value = "poiIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY),
            @SecurityParam(value = "warehouseIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @RequestMapping(value = "/exportOrderList", method = RequestMethod.POST)
    public Result<String> asyncExportFuseOrderList(@Valid @RequestBody OrderFuseQueryRequest request) {
        request.selfCheck();
        OrderFuseQueryBO orderFuseQueryBO = new OrderFuseQueryBO(request);
        orderFuseQueryBO.setTenantId(ContextHolder.currentUserTenantId());
        List<Long> poiIds = Lists.newArrayList();
        List<Long> warehouseIds = Lists.newArrayList();
        boolean hasPermission = fillPermissionParam(request.getPoiIdList(), request.getWarehouseIdList(), poiIds, warehouseIds);
        if (!hasPermission) {
            return ResultBuilder.buildFailResult("当前无门店权限 无法导出订单");
        }
        orderFuseQueryBO.setPoiIds(poiIds);
        orderFuseQueryBO.setWarehouseIds(warehouseIds);
        return fuseOrderService.exportOrderListTask(orderFuseQueryBO);
    }

    @MethodDoc(
            displayName = "异步导出融合订单接口",
            description = "异步导出融合订单接口",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：session.tenantId=data.tenantId，校验用户所在的门店的权限"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "异步导出融合退单列表")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @DataSecurity({
            @SecurityParam(value = "poiIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY),
            @SecurityParam(value = "warehouseIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @RequestMapping(value = "/exportAfsOrderDetail", method = {RequestMethod.POST, RequestMethod.GET})
    public Result<String> exportAfsOrderDetail(@Valid @RequestBody RefundOrderQueryRequest request) {
        request.selfCheck();
        request.setTenantId(ContextHolder.currentUserTenantId());
        List<Long> poiIds = Lists.newArrayList();
        List<Long> warehouseIds = Lists.newArrayList();
        boolean hasPermission = fillPermissionParam(request.getPoiIdList(), request.getWarehouseIdList(), poiIds, warehouseIds);
        if (!hasPermission) {
            return ResultBuilder.buildFailResult("当前无门店权限 无法导出退单");
        }
        request.setPoiIdList(poiIds);
        request.setWarehouseIdList(warehouseIds);
        return fuseOrderService.exportRefundOrderList(request);
    }

    /**
     * 异步导出订单详情
     * 导出逻辑实现在mng
     *
     * @param request
     * @return
     */
    @MethodDoc(
            displayName = "异步导出订单详情接口",
            description = "异步导出订单详情",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：session.tenantId=data.tenantId，校验用户所在的门店的权限"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "异步导出订单详情明细接口")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @DataSecurity({
            @SecurityParam(value = "poiIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY),
            @SecurityParam(value = "warehouseIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @RequestMapping(value = "/exportOrderItemDetail", method = RequestMethod.POST)
    public Result<String> asyncExportOrdersItemDetail(@Valid @RequestBody OrderItemFuseQueryExportRequest request) {
        request.selfCheck();
        OrderItemFuseQueryBO orderItemFuseQueryBO = new OrderItemFuseQueryBO(request);
        try {
            orderItemFuseQueryBO.setTenantId(ContextHolder.currentUserTenantId());
            List<Long> poiIds = Lists.newArrayList();
            List<Long> warehouseIds = Lists.newArrayList();

            boolean hasPermission = fillPermissionParam(request.getPoiIdList(), request.getWarehouseIdList(), poiIds, warehouseIds);
            if (!hasPermission) {
                return ResultBuilder.buildFailResult("当前无门店权限 无法导出订单明细");
            }
            orderItemFuseQueryBO.setPoiIdList(poiIds);
            orderItemFuseQueryBO.setWarehouseIdList(warehouseIds);
            fuseOrderService.checkFuseOrderItemsRequest(orderItemFuseQueryBO);
        } catch (CommonRuntimeException commonException) {
            log.warn("FuseOrderServiceImpl.exportOrderListTask commonException error:", commonException);
            return ResultBuilder.buildFailResult(commonException.getMessage());
        } catch (Exception e) {
            log.warn("FuseOrderServiceImpl.exportOrderListTask error:", e);
            return ResultBuilder.buildFailResult(e.getMessage());
        }

        return process(request, "asyncExportOrdersItemDetail", r -> {
            FuseOrderDetailListExportTaskCreateRequest req = orderItemFuseQueryBO.toFuseOrderDetailListExportTaskCreateRequest();
            boolean hasBatchInfo = request.getExportFields().removeIf(e->Objects.equals(e,"batchInfo"));
            if(hasBatchInfo){
                request.getExportFields().add("batchNo");
                request.getExportFields().add("locationCode");
                request.getExportFields().add("productionDate");
            }
            req.setExportFieldsList(request.getExportFields());
            // 查询导出限制，判断是否超过导出限制
            ExportLimitResponse limitResponse = fuseOrderListExportThriftService
                    .queryExecuteExportLimit(ExportLimitRequest.builder().orderDetailListReq(req).build());
            log.info("queryExecuteExportLimit limitResponse:{}", limitResponse);
            ResponseHandler.checkResponseAndStatus(limitResponse, rep -> rep.getStatus().getCode(),
                    rep -> rep.getStatus().getMessage());
            if (limitResponse.getIsOverLimit()) {
                throw new BizException(ResultCodeEnum.FAIL.getValue(), "超出导出上限,单次最多导出" + limitResponse.getExportLimitCount() + "条退单");
            }
            ExportResponse response = RpcInvoker.invoke(() -> fuseOrderListExportThriftService.exportFuseOrderDetailList(req));
            ResponseHandler.checkResponseAndStatus(response, rep -> rep.getStatus().getCode(), rep -> rep.getStatus().getMessage());
            return ResultBuilder.buildSuccess(response.getToken());
        });
    }

    /**
     * 异步导出订单详情
     * 导出逻辑实现在mng
     *
     * @param request
     * @return
     */
    @MethodDoc(
            displayName = "异步导出售后单明细详情接口",
            description = "异步导出售后单明细详情",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：session.tenantId=data.tenantId，校验用户所在的门店的权限"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "异步导出售后单明细接口")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @DataSecurity({
            @SecurityParam(value = "poiIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY),
            @SecurityParam(value = "warehouseIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @RequestMapping(value = "/exportAfsOrderItemDetail", method = RequestMethod.POST)
    public Result<String> asyncExportAfsOrdersItemDetail(@Valid @RequestBody RefundOrderQueryRequest request) {
        request.selfCheck();
        request.setTenantId(ContextHolder.currentUserTenantId());
        List<Long> poiIds = Lists.newArrayList();
        List<Long> warehouseIds = Lists.newArrayList();
        boolean hasPermission = fillPermissionParam(request.getPoiIdList(), request.getWarehouseIdList(), poiIds, warehouseIds);
        if (!hasPermission) {
            return ResultBuilder.buildFailResult("当前无门店权限 无法导出退单明细");
        }
        request.setPoiIdList(poiIds);
        request.setWarehouseIdList(warehouseIds);
        return fuseOrderService.exportRefundOrderDetailList(request);
    }

    @MethodDoc(
            displayName = "克重退检查",
            description = "克重退检查",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "克重退检查",
                            paramType = ParamType.REQUEST_PARAM
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见PoiResp",
            restExampleUrl = "https://fnsaas.waimai.test.sankuai.com/api/v1/order/weightrefundcheck",
            restExamplePostData = "token=aaaa",
            restExampleResponseData="{\"code\":0,\"msg\":\"\",\"data\":null}"
    )
    @ApiOperation(value = "克重退检查")
    @MethodLog(logResponse = true, logRequest = true, logger = "http")
    @ResultDataSecurity
    @PostMapping(value = "/weightRefundCheck")
    public CommonResponse<WeightRefundCheckResponse> weightRefundCheck(@Valid @RequestBody WeightRefundCheckRequest weightRefundCheckRequest) {
        return fuseAfterSaleApplyService.weightRefundCheck(weightRefundCheckRequest);
    }

    @MethodDoc(
            description = "克重退差价试算",
            displayName = "克重退差价试算",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "克重退差价试算",
                            type = RefundByWeightRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "{\\n\" +\n" +
                    "                    \"    \\\"code\\\": 0,\\n\" +\n" +
                    "                    \"    \\\"message\\\": \\\"操作成功\\\",\\n\" +\n" +
                    "                    \"    \\\"data\\\": {}\\n\" +\n" +
                    "                    \"}",
            restExampleUrl = "/api/v1/order/weightrefundcalculate",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "克重退差价试算")
    @MethodLog(logResponse = true, logRequest = true)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @RequestMapping(value = "/weightrefundcalculate", method = {RequestMethod.POST})
    @ResponseBody
    public CommonResponse weightRefundCalculate(@Valid @RequestBody RefundByWeightRequest request) {
        return fuseAfterSaleApplyService.weightRefundCalculate(request);
    }


    @MethodDoc(
            description = "发起按克重退差价",
            displayName = "发起按克重退差价",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "发起按克重退差价",
                            type = RefundByWeightRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "{\\n\" +\n" +
                    "                    \"    \\\"code\\\": 0,\\n\" +\n" +
                    "                    \"    \\\"message\\\": \\\"操作成功\\\",\\n\" +\n" +
                    "                    \"    \\\"data\\\": {}\\n\" +\n" +
                    "                    \"}",
            restExampleUrl = "/pieapi/order/weightRefund",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "发起按克重退差价")
    @MethodLog(logResponse = true, logRequest = true)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @RequestMapping(value = "/weightRefund", method = {RequestMethod.POST})
    @ResponseBody
    public CommonResponse weightRefund(@Valid @RequestBody RefundByWeightRequest request) {
        return fuseAfterSaleApplyService.weightRefund(request);
    }


    @MethodDoc(
            displayName = "开发票",
            description = "开发票",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "开发票",
                            type = ExportTypeRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/v1/orderfuse/createInvoice",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "开发票")
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/createInvoice", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public CommonFuseResponse<CreateQnhInvoiceResponse> createInvoice(@Valid @RequestBody CreateQnhInvoiceRequest request) {
        return fuseOrderService.createInvoice(request);
    }





    @MethodDoc(
            displayName = "orderDeliveryStatus",
            description = "查询订单配送状态",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询订单配送状态请求",
                            type = OrderDeliveryDetailRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )
            },
            restExampleResponseData = "",
            restExampleUrl = "/api/v1/orderfuse/orderDeliveryStatus",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @MethodLog(logResponse = true, logRequest = true)
    @RequestMapping(value = "/orderDeliveryStatus", method = {RequestMethod.POST, RequestMethod.GET})
    public CommonResponse<DeliveryStatusVO> orderDeliveryStatus(@Valid @RequestBody OrderDeliveryDetailRequest request) {
        String validateResult = request.validate();
        if (validateResult != null) {
            return CommonResponse.fail(ResultCode.FAIL.getCode(), validateResult);
        }
        BusinessIdTracer.putBusiness(RecoBusinessIdEnum.OPEN_HISTORY_SEARCH);
        return fuseOrderService.queryDeliveryDetail(request);
    }

    @MethodDoc(
            displayName = "自提码校验接口",
            description = "自提码校验接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "自提码校验接口",
                            type = HttpServletRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/v1/orderfuse/checkSelfFetchCode",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "自提码校验接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @RequestMapping(value = "/checkSelfFetchCode", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public Result<CheckSelfDeliveryCodeResponse> checkSelfFetchCode(@Valid @RequestBody CheckSelfFetchCodeRequest request) {
        return fuseOrderService.queryOrderBySelfFetchCode(request);
    }

    @MethodDoc(
            displayName = "自提码核验接口",
            description = "自提码核验接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "自提码核验接口",
                            type = HttpServletRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/v1/orderfuse/verifySelfFetchCode",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "自提码核验接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @RequestMapping(value = "/verifySelfFetchCode", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public Result verifySelfFetchCode(@Valid @RequestBody VerifySelfFetchCodeRequest request) {
        return fuseOrderService.verifySelfFetchCode(request);
    }

    /**
     * 填充有权限的门店或仓库id
     * @param paramPoiIds
     * @param paramWarehouseIds
     * @param permissionPoiIds
     * @param permissionWarehouseIds
     * @return
     */
    private boolean fillPermissionParam(List<Long> paramPoiIds, List<Long> paramWarehouseIds, List<Long> permissionPoiIds, List<Long> permissionWarehouseIds) {
        if (CollectionUtils.isNotEmpty(paramPoiIds) || CollectionUtils.isNotEmpty(paramWarehouseIds)) {
            Optional.ofNullable(paramPoiIds).ifPresent(permissionPoiIds::addAll);
            Optional.ofNullable(paramWarehouseIds).ifPresent(permissionWarehouseIds::addAll);
            return true;
        }

        AccountInfoVo accountInfo = accountRemoteService.querySimpleAccountInfoById(ContextHolder.currentUid());
        if (Objects.nonNull(accountInfo) && Objects.equals(accountInfo.getAccountType(), AccountTypeEnum.ADMIN.getValue())) {
            return true;
        }
        List<Long> poiIds = authFacade.getCurrentUserFullStoreIdList();
        if (CollectionUtils.isNotEmpty(poiIds)) {
            permissionPoiIds.addAll(poiIds);
            return true;
        }


        return false;
    }


    private Optionable getTypeEnum(String type) {
        switch (type) {
            case "channelOrderNewStatus":
                return ChannelOrderNewStatusEnum.CANCELED;
            case "orderType":
                return OrderTypeEnum.RESERVE;
            case "memberCard":
                return MemberCardEnum.ALL;
            case "trackType":
                return TrackTypeEnum.ORDER;
            case "refundType":
                return RefundTypeEnum.REFUND;
            case "refundStatus":
                return RefundStatusEnum.DEAL_ING;
            case "applyUserType":
                return ApplyUserEnum.MERCHANT;
            default:
                return null;
        }
    }

    private MultiStageOptionable getMultiStageEnum(String type) {
        switch (type) {
            case "deliveryType":
                return DeliveryTypeEnum.ORDER_PLATFORM_DELIVERY;
            case "orderLabel":
                return OrderLabelEnum.POS;
            case "refundOrderLabel":
                return RefundOrderLabelEnum.SUB_CHANNEL;
            case "dhOrderLabel":
                //歪马订单页面标签筛选
                return OrderLabelEnum.DRUNK_HORSE_LABLE;
            case "dhRefundOrderLabel":
                //歪马退单页面标签筛选
                return RefundOrderLabelEnum.DRUNK_HORSE_REFUND_LABLE;
            default:
                return null;
        }
    }

    @MethodDoc(
            displayName = "查询金额退可退差商品列表接口",
            description = "查询金额退可退差商品列表接口",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "金额退页面检查接口"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/api/v1/orderfuse/moneyRefundCheck",
            restExamplePostData = "{\"channelId\":\"Integer\",\"tenantId\":\"Long\",\"channelOrderId\":\"String\"}",
            restExampleResponseData = "{\"code\": 0,\"msg\": \"string\"}"
    )
    @ApiOperation(value = "查询金额退可退差商品列表接口")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @ResultDataSecurity
    @RequestMapping(value = "/moneyRefundCheck", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse<OrderMoneyRefundCheckResponse> moneyRefundCheck(@RequestBody OrderMoneyRefundCheckRequest request) {
        try {
            if (request == null) {
                throw new ParamInvalidException("调用OrderFuseController.moneyRefundCheck,OrderMoneyRefundCheckRequest为null");
            }
            request.selfCheck();

            if (Objects.equals(request.getChannelId(), DynamicChannelType.MEITUAN.getChannelId())) {
                // 美团名酒馆订单进行错误提示
                Pair<Boolean, Pair<Integer, String>> resultPair = ocmsChannelRemoteService.refundCheckMtFamousTavern(
                        request.getChannelOrderId(), ContextHolder.currentUserTenantId(),
                        ErrorCodeEnum.REFUND_CHECK_MT_FAMOUS_TAVERN_PROMPT, ErrorCodeEnum.WEB_MT_FAMOUS_TAVERN_GATHER_PHONE_NOT_FIND);
                if (resultPair.getKey()) {
                    return CommonResponse.fail(resultPair.getValue().getKey(), resultPair.getValue().getValue());
                }
            }
            OrderMoneyRefundCheckResponse res = new OrderMoneyRefundCheckResponse();
            List<OrderItemMoneyRefundCheckVO> moneyRefundCheckVOList = fuseAfterSaleApplyService.moneyRefundCheck(request);
            if (CollectionUtils.isEmpty(moneyRefundCheckVOList)) {
                return CommonResponse.fail(StatusCodeEnum.FAIL.getCode(), "当前无可金额退的商品，无法发起金额退");
            }
            res.setMoneyRefundCheckVOList(moneyRefundCheckVOList);
            //淘鲜达需要获取退款理由
            if (Objects.equals(request.getChannelId(), DynamicChannelType.TAO_XIAN_DA.getChannelId())) {
                List<RefundReasonAndCodeVO> refundReasonAndCodeVOS = fuseAfterSaleApplyService.queryRefundReasons(RefundReasonAndCodeRequest.builder()
                        .orderBizType(DynamicOrderBizType.channelId2OrderBizTypeValue(request.getChannelId()))
                        .viewOrderId(request.getChannelOrderId())
                        .refundType(com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.RefundTypeEnum.AMOUNT.getValue())
                        .build());
                res.setRefundReasons(refundReasonAndCodeVOS);
            }
            return CommonResponse.success(res);
        } catch (BizException e) {
            return CommonResponse.fail(StatusCodeEnum.FAIL.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("moneyRefundCheck failed, request:{}", JSONObject.toJSONString(request), e);
            return CommonResponse.fail(StatusCodeEnum.FAIL.getCode(), "金额退页面检查失败了！原因：" + e.getMessage());
        }
    }

    @MethodDoc(
            displayName = "商家金额退接口",
            description = "商家金额退接口",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "商家金额退接口"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/api/v1/orderfuse/moneyRefund",
            restExamplePostData = "{\"channelId\":\"Integer\",\"tenantId\":\"Long\",\"channelOrderId\":\"String\"}",
            restExampleResponseData = "{\"code\": 0,\"msg\": \"string\"}"
    )
    @ApiOperation(value = "商家金额退接口")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @RequestMapping(value = "/moneyRefund", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse moneyRefund(@RequestBody OrderMoneyRefundRequest request) {
        try {
            if (request == null) {
                throw new ParamInvalidException("调用OrderFuseController.moneyRefund,OrderMoneyRefundRequest为null");
            }
            request.selfCheck();
            return fuseAfterSaleApplyService.moneyRefund(request);
        } catch (Exception e) {
            log.error("moneyRefund failed, request:{}", JSONObject.toJSONString(request), e);
            return CommonResponse.fail(StatusCodeEnum.FAIL.getCode(), "金额退失败！原因：" + e.getMessage());
        }
    }

    @MethodDoc(
            displayName = "售后退款",
            description = "售后退款，针对指定渠道订单的指定商品列表进行部分退款。",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "售后退款",
                            type = PartRefundRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/v1/orderfuse/afterSaleRefund",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "售后退款")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @RequestMapping(value = "/afterSaleRefund", method = RequestMethod.POST)
    @ResponseBody
    public CommonResponse afterSaleRefund(@Valid @RequestBody AfterSaleRefundRequest request) {
        request.selfCheck();
        return fuseAfterSaleApplyService.afterSaleRefund(request);
    }

    @MethodDoc(
            displayName = "异步导出融合订单列表与详情",
            description = "异步导出融合订单列表与详情",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：session.tenantId=data.tenantId，校验用户所在的门店的权限"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "异步导出融合订单列表与详情")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @DataSecurity({
            @SecurityParam(value = "poiIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY),
            @SecurityParam(value = "warehouseIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @RequestMapping(value = "/exportOrderListAndDetail", method = RequestMethod.POST)
    public Result<String> exportListAndDetail(@Valid @RequestBody OrderFuseQueryRequest request){
        request.selfCheck();
        OrderFuseQueryBO orderFuseQueryBO = new OrderFuseQueryBO(request);
        try {
            orderFuseQueryBO.setTenantId(ContextHolder.currentUserTenantId());
            List<Long> poiIds = Lists.newArrayList();
            List<Long> warehouseIds = Lists.newArrayList();
            boolean hasPermission = fillPermissionParam(request.getPoiIdList(), request.getWarehouseIdList(), poiIds, warehouseIds);
            if (!hasPermission) {
                return ResultBuilder.buildFailResult("当前无门店权限 无法导出订单");
            }
            orderFuseQueryBO.setPoiIds(poiIds);
            orderFuseQueryBO.setWarehouseIds(warehouseIds);

            fuseOrderService.checkOrderQueryRequest(orderFuseQueryBO);

            OrderExportMaxCountVO vo = fuseOrderService.queryOrderCount(orderFuseQueryBO);
            if (vo.getIsOverLimit()) {
                return Result.fail(StatusCodeEnum.FAIL.getCode(), "超出导出上限,单次最多导出" + vo.getLimit() + "条订单");
            }
            return fuseOrderService.exportOrderListAndDetail(orderFuseQueryBO);
        } catch (CommonRuntimeException commonException) {
            log.warn("FuseOrderServiceImpl.exportOrderListTask commonException error:", commonException);
            return ResultBuilder.buildFailResult(commonException.getMessage());
        } catch (Exception e) {
            log.warn("FuseOrderServiceImpl.exportOrderListTask error:", e);
            return ResultBuilder.buildFailResult(e.getMessage());
        }
    }


    /**
     * 查询订单列表
     *
     * @param request
     * @return
     */
    @MethodDoc(
            displayName = "PC客户端分页查询全部订单列表",
            description = "PC客户端分页查询全部订单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "全部订单列表分页查询请求",
                            type = OrderFuseQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/v1/orderfuse/queryPCOrderList",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "poiIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY),
            @SecurityParam(value = "warehouseIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiOperation(value = "PC客户端分页查询全部订单列表")
    @RequestMapping(value = "/queryPCOrderList", method = {RequestMethod.POST, RequestMethod.GET})
    public CommonFuseResponse<OrderFuseListResponse> queryPCOrderList(@Valid @RequestBody OrderPCFuseQueryRequest request) {
        Long requestTime = System.currentTimeMillis();
        request.selfCheck();
        Long currentStoreId = getCurrentStoreId(request.getPoiIdList());
        BusinessIdTracer.putBusiness(RecoBusinessIdEnum.OPEN_HISTORY_SEARCH);
        List<Long> poiIds = Lists.newArrayList();
        List<Long> warehouseIds = Lists.newArrayList();
        boolean hasPermission;
        // 搜索关键字大于4位，则查当前账号全部门店。否则若前端传了门店筛选，则取前端传的门店。
        if(StringUtils.isNotEmpty(request.getSmartQuery()) && request.getSmartQuery().length() > 4){
            hasPermission = fillPermissionParam(null, null, poiIds, warehouseIds);
        }else {
            hasPermission = fillPermissionParam(request.getPoiIdList(), request.getWarehouseIdList(), poiIds, warehouseIds);
        }
        if (!hasPermission) {
            OrderFuseListResponse response = new OrderFuseListResponse();
            PageInfoVO pageInfo = new PageInfoVO();
            pageInfo.setPage(request.getPage());
            pageInfo.setSize(request.getPageSize());
            pageInfo.setTotalPage(0);
            pageInfo.setTotalSize(0);
            response.setPageInfo(pageInfo);
            response.setRequestTime(requestTime);
            return CommonFuseResponse.success(response);
        }
        request.setPoiIdList(poiIds);
        request.setWarehouseIdList(warehouseIds);
        OrderFuseQueryBO orderFuseQueryBO = new OrderFuseQueryBO(request);
        orderFuseQueryBO.setTenantId(ContextHolder.currentUserTenantId());
        //该接口修改，需要在orderbiz的com.meituan.shangou.saas.o2o.service.BizOrderThriftService#queryFuseOrders一起修改
        //该部分涉及PC端的性能优化，详情咨询：lukaixuan02
        CommonFuseResponse<OrderFuseListResponse> orderFuseListResponseCommonFuseResponse = fuseOrderService.queryFuseOrders(orderFuseQueryBO, currentStoreId);
        if (Objects.nonNull(orderFuseListResponseCommonFuseResponse) && Objects.nonNull(orderFuseListResponseCommonFuseResponse.getData())) {
            orderFuseListResponseCommonFuseResponse.getData().setRequestTime(requestTime);
        }
        return orderFuseListResponseCommonFuseResponse;
    }

    @MethodDoc(
            displayName = "订单子状态数量查询",
            description = "订单子状态数量查询",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "订单子状态数量查询",
                            type = HttpServletRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/v1/orderfuse/queryordersubstatuscount",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "poiIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY),
            @SecurityParam(value = "warehouseIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiOperation(value = "订单子状态数量查询")
    @RequestMapping(value = "/queryPCOrderSubStatusCount", method = {RequestMethod.POST, RequestMethod.GET})
    public CommonFuseResponse<OrderSubStatusCountResponse> queryPCOrderSubStatusCount(@Valid @RequestBody OrderPCFuseQueryRequest request) {
        request.selfCheck();
        BusinessIdTracer.putBusiness(RecoBusinessIdEnum.OPEN_HISTORY_SEARCH);
        List<Long> poiIds = Lists.newArrayList();
        List<Long> warehouseIds = Lists.newArrayList();
        boolean hasPermission;
        // 订单号大于4位，则查当前账号全部门店
        if(StringUtils.isNotEmpty(request.getSmartQuery()) && request.getSmartQuery().length() > 4){
            hasPermission = fillPermissionParam(null, null, poiIds, warehouseIds);
        }else {
            hasPermission = fillPermissionParam(request.getPoiIdList(), request.getWarehouseIdList(), poiIds, warehouseIds);
        }
        if (!hasPermission) {
            OrderSubStatusCountResponse response = new OrderSubStatusCountResponse();
            response.setAllSubStatuseCount(0);
            response.setWaitToTakeOrderCount(0);
            response.setWaitToPickCount(0);
            response.setOrderCancelingCount(0);
            response.setOrderCompletedCount(0);
            response.setDeliveringCount(0);
            response.setPickFinishCount(0);
            response.setOrderCanceledCount(0);
            response.setDeliveringErrorCount(0);
            response.setOrderCancelingCount(0);
            return CommonFuseResponse.success(response);
        }
        request.setPoiIdList(poiIds);
        request.setWarehouseIdList(warehouseIds);
        OrderFuseQueryBO orderFuseQueryBO = new OrderFuseQueryBO(request);
        orderFuseQueryBO.setTenantId(ContextHolder.currentUserTenantId());
        return fuseOrderService.queryWaitDeliverySubTypeCount(orderFuseQueryBO);
    }


    /**
     * 查询订单列表统计数据
     *
     * @param request
     * @return
     */
    @MethodDoc(
            displayName = "PC客户端查询订单列表统计数据",
            description = "查询订单列表统计数据",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询订单列表统计数据",
                            type = OrderFuseQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/v1/orderfuse/getPCOrderStatisticsData",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @DataSecurity({
            @SecurityParam(value = "poiIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY),
            @SecurityParam(value = "warehouseIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiOperation(value = "PC客户端查询订单列表统计数据")
    @RequestMapping(value = "/getPCOrderStatisticsData", method = {RequestMethod.POST, RequestMethod.GET})
    public CommonFuseResponse<OrderFuseStatisticsResponse> getPCOrderStatisticsData(@Valid @RequestBody OrderPCFuseStatisticsDataQueryRequest request) {
        request.selfCheck();
        BusinessIdTracer.putBusiness(RecoBusinessIdEnum.OPEN_HISTORY_SEARCH);
        if(request.isContainFuzzSearch()){
            return CommonFuseResponse.success(OrderFuseStatisticsResponse.builder().build());
        }
        List<Long> poiIds = Lists.newArrayList();
        List<Long> warehouseIds = Lists.newArrayList();
        boolean hasPermission;
        // 订单号大于4位，则查当前账号全部门店
        if(StringUtils.isNotEmpty(request.getSmartQuery()) && request.getSmartQuery().length() > 4){
            hasPermission = fillPermissionParam(null, null, poiIds, warehouseIds);
        }else {
            hasPermission = fillPermissionParam(request.getPoiIdList(), request.getWarehouseIdList(), poiIds, warehouseIds);
        }
        if (!hasPermission) {
            return CommonFuseResponse.success(OrderFuseStatisticsResponse.builder().build());
        }
        request.setPoiIdList(poiIds);
        request.setWarehouseIdList(warehouseIds);
        OrderFuseQueryStatisticsDataBO orderFuseQueryStatisticsDataBO = new OrderFuseQueryStatisticsDataBO(request);
        orderFuseQueryStatisticsDataBO.setTenantId(ContextHolder.currentUserTenantId());
        return fuseOrderService.queryFuseOrderStatisticsData(orderFuseQueryStatisticsDataBO);
    }

    /**
     * 添加赠品到商品列表并排序
     * @param orderFuseDetailBO
     * @param items
     */
    private void addGift2ItemAndSort(OrderFuseDetailBO orderFuseDetailBO, List<ItemInfoVO> items, BaseInfoVO baseInfo) {
        try {
            if (null == orderFuseDetailBO.getBaseInfo()) {
                return;
            }

            Long tenantId = ContextHolder.currentUserTenantId();
            TenantBusinessModeEnum businessMode = tenantService.queryTenantBusinessMode(tenantId);
            //  如果不是医药无人仓业态，则直接返回，不执行后续操作。
            if (!TenantBusinessModeEnum.MEDICINE_UNMANNED_WAREHOUSE.equals(businessMode)
                    || !MccConfigUtil.isUmwOrderItemsIncludeGift(tenantId)) {
                return;
            }

            Integer channelId = orderFuseDetailBO.getBaseInfo().getChannelId();

            if (null != channelId && channelId.equals(DynamicChannelType.MEITUAN.getChannelId())) {
                int beforeAddGiftSize = CollectionUtils.size(items);
                orderFuseDetailBO.addGift2Item(items);
                int afterAddGiftSize = CollectionUtils.size(items);
                // 如果没有添加赠品数据则不需要重新计算总数
                if (beforeAddGiftSize != afterAddGiftSize) {
                    // 重新统计包含赠品的商品数量
                    int productCount = items.stream().map(ItemInfoVO::getQuantity).reduce(0, Integer::sum);
                    long productCategoryCount = items.stream().map(item -> {
                        String tmpSku = StringUtils.isBlank(item.getSku()) ? StringUtils.EMPTY : item.getSku();
                        return tmpSku + item.getSkuName();
                    }).distinct().count();

                    baseInfo.setProductCategoryCount((int) productCategoryCount);
                    baseInfo.setProductCount(productCount);
                }
            }
            aggOrderGiftItems(items);
            // 升序排列 如果货号为空放最后
            items.sort((item1, item2) -> GoodsCodeCompareUtil.compareByGoodsCode(item1.getGoodsCode(), item2.getGoodsCode()));
        } catch (Exception e) {
            log.error("[order-detail] 赠品添加至商品中出现异常, tenantId: [{}], orderId: [{}]", ContextHolder.currentUserTenantId(),
                    baseInfo.getOrderId(), e);
        }
    }

    /**
     * 聚合订单赠品
     * @param items
     */
    private void aggOrderGiftItems(List<ItemInfoVO> items) {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }

        List<ItemInfoVO> giftItems = items.stream()
                .filter(Objects::nonNull)
                // giftType为平台赠品
                .filter(item -> null != item.getGiftType() && item.getGiftType() == 0)
                .collect(Collectors.toList());

        if (CollectionUtils.size(giftItems) <= 1) {
            return;
        }

        Map<String, ItemInfoVO> uniqKey2Item = giftItems.stream()
                .collect(Collectors.toMap(item -> item.getSku() + item.getCustomSkuId() + item.getSkuName(), Function.identity(),
                (oldItem, newItem) -> {
                    oldItem.setQuantity(oldItem.getQuantity() + newItem.getQuantity());
                    return oldItem;
                }));
        // 没有重复的商品不处理
        if (uniqKey2Item.size() == giftItems.size()) {
            return;
        }
        // 移除未聚合的赠品 重新设置聚合后的数据
        items.removeAll(giftItems);
        items.addAll(uniqKey2Item.values());
    }


    /**
     * 返货审核
     *
     * @param request
     * @return
     */
    @MethodDoc(
            displayName = "PC返货验货审核",
            description = "返货验货审核",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "返货审核",
                            type = OrderFuseQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/v1/orderfuse/returnGoodsAudit",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @MethodLog(logRequest = true, logResponse = true)
    @DataSecurity({
            @SecurityParam(value = "poiIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY),
            @SecurityParam(value = "warehouseIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiOperation(value = "返货验货审核")
    @RequestMapping(value = "/returnGoodsAudit", method = {RequestMethod.POST, RequestMethod.GET})
    public CommonResponse<Void> returnGoodsAudit(@Valid @RequestBody ReturnGoodsAuditRequest request) {
        request.selfCheck();
        return fuseOrderService.returnGoodsAudit(request);
    }

    /**
     * 查询隐私号
     *
     * @param request
     * @return
     */
    @MethodDoc(
            displayName = "查询隐私号",
            description = "查询隐私号",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "查询隐私号",
                            type = OrderFuseQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/v1/orderfuse/queryVirtualPhone",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @MethodLog(logRequest = true, logResponse = true)
    @DataSecurity({
            @SecurityParam(value = "poiIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY),
            @SecurityParam(value = "warehouseIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiOperation(value = "查询隐私号")
    @ResultDataSecurity
    @RequestMapping(value = "/queryVirtualPhone", method = {RequestMethod.POST, RequestMethod.GET})
    public CommonFuseResponse<QueryVirtualPhoneResponse> queryVirtualPhone(@Valid @RequestBody QueryVirtualPhoneRequest request) {
        return fuseOrderService.queryVirtualPhone(request);
    }


    /**
     * 返货审核
     *
     * @param request
     * @return
     */
    @MethodDoc(
            displayName = "录入换货售后信息",
            description = "录入换货售后信息",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "返货审核",
                            type = OrderFuseQueryRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "",
            restExampleUrl = "/api/v1/orderfuse/importrefundexchangeinfo",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @MethodLog(logRequest = true, logResponse = true)
    @DataSecurity({
            @SecurityParam(value = "poiIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY),
            @SecurityParam(value = "warehouseIdList", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @ApiOperation(value = "录入换货售后信息")
    @RequestMapping(value = "/importrefundexchangeinfo", method = {RequestMethod.POST, RequestMethod.GET})
    public CommonResponse<Void> importRefundExchangeInfo(@Valid @RequestBody ImportRefundExchangeInfoRequest request) {
        request.selfCheck();
        request.setTenantId(ContextHolder.currentUserTenantId());
        return fuseOrderService.importRefundExchangeInfo(request);
    }
}
