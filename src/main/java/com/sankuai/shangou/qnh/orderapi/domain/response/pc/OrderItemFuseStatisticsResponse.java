package com.sankuai.shangou.qnh.orderapi.domain.response.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @program: reco_store_saas_e_api
 * @description: 查询订单明细列表统计数据响应
 * @author: jinyi
 * @create: 2023-08-17 16:55
 **/
@TypeDoc(description = "查询订单明细列表统计数据响应")
@Data
@ApiModel("查询订单明细列表统计数据响应")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderItemFuseStatisticsResponse {

    private final static String defaultSumString = "-";

    /**
     * 商品数量
     */
    @FieldDoc(description = "商品数量", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "商品数量", required = true)
    private String quantitySum = defaultSumString;

    /**
     * 商品金额
     */
    @FieldDoc(description = "商品金额", requiredness = Requiredness.REQUIRED)
    @ApiModelProperty(value = "商品金额", required = true)
    private String totalPayAmountSum = defaultSumString;
}
