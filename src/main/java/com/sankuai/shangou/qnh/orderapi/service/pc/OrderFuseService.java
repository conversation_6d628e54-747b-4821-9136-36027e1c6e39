package com.sankuai.shangou.qnh.orderapi.service.pc;

import com.sankuai.shangou.qnh.orderapi.domain.request.pc.CheckRefundRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.RefundApplyAuditRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.TrackRequest;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.CheckRefundResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.CommonResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.OrderTrackVO;
import org.apache.thrift.TException;

/**
 * 轨迹查询
 */
public interface OrderFuseService {

    OrderTrackVO queryAllOrderTrackList(TrackRequest request) throws TException;

    CommonResponse<CheckRefundResponse> checkRefund(CheckRefundRequest request);

    CommonResponse refundAudit(RefundApplyAuditRequest request);

}
