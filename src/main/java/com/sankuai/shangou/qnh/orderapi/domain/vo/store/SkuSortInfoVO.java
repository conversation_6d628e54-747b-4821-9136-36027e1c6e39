package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TypeDoc(
        description = "类目信息"
)
@Data
@ApiModel("类目信息")
public class SkuSortInfoVO {

    @FieldDoc(
            description = "品类编号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "品类编号", required = true)
    private String sortCode;

    @FieldDoc(
            description = "品类名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "品类名称", required = true)
    private String sortName;

    @FieldDoc(
            description = "品类图标", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "品类图标", required = true)
    private String imgUrl;

    @FieldDoc(
            description = "速记码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "速记码", required = true)
    private String shorthandCode;

    @FieldDoc(
            description = "要货截止时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "要货截止时间", required = true)
    private String orderDeadline;

    @FieldDoc(
            description = "加单截止时间", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "加单截止时间", required = true)
    private String orderMoreDeadline;

    @FieldDoc(
            description = "顺序码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "顺序码", required = true)
    private Integer sequence;

    @FieldDoc(
            description = "上级品类编号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "上级品类编号", required = true)
    private String parentSortCode;
}
