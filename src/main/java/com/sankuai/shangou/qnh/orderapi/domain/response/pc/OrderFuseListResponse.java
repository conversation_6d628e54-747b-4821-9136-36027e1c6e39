package com.sankuai.shangou.qnh.orderapi.domain.response.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.OrderFuseVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.PageInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/6
 * desc: 分页查询订单列表响应
 */
@TypeDoc(
        description = "分页查询订单列表响应"
)
@Data
@ApiModel("分页查询订单列表响应")
public class OrderFuseListResponse {

    @FieldDoc(
            description = "分页信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "分页信息", required = true)
    private PageInfoVO pageInfo;

    @FieldDoc(
            description = "订单列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单列表", required = true)
    private List<OrderFuseVO> orderList;

    @FieldDoc(
            description = "请求时间", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "请求时间", required = true)
    private Long requestTime;
}
