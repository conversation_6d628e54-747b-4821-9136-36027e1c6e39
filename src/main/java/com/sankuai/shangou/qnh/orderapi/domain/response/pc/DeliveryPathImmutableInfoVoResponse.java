package com.sankuai.shangou.qnh.orderapi.domain.response.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OrderDetailVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.CoordinateVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.CustomerInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.DeliveryDetailVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@TypeDoc(
        description = "配送路径静态信息"
)
@Data
@ApiModel("初始信息")
public class DeliveryPathImmutableInfoVoResponse {
    @FieldDoc(
            description = "商铺坐标", requiredness = Requiredness.REQUIRED
    )
    private CoordinateVO shopCoordinate;
    @FieldDoc(
            description = "用户坐标", requiredness = Requiredness.REQUIRED
    )
    private CoordinateVO customerCoordinate;
    @FieldDoc(
            description = "用户信息", requiredness = Requiredness.REQUIRED
    )
    private CustomerInfoVO customerInfo;
    @FieldDoc(
            description = "订单详情", requiredness = Requiredness.REQUIRED
    )
    private OrderDetailVo orderDetailVo;
    @FieldDoc(
            description = "配送相关信息", requiredness = Requiredness.REQUIRED
    )
    private DeliveryDetailVO deliveryDetailVO;

    @FieldDoc(
            description = "当前时间戳，毫秒", requiredness = Requiredness.REQUIRED
    )
    private String currentTime;

    @FieldDoc(
            description = "商品总重量(克)", requiredness = Requiredness.REQUIRED
    )
    private String totalWeight;
}
