package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import lombok.Data;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2019/3/30 15:44
 * @Description:
 */
@Data
public class PartRefundRequest implements BaseRequest {

    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 渠道编码
     */
    private String channelId;

    /**
     * 门店名称
     */
    private String poiId;

    /**
     * 退款商品列表
     */
    private List<PartRefundItem> refundItems;

    /**
     * 退款原因编码
     */
    private int reasonCode;

    /**
     * 退款原因
     */
    private String reason;

    @Override
    public void selfCheck() {
        AssertUtil.notEmpty(orderId, "订单号不能为空", "orderId");
        AssertUtil.notEmpty(channelId, "渠道编码不能为空", "channelId");
        AssertUtil.notEmpty(refundItems, "部分退款的商品列表不能为空");
    }

    @Data
    public static class PartRefundItem {
        /**
         * 商品库skuId
         */
        private String sku;

        /**
         * 退款数量
         */
        private String count;

        /**
         * 线上渠道skuId
         */
        private String customSkuId;

        /**
         * 线上渠道spuId
         */
        private String customerSpuId;

        /**
         * sku名称
         */
        private String skuName;

        /**
         * 是否清空库存
         */
        private Boolean stockResetFlag = Boolean.FALSE;

        /**
         * 是否缺货下架
         */
        private Boolean offShelfFlag = Boolean.FALSE;

        /**
         * 渠道门店SKU编码 （仅针对京东渠道无customSkuId时）
         */
        private String extCustomSkuId;

        /**
         * 订单行ID
         */
        public Long orderItemId;
    }

}
