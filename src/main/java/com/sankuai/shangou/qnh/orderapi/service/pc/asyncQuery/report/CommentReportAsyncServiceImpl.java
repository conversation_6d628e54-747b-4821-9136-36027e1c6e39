package com.sankuai.shangou.qnh.orderapi.service.pc.asyncQuery.report;

import com.meituan.shangou.saas.order.platform.client.dto.model.AfsOrderCompleteDetail;
import com.meituan.shangou.saas.order.platform.client.dto.model.CommentReport;
import com.meituan.shangou.saas.order.platform.client.dto.model.OrderReturnReport;
import com.meituan.shangou.saas.order.platform.client.dto.request.OrderReportRequest;
import com.meituan.shangou.saas.order.platform.client.dto.response.CommentReportResponse;
import com.meituan.shangou.saas.order.platform.client.dto.response.OrderReturnReportResponse;
import com.meituan.shangou.saas.order.platform.client.enums.StatusCodeEnum;
import com.meituan.shangou.saas.order.platform.client.service.report.OrderFulfillmentReportThriftService;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.OrderFulfillmentReportReq;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.OrderFulfillmentReportQueryResp;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.OrderFulfillmentReportVO;
import com.sankuai.shangou.qnh.orderapi.enums.pc.RefundTypeEnum;
import com.sankuai.shangou.qnh.orderapi.remote.PoiRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CommentReportAsyncServiceImpl implements OrderReportAsyncService {

    @Resource
    private OrderFulfillmentReportThriftService orderFulfillmentReportThriftService;
    @Resource
    protected PoiRemoteService poiRemoteService;
    @Override
    public OrderFulfillmentReportQueryResp asyncQueryReport(OrderFulfillmentReportReq request, List<Long> poiIds, List<Long> warehouseIds) {
        OrderFulfillmentReportQueryResp resp = new OrderFulfillmentReportQueryResp();
        log.info("AfsOrderReportAsyncServiceImpl asyncQueryReport request:{}, poiIds:{}, warehouseIds:{}", request, poiIds, warehouseIds);
        OrderReportRequest orderReportRequest = new OrderReportRequest();
        orderReportRequest.setTenantId(request.getTenantId());
        if (request.getFilterType() != 0) {
            if (CollectionUtils.isNotEmpty(warehouseIds)) {
                Map<Long, List<Long>> warehouseId2PoiIdMap = poiRemoteService.queryShareableWarehouseRelatedStoreId(request.getTenantId(), warehouseIds);
                if (MapUtils.isNotEmpty(warehouseId2PoiIdMap)) {
                    poiIds.addAll(warehouseId2PoiIdMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList()));
                    poiIds = poiIds.stream().distinct().collect(Collectors.toList());
                }
            }
        }
        orderReportRequest.setShopIdList(poiIds);
        try {
            CommentReportResponse commentReport = orderFulfillmentReportThriftService.queryCommentReport(orderReportRequest);
            if (commentReport.getStatus().getCode() != StatusCodeEnum.SUCCESS.getValue()) {
                log.error("AfsOrderReportAsyncServiceImpl asyncQueryReport 查询失败：param {}，result:{}", orderReportRequest, commentReport);
                return resp;
            }
            if (CollectionUtils.isEmpty(commentReport.getCommentReportList())) {
                resp.setOrderFulfillmentDetailReportList(Lists.newArrayList());
                return resp;
            }
            List<OrderFulfillmentReportVO> reportVOList = commentReport.getCommentReportList().stream().map(orderReport -> toBuild(orderReport)).collect(Collectors.toList());
            resp.setOrderFulfillmentDetailReportList(reportVOList);
        } catch (TException e) {
            log.error("AfsOrderReportAsyncServiceImpl asyncQueryReport error", e);
            throw new RuntimeException(e);
        }

        return resp;

    }

    private OrderFulfillmentReportVO toBuild(CommentReport orderReport) {
        OrderFulfillmentReportVO reportVO = new OrderFulfillmentReportVO();
        reportVO.setShopId(orderReport.getShopId());
        reportVO.setShopName(orderReport.getShopName());
        reportVO.setWarehouseId(orderReport.getWarehouseId());
        reportVO.setWarehouseName(orderReport.getWarehouseName());
        reportVO.setBadCommentCount(orderReport.getBadCommentCount());
        reportVO.setBadCommentRatio(orderReport.getBadCommentRatio());
        reportVO.setBadCommentUnReplyCount(orderReport.getBadCommentUnReplyCount());
        reportVO.setTotalCommentCount(orderReport.getTotalCommentCount());
        return reportVO;
    }
}
