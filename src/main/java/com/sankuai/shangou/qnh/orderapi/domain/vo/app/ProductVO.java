package com.sankuai.shangou.qnh.orderapi.domain.vo.app;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.ExchangeProductVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.SubProductVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 商品信息
 *
 * <AUTHOR>
 * @since 2019/7/10
 *
 */
@TypeDoc(
        description = "商品信息"
)
@ApiModel("商品信息")
@Data
public class ProductVO {

    @FieldDoc(
            description = "skuId", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "skuId")
    private String skuId;

    @FieldDoc(
            description = "orderItemId", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "orderItemId")
    private Long orderItemId;

    @FieldDoc(
            description = "upc码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "upc码", required = true)
    private String upcCode;

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "商品名称", required = true)
    private String skuName;

    @FieldDoc(
            description = "商品图片URL", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "商品图片URL", required = true)
    private String picUrl;

    @FieldDoc(
            description = "规格", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "规格", required = true)
    private String specification;

    @FieldDoc(
            description = "售卖单位", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "售卖单位", required = true)
    private String sellUnit;

    @FieldDoc(
            description = "原总价  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "原总价  单位:分", required = true)
    private Integer originalTotalPrice;

    @FieldDoc(
            description = "实付金额  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "实付金额  单位:分", required = true)
    private Integer totalPayAmount;

    @FieldDoc(
            description = "单价  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "单价  单位:分", required = true)
    private Integer unitPrice;

    @FieldDoc(
            description = "购买数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "购买数量", required = true)
    private Integer count;

    @FieldDoc(
            description = "商品项线下价格=线下价格*数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "商品项线下价格", required = true)
    private Integer orderItemOfflinePrice;

    @FieldDoc(
            description = "商品标签信息列表(1履约标签、2拣货标签)、客户端将type用于修改样式、新标签需要使用tagInfos", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "商品标签信息列表(1履约标签、2拣货标签)、客户端将type用于修改样式、新标签需要使用tagInfos")
    private List<TagInfoVO> tagInfoList;

    @FieldDoc(
            description = "商品标签信息列表(除1履约标签、2拣货标签之外的其它新标签都用此对象)  3商品属性标签", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "商品标签信息列表(除1履约标签、2拣货标签之外的其它新标签都用此对象) 3商品属性标签")
    private List<TagInfoVO> tagInfos;

    @FieldDoc(
            description = "商品现价", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "商品现价")
    private Integer currentPrice;

    @FieldDoc(
            description = "组合品子商品列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "组合品子商品列表")
    private List<SubProductVo> subProductVoList;

    @FieldDoc(
            description = "换货商品列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "换货商品列表")
    private List<ExchangeProductVo> exchangeProductVoList;


    @FieldDoc(
            description = "erpCode", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "erpCode")
    private String erpItemCode;

    @FieldDoc(
            description = "按配置解析后的属性", requiredness = Requiredness.OPTIONAL
    )
    private List<PropertiesViewVO> parsedProperties;

    @FieldDoc(
            description = "多张商品图片URL", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "多张商品图片URL")
    private List<String> multiPicUrl;

    @FieldDoc(
            description = "赠品类型 0-平台赠品 1-自定义赠品", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "赠品类型 0-平台赠品 1-自定义赠品")
    private Integer giftType;

    @FieldDoc(
            description = "货架码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "goodsCode")
    private String goodsCode;

    @FieldDoc(
            description = "商品标签信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "商品标签信息")
    private List<ChannelLabelVO> channelLabelList;

    @FieldDoc(
            description = "商品标签附加信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "商品标签附加信息")
    private String labelSubDesc;

    @FieldDoc(
            description = "赠品对应主品skuId", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "赠品对应主品skuId")
    private String belongSkuId;

}
