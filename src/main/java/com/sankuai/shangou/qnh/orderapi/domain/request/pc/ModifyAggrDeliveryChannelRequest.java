package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/12/30
 */
@Data
public class ModifyAggrDeliveryChannelRequest implements BaseRequest {

    @FieldDoc(
            description = "门店ID"
    )
    @ApiModelProperty(value = "门店ID", required = true)
    private Long poiId;

    @FieldDoc(
            description = "配送渠道ID"
    )
    @ApiModelProperty(value = "配送渠道ID")
    private Integer deliveryChannelId;

    @FieldDoc(
            description = "是否已开通渠道,0-未开通，1-开通"
    )
    @ApiModelProperty(value = "是否已开通")
    private int isOpen;

    @FieldDoc(
            description = "配送渠道门店id"
    )
    @ApiModelProperty(value = "配送渠道门店ID")
    private String deliveryChannelPoiId;

    @FieldDoc(
            description = "配送渠道商户ID"
    )
    @ApiModelProperty(value = "配送渠道商户ID")
    private String deliveryChannelMerchantId;

    @FieldDoc(
            description = "绑定配送渠道门店所需的扩展信息，JSON 字符串"
    )
    @ApiModelProperty(value = "绑定配送渠道门店所需的扩展信息，JSON 字符串")
    private String deliveryChannelPoiExt;

    @Override
    public void selfCheck() {
        AssertUtil.notNull(poiId, "门店ID不能为空" , "poiId");
        AssertUtil.notNull(deliveryChannelId, "渠道Id不能为空" , "deliveryChannelId");
        AssertUtil.notNull(deliveryChannelPoiId, "配送渠道门店ID不能为空" , "deliveryChannelPoiId");
    }
}
