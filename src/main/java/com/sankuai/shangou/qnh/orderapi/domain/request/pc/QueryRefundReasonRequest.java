package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import lombok.Data;

/**
 * @Author: <EMAIL>
 * @Date: 2019/4/22 15:20
 * @Description:
 */
@Data
public class QueryRefundReasonRequest implements BaseRequest {

    private String channelId;

    private int orderType;


    @Override
    public void selfCheck() {
        AssertUtil.notEmpty(channelId,"渠道编码不能为空");
    }
}
