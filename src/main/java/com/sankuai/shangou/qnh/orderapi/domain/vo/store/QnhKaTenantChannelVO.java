package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/22 3:32 PM
 * @description 牵牛花ka租户渠道配置列表
 */
@TypeDoc(
        description = "牵牛花ka租户渠道配置项"
)
@ApiModel("牵牛花ka租户渠道配置项")
@Data
public class QnhKaTenantChannelVO {

    private Long tenantId;

    private List<ChannelVO> channels;
}
