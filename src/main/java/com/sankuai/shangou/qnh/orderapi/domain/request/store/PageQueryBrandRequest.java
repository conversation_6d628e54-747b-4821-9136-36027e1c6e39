package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2019/6/12
 * desc: 品牌分页查询请求
 */
@TypeDoc(
        description = "品牌分页查询请求"
)
@Data
@ApiModel("品牌分页查询请求")
public class PageQueryBrandRequest {

    @FieldDoc(
            description = "关键字", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "关键字")
    private String keywords;

    @FieldDoc(
            description = "页码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "页码", required = true)
    @NotNull
    private Integer pageNum;

    @FieldDoc(
            description = "每页条数(最大200)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "页码", required = true)
    @NotNull
    private Integer pageSize;
}
