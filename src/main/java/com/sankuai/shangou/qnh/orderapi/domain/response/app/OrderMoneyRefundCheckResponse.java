package com.sankuai.shangou.qnh.orderapi.domain.response.app;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.OrderItemMoneyRefundCheckVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.RefundReasonAndCodeVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Created by suxiaoyu on 2023/3/7 14:19
 */
@TypeDoc(
        description = "金额退页面检查的response"
)
@Data
public class OrderMoneyRefundCheckResponse {
    @FieldDoc(
            description = "金额退页面检查的商品信息"
    )
    private List<OrderItemMoneyRefundCheckVO> moneyRefundCheckVOList;

    @FieldDoc(
            description = "退款理由"
    )
    private List<RefundReasonAndCodeVO> refundReasons;

    @FieldDoc(
            description = "应该获取集合店商家手机号,true:是", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "应该获取集合店商家手机号")
    private Boolean shouldGetGatherPhone;
}
