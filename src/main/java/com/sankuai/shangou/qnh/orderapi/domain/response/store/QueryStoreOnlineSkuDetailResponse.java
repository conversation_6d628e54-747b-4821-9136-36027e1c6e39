package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.CdqStoreSkuWithChannelInfoVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 获取门店商品详情响应
 * @author: Wang<PERSON><PERSON><PERSON>
 * @create: 2020-01-02
 **/
@TypeDoc(
        description = "获取门店商品详情响应"
)
@Data
@ApiModel("获取门店商品详情响应")
public class QueryStoreOnlineSkuDetailResponse {

    @FieldDoc(
            description = "门店商品明细", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店商品明细", required = true)
    private CdqStoreSkuWithChannelInfoVo storeSku;

}
