// Copyright (C) 2020 Meituan
// All rights reserved
package com.sankuai.shangou.qnh.orderapi.context.store;

import com.sankuai.shangou.qnh.orderapi.domain.vo.store.H5LoginInfo;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 2020/3/2 下午2:28
 **/
public class H5LoginContext {
    private static ThreadLocal<H5LoginInfo> loginInfoThreadLocal = ThreadLocal
            .withInitial(H5LoginInfo::new);;

    public static void setLoginInfo(H5LoginInfo h5LoginInfo) {
        loginInfoThreadLocal.set(h5LoginInfo);
    }

    public static H5LoginInfo getLoginInfo() {
        return loginInfoThreadLocal.get();
    }
}
