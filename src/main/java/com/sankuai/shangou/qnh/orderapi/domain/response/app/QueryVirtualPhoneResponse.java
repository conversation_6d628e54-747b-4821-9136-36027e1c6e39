package com.sankuai.shangou.qnh.orderapi.domain.response.app;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/7/11
 * desc: 查询虚拟电话响应
 */
@TypeDoc(
        description = "查询虚拟号响应"
)
@ApiModel("查询虚拟号响应")
@Data
public class QueryVirtualPhoneResponse {

    @FieldDoc(
            description = "收货号码-隐私号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "收货号码-隐私号", required = true)
    private String phoneNo;

    @FieldDoc(
            description = "收货号码-备用隐私号码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "收货号码-备用隐私号码", required = true)
    private List<String> backUpPhoneNo = new ArrayList<>();


    @FieldDoc(
            description = "微信绑定手机号-隐私号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "微信绑定手机号-隐私号", required = true)
    private String bindPhoneNo;

    @FieldDoc(
            description = "微信绑定手机号-备用隐私号码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "微信绑定手机号-备用隐私号码", required = true)
    private List<String> bindBackupPhoneNo = new ArrayList<>();

    @FieldDoc(
            description = "应该获取集合店手机号,ture:前端进行弹窗提示，后续上送phoneType=3-美团名酒馆集合店号码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "应该获取集合店手机号")
    private boolean shouldGetGatherPhone;
}