package com.sankuai.shangou.qnh.orderapi.domain.result.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import lombok.*;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/23 11:24
 * @Description:
 */
@TypeDoc(
        description = "列表结果封装",
        authors = {
                "RAUL.CHENG"
        },
        version = "V1.0"
)
@ApiModel("列表结果封装")
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ListResultData<T> {
    @FieldDoc(
            description = "返回数据"
    )
    private List<T> list = new ArrayList<>();

}
