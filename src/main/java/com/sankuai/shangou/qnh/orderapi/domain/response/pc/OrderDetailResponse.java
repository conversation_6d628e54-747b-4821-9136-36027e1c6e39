package com.sankuai.shangou.qnh.orderapi.domain.response.pc;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.OrderDetailVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@TypeDoc(
        description = "订单详情"
)
@ApiModel("订单详情")
@Data
public class OrderDetailResponse {
    private OrderDetailVO orderDetail;
    private Integer code;
    private String msg;
}
