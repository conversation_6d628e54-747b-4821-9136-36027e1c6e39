package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/12/5  5:21 下午
 * @since 1.0.0
 */
@TypeDoc(
        description = "根据导出类型查询导出字段信息"
)
@ApiModel("根据导出类型查询导出字段信息")
@Getter
@Setter
@ToString
@NoArgsConstructor
public class ExportTypeRequest {
    @FieldDoc(
            description = "导出类型"
    )
    @ApiModelProperty(value = "导出类型", required = true)
    private String type;

}
