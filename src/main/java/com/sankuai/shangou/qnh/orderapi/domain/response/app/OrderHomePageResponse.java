package com.sankuai.shangou.qnh.orderapi.domain.response.app;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.OrderHomePageModuleVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.OrderPendingTaskVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.ThriftPartyModuleVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.ThriftPartyTaskVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @createTime 2019/11/19
 * @description
 */
@TypeDoc(
        description = "订单首页查询返回"
)
@Data
@ApiModel("订单首页查询返回")
public class OrderHomePageResponse {

    @FieldDoc(
            description = "订单首页模块", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单首页模块", required = true)
    @NotNull
    private OrderHomePageModuleVO orderHomePageModuleVO;

    @FieldDoc(
            description = "订单首页待办", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单首页待办", required = true)
    @NotNull
    private OrderPendingTaskVO orderPendingTaskVO;

    @FieldDoc(
            description = "三方Tab", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "三方Tab待办", required = true)
    private ThriftPartyModuleVO thriftPartyModuleVO;

    @FieldDoc(
            description = "三方Tab待办", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "三方Tab待办", required = true)
    private ThriftPartyTaskVO thriftPartyTaskVO;

}
