package com.sankuai.shangou.qnh.orderapi.domain.response.pc;

import java.util.List;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.PoiGroupLogVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * @Author: wangjian
 * @Date: 2022/07/27
 * @Description:
 */
@TypeDoc(
        description = "门店分组列表"
)
@ApiModel("门店分组列表")
@Getter
@Setter
@ToString
@NoArgsConstructor
public class PoiGroupLogResp {

    @FieldDoc(
            description = "第几页"
    )
    @ApiModelProperty(value = "第几页", required = true)
    public Integer page;

    /**
     * 每页大小
     */
    @FieldDoc(
            description = "每页大小"
    )
    @ApiModelProperty(value = "每页大小", required = true)
    public Integer pageSize;

    /**
     * 总条数
     */
    @FieldDoc(
            description = "总条数"
    )
    @ApiModelProperty(value = "总条数", required = true)
    public Integer total;

    /**
     * 总条数
     */
    @FieldDoc(
            description = "总条数"
    )
    @ApiModelProperty(value = "总条数", required = true)
    public List<PoiGroupLogVO> list;
}