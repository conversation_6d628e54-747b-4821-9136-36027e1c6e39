package com.sankuai.shangou.qnh.orderapi.domain.request.app;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.ChannelOrderIdConditionVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

@TypeDoc(
        description = "订单列表查询请求"
)
@ApiModel("订单列表查询请求")
@Data
@Slf4j
public class OrderListQueryRequest {

    @FieldDoc(
            description = "订单列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "订单列表")
    private List<ChannelOrderIdConditionVO> orderList;

    @FieldDoc(
            description = "门店类型 3-门店 5-中心仓 6-前置仓", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店类型")
    private Integer entityType;

    
    public String validate() {
        if (CollectionUtils.isEmpty(orderList)) {
            return "订单列表不能为空";
        }
        return null;
    }
}
