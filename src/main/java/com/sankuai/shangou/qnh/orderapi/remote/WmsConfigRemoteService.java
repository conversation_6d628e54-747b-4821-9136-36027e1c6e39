package com.sankuai.shangou.qnh.orderapi.remote;

import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Lists;
import com.sankuai.meituan.reco.store.management.wms.api.config.WmsConfigurationThriftService;
import com.sankuai.meituan.reco.store.management.wms.api.config.model.StockDockingConfigDTO;
import com.sankuai.meituan.reco.store.management.wms.api.config.req.BatchQueryStockConfigReq;
import com.sankuai.shangou.common.Result;
import com.sankuai.shangou.qnh.orderapi.utils.pc.RpcInvoker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Optional;

@Service
@Slf4j
@Rhino
public class WmsConfigRemoteService {
    @Resource
    private WmsConfigurationThriftService wmsConfigurationThriftService;

    public final static Integer STOCK_BY_POS = 1;


    // 0 牵牛花管理库存 1 外部系统管理库存
    public Integer queryStockDockingConfig(Long tenantId, Long shopId) {
        BatchQueryStockConfigReq batchQueryStockConfigReq = new BatchQueryStockConfigReq();
        batchQueryStockConfigReq.setMerchantId(tenantId);

        // 最多100个门店
        batchQueryStockConfigReq.setWarehouseIdList(Lists.newArrayList(shopId));

        Result<StockDockingConfigDTO> stockDockingConfigDTO = RpcInvoker.invoke(() -> wmsConfigurationThriftService.batchQueryStockDockingConfig(batchQueryStockConfigReq));

        Assert.notNull(stockDockingConfigDTO);
        Assert.notNull(stockDockingConfigDTO.getModule());

        if (stockDockingConfigDTO.getCode() == 0) {
            return Optional.ofNullable(stockDockingConfigDTO.getModule().getStockDockingTypeMap()).orElse(new HashMap<>()).get(shopId);
        } else {
            log.error("查询库存对接类型失败，{}", stockDockingConfigDTO);
        }

        return null;
    }


}
