package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import javax.validation.constraints.NotNull;

@TypeDoc(
        description = "券配置VO"
)
@Data
@NotNull
public class IMCouponConfigVO {
    @FieldDoc(
            description = "减免金额上限",
            example = {}
    )
    private String reduceFeeUpperLimit;
    @FieldDoc(
            description = "减免金额下限",
            example = {}
    )
    private String reduceFeeLowerLimit;
    @FieldDoc(
            description = "有效期上限（日）",
            example = {}
    )
    private Integer validUpperLimit;
    @FieldDoc(
            description = "日总计金额上限",
            example = {}
    )
    private String dailySumLimit;
    @FieldDoc(
            description = "日总计次数上限",
            example = {}
    )
    private Integer dailyCountLimit;
    @FieldDoc(
            description = "租户id",
            example = {}
    )
    private String tenantBizMode;
    @FieldDoc(
            description = "门店id",
            example = {}
    )
    private Integer poiId;
}
