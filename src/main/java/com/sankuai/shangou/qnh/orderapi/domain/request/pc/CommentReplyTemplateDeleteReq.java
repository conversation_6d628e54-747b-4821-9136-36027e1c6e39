package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.exception.pc.ParamInvalidException;
import lombok.Data;

@TypeDoc(
        description = "删除评价回复模板请求参数"
)
@Data
public class CommentReplyTemplateDeleteReq {

    @FieldDoc(
            description = "模板id", requiredness = Requiredness.REQUIRED
    )
    private Long templateId;

    public void selfCheck() {
        if (this.templateId == null) {
            throw new ParamInvalidException("模板id不能为空");
        }
    }
}