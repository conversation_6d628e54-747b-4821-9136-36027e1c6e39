package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.dto.request.ocms.OCMSOrderPartRefundRequest;
import com.meituan.shangou.saas.o2o.dto.model.OCMSOrderPartRefundProductModel;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.PartRefundProductVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.RefundGoodsSoldOutVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.RefundGoodsTakenOffVO;
import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by suxiaoyu on 2023/3/15 14:56
 */
@TypeDoc(
        description = "售后退款请求"
)
@ApiModel("售后退款请求")
@Data
public class AfterSaleRefundRequest implements BaseRequest {

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道ID")
    private Integer channelId;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道订单号")
    private String channelOrderId;

    @FieldDoc(
            description = "部分退款原因", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "部分退款原因")
    @NotNull(message = "退款原因不能为空")
    private String reason;

    @FieldDoc(
            description = "部分原因code,如果是自定义原因传-1", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "部分原因code")
    public int reasonCode;

    @FieldDoc(
            description = "部分退款商品列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "部分退款商品列表")
    private List<PartRefundProductVO> partRefundProductList;

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID")
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "商品改库存为0列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品已售完列表")
    private List<RefundGoodsSoldOutVO> refundGoodsSoldOutVOList;

    @FieldDoc(
            description = "商品下架列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品售罄列表")
    private List<RefundGoodsTakenOffVO> refundGoodsTakenOffVOList;

    public OCMSOrderPartRefundRequest convertOCMSOrderPartRefundRequest() {
        OCMSOrderPartRefundRequest bizRequest = new OCMSOrderPartRefundRequest();
        bizRequest.setOperatorUserId(ContextHolder.currentUserStaffId());
        bizRequest.setOperatorUserName(ContextHolder.currentUserName());
        bizRequest.setOrderBizType(ChannelOrderConvertUtils.sourceMid2Biz(this.getChannelId()));
        bizRequest.setPartRefundProductModelList(this.getPartRefundProductList().stream().map(refundItem -> {
            OCMSOrderPartRefundProductModel model = new OCMSOrderPartRefundProductModel();
            model.setCount(refundItem.getCount());
            model.setCustomSkuId(refundItem.getCustomSkuId());
            model.setSkuId2(refundItem.getSkuId());
            model.setSkuName(refundItem.getSkuName());
            model.setOrderItemId(refundItem.getOrderItemId());
            return model;
        }).collect(Collectors.toList()));
        bizRequest.setReason(this.getReason());
        bizRequest.setReasonCode(this.getReasonCode());
        bizRequest.setShopId(this.getStoreId());
        bizRequest.setTenantId(ContextHolder.currentUserTenantId());
        bizRequest.setViewOrderId(this.getChannelOrderId());
        bizRequest.setRequestId(System.currentTimeMillis() + this.getChannelOrderId());
        return bizRequest;
    }

    public void selfCheck() {
        //前端存在上送【partRefundProductList】为空的情况，导致后端出现NPE异常
        AssertUtil.isTrue(CollectionUtils.isNotEmpty(this.getPartRefundProductList()), "缺少商品信息");
    }
}
