package com.sankuai.shangou.qnh.orderapi.context.pc;

import com.meituan.shangou.sac.dto.model.SacManagerAccountDto;
import com.sankuai.meituan.shangou.empower.auth.sdk.bean.SessionInfo;
import com.sankuai.meituan.shangou.empower.auth.sdk.context.SessionContext;
import com.sankuai.meituan.shangou.empower.auth.thrift.enums.AccountTypeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.AccountSessionVO;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.qnh.orderapi.constant.pc.ConfigDefaultValueConstant;
import com.sankuai.shangou.qnh.orderapi.utils.pc.LoginUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * 上下文信息
 *
 * <AUTHOR>
 */
public class ContextHolder {

    private static final ThreadLocal<User> USER_THREAD_LOCAL = new ThreadLocal<User>();

    /**
     * 注册当前用户信息
     * 仅测试用例使用
     */
    @Deprecated
    public static void registerCurrentUser(Long uid, String account, Long tenantId, String token, Long staffId,
        Integer appId) {
        USER_THREAD_LOCAL.set(new User(uid, account, tenantId, token, staffId, appId));
        SessionContext.init(convertUserToSessionInfo(USER_THREAD_LOCAL.get()));
    }

    /**
     * 注册EP登录方式的当前用户信息
     */
    public static void registerCurrentUser(AccountSessionVO accountSessionVO, String token,
        HttpServletRequest request) {
        USER_THREAD_LOCAL.set(User.builder()
                .uid(accountSessionVO.getAccountId())
                .account(accountSessionVO.getAccountName())
                .name(accountSessionVO.getEmpName())
                .tenantId(accountSessionVO.getTenantId())
                .staffId(accountSessionVO.getStaffId())
                .appId(LoginUtil.getAppId(request))
                .token(token)
                .bizAppId(LoginUtil.getBizAppId(request))
                .accountType(accountSessionVO.getAccountType())
                .build());
        SessionContext.init(convertUserToSessionInfo(USER_THREAD_LOCAL.get()));
    }

    /**
     * 注册M端一键SSO登录方式的当前用户信息
     */
    public static void registerSsoUser(AccountSessionVO accountSessionVO, HttpServletRequest request) {
        Integer appId = LoginUtil.getAppId(request);
        if (Objects.isNull(appId)){
            throw new BizException("鉴权必要参数校验失败，请尝试重新登录");
        }
        USER_THREAD_LOCAL.set(User.builder()
                .uid(accountSessionVO.getAccountId())
                .account(accountSessionVO.getAccountName())
                .name(accountSessionVO.getEmpName())
                .tenantId(accountSessionVO.getTenantId())
                .staffId(accountSessionVO.getStaffId())
                .appId(appId)
                .bizAppId(LoginUtil.getBizAppId(request))
                .accountType(AccountTypeEnum.MT_MANAGER.getValue())
                .build());
        SessionContext.init(convertUserToSessionInfo(USER_THREAD_LOCAL.get()));
    }


    /**
     * 注册当前用户信息
     * 仅测试用例使用
     */
    @Deprecated
    public static void registerCurrentUser(Long uid, String account, Long tenantId, String token, Long staffId) {
        USER_THREAD_LOCAL
                .set(new User(uid, account, tenantId, token, staffId, ConfigDefaultValueConstant.SAAS_B_APP_ID));
        SessionContext.init(convertUserToSessionInfo(USER_THREAD_LOCAL.get()));
    }

    /**
     * 直接设置 User 信息，仅用于用于跨线程同步
     *
     * @param user 用户信息
     */
    public static void setUserDirectly(User user) {
        USER_THREAD_LOCAL.set(user);
        SessionContext.init(convertUserToSessionInfo(USER_THREAD_LOCAL.get()));
    }

    /**
     * 获取当前 User 信息
     *
     * @return 当前 User 信息
     */
    public static User currentUser() {
        return USER_THREAD_LOCAL.get();
    }

    /**
     * 获取当前账号Id，即 accountId
     */
    public static Long currentUid() {
        User user = USER_THREAD_LOCAL.get();
        return user == null ? null : user.uid;
    }

    /**
     * 获取当前用户账号，即 accountName
     */
    public static String currentAccount() {
        User user = USER_THREAD_LOCAL.get();
        return user == null ? null : user.account;
    }

    /**
     * 获取当前用户账号类型 AccountTypeEnum
     */
    public static Integer currentAccountType() {
        User user = USER_THREAD_LOCAL.get();
        return user == null ? null : user.accountType;
    }

    /**
     * 获取当前用户名称，即员工姓名 employeeName （租户域）
     * @return
     */
    public static String currentUserName() {
        User user = USER_THREAD_LOCAL.get();
        return user == null ? null : user.name;
    }

    /**
     * 获取当前用户租户id
     */
    public static Long currentUserTenantId() {
        User user = USER_THREAD_LOCAL.get();
        return user == null ? null : user.tenantId;
    }

    /**
     * 获取当前用户登录token
     */
    public static String currentUserLoginToken() {
        User user = USER_THREAD_LOCAL.get();
        return user == null ? null : user.token;
    }

    /**
     * 获取当前用户登录的appId
     */
    public static Integer currentUserLoginAppId() {
        User user = USER_THREAD_LOCAL.get();
        return user == null ? ConfigDefaultValueConstant.SAAS_B_APP_ID : user.appId;
    }

    /**
     * 从 cookie 中获取当前用户登录的appId，获取不到返回 null，无默认值逻辑
     */
    public static Integer currentUserLoginAppIdFromCookie() {
        User user = USER_THREAD_LOCAL.get();
        return user == null ? null : user.appId;
    }

    /**
     * 获取当前用户登录的appId
     */
    public static Integer currentUserBizAppId() {
        User user = USER_THREAD_LOCAL.get();
        return user == null ? ConfigDefaultValueConstant.DEFAULT_BIZ_APP_ID  : user.bizAppId;
    }


    /**
     * 获取当前用户id
     */
    public static Long currentUserStaffId() {
        User user = USER_THREAD_LOCAL.get();
        return user == null ? null : user.staffId;
    }

    /**
     * 当前账号是否为租户管理员账号
     *
     * @return bool ｜ null 表示未知（正常情况不会为null）
     */
    public static Boolean isTenantManagerAccountNullable() {
        User user = USER_THREAD_LOCAL.get();
        return (Objects.isNull(user) || Objects.isNull(user.getAccountType()))
                ? null :
                Objects.equals(user.getAccountType(), AccountTypeEnum.ADMIN.getValue());
    }

    /**
     * 当前账号是否为租户管理员账号
     * 获取不到时抛出异常，理论上需要登录才能访问的接口都能获取到
     *
     * @return bool
     */
    public static boolean isTenantManagerAccount() {
        Boolean bool = isTenantManagerAccountNullable();
        if (Objects.isNull(bool)) {
            throw new IllegalStateException("获取账号类型为空");
        }
        return bool;
    }

    /**
     * 当前账号是否为美团管理员账号（SSO一键登录账号）
     *
     * @return bool ｜ null 表示未知（正常情况不会为null）
     */
    public static Boolean isMtManagerAccountNullable() {
        User user = USER_THREAD_LOCAL.get();
        return (Objects.isNull(user) || Objects.isNull(user.getAccountType()))
                ? null :
                Objects.equals(user.getAccountType(), AccountTypeEnum.MT_MANAGER.getValue());
    }

    /**
     * 当前账号是否为美团管理员账号
     * 获取不到时抛出异常，理论上需要登录才能访问的接口都能获取到
     *
     * @return bool
     */
    public static boolean isMtManagerAccount() {
        Boolean bool = isMtManagerAccountNullable();
        if (Objects.isNull(bool)) {
            throw new IllegalStateException("获取账号类型为空");
        }
        return bool;
    }

    /**
     * 是否为管理员账号（租户管理员/美团管理员）
     *
     * @return bool ｜ null 表示未知（正常情况不会为null）
     */
    public static Boolean isManagerAccountNullable() {
        Boolean isManagerAccountNullable = null;
        if (Boolean.TRUE.equals(isTenantManagerAccountNullable()) || Boolean.TRUE.equals(isMtManagerAccountNullable())) {
            isManagerAccountNullable = Boolean.TRUE;
        } else if (Boolean.FALSE.equals(isTenantManagerAccountNullable()) || Boolean.FALSE.equals(isMtManagerAccountNullable())) {
            isManagerAccountNullable = Boolean.FALSE;
        }
        return isManagerAccountNullable;
    }

    /**
     * 当前账号是否为管理员账号（租户管理员/美团管理员）
     * 获取不到时抛出异常，理论上需要登录才能访问的接口都能获取到
     *
     * @return bool
     */
    public static boolean isManagerAccount() {
        Boolean bool = isManagerAccountNullable();
        if (Objects.isNull(bool)) {
            throw new IllegalStateException("获取账号类型为空");
        }
        return bool;
    }

    /**
     * 销毁上下文数据
     */
    public static void release() {
        USER_THREAD_LOCAL.remove();
        SessionContext.destroy();
    }

    /**
     * 将 eapi 中 user 信息转换为 auth 域类的 SessionInfo
     *
     * @param user User
     * @return SessionInfo
     */
    private static SessionInfo convertUserToSessionInfo(User user) {
        if (Objects.isNull(user)) {
            return null;
        }
        // 只保存需要的参数
        SessionInfo sessionInfo = new SessionInfo();
        sessionInfo.setTenantId(user.getTenantId());
        sessionInfo.setAccountId(user.getUid());
        sessionInfo.setAccountName(user.getAccount());
        sessionInfo.setEmpName(user.getName());
        return sessionInfo;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Getter
    @Setter
    @Builder
    @ToString
    public static class User {

        /**
         * 账号Id，即 accountId
         */
        private Long uid;
        /**
         * 账号名，即 accountName
         */
        private String account;
        /**
         * 租户id
         */
        private Long tenantId;

        /**
         * 登录token
         */
        private String token;

        /**
         * 用户名称，即员工姓名 employeeName （租户域）
         */
        private String name;

        /**
         * 用户id，即员工id employeeId （租户域）
         */
        private Long staffId;

        /**
         * 权限主应用Id
         */
        private Integer appId;

        /**
         * 业务系统
         */
        private Integer bizAppId;

        /**
         * 账号类型
         * @see AccountTypeEnum
         */
        private Integer accountType;

        public User(Long uid, String account, Long tenantId, String token, Long staffId, Integer appId) {
            this.uid = uid;
            this.account = account;
            this.tenantId = tenantId;
            this.token = token;
            this.staffId = staffId;
            this.appId = appId;
        }
    }
}
