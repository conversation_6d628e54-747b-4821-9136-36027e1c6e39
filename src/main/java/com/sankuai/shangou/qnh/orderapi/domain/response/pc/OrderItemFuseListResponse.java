package com.sankuai.shangou.qnh.orderapi.domain.response.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.OrderFuseFinanceDetailBO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.SubProductVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.OrderItemFuseVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.PageInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/12/6
 * desc: 分页查询订单明细列表响应
 */
@TypeDoc(
        description = "分页查询订单明细列表响应"
)
@Data
@ApiModel("分页查询订单明细列表响应")
public class OrderItemFuseListResponse {

    @FieldDoc(
            description = "分页信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "分页信息", required = true)
    private PageInfoVO pageInfo;

    @FieldDoc(
            description = "订单列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单列表", required = true)
    private List<OrderItemFuseVO> itemList;


    public void mergeFinanceInfo(Map<Long, OrderFuseFinanceDetailBO.ComposeItemFinanceInfo> composeItemFinanceInfoMap) {
        if (MapUtils.isEmpty(composeItemFinanceInfoMap)) {
            return;
        }
        for (OrderItemFuseVO v : itemList) {
            if (CollectionUtils.isNotEmpty(v.getSubProduct())) {
                for (SubProductVo item : v.getSubProduct()) {
                    item.mergeFinanceInfo(composeItemFinanceInfoMap.get(item.getServiceId()));
                }
            }
        }
    }
}
