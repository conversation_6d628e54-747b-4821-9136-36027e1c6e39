package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.AdjustListDeatil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "查询调价列表响应"
)
@Data
public class SearchAdjustListResponse {

    @FieldDoc(
            description = "是否有更多"
    )
    @ApiModelProperty(value = "是否有更多")
    private Boolean hasMore;

    @FieldDoc(
            description = "总数"
    )
    @ApiModelProperty(value = "总数")
    private Integer totalCount;

    @FieldDoc(
            description = "调价单列表"
    )
    @ApiModelProperty(value = "调价单列表")
    private List<AdjustListDeatil> adjustOrderList;

}
