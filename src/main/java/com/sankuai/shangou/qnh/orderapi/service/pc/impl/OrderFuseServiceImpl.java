package com.sankuai.shangou.qnh.orderapi.service.pc.impl;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.rhino.annotation.Degrade;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.meituan.mtrace.thread.pool.TraceExecutors;
import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.meituan.reco.pickselect.common.domain.orderTrack.OperationScene;
import com.meituan.reco.pickselect.common.domain.orderTrack.TrackOpType;
import com.meituan.reco.pickselect.common.domain.orderTrack.TrackSource;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.meituan.shangou.saas.dto.Status;
import com.meituan.shangou.saas.dto.StatusCodeEnum;
import com.meituan.shangou.saas.dto.request.ocms.QueryPartRefundGoodsRequest;
import com.meituan.shangou.saas.dto.response.OrderTrackDetailResponse;
import com.meituan.shangou.saas.dto.response.model.PartRefundGoodsModel;
import com.meituan.shangou.saas.dto.response.ocms.QueryOnlinePartRefundGoodsResponse;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderOffShelfGoodsModel;
import com.meituan.shangou.saas.o2o.dto.model.EvidenceModel;
import com.meituan.shangou.saas.o2o.dto.model.OCMSRefundReasonAndCodeModel;
import com.meituan.shangou.saas.o2o.dto.request.BizOrderOffShelfGoodsRequest;
import com.meituan.shangou.saas.o2o.dto.request.OCMSCheckRefundRequest;
import com.meituan.shangou.saas.o2o.dto.request.OCMSTenantAgreeRefundRequest;
import com.meituan.shangou.saas.o2o.dto.request.OCMSTenantReceiveProductsRequest;
import com.meituan.shangou.saas.o2o.dto.request.OCMSTenantRefundGoodsRequest;
import com.meituan.shangou.saas.o2o.dto.request.OCMSTenantRejectRefundRequest;
import com.meituan.shangou.saas.o2o.dto.request.OrderTrackRequest;
import com.meituan.shangou.saas.o2o.dto.response.OCMSRefundCheckResponse;
import com.meituan.shangou.saas.o2o.dto.response.OrderTrackResponse;
import com.meituan.shangou.saas.o2o.enums.AfsAuditStageEnum;
import com.meituan.shangou.saas.o2o.enums.AfsReviewTypeEnum;
import com.meituan.shangou.saas.o2o.service.BizOrderThriftService;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OcmsOrderDetailReq;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.model.AfterSaleApply;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.model.AfterSaleApplyDetail;
import com.meituan.shangou.saas.order.management.client.dto.response.ordersearch.model.OrderDetail;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.*;
import com.meituan.shangou.saas.service.ocms.OCMSOrderOperateThriftService;
import com.meituan.shangou.saas.service.ocms.OCMSOrderThriftService;
import com.meituan.shangou.saas.tenant.common.print.PrinterConnectionMode;
import com.meituan.shangou.saas.tenant.thrift.UserThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.enums.ChannelOnlineTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.SceneTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.user.UserDto;
import com.meituan.shangou.saas.tenant.thrift.dto.user.response.UserListResponse;
import com.sankuai.meituan.shangou.empower.finance.dto.model.OrderFinanceDetailModel;
import com.sankuai.meituan.shangou.empower.finance.dto.model.OrderFinanceModel;
import com.sankuai.meituan.shangou.empower.finance.dto.request.OrderFinanceQueryRequest;
import com.sankuai.meituan.shangou.empower.finance.dto.responce.OrderFinanceQueryResponse;
import com.sankuai.meituan.shangou.empower.finance.service.OrderFinanceThriftService;
import com.sankuai.meituan.shangou.empower.ocms.channel.thrift.dto.order.ChannelOrderDetailDTO;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.AgreeRefundReq;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.AgreeRefundResp;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.RejectRefundReq;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.RejectRefundResp;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.ChannelOrderTenantThriftService;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.qnh.orderapi.configuration.pc.OrderConfiguration;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.DeliveryExtBO;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.UserBo;
import com.sankuai.shangou.qnh.orderapi.domain.dto.pc.EvidenceDto;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.CheckRefundRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.RefundApplyAuditRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.TrackRequest;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.CheckRefundResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.CommonResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.OrderDetailResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.*;
import com.sankuai.shangou.qnh.orderapi.enums.IsReturnGoodsTypeEnum;
import com.sankuai.shangou.qnh.orderapi.enums.app.ErrorCodeEnum;
import com.sankuai.shangou.qnh.orderapi.enums.pc.RefundTypeEnum;
import com.sankuai.shangou.qnh.orderapi.enums.pc.TrackTypeEnum;
import com.sankuai.shangou.qnh.orderapi.exception.pc.CommonRuntimeException;
import com.sankuai.shangou.qnh.orderapi.remote.OCMSOrderRemoteService;
import com.sankuai.shangou.qnh.orderapi.remote.OcmsChannelRemoteService;
import com.sankuai.shangou.qnh.orderapi.remote.TenantRemoteService;
import com.sankuai.shangou.qnh.orderapi.service.pc.OrderFuseService;
import com.sankuai.shangou.qnh.orderapi.service.pc.OrderMngService;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import com.sankuai.shangou.qnh.orderapi.utils.pc.LionUtils;

import javafx.util.Pair;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;


import java.math.BigDecimal;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil.getTxdRefundAuditErrorMessage;

@Service
@Slf4j
public class OrderFuseServiceImpl implements OrderFuseService {

    /**
     * 同意退款
     */
    private static final int AGREE_REFUND = 1;

    /**
     * 拒绝退款
     */
    private static final int REJECT_REFUND = 2;

    @Resource
    private BizOrderThriftService bizOrderThriftService;

    @Autowired
    private UserThriftService userThriftService;

    @Resource
    private OCMSOrderOperateThriftService ocmsOrderOperateThriftService;

    @Autowired
    private OrderMngService orderMngService;

    @Autowired
    private TenantRemoteService tenantRemoteService;

    @Autowired
    private ChannelOrderTenantThriftService.Iface channelOrderService;

    @Resource
    private OCMSOrderThriftService ocmsOrderThriftService;

    @Resource
    private OCMSOrderRemoteService ocmsOrderRemoteService;

    @Resource
    private OrderFinanceThriftService orderFinanceThriftService;

    @Resource
    private OcmsChannelRemoteService ocmsChannelRemoteService;


    private static final ExecutorService fuseOrderExecutor = TraceExecutors.getTraceExecutorService(new ThreadPoolExecutor(
            20, 20,
            10, TimeUnit.SECONDS, new LinkedBlockingQueue<>(2000),
            new ThreadFactoryBuilder().setNameFormat("orderList Pool thread-%d").build()
    ));

    @Override
    @MethodLog(logRequest = true, logResponse = true)
    @CatTransaction
    public OrderTrackVO queryAllOrderTrackList(TrackRequest request) throws TException {


        if (!ObjectUtils.allNotNull(request.getOrderId(), request.getChannelId())) {
            throw new CommonRuntimeException("参数错误");
        }

        OrderTrackRequest orderTrackRequest = new OrderTrackRequest();
        orderTrackRequest.setOrderBizType(ChannelOrderConvertUtils.convertBizType(request.getChannelId()));
        orderTrackRequest.setViewOrderId(request.getOrderId());
        final Long tenantId = ContextHolder.currentUserTenantId();
        orderTrackRequest.setTenantId(tenantId);
        final TrackTypeEnum trackTypeEnum = TrackTypeEnum.getByCode(request.getOperatorType());

        if (Objects.nonNull(trackTypeEnum)) {
            switch (trackTypeEnum) {
                case ORDER:
                    orderTrackRequest.setTrackSourceList(Lists.newArrayList(TrackSource.CHANNEL.getType(),
                            TrackSource.SELF_PICK_SELECT.getType(), TrackSource.PRINT.getType(),
                            TrackSource.ORDER_HANDLE.getType(), TrackSource.QNH_OPEN.getType()));
                    break;
                case VERIFICATION:
                    orderTrackRequest.setTrackSourceList(Lists.newArrayList(TrackSource.VERIFICATION.getType()));
                    break;
                case DELIVERY:
                    orderTrackRequest.setTrackSourceList(Lists.newArrayList(TrackSource.DELIVERY.getType()));
                    break;
                default:
                    break;
            }
        }

        OrderTrackResponse response = bizOrderThriftService.queryOrderTrack(orderTrackRequest);

        if (response.getStatus().getCode() != StatusCodeEnum.SUCCESS.getCode()) {
            log.info("查询轨迹报错");
            throw new CommonRuntimeException("查询轨迹报错");
        }

        return convertResponseToTrackVo(response, tenantId, ChannelOrderConvertUtils.convertBizType(request.getChannelId()));

    }

    @Degrade(rhinoKey = "OrderFuseService.refundReason",
            fallBackMethod = "checkRefundFallback",
            timeoutInMilliseconds = 5000)
    @Override
    @MethodLog(logRequest = true, logResponse = true)
    @CatTransaction
    public CommonResponse<CheckRefundResponse> checkRefund(CheckRefundRequest request) {

        try {
            //校验是否可退
            OCMSRefundCheckResponse checkResponse = ocmsOrderOperateThriftService.refundCheck(OCMSCheckRefundRequest.builder()
                    .orderBizType(ChannelOrderConvertUtils.convertBizType(request.getChannelId()))
                    .refundType(request.getRefundType())
                    .tenantId(ContextHolder.currentUserTenantId())
                    .viewOrderId(request.getChannelOrderId())
                    .refundOperationType(request.getRefundOperationType())
                    .afterSaleId(request.getAfterSaleId())
                    .build());
            if (success(checkResponse.getStatus())) {
                CheckRefundResponse response = new CheckRefundResponse();
                response.setRefundReasons(checkResponse.getPossibleRefundReasons().stream()
                        .map(this::convertRefundReason)
                        .collect(Collectors.toList()));
                OrderDetailVO orderDetail = orderMngService.getOrderDetail(buildOcmsOrderDetailReq(request)).getOrderDetail();
                // 美团名酒馆订单主动发起退款时，进行错误提示
                if (BooleanUtils.isTrue(orderDetail.getIsMtFamousTavern())) {
                    // 退款退货获取原因列表和主动发起退款的提示不同
                    ErrorCodeEnum errorCodeEnum = Objects.equals(request.getRefundType(), 4)
                            || Objects.equals(request.getRefundType(), 5)
                                    ? ErrorCodeEnum.REFUND_AUDIT_MT_FAMOUS_TAVERN_PROMPT
                                    : ErrorCodeEnum.REFUND_CHECK_MT_FAMOUS_TAVERN_PROMPT;
                    Pair<Boolean, Pair<Integer, String>> resultPair = ocmsChannelRemoteService
                            .refundCheckMtFamousTavern(orderDetail.getChannelOrderId(),
                                    ContextHolder.currentUserTenantId(), errorCodeEnum,
                                    ErrorCodeEnum.WEB_MT_FAMOUS_TAVERN_GATHER_PHONE_NOT_FIND);
                    return CommonResponse.fail(resultPair.getValue().getKey(), resultPair.getValue().getValue());
                }
                // 过滤线下赠品商品 和过滤换货商品
                if (Objects.nonNull(orderDetail)
                        && CollectionUtils.isNotEmpty(orderDetail.getProductList())) {
                    List<ProductVOForRefund> filteredProducts = orderDetail.getProductList().stream()
                            .filter(product -> !Objects.equals(1, product.getGiftType()) && !Boolean.TRUE.equals(product.getIsExchangeProduct()))
                            .collect(Collectors.toList());
                    // 抖音同时过滤线上赠品
                    if(Objects.equals(DynamicChannelType.DOU_YIN.getChannelId(), request.getChannelId())){
                        filteredProducts = filteredProducts.stream()
                                .filter(product -> !Objects.equals(0, product.getGiftType()))
                                .collect(Collectors.toList());
                    }
                    orderDetail.setProductList(filteredProducts);
                }
                if (Objects.equals(DynamicChannelType.TAO_XIAN_DA.getChannelId(), request.getChannelId()) && isBeforePickCompleted(orderDetail.getPickupStatus())) {
                    return getCanRefundCountForDraft(orderDetail, response, request);
                }
                //美团渠道部分退时从渠道接口获取商品的可退数量
                if (supportOnlineRefundGoodsQuery(request.getChannelId(), request.getRefundType())){
                    return queryOnlinePartRefundGoodsAndSet(request, orderDetail, response);
                }else {
                    //部分退过滤可退数量为0的
                    if(Objects.equals(request.getRefundType(), 2)){
                        orderDetail.setProductList(orderDetail.getProductList().stream()
                                .filter(product -> product.getCanRefundCount() > 0).collect(Collectors.toList()));
                    }
                    response.setOrderInfo(orderDetail);
                }
                return CommonResponse.success(response);
            }
            return CommonResponse.fail(checkResponse.getStatus().getCode(), checkResponse.getStatus().getMessage());
        } catch (TException e) {
            log.error("OCMSOrderServiceWrapper.checkRefund  调用channelOrderTenantThriftService.checkRefund error", e);
            throw new CommonRuntimeException(e);
        }
    }

    /**
     * 是否支持渠道接口获取退款信息
     *
     * @param channelId
     * @param refundType
     * @return
     */
    private Boolean supportOnlineRefundGoodsQuery(Integer channelId, Integer refundType) {
        if (Objects.equals(DynamicChannelType.MEITUAN.getChannelId(), channelId) && Objects.equals(refundType, com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.RefundTypeEnum.PART.getValue())) {
            return true;
        }
        if (Objects.equals(DynamicChannelType.TAO_XIAN_DA.getChannelId(), channelId)){
            return true;
        }
        return false;
    }

    private boolean isBeforePickCompleted(Integer pickupStatus) {
        if (pickupStatus != null) {
            if (DeliveryStatusEnum.WAIT_TO_CONFIRM.getValue() != pickupStatus
                    && DeliveryStatusEnum.WAIT_TO_PICK.getValue() != pickupStatus
                    && DeliveryStatusEnum.PICKING.getValue() != pickupStatus) {
                return false;
            }
        }
        return true;
    }

    /**
     * 参考app端查询渠道可退数量并匹配商品
     * com.sankuai.shangou.qnh.orderapi.service.app.OrderService#partRefundCheck(com.sankuai.shangou.qnh.orderapi.domain.request.app.PartRefundCheckRequest)
     *
     * @param request
     * @param orderDetail
     * @param response
     * @return
     */
    private CommonResponse<CheckRefundResponse> queryOnlinePartRefundGoodsAndSet(CheckRefundRequest request, OrderDetailVO orderDetail, CheckRefundResponse response) {
        List<PartRefundGoodsModel> partRefundGoodsModels = onlineQueryPartRefundGoods(request);
        if (CollectionUtils.isEmpty(partRefundGoodsModels)) {
            throw new BizException("调用渠道接口获取可退商品失败");
        }
        if (Objects.equals(DynamicChannelType.MEITUAN.getChannelId(), request.getChannelId())) {
            Map<Long, List<PartRefundGoodsModel>> onlineMap = partRefundGoodsModels.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.groupingBy(PartRefundGoodsModel::getOutItemId));
            List<ProductVOForRefund> productList = orderDetail.getProductList();
            //美团渠道extData字段为outItemId
            for (ProductVOForRefund productVOForRefund : productList) {
                Long outItemId = productVOForRefund.getOutItemId();
                if (outItemId == null) {
                    productVOForRefund.setCanRefundCount(0);
                    continue;
                }
                List<PartRefundGoodsModel> onlineGoodsList = onlineMap.get(outItemId);
                if (CollectionUtils.isEmpty(onlineGoodsList)) {
                    productVOForRefund.setCanRefundCount(0);
                    continue;
                }
                //美团渠道接口可能返回同itemId多行商品
                Integer canRefundCount = onlineGoodsList.stream().map(PartRefundGoodsModel::getCanRefundCount).reduce(0, Integer::sum);
                productVOForRefund.setCanRefundCount(canRefundCount);
                productVOForRefund.setUnitPrice(Integer.valueOf(onlineGoodsList.get(0).getRefundPrice()));
            }
            productList = productList.stream().filter(Objects::nonNull).filter(v -> v.getCanRefundCount() > 0).collect(Collectors.toList());
            orderDetail.setProductList(productList);

        } else if (Objects.equals(DynamicChannelType.TAO_XIAN_DA.getChannelId(), request.getChannelId())) {
            Map<Long, PartRefundGoodsModel> onlineMap = partRefundGoodsModels.stream().filter(Objects::nonNull)
                    .filter(i -> Objects.nonNull(i.getOrderItemId())).collect(Collectors.toMap(PartRefundGoodsModel::getOrderItemId, v -> v));
            List<ProductVOForRefund> productList = orderDetail.getProductList();
            for (ProductVOForRefund productVOForRefund : productList) {
                PartRefundGoodsModel onlineGoods = onlineMap.get(productVOForRefund.getOrderItemId());
                if (onlineGoods == null) {
                    productVOForRefund.setCanRefundCount(0);
                    //为空就认为退完了
                    productVOForRefund.setRefundCount(productVOForRefund.getCount());
                    continue;
                }
                productVOForRefund.setCanRefundCount(onlineGoods.getCanRefundCount());
                //防止扣出负数及npe
                productVOForRefund.setRefundCount(Math.max(0, (productVOForRefund.getCount() - (onlineGoods.getCanRefundCount() == null ? 0 : onlineGoods.getCanRefundCount()))));
                productVOForRefund.setUnitPrice(Integer.valueOf(onlineGoods.getRefundPrice()));
            }
            List<ProductVOForRefund> partRefundList = productList.stream().filter(Objects::nonNull).filter(v -> v.getCanRefundCount() > 0).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(partRefundList) && Objects.equals(request.getRefundType(), com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.RefundTypeEnum.PART.getValue())) {
                throw new BizException("无可部分退商品");
            }
            orderDetail.setProductList(productList);
        }
        response.setOrderInfo(orderDetail);
        return CommonResponse.success(response);
    }

    /**
     * 拣货完成前获取暂存单已退掉的商品数量计算可退数量
     * @param orderDetail
     * @param response
     * @param request
     * @return
     */
    private CommonResponse<CheckRefundResponse> getCanRefundCountForDraft(OrderDetailVO orderDetail, CheckRefundResponse response, CheckRefundRequest request) {
        OrderDetail order = ocmsOrderRemoteService.getOrderDetail(orderDetail.getChannelOrderId()
                , orderDetail.getChannelId(), ContextHolder.currentUserTenantId());
        OrderFinanceModel orderFinanceModel = queryOrderFinance(order);
        if (orderFinanceModel == null) {
            throw new BizException("未查询到订单财务信息");
        }
        Map<Long, OrderFinanceDetailModel> financeDetailModelMap = orderFinanceModel.getOrderItemCreateModelList().stream()
                .collect(Collectors.toMap(OrderFinanceDetailModel::getOrderItemId, java.util.function.Function.identity(), (o1, o2) -> o1));
        Map<Long, BigDecimal> draftRefundedCount = getDraftRefundedCount(order);
        List<ProductVOForRefund> productList = orderDetail.getProductList();
        for (ProductVOForRefund p : productList) {
            int refundedCount = (int) Math.floor(draftRefundedCount.getOrDefault(p.getOrderItemId(), BigDecimal.ZERO).doubleValue());
            int canRefundCount = p.getCount() - refundedCount;
            p.setCanRefundCount(Math.max(canRefundCount, 0));
            p.setRefundCount(refundedCount);
            p.setUnitPrice(Optional.ofNullable(financeDetailModelMap.getOrDefault(p.getOrderItemId(), new OrderFinanceDetailModel())
                    .getBillPrice()).orElse(0));
        }
        List<ProductVOForRefund> partRefundList = productList.stream().filter(Objects::nonNull).filter(v -> v.getCanRefundCount() > 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(partRefundList) && Objects.equals(request.getRefundType(), com.sankuai.meituan.shangou.empower.ocms.channel.thrift.constants.RefundTypeEnum.PART.getValue())) {
            throw new BizException("无可部分退商品");
        }
        orderDetail.setProductList(productList);
        response.setOrderInfo(orderDetail);
        return CommonResponse.success(response);
    }

    private OrderFinanceModel queryOrderFinance(OrderDetail orderDetail) {
        OrderFinanceQueryRequest financeQueryRequest = OrderFinanceQueryRequest.builder()
                .ids(Lists.newArrayList(orderDetail.getOrderBase().getOrderId()))
                .includeDetail(true)
                .type(1)
                .shopId(orderDetail.getOrderBase().getShopId())
                .tenantId(orderDetail.getOrderBase().getTenantId()).build();
        try {
            log.info("查询订单参悟数据,request:{}", financeQueryRequest);
            OrderFinanceQueryResponse response = orderFinanceThriftService.queryOrderFinanceInfo(financeQueryRequest);
            log.info("查询订单财务数据,Response:{}", response);
            if (response == null || response.getStatus() == null || response.getStatus().getCode() != StatusCodeEnum.SUCCESS.getCode()) {
                return null;
            }
            return response.getOrderFinanceModelList().stream().findFirst().orElse(null);
        }catch (Exception e) {
            log.error("查询订单财务数据异常", e);
            return null;
        }
    }
    /**
     * 计算已经暂存的退单的商品退款数量
     * @param order
     * @return
     */
    private Map<Long, BigDecimal> getDraftRefundedCount(OrderDetail order) {
        Map<Long, BigDecimal> draftRefundedCount = new HashMap<>();
        if (CollectionUtils.isEmpty(order.getAfterSaleApplyList())) {
            return draftRefundedCount;
        }
        for (AfterSaleApply afterSaleApply : order.getAfterSaleApplyList()) {
            if (AfterSalePatternEnum.AMOUNT.getValue() == afterSaleApply.getAfsPattern()
                    || AfterSalePatternEnum.WEIGHT.getValue() == afterSaleApply.getAfsPattern()) {
                for (AfterSaleApplyDetail afsDetail : afterSaleApply.getAfterSaleApplyDetailList()) {
                    BigDecimal refundedCount = draftRefundedCount.getOrDefault(afsDetail.getOrderItemId(), BigDecimal.ZERO);
                    draftRefundedCount.put(afsDetail.getOrderItemId(), refundedCount.add(BigDecimal.valueOf(afsDetail.getPartialRefundCount())));
                }
            } else {
                for (AfterSaleApplyDetail afsDetail : afterSaleApply.getAfterSaleApplyDetailList()) {
                    BigDecimal refundedCount = draftRefundedCount.getOrDefault(afsDetail.getOrderItemId(), BigDecimal.ZERO);
                    draftRefundedCount.put(afsDetail.getOrderItemId(), refundedCount.add(BigDecimal.valueOf(afsDetail.getCount())));
                }
            }
        }
        return draftRefundedCount;
    }

    private List<PartRefundGoodsModel> onlineQueryPartRefundGoods(CheckRefundRequest request) {
        QueryPartRefundGoodsRequest queryPartRefundGoodsRequest = new QueryPartRefundGoodsRequest();
        queryPartRefundGoodsRequest.setTenantId(ContextHolder.currentUserTenantId());
        queryPartRefundGoodsRequest.setViewOrderId(request.getChannelOrderId());
        queryPartRefundGoodsRequest.setOrderBizType(ChannelOrderConvertUtils.convertBizType(request.getChannelId()));
        List<PartRefundGoodsModel> resultList = Lists.newArrayList();
        try {
            log.info("OrderService.onlineQueryPartRefundGoods   request:{}", request);
            QueryOnlinePartRefundGoodsResponse response = ocmsOrderThriftService.queryPartRefundGoods(queryPartRefundGoodsRequest);
            log.info("OrderService.onlineQueryPartRefundGoods   response:{}", response);
            if (response == null) {
                log.error("查询渠道线上可部分退商品为空");
            }
            if (StatusCodeEnum.SUCCESS.getCode() == response.getStatus().getCode()) {
                resultList = response.getPartRefundGoodsModelList();
            }
        } catch (TException e) {
            log.error("OrderService.onlineQueryPartRefundGoods  ocmsOrderThriftService.queryPartRefundGoods error", e);
            return resultList;
        }
        return resultList;
    }


    @Override
    @MethodLog(logRequest = true, logResponse = true)
    @CatTransaction
    public CommonResponse refundAudit(RefundApplyAuditRequest request) {

        if (Objects.equals(request.getChannelId(), DynamicOrderBizType.TAO_XIAN_DA.getChannelId())) {
            boolean canAudit = preCheckForTxd(request);
            if (!canAudit) {
                return CommonResponse.fail(ResultCode.FAIL, getTxdRefundAuditErrorMessage());
            }
        } else if (Objects.equals(request.getChannelId(), DynamicOrderBizType.MEITUAN_WAIMAI.getChannelId())) {
            // 美团外卖的订单非退货退款审核时，如果为美团名酒馆订单，则进行错误提示
            Pair<Boolean, Pair<Integer, String>> resultPair = ocmsChannelRemoteService.refundCheckMtFamousTavern(
                    request.getChannelOrderId(), ContextHolder.currentUserTenantId(),
                    ErrorCodeEnum.REFUND_AUDIT_MT_FAMOUS_TAVERN_PROMPT, ErrorCodeEnum.WEB_MT_FAMOUS_TAVERN_GATHER_PHONE_NOT_FIND);
            if (resultPair.getKey()) {
                return CommonResponse.fail(resultPair.getValue().getKey(), resultPair.getValue().getValue());
            }
        }
        //处理缺货商品,异步处理不影响原流程
        dealOffShelfGoods(request);

        RefundTypeEnum refundTypeEnum = RefundTypeEnum.enumOf(request.getRefundType());
        switch (refundTypeEnum) {
            case REFUND_GOODS:
            case PART_REFUND_GOODS:
            case ALL_REFUND_GOODS:
            case AMOUNT_REFUND_GOODS:
                return refundGoodsAudit(request);
            case REJECT_BY_CUSTOMER:
                return tenantReceiveRefundProducts(request);
            default:
                break;
        }
        if (!OrderConfiguration.hitOcmsMigration(0l)) {
            return refundAuditThroughOcms(request);
        }

        String orderId = request.getChannelOrderId();
        Long tenantId = ContextHolder.currentUserTenantId();
        Long operatorId = ContextHolder.currentUserStaffId();
        String operator = tenantRemoteService.getEmployeeName(tenantId, operatorId);
        String afterSaleId = String.valueOf(request.getAfterSaleId());
        int channelId = request.getChannelId();

        try {
            if (request.getAuditResult() == AGREE_REFUND) {
                OCMSTenantAgreeRefundRequest agreeRefundRequest = buildBizAgreeRefundRequest(orderId, tenantId, operatorId, operator, "OK", channelId, afterSaleId, request.getIsReturnGoods());
                return checkRpcResponse(ocmsOrderOperateThriftService.agreeRefundByTenant(agreeRefundRequest));
            } else if (request.getAuditResult() == REJECT_REFUND) {
                OCMSTenantRejectRefundRequest rejectRefundRequest = buildBizRejectRefundRequest(orderId, tenantId, operatorId, operator, request.getAuditReason(), request.getAuditReasonCode(), channelId, afterSaleId, request.getEvidenceList());
                return checkRpcResponse(ocmsOrderOperateThriftService.rejectRefundByTenant(rejectRefundRequest));
            }
        } catch (Exception e) {
            log.error("OCMSOrderServiceWrapper.refundAudit  openPickThriftService.refundAudit error", e);
            throw new CommonRuntimeException(e);
        }

        return CommonResponse.success(null);
    }

    // 检查订单是否完成了，完成前的订单，无需商家审核
    private boolean preCheckForTxd(RefundApplyAuditRequest request) {

        try {
            String poiId = request.getPoiId();
            Long storeId = null;
            if (StringUtils.isNotBlank(poiId)) {
                storeId = Long.parseLong(poiId);
            }
            ChannelOrderDetailDTO channelOrderDetailDTO =
                    ocmsChannelRemoteService.queryOnlineOrderDetail(request.getChannelOrderId(),
                            DynamicChannelType.TAO_XIAN_DA, ContextHolder.currentUserTenantId(), storeId);

            int channelOrderStatus = channelOrderDetailDTO.getChannelOrderStatus();
            return MccConfigUtil.needAuditRefund(channelOrderStatus);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void dealOffShelfGoods(RefundApplyAuditRequest request) {
        try {
            if (CollectionUtils.isEmpty(request.getRefundGoodsTakenOffVOList())) {
                return;
            }
            Long tenantId = ContextHolder.currentUserTenantId();
            Integer appId = ContextHolder.currentUserLoginAppId();
            Long uid = ContextHolder.currentUid();
            String operatorName = ContextHolder.currentUserName();

            BizOrderOffShelfGoodsRequest offShelfGoodsRequest = new BizOrderOffShelfGoodsRequest();

            offShelfGoodsRequest.setAppId(appId);
            offShelfGoodsRequest.setOperatorName(operatorName);
            offShelfGoodsRequest.setViewOrderId(request.getChannelOrderId());
            offShelfGoodsRequest.setTenantId(tenantId);
            offShelfGoodsRequest.setShopId(Long.valueOf(request.getPoiId()));
            offShelfGoodsRequest.setUid(uid);
            offShelfGoodsRequest.setOrderBizType(DynamicOrderBizType.channelId2OrderBizTypeValue(request.getChannelId()));
            offShelfGoodsRequest.setTrackOpType(TrackOpType.ORDER_REFUND_AUDIT_SOLD_OUT_OFF_SHELF);
            offShelfGoodsRequest.setOffShelfGoodsList(request.getRefundGoodsTakenOffVOList().stream().map(item ->
                            new BizOrderOffShelfGoodsModel(item.getSkuId(), item.getCustomSkuId(), item.getSkuName()))
                    .collect(Collectors.toList()));

            fuseOrderExecutor.submit(() -> {
                bizOrderThriftService.offShelfOrderGoods(offShelfGoodsRequest);
            });
        } catch (Exception e) {
            log.info("执行商品下架操作异常", e);
        }
    }

    private PageInfoVO buildPageInfoVO(Integer page, Integer size, Integer totalSize) {
        PageInfoVO pageInfoVO = new PageInfoVO();
        pageInfoVO.setPage(page);
        pageInfoVO.setSize(size);
        pageInfoVO.setTotalSize(totalSize);
        pageInfoVO.setTotalPage(totalSize % size == 0 ? totalSize / size : totalSize / size + 1);
        return pageInfoVO;
    }

    private CommonResponse checkRpcResponse(int code, String message) {
        if (code == ResultCode.SUCCESS.getCode()) {
            return CommonResponse.success(null);
        } else {
            return CommonResponse.fail(code, message);
        }
    }

    private CommonResponse checkRpcResponse(com.meituan.shangou.saas.o2o.dto.response.CommonResponse agreeRefundByTenant) {
        return checkRpcResponse(agreeRefundByTenant.getStatus().getCode(), agreeRefundByTenant.getStatus().getMessage());
    }

    private OCMSTenantAgreeRefundRequest buildBizAgreeRefundRequest(String orderId, Long tenantId, Long operatorId, String operator, String reason, int channelId, String afterSaleId, Boolean isReturnGoods) {
        OCMSTenantAgreeRefundRequest req = new OCMSTenantAgreeRefundRequest();
        Integer orderBizType = ChannelOrderConvertUtils.convertBizType(channelId);

        req.setViewOrderId(orderId);
        req.setTenantId(tenantId);
        req.setReason("OK");
        req.setOrderBizType(orderBizType);
        req.setOperatorUserName(operator);
        req.setOperatorUserId(operatorId);
        req.setAfterSaleId(afterSaleId);
        req.setAppId(String.valueOf(ContextHolder.currentUserLoginAppId()));
        req.setOperatorAccountId(ContextHolder.currentUid());
        req.setIsReturnGoods(isReturnGoods);
        return req;
    }


    private OCMSTenantRejectRefundRequest buildBizRejectRefundRequest(String orderId, Long tenantId, Long operatorId, String operator, String reason, Integer reasonCode, int channelId, String afterSaleId, List<EvidenceDto> evidenceList) {
        OCMSTenantRejectRefundRequest req = new OCMSTenantRejectRefundRequest();
        Integer orderBizType = ChannelOrderConvertUtils.convertBizType(channelId);
        req.setViewOrderId(orderId);
        req.setTenantId(tenantId);
        req.setReason(reason);
        req.setOrderBizType(orderBizType);
        req.setOperatorUserName(operator);
        req.setOperatorUserId(operatorId);
        req.setAfterSaleId(afterSaleId);
        req.setAppId(String.valueOf(ContextHolder.currentUserLoginAppId()));
        req.setOperatorAccountId(ContextHolder.currentUid());
        req.setReasonCode(reasonCode != null ? String.valueOf(reasonCode) : null);
        if (CollectionUtils.isNotEmpty(evidenceList)) {
            req.setEvidenceList(evidenceList.stream().map(item -> {
                EvidenceModel model = new EvidenceModel();
                model.setType(item.getType());
                model.setUrl(item.getUrl());
                model.setDesc(item.getDesc());
                return model;
            }).collect(Collectors.toList()));
        }
        return req;
    }


    private CommonResponse refundAuditThroughOcms(RefundApplyAuditRequest request) {
        String orderId = request.getChannelOrderId();
        Long tenantId = ContextHolder.currentUserTenantId();
        Long operatorId = ContextHolder.currentUserStaffId();
        String operator = tenantRemoteService.getEmployeeName(tenantId, operatorId);
        String afterSaleId = String.valueOf(request.getAfterSaleId());
        int channelId = request.getChannelId();
        try {
            if (request.getAuditResult() == AGREE_REFUND) {
                AgreeRefundReq request1 = buildAgreeRefundRequest(orderId, tenantId, operatorId, operator, "OK", channelId, afterSaleId);
                AgreeRefundResp resp = channelOrderService.agreeRefund(request1);
                return checkRpcResponse(resp.getStatus().getCode(), resp.getStatus().getMsg());
            } else if (request.getAuditResult() == REJECT_REFUND) {
                RejectRefundReq request2 = buildRejectRefundRequest(orderId, tenantId, operatorId, operator, request.getAuditReason(), channelId, afterSaleId);
                RejectRefundResp resp = channelOrderService.rejectRefund(request2);
                return checkRpcResponse(resp.getStatus().getCode(), resp.getStatus().getMsg());
            }
        } catch (TException e) {
            log.error("OCMSOrderServiceWrapper.refundAudit  openPickThriftService.refundAudit error", e);
            throw new CommonRuntimeException(e);
        }
        return CommonResponse.success(null);
    }

    private RejectRefundReq buildRejectRefundRequest(String orderId, Long tenantId, Long operatorId, String operator, String reason, int channelId, String afterSaleId) throws TException {
        RejectRefundReq req = new RejectRefundReq();
        req.setChannelType(channelId);
        req.setChannelOrderId(orderId);
        req.setOptUserId(operatorId);
        req.setOptUserName(operator);
        req.setTenantId(tenantId);
        req.setReason(reason);
        req.setAfterSaleId(afterSaleId);
        return req;
    }

    private AgreeRefundReq buildAgreeRefundRequest(String orderId, Long tenantId, Long operatorId, String operator, String reason, int channelId, String afterSaleId) throws TException {
        AgreeRefundReq req = new AgreeRefundReq();
        req.setChannelType(channelId);
        req.setChannelOrderId(orderId);
        req.setOptUserId(operatorId);
        req.setOptUserName(operator);
        req.setTenantId(tenantId);
        req.setReason(reason);
        req.setAfterSaleId(afterSaleId);
        return req;
    }

    public CommonResponse refundGoodsAudit(RefundApplyAuditRequest request) {
        Long tenantId = ContextHolder.currentUserTenantId();
        Long operatorId = ContextHolder.currentUserStaffId();
        String operator = tenantRemoteService.getEmployeeName(tenantId, operatorId);
        try {
            OCMSTenantRefundGoodsRequest refundGoodsRequest = buildBizRefundGoodsRequest(request, tenantId, operatorId, operator);
            return checkRpcResponse(ocmsOrderOperateThriftService.refundGoodsByTenant(refundGoodsRequest));
        } catch (Exception e) {
            log.error("OCMSOrderServiceWrapper.refundGoodsAudit  openPickThriftService.refundAudit error", e);
            throw new CommonRuntimeException(e);
        }
    }

    private OCMSTenantRefundGoodsRequest buildBizRefundGoodsRequest(RefundApplyAuditRequest request, Long tenantId, Long operatorId, String operator) {
        Integer reviewType = getReviewType(request.getAuditResult(), request.getAuditStage());
        int channelId = request.getChannelId();
        OCMSTenantRefundGoodsRequest req = new OCMSTenantRefundGoodsRequest();
        Integer orderBizType = ChannelOrderConvertUtils.convertBizType(channelId);
        req.setViewOrderId(request.getChannelOrderId());
        req.setTenantId(tenantId);
        req.setReason(request.getAuditReason());
        req.setOrderBizType(orderBizType);
        req.setOperatorUserName(operator);
        req.setOperatorUserId(operatorId);
        req.setAfterSaleId(String.valueOf(request.getAfterSaleId()));
        req.setReviewType(reviewType);
        req.setReasonCode(request.getAuditReasonCode());
        req.setAuditStage(request.getAuditStage());
        req.setAppId(String.valueOf(ContextHolder.currentUserLoginAppId()));
        req.setOperatorAccountId(ContextHolder.currentUid());
        //是否返货
        req.setIsReturnGoods(Boolean.TRUE.equals(request.getIsReturnGoods()) ? IsReturnGoodsTypeEnum.RETURN_GOODS.getValue() : IsReturnGoodsTypeEnum.NON_RETURN_GOODS.getValue());
        if (CollectionUtils.isNotEmpty(request.getEvidenceList())) {
            req.setEvidenceList(request.getEvidenceList().stream().map(item -> {
                EvidenceModel model = new EvidenceModel();
                model.setType(item.getType());
                model.setUrl(item.getUrl());
                model.setDesc(item.getDesc());
                return model;
            }).collect(Collectors.toList()));
        }
        req.setOnlyRefundWithoutReturnGoods(request.getOnlyRefundWithoutReturnGoods());
        return req;
    }

    private Integer getReviewType(Integer auditResult, Integer auditStage) {
        if (auditResult == AGREE_REFUND && auditStage == AfsAuditStageEnum.FINAL_AUDIT.getValue()) {
            return AfsReviewTypeEnum.AGREE_REFUND.getValue();
        } else if (auditResult == AGREE_REFUND && auditStage == AfsAuditStageEnum.FIRST_AUDIT.getValue()) {
            return AfsReviewTypeEnum.AGREE_REFUND_GOODS.getValue();
        }
        return AfsReviewTypeEnum.REJECT_REFUND.getValue();
    }

    public CommonResponse tenantReceiveRefundProducts(RefundApplyAuditRequest request) {
        Long tenantId = ContextHolder.currentUserTenantId();
        Long operatorId = ContextHolder.currentUserStaffId();
        String operator = tenantRemoteService.getEmployeeName(tenantId, operatorId);
        try {
            OCMSTenantReceiveProductsRequest receiveProductsRequest = buildBizReceiveProductsRequest(request, tenantId, operatorId, operator);
            return checkRpcResponse(ocmsOrderOperateThriftService.tenantReceiveRefundProducts(receiveProductsRequest));
        } catch (Exception e) {
            log.error("OCMSOrderServiceWrapper.tenantReceiveRefundProducts error", e);
            throw new CommonRuntimeException(e);
        }
    }

    private OCMSTenantReceiveProductsRequest buildBizReceiveProductsRequest(RefundApplyAuditRequest request, Long tenantId, Long operatorId, String operator) {
        Integer reviewType = getReviewType(request.getAuditResult(), request.getAuditStage());
        int channelId = request.getChannelId();
        OCMSTenantReceiveProductsRequest req = new OCMSTenantReceiveProductsRequest();
        Integer orderBizType = ChannelOrderConvertUtils.convertBizType(channelId);
        req.setViewOrderId(request.getChannelOrderId());
        req.setTenantId(tenantId);
        req.setReason(request.getAuditReason());
        req.setOrderBizType(orderBizType);
        req.setOperatorUserName(operator);
        req.setOperatorUserId(operatorId);
        req.setAfterSaleId(String.valueOf(request.getAfterSaleId()));
        req.setReviewType(reviewType);
        req.setReasonCode(request.getAuditReasonCode());
        req.setAuditStage(request.getAuditStage());
        req.setAppId(String.valueOf(ContextHolder.currentUserLoginAppId()));
        req.setOperatorAccountId(ContextHolder.currentUid());
        return req;
    }

    private OrderDetailResponse queryOrderDetail(Integer channelId, String channelOrderId) {
        OcmsOrderDetailReq orderDetailReq = new OcmsOrderDetailReq();
        orderDetailReq.setTenantId(ContextHolder.currentUserTenantId());
        orderDetailReq.setOperator(ContextHolder.currentUserStaffId());
        orderDetailReq.setChannelId(channelId);
        orderDetailReq.setChannelOrderId(channelOrderId);
        try {
            return orderMngService.getOrderDetail(orderDetailReq);
        } catch (Exception e) {
            log.error("getOrderDetail error orderMngFacade.getOrderDetail error", e);
            throw new CommonRuntimeException(e);
        }
    }


    private OcmsOrderDetailReq buildOcmsOrderDetailReq(CheckRefundRequest checkRefundRequest) {
        OcmsOrderDetailReq req = new OcmsOrderDetailReq();
        req.setTenantId(ContextHolder.currentUserTenantId());
        req.setOperator(ContextHolder.currentUserStaffId());
        req.setChannelOrderId(checkRefundRequest.getChannelOrderId());
        req.setChannelId(checkRefundRequest.getChannelId());
        return req;
    }

    private RefundReasonAndCodeVO convertRefundReason(OCMSRefundReasonAndCodeModel model) {
        RefundReasonAndCodeVO vo = new RefundReasonAndCodeVO();
        vo.setCode(model.getCode());
        vo.setReason(model.getReason());
        vo.setEvidenceNeed(model.getEvidenceNeed());
        vo.setEvidenceDescription(model.getEvidenceDescription());
        return vo;
    }

    private boolean success(Status status) {
        return Objects.equals(ResultCode.SUCCESS.getCode(), status.getCode());
    }

    public CommonResponse<CheckRefundResponse> checkRefundFallback(CheckRefundRequest request) {
        log.info("OrderFuseService.checkRefund  调用降级方法 request:{}", request);
        throw new CommonRuntimeException("OrderFuseService.checkRefund  调用降级方法");
    }

    private OrderTrackVO convertResponseToTrackVo(OrderTrackResponse response, Long tenantId, Integer orderBizType) {
        OrderTrackVO orderTrackVO = new OrderTrackVO();
        if (CollectionUtils.isEmpty(response.getOrderTrackDetailResponseList())) {
            return orderTrackVO;
        }
        orderTrackVO.setPoiId(response.getPoiId());
        orderTrackVO.setDispatchShopId(response.getDispatchShopId());

        //获取所有的操作人,ID，和姓名
        final Map<Long, UserBo> operatorNameMap = queryOperatorName(response.getOrderTrackDetailResponseList(), tenantId);

        final List<Integer> filterOpType = Lists.newArrayList(TrackOpType.RECEIVE_TASK.getOpType(), TrackOpType.PICK_TASK.getOpType(), TrackOpType.RECEIVE_TASK_NOT_FULFILL.getOpType(), TrackOpType.PICK_TASK_NOT_FULFILL.getOpType());

        final Map<String, String> appIdDesc = getAppIdDesc();

        final List<OrderTrackDetailVO> orderTrackDetailVOS = response.getOrderTrackDetailResponseList().stream().filter(item -> ObjectUtils.allNotNull(item.getTrackSource(), item.getTrackOpType()) && !filterOpType.contains(item.getTrackOpType())).map(item -> {
            try {
                final TrackSource trackSource = TrackSource.codeOf(item.getTrackSource());
                final TrackOpType trackOpType = TrackOpType.codeOf(item.getTrackOpType());
                if (TrackSource.UN_KNOWN.equals(trackSource) || TrackOpType.UN_KNOWN.equals(trackOpType)) {
                    log.info("数据不完整,item:{}", item);
                    return convertUnCategoryTrack(item, operatorNameMap, appIdDesc);

                }

                switch (trackSource) {
                    case CHANNEL:
                        return convertChannelTrack(item, orderBizType);
                    case SELF_PICK_SELECT:
                        return convertPickTrack(item, operatorNameMap, appIdDesc);
                    case DELIVERY:
                        return convertDeliveryTrack(item, operatorNameMap, appIdDesc,orderBizType);
                    case PRINT:
                        return convertPrintTrack(item, operatorNameMap, appIdDesc);
                    case ORDER_HANDLE:
                        return convertOrderHandleTrack(item, operatorNameMap, appIdDesc);
                    case QNH_OPEN:
                        return convertQnhOpenTrack(item, operatorNameMap, appIdDesc);
                    case VERIFICATION:
                        return convertOrderPosTrack(item);
                    default:
                        Cat.logEvent("CONVERT_ORDER_TRACK", "ERROR");
                        return null;
                }
            } catch (Exception e) {
                log.error("CONVERT_ORDER_TRACK error", e);
                Cat.logEvent("CONVERT_ORDER_TRACK", "ERROR");
                return null;
            }
        }).filter(Objects::nonNull).sorted(Comparator.comparingLong(item -> Optional.ofNullable(item.getOperationTime()).orElse(0L))).collect(Collectors.toList());
        orderTrackVO.setOrderOperatorLogList(orderTrackDetailVOS);
        return orderTrackVO;

    }

    private OrderTrackDetailVO convertUnCategoryTrack(OrderTrackDetailResponse item, Map<Long, UserBo> operatorNameMap, Map<String, String> appIdDesc) {
        if (StringUtils.isBlank(item.getExtData())){
            return null;
        }
        JSONObject extData;
        try{
            extData = JSONObject.parseObject(item.getExtData());
        }catch (Exception e){
            return null;
        }
        String remark = extData.getString("remark");
        if (StringUtils.isBlank(remark)){
            return null;
        }
        OrderTrackDetailVO orderTrackDetailVO = new OrderTrackDetailVO();
        orderTrackDetailVO.setOperatorCode(item.getTrackOpType());
        orderTrackDetailVO.setOperatorDesc(remark);
        if (CollectionUtils.isNotEmpty(item.getOperatorIdList())) {
            final List<String> operatorNameList = item.getOperatorIdList().stream().map(v -> operatorNameMap.getOrDefault(v, new UserBo("system","系统"))
                    .getDisplayName()).distinct().collect(Collectors.toList());
            orderTrackDetailVO.setOperatorName(String.join(",", operatorNameList));
        }else {
            orderTrackDetailVO.setOperatorName("");
        }
        orderTrackDetailVO.setOperationTime(item.getOperateTime());
        orderTrackDetailVO.setOperatorType(TrackTypeEnum.ORDER.getCode());
        return orderTrackDetailVO;
    }

    private OrderTrackDetailVO convertOrderPosTrack(OrderTrackDetailResponse item) {
        OrderTrackDetailVO orderTrackDetailVO = new OrderTrackDetailVO();
        final TrackOpType trackOpType = TrackOpType.codeOf(item.getTrackOpType());
        orderTrackDetailVO.setOperatorCode(item.getTrackOpType());
        orderTrackDetailVO.setOperatorDesc(trackOpType.getMsg());
        orderTrackDetailVO.setOperatorName("系统-system");
        orderTrackDetailVO.setOperationTime(item.getOperateTime());
        orderTrackDetailVO.setOperatorType(TrackTypeEnum.VERIFICATION.getCode());
        if (StringUtils.isNotBlank(item.getExtData())){
            JSONObject extData = JSONObject.parseObject(item.getExtData());
            orderTrackDetailVO.setComment(extData.getString("remark"));
        }
        return orderTrackDetailVO;
    }


    private Map<Long, UserBo> queryOperatorName(List<OrderTrackDetailResponse> orderTrackDetailResponseList, Long tenantId) {
        Map<Long, UserBo> resultMap = new HashMap<>();
        try {
            final List<Long> collectOperatorId = orderTrackDetailResponseList.stream().filter(item -> CollectionUtils.isNotEmpty(item.getOperatorIdList())).flatMap(item -> item.getOperatorIdList().stream()).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collectOperatorId)) {
                return resultMap;
            }
            final UserListResponse userListResponse = userThriftService.queryTenantUserByAccountIds(tenantId, collectOperatorId);
            if (userListResponse.getStatus().getCode() == StatusCodeEnum.SUCCESS.getCode() && CollectionUtils.isNotEmpty(userListResponse.getUserList())) {
                return userListResponse.getUserList().stream().collect(Collectors.toMap(UserDto::getAccountId, it -> new UserBo(it.getAccountName(), it.getEmpName()), (a, b) -> b));
            }
        } catch (Exception e) {
            log.error("queryEmployeeByIds error", e);
        }
        return resultMap;
    }

    private OrderTrackDetailVO convertChannelTrack(OrderTrackDetailResponse orderTrackDetailResponse, Integer orderBizType) {
        OrderTrackDetailVO orderTrackDetailVO = new OrderTrackDetailVO();
        final TrackOpType trackOpType = TrackOpType.codeOf(orderTrackDetailResponse.getTrackOpType());
        orderTrackDetailVO.setOperatorCode(orderTrackDetailResponse.getTrackOpType());
        orderTrackDetailVO.setOperatorDesc(trackOpType.getMsg());
        orderTrackDetailVO.setOperatorName(getChannelOperateName(orderBizType));

        //用户自主发起金额退，记录自定义操作人名称
        if (StringUtils.isNotBlank(orderTrackDetailResponse.getExtData())) {
            JSONObject extData = JSONObject.parseObject(orderTrackDetailResponse.getExtData());
            if (StringUtils.isNotBlank(extData.getString("operatorName")) && orderBizType == OrderBizTypeEnum.ELE_ME.getValue()) {
                orderTrackDetailVO.setOperatorName(extData.getString("operatorName"));
            }
            if (StringUtils.isNotBlank(extData.getString("afterSaleId"))) {
                orderTrackDetailVO.setComment("渠道退单号:" + extData.getString("afterSaleId"));
            }
            if (StringUtils.isNotBlank(extData.getString("remark"))) {
                orderTrackDetailVO.setComment(extData.getString("remark"));
            }
            if(StringUtils.isNotBlank(extData.getString("responsibleParty")) && StringUtils.isNotBlank(extData.getString("compensateType"))){
                setCompensationComment(orderTrackDetailVO, extData.getString("responsibleParty"), extData.getString("compensateType"));
            }
        }
        orderTrackDetailVO.setOperationTime(orderTrackDetailResponse.getOperateTime());
        orderTrackDetailVO.setOperatorType(TrackTypeEnum.ORDER.getCode());
        return orderTrackDetailVO;
    }

    private void setCompensationComment(OrderTrackDetailVO orderTrackDetailVO, String responsibleParty, String compensateType){
        try {
            int responsiblePartyInt = Integer.parseInt(responsibleParty);
            int compensateTypeInt = Integer.parseInt(compensateType);
            OrderCompensateTypeEnum orderCompensateTypeEnum = OrderCompensateTypeEnum.enumOf(compensateTypeInt);
            ResponsiblePartyEnum responsiblePartyEnum = ResponsiblePartyEnum.enumOf(responsiblePartyInt);
            if(orderCompensateTypeEnum == null || responsiblePartyEnum == null){
                return;
            }
            String comment = orderCompensateTypeEnum.getDesc() + "已生效，由" + responsiblePartyEnum.getDesc()+ "承担赔付。";
            orderTrackDetailVO.setComment(comment);
        }catch (Exception e){
            log.warn("setCompensationComment error", e);
        }
    }

    private void setTrackComment(OrderTrackDetailResponse orderTrackDetailResponse, OrderTrackDetailVO orderTrackDetailVO) {
        try {
            if (StringUtils.isNotEmpty(orderTrackDetailResponse.getExtData())) {
                JSONObject extData;
                extData = JSONObject.parseObject(orderTrackDetailResponse.getExtData());
                String remark = extData.getString("remark");
                if (StringUtils.isNotEmpty(remark)) {
                    orderTrackDetailVO.setComment(remark);
                }
            }
        } catch (Exception e) {
            log.error("获取轨迹备注信息失败", e);
        }
    }

    private OrderTrackDetailVO convertPickTrack(OrderTrackDetailResponse orderTrackDetailResponse, Map<Long, UserBo> operatorNameMap, Map<String, String> appIdDesc) {
        OrderTrackDetailVO orderTrackDetailVO = new OrderTrackDetailVO();
        final TrackOpType trackOpType = TrackOpType.codeOf(orderTrackDetailResponse.getTrackOpType());
        orderTrackDetailVO.setOperatorCode(orderTrackDetailResponse.getTrackOpType());

        if (CollectionUtils.isNotEmpty(orderTrackDetailResponse.getOperatorIdList())) {
            final List<String> operatorNameList = orderTrackDetailResponse.getOperatorIdList().stream().map(item -> operatorNameMap.getOrDefault(item, new UserBo("system","系统")).getDisplayName()).distinct().collect(Collectors.toList());
            orderTrackDetailVO.setOperatorName(String.join(",", operatorNameList));

            //增加操作端
            if (StringUtils.isNotEmpty(orderTrackDetailResponse.getAppId())) {
                orderTrackDetailVO.setOperatorName(orderTrackDetailVO.getOperatorName() + " " + Optional.ofNullable(appIdDesc.get(orderTrackDetailResponse.getAppId())).orElse(""));
            }
        }
        orderTrackDetailVO.setOperationTime(orderTrackDetailResponse.getOperateTime());
        orderTrackDetailVO.setOperatorType(TrackTypeEnum.ORDER.getCode());

        orderTrackDetailVO.setOperationTime(orderTrackDetailResponse.getOperateTime());
        orderTrackDetailVO.setOperatorDesc(trackOpType.getMsg());
        if (StringUtils.isNotBlank(orderTrackDetailResponse.getExtData())) {
            JSONObject jsonObject = JSONObject.parseObject(orderTrackDetailResponse.getExtData());
            orderTrackDetailVO.setComment(jsonObject.getString("remark"));

            if (StringUtils.isNotEmpty(jsonObject.getString("accountName"))) {
                orderTrackDetailVO.setOperatorDesc(orderTrackDetailVO.getOperatorDesc().concat(jsonObject.getString("accountName")));
            }
        }


        return orderTrackDetailVO;
    }

    private OrderTrackDetailVO convertDeliveryTrack(OrderTrackDetailResponse orderTrackDetailResponse,
            Map<Long, UserBo> operatorNameMap, Map<String, String> appIdDesc, Integer orderBizType) {
        final TrackOpType trackOpType = TrackOpType.codeOf(orderTrackDetailResponse.getTrackOpType());
        OrderTrackDetailVO orderTrackDetailVO = new OrderTrackDetailVO();

        String traceOperatorName = getChannelOperateName(orderBizType);
        String vehicleNameListStr = StringUtils.EMPTY;
        if (Objects.nonNull(orderTrackDetailResponse.getExtData())) {
            try {
                final DeliveryExtBO deliveryExtBO = JacksonUtils.fromJson(orderTrackDetailResponse.getExtData(), DeliveryExtBO.class);
                if(StringUtils.isNotEmpty(deliveryExtBO.getRiderName())){
                    traceOperatorName = deliveryExtBO.getRiderName();
                }
                if (StringUtils.isNotBlank(deliveryExtBO.getVehicleNameListStr())) {
                    vehicleNameListStr = deliveryExtBO.getVehicleNameListStr();
                }
            } catch (Exception e) {
                log.error("convertDeliveryTrack error", e);
            }
        }
        if (CollectionUtils.isNotEmpty(orderTrackDetailResponse.getOperatorIdList())) {
            String operatorName = orderTrackDetailResponse.getOperatorIdList()
                    .stream()
                    .filter(operatorNameMap::containsKey)
                    .map(operatorId -> operatorNameMap.getOrDefault(operatorId, UserBo.SYSTEM).getDisplayName())
                    .distinct()
                    .collect(Collectors.joining(","));
            String appName = ObjectUtils.defaultIfNull(appIdDesc.get(orderTrackDetailResponse.getAppId()), "");
            if(StringUtils.isNotEmpty(operatorName)){
                traceOperatorName = operatorName + "-" + appName;
            }
        }
        if(StringUtils.isNotEmpty(traceOperatorName)){
            orderTrackDetailVO.setOperatorName(traceOperatorName);
        }
        orderTrackDetailVO.setOperatorCode(orderTrackDetailResponse.getTrackOpType());
        orderTrackDetailVO.setOperatorDesc(trackOpType.getMsg());
        orderTrackDetailVO.setOperationTime(orderTrackDetailResponse.getOperateTime());
        orderTrackDetailVO.setOperatorType(TrackTypeEnum.DELIVERY.getCode());
        orderTrackDetailVO.setComment(vehicleNameListStr);
        return orderTrackDetailVO;
    }

    @Nullable
    private String getChannelOperateName(Integer orderBizType){
        DynamicOrderBizType dynamicOrderBizType = DynamicOrderBizType.findOf(orderBizType);
        if (dynamicOrderBizType != null && ChannelOnlineTypeEnum.isPrivate(dynamicOrderBizType.getChannelStandard())){
            return "牵牛花开放平台";
        }
        final OrderBizTypeEnum orderBizTypeEnum = OrderBizTypeEnum.enumOf(Optional.ofNullable(orderBizType).orElse(OrderBizTypeEnum.MEITUAN_WAIMAI.getValue()));
        if (orderBizTypeEnum == null){
            return null;
        }
        switch (orderBizTypeEnum) {
            case MEITUAN_WAIMAI:
                return "美团-闪购开放平台";
            case ELE_ME:
                return "饿了么-饿了么开放平台";
            case JING_DONG:
                return "京东-京东开放平台";
            case YOU_ZAN:
            case YOU_ZAN_MIDDLE:
                return "有赞-有赞开放平台";
            case DOU_YIN:
                return "抖音-抖音开放平台";
            case TAO_XIAN_DA:
                return "淘鲜达-淘宝开放平台";
            default:
                return null;
        }
    }

    private OrderTrackDetailVO convertPrintTrack(OrderTrackDetailResponse orderTrackDetailResponse, Map<Long, UserBo> operatorNameMap, Map<String, String> appIdDesc) {
        final TrackOpType trackOpType = TrackOpType.codeOf(orderTrackDetailResponse.getTrackOpType());
        if (trackOpType == TrackOpType.PRINT_DONE || trackOpType == TrackOpType.REPRINT_DONE) {
            return buildPrintDoneTrackDetail(orderTrackDetailResponse, operatorNameMap, appIdDesc, trackOpType);
        }
        return buildPrintingTrackDetail(orderTrackDetailResponse, operatorNameMap, appIdDesc, trackOpType);
    }

    /**
     * 构造打印
     */
    private OrderTrackDetailVO buildPrintingTrackDetail(OrderTrackDetailResponse orderTrackDetailResponse,
            Map<Long, UserBo> operatorNameMap, Map<String, String> appIdDesc, TrackOpType trackOpType) {
        OrderTrackDetailVO orderTrackDetailVO = new OrderTrackDetailVO();
        orderTrackDetailVO.setOperatorCode(orderTrackDetailResponse.getTrackOpType());

        final OperationScene operationScene = OperationScene.fromCode(orderTrackDetailResponse.getOperationScene());
        if (Objects.nonNull(operationScene)) {
            orderTrackDetailVO.setOperatorDesc(operationScene.getDesc() + "" + trackOpType.getMsg());
        } else {
            orderTrackDetailVO.setOperatorDesc(trackOpType.getMsg());
        }

        String operatorName = "";
        if (CollectionUtils.isNotEmpty(orderTrackDetailResponse.getOperatorIdList())) {
            final List<String> operatorNameList = orderTrackDetailResponse.getOperatorIdList().stream().map(item -> operatorNameMap.getOrDefault(item, new UserBo("system", "系统")).getDisplayName()).distinct().collect(Collectors.toList());
            operatorName = String.join(",", operatorNameList);
        }

        if (StringUtils.isEmpty(orderTrackDetailResponse.getAppId())) {
            orderTrackDetailVO.setOperatorName(operatorName);
        } else {
            orderTrackDetailVO.setOperatorName(operatorName + " " + Optional.ofNullable(appIdDesc.get(orderTrackDetailResponse.getAppId())).orElse(""));
        }

        if (StringUtils.isNotBlank(orderTrackDetailResponse.getExtData())) {
            HashMap<String, Object> kv = JacksonUtils.fromJsonToMap(orderTrackDetailResponse.getExtData());
            Optional.ofNullable(kv.get("printerConnectionMode"))
                    .ifPresent(printerConnectionMode -> {
                        try {
                            PrinterConnectionMode mode = PrinterConnectionMode.valueOf((String) printerConnectionMode);
                            orderTrackDetailVO.setComment(mode.getDesc());
                        } catch (IllegalArgumentException e) {
                            log.error("Unknown PrinterConnectionMode value: {}, error: {}", printerConnectionMode, e.getMessage(), e);
                        }
                    });
        }

        orderTrackDetailVO.setOperationTime(orderTrackDetailResponse.getOperateTime());
        orderTrackDetailVO.setOperatorType(TrackTypeEnum.ORDER.getCode());

        return orderTrackDetailVO;
    }

    /**
     * 构造打印完成轨迹详情
     */
    private OrderTrackDetailVO buildPrintDoneTrackDetail(OrderTrackDetailResponse orderTrack,
            Map<Long, UserBo> operatorNameMap, Map<String, String> appIdDesc, TrackOpType trackOpType) {
        String appName = appIdDesc.getOrDefault(orderTrack.getAppId(), "");

        String operator = UserBo.SYSTEM.getAccountName();
        if (CollectionUtils.isNotEmpty(orderTrack.getOperatorIdList())) {
            List<String> operatorNameList = orderTrack.getOperatorIdList()
                    .stream()
                    .map(item -> operatorNameMap.getOrDefault(item, UserBo.SYSTEM).getDisplayName())
                    .distinct()
                    .collect(Collectors.toList());
            operator = String.join(",", operatorNameList);
        }

        StringBuilder comment = new StringBuilder();
        if (StringUtils.isNotBlank(orderTrack.getExtData())) {
            HashMap<String, Object> kv = JacksonUtils.fromJsonToMap(orderTrack.getExtData());
            Optional.ofNullable(kv.get("pcClientVersion"))
                    .ifPresent(pcClientVersion -> comment.append("客户端版本：").append(pcClientVersion).append('；'));
            Optional.ofNullable(kv.get("storeName"))
                    .ifPresent(comment::append);
            Optional.ofNullable(kv.get("printTimes"))
                    .ifPresent(printTimes -> comment.append(String.format("第%d次打印；", (Integer) printTimes)));
            Optional.ofNullable(kv.get("contentType"))
                    .map(contentType -> SceneTypeEnum.enumOf((Integer) contentType))
                    .ifPresent(sceneType -> comment.append(sceneType.getValue()).append('；'));
            Optional.ofNullable(kv.get("printerConnectionMode"))
                    .map(printerConnectionMode -> PrinterConnectionMode.valueOf(printerConnectionMode.toString().toUpperCase()))
                    .ifPresent(printerConnectionMode -> {
                        comment.append(printerConnectionMode.getDesc()).append('；');
                    });
        }

        return OrderTrackDetailVO.builder()
                .uuid(UUID.randomUUID().toString())
                .operatorCode(orderTrack.getTrackOpType())
                .operatorDesc(trackOpType.getMsg())
                .operatorName(operator + " " + appName)
                .operationTime(orderTrack.getOperateTime())
                .operatorType(TrackTypeEnum.ORDER.getCode())
                .comment(comment.toString())
                .build();
    }

    private OrderTrackDetailVO convertOrderHandleTrack(OrderTrackDetailResponse orderTrackDetailResponse, Map<Long, UserBo> operatorNameMap, Map<String, String> appIdDesc) {
        OrderTrackDetailVO orderTrackDetailVO = new OrderTrackDetailVO();
        final TrackOpType trackOpType = TrackOpType.codeOf(orderTrackDetailResponse.getTrackOpType());
        orderTrackDetailVO.setOperatorCode(orderTrackDetailResponse.getTrackOpType());
        orderTrackDetailVO.setOperatorDesc(trackOpType.getMsg());

        String operatorName = "";
        if (CollectionUtils.isNotEmpty(orderTrackDetailResponse.getOperatorIdList())) {
            final List<String> operatorNameList = orderTrackDetailResponse.getOperatorIdList().stream().map(item -> operatorNameMap.getOrDefault(item, new UserBo("system", "系统")).getDisplayName()).distinct().collect(Collectors.toList());
            operatorName = String.join(",", operatorNameList);
        } else {
            operatorName = new UserBo("system", "系统").getDisplayName();
        }

        if (StringUtils.isEmpty(orderTrackDetailResponse.getAppId())) {
            orderTrackDetailVO.setOperatorName(operatorName);
        } else {
            orderTrackDetailVO.setOperatorName(operatorName + " " + Optional.ofNullable(appIdDesc.get(orderTrackDetailResponse.getAppId())).orElse(""));
        }

        if (StringUtils.isNotBlank(orderTrackDetailResponse.getExtData())) {
            JSONObject extData = JSONObject.parseObject(orderTrackDetailResponse.getExtData());
            if (StringUtils.isNotBlank(extData.getString("afterSaleId"))) {
                orderTrackDetailVO.setComment("渠道退单号:" + extData.getString("afterSaleId"));
            }
        }

        orderTrackDetailVO.setOperationTime(orderTrackDetailResponse.getOperateTime());
        orderTrackDetailVO.setOperatorType(TrackTypeEnum.ORDER.getCode());
        orderTrackDetailVO.setOperationTime(orderTrackDetailResponse.getOperateTime());
        setTrackComment(orderTrackDetailResponse, orderTrackDetailVO);
        return orderTrackDetailVO;
    }


    private OrderTrackDetailVO convertQnhOpenTrack(OrderTrackDetailResponse orderTrackDetailResponse, Map<Long, UserBo> operatorNameMap, Map<String, String> appIdDesc) {
        OrderTrackDetailVO orderTrackDetailVO = new OrderTrackDetailVO();
        final TrackOpType trackOpType = TrackOpType.codeOf(orderTrackDetailResponse.getTrackOpType());
        orderTrackDetailVO.setOperatorCode(orderTrackDetailResponse.getTrackOpType());
        orderTrackDetailVO.setOperatorDesc(trackOpType.getMsg());

        String operatorName = "牵牛花开放平台";

        if (StringUtils.isEmpty(orderTrackDetailResponse.getAppId())) {
            orderTrackDetailVO.setOperatorName(operatorName);
        } else {
            orderTrackDetailVO.setOperatorName(operatorName + " " + Optional.ofNullable(appIdDesc.get(orderTrackDetailResponse.getAppId())).orElse(""));
        }

        if (StringUtils.isNotBlank(orderTrackDetailResponse.getExtData())) {
            JSONObject extData = JSONObject.parseObject(orderTrackDetailResponse.getExtData());
            if (StringUtils.isNotBlank(extData.getString("afterSaleId"))) {
                orderTrackDetailVO.setComment("渠道退单号:" + extData.getString("afterSaleId"));
            }
        }

        orderTrackDetailVO.setOperationTime(orderTrackDetailResponse.getOperateTime());
        orderTrackDetailVO.setOperatorType(TrackTypeEnum.ORDER.getCode());
        orderTrackDetailVO.setOperationTime(orderTrackDetailResponse.getOperateTime());
        setTrackComment(orderTrackDetailResponse, orderTrackDetailVO);
        return orderTrackDetailVO;
    }

    private Map<String, String> getAppIdDesc() {
        final String appIdMapString = LionUtils.getAppidMap();
        if (StringUtils.isEmpty(appIdMapString)) {
            return new HashMap<>();
        }
        final Map<String, String> fromJson = JacksonUtils.fromJson(appIdMapString, Map.class);
        return fromJson;
    }
}
