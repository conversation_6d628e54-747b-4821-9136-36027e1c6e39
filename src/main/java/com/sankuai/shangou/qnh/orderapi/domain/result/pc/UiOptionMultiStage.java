package com.sankuai.shangou.qnh.orderapi.domain.result.pc;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2022/12/02 19:50
 * @Description:
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
public class UiOptionMultiStage {

    private Integer value;

    private String labelId;

    private String code;

    private String desc;

    private List<UiChildOption> children;


    public UiOptionMultiStage(Integer value, String code, String desc, List<UiChildOption> children) {
        this.value = value;
        this.code = code;
        this.desc = desc;
        this.children = children;
    }

    public UiOptionMultiStage(String labelId, String code, String desc, List<UiChildOption> children) {
        this.labelId = labelId;
        this.code = code;
        this.desc = desc;
        this.children = children;
    }
}
