package com.sankuai.shangou.qnh.orderapi.domain.vo.app;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/3/13 11:14
 */
@TypeDoc(
        description = "金额退商品信息"
)
@Data
public class MoneyRefundItemVo {

    @FieldDoc(
            description = "商品内部skuId"
    )
    private String innerSkuId;

    @FieldDoc(
            description = "商家在渠道的skuId"
    )
    private String customerSkuId;

    @FieldDoc(
            description = "实付价格"
    )
    private Integer currentPrice;

    @FieldDoc(
            description = "可退金额，分"
    )
    private Integer refundMoney;

    @FieldDoc(
            description = "订单商品行id"
    )
    private Long orderItemId;

    @FieldDoc(
            description = "申请退款重量，毫克 (抖音渠道需要)"
    )
    private Long applyWeight;

    @FieldDoc(
            description = "申请退款数量,(暂美团渠道需要使用)"
    )
    private Integer refundCount;

    @FieldDoc(
            description = "渠道商品spu,(暂美团渠道需要)"
    )
    private String customSpu;
}
