package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/11/18
 * @email jianglilin02@meituan
 */
@TypeDoc(
        description = "查询虚拟号返回",
        authors = "jianglilin02"
)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class VirtualNumberInfo {

    @FieldDoc(
            description = "是否可以联系用户", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "是否可以联系用户", required = true)
    private boolean canContactUser;

    @FieldDoc(
            description = "用户隐私号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "用户隐私号", required = true)
    private String userVirtualPhone;

}
