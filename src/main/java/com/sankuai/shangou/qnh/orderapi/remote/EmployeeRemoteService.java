package com.sankuai.shangou.qnh.orderapi.remote;

import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.meituan.shangou.saas.tenant.thrift.EmployeeThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.employee.EmployeeDepDto;
import com.meituan.shangou.saas.tenant.thrift.dto.employee.response.EmployDepInfoResponse;
import com.meituan.shangou.saas.tenant.thrift.dto.employee.response.EmployDepMapResponse;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ResponseHandler;
import com.sankuai.shangou.qnh.orderapi.utils.pc.RpcInvoker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2018/10/26
 * 员工的外部接口
 **/
@Component
@Slf4j
public class EmployeeRemoteService {

    @Resource(name = "employThriftService")
    EmployeeThriftService employeeThriftService;

    /**
     * 批量查询员工部门
     *
     * @param empIds   员工ID集合
     * @param tenantId 租户ID
     * @return
     */
    @MethodLog(logRequest = true, logResponse = true)
    public Map<Long, EmployeeDepDto> queryEmployeeByIds(List<Long> empIds, Long tenantId) {
        //如果是null的，直接返回，不调用
        if (CollectionUtils.isEmpty(empIds)) {
            return new HashMap<>();
        }
        EmployDepMapResponse response = RpcInvoker.invokeReturn(() -> employeeThriftService.queryEmployeeByIds(empIds, tenantId));
        ResponseHandler.handleStatus(response.getStatus());
        return response.getEmployeeInfoMap();
    }

    /**
     * 查询员工信息
     *
     * @param empId    员工ID
     * @param tenantId 租户ID
     * @return
     */
    public EmployeeDepDto queryEmployeeById(Long empId, Long tenantId) {
        EmployDepInfoResponse response = RpcInvoker.invokeReturn(() -> employeeThriftService.queryEmployeeById(empId, tenantId));
        ResponseHandler.handleStatus(response.getStatus());
        return response.getEmployeeInfo();
    }

}
