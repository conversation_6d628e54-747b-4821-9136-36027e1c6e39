package com.sankuai.shangou.qnh.orderapi.domain.request.app;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.constant.app.IntegerBooleanConstants;
import com.sankuai.shangou.qnh.orderapi.utils.app.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 全部订单列表分页查询请求.
 *
 * <AUTHOR>
 * @since 2021/11/8 11:16
 */
@TypeDoc(
        description = "全部订单列表分页查询请求"
)
@ApiModel("全部订单列表分页查询请求")
@Data
public class OrderSearchRequest {

    @FieldDoc(
            description = "门店 ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店 ID")
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "第几页", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "第几页", required = true)
    @NotNull
    private Integer page;

    @FieldDoc(
            description = "每页行数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "每页行数", required = true)
    @NotNull
    private Integer size = 10;

    @FieldDoc(
            description = "关键词搜索", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "关键词搜索")
    @NotNull(message = "搜索词不能为空")
    private String keyword;

    @FieldDoc(
            description = "门店类型 3-门店 5-中心仓 6-前置仓", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店类型")
    private Integer entityType;

    public OrderListRequest build() {
        OrderListRequest orderListRequest = new OrderListRequest();
        orderListRequest.setStoreId(storeId);
        orderListRequest.setBeginCreateDate(DateUtils.minusDaysFormatter(IntegerBooleanConstants.SIXTY));
        orderListRequest.setEndCreateDate(DateUtils.addDaysFormatter());
        orderListRequest.setKeyword(keyword);
        orderListRequest.setPage(page);
        orderListRequest.setPageSize(size);
        orderListRequest.setEntityType(entityType);
        return orderListRequest;
    }

}
