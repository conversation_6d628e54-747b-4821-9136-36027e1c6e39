package com.sankuai.shangou.qnh.orderapi.remote;

import com.dianping.cat.Cat;
import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.google.common.collect.Lists;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.meituan.shangou.saas.order.management.client.dto.request.online.ViewIdCondition;
import com.sankuai.shangou.commons.exception.common.exceptions.BizException;
import com.sankuai.shangou.commons.exception.common.exceptions.ThirdPartyException;
import com.sankuai.shangou.commons.thrift.publisher.response.TResult;
import com.sankuai.shangou.logistics.warehouse.TradeShippingOrderService;
import com.sankuai.shangou.logistics.warehouse.dto.AbnOrderDTO;
import com.sankuai.shangou.logistics.warehouse.dto.TradeShippingOrderDTO;
import com.sankuai.shangou.logistics.warehouse.enums.TradeShippingOrderStatus;
import com.sankuai.shangou.qnh.orderapi.exception.pc.CommonRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-01-29
 * @email <EMAIL>
 */
@Slf4j
@Component
@Rhino
public class TradeShippingOrderRemoteService {

    @Resource
    private TradeShippingOrderService tradeShippingOrderService;

    @Degrade(rhinoKey = "TradeShippingOrderRemoteService-getUnprocessedAbnOrderCountFallback",
            fallBackMethod = "getByChannelOrderId",
            timeoutInMilliseconds = 1000)
    public TradeShippingOrderDTO getByChannelOrderId(long warehouseId, int orderBizType, String channelOrderId) {
        try {
            TResult<List<TradeShippingOrderDTO>> tradeShippingOrderDTOListResult = tradeShippingOrderService.getByTradeOrderNos(warehouseId, orderBizType, Lists.newArrayList(channelOrderId));

            if (!tradeShippingOrderDTOListResult.isSuccess() || CollectionUtils.isEmpty(tradeShippingOrderDTOListResult.getData())) {
                return null;
            }
            return tradeShippingOrderDTOListResult.getData().get(0);
        } catch (Exception e) {
            log.error("invoke abnOrderService.getUnprocessed error");
            return null;
        }

    }

    @Degrade(rhinoKey = "TradeShippingOrderRemoteService-getRecentListByOperatorIdAndStatusList",
            fallBackMethod = "getRecentListByOperatorIdAndStatusListFallback",
            timeoutInMilliseconds = 1000)
    public List<TradeShippingOrderDTO> getRecentListByOperatorIdAndStatusList(long warehouseId, Long operatorId, List<Integer> tradeShippingOrderStatusList) {
        try {
            TResult<List<TradeShippingOrderDTO>> tradeShippingOrderDTOListResult =
                    tradeShippingOrderService.getRecentListByOperatorIdAndStatusList(warehouseId, operatorId, tradeShippingOrderStatusList);

            if (!tradeShippingOrderDTOListResult.isSuccess()) {
                throw new BizException("查询出库单列表失败");
            }

            return tradeShippingOrderDTOListResult.getData();
        } catch (Exception e) {
            log.error("invoke abnOrderService.getUnprocessed error", e);
            Cat.logEvent("PICK_DELIVERY_SPLIT", "QUERY_TRADE_SHIPPING_ORDER_ERROR");
            return Collections.emptyList();
        }

    }

    @Degrade(rhinoKey = "TradeShippingOrderRemoteService.queryByTradeOrderIds", fallBackMethod = "queryByTradeOrderIdsFallback", timeoutInMilliseconds = 2000)
    @MethodLog(logRequest = true, logResponse = true)
    public List<TradeShippingOrderDTO> queryByTradeOrderIds(long warehouseId, List<ViewIdCondition> viewIdConditionList) {
        if (CollectionUtils.isEmpty(viewIdConditionList)) {
            return Lists.newArrayList();
        }
        Map<Integer, List<ViewIdCondition>> orderBizTypeMap = viewIdConditionList.stream().collect(Collectors.groupingBy(ViewIdCondition::getOrderBizType));
        List<TradeShippingOrderDTO> res = Lists.newArrayList();
        for (Map.Entry<Integer, List<ViewIdCondition>> entry : orderBizTypeMap.entrySet()) {
            TResult<List<TradeShippingOrderDTO>> result = tradeShippingOrderService.getByTradeOrderNos(warehouseId, entry.getKey(),
                    entry.getValue().stream().map(ViewIdCondition::getViewOrderId).collect(Collectors.toList()));
            if (!result.isSuccess()) {
                throw new ThirdPartyException("查询拣货出库单失败");
            }
            res.addAll(Optional.ofNullable(result.getData()).orElse(Lists.newArrayList()));
        }
        return res;
    }

    public List<TradeShippingOrderDTO> queryByTradeOrderIdsFallback(long warehouseId, List<ViewIdCondition> viewIdConditionList) {
        log.error("TradeShippingOrderServiceWrapper.queryByTradeOrderIds 发生降级");
        return Collections.emptyList();
    }

    public List<TradeShippingOrderDTO> getRecentListByOperatorIdAndStatusListFallback(long warehouseId, Long operatorId, List<Integer> tradeShippingOrderStatusList) {
        return Collections.emptyList();
    }


    public TradeShippingOrderDTO getByChannelOrderIdFallback(long warehouseId, int orderBizType, String channelOrderId) {
        log.warn("getByChannelOrderId has fallback,channelOrderId = {}", channelOrderId);
        return  null;
    }

    @MethodLog(logRequest = true, logResponse = true)
    public List<TradeShippingOrderDTO> getByTradeOrderNos(long warehouseId, int orderBizType, List<String> channelOrderIds) {
        TResult<List<TradeShippingOrderDTO>> recentListResult = tradeShippingOrderService.getByTradeOrderNos(
                warehouseId, orderBizType, channelOrderIds
        );
        if (!recentListResult.isSuccess()) {
            throw new CommonRuntimeException("查询出库单失败");
        }
        return recentListResult.getData();
    }
}
