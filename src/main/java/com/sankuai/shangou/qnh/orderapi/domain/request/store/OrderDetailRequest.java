package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2019/7/10
 * desc: 查询订单详情请求
 */
@TypeDoc(
        description = "查询订单详情请求"
)
@ApiModel("查询订单详情请求")
@Data
public class OrderDetailRequest extends BaseOrderReq {

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID")
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "租户ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户ID")
    @NotNull
    private Long tenantId;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "外卖门店Id")
    @NotNull
    private String wmPoiId;

    public void validate(){
    }
}
