package com.sankuai.shangou.qnh.orderapi.remote;

import com.google.common.collect.Maps;
import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.meituan.shangou.saas.dto.StatusCodeEnum;
import com.meituan.shangou.saas.dto.request.ocms.OCMSOrderCancelRequest;
import com.meituan.shangou.saas.dto.request.ocms.OCMSOrderPartRefundRequest;
import com.meituan.shangou.saas.dto.response.OrderTrackDetailResponse;
import com.meituan.shangou.saas.o2o.dto.model.OCMSOrderKey;
import com.meituan.shangou.saas.o2o.dto.model.OCMSOrderPartRefundProductModel;
import com.meituan.shangou.saas.o2o.dto.request.OCMSOperateCheckRequest;
import com.meituan.shangou.saas.o2o.dto.request.OrderTrackRequest;
import com.meituan.shangou.saas.o2o.dto.response.OCMSOperateCheckResponse;
import com.meituan.shangou.saas.o2o.dto.response.OrderTrackResponse;
import com.meituan.shangou.saas.o2o.service.BizOrderThriftService;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.meituan.shangou.saas.service.ocms.OCMSOrderOperateThriftService;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.CancelOrderRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.PartRefundRequest;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.CommonResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.User;
import com.sankuai.shangou.qnh.orderapi.exception.store.CommonRuntimeException;
import com.sankuai.shangou.qnh.orderapi.utils.store.ApiMethodParamThreadLocal;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @createTime 2020/4/19
 * @description
 */
@Service(value = "storeOrderBizRemoteService")
@Slf4j
public class OrderBizForStoreRemoteService {

    @Autowired
    private BizOrderThriftService bizOrderThriftService;

    @Resource
    OCMSOrderOperateThriftService ocmsOrderOperateThriftService;

    public Map<OCMSOrderKey, List<Integer>> queryOrderOperateItems(Long tenantId, List<OCMSOrderKey> orderKeys,
                                                                   List<Integer> operateItems) throws TException {
        OCMSOperateCheckRequest request = new OCMSOperateCheckRequest();
        request.setTenantId(tenantId);
        request.setOrderList(orderKeys);
        request.setToCheckOperateItems(operateItems);
        OCMSOperateCheckResponse response = ocmsOrderOperateThriftService.checkOrderCouldOperateItems(request);
        if (response == null) {
            return Maps.newHashMap();
        }
        return Objects.isNull(response.getCouldOperateItems()) ? new HashMap<>() : response.getCouldOperateItems();
    }

    public CommonResponse tenantPartRefund(PartRefundRequest request) {
        try {
            OCMSOrderPartRefundRequest bizRequest = buildPartRefundRequest(request);
            com.meituan.shangou.saas.o2o.dto.response.CommonResponse response = ocmsOrderOperateThriftService.tenantPartRefund(bizRequest);
            log.info("商家部分退款接口调用request：{}, response:{}", bizRequest, response);
            if (success(response)){
                return CommonResponse.success(null);
            }
            return CommonResponse.fail(response.getStatus().getCode(), response.getStatus().getMessage());
        }catch (Exception e){
            log.error("orderbiz tenantPartRefund TException request:{}", request, e);
            return CommonResponse.fail(ResultCode.FAIL);
        }
    }

    public CommonResponse tenantCancelOrder(CancelOrderRequest request) {
        try {
            OCMSOrderCancelRequest bizRequest = buildCancelOrderRequest(request);
            com.meituan.shangou.saas.o2o.dto.response.CommonResponse response = ocmsOrderOperateThriftService.tenantCancelOrder(bizRequest);
            log.info("商家全单取消接口调用request：{}, response:{}", bizRequest, response);
            if (success(response)){
                return CommonResponse.success(null);
            }
            return  CommonResponse.fail(ResultCode.FAIL, responseMsg(response));
        }catch (Exception e){
            log.error("orderbiz tenantCancelOrder TException request:{}", request, e);
            return  CommonResponse.fail(ResultCode.FAIL, "未知异常");
        }
    }

    public List<OrderTrackDetailResponse> queryOrderTrack(long tenantId, String viewOrderId, int orderBizType, List<Integer> trackSources) {
        OrderTrackRequest req = OrderTrackRequest.builder()
                .tenantId(tenantId)
                .viewOrderId(viewOrderId)
                .orderBizType(orderBizType)
                .trackSourceList(trackSources)
                .build();
        try {
            OrderTrackResponse resp = bizOrderThriftService.queryOrderTrack(req);
            log.info("BizOrderThriftService#queryOrderTrack request: {}, response: {}", req, resp);
            if (resp.getStatus().getCode() != 0) {
                return Collections.emptyList();
            }
            return ObjectUtils.defaultIfNull(resp.getOrderTrackDetailResponseList(), Collections.emptyList());
        } catch (TException e) {
            log.error("Request bizOrderThriftService.queryOrderTrack error: {}", e.getMessage(), e);
            throw new CommonRuntimeException(e);
        }
    }

    private OCMSOrderCancelRequest buildCancelOrderRequest(CancelOrderRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        OCMSOrderCancelRequest bizRequest = new OCMSOrderCancelRequest();
        bizRequest.setOperatorUserId(user.getAccountId());
        bizRequest.setOperatorUserName(user.getUsername());
        bizRequest.setOrderBizType(ChannelOrderConvertUtils.sourceMid2Biz(
                request.getChannelId()
        ));
        bizRequest.setReason(request.getReason());
        bizRequest.setReasonCode(request.getReasonCode());
        bizRequest.setTenantId(user.getTenantId());
        bizRequest.setViewOrderId(request.getChannelOrderId());
        bizRequest.setAppId(ApiMethodParamThreadLocal.getIdentityInfo().getAppId());
        bizRequest.setOperatorAccountId(user.getAccountId());
        return bizRequest;
    }


    private String responseMsg(com.meituan.shangou.saas.o2o.dto.response.CommonResponse response) {
        return response != null && response.getStatus() != null ? response.getStatus().getMessage() : StatusCodeEnum.FAIL.getMessage();
    }

    private boolean success(com.meituan.shangou.saas.o2o.dto.response.CommonResponse response) {
        return response != null && response.getStatus() != null
                && Objects.equals(response.getStatus().getCode(), StatusCodeEnum.SUCCESS.getCode());
    }

    private OCMSOrderPartRefundRequest buildPartRefundRequest(PartRefundRequest request) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        OCMSOrderPartRefundRequest bizRequest = new OCMSOrderPartRefundRequest();
        bizRequest.setOperatorUserId(user.getAccountId());
        bizRequest.setOperatorUserName(user.getUsername());
        bizRequest.setOrderBizType(
                ChannelOrderConvertUtils.sourceMid2Biz(
                    request.getChannelId()
                ));
        bizRequest.setPartRefundProductModelList(request.getPartRefundProductList().stream().map(refundItem->{
            OCMSOrderPartRefundProductModel model = new OCMSOrderPartRefundProductModel();
            model.setCount(refundItem.getCount());
            model.setCustomSkuId(refundItem.getCustomSkuId());
            model.setSkuId2(refundItem.getSkuId());
            model.setSkuName(refundItem.getSkuName());
            return model;
        }).collect(Collectors.toList()));
        bizRequest.setReason(request.getReason());
        bizRequest.setShopId(request.getStoreId());
        bizRequest.setTenantId(user.getTenantId());
        bizRequest.setViewOrderId(request.getChannelOrderId());
        bizRequest.setRequestId(System.currentTimeMillis() + request.getChannelOrderId());
        bizRequest.setAppId(ApiMethodParamThreadLocal.getIdentityInfo().getAppId());
        bizRequest.setOperatorAccountId(user.getAccountId());
        return bizRequest;
    }

}
