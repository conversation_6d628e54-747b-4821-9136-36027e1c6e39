package com.sankuai.shangou.qnh.orderapi.domain.request.app;


import com.dianping.lion.client.Lion;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.constant.app.MiniAppConstants;
import com.sankuai.shangou.qnh.orderapi.utils.app.DateUtils;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 全部订单列表分页查询请求.
 *
 * <AUTHOR>
 * @date 2023-07-11
 */
@TypeDoc(
        description = "全部订单列表分页查询请求"
)
@ApiModel("全部订单列表分页查询请求")
@Data
public class OrderSearchMiniAppRequest {

    @FieldDoc(
            description = "门店 ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店 ID")
    private List<Long> storeIds;

    @FieldDoc(
            description = "第几页", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "第几页", required = true)
    @NotNull
    private Integer page;

    @FieldDoc(
            description = "每页行数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "每页行数", required = true)
    @NotNull
    private Integer size = 10;

    @FieldDoc(
            description = "关键词搜索", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "关键词搜索")
    @NotNull(message = "搜索词不能为空")
    private String keyword;

    @FieldDoc(
            description = "门店类型 3-门店 5-中心仓 6-前置仓", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店类型")
    private Integer entityType;

    public OrderListMiniAppRequest build() {
        OrderListMiniAppRequest orderListRequest = new OrderListMiniAppRequest();
        orderListRequest.setStoreIds(storeIds);
        Integer maximumDay = MccConfigUtil.getMiniAppOrderSearchMaximumDay();
        orderListRequest.setBeginCreateDate(DateUtils.minusDaysFormatter(maximumDay));
        orderListRequest.setEndCreateDate(DateUtils.addDaysFormatter());
        orderListRequest.setKeyword(keyword);
        orderListRequest.setPage(page);
        orderListRequest.setPageSize(size);
        orderListRequest.setEntityType(entityType);
        return orderListRequest;
    }

}
