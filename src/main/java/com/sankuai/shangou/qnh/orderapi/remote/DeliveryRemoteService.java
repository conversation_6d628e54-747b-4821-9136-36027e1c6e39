package com.sankuai.shangou.qnh.orderapi.remote;

import com.google.common.collect.Lists;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OcmsOrderDetailReq;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OrderDetailVo;
import com.sankuai.meituan.shangou.empower.delivery.thrift.request.CreateDeliveryRequest;
import com.sankuai.meituan.shangou.empower.delivery.thrift.response.CommonResponse;
import com.sankuai.meituan.shangou.empower.delivery.thrift.response.QueryDeliveryChannelsResponse;
import com.sankuai.meituan.shangou.empower.delivery.thrift.response.QueryDeliveryConfigsResponse;
import com.sankuai.meituan.shangou.empower.delivery.thrift.response.QueryShopDeliveryConfigsDetailResponse;
import com.sankuai.meituan.shangou.empower.delivery.thrift.response.QueryShopDeliveryConfigsResponse;
import com.sankuai.meituan.shangou.empower.delivery.thrift.response.SyncDeliveryRangeResponse;
import com.sankuai.meituan.shangou.empower.delivery.thrift.service.DeliveryConfigThriftService;
import com.sankuai.meituan.shangou.empower.delivery.thrift.service.DeliveryThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.DeliveryConfigurationThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.dto.BatchStoreDeliveryConfigDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.dto.DeliveryPlatformConfigDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.dto.StoreChannelDeliveryConfigDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.dto.StoreDeliveryConfigDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request.BatchStoreDeliveryConfigQueryRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request.ChannelDeliveryConfigUpdateRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request.DeliveryStoreConfigSaveRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request.StoreAggDeliveryConfigModifyRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request.StoreConfigQueryRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request.StoreConfigSaveRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request.StoreDeliveryConfigQueryRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request.TenantDeliveryConfigQueryRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request.TenantDeliveryConfigUpdateRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.request.UnbindDeliveryPoiOnAggrPlatformRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.ConfigCommonResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.DeliveryRangeSyncResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.StoreConfigQueryResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.StoreDeliveryConfigBatchQueryResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.StoreDeliveryConfigQueryResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.TLaunchRule;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.TOuterDeliveryConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.TStoreConfig;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.TenantDeliveryConfigQueryResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.configuration.response.TenantDeliveryConfigUpdateResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.QueryDeliveryInfoThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.DeliveryOperateItem;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.QueryOrderDeliveryInfoKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryOrder;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request.QueryDeliveryOperateItemRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request.QueryDeliveryOrderRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.QueryDeliveryOperateItemResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.QueryDeliveryOrderResponse;
import com.sankuai.meituan.shangou.saas.common.data.PageResult;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.shangou.qnh.orderapi.builder.pc.DeliveryBuilder;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.DeliveryInfoBO;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.BaseOrderReq;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.DeleteShopDeliveryConfigRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.ImportShopDeliveryConfigListRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.ModifyDeliveryConfigRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.ModifyShopDeliveryConfigDetailRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.QueryShopDeliveryConfigDetailRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.QueryShopDeliveryConfigRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.SyncDeliveryRangeRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.RetryCreateDeliveryRequest;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.QueryShopDeliveryConfigDetailResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.QueryStoreDeliveryConfigResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.pc.StoreDeliveryConfigResponse;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.Result;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.ResultBuilder;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.AggrPlatformDeliveryConfigVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.DeliveryChannelConfigVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.DeliveryChannelVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.DeliveryConfigVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.DeliveryPlatformConfigVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.FailReasonVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.LaunchRuleVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.ShopAuth;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.ShopDeliveryConfigVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.StoreDeliveryConfigVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.StoreLaunchDeliveryConfigVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.User;
import com.sankuai.shangou.qnh.orderapi.exception.store.CommonRuntimeException;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import com.sankuai.shangou.qnh.orderapi.utils.pc.LionUtils;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ResponseHandler;
import com.sankuai.shangou.qnh.orderapi.utils.pc.RpcInvoker;
import com.sankuai.shangou.qnh.orderapi.utils.store.ApiMethodParamThreadLocal;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/3/16
 * desc: 配送调用Facade
 */
@Service
@Slf4j
public class DeliveryRemoteService {

    public static final int OPEN = 1;
    public static final int NOT_OPEN = 0;
    private static final int OUTER_OPEN = 1;

    @Resource
    private DeliveryConfigThriftService.Iface deliveryConfigThriftService;
    @Resource
    private DeliveryConfigurationThriftService deliveryConfigurationThriftService;
    @Autowired
    private QueryDeliveryInfoThriftService queryDeliveryInfoThriftService;
    @Resource
    private DeliveryThriftService.Iface deliveryThriftService;

    public com.sankuai.shangou.qnh.orderapi.domain.response.store.CommonResponse retryCreateDelivery(RetryCreateDeliveryRequest retryCreateDeliveryRequest) {
        CreateDeliveryRequest request = buildCreateDeliveryRequest(retryCreateDeliveryRequest);
        log.info("调用配送服务 deliveryThriftService.createDelivery()  request:{}", request);
        try {
            com.sankuai.meituan.shangou.empower.delivery.thrift.response.CommonResponse commonResponse = deliveryThriftService.createDelivery(request);
            log.info("调用配送服务 deliveryThriftService.createDelivery()  response:{}", commonResponse);
            return new com.sankuai.shangou.qnh.orderapi.domain.response.store.CommonResponse(commonResponse.getCode(), commonResponse.getMsg(), null);
        } catch (TException e) {
            log.error("调用配送服务异常 deliveryThriftService.createDelivery()  request:{}", request);
            throw new CommonRuntimeException(e);
        }

    }

    @MethodLog(logRequest = true, logResponse = true)
    public Map<Long, DeliveryOperateItem> queryDeliveryOperateItem(Long tenantId, Long storeId, List<Long> orderIdList){
        QueryDeliveryOperateItemRequest request = new QueryDeliveryOperateItemRequest();
        request.setTenantId(tenantId);
        request.setStoreId(storeId);
        request.setOrderIdList(orderIdList);
        try {
            QueryDeliveryOperateItemResponse response = queryDeliveryInfoThriftService.queryDeliveryOperateItem(request);

            if(response==null || response.getStatus()==null || response.getStatus().getCode()!= Status.SUCCESS.getCode()){
                log.error("queryDeliveryOperateItem error ,storeId:{},orderIdList:{}",storeId,orderIdList);
                return Collections.emptyMap();
            }
            return response.getOperateItemMap();
        }catch (Exception e){
            log.error("queryDeliveryOperateItem error ,storeId:{},orderIdList:{}",storeId,orderIdList,e);
        }
        return Collections.emptyMap();
    }


    /**
     * 查询门店配送配置,聚合运力平台
     * @param request
     * @return
     */
    @MethodLog(logRequest = true, logResponse = true)
    public StoreDeliveryConfigResponse queryStoreConfig(StoreConfigQueryRequest request) {
        StoreConfigQueryResponse response = RpcInvoker.invoke(() -> deliveryConfigurationThriftService
                .queryStoreConfiguration(request));

        ResponseHandler.checkResponseAndStatus(response, r -> r.getStatus().getCode(), r -> r.getStatus().getMsg());

        return buildConfigResp(response.getOuterDeliveryConfigs(), response.getTStoreConfig());
    }

    /**
     * 保存门店配送配置
     * @param request
     * @return
     */
    @MethodLog(logRequest = true, logResponse = true)
    public void saveStoreConfig(StoreConfigSaveRequest request) {
        ConfigCommonResponse response = RpcInvoker.invoke(() -> deliveryConfigurationThriftService
                .saveStoreConfiguration(request));

        ResponseHandler.checkResponseAndStatus(response, r -> r.getStatus().getCode(), r -> r.getStatus().getMsg());
    }

    /**
     * 中台门店解绑（聚合运力平台）配送商门店.
     *
     * @param tRequest 发起解绑聚合平台配送商门店的 thrift 请求
     */
    public void unbindDeliveryCompanyPoiOnAggr(UnbindDeliveryPoiOnAggrPlatformRequest tRequest) {
        ConfigCommonResponse response = RpcInvoker.invoke(() -> deliveryConfigurationThriftService
                .unbindDeliveryCompanyPoiOnAggr(tRequest));

        ResponseHandler.checkResponseAndStatus(response, r -> r.getStatus().getCode(), r -> r.getStatus().getMsg());
    }

    /**
     * 查询门店配送配置
     * @param request
     * @return
     */
    @MethodLog(logRequest = true, logResponse = true)
    public PageResult<ShopDeliveryConfigVO> queryShopDeliveryConfig(QueryShopDeliveryConfigRequest request) {

        QueryShopDeliveryConfigsResponse response = RpcInvoker.invoke(() -> deliveryConfigThriftService.queryShopDeliveryConfigs(DeliveryBuilder.buildRequest(request)));

        ResponseHandler.checkResponseAndStatus(response, r -> r.getStatus().getCode(), r -> r.getStatus().getMsg());

        List<ShopDeliveryConfigVO> shopDeliveryConfigVOList = CollectionUtils.isEmpty(response.getShopDeliveryConfigs()) ? Lists.newArrayList() : ConverterUtils.convertList(response.getShopDeliveryConfigs(), DeliveryBuilder::build);

        PageResult<ShopDeliveryConfigVO> pageResult = new PageResult<>(shopDeliveryConfigVOList,
                response.getPage(),response.getPageSize(), response.getTotal());

        return pageResult;
    }

    /**
     * 查询配送配置
     * @return
     */
    @MethodLog(logRequest = true, logResponse = true)
    public Result<List<DeliveryConfigVO>> queryDeliveryConfig() {
        if (LionUtils.getDeliveryShiftQueryTenantDeliveryConfig()) {
            TenantDeliveryConfigQueryResponse response = RpcInvoker.invoke(() -> deliveryConfigurationThriftService.queryTenantDeliveryConfig(
                    new TenantDeliveryConfigQueryRequest(ContextHolder.currentUserTenantId())
            ));
            ResponseHandler.checkResponseAndStatus(response, r -> r.getStatus().getCode(), r -> r.getStatus().getMsg());

            return ResultBuilder.buildSuccess(
                    Optional.ofNullable(response.getChannelDeliveryConfigs())
                            .orElse(new ArrayList<>())
                            .stream()
                            .map(it -> new DeliveryConfigVO(
                                    String.valueOf(it.getDeliveryChannelId()),
                                    it.getDeliveryChannelName(),
                                    it.getAppKey(),
                                    it.getSecret(),
                                    it.getStatus())
                            )
                            .collect(Collectors.toList())
            );

        } else {
            QueryDeliveryConfigsResponse response = RpcInvoker.invoke(() -> deliveryConfigThriftService.queryDeliveryConfigs(DeliveryBuilder.buildRequest()));
            ResponseHandler.checkResponseAndStatus(response, r -> r.getStatus().getCode(), r -> r.getStatus().getMsg());

            List<DeliveryConfigVO> deliveryConfigVOList = CollectionUtils.isEmpty(response.getDeliveryConfigs()) ? Lists.newArrayList() : ConverterUtils.convertList(response.getDeliveryConfigs(), DeliveryBuilder::build);
            return ResultBuilder.buildSuccess(deliveryConfigVOList);
        }
    }

    /**
     * 查询配送配置
     * @param request
     * @return
     */
    @MethodLog(logRequest = true, logResponse = true)
    public Result modifyDeliveryConfig(ModifyDeliveryConfigRequest request) {
        if (LionUtils.getDeliveryShiftUpdateTenantDeliveryConfig()) {
            TenantDeliveryConfigUpdateResponse response = RpcInvoker.invoke(() -> deliveryConfigurationThriftService.updateTenantDeliveryConfig(
                    new TenantDeliveryConfigUpdateRequest(
                            ContextHolder.currentUserTenantId(),
                            Optional.ofNullable(request.getList())
                                    .orElse(new ArrayList<>())
                                    .stream()
                                    .filter(it -> StringUtils.isNumeric(it.getDeliveryChannelId()))
                                    .map(it -> new ChannelDeliveryConfigUpdateRequest(
                                            Integer.parseInt(it.getDeliveryChannelId()),
                                            it.getAppKey(),
                                            it.getSecret(),
                                            it.getEnable()
                                    ))
                                    .collect(Collectors.toList())
                            ,
                            ContextHolder.currentUid(),
                            ContextHolder.currentAccount()
                    )
            ));
            ResponseHandler.checkResponseAndStatus(response, r -> r.getStatus().getCode(), r -> r.getStatus().getMsg());
            return ResultBuilder.buildResult(response.getStatus().getCode(), response.getStatus().getMsg(), null);

        } else {
            CommonResponse response = RpcInvoker.invoke(() -> deliveryConfigThriftService.modifyDeliveryConfigs(DeliveryBuilder.buildRequest(request)));
            ResponseHandler.checkResponseAndStatus(response, r -> r.getCode(), r -> r.getMsg());
            return ResultBuilder.buildResult(response.getCode(), response.getMsg(), null);
        }
    }

    /**
     * 批量导入门店配送配置
     * @param request
     * @return
     */
    @MethodLog(logRequest = true, logResponse = true)
    public Result importShopDeliveryConfigList(ImportShopDeliveryConfigListRequest request) {

        CommonResponse response = RpcInvoker.invoke(() -> deliveryConfigThriftService.importShopDeliveryConfigs(DeliveryBuilder.buildRequest(request)));

        ResponseHandler.checkResponseAndStatus(response, r -> r.getCode(), r -> r.getMsg());

        Result<List<DeliveryConfigVO>> result = ResultBuilder.buildResult(response.getCode(), response.getMsg(), null);

        return result;
    }

    /**
     * 批量导入门店配送配置
     * @param request
     * @return
     */
    @MethodLog(logRequest = true, logResponse = true)
    public Result deleteShopDeliveryConfig(DeleteShopDeliveryConfigRequest request) {

        CommonResponse response = RpcInvoker.invoke(() -> deliveryConfigThriftService.deleteShopDeliveryConfigs(DeliveryBuilder.buildRequest(request)));

        ResponseHandler.checkResponseAndStatus(response, r -> r.getCode(), r -> r.getMsg());

        Result<List<DeliveryConfigVO>> result = ResultBuilder.buildResult(response.getCode(), response.getMsg(), null);

        return result;
    }

    /**
     * 查询门店配送配置详情
     * @param request
     * @return
     */
    @MethodLog(logRequest = true, logResponse = true)
    public Result<QueryShopDeliveryConfigDetailResponse> queryShopDeliveryConfigDetail(QueryShopDeliveryConfigDetailRequest request) {

        QueryShopDeliveryConfigsDetailResponse response = RpcInvoker.invoke(() -> deliveryConfigThriftService.queryShopDeliveryConfigsDetail(DeliveryBuilder.buildRequest(request)));

        ResponseHandler.checkResponseAndStatus(response, r -> r.getStatus().getCode(), r -> r.getStatus().getMsg());

        QueryShopDeliveryConfigDetailResponse queryShopDeliveryConfigDetailResponse = DeliveryBuilder.build(response);

        Result<QueryShopDeliveryConfigDetailResponse> result = ResultBuilder.buildSuccess(queryShopDeliveryConfigDetailResponse);

        return result;
    }

    /**
     * 修改门店配送配置详情
     * @param request
     * @return
     */
    @MethodLog(logRequest = true, logResponse = true)
    public Result modifyShopDeliveryConfigDetail(ModifyShopDeliveryConfigDetailRequest request) {

        CommonResponse response = RpcInvoker.invoke(() -> deliveryConfigThriftService.modifyShopDeliveryConfigs(DeliveryBuilder.buildRequest(request)));

        ResponseHandler.checkResponseAndStatus(response, r -> r.getCode(), r -> r.getMsg());

        Result result = ResultBuilder.buildResult(response.getCode(), response.getMsg(), null);

        return result;
    }

    /**
     * 查询支持配送渠道
     * @return
     */
    @MethodLog(logRequest = true, logResponse =  true)
    public Result<List<DeliveryChannelVO>> queryDeliveryChannels() {

        QueryDeliveryChannelsResponse response = RpcInvoker.invoke(() -> deliveryConfigThriftService.queryDeliveryChannels());

        ResponseHandler.checkResponseAndStatus(response, r -> r.getStatus().getCode(), r -> r.getStatus().getMsg());

        List<DeliveryChannelVO> deliveryConfigVOList = CollectionUtils.isEmpty(response.getDeliveryChannelInfos()) ? Lists.newArrayList() : ConverterUtils.convertList(response.getDeliveryChannelInfos(), DeliveryBuilder::build);

        Result<List<DeliveryChannelVO>> result = ResultBuilder.buildSuccess(deliveryConfigVOList);

        return result;
    }

    /**
     * 同步配送范围
     * @param request
     * @return
     */
    @MethodLog(logRequest = true, logResponse = true)
    public Result<List<FailReasonVO>> syncDeliveryRange(@RequestBody SyncDeliveryRangeRequest request) {
        if (LionUtils.getDeliveryShiftSyncDeliveryRange()) {
            DeliveryRangeSyncResponse response = RpcInvoker.invoke(() -> deliveryConfigurationThriftService.syncDeliveryRange(DeliveryBuilder.buildTmsRequest(request)));
            ResponseHandler.checkResponseAndStatus(response, r -> r.getStatus().getCode(), r -> r.getStatus().getMsg());

            return ResultBuilder.buildSuccess(
                    CollectionUtils.isEmpty(response.getFailReasons()) ?
                            Lists.newArrayList() :
                            ConverterUtils.convertList(response.getFailReasons(), it -> new FailReasonVO(it.getChannelId(), it.getFailReason())));

        } else {
            SyncDeliveryRangeResponse response = RpcInvoker.invoke(() -> deliveryConfigThriftService.syncDeliveryRange(DeliveryBuilder.buildRequest(request)));
            ResponseHandler.checkResponseAndStatus(response, r -> r.getStatus().getCode(), r -> r.getStatus().getMsg());

            return ResultBuilder.buildSuccess(
                    CollectionUtils.isEmpty(response.getFailReasons()) ?
                            Lists.newArrayList() : ConverterUtils.convertList(response.getFailReasons(), DeliveryBuilder::build));
        }
    }

    /**
     * 查询门店/仓库的聚合配送设置
     * @param request
     * @return
     */
    @MethodLog(logRequest = true, logResponse = true)
    public QueryStoreDeliveryConfigResponse queryStoreDeliveryConfig(StoreDeliveryConfigQueryRequest request) {

        StoreDeliveryConfigQueryResponse storeDeliveryConfigQueryResponse = RpcInvoker.invoke(() -> deliveryConfigurationThriftService
                .queryStoreDeliveryConfig(request));
        ResponseHandler.checkResponseAndStatus(storeDeliveryConfigQueryResponse, r -> r.getStatus().getCode(), r -> r.getStatus().getMsg());

        return buildDeliveryConfigResponse(storeDeliveryConfigQueryResponse.getStoreDeliveryConfigDto());
    }

    /**
     * @description: 修改门店/仓库的聚合配送设置
     * @param: request
     * @return: response
    */
    @MethodLog(logRequest = true, logResponse = true)
    public ConfigCommonResponse modifyStoreDeliveryConfig(StoreAggDeliveryConfigModifyRequest request) {
        ConfigCommonResponse response = RpcInvoker.invoke(() -> deliveryConfigurationThriftService
                .modifyStoreAggDeliveryConfig(request));
        ResponseHandler.checkNullResponse(response);
        return response;
    }

    /**
     * @description: 修改门店/仓库的配送配置设置
     * @param: request
     * @return: response
     */
    @MethodLog(logRequest = true, logResponse = true)
    public ShopAuth updateStoreDeliveryConfig(DeliveryStoreConfigSaveRequest request) {
        ConfigCommonResponse response = RpcInvoker.invoke(() -> deliveryConfigurationThriftService
                .saveDeliveryStoreConfiguration(request));
        ResponseHandler.checkResponseAndStatus(response, r -> r.getStatus().getCode(), r -> r.getStatus().getMsg());
        ResponseHandler.checkNullResponse(response);
        return buildShopAuth(response);
    }

    private ShopAuth buildShopAuth (ConfigCommonResponse response) {
        ShopAuth shopAuth = new ShopAuth();
        if (response.getShopAuthConfig() != null) {
            shopAuth.setCode(response.getShopAuthConfig().getCode());
            shopAuth.setUrl(Optional.ofNullable(response.getShopAuthConfig().getDapLinkUrl()).orElse(""));
            shopAuth.setIsAuthed(response.getShopAuthConfig().getIsAuthed());
        }
        return shopAuth;
    }

    private StoreDeliveryConfigResponse buildConfigResp(List<TOuterDeliveryConfig> outerDeliveryConfigs,
                                                        TStoreConfig tStoreConfig) {
        StoreDeliveryConfigResponse response = new StoreDeliveryConfigResponse();
        List<AggrPlatformDeliveryConfigVO> aggrPlatformDeliveryConfig = Lists.newArrayList();
        StoreLaunchDeliveryConfigVO storeLaunchDeliveryConfig = new StoreLaunchDeliveryConfigVO();

        boolean hasOpenChannel = false;
        if (Objects.nonNull(tStoreConfig)) {
            // 实时单 自动发配送延时分钟数
            if (Objects.nonNull(tStoreConfig.getAutoLaunchDelayMinutes())) {
                storeLaunchDeliveryConfig.setAutoLaunchDelayMinutes(tStoreConfig.getAutoLaunchDelayMinutes());
            }
            // 实时单 自动发配送时间点
            if (Objects.nonNull(tStoreConfig.getAutoLaunchPoint())) {
                storeLaunchDeliveryConfig.setAutoLaunchPoint(tStoreConfig.getAutoLaunchPoint());
            }
            // 预约单 自动发配送分钟数
            if (Objects.nonNull(tStoreConfig.getBookingOrderAutoLaunchMinutes())) {
                storeLaunchDeliveryConfig.setBookingOrderAutoLaunchMinutes(tStoreConfig.getBookingOrderAutoLaunchMinutes());
            }
            // 预约单 自动发配送时间点
            if (Objects.nonNull(tStoreConfig.getBookingOrderAutoLaunchPoint())) {
                storeLaunchDeliveryConfig.setBookingOrderAutoLaunchPoint(tStoreConfig.getBookingOrderAutoLaunchPoint());
            }
            // 发配送模式 自动 or 手动
            if (Objects.nonNull(tStoreConfig.getLaunchPattern())) {
                storeLaunchDeliveryConfig.setLaunchPattern(tStoreConfig.getLaunchPattern());
            }
            // 发配送规则 低价优先 or ...
            if (Objects.nonNull(tStoreConfig.getLaunchRule())) {
                storeLaunchDeliveryConfig.setLaunchRule(tStoreConfig.getLaunchRule());
            }
            // 可用的发配送规则
            if (CollectionUtils.isNotEmpty(tStoreConfig.getAvailableLaunchRules())) {
                storeLaunchDeliveryConfig.setAvailableLaunchRules(ConverterUtils.convertList(tStoreConfig.getAvailableLaunchRules(),
                        this::buildLaunchRuleVO));
            }
            response.setStoreName(tStoreConfig.getStoreName());
        }

        if (CollectionUtils.isNotEmpty(outerDeliveryConfigs)) {
            for (TOuterDeliveryConfig it : outerDeliveryConfigs) {
                AggrPlatformDeliveryConfigVO vo = new AggrPlatformDeliveryConfigVO();
                vo.setDeliveryChannelId(it.getDeliveryChannelId());
                vo.setDeliveryChannelName(it.getDeliveryChannelName());
                vo.setIsOpen(it.getOpenFlag() == OUTER_OPEN ? OPEN : NOT_OPEN);
                vo.setDeliveryChannelMerchantId(it.getDeliveryChannelMerchantId());
                vo.setDeliveryChannelPoiId(it.getDeliveryChannelPoiId());
                vo.setDeliveryChannelPoiExt(it.getDeliveryChannelPoiExt());

                aggrPlatformDeliveryConfig.add(vo);
                if (vo.getIsOpen() == OPEN) {
                    hasOpenChannel = true;
                }
            }
        }

        response.setAggrPlatformDeliveryConfig(aggrPlatformDeliveryConfig);
        response.setStoreLaunchDeliveryConfig(hasOpenChannel ? storeLaunchDeliveryConfig : null);
        return response;
    }

    /**
     * 将 TLaunchRule 转换为 LaunchRuleVO.
     *
     * @param tLaunchRule 发配送规则
     * @return 配送规则 VO 对象
     */
    private LaunchRuleVO buildLaunchRuleVO(TLaunchRule tLaunchRule) {
        LaunchRuleVO launchRuleVO = new LaunchRuleVO();
        launchRuleVO.setCode(tLaunchRule.getCode());
        launchRuleVO.setName(tLaunchRule.getName());
        return launchRuleVO;
    }

    private QueryStoreDeliveryConfigResponse buildDeliveryConfigResponse(StoreDeliveryConfigDto deliveryConfig) {
        QueryStoreDeliveryConfigResponse queryResponse = new QueryStoreDeliveryConfigResponse();
        List<StoreChannelDeliveryConfigDto> storeChannelDeliveryConfigList =
                deliveryConfig.getStoreChannelDeliveryConfigList();
        StoreDeliveryConfigVO storeDeliveryConfig = new StoreDeliveryConfigVO();

        if (CollectionUtils.isEmpty(storeChannelDeliveryConfigList)) {
            log.info("查询聚合配送配置为空");
            storeDeliveryConfig.setDeliveryChannelConfigVoList(Collections.emptyList());
        } else {
            List<DeliveryChannelConfigVo> deliveryChannelConfigVoList =
                    storeChannelDeliveryConfigList.stream().map(config -> {
                        DeliveryChannelConfigVo deliveryChannelConfigVo = new DeliveryChannelConfigVo();
                        deliveryChannelConfigVo.setChannelType(config.getChannelType());
                        deliveryChannelConfigVo.setDeliveryLaunchPoint(config.getDeliveryLaunchPoint());
                        deliveryChannelConfigVo.setDeliveryPlatformConfigVo(buildDeliveryPlatformConfigVo(config.getDeliveryPlatformConfig()));
                        return deliveryChannelConfigVo;
                    }
            ).collect(Collectors.toList());
            storeDeliveryConfig.setDeliveryChannelConfigVoList(deliveryChannelConfigVoList);
        }
        queryResponse.setStoreAggDeliveryConfig(storeDeliveryConfig);
        return queryResponse;
    }

    private DeliveryPlatformConfigVo buildDeliveryPlatformConfigVo (DeliveryPlatformConfigDto configDto) {
        if(Objects.isNull(configDto)) {
            log.error("buildDeliveryPlatformConfigVo, configDto is null");
            return DeliveryPlatformConfigVo.EMPTY;
        }
        return DeliveryPlatformConfigVo.builder()
                .platformCode(configDto.getPlatformCode())
                .status(configDto.getOpenFlag())
                .redirectUrl(configDto.getRedirectUrl())
                .build();
    }

    public List<BatchStoreDeliveryConfigDto> batchQueryStoreDeliveryConfig(Long tenantId, List<Long> storeIdList) {
        BatchStoreDeliveryConfigQueryRequest request = new BatchStoreDeliveryConfigQueryRequest();
        request.setTenantId(tenantId);
        request.setStoreIdList(storeIdList);
        StoreDeliveryConfigBatchQueryResponse response = RpcInvoker.invoke(() -> deliveryConfigurationThriftService.batchQueryStoreDeliveryConfig(request));

        if (Objects.isNull(response)) {
            log.error("batchQueryStoreDeliveryConfig, response is null, tenantId: {}, storeIdList: {}", tenantId, storeIdList);
            return Collections.emptyList();
        }

        if (CollectionUtils.isEmpty(response.getBatchStoreDeliveryConfigDtoList())) {
            log.warn("batchQueryStoreDeliveryConfig batchStoreDeliveryConfigDtoList is empty, tenantId: {}, storeIdList: {}", tenantId, storeIdList);
            return Collections.emptyList();
        }

        return response.getBatchStoreDeliveryConfigDtoList();
    }

    private QueryOrderDeliveryInfoKey buildQueryOrderKey(OrderDetailVo order) {
        Long shopId=order.getOrderBaseDto().getShopId();
        if(order.getOrderBaseDto().getWarehouseId()!=null){
            shopId=order.getOrderBaseDto().getWarehouseId();
        }
        long orderId = order.getOrderBaseDto().getOrderId();
        return new QueryOrderDeliveryInfoKey(order.getOrderBaseDto().getTenantId(), shopId, orderId, order.getOrderBaseDto().getOrderStatus(), order.getOrderBaseDto().getOrderSource(), order.getOrderBaseDto().getDeliveryMethod(), order.getOrderBaseDto().getIsSelfDelivery());

    }

    private OcmsOrderDetailReq buildOcmsOrderDetailReq(BaseOrderReq request) {
        OcmsOrderDetailReq orderDetailReq = new OcmsOrderDetailReq();
        orderDetailReq.setChannelOrderId(request.getChannelOrderId());
        orderDetailReq.setChannelId(request.getChannelId());
        orderDetailReq.setTenantId(ContextHolder.currentUserTenantId());
        orderDetailReq.setOperator(ContextHolder.currentUid());
        return orderDetailReq;
    }

    private String processNullOrEmpty(String source) {
        try{
            return String.valueOf(Double.parseDouble(source));
        }catch (Exception e){
            return "0";
        }
    }

    public List<TDeliveryOrder> queryActiveDeliveryInfoByOrderIds(List<Long> orderList) {
        log.info("OCMSOrderServiceWrapper-queryDeliveryInfoByOrderIds, req={}", orderList);
        QueryDeliveryOrderResponse response = queryDeliveryInfoThriftService.queryActiveDeliveryOrderByOrderIdList(orderList);
        log.info("OCMSOrderServiceWrapper-queryDeliveryInfoByOrderIds, response={}", response);
        return response.getTDeliveryOrders();
    }

    public List<DeliveryInfoBO> queryDeliveryOrderByOrderId(Long orderId) {
        if (orderId == null) {
            return Collections.emptyList();
        }

        QueryDeliveryOrderRequest request = new QueryDeliveryOrderRequest();
        request.setOrderId(orderId);
        request.setMasterOnly(false);
        try {
            QueryDeliveryOrderResponse response = queryDeliveryInfoThriftService.queryDeliveryOrderByOrderId(request);
            if (response == null || response.getStatus() == null || response.getStatus().getCode() != Status.SUCCESS.getCode()) {
                return Collections.emptyList();
            }
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(response.getTDeliveryOrders())) {
                return Collections.emptyList();
            }
            return ConverterUtils.convertList(response.getTDeliveryOrders(), DeliveryInfoBO::toDeliveryBO);
        } catch (Exception e) {
            log.error("DeliveryInfoService queryDeliveryOrderByOrderId error orderId:{}", orderId, e);
        }
        return Collections.emptyList();
    }

    public List<DeliveryInfoBO> queryActiveAndSucDeliveryOrderByOrderId(Long orderId) {
        List<DeliveryInfoBO> deliveryInfoBOList = queryDeliveryOrderByOrderId(orderId);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(deliveryInfoBOList)) {
            return Collections.emptyList();
        }
        return deliveryInfoBOList.stream().filter(new Predicate<DeliveryInfoBO>() {
            @Override
            public boolean test(DeliveryInfoBO deliveryInfoBO) {
                return deliveryInfoBO.getActiveStatus() || (deliveryInfoBO.getStatus() != null && deliveryInfoBO.getStatus() == DeliveryStatusEnum.DELIVERY_DONE.getCode());
            }
        }).collect(Collectors.toList());
    }

    private CreateDeliveryRequest buildCreateDeliveryRequest(RetryCreateDeliveryRequest retryCreateDeliveryRequest) {
        User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
        CreateDeliveryRequest request = new CreateDeliveryRequest();
        request.setTenantId(user.getTenantId());
        request.setOrderId(retryCreateDeliveryRequest.getOrderId());
        return request;
    }

    public QueryDeliveryOrderResponse queryDeliveryInfoByOrderId(Long orderId){
        QueryDeliveryOrderRequest request=new QueryDeliveryOrderRequest();
        request.setOrderId(orderId);
        request.setMasterOnly(false);
        try {
            QueryDeliveryOrderResponse response=queryDeliveryInfoThriftService.queryDeliveryOrderByOrderId(request);
            return response;
        }catch (Exception e){
            log.error("queryDeliveryInfoByOrderId error orderId:{}",orderId,e);
        }
        return null;
    }
}
