package com.sankuai.shangou.qnh.orderapi.domain.vo.app;

import com.facebook.swift.codec.ThriftField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.SubProductVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2020-02-17 16:33
 * @Description:
 */
@TypeDoc(
        description = "礼袋部分退款可退商品"
)
@ApiModel("礼袋部分退款可退商品")
@Data
public class GiftBagPartRefundCheckVO {


    @FieldDoc(
            description = "货品ID"
    )
    @ApiModelProperty(name = "货品ID", required = true)
    public String materialSkuId;

    @FieldDoc(
            description = "商品名称"
    )
    @ApiModelProperty(name = "商品名称", required = true)
    public String materialSkuName;
    @FieldDoc(
            description = "可退数量"
    )
    @ApiModelProperty(name = "可退数量", required = true)
    public Integer canRefundCount;


    @FieldDoc(
            description = "主品sku"
    )
    @ApiModelProperty(name = "主品sku", required = true)
    public String belongSkuId;

    @FieldDoc(
            description = "下单时的数量"
    )
    @ApiModelProperty(name = "下单时的数量", required = true)
    public int cnt;

    @FieldDoc(
            description = "1:品牌礼袋， 2:歪马礼袋"
    )
    @ApiModelProperty(name = "1:品牌礼袋， 2:歪马礼袋", required = true)
    public int type;

    @FieldDoc(
            description = "图片"
    )
    @ApiModelProperty(name = "图片", required = true)
    public String picUrl;

    @FieldDoc(
            description = "upc"
    )
    @ApiModelProperty(name = "upc", required = true)
    public String upc;


    @FieldDoc(
            description = "是否缺货品"
    )
    @ApiModelProperty(name = "是否缺货品", required = true)
    public Boolean isIncludeStockLackGoods;

}

