package com.sankuai.shangou.qnh.orderapi.converter.app;

import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.TagInfoVO;
import com.sankuai.shangou.qnh.orderapi.enums.app.ActionTagTypeEnum;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class ActionTagConverter {

    public static List<TagInfoVO> ocmsOrderVoConvertTag(OCMSOrderVO ocmsOrderVO){
        if(ocmsOrderVO==null){
            return Collections.emptyList();
        }
        List<TagInfoVO> tagInfoVOList=new ArrayList<>();
        if(ocmsOrderVO.getIsNeedInvoice()!=null && ocmsOrderVO.getIsNeedInvoice()==1){
            TagInfoVO tagInfoVO=new TagInfoVO();
            tagInfoVO.setName(ActionTagTypeEnum.INVOICE.getTemplate());
            tagInfoVO.setType(ActionTagTypeEnum.INVOICE.getType());
            tagInfoVOList.add(tagInfoVO);
        }
        return tagInfoVOList;
    }

}
