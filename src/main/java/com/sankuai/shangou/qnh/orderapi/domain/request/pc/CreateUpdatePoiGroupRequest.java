package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import java.util.List;

import com.meituan.servicecatalog.api.annotations.TypeDoc;

import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import lombok.Data;

/**
 * @Author: wang<PERSON>an
 * @Date: 2022/07/27
 * @Description:
 */
@TypeDoc(
        name = "创建或者修改门店分组请求对象",
        description = "创建或者修改门店分组请求对象"
)
@Data
public class CreateUpdatePoiGroupRequest implements BaseRequest {

    Integer poiGroupId;

    String poiGroupName;

    List<Long> addPoiIds;
}
