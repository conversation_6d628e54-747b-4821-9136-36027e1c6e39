package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * GetDeliveryLogReq
 *
 * <AUTHOR>
 * @since 2023/3/21
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class GetDeliveryLogReq extends BaseOrderReq {

    @FieldDoc(description = "门店ID", requiredness = Requiredness.REQUIRED)
    @NotNull(message = "门店id不能为null")
    private Long storeId;

}
