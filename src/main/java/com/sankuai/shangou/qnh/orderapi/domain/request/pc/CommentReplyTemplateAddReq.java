package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.exception.pc.ParamInvalidException;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@TypeDoc(
        description = "添加评价回复模板请求参数"
)
@Data
public class CommentReplyTemplateAddReq {

    @FieldDoc(
            description = "模板内容", requiredness = Requiredness.REQUIRED
    )
    private String templateContent;

    @FieldDoc(
            description = "模版生效门店", requiredness = Requiredness.OPTIONAL
    )
    private List<Long> storeIds;

    public void selfCheck() {
        if (StringUtils.isEmpty(this.templateContent)) {
            throw new ParamInvalidException("模板内容不能为空");
        }
    }
}