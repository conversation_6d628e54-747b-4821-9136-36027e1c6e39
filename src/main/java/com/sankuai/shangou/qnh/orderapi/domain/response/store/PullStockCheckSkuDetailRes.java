package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.StockCheckSkuItem;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "下载单据详情响应",
        authors = {
                "qianteng"
        }
)
@Data
public class PullStockCheckSkuDetailRes {
    @FieldDoc(
            description = "页面大小"
    )
    private Integer pageSize;

    @FieldDoc(
            description = "页数"
    )
    private Integer pageNo;

    @FieldDoc(
            description = "总数"
    )
    private Integer totalCount;

    @FieldDoc(
            description = "sku列表"
    )
    private List<StockCheckSkuItem> skuList;
    @FieldDoc(
            description = "是否是便利店"
    )
    @ApiModelProperty(name = "是否是便利店")
    private boolean convenienceStore;
}
