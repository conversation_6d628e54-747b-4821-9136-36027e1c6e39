package com.sankuai.shangou.qnh.orderapi.remote;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryChannelEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.FailureCodeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.DeliveryChannelThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.dto.DeliveryChannelDto;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.request.DeliveryChannelBatchQueryByCarrierCodeRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.deliverychannel.response.DeliveryChannelBatchQueryResponse;
import com.sankuai.shangou.qnh.orderapi.enums.pc.AggDeliveryPlatformEnum;
import com.sankuai.shangou.qnh.orderapi.utils.pc.MccConfigUtil;
import com.sankuai.shangou.qnh.orderapi.utils.pc.RetryTemplateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description:
 * @Auther: nifei
 * @Date: 2023/4/7 18:11
 */
@Slf4j
@Service
public class DeliveryChannelRemoteService {

    @Autowired
    private DeliveryChannelThriftService deliveryChannelThriftService;

    private static final int MAX_RETRY_COUNT = 3;
    private static final int BACK_OFF_PERIOD = 100;
    private static final Integer UNKNOWN_DELIVERY_PLATFORM_CODE = -1;
    private static final String UNKNOWN_CARRIER_NAME = "未知承运商";

    /**
     * 检查某平台是否已配置该渠道
     * @param deliveryChannelCode,deliveryPlatFormCode
     * @return 包含该配置返回true，否则返回false
     */
    public boolean checkChannel(Integer deliveryChannelCode, Integer deliveryPlatFormCode, Long tenantId, Long storeId, Map<Integer,Integer> channelMap){
        if(deliveryChannelCode == null || deliveryPlatFormCode == null){
            return false;
        }
        try {
            if (MapUtils.isEmpty(channelMap) || this.checkTenantAndStore(tenantId,storeId)){
                AggDeliveryPlatformEnum aggDeliveryPlatformEnum = AggDeliveryPlatformEnum.codeValueOf(deliveryPlatFormCode);
                log.info("DeliveryChannelWrapper checkChannel from enum");
                return Objects.nonNull(aggDeliveryPlatformEnum) && aggDeliveryPlatformEnum.checkChanel(deliveryChannelCode);
            }else {
                log.info("DeliveryChannelWrapper checkChannel from channelMap");
                return channelMap.containsKey(deliveryChannelCode) && deliveryPlatFormCode.equals(channelMap.get(deliveryChannelCode));
            }
        }catch (Exception e){
            log.error("DeliveryChannelWrapper checkChannel error",e);
            AggDeliveryPlatformEnum aggDeliveryPlatformEnum = AggDeliveryPlatformEnum.codeValueOf(deliveryPlatFormCode);
            return Objects.nonNull(aggDeliveryPlatformEnum) && aggDeliveryPlatformEnum.checkChanel(deliveryChannelCode);
        }
    }


    public Map<Integer,String> getDeliveryChannelNameMap(Set<Integer> channelCodes){
        HashMap<Integer, String> channelMap = new HashMap<>();
        if (CollectionUtils.isEmpty(channelCodes)){
            return channelMap;
        }
        DeliveryChannelBatchQueryByCarrierCodeRequest request = new DeliveryChannelBatchQueryByCarrierCodeRequest();
        RetryTemplate retryTemplate = RetryTemplateUtil.simpleWithFixedRetry(MAX_RETRY_COUNT, BACK_OFF_PERIOD);
        request.setCarrierCodeSet(channelCodes);

        try {
            DeliveryChannelBatchQueryResponse response = retryTemplate.execute((RetryCallback<DeliveryChannelBatchQueryResponse, Exception>) retryContext -> deliveryChannelThriftService.batchQueryDeliveryChannelByCarrierCodeList(request));
            if (Objects.isNull(response)|| response.getStatus().getCode() != FailureCodeEnum.SUCCESS.getCode() || CollectionUtils.isEmpty(response.getDeliveryChannelDtoList())) {
                Arrays.stream(DeliveryChannelEnum.values())
                        .filter(item -> Objects.nonNull(item.getDeliveryPlatform()))
                        .forEach(e -> channelMap.put(e.getCode(), e.getName()));
                log.info("DeliveryChannelWrapper getDeliveryChannelNameMap from enum");
                return channelMap;
            }
            log.info("DeliveryChannelWrapper getDeliveryChannelNameMap from rpc");
            return response.getDeliveryChannelDtoList()
                    .stream()
                    .filter(channel -> Objects.nonNull(channel) && Objects.nonNull(channel.getCarrierCode()))
                    .collect(Collectors.toMap(DeliveryChannelDto::getCarrierCode, DeliveryChannelDto::getCarrierName));
        }catch (Exception e){
            log.error("DeliveryChannelService getDeliveryChannelNameMap error :",e);
            Arrays.stream(DeliveryChannelEnum.values())
                    .filter(item -> Objects.nonNull(item.getDeliveryPlatform()))
                    .forEach(a -> channelMap.put(a.getCode(), a.getName()));
            return channelMap;
        }
    }


    /**
     * store方法
     */
    public String getDeliveryChannelName(Long tenantId, Long storeId,Integer deliveryChannel, Map<Integer, String> deliveryChannelMap) {

        if (Objects.isNull(deliveryChannel)){
            log.error("返回“未知承运商” ，deliveryChannel is null");
            return UNKNOWN_CARRIER_NAME;
        }

        try {
            if (MapUtils.isEmpty(deliveryChannelMap) || this.checkTenantAndStore(tenantId, storeId)) {
                log.info("DeliveryChannelWrapper getDeliveryChannelName from enum");
                return Objects.isNull(DeliveryChannelEnum.valueOf(deliveryChannel))?UNKNOWN_CARRIER_NAME:DeliveryChannelEnum.valueOf(deliveryChannel).getName();
            }
            log.info("DeliveryChannelWrapper getDeliveryChannelName from deliveryChannelMap");
            return deliveryChannelMap.getOrDefault(deliveryChannel,Objects.isNull(DeliveryChannelEnum.valueOf(deliveryChannel))?UNKNOWN_CARRIER_NAME:DeliveryChannelEnum.valueOf(deliveryChannel).getName());
        } catch (Exception e) {
            log.error("DeliveryChannelWrapper getDeliveryChannelName error :",e);
            return Objects.isNull(DeliveryChannelEnum.valueOf(deliveryChannel))?UNKNOWN_CARRIER_NAME:DeliveryChannelEnum.valueOf(deliveryChannel).getName();
        }
    }


    /**
     *
     * @param tenantId
     * @param storeId
     * @return 全局开关打开 或 歪马租户 或 非灰度门店 走枚举类.返回true
     */
    public boolean checkTenantAndStore(Long tenantId, Long storeId){
        if (Objects.isNull(tenantId) && Objects.isNull(storeId)){
            return true;
        }
        // 如果是全局开关打开 或 歪马租户 或 非灰度门店，走之前枚举类的逻辑
        return MccConfigUtil.isDeliveryChannelQuery4Enum() || MccConfigUtil.getDHTenantIdList().contains(String.valueOf(tenantId)) || !MccConfigUtil.isDeliveryChannelConfigStore(storeId);
    }

    public Map<Integer,Integer> tratranslateToChannelIntgerMap(Map<Integer,DeliveryChannelDto> channelDtoMap){
        return channelDtoMap.entrySet().stream().filter(e -> Objects.nonNull(e.getValue())).collect(Collectors.toMap(Map.Entry::getKey,entry -> entry.getValue().deliveryPlatFormCode));
    }


    public Map<Integer,String> tratranslateToChannelNameMap(Map<Integer,DeliveryChannelDto> channelDtoMap){
        return channelDtoMap.entrySet().stream().filter(e -> Objects.nonNull(e.getValue())).collect(Collectors.toMap(Map.Entry::getKey,entry -> entry.getValue().getCarrierName()));
    }

    public Map<Integer,DeliveryChannelDto> getDeliveryChannelDtoMap(Set<Integer> channelCodes){
        HashMap<Integer, DeliveryChannelDto> channelMap = new HashMap<>();
        if (CollectionUtils.isEmpty(channelCodes)){
            return channelMap;
        }
        RetryTemplate retryTemplate = RetryTemplateUtil.simpleWithFixedRetry(MAX_RETRY_COUNT, BACK_OFF_PERIOD);
        DeliveryChannelBatchQueryByCarrierCodeRequest request = new DeliveryChannelBatchQueryByCarrierCodeRequest();
        request.setCarrierCodeSet(channelCodes);
        try {
            DeliveryChannelBatchQueryResponse response = retryTemplate.execute((RetryCallback<DeliveryChannelBatchQueryResponse, Exception>) retryContext -> deliveryChannelThriftService.batchQueryDeliveryChannelByCarrierCodeList(request));

            if (Objects.isNull(response)|| response.getStatus().getCode() != FailureCodeEnum.SUCCESS.getCode() || CollectionUtils.isEmpty((response.getDeliveryChannelDtoList()))) {

                Arrays.stream(DeliveryChannelEnum.values())
                        .filter(item -> Objects.nonNull(item.getDeliveryPlatform()))
                        .forEach(e -> channelMap.put(e.getCode(), translateFromDeliveryChannelEnum(e.getCode())));
                log.info("DeliveryChannelWrapper getDeliveryChannelDtoMap from enum");
                return channelMap;
            }
            log.info("DeliveryChannelWrapper getDeliveryChannelDtoMap from rpc");
            return response.getDeliveryChannelDtoList()
                    .stream()
                    .filter(channel -> Objects.nonNull(channel) && Objects.nonNull(channel.getCarrierCode()))
                    .collect(Collectors.toMap(DeliveryChannelDto::getCarrierCode, Function.identity()));
        }catch (Exception e){
            log.error("DeliveryChannelService getDeliveryChannelDtoMap error",e);
            Arrays.stream(DeliveryChannelEnum.values())
                    .filter(item -> Objects.nonNull(item.getDeliveryPlatform()))
                    .forEach(a -> channelMap.put(a.getCode(), translateFromDeliveryChannelEnum(a.getCode())));
            return channelMap;
        }
    }

    private DeliveryChannelDto getUnknownDeliveryChannel(Integer carrierCode) {
        return DeliveryChannelDto.builder()
                .logisticMark(StringUtils.EMPTY)
                .deliveryPlatFormCode(UNKNOWN_DELIVERY_PLATFORM_CODE)
                .carrierCode(carrierCode)
                .carrierName(UNKNOWN_CARRIER_NAME)
                .orderChannelCode(NumberUtils.INTEGER_ZERO).build();
    }

    private DeliveryChannelDto translateFromDeliveryChannelEnum(Integer carrierCode) {
        if (null == carrierCode){
            return null;
        }
        DeliveryChannelEnum deliveryChannelEnum = DeliveryChannelEnum.valueOf(carrierCode);
        if (Objects.isNull(deliveryChannelEnum)) {
            return getUnknownDeliveryChannel(carrierCode);
        }

        DeliveryChannelDto deliveryChannelDto = new DeliveryChannelDto();
        deliveryChannelDto.setLogisticMark(StringUtils.EMPTY);
        if (Objects.isNull(deliveryChannelEnum.getDeliveryPlatform())) {
            // 平台配送的deliveryPlatform为null
            deliveryChannelDto.setDeliveryPlatFormCode(UNKNOWN_DELIVERY_PLATFORM_CODE);
        } else {
            deliveryChannelDto.setDeliveryPlatFormCode(deliveryChannelEnum.getDeliveryPlatform().getCode());
        }
        deliveryChannelDto.setCarrierCode(deliveryChannelEnum.getCode());
        deliveryChannelDto.setCarrierName(deliveryChannelEnum.getName());
        deliveryChannelDto.setOrderChannelCode(NumberUtils.INTEGER_ZERO);

        return deliveryChannelDto;
    }
}
