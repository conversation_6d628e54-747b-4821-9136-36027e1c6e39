package com.sankuai.shangou.qnh.orderapi.remote;

import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.BoothOrderDetailReq;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.BoothOrderDetailResp;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.BoothOrderLineReq;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.BoothOrderLineResp;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.ChannelOrderTenantThriftService;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.GetOrderDetailForBootRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.store.GetOrderForBoothRequest;
import com.sankuai.shangou.qnh.orderapi.domain.response.store.CommonResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.GetOrderForBoothResponseVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.OrderForBoothVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.User;
import com.sankuai.shangou.qnh.orderapi.exception.store.CommonRuntimeException;
import com.sankuai.shangou.qnh.orderapi.utils.store.OCMSUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/4/11
 * desc: 中台商品库存服务
 */
@Service
@Slf4j
public class OCMSRemoteService {

    @Resource
    private ChannelOrderTenantThriftService.Iface channelOrderTenantThriftService;

    public CommonResponse<GetOrderForBoothResponseVO> getOrderForBoothList(User user, List<String> boothIdList, GetOrderForBoothRequest request) {
        BoothOrderLineReq req = new BoothOrderLineReq();
        req.setTenantId(user.getTenantId());
        req.setBoothIdList(boothIdList.stream().map(e -> Long.parseLong(e)).collect(Collectors.toList()));
        req.setBeginCreateTime(getBeginDate(request.getDate()));
        req.setEndCreateTime(getEndDate(request.getDate()));
        try {
            BoothOrderLineResp boothOrderLineResp = channelOrderTenantThriftService.boothOrderLineList(req);
            return CommonResponse.success(OCMSUtils.convertResponse(boothOrderLineResp));
        } catch (TException e) {
            throw new CommonRuntimeException(e);
        }
    }

    public CommonResponse<OrderForBoothVO> getOrderDetailForBooth(User user, List<String> boothIdList, GetOrderDetailForBootRequest getOrderDetailForBootRequest) {
        BoothOrderDetailReq req = new BoothOrderDetailReq();
        req.setTenantId(user.getTenantId());
        req.setChannelOrderId(getOrderDetailForBootRequest.getChannelOrderId());
        req.setBoothIdList(boothIdList.stream().map(e -> Long.parseLong(e)).collect(Collectors.toList()));

        try {
            BoothOrderDetailResp boothOrderDetailResp = channelOrderTenantThriftService.boothOrderDetail(req);
            return CommonResponse.success(OCMSUtils.convertResponse(boothOrderDetailResp));
        } catch (TException e) {
            throw new CommonRuntimeException(e);
        }
    }

    private Long getBeginDate(String beginDateString) {
        String format = "yyyy-MM-dd HH:mm:ss";
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
        simpleDateFormat.setLenient(false);
        Date beginDate;
        try {
            beginDate = simpleDateFormat.parse(beginDateString + " 00:00:00");
        } catch (ParseException e) {
            log.error("OCMSServiceWrapper.getBeginDate   时间解析错误:{}", beginDateString, e);
            throw new CommonRuntimeException("时间格式错误");
        }
        return Long.valueOf(beginDate.getTime());
    }

    private Long getEndDate(String endDateString) {
        String format = "yyyy-MM-dd HH:mm:ss";
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
        simpleDateFormat.setLenient(false);
        Date beginDate;
        try {
            beginDate = simpleDateFormat.parse(endDateString + " 23:59:59");
        } catch (ParseException e) {
            log.error("OCMSServiceWrapper.getBeginDate   时间解析错误:{}", endDateString, e);
            throw new CommonRuntimeException("时间格式错误");
        }
        return Long.valueOf(beginDate.getTime() + 1000);
    }

}
