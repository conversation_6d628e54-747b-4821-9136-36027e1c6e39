package com.sankuai.shangou.qnh.orderapi.service.common;

import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.PageInfoVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.PageInfoVO;

/**
 * <AUTHOR>
 * @since 2024/7/15
 **/
public class PageUtil {
    public static PageInfoVO buildPageInfoVO(Integer page, Integer size, Integer totalSize) {
        PageInfoVO pageInfoVO = new PageInfoVO();
        pageInfoVO.setPage(page);
        pageInfoVO.setSize(size);
        pageInfoVO.setTotalSize(totalSize);
        pageInfoVO.setTotalPage(totalSize % size == 0 ? totalSize / size : totalSize / size + 1);
        return pageInfoVO;
    }

    public static PageInfoVo buildPageInfoVo(Integer page, Integer size, Integer totalSize) {
        PageInfoVo pageInfoVO = new PageInfoVo();
        pageInfoVO.setPage(page);
        pageInfoVO.setSize(size);
        pageInfoVO.setTotalSize(totalSize);
        pageInfoVO.setTotalPage(totalSize % size == 0 ? totalSize / size : totalSize / size + 1);
        return pageInfoVO;
    }

    public static PageInfoVO buildPageInfoVO(PageInfoVo pageInfoVo) {
        PageInfoVO pageInfoVO = new PageInfoVO();
        pageInfoVO.setPage(pageInfoVo.page);
        pageInfoVO.setSize(pageInfoVo.size);
        pageInfoVO.setTotalSize(pageInfoVo.totalSize);
        pageInfoVO.setTotalPage(pageInfoVo.totalPage);
        return pageInfoVO;
    }

    public static PageInfoVO buildEmptyPageInfoVO() {
        PageInfoVO pageInfoVO = new PageInfoVO();
        pageInfoVO.setPage(1);
        pageInfoVO.setSize(0);
        pageInfoVO.setTotalSize(0);
        pageInfoVO.setTotalPage(0);
        return pageInfoVO;
    }
}
