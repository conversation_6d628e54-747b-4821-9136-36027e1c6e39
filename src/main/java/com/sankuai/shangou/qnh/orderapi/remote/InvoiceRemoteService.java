package com.sankuai.shangou.qnh.orderapi.remote;

import com.google.common.collect.Lists;
import com.meituan.shangou.saas.order.management.client.enums.StatusCodeEnum;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.request.invoice.OrderInvoiceApplyRecordRequest;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.response.invoice.OrderInvoiceApplyRecordListResponse;
import com.sankuai.sgxsupply.wxmall.bizmanagement.client.thrift.InvoiceRecordQueryThriftService;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.InvoiceApplyRecordBO;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class InvoiceRemoteService {

    @Resource
    InvoiceRecordQueryThriftService invoiceRecordQueryThriftService;

    public List<InvoiceApplyRecordBO> queryInvoiceRecord(String viewOrderId) {
        try {
            OrderInvoiceApplyRecordListResponse response = invoiceRecordQueryThriftService.queryOrderInvoiceApplyRecordList(new OrderInvoiceApplyRecordRequest(viewOrderId));
            if (response != null && response.getCode() == StatusCodeEnum.SUCCESS.getCode()) {
                return ConverterUtils.convertList(response.getInvoiceApplyRecordList(), InvoiceApplyRecordBO::buildInvoiceApplyRecordBO);
            }
        } catch (Exception ex) {
            log.error("queryInvoiceRecord error, viewOrderId:{}", viewOrderId, ex);
        }
        return Lists.newArrayList();

    }




}
