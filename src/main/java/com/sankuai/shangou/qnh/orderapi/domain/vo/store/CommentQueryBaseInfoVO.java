package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 评价查询条件信息
 *
 * <AUTHOR>
 */
@TypeDoc(
        description = "评价查询基本信息"
)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CommentQueryBaseInfoVO {
    @FieldDoc(
            description = "渠道信息列表", requiredness = Requiredness.OPTIONAL
    )
    private List<ChannelInfoVO> channelInfoVOList;
}
