package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.TimeSlotDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;


@TypeDoc(
        description = "时间段"
)
@Data
@ApiModel("时间段")
@NoArgsConstructor
public class TimeSlotVO {

    @FieldDoc(
            description = "开始时间，参考\"09:00\"", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "开始时间", required = true)
    private String startTime;

    @FieldDoc(
            description = "结束时间，参考\"09:30\"", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "结束时间", required = true)
    private String endTime;

    public TimeSlotDTO buildTimeSlotDTO(){
        TimeSlotDTO timeSlotDTO = new TimeSlotDTO();
        timeSlotDTO.setStartTime(this.getStartTime());
        timeSlotDTO.setEndTime(this.getEndTime());
        return timeSlotDTO;
    }

    public TimeSlotVO(TimeSlotDTO timeSlotDTO){
        this.startTime = timeSlotDTO.getStartTime();
        this.endTime = timeSlotDTO.getEndTime();
    }
}
