package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.OrderDetailVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.RefundReasonAndCodeVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/7/16
 * desc: 检查退款响应
 */
@TypeDoc(
        description = "检查退款请求响应"
)
@ApiModel("检查退款请求响应")
@Data
public class CheckRefundResponse {

    @FieldDoc(
            description = "退款理由列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "退款理由列表", required = true)
    private List<RefundReasonAndCodeVO> refundReasons;

    @FieldDoc(
            description = "订单详情", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "订单详情", required = false)
    private OrderDetailVO orderInfo;
}
