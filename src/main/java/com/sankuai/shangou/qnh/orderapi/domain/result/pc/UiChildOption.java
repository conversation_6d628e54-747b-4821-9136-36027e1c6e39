package com.sankuai.shangou.qnh.orderapi.domain.result.pc;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * @Author: <EMAIL>
 * @Date: 2022/12/02 19:50
 * @Description:
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode
public class UiChildOption {

    private Integer value;

    private String labelId;

    private String code;

    private Integer parentId;

    private String parentIdStr;

    private String desc;

    public UiChildOption(Integer value, String code, Integer parentId, String desc) {
        this.value = value;
        this.code = code;
        this.parentId = parentId;
        this.desc = desc;
    }

    public UiChildOption(String labelId, String code, String parentId, String desc) {
        this.labelId = labelId;
        this.code = code;
        this.parentIdStr = parentId;
        this.desc = desc;
    }

}
