package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2019/8/26
 * desc:
 */
@TypeDoc(
        description = "查询渠道品牌请求"
)
@Data
@ApiModel("查询渠道品牌请求")
public class GetChannelBrandRequest {

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID", required = true)
    @NotNull
    private Integer channelId;

    @FieldDoc(
            description = "品牌名称(模糊查询)   不传代表查询所有", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "品牌名称")
    private String brandName;

    @FieldDoc(
            description = "页号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "页号", required = true)
    @NotNull
    private Integer page;

    @FieldDoc(
            description = "每页行数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "每页行数", required = true)
    @NotNull
    private Integer size;
}
