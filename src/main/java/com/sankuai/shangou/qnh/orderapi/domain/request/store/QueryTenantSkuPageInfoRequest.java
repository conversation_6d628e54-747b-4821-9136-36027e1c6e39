package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/6/12
 * desc:
 */
@TypeDoc(
        description = "分页查询租户商品列表请求"
)
@Data
@ApiModel("分页查询租户商品列表请求")
public class QueryTenantSkuPageInfoRequest {

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID")
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "关键词", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "关键词")
    private String keyword;

    @FieldDoc(
            description = "后台分类code列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "后台分类code列表")
    private List<String> categoryCodes;

    @FieldDoc(
            description = "当前页数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "当前页数", required = true)
    @NotNull
    private Integer pageNum;

    @FieldDoc(
            description = "每页条数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "每页条数", required = true)
    @NotNull
    private Integer pageSize;
}
