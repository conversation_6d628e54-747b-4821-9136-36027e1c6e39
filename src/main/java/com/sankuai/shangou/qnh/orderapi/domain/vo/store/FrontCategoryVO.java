package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/6/12
 * desc:
 */
@TypeDoc(
        description = "前台分类信息"
)
@Data
@ApiModel("前台分类信息")
public class FrontCategoryVO {

    @FieldDoc(
            description = "分类ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "分类ID", required = true)
    private Long id;

    @FieldDoc(
            description = "分类名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "分类名称", required = true)
    private String name;

    @FieldDoc(
            description = "分类层级", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "分类层级", required = true)
    private Integer level;

    @FieldDoc(
            description = "父分类ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "父分类ID", required = true)
    private Long parentId;

    @FieldDoc(
            description = "排序", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "排序", required = true)
    private Integer sort;

    @FieldDoc(
            description = "是否有商品   1-是 0-否", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "是否有商品", required = true)
    private Integer hasSku;

    @FieldDoc(
            description = "下一级分类数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "下一级分类数量", required = true)
    private Integer subAmount;

    @FieldDoc(
            description = "门店商品分类ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店商品分类ID", required = true)
    private String storeCategoryId;

    @FieldDoc(
            description = "渠道ID  -1-线下 100-美团 200-饿了么 300-京东到家", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID  -1-线下 100-美团 200-饿了么 300-京东到家", required = true)
    private Integer channelId;
}
