package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.constant.pc.CommercialAgentConstant;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 合作商配置请求
 *
 * @<AUTHOR>
 * @since 2020/09/16
 */
@TypeDoc(
        description = "合作商配置请求"
)
@ApiModel("合作商配置请求")
@Data
public class CommercialAgentConfigRequest implements BaseRequest {

    @FieldDoc(
            description = "合作商id"
    )
    @ApiModelProperty(value = "合作商id")
    private Long commercialAgentId;

    @FieldDoc(
            description = "是否启用外采现结(0否1是)"
    )
    @ApiModelProperty(value = "是否启用外采现结(0否1是)")
    private Integer useRealTimeSettle;

    @FieldDoc(
            description = "结算价限额(不限额传递-1)"
    )
    @ApiModelProperty(value = "结算价限额(不限额传递-1)")
    private Double settleLimitRatio;


    @FieldDoc(
            description = "支付宝appId"
    )
    @ApiModelProperty(value = "支付宝appId")
    private String alipayAppId;

    @FieldDoc(
            description = "支付宝应用私钥(未变化时传递******)"
    )
    @ApiModelProperty(value = "支付宝应用私钥")
    private String alipayPrivateKey;

    @FieldDoc(
            description = "应用公钥证书(未变化时传递******)"
    )
    @ApiModelProperty(value = "应用公钥证书")
    private String appPublicKeyCert;

    @FieldDoc(
            description = "支付宝公钥证书(未变化时传递******)"
    )
    @ApiModelProperty(value = "支付宝公钥证书")
    private String alipayPublicKeyCert;

    @FieldDoc(
            description = "支付宝根证书(未变化时传递******)"
    )
    @ApiModelProperty(value = "支付宝根证书")
    private String alipayRootCert;

    @FieldDoc(
            description = "前端加密后的对称加密秘钥"
    )
    @ApiModelProperty(value = "前端加密后的对称加密秘钥")
    private String frontKey;

    @Override
    public void selfCheck() {
        AssertUtil.notNull(commercialAgentId, "合作商id不能为空");
        AssertUtil.betweenClose(useRealTimeSettle, 0, 1, "是否启用外采现结不能为空");
        AssertUtil.notNull(settleLimitRatio, "结算价限额不能为空");
        AssertUtil.notEmpty(alipayAppId, "支付宝appId不能为空");
        AssertUtil.notEmpty(alipayPrivateKey, "支付宝应用私钥不能为空");
        AssertUtil.notEmpty(appPublicKeyCert, "应用公钥证书不能为空");
        AssertUtil.notEmpty(alipayPublicKeyCert, "支付宝公钥证书不能为空");
        AssertUtil.notEmpty(alipayRootCert, "支付宝根证书不能为空");
        AssertUtil.notEmpty(frontKey, "前端秘钥不能为空");
    }


    /**
     * 判断密钥及证书是否有变化
     *
     * @return
     */
    public boolean hasKeyOrCertChanged() {
        return !CommercialAgentConstant.NONE_CHANGE_FILED.equals(alipayPrivateKey)
                || !CommercialAgentConstant.NONE_CHANGE_FILED.equals(appPublicKeyCert)
                || !CommercialAgentConstant.NONE_CHANGE_FILED.equals(alipayPublicKeyCert)
                || !CommercialAgentConstant.NONE_CHANGE_FILED.equals(alipayRootCert);
    }
}
