package com.sankuai.shangou.qnh.orderapi.interceptor.app;

import com.meituan.shangou.saas.tenant.thrift.ConfigThriftService;
import com.meituan.shangou.saas.tenant.thrift.dto.config.request.TenantSwitchGetRequest;
import com.meituan.shangou.saas.tenant.thrift.dto.config.response.TenantSwitchGetResponse;
import com.sankuai.shangou.qnh.orderapi.context.app.ApiMethodParamThreadLocal;
import com.sankuai.shangou.qnh.orderapi.domain.dto.app.User;
import com.sankuai.shangou.qnh.orderapi.enums.app.ResultCodeEnum;
import com.sankuai.shangou.qnh.orderapi.exception.app.CommonLogicException;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import com.sankuai.shangou.qnh.orderapi.annotation.app.Auth;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Create by zhangbo86 on 2020/5/9.
 */
@Slf4j
@Component
public class StandardSpecInterceptor extends HandlerInterceptorAdapter {
    @Resource
    private ConfigThriftService configThriftService;
    private static final String StandeSpecSwitch = "1";//灰度开关
    private static final String standardSpecPara = "SPEC_STANDARD";  //查询灰度开启的参数

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;

            Class<?> type = handlerMethod.getBeanType();

            AuthInfo authInfo = new AuthInfo(type.getDeclaredAnnotation(Auth.class),
                    handlerMethod.getMethodAnnotation(Auth.class));

            if (!authInfo.authenticate()) {
                return true;
            }
            User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
            if (user == null) {
                return true;
            }
            TenantSwitchGetRequest tenantSwitchGetRequest =new TenantSwitchGetRequest();
            List<String> switchKey = new ArrayList<>();
            tenantSwitchGetRequest.setTenantId(user.getTenantId());
            switchKey.add(standardSpecPara);
            tenantSwitchGetRequest.setSwitchKey(switchKey);
            TenantSwitchGetResponse switchGetResponse = configThriftService.getTenantSwitch(tenantSwitchGetRequest);
            Map<String, String> switchValue = switchGetResponse.getSwitchValue();
            log.info("pre handle api method, switchValue={}", switchValue.get(standardSpecPara));
            if(MccConfigUtil.getinterceptor_enable()){
                if (switchValue.get(standardSpecPara) !=null && switchValue.get(standardSpecPara).equals(StandeSpecSwitch)){
                    log.info("pre handle api method, url={}, switch={}, request ={}", request.getRequestURI(), switchValue.get(standardSpecPara), ApiMethodParamThreadLocal.getIdentityInfo());
                    throw new CommonLogicException(MccConfigUtil.getErrorInformation(), ResultCodeEnum.FAIL);
                }
            }
        }
        return true;
    }

    private static final class AuthInfo {
        private final boolean authenticate;

        private AuthInfo(Auth authOnClass, Auth authOnMethod) {

            if (authOnClass == null && authOnMethod == null) {
                authenticate = false;
            } else if (authOnMethod != null) {
                authenticate = authOnMethod.authenticate();
            } else {
                authenticate = authOnClass.authenticate();
            }
        }

        public boolean authenticate() {
            return authenticate;
        }
    }
}
