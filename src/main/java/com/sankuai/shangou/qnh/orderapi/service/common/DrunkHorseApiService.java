package com.sankuai.shangou.qnh.orderapi.service.common;


import com.sankuai.qnh.ofc.ofw.client.thrift.request.order.ChannelOrderIdKeyReq;
import com.sankuai.shangou.logistics.delivery.gray.enums.GrayKeyEnum;
import com.sankuai.shangou.logistics.delivery.gray.utils.GrayConfigUtils;
import com.sankuai.shangou.logistics.warehouse.dto.AbnOrderDTO;
import com.sankuai.shangou.qnh.orderapi.context.app.ApiMethodParamThreadLocal;
import com.sankuai.shangou.qnh.orderapi.converter.store.ChannelOrderConverter;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.OrderListRequest;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.OrderListResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.OrderVO;
import com.sankuai.shangou.qnh.orderapi.remote.AbnOrderRemoteService;
import com.sankuai.shangou.qnh.orderapi.remote.ChannelOrderRemoteService;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import com.sankuai.shangou.qnh.orderapi.utils.store.MccDynamicConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/7/15
 **/
@Slf4j
@Service
public class DrunkHorseApiService {
    @Resource
    private ChannelOrderRemoteService channelOrderRemoteService;
    @Resource
    private AbnOrderRemoteService abnOrderServiceWrapper;

    public void appendSealTag(Long tenantId, Long storeId, OrderListResponse orderListResponse) {
        try {
            if(!MccConfigUtil.isDrunkHorseTenant(tenantId)) {
                return;
            }
            if(!GrayConfigUtils.judgeIsGrayStore(tenantId, storeId, GrayKeyEnum.SEAL_DELIVERY.getGrayKey(), false) || orderListResponse == null) {
                return;
            }
            List<ChannelOrderIdKeyReq> orderKeyList = Optional.ofNullable(orderListResponse.getOrderList()).orElse(Collections.emptyList()).stream()
                    .map(order -> new ChannelOrderIdKeyReq(ChannelOrderConverter.convertChannelId2OrderBizType(order.getChannelId()), order.getChannelOrderId()))
                    .collect(Collectors.toList());
            Map<String, Boolean> selfOrderMap = channelOrderRemoteService.getSelfDeliveryOrderMap(tenantId, storeId, orderKeyList);
            if(MapUtils.isEmpty(selfOrderMap)) {
                return;
            }
            Optional.ofNullable(orderListResponse.getOrderList()).orElse(Collections.emptyList()).stream().forEach(order ->
                    order.setIsSealDelivery(selfOrderMap.getOrDefault(order.getChannelOrderId(), false)));
        } catch (Exception ex) {
            log.error("appendSealTag error,orderListResponse:{}", orderListResponse, ex);
        }

    }

    public void appendLackTag(OrderListRequest request, OrderListResponse orderListResponse) {
        try {
            if (!MccDynamicConfigUtil.getWmsjTenantIds().contains((ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId()))
                    || !MccDynamicConfigUtil.isNewPickGrayStore(request.getStoreId())) {
                return;
            }
            List<AbnOrderDTO> allUnprocessedAbnOrder = abnOrderServiceWrapper.getAllUnprocessedAbnOrder(request.getStoreId());
            List<AbnOrderDTO> filteredUnprocessedAbnOrder  = Optional.ofNullable(allUnprocessedAbnOrder)
                    .orElse(Lists.newArrayList())
                    .stream()
                    .filter(abnOrderDTO -> CollectionUtils.isNotEmpty(abnOrderDTO.getItems()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filteredUnprocessedAbnOrder)) {
                Map<String, AbnOrderDTO> channelOrderIdMap = filteredUnprocessedAbnOrder
                        .stream()
                        .collect(Collectors.toMap(AbnOrderDTO::getSourceOrderNo, java.util.function.Function.identity(), (older, newer) -> newer));
                for (OrderVO orderVO : orderListResponse.getOrderList()) {
                    orderVO.setHasLackGoods(channelOrderIdMap.containsKey(orderVO.getChannelOrderId()));
                }
            }
        } catch (Exception e) {
            log.error("appendLackTag error", e);
        }
    }

    public void appendLackTag(Long storeId, OrderListResponse orderListResponse) {
        try {
            if (!MccDynamicConfigUtil.getWmsjTenantIds().contains((ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId()))
                    || !MccDynamicConfigUtil.isNewPickGrayStore(storeId)) {
                return;
            }
            List<AbnOrderDTO> allUnprocessedAbnOrder = abnOrderServiceWrapper.getAllUnprocessedAbnOrder(storeId);
            List<AbnOrderDTO> filteredUnprocessedAbnOrder  = Optional.ofNullable(allUnprocessedAbnOrder)
                    .orElse(Lists.newArrayList())
                    .stream()
                    .filter(abnOrderDTO -> CollectionUtils.isNotEmpty(abnOrderDTO.getItems()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filteredUnprocessedAbnOrder)) {
                Map<String, AbnOrderDTO> channelOrderIdMap = filteredUnprocessedAbnOrder
                        .stream()
                        .collect(Collectors.toMap(AbnOrderDTO::getSourceOrderNo, java.util.function.Function.identity(), (older, newer) -> newer));
                for (OrderVO orderVO : orderListResponse.getOrderList()) {
                    orderVO.setHasLackGoods(channelOrderIdMap.containsKey(orderVO.getChannelOrderId()));
                }
            }
        } catch (Exception e) {
            log.error("appendLackTag error", e);
        }
    }

}
