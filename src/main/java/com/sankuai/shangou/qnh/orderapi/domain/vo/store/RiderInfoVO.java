package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TypeDoc(
        description = "骑手信息"
)
@ApiModel("骑手信息")
@Data
public class RiderInfoVO {

    @FieldDoc(
            description = "渠道信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道信息", required = true)
    private String deliveryChannelName;

    @FieldDoc(
            description = "骑手姓名", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "骑手姓名", required = true)
    private String riderName;

    @FieldDoc(
            description = "骑手电话", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "骑手电话", required = true)
    private String riderPhone;

    @FieldDoc(
            description = "状态描述", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "状态描述", required = true)
    private String statusDes;

    @FieldDoc(
            description = "第N次配送", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "第N次配送", required = true)
    private String deliveryCount;

}
