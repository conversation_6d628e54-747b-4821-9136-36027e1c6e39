package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2019/7/11
 * desc: 查询虚拟电话请求
 */
@TypeDoc(
        description = "查询虚拟电话请求"
)
@ApiModel("查询虚拟电话请求")
@Data
public class QueryVirtualPhoneRequest {

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道订单号")
    @NotNull
    private String channelOrderId;

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID")
    @NotNull
    private Integer channelId;

    @FieldDoc(
            description = "查询电话类型  1-用户 2-骑手", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "查询电话类型  1-用户 2-骑手")
    @NotNull
    private Integer phoneType;

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID")
    private Long storeId;

    @FieldDoc(
            description = "门店类型 3-门店 5-中心仓 6-前置仓", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店类型")
    private Integer entityType;


    @FieldDoc(
            description = "订单类型 0-正单 1-退单", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "订单类型")
    private Integer orderType;


    @FieldDoc(
            description = "退单号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "退单号")
    private String afterSaleId;
}
