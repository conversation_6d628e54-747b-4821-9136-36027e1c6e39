package com.sankuai.shangou.qnh.orderapi.domain.response.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/08/15
 * desc: 查询订单列表统计数据响应
 */
@TypeDoc(
        description = "查询订单列表统计数据响应"
)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("查询订单列表统计数据响应")
public class OrderFuseStatisticsResponse {

    private final static String defaultSumString = "-";

    @FieldDoc(
            description = "预计收入金额", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "预计收入金额", required = true)
    @Builder.Default
    private String bizReceiveAmtSum = defaultSumString;

    @FieldDoc(
            description = "用户实付金额", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "用户实付金额", required = true)
    @Builder.Default
    private String actualPayAmtSum = defaultSumString;
}
