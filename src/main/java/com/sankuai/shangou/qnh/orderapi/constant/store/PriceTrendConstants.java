package com.sankuai.shangou.qnh.orderapi.constant.store;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * 价格趋势权限constant
 *
 * <AUTHOR>
 */
public class PriceTrendConstants {

    /**
     * 线下基准价格趋势权限码
     */
    public static String CITY_BASE_PRICE_TREND_PERMISSION_CODE = "city_base_price_trend";

    /**
     * 线上基准价格趋势权限码
     */
    public static String CHANNEL_CITY_BASE_PRICE_TREND_PERMISSION_CODE = "channel_city_base_price_trend";

    /**
     * 市调价格趋势权限码
     */
    public static String MR_PRICE_TREND_PERMISSION_CODE = "mr_price_trend";

    /**
     * 线下价格趋势权限码
     */
    public static String STORE_PRICE_TREND_PERMISSION_CODE = "store_price_trend";

    /**
     * 门店商品价格趋势默认查询天数
     */
    public static final int STORE_SKU_PRICE_TREND_DEFAULT_QUERY_DAYS = 15;


    /**
     * 获取价格趋势权限码列表
     *
     * @return 价格趋势权限码列表
     */
    public static List<String> getPriceTrendPermissionCodes() {
        return Lists.newArrayList(CITY_BASE_PRICE_TREND_PERMISSION_CODE, CHANNEL_CITY_BASE_PRICE_TREND_PERMISSION_CODE,
                MR_PRICE_TREND_PERMISSION_CODE);
    }

    /**
     * 提报价价格趋势权限码
     *
     * @return
     */
    public static List<String> getQuotePriceTrendPermissionCodes() {
        return Lists.newArrayList(CITY_BASE_PRICE_TREND_PERMISSION_CODE, MR_PRICE_TREND_PERMISSION_CODE);
    }
}
