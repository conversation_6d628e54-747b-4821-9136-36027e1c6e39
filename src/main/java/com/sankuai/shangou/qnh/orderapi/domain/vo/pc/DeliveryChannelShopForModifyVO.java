package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/16
 * desc: 待修改配送渠道门店
 */
@Getter
@Setter
public class DeliveryChannelShopForModifyVO {

    @FieldDoc(
            description = "配送渠道ID"
    )
    @ApiModelProperty(value = "配送渠道ID", required = true)
    private String deliveryChannelId;

    @FieldDoc(
            description = "配送门店ID"
    )
    @ApiModelProperty(value = "配送门店ID")
    private String deliveryShopId;

    @FieldDoc(
            description = "配送门店名称"
    )
    @ApiModelProperty(value = "配送门店名称")
    private String deliveryShopName;

    @FieldDoc(
            description = "服务包编码列表"
    )
    @ApiModelProperty(value = "服务包编码列表")
    private List<String> deliveryServiceCodes;

    @FieldDoc(
            description = "状态 0:禁用  1:启用"
    )
    @ApiModelProperty(value = "状态 0:禁用  1:启用")
    private Integer status;
}
