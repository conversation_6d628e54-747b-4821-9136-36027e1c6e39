package com.sankuai.shangou.qnh.orderapi.configuration.pc;

import com.google.common.base.Splitter;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import com.sankuai.shangou.qnh.orderapi.context.pc.GrayscaleContextHolder;
import com.sankuai.shangou.qnh.orderapi.utils.pc.LionUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;

/***
 * author : <EMAIL> 
 * data : 2021/3/12 
 * time : 下午5:17
 **/
public class OrderConfiguration {

    private static final String SPLITTER = ",";

    // 此开关线上已固定为true
    public static boolean hitOcmsMigration(long shopId) {
        long tenantId = ContextHolder.currentUserTenantId();
        String shopIdList = LionUtils.getOrderOcmsMigrationShopId();
        String tenantIdList = LionUtils.getOrderOcmsMigrationTenantId();
        boolean fullOpen = LionUtils.getOrderOcmsMigrationSwitch();
        return GrayscaleContextHolder.isOcmsOrderMigrateGray()//灰度测试
                || fullOpen
                || containString(tenantIdList, String.valueOf(tenantId))
                || containString(shopIdList, String.valueOf(shopId));
    }

    public static boolean hitOcmsMigration(List<Long> shopIdList) {
        if (CollectionUtils.isEmpty(shopIdList)) {
            return hitOcmsMigration(0L);
        }
        for (Long shopId : shopIdList) {
            if (hitOcmsMigration(shopId)) {
                return true;
            }
        }
        return false;
    }

    private static boolean containString(String str, String subStr) {
        return StringUtils.isNotBlank(str) && StringUtils.isNotBlank(subStr) &&
                Splitter.on(SPLITTER).trimResults().splitToList(str).contains(subStr);
    }

}
