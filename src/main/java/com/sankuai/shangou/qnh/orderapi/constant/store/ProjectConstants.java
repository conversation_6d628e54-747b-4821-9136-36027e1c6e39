package com.sankuai.shangou.qnh.orderapi.constant.store;

import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;


/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/5/10.
 */
public interface ProjectConstants {

    String TOKEN = CommonConstant.AUTHORIZATION_HEADER;
    String E_TOKEN = CommonConstant.E_TOKEN_HEADER;
    String UUID = "uuid";
    String APPID = "appId";
    String OS = "os";
    String APPVERSION = "appVersion";
    String JSVERSION = "jsVersion";
    String STORE_ID = "storeId";
    String MRN_APP = "mrnApp";
    String MRN_VERSION = "mrnVersion";
    String AUTH_ID = "authId";
    String SUB_AUTH_ID = "subAuthId";
    String APP_CHANNEL = "appChannel";

    /**
     * Header 中 os 为安卓标识
     */
    String HEADER_OS_ANDROID = "android";
    /**
     * Header 中 os 为 iOS 标识
     */
    String HEADER_OS_IOS = "ios";

    /**
     * 门店系统的APP TYPE定义为1，用于区分功能权限
     * */
    int APP_TYPE = 1;


    int ORDER_PAGE_SIZE = 20;//订单列表页每页大小

    int STANDARD_COUNT = 1;
    int NONSTAND_QUANTITY = 1;
    int DEFAULT_STOCK_CHECK_PULL_SIZE = 100;//盘点下载商品列表默认page的分页大小

    int MILLISECOND_ONE_DAY = 86400000;

    /** 英文逗号限定符 **/
    String ENGLISH_COMMA_DELIMITER = ",";

    String QUOTE_REVIEW_MODEL = "quoteReview";

    int SKU_LIST_PARTITION_NUM = 50;

    /**
     * 线下渠道
     */
    int OFFLINE_CHANNEL_ID = -1;

    /**
     * 重量500g
     */
    int WEIGHT_500G = 500;

    public static final Integer BOOLEAN_TRUE = 1;
    public static final Integer BOOLEAN_FALSE = 0;

    /**
     * 价格新应用迁移开关
     */
    String PRICE_NEW_APP_MIGRATE_SWITCH = "priceNewAppMigrateSwitch";
    String RETAIL_PRICE_MIGRATE_SWITCH = "retailPriceMigrateSwitch";
    String OFFLINE_PRICE_MIGRATE_SWITCH = "offlinePriceMigrateSwitch";
    String PRICE_SYNC_STRATEGY_SWITCH = "priceStrategyModuleSwitch";
    String MR_SELECT_MIGRATE_SWITCH = "mrSelectMigrateSwitch";

    // 租户维度调价策略配置开关
    public static final String TENANT_INITIAL_PRICE_STRATEGY = "tenantInitialPriceStrategy";

}
