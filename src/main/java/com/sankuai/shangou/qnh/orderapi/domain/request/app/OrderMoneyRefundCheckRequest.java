package com.sankuai.shangou.qnh.orderapi.domain.request.app;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.o2o.dto.request.BizOrderMoneyRefundCheckRequest;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/3/7 11:57
 */
@TypeDoc(
        description = "金额退页面检查的request"
)
@Data
public class OrderMoneyRefundCheckRequest {

    @FieldDoc(
            description = "渠道id"
    )
    @NotNull(message = "渠道id不能为空")
    private Integer channelId;

    @FieldDoc(
            description = "渠道订单号"
    )
    @NotNull(message = "渠道订单号不能为空")
    private String channelOrderId;

    public BizOrderMoneyRefundCheckRequest convertToBizOrderMoneyRefundCheckRequest() {
        return BizOrderMoneyRefundCheckRequest.builder()
                .channelId(this.channelId)
                .channelOrderId(this.channelOrderId)
                .build();
    }
}
