package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/8/26
 * desc:
 */
@TypeDoc(
        description = "品牌信息"
)
@Data
@ApiModel("品牌信息")
public class ChannelBrandVO {

    @FieldDoc(
            description = "品牌code", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "品牌code", required = true)
    private String brandCode;

    @FieldDoc(
            description = "中文名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "中文名称", required = true)
    private String zhName;

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID", required = true)
    private Integer channelId;
}
