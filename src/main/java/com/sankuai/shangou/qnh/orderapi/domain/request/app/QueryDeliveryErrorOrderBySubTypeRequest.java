package com.sankuai.shangou.qnh.orderapi.domain.request.app;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @createTime 2022/1/11
 * @description
 */
@TypeDoc(
        description = "根据子类型查询待配送订单列表"
)
@ApiModel("根据子类型查询待配送订单列表")
@Data
public class QueryDeliveryErrorOrderBySubTypeRequest {

    @FieldDoc(
            description = "第几页", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "第几页", required = true)
    @NotNull
    private Integer page;

    @FieldDoc(
            description = "每页行数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "每页行数", required = true)
    @NotNull
    private Integer size;

    @FieldDoc(
            description = "子类型", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "子类型", required = true)
    @NotNull
    private Integer subType;
}
