package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/5/5
 * desc:
 */
@TypeDoc(
        description = "摊位订单商品信息"
)
@Data
@ApiModel("摊位订单商品信息")
public class OrderItemForBoothVO {

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称", required = true)
    private String skuName;

    @FieldDoc(
            description = "数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "数量", required = true)
    private Integer quantity;

    @FieldDoc(
            description = "售卖单位", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "售卖单位", required = true)
    private String sellUnit;

    @FieldDoc(
            description = "SKU编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "SKU编码", required = true)
    private String skuCode;

    @FieldDoc(
            description = "线下单价 单位:元", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "线下单价 单位:元", required = true)
    private Double offlinePrice;

    @FieldDoc(
            description = "金额 单位:元", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "金额 单位:元", required = true)
    private Double amount;
}
