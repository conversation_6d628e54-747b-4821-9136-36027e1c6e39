package com.sankuai.shangou.qnh.orderapi.service.common.query;

import com.dianping.cat.util.MetricHelper;
import com.google.common.collect.Maps;
import com.meituan.shangou.saas.common.enums.OrderCanOperateItem;
import com.meituan.shangou.saas.order.management.client.dto.request.online.ViewIdCondition;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionSubTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TOrderIdentifier;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.OrderListResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.PageInfoVO;
import com.sankuai.shangou.qnh.orderapi.enums.pda.OrderTabSubTypeEnum;
import com.sankuai.shangou.qnh.orderapi.exception.app.CommonRuntimeException;
import com.sankuai.shangou.qnh.orderapi.remote.TmsRemoteService;
import com.sankuai.shangou.qnh.orderapi.service.common.OrderListRequestContext;
import com.sankuai.shangou.qnh.orderapi.service.common.OrderOperateItemsService;
import com.sankuai.shangou.qnh.orderapi.service.common.OrderStatusUtil;
import com.sankuai.shangou.qnh.orderapi.service.common.PageUtil;
import com.sankuai.shangou.qnh.orderapi.service.common.query.QueryOrderService;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @since 2024/7/17
 **/
@Service
@Slf4j
public class DeliveryErrorQueryOrderService extends QueryOrderService {

    @Resource
    private TmsRemoteService tmsServiceWrapper;
    @Resource
    private OrderOperateItemsService orderOperateItemsService;

    protected Pair<List<OCMSOrderVO>, PageInfoVO> queryOrderInfo(OrderListRequestContext request) {
        List<TOrderIdentifier> tOrderIdentifiers;
        // 请求 TMS 系统获取订单号列表
        try {
            if (Objects.isNull(request.getSubType()) || Objects.equals(request.getSubType(), OrderTabSubTypeEnum.ALL.getCode())) {
                tOrderIdentifiers = tmsServiceWrapper.queryDeliveryErrorOrders(request.getStoreId(), request.getTenantId());
            } else {
                //映射两种枚举
                tOrderIdentifiers = tmsServiceWrapper.queryDeliveryErrorOrdersBySubType(request.getSubType(), request.getStoreId(), request.getTenantId());
            }
            if (CollectionUtils.isEmpty(tOrderIdentifiers)) {
                return new Pair<>(Lists.newArrayList(), PageUtil.buildEmptyPageInfoVO());
            }
            request.setTOrderIdentifiers(tOrderIdentifiers);
            int totalOrderNum = tOrderIdentifiers.size();
            // 下游支持的是全量查询
            PageInfoVO pageInfoVO = PageUtil.buildPageInfoVO(1, totalOrderNum > 0 ? totalOrderNum : 1, totalOrderNum);
            List<OCMSOrderVO> ocmsOrderVOList = queryOCMSVoByViewOrderId(request.getTenantId(), tOrderIdentifiers.stream().map(v -> new ViewIdCondition(v.getOrderBizTypeCode(), v.getChannelOrderId())).collect(Collectors.toList()), request.isShowSalePrice());
            return new Pair<>(ocmsOrderVOList, pageInfoVO);
        } catch (Exception e) {
            log.error("调用 tmsServiceWrapper.queryDeliveryErrorOrdersBySubType error", e);
            MetricHelper.build().name("order.deliveryErrorBySubType.err").tag("tenantId", String.valueOf(request.getTenantId())).tag("storeId",
                    String.valueOf(request.getStoreId())).count();
            throw new CommonRuntimeException(e);
        }
    }

    /**
     * 统计异常订单类型  **需要去除已经取消的订单**
     * @param tenantId
     * @param storeIdList
     * @return
     */
    public Map<DeliveryExceptionSubTypeEnum, Integer> countDeliveryErrorBySubType(Long tenantId, List<Long> storeIdList) {
        HashMap<DeliveryExceptionSubTypeEnum, Integer> rs = Maps.newHashMap();
        List<TOrderIdentifier> tOrderIdentifiers = tmsServiceWrapper.queryDeliveryErrorOrdersBySubType(0, storeIdList.get(0), tenantId);
        if(CollectionUtils.isEmpty(tOrderIdentifiers)){
            return rs;
        }
        Map<String,TOrderIdentifier> tOrderIdentifierMap = new HashMap<>();
        for (TOrderIdentifier orderIdentifier : tOrderIdentifiers){
            tOrderIdentifierMap.put(orderIdentifier.getChannelOrderId(), orderIdentifier);
        }
        List<ViewIdCondition> viewIdConditionRequest = buildOCMSListViewIdConditionRequestByTOrderIdentifier(tOrderIdentifiers);
        List<OCMSOrderVO> ocmsOrderVOList = queryOCMSVoByViewOrderIdWithoutRevenueQuery(tenantId, viewIdConditionRequest);
        if(CollectionUtils.isEmpty(ocmsOrderVOList)){
            return rs;
        }
        for (OCMSOrderVO ocmsOrderVO : ocmsOrderVOList){
            if(ocmsOrderVO.getOrderStatus()== OrderStatusEnum.CANCELED.getValue()){
                continue;
            }
            TOrderIdentifier orderIdentifier=tOrderIdentifierMap.get(ocmsOrderVO.getViewOrderId());
            if(orderIdentifier == null){
                continue;
            }
            DeliveryExceptionSubTypeEnum deliveryExceptionSubTypeEnum = DeliveryExceptionSubTypeEnum.deliveryStatusCodeValueOfWithOutAll(
                    orderIdentifier.getExceptionTypeCode(),
                    orderIdentifier.getDeliveryExceptionCode(),
                    orderIdentifier.getDeliveryStatus().getCode());
            if (deliveryExceptionSubTypeEnum != null) {
                rs.put(deliveryExceptionSubTypeEnum, rs.getOrDefault(deliveryExceptionSubTypeEnum, 0) + 1);
                rs.put(DeliveryExceptionSubTypeEnum.ALL, rs.getOrDefault(DeliveryExceptionSubTypeEnum.ALL, 0) + 1);
            }
        }
        return rs;

    }

    public Integer countAll(Long tenantId, List<Long> storeIdList, Integer entityType) {
        Map<DeliveryExceptionSubTypeEnum, Integer> deliveryExceptionSubTypeEnumIntegerMap = countDeliveryErrorBySubType(tenantId, storeIdList);
        return deliveryExceptionSubTypeEnumIntegerMap.getOrDefault(DeliveryExceptionSubTypeEnum.ALL, 0);
    }

    private List<ViewIdCondition> buildOCMSListViewIdConditionRequestByTOrderIdentifier(List<TOrderIdentifier> tOrderIdentifiers) {
        if(CollectionUtils.isEmpty(tOrderIdentifiers)){
            return Lists.newArrayList();
        }
        return tOrderIdentifiers.stream().map(v -> new ViewIdCondition(v.getOrderBizTypeCode(), v.getChannelOrderId())).collect(Collectors.toList());
    }

    @Override
    public void addExtraInfo(OrderListResponse orderListResponse, OrderListRequestContext request) {
        OrderStatusUtil.setOrderListResponseViewStatusByExceptionSubType(orderListResponse, request.getTOrderIdentifiers());
        orderOperateItemsService.justKeepPartOperateItems(orderListResponse, Lists.newArrayList(OrderCanOperateItem.FULL_ORDER_REFUND, OrderCanOperateItem.CREATE_INVOICE, OrderCanOperateItem.SETTING_ORDER_TAG));
    }

}
