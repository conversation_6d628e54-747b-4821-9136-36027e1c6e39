package com.sankuai.shangou.qnh.orderapi.service.common.query;


import com.sankuai.shangou.qnh.orderapi.domain.request.pda.QueryUnfinishedOrderRequest;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.OrderListResponse;
import com.sankuai.shangou.qnh.orderapi.enums.pda.OrderTabTypeEnum;
import com.sankuai.shangou.qnh.orderapi.service.common.OrderListRequestContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;


/**
 * <AUTHOR>
 * @since 2024/7/17
 **/
@Service
@Slf4j
public class UnfinishedQueryOrderService extends QueryOrderService {

    @Resource
    private Wait2ConfirmQueryOrderService wait2ConfirmQueryOrderService;
    @Resource
    private Wait2DeliveryQueryOrderService wait2DeliveryQueryOrderService;
    @Resource
    private Wait2PickQueryOrderService wait2PickQueryOrderService;
    @Resource
    private Wait2SelfFetchQueryOrderService wait2SelfFetchQueryOrderService;
    @Resource
    private DeliveryErrorQueryOrderService deliveryErrorQueryOrderService;


    @Override
    public OrderListResponse queryOrderList(OrderListRequestContext request) {
        QueryUnfinishedOrderRequest condition = (QueryUnfinishedOrderRequest) request.getRequest();
        OrderTabTypeEnum orderTabTypeEnum = OrderTabTypeEnum.enumOf(condition.getOrderType());
        if (Objects.isNull(orderTabTypeEnum)) {
            throw new IllegalArgumentException("子类型错误");
        }
        switch (orderTabTypeEnum) {
            case WAIT_TO_CONFIRM:
                return wait2ConfirmQueryOrderService.queryOrderList(request);
            case WAIT_TO_PICK:
                return wait2PickQueryOrderService.queryOrderList(request);
            case DELIVERY_EXCEPTION:
                return deliveryErrorQueryOrderService.queryOrderList(request);
            case WAIT_TO_SELF_FETCH:
                return wait2SelfFetchQueryOrderService.queryOrderList(request);
            case WAIT_TO_DELIVERY:
                return wait2DeliveryQueryOrderService.queryOrderList(request);
            default:
                throw new IllegalArgumentException("子类型错误");
        }
    }



}
