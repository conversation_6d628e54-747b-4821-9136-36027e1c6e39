package com.sankuai.shangou.qnh.orderapi.constant.store;

/**
 * 评价常量
 *
 * <AUTHOR>
 */
public interface CommentConstants {

    /** 评价未回复 **/
    Integer REPLY_STATUS_NOT_REPLY = 0;
    /** 评价已回复 **/
    Integer REPLY_STATUS_REPLIED = 1;
    /** 评价列表一页最大记录数 **/
    Integer COMMENT_LIST_PAGE_SIZE_MAX = 50;
    /** 评价列表查询, 评价时间间隔天数最大值 **/
    int COMMENT_LIST_QUERY_COMMENT_TIME_INTERVAL_DAY_MAX = 31;
    /** 通过其他平台回复, 展示回复内容 **/
    String COMMENT_REPLY_BY_OTHER_PLATFORM_SHOW_CONTENT = "商家已通过其他后台回复";
    /** 评价模块，thrift接口中整数类型NULL值使用-1标识 **/
    int COMMENT_THRIFT_INT_VALUE_NULL = -1;
}
