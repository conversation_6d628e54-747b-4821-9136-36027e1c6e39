package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.DeliveryConfigForModifyVO;
import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/16
 * desc: 修改配送配置请求
 */
@Getter
@Setter
public class ModifyDeliveryConfigRequest implements BaseRequest {

    @FieldDoc(
            description = "待修改配送配置列表"
    )
    @ApiModelProperty(value = "待修改配送配置列表", required = true)
    private List<DeliveryConfigForModifyVO> list;

    @Override
    public void selfCheck() {
        AssertUtil.notEmpty(list, "待修改配送配置列表不能为空" , "list");
    }
}
