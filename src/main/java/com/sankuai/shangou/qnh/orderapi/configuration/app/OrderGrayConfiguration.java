package com.sankuai.shangou.qnh.orderapi.configuration.app;

import com.google.common.base.Splitter;
import com.sankuai.shangou.qnh.orderapi.context.app.ApiMethodParamThreadLocal;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import lombok.Data;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;

/***
 * author : <EMAIL> 
 * data : 2021/3/12 
 * time : 下午5:17
 **/
public class OrderGrayConfiguration {

    private static final String SPLITTER = ",";

    private static final ThreadLocal<GraySwitch> LOCAL = ThreadLocal.withInitial(GraySwitch::new);

    public static boolean hitOcmsMigration(){
        return hitOcmsMigration(0L);
    }

    // 线上固定返回true
    public static boolean hitOcmsMigration(Long shopId){
        long tenantId = ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId();
        String shopIdList = MccConfigUtil.getOrderOcmsMigrationShopId();
        String tenantIdList = MccConfigUtil.getOrderOcmsMigrationTenantId();
        boolean fullOpen = MccConfigUtil.getOrderOcmsMigrationSwitch();
        return isOcmsOrderMigrateGray()//灰度测试
                ||fullOpen
                || containString(tenantIdList, String.valueOf(tenantId))
                || containString(shopIdList, String.valueOf(shopId));
    }

    private static boolean containString(String str, String subStr) {
        return StringUtils.isNotBlank(str) && StringUtils.isNotBlank(subStr) &&
                Splitter.on(SPLITTER).trimResults().splitToList(str).contains(subStr);
    }



    public static void setOcmsOrderMigrateGray(String ocmsOrderMigrate){
        LOCAL.get().setOcmsOrderMigrate(ocmsOrderMigrate);
    }

    static boolean isOcmsOrderMigrateGray(){
        return BooleanUtils.toBoolean(LOCAL.get().ocmsOrderMigrate);
    }

    @Data
    static class GraySwitch{

        private String ocmsOrderMigrate;

    }

    /**
     * 销毁上下文数据
     */
    public static void release() {
        LOCAL.remove();
    }
}
