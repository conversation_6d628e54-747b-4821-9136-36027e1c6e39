package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * Created by lin<PERSON><PERSON><PERSON> on 19/03/25.
 */
@TypeDoc(
        description = "Created by lin<PERSON><PERSON><PERSON> on 19/03/25."
)
@Data
@ApiModel("App模块请求")
public class AppModuleReq {

    @FieldDoc(
            description = "门店id"
    )
    @ApiModelProperty(value = "门店id", required = true)
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "门店id"
    )
    @ApiModelProperty(name = "实体Id")
    private long entityId;

    @FieldDoc(
            description = "实体类型，1-前置仓，2-普通仓，3-门店，4-无人仓"
    )
    @ApiModelProperty(name = "实体类型，1-前置仓，2-普通仓，3-门店，4-无人仓")
    private int entityType = 3;
}
