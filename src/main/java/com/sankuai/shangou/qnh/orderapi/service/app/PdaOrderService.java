package com.sankuai.shangou.qnh.orderapi.service.app;

import com.dianping.rhino.annotation.Degrade;

import com.meituan.shangou.saas.o2o.dto.request.OCMSOrderPickCompleteRequest;
import com.meituan.shangou.saas.o2o.dto.response.OCMSOrderConfirmResponse;
import com.meituan.shangou.saas.order.management.client.dto.request.online.*;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.service.ocms.OCMSOrderThriftService;
import com.meituan.shangou.saas.tenant.highlevelclient.util.ChannelIdOrderBizTypeUtil;

import com.sankuai.meituan.shangou.empower.tms.delivery.client.common.Status;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.SiteTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.DeliveryOperationThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.request.QueryAggOrderDetailLinkRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.operation.response.QueryAggLinkResponse;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.QueryDeliveryInfoThriftService;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.QueryAggDeliveryRedirectModuleKey;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TDeliveryRedirectDetail;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.request.QueryAggDeliveryRedirectModuleRequest;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.response.QueryAggDeliveryRedirectModuleResponse;
import com.sankuai.meituan.shangou.saas.common.cat.CatTransaction;
import com.sankuai.shangou.qnh.orderapi.context.app.ApiMethodParamThreadLocal;
import com.sankuai.shangou.qnh.orderapi.domain.dto.app.IdentityInfo;
import com.sankuai.shangou.qnh.orderapi.domain.dto.app.User;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.*;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.OrderListRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pda.AggDeliveryUrlRequest;
import com.sankuai.shangou.qnh.orderapi.domain.request.pda.QueryUnfinishedOrderRequest;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.OrderListResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.RefundApplyListResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.pda.AggDeliveryUrlResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.pda.PdaOrderHomePageResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.OrderVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pda.CommonOrderListResponse;
import com.sankuai.shangou.qnh.orderapi.enums.app.*;
import com.sankuai.shangou.qnh.orderapi.enums.pda.OrderTabTypeEnum;
import com.sankuai.shangou.qnh.orderapi.exception.app.CommonLogicException;
import com.sankuai.shangou.qnh.orderapi.exception.app.CommonRuntimeException;
import com.sankuai.shangou.qnh.orderapi.service.common.*;
import com.sankuai.shangou.qnh.orderapi.service.common.query.*;
import com.sankuai.shangou.qnh.orderapi.utils.pc.OrderUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
/**
 * @program: qnh_order_api
 * @description:
 * @author: jinyi
 * @create: 2023-10-19 19:28
 **/
@Service
@Slf4j
public class PdaOrderService {
    @Resource
    private UnfinishedQueryOrderService unfinishedQueryOrderService;
    @Resource
    private AfterSaleQueryOrderService afterSaleQueryOrderService;
    @Resource
    private AppCacheChangeQueryOrderService appCacheChangeQueryOrderService;
    @Resource
    private AppCacheListQueryOrderService appCacheListQueryOrderService;
    @Resource
    private OrderListSearchOrderService orderListSearchOrderService;
    @Resource
    private HomePageService homePageService;
    @Resource
    private OCMSOrderThriftService ocmsOrderThriftService;
    @Resource
    private QueryOrderService queryOrderService;
    @Resource
    private QueryDeliveryInfoThriftService queryDeliveryInfoThriftService;
    @Resource
    private DeliveryOperationThriftService deliveryOperationThriftService;


    @Degrade(rhinoKey = "OrderService.queryWaitToConfirmOrder",
            fallBackMethod = "queryWaitToConfirmOrderFallback",
            timeoutInMilliseconds = 5000)
    @CatTransaction
    public CommonResponse<CommonOrderListResponse> queryUnfinishedOrder(IdentityInfo identityInfo, QueryUnfinishedOrderRequest request) {
        OrderListResponse orderListResponse;
        OrderListRequestContext context = OrderListRequestContext.buildWaitToAuditRefund(identityInfo, request);
        if (Objects.equals(request.getOrderType(), OrderTabTypeEnum.AFTER_SALE.getCode())){
            RefundApplyListResponse refundApplyListResponse = afterSaleQueryOrderService.queryAfterSaleOrderList(context);
            return CommonResponse.success(new CommonOrderListResponse(refundApplyListResponse.getPageInfo(), refundApplyListResponse.getRefundApplyRecordVOList().stream().map(v->v.getOrderVO().toCommonOrderVO()).collect(Collectors.toList())));
        }else{
            // 当前仅有【订单-待拣货】列表需要使用
            if(Objects.nonNull(request.getOrderType()) && Objects.equals(request.getOrderType(), OrderTabTypeEnum.WAIT_TO_PICK.getCode())){
                context.setSortType(request.getSortType());
            }
            orderListResponse = unfinishedQueryOrderService.queryOrderList(context);
        }
        return CommonResponse.success(new CommonOrderListResponse(orderListResponse.getPageInfo(), orderListResponse.getOrderList().stream().map(OrderVO::toCommonOrderVO).collect(Collectors.toList())));
    }


    @Degrade(rhinoKey = "OrderService.orderList",
            fallBackMethod = "orderListFallback",
            timeoutInMilliseconds = 5000)
    @CatTransaction
    public CommonResponse<CommonOrderListResponse> orderList(OrderListRequest request, IdentityInfo identityInfo) {
        try {
            OrderListRequestContext context = OrderListRequestContext.buildWaitToAuditRefund(identityInfo, request);
            OrderListResponse orderListResponse = orderListSearchOrderService.queryOrderList(context);
            return CommonResponse.success(new CommonOrderListResponse(orderListResponse.getPageInfo(), orderListResponse.getOrderList().stream().map(OrderVO::toCommonOrderVO).collect(Collectors.toList())));
        } catch (Exception e) {
            log.warn("OrderService.orderList call ocmsOrderSearchService.orderList error.}", e);
            return CommonResponse.fail(ResultCodeEnum.FAIL.getCode(), "系统异常，请稍后重试");
        }
    }


    @CatTransaction
    public CommonResponse<CommonOrderListResponse> changedOrderListForAppLocalCache(ChangedOrderListForAppLocalCacheRequest request, IdentityInfo identityInfo) {
        try {
            OrderListRequestContext context = OrderListRequestContext.buildWaitToAuditRefund(identityInfo, request);
            OrderListResponse orderListResponse = appCacheChangeQueryOrderService.queryOrderList(context);
            return CommonResponse.success(new CommonOrderListResponse(orderListResponse.getPageInfo(), orderListResponse.getOrderList().stream().map(OrderVO::toCommonOrderVO).collect(Collectors.toList())));
        }catch (Exception e) {
            log.warn("OrderService.orderList call ocmsOrderSearchService.orderList error.}", e);
            return CommonResponse.fail(ResultCodeEnum.FAIL.getCode(), "系统异常，请稍后重试");
        }
    }

    @CatTransaction
    public CommonResponse<CommonOrderListResponse> orderListForAppLocalCache(OrderListForAppLocalCacheRequest request, IdentityInfo identityInfo) {
        try {
            OrderListRequestContext context = OrderListRequestContext.buildWaitToAuditRefund(identityInfo, request);
            OrderListResponse orderListResponse = appCacheListQueryOrderService.queryOrderList(context);
            return CommonResponse.success(new CommonOrderListResponse(orderListResponse.getPageInfo(), orderListResponse.getOrderList().stream().map(OrderVO::toCommonOrderVO).collect(Collectors.toList())));
        } catch (Exception e) {
            log.warn("OrderService.orderList call ocmsOrderSearchService.orderList error.}", e);
            return CommonResponse.fail(ResultCodeEnum.FAIL.getCode(), "系统异常，请稍后重试");
        }
    }

    @CatTransaction
    public CommonResponse<PdaOrderHomePageResponse> homePage(HomePageRequest homePageRequest) {
        return homePageService.pdaHomePage(homePageRequest);
    }


    @Degrade(rhinoKey = "OrderService.pickComplete",
            fallBackMethod = "pickCompleteOrderFallback",
            timeoutInMilliseconds = 5000)
    @CatTransaction
    public CommonResponse pickComplete(OrderPickCompleteRequest request) {
        OCMSOrderPickCompleteRequest pickCompleteRequest = buildOCMSPickCompleteRequest(request);
        try {
            log.info("OrderService.confirmOrder  调用ocmsOrderThriftService.pickComplete request:{}", pickCompleteRequest);
            OCMSOrderConfirmResponse response = ocmsOrderThriftService.pickComplete(pickCompleteRequest);
            log.info("OrderService.confirmOrder  调用ocmsOrderThriftService.pickComplete response:{}", response);
            if (response.getStatus().getCode() != com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum.SUCCESS.getValue()) {
                return CommonResponse.fail(ResultCodeEnum.FAIL.getCode(), response.getStatus().getMessage());
            }
            return CommonResponse.success(null);
        } catch (TException e) {
            log.error("OrderService.confirmOrder  调用ocmsOrderThriftService.confirmOrder error", e);
            throw new CommonRuntimeException(e);
        }
    }

    private OCMSOrderPickCompleteRequest buildOCMSPickCompleteRequest(OrderPickCompleteRequest request) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        long tenantId = identityInfo.getUser().getTenantId();
        OCMSOrderPickCompleteRequest ocmsOrderPickCompleteRequest = new OCMSOrderPickCompleteRequest();
        ocmsOrderPickCompleteRequest.setPickCompleteTime(System.currentTimeMillis());
        ocmsOrderPickCompleteRequest.setViewOrderId(request.getChannelOrderId());
        ocmsOrderPickCompleteRequest.setTenantId(tenantId);
        ocmsOrderPickCompleteRequest.setAppId(identityInfo.getAppId());
        ocmsOrderPickCompleteRequest.setOperatorUserId(identityInfo.getUser().getAccountId());
        ocmsOrderPickCompleteRequest.setOrderBizType(DynamicOrderBizType.channelId2OrderBizTypeValue(request.getChannelId()));
        return ocmsOrderPickCompleteRequest;
    }

    public CommonResponse<String> pickCompleteOrderFallback(ConfirmOrderRequest request) {
        log.info("OrderService.pickComplete  调用降级方法 request:{}", request);
        throw new CommonLogicException(ResultCodeEnum.RETRY_INNER_FAIL);
    }

    @CatTransaction
    public CommonResponse<AggDeliveryUrlResponse> deliveryUrl(Long tenantId, AggDeliveryUrlRequest request) {
        List<OCMSOrderVO> ocmsOrderVOList = queryOrderService.queryOCMSVoByViewOrderId(tenantId, Lists.newArrayList(new ViewIdCondition(ChannelIdOrderBizTypeUtil.channelId2OderBizTypeByRule(request.getChannelId()), request.channelOrderId)), false);
        if (CollectionUtils.isEmpty(ocmsOrderVOList)) {
            return CommonResponse.fail(ResultCodeEnum.FAIL.getCode(), "订单不存在");
        }
        //参数校验，非三方配送不返回数据
        OCMSOrderVO ocmsOrderVO = ocmsOrderVOList.get(0);
        QueryAggDeliveryRedirectModuleKey aggDeliveryRedirectModuleKey = new QueryAggDeliveryRedirectModuleKey();
        aggDeliveryRedirectModuleKey.setTenantId(ocmsOrderVO.getTenantId());
        aggDeliveryRedirectModuleKey.setStoreId(OrderUtils.getStoreId(ocmsOrderVO.getDispatchShopId(), ocmsOrderVO.getWarehouseId(), ocmsOrderVO.getShopId()));
        aggDeliveryRedirectModuleKey.setEmpowerOrderId(ocmsOrderVO.getOrderId());
        QueryAggDeliveryRedirectModuleRequest redirectModuleRequest = new QueryAggDeliveryRedirectModuleRequest(Lists.newArrayList(aggDeliveryRedirectModuleKey));
        try {
            QueryAggDeliveryRedirectModuleResponse queryAggDeliveryRedirectModuleResponse = queryDeliveryInfoThriftService.queryAggDeliveryRedirectModule(redirectModuleRequest);
            if (CollectionUtils.isNotEmpty(queryAggDeliveryRedirectModuleResponse.getTDeliveryRedirectDetails())) {
                TDeliveryRedirectDetail tDeliveryRedirectDetail = queryAggDeliveryRedirectModuleResponse.getTDeliveryRedirectDetails().get(0);
                if (tDeliveryRedirectDetail.getTDeliveryRedirectModule() != null) {
                    AggDeliveryUrlResponse aggDeliveryUrlResponse = new AggDeliveryUrlResponse();
                    aggDeliveryUrlResponse.setUrl(tDeliveryRedirectDetail.getTDeliveryRedirectModule().getUrl());
                    aggDeliveryUrlResponse.setUrlText(tDeliveryRedirectDetail.getTDeliveryRedirectModule().getUrlText());
                    aggDeliveryUrlResponse.setTitle(tDeliveryRedirectDetail.getTDeliveryRedirectModule().getTitle());
                    return CommonResponse.success(aggDeliveryUrlResponse);
                }
            }
        }catch (Exception e){
            log.error("queryDeliveryInfoThriftService.queryAggDeliveryRedirectModule error, request,{}",redirectModuleRequest, e);
            return CommonResponse.fail(ResultCodeEnum.FAIL.getCode(), "获取url失败");
        }
        return CommonResponse.fail(ResultCodeEnum.FAIL.getCode(), "获取url失败");
    }

    @CatTransaction
    public CommonResponse<AggDeliveryUrlResponse> deliveryAuthUrl(AggDeliveryUrlRequest request) {
        try {
            User user = ApiMethodParamThreadLocal.getIdentityInfo().getUser();
            List<OCMSOrderVO> ocmsOrderVOList = queryOrderService.queryOCMSVoByViewOrderId(user.getTenantId(), Lists.newArrayList(new ViewIdCondition(ChannelIdOrderBizTypeUtil.channelId2OderBizTypeByRule(request.getChannelId()), request.channelOrderId)), false);
            if (CollectionUtils.isEmpty(ocmsOrderVOList)) {
                return CommonResponse.fail(ResultCodeEnum.FAIL.getCode(), "订单不存在");
            }
            //参数校验，非三方配送不返回数据
            OCMSOrderVO ocmsOrderVO = ocmsOrderVOList.get(0);

            QueryAggOrderDetailLinkRequest linkRequest = new QueryAggOrderDetailLinkRequest();
            linkRequest.setOrderId(ocmsOrderVO.getOrderId());
            linkRequest.setPoiId(OrderUtils.getStoreId(ocmsOrderVO.getDispatchShopId(), ocmsOrderVO.getWarehouseId(), ocmsOrderVO.getShopId()));
            linkRequest.setTenantId(user.getTenantId());
            linkRequest.setDeviceType(SiteTypeEnum.APP.getCode());
            linkRequest.setOperatorAccount(String.valueOf(user.getEmployeeId()));
            linkRequest.setOperatorName(user.getOperatorName());
            QueryAggLinkResponse response = deliveryOperationThriftService.queryAggOrderDetailLink(linkRequest);
            if (response == null || !Objects.equals(response.getStatus().getCode(), Status.SUCCESS.code) || StringUtils.isBlank(response.getUrl())) {
                log.error("deliveryAuthUrl error, request:{}, response:{}", request, response);
                return CommonResponse.fail(ResultCodeEnum.FAIL.getCode(), "获取配送详情链接异常");
            }
            AggDeliveryUrlResponse aggDeliveryUrlResponse = new AggDeliveryUrlResponse();
            aggDeliveryUrlResponse.setUrl(response.getUrl());
            aggDeliveryUrlResponse.setUrlText(response.getUrlText());
            aggDeliveryUrlResponse.setTitle(response.getTitle());
            return CommonResponse.success(aggDeliveryUrlResponse);
        } catch (Exception e) {
            log.error("deliveryAuthUrl error, request,{}", request, e);
            return CommonResponse.fail(ResultCodeEnum.FAIL.getCode(), "获取配送详情链接异常");
        }
    }
}
