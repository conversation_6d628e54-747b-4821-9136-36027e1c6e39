package com.sankuai.shangou.qnh.orderapi.converter.app;

import com.meituan.shangou.saas.order.management.client.enums.ChannelOrderStatus;
import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;

import java.util.HashMap;
import java.util.Map;

/**
 * @description:订单状态转换
 * @author: gong_qisheng
 * @date: 2023/7/21
 * @time: 15:13
 *        Copyright (C) 2015 Meituan
 *        All rights reserved
 */
public class OrderStatusConverter {

    public static final Map<Integer, Integer> ORDER_STATUS_CHANNEL_ORDER_STATUS = new HashMap();

    static {
        ORDER_STATUS_CHANNEL_ORDER_STATUS.put(OrderStatusEnum.SUBMIT.getValue(), ChannelOrderStatus.NEW_ORDER.getValue());

        ORDER_STATUS_CHANNEL_ORDER_STATUS.put(OrderStatusEnum.MERCHANT_CONFIRMED.getValue(), ChannelOrderStatus.BIZ_CONFIRMED.getValue());

        ORDER_STATUS_CHANNEL_ORDER_STATUS.put(OrderStatusEnum.PICKING.getValue(), ChannelOrderStatus.FULFILLMENT.getValue());

        ORDER_STATUS_CHANNEL_ORDER_STATUS.put(OrderStatusEnum.COMPLETED.getValue(), ChannelOrderStatus.FINISHED.getValue());

        ORDER_STATUS_CHANNEL_ORDER_STATUS.put(OrderStatusEnum.REFUND_APPLIED.getValue(), ChannelOrderStatus.CANCEL_APPLIED.getValue());

        ORDER_STATUS_CHANNEL_ORDER_STATUS.put(OrderStatusEnum.CANCELED.getValue(), ChannelOrderStatus.CANCELED.getValue());

        ORDER_STATUS_CHANNEL_ORDER_STATUS.put(OrderStatusEnum.LOCKED.getValue(), ChannelOrderStatus.LOCKED.getValue());
    }
}
