package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@TypeDoc(
        description = "提价审核操作结果请求"
)
@Data
@ApiModel("提价审核操作结果请求")
public class QuoteReviewedRequest {

    @FieldDoc(
            description = "审核记录ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "审核记录ID", required = true)
    @NotNull
    private List<Long> quoteRecordIds;

    @FieldDoc(
            description = "审核操作类型", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "审核操作类型", required = true)
    @NotNull
    private Integer operateType;

    @FieldDoc(
            description = "驳回原因", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "驳回原因", required = true)
    private String rejectReason;

    @FieldDoc(
            description = "驳回原因类型编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "驳回原因类型编码", required = true)
    private Integer rejectReasonNo;
}
