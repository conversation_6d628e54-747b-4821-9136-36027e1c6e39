package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 评论回复模板VO
 *
 * <AUTHOR>
 */
@TypeDoc(
        name = "订单履约看板门店数据",
        description = "评论回复模板VO"
)
@AllArgsConstructor
@NoArgsConstructor
@Data
public class OrderFulfillmentReportVO {

    @FieldDoc(
            description = "仓Id", requiredness = Requiredness.OPTIONAL
    )
    private Long warehouseId;


    @FieldDoc(
            description = "仓名称", requiredness = Requiredness.OPTIONAL
    )
    private String warehouseName;

    @FieldDoc(
            description = "门店Id", requiredness = Requiredness.OPTIONAL
    )
    private Long shopId;
    @FieldDoc(
            description = "门店名称", requiredness = Requiredness.OPTIONAL
    )
    private String shopName;
    @FieldDoc(
            description = "erp门店Id", requiredness = Requiredness.OPTIONAL
    )
    private String outShopId;
    @FieldDoc(
            description = "渠道门店信息", requiredness = Requiredness.OPTIONAL
    )
    private List<ChannelShopInfo> channelShopInfoList;

    @FieldDoc(
            description = "总单量", requiredness = Requiredness.OPTIONAL
    )
    private int orderCount;
    @FieldDoc(
            description = "履约中订单", requiredness = Requiredness.OPTIONAL
    )
    private int dealingOrderCount;

    @FieldDoc(
            description = "已完成", requiredness = Requiredness.OPTIONAL
    )
    private int completeOrderCount;
    @FieldDoc(
            description = "已取消", requiredness = Requiredness.OPTIONAL
    )

    private int cancelOrderCount;
    @FieldDoc(
            description = "待拣货", requiredness = Requiredness.OPTIONAL
    )
    private int unPickOrderCount;

    @FieldDoc(
            description = "待领取", requiredness = Requiredness.OPTIONAL
    )
    private int waitReceiveOrderCount;

    @FieldDoc(
            description = "拣货中", requiredness = Requiredness.OPTIONAL
    )
    private int pickingOrderCount;
    @FieldDoc(
            description = "拣货超时单数", requiredness = Requiredness.OPTIONAL
    )
    private int pickTimeoutCount;
    @FieldDoc(
            description = "领取超时", requiredness = Requiredness.OPTIONAL
    )
    private int receivePickTimeoutCount;
    @FieldDoc(
            description = "骑手到店未拣货", requiredness = Requiredness.OPTIONAL
    )
    private int riderReachUnpickCount;

    @FieldDoc(
            description = "交付中", requiredness = Requiredness.OPTIONAL
    )
    private int undelivered;

    @FieldDoc(
            description = "拣货完成待配送", requiredness = Requiredness.OPTIONAL
    )
    private int pickCompleteCount;
    @FieldDoc(
            description = "配送中", requiredness = Requiredness.OPTIONAL
    )
    private int delivering;
    @FieldDoc(
            description = "超时无骑手接单", requiredness = Requiredness.OPTIONAL
    )
    private int riderReceiveTimeoutCount;
    @FieldDoc(
            description = "差评率", requiredness = Requiredness.OPTIONAL
    )
    private String badCommentRatio ="0";
    @FieldDoc(
            description = "差评未回复数量", requiredness = Requiredness.OPTIONAL
    )
    private int badCommentUnReplyCount;

    @FieldDoc(
            description = "差评数", requiredness = Requiredness.OPTIONAL
    )
    private int badCommentCount;

    @FieldDoc(
            description = "评论数", requiredness = Requiredness.OPTIONAL
    )
    private int totalCommentCount;
    @FieldDoc(
            description = "售后申请待处理", requiredness = Requiredness.OPTIONAL
    )
    private int afsOrderDealingCount;
    @FieldDoc(
            description = "已完成退单数量统计", requiredness = Requiredness.OPTIONAL
    )
    private int afsOrderCompleteCount;
    @FieldDoc(
            description = "已完成售后退单统计", requiredness = Requiredness.OPTIONAL
    )
    private List<AfsOrderCompleteDetail> AfsOrderCompleteDetailList;
    @FieldDoc(
            description = "待自提", requiredness = Requiredness.OPTIONAL
    )
    private int unSelfPickCount;

    @FieldDoc(
            description = "仅退款数量"
    )
    private int refundMoneyCount;
    @FieldDoc(
            description = "退货退款数量"
    )
    private int refundGoodCount;





}
