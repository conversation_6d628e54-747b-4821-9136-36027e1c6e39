package com.sankuai.shangou.qnh.orderapi.domain.request.app;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@TypeDoc(
        description = "查询待接单列表"
)
@ApiModel("查询待接单列表")
@Data
public class QueryWaitToPickStoresOrderRequest extends QueryWaitToPickOrderRequest {
    @FieldDoc(
            description = "租户id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户id")
    @NotNull
    private Long tenantId;

    @FieldDoc(
            description = "门店id集合", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店id集合")
    private List<Long> storeIds;
}

