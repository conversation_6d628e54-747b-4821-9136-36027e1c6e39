package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.ChannelCategoryVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/8/26
 * desc:
 */
@TypeDoc(
        description = "查询渠道分类响应"
)
@Data
@ApiModel("查询渠道分类响应")
public class GetChannelCategoryResponse {

    @FieldDoc(
            description = "渠道分类列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道品牌列表", required = true)
    private List<ChannelCategoryVO> categoryList;
}
