package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2019/7/11
 * desc: 查询打印状态请求
 */
@TypeDoc(
        description = "打印小票请求"
)
@ApiModel("打印小票请求")
@Data
public class QueryPrintStatusRequest {

    @FieldDoc(
            description = "打印ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "打印ID")
    @NotNull
    private String printId;
}
