package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/1/6
 * @email jianglilin02@meituan
 */
@TypeDoc(
        description = "评价回访人",
        authors = {"jianglilin02"}
)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommentContactOperator {
    @FieldDoc(
            description = "回访人员名字，为空不展示"
    )
    private String contactOperatorName;

    @FieldDoc(
            description = "回访人员电话，为空不展示"
    )
    private String contactOperatorPhone;
}
