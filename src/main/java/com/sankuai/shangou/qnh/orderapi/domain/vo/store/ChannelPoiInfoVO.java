package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2019/12/13 15:56
 * @Description:
 */
@TypeDoc(
        description = "渠道门店信息"
)
@Data
@ApiModel("渠道门店信息")
public class ChannelPoiInfoVO {

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID", required = true)
    private String channelId;

    @FieldDoc(
            description = "渠道名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道名称", required = true)
    private String channelName;

    @FieldDoc(
            description = "渠道门店状态"
    )
    @ApiModelProperty(name = "渠道门店状态")
    private Integer status;

    @FieldDoc(
            description = "渠道门店公告"
    )
    @ApiModelProperty(name = "渠道门店公告")
    private String promotionInfo;

    @FieldDoc(
            description = "渠道门店营业时间"
    )
    @ApiModelProperty(name = "渠道门店营业时间")
    private List<String> shippingTime;

    @FieldDoc(
            description = "渠道门店营业日,如：\"0,1,3,6\"表示周一、二、四和周日营业，该字段为空表示每天"
    )
    @ApiModelProperty(name = "渠道门店营业日")
    private String shippingDay;

    @FieldDoc(
            description = "渠道门店是否支持预订单"
    )
    @ApiModelProperty(name = "渠道门店是否支持预订单")
    private Boolean preBook;

    @FieldDoc(
            description = "商家接受预订日期的最早日期"
    )
    @ApiModelProperty(name = "商家接受预订日期的最早日期")
    private Integer prebookMinDays;

    @FieldDoc(
            description = "商家接受预订日期的最长日期"
    )
    @ApiModelProperty(name = "商家接受预订日期的最长日期")
    private Integer prebookMaxDays;

}

