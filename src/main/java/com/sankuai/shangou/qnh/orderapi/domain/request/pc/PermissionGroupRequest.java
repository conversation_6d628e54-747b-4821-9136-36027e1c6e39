package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryPermissionGroupRequest;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Objects;

/**
 * @Author: <EMAIL>
 * @Date: 2018/11/28 下午7:49
 */
@TypeDoc(
        description = "根据acountid查询数据权限组入参"
)
@ApiModel("根据acountid查询数据权限组入参")
@Data
public class PermissionGroupRequest {
    @FieldDoc(
            description = "租户id"
    )
    private long tenantId;

    @FieldDoc(
            description = "应用id"
    )
    private Integer appId;

    @FieldDoc(
            description = "账户id"
    )
    private long accountId;

    @FieldDoc(
            description = "数据权限类型"
    )
    private int type;

    public QueryPermissionGroupRequest convert2ThrifRequest(){
        QueryPermissionGroupRequest request = new QueryPermissionGroupRequest();

        request.tenantId = ContextHolder.currentUserTenantId();
        request.accountId = this.accountId;
        request.type = type;
        if (Objects.nonNull(appId)) {
            request.setAppId(appId);
        }

        return request;
    }

    public void selfCheck(){
        //不需要检查
    }
}
