package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderItemMoneyRefundCheckModel;
import com.sankuai.shangou.qnh.orderapi.domain.vo.ExchangeProductVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.SubProductVo;
import com.sankuai.shangou.qnh.orderapi.utils.CombinationProductUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Created by suxiaoyu on 2023/3/7 14:28
 */
@TypeDoc(
        description = "金额退页面检查的商品信息"
)
@Data
public class OrderItemMoneyRefundCheckVO {
    @FieldDoc(
            description = "商品内部skuId"
    )
    private String innerSkuId;

    @FieldDoc(
            description = "商家在渠道的skuId"
    )
    private String customerSkuId;

    @FieldDoc(
            description = "商品名称"
    )
    private String skuName;

    @FieldDoc(
            description = "商品图片URL"
    )
    private String picUrl;

    @FieldDoc(
            description = "规格"
    )
    private String specification;

    @FieldDoc(
            description = "商品数量"
    )
    private Integer skuCount;

    @FieldDoc(
            description = "可退金额，分"
    )
    private Integer canRefundMoney;

    @FieldDoc(
            description = "实付价格"
    )
    private Integer currentPrice;

    @FieldDoc(
            description = "订单商品行id"
    )
    private String orderItemId;

    @FieldDoc(
            description = "商品spu"
    )
    private String spu;

    @FieldDoc(
            description = "erp商品编码"
    )
    private String erpItemCode;

    @FieldDoc(
            description = "商品条码"
    )
    private String upc;

    @FieldDoc(
            description = "商品渠道重量"
    )
    private Integer channelWeight;

    @FieldDoc(
            description = "商品总渠道重量"
    )
    private Integer totalChannelWeight;

    @FieldDoc(
            description = "组合商品"
    )
    private List<SubProductVo> subProduct;

    @FieldDoc(
            description = "渠道商品spu(暂美团渠道需要)"
    )
    private String customSpu;

    @FieldDoc(
            description = "换货商品列表", requiredness = Requiredness.OPTIONAL
    )
    private List<ExchangeProductVo> exchangeProductVoList;

    @FieldDoc(
            description = "被换货数量（缺货数量）", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "被换货数量（缺货数量）", required = true)
    private Integer exchangeFromCount;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "替换的数量（已换数量）", required = true)
    private Integer exchangeToCount;

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店ID", required = true)
    private Long shopId;

    @FieldDoc(
            description = "转单门店ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "转单门店ID", required = true)
    private Long dispatchShopId;

    public static OrderItemMoneyRefundCheckVO buildOrderItemMoneyRefundCheckVO(BizOrderItemMoneyRefundCheckModel moneyRefundCheckModel) {
        OrderItemMoneyRefundCheckVO moneyRefundCheckVO = new OrderItemMoneyRefundCheckVO();
        moneyRefundCheckVO.setCanRefundMoney(moneyRefundCheckModel.getCanRefundMoney());
        moneyRefundCheckVO.setCurrentPrice(moneyRefundCheckModel.getCurrentPrice());
        moneyRefundCheckVO.setInnerSkuId(moneyRefundCheckModel.getInnerSkuId());
        moneyRefundCheckVO.setPicUrl(moneyRefundCheckModel.getPicUrl());
        moneyRefundCheckVO.setSkuCount(moneyRefundCheckModel.getSkuCount());
        moneyRefundCheckVO.setSkuName(moneyRefundCheckModel.getSkuName());
        moneyRefundCheckVO.setCustomerSkuId(moneyRefundCheckModel.getCustomerSkuId());
        moneyRefundCheckVO.setSpecification(moneyRefundCheckModel.getSpecification());
        moneyRefundCheckVO.setOrderItemId(moneyRefundCheckModel.getOrderItemId() != null ? moneyRefundCheckModel.getOrderItemId().toString() : null);
        moneyRefundCheckVO.setSpu(moneyRefundCheckModel.getSpu());
        moneyRefundCheckVO.setErpItemCode(moneyRefundCheckModel.getErpItemCode());
        moneyRefundCheckVO.setUpc(moneyRefundCheckModel.getBarCode());
        moneyRefundCheckVO.setSubProduct(CombinationProductUtil.buildSubProductByModel(moneyRefundCheckModel.getCombinationProductModelList()));
        moneyRefundCheckVO.setCustomSpu(moneyRefundCheckModel.getCustomSpu());
        return moneyRefundCheckVO;
    }
}
