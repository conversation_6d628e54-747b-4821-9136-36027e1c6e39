package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/8 11:22
 * @Description:
 */
@Setter
@Getter
@ToString
public class AuditOrderQueryRequest extends PageRequest implements BaseRequest {


    /**
     * 收货人姓名
     */
    private String receiverName;


    /**
     * 收货人电话
     */
    private String receiverPhone;

    /**
     * 门店名称
     */
    private String poiName;


    /**
     * 门店编码
     */
    private String poiId;

    /**
     * 开始时间
     */
    private String createStartTime;


    /**
     * 结束时间
     */
    private String createEndTime;


    /**
     *  申请取消的开始时间
     */
    private String cancelStartTime;


    /**
     *  申请取消的结束时间
     */
    private String cancelEndTime;


    /**
     * 退款类型
     */
    private String refundType;


    /**
     * 渠道列表
     */
    private List<String> channelIds;


    /**
     * 申请人
     */
    private String applicant;

    /**
     * 渠道流水号
     */
    private String orderSerialNumber;

    /**
     * 仓id
     */
    private List<Long> warehouseIdList;
}
