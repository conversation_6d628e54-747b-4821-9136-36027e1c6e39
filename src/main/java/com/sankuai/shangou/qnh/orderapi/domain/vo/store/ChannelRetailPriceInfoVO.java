package com.sankuai.shangou.qnh.orderapi.domain.vo.store;


import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * author: guo<PERSON><PERSON>@meituan.com
 * date: 2020-04-17 18:22:05
 *
 * desc: 价格趋势消息体
 */
@Data
public class ChannelRetailPriceInfoVO {

    /**
     * 渠道标识
     */
    private int channelId;

    /**
     * 同步提价策略
     */
    private int strategyType;

    /**
     * 提价固定金额，单位-元
     */
    private String raisePrice;

    /**
     * 提价百分比：放大100倍
     */
    private String raisePercent;

    /**
     * 线上售卖价：单位-元
     */
    private String retailPrice;

    /**
     * 线上售卖价，市斤价：单位-元
     */
    private String retailPricePer500g;

    /**
     * 通用掉价策略的具体信息
     */
    private String description;
}
