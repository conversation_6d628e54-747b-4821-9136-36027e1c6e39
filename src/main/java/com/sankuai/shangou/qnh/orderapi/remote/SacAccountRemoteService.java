package com.sankuai.shangou.qnh.orderapi.remote;

import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import com.meituan.reco.pickselect.common.methodlog.MethodLog;
import com.meituan.shangou.sac.dto.model.SacManagerAccountDto;
import com.meituan.shangou.sac.dto.request.authenticate.AccountAuthPermissionsRequest;
import com.meituan.shangou.sac.dto.response.SacStatus;
import com.meituan.shangou.sac.dto.response.authenticate.AccountAuthPermissionsResponse;
import com.meituan.shangou.sac.dto.response.search.QueryManagerAccountResponse;
import com.meituan.shangou.sac.thrift.authenticate.AuthenticateService;
import com.meituan.shangou.sac.thrift.search.SacAccountSearchThriftService;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import com.sankuai.shangou.qnh.orderapi.utils.pc.LionUtils;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ResponseHandler;
import com.sankuai.shangou.qnh.orderapi.utils.pc.RpcInvoker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * @description:
 * @author: WangSukuan
 * @create: 2020-12-01
 **/
@Component
@Slf4j
public class SacAccountRemoteService {

    @Autowired
    private SacAccountSearchThriftService sacAccountSearchThriftService;
    @Autowired
    private AuthenticateService authenticateService;

    /**
     * 检查用户在app下是否有相应的权限码
     * @param appId
     * @param accountId
     * @param needCheckAuthCode
     * @return
     */
    public Map<String, Boolean> checkAccountHasAuth(int appId, long accountId, Set<String> needCheckAuthCode) {
        AccountAuthPermissionsRequest request = new AccountAuthPermissionsRequest();
        request.setAccountId(accountId);
        request.setAppId(appId);
        request.setPermissionCodes(new ArrayList<>(needCheckAuthCode));
        AccountAuthPermissionsResponse resp = authenticateService.accountAuthPermissions(request);
        if (!SacStatus.success().equals(resp.getSacStatus())) {
            log.info("调用查看用户鉴权接口失败: msg:{}", resp.getSacStatus().getMessage());
            throw new BizException(resp.getSacStatus().getMessage());
        }
        return resp.getAuthResult();
    }

    /**
     * 简单订单操作的相应权限
     *
     * @return
     */
    @MethodLog(logResponse = true)
    public Map<Integer, Boolean> queryAccountCode() {
        try {
            Map<String, Integer> permissionCodeMap = fetchPermissionFromConfig();
            if (MapUtils.isNotEmpty(permissionCodeMap)) {
                Map<String, Boolean> authMap = checkAccountHasAuth(ContextHolder.currentUserLoginAppId(), ContextHolder.currentUid(), permissionCodeMap.keySet());
                log.info("请求订单元素权限：{}， result:{}", ContextHolder.currentUid(), authMap);
                if (MapUtils.isEmpty(authMap)) {
                    return Maps.newHashMap();
                }
                return authMap.entrySet().stream().collect(Collectors.toMap(entry -> permissionCodeMap.get(entry.getKey()), entry -> entry.getValue(), (f, s) -> s));
            }
        } catch (Exception e) {
            log.error("查询权限元素权限失败", e);
        }
        log.info("没有配置待检查的元素权限");
        return Maps.newHashMap();
    }

    private Map<String, Integer> fetchPermissionFromConfig() {
        String permissionCodes = LionUtils.getOrderOperatorAuthCodes();
        log.info("permissionCodes:{}", permissionCodes);
        if (StringUtils.isNotBlank(permissionCodes)) {
            /*
             * 解析  ACCEPT_ORDER,1;REFUND,2;.....
             */
            List<String> permissionMap = Splitter.on(";").splitToList(permissionCodes);
            return permissionMap.stream()
                    .map(e -> Splitter.on(",").splitToList(e))
                    .filter(e -> e.size() >= 2)
                    .collect(Collectors.toMap(e -> e.get(0), e -> NumberUtils.toInt(e.get(1)), (f, s) -> s));
        }
        return Maps.newHashMap();
    }

    public SacManagerAccountDto getManagerAccountByMisId(String misId) {
        QueryManagerAccountResponse response = RpcInvoker.invokeReturn(() -> sacAccountSearchThriftService.queryManagerAccount(misId));
        ResponseHandler.checkResponseAndStatus(response, res -> res.getSacStatus().getCode(), res -> res.getSacStatus().getMessage());
        return response.getManagerAccount();
    }


    /**
     * 批量查询权限code是否有权限
     *
     * @param accountId
     * @param authId
     * @param permissionCodes
     * @return
     */
    public Map<String, Boolean> accountAuthPermissions(long accountId, int authId, List<String> permissionCodes) {
        if (CollectionUtils.isEmpty(permissionCodes)) {
            return Collections.emptyMap();
        }
        Map<String, Boolean> result = permissionCodes
                .stream()
                .distinct()
                .collect(Collectors.toMap(Function.identity(),
                        s -> false));
        AccountAuthPermissionsRequest request = new AccountAuthPermissionsRequest();
        request.setAccountId(accountId);
        request.setAppId(authId);
        request.setPermissionCodes(permissionCodes);
        AccountAuthPermissionsResponse response;
        try {
            log.info("批量查询权限code是否有权限 request:{}", request);
            response = authenticateService.accountAuthPermissions(request);
            log.info("批量查询权限code是否有权限 response:{}", response);
        } catch (Exception e) {
            log.error("批量查询权限code是否有权限异常 request:{}", request);
            return result;
        }
        if (response == null || response.sacStatus == null || response.sacStatus.code != 0) {
            log.error("批量查询权限code是否有权限失败,request:{},response:{}", request, response);
            return result;
        }
        if (org.apache.commons.collections4.MapUtils.isNotEmpty(response.getAuthResult())) {
            response.getAuthResult().forEach(result::put);
        }
        return result;
    }

}
