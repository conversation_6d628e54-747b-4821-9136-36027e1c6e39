package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import com.sankuai.shangou.qnh.orderapi.utils.pc.LionUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/2 11:38
 * @Description:
 */
@Setter
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(
        description = "订单明细分页查询请求"
)
public class OrderItemFuseQueryRequest extends PageRequest implements BaseRequest {

    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 融合订单状态
     */
    public List<String> fuseOrderStatus;

    /**
     * 渠道列表
     */
    private List<String> channelIds;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 门店ID集合
     */
    private List<Long> poiIdList;

    /**
     * 仓库id
     */
    private List<Long> warehouseIdList;

    /**
     * 商品skuid
     */
    private String skuId;


    /**
     * 商品条码
     */
    private String upc;


    /**
     * 线上分类
     */
    private String category;


    /**
     * 订单创建开始时间
     */
    private String createStartTime;

    /**
     * 订单创建结束时间
     */
    private String createEndTime;

    /**
     * 商品erp
     */
    private String erpCode;

    /**
     * 店内分类idlist
     */
    private List<String> inStoreCategoryIds;

    /**
     * 商品erp list
     */
    private List<String> erpCodeList;

    /**
     * 商品条码 list
     */
    private List<String> upcList;

    /**
     * SKU list
     */
    private List<String> skuIdList;

    /**
     * 订单标识列表
     */
    public List<String> orderMarkStrList;

    @Override
    public void selfCheck() {
        if (StringUtils.isNotEmpty(orderId)) {
            Integer limitLength = LionUtils.getOrderParamsLengthLimitConfig();
            AssertUtil.isTrue(orderId.length() <= limitLength, "渠道单号最多输入" + limitLength + "个数字");
        }

        if (StringUtils.isNotEmpty(skuId)) {
            AssertUtil.isTrue(skuId.length() <= 20, "商品SKU最多输入20个字符");
        }

        if (StringUtils.isNotEmpty(erpCode)) {
            AssertUtil.isTrue(erpCode.length() <= 20, "ERP编码最多输入20个字符");
        }

        if (StringUtils.isNotEmpty(upc)) {
            AssertUtil.isTrue(upc.length() <= 20, "商品条码最多输入20个字符");
        }
    }
}
