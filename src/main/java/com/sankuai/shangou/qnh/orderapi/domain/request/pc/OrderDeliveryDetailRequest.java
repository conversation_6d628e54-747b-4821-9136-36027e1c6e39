package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.dianping.cat.util.StringUtils;
import lombok.Data;

@Data
public class OrderDeliveryDetailRequest {

    private String channelOrderId;

    private Integer channelId;

    public String validate(){
        if(StringUtils.isBlank(channelOrderId)){
            return "channelOrderId不能为空";
        }
        if(channelId==null){
            return "channelId不能为空";
        }
        return null;
    }

}
