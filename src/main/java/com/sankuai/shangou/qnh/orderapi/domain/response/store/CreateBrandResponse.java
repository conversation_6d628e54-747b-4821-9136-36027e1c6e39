package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/6/17
 * desc:
 */
@TypeDoc(
        description = "新建品牌响应"
)
@Data
@ApiModel("新建品牌响应")
public class CreateBrandResponse {

    @FieldDoc(
            description = "品牌Code", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "品牌Code", required = true)
    private String brandCode;
}
