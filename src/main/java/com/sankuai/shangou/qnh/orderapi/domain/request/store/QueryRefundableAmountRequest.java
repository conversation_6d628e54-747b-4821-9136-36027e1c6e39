package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.PartRefundProductVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/7/12
 * desc: 查询退款金额请求
 */
@TypeDoc(
        description = "查询退款金额请求"
)
@ApiModel("查询退款金额请求")
@Data
public class QueryRefundableAmountRequest {

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID")
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID")
    @NotNull
    private Integer channelId;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道订单号")
    @NotNull
    private String channelOrderId;

    @FieldDoc(
            description = "退款类型，1：全单退款，2：部分退款", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "退款类型，1：全单退款，2：部分退款")
    @NotNull
    private Integer refundType;

    @FieldDoc(
            description = "退款商品列表 部分退款时必传", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "退款商品列表 部分退款时必传")
    private List<PartRefundProductVO> partRefundProductList;
}
