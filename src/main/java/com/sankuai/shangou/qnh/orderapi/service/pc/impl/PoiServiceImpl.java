package com.sankuai.shangou.qnh.orderapi.service.pc.impl;

import com.google.common.collect.Maps;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.PoiInfoBO;
import com.sankuai.shangou.qnh.orderapi.remote.PoiRemoteService;
import com.sankuai.shangou.qnh.orderapi.service.pc.PoiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/16 11:25
 * @Description:
 */
@Service
@Slf4j
public class PoiServiceImpl implements PoiService {
    @Autowired
    private PoiRemoteService poiRemoteService;

    @Override
    public Map<Long, PoiInfoBO> queryPoiByIds(Long tenantId, List<Long> poiIds) {
        Map<Long, PoiInfoDto> poiInfoDtoMap = poiRemoteService.queryPoiByIds(tenantId, poiIds);

        if (MapUtils.isNotEmpty(poiInfoDtoMap)) {
            Map<Long, PoiInfoBO> retMap = Maps.newHashMapWithExpectedSize(poiInfoDtoMap.size());

            for (Map.Entry<Long, PoiInfoDto> entry : poiInfoDtoMap.entrySet()) {
                retMap.put(entry.getKey(), new PoiInfoBO(entry.getValue()));
            }
            return retMap;
        }
        return Maps.newHashMap();
    }

}
