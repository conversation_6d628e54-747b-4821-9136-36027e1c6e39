package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.PartRefundProductVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/7/11
 * desc: 部分退款请求
 */
@TypeDoc(
        description = "部分退款请求"
)
@ApiModel("部分退款请求")
@Data
public class PartRefundRequest {

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道ID")
    private Integer channelId;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道订单号")
    private String channelOrderId;

    @FieldDoc(
            description = "部分退款原因", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "部分退款原因")
    @NotNull(message = "退款原因不能为空")
    private String reason;

    @FieldDoc(
            description = "部分退款商品列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "部分退款商品列表")
    private List<PartRefundProductVO> partRefundProductList;

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID")
    @NotNull
    private Long storeId;
}
