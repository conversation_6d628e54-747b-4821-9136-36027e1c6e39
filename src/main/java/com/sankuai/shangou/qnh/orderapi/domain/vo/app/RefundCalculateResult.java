package com.sankuai.shangou.qnh.orderapi.domain.vo.app;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/3/17
 * desc: 克重退差试算结果
 */
@TypeDoc(
        description = "检查退款请求响应"
)
@ApiModel("检查退款请求响应")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RefundCalculateResult {



    @FieldDoc(
            description = "sku", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "sku", required = true)
    private String skuId;

    @FieldDoc(
            description = "预计退款金额", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "预计退款金额", required = true)
    private Integer refundMoney;
}
