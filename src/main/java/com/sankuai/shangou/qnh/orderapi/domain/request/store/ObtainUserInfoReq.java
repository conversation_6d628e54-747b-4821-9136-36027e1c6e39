package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

@Data
@ToString
@NoArgsConstructor
public class ObtainUserInfoReq {
    private Long tenantId;
    private Long accountId;
    private Long employeeId;
    private int appId;
    private String token;
    private boolean needNewest;
}
