package com.sankuai.shangou.qnh.orderapi.filter.pc;

import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import java.io.IOException;

/**
 * 请求结束时释放资源, 销毁上线文数据
 * 
 * <AUTHOR>
 * 
 */
public class ContextFilter implements Filter {

	@Override
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
		try {
			chain.doFilter(request, response);
		} finally {
			ContextHolder.release();
		}
	}

	@Override
	public void init(FilterConfig arg0) {
	}

	@Override
	public void destroy() {

	}

}
