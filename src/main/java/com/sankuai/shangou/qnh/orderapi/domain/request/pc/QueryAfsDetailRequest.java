package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/4 17:20
 * @Description:
 */
@Getter
@Setter
@ToString
public class QueryAfsDetailRequest implements BaseRequest {

    private String channelAfsId;

    private String channelOrderId;

    private String channelId;

    @Override
    public void selfCheck() {
        AssertUtil.notEmpty(channelAfsId, "售后单号不能为空", "channnelAfsId");
        AssertUtil.notEmpty(channelOrderId, "订单单号不能为空", "channelOrderId");
        AssertUtil.notEmpty(channelId, "渠道编码不能为空", "channelId");
    }
}
