package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/7/10
 * desc: 订单分页查询请求
 */
@TypeDoc(
        description = "订单分页查询请求"
)
@ApiModel("订单分页查询请求")
@Data
public class OrderListRequest {

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店ID")
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "渠道ID列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道ID列表")
    private List<Integer> channelIdList;

    @FieldDoc(
            description = "退款类型列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "退款类型列表")
    private List<Integer> refundTagIdList;

    @FieldDoc(
            description = "渠道订单状态列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道订单状态列表")
    private List<Integer> channelOrderStatusList;

    @FieldDoc(
            description = "订单创建起始日期 格式:2019-06-22", requiredness = Requiredness.OPTIONAL_IN_REQUIRED_OUT
    )
    @ApiModelProperty(name = "订单创建起始日期 格式:2019-06-22")
    private String beginCreateDate;

    @FieldDoc(
            description = "订单创建结束日期 格式:2019-06-22", requiredness = Requiredness.OPTIONAL_IN_REQUIRED_OUT
    )
    @ApiModelProperty(name = "订单创建结束日期 格式:2019-06-22")
    private String endCreateDate;

    @FieldDoc(
            description = "第几页", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "第几页", required = true)
    @NotNull
    private Integer page;

    @FieldDoc(
            description = "每页行数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "每页行数", required = true)
    @NotNull
    private Integer size;

    @FieldDoc(
            description = "需要判断的按钮集合", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "需要判断的按钮集合")
    private List<Integer> showOperateItems;

    @FieldDoc(
            description = "关键词搜索", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "关键词搜索")
    private String keyword;

    @FieldDoc(
            description = "聚合订单状态  1-进行中订单 2-已完成订单 3-已取消订单", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "聚合订单状态")
    private Integer aggregationOrderStatus;

    @FieldDoc(
            description = "配送订单类型  1-立即单 2-预约单", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "配送订单类型  1-立即单 2-预约单")
    private Integer deliveryOrderType;

}
