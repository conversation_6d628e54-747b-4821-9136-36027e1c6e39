package com.sankuai.shangou.qnh.orderapi.domain.vo.app;


import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.*;
import com.sankuai.shangou.qnh.orderapi.domain.vo.AfsExchangeProductVo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.ExchangeProductVo;
import com.meituan.reco.pickselect.common.utils.JacksonUtils;
import com.sankuai.shangou.qnh.orderapi.service.common.ExchangeItemUtil;
import com.sankuai.shangou.qnh.orderapi.utils.ProductLabelUtil;
import javafx.util.Pair;
import org.apache.commons.lang3.StringUtils;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.order.management.client.enums.PriceDisplayType;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.sankuai.shangou.qnh.orderapi.domain.vo.SubProductVo;
import com.sankuai.shangou.qnh.orderapi.utils.CombinationProductUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.codehaus.jackson.JsonNode;
import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.type.TypeReference;

import java.math.BigInteger;
import java.util.*;

/**
 * 退款申请商品信息
 *
 * <AUTHOR>
 * @since 2019/11/18
 */
@TypeDoc(
        description = "退款申请商品信息"
)
@ApiModel("退款申请商品信息")
@Data
public class RefundApplyRecordDetailVO {

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道ID", required = true)
    private Integer channelId;

    @FieldDoc(
            description = "售后服务唯一ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "售后服务唯一ID", required = true)
    private Long serviceId;

    @FieldDoc(
            description = "orderItemId", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "orderItemId", required = true)
    private Long orderItemId;

    @FieldDoc(
            description = "skuId", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "skuId", required = true)
    private String skuId;

    @FieldDoc(
            description = "upc码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "upc码", required = true)
    private String upcCode;

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "商品名称", required = true)
    private String skuName;

    @FieldDoc(
            description = "规格", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "规格", required = true)
    private String specification;

    @FieldDoc(
            description = "售卖单位", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "售卖单位", required = true)
    private String sellUnit;

    @FieldDoc(
            description = "单价  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "单价  单位:分", required = true)
    private Integer unitPrice;

    @FieldDoc(
            description = "退货数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "退货数量", required = true)
    private Integer count;

    @FieldDoc(
            description = "退款金额  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "退款金额  单位:分", required = true)
    private Integer refundAmt;

    @FieldDoc(
            description = "克重退款重量，单位：克", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "克重退款重量，单位：克")
    private Double refundWeight;


    @FieldDoc(
            description = "商品标签信息列表", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "商品标签信息列表")
    private List<TagInfoVO> tagInfoList;

    @FieldDoc(
            description = "退款总金额  单位:分", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "退款总金额  单位:分", required = true)
    private Integer totalRefundAmt;

    @FieldDoc(
            description = "商品图片URL", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "商品图片URL", required = true)
    private String picUrl;

    @FieldDoc(
            description = "商品标签类型:1履约标签、2拣货标签、3属性标签"
    )
    @ApiModelProperty(value = "商品标签信息列表(除1履约标签、2拣货标签之外的其它新标签都用此对象) 3商品属性标签")
    private List<OrderItemTagVO> tagInfos;

    @FieldDoc(
            description = "商品项线下价格=线下价格*数量", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "商品项线下价格", required = true)
    private Integer orderItemOfflinePrice;

    @FieldDoc(
            description = "组合品子商品", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "组合品子商品")
    private List<SubProductVo> subProductVoList;

    @FieldDoc(
            description = "换货商品", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "换货商品")
    private List<ExchangeProductVo> exchangeProductVoList;

    @FieldDoc(
            description = "erpCode", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "erpCode")
    private String erpItemCode;

    @FieldDoc(
            description = "goodsCode", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "goodsCode")
    private String goodsCode;

    @FieldDoc(
            description = "退单商品标签", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "channelLabelList")
    private List<ChannelLabelVO> channelLabelList;

    @FieldDoc(
            description = "售后商品对应的换货商品信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "售后商品对应的换货商品信息")
    private AfsExchangeProductVo afsExchangeProduct;

    @FieldDoc(
            description = "商品标签附加信息", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "商品标签附加信息")
    private String labelSubDesc;

    public static RefundApplyRecordDetailVO buildRefundApplyRecordDetailVO(OCMSAfterSaleApplyDetailVO ocmsAfterSaleApplyDetailVO) {
        RefundApplyRecordDetailVO refundApplyRecordDetailVO = new RefundApplyRecordDetailVO();
        refundApplyRecordDetailVO.setChannelId(ChannelOrderConvertUtils.sourceBiz2Mid(ocmsAfterSaleApplyDetailVO.getOrderBizType()));
        refundApplyRecordDetailVO.setServiceId(ocmsAfterSaleApplyDetailVO.getServiceId());
        refundApplyRecordDetailVO.setSkuId(ocmsAfterSaleApplyDetailVO.getInstoreSku2());
        refundApplyRecordDetailVO.setOrderItemId(ocmsAfterSaleApplyDetailVO.getOrderItemId());
        refundApplyRecordDetailVO.setUpcCode(ocmsAfterSaleApplyDetailVO.getSkuCode());
        refundApplyRecordDetailVO.setSkuName(ocmsAfterSaleApplyDetailVO.getSkuName());
        refundApplyRecordDetailVO.setSpecification(ocmsAfterSaleApplyDetailVO.getSpecification());
        refundApplyRecordDetailVO.setSellUnit(ocmsAfterSaleApplyDetailVO.getSellUnit());
        refundApplyRecordDetailVO.setUnitPrice(ocmsAfterSaleApplyDetailVO.getOriginalAmt());
        refundApplyRecordDetailVO.setCount(ocmsAfterSaleApplyDetailVO.getCount());
        // 商品线下价格
        refundApplyRecordDetailVO.setRefundAmt(ocmsAfterSaleApplyDetailVO.getOfflinePrice() == null
                ? BigInteger.ZERO.intValue() : ocmsAfterSaleApplyDetailVO.getOfflinePrice());
        refundApplyRecordDetailVO.setTotalRefundAmt(Optional.ofNullable(refundApplyRecordDetailVO.getCount()).orElse(
                BigInteger.ZERO.intValue()) * refundApplyRecordDetailVO.getRefundAmt());
        refundApplyRecordDetailVO.setRefundWeight(ocmsAfterSaleApplyDetailVO.getRefundWeight());
        // 展示类型为退款金额 重置为该商品实际的退款总金额
        if(Integer.valueOf(PriceDisplayType.REFUND_AMOUNT.getCode()).equals(ocmsAfterSaleApplyDetailVO.getPriceDisplayType())){
            refundApplyRecordDetailVO.setTotalRefundAmt(ocmsAfterSaleApplyDetailVO.getRefundAmt());
        }
        refundApplyRecordDetailVO.setSubProductVoList(CombinationProductUtil.getAfsDetailComposeProductList(ocmsAfterSaleApplyDetailVO.getComposeProduct()));
        refundApplyRecordDetailVO.setErpItemCode(ocmsAfterSaleApplyDetailVO.getErpItemCode());
        refundApplyRecordDetailVO.setGoodsCode(ocmsAfterSaleApplyDetailVO.getGoodsCode());
        return refundApplyRecordDetailVO;
    }

    public static RefundApplyRecordDetailVO buildRefundApplyRecordDetailVO(OCMSAfterSaleApplyDetailVO ocmsAfterSaleApplyDetailVO, Map<Integer, ProductLabelVO> productLabelVOMap, Pair<Map<Long, OCMSOrderItemVO>, Map<Long, CombinationChildProductVo>> afsDetailBuildPair) {
        RefundApplyRecordDetailVO refundApplyRecordDetailVO = new RefundApplyRecordDetailVO();
        refundApplyRecordDetailVO.setChannelId(ChannelOrderConvertUtils.sourceBiz2Mid(ocmsAfterSaleApplyDetailVO.getOrderBizType()));
        refundApplyRecordDetailVO.setServiceId(ocmsAfterSaleApplyDetailVO.getServiceId());
        refundApplyRecordDetailVO.setSkuId(ocmsAfterSaleApplyDetailVO.getInstoreSku2());
        refundApplyRecordDetailVO.setUpcCode(ocmsAfterSaleApplyDetailVO.getSkuCode());
        refundApplyRecordDetailVO.setSkuName(ocmsAfterSaleApplyDetailVO.getSkuName());
        refundApplyRecordDetailVO.setSpecification(ocmsAfterSaleApplyDetailVO.getSpecification());
        refundApplyRecordDetailVO.setSellUnit(ocmsAfterSaleApplyDetailVO.getSellUnit());
        refundApplyRecordDetailVO.setUnitPrice(ocmsAfterSaleApplyDetailVO.getOriginalAmt());
        refundApplyRecordDetailVO.setCount(ocmsAfterSaleApplyDetailVO.getCount());
        // 商品线下价格
        refundApplyRecordDetailVO.setRefundAmt(ocmsAfterSaleApplyDetailVO.getOfflinePrice() == null
                ? BigInteger.ZERO.intValue() : ocmsAfterSaleApplyDetailVO.getOfflinePrice());
        refundApplyRecordDetailVO.setTotalRefundAmt(Optional.ofNullable(refundApplyRecordDetailVO.getCount()).orElse(
                BigInteger.ZERO.intValue()) * refundApplyRecordDetailVO.getRefundAmt());
        refundApplyRecordDetailVO.setRefundWeight(ocmsAfterSaleApplyDetailVO.getRefundWeight());
        // 展示类型为退款金额 重置为该商品实际的退款总金额
        if(Integer.valueOf(PriceDisplayType.REFUND_AMOUNT.getCode()).equals(ocmsAfterSaleApplyDetailVO.getPriceDisplayType())){
            refundApplyRecordDetailVO.setTotalRefundAmt(ocmsAfterSaleApplyDetailVO.getRefundAmt());
        }
        refundApplyRecordDetailVO.setSubProductVoList(CombinationProductUtil.getAfsDetailComposeProductList(ocmsAfterSaleApplyDetailVO, afsDetailBuildPair.getValue()));
        refundApplyRecordDetailVO.setErpItemCode(ocmsAfterSaleApplyDetailVO.getErpItemCode());
        refundApplyRecordDetailVO.setGoodsCode(ocmsAfterSaleApplyDetailVO.getGoodsCode());
        refundApplyRecordDetailVO.setChannelLabelList(ProductLabelUtil.buildChannelLabelVOList(ocmsAfterSaleApplyDetailVO.getChannelLabel(), productLabelVOMap));
        refundApplyRecordDetailVO.setLabelSubDesc(ProductLabelUtil.buildAfterSaleChannelLabelSubDesc(ocmsAfterSaleApplyDetailVO.getChannelLabel(), ocmsAfterSaleApplyDetailVO.getChannelPickingStart(), ocmsAfterSaleApplyDetailVO.getChannelPickingEnd()));
        refundApplyRecordDetailVO.setAfsExchangeProduct(ExchangeItemUtil.buildAfsExchangeProduct(ocmsAfterSaleApplyDetailVO, afsDetailBuildPair.getKey()));
        return refundApplyRecordDetailVO;
    }

    public static void addSubProductVoName(List<SubProductVo> subProductVoList, List<CombinationChildProductVo> childProductVoList) {
        if(CollectionUtils.isEmpty(subProductVoList)){
            return;
        }
        if(CollectionUtils.isEmpty(childProductVoList)){
            return;
        }
        for (SubProductVo subProductVo : subProductVoList) {
            if(StringUtils.isEmpty(subProductVo.getName())){
                for (CombinationChildProductVo combinationChildProductVo : childProductVoList) {
                    if(Objects.equals(subProductVo.getServiceId(),combinationChildProductVo.getServiceId())){
                        subProductVo.setName(combinationChildProductVo.getName());
                    }
                }
            }
        }
    }

}
