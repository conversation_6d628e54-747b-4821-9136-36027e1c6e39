package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/4 17:20
 * @Description:
 */
@Getter
@Setter
@ToString
public class QueryDetailRequest implements BaseRequest {

    private String orderId;

    private String channelId;

    /**
     * PC客户端点打印的时候传值
     */
    private String poiId;

    private Boolean containsMaterialSku;


    @Override
    public void selfCheck() {
        AssertUtil.notEmpty(orderId, "订单号不能为空", "orderId");
        AssertUtil.notEmpty(channelId, "渠道编码不能为空", "channelId");
    }
}
