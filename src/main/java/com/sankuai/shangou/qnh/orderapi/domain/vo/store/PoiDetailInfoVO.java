package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.ChannelPoiInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2019/12/13 14:54
 * @Description:
 */
@TypeDoc(
        description = "门店详情，详情中包含门店基础信息，以及关联渠道上的门店信息"
)
@Data
@ApiModel("门店详情")
public class PoiDetailInfoVO {

    @FieldDoc(
            description = "门店编码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店编码", required = true)
    private String poiId;

    @FieldDoc(
            description = "门店名称"
    )
    @ApiModelProperty(name = "门店名称")
    private String poiName;

    @FieldDoc(
            description = "门店地址"
    )
    @ApiModelProperty(name = "门店地址")
    private String address;

    @FieldDoc(
            description = "门店所在城市名"
    )
    @ApiModelProperty(name = "门店所在城市名")
    private String cityName;

    @FieldDoc(
            description = "门店所在区/县名"
    )
    @ApiModelProperty(name = "门店所在区/县名")
    private String areaName;

    @FieldDoc(
            description = "门店联系人"
    )
    @ApiModelProperty(name = "门店联系人")
    private String contact;

    @FieldDoc(
            description = "门店电话"
    )
    @ApiModelProperty(name = "门店电话")
    private String mobile;

    @FieldDoc(
            description = "渠道门店列表"
    )
    @ApiModelProperty(name = "渠道门店列表")
    private List<ChannelPoiInfoVO> channelPois;

    @FieldDoc(description = "经度")
    @ApiModelProperty(name = "经度")
    private Double lon;

    @FieldDoc(description = "纬度")
    @ApiModelProperty(name = "纬度")
    private Double lat;

}
