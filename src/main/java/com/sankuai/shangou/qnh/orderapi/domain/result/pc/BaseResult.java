package com.sankuai.shangou.qnh.orderapi.domain.result.pc;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version V1.0
 * @ClassName: BaseResult
 * @Description: TODO
 * @date 25/06/2018 7:41 PM
 **/
@Setter
@Getter
public class BaseResult {

    private int code;
    private String msg;

    public final static BaseResult SUCCESS = new BaseResult(0, "成功");
    public final static BaseResult FAIL = new BaseResult(1, "系统异常");
    public final static BaseResult PROCESSING = new BaseResult(2, "处理中");
    public final static BaseResult PARAM_INVALID = new BaseResult(101, "param invalid");
    public final static BaseResult NOT_LOGIN = new BaseResult(102, "未登录");
    public final static BaseResult NOT_SECURITY = new BaseResult(103, "not security");
    public final static BaseResult NOT_SELECT = new BaseResult(104, "未选仓");
    public final static BaseResult NOT_BIND = new BaseResult(105, "无绑定关系");
    public final static BaseResult UNAUTHORIZED = new BaseResult(401, "您没有权限");
    public final static BaseResult NOT_FOUND = new BaseResult(404, "not found");
    public final static BaseResult INTERNAL_SERVER_ERROR = new BaseResult(500, "系统繁忙，请稍后重试");
    public final static BaseResult EXCEL_ANALYZE_ERROR = new BaseResult(601, "excel解析失败");
    public final static BaseResult MIF_MEMBER_UPDATE_ASSET = new BaseResult(701, "超时, 请到流水查询页面确认结果");
    public final static BaseResult BIZ_FAIL = new BaseResult(801, "业务失败");
    public final static BaseResult BIZ_STATUS_CHANGE = new BaseResult(802, "业务状态发生变更");
    public final static BaseResult PULL_ONLINE_FAIL = new BaseResult(803, "推线上渠道失败");
    public final static BaseResult BIZ_FAIL_NEED_USER_TO_CONFIRM = new BaseResult(804, "业务失败（失败msg需用户确认）");
    public final static BaseResult INSUFFICIENT = new BaseResult(888, "不足额");
    public final static BaseResult UNCLOSED_ORDER = new BaseResult(1001, "门店有未完成的订单");
    public final static BaseResult SERVICE_UNAVAILABLE = new BaseResult(1002, "系统维护中，请00:00点之后再进行操作，如有疑问请咨询您的渠道经理。");
    public final static BaseResult SPU_GRAY_SERVICE_UNAVAILABLE = new BaseResult(1003, "系统已升级，请刷新页面后再使用");
    public final static BaseResult SUPPLIER_DISABLED = new BaseResult(1101, "网络不佳，请稍后重试");
    public static final BaseResult BATCH_UPDATE_SHARE_STORE_ERROR = new BaseResult(1110, "不可批量修改共享仓门店的履约配置");

    public static final BaseResult NO_SHOPPING_CART = new BaseResult(2005, "没有找到购物车id");
    public static final BaseResult BIND_ACCOUNT_SHOPPING_CART_ID_FAIL = new BaseResult(2006, "绑定账号和购物车id失败");
    public static final BaseResult INVALID_PAGE_SIZE = new BaseResult(2007, "页面参数异常");
    public static final BaseResult NO_TENANTID = new BaseResult(2008, "租户id不能为空");
    public static final BaseResult REPOSITORY_NUM_MORE_THAN_LIMIT = new BaseResult(2021, "查询门店数最多支持200，请分批查询。");

    public static final BaseResult OUT_WAREHOUSE_ORDER_NOT_FOUND = new BaseResult(2022, "出库单不存在。");

    public static final BaseResult WORK_ORDER_HAS_CLAIMED_BY_SYSTEM = new BaseResult(8021, "店员已响应该异常，请保持关注");
    public static final BaseResult WORK_ORDER_HAS_CLAIMED = new BaseResult(8022, "工单正在处理");
    public static final BaseResult WORK_ORDER_HAS_SUBMITTED = new BaseResult(8023, "工单已处理");
    public static final BaseResult BASE_EMPLOYEE_NOT_EXIST = new BaseResult(8024, "员工不存在");

    public static final BaseResult ADD_COMPOSE_SKU_WARNING = new BaseResult(31001, "注意，该商品在已关联此仓库的所有门店中不存在");
    public static final BaseResult QUERY_SKU_SORT_FAILURE = new BaseResult(31002, "查询商品类目出错了");

    public static final BaseResult NO_MATCH_STANDARD_SPU = new BaseResult(41001, "未匹配到标品库商品");
    public static final BaseResult QUERY_OPERATE_LOG_ERROR = new BaseResult(41005, "查询操作日志失败");
    public static final BaseResult QUERY_CHANNEL_LOG_ERROR = new BaseResult(41006, "查询渠道日志失败");


    private BaseResult(int code, String message) {
        this.code = code;
        this.msg = message;
    }

}
