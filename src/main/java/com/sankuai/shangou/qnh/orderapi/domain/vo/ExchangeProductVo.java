package com.sankuai.shangou.qnh.orderapi.domain.vo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * pc app共用dto
 * 部分退 克重退 金额退 组合商品信息
 */
@TypeDoc(
        description = "换货商品"
)
@ApiModel("换货商品")
@Data
public class ExchangeProductVo {

    @FieldDoc(
            description = "被换货数量（缺货数量）", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "被换货数量（缺货数量）", required = true)
    private Double exchangeFromCnt;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "替换的数量（已换数量）", required = true)
    private Double exchangeToCnt;

    @FieldDoc(
            description = "skuId", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "skuId")
    private String skuId;

    @FieldDoc(
            description = "upc码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "upc码", required = true)
    private String upcCode;

    @FieldDoc(
            description = "商品名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称", required = true)
    private String skuName;

    @FieldDoc(
            description = "多张商品图片URL", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "多张商品图片URL", required = true)
    private List<String> multiPicUrl;

    @FieldDoc(
            description = "商品图片URL,多张则以逗号隔开", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品图片URL,多张则以逗号隔开", required = true)
    private String picUrl;

    @FieldDoc(
            description = "规格", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "规格", required = true)
    private String specification;

    @FieldDoc(
            description = "售卖单位", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "售卖单位", required = true)
    private String sellUnit;

    @FieldDoc(
            description = "商品对应的orderItemId", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品对应的orderItemId")
    private Long orderItemId;

    @FieldDoc(
            description = "子商品对应的orderItemId", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "子商品对应的orderItemId")
    private Long childOrderItemId;

    @FieldDoc(
            description = "erp item 编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "erp item 编码")
    public String erpItemCode;

    @FieldDoc(
            description = "商品原单价", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品原单价")
    private String originPrice;

    @FieldDoc(
            description = "当前单价", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "当前单价")
    private String salePrice;

    @FieldDoc(
            description = "商品价格", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "商品价格")
    private String totalPrice;

    @FieldDoc(
            description = "数量或者份数"
    )
    @ApiModelProperty(name = "数量或者份数")
    public String quantity;

    @FieldDoc(
            description = "重量单位 g"
    )
    @ApiModelProperty(name = "重量单位 g")
    public String weight;

    @FieldDoc(description = "平台整单优惠")
    @ApiModelProperty(name = "平台整单优惠")
    public String platDiscount = "0.00";

    @FieldDoc(description = "商家整单优惠")
    @ApiModelProperty(name = "商家整单优惠")
    public String poiDiscount = "0.00";

    @FieldDoc(description = "整合营销推广结算(商家）")
    @ApiModelProperty(name = "整合营销推广结算(商家）")
    public String poiMarketDiscount = "0.00";

    @FieldDoc(description = "整合营销推广结算(供应商)")
    @ApiModelProperty(name = "整合营销推广结算(供应商)")
    public String supplierMarketDiscount = "0.00";

    @FieldDoc(description = "商家单品优惠")
    @ApiModelProperty(name = "商家单品优惠")
    public String poiItemDiscount = "0.00";

    @FieldDoc(description = "平台单品优惠")
    @ApiModelProperty(name = "平台单品优惠")
    public String platItemDiscount = "0.00";

    @FieldDoc(description = "开票价格")
    @ApiModelProperty(name = "开票价格")
    public String billPrice = "0.00";

    @FieldDoc(description = "开票金额")
    @ApiModelProperty(name = "开票金额")
    public String billValue = "0.00";

}
