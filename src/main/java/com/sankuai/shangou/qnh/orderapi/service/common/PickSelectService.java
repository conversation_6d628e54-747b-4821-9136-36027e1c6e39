package com.sankuai.shangou.qnh.orderapi.service.common;

import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.meituan.shangou.saas.order.platform.common.types.DynamicOrderBizType;
import com.meituan.shangou.saas.order.platform.enums.FuseOrderStatusEnum;
import com.sankuai.meituan.reco.pickselect.query.thrift.fulfill.request.PickDurationRequest;
import com.sankuai.meituan.reco.pickselect.query.thrift.order.OrderThriftService;
import com.sankuai.meituan.reco.pickselect.query.thrift.order.dto.OrderKeyDTO;
import com.sankuai.meituan.reco.pickselect.query.thrift.order.dto.OrderLogInfoDTO;
import com.sankuai.meituan.reco.pickselect.query.thrift.order.request.QueryOrderLogIdListRequest;
import com.sankuai.meituan.reco.pickselect.query.thrift.order.response.QueryOrderLogIdListResponse;
import com.sankuai.meituan.reco.pickselect.query.thrift.picking.PickingThriftService;
import com.sankuai.meituan.reco.pickselect.query.thrift.picking.response.OrderPickDurationDetailResponse;
import com.sankuai.meituan.reco.pickselect.query.thrift.picking.response.OrderPickDurationResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.OrderListResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.OrderVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.PickWarnDurationVO;
import com.sankuai.shangou.qnh.orderapi.enums.app.ResultCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.map.MultiKeyMap;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/7/15
 **/
@Service
@Slf4j
public class PickSelectService {
    @Resource(name = "queryPickingThriftService")
    private PickingThriftService queryPickingThriftService;

    @Resource
    private OrderThriftService orderThriftService;

    public void fillExistsPickTaskForWait2PickOrder(OrderListResponse orderListResponse, Long currentStoreId) {
        List<OrderVO> orderVOs = orderListResponse.getOrderList();
        if (CollectionUtils.isEmpty(orderVOs)) {
            return;
        }

        List<OrderVO> wait2PickOrder = orderVOs.stream()
                .filter(v -> Objects.equals(v.getViewFuseOrderStatus(), FuseOrderStatusEnum.MERCHANT_CONFIRMED.getValue())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(wait2PickOrder)) {
            return;
        }
        List<OrderVO> notDispatchOrderList = wait2PickOrder.stream().filter(v -> Objects.isNull(v.getDispatchShopId())).collect(Collectors.toList());
        List<OrderVO> dispatchOrderList = wait2PickOrder.stream().filter(v -> Objects.nonNull(v.getDispatchShopId())).filter(v->Objects.equals(v.getDispatchShopId(), currentStoreId)).collect(Collectors.toList());
        dealWait2PickOrder(notDispatchOrderList, false);
        dealWait2PickOrder(dispatchOrderList, true);

        //转出去订单设置为false
        wait2PickOrder.stream().filter(v -> Objects.nonNull(v.getDispatchShopId()))
                .filter(v -> !Objects.equals(v.getDispatchShopId(), currentStoreId))
                .forEach(v -> v.setExitsPickTask(false));

        // 设置拣货倒计时信息
        setPickWarnDuration(wait2PickOrder);

    }

    private void dealWait2PickOrder(List<OrderVO> wait2PickOrder, boolean isDispatchOrder){

        if (CollectionUtils.isEmpty(wait2PickOrder)){
            return;
        }
        Map<Long, List<OrderVO>> orderMap;
        if (isDispatchOrder){
            orderMap = wait2PickOrder.stream().collect(Collectors.groupingBy(OrderVO::getDispatchShopId));
        }else {
            orderMap = wait2PickOrder.stream().collect(Collectors.groupingBy(OrderVO::getStoreId));
        }
        for (Map.Entry<Long, List<OrderVO>> entry : orderMap.entrySet()){
            fillExistsPickTaskForWait2PickOrderByShop(entry.getValue(), entry.getKey());
        }
    }

    private void fillExistsPickTaskForWait2PickOrderByShop(List<OrderVO> wait2PickOrder, Long shopId){
        QueryOrderLogIdListRequest request = new QueryOrderLogIdListRequest();
        request.setTenantId(wait2PickOrder.get(0).getTenantId());
        request.setOfflineStoreId(shopId);
        request.setOrderKeyDTOList(wait2PickOrder.stream().map(v->{
            OrderKeyDTO orderKeyDTO = new OrderKeyDTO();
            orderKeyDTO.setOrderBizType(DynamicOrderBizType.channelId2OrderBizTypeValue(v.getChannelId()));
            orderKeyDTO.setViewOrderId(v.getChannelOrderId());
            return orderKeyDTO;
        }).collect(Collectors.toList()));

        try{
            QueryOrderLogIdListResponse response = orderThriftService.queryOrderLogIdList(request);
            log.info("orderThriftService.queryOrderLogIdList,request:{},req:{}", request, response);
            if (!Objects.equals(response.getStatus(), com.sankuai.meituan.reco.pickselect.query.common.Status.SUCCESS)){
                log.error("orderThriftService.queryOrderLogIdList,错误,request:{},req:{}", request, response);
                return;
            }
            if (CollectionUtils.isEmpty(response.getOrderLogInfoDTOList())){
                wait2PickOrder.forEach(v->v.setExitsPickTask(false));
                return;
            }
            if (response.getOrderLogInfoDTOList().size() == wait2PickOrder.size()){
                return;
            }
            Set<String> viewOrderIds = response.getOrderLogInfoDTOList().stream().map(OrderLogInfoDTO::getViewOrderId).collect(Collectors.toSet());
            wait2PickOrder.stream().filter(v->!viewOrderIds.contains(v.getChannelOrderId())).forEach(v->v.setExitsPickTask(false));
        }catch (Exception e){
            log.error("orderThriftService.queryOrderLogIdList,错误,request:{}", request, e);
        }
    }

    public void setPickWarnDuration(List<OrderVO> orderList) {
        try {
            Long tenantId = orderList.get(0).getTenantId();
            final List<PickDurationRequest> pickDurationRequests = orderList.stream()
                    .map(item -> new PickDurationRequest(item.getChannelOrderId(), ChannelOrderConvertUtils.convertBizType(item.getChannelId())))
                    .collect(Collectors.toList());
            OrderPickDurationResponse response = queryPickingThriftService.queryPickDuration(pickDurationRequests, tenantId);

            if (response.getStatus().getCode() != ResultCodeEnum.SUCCESS.getCode() || CollectionUtils.isEmpty(response.getPickWarnDuration())) {
                return;
            }
            MultiKeyMap<String, OrderPickDurationDetailResponse> map = new MultiKeyMap<>();
            response.getPickWarnDuration().forEach(item -> map.put(item.getViewOrderId(), String.valueOf(item.getOrderBizType()), item));
            orderList.forEach(item -> {
                final String orderBizType = String.valueOf(ChannelOrderConvertUtils.convertBizType(item.getChannelId()));
                final OrderPickDurationDetailResponse orderPickDurationDetailResponse = map.get(item.getChannelOrderId(), orderBizType);
                if (Objects.nonNull(orderPickDurationDetailResponse)) {
                    PickWarnDurationVO pickWarnDurationVO = new PickWarnDurationVO();
                    pickWarnDurationVO.setWarnDuration(orderPickDurationDetailResponse.getWarnDuration());
                    pickWarnDurationVO.setDeliverTime(orderPickDurationDetailResponse.getDeliverTime());
                    pickWarnDurationVO.setIsReserved(orderPickDurationDetailResponse.getIsReserved());
                    pickWarnDurationVO.setPushTaskTime(orderPickDurationDetailResponse.getPushTaskTime());
                    // set拣货截止时间
                    pickWarnDurationVO.setPickTimeOutTime(orderPickDurationDetailResponse.getPickTimeOutTime());
                    item.setPickWarnDuration(pickWarnDurationVO);
                }
            });
        } catch (Exception e) {
            log.info("setPickWarnDuration error:{}", e.getMessage());
        }
    }
}
