package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@TypeDoc(
        description = "销退单关闭请求对象",
        version = "V1.0"
)
@ApiModel("销退单关闭请求对象")
@Data
public class CloseSaleReturnOrderRequest extends CommonRequest {

    @FieldDoc(description = "销退单单据号")
    @ApiModelProperty(value = "销退单单据号", required = true)
    @NotBlank(message = "销退单单据号不能为空")
    private String saleReturnOrderNo;
}
