/*
 * Copyright (c) 2019 Meituan.com. All Rights Reserved.
 */
package com.sankuai.shangou.qnh.orderapi.domain.result.store;

import com.sankuai.shangou.qnh.orderapi.domain.vo.store.User;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <br><br>
 * Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <br>
 * Date: 2019-03-27 Time: 15:29
 * @since 2.1 权限迁移版本
 */
@Builder
@Data
public class PendingTaskParam {

    private long tenantId;
    private long entityId;
    private int entityType;
    private long storeId;
    private List<Long> storeIds;
    private User user;
    private Set<String> authCodes;
}
