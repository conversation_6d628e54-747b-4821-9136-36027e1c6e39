package com.sankuai.shangou.qnh.orderapi.domain.vo.app;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.reco.pickselect.logic.thrift.print.dto.PrintDeviceInfoDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/***
 * author : <EMAIL>
 * data : 2020/2/4
 * time : 下午4:40
 **/
@TypeDoc(
        description = "订单小票打印状态"
)
@ApiModel("订单小票打印状态")
@Data
public class OrderPrintStatusVo {

    @FieldDoc(
            description = "打印状态 0-准备打印 10-打印中 20-打印成功 30-打印超时 99-第三方服务不可用 999-未知", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "打印状态 0-准备打印 10-打印中 20-打印成功 30-打印超时 99-第三方服务不可用 999-未知", required = true)
    public Integer printStatus;


    @FieldDoc(
            description = "打印失败提示,打印失败，打印超时时有提示文案", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "打印失败提示", required = true)
    private String printFailToast;

    @FieldDoc(
            description = "打印机设备号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "打印机设备号", required = true)
    public PrintDeviceInfoDto deviceInfo;
}
