package com.sankuai.shangou.qnh.orderapi.controller.pc;


import com.dianping.cat.Cat;
import com.meituan.servicecatalog.api.annotations.*;

import com.meituan.shangou.goodscenter.dto.DepotGoodsDetailDto;
import com.meituan.shangou.goodscenter.dto.GoodsExpirationDto;
import com.meituan.shangou.saas.order.management.client.export.dto.request.ExportChannelOrderDetailRequest;
import com.meituan.shangou.saas.order.management.client.export.dto.request.GetExportResultRequest;
import com.meituan.shangou.saas.order.management.client.export.dto.response.ExportResponse;
import com.meituan.shangou.saas.order.management.client.export.dto.response.ExportResultResponse;
import com.meituan.shangou.saas.order.management.client.export.service.ChannelOrderDetailExportThriftService;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.meituan.shangou.saas.order.platform.client.dto.model.OrderLabelModel;
import com.meituan.shangou.saas.order.platform.enums.DistributeStatusEnum;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;

import com.sankuai.meituan.shangou.empower.auth.thrift.enums.AccountTypeEnum;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.AccountInfoVo;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ResponseCodeEnum;
import com.sankuai.meituan.shangou.empower.ocms.thrift.service.ChannelOrderTenantThriftService;

import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.QueryDeliveryProofPhotoResponse;
import com.sankuai.meituan.shangou.saas.common.data.PageResult;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.meituan.shangou.saas.common.file.ExcelUtil;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;

import com.sankuai.shangou.infra.osw.api.org.dto.response.EmployeeDTO;
import com.sankuai.shangou.logistics.delivery.gray.enums.GrayKeyEnum;
import com.sankuai.shangou.logistics.delivery.gray.utils.GrayConfigUtils;
import com.sankuai.shangou.logistics.warehouse.dto.PickConsumableItemDTO;
import com.sankuai.shangou.logistics.warehouse.dto.TradeShippingOrderDTO;
import com.sankuai.shangou.logistics.warehouse.dto.TradeShippingOrderItemDTO;
import com.sankuai.shangou.logistics.warehouse.enums.TradeShippingOrderStatus;
import com.sankuai.shangou.qnh.orderapi.annotation.ResultDataSecurity;
import com.sankuai.shangou.qnh.orderapi.configuration.pc.OrderConfiguration;
import com.sankuai.shangou.qnh.orderapi.constant.pc.Constants;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import com.sankuai.shangou.qnh.orderapi.controller.BaseController;
import com.sankuai.shangou.qnh.orderapi.converter.pc.ChannelOrderConverter;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.*;
import com.sankuai.shangou.qnh.orderapi.domain.request.pc.*;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.*;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.*;
import com.sankuai.shangou.qnh.orderapi.enums.DepotGoodsExpireUnitEnum;
import com.sankuai.shangou.qnh.orderapi.enums.pc.OrderCouldOperateItemEnum;
import com.sankuai.shangou.qnh.orderapi.exception.pc.CommonRuntimeException;
import com.sankuai.shangou.qnh.orderapi.exception.pc.ParamInvalidException;
import com.sankuai.shangou.qnh.orderapi.interceptor.pc.DrunkHorseData;
import com.sankuai.shangou.qnh.orderapi.remote.*;
import com.sankuai.shangou.qnh.orderapi.service.pc.*;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ConverterUtils;
import com.sankuai.shangou.qnh.orderapi.utils.pc.MccConfigUtil;
import com.sankuai.shangou.qnh.orderapi.utils.pc.ResponseHandler;
import com.sankuai.shangou.qnh.orderapi.utils.pc.RpcInvoker;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * @Author: <EMAIL>
 * @Date: 2019/1/2 11:34
 * @Description:
 */
@InterfaceDoc(
        displayName = "中台订单接口",
        type = "restful",
        scenarios = "用于中台订单的查询,审核等操作",
        description = "用于中台订单的查询,审核等操作"
)
@Api(value = "中台订单相关接口")
@Slf4j
@RestController
@RequestMapping("/api/v1/channelOrder")
public class ChannelOrderController extends BaseController {

    @Autowired
    private ChannelOrderService channelOrderService;

    @Autowired
    private ChannelOrderConverter channelOrderConverter;
    @Autowired
    private AccountRemoteService accountClient;
    @Autowired
    private AuthRemoteService authClient;
    @Autowired
    private PoiRemoteService poiClient;
    @Autowired
    private ChannelOrderDetailExportThriftService channelOrderDetailExportThriftService;
    @Resource
    private DeliveryRemoteService deliveryRemoteService;
    @Autowired
    private DeliveryChannelRemoteService deliveryChannelWrapper;
    @Resource
    private RiderDeliveryRemoteService riderDeliveryService;
    @Autowired
    private TradeShippingOrderRemoteService tradeShippingOrderServiceWrapper;
    @Autowired
    private GoodsCenterRemoteService goodsCenterClient;
    @Autowired
    private OswRemoteService oswClient;
    @Autowired
    private SupplyProductClient supplyProductClient;
    @Autowired
    private OrderLabelService orderLabelService;

    public static final String DEFAULT_LONG_STR = "-1";
    public static final String SYSTEM_STOCK_OUT = "系统自动出库";

    @MethodDoc(
            displayName = "部分退款接口",
            description = "部分退款接口",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "部分退款请求"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：session.tenantId=data.tenantId，校验用户所在的门店的权限"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/api/v1/channelOrder/partRefund",
            restExamplePostData = "{\"channelId\":\"string\",\"orderId\":\"string\",\"reason\":\"string\",\"reasonCode\":0,\"refundItems\":[{\"count\":\"string\",\"sku\":\"string\"}]}",
            restExampleResponseData = "{\"code\": 0,\"msg\": \"string\"}"
    )
    @ApiOperation(value = "部分退款接口")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @DataSecurity({
            @SecurityParam(value = "poiId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @RequestMapping(value = "/partRefund", method = RequestMethod.POST)
    public Result partRefund(@RequestBody PartRefundRequest request) {
        return process(request, "partRefund", r -> {
            //命中则走orderBiz
            if (OrderConfiguration.hitOcmsMigration(NumberUtils.toLong(request.getPoiId()))) {
                return convertResult(channelOrderService.tenantPartRefund(request));
            }

            Result result = thriftService.invoke(r
                    , channelOrderConverter::partRefundRequestConvert
                    , channelOrderConverter::partRefundResponseConvert
                    , ChannelOrderTenantThriftService.Iface.class
                    , (s, re) -> RpcInvoker.invoke(() -> s.tenantPartRefundOrder(re))
                    , rs -> ResponseHandler.checkResponseAndStatus(rs, r1 -> r1.getStatus().getCode(), r1 -> r1.getStatus().getMsg()));
            return result;
        });
    }

    @MethodDoc(
            displayName = "退款接口",
            description = "退款接口",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "退款请求"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/api/v1/channelOrder/refund",
            restExamplePostData = "{\"channelId\":\"string\",\"orderId\":\"string\",\"reason\":\"string\",\"reasonCode\":0}",
            restExampleResponseData = "{\"code\": 0,\"msg\": \"string\"}"
    )
    @ApiOperation(value = "退款接口")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @RequestMapping(value = "/refund", method = RequestMethod.POST)
    public Result refund(@RequestBody RefundRequest request) {
        return process(request, "refund", r -> {
            //命中则走orderBiz, 这里获取不了shopId
            if (OrderConfiguration.hitOcmsMigration(0L)) {
                return convertResult(channelOrderService.tenantCancelOrder(request));
            }
            Result result = thriftService.invoke(r
                    , channelOrderConverter::cancelRequestConvert
                    , channelOrderConverter::cancelResponseConvert
                    , ChannelOrderTenantThriftService.Iface.class
                    , (s, re) -> RpcInvoker.invoke(() -> s.tenantCancelOrder(re))
                    , rs -> ResponseHandler.checkResponseAndStatus(rs, r1 -> r1.getStatus().getCode(), r1 -> r1.getStatus().getMsg()));
            return result;
        });
    }

    @MethodDoc(
            displayName = "打印小票接口",
            description = "打印小票接口",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "打印小票请求"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/api/v1/channelOrder/print",
            restExamplePostData = "{\"channelId\":\"string\",\"orderId\":\"string\"}",
            restExampleResponseData = "{\"code\": 0,\"msg\": \"string\"}"
    )
    @ApiOperation(value = "打印小票")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @RequestMapping(value = "/print", method = RequestMethod.POST)
    public Result print(@RequestBody QueryDetailRequest request) {
        return process(request, "print", r -> {
            //命中则走orderBiz, 这里获取不了shopId
            if (OrderConfiguration.hitOcmsMigration(0L)) {
                return convertResult(channelOrderService.printReceipt(request.getOrderId(), NumberUtils.toInt(request.getChannelId()), request.getPoiId()));
            }
            Result result = thriftService.invoke(r
                    , channelOrderConverter::printReceiptReqConvert
                    , channelOrderConverter::printReceiptRespConvert
                    , ChannelOrderTenantThriftService.Iface.class
                    , (s, re) -> RpcInvoker.invoke(() -> s.printReceipt(re))
                    , rs -> ResponseHandler.checkResponseAndStatus(rs, r1 -> r1.getStatus().getCode(), r1 -> r1.getStatus().getMsg()));
            return result;
        });
    }

    @MethodDoc(
            displayName = "手工接单接口",
            description = "手工接单接口",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "手工接单请求"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/api/v1/channelOrder/confirmOrder",
            restExamplePostData = "{\"channelId\":\"string\",\"orderId\":\"string\"}",
            restExampleResponseData = "{\"code\": 0,\"msg\": \"string\"}"
    )
    @ApiOperation(value = "手工接单接口")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @RequestMapping(value = "/confirmOrder", method = RequestMethod.POST)
    public Result confirmOrder(@RequestBody QueryDetailRequest request) {
        return process(request, "confirmOrder", r -> {
                return convertResult(channelOrderService.confirmOrder(request.getOrderId(), NumberUtils.toInt(request.getChannelId())));
        });
    }

    @MethodDoc(
            displayName = "完成拣货接口",
            description = "完成拣货接口",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "完成拣货请求"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/api/v1/channelOrder/pick",
            restExamplePostData = "{\"channelId\":\"string\",\"orderId\":\"string\"}",
            restExampleResponseData = "{\"code\": 0,\"msg\": \"string\"}"
    )
    @ApiOperation(value = "完成接口")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @RequestMapping(value = "/pick", method = RequestMethod.POST)
    public Result pick(@RequestBody QueryDetailRequest request) {
        return process(request, "pick", r -> {
                return convertResult(channelOrderService.completePickUp(request));
        });
    }

    /**
     * 查询订单列表
     *
     * @param request
     * @return
     */
    @MethodDoc(
            displayName = "查询在线订单列表接口",
            description = "查询在线订单列表接口",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "查询在线订单列表请求"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：session.tenantId=data.tenantId，校验用户所在的门店的权限"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/api/v1/channelOrder/queryList",
            restExamplePostData = "{\"page\": 0,\"pageSize\": 0,\"receiverName\": \"string\",\"receiverPhone\": \"string\",\"poiName\": \"string\",\"poiId\": \"string\",\"orderId\": \"string\",\"createStartTime\": \"string\",\"createEndTime\": \"string\",\"status\": \"string\",\"channelIds\": [\"string\"],\"refundTypes\": [\"string\"]}",
            restExampleResponseData = "{\"code\": 0,\"data\": {\"list\": [{\"channelId\": \"string\",\"channelName\": \"string\",\"createTime\": \"string\",\"deliveryMethod\": \"string\",\"merchantAmount\": \"string\",\"orderId\": \"string\",\"paidAmount\": \"string\",\"poiName\": \"string\",\"quantity\": 0,\"receiverAddress\": \"string\",\"receiverName\": \"string\",\"receiverPhone\": \"string\",\"refundType\": \"string\",\"status\": \"string\"}],\"page\": 0,\"pageSize\": 0,\"total\": 0},\"msg\": \"string\"}"
    )
    @ApiOperation(value = "查询在线订单列表")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @DataSecurity({
            @SecurityParam(value = "poiId", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @RequestMapping(value = "/queryList", method = RequestMethod.POST)
    public Result<PageResult<ChannelOrderVO>> queryOrders(@RequestBody ChannelOrderQueryRequest request) {

        return process(request, "queryOrders", r -> {
            ChannelOrderQueryBO channelOrderQueryBO = new ChannelOrderQueryBO(r);
            channelOrderQueryBO.setTenantId(ContextHolder.currentUserTenantId());
            List<Long> poiIds = Lists.newArrayList();
            List<Long> warehouseIds = Lists.newArrayList();
            boolean hasPermission = fillPermissionParam(r.getPoiIdList(), r.getWarehouseIdList(), poiIds, warehouseIds);
            if (!hasPermission) {
                return ResultBuilder.buildSuccess(new PageResult<>(Lists.newArrayList(), r.getPage(), r.getPageSize(), 0));
            }
            channelOrderQueryBO.setPoiIds(poiIds);
            channelOrderQueryBO.setWarehouseIds(warehouseIds);
            channelOrderQueryBO.setClientType(r.getClientType());
            PageResult<ChannelOrderBO> pageResult = channelOrderService.queryOrders(channelOrderQueryBO);
            List<ChannelOrderVO> channelOrderVOS = ConverterUtils.convertList(pageResult.getList(), ChannelOrderBO::toChannelOrderVO);
            List<OrderLabelModel> orderLabelModels = orderLabelService.queryCommonOrderShowLabel(ContextHolder.currentUserTenantId());
            Set<Long> needSortOrderLabelIds = MccConfigUtil.getNeedSortOrderLabelIds();
            channelOrderVOS.forEach(channelOrderVO ->
                    channelOrderVO.setSortedTagList(SortedTagVO.convertSortedTagList(channelOrderVO, needSortOrderLabelIds, orderLabelModels)));
            PageResult<ChannelOrderVO> result = new PageResult<>(channelOrderVOS
                    , pageResult.getPage(), pageResult.getPageSize(), pageResult.getTotal());

            return ResultBuilder.buildSuccess(result);
        });

    }

    /**
     * 填充有权限的门店或仓库id
     * @param paramPoiIds
     * @param paramWarehouseIds
     * @param permissionPoiIds
     * @param permissionWarehouseIds
     * @return
     */
    private boolean fillPermissionParam(List<Long> paramPoiIds, List<Long> paramWarehouseIds, List<Long> permissionPoiIds, List<Long> permissionWarehouseIds) {
        if (CollectionUtils.isNotEmpty(paramPoiIds) || CollectionUtils.isNotEmpty(paramWarehouseIds)) {
            Optional.ofNullable(paramPoiIds).ifPresent(permissionPoiIds::addAll);
            Optional.ofNullable(paramWarehouseIds).ifPresent(permissionWarehouseIds::addAll);
            return true;
        }

        AccountInfoVo accountInfo = accountClient.querySimpleAccountInfoById(ContextHolder.currentUid());
        if (Objects.nonNull(accountInfo) && Objects.equals(accountInfo.getAccountType(), AccountTypeEnum.ADMIN.getValue())) {
            return true;
        }
        Long tenantId = ContextHolder.currentUserTenantId();
        List<PermissionGroupVO> permissionGroupVOS = authClient.batchQueryAllPoiPermissionGroup(tenantId, ContextHolder.currentUid(), Arrays.asList(Constants.AuthType.POI_TYPE, Constants.AuthType.SHAREABLE_WAREHOUSE));
        Map<Integer, List<Long>> type2Code = permissionGroupVOS.stream().collect(Collectors.groupingBy(PermissionGroupVO::getType, Collectors.mapping(item -> Long.valueOf(item.getCode()), Collectors.toList())));
        List<Long> poiIds = type2Code.getOrDefault(Constants.AuthType.POI_TYPE, Lists.newArrayList());
        List<Long> warehouseIds = type2Code.getOrDefault(Constants.AuthType.SHAREABLE_WAREHOUSE, Lists.newArrayList());
        // 区分门店/仓id
        // 查询仓关联的poi
        if (CollectionUtils.isNotEmpty(warehouseIds)) {
            Map<Long, List<Long>> warehouseId2PoiIdMap = poiClient.queryShareableWarehouseRelatedStoreId(tenantId, warehouseIds);
            if (MapUtils.isNotEmpty(warehouseId2PoiIdMap)) {
                poiIds.addAll(warehouseId2PoiIdMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList()));
                poiIds = poiIds.stream().distinct().collect(Collectors.toList());
            }
        }
        if (CollectionUtils.isNotEmpty(poiIds)) {
            permissionPoiIds.addAll(poiIds);
            return true;
        }

        return false;
    }

    /**
     * 异步导出订单详情
     * 导出逻辑实现在mng
     *
     * @param request
     * @return
     */
    @MethodDoc(
            displayName = "异步导出订单详情接口",
            description = "异步导出订单详情",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：session.tenantId=data.tenantId，校验用户所在的门店的权限"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "异步导出订单详情接口")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @DataSecurity({
            @SecurityParam(value = "poiId", type = ParamDataType.ALL_POI, source = ParamSource.QUERY_PARAM)
    })
    @RequestMapping(value = "/exportOrderDetail", method = RequestMethod.GET)
    public Result<String> asyncExportOrdersDetail(ChannelOrderDownloadRequest request) {
        ChannelOrderQueryBO channelOrderQueryBO = new ChannelOrderQueryBO(request);
        channelOrderQueryBO.setTenantId(ContextHolder.currentUserTenantId());
        List<Long> poiIds = Lists.newArrayList();
        List<Long> warehouseIds = Lists.newArrayList();
        fillPermissionParam(request.getPoiIdList(), request.getWarehouseIdList(), poiIds, warehouseIds);
        channelOrderQueryBO.setPoiIds(poiIds);
        channelOrderQueryBO.setWarehouseIds(warehouseIds);
        return process(request, "asyncExportOrdersDetail", r -> {
            ExportChannelOrderDetailRequest req = channelOrderQueryBO.toExportChannelOrderDetailRequest();
            req.setShopIdList(channelOrderQueryBO.getPoiIds());
            ExportResponse response = RpcInvoker.invoke(() -> channelOrderDetailExportThriftService.exportOrdersDetail(req));
            ResponseHandler.checkResponseAndStatus(response, rep -> rep.getStatus().getCode(), rep -> rep.getStatus().getMessage());
            return ResultBuilder.buildSuccess(response.getToken());
        });
    }

    /**
     * 异步导出订单详情任务查
     * 导出逻辑实现在mng
     *
     * @param request
     * @return
     */
    @MethodDoc(
            displayName = "异步导出订单详情接口",
            description = "异步导出订单详情查询",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "异步导出订单任务查询"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @ApiOperation(value = "异步导出订单详情任务查询接口")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @RequestMapping(value = "/getAsyncExportOrdersTaskResult", method = RequestMethod.POST)
    public Result<AsyncExportOrdersTaskVO> getAsyncExportOrdersTaskResult(@RequestBody QueryAsyncExportOrdersTaskRequest request) {
        return process(request, "getAsyncExportOrdersTaskResult", r -> {
            GetExportResultRequest getExportResultRequest = new GetExportResultRequest();
            getExportResultRequest.setToken(request.getToken());
            getExportResultRequest.setOperatorId(ContextHolder.currentUid());
            getExportResultRequest.setTenantId(ContextHolder.currentUserTenantId());
            ExportResultResponse response = RpcInvoker.invoke(() -> channelOrderDetailExportThriftService.getTaskResult(getExportResultRequest));
            ResponseHandler.checkResponseAndStatus(response, rep -> rep.getStatus().getCode(), rep -> rep.getStatus().getMessage());
            return ResultBuilder.buildSuccess(ConverterUtils.nonNullConvert(response, channelOrderConverter::asyncExportOrdersDetailTaskVOConvert));
        });
    }

    /**
     * 查询订单详情
     *
     * @param request
     * @return
     */
    @MethodDoc(
            displayName = "查询中台订单详情接口",
            description = "查询中台订单详情接口",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "查询在线订单列表请求"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/api/v1/channelOrder/detail",
            restExamplePostData = "{\"channelId\": \"100\",\"orderId\": \"001\"}",
            restExampleResponseData = "{\"code\": 0,\"data\": {\"baseInfo\": {\"afterSaleApplyStatus\": \"string\",\"amount\": \"string\",\"channelName\": \"string\",\"createTime\": \"string\",\"deliveryAmount\": \"string\",\"deliveryMethod\": \"string\",\"deliveryOrderCreateTime\": \"string\",\"invoiceTaxNo\": \"string\",\"invoiceTitle\": \"string\",\"merchantAmount\": \"string\",\"needInvoice\": 0,\"orderId\": \"string\",\"packageAmount\": \"string\",\"paidAmount\": \"string\",\"paidOnline\": 0,\"platformAmount\": \"string\",\"poiName\": \"string\",\"receiverAddress\": \"string\",\"receiverName\": \"string\",\"receiverPhone\": \"string\",\"refundReason\": \"string\",\"refundTagId\": \"string\",\"riderName\": \"string\",\"riderPhone\": \"string\",\"status\": \"string\"},\"deliveryDetail\": [{\"deliveryStatus\": \"string\",\"updateTime\": \"string\"}],\"itemInfo\": [{\"isRefund\": \"string\",\"quantity\": 0,\"refundCount\": \"string\",\"sku\": \"string\",\"skuName\": \"string\",\"spec\": \"string\",\"totalPrice\": \"string\",\"unit\": \"string\",\"unitPrice\": \"string\",\"upc\": \"string\"}],\"operateLog\": [{\"opContent\": \"string\",\"opTime\": \"string\",\"opType\": \"string\",\"operator\": \"string\"}],\"promotionInfo\": [{\"agentAmount\": \"string\",\"logisticsBearAmount\": \"string\",\"merchantAmount\": \"string\",\"platformAmount\": \"string\",\"promotionAmount\": \"string\",\"promotionName\": \"string\"}]},\"msg\": \"string\"}"
    )
    @ApiOperation(value = "查询中台订单详情")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @RequestMapping(value = "/detail", method = RequestMethod.POST)
    @ResultDataSecurity
    public Result<ChannelOrderDetailVO> queryDetail(@RequestBody QueryDetailRequest request) {

        return process(request, "queryDetail", r -> {

            ChannelOrderDetailBO channelOrderDetailBO = channelOrderService.queryDetail(request.getOrderId()
                    , ContextHolder.currentUserTenantId(), Integer.valueOf(r.getChannelId()), ContextHolder.currentUid(),request.getContainsMaterialSku());
            ChannelOrderDetailVO detailVO = channelOrderDetailBO.toChannelOrderDetailVO();
            // 查询是否发生过调整
            CommonDataBO<List<OrderAdjustRecordVO>> commonDataBO = channelOrderService.queryAdjustOrderRecord(ContextHolder.currentUserTenantId(),
                    Integer.parseInt(request.getChannelId()), request.getOrderId());
            detailVO.setHasOrderAdjustLog(CollectionUtils.isNotEmpty(commonDataBO.getData()));
            List<OrderLabelModel> orderLabelModels = orderLabelService.queryCommonOrderShowLabel(ContextHolder.currentUserTenantId());
            sortOrderMarks(channelOrderDetailBO, detailVO, orderLabelModels);
            List<DeliveryInfoBO> deliveryInfoBOList=new ArrayList<>();
            if(channelOrderDetailBO.getBaseInfo()!=null){
                deliveryInfoBOList = deliveryRemoteService.queryActiveAndSucDeliveryOrderByOrderId(channelOrderDetailBO.getBaseInfo().getOfflineOrderId());

                //歪马租户填充拣货相关信息
                fillPickInfo(detailVO, request);

                //歪马租户查送达照片
                fillDeliveryInfo(detailVO, channelOrderDetailBO);

            }
            Map<Integer, String> channelMap = null;
            if ((Objects.nonNull(ContextHolder.currentUserTenantId()) || Objects.nonNull(channelOrderDetailBO.getBaseInfo()) || Objects.isNull(channelOrderDetailBO.getBaseInfo().getPoiId())) && !deliveryChannelWrapper.checkTenantAndStore(ContextHolder.currentUserTenantId(),channelOrderDetailBO.getBaseInfo().getPoiId())){
                channelMap = deliveryChannelWrapper.getDeliveryChannelNameMap(deliveryInfoBOList.stream().map(DeliveryInfoBO::getDeliveryChannel).collect(Collectors.toSet()));
                detailVO.setDeliveryInfoList(channelOrderDetailBO.toDeliveryInfoVo(deliveryInfoBOList,channelMap));
            }else {
                detailVO.setDeliveryInfoList(channelOrderDetailBO.toDeliveryInfoVo(deliveryInfoBOList, null));
            }
            return ResultBuilder.buildSuccess(detailVO);

        });
    }

    private void sortOrderMarks(ChannelOrderDetailBO channelOrderDetailBO, ChannelOrderDetailVO detailVO, List<OrderLabelModel> orderLabelModels) {
        List<SortedTagVO> sortedTagVOS = SortedTagVO.convertSortedTagList(channelOrderDetailBO.getBaseInfo(),
               MccConfigUtil.getNeedSortOrderLabelIds(), orderLabelModels);
        detailVO.getBaseInfo().setSortedTagList(sortedTagVOS);
    }

    @MethodDoc(
            displayName = "查询退款原因接口",
            description = "查询退款原因接口",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "查询退款原因请求"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/api/v1/channelOrder/refundReasons",
            restExamplePostData = "{\"channelId\":\"string\",\"orderType\":0}",
            restExampleResponseData = "{\"code\":0,\"data\":{\"list\":[{\"code\":\"string\",\"value\":\"string\"}]},\"msg\":\"string\"}"
    )
    @ApiOperation(value = "查询退款原因")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @RequestMapping(value = "/refundReasons", method = RequestMethod.POST)
    public Result<ListResultData<UiOption>> refundReasons(@RequestBody QueryRefundReasonRequest request) {
        return process(request, "", r -> {
            //命中则走orderBiz, 这里获取不了shopId
            if (OrderConfiguration.hitOcmsMigration(0L)) {
                return convertListResult(channelOrderService.refundReasonList(request));
            }
            Result<List<UiOption>> result = thriftService.invoke(r
                    , channelOrderConverter::refundReasonRequestConvert
                    , channelOrderConverter::refundReasonResponseConvert
                    , ChannelOrderTenantThriftService.Iface.class
                    , (s, re) -> RpcInvoker.invoke(() -> s.refundReasonAndCode(re))
                    , rs -> ResponseHandler.checkResponseAndStatus(rs, r1 -> r1.getStatus().getCode(), r1 -> r1.getStatus().getMsg()));
            return ResultBuilder.buildSuccessListResult(result.getData());
        });
    }

    @MethodDoc(
            displayName = "查询待确认订单列表-支持多门店查询",
            description = "查询待审核订单数量接口",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "查询待审核订单数量请求"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId")
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/api/v1/channelOrder/multiShopConfirmOrderList",
            restExamplePostData = "",
            restExampleResponseData = "{  \"code\": 0,  \"data\": 1,  \"msg\": \"string\"}"
    )
    @ApiOperation(value = "查询待确认订单列表")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @RequestMapping(value = "/multiShopConfirmOrderList", method = RequestMethod.POST)
    public Result<PageResultV2<ConfirmOrderVO>> multiShopConfirmOrderList(@RequestBody MultiShopConfirmOrderQueryRequest request) {
        return process(request, "multiShopConfirmOrderList", r -> {

            List<Long> permissionPoiIds = Lists.newArrayList();
            List<Long> permissionWarehouseIds = Lists.newArrayList();
            boolean hasPermission = fillPermissionParam(r.getPoiIdList(), request.getWarehouseIdList(), permissionPoiIds, permissionWarehouseIds);
            if (!hasPermission) {
                return ResultBuilder.buildSuccess(new PageResultV2<>(Lists.newArrayList(), r.getPage(), r.getPageSize(), 0));
            }
            request.setWarehouseIdList(permissionWarehouseIds);
            request.setPoiIdList(permissionPoiIds);

            //加个orderSerialNumber非数字默认值处理
            if (StringUtils.isNotEmpty(request.getOrderSerialNumber())
                    && !NumberUtils.isDigits(request.getOrderSerialNumber())) {
                request.setOrderSerialNumber(DEFAULT_LONG_STR);
            }
            //命中请求走orderMng
            Result<PageResultV2<ConfirmOrderVO>> pageResultV2Result = ResultBuilder.buildSuccess(channelOrderService.unDoneOrderList(request));
            //歪马web端不需要 出库和打印小票。强制在APP操作
            if (MccConfigUtil.isDhTenant(ContextHolder.currentUserTenantId())) {
                if (Objects.nonNull(pageResultV2Result.getData()) && CollectionUtils.isNotEmpty(pageResultV2Result.getData().getList())) {
                    for (ConfirmOrderVO confirmOrderVO : pageResultV2Result.getData().getList()) {
                        //灰度控制
                        if (MccConfigUtil.isNewPickGrayStore(confirmOrderVO.getShopId())) {
                            List<String> couldOperateItemList = Lists.newArrayList(confirmOrderVO.getCouldOperateItemList());
                            couldOperateItemList.remove(String.valueOf(OrderCouldOperateItemEnum.COMPLETE_PICK.getValue()));
                            couldOperateItemList.remove(String.valueOf(OrderCouldOperateItemEnum.PRINT_RECEIPT.getValue()));
                            confirmOrderVO.setCouldOperateItemList(couldOperateItemList);
                        }
                    }
                }
            }
            return pageResultV2Result;
        });
    }

    /**
     * 查询订单列表-支持多门店查询
     *
     * @param request
     * @return
     */
    @MethodDoc(
            displayName = "查询待审核订单列表接口-支持多门店查询",
            description = "查询待审核订单列表接口",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "查询在线订单列表请求"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "鉴权逻辑为：session.tenantId=data.tenantId")
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/api/v1/channelOrder/multiShopQueryAuditOrderList",
            restExamplePostData = "{\"cancelEndTime\": \"string\",\"cancelStartTime\": \"string\",\"channelIds\": [\"string\"],\"createEndTime\": \"string\",\"createStartTime\": \"string\",\"page\": 0,\"pageSize\": 0,\"poiId\": \"string\",\"poiName\": \"string\",\"receiverName\": \"string\",\"receiverPhone\": \"string\",\"refundType\": \"string\"}",
            restExampleResponseData = "{\"code\": 0,\"data\": {\"list\": [{\"channelId\": \"string\",\"channelName\": \"string\",\"createTime\": \"string\",\"orderId\": \"string\",\"paidAmt\": \"string\",\"poiName\": \"string\",\"receiveAddress\": \"string\",\"receiverName\": \"string\",\"receiverPhone\": \"string\",\"refundAmt\": \"string\",\"refundTagId\": \"string\",\"refundType\": \"string\",\"status\": \"string\"}],\"page\": 0,\"pageSize\": 0,\"total\": 0},\"msg\": \"string\"}"
    )
    @ApiOperation(value = "查询待审核订单列表")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @RequestMapping(value = "/multiShopQueryAuditOrderList", method = RequestMethod.POST)
    public Result<PageResult<AuditOrderVO>> MultiShopQueryNeedApproveOrders(@RequestBody MultiShopAuditOrderQueryRequest request) {

        return process(request, "multiShopQueryNeedApproveOrders", r -> {
            AuditOrderListQueryBO channelOrderQueryBO = new AuditOrderListQueryBO(r);
            channelOrderQueryBO.setTenantId(ContextHolder.currentUserTenantId());
            List<Long> poiIds = Lists.newArrayList();
            List<Long> warehouseIds = Lists.newArrayList();
            boolean hasPermission = fillPermissionParam(r.getPoiIdList(), r.getWarehouseIdList(), poiIds, warehouseIds);
            if (!hasPermission) {
                return ResultBuilder.buildSuccess(new PageResult<>(Lists.newArrayList(), r.getPage(), r.getPageSize(), 0));
            }
            channelOrderQueryBO.setPoiIds(poiIds);
            channelOrderQueryBO.setWarehouseIds(warehouseIds);
            //加个orderSerialNumber非数字默认值处理
            if (StringUtils.isNotEmpty(channelOrderQueryBO.getOrderSerialNumber())
                    && !NumberUtils.isDigits(channelOrderQueryBO.getOrderSerialNumber())) {
                channelOrderQueryBO.setOrderSerialNumber(DEFAULT_LONG_STR);
            }

//            if(channelOrderQueryBO.getChannelIds()!=null) {
//                channelOrderQueryBO.getChannelIds().remove();
//            }
            channelOrderQueryBO.setClientType(r.getClientType());
            PageResult<AuditOrderBO> pageResult = channelOrderService.queryAuditOrders(channelOrderQueryBO);

            List<AuditOrderVO> channelOrderVOS = ConverterUtils.convertList(pageResult.getList(), AuditOrderBO::toChannelOrderVO);

            PageResult<AuditOrderVO> result = new PageResult<>(channelOrderVOS
                    , pageResult.getPage(), pageResult.getPageSize(), pageResult.getTotal());

            return ResultBuilder.buildSuccess(result);
        });

    }

    /**
     * 查询超时订单接口,歪马使用
     *
     * @param request
     * @return
     */
    @MethodDoc(
            displayName = "查询超时订单接口",
            description = "查询超时订单接口",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "查询超时订单请求"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：session.tenantId=data.tenantId，校验用户所在的门店的权限"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/api/v1/channelOrder/queryLateOrderList",
            restExamplePostData = "{\"page\": 0,\"pageSize\": 0,\"receiverName\": \"string\",\"receiverPhone\": \"string\",\"poiName\": \"string\",\"poiId\": \"string\",\"orderId\": \"string\",\"createStartTime\": \"string\",\"createEndTime\": \"string\",\"status\": \"string\",\"channelIds\": [\"string\"],\"refundTypes\": [\"string\"]}",
            restExampleResponseData = "\"code\": 0,\n" +
                    "  \"msg\": \"string\",\n" +
                    "  \"data\": {\n" +
                    "    \"list\": [\n" +
                    "      {\n" +
                    "        \"orderId\": \"string\",\n" +
                    "        \"poiName\": \"string\",\n" +
                    "        \"channelId\": \"string\",\n" +
                    "        \"actualPayAmount\": \"string\",\n" +
                    "        \"orderStatus\": \"string\",\n" +
                    "        \"hasCompensated\": \"string\",\n" +
                    "        \"orderType\": \"string\",\n" +
                    "        \"createTime\": \"string\",\n" +
                    "        \"estimateArrivalTime\": 0,\n" +
                    "        \"actualArrivalTime\": \"string\"\n" +
                    "      }\n" +
                    "    ],\n" +
                    "    \"page\": 0,\n" +
                    "    \"pageSize\": 0,\n" +
                    "    \"total\": 0\n" +
                    "  }")
    @ApiOperation(value = "查询超时订单接口")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @DataSecurity({
            @SecurityParam(value = "storeIds", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_BODY)
    })
    @RequestMapping(value = "/queryLateOrderList", method = RequestMethod.POST)
    @DrunkHorseData
    public Result<PageResultV2<DhLateOrderVO>> queryLateOrderList(@RequestBody QueryLateOrderRequest request) {
        List<Long> poiIds = Lists.newArrayList();
        List<Long> warehouseIds = Lists.newArrayList();
        boolean hasPermission = fillPermissionParam(request.getStoreIds(), null, poiIds, warehouseIds);
        if (!hasPermission) {
            return ResultBuilder.buildSuccess(new PageResultV2<>(Lists.newArrayList(), request.getPage(), request.getPageSize(), 0));
        }
        request.setStoreIds(poiIds);

        return ResultBuilder.buildSuccess(channelOrderService.queryLateOrderVOList(request));
    }

    @MethodDoc(
            displayName = "收到拒收商品接口",
            description = "收到拒收商品接口",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "收到拒收商品请求"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/api/v1/channelOrder/receiveRejectGoods",
            restExamplePostData = "",
            restExampleResponseData = "{  \"code\": 0,  \"data\": 1,  \"msg\": \"string\"}"
    )
    @ApiOperation(value = "收到拒收商品")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @RequestMapping(value = "/receiveRejectGoods", method = RequestMethod.POST)
    public Result receiveRejectGoods(@RequestBody QueryDetailRequest request) {
        return process(request, "receiveRejectGoods", r -> {

            Result result = thriftService.invoke(r
                    , channelOrderConverter::receiveRefundRequestConvert
                    , channelOrderConverter::receiveRefundResponseConvert
                    , ChannelOrderTenantThriftService.Iface.class
                    , (s, re) -> RpcInvoker.invoke(() -> s.tenantReceiveRefundProducts(re))
                    , rs -> ResponseHandler.checkResponseAndStatus(rs, r1 -> r1.getStatus().getCode(), r1 -> r1.getStatus().getMsg()));
            return result;
        });
    }

    /**
     * 退款审核
     *
     * @param request
     * @return
     */
    @MethodDoc(
            displayName = "中台订单审核接口",
            description = "中台订单审核接口",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "中台订单审核请求"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：session.tenantId=data.tenantId"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/api/v1/channelOrder/approve",
            restExamplePostData = "{\"channelId\": \"string\",\"decision\": \"string\",\"note\": \"string\",\"orderId\": \"string\",\"refundTagId\": \"string\"}",
            restExampleResponseData = "{\"code\": 0,\"msg\": \"成功\"}"
    )
    @ApiOperation(value = "中台订单审核")
    @MethodLog(logRequest = true, logResponse = true, logger = "http")
    @RequestMapping(value = "/approve", method = RequestMethod.POST)
    public Result approve(@RequestBody ApproveRefundRequest request) {

        return process(request, "approve", r -> {
            ApproveRefundBO approveRefundBO = new ApproveRefundBO(request);
            approveRefundBO.setTenantId(ContextHolder.currentUserTenantId());
            approveRefundBO.setOperatorId(ContextHolder.currentUid());
            approveRefundBO.setOperator(ContextHolder.currentAccount());
            approveRefundBO.setChannelId(Integer.valueOf(r.getChannelId()));
            approveRefundBO.setAgree(StringUtils.equalsIgnoreCase(r.getDecision(), "AGREE"));
            approveRefundBO.setReason(r.getNote());
            approveRefundBO.setRefundTagId(Integer.parseInt(r.getRefundTagId()));

            channelOrderService.approveRefund(approveRefundBO);

            return ResultBuilder.buildSuccessResult();
        });

    }

    /**
     * 超时订单列表下载
     */
    @MethodDoc(
            displayName = "超时订单列表下载接口",
            description = "超时订单列表下载接口",
            parameters = {
                    @ParamDoc(
                            paramType = ParamType.REQUEST_BODY,
                            name = "request",
                            description = "超时订单列表导出请求"
                    )
            },
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：session.tenantId=data.tenantId，校验用户所在的门店的权限"),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            },
            returnValueDescription = "详见Result",
            restExampleUrl = "http://fnsaas.waimai.dev.sankuai.com/apiproxy/api/v1/channelOrder/downloadLateOrders",
            restExamplePostData = "",
            restExampleResponseData = "{  \"code\": 0,  \"data\": 1,  \"msg\": \"string\"}"
    )
    @ApiOperation(value = "超时订单列表下载")
    @DataSecurity({
            @SecurityParam(value = "storeIds", type = ParamDataType.ALL_POI, source = ParamSource.QUERY_PARAM)
    })
    @RequestMapping(value = "/downloadLateOrders", method = RequestMethod.GET)
    @DrunkHorseData
    public Result downloadLateOrders(LateOrderDownloadRequest request, HttpServletResponse response) {
        try {
            List<Long> poiIds = Lists.newArrayList();
            List<Long> warehouseIds = Lists.newArrayList();
            boolean hasPermission = fillPermissionParam(request.getStoreIds(), null, poiIds, warehouseIds);
            if (!hasPermission) {
                return ResultBuilder.buildSuccessResult();
            }
            request.setStoreIds(poiIds);

            String[] titles = Constants.ExcelExportTitles.LATE_ORDER_TITLES.split(",");
            List<DhLateOrderVO> queryAllLateOrderVOList = channelOrderService.queryAllLateOrderVOList(request);

            List<HashMap<String, String>> datas = ConverterUtils.convertList(queryAllLateOrderVOList, DhLateOrderVO::toExcelMap);
            HSSFWorkbook wb = ExcelUtil.exportExcelFile("超时订单列表", Arrays.asList(titles), datas);

            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = "超时订单列表" + DateFormatUtils.format(new Date(), "yyyyMMdd") + ".xls";
            fileName = new String(fileName.getBytes(), "ISO-8859-1");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName);
            OutputStream os = response.getOutputStream();
            wb.write(os);

            return ResultBuilder.buildSuccessResult();
        } catch (BizException | ParamInvalidException e) {
            log.warn("downloadOrders error", e);
            return ResultBuilder.buildFailResult(e.getMessage());
        } catch (Exception e) {
            log.error("downloadOrders error", e);
            return ResultBuilder.buildFailResult();
        }
    }

    private <T> Result<ListResultData<T>> convertListResult(CommonDataBO<List<T>> result) {
        if (result.getSuccess()) {
            return ResultBuilder.buildSuccessListResult(result.getData());
        } else {
            return ResultBuilder.buildBizFailResult(result.getMessage(), null);
        }
    }

    public void fillDeliveryInfo(ChannelOrderDetailVO detailVO, ChannelOrderDetailBO channelOrderDetailBO) {
        try {
            if (MccConfigUtil.getWmsjTenantIds().contains(ContextHolder.currentUserTenantId())) {
                QueryDeliveryProofPhotoResponse proofPhotoResponse = riderDeliveryService.getDeliveryProofPhotoInfo(channelOrderDetailBO.getBaseInfo().getOfflineOrderId());
                List<DeliveryCompleteInfoVO.ProofPhotoInfo> proofPhotoInfoList = proofPhotoResponse.getTDeliveryProofPhotoInfoList().stream().map(photoInfo -> {
                    DeliveryCompleteInfoVO.ProofPhotoInfo proofPhotoInfoVO = new DeliveryCompleteInfoVO.ProofPhotoInfo();
                    proofPhotoInfoVO.setAuditStatus(photoInfo.getAuditStatus());
                    proofPhotoInfoVO.setProofPhotoUrl(photoInfo.getUrl());
                    return proofPhotoInfoVO;
                }).collect(Collectors.toList());

                DeliveryCompleteInfoVO deliveryCompleteInfoVO = new DeliveryCompleteInfoVO();
                deliveryCompleteInfoVO.setDeliveryProofPhotoList(proofPhotoInfoList);
                deliveryCompleteInfoVO.setSignType(proofPhotoResponse.getSignType());
                deliveryCompleteInfoVO.setIsWeakNetwork(proofPhotoResponse.getIsWeakNetwork());

                deliveryCompleteInfoVO.setRiderName(channelOrderDetailBO.getBaseInfo().getRiderName());


                deliveryCompleteInfoVO.setRiderAccountName(channelOrderDetailBO.getBaseInfo().getRiderAccountName());

                if(!Objects.equals(channelOrderDetailBO.getBaseInfo().getIsBooking(), true)
                        && Objects.nonNull(channelOrderDetailBO.getDeliveryDetail())) {
                    List<ChannelOrderDetailBO.DeliveryDetail> deliveryDetail = channelOrderDetailBO.getDeliveryDetail();

                    //相同操作节点，取最后一次记录
                    Map<Integer, ChannelOrderDetailBO.DeliveryDetail> deliveryDetailMap = deliveryDetail.stream()
                            .filter(logDetail -> isDeliveryStatusLog(logDetail.getDeliveryStatus()))
                            .sorted(Comparator.comparing(ChannelOrderDetailBO.DeliveryDetail::getUpdateTime))
                            .collect(Collectors.toMap(ChannelOrderDetailBO.DeliveryDetail::getTargetStatus, Function.identity(), (k1, k2) -> k2));
                    if (deliveryDetailMap.containsKey(DistributeStatusEnum.RIDER_DELIVERED.getValue())) {
                        long deliveryDoneTime = deliveryDetailMap.get(DistributeStatusEnum.RIDER_DELIVERED.getValue()).getUpdateTime().getTime();
                        Long payTime = channelOrderDetailBO.getBaseInfo().getPayTime();
                        Long fulfillDurationInMillSnd = (deliveryDoneTime - payTime) / 1000;
                        //计算整单履约时长
                        deliveryCompleteInfoVO.setFulfillDuration(new BigDecimal(fulfillDurationInMillSnd)
                                .divide(new BigDecimal(60), 2, RoundingMode.HALF_UP).toString());

                        if (deliveryDetailMap.containsKey(DistributeStatusEnum.RIDER_TAKE_GOODS.getValue())) {
                            Long takeGoodsTime = deliveryDetailMap.get(DistributeStatusEnum.RIDER_TAKE_GOODS.getValue()).getUpdateTime().getTime();
                            Long inTransitDurationInMillSnd = (deliveryDoneTime - takeGoodsTime) / 1000;
                            //计算在途时长
                            deliveryCompleteInfoVO.setInTransitDuration(new BigDecimal(inTransitDurationInMillSnd)
                                    .divide(new BigDecimal(60), 2, RoundingMode.HALF_UP).toString());
                        }
                    }

                    if(deliveryDetailMap.containsKey(DistributeStatusEnum.RIDER_TAKE_GOODS.getValue())) {
                        Long takeGoodsTime = deliveryDetailMap.get(DistributeStatusEnum.RIDER_TAKE_GOODS.getValue()).getUpdateTime().getTime();
                        Long payTime = channelOrderDetailBO.getBaseInfo().getPayTime();

                        //计算出仓时长
                        deliveryCompleteInfoVO.setOutWarehouseDuration(new BigDecimal((takeGoodsTime - payTime) / 1000)
                                .divide(new BigDecimal(60), 2, RoundingMode.HALF_UP).toString());
                    }
                }

                detailVO.setDeliveryCompleteInfo(deliveryCompleteInfoVO);
            }
        } catch(Exception e) {
            //不影响主流程
            log.warn("查询歪马骑手送达照片失败,orderId:{}", channelOrderDetailBO.getBaseInfo().getOfflineOrderId(), e);
        }
    }

    public void fillPickInfo(ChannelOrderDetailVO detailVO, QueryDetailRequest request) {
        try {
            Long tenantId = ContextHolder.currentUserTenantId();
            Long poiId = detailVO.getBaseInfo().getPoiId();
            //非歪马租户直接返回
            if (!MccConfigUtil.getWmsjTenantIds().contains(tenantId)) {
                return;
            }

            //查拣货单信息
            List<TradeShippingOrderDTO> tradeShippingOrderDTOS = tradeShippingOrderServiceWrapper.getByTradeOrderNos(poiId, ChannelOrderConvertUtils.convertBizType(Integer.parseInt(request.getChannelId())), Collections.singletonList(request.getOrderId()));
            if (CollectionUtils.isEmpty(tradeShippingOrderDTOS)) {
                throw new CommonRuntimeException("未查询到出库单");
            }
            TradeShippingOrderDTO tradeShippingOrderDTO = tradeShippingOrderDTOS.get(0);


            List<String> goodsIds = tradeShippingOrderDTO.getItems().stream().map(TradeShippingOrderItemDTO::getSkuId).distinct().collect(Collectors.toList());

            //查货品信息
            List<DepotGoodsDetailDto> depotGoodsDetailDtos = goodsCenterClient.queryGoodsInfo(tenantId, poiId, goodsIds);

            detailVO.setPickingCheckPictureUrlList(tradeShippingOrderDTO.getPickingCheckPictureUrlList());
            detailVO.setPickItemInfoList(buildPickItemInfoList(tradeShippingOrderDTO, depotGoodsDetailDtos));
            // 出库订单三组图片
            detailVO.setPackingPictureInfo(new ChannelOrderDetailVO.PackingPictureInfo().copyFromPackingPicDTO(
                    tradeShippingOrderDTO.getPackingPictureInfo()));

            PickInfoVO pickInfoVO = new PickInfoVO();
            pickInfoVO.setPickingCheckPictureUrlList(tradeShippingOrderDTO.getPickingCheckPictureUrlList());
            pickInfoVO.setPickItemInfoList(buildPickItemInfoList(tradeShippingOrderDTO, depotGoodsDetailDtos));


            if (Objects.nonNull(tradeShippingOrderDTO.getOperatorId()) &&
                    (Objects.equals(tradeShippingOrderDTO.getStatus(), TradeShippingOrderStatus.FINISH.getCode()) || Objects.equals(tradeShippingOrderDTO.getStatus(), TradeShippingOrderStatus.INVALID.getCode()))) {
                if (Objects.equals(tradeShippingOrderDTO.getOperatorId(), 0L)) {
                    pickInfoVO.setPickerName(SYSTEM_STOCK_OUT);
                    pickInfoVO.setPickerAccountName(SYSTEM_STOCK_OUT);
                } else {
                    Optional<EmployeeDTO> employeeDTOOpt = oswClient.queryEmpByAccountId(tradeShippingOrderDTO.getMerchantId(), tradeShippingOrderDTO.getOperatorId());
                    if (employeeDTOOpt.isPresent()) {
                        pickInfoVO.setPickerAccountName(employeeDTOOpt.get().getAccountName());
                        pickInfoVO.setPickerName(employeeDTOOpt.get().getEmpName());
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(tradeShippingOrderDTO.getPickConsumableItems())) {
                pickInfoVO.setConsumableItemInfoList(
                        tradeShippingOrderDTO.getPickConsumableItems().stream().map(consumable -> {
                            ConsumableItemVO consumableItemVO = new ConsumableItemVO();
                            consumableItemVO.setSkuName(consumable.getSkuName());
                            consumableItemVO.setStockoutQuantity(consumable.getQuantity());
                            if (CollectionUtils.isNotEmpty(consumable.getEnteringTypeInfos())) {
                                Map<String, Integer> enteringTypeMap = consumable.getEnteringTypeInfos().stream()
                                        .collect(Collectors.toMap(PickConsumableItemDTO.EnteringTypeInfoDTO::getCode, PickConsumableItemDTO.EnteringTypeInfoDTO::getCodeEnteringType, (k1, k2) -> k2));
                                consumableItemVO.setCodeEnteringType(enteringTypeMap);
                            }
                            return consumableItemVO;
                        }).collect(Collectors.toList()));
            }

            pickInfoVO.setCompleteTime(Optional.ofNullable(tradeShippingOrderDTO.getShipTime()).map(shipTime -> shipTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).orElse(null));
            detailVO.setPickInfo(pickInfoVO);

            appendHighPriceTag(tenantId, detailVO);
        } catch (Exception e) {
            //不影响主流程
            Cat.logEvent("ADAPT_SH", "ORDER_DETAIL_QUERY_PICK_INFO_FAIL");
            log.warn("查询拣货信息失败,orderId:{}", request.getOrderId(), e);
        }
    }

    //把配送异常上报、暂停配送等日志过滤掉
    private boolean isDeliveryStatusLog(String statusDesc) {
        if (StringUtils.isBlank(statusDesc)) {
            return false;
        }
        return Arrays.stream(DistributeStatusEnum.values())
                .anyMatch(distributeStatusEnum -> statusDesc.startsWith(distributeStatusEnum.getDesc()));
    }

    private void appendHighPriceTag(Long tenantId, ChannelOrderDetailVO channelOrderDetailVO){
        try {
            if (Objects.isNull(channelOrderDetailVO)
                    || Objects.isNull(channelOrderDetailVO.getBaseInfo())
                    || !MccConfigUtil.isHighPriceTagGrayStore(channelOrderDetailVO.getBaseInfo().getPoiId())) {
                return ;
            }

            List<String> skuIds = channelOrderDetailVO.getPickInfo().getPickItemInfoList()
                    .stream()
                    .map(PickItemVO::getSkuId)
                    .collect(Collectors.toList());

            Map<String, Boolean> productIsHighPriceMap = supplyProductClient.batchGetProductHighPriceTag(tenantId, skuIds);

            for (PickItemVO pickItem : channelOrderDetailVO.getPickItemInfoList()) {
                pickItem.setIsHighWacGoods(productIsHighPriceMap.getOrDefault(pickItem.getSkuId(), false));
            }

            for (PickItemVO pickItem : channelOrderDetailVO.getPickInfo().getPickItemInfoList()) {
                pickItem.setIsHighWacGoods(productIsHighPriceMap.getOrDefault(pickItem.getSkuId(), false));
            }
        } catch (Exception e) {
            log.warn("查询商品高价值标签失败", e);
            Cat.logEvent("QUERY_HIGH_PRICE_TAG", "FAIL");
        }
    }

    private List<PickItemVO> buildPickItemInfoList(TradeShippingOrderDTO tradeShippingOrderDTO,
                                                   List<DepotGoodsDetailDto> depotGoodsDetailDtos) {
        if (Objects.isNull(tradeShippingOrderDTO)) {
            return Collections.emptyList();
        }

        Map<String, GoodsExpirationDto> goodsExpirationMap = depotGoodsDetailDtos.stream()
                .collect(Collectors.toMap(DepotGoodsDetailDto::getGoodsId, DepotGoodsDetailDto::getExpirationInfo, (k1, k2) -> k2));


        return tradeShippingOrderDTO.getItems().stream().map(task -> {
            PickItemVO pickItemVO = new PickItemVO();
            pickItemVO.setAttribute(task.getTemperatureZoneCode());
            pickItemVO.setShouldPickNum(task.getPlannedQty().subtract(task.getRefundQty()).intValue());
            pickItemVO.setIsSnProduct(task.getIsManagementSnCode());
            pickItemVO.setSnCodeEnteringType(task.getSnCodeEnteringTypeMap());
            pickItemVO.setStockoutEnteringType(task.getDrunkHorsePickType());
            pickItemVO.setProductName(task.getSkuName());
            pickItemVO.setSkuId(task.getSkuId());
            pickItemVO.setUpcList(buildUpcAndBoxCodeSet(task));
            // 填充效期相关字段
            if (goodsExpirationMap.containsKey(task.getSkuId())) {
                GoodsExpirationDto expirationDto = goodsExpirationMap.get(task.getSkuId());
                fillExpirationInfoForPickItem(pickItemVO, expirationDto, tradeShippingOrderDTO.getWarehouseId());
            }
            if (GrayConfigUtils.judgeIsGrayStore(tradeShippingOrderDTO.getMerchantId(), tradeShippingOrderDTO.getWarehouseId(),
                    GrayKeyEnum.SEAL_DELIVERY.getGrayKey(), false)) {
                pickItemVO.setIsSealDelivery(task.getNeedSealDelivery());
            }

            return pickItemVO;
        }).collect(Collectors.toList());
    }

    /**
     * 填充效期相关字段
     *
     * @param pickItemVO     拣货任务明细
     * @param expirationDto  效期信息
     * @param warehouseId    仓ID
     */
    private void fillExpirationInfoForPickItem(PickItemVO pickItemVO, GoodsExpirationDto expirationDto, Long warehouseId) {
        if (expirationDto == null) {
            pickItemVO.setIsShortExpirationGoods(false);
            return;
        }
        pickItemVO.setExpiration(expirationDto.getExpireForUnit());
        pickItemVO.setExpirationUnit(convert2Desc(expirationDto.getExpireUnit()));

        DepotGoodsExpireUnitEnum unit = DepotGoodsExpireUnitEnum.findByCode(expirationDto.getExpireUnit());
        // 设置商品是否是短保品。根据门店灰度，取不同的字段：
        // （1）灰度门店内：取 (expireForUnit + expireUnit) 两个字段，统一换算成天进行处理
        if (MccConfigUtil.goodsQuerySwitch2NewFields(warehouseId) && expirationDto.getExpireForUnit() != null && unit != null) {
            pickItemVO.setIsShortExpirationGoods(MccConfigUtil.getShortExpirationThreshold() > (expirationDto.getExpireForUnit() * unit.getRatio()));
        }
        else if (expirationDto.getExpire() != null) {
            //  （2）灰度门店外（或者是当新字段为空时使用老字段兜底）：保持老逻辑不变，取 expire 字段
            //  全量一段时间后，可直接删除这个 else if 和上面的 MccConfigUtil.goodsQuerySwitch2NewFields(warehouseId) 判断条件
            pickItemVO.setIsShortExpirationGoods(MccConfigUtil.getShortExpirationThreshold() > expirationDto.getExpire());
        }
        else {
            // 是否是短保品，兜底默认值
            pickItemVO.setIsShortExpirationGoods(false);
        }
    }

    private  List<String> buildUpcAndBoxCodeSet(TradeShippingOrderItemDTO itemDTO) {
        Set<String> upcAndBoxCodeList = new HashSet<>();
        if (CollectionUtils.isNotEmpty(itemDTO.getBarCodes())) {
            upcAndBoxCodeList.addAll(itemDTO.getBarCodes());
        }

        if (CollectionUtils.isNotEmpty(itemDTO.getBoxCodes())) {
            upcAndBoxCodeList.addAll(itemDTO.getBoxCodes());
        }

        return new ArrayList<>(upcAndBoxCodeList);
    }

    private String convert2Desc(String expireUnit) {
        if(StringUtils.isBlank(expireUnit)) {
            return "";
        }
        //DAY-日;MONTH-月;YEAR-年
        switch (expireUnit) {
            case "DAY":
                return "天";
            case "MONTH":
                return "个月";
            case "YEAR":
                return "年";
            default:
                return "";
        }

    }

    private Result convertResult(CommonResultBO response) {
        Result result = new Result();
        result.setCode(BooleanUtils.isTrue(response.getSuccess()) ?
                ResponseCodeEnum.SUCCESS.getValue() :
                ResponseCodeEnum.FAILED.getValue());
        result.setMsg(response.getMessage());
        return result;
    }


}
