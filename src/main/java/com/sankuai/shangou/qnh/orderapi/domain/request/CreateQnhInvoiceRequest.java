package com.sankuai.shangou.qnh.orderapi.domain.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/12/5  5:21 下午
 * @since 1.0.0
 */
@TypeDoc(
        description = "开发票入参"
)
@ApiModel("开发票入参")
@Getter
@Setter
@ToString
@NoArgsConstructor
public class CreateQnhInvoiceRequest {
    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道ID")
    @NotNull
    private Integer channelId;

    @FieldDoc(
            description = "渠道订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道订单号")
    @NotNull
    public String channelOrderId;

    @FieldDoc(
            description = "秘钥", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "秘钥")
    @NotNull
    public String orderToken;
}
