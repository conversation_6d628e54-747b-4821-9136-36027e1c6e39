package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.dianping.cat.util.StringUtils;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.meituan.shangou.saas.o2o.dto.model.BizOrderMoneyRefundItemModel;
import com.meituan.shangou.saas.o2o.dto.request.BizOrderMoneyRefundRequest;
import com.meituan.shangou.saas.order.platform.common.types.DynamicChannelType;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.MoneyRefundItemVo;
import com.sankuai.shangou.qnh.orderapi.exception.pc.ParamInvalidException;
import com.sankuai.shangou.qnh.orderapi.utils.pc.AssertUtil;
import lombok.Data;

import java.text.ParseException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Created by suxiaoyu on 2023/3/13 10:42
 */
@TypeDoc(
        description = "金额退request"
)
@Data
public class OrderMoneyRefundRequest {

    @FieldDoc(
            description = "渠道id"
    )
    private Integer channelId;

    @FieldDoc(
            description = "渠道订单号"
    )
    private String channelOrderId;

    @FieldDoc(
            description = "退款商品列表"
    )
    private List<MoneyRefundItemVo> refundItems;

    @FieldDoc(
            description = "退款原因编码"
    )
    private String refundReasonCode;

    @FieldDoc(
            description = "退款原因"
    )
    private String refundReason;

    public void selfCheck() throws ParseException {
        AssertUtil.notEmpty(refundItems, "退款商品列表不能为空");
        AssertUtil.notNull(channelId, "渠道id不能为空");
        AssertUtil.notNull(channelOrderId, "渠道订单号不能为空");
        if (DynamicChannelType.MEITUAN.getChannelId() == channelId) {
            Optional<MoneyRefundItemVo> refundCountIsNull = refundItems.stream()
                    .filter(Objects::nonNull)
                    .filter(i -> Objects.isNull(i.getRefundCount())).findAny();
            if (refundCountIsNull.isPresent()) {
                throw new ParamInvalidException("美团渠道商品退款数量不能为空");
            }
            Optional<MoneyRefundItemVo> refundMoneyIsNull = refundItems.stream()
                    .filter(Objects::nonNull)
                    .filter(i -> Objects.isNull(i.getRefundMoney())).findAny();
            if (refundMoneyIsNull.isPresent()) {
                throw new ParamInvalidException("美团渠道商品退款金额不能为空");
            }
            Optional<MoneyRefundItemVo> any = refundItems.stream().filter(i -> i.getRefundCount() <= 0).findAny();
            if (any.isPresent()) {
                throw new ParamInvalidException("美团渠道商品数量不能小于1");
            }
        }
        if (DynamicChannelType.TAO_XIAN_DA.getChannelId() == channelId) {
            if (StringUtils.isBlank(refundReasonCode) || StringUtils.isBlank(refundReason)) {
                throw new ParamInvalidException("淘鲜达渠道退款原因不能为空");
            }
        }
    }

    public BizOrderMoneyRefundRequest convertToBizOrderMoneyRefundRequest() {
        return BizOrderMoneyRefundRequest.builder()
                .channelOrderId(this.channelOrderId)
                .channelId(this.channelId)
                .moneyRefundItemModels(refundItems.stream().map(this::convertToBizOrderMoneyRefundItemModel).collect(Collectors.toList()))
                .refundReasonCode(refundReasonCode)
                .refundReason(refundReason)
                .build();
    }

    private BizOrderMoneyRefundItemModel convertToBizOrderMoneyRefundItemModel(MoneyRefundItemVo vo) {
        return BizOrderMoneyRefundItemModel.builder()
                .refundMoney(vo.getRefundMoney())
                .applyWeight(vo.getApplyWeight())
                .currentPrice(vo.getCurrentPrice())
                .customerSkuId(vo.getCustomerSkuId())
                .innerSkuId(vo.getInnerSkuId())
                .orderItemId(vo.getOrderItemId())
                .count(vo.getRefundCount())
                .customSpu(vo.getCustomSpu())
                .build();
    }
}
