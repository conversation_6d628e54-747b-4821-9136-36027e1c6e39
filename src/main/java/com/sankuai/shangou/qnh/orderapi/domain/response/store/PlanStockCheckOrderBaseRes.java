package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "盘点计划单基本信息列表返回",
        authors = {
                "qianteng"
        }
)
@Data
public class PlanStockCheckOrderBaseRes {

    @FieldDoc(
            description = "是否还有下一页"
    )
    public boolean hasMore;
    @FieldDoc(
            description = "盘点计划单列表"
    )
    public List<PlanStockCheckOrderBaseInfo> stockCheckOrderList;

    @TypeDoc(
            description = "盘点计划单基本信息"
    )
    @Data
    public static class PlanStockCheckOrderBaseInfo {
        @FieldDoc(
                description = "盘点计划单"
        )
        public String stockCheckOrderPlanId; // required
        @FieldDoc(
                description = "盘点类型：1-全盘；2-品类盘；3-随机盘；4-指定sku"
        )
        public int planType; // required
        @FieldDoc(
                description = "盘点备注"
        )
        public String comment; // required
        @FieldDoc(
                description = "计划盘点时间"
        )
        public String planExecuteTime; // required
        @FieldDoc(
                description = "执行时间"
        )
        public String executeTime;
        @FieldDoc(
                description = "盘点单状态：-1-无效；1-待提交；2-待审核；3-审核通过；4-驳回"
        )
        public int status; // required
    }
}
