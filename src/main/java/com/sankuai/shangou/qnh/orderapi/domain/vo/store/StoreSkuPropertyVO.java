package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.thrift.dto.StoreSkuPropertyDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@TypeDoc(
        description = "商品属性"
)
@Data
@ApiModel("商品属性")
@NoArgsConstructor
public class StoreSkuPropertyVO {

    @FieldDoc(
            description = "属性名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "属性名称", required = true)
    private String propertyName;

    @FieldDoc(
            description = "属性值，不超过十个", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "属性值", required = true)
    private List<String> propertyValues;

    public StoreSkuPropertyDTO buildStoreSkuPropertyDTO(){
        StoreSkuPropertyDTO storeSkuPropertyDTO = new StoreSkuPropertyDTO();
        storeSkuPropertyDTO.setPropertyName(this.getPropertyName());
        storeSkuPropertyDTO.setPropertyValues(this.getPropertyValues());
        return storeSkuPropertyDTO;
    }

    public StoreSkuPropertyVO(StoreSkuPropertyDTO storeSkuPropertyDTO){
        this.propertyName = storeSkuPropertyDTO.getPropertyName();
        this.propertyValues = storeSkuPropertyDTO.getPropertyValues();
    }

}
