package com.sankuai.shangou.qnh.orderapi.service.pc.impl;

import com.sankuai.meituan.shangou.empower.auth.thrift.enums.TokenTypeEmum;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.LoginRequest;
import com.sankuai.shangou.qnh.orderapi.cache.pc.LoginCache;
import com.sankuai.shangou.qnh.orderapi.constant.pc.ConfigDefaultValueConstant;
import com.sankuai.shangou.qnh.orderapi.domain.bo.pc.AccountLoginBo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.LoginTicket;
import com.sankuai.shangou.qnh.orderapi.remote.LoginRemoteService;
import com.sankuai.shangou.qnh.orderapi.service.pc.LoginManagerService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 登录服务
 *
 * <AUTHOR>
 */
@Service
public class LoginManagerServiceImpl implements LoginManagerService {

    @Resource
    private LoginCache loginCache;

    @Resource
    private LoginRemoteService loginRemoteService;

    @Override
    public AccountLoginBo check(String account, String password) {
        LoginRequest request = new LoginRequest();
        request.setAccountName(account);
        request.setPassword(password);
        request.setAppId(ConfigDefaultValueConstant.SAAS_B_APP_ID);
        return loginRemoteService.login(request);
    }

    @Override
    public AccountLoginBo accountCheck(String account, String password) {
        LoginRequest request = new LoginRequest();
        request.setAccountName(account);
        request.setPassword(password);
        request.setAppId(ConfigDefaultValueConstant.SAAS_B_APP_ID);
        return loginRemoteService.accountLogin(request);
    }

    @Override
    public void addLoginTicket(String ticket, LoginTicket loginTicket, int expireTimeInSeconds) {
        loginCache.addLoginTicket(ticket, loginTicket, expireTimeInSeconds);
    }

    @Override
    public LoginTicket getLoginTicket(String ticket) {
        return loginCache.getLoginTicket(ticket);
    }

    @Override
    public void refreshLoginTicket(String ticket, int expireTimeInSeconds) {
        loginCache.refreshLoginTicket(ticket, expireTimeInSeconds);
    }

    @Override
    @Deprecated
    public void removeLoginTicket(String ticket) {
        loginCache.removeLoginTicket(ticket);
        loginRemoteService.loginOut(TokenTypeEmum.AUTH_TOKEN, ticket);
    }

    @Override
    public void loginOut(TokenTypeEmum tokenTypeEmum, String ticket) {
        loginCache.removeLoginTicket(ticket);
        loginRemoteService.loginOut(tokenTypeEmum, ticket);
    }
}
