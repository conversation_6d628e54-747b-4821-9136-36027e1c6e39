package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelDynamicInfoDTO;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelDynamicInfoValueDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelDynamicInfoBizDTO;
import com.sankuai.meituan.shangou.empower.productbiz.client.dto.ChannelDynamicInfoValueBizDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/6/17 14:21
 **/
@TypeDoc(
        name = "渠道动态信息VO对象",
        description = "渠道动态新VO对象"
)
@Data
@ToString
@EqualsAndHashCode
public class ChannelDynamicInfoVO {

    @FieldDoc(
            description = "属性id"
    )
    private Long attrId;

    @FieldDoc(
            description = "属性名称"
    )
    private String attrName;

    @FieldDoc(
            description = "属性值类型，1-单选,2-多选,3-文本,4-搜索单选,5-级联单选"
    )
    private String attrValueType;

    @FieldDoc(
            description = "属性值可录入的字符类型，参考值：1-中文；2-字母；3-数字；4-标点符号"
    )
    private String characterType;

    @FieldDoc(
            description = "属性值允许录入的最大长度"
    )
    private Integer maxTextLength;

    @FieldDoc(
            description = "是否必传， 1-必传,2-非必传"
    )
    private Integer isRequired;

    @FieldDoc(
            description = "属性的顺序值"
    )
    private Integer attrSequence;

    @FieldDoc(
            description = "属性值列表"
    )
    private List<ChannelDynamicInfoValueVO> attrValueList;

    @FieldDoc(
            description = "用户所选的值"
    )
    private List<ChannelDynamicInfoValueVO> customValue;

    @TypeDoc(
            name = "渠道动态信息属性值",
            description = "渠道动态信息属性值"
    )
    @Data
    public static class ChannelDynamicInfoValueVO{
        @FieldDoc(
                description = "属性值id"
        )
        private String attrValueId;

        @FieldDoc(
                description = "属性值"
        )
        private String attrValue;

        public static List<ChannelDynamicInfoValueVO> ofPlatformDTOList(List<com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.dto.ChannelDynamicInfoValueDTO> categoryAttrValueDTOList){
            if(CollectionUtils.isEmpty(categoryAttrValueDTOList)){
                return Lists.newArrayList();
            }
            List<ChannelDynamicInfoValueVO> dynamicInfoValueVOList = new ArrayList<>();
            for(com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.dto.ChannelDynamicInfoValueDTO categoryAttrValueDTO : categoryAttrValueDTOList){
                ChannelDynamicInfoValueVO dynamicInfoValueVO = new ChannelDynamicInfoValueVO();
                dynamicInfoValueVO.setAttrValueId(categoryAttrValueDTO.getAttrValueId());
                dynamicInfoValueVO.setAttrValue(categoryAttrValueDTO.getAttrValue());
                dynamicInfoValueVOList.add(dynamicInfoValueVO);
            }
            return dynamicInfoValueVOList;
        }

        public static List<ChannelDynamicInfoValueVO> ofOcmsDTOList(List<ChannelDynamicInfoValueDTO> categoryAttrValueDTOList){
            if(CollectionUtils.isEmpty(categoryAttrValueDTOList)){
                return Lists.newArrayList();
            }
            List<ChannelDynamicInfoValueVO> dynamicInfoValueVOList = new ArrayList<>();
            for(ChannelDynamicInfoValueDTO categoryAttrValueDTO : categoryAttrValueDTOList){
                ChannelDynamicInfoValueVO dynamicInfoValueVO = new ChannelDynamicInfoValueVO();
                dynamicInfoValueVO.setAttrValueId(categoryAttrValueDTO.getAttrValueId());
                dynamicInfoValueVO.setAttrValue(categoryAttrValueDTO.getAttrValue());
                dynamicInfoValueVOList.add(dynamicInfoValueVO);
            }
            return dynamicInfoValueVOList;
        }

        public static List<ChannelDynamicInfoValueVO> ofBizDTOList(List<ChannelDynamicInfoValueBizDTO> categoryAttrValueDTOList){
            if(CollectionUtils.isEmpty(categoryAttrValueDTOList)){
                return Lists.newArrayList();
            }
            List<ChannelDynamicInfoValueVO> dynamicInfoValueVOList = new ArrayList<>();
            for(ChannelDynamicInfoValueBizDTO categoryAttrValueDTO : categoryAttrValueDTOList){
                ChannelDynamicInfoValueVO dynamicInfoValueVO = new ChannelDynamicInfoValueVO();
                dynamicInfoValueVO.setAttrValueId(categoryAttrValueDTO.getAttrValueId());
                dynamicInfoValueVO.setAttrValue(categoryAttrValueDTO.getAttrValue());
                dynamicInfoValueVOList.add(dynamicInfoValueVO);
            }
            return dynamicInfoValueVOList;
        }

        public static List<ChannelDynamicInfoValueBizDTO> toBizDTOList(List<ChannelDynamicInfoValueVO> dynamicInfoValueVOList){
            if(CollectionUtils.isEmpty(dynamicInfoValueVOList)){
                return Lists.newArrayList();
            }
            List<ChannelDynamicInfoValueBizDTO> dynamicInfoValueBizDTOS = new ArrayList<>();
            for(ChannelDynamicInfoValueVO dynamicInfoValueVO : dynamicInfoValueVOList){
                ChannelDynamicInfoValueBizDTO dynamicInfoValueBizDTO = new ChannelDynamicInfoValueBizDTO();
                dynamicInfoValueBizDTO.setAttrValueId(dynamicInfoValueVO.getAttrValueId());
                dynamicInfoValueBizDTO.setAttrValue(dynamicInfoValueVO.getAttrValue());
                dynamicInfoValueBizDTOS.add(dynamicInfoValueBizDTO);
            }
            return dynamicInfoValueBizDTOS;
        }

        public static List<ChannelDynamicInfoValueDTO> toOcmsDTOList(List<ChannelDynamicInfoValueVO> dynamicInfoValueVOList){
            if(CollectionUtils.isEmpty(dynamicInfoValueVOList)){
                return Lists.newArrayList();
            }
            List<ChannelDynamicInfoValueDTO> dynamicInfoValueDTOS = new ArrayList<>();
            for(ChannelDynamicInfoValueVO dynamicInfoValueVO : dynamicInfoValueVOList){
                ChannelDynamicInfoValueDTO dynamicInfoValueDTO = new ChannelDynamicInfoValueDTO();
                dynamicInfoValueDTO.setAttrValueId(dynamicInfoValueVO.getAttrValueId());
                dynamicInfoValueDTO.setAttrValue(dynamicInfoValueVO.getAttrValue());
                dynamicInfoValueDTOS.add(dynamicInfoValueDTO);
            }
            return dynamicInfoValueDTOS;
        }
    }

    public static List<ChannelDynamicInfoVO> ofPlatformDTOList(List<com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.dto.ChannelDynamicInfoDTO> categoryAttrDTOList){
        if(CollectionUtils.isEmpty(categoryAttrDTOList)){
            return Lists.newArrayList();
        }
        List<ChannelDynamicInfoVO> dynamicInfoVOList = new ArrayList<>();
        for(com.sankuai.meituan.shangou.empower.productplatform.thrift.channelcategory.dto.ChannelDynamicInfoDTO categoryAttrDTO : categoryAttrDTOList){
            ChannelDynamicInfoVO dynamicInfoVO = new ChannelDynamicInfoVO();
            dynamicInfoVO.setAttrId(Long.parseLong(categoryAttrDTO.getAttrId()));
            dynamicInfoVO.setAttrName(categoryAttrDTO.getAttrName());
            dynamicInfoVO.setAttrValueType(categoryAttrDTO.getAttrValueType());
            dynamicInfoVO.setIsRequired(categoryAttrDTO.getIsRequired());
            dynamicInfoVO.setCharacterType(categoryAttrDTO.getCharacterType());
            dynamicInfoVO.setMaxTextLength(categoryAttrDTO.getMaxTextLength());
            dynamicInfoVO.setAttrSequence(categoryAttrDTO.getAttrSequence());
            dynamicInfoVO.setAttrValueList(ChannelDynamicInfoValueVO.ofPlatformDTOList(categoryAttrDTO.getAttrValueList()));
            dynamicInfoVO.setCustomValue(ChannelDynamicInfoValueVO.ofPlatformDTOList(categoryAttrDTO.getCustomValue()));
            dynamicInfoVOList.add(dynamicInfoVO);
        }
        return dynamicInfoVOList;
    }

    public static List<ChannelDynamicInfoBizDTO> toBizDTOList(List<ChannelDynamicInfoVO> dynamicInfoVOList){
        if(CollectionUtils.isEmpty(dynamicInfoVOList)){
            return Lists.newArrayList();
        }
        List<ChannelDynamicInfoBizDTO> dynamicInfoBizDTOS = new ArrayList<>();
        for(ChannelDynamicInfoVO dynamicInfoVO : dynamicInfoVOList){
            ChannelDynamicInfoBizDTO dynamicInfoBizDTO = new ChannelDynamicInfoBizDTO();
            dynamicInfoBizDTO.setAttrId(String.valueOf(dynamicInfoVO.getAttrId()));
            dynamicInfoBizDTO.setAttrName(dynamicInfoVO.getAttrName());
            dynamicInfoBizDTO.setAttrValueType(dynamicInfoVO.getAttrValueType());
            dynamicInfoBizDTO.setIsRequired(dynamicInfoVO.getIsRequired());
            dynamicInfoBizDTO.setCharacterType(dynamicInfoVO.getCharacterType());
            dynamicInfoBizDTO.setMaxTextLength(dynamicInfoVO.getMaxTextLength());
            dynamicInfoBizDTO.setAttrSequence(dynamicInfoVO.getAttrSequence());
            dynamicInfoBizDTO.setAttrValueList(ChannelDynamicInfoValueVO.toBizDTOList(dynamicInfoVO.getAttrValueList()));
            dynamicInfoBizDTO.setCustomValue(ChannelDynamicInfoValueVO.toBizDTOList(dynamicInfoVO.getCustomValue()));
            dynamicInfoBizDTOS.add(dynamicInfoBizDTO);
        }
        return dynamicInfoBizDTOS;
    }

    public static List<ChannelDynamicInfoDTO> toOcmsDTOList(List<ChannelDynamicInfoVO> dynamicInfoVOList){
        if(CollectionUtils.isEmpty(dynamicInfoVOList)){
            return Lists.newArrayList();
        }
        List<ChannelDynamicInfoDTO> dynamicInfoDTOS = new ArrayList<>();
        for(ChannelDynamicInfoVO dynamicInfoVO : dynamicInfoVOList){
            ChannelDynamicInfoDTO dynamicInfoDTO = new ChannelDynamicInfoDTO();
            dynamicInfoDTO.setAttrId(String.valueOf(dynamicInfoVO.getAttrId()));
            dynamicInfoDTO.setAttrName(dynamicInfoVO.getAttrName());
            dynamicInfoDTO.setAttrValueType(dynamicInfoVO.getAttrValueType());
            dynamicInfoDTO.setIsRequired(dynamicInfoVO.getIsRequired());
            dynamicInfoDTO.setCharacterType(dynamicInfoVO.getCharacterType());
            dynamicInfoDTO.setMaxTextLength(dynamicInfoVO.getMaxTextLength());
            dynamicInfoDTO.setAttrSequence(dynamicInfoVO.getAttrSequence());
            dynamicInfoDTO.setAttrValueList(ChannelDynamicInfoValueVO.toOcmsDTOList(dynamicInfoVO.getAttrValueList()));
            dynamicInfoDTO.setCustomValue(ChannelDynamicInfoValueVO.toOcmsDTOList(dynamicInfoVO.getCustomValue()));
            dynamicInfoDTOS.add(dynamicInfoDTO);
        }
        return dynamicInfoDTOS;
    }

    public static List<ChannelDynamicInfoVO> ofOcmsDTOList(List<ChannelDynamicInfoDTO> dynamicInfoDTOList){
        if(CollectionUtils.isEmpty(dynamicInfoDTOList)){
            return Lists.newArrayList();
        }
        List<ChannelDynamicInfoVO> dynamicInfoVOList = new ArrayList<>();
        for(ChannelDynamicInfoDTO categoryAttrDTO : dynamicInfoDTOList){
            ChannelDynamicInfoVO dynamicInfoVO = new ChannelDynamicInfoVO();
            dynamicInfoVO.setAttrId(Long.parseLong(categoryAttrDTO.getAttrId()));
            dynamicInfoVO.setAttrName(categoryAttrDTO.getAttrName());
            dynamicInfoVO.setAttrValueType(categoryAttrDTO.getAttrValueType());
            dynamicInfoVO.setIsRequired(categoryAttrDTO.getIsRequired());
            dynamicInfoVO.setCharacterType(categoryAttrDTO.getCharacterType());
            dynamicInfoVO.setMaxTextLength(categoryAttrDTO.getMaxTextLength());
            dynamicInfoVO.setAttrSequence(categoryAttrDTO.getAttrSequence());
            dynamicInfoVO.setAttrValueList(ChannelDynamicInfoValueVO.ofOcmsDTOList(categoryAttrDTO.getAttrValueList()));
            dynamicInfoVO.setCustomValue(ChannelDynamicInfoValueVO.ofOcmsDTOList(categoryAttrDTO.getCustomValue()));
            dynamicInfoVOList.add(dynamicInfoVO);
        }
        return dynamicInfoVOList;
    }

    public static List<ChannelDynamicInfoVO> ofBizDTOList(List<ChannelDynamicInfoBizDTO> dynamicInfoDTOList){
        if(CollectionUtils.isEmpty(dynamicInfoDTOList)){
            return Lists.newArrayList();
        }
        List<ChannelDynamicInfoVO> dynamicInfoVOList = new ArrayList<>();
        for(ChannelDynamicInfoBizDTO categoryAttrDTO : dynamicInfoDTOList){
            ChannelDynamicInfoVO dynamicInfoVO = new ChannelDynamicInfoVO();
            dynamicInfoVO.setAttrId(Long.parseLong(categoryAttrDTO.getAttrId()));
            dynamicInfoVO.setAttrName(categoryAttrDTO.getAttrName());
            dynamicInfoVO.setAttrValueType(categoryAttrDTO.getAttrValueType());
            dynamicInfoVO.setIsRequired(categoryAttrDTO.getIsRequired());
            dynamicInfoVO.setCharacterType(categoryAttrDTO.getCharacterType());
            dynamicInfoVO.setMaxTextLength(categoryAttrDTO.getMaxTextLength());
            dynamicInfoVO.setAttrSequence(categoryAttrDTO.getAttrSequence());
            dynamicInfoVO.setAttrValueList(ChannelDynamicInfoValueVO.ofBizDTOList(categoryAttrDTO.getAttrValueList()));
            dynamicInfoVO.setCustomValue(ChannelDynamicInfoValueVO.ofBizDTOList(categoryAttrDTO.getCustomValue()));
            dynamicInfoVOList.add(dynamicInfoVO);
        }
        return dynamicInfoVOList;
    }

}
