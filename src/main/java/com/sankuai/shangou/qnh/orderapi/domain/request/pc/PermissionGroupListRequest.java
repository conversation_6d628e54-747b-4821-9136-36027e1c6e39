package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.auth.thrift.vo.QueryPermissionGroupRequest;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * @Author: <EMAIL>
 * @Date: 2018/12/4 上午11:32
 */
@TypeDoc(
        description = "查询数据权限组全部列表入参"
)
@ApiModel("查询数据权限组全部列表入参")
@Data
public class PermissionGroupListRequest {
    @FieldDoc(
            description = "租户id"
    )
    private long tenantId;

    @FieldDoc(
            description = "应用id"
    )
    private Integer appId;

    @FieldDoc(
            description = "数据权限类型"
    )
    private int type;

    /**
     * 将用户层的request转换成thrift层的request
     * @return
     */
    public QueryPermissionGroupRequest convert2ThrifRequest(){
        QueryPermissionGroupRequest request = new QueryPermissionGroupRequest();

        request.tenantId = ContextHolder.currentUserTenantId();
        request.type = type;

        return request;
    }

    public void selfCheck(){
        //不需要检查
    }
}
