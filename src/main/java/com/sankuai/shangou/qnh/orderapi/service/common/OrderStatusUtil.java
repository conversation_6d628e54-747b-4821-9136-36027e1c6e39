package com.sankuai.shangou.qnh.orderapi.service.common;

import com.meituan.shangou.saas.order.management.client.enums.WaitToDeliveryOrderSubTypeEnum;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.meituan.shangou.saas.order.platform.enums.*;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryExceptionSubTypeEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.thrift.query.dto.TOrderIdentifier;
import com.sankuai.shangou.qnh.orderapi.constant.app.OrderShowStatusConstants;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.OrderListResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.OrderVO;
import com.sankuai.shangou.qnh.orderapi.enums.app.OrderViewStatusEnum;
import com.sankuai.shangou.qnh.orderapi.enums.app.TmsDeliveryStatusDescEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @since 2024/7/16
 **/
@Slf4j
public class OrderStatusUtil {
    /**
     * 针对全部订单列表中的订单 VO 对象，获取优化的的订单状态描述.
     *
     * @param orderVo 订单 VO 对象
     * @return 优化的订单状态描述
     */
    public static String getOrderOptStatusDesc(OrderVO orderVo) {
        OrderStatusEnum orderStatusEnum = OrderStatusEnum.enumOf(orderVo.getOrderStatus());
        TmsDeliveryStatusDescEnum realDeliveryStatusEnum = null;
        if (Objects.nonNull(orderVo.getRealDistributeStatus())) {
            realDeliveryStatusEnum = TmsDeliveryStatusDescEnum.getFromCode(orderVo.getRealDistributeStatus());
        }
        DistributeStatusEnum distributeStatusEnum = null;
        if (Objects.nonNull(orderVo.getDistributeStatus())) {
            distributeStatusEnum = DistributeStatusEnum.enumOf(orderVo.getDistributeStatus());
        }
        // 判断是否在配送中
        boolean isInDelivery = false;
        if ((realDeliveryStatusEnum != null && realDeliveryStatusEnum.getCode() > TmsDeliveryStatusDescEnum.DELIVERY_LAUNCHED.getCode()) ||
                (distributeStatusEnum != null && distributeStatusEnum.getValue() > DistributeStatusEnum.UN_KNOWN.getValue())) {
            isInDelivery = true;
        }
        DeliveryStatusEnum pickStatusEnum = null;
        if (Objects.nonNull(orderVo.getPickStatus())) {
            pickStatusEnum = DeliveryStatusEnum.enumOf(orderVo.getPickStatus());
        }
        String desc = "";
        if (orderStatusEnum != null) {
            switch (orderStatusEnum) {
                case SUBMIT:
                case PAYING:
                case PAYED:
                    desc = OrderShowStatusConstants.WAIT_RECEIVE;
                    break;
                case MERCHANT_CONFIRMED:
                    desc = OrderShowStatusConstants.RECEIVED;
                    break;
                case PICKING:
                    desc = OrderShowStatusConstants.PICKING;
                    if (pickStatusEnum != null && pickStatusEnum.getValue() == DeliveryStatusEnum.PICKED.getValue()) {
                        //待自提
                        if (orderVo.getSelfFetchStatus() != null && orderVo.getSelfFetchStatus() == SelfFetchStatusEnum.WAIT_TO_SELF_FETCH.getValue()) {
                            desc = OrderShowStatusConstants.WAIT_SELF_FETCH;
                        }
                        else if (isInDelivery) {
                            desc = orderVo.getDistributeStatusDesc();
                        } else {
                            desc = OrderShowStatusConstants.WAIT_DELIVERY;
                        }
                    }
                    break;
                case COMPLETED:
                    desc = OrderShowStatusConstants.COMPLETED;
                    break;
                case REFUND_APPLIED:
                case APPEAL_APPLIED:
                    desc = OrderShowStatusConstants.REFUNDING;
                    break;
                case CANCELED:
                    desc = OrderShowStatusConstants.CANCELED;
                    break;
                default:
                    // 兜底-取订单状态
                    desc = orderStatusEnum.getDesc();
            }
        }
        return org.apache.commons.lang.StringUtils.defaultIfBlank(desc, "未知");
    }

    /**
     * 针对全部订单列表中的订单 VO 对象，获取优化的的订单状态描述.-用于前端缓存
     *
     * @param orderVo 订单 VO 对象
     * @return 优化的订单状态描述
     */
    public static String getOrderOptStatusDescForAppCache(OrderVO orderVo) {
        String desc = "";
        FuseOrderStatusEnum fuseOrderStatusEnum = FuseOrderStatusEnum.enumOf(orderVo.getViewFuseOrderStatus());
        switch (fuseOrderStatusEnum){
            case SUBMIT:
            case PAYING:
            case PAYED:
                desc = OrderViewStatusEnum.WAIT_TO_MERCHANT_ACCEPT.getDesc();
                break;
            case MERCHANT_CONFIRMED:
                desc = OrderViewStatusEnum.WAIT_TO_PICK_ORDER.getDesc();
                break;
            case PICKED:
            case SHIPPING:
                desc = orderVo.getDistributeStatusDesc();
                break;
            case COMPLETED:
                desc = OrderShowStatusConstants.COMPLETED;
                break;
            case CANCELED:
                desc = OrderShowStatusConstants.CANCELED;
                break;
            case WAIT_SELF_FETCH:
                desc = OrderViewStatusEnum.WAIT_TO_SELF_FETCH.getDesc();
                break;
            default:
                // 兜底-取订单状态
                desc = fuseOrderStatusEnum.getDesc();
        }
        return org.apache.commons.lang.StringUtils.defaultIfBlank(desc, "未知");
    }

    public static void fixOrdersStatusDescForAppCache(OrderListResponse orderListResponse) {
        if (CollectionUtils.isEmpty(orderListResponse.getOrderList())) {
            return;
        }
        orderListResponse.getOrderList().stream().forEach(order -> {
            order.setOrderStatusDesc(getOrderOptStatusDescForAppCache(order));
            order.setViewStatus(getOrderViewStatusForAppCache(order));
        });
    }

    /**
     * 针对全部订单列表，完善订单状态描述.
     *
     * @param orderListResponse 订单列表
     */
    public static void fixOrdersStatusDesc(OrderListResponse orderListResponse) {
        if (CollectionUtils.isEmpty(orderListResponse.getOrderList())) {
            return;
        }
        orderListResponse.getOrderList().stream().forEach(order -> {
            order.setOrderStatusDesc(OrderStatusUtil.getOrderOptStatusDesc(order));
        });
    }


    public static Integer getOrderViewStatusByWaitToDeliveryOrderSubType(WaitToDeliveryOrderSubTypeEnum waitToDeliveryOrderSubTypeEnum){
        if (waitToDeliveryOrderSubTypeEnum == null) {
            return null;
        }
        Integer orderViewStatus = null;
        switch (waitToDeliveryOrderSubTypeEnum) {
            case WAIT_TO_SEND_DELIVERY:
                orderViewStatus = OrderViewStatusEnum.WAIT_TO_SEND_DELIVERY.getCode();
                break;
            case WAIT_TO_RIDER_ACCEPT:
                orderViewStatus = OrderViewStatusEnum.WAIT_TO_RIDER_ACCEPT.getCode();
                break;
            case WAIT_TO_ARRIVE_SHOP:
                orderViewStatus = OrderViewStatusEnum.WAIT_TO_ARRIVE_SHOP.getCode();
                break;
            case WAIT_TO_TAKE_GOODS:
                orderViewStatus = OrderViewStatusEnum.WAIT_TO_TAKE_GOODS.getCode();
                break;
            case DELIVERING:
                orderViewStatus = OrderViewStatusEnum.DELIVERING.getCode();
                break;
            default:
                return null;
        }
        return orderViewStatus;
    }

    public static void setOrderListResponseViewStatus(OrderListResponse response, OrderViewStatusEnum orderViewStatusEnum) {
        if (Objects.isNull(response) || CollectionUtils.isEmpty(response.getOrderList()) || Objects.isNull(orderViewStatusEnum)) {
            return;
        }
        for (OrderVO each : response.getOrderList()) {
            each.setViewStatus(orderViewStatusEnum.getCode());
        }
    }

    public static void setOrderListResponseViewStatusByWaitToDeliverySubType(OrderListResponse response) {
        if (Objects.isNull(response) || CollectionUtils.isEmpty(response.getOrderList())) {
            return;
        }
        for (OrderVO each : response.getOrderList()) {
            if (Objects.nonNull(each.getDistributeStatus())) {
                WaitToDeliveryOrderSubTypeEnum waitToDeliveryOrderSubTypeEnum = WaitToDeliveryOrderSubTypeEnum.distributeStatusValueOf(each.getDistributeStatus());
                if (Objects.nonNull(waitToDeliveryOrderSubTypeEnum)) {
                    each.setViewStatus(getOrderViewStatusByWaitToDeliveryOrderSubType(waitToDeliveryOrderSubTypeEnum));
                }
            }
        }
    }

    public static void setOrderListResponseViewStatusByExceptionSubType(OrderListResponse response, List<TOrderIdentifier> tOrderIdentifiers) {
        if (Objects.isNull(response) || CollectionUtils.isEmpty(response.getOrderList()) || CollectionUtils.isEmpty(tOrderIdentifiers)) {
            return;
        }
        Map<String, TOrderIdentifier> tOrderIdentifierMap = tOrderIdentifiers.stream().collect(Collectors.toMap(o -> o.getChannelOrderId() + "_" + o.getOrderBizTypeCode(), o -> o, (f, s) -> f));
        for (OrderVO each : response.getOrderList()) {
            String tOrderIdentifierMapKey = each.getChannelOrderId() + "_" + ChannelOrderConvertUtils.sourceMid2Biz(each.getChannelId());
            if (tOrderIdentifierMap.containsKey(tOrderIdentifierMapKey)) {
                TOrderIdentifier tOrderIdentifier= tOrderIdentifierMap.get(tOrderIdentifierMapKey);
                DeliveryExceptionSubTypeEnum deliveryExceptionSubTypeEnum = DeliveryExceptionSubTypeEnum.deliveryStatusCodeValueOfWithOutAll(
                        Optional.ofNullable(tOrderIdentifier.getExceptionTypeCode()).orElse(null),
                        tOrderIdentifier.getDeliveryExceptionCode(),
                        tOrderIdentifier.getDeliveryStatus().getCode());
                each.setViewStatus(getOrderViewStatusByExceptionOrderSubType(deliveryExceptionSubTypeEnum));
                each.setAllowLatestAuditTime(tOrderIdentifier.getAllowLatestAuditTime());
                each.setExceptionCode(tOrderIdentifier.getDeliveryExceptionCode());
            }
        }
    }

    public static Integer getOrderViewStatusByExceptionOrderSubType(DeliveryExceptionSubTypeEnum deliveryExceptionSubTypeEnum){
        if (deliveryExceptionSubTypeEnum == null) {
            return null;
        }
        Integer orderViewStatus = null;
        switch (deliveryExceptionSubTypeEnum) {
            case NO_RIDER_ACCEPT:
                orderViewStatus = OrderViewStatusEnum.NO_RIDER_ACCEPT.getCode();
                break;
            case NO_ARRIVAL_STORE:
                orderViewStatus = OrderViewStatusEnum.NO_ARRIVAL_STORE.getCode();
                break;
            case NO_RIDER_TAKE_GOODS:
                orderViewStatus = OrderViewStatusEnum.NO_RIDER_TAKE_GOODS.getCode();
                break;
            case DELIVERY_TIMEOUT:
                orderViewStatus = OrderViewStatusEnum.DELIVERY_TIMEOUT.getCode();
                break;
            case SYSTEM_EXCEPTION:
                orderViewStatus = OrderViewStatusEnum.SYSTEM_EXCEPTION.getCode();
                break;
            case REPORT_EXCEPTION:
                orderViewStatus = OrderViewStatusEnum.REPORT_EXCEPTION.getCode();
                break;
            case TAKE_EXCEPTION:
                orderViewStatus = OrderViewStatusEnum.TAKE_EXCEPTION.getCode();
                break;
            default:
                return null;
        }
        return orderViewStatus;
    }


    public static Integer getOrderViewStatusForAppCache(OrderVO order) {
        FuseOrderStatusEnum fuseOrderStatusEnum = FuseOrderStatusEnum.enumOf(order.getViewFuseOrderStatus());
        switch (fuseOrderStatusEnum){
            case SUBMIT:
            case PAYING:
            case PAYED:
                return OrderViewStatusEnum.WAIT_TO_MERCHANT_ACCEPT.getCode();
            case MERCHANT_CONFIRMED:
                return OrderViewStatusEnum.WAIT_TO_PICK_ORDER.getCode();
            case PICKED:
            case SHIPPING:
                if (Objects.nonNull(order.getDistributeStatus())) {
                    WaitToDeliveryOrderSubTypeEnum waitToDeliveryOrderSubTypeEnum = WaitToDeliveryOrderSubTypeEnum.distributeStatusValueOf(order.getDistributeStatus());
                    if (Objects.nonNull(waitToDeliveryOrderSubTypeEnum)) {
                        return getOrderViewStatusByWaitToDeliveryOrderSubType(waitToDeliveryOrderSubTypeEnum);
                    }
                }
                log.info("转换配送状态错误:{}", order);
                return null;
            case WAIT_SELF_FETCH:
                return OrderViewStatusEnum.WAIT_TO_SELF_FETCH.getCode();
            default:
                // 兜底-取订单状态
                return null;
        }
    }


}
