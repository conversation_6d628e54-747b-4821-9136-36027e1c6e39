package com.sankuai.shangou.qnh.orderapi.controller;

import com.meituan.linz.boot.util.Fun;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiEntityTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.PoiInfoDto;
import com.meituan.shangou.saas.tenant.thrift.dto.poi.request.PoiListQueryRequest;
import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.ResponseCodeEnum;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.qnh.orderapi.remote.AccountRemoteService;
import com.sankuai.shangou.qnh.orderapi.remote.AuthRemoteService;
import com.sankuai.shangou.qnh.orderapi.remote.PoiRemoteService;
import com.sankuai.shangou.qnh.orderapi.enums.pc.ChannelStockErrorTypeEnum;
import com.sankuai.shangou.qnh.orderapi.enums.pc.PriceErrorTypeEnum;
import com.sankuai.shangou.qnh.orderapi.context.pc.ContextHolder;
import com.sankuai.shangou.qnh.orderapi.exception.pc.ParamInvalidException;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.BaseResult;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.Result;
import com.sankuai.shangou.qnh.orderapi.domain.result.pc.ResultBuilder;
import com.sankuai.shangou.qnh.orderapi.service.pc.BaseExcelExporterService;
import com.sankuai.shangou.qnh.orderapi.service.pc.ThriftService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellUtil;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.shangou.qnh.orderapi.constant.pc.ConfigDefaultValueConstant.SAAS_B_APP_ID;
import static org.apache.commons.collections4.ListUtils.emptyIfNull;

/**
 * 基本控制器,公共方法放在此类中
 *
 * @Author: <EMAIL>
 * @Date: 2019/1/17 11:
 * @Description:
 */
@Slf4j
public abstract class BaseController {


    @Autowired
    protected ThriftService thriftService;

    @Autowired
    protected AuthRemoteService authRemoteService;

    @Autowired
    protected AccountRemoteService accountRemoteService;

    @Autowired
    protected PoiRemoteService poiRemoteService;


    /**
     * 统一流程处理
     *
     * @param r         请求参数
     * @param logPrefix 日志前缀
     * @param function  逻辑处理方法
     * @param <R>
     * @return
     */
    public <R extends BaseRequest, RE> Result<RE> process(R r, String logPrefix, Function<R, Result<RE>> function) {
        try {
            if (r != null) {
                r.selfCheck();
            }
            return function.apply(r);
        } catch (BizException e) {
            if (e.getErrorCode() == PriceErrorTypeEnum.CREATE_PRICE_ALL_FAIL.getCode() || e.getErrorCode() == PriceErrorTypeEnum.SAVE_PRICE_PARAM_ERROR.getCode()
                    || e.getErrorCode() == ChannelStockErrorTypeEnum.PART_GOODS_CONFLICT.getCode() || e.getErrorCode() == BaseResult.PULL_ONLINE_FAIL.getCode()
                    || e.getErrorCode() == BaseResult.ADD_COMPOSE_SKU_WARNING.getCode()
            ) {
                return ResultBuilder.buildResult(e.getErrorCode(), e.getMessage(), null);
            } else if (e.getErrorCode() == ResponseCodeEnum.ANALYZE_EXCEL_ERROR.getValue()) {
                return ResultBuilder.buildResult(BaseResult.EXCEL_ANALYZE_ERROR.getCode(), e.getMessage(), null);
            } else if (e.getErrorCode() == BaseResult.BIZ_STATUS_CHANGE.getCode()) {
                return ResultBuilder.buildResult(BaseResult.BIZ_STATUS_CHANGE.getCode(), e.getMessage(), null);
            } else {
                log.debug("{} error", logPrefix, e);
                return ResultBuilder.buildFailResult(e.getMessage());
            }
        } catch (ParamInvalidException e) {
            log.debug("param invalid ", e);
            if (StringUtils.isNotEmpty(e.getFielddName())) {
                return ResultBuilder.buildParamInvalidResult(e.getMessage(), e.getFielddName());
            } else {
                return ResultBuilder.buildFailResult(e.getMessage());
            }
        } catch (IllegalArgumentException e) {
            log.debug("param invalid ", e);
            return ResultBuilder.buildFailResult(e.getMessage());
        } catch (Exception e) {
            log.error("{} error", logPrefix, e);
            return ResultBuilder.buildFailResult();
        }
    }


    public static HSSFWorkbook exportExcelFile(String sheetName, List<String> colNameList, List<HashMap<String, String>> sourceList, boolean headStyle) {
        // 第一步，创建一个webbook，对应一个Excel文件
        HSSFWorkbook wb = new HSSFWorkbook();
        // 第二步，在webbook中添加一个sheet,对应Excel文件中的sheet
        HSSFSheet sheet = wb.createSheet(sheetName);

        sheet.setDefaultColumnWidth(15);
//        sheet.setDefaultRowHeight((short)300);

        // 第三步，在sheet中添加表头第0行,注意老版本poi对Excel的行数列数有限制short
        HSSFRow row = sheet.createRow((int) 0);
        // 第四步，创建单元格，并设置值表头 设置表头居中

        HSSFCellStyle styleHead = null;

        if (headStyle) {
            styleHead = BaseExcelExporterService.createHeadStyle(wb);
        }
        for (int i = 0; i < colNameList.size(); i++) {
            HSSFCell cell = row.createCell((int) i);
            cell.setCellValue(colNameList.get(i));
            cell.setCellStyle(styleHead);
        }

        CellStyle cs = wb.createCellStyle();
        cs.setAlignment(HorizontalAlignment.CENTER);
        cs.setWrapText(true);

        for (int i = 0; i < sourceList.size(); i++) {
            row = sheet.createRow((int) i + 1);
            HashMap<String, String> sourceItem = sourceList.get(i);

            // 第五步，创建单元格，并设置值
            for (int j = 0; j < colNameList.size(); j++) {
                HSSFCell cell = row.createCell((int) j);
                cell.setCellStyle(cs);
                cell.setCellValue(wrapString(sourceItem.get(colNameList.get(j))));
            }
        }

        return wb;
    }

    public static HSSFWorkbook exportExcelFileWithAutoSizeCol(String sheetName, List<String> colNameList, List<HashMap<String, String>> sourceList, boolean headStyle) {
        // 第一步，创建一个webbook，对应一个Excel文件
        HSSFWorkbook wb = new HSSFWorkbook();
        // 第二步，在webbook中添加一个sheet,对应Excel文件中的sheet
        HSSFSheet sheet = wb.createSheet(sheetName);

        //sheet.setDefaultColumnWidth(15);
        //sheet.setDefaultRowHeight((short)300);

        // 第三步，在sheet中添加表头第0行,注意老版本poi对Excel的行数列数有限制short
        HSSFRow row = sheet.createRow((int) 0);
        // 第四步，创建单元格，并设置值表头 设置表头居中

        HSSFCellStyle styleHead = null;

        if (headStyle) {
            styleHead = BaseExcelExporterService.createHeadStyle(wb);
        }
        for (int i = 0; i < colNameList.size(); i++) {
            HSSFCell cell = row.createCell((int) i);
            cell.setCellValue(colNameList.get(i));
            cell.setCellStyle(styleHead);
        }


        for (int i = 0; i < sourceList.size(); i++) {
            row = sheet.createRow((int) i + 1);
            HashMap<String, String> sourceItem = sourceList.get(i);

            // 第五步，创建单元格，并设置值
            for (int j = 0; j < colNameList.size(); j++) {
                HSSFCell cell = row.createCell((int) j);
                cell.setCellValue(wrapString(sourceItem.get(colNameList.get(j))));
                sheet.autoSizeColumn(j);
            }
        }

        return wb;
    }

    public static HSSFWorkbook exportExcelFileWithSpecHead(String sheetName, String headContent, List<String> colNameList, List<HashMap<String, String>> sourceList, boolean headStyle) {
        // 第一步，创建一个webbook，对应一个Excel文件
        HSSFWorkbook wb = new HSSFWorkbook();
        // 第二步，在webbook中添加一个sheet,对应Excel文件中的sheet
        HSSFSheet sheet = wb.createSheet(sheetName);

        //sheet.setDefaultColumnWidth(15);
        //sheet.setDefaultRowHeight((short)300);
        int rowStart = 0;
        //如果有特殊表头要求，新建一行
        if (StringUtils.isNotBlank(headContent)) {
            HSSFRow row1 = sheet.createRow((int) 0);
            HSSFRow row2 = sheet.createRow((int) 1);
            HSSFCell cell = row1.createCell((int) 0);
            cell.setCellValue(headContent);
            sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, colNameList.size() - 1));
            CellUtil.setAlignment(cell, HorizontalAlignment.CENTER);
            CellUtil.setVerticalAlignment(cell, VerticalAlignment.CENTER);
            rowStart += 2;
        }


        HSSFRow row = sheet.createRow((int) rowStart);

        HSSFCellStyle styleHead = null;

        if (headStyle) {
            styleHead = BaseExcelExporterService.createHeadStyle(wb);
        }
        for (int i = 0; i < colNameList.size(); i++) {
            HSSFCell cell = row.createCell((int) i);
            cell.setCellValue(colNameList.get(i));
            cell.setCellStyle(styleHead);
        }

        CellStyle cs = wb.createCellStyle();
        cs.setAlignment(HorizontalAlignment.CENTER);
        cs.setWrapText(true);
        for (int i = 0; i < sourceList.size(); i++) {
            row = sheet.createRow(rowStart + 1);
            HashMap<String, String> sourceItem = sourceList.get(i);
            rowStart++;

            // 第五步，创建单元格，并设置值
            for (int j = 0; j < colNameList.size(); j++) {
                HSSFCell cell = row.createCell((int) j);
                cell.setCellStyle(cs);
                cell.setCellValue(wrapString(sourceItem.get(colNameList.get(j))));
            }
        }


        return wb;
    }

    public static HSSFWorkbook exportExcelFileWithFillColor(String sheetName, List<String> colNameList, List<HashMap<String, String>> sourceList, List<HashMap<String, Boolean>> fillColorInfo, IndexedColors color, boolean headStyle) {
        // 第一步，创建一个webbook，对应一个Excel文件
        HSSFWorkbook wb = new HSSFWorkbook();
        // 第二步，在webbook中添加一个sheet,对应Excel文件中的sheet
        HSSFSheet sheet = wb.createSheet(sheetName);

        sheet.setDefaultColumnWidth(15);
//        sheet.setDefaultRowHeight((short)300);

        // 第三步，在sheet中添加表头第0行,注意老版本poi对Excel的行数列数有限制short
        HSSFRow row = sheet.createRow((int) 0);
        // 第四步，创建单元格，并设置值表头 设置表头居中

        HSSFCellStyle styleHead = null;

        if (headStyle) {
            styleHead = BaseExcelExporterService.createHeadStyle(wb);
        }
        for (int i = 0; i < colNameList.size(); i++) {
            HSSFCell cell = row.createCell((int) i);
            cell.setCellValue(colNameList.get(i));
            cell.setCellStyle(styleHead);
        }

        CellStyle cs = wb.createCellStyle();
        cs.setAlignment(HorizontalAlignment.CENTER);
        cs.setWrapText(true);

        CellStyle csWithColor = wb.createCellStyle();
        csWithColor.setAlignment(HorizontalAlignment.CENTER);
        csWithColor.setWrapText(true);
        csWithColor.setFillForegroundColor(color.getIndex());
        csWithColor.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        for (int i = 0; i < sourceList.size(); i++) {
            row = sheet.createRow((int) i + 1);
            HashMap<String, String> sourceItem = sourceList.get(i);
            HashMap<String, Boolean> fillColorItem = fillColorInfo.get(i);

            // 第五步，创建单元格，并设置值
            for (int j = 0; j < colNameList.size(); j++) {
                HSSFCell cell = row.createCell((int) j);
                //判断该单元格是否使用需填充颜色的样式
                if (fillColorItem.get(colNameList.get(j)) == null || !fillColorItem.get(colNameList.get(j))) {
                    cell.setCellStyle(cs);
                } else {
                    cell.setCellStyle(csWithColor);
                }
                cell.setCellValue(wrapString(sourceItem.get(colNameList.get(j))));
            }
        }

        return wb;
    }


    public static XSSFWorkbook exportXlsxFile(String sheetName, List<String> colNameList, List<HashMap<String, String>> sourceList) {
        // 第一步，创建一个webbook，对应一个Excel文件
        XSSFWorkbook wb = new XSSFWorkbook();
        // 第二步，在webbook中添加一个sheet,对应Excel文件中的sheet
        XSSFSheet sheet = wb.createSheet(sheetName);
        // 第三步，在sheet中添加表头第0行,注意老版本poi对Excel的行数列数有限制short
        XSSFRow row = sheet.createRow((int) 0);
        // 第四步，创建单元格，并设置值表头 设置表头居中

        for (int i = 0; i < colNameList.size(); i++) {
            XSSFCell cell = row.createCell((int) i);
            cell.setCellValue(colNameList.get(i));
        }

        for (int i = 0; i < sourceList.size(); i++) {
            row = sheet.createRow((int) i + 1);
            HashMap<String, String> sourceItem = sourceList.get(i);

            // 第五步，创建单元格，并设置值
            for (int j = 0; j < colNameList.size(); j++) {
                XSSFCell cell = row.createCell((int) j);
                cell.setCellValue(wrapString(sourceItem.get(colNameList.get(j))));

            }
        }

        return wb;
    }

    public static XSSFWorkbook continueWriteExcel(XSSFWorkbook wb, int sheetIndex, List<HashMap<String, String>> sourceList) {
        // 第一步，获取webbook中的第一个sheet页
        XSSFSheet sheet = wb.getSheetAt(sheetIndex);
        // 第二步，获取sheet页第一行，即表头
        List<String> colNameList = new ArrayList<String>();
        Iterator<Cell> iterator = sheet.getRow(0).cellIterator();
        while (iterator.hasNext()) {
            Cell cell = iterator.next();
            colNameList.add(cell.getStringCellValue());
        }
        //第三步，获取当前sheet页已经写到了哪一行
        int lastRow = sheet.getLastRowNum();

        for (int i = 0; i < sourceList.size(); i++) {
            //第四步，从当前行的下一行开始续写
            XSSFRow row = sheet.createRow((int) i + 1 + lastRow);
            HashMap<String, String> sourceItem = sourceList.get(i);

            // 第五步，创建单元格，并设置值
            for (int j = 0; j < colNameList.size(); j++) {
                XSSFCell cell = row.createCell(j);
                cell.setCellValue(wrapString(sourceItem.get(colNameList.get(j))));
            }
        }

        return wb;


    }

    private static String wrapString(String s) {
        return s == null || StringUtils.equals("null", s) ? "" : s;
    }


    protected HSSFWorkbook continueWriteExcel(HSSFWorkbook wb, int sheetIndex, List<HashMap<String, String>> sourceList) {
        // 第一步，获取webbook中的第一个sheet页
        HSSFSheet sheet = wb.getSheetAt(sheetIndex);
        // 第二步，获取sheet页第一行，即表头
        List<String> colNameList = new ArrayList<String>();
        Iterator<Cell> iterator = sheet.getRow(0).cellIterator();
        while (iterator.hasNext()) {
            Cell cell = iterator.next();
            colNameList.add(cell.getStringCellValue());
        }
        //第三步，获取当前sheet页已经写到了哪一行
        int lastRow = sheet.getLastRowNum();

        HSSFCellStyle style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER); // 创建一个居中格式

        CellStyle cs = wb.createCellStyle();
        cs.setAlignment(HorizontalAlignment.CENTER);
        cs.setWrapText(true);

        for (int i = 0; i < sourceList.size(); i++) {
            //第四步，从当前行的下一行开始续写
            HSSFRow row = sheet.createRow((int) i + 1 + lastRow);
            HashMap<String, String> sourceItem = sourceList.get(i);

            // 第五步，创建单元格，并设置值
            for (int j = 0; j < colNameList.size(); j++) {
                HSSFCell cell = row.createCell(j);
                cell.setCellStyle(cs);
                cell.setCellValue(wrapString(sourceItem.get(colNameList.get(j))));
            }
        }

        return wb;


    }

    protected void autoSizeColumns(HSSFWorkbook wb, List<Integer> colNumList) {
        for (int index = 0; index < colNumList.size(); index++) {
            HSSFSheet sheet = wb.getSheetAt(index);
            int colNum = colNumList.get(index);
            for (int colIndex = 0; colIndex < colNum; colIndex++) {
                sheet.autoSizeColumn(colIndex);
            }
        }
    }

    protected HSSFWorkbook continueWriteExcelWithSpecHead(HSSFWorkbook wb, int headRowIndex, int sheetIndex, List<HashMap<String, String>> sourceList) {
        // 第一步，获取webbook中的第一个sheet页
        HSSFSheet sheet = wb.getSheetAt(sheetIndex);
        // 第二步，获取表头
        List<String> colNameList = new ArrayList<String>();
        Iterator<Cell> iterator = sheet.getRow(headRowIndex).cellIterator();
        while (iterator.hasNext()) {
            Cell cell = iterator.next();
            colNameList.add(cell.getStringCellValue());
        }
        //第三步，获取当前sheet页已经写到了哪一行
        int lastRow = sheet.getLastRowNum();

        CellStyle cs = wb.createCellStyle();
        cs.setAlignment(HorizontalAlignment.CENTER);
        cs.setWrapText(true);

        for (int i = 0; i < sourceList.size(); i++) {
            //第四步，从当前行的下一行开始续写
            HSSFRow row = sheet.createRow((int) i + 1 + lastRow);
            HashMap<String, String> sourceItem = sourceList.get(i);

            // 第五步，创建单元格，并设置值
            for (int j = 0; j < colNameList.size(); j++) {
                HSSFCell cell = row.createCell(j);
                cell.setCellStyle(cs);
                cell.setCellValue(wrapString(sourceItem.get(colNameList.get(j))));
            }
        }

        return wb;


    }


    public static HSSFWorkbook exportExcelFile(HSSFWorkbook wb, String sheetName, List<String> colNameList, List<HashMap<String, String>> sourceList) {
        // 第二步，在webbook中添加一个sheet,对应Excel文件中的sheet
        HSSFSheet sheet = wb.createSheet(sheetName);
        // 第三步，在sheet中添加表头第0行,注意老版本poi对Excel的行数列数有限制short
        HSSFRow row = sheet.createRow((int) 0);
        // 第四步，创建单元格，并设置值表头 设置表头居中
        for (int i = 0; i < colNameList.size(); i++) {
            row.createCell((int) i).setCellValue(colNameList.get(i));
        }

        HSSFCellStyle style = wb.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER); // 创建一个居中格式

        for (int i = 0; i < sourceList.size(); i++) {
            row = sheet.createRow((int) i + 1);
            HashMap<String, String> sourceItem = sourceList.get(i);

            // 第五步，创建单元格，并设置值
            for (int j = 0; j < colNameList.size(); j++) {
                row.createCell((int) j).setCellValue(sourceItem.get(colNameList.get(j)));
            }
        }

        return wb;
    }

    public static void createSheet(HSSFWorkbook wb, String sheetName, String headContent, List<String> colNameList, HSSFCellStyle headStyle) {
        HSSFSheet sheet = wb.createSheet(sheetName);

        int rowStart = 0;
        //如果有特殊表头要求，新建一行
        if (StringUtils.isNotBlank(headContent)) {
            HSSFRow row1 = sheet.createRow((int) 0);
            HSSFRow row2 = sheet.createRow((int) 1);
            HSSFCell cell = row1.createCell((int) 0);
            cell.setCellValue(headContent);
            sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, colNameList.size() - 1));
            CellUtil.setAlignment(cell, HorizontalAlignment.CENTER);
            CellUtil.setVerticalAlignment(cell, VerticalAlignment.CENTER);
            rowStart += 2;
        }


        HSSFRow row = sheet.createRow((int) rowStart);

        // 第四步，创建单元格，并设置值表头 设置表头居中
        for (int i = 0; i < colNameList.size(); i++) {
            HSSFCell cell = row.createCell(i);
            cell.setCellValue(colNameList.get(i));
            cell.setCellStyle(headStyle);
        }
    }

    public static void writeOutPutStream(HttpServletResponse response, HSSFWorkbook wb, String fileName) throws IOException {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        fileName = new String(fileName.getBytes(), StandardCharsets.ISO_8859_1.name());
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
        OutputStream os = response.getOutputStream();
        wb.write(os);
    }



    /**
     * 根据当前用户返回门店和仓列表数据权限
     *
     * @return
     */
    protected Map<Long, PoiInfoDto> queryHasPermissionPoiInfoWithWarehouseMapOfCurrentUser() {
        // 查询当前账户信息 根据账户返回有权限查看的门店列表
        Long tenantId = ContextHolder.currentUserTenantId();

        PoiListQueryRequest request = new PoiListQueryRequest();
        request.setTenantId(tenantId);
        request.setEntityTypes(Arrays.asList(PoiEntityTypeEnum.STORE.code(),
                PoiEntityTypeEnum.REGIONAL_WAREHOUSE.code(), PoiEntityTypeEnum.SHAREABLE_WAREHOUSE.code()));
        // 排除绑定过共享前置仓的门店，按需使用
//        request.setExcludeShareableWarehouseBindingStore(true);
        List<PoiInfoDto> poiInfoBoList = emptyIfNull(poiRemoteService.queryPoiListWithCondition(request));

        if (CollectionUtils.isNotEmpty(poiInfoBoList)) {
            // 查询用户的门店权限
            List<Long> poiIds =
                    authRemoteService.queryAllPoiPermissionOfCurrentUser(
                            tenantId,
                            ContextHolder.currentUid(),
                            SAAS_B_APP_ID);
            poiInfoBoList = poiInfoBoList.stream()
                    .filter(poi -> poiIds.contains(poi.getPoiId()))
                    .collect(Collectors.toList());
        }

        poiInfoBoList.sort(Comparator.comparing(PoiInfoDto::getEntityType).reversed());

        return Fun.toMapQuietly(poiInfoBoList, PoiInfoDto::getPoiId, Function.identity());
    }

}
