package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/6/12
 * desc: 品牌分页查询响应
 */
@TypeDoc(
        description = "品牌分页查询响应"
)
@Data
@ApiModel("品牌分页查询响应")
public class PageQueryBrandVO {

    @FieldDoc(
            description = "品牌信息列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "品牌信息列表", required = true)
    private List<SkuBrandInfoVO> brandList;

    @FieldDoc(
            description = "分页信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "分页信息", required = true)
    private PageInfoVO pageInfo;
}
