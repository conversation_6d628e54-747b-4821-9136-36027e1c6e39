package com.sankuai.shangou.qnh.orderapi.configuration.pc;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * Date 2022/1/9 8:47 下午
 * Description
 */
@Component
public class SpringUtils implements ApplicationContextAware {

    private static ApplicationContext applicationContext = null;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        SpringUtils.applicationContext = applicationContext;
    }

    public static ApplicationContext getApplicationContext() {
        return applicationContext;
    }


    public static <T> T getBean(String beanName) {
        return (T) applicationContext.getBean(beanName);
    }
    public static <T> T getBean(Class<T> clazz) {return applicationContext.getBean(clazz);}

}