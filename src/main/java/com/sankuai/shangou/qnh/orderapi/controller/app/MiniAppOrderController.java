package com.sankuai.shangou.qnh.orderapi.controller.app;

import com.meituan.servicecatalog.api.annotations.*;
import com.sankuai.meituan.shangou.empower.auth.sdk.constant.CommonConstant;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.DataSecurity;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamDataType;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.ParamSource;
import com.sankuai.meituan.shangou.empower.auth.sdk.datasecurity.SecurityParam;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import com.sankuai.shangou.qnh.orderapi.annotation.ValidHeaderParam;
import com.sankuai.shangou.qnh.orderapi.context.app.ApiMethodParamThreadLocal;
import com.sankuai.shangou.qnh.orderapi.domain.dto.app.IdentityInfo;
import com.sankuai.shangou.qnh.orderapi.domain.request.app.*;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.CommonResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.DeliveryErrorSubTypeCountResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.OrderHomePageResponse;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.OrderListResponse;
import com.sankuai.shangou.qnh.orderapi.enums.app.ResultCodeEnum;
import com.sankuai.shangou.qnh.orderapi.service.app.MiniAppOrderService;
import com.sankuai.shangou.qnh.orderapi.annotation.app.Auth;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * @description:小程序订单服务
 * @author: gong_qisheng
 * @date: 2023/7/3
 * @time: 20:32
 * Copyright (C) 2015 Meituan
 * All rights reserved
 */
@InterfaceDoc(
        displayName = "小程序订单服务",
        type = "restful",
        scenarios = "包含查询各个状态的订单列表",
        description = "包含查询各个状态的订单列表",
        host = "https://pieapi-empower.meituan.com/"
)
@Slf4j
@Api(value = "小程序订单服务")
@RestController
@RequestMapping("/pieapi/miniapp/order")
public class MiniAppOrderController {

    @Resource
    MiniAppOrderService miniAppOrderService;

    @MethodDoc(
            displayName = "小程序订单tab页首页查询接口",
            description = "小程序订单tab页首页查询接口",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "小程序订单tab页首页查询接口请求",
                            type = HttpServletRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleUrl = "/pieapi/miniapp/order/homepage",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "小程序订单tab页首页查询接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/homepage", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    @ValidHeaderParam
    public CommonResponse<OrderHomePageResponse> homePage(@RequestBody HomePageRequest request) {
        return miniAppOrderService.homePage(request);
    }

    @MethodDoc(
            displayName = "分页查询订单列表",
            description = "分页查询订单列表，分页查询订单概要信息，包含对应商品列表。",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询订单列表请求",
                            type = OrderListRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleResponseData = "{\n" +
                    "    \"code\": 0,\n" +
                    "    \"message\": \"操作成功\",\n" +
                    "    \"data\": {\n" +
                    "        \"pageInfo\": {\n" +
                    "            \"page\": 1,\n" +
                    "            \"size\": 2,\n" +
                    "            \"totalPage\": 20,\n" +
                    "            \"totalSize\": 39\n" +
                    "        },\n" +
                    "        \"orderList\": [\n" +
                    "            {\n" +
                    "                \"tenantId\": \"1000011\",\n" +
                    "                \"channelId\": 300,\n" +
                    "                \"channelName\": \"京东到家\",\n" +
                    "                \"storeId\": \"1000017\",\n" +
                    "                \"storeName\": \"中台测试门店(共用饿了么京东渠道)\",\n" +
                    "                \"channelOrderId\": \"916923738000441\",\n" +
                    "                \"serialNo\": \"0\",\n" +
                    "                \"itemCount\": 7,\n" +
                    "                \"actualPayAmt\": 3810,\n" +
                    "                \"bizReceiveAmt\": 3091,\n" +
                    "                \"deliveryMethod\": 1,\n" +
                    "                \"deliveryMethodDesc\": \"达达专送\",\n" +
                    "                \"deliveryUserName\": null,\n" +
                    "                \"deliveryUserPhone\": null,\n" +
                    "                \"receiverName\": \"闻雄\",\n" +
                    "                \"receiverPhone\": \"13107111899,1425\",\n" +
                    "                \"receiveAddress\": \"长沙市岳麓区通程商业广场(麓山店)A座测试1号\",\n" +
                    "                \"channelOrderStatus\": 50,\n" +
                    "                \"channelOrderStatusDesc\": \"订单已取消\",\n" +
                    "                \"createTime\": \"1563195738000\",\n" +
                    "                \"refundAmt\": 0,\n" +
                    "                \"refundTagId\": 100,\n" +
                    "                \"refundTagDesc\": \"无\",\n" +
                    "                \"auditingRefundTagId\": 0,\n" +
                    "                \"auditingRefundTagDesc\": null,\n" +
                    "                \"couldOperateItemList\": [],\n" +
                    "                \"deliveryOrderType\": 0,\n" +
                    "                \"deliveryOrderTypeName\": \"即时订单\",\n" +
                    "                \"estimatedSendArriveTimeStart\": \"1563268500000\",\n" +
                    "                \"estimatedSendArriveTimeEnd\": \"1563268500000\",\n" +
                    "                \"pickupStatus\": 1,\n" +
                    "                \"pickupCompleteTime\": \"1563195817095\",\n" +
                    "                \"distributeStatus\": 1,\n" +
                    "                \"offlineOrderStatus\": 25,\n" +
                    "                \"updateTime\": \"1563246618000\",\n" +
                    "                \"channelExtraOrderId\": null,\n" +
                    "                \"comments\": null,\n" +
                    "                \"productList\": [\n" +
                    "                    {\n" +
                    "                        \"skuId\": \"1145223620684083287\",\n" +
                    "                        \"upcCode\": \"\",\n" +
                    "                        \"skuName\": \"快捷优化00006 无\",\n" +
                    "                        \"picUrl\": null,\n" +
                    "                        \"specification\": \"500g/份\",\n" +
                    "                        \"sellUnit\": \"1\",\n" +
                    "                        \"originalTotalPrice\": null,\n" +
                    "                        \"totalPayAmount\": null,\n" +
                    "                        \"totalDiscountAmount\": null,\n" +
                    "                        \"unitPrice\": 480,\n" +
                    "                        \"count\": 7,\n" +
                    "                        \"isRefund\": 1,\n" +
                    "                        \"refundCount\": 7,\n" +
                    "                        \"boothName\": null,\n" +
                    "                        \"offlinePrice\": 750,\n" +
                    "                        \"stallSettleAmt\": 5250,\n" +
                    "                        \"realQuantity\": null,\n" +
                    "                        \"customerSkuId\": \"1145223620684083287\"\n" +
                    "                    }\n" +
                    "                ]\n" +
                    "            },\n" +
                    "            {\n" +
                    "                \"tenantId\": \"1000011\",\n" +
                    "                \"channelId\": 300,\n" +
                    "                \"channelName\": \"京东到家\",\n" +
                    "                \"storeId\": \"1000017\",\n" +
                    "                \"storeName\": \"中台测试门店(共用饿了么京东渠道)\",\n" +
                    "                \"channelOrderId\": \"916921041000242\",\n" +
                    "                \"serialNo\": \"0\",\n" +
                    "                \"itemCount\": 7,\n" +
                    "                \"actualPayAmt\": 3810,\n" +
                    "                \"bizReceiveAmt\": 3091,\n" +
                    "                \"deliveryMethod\": 1,\n" +
                    "                \"deliveryMethodDesc\": \"达达专送\",\n" +
                    "                \"deliveryUserName\": null,\n" +
                    "                \"deliveryUserPhone\": null,\n" +
                    "                \"receiverName\": \"闻雄\",\n" +
                    "                \"receiverPhone\": \"13107216795,1232\",\n" +
                    "                \"receiveAddress\": \"长沙市岳麓区通程商业广场(麓山店)A座测试1号\",\n" +
                    "                \"channelOrderStatus\": 50,\n" +
                    "                \"channelOrderStatusDesc\": \"订单已取消\",\n" +
                    "                \"createTime\": \"1563193202000\",\n" +
                    "                \"refundAmt\": 0,\n" +
                    "                \"refundTagId\": 200,\n" +
                    "                \"refundTagDesc\": \"全部\",\n" +
                    "                \"auditingRefundTagId\": 0,\n" +
                    "                \"auditingRefundTagDesc\": null,\n" +
                    "                \"couldOperateItemList\": [],\n" +
                    "                \"deliveryOrderType\": 0,\n" +
                    "                \"deliveryOrderTypeName\": \"即时订单\",\n" +
                    "                \"estimatedSendArriveTimeStart\": \"1563277500000\",\n" +
                    "                \"estimatedSendArriveTimeEnd\": \"1563277500000\",\n" +
                    "                \"pickupStatus\": 48,\n" +
                    "                \"pickupCompleteTime\": \"1563278626916\",\n" +
                    "                \"distributeStatus\": 48,\n" +
                    "                \"offlineOrderStatus\": 25,\n" +
                    "                \"updateTime\": \"1563332845000\",\n" +
                    "                \"channelExtraOrderId\": null,\n" +
                    "                \"comments\": null,\n" +
                    "                \"productList\": [\n" +
                    "                    {\n" +
                    "                        \"skuId\": \"1145223620684083287\",\n" +
                    "                        \"upcCode\": \"\",\n" +
                    "                        \"skuName\": \"快捷优化00006 无\",\n" +
                    "                        \"picUrl\": null,\n" +
                    "                        \"specification\": \"500g/份\",\n" +
                    "                        \"sellUnit\": \"1\",\n" +
                    "                        \"originalTotalPrice\": null,\n" +
                    "                        \"totalPayAmount\": null,\n" +
                    "                        \"totalDiscountAmount\": null,\n" +
                    "                        \"unitPrice\": 480,\n" +
                    "                        \"count\": 7,\n" +
                    "                        \"isRefund\": 1,\n" +
                    "                        \"refundCount\": 7,\n" +
                    "                        \"boothName\": null,\n" +
                    "                        \"offlinePrice\": 750,\n" +
                    "                        \"stallSettleAmt\": 5250,\n" +
                    "                        \"realQuantity\": null,\n" +
                    "                        \"customerSkuId\": \"1145223620684083287\"\n" +
                    "                    }\n" +
                    "                ]\n" +
                    "            }\n" +
                    "        ]\n" +
                    "    }\n" +
                    "}",
            restExampleUrl = "/pieapi/miniapp/order/search",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @ApiOperation(value = "订单搜索")
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/search", method = RequestMethod.POST)
    @ResponseBody
    @ValidHeaderParam
    public CommonResponse<OrderListResponse> orderSearch(@Valid @RequestBody OrderSearchMiniAppRequest request) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        List<Long> storeIdList = identityInfo.getStoreIdList();
        if (CollectionUtils.isEmpty(storeIdList)) {
            return CommonResponse.fail(ResultCodeEnum.FAIL.getCode(), "请选择门店");
        }
        request.setStoreIds(storeIdList);
        return miniAppOrderService.orderList(request.build());
    }

    @MethodDoc(
            displayName = "分页查询全部订单列表",
            description = "分页查询全部订单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "全部订单列表分页查询请求",
                            type = OrderListRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleUrl = "/pieapi/miniapp/order/orderList",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "分页查询全部订单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.E_TOKEN_HEADER, required = true, paramType = "header", dataType = "string")
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @ValidHeaderParam
    @RequestMapping(value = "/orderList", method = {RequestMethod.POST})
    public CommonResponse<OrderListResponse> orderList(@Valid @RequestBody OrderListMiniAppRequest request) {
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        request.setStoreIds(identityInfo.getStoreIdList());
        // 进行参数校验
        String validateResult = request.validate();
        if (validateResult != null) {
            return CommonResponse.fail(ResultCodeEnum.FAIL.getCode(), validateResult);
        }
        return miniAppOrderService.orderList(request);
    }

    @MethodDoc(
            displayName = "分页查询待多门店拣货订单列表",
            description = "分页查询待多门店拣货订单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "分页查询多门店待拣货订单列表请求",
                            type = QueryWaitToPickStoresOrderRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleUrl = "/pieapi/miniapp/order/querywaitpick",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "分页查询多门店待拣货订单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/querywaitpick", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    @ValidHeaderParam
    public CommonResponse<OrderListResponse> queryWaitPickOrder(@Valid @RequestBody QueryWaitToPickStoresOrderRequest request) {
        return miniAppOrderService.queryWaitToPickOrder(request);
    }

    @MethodDoc(
            displayName = "根据子类型分页查询异常订单列表",
            description = "根据子类型分页查询异常订单列表",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "根据子类型分页查询异常订单列表请求",
                            type = QueryDeliveryErrorOrderBySubTypeRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleUrl = "/pieapi/miniapp/order/querydeliveryerrorbysubtype",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "根据子类型分页查询异常订单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/querydeliveryerrorbysubtype", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    @ValidHeaderParam
    public CommonResponse<OrderListResponse> queryDeliveryErrorBySubType(@Valid @RequestBody QueryDeliveryErrorOrderBySubTypeMiniAppRequest request) {
        return miniAppOrderService.queryDeliveryErrorBySubType(request);
    }

    @MethodDoc(
            displayName = "异常订单子类型数量查询",
            description = "异常订单子类型数量查询",
            parameters = {
                    @ParamDoc(
                            name = "request",
                            description = "异常订单子类型数量查询",
                            type = HttpServletRequest.class,
                            paramType = ParamType.REQUEST_BODY,
                            rule = "非空",
                            requiredness = Requiredness.REQUIRED
                    )

            },
            restExampleUrl = "/pieapi/miniapp/order/querydeliveryerrorsubtypecount",
            extensions = {
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE",
                            content = "数据鉴权逻辑：门店权限"
                    ),
                    @ExtensionDoc(
                            name = "SECURITY_PRIVILEGE_VERIFY",
                            content = "True"
                    ),
            }
    )
    @Auth
    @ApiOperation(value = "异常订单子类型数量查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "uuid", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "os", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "appVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "jsVersion", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = "storeId", required = true, paramType = "header", dataType = "string"),
            @ApiImplicitParam(name = CommonConstant.AUTHORIZATION_HEADER, required = true, paramType = "header", dataType = "string"),
    })
    @MethodLog(logResponse = true, logRequest = true)
    @DataSecurity({
            @SecurityParam(value = "storeid", type = ParamDataType.ALL_POI, source = ParamSource.REQUEST_HEADER)
    })
    @RequestMapping(value = "/querydeliveryerrorsubtypecount", method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    @ValidHeaderParam
    public CommonResponse<DeliveryErrorSubTypeCountResponse> queryDeliveryErrorSubTypeCount() {
        return miniAppOrderService.queryDeliveryErrorSubTypeCount();
    }
}
