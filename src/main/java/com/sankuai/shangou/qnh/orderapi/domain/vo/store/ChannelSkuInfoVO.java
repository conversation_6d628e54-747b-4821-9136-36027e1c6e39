package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.ChannelSkuPriceTrendVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/6/12
 * desc:
 */
@TypeDoc(
        description = "渠道商品信息"
)
@Data
@ApiModel("渠道商品信息")
public class ChannelSkuInfoVO {

    @FieldDoc(
            description = "渠道ID   -1-线下 100-美团 200-饿了么 300-京东", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID", required = true)
    private Integer channelId;

    @FieldDoc(
            description = "商品状态 -1-未上线 1-已上架 2-已上架", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品状态", required = true)
    private Integer skuStatus;

    @FieldDoc(
            description = "售价 单位:元", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "售价", required = true)
    private Double price;

    @FieldDoc(
            description = "库存", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "库存", required = true)
    private Integer stock;

    @FieldDoc(
            description = "库存描述字段", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "库存描述字段")
    private String stockDesc;

    @FieldDoc(
            description = "前台分类Id", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "前台分类Id", required = true)
    private String frontCategoryId;

    @FieldDoc(
            description = "前台分类名称", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "前台分类名称", required = true)
    private String frontCategoryName;

    @FieldDoc(
            description = "前台分类编码全路径", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "前台分类编码全路径", required = true)
    private String frontCategoryCodePath;

    @FieldDoc(
            description = "前台分类名称全路径", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "前台分类名称全路径", required = true)
    private String frontCategoryNamePath;

    @FieldDoc(
            description = "市斤价, 单位:元", requiredness = Requiredness.NONE
    )
    @ApiModelProperty(name = "市斤价")
    private Double pricePer500g;

    @FieldDoc(
            description = "显示价格趋势图标 true-显示 false-不展示", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "显示价格趋势图标 true-显示 false-不展示", required = true)
    private Boolean showPriceTrendIcon;

    @FieldDoc(
            description = "价格趋势"
    )
    @ApiModelProperty(name = "价格趋势")
    private ChannelSkuPriceTrendVO priceTrend;

    @FieldDoc(
            description = "是否在促销中"
    )
    @ApiModelProperty(name = "是否在促销中")
    private Boolean atPromotion;

    @FieldDoc(
            description = "渠道呈现价和当前价是否一致"
    )
    @ApiModelProperty(name = "渠道呈现价和当前价是否一致")
    private Boolean priceEqualSign;

    @FieldDoc(
            description = "渠道价"
    )
    @ApiModelProperty(name = "渠道价")
    private String presentPrice;

    @FieldDoc(
            description = "渠道可售状态,0-全部 1-可售 2-不可售"
    )
    @ApiModelProperty(name = "渠道可售状态")
    private Integer allowSale;

    @FieldDoc(
            description = "渠道审核状态"
    )
    @ApiModelProperty(name = "渠道审核状态")
    private Integer auditStatus;

    @FieldDoc(
            description = "渠道类目Id(末级)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道类目Id", required = true)
    private String channelCategoryCode;

    @FieldDoc(
            description = "渠道类目名称(末级)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道类目类名称", required = true)
    private String channelCategoryName;

    @FieldDoc(
            description = "渠道类目编码全路径", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道类目编码全路径", required = true)
    private String channelCategoryCodePath;

    @FieldDoc(
            description = "渠道类目名称全路径", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道类目名称全路径", required = true)
    private String channelCategoryNamePath;
}
