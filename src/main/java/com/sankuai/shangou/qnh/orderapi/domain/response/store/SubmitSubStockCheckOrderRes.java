package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

@TypeDoc(
        description = "子盘点单提交返回结果",
        authors = {
                "qianteng"
        }
)
@Data
@ApiModel("子盘点单提交返回结果")
public class SubmitSubStockCheckOrderRes {
    @FieldDoc(
            description = "子单id"
    )
    public String subCheckOrderId;
    @FieldDoc(
            description = "失败条目列表"
    )
    public List<FailSkuItem> failSkuItemList;
    public static class FailSkuItem {
        private String skuId;
        private String failText;

        public String getSkuId() {
            return skuId;
        }

        public void setSkuId(String skuId) {
            this.skuId = skuId;
        }

        public String getFailText() {
            return failText;
        }

        public void setFailText(String failText) {
            this.failText = failText;
        }
    }
}
