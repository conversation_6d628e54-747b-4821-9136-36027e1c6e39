package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/8/26
 * desc:
 */
@TypeDoc(
        description = "查询渠道分类请求"
)
@Data
@ApiModel("查询渠道分类请求")
public class GetChannelCategoryRequest {

    @FieldDoc(
            description = "渠道ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道ID", required = true)
    @NotNull
    private Integer channelId;

    @FieldDoc(
            description = "父类目ID列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "父类目ID列表", required = true)
    @NotEmpty
    private List<String> parentIdList;

    @FieldDoc(
            description = "分类层级  从1开始", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "分类层级  从1开始")
    private Integer depth;

}
