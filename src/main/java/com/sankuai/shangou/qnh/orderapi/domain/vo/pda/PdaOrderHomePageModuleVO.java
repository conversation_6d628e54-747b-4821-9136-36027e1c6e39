package com.sankuai.shangou.qnh.orderapi.domain.vo.pda;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.annotation.app.BindAuthCode;
import com.sankuai.shangou.qnh.orderapi.constant.app.IntegerBooleanConstants;
import com.sankuai.shangou.qnh.orderapi.enums.app.AuthCodeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @createTime 2019/11/19
 * @description
 */
@TypeDoc(
        description = "订单首页模块"
)
@Data
@ApiModel("订单首页模块")
public class PdaOrderHomePageModuleVO {

    @FieldDoc(
            description = "是否展示待接单tab (0:否, 1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示待接单tab (0:否, 1:是)", required = true)
    @NotNull
    private Integer showWaitToTakeOrderTab;

    @FieldDoc(
            description = "是否展示待拣货tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示待拣货tab (0:否，1:是)", required = true)
    @NotNull
    @BindAuthCode(AuthCodeEnum.ORDER_WAIT_TO_PICK)
    private Integer showWaitToPickTab;

    @FieldDoc(
            description = "是否展示待配送tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示待配送tab (0:否，1:是)", required = true)
    @NotNull
    @BindAuthCode(AuthCodeEnum.ORDER_WAIT_TO_DELIVERY)
    private Integer showWaitToDeliveryTab;

    @FieldDoc(
            description = "是否展示配送异常tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示配送异常tab (0:否，1:是)", required = true)
    @NotNull
    @BindAuthCode(AuthCodeEnum.ORDER_DELIVERY_ERROR)
    private Integer showDeliveryErrorTab;

    @FieldDoc(
            description = "是否展示退款tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示退款tab (0:否，1:是)", required = true)
    @NotNull
    @BindAuthCode(AuthCodeEnum.ORDER_REFUND_AUDIT)
    private Integer showWaitToAuditRefundTab;

    @FieldDoc(
            description = "是否展示售后tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示售后tab (0:否，1:是)", required = true)
    @NotNull
    @BindAuthCode(AuthCodeEnum.ORDER_TAB_AFTER_SALES)
    private Integer showWaitToAuditAfterSaleTab;

    @FieldDoc(
            description = "是否展示订单tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示订单tab (0:否，1:是)", required = true)
    @NotNull
    @BindAuthCode(AuthCodeEnum.ORDER_SUB_TAB)
    private Integer showOrderTab;


    @FieldDoc(
            description = "是否展示订单查询 (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示订单查询 (0:否，1:是)", required = true)
    @NotNull
    @BindAuthCode(AuthCodeEnum.ORDER_SEARCH)
    private Integer showOrderSearch;

    @FieldDoc(
            description = "是否将规格信息放到订单信息中(0:不展示,1:展示)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否将规格信息放到订单信息中(0:不展示,1:展示)", required = true)
    @NotNull
    private Integer appendSpecToOrderItem;

    @FieldDoc(
            description = "是否展示待自提tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示待自提tab (0:否，1:是)", required = true)
    @NotNull
    @BindAuthCode(AuthCodeEnum.WAIT_TO_SELF_FETCH)
    private Integer showWaitToSelfFetchTab;


    public PdaOrderHomePageModuleVO(){
        this.setShowWaitToTakeOrderTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        this.setShowWaitToPickTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        this.setShowWaitToSelfFetchTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        this.setShowWaitToDeliveryTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        this.setShowDeliveryErrorTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        this.setShowWaitToAuditAfterSaleTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        this.setShowOrderSearch(IntegerBooleanConstants.BOOLEAN_FALSE);
        this.setShowWaitToAuditRefundTab(IntegerBooleanConstants.BOOLEAN_FALSE);
    }

}
