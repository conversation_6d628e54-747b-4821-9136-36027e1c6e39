package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2019/3/30 14:51
 * @Description:
 */
@Data
@NoArgsConstructor
public class ConfirmOrderQueryRequest extends PageRequest {

    private String poiName;

    private String poiId;

    private List<String> channelIds;

    private String confirmStatus;

    private String pickStatus;

    private String orderType;

    /**
     * 渠道流水号
     */
    private String orderSerialNumber;

    /**
     * 前置仓id
     */
    private List<Long> warehouseIdList;

}
