package com.sankuai.shangou.qnh.orderapi.constant.pc;

/**
 * 配置系统Key常量
 *
 * <AUTHOR>
 */
public abstract class ConfigDefaultValueConstant {

    /**
     * 默认输入验证码开关
     **/
    public static final String DEFAULT_INPUT_VERIFICATION_CODE_SWITCH = "ON";
    /**
     * 默认登录过期时间 2个小时
     **/
    public static final int DEFAULT_LOGIN_EXPIRE_TIME_IN_SECONDS = 2 * 3600;
    /**
     * 默认允许访问域名白名单
     **/
    public static final String DEFAULT_ALLOW_DOMAIN_WHITELIST = "sankuai.com,meituan.com,localhost,127.0.0.1";

    /**
     * 角色列表默认大小
     */
    public static final Integer ROLE_DEFAULT_LIST_SIZE = 300;

    /**
     * B端商家的appId
     */
    public static final Integer SAAS_B_APP_ID = 3;
    /**
     * 兼容逻辑 默认商家端 避免上线后清除所有token
     */
    public static final Integer DEFAULT_BIZ_APP_ID = 2;
    /**
     * 摊位结算导出查询每页记录数
     */
    public static final int DEFAULT_BOOTH_SETTLEMENT_EXPORT_QUERY_PAGE_SIZE = 100;

    /**
     * 默认登录过期时间 2个小时
     **/
    public static final int DEFAULT_SELECT_REPO_EXPIRE_TIME_IN_SECONDS = 2 * 3600;

}
