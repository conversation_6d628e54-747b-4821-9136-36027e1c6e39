package com.sankuai.shangou.qnh.orderapi.domain.response.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.PageInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.SkuBasicInfoVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: WangSuku<PERSON>
 * @create: 2019-11-29
 **/

@TypeDoc(
        description = "根据标品名称分页查询标品信息响应"
)
@Data
@ApiModel("根据标品名称分页查询标品信息响应")
public class QuerySkuPageInfoBySkuNameResponse {


    @FieldDoc(
            description = "分页信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "分页信息", required = true)
    private PageInfoVO pageInfo;

    @FieldDoc(
            description = "当前页内容", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "当前页内容", required = true)
    private List<SkuBasicInfoVo> skuLIst;




}
