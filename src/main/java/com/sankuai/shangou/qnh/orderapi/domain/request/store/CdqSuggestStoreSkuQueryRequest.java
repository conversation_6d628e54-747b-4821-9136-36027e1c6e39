package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@TypeDoc(
        description = " 商品名称联想查询分页查询请求"
)
@Data
@ApiModel(" 商品名称联想查询分页查询请求")
public class CdqSuggestStoreSkuQueryRequest {

    @FieldDoc(
            description = "商品名称，支持总库商品和区域商品名称联线查询", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "商品名称", required = true)
    @NotNull
    private String skuName;
    @FieldDoc(
            description = "传入目标门店后，回包中会返回该商品在门店商品是否已经存在", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "目标门店", required = true)
    @NotNull
    private Integer storeId;
    @FieldDoc(
            description = "页大小,最大50", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "页大小", required = true)
    @NotNull
    private Integer pageSize;
    @FieldDoc(
            description = "页码，下标从1开始", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "页码", required = true)
    @NotNull
    private Integer pageNo;


}
