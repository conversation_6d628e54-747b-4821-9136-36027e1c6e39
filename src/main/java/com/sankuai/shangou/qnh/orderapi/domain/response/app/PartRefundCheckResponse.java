package com.sankuai.shangou.qnh.orderapi.domain.response.app;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.GiftBagPartRefundCheckVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.OrderItemPartRefundCheckVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2020-02-17 16:31
 * @Description:
 */
@TypeDoc(
        description = "部分退款页面检查退款请求响应"
)
@ApiModel("部分退款页面检查退款请求响应")
@Data
public class PartRefundCheckResponse {

    @FieldDoc(
            description = "退款商品列表", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "退款商品列表", required = true)
    private List<OrderItemPartRefundCheckVO> partRefundCheckVOList;

    @FieldDoc(
            description = "可退礼袋", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "可退礼袋", required = true)
    private List<GiftBagPartRefundCheckVO> giftBagCheckList;

    @FieldDoc(
            description = "拣货完成", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "拣货完成", required = true)
    private boolean pickupCompleted;

    @FieldDoc(
            description = "应该获取集合店商家手机号,true:是", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "应该获取集合店商家手机号")
    private Boolean shouldGetGatherPhone;
}

