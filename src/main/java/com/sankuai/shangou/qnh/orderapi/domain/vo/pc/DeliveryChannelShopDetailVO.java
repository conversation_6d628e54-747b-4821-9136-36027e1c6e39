package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/16
 * desc: 配送渠道门店详情
 */
@Setter
@Getter
public class DeliveryChannelShopDetailVO {

    @FieldDoc(
            description = "配送渠道ID"
    )
    @ApiModelProperty(value = "配送渠道ID")
    private String deliveryChannelId;

    @FieldDoc(
            description = "配送渠道名称"
    )
    @ApiModelProperty(value = "配送渠道名称")
    private String deliveryChannelName;

    @FieldDoc(
            description = "配送门店ID"
    )
    @ApiModelProperty(value = "配送门店ID")
    private String deliveryShopId;

    @FieldDoc(
            description = "配送门店名称"
    )
    @ApiModelProperty(value = "配送门店名称")
    private String deliveryShopName;

    @FieldDoc(
            description = "配送服务包编码"
    )
    @ApiModelProperty(value = "配送服务包编码")
    private List<String> deliveryServiceCodes;

    @FieldDoc(
            description = "状态 0:禁用 1:启用"
    )
    @ApiModelProperty(value = "状态")
    private Integer status;
}
