package com.sankuai.shangou.qnh.orderapi.service.common.query;

import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.meituan.shangou.saas.order.management.client.dto.request.online.ViewIdCondition;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.sankuai.meituan.reco.pickselect.query.consts.SortTypeEnum;
import com.sankuai.meituan.reco.pickselect.query.dto.OrderPickDetailDto;
import com.sankuai.meituan.reco.pickselect.query.thrift.fulfill.FulfillThriftService;
import com.sankuai.meituan.reco.pickselect.query.thrift.fulfill.request.PageQueryFulfillingOrderRequest;
import com.sankuai.meituan.reco.pickselect.query.thrift.fulfill.response.PagedFulfillingOrdersResponseV2;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.OrderListResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.OrderVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.PageInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.PickWarnDurationVO;
import com.sankuai.shangou.qnh.orderapi.enums.app.OrderViewStatusEnum;
import com.sankuai.shangou.qnh.orderapi.service.common.OrderListRequestContext;
import com.sankuai.shangou.qnh.orderapi.service.common.OrderRequestBuilder;
import com.sankuai.shangou.qnh.orderapi.service.common.PageUtil;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.map.MultiKeyMap;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @since 2024/7/17
 **/
@Service
@Slf4j
public class Wait2PickQueryOrderService extends QueryOrderService {

    @Resource
    private OrderRequestBuilder orderRequestBuilder;

    @Resource(name = "queryFulfillThriftService")
    private FulfillThriftService queryFulfillThriftService;

    protected Pair<List<OCMSOrderVO>, PageInfoVO> queryOrderInfo(OrderListRequestContext request) {
        //请求拣货获取订单号列表
        PageQueryFulfillingOrderRequest pageQueryFulfillingOrderRequest = orderRequestBuilder.buildPageQueryFulfillingOrderRequest(request.getTenantId(), request.getStoreId(), request.getPage(), request.getSize());
        boolean pickQuerySortSwitch = MccConfigUtil.isWaitPickSortingRuleByParam();
        // 传递排序字段 -> 前端未传递 || 传递为默认值（0） || 拣货服务开关关闭，则传递默认排序 ->【订单-待拣货】页面默认排序是按预计送达时间升序排序
        Integer sortType = Objects.isNull(request.getSortType()) || Objects.equals(SortTypeEnum.DEFAULT.getCode(), request.getSortType()) || !pickQuerySortSwitch ? SortTypeEnum.DELIVERY_TIME_ASC.getCode() : request.getSortType();
        pageQueryFulfillingOrderRequest.setSortType(sortType);
        log.info("fulfillThriftService.pageQueryFulfillingOrders request:{}", pageQueryFulfillingOrderRequest);
        PagedFulfillingOrdersResponseV2 pagedFulfillingOrdersResponse = queryFulfillThriftService.pageQueryFulfillingOrdersV2(pageQueryFulfillingOrderRequest);
        log.info("fulfillThriftService.pageQueryFulfillingOrders response:{}", pagedFulfillingOrdersResponse);
        if (pagedFulfillingOrdersResponse.getStatus().getCode() != com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum.SUCCESS.getValue()) {
            throw new BizException(ResultCode.FAIL.getCode(), pagedFulfillingOrdersResponse.getStatus().getMessage());
        }
        // 开关关闭 -> 返给前端-1
        sortType = !pickQuerySortSwitch ? SortTypeEnum.DEFAULT.getCode() : sortType;
        if (CollectionUtils.isEmpty(pagedFulfillingOrdersResponse.getOrders())) {
            PageInfoVO pageInfoVO = PageUtil.buildEmptyPageInfoVO();
            pageInfoVO.setSortType(sortType);
            return new Pair<>(Lists.emptyList(), pageInfoVO);
        }
        request.setWaitToPickOrderList(pagedFulfillingOrdersResponse.getOrders());
        PageInfoVO pageInfoVO = PageUtil.buildPageInfoVO(pagedFulfillingOrdersResponse.getPageNo(), pagedFulfillingOrdersResponse.getPageSize(), pagedFulfillingOrdersResponse.getTotalSize().intValue());
        // 返回给前端的排序类型
        pageInfoVO.setSortType(sortType);
        List<ViewIdCondition> viewIdConditions = pagedFulfillingOrdersResponse.getOrders().stream().map(e -> new ViewIdCondition(e.getOrderBizType(), e.getChannelOrderId())).collect(Collectors.toList());
        List<OCMSOrderVO> ocmsOrderVOS = queryOCMSVoByViewOrderId(request.getTenantId(), viewIdConditions, request.isShowSalePrice());
        // 重新排序 -- 按照拣货服务返回的ID顺序
        List<OCMSOrderVO> ocmsOrderVOSortList = reSort(ocmsOrderVOS, pagedFulfillingOrdersResponse.getOrders());
        return new Pair<>(ocmsOrderVOSortList, pageInfoVO);
    }

    /**
     * 重新排序
     * @param ocmsOrderVOS
     * @param orderPickDetailDtos
     * @return
     */
    private List<OCMSOrderVO> reSort(List<OCMSOrderVO> ocmsOrderVOS, List<OrderPickDetailDto> orderPickDetailDtos) {
        if (CollectionUtils.isEmpty(ocmsOrderVOS) || CollectionUtils.isEmpty(orderPickDetailDtos)) {
            return ocmsOrderVOS;
        }
        Map<String, Integer> orderSortMap = new HashMap<>(orderPickDetailDtos.size());
        for (int i = 0; i < orderPickDetailDtos.size(); i++) {
            orderSortMap.put(orderPickDetailDtos.get(i).getChannelOrderId(), i);
        }
        // 正常情况下，两个集合的数据是一致的，兜底：优先按照存在拣货服务返回的订单id排序
        return ocmsOrderVOS.stream()
                .sorted(Comparator.comparingInt(order -> orderSortMap.getOrDefault(order.getViewOrderId(), Integer.MAX_VALUE)))
                .collect(Collectors.toList());
    }


    /**
     * 不支持 多门店模式
     * @param tenantId
     * @param storeIdList
     * @param entityType
     * @return
     */
    @Override
    public Integer countAll(Long tenantId, List<Long> storeIdList, Integer entityType) {
        Long storeId = storeIdList.get(0);
        //请求拣货获取待拣货数量
        PageQueryFulfillingOrderRequest pageQueryFulfillingOrderRequest = orderRequestBuilder.buildPageQueryFulfillingOrderRequest(tenantId, storeId, 1, 1);
        log.info("OCMSOrderServiceWrapper.homePage   fulfillThriftService.pageQueryFulfillingOrders request:{}", pageQueryFulfillingOrderRequest);
        com.sankuai.meituan.reco.pickselect.query.thrift.fulfill.response.PagedFulfillingOrdersResponse pagedFulfillingOrdersResponse = queryFulfillThriftService.pageQueryFulfillingOrders(pageQueryFulfillingOrderRequest);
        log.info("OCMSOrderServiceWrapper.homePage  fulfillThriftService.pageQueryFulfillingOrders response:{}", pagedFulfillingOrdersResponse);
        if (pagedFulfillingOrdersResponse.getStatus().getCode() == com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum.SUCCESS.getValue()) {
            return pagedFulfillingOrdersResponse.getTotalSize().intValue();
        }
        return 0;
    }

    @Override
    public void addExtraInfo(OrderListResponse orderListResponse, OrderListRequestContext request) {
        setOrderListResponseViewStatus(orderListResponse, OrderViewStatusEnum.WAIT_TO_PICK_ORDER);
        setPickWarnDuration(orderListResponse.getOrderList(), request);
    }

    public void setPickWarnDuration(List<OrderVO> orderList, OrderListRequestContext request) {
        if (CollectionUtils.isEmpty(request.getWaitToPickOrderList()) || CollectionUtils.isEmpty(orderList)) {
            return;
        }
        try {
            MultiKeyMap<String, OrderPickDetailDto> map = new MultiKeyMap<>();
            request.getWaitToPickOrderList().forEach(item -> map.put(item.getChannelOrderId(), String.valueOf(item.getOrderBizType()), item));
            orderList.forEach(item -> {
                final String orderBizType = String.valueOf(ChannelOrderConvertUtils.convertBizType(item.getChannelId()));
                final OrderPickDetailDto orderPickDetailDto = map.get(item.getChannelOrderId(), orderBizType);
                if (Objects.nonNull(orderPickDetailDto)) {
                    PickWarnDurationVO pickWarnDurationVO = new PickWarnDurationVO();
                    pickWarnDurationVO.setWarnDuration(orderPickDetailDto.getWarnDuration());
                    pickWarnDurationVO.setDeliverTime(orderPickDetailDto.getDeliverTime());
                    pickWarnDurationVO.setIsReserved(orderPickDetailDto.getIsReserved());
                    pickWarnDurationVO.setPushTaskTime(orderPickDetailDto.getPushTaskTime());
                    pickWarnDurationVO.setCurrentTime(orderPickDetailDto.getCurrentTime());
                    pickWarnDurationVO.setPickTimeOutTime(orderPickDetailDto.getPickTimeOutTime());
                    item.setPickWarnDuration(pickWarnDurationVO);
                }
            });
        } catch (Exception e) {
            log.info("setPickWarnDuration error:{}", e.getMessage());
        }
    }

}
