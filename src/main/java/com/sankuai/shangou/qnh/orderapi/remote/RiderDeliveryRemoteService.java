package com.sankuai.shangou.qnh.orderapi.remote;

import com.dianping.cat.Cat;
import com.dianping.rhino.annotation.Degrade;
import com.dianping.rhino.annotation.Rhino;
import com.meituan.reco.pickselect.common.constants.ResultCode;
import com.meituan.shangou.saas.order.management.client.utils.ChannelOrderConvertUtils;
import com.sankuai.meituan.shangou.empower.rider.client.common.ResponseCodeEnum;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.RiderQueryThriftService;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TDeliveryProofPhotoInfo;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.dto.TRiderDeliveryException;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.QueryActivateDeliveryOrderCntDetailRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.QueryDeliveryExceptionByChannelOrderRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.request.QueryDeliveryQuantityRequest;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.DeliveryExceptionResponse;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.QueryActivateDeliveryOrderCntDetailResponse;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.QueryDeliveryProofPhotoResponse;
import com.sankuai.meituan.shangou.empower.rider.client.thrift.query.response.QueryDeliveryQuantityResponse;
import com.sankuai.shangou.qnh.orderapi.context.app.ApiMethodParamThreadLocal;
import com.sankuai.shangou.qnh.orderapi.domain.dto.app.IdentityInfo;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.DeliveryExceptionInfoVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.DeliveryExceptionSummaryVO;
import com.sankuai.shangou.qnh.orderapi.enums.app.ResultCodeEnum;
import com.sankuai.shangou.qnh.orderapi.enums.app.TmsDeliveryStatusDescEnum;
import com.sankuai.shangou.qnh.orderapi.exception.store.CommonLogicException;
import com.sankuai.shangou.qnh.orderapi.exception.store.CommonRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/5/4 22:46
 **/
@Service
@Rhino
@Slf4j
public class RiderDeliveryRemoteService {

    @Resource
    private RiderQueryThriftService riderQueryThriftService;

    @Degrade(rhinoKey = "RiderDeliveryRemoteService.getDeliveryProofPhotoInfo",
            fallBackMethod = "getDeliveryProofPhotoInfoFallback",
            timeoutInMilliseconds = 2000,
            ignoreExceptions = CommonLogicException.class)
    public QueryDeliveryProofPhotoResponse getDeliveryProofPhotoInfo(Long offlineOrderId) throws CommonLogicException {
        QueryDeliveryProofPhotoResponse response = null;
        try {
            log.info("start invoke riderQueryThriftService.queryDeliveryProofPhotosByOrderId, orderId:{}", offlineOrderId);
            response = riderQueryThriftService.queryDeliveryProofPhotosByOrderId(offlineOrderId);
            log.info("end invoke riderQueryThriftService.queryDeliveryProofPhotosByOrderId, orderId:{}, response:{}", offlineOrderId, response);
        } catch (Exception e) {
            log.error("invoke riderQueryThriftService.queryDeliveryProofPhotosByOrderId error", e);
            throw new CommonRuntimeException();
        }

        if (response == null || response.getStatus() == null) {
            throw new CommonRuntimeException();
        }
        if (response.getStatus().getCode() != ResultCode.SUCCESS.getCode()) {
            throw new CommonLogicException(response.getStatus().getMsg());
        }

        return response;
    }

    private List<TDeliveryProofPhotoInfo> getDeliveryProofPhotoInfoFallback(Long offlineOrderId) {
        log.warn("riderQueryThriftService.queryDeliveryProofPhotosByOrderId 方法降级");
        return Collections.emptyList();
    }


    /**
     * 查询骑手各状态的运单数量.
     *
     * @param statusList 待查询状态列表
     * @return Map<Integer, Integer> deliveryStatus-quantity
     */
    public Map<Integer, Integer> queryDeliveryOrderQuantity(List<TmsDeliveryStatusDescEnum> statusList) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(statusList)) {
            return Collections.emptyMap();
        }
        IdentityInfo identityInfo = ApiMethodParamThreadLocal.getIdentityInfo();
        List<Long> storeIdList = identityInfo.getStoreIdList();
        long tenantId = identityInfo.getUser().getTenantId();
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(storeIdList) || storeIdList.size() > 1) {
            log.error("骑手订单数量查询只支持单门店模式：storeIdList={}", storeIdList);
            return Collections.emptyMap();
        }
        long operatorAccountId = identityInfo.getUser().getAccountId();

        QueryDeliveryQuantityRequest request = new QueryDeliveryQuantityRequest();
        request.setTenantId(tenantId);
        request.setStoreId(storeIdList.get(0));
        request.setDeliveryStatusList(statusList.stream().map(TmsDeliveryStatusDescEnum::getCode).collect(Collectors.toList()));
        request.setRiderAccountId(operatorAccountId);
        try {
            log.info("RiderDeliveryRemoteService call RiderQueryThriftService-queryDeliveryQuantity begin. request:{}", request);
            QueryDeliveryQuantityResponse response = riderQueryThriftService.queryDeliveryQuantity(request);
            log.info("RiderDeliveryRemoteService call RiderQueryThriftService-queryDeliveryQuantity end. request:{}, resonse:{}",
                    request, response);
            if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
                log.warn("RiderDeliveryRemoteService call RiderQueryThriftService-queryDeliveryQuantity wrong. request:{}, response:{}",
                        request, response);
                return Collections.emptyMap();
            }
            return response.getDeliveryStatusQuantityMap();
        } catch (Exception e) {
            log.error("RiderDeliveryRemoteService call RiderQueryThriftService-queryDeliveryQuantity error.", e);
            return Collections.emptyMap();
        }
    }

    /**
     * 查询门店生效中的运单数量
     */
    @Degrade(rhinoKey = "RiderDeliveryRemoteService.queryActivateDeliveryOrderCntDetail",
            fallBackMethod = "queryActivateDeliveryOrderCntDetailFallback",
            timeoutInMilliseconds = 2000)
    public Optional<QueryActivateDeliveryOrderCntDetailResponse> queryActivateDeliveryOrderCntDetail() {
        try {
            QueryActivateDeliveryOrderCntDetailRequest request = new QueryActivateDeliveryOrderCntDetailRequest();
            request.setRiderAccountId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getAccountId());
            request.setStoreId(ApiMethodParamThreadLocal.getIdentityInfo().getStoreId());
            request.setTenantId(ApiMethodParamThreadLocal.getIdentityInfo().getUser().getTenantId());
            log.info("RiderDeliveryRemoteService call RiderQueryThriftService-queryActivateDeliveryOrderCntDetail begin. request:{}", request);
            QueryActivateDeliveryOrderCntDetailResponse response = riderQueryThriftService.queryActivateDeliveryOrderCntDetail(request);
            log.info("RiderDeliveryRemoteService call RiderQueryThriftService-queryActivateDeliveryOrderCntDetail end. response: {}", response);
            if (response.getStatus().getCode() != ResponseCodeEnum.SUCCESS.getValue()) {
                Cat.logEvent("PICK_DELIVERY_SPLIT", "QUERY_COUNT_FAIL");
                return Optional.empty();
            }
            return Optional.of(response);
        } catch (Exception e) {
            log.error("RiderDeliveryRemoteService call RiderQueryThriftService-queryActivateDeliveryOrderCntDetail error.", e);
            Cat.logEvent("PICK_DELIVERY_SPLIT", "QUERY_COUNT_ERROR");
            return Optional.empty();
        }
    }

    public Optional<QueryActivateDeliveryOrderCntDetailResponse> queryActivateDeliveryOrderCntDetailFallback() {
        log.error("RiderDeliveryRemoteService.queryActivateDeliveryOrderCntDetailFallback 发生降级");
        Cat.logEvent("PICK_DELIVERY_SPLIT", "QUERY_COUNT_ERROR");
        return Optional.empty();
    }

    /**
     * 查询骑手上报异常
     * @return
     */
    public List<DeliveryExceptionSummaryVO> queryRiderReportException(long tenantId, long storeId,
                                                                      List<Pair<String, Integer>> channelOrderPairList) {
        QueryDeliveryExceptionByChannelOrderRequest request = new QueryDeliveryExceptionByChannelOrderRequest();
        request.setTenantId(tenantId);
        request.setStoreId(storeId);
        request.setChannelOrderInfoList(channelOrderPairList.stream().map(pair ->
                        new QueryDeliveryExceptionByChannelOrderRequest.ChannelOrderInfo(pair.getLeft(), pair.getRight()))
                .collect(Collectors.toList()));
        DeliveryExceptionResponse response = null;
        try {
            response = riderQueryThriftService.queryDeliveryExceptionByChannelOrder(request);
            if (response == null || response.getStatus() == null ||
                    response.getStatus().getCode() != ResultCodeEnum.SUCCESS.getCode()) {
                log.warn("RiderDeliveryRemoteService call riderQueryRpcService.queryRiderReportException error. request:{}, response:{}", request, response);
                return Collections.emptyList();
            }
            return buildDeliveryExceptionSummaryVOList(response.getTRiderDeliveryExceptionList());
        } catch (Exception e) {
            log.warn("RiderDeliveryRemoteService call riderQueryRpcService.queryRiderReportException error. request:{}, response:{}", request, response, e);
            return Collections.emptyList();
        }
    }


    private List<DeliveryExceptionSummaryVO> buildDeliveryExceptionSummaryVOList(List<TRiderDeliveryException> tRiderDeliveryExceptionList) {

        List<DeliveryExceptionInfoVO> deliveryExceptionInfoVOS = tRiderDeliveryExceptionList.stream()
                .map(this::transform2DeliveryExceptionInfoVO)
                .collect(Collectors.toList());

        //Map化 deliveryOrderId --> DeliveryExceptionInfoVOList
        Map<String, List<DeliveryExceptionInfoVO>> deliveryExceptionInfoVOMap = new HashMap<>();
        for (DeliveryExceptionInfoVO deliveryExceptionInfoVO : deliveryExceptionInfoVOS) {
            if (!deliveryExceptionInfoVOMap.containsKey(deliveryExceptionInfoVO.getDeliveryOrderId())) {
                deliveryExceptionInfoVOMap.put(deliveryExceptionInfoVO.getDeliveryOrderId(), new ArrayList<>());
            }

            deliveryExceptionInfoVOMap.get(deliveryExceptionInfoVO.getDeliveryOrderId()).add(deliveryExceptionInfoVO);
        }

        //Map化 deliveryOrderId --> TRiderDeliveryException
        Map<String, TRiderDeliveryException> tRiderDeliveryExceptionMap = tRiderDeliveryExceptionList.stream()
                .collect(Collectors.toMap(tRiderDeliveryException -> tRiderDeliveryException.getDeliveryOrderId().toString(),
                        Function.identity(), (k1, k2) -> k1));


        return transform2DeliveryExceptionSummaryVO(tRiderDeliveryExceptionMap, deliveryExceptionInfoVOMap);
    }

    private List<DeliveryExceptionSummaryVO> transform2DeliveryExceptionSummaryVO(Map<String, TRiderDeliveryException> tRiderDeliveryExceptionMap,
                                                                                  Map<String, List<DeliveryExceptionInfoVO>> exceptionInfoVOMap) {
        return exceptionInfoVOMap.entrySet().stream().map(entry -> {
                    TRiderDeliveryException tRiderDeliveryException = tRiderDeliveryExceptionMap.get(entry.getKey());

                    if (tRiderDeliveryException == null) {
                        return null;
                    }

                    DeliveryExceptionSummaryVO exceptionSummaryVO = new DeliveryExceptionSummaryVO();
                    exceptionSummaryVO.setChannelId(ChannelOrderConvertUtils.convertChannelId(tRiderDeliveryException.getOrderBizType()));
                    exceptionSummaryVO.setChannelOrderId(tRiderDeliveryException.getChannelOrderId().toString());
                    exceptionSummaryVO.setPayTime(tRiderDeliveryException.getPayTime());
                    exceptionSummaryVO.setDaySeq(tRiderDeliveryException.getDaySeq());
                    exceptionSummaryVO.setStoreId(tRiderDeliveryException.getStoreId());
                    exceptionSummaryVO.setDeliveryOrderId(tRiderDeliveryException.getDeliveryOrderId().toString());
                    exceptionSummaryVO.setDeliveryExceptionInfoVOS(entry.getValue());

                    return exceptionSummaryVO;
                }).filter(Objects::nonNull)
                .sorted((o1, o2) -> -(o1.getPayTime().compareTo(o2.getPayTime())))
                .collect(Collectors.toList());
    }

    private DeliveryExceptionInfoVO transform2DeliveryExceptionInfoVO(TRiderDeliveryException tRiderDeliveryException) {

        DeliveryExceptionInfoVO deliveryExceptionInfoVO = new DeliveryExceptionInfoVO();

        deliveryExceptionInfoVO.setExceptionType(tRiderDeliveryException.getExceptionType());
        deliveryExceptionInfoVO.setReportTimeStamp(tRiderDeliveryException.getCreateTime());
        deliveryExceptionInfoVO.setRiderAccountId(tRiderDeliveryException.getRiderAccountId().toString());
        deliveryExceptionInfoVO.setRiderAccountName(tRiderDeliveryException.getRiderAccountName());
        deliveryExceptionInfoVO.setExceptionTypeDesc(tRiderDeliveryException.getExceptionTypeDesc());
        deliveryExceptionInfoVO.setDeliveryOrderId(tRiderDeliveryException.getDeliveryOrderId().toString());

        return deliveryExceptionInfoVO;
    }

}
