package com.sankuai.shangou.qnh.orderapi.domain.vo.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 聚合配送渠道配送节点
 * @Author: zhangjian155
 * @Date: 2022/10/11 15:20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeliveryChannelConfigVo {
    @FieldDoc(
            description = "渠道名称"
    )
    @ApiModelProperty(value = "渠道名称")
    private Integer channelType;

    @FieldDoc(
            description = "配送发单节点"
    )
    @ApiModelProperty(value = "配送发单节点")
    private Integer deliveryLaunchPoint;

    @FieldDoc(
            description = "配送平台设置"
    )
    @ApiModelProperty(value = "配送平台设置")
    private DeliveryPlatformConfigVo deliveryPlatformConfigVo;
}
