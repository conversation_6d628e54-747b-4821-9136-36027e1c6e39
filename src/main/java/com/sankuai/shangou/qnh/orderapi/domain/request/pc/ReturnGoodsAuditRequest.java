package com.sankuai.shangou.qnh.orderapi.domain.request.pc;


import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.request.BaseRequest;
import lombok.*;

@Setter
@Getter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(
        description = "PC返货审核"
)
public class ReturnGoodsAuditRequest extends PageRequest implements BaseRequest {

    /**
     * 渠道orderId
     */
    private String viewOrderId;

    /**
     * 渠道Id
     */
    private Integer channelId;

    /**
     * 租户Id
     */
    private Long tenantId;

    /**
     * 渠道退款申请唯一ID
     */
    private String afterSaleId;

    /**
     * 审核结果 0拒绝 1审核通过
     */
    private Integer auditResult;

    /**
     * 拒绝code
     */
    private Integer rejectReasonCode;

    /**
     * 拒绝原因
     */
    private String rejectOtherReason;

}
