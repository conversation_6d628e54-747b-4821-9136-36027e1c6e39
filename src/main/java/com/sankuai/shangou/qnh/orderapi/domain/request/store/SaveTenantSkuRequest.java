package com.sankuai.shangou.qnh.orderapi.domain.request.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.store.TenantSku;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/6/12
 * desc:
 */
@TypeDoc(
        description = "编辑租户商品请求"
)
@Data
@ApiModel("编辑租户商品请求")
public class SaveTenantSkuRequest {

    @FieldDoc(
            description = "租户商品信息", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "租户商品信息", required = true)
    @NotNull
    private TenantSku tenantSku;

    @FieldDoc(
            description = "分渠道门店分类信息 channelId -> storeCategoryId", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "分渠道门店分类信息")
    private Map<Integer, String> channelStoreCategoryMap;

    @FieldDoc(
            description = "业务类型 1-商超 2-买菜 默认为1", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "业务类型")
    private Integer bizType;

    @FieldDoc(
            description = "门店ID", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店ID")
    private Long storeId;
}
