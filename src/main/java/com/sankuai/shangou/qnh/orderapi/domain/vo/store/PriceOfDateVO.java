package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.saas.crm.data.client.dto.price.PriceOfDateDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;

@TypeDoc(
        description = "日期价格对象",
        authors = "hejunliang"
)
@ApiModel("门店商品价格趋势")
@Data
@NoArgsConstructor
public class PriceOfDateVO {

    @FieldDoc(
            description = "数据日期"
    )
    @ApiModelProperty(name = "数据日期")
    private String date;

    @FieldDoc(
            description = "价格"
    )
    @ApiModelProperty(name = "价格")
    private String value;

    public static PriceOfDateVO buildPriceOfDateVO(PriceOfDateDTO priceOfDateDTO, boolean isUsePricePer500g) {

        if (priceOfDateDTO == null) {
            return null;
        }

        Long priceValue;
        if (isUsePricePer500g) {
            priceValue = priceOfDateDTO.getPricePer500g();
        } else {
            priceValue = priceOfDateDTO.getPrice();
        }

        PriceOfDateVO priceOfDateVO = new PriceOfDateVO();
        priceOfDateVO.setDate(priceOfDateDTO.getDate());

        if (priceValue != null) {
            BigDecimal priceByYuan = BigDecimal.valueOf(priceValue).divide(BigDecimal.valueOf(100), 2,  RoundingMode.DOWN);
            priceOfDateVO.setValue(String.valueOf(priceByYuan));
        }

        return priceOfDateVO;
    }
}
