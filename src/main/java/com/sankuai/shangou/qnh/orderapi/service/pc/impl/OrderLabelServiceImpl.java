package com.sankuai.shangou.qnh.orderapi.service.pc.impl;

import com.google.common.collect.Lists;
import com.meituan.shangou.saas.order.platform.client.dto.model.OrderLabelModel;
import com.meituan.shangou.saas.order.platform.client.utils.StreamUtil;
import com.meituan.shangou.saas.order.platform.enums.LabelSourceEnum;
import com.meituan.shangou.saas.order.platform.enums.LabelTypeEnum;
import com.sankuai.shangou.qnh.orderapi.remote.OrderLabelRemoteService;
import com.sankuai.shangou.qnh.orderapi.service.pc.OrderLabelService;
import com.sankuai.shangou.qnh.orderapi.utils.pc.MccConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OrderLabelServiceImpl implements OrderLabelService {

    @Autowired
    private OrderLabelRemoteService orderLabelRemoteService;

    @Override
    public List<OrderLabelModel> queryCommonOrderShowLabel(Long tenantId) {

        //租户开启了开关，则租户下全部门店都可以展示标签
        if (!MccConfigUtil.orderSortTagSwitch(tenantId, null)) {
            return new ArrayList<>();
        }

        try {
            List<OrderLabelModel> orderLabelModelList = orderLabelRemoteService.queryTenantLabel(tenantId, Lists.newArrayList(LabelSourceEnum.COMMON.getValue()),
                    Lists.newArrayList(LabelTypeEnum.MAIN_ORDER.getValue()), false);
            if (CollectionUtils.isEmpty(orderLabelModelList)) {
                return new ArrayList<>();
            }
            return StreamUtil.filterList(orderLabelModelList, OrderLabelModel::getIsShow);
        } catch (Exception e) {
            log.error("OrderLabelService.queryCommonOrderShowLabel error", e);
            return new ArrayList<>();
        }
    }
}
