package com.sankuai.shangou.qnh.orderapi.domain.request.app;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 全部订单列表分页查询请求.
 *
 * <AUTHOR>
 * @since 2021/7/2 11:16
 */
@TypeDoc(
        description = "全部订单列表分页查询请求"
)
@ApiModel("全部订单列表分页查询请求")
@Data
public class ChangedOrderListForAppLocalCacheRequest {

    @FieldDoc(
            description = "门店 ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "门店 ID")
    @NotNull
    private Long storeId;

    @FieldDoc(
            description = "下单截止时间，可以为空或0", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "变化开始时间")
    private Long changeStartTime;

    @FieldDoc(
            description = "channelId", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "channelId")
    private Integer channelId;

    @FieldDoc(
            description = "渠道订单ID", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "渠道订单ID")
    private String channelOrderId;


    @FieldDoc(
            description = "每页行数", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "每页行数", required = true)
    @NotNull
    private Integer pageSize;

    @FieldDoc(
            description = "页码", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(name = "页码", required = true)
    private Integer pageNo;


    @FieldDoc(
            description = "门店类型 3-门店 5-中心仓 6-前置仓", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "门店类型")
    private Integer entityType;



    public String validate() {
        if (storeId == null) {
            return "门店ID和前置仓ID不能都为空";
        }
        return null;
    }
}
