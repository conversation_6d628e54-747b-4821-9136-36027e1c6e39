package com.sankuai.shangou.qnh.orderapi.converter.app;

import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import javafx.util.Pair;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;

import static com.sankuai.shangou.qnh.orderapi.constant.app.DeliveryTypeModeConstants.*;

public class DeliveryTypeModeConverter {

    /**
     * 根据订单的配送类型获取对应的配送模式
     *
     * @param originalDistributeType 平台配送类型
     * @param deliveryChannelId 自配送类型
     * @return
     */
    public static Integer getDeliveryTypeMode(Integer originalDistributeType, Integer deliveryChannelId) {
        // 自配送类型为空时，根据自配送类型区分聚合配送还是平台配送
        if (Objects.nonNull(deliveryChannelId)) {
            // 判断是否为自配送
            if (MccConfigUtil.getSelfDeliveryChannelIdConfig().contains(deliveryChannelId)) {
                return SELF_DELIVERY;
            }
            // 判断是否为聚合配送
            if (MccConfigUtil.getAggregateDeliveryChannelIdConfig().contains(deliveryChannelId)) {
                return AGGREGATE_DELIVERY;
            }
        }
        // 当自配送类型为空，平台配送类型不为空时，则为平台配送
        if (Objects.isNull(deliveryChannelId) && Objects.nonNull(originalDistributeType)
                && MccConfigUtil.getPlatformDeliveryOriginalDistributeTypeConfig().contains(originalDistributeType)) {
            return PLATFORM_DELIVERY;
        }
        return null;
    }

    /**
     * 根据配送模式获取对应的配送参数
     *
     * @param deliveryTypeModeList
     * @return key为平台配送类型(originalDistributeType参数)，value为自配送类型（deliveryChannelId参数）
     */
    public static Pair<List<Integer>, List<Integer>> getDeliveryParam(List<Integer> deliveryTypeModeList) {
        Pair<List<Integer>, List<Integer>> pair = new Pair<>(new ArrayList<>(), new ArrayList<>());
        if (CollectionUtils.isEmpty(deliveryTypeModeList)) {
            return pair;
        }
        Set<Integer> deliveryTypeSet = new HashSet<>(deliveryTypeModeList);
        //传入了所有的配送模式，则不需要传入配送类型参数查询
        if (deliveryTypeSet.contains(PLATFORM_DELIVERY) && deliveryTypeSet.contains(AGGREGATE_DELIVERY)
                && deliveryTypeSet.contains(SELF_DELIVERY)) {
            return pair;
        }
        if (deliveryTypeSet.contains(PLATFORM_DELIVERY)) {
            pair.getKey().addAll(MccConfigUtil.getPlatformDeliveryOriginalDistributeTypeConfig());
        }
        //商家自配送和聚合配送都是用deliveryChannelId参数查询
        if (deliveryTypeSet.contains(AGGREGATE_DELIVERY)) {
            pair.getValue().addAll(MccConfigUtil.getAggregateDeliveryChannelIdConfig());
        }
        if (deliveryTypeSet.contains(SELF_DELIVERY)) {
            pair.getValue().addAll(MccConfigUtil.getSelfDeliveryChannelIdConfig());
        }
        return pair;
    }
}
