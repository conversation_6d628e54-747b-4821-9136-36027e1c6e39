package com.sankuai.shangou.qnh.orderapi.domain.vo.app;


import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.qnh.orderapi.annotation.app.BindAuthCode;
import com.sankuai.shangou.qnh.orderapi.constant.app.IntegerBooleanConstants;
import com.sankuai.shangou.qnh.orderapi.enums.app.AuthCodeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @createTime 2019/11/19
 * @description
 */
@TypeDoc(
        description = "订单首页模块"
)
@Data
@ApiModel("订单首页模块")
public class OrderHomePageModuleVO {

    @FieldDoc(
            description = "是否展示待接单tab (0:否, 1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示待接单tab (0:否, 1:是)", required = true)
    @NotNull
//    @BindAuthCode(AuthCodeEnum.ORDER_WAIT_TO_CONFIRM)
    private Integer showWaitToTakeOrderTab;

    @FieldDoc(
            description = "是否展示待拣货tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示待拣货tab (0:否，1:是)", required = true)
    @NotNull
    @BindAuthCode(AuthCodeEnum.ORDER_WAIT_TO_PICK)
    private Integer showWaitToPickTab;

    @FieldDoc(
            description = "是否展示待配送tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示待配送tab (0:否，1:是)", required = true)
    @NotNull
    @BindAuthCode(AuthCodeEnum.ORDER_WAIT_TO_DELIVERY)
    private Integer showWaitToDeliveryTab;

    @FieldDoc(
            description = "是否展示配送异常tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示配送异常tab (0:否，1:是)", required = true)
    @NotNull
    @BindAuthCode(AuthCodeEnum.ORDER_DELIVERY_ERROR)
    private Integer showDeliveryErrorTab;

    @FieldDoc(
            description = "是否展示退款tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示退款tab (0:否，1:是)", required = true)
    @NotNull
    @BindAuthCode(AuthCodeEnum.ORDER_REFUND_AUDIT)
    private Integer showWaitToAuditRefundTab;

    @FieldDoc(
            description = "是否展示售后tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示售后tab (0:否，1:是)", required = true)
    @NotNull
    @BindAuthCode(AuthCodeEnum.ORDER_TAB_AFTER_SALES)
    private Integer showWaitToAuditAfterSaleTab;

    @FieldDoc(
            description = "是否展示订单tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示订单tab (0:否，1:是)", required = true)
    @NotNull
    @BindAuthCode(AuthCodeEnum.ORDER_SUB_TAB)
    private Integer showOrderTab;

    @FieldDoc(
            description = "是否展示拣货tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示拣货tab (0:否，1:是)", required = true)
    @NotNull
    @BindAuthCode(AuthCodeEnum.PICK_SUB_TAB)
    private Integer showPickTab;

    @FieldDoc(
            description = "是否展示订单查询 (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示订单查询 (0:否，1:是)", required = true)
    @NotNull
    @BindAuthCode(AuthCodeEnum.ORDER_SEARCH)
    private Integer showOrderSearch;

    /**
     * 骑手tab start
     */
    @FieldDoc(
            description = "是否展示骑手领取订单页面，即新配送任务页面 (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示骑手领取订单页面，即新配送任务页面 (0:否，1:是)", required = true)
    @NotNull
    @BindAuthCode(AuthCodeEnum.SELF_RIDER_WAIT_TO_GET)
    private Integer showRiderGetOrderTab;

    @FieldDoc(
            description = "是否展示骑手待取货页面 (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示骑手待取货页面 (0:否，1:是)", required = true)
    @NotNull
    @BindAuthCode(AuthCodeEnum.SELF_RIDER_WAIT_TO_TAKEGOODS)
    private Integer showRiderTakeGoodsTab;

    @FieldDoc(
            description = "是否展示骑手配送中页面 (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示骑手配送中页面 (0:否，1:是)", required = true)
    @NotNull
    @BindAuthCode(AuthCodeEnum.SELF_RIDER_IN_DELIVERY)
    private Integer showRiderInDeliveryTab;

    @FieldDoc(
            description = "是否展示骑手已完成页面 (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示骑手已完成页面 (0:否，1:是)", required = true)
    @NotNull
    @BindAuthCode(AuthCodeEnum.SELF_RIDER_COMPLETED)
    private Integer showRiderCompletedTab;

    @FieldDoc(
            description = "是否展示歪马异常TAB (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示歪马异常TAB (0:否，1:是)", required = true)
    @NotNull
    @BindAuthCode(AuthCodeEnum.DH_EXCEPTION)
    private Integer showDhExceptionTab;

    @FieldDoc(
            description = "是否展示新任务-仅拣货tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示新任务-仅拣货tab(0:否，1:是)", required = true)
    private Integer showNewJustPickTaskTab;

    @FieldDoc(
            description = "是否展示新任务-仅配送tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示新任务-仅配送tab (0:否，1:是)", required = true)
    private Integer showNewJustDeliveryTaskTab;

    @FieldDoc(
            description = "是否展示新任务-拣配任务tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示新任务-拣配任务tab (0:否，1:是)", required = true)
    private Integer showNewPickDeliveryTaskTab;

    @FieldDoc(
            description = "是否展示待取货-拣货任务tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示待取货-拣货任务tab (0:否，1:是)", required = true)
    private Integer showWaitTakePickTaskTab;

    @FieldDoc(
            description = "是否展示待取货-配送任务tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示待取货-配送任务tab (0:否，1:是)", required = true)
    private Integer showWaitTakeDeliveryTaskTab;

    @FieldDoc(
            description = "是否展示已完成-已拣货tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示已完成-已拣货tab (0:否，1:是)", required = true)
    private Integer showCompletedPickTaskTab;

    @FieldDoc(
            description = "是否展示已完成-已拣货tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示已完成-已拣货tab (0:否，1:是)", required = true)
    private Integer showCompletedDeliveryTaskTab;

    /**
     * 骑手tab end
     */


    @FieldDoc(
            description = "是否将规格信息放到订单信息中(0:不展示,1:展示)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否将规格信息放到订单信息中(0:不展示,1:展示)", required = true)
    @NotNull
    private Integer appendSpecToOrderItem;

    @FieldDoc(
            description = "是否展示待自提tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示待自提tab (0:否，1:是)", required = true)
    @NotNull
    @BindAuthCode(AuthCodeEnum.WAIT_TO_SELF_FETCH)
    private Integer showWaitToSelfFetchTab;

    @FieldDoc(
            description = "是否展示评价tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示评价tab (0:否，1:是)")
    @NotNull
    private Integer showCommitManagementTab;

    @FieldDoc(
            description = "是否展示推广自提tab (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示推广自提tab (0:否，1:是)", required = true)
    @NotNull
    @BindAuthCode(AuthCodeEnum.SELF_RIDER_SELF_PICK_PROMOTE_ORDER)
    private Integer showSelfPickPromoteOrder;

    @FieldDoc(
            description = "是否展示拣配分离错误页面 (0:否，1:是)", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "是否展示拣配分离错误页面 (0:否，1:是)", required = true)
    private Integer showNewTaskSplitErrorHint;

    public static OrderHomePageModuleVO homePageInit(){
        OrderHomePageModuleVO orderHomePageModuleVO = new OrderHomePageModuleVO();
        orderHomePageModuleVO.setShowWaitToTakeOrderTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowWaitToPickTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowWaitToSelfFetchTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowWaitToDeliveryTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowDeliveryErrorTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowWaitToAuditRefundTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowWaitToAuditAfterSaleTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowOrderTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowPickTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowRiderGetOrderTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowRiderTakeGoodsTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowRiderInDeliveryTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowRiderCompletedTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        //全部订单、部分订单、评价默认无权限
        orderHomePageModuleVO.setShowOrderSearch(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowOrderTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowCommitManagementTab(IntegerBooleanConstants.BOOLEAN_FALSE);


        return orderHomePageModuleVO;
    }

    public static OrderHomePageModuleVO appHomePageInit(){
        OrderHomePageModuleVO orderHomePageModuleVO = new OrderHomePageModuleVO();
        orderHomePageModuleVO.setShowWaitToTakeOrderTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowWaitToPickTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowWaitToSelfFetchTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowWaitToDeliveryTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowDeliveryErrorTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowWaitToAuditRefundTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowWaitToAuditAfterSaleTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowOrderTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowPickTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowOrderSearch(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowSelfPickPromoteOrder(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowDhExceptionTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        // 骑手 tab 页配置 start
        orderHomePageModuleVO.setShowRiderGetOrderTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowRiderTakeGoodsTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowRiderInDeliveryTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowRiderCompletedTab(IntegerBooleanConstants.BOOLEAN_FALSE);


        return orderHomePageModuleVO;
    }

    public static OrderHomePageModuleVO homePageInit4App() {
        OrderHomePageModuleVO orderHomePageModuleVO = new OrderHomePageModuleVO();
        orderHomePageModuleVO.setShowWaitToTakeOrderTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowWaitToPickTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowWaitToSelfFetchTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowWaitToDeliveryTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowDeliveryErrorTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowWaitToAuditRefundTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowWaitToAuditAfterSaleTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowOrderTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowPickTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowOrderSearch(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowSelfPickPromoteOrder(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowDhExceptionTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        // 骑手 tab 页配置 start
        orderHomePageModuleVO.setShowRiderGetOrderTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowRiderTakeGoodsTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowRiderInDeliveryTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        orderHomePageModuleVO.setShowRiderCompletedTab(IntegerBooleanConstants.BOOLEAN_FALSE);
        // 骑手 tab 页配置 end
        return orderHomePageModuleVO;
    }

}
