package com.sankuai.shangou.qnh.orderapi.service.pc;

import com.sankuai.meituan.shangou.empower.ocms.thrift.enums.TaskItemErrorType;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFPalette;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellUtil;

import java.util.Arrays;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2019/1/28 09:34
 * @Description:
 */
public abstract class BaseExcelExporterService {


    protected static List<TaskItemErrorType> SKU_ERRORTYPES = Arrays.asList(TaskItemErrorType.SKU_NOT_EXIST
            ,TaskItemErrorType.SKU_FIELD_LACK,TaskItemErrorType.SKU_CATEGORY_NOT_EXIST,TaskItemErrorType.SKU_REPETITION_DATA
            , TaskItemErrorType.UPC_NOT_EXISTS, TaskItemErrorType.FRONT_CATEGORY_HAS_CHILDREN, TaskItemErrorType.SKU_ONLINE_FAILED
            , TaskItemErrorType.SKU_PARAMS_ERROR);


    public static final short DEFAULT_TITLE_LINE_HEIGHT = 600;

    public static final int DEFAULT_COL_WIDTH = 14;

    public static final short DEFAULT_ROW_HEIGHT = 300;


    /**
     * 创建标题行
     * @param workbook
     * @param headRow
     * @param heads
     * @param errorHead
     */
    protected void createHeadRow(HSSFWorkbook workbook, HSSFRow headRow, String[] heads, String errorHead) {

        headRow.setHeight(DEFAULT_ROW_HEIGHT);
        HSSFCellStyle headStyle = createHeadStyle(workbook);

        for (int i=0;i<heads.length;i++) {
            HSSFCell headCell = headRow.createCell(i);
            headCell.setCellValue(heads[i]);
            headCell.setCellStyle(headStyle);
        }

        HSSFCell errMsgCell = headRow.createCell(heads.length);
        errMsgCell.setCellValue(errorHead);

        HSSFCellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);

        errMsgCell.setCellStyle(style);
    }


    protected HSSFCellStyle createTitleStyle(HSSFWorkbook workbook) {
        HSSFCellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        HSSFFont bold = workbook.createFont();
        bold.setBold(true);
        bold.setFontHeightInPoints((short)14);

        style.setFont(bold);

        return style;
    }


    /**
     * 创建错误的单元格样式
     * @param workbook
     * @return
     */
    protected HSSFCellStyle createErrorStyle(HSSFWorkbook workbook) {
        HSSFCellStyle errorStyle = workbook.createCellStyle();
        HSSFFont errorFont = workbook.createFont();
        errorFont.setColor(HSSFColor.HSSFColorPredefined.RED.getIndex());
        errorStyle.setFont(errorFont);

        return errorStyle;
    }



    /**
     * 创建表头样式
     * @param workbook
     * @return
     */
    public static HSSFCellStyle createHeadStyle(HSSFWorkbook workbook) {
        HSSFPalette customPalette = workbook.getCustomPalette();
        customPalette.setColorAtIndex(HSSFColor.HSSFColorPredefined.LIME.getIndex() ,(byte)201, (byte)223, (byte)241);
        HSSFCellStyle headStyle = workbook.createCellStyle();
        headStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headStyle.setFillForegroundColor(HSSFColor.HSSFColorPredefined.LIME.getIndex());
        headStyle.setAlignment(HorizontalAlignment.CENTER);
        headStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headStyle.setBorderTop(BorderStyle.THIN);
        headStyle.setBorderBottom(BorderStyle.THIN);
        headStyle.setBorderLeft(BorderStyle.THIN);
        headStyle.setBorderRight(BorderStyle.THIN);
        HSSFFont headFont = workbook.createFont();
        headFont.setBold(true);
        headFont.setFontHeightInPoints((short)10);
        headStyle.setFont(headFont);

        return headStyle;
    }


    public HSSFSheet createSheetWithSpecHead(HSSFWorkbook wb, String sheetName, String headContent, List<String> colNameList, HSSFCellStyle headStyle){
        HSSFSheet sheet = wb.createSheet(sheetName);

        int rowStart = 0;
        //如果有特殊表头要求，新建一行
        if (StringUtils.isNotBlank(headContent)) {
            HSSFRow row1 = sheet.createRow(0);
            HSSFRow row2 = sheet.createRow(1);
            HSSFCell cell = row1.createCell(0);
            cell.setCellValue(headContent);
            sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, colNameList.size() - 1));
            CellUtil.setAlignment(cell, HorizontalAlignment.CENTER);
            CellUtil.setVerticalAlignment(cell, VerticalAlignment.CENTER);
            rowStart += 2;
        }


        HSSFRow row = sheet.createRow(rowStart);

        // 第四步，创建单元格，并设置值表头 设置表头居中
        for(int i=0;i<colNameList.size();i++){
            HSSFCell cell = row.createCell(i);
            cell.setCellValue(colNameList.get(i));
            cell.setCellStyle(headStyle);
        }

        return sheet;
    }

    public void autoSizeColumns(HSSFWorkbook wb, List<Integer> colNumList) {
        for (int index = 0; index < colNumList.size(); index++) {
            HSSFSheet sheet = wb.getSheetAt(index);
            int colNum = colNumList.get(index);
            for (int colIndex = 0; colIndex < colNum; colIndex++) {
                sheet.autoSizeColumn(colIndex);
            }
        }
    }

    public void createContentCellAndFillValue(HSSFRow contentRow, int cellIndex, Object value) {
        HSSFCell contentCell = contentRow.createCell(cellIndex);
        contentCell.setCellValue(value == null ? null : String.valueOf(value));
    }


}
