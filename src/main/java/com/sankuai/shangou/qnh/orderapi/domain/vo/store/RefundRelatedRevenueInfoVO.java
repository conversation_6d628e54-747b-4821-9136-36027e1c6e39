package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @createTime 2020-11-11
 * @description
 */
@TypeDoc(
        description = "退款关联收款信息",
        version = "1.0"
)
@Data
@ApiModel("退款关联收款信息")
public class RefundRelatedRevenueInfoVO {
    @FieldDoc(
            description = "渠道", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "渠道")
    public Integer channelId;

    @FieldDoc(
            description = "订单号", requiredness = Requiredness.REQUIRED
    )
    @ApiModelProperty(value = "订单号")
    private String viewOrderId;

    @FieldDoc(
            description = "摊位id", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "摊位id")
    private Long boothId;

    @FieldDoc(
            description = "分账唯一键", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(value = "分账唯一键")
    private String benefitUnifyCode;
}
