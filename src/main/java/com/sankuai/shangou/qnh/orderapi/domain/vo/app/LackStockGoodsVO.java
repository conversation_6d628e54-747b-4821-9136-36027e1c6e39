package com.sankuai.shangou.qnh.orderapi.domain.vo.app;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@Builder
@NoArgsConstructor
public class LackStockGoodsVO {
    @FieldDoc(description = "货品名称")
    private String goodsName;
    @FieldDoc(description = "货品SKU编号")
    private String skuId;
    @FieldDoc(description = "货品实拍图")
    private String picUrl;
    @FieldDoc(description = "货品规格")
    private String spec;
    @FieldDoc(description = "货品upc编号列表")
    private List<String> upcList;
    @FieldDoc(description = "要货量")
    private Integer needCount;
    @FieldDoc(description = "剩余可售库存")
    private Integer salableCount;
    @FieldDoc(description = "缺货量")
    private Integer lackCount;
}
