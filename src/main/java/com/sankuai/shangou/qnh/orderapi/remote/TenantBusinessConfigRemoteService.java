package com.sankuai.shangou.qnh.orderapi.remote;

import com.dianping.rhino.annotation.Rhino;
import com.dianping.zebra.util.StringUtils;
import com.meituan.shangou.saas.tenant.thrift.BusinessConfigThriftService;
import com.meituan.shangou.saas.tenant.thrift.common.StatusCodeEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.AcceptOrderModeEnum;
import com.meituan.shangou.saas.tenant.thrift.dto.config.response.AcceptOrderModeQueryResponse;
import com.sankuai.meituan.shangou.saas.common.method.MethodLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @program: qnh_order_api
 * @description:
 * @author: jinyi
 * @create: 2023-10-23 19:07
 **/
@Component
@Slf4j
public class TenantBusinessConfigRemoteService {

    @Autowired
    private BusinessConfigThriftService businessConfigThriftService;


    /**
     * 查询门店是否是手动接单
     * @param tenantId
     * @param shopId
     * @return
     */
    @MethodLog(logRequest = true, logResponse = true)
    public boolean isManualTakeOrder(Long tenantId, Long shopId) {

        String acceptOrderMode = queryAcceptOrderMode(tenantId, shopId);
        if (StringUtils.isBlank(acceptOrderMode)) {
            return isManualTakeOrderFallback(tenantId, shopId);
        } else {
            return AcceptOrderModeEnum.MANUAL.getKey().equals(acceptOrderMode);
        }
    }


    private String queryAcceptOrderMode(Long tenantId, Long shopId) {
        try {
            AcceptOrderModeQueryResponse acceptOrderModeQueryResponse = businessConfigThriftService.queryAcceptOrderMode(tenantId, shopId);
            log.info("查询门店自动接单配置 tenantId={}, shopId={}, acceptOrderModeQueryResponse={}", tenantId, shopId, acceptOrderModeQueryResponse);
            if (acceptOrderModeQueryResponse != null
                    && acceptOrderModeQueryResponse.getStatus() != null
                    && acceptOrderModeQueryResponse.getStatus().getCode() != null
                    && StatusCodeEnum.SUCCESS.getCode() == acceptOrderModeQueryResponse.getStatus().getCode()) {
                // 非自动接单&非，视为手动接单，防止自动接单的定义过于宽泛，所以采取安全的判断方式,手动接单的错判比自动接单的错判影响更小
                String acceptOrderMode = acceptOrderModeQueryResponse.getAcceptOrderMode();
                return acceptOrderMode;
            } else {
                // 非正常返回, 返回空值
                log.error("查询门店自动接单配置异常,请求参数,tenantId:{}, shopId:{},response:{}", tenantId, shopId, acceptOrderModeQueryResponse);
                return "";
            }
        } catch (TException ex) {
            log.error("businessConfigThriftService.queryAcceptOrderMode TException", ex);
            // 发生调用异常，返回空值
            return "";
        }
    }

    /**
     * 查询门店是否是手动接单降级方法,默认都是手动接单，可以给商家展示待接单
     * @param tenantId
     * @param shopId
     * @return
     */
    private boolean isManualTakeOrderFallback(Long tenantId, Long shopId) {
        return true;
    }

}
