package com.sankuai.shangou.qnh.orderapi.domain.vo.store;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.meituan.shangou.empower.ocms.client.product.dto.ChannelCategoryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Title: ChannelCategoryVO
 * @Description: 渠道类目信息
 * @Author: zhaolei12
 * @Date: 2020/5/16 5:57 下午
 */
@TypeDoc(
        description = "渠道类目信息"
)
@Data
@ApiModel("渠道类目信息")
public class ChannelCategoryWithPathVO {
    @FieldDoc(
            description = "渠道编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道编码")
    private Integer channelId;

    @FieldDoc(
            description = "渠道类目编码", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道类目编码")
    private String channelCategoryCode;

    @FieldDoc(
            description = "渠道类目名称", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道类目名称")
    private String channelCategoryName;

    @FieldDoc(
            description = "渠道类目编码全路径", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道类目编码全路径")
    private String channelCategoryCodePath;

    @FieldDoc(
            description = "渠道类目名称全路径", requiredness = Requiredness.OPTIONAL
    )
    @ApiModelProperty(name = "渠道类目名称全路径")
    private String channelCategoryNamePath;

}
