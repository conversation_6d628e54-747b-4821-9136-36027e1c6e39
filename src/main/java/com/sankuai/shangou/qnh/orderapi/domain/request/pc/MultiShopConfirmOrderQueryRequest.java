package com.sankuai.shangou.qnh.orderapi.domain.request.pc;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2022/7/14 16:22
 * @Description:
 */
@Data
@NoArgsConstructor
public class MultiShopConfirmOrderQueryRequest extends PageRequest {

    private String poiName;

    private List<Long> poiIdList;

    private List<String> channelIds;

    private String confirmStatus;

    private String pickStatus;

    private String orderType;

    /**
     * 渠道流水号
     */
    private String orderSerialNumber;

    /**
     * 前置仓id
     */
    private List<Long> warehouseIdList;

    private String clientType;

    /**
     * 订单标识列表
     */
    public List<Integer> orderMarks;

}
