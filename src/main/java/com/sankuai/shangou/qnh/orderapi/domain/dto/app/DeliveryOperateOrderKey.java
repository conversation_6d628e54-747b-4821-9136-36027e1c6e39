package com.sankuai.shangou.qnh.orderapi.domain.dto.app;

import com.meituan.shangou.saas.order.platform.enums.OrderStatusEnum;
import com.sankuai.meituan.shangou.empower.tms.delivery.client.enums.DeliveryPlatformEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023-09-08
 * @email <EMAIL>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(of = {"orderId"})
public class DeliveryOperateOrderKey {

    private long orderId;

    private int actualPayAmt;

    private DeliveryPlatformEnum deliveryPlatformEnum;

    private OrderStatusEnum orderStatusEnum;

    private String scene;

    private Boolean isSealOrder;
}
