package com.sankuai.shangou.qnh.orderapi.domain.response.pc;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.AggrPlatformDeliveryConfigVO;
import com.sankuai.shangou.qnh.orderapi.domain.vo.pc.StoreLaunchDeliveryConfigVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020/12/30
 */
@Data
public class StoreDeliveryConfigResponse {


    @FieldDoc(
            description = "门店开通的渠道信息"
    )
    @ApiModelProperty(value = "门店开通的渠道信息")
    private List<AggrPlatformDeliveryConfigVO> aggrPlatformDeliveryConfig;

    @FieldDoc(
            description = "门店发配送规则"
    )
    @ApiModelProperty(value = "门店发配送规则")
    private StoreLaunchDeliveryConfigVO storeLaunchDeliveryConfig;

    @FieldDoc(
            description = "门店名称"
    )
    @ApiModelProperty(value = "门店名称")
    private String storeName;

}
