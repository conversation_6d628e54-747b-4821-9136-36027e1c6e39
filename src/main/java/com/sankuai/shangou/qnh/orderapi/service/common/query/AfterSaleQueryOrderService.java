package com.sankuai.shangou.qnh.orderapi.service.common.query;


import com.google.common.collect.Maps;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OCMSListWaitAuditOrderRequest;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OCMSListWaitAuditOrderRevenueDetailRequest;
import com.meituan.shangou.saas.order.management.client.dto.request.online.OcmsWaitAuditOrderCountRequest;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OCMSListWaitAuditOrderResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.online.OcmsWaitToRefundGoodsOrderSubTypeCountResponse;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSOrderVO;
import com.meituan.shangou.saas.order.management.client.dto.response.online.vo.OCMSWaitAuditOrderVO;
import com.meituan.shangou.saas.order.management.client.enums.SortByEnum;
import com.meituan.shangou.saas.order.management.client.enums.WaitToAuditRefundGoodsOrderSubTypeEnum;
import com.meituan.shangou.saas.order.management.client.service.online.OCMSQueryThriftService;
import com.meituan.shangou.saas.order.management.client.service.revenue.MerChantRevenueQueryService;
import com.meituan.shangou.saas.order.platform.client.dto.model.OrderLabelModel;
import com.meituan.shangou.saas.order.platform.enums.AfterSaleApplyStatusEnum;
import com.meituan.shangou.saas.order.platform.enums.AfterSaleTypeEnum;
import com.meituan.shangou.saas.tenant.thrift.common.enums.PoiEntityTypeEnum;
import com.sankuai.meituan.shangou.saas.common.exception.BizException;
import com.sankuai.shangou.qnh.orderapi.domain.response.app.RefundApplyListResponse;
import com.sankuai.shangou.qnh.orderapi.domain.vo.app.PageInfoVO;
import com.sankuai.shangou.qnh.orderapi.enums.app.ResultCodeEnum;
import com.sankuai.shangou.qnh.orderapi.service.common.OrderListRequestContext;
import com.sankuai.shangou.qnh.orderapi.service.common.PageUtil;
import com.sankuai.shangou.qnh.orderapi.utils.app.MccConfigUtil;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <AUTHOR>
 * @since 2024/7/17
 **/
@Service
@Slf4j
public class AfterSaleQueryOrderService extends QueryOrderService {

    @Resource
    private MerChantRevenueQueryService merChantRevenueQueryService;

    @Resource
    private OCMSQueryThriftService ocmsQueryThriftService;

    @Override
    public Pair<List<OCMSOrderVO>, PageInfoVO> queryOrderInfo(OrderListRequestContext request) {
        throw new UnsupportedOperationException();
    }

    @Override
    public RefundApplyListResponse queryAfterSaleOrderList(OrderListRequestContext request) {
        initOrderListRequestContext(request);
        checkOrderListRequest(request);
        Pair<List<OCMSWaitAuditOrderVO>, PageInfoVO> listPageInfoVOPair = queryAfterSaleOrderInfo(request);
        List<OrderLabelModel> showLabelList = queryOrderShowLabel(request.getTenantId());
        return buildRefundApplyListResponse(request, listPageInfoVOPair, showLabelList);
    }

    private Pair<List<OCMSWaitAuditOrderVO>, PageInfoVO> queryAfterSaleOrderInfo(OrderListRequestContext request) {
        // 对数据进行包装
        OCMSListWaitAuditOrderRevenueDetailRequest ocmsListWaitAuditOrderRequest = buildOCMSListWaitAuditOrderRequest(request);
        log.info("OrderService.queryAfterSaleOrderList  调用merChantRevenueQueryService.listWaitAuditOrderRevenueDetail request:{}", ocmsListWaitAuditOrderRequest);
        OCMSListWaitAuditOrderResponse response = merChantRevenueQueryService.listWaitAuditOrderRevenueDetail(ocmsListWaitAuditOrderRequest);
        log.info("OrderService.queryAfterSaleOrderList  调用merChantRevenueQueryService.listWaitAuditOrderRevenueDetail response:{}", response);
        if (response.getStatus().getCode() != com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum.SUCCESS.getValue()) {
            throw new BizException(ResultCodeEnum.FAIL.getCode(), response.getStatus().getMessage());
        }
        return new Pair<>(response.getOcmsWaitAuditOrderVOList(), PageUtil.buildPageInfoVO(request.getPage(), request.getSize(), response.getTotalCount()));
    }

    private OCMSListWaitAuditOrderRevenueDetailRequest buildOCMSListWaitAuditOrderRequest(OrderListRequestContext request) {
        OCMSListWaitAuditOrderRevenueDetailRequest ocmsListWaitAuditOrderRequest = new OCMSListWaitAuditOrderRevenueDetailRequest();
        ocmsListWaitAuditOrderRequest.setTenantId(request.getTenantId());
        ocmsListWaitAuditOrderRequest.setShopIdList(request.getStoreIdList());
        if (Objects.equals(PoiEntityTypeEnum.SHAREABLE_WAREHOUSE.code(), request.getEntityType())) {
            ocmsListWaitAuditOrderRequest.setShopIdList(Lists.newArrayList());
            ocmsListWaitAuditOrderRequest.setWarehouseIdList(request.getStoreIdList());
        }
        ocmsListWaitAuditOrderRequest.setPage(request.getPage());
        ocmsListWaitAuditOrderRequest.setSize(request.getSize());
        ocmsListWaitAuditOrderRequest.setBeginCreateTime(getAfterSaleQueryStartTime());
        ocmsListWaitAuditOrderRequest.setEndCreateTime(System.currentTimeMillis());
        ocmsListWaitAuditOrderRequest.setSort(SortByEnum.DESC);

        // 目前仅有赞渠道在用申诉中状态，虽然申诉中的单子无需商家审批，但是也需要展示在订单列表页面
        WaitToAuditRefundGoodsOrderSubTypeEnum waitToAuditRefundGoodsOrderSubTypeEnum = WaitToAuditRefundGoodsOrderSubTypeEnum.valueOf(request.getSubType());
        if (Objects.isNull(waitToAuditRefundGoodsOrderSubTypeEnum)){
            throw new IllegalArgumentException("非法子类型参数");
        }
        if(waitToAuditRefundGoodsOrderSubTypeEnum == WaitToAuditRefundGoodsOrderSubTypeEnum.ONLY_REFUND){
            ocmsListWaitAuditOrderRequest.setAfterSaleApplyStatusList(Lists.newArrayList(AfterSaleApplyStatusEnum.COMMIT.getValue(), AfterSaleApplyStatusEnum.WAIT_ASSIGN.getValue(),
                    AfterSaleApplyStatusEnum.APPEAL_ING.getValue()));
        }else if(waitToAuditRefundGoodsOrderSubTypeEnum == WaitToAuditRefundGoodsOrderSubTypeEnum.REJECT_BY_CUSTOMER){
            ocmsListWaitAuditOrderRequest.setAfterSaleApplyStatusList(Lists.newArrayList(AfterSaleApplyStatusEnum.FIRST_AUDITED.getValue(),
                    AfterSaleApplyStatusEnum.APPEAL_ING.getValue()));
        } else if(waitToAuditRefundGoodsOrderSubTypeEnum == WaitToAuditRefundGoodsOrderSubTypeEnum.APPEAL) {
            ocmsListWaitAuditOrderRequest.setAfterSaleApplyStatusList(Lists.newArrayList(AfterSaleApplyStatusEnum.COMMIT.getValue(), AfterSaleApplyStatusEnum.APPEAL_ING.getValue()));
        } else {
            ocmsListWaitAuditOrderRequest.setAfterSaleApplyStatusList(Lists.newArrayList(AfterSaleApplyStatusEnum.COMMIT.getValue(), AfterSaleApplyStatusEnum.WAIT_ASSIGN.getValue(), AfterSaleApplyStatusEnum.FIRST_AUDITED.getValue(), AfterSaleApplyStatusEnum.FIRST_AUTO_AUDITED.getValue(),
                    AfterSaleApplyStatusEnum.APPEAL_ING.getValue()));
        }

        ocmsListWaitAuditOrderRequest.setAfterSaleApplyTypeList(buildAfterSaleApplyTypeList(waitToAuditRefundGoodsOrderSubTypeEnum.getAfterSaleTypeEnumList()));
        ocmsListWaitAuditOrderRequest.setIncludeRevenueDetail(request.isShowSalePrice());
        return ocmsListWaitAuditOrderRequest;
    }

    private List<Integer> buildAfterSaleApplyTypeList(List<AfterSaleTypeEnum> afterSaleTypeEnumList) {
        return Optional.ofNullable(afterSaleTypeEnumList)
                .map(List::stream)
                .orElse(Stream.empty())
                .map(AfterSaleTypeEnum::getValue)
                .collect(Collectors.toList());
    }


    public Map<WaitToAuditRefundGoodsOrderSubTypeEnum, Integer> countByType(Long tenantId, List<Long> storeIdList, Integer entityType){
        OcmsWaitAuditOrderCountRequest ocmsListWaitAuditOrderRequest = buildOCMSListWaitAuditOrderCountRequest(tenantId, storeIdList, entityType);
        log.info("OrderService.queryWaitAuditRefundGoodsBySubtypeCount  调用ocmsQueryThriftService.queryWaitToRefundGoodsBySubtypeCount request:{}", ocmsListWaitAuditOrderRequest);
        OcmsWaitToRefundGoodsOrderSubTypeCountResponse response = ocmsQueryThriftService.queryWaitToRefundGoodsBySubtypeCountV2(ocmsListWaitAuditOrderRequest);
        log.info("OrderService.queryWaitAuditRefundGoodsBySubtypeCount  调用ocmsQueryThriftService.queryWaitToRefundGoodsBySubtypeCount response:{}", response);
        if (response.getStatus().getCode() != com.sankuai.meituan.shangou.empower.auth.thrift.enums.ResultCodeEnum.SUCCESS.getValue()) {
            throw new BizException(ResultCodeEnum.FAIL.getCode(), response.getStatus().getMessage());
        }
        Map<WaitToAuditRefundGoodsOrderSubTypeEnum, Integer> rs = Maps.newHashMap();
        rs.put(WaitToAuditRefundGoodsOrderSubTypeEnum.APPEAL, response.getAppealCount());
        rs.put(WaitToAuditRefundGoodsOrderSubTypeEnum.REJECT_BY_CUSTOMER, response.getRejectByCustomerCount());
        rs.put(WaitToAuditRefundGoodsOrderSubTypeEnum.ONLY_REFUND, response.getOnlyRefundCount());
        rs.put(WaitToAuditRefundGoodsOrderSubTypeEnum.ALL, response.getAllSubTypeCount());
        rs.put(WaitToAuditRefundGoodsOrderSubTypeEnum.RETURN_AND_REFUND, response.getReturnAndRefundCount());
        return rs;
    }


    private OcmsWaitAuditOrderCountRequest buildOCMSListWaitAuditOrderCountRequest(Long tenantId, List<Long> storeIdList, Integer entityType) {
        OcmsWaitAuditOrderCountRequest ocmsListWaitAuditOrderRequest = new OcmsWaitAuditOrderCountRequest();
        ocmsListWaitAuditOrderRequest.setTenantId(tenantId);
        ocmsListWaitAuditOrderRequest.setShopIdList(storeIdList);
        if (Objects.equals(PoiEntityTypeEnum.SHAREABLE_WAREHOUSE.code(), entityType)) {
            ocmsListWaitAuditOrderRequest.setShopIdList(Lists.newArrayList());
            ocmsListWaitAuditOrderRequest.setWarehouseIdList(storeIdList);
        }

        ocmsListWaitAuditOrderRequest.setBeginCreateTime(getAfterSaleQueryStartTime());
        ocmsListWaitAuditOrderRequest.setEndCreateTime(System.currentTimeMillis());

        ocmsListWaitAuditOrderRequest.setAfterSaleApplyStatusList(Lists.newArrayList(AfterSaleApplyStatusEnum.COMMIT.getValue()));
        return ocmsListWaitAuditOrderRequest;
    }

    /**
     * 售后开始时间
     * @return
     */
    public static Long getAfterSaleQueryStartTime(){
        int month = MccConfigUtil.queryAfsOrderCreateTimeMonthBefore();
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -month);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTimeInMillis();
    }

}
