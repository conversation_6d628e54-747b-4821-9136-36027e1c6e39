# web服务端口号
server.port: 8080
management:
  endpoints:
    web:
      base-path: /monitor
      path-mapping:
        health: /alive

spring:
  profiles:
    active: '@active-profile@'
  application:
    name: ${app.name}
  main:
    allow-bean-definition-overriding: true

# 关闭x-frame的InfFilter
web:
  filter:
    inf:
      enabled: false

sso:
  clientId: b996447f09
  env: test

mthrift.timeout: 5000
mthrift.conntimeout: 1000

